package com.actonia.subserver.tools.upload.newbot;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.summary.ClarityDBBotDailyUploadThreadMain;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * One-time script
 * example:
 *  ************ - - [28/Jan/2020:00:00:03 +0900] "GET /async_frames/all_popular_books?book_id=100965&element_id=async_frame_all_popular_books HTTP/1.1" 200 12787 "https://sp.comics.mecha.cc/books/100965?&agent_id=14&src_ad=non_000_google_title_ladies_normal&lp_id=111&ebisAdID=Google_adg_061254&utm_source=adwords&utm_medium=cpc&utm_campaign=adwords_title_ladies_normal&gclid=EAIaIQobChMIwPu5t4qs3AIVAQAAAB0BAAAAEAAYACAAEgJVzfD_BwE&gclsrc=aw.ds" "Mozilla/5.0 (iPhone; CPU iPhone OS 9_1 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1 (compatible; AdsBot-Google-Mobile; +http://www.google.com/mobile/adsbot.html)" 125279
 */
@CommonsLog
public class Import5303BotDataToClarityDB {
    private ClarityDBBotDailyUploadThreadMain uploadThreadMain = new ClarityDBBotDailyUploadThreadMain();
    private Gson gson = new Gson();
    private static String domain = "sp.comics.mecha.cc";
    private static int domainId = 5303;
    private static Integer version = 2;
    private final String filePath = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/5303/";
    //local test
    //private final String filePath = "C:\\Users\\<USER>\\Desktop\\work\\bot\\5303\\";
    private String currKey = "";
    // example: 28/Jan/2020:00:00:01
    private String getJSONFilePath(){
        return filePath + "/json";
    }

    public static void main(String[] args) {
        Import5303BotDataToClarityDB bot = new Import5303BotDataToClarityDB();
        bot.process();
    }

    private void process(){
        Map<String,String> map = new HashMap<>();
        map.put("2020-01-28", filePath + "bot_access_log_20200128");
        map.put("2020-07-22", filePath + "bot_access_log_20200722");
        map.put("2021-04-05", filePath + "bot_access_log_20210405");
        Set<String> stringSet = map.keySet();
        for (String dateStr : stringSet) {
            currKey = dateStr;
            String filePath = map.get(dateStr);
            Date logDate = null;
            try {
                logDate = DateUtils.parseDate(dateStr,new String[]{"yyyy-MM-dd"});
            } catch (ParseException e) {
                e.printStackTrace();
            }
            File localLogFile = new File(filePath);
            extractJsonFileAndLoad(localLogFile, domain, domainId, logDate);
        }
        try {
            uploadThreadMain.waitForThreadPool();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) {
        List<CSVRecord> csvRecords;
        try {
            CSVFormat csvFormat = CSVFormat.DEFAULT;
            if (currKey.equals("2020-01-28")) {
                csvFormat = CSVFormat.DEFAULT.withDelimiter(' ');
            }else if (currKey.equals("2020-07-22") || currKey.equals("2021-04-05")) {
                csvFormat = CSVFormat.DEFAULT.withDelimiter('\t');
            }
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);
            String ip = null;
            String dateStr = null;
            Date date = null;
            String status = null;
            String userAgent = null;
            String url = null;
            String uri = null;
            String reqProtocol = null;
            if (currKey.equals("2020-01-28")) {
                final SimpleDateFormat dataFormatter = new SimpleDateFormat("dd/MMM/yyyy:HH:mm:ss", Locale.ENGLISH);
                ip = csvRecord.get(0);
                dateStr = csvRecord.get(3).trim().replace("[","").replace("\"\"","\"");
                date = dataFormatter.parse(dateStr);
                status = csvRecord.get(6);
                userAgent = csvRecord.get(9);
                url = csvRecord.get(8);
                if (url.equals("-")) {
                    return null;
                }
                uri = FormatUtils.getUriFromUrl(url);
                reqProtocol = FormatUtils.getProtocolFromUrl(url);
            }else if (currKey.equals("2020-07-22") || currKey.equals("2021-04-05")) {
                final SimpleDateFormat dataFormatter = new SimpleDateFormat("dd/MMM/yyyy:HH:mm:ss", Locale.ENGLISH);
                ip = csvRecord.get(0);
                String[] s = csvRecord.get(2).trim().split(" ");
                dateStr = s[0];
                date = dataFormatter.parse(dateStr);
                status = csvRecord.get(4);
                userAgent = csvRecord.get(7);
                url = csvRecord.get(6);
                if (url.equals("-")) {
                    return null;
                }
                uri = FormatUtils.getUriFromUrl(url);
                reqProtocol = FormatUtils.getProtocolFromUrl(url);
            }else {
                System.out.println("errrrrrorororororor!");
                return null;
            }

            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }
            long timestamp = date.getTime();
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setReqProtocol(reqProtocol);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));

            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            log.error("Error line : " + line);
            return null;
        }
    }

    private void extractJsonFileAndLoad(File logFile, String domainName, int domainId, Date logDate) {
        if (logDate == null){
            return;
        }
        List<String> outPutJsonFilePaths = new ArrayList<>();
        File localJsonFile;
        FileOutputStream outputStream = null;
        try (FileInputStream inputStream = new FileInputStream(logFile);
             Scanner sc = new Scanner(inputStream, "UTF-8")) {
            int records = 0;
            while (sc.hasNextLine()) {
                String line = sc.nextLine();
                BotJsonVO botVO = parserLineToVO(line, domainName, domainId);
                if (botVO != null) {
                    if (records == 0 || records % 500000 == 0) {
                        if (outputStream != null) {
                            outputStream.flush();
                            outputStream.close();
                        }
                        localJsonFile = new File(getJSONFilePath() + "/" + StringUtils.replace(logFile.getName(), "\\", "") + "_" + records + ".json");
                        log.info(records + " for path2 : " + localJsonFile.getAbsolutePath());
                        outPutJsonFilePaths.add(localJsonFile.getAbsolutePath());
                        outputStream = new FileOutputStream(localJsonFile);
                    }
                    IOUtils.write(gson.toJson(botVO) + "\n", outputStream);
                    records++;
                }
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (sc.ioException() != null) {
                throw sc.ioException();
            }
            log.info("Total count: " + records);
        } catch (Exception e) {
            e.printStackTrace();
        }

        log.info("finished processing file : " + StringUtils.replace(logFile.getName(), "\\", ""));
        //Insert into Database
        for (String outPutJsonFilePath : outPutJsonFilePaths) {
            System.out.println("执行文件导入");
            uploadThreadMain.loadTargetFile(logDate, outPutJsonFilePath);
        }

    }
}
