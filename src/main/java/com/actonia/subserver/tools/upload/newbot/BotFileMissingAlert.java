package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.ScriptRunningDetailJdbcDAO;
import seoclarity.backend.dao.actonia.notice.NoticeInstanceDAO;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ScriptRunningDetailEntity;
import seoclarity.backend.entity.actonia.notice.NoticeInstance;
import seoclarity.backend.summary.ClarityDBBotDailyUploadThreadMain;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

// https://www.wrike.com/open.htm?id=1538422271
@Slf4j
public class BotFileMissingAlert {

    private static final int MESSAGE_BOT_ID = 10600;
    private static final String MESSAGE_BOT_CONTENT = "Your bot data file is missing for %s%.<BR><BR>The file is typically sent via FTP or S3. Please try uploading the file to FTP to ensure the traffic data in the platform is correctly updated. Let us know if you need help!";
    private static final String category = "bot";
    private static final LocalDate today = LocalDate.now();
    private static final int defaultOldSubDays = 15;
    private static final int defaultNewSubDays  = 3;
    private static final int DEFAULT_CONFIG_ID = 8;
    private static final int DEFAULT_DOMAIN_ID = 4765;
    private static final int BATCH_INSERT = 100;


    private NoticeInstanceDAO noticeInstanceDAO;
    private ScriptRunningDetailJdbcDAO scriptRunningDetailJdbcDAO;

    private boolean isTest;
    private int processType;
    private Set<LocalDate> subDaySet;


    public BotFileMissingAlert() {
        noticeInstanceDAO = SpringBeanFactory.getBean("noticeInstanceDAO");
        scriptRunningDetailJdbcDAO = SpringBeanFactory.getBean("scriptRunningDetailJdbcDAO");
    }

    public static void main(String[] args) {
        BotFileMissingAlert botFileMissingAlert = new BotFileMissingAlert();
        botFileMissingAlert.startProcess(args);
    }

    private void startProcess(String[] args) {
        isTest = Boolean.parseBoolean(args[0]);
        processType = Integer.parseInt(args[1]);
        if (processType == 0) {
            processMissBotFile();
            processNotice();
        } else if (processType == 1) {
            processNotice();
        }
    }

    private void processMissBotFile() {
        ClarityDBBotDailyUploadThreadMain clarityDBBotDailyUploadThreadMain = new ClarityDBBotDailyUploadThreadMain();
        Date checkStartDate = DateUtils.addDays(new Date(), -1);
        clarityDBBotDailyUploadThreadMain.checkMissBotDate(checkStartDate);
    }

    // https://www.wrike.com/open.htm?id=1615114719
    private void processNotice() {
        initNoticeParam();

        List<Integer> normalBotDomainIdList = scriptRunningDetailJdbcDAO.getAllBotDomainId();

        checkFileMissing(normalBotDomainIdList);
        checkUpdate(normalBotDomainIdList);
    }

    private void checkFileMissing(List<Integer> normalBotDomainIdList) {
        LocalDate oldestDate = today.minusDays(defaultOldSubDays);
        LocalDate newestDate = today.minusDays(defaultNewSubDays);
        int oldestDateStr = Integer.parseInt(oldestDate.format(DateTimeFormatter.BASIC_ISO_DATE));
        int newestDateStr = Integer.parseInt(newestDate.format(DateTimeFormatter.BASIC_ISO_DATE));

        log.info("==checkMissing oldDate:{} newDate:{}", oldestDateStr, newestDateStr);


        List<List<Integer>> normalDomainSubList = CollectionSplitUtils.splitCollectionBySize(normalBotDomainIdList, BATCH_INSERT);
        List<ScriptRunningDetailEntity> detailList = null;

        List<NoticeInstance> noticeInstanceList = new ArrayList<>();
        List<NoticeInstance> noticeInstancesAlreadyExist = new ArrayList<>();
        List<Long> existIdList = new ArrayList<>();
        int beforeInsertCount = 0;
        int afterInsertCount = 0;
        int existCount = 0;
        int updateCount = 0;

        for (List<Integer> domainList : normalDomainSubList) {
            detailList = scriptRunningDetailJdbcDAO.getDetailByConfigId(DEFAULT_CONFIG_ID, ScriptRunningDetailEntity.STATUS_ERROR, oldestDateStr, newestDateStr, domainList);
            if (detailList == null || detailList.isEmpty()) {
                continue;
            }

            for (ScriptRunningDetailEntity runningDetailEntity : detailList) {
                NoticeInstance noticeInstance = createUploadMissing(runningDetailEntity.getOwnDomainId(), runningDetailEntity.getLogDate());
                noticeInstanceList.add(noticeInstance);
            }


            noticeInstanceList = new ArrayList<>(noticeInstanceList.stream()
                    .collect(Collectors.toMap(
                            instance -> instance.getCategory() + instance.getOwnDomainId() + instance.getTrackDate() + instance.getDataType() + instance.getSubId(),
                            instance -> instance,
                            (existing, replacement) -> existing
                    ))
                    .values());
            beforeInsertCount = noticeInstanceList.size();


            noticeInstancesAlreadyExist = noticeInstanceDAO
                    .queryNoticeInstanceByBetweenTrackDateAndInDomainIds(oldestDate.format(DateTimeFormatter.BASIC_ISO_DATE),
                            newestDate.format(DateTimeFormatter.BASIC_ISO_DATE), StringUtils.join(domainList, ','), category);

            if (!noticeInstancesAlreadyExist.isEmpty()) {

                existCount = noticeInstancesAlreadyExist.size();

                Set<String> domainMissingDateSet = noticeInstancesAlreadyExist.stream()
                        .map(noticeInstance -> noticeInstance.getOwnDomainId() + "::" + noticeInstance.getTrackDate()).collect(Collectors.toSet());
                noticeInstanceList = noticeInstanceList.stream().filter(noticeInstance -> {
                    boolean domainTrackDateExist = domainMissingDateSet.contains(noticeInstance.getOwnDomainId() + "::" + noticeInstance.getTrackDate());
                    if (domainTrackDateExist) {
                        existIdList.add(noticeInstance.getId());
                    }
                    return !domainTrackDateExist;
                }).collect(Collectors.toList());

                if (!existIdList.isEmpty()) {
                    updateCount = existIdList.size();
                    String idString = existIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
                    if (!isTest) {
                        noticeInstanceDAO.batchUpdateStatusById(idString, 4);
                    }
                    existIdList.clear();
                }

                noticeInstancesAlreadyExist.clear();
            }

            if (!noticeInstanceList.isEmpty()) {
                afterInsertCount = noticeInstanceList.size();
                if (!isTest) {
                    noticeInstanceDAO.batchInsert(noticeInstanceList);
                }
                noticeInstanceList.clear();
            }

            detailList.clear();
            log.info("==batchInfoNormalDomain beforeInsertCount:{} afterInsertCount:{} existCount:{} updateCount:{}", beforeInsertCount, afterInsertCount, existCount, updateCount);
        }

        List<String> fileNameList = new ArrayList<>();
        for (LocalDate localDate : subDaySet) {
            int logDate = Integer.parseInt(localDate.format(DateTimeFormatter.BASIC_ISO_DATE));
            Set<String> fileNameSet = scriptRunningDetailJdbcDAO.checkExistsList(DEFAULT_DOMAIN_ID, DEFAULT_CONFIG_ID, logDate);
            if (fileNameSet == null || fileNameSet.isEmpty()) {
                for (String domainName : ClarityDBBotDailyUploadThreadMain.domainMap.keySet()) {
                    OwnDomainEntity domainEntity = ClarityDBBotDailyUploadThreadMain.domainMap.get(domainName);
                    NoticeInstance noticeInstance = createUploadMissing(domainEntity.getId(), logDate);
                    noticeInstanceList.add(noticeInstance);
                }
            } else {
                fileNameList.addAll(fileNameSet);
                for (String domainName : ClarityDBBotDailyUploadThreadMain.domainMap.keySet()) {
                    boolean isFind = false;
                    for (String fileName : fileNameList) {
                        if (StringUtils.containsIgnoreCase(fileName, domainName)) {
                            isFind = true;
                            break;
                        }
                    }
                    if (!isFind) {
                        OwnDomainEntity domainEntity = ClarityDBBotDailyUploadThreadMain.domainMap.get(domainName);
                        NoticeInstance noticeInstance = createUploadMissing(domainEntity.getId(), logDate);
                        noticeInstanceList.add(noticeInstance);
                    }
                }
                fileNameList.clear();
            }

            if (noticeInstanceList.isEmpty()) {
                log.info("==expediaMissCheckNoticeEmpty logDate:{}", logDate);
                continue;
            }

            noticeInstanceList = new ArrayList<>(noticeInstanceList.stream()
                    .collect(Collectors.toMap(
                            instance -> instance.getCategory() + instance.getOwnDomainId() + instance.getTrackDate() + instance.getDataType() + instance.getSubId(),
                            instance -> instance,
                            (existing, replacement) -> existing
                    ))
                    .values());
            beforeInsertCount = noticeInstanceList.size();

            noticeInstancesAlreadyExist = noticeInstanceDAO
                    .queryNoticeInstanceByBetweenTrackDateAndInDomainIds(oldestDate.format(DateTimeFormatter.BASIC_ISO_DATE),
                            newestDate.format(DateTimeFormatter.BASIC_ISO_DATE), StringUtils.join(noticeInstanceList.stream().map(NoticeInstance::getOwnDomainId).collect(Collectors.toList()), ','), category);

            if (!noticeInstancesAlreadyExist.isEmpty()) {
                existCount = noticeInstancesAlreadyExist.size();

                Set<String> domainMissingDateSet = noticeInstancesAlreadyExist.stream()
                        .map(noticeInstance -> noticeInstance.getOwnDomainId() + "::" + noticeInstance.getTrackDate()).collect(Collectors.toSet());
                noticeInstanceList = noticeInstanceList.stream().filter(noticeInstance -> {
                    boolean domainTrackDateExist = domainMissingDateSet.contains(noticeInstance.getOwnDomainId() + "::" + noticeInstance.getTrackDate());
                    if (domainTrackDateExist) {
                        existIdList.add(noticeInstance.getId());
                    }
                    return !domainTrackDateExist;
                }).collect(Collectors.toList());

                if (!existIdList.isEmpty()) {
                    updateCount = existIdList.size();
                    String idString = existIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
                    if (!isTest) {
                        noticeInstanceDAO.batchUpdateStatusById(idString, 4);
                    }
                    existIdList.clear();
                }

                noticeInstancesAlreadyExist.clear();
            }

            if (!noticeInstanceList.isEmpty()) {
                afterInsertCount = noticeInstanceList.size();
                if (!isTest) {
                    noticeInstanceDAO.batchInsert(noticeInstanceList);
                }
                noticeInstanceList.clear();
            }
            log.info("==batchInfoExpedia logDate:{} beforeInsertCount:{} afterInsertCount:{} existCount:{} updateCount:{}", logDate, beforeInsertCount, afterInsertCount, existCount, updateCount);
        }
    }

    private void checkUpdate(List<Integer> normalBotDomainIdList) {
        Date eDate = DateUtils.addDays(new Date(), -2);
        int eDateInt = Integer.parseInt(FormatUtils.formatDate(eDate, "yyyyMMdd"));

        Date sDate = DateUtils.addDays(eDate, -30);
        int sDateInt = Integer.parseInt(FormatUtils.formatDate(sDate, "yyyyMMdd"));

        List<NoticeInstance> missNoticeList = null;
        Map<String, NoticeInstance> missNoticeInstanceGroup = new HashMap<>();
        List<ScriptRunningDetailEntity> detailInfoList = null;
        Set<Integer> oidSet = new HashSet<>();
        Set<Integer> dateSet = new HashSet<>();
        List<NoticeInstance> updateList = new ArrayList<>();

        List<List<Integer>> subNormalDomainList = CollectionSplitUtils.splitCollectionBySizeWithStream(normalBotDomainIdList, BATCH_INSERT);
        for (List<Integer> domainList : subNormalDomainList) {
            missNoticeList = noticeInstanceDAO.queryNoticeByStatusAndCategory(4, category, sDateInt, eDateInt, domainList, false);
            if (missNoticeList == null || missNoticeList.isEmpty()) {
                continue;
            }

            for (NoticeInstance noticeInstance : missNoticeList) {
                oidSet.add(noticeInstance.getOwnDomainId());
                dateSet.add(noticeInstance.getTrackDate());
            }
            missNoticeInstanceGroup.putAll(missNoticeList.stream().collect(Collectors.toMap(v1 -> v1.getOwnDomainId() + "_" + v1.getTrackDate(), v2 -> v2, (v3, v4) -> v3)));


            detailInfoList = scriptRunningDetailJdbcDAO.getDetailByConfigId(DEFAULT_CONFIG_ID, ScriptRunningDetailEntity.STATUS_FINISHED, StringUtils.join(dateSet, ","), StringUtils.join(oidSet, ","));
            if (detailInfoList != null && !detailInfoList.isEmpty()) {
                for (ScriptRunningDetailEntity runningDetailEntity : detailInfoList) {
                    String key = runningDetailEntity.getOwnDomainId() + "_" + runningDetailEntity.getLogDate();
                    NoticeInstance noticeInstance = missNoticeInstanceGroup.get(key);
                    if (noticeInstance != null) {
                        noticeInstance.setStatus(2);
                        noticeInstance.setUpdateDate(new Date());
                        updateList.add(noticeInstance);
                    }
                }

                noticeInstanceDAO.batchUpdate(updateList);
                updateList.clear();
                detailInfoList.clear();
            }

            missNoticeList.clear();
            missNoticeInstanceGroup.clear();
            oidSet.clear();
            dateSet.clear();
        }

        List<Integer> expediaDomainList = new ArrayList<>();
        Map<Integer, String> idPDomainMap = new HashMap<>();
        for (String domainName : ClarityDBBotDailyUploadThreadMain.domainMap.keySet()) {
            Integer domainId = ClarityDBBotDailyUploadThreadMain.domainMap.get(domainName).getId();

            idPDomainMap.put(domainId, domainName);
            expediaDomainList.add(domainId);
        }

        List<List<Integer>> subExpediaDomainList = CollectionSplitUtils.splitCollectionBySizeWithStream(expediaDomainList, BATCH_INSERT);


        Map<String, Integer> missDomainPIdMap = new HashMap<>();
        List<ScriptRunningDetailEntity> detailList = null;
        for (List<Integer> domainList : subExpediaDomainList) {
            missNoticeList = noticeInstanceDAO.queryNoticeByStatusAndCategory(4, category, sDateInt, eDateInt, domainList, false);
            if (missNoticeList == null || missNoticeList.isEmpty()) {
                continue;
            }

            for (NoticeInstance noticeInstance : missNoticeList) {
                int ownDomainId = noticeInstance.getOwnDomainId();
                dateSet.add(noticeInstance.getTrackDate());

                String domain = idPDomainMap.get(ownDomainId);
                if (!missDomainPIdMap.containsKey(domain)) {
                    missDomainPIdMap.put(domain, ownDomainId);
                }
            }

            missNoticeInstanceGroup.putAll(missNoticeList.stream().collect(Collectors.toMap(v1 -> v1.getOwnDomainId() + "_" + v1.getTrackDate(), v2 -> v2, (v3, v4) -> v3)));



            for (int logDate : dateSet) {
                detailList = scriptRunningDetailJdbcDAO.getDetailByConfigId(DEFAULT_CONFIG_ID, ScriptRunningDetailEntity.STATUS_FINISHED, DEFAULT_DOMAIN_ID, logDate);
                if (detailList == null || detailList.isEmpty()) {
                    continue;
                }
                List<String> fileNameList = detailList.stream().map(ScriptRunningDetailEntity::getSymbol).toList();
                for (String domainName : missDomainPIdMap.keySet()) {
                    boolean isFind = false;
                    for (String fileName : fileNameList) {
                        if (StringUtils.containsIgnoreCase(fileName, domainName)) {
                            isFind = true;
                            break;
                        }
                    }

                    if (isFind) {
                        Integer domainId = missDomainPIdMap.get(domainName);
                        String key = domainId + "_" + logDate;
                        NoticeInstance noticeInstance = missNoticeInstanceGroup.get(key);
                        if (noticeInstance != null) {
                            noticeInstance.setStatus(2);
                            noticeInstance.setUpdateDate(new Date());
                            updateList.add(noticeInstance);
                        }
                    }
                }
                detailList.clear();
            }

            noticeInstanceDAO.batchUpdate(updateList);

            missDomainPIdMap.clear();
            dateSet.clear();
            missNoticeInstanceGroup.clear();
            updateList.clear();
            missNoticeList.clear();
        }
    }

    private void initNoticeParam() {
        this.subDaySet = getDateRange(defaultOldSubDays, defaultNewSubDays);
    }

    private Set<LocalDate> getDateRange(int oldSubDays, int newSubDays) {
        LocalDate startDate = today.minusDays(oldSubDays);
        LocalDate endDate = today.minusDays(newSubDays);
        log.info("====startDate:{} endDate:{}", startDate, endDate);
        Set<LocalDate> collect = new TreeSet<>();
        for (LocalDate date = startDate; (date.isBefore(endDate) || date.isEqual(endDate)); date = date.plusDays(1)) {
            collect.add(date);
        }
        final int daysOfPeriod = oldSubDays - newSubDays + 1;
        log.info("====daysOfPeriod:{} collectSize:{} data:{}", daysOfPeriod, collect.size(),collect);
        return collect;
    }

    public NoticeInstance createUploadMissing(Integer domainId, int logDate) {
        return  NoticeInstance.builder()
                .ownDomainId(domainId)
                .category(category)
                .dataType(0)
                .subId(0)
                .trackDate(logDate)
                .status(4)
                .messageId(MESSAGE_BOT_ID)
                .authorId(7)
                .createDate(new Date())
                .build();
    }
}
