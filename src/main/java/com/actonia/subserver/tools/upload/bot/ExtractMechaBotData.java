package com.actonia.subserver.tools.upload.bot;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.ExtractMechaBotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2017-11-21"
 */
public class ExtractMechaBotData extends AbstractExtractBotCommonJsonFile {

	private final String IP_PATTERN = "^(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
	// https://www.wrike.com/open.htm?id=519932437
	// private final String DATE_PATTERN = "\\[.*\\]";
	private final String DATE_PATTERN = "(0?[1-9]|[12][0-9]|3[01])\\/(jan|Jan|JAN|feb|Feb|FEB|mar|Mar|MAR|apr|Apr|APR|may|May|MAY|jun|Jun|JUN|jul|Jul|JUL|aug|Aug|AUG|sep|Sep|SEP|oct|Oct|OCT|nov|Nov|NOV|dec|Dec|DEC)\\/(?!0000)[0-9]{4}:([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9] \\+(?!0000)[0-9]{4}";
	private final String URL_PATTERN = "(GET |POST |HEAD |PUT |OPTIONS |DELETE)[^\\s]{0,} ";
	private final String RM_URL_PATTERN = "^POST|^GET|^HEAD|^PUT|^OPTIONS|^DELETE";
	private final String STATUS_PATTERN = "	\\d{3}	";
	private final String STATUS_PATTERN2 = " \\d{3} ";
	private final String USER_AGENT_PATTERN = "\"[^\"]+\" \\d+$";
	private final String RM_USER_AGENT_PATTERN = " \\d+$";
	private static final SimpleDateFormat formatter = new SimpleDateFormat("dd/MMM/yyyy:hh:mm:ss", Locale.ENGLISH);
	private static final String SPLIT_TAB = "\t";

	private static Date processDate = new Date();
	private static Date logDate;
	private static String localPath = "/opt/bot/";
	private static Integer version;

	private static BotDetailDao botDetailDao;
	
	private int logCnt = 0;

	public ExtractMechaBotData() {
		super();
		domainMaps.put(5303, "mechacomic.jp");
		domainFileNameMap.put(5303, Arrays.asList("bot_access_log_%s"));
	}

	public static void main(String[] args) {
		ExtractMechaBotData extractMechaBotData = new ExtractMechaBotData();
		if (args != null & args.length > 0) {
			if(StringUtils.containsIgnoreCase(args[0], ",")) {
				Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
				Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
				if (args.length > 1) {
					version = Integer.parseInt(args[1]);
				}
				while(sDate.compareTo(eDate) <= 0) {
					logDate = sDate;
					processDate = logDate;
					extractMechaBotData.startProcess(true, false);
					sDate = DateUtils.addDays(sDate, 1);
				}
			} else {
				logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
				processDate = logDate;
				if (args.length > 1) {
					version = Integer.parseInt(args[1]);
				}
				extractMechaBotData.startProcess(true, false);
			}
			extractMechaBotData.waitForThreadPool();
		} else {
            logDate = DateUtils.addDays(new Date(), -1);
			processDate = logDate;
			extractMechaBotData.startProcess(false);
        }
	}

	@Override
	protected Date getBotLogDate() {
		return logDate;
	}

	@Override
	protected String getRemoteFilePath(int domainId) {
		return "/home/<USER>/"+domainId;
	}

	@Override
	protected List<String> getRemoteFileNames(int domainId, Date logDate) {
		List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
		for (String fileName : domainFileNameMap.get(domainId)) {
			fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, "yyyyMMdd")));
		}
		return fileNames;
	}

	@Override
	protected String getLocalFilePath() {
		return "/tmp/";
	}

	@Override
	protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException {
		try {
			String ip = getValueByPattern(IP_PATTERN, line).trim();
			String date = getValueByPattern(DATE_PATTERN, line).trim().replaceAll("\\[|]","");
			String uri = getUri(line);
			if (UrlFilterUtil.shouldSkipUrl(uri)) {
				return null;
			}
			String status = null;
			try {
				status = getValueByPattern(STATUS_PATTERN, line).trim();
			} catch (Exception e) {
				status = getValueByPattern(STATUS_PATTERN2, line).trim();
			}
			
			String userAgent = getUserAgent(line);
			try {
				if (StringUtils.isEmpty(userAgent)) {
					String[] arr = line.split(SPLIT_TAB);
					userAgent = arr[arr.length - 4];
//					System.out.println(" ==UserAgent:" + userAgent);
				}
			} catch (Exception e) {
				if (logCnt % 2000 == 0) {
					System.out.println(" logCnt:" + logCnt);
					e.printStackTrace();
				}
			}

			int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
			if (uaGroupId < 0) {
				return null;
			}

			date = StringUtils.split(date, " ")[0];
			Date time = formatter.parse(date);
			long timestamp = time.getTime();

			if(StringUtils.isEmpty(ip) || StringUtils.isEmpty(domainName) || StringUtils.isEmpty(status) || StringUtils.isEmpty(userAgent) ||
					StringUtils.isEmpty(uri) || StringUtils.isEmpty(String.valueOf(timestamp))) {
				System.out.println(line);
				System.out.println(ip+"\n"+date+"\n"+uri+"\n"+status+"\n"+userAgent+"\n"+time+"\n"+timestamp);
				return null;
			}

			BotJsonVO jsonVO = new BotJsonVO();
			BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
			dataVO.setUaGroupId(uaGroupId);
			dataVO.setOriginIP(ip);
			dataVO.setReqHost(domainName);
			dataVO.setStatus(status);
			dataVO.setUserAgent(userAgent);
			dataVO.setUserAgentStr(userAgent);
			dataVO.setReqPath(uri);
			//Leo - https://www.wrike.com/open.htm?id=186725979
			dataVO.setDate(FormatUtils.formatDate(time, "yyyy-MM-dd HH:mm:ss"));
			dataVO.setTimestamp(String.valueOf(timestamp));
			if (null != domainId && domainId > 0) {
				dataVO.setDomainId(domainId);
			}
			dataVO.setDisableTimeZone(true);
			dataVO.setVersion(version);

			jsonVO.setData(dataVO);
			logCnt++;
			return jsonVO;
		} catch (Exception exp) {
			if (logCnt % 2000 == 0) {
				System.out.println(" logCnt:" + logCnt);
				exp.printStackTrace();
			}
			return null;
		}
	}

	@Override
	protected String getLocalGzFilePath() {
		return localPath;
	}

	private String getUri(String str) {
		String uri = getValueByPattern(URL_PATTERN, str);
		if(StringUtils.isNotEmpty(uri)) {
			return uri.replaceAll(RM_URL_PATTERN, "").trim();
		}
		return uri;
	}

	private String getUserAgent(String str) {
		String agent = getValueByPattern(USER_AGENT_PATTERN, str);
		if(StringUtils.isNotEmpty(agent)) {
			return agent.replaceAll(RM_USER_AGENT_PATTERN, "").replaceAll("\"", "").trim();
		}
		return agent;
	}

	private String getValueByPattern(String pattern, String str) {
		Pattern r = Pattern.compile(pattern);
		Matcher m = r.matcher(str);
		while (m.find()) {
			return m.group();
		}
		return null;
	}
}
