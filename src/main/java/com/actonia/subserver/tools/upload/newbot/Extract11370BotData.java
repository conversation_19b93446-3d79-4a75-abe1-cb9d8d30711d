package com.actonia.subserver.tools.upload.newbot;

import ch.ethz.ssh2.Connection;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.actonia.ScriptRunningDetailJdbcDAO;
import seoclarity.backend.entity.actonia.ScriptRunningDetailEntity;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.*;

import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

// https://www.wrike.com/open.htm?id=1529954186
@CommonsLog
public class Extract11370BotData extends AbstractExtractBotCommonJsonFile {

    private static final SimpleDateFormat dataDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t');
    private static final int DEFAULT_CONFIG_ID = 8;

    private static Integer version = 0;
    private static Date logDate;

    private static String localPath = "/opt/bot/";
    private static String domain = "www.hilton.com";
    private static int domainId = 11370;

    private ScriptRunningDetailJdbcDAO scriptRunningDetailJdbcDAO;

    public Extract11370BotData() {
        super();
        domainMaps.put(domainId, domain);
        domainFileNameMap.put(domainId, Arrays.asList("www_hilton_com_%s"));
        fileNamePatten = "yyyyMMdd";
        scriptRunningDetailJdbcDAO = SpringBeanFactory.getBean("scriptRunningDetailJdbcDAO");
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        this.isTestFlag = false;
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot " + domainId + ", current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/www.hilton.com/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNameList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();

        Connection connection = FTPUtils.login(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW);
        String fileLin = FTPUtils.execute(connection, "ls " + getRemoteFilePath(domainId));
        connection.close();
        String[] fileNames = fileLin.split("\n");
        if (fileNames.length == 0) {
            fileNameList.add(map);
            return fileNameList;
        }

        System.out.println("==remoteFile domainId:" + domainId + " fileList:" + fileNames.length);

        for (String fileName : fileNames) {
            ScriptRunningDetailEntity runningDetailEntity = scriptRunningDetailJdbcDAO.checkExistsByDomainIdAndFileName(domainId, DEFAULT_CONFIG_ID, fileName);
            if (runningDetailEntity == null) {
                map.put(fileName, fileName);
            } else {
                if (runningDetailEntity.getStatus() != null) {
                    Integer status = runningDetailEntity.getStatus();
                    if (status != ScriptRunningDetailEntity.STATUS_FINISHED) {
                        map.put(fileName, fileName);
                    }
                }
            }
        }
        System.out.println("==needProcessBotFileList domainId:" + domainId + " fileList:" + map.size());
        fileNameList.add(map);
        return fileNameList;
    }


    @Override
    protected boolean isSpecialFormat() {
        return true;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected boolean checkDataExist() {
        return false;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.containsIgnoreCase(line, "#Version:")
                || StringUtils.containsIgnoreCase(line, "#Fields:")) {
            log.info("SKIP header line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }

            CSVRecord csvRecord = csvRecords.get(0);

            String userAgent = csvRecord.get(9);
            if (StringUtils.isEmpty(userAgent) || StringUtils.equalsIgnoreCase(userAgent, "-")) {
                return null;
            }

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String url = csvRecord.get(8);
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }

            String uri = FormatUtils.getUriFromUrl(url);

            String date = csvRecord.get(0).trim();
            String time = csvRecord.get(1).trim();
            String dateStr = date + " " + time;
            Date datetime = dataDateFormatter.parse(dateStr);

            String ip = csvRecord.get(2);

            String status = csvRecord.get(5);


            long timestamp = datetime.getTime();

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.equalsIgnoreCase(status, "0") ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.endsWithIgnoreCase(userAgent, "-") ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.endsWithIgnoreCase(uri,"-") ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(dateStr);
            dataVO.setTimestamp(String.valueOf(timestamp));


            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);
            dataVO.setUseFileContextDate(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            return null;
        }
    }


}
