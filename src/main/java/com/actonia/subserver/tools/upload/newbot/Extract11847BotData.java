package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@CommonsLog
public class Extract11847BotData extends AbstractExtractBotCommonJsonFile{

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true);
    SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
    private static Date logDate;
    private static final String localPath = "/opt/bot/";
    private static final String domain = "peoplewin.com";
    private static final String pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    private static Integer version = 0;

    public Extract11847BotData() {
        super();

        s3AccessKey = "********************";
        s3SecretKey = "vnIhwoLtzEBVMZ80lw1Ab1a9chjKaOCJp3ETaa2z";
        s3BusketName = "tf-spokeo-production-vendor-logs";

        domainMaps.put(11847, domain);
        domainFileNameMap.put(11847, Arrays.asList("peoplewin/%s/%s/%s-bingbot", "peoplewin/%s/%s/%s-googlebot"));
        fileNamePatten = "yyyyMMdd";

        dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        emailTo = "<EMAIL>";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;

        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 11847, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        String formatDate = FormatUtils.formatDate(logDate, "yyyyMMdd");
        String year = formatDate.substring(0, 4);
        String month = formatDate.substring(4, 6);
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            String fullFileName = String.format(fileName, year, month, formatDate);
            fileNameMap.put(fullFileName, fullFileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected boolean isDownloadFromS3() {
        return true;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected boolean isMultiFile() {
        return true;
    }

    @Override
    protected boolean isSendEmail() {
        return true;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String userAgent = csvRecord.get(4).trim();
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String url = csvRecord.get(1).trim();

            if (url.equals("-")) {
                return null;
            }
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }

            domainName = FormatUtils.getDomainByUrl(url);

            String ip = csvRecord.get(3).trim();

            String status = csvRecord.get(2).trim();

            String dateStr  = csvRecord.get(0).trim();
            Date date = dateFormat.parse(dateStr);
            long timestamp = date.getTime();

            String uri = FormatUtils.getUriFromUrl(url);

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setUseFileContextDate(true);
            dataVO.setDomainId(domainId);
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            log.error("Error line : " + line);
            errorCnt++;
            return null;
        }
    }
}
