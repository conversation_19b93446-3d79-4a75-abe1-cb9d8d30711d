package com.actonia.subserver.tools.upload.bot;

import ch.ethz.ssh2.Connection;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ZipUtil;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.Charsets;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.ScriptRunningDetailJdbcDAO;
import seoclarity.backend.dao.actonia.UploadFileDetailDao;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.entity.UploadFileDetailEntity;
import seoclarity.backend.entity.actonia.BackendScriptConfigEntity;
import seoclarity.backend.entity.actonia.ScriptRunningDetailEntity;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.summary.ClarityDBBotDailyUploadThreadMain;
import seoclarity.backend.utils.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.text.ParseException;
import java.util.*;

/**
 * Created by xucongjie on 2017/11/17.
 */
@CommonsLog
public abstract class AbstractExtractBotCommonJsonFile {

    protected String s3AccessKey;
    protected String s3SecretKey;
    protected String s3BusketName;
    protected static final String DEFAULT_IP = "0.0.0.0";

    private Gson gson = new Gson();
    protected Map<Integer, String> domainMaps = new HashMap<>();
    protected Map<Integer, List<String>> domainFileNameMap = new HashMap<>();
    protected Map<Integer, Integer> domainCountMaps = new HashMap<>();
    private ClarityDBBotDailyUploadThreadMain uploadThreadMain = new ClarityDBBotDailyUploadThreadMain();

    private ScriptRunningDetailJdbcDAO scriptRunningDetailJdbcDAO;
    private static BotDetailDao botDetailDao;
    private UploadFileDetailDao uploadFileDetailDao;

    public AbstractExtractBotCommonJsonFile() {
        scriptRunningDetailJdbcDAO = SpringBeanFactory.getBean("scriptRunningDetailJdbcDAO");
        botDetailDao = SpringBeanFactory.getBean("botDetailDao");
        uploadFileDetailDao = SpringBeanFactory.getBean("uploadFileDetailDao");
    }

    /**
     * get the process date
     *
     * @return
     */
    protected abstract Date getBotLogDate();

    /**
     * get the remote file path
     *
     * @param domainId
     * @return
     */
    protected abstract String getRemoteFilePath(int domainId);

    /**
     * get the remote files name on file path
     *
     * @param domainId
     * @return
     */
    protected abstract List<String> getRemoteFileNames(int domainId, Date logDate);

    /**
     * get local save file path
     *
     * @return
     */
    protected abstract String getLocalFilePath();

    /**
     * input one line and return the standard VO
     *
     * @param line
     * @param domainName
     * @param domainId
     * @return
     */
    protected abstract BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException;

    /**
     * get local GZip file path
     *
     * @return
     */
    protected abstract String getLocalGzFilePath();

    protected ScriptRunningDetailEntity insertDailyRunningDetail(int domainId, Integer logDate) {
        ScriptRunningDetailEntity scriptRunningDetailEntity = new ScriptRunningDetailEntity();
        scriptRunningDetailEntity.setConfigId(BackendScriptConfigEntity.BACKEND_SCRIPT_BOT_DAILY);
        scriptRunningDetailEntity.setOwnDomainId(domainId);
        scriptRunningDetailEntity.setCreateDate(new Date());
        scriptRunningDetailEntity.setLogDate(logDate);
        scriptRunningDetailEntity.setStatus(ScriptRunningDetailEntity.STATUS_RUNNING);
        scriptRunningDetailEntity.setRerunNo(0);
        int id = scriptRunningDetailJdbcDAO.insert(scriptRunningDetailEntity);
        scriptRunningDetailEntity.setId(id);
        return scriptRunningDetailEntity;
    }

    protected ScriptRunningDetailEntity getRunningDetail(int domainId, Integer logDate) {
        return scriptRunningDetailJdbcDAO.checkExistsProcessing(
                domainId,
                BackendScriptConfigEntity.BACKEND_SCRIPT_BOT_DAILY,
                logDate,
                new Integer[]{
                        ScriptRunningDetailEntity.STATUS_RUNNING,
                        ScriptRunningDetailEntity.STATUS_ERROR,
                        ScriptRunningDetailEntity.STATUS_FINISHED
                });
    }

    protected boolean isMultiFile() {
        return false;
    }

    protected boolean isDynamicFileName() {
        return false;
    }

    protected boolean isDownloadFromS3() {
        return false;
    }

    protected void process(int domainId, String domainName, Date logDate) throws Exception {
        process(domainId, domainName, logDate, false, null, null);
    }

    protected void process(int domainId, String domainName, Date logDate, boolean backProcess, String s3Key, String s3FileName) throws Exception {
        log.info("processing domainId : " + domainId + " domainName : " + domainName + " BotLogDate :" + logDate);
        if (!backProcess && !isMultiFile()) {
            //check if clarityDB have data?
            boolean existData = botDetailDao.getBotExistData(domainId, logDate);
            if (existData) {
                log.error("domainId : " + domainId + " domainName : " + domainName + " BotLogDate :" + logDate + " # already exist data, please check.");
                return;
            }
        }

        File remoteFolder = new File(getRemoteFilePath(domainId));
        if (!remoteFolder.exists()) {
            remoteFolder.mkdirs();
        }

        if (isDownloadFromS3()) {
            log.info("start processing file from s3 : " + s3Key);
            String localFilePath = downloadFileFromS3(domainId, s3Key, s3FileName, logDate);
            if (StringUtils.isBlank(localFilePath)) {
                log.info("===s3 file does not exit , exit !");
                return;
            }
            File localFile = new File(localFilePath);
//            extractJsonFileAndLoadForBigFile(s3FileName, localFile, domainName, domainId, logDate);
            extractJsonFileAndLoad(localFile, domainName, domainId, logDate, s3FileName);
            localFile.deleteOnExit();
        } else {
            boolean uploadStatus = false;
            List<String> remoteFileNames = getRemoteFileNames(domainId, logDate);
            if (remoteFileNames == null || remoteFileNames.isEmpty()) {
                log.info("===remote file does not exit , exit ! domainId: " + domainId);
                return;
            }
            for (String fileName : remoteFileNames) {
                log.info("start processing file : " + fileName);
                try {
                    if (isDynamicFileName()) {
                        log.info("is dynamic file logic.");
                        Connection connection = FTPUtils.login(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW);
                        String fileLin = FTPUtils.execute(connection, "ls " + getRemoteFilePath(domainId));
                        connection.close();
                        System.out.println("get remote file path : " + fileLin);
                        String[] fileNames = fileLin.split("\n");
                        for (String name : fileNames) {
                            if (name.startsWith(fileName)) {
                                System.out.println("Find target file : " + name);
                                fileName = name;
                            }
                        }
                    }

                    // copy tht FTP file into local
                    FTPUtils.copyFileToLocalBySSH(
                            FTPUtils.getFasterServer(),
                            FTPUtils.FTP_SERVER_USER,
                            FTPUtils.FTP_SERVER_PW,
                            Arrays.asList(fileName),
                            getRemoteFilePath(domainId),
                            getLocalFilePath());

                } catch (Exception e) {
                    e.printStackTrace();
                    if (isMultiFile()) {
                        log.info("this is multi file domain...continue process other files.");
                        continue;
                    }
                    if (domainCountMaps.get(domainId) >= 1) {
                        //if one domain have more than 1 file don't throw exception.
                        log.info("domain files not exist but this is not first file so skip this domain.");
                        return;
                    } else {
                        log.info("Error with domain: " + domainId + " domainName:" + domainName);
                        throw new Exception("Bot File Copy Error with domain: " + domainId + " domainName:" + domainName + " date: " + logDate + " Msg: " + e.getMessage());
                    }
                }
                log.info("finish copy file to local : " + getLocalFilePath());

                File localFile = new File(getLocalFilePath() + "/" + StringUtils.replace(fileName, "\\", ""));
                if (localFile.length() == 0) {
                    continue;
                }
                uploadStatus = true;

                // insert new uploadFileDetailInfo
                insertUploadFileDetailInfo(domainId, fileName, logDate, localFile.length());

                log.info("path : " + localFile.getAbsolutePath());
                localFile.deleteOnExit();
                extractJsonFileAndLoad(localFile, domainName, domainId, logDate, fileName);
//            uploadThreadMain.loadTargetFile(logDate, gzFile);
            }
            if (!uploadStatus) {
                throw new Exception("Bot File Copy Error with domain: " + domainId + " domainName:" + domainName + " date: " + logDate + " Msg: didn't process any files.");
            }

        }

    }

    private String downloadFileFromS3(int domainId, String key, String fileName, Date logDate) {

        log.info("===downloadFile key: " + key);

        String filePath = getRemoteFilePath(domainId) + fileName;
        try {
            AWSCredentials credentials = new BasicAWSCredentials(s3AccessKey, s3SecretKey);
            AmazonS3 s3client = new AmazonS3Client(credentials);
//            ObjectListing objectListing = s3client.listObjects(s3BusketName);
//            System.out.println("===" + JSON.toJSONString(objectListing));

            GetObjectRequest rangeObjectRequest = new GetObjectRequest(s3BusketName, key);
            S3Object objectPortion = s3client.getObject(rangeObjectRequest);
            File outFile = new File(filePath);
            if (objectPortion != null && objectPortion.getObjectContent() != null) {
                InputStream objectData = objectPortion.getObjectContent();
                FileOutputStream fos = new FileOutputStream(outFile);
                byte[] readBuf = new byte[1024];
                int readLen;
                while ((readLen = objectData.read(readBuf)) > 0) {
                    fos.write(readBuf, 0, readLen);
                }
                objectData.close();
                fos.close();

                System.out.println("===downloadFile success : " + outFile.getAbsolutePath());
            }

            // insert new info
            insertUploadFileDetailInfo(domainId, fileName, logDate, outFile.length());

        } catch (Exception e1) {
            e1.printStackTrace();
            return null;
        }

        return filePath;
    }

    private void extractJsonFileAndLoad(File localFile, String domainName, int domainId, Date logDate, String remoteFileName) throws Exception {

        //uncompress before read
        if (localFile.getName().endsWith(".gz")) {
            String textFilePath = GZipUtil.unGzipFile(localFile.getAbsolutePath());
            log.info("Find Gzip file, will uncompress to : " + textFilePath);
            localFile = new File(textFilePath);
            updateUnzippedFileSize(localFile.length(), domainId, remoteFileName, logDate);
        } else if (localFile.getName().endsWith(".zip")) {
            String targetFolder = "/tmp/" + IdUtil.randomUUID() + "/";
            log.info("Start process Zip files in folder : " + targetFolder);
            ZipUtil.unzip(localFile.getAbsolutePath(), targetFolder, Charsets.UTF_8);
            File tmpFile = new File(targetFolder);
            localFile.delete();
            localFile = FileUtil.createTempFile(new File("/tmp/"));
            localFile.deleteOnExit();
            log.info("new tmp file name : " + localFile.getAbsolutePath());
            for (File file : tmpFile.listFiles()) {
                FileUtil.appendLines(FileUtil.readLines(file, Charset.forName("utf-8")), localFile, Charset.forName("utf-8"));
                FileUtil.appendUtf8String("\n", localFile);
            }
            updateUnzippedFileSize(localFile.length(), domainId, remoteFileName, logDate);
        }

        List<String> outPutJsonFilePaths = new ArrayList<>();
        File localJsonFile;
        FileOutputStream outputStream = null;
        try (FileInputStream inputStream = new FileInputStream(localFile);
             Scanner sc = new Scanner(inputStream, "UTF-8")) {
            int records = 0;
            while (sc.hasNextLine()) {
                String line = sc.nextLine();
                BotJsonVO botVO = parserLineToVO(line, domainName, domainId);
                if (botVO != null) {
                    if (records == 0 || records % 500000 == 0) {
                        if (outputStream != null) {
                            outputStream.flush();
                            outputStream.close();
                        }
                        localJsonFile = new File(getLocalFilePath() + "/" + StringUtils.replace(localFile.getName(), "\\", "") + "_" + records + ".json");
                        log.info(records + " for path2 : " + localJsonFile.getAbsolutePath());
                        localJsonFile.deleteOnExit();
                        outPutJsonFilePaths.add(localJsonFile.getAbsolutePath());
                        outputStream = new FileOutputStream(localJsonFile);
                    }
                    IOUtils.write(gson.toJson(botVO) + "\n", outputStream);
                    records++;
                }
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (sc.ioException() != null) {
                //occur error when scan
                throw sc.ioException();
            }
            log.info("Total count: " + records);
            updateDataCount(records, domainId, remoteFileName, logDate);
        } catch (Exception e) {
            e.printStackTrace();
        }

        log.info("finished processing file : " + StringUtils.replace(localFile.getName(), "\\", ""));
        localFile.deleteOnExit();
        //Insert into Database
        for (String outPutJsonFilePath : outPutJsonFilePaths) {
            uploadThreadMain.loadTargetFile(logDate, outPutJsonFilePath);
        }
        Integer count = domainCountMaps.get(domainId);
        if (count == null) {
            count = 0;
        }
        domainCountMaps.put(domainId, ++count);
    }

    protected List<ScriptRunningDetailEntity> getNeedBackProcessDates(int domainId) {

        //total backprocess 30 days
        Date eDate = DateUtils.addDays(new Date(), -2);
        Date sDate = DateUtils.addDays(eDate, -30);
        List<ScriptRunningDetailEntity> detailEntities = scriptRunningDetailJdbcDAO.checkExistsErrorProcessing(
                domainId,
                BackendScriptConfigEntity.BACKEND_SCRIPT_BOT_DAILY,
                FormatUtils.formatDateToYyyyMmDd(sDate),
                FormatUtils.formatDateToYyyyMmDd(eDate),
                ScriptRunningDetailEntity.STATUS_ERROR);
        for (ScriptRunningDetailEntity detailEntity : detailEntities) {
            log.info(detailEntity.getId() + " need backprocess..");
        }
        return detailEntities;
    }

    protected void waitForThreadPool() {
        try {
            uploadThreadMain.waitForThreadPool();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    protected void startProcess(boolean backProcess) {
        startProcess(backProcess, true, null, null);
    }

    protected void startProcess(boolean backProcess, boolean autoClosePool) {
        startProcess(backProcess, autoClosePool, null, null);
    }

    protected void startProcess(boolean backProcess, boolean autoClosePool, String s3Key, String s3FileName) {
        startProcess(backProcess, autoClosePool, s3Key, s3FileName, true);
    }

    protected void startProcess(boolean backProcess, boolean autoClosePool, String s3Key, String s3FileName, boolean statusCheck) {
        for (Map.Entry<Integer, String> entry : domainMaps.entrySet()) {
            int domainId = entry.getKey();
            String domainName = entry.getValue();
            //check running info
            ScriptRunningDetailEntity detailEntity = getRunningDetail(domainId, FormatUtils.formatDateToYyyyMmDd(getBotLogDate()));
            try {
                if (statusCheck && !backProcess && null != detailEntity && detailEntity.getStatus() != ScriptRunningDetailEntity.STATUS_ERROR) {
                    log.error("ScriptRunningDetailEntity find on domainId :" + domainId + " logDate : " + getBotLogDate() + " id:" + detailEntity.getId() + " status :" + detailEntity.getStatus());
                    break;
                }
                if (null == detailEntity) {
                    detailEntity = insertDailyRunningDetail(domainId, FormatUtils.formatDateToYyyyMmDd(getBotLogDate()));
                    log.info("Insert New RUNNING detail : " + detailEntity.getId());
                }
                process(domainId, domainName, getBotLogDate(), backProcess, s3Key, s3FileName);
                scriptRunningDetailJdbcDAO.updateStatusAndRerunNoById(
                        detailEntity.getId(),
                        ScriptRunningDetailEntity.STATUS_FINISHED,
                        detailEntity.getRerunNo());
            } catch (Exception e) {
                e.printStackTrace();
                scriptRunningDetailJdbcDAO.updateStatusAndRerunNoById(
                        detailEntity.getId(),
                        ScriptRunningDetailEntity.STATUS_ERROR,
                        backProcess ? detailEntity.getRerunNo() + 1 : 0);
            }
            if (!backProcess) {
                List<ScriptRunningDetailEntity> detailEntities = getNeedBackProcessDates(domainId);
                if (CollectionUtils.isNotEmpty(detailEntities)) {
                    for (ScriptRunningDetailEntity entity : detailEntities) {
                        log.info("Reprocess : " + gson.toJson(entity));
                        try {
                            process(domainId, domainName, FormatUtils.formatIntegerToDate(entity.getLogDate()), backProcess, s3Key, s3FileName);
                            scriptRunningDetailJdbcDAO.updateStatusAndRerunNoById(
                                    entity.getId(),
                                    ScriptRunningDetailEntity.STATUS_FINISHED,
                                    entity.getRerunNo() + 1);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        if (autoClosePool) {
            waitForThreadPool();
        }
    }

//    protected void startProcessByS3File(boolean backProcess, boolean autoClosePool, String s3Key, String s3FileName) {
//        for (Map.Entry<Integer, String> entry : domainMaps.entrySet()) {
//            int domainId = entry.getKey();
//            String domainName = entry.getValue();
//            //check running info
//            ScriptRunningDetailEntity detailEntity = getRunningDetail(domainId, FormatUtils.formatDateToYyyyMmDd(getBotLogDate()));
//            try {
//                if (!backProcess && null != detailEntity && detailEntity.getStatus() != ScriptRunningDetailEntity.STATUS_ERROR) {
//                    log.error("ScriptRunningDetailEntity find on domainId :" + domainId + " logDate : " + getBotLogDate() + " id:" + detailEntity.getId() + " status :" + detailEntity.getStatus());
//                    break;
//                }
//                if (null == detailEntity) {
//                    detailEntity = insertDailyRunningDetail(domainId, FormatUtils.formatDateToYyyyMmDd(getBotLogDate()));
//                    log.info("Insert New RUNNING detail : " + detailEntity.getId());
//                }
//                process(domainId, domainName, getBotLogDate(), backProcess, s3Key, s3FileName);
//                scriptRunningDetailJdbcDAO.updateStatusAndRerunNoById(
//                        detailEntity.getId(),
//                        ScriptRunningDetailEntity.STATUS_FINISHED,
//                        detailEntity.getRerunNo());
//            } catch (Exception e) {
//                e.printStackTrace();
//                scriptRunningDetailJdbcDAO.updateStatusAndRerunNoById(
//                        detailEntity.getId(),
//                        ScriptRunningDetailEntity.STATUS_ERROR,
//                        backProcess ? detailEntity.getRerunNo() + 1 : 0);
//            }
//            if (!backProcess) {
//                List<ScriptRunningDetailEntity> detailEntities = getNeedBackProcessDates(domainId);
//                if (CollectionUtils.isNotEmpty(detailEntities)) {
//                    for (ScriptRunningDetailEntity entity : detailEntities) {
//                        log.info("Reprocess : " + gson.toJson(entity));
//                        try {
//                            process(domainId, domainName, FormatUtils.formatIntegerToDate(entity.getLogDate()), backProcess, s3Key, s3FileName);
//                            scriptRunningDetailJdbcDAO.updateStatusAndRerunNoById(
//                                    entity.getId(),
//                                    ScriptRunningDetailEntity.STATUS_FINISHED,
//                                    entity.getRerunNo() + 1);
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }
//                    }
//                }
//            }
//        }
//        if (autoClosePool) {
//            waitForThreadPool();
//        }
//    }

    //update 20201015 ewain update bot every day file size for monitor
    //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/577078308
    /**
     * insert new record
     * @param domainId
     * @param fileName
     * @param logDate
     * @param fileSize
     */
    protected void insertUploadFileDetailInfo(int domainId, String fileName, Date logDate, Long fileSize) {
        try {
            UploadFileDetailEntity fileDetail = new UploadFileDetailEntity();
            fileDetail.setOwnDomainId(domainId);
            fileDetail.setUploadType(UploadFileDetailEntity.UPLOAD_TYPE_BOT);
            fileDetail.setFileName(fileName);
            fileDetail.setFileNameDay(FormatUtils.formatDateToYyyyMmDd(logDate));
            fileDetail.setDataCount(0);
            fileDetail.setLoadedCount(0);
            fileDetail.setCreateDate(new Date());
            UploadFileDetailEntity uploadFileDetail = uploadFileDetailDao.getUploadFileDetailByUniqueKey(fileDetail);
            if (null == uploadFileDetail) {
                if (fileName.endsWith(".zip") || fileName.endsWith(".gz")) {
                    fileDetail.setZipFileSize(fileSize);
                    fileDetail.setUnzippedFileSize(0L);
                } else {
                    fileDetail.setZipFileSize(0L);
                    fileDetail.setUnzippedFileSize(fileSize);
                }
                uploadFileDetailDao.insert(fileDetail);
                log.info("--------------> insert new uploadFileDetail info for " + fileName);
            } else {
                log.info("--------------> uploadFileDetail has existed, No need to insert new data for " + fileName);
            }
        } catch (Exception e) {
            log.error("--------------> insert new uploadFileDetail info for " + fileName + " failed, need to check data");
        }
    }

    /**
     * update unzipped file size
     * @param fileLength
     * @param domainId
     * @param fileName
     */
    protected void updateUnzippedFileSize(Long fileLength, int domainId, String fileName, Date logDate) {
        try {
            UploadFileDetailEntity fileDetail = new UploadFileDetailEntity();
            fileDetail.setUploadType(UploadFileDetailEntity.UPLOAD_TYPE_BOT);
            fileDetail.setOwnDomainId(domainId);
            fileDetail.setFileName(fileName);
            fileDetail.setFileNameDay(FormatUtils.formatDateToYyyyMmDd(logDate));
            fileDetail.setUnzippedFileSize(fileLength);
            UploadFileDetailEntity uploadFileDetailQuery = uploadFileDetailDao.getUploadFileDetailByUniqueKey(fileDetail);
            if (null != uploadFileDetailQuery) {
                uploadFileDetailDao.updateUnzippedFileSize(uploadFileDetailQuery.getId(), fileDetail);
                log.info("--------------> update uploadFileDetail unzippedFileSize for " + fileName);
            } else {
                log.info("--------------> update uploadFileDetail unzippedFileSize failed, uploadFileDetail info for " + fileName + " is not exist");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("--------------> update uploadFileDetail unzippedFileSize for " + fileName + " failed, need to check data");
        }
    }

    /**
     * update data count
     * @param cnt
     * @param domainId
     * @param fileName
     */
    protected void updateDataCount(int cnt, int domainId, String fileName, Date logDate) {
        try {
            UploadFileDetailEntity fileDetail = new UploadFileDetailEntity();
            fileDetail.setUploadType(UploadFileDetailEntity.UPLOAD_TYPE_BOT);
            fileDetail.setOwnDomainId(domainId);
            fileDetail.setFileName(fileName);
            fileDetail.setFileNameDay(FormatUtils.formatDateToYyyyMmDd(logDate));
            fileDetail.setDataCount(cnt);
            UploadFileDetailEntity uploadFileDetailQuery = uploadFileDetailDao.getUploadFileDetailByUniqueKey(fileDetail);
            if (null != uploadFileDetailQuery) {
                uploadFileDetailDao.updateDataCount(uploadFileDetailQuery.getId(), fileDetail);
                log.info("--------------> update uploadFileDetail parseDataCount for " + fileName);
            } else {
                log.info("--------------> update uploadFileDetail parseDataCount failed, uploadFileDetail info for " + fileName + " is not exist");
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("--------------> update uploadFileDetail parseDataCount for " + fileName + " failed, need to check data");
        }
    }


}
