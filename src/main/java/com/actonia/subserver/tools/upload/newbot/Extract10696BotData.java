package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@CommonsLog
public class Extract10696BotData extends AbstractExtractBotCommonJsonFile {

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(' ');
    private static Date processDate = new Date();
    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static String domain = "cdon.se";

    private static Integer version = 0;

    public Extract10696BotData() {
        super();
        domainMaps.put(10696, domain);
        domainFileNameMap.put(10696, Arrays.asList("cdon.se.%s.log_"));
        fileNamePatten = "yyyy-MM-dd";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 10696, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, false);
        } else {
            // for 10696, we need process the day before yesterday's bot files!!!
            this.logDate = DateUtils.addDays(this.logDate, -1);
            bot.startProcess(false, true);
        }
    }

    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId ;
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }
    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.containsIgnoreCase(line, "Date,URL,ResponseCode")) {
            log.info("SKIP header line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }

            CSVRecord csvRecord = csvRecords.get(0);

            String dateStr = csvRecord.get(0) + " " + csvRecord.get(1);
            Date dateTime = formatter.parse(dateStr);
            long timestamp = dateTime.getTime();

            String url = csvRecord.get(3);

            if (url.equals("-")) {
                return null;
            }

            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }

            String uri = FormatUtils.getUriFromUrl(url);
            domainName = FormatUtils.getDomainByUrl(url);

            String ip = "0.0.0.0";
            String userAgent = csvRecord.get(5);
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String status = csvRecord.get(6);
            String reqProtocol = FormatUtils.getProtocolFromUrl(url);

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.endsWithIgnoreCase(userAgent, "-") ||
                    StringUtils.isEmpty(url) ||
                    StringUtils.endsWithIgnoreCase(url,"-") ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + domainName + "\n" + dateStr + "\n"
//                        + url + "\n" + status + "\n" + userAgent + "\n" + dateTime + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setDate(dateStr);
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setVersion(version);
            dataVO.setReqProtocol(reqProtocol);
            dataVO.setDomainId(domainId);
            /*使用bot log文件中的date*/
            dataVO.setUseFileContextDate(true);
            jsonVO.setData(dataVO);

            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line !!!!" + line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return true;
    }

}
