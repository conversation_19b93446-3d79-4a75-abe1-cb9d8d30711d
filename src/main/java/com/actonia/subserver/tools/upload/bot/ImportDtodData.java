package com.actonia.subserver.tools.upload.bot;

import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2018-06-10 19:52
 **/
public class ImportDtodData extends AbstractLocalExtractBotJsonFile {

    private final String IP_PATTERN = "^(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d) ";
    private final String DATE_PATTERN = "\\[.*\\]";
    private final String URL_PATTERN = "(GET |POST |HEAD |PUT |OPTIONS )[^\\s]{0,} ";
    private final String RM_URL_PATTERN = "^POST|^GET|^HEAD|^PUT|^OPTIONS";
    private final String STATUS_PATTERN = " \\d{3} ";
    private final String USER_AGENT_PATTERN = " \\d{3} (\\d+|-) \"(-|((https|http|ftp|rtsp|mms)?:\\/\\/)[^\\s]+)\" \"([^\"]{0,}|-)\"";
    private final String RM_USER_AGENT_PATTERN = " \\d{3} (\\d+|-) \"(-|((https|http|ftp|rtsp|mms)?:\\/\\/)[^\\s]+)\" ";

    private SimpleDateFormat formatter = new SimpleDateFormat("dd/MMM/yyyy:hh:mm:ss", Locale.ENGLISH);

    public static void main(String[] args) {
        ImportDtodData importDtodData = new ImportDtodData();
        try {
            importDtodData.process();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected String getHost(String line) {
        return "www.dtod.ne.jp";
    }

    @Override
    protected String[] getInputDirPath() {
        return new String[]{"E:\\TEMP\\bot\\dtod\\"};
    }

    @Override
    protected String getOutPutDirPath() {
        return "E:\\TEMP\\bot\\dtod_output\\";
    }

    @Override
    protected int getDomainId() {
        return 6820;
    }

    @Override
    protected String getIP(String line) {
        return StringUtils.trimToNull(getValueByPattern(IP_PATTERN, line));
    }

    @Override
    protected String getUserAgent(String line) {
        String agent = getValueByPattern(USER_AGENT_PATTERN, line);
        if(StringUtils.isNotEmpty(agent)) {
            return agent.replaceAll(RM_USER_AGENT_PATTERN, "").replaceAll("\"", "").trim();
        }
        return agent;
    }

    @Override
    protected String getStatus(String line) {
        return StringUtils.trim(getValueByPattern(STATUS_PATTERN, line));
    }

    @Override
    protected String getUri(String line) {
        String uri = getValueByPattern(URL_PATTERN, line);
        if(StringUtils.isNotEmpty(uri)) {
            return uri.replaceAll(RM_URL_PATTERN, "").trim();
        }
        return uri;
    }

    @Override
    protected Date getDate(String line) throws ParseException {
        String date = getValueByPattern(DATE_PATTERN, line);
        if(StringUtils.isNotEmpty(date)) {
            date = date.trim().replaceAll(" \\+\\d{4}.*","").replaceAll("\\[|\\]", "");
        }
        return formatter.parse(date);
    }


}
