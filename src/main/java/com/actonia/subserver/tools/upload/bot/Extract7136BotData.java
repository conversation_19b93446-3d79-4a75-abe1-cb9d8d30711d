package com.actonia.subserver.tools.upload.bot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@CommonsLog
public class Extract7136BotData extends AbstractExtractBotCommonJsonFile{

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(' ').withIgnoreEmptyLines(true);

    private static Integer version = 0;

    SimpleDateFormat formatter = new SimpleDateFormat("dd/MMM/yyyy:HH:mm:ss", Locale.ENGLISH);
    SimpleDateFormat timestampFormatter = new SimpleDateFormat("dd/MMM/yyyy:HH:mm:ss Z", Locale.ENGLISH);

    private static Date processDate = new Date();
    private static Date logDate;
    private static String localPath = "/opt/bot/";

    public Extract7136BotData() {
        super();
        domainMaps.put(7136, "kaigoshoku.mynavi.jp");
        domainFileNameMap.put(7136, Arrays.asList("access_log.%s"));
    }

    public static void main(String[] args) throws Exception {

        Extract7136BotData extract7136BotData = new Extract7136BotData();
//        String line = "*************** - - [31/Dec/2019:23:59:57 +0900] \"POST /r/search.php HTTP/1.1\" 302 3 \"4965241\" \"https://kaigoshoku.mynavi.jp/support/column/tenshoku-shippai/\" \"Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.4 Mobile/15E148 Safari/604.1\"";
//        BotJsonVO botJsonVO = extract7136BotData.parserLineToVO(line,"kaigoshoku.mynavi.jp",7136);
//        System.out.println("-----botJsonVO : " + JSON.toJSONString(botJsonVO));

        if (args != null & args.length > 0) {
            if(StringUtils.containsIgnoreCase(args[0], ",")) {
                Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
                Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
                if (args.length > 1) {
                    localPath = args[1];
                }
                while(sDate.compareTo(eDate) <= 0) {
                    logDate = sDate;
                    processDate = logDate;
                    extract7136BotData.startProcess(true, false);
                    sDate = DateUtils.addDays(sDate, 1);
                }
            } else {
                logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
                processDate = logDate;
                if (args.length > 1) {
                    localPath = args[1];
                }
                extract7136BotData.startProcess(true, false);
            }

            if (args.length > 2) {
                version = NumberUtils.toInt(args[2]);
            }
            extract7136BotData.waitForThreadPool();
        } else {
            logDate = new Date();
            processDate = logDate;
            extract7136BotData.startProcess(false);
        }

    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/"+domainId+"/";
    }

    @Override
    protected List<String> getRemoteFileNames(int domainId, Date logDate) {
        List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, "yyyyMMdd")));
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return "/tmp/";
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException {

        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : "+line);
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String ip = csvRecord.get(0);
            String date = csvRecord.get(3).replaceAll("\\[","");
            String timeZone = csvRecord.get(4).replaceAll("\\]","");
            String timeZoneDate = date + " " + timeZone;
            String uri = csvRecord.get(9);
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }
            String status = csvRecord.get(6);
            String userAgent = csvRecord.get(10);

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            Date botDate = formatter.parse(date);
            Date time = timestampFormatter.parse(timeZoneDate);
            long timestamp = time.getTime();

            if(StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
                System.out.println(ip+"\n"+date+"\n"+uri+"\n"+status+"\n"+userAgent+"\n"+time+"\n"+timestamp);
                return null;
            }

            if(uri.equals("-")){
                log.info("==url is -,skip");
                return null;
            }
            if(userAgent.equalsIgnoreCase("-") || userAgent.equalsIgnoreCase("Stanby Crawler")){
                log.info("==userAgent is invalid,skip:" + userAgent);
                return null;
            }

            domainName = FormatUtils.getDomainByUrl(uri, false);
            uri = FormatUtils.getUriFromUrl(uri);

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);

            dataVO.setVersion(version);

            //Leo - https://www.wrike.com/open.htm?id=186725979
            dataVO.setDate(FormatUtils.formatDate(botDate, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
//            dataVO.setDisableTimeZone(true);

            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : "+line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    private String removeHostPotocal(String host) {
        return host.replaceAll("^http[s]://", "");
    }

}
