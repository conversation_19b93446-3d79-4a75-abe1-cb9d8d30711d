package com.actonia.subserver.tools.upload.newbot;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.summary.ClarityDBBotDailyUploadThreadMain;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * One-time script
 * example:
 *  2020-08-08T06:04:21.444180Z,*************,200,www-us-east-1.carfax.com,/Research-2014-Lincoln-MKX_z12912,HTTP/1.1,"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.92 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
 */
@CommonsLog
public class Import4609BotDataToClarityDB {
    private ClarityDBBotDailyUploadThreadMain uploadThreadMain = new ClarityDBBotDailyUploadThreadMain();
    private Gson gson = new Gson();
    private static String domain = "www.carfax.com";
    private static int domainId = 4609;
    private static Integer version = 0;
    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',');
    // example: 2020-08-08T06:04:21.444180Z
    private static final SimpleDateFormat dataFormatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
    private String getJSONFilePath(){
        return "/home/<USER>/source/ewain/formal-clarity-backend-scripts/bot_log_data/json_file/";
    }

    public static void main(String[] args) {
        Import4609BotDataToClarityDB bot = new Import4609BotDataToClarityDB();
        bot.process();
    }

    private void process(){
        Map<String,String> map = new HashMap<>();
        map.put("2020-10-09", "/home/<USER>/source/ewain/formal-clarity-backend-scripts/bot_log_data/4609/20201009.csv");
        Set<String> stringSet = map.keySet();
        for (String dateStr : stringSet) {
            String filePath = map.get(dateStr);
            Date logDate = null;
            try {
                logDate = DateUtils.parseDate(dateStr,new String[]{"yyyy-MM-dd"});
            } catch (ParseException e) {
                e.printStackTrace();
            }
            File localLogFile = new File(filePath);
            extractJsonFileAndLoad(localLogFile, domain, domainId, logDate);
        }
        try {
            uploadThreadMain.waitForThreadPool();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) {
        if (StringUtils.startsWith(line, "head symbol")) {
            log.info("SKIP special line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String ip = csvRecord.get(1);
            String dateStr = csvRecord.get(0).trim();
            Date date = dataFormatter.parse(dateStr);
            String status = csvRecord.get(2);
            String userAgent = csvRecord.get(6);
            String uri = csvRecord.get(4);
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }
            long timestamp = date.getTime();
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));

            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            //e.printStackTrace();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            log.error("Error line : " + line);
            return null;
        }
    }

    private void extractJsonFileAndLoad(File logFile, String domainName, int domainId, Date logDate) {
        if (logDate == null){
            return;
        }
        List<String> outPutJsonFilePaths = new ArrayList<>();
        File localJsonFile;
        FileOutputStream outputStream = null;
        try (FileInputStream inputStream = new FileInputStream(logFile);
             Scanner sc = new Scanner(inputStream, "UTF-8")) {
            int records = 0;
            while (sc.hasNextLine()) {
                String line = sc.nextLine();
                BotJsonVO botVO = parserLineToVO(line, domainName, domainId);
                if (botVO != null) {
                    if (records == 0 || records % 500000 == 0) {
                        if (outputStream != null) {
                            outputStream.flush();
                            outputStream.close();
                        }
                        localJsonFile = new File(getJSONFilePath() + "/" + StringUtils.replace(logFile.getName(), "\\", "") + "_" + records + ".json");
                        log.info(records + " for path2 : " + localJsonFile.getAbsolutePath());
                        outPutJsonFilePaths.add(localJsonFile.getAbsolutePath());
                        outputStream = new FileOutputStream(localJsonFile);
                    }
                    IOUtils.write(gson.toJson(botVO) + "\n", outputStream);
                    records++;
                }
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (sc.ioException() != null) {
                throw sc.ioException();
            }
            log.info("Total count: " + records);
        } catch (Exception e) {
            e.printStackTrace();
        }

        log.info("finished processing file : " + StringUtils.replace(logFile.getName(), "\\", ""));
        //Insert into Database
        for (String outPutJsonFilePath : outPutJsonFilePaths) {
            System.out.println("执行文件导入");
            uploadThreadMain.loadTargetFile(logDate, outPutJsonFilePath);
        }

    }
}
