package com.actonia.subserver.tools.upload.bot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO.BotDataVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.Extract7135Part2BotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2018-12-04"
 */
@Deprecated
@CommonsLog
public class Extract7135Part2BotData extends AbstractExtractBotCommonJsonFile {

	CSVFormat csvFormat = CSVFormat.DEFAULT;
	private static Date processDate = new Date();
	private static Date logDate;
	private static String localPath = "/opt/bot/";

	private static Integer version = 0;

	public Extract7135Part2BotData() {
		super();
		domainMaps.put(7135, "www.lowes.com");
		domainFileNameMap.put(7135, Arrays.asList("bottrafficreport_HELIXSTOREGCP%s.csv","bottrafficreport_PDGCP%s.csv", "bottrafficreport_PLGCP%s.csv"));
	}

	public static void main(String[] args) {
		Extract7135Part2BotData extractMechaBotData = new Extract7135Part2BotData();
//		String line = "https://www.lowes.com/pd/First-Alert-AC-Hardwired-120-Volt-Photoelectric-Sensor-Smoke-Detector/3031834/reviews,\"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.120 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)\",200,2020-02-09 23:07:49.226666 UTC";
//		try {
//			BotJsonVO k = extractMechaBotData.parserLineToVO(line, "a", 7135);
//			System.out.println(JSONUtil.toJsonPrettyStr(k));
//		} catch (ParseException e) {
//			e.printStackTrace();
//		}
//		System.exit(1);

		if (args != null & args.length > 0) {
			if(StringUtils.containsIgnoreCase(args[0], ",")) {
				Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
				Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
				if (args.length > 1) {
					localPath = args[1];
				}
				while(sDate.compareTo(eDate) <= 0) {
					logDate = sDate;
					processDate = logDate;
					extractMechaBotData.startProcess(true, false);
					sDate = DateUtils.addDays(sDate, 1);
				}
			} else {
				logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
				processDate = logDate;
				if (args.length > 1) {
					localPath = args[1];
				}
				extractMechaBotData.startProcess(true, false);
			}

			if (args.length > 2) {
				version = NumberUtils.toInt(args[2]);
			}
			extractMechaBotData.waitForThreadPool();
		} else {
			logDate = DateUtils.addDays(new Date(), -1);
			processDate = logDate;
			extractMechaBotData.startProcess(false, true, null, null, false);
        }
	}

	private final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss", Locale.ENGLISH);

	@Override
	protected Date getBotLogDate() {
		return logDate;
	}

	@Override
	protected boolean isDynamicFileName() {
		return true;
	}

	@Override
	protected String getRemoteFilePath(int domainId) {
		return "/home/<USER>/"+domainId+"/";
	}

	@Override
	protected List<String> getRemoteFileNames(int domainId, Date logDate) {
		List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
		for (String fileName : domainFileNameMap.get(domainId)) {
			fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, "MM-dd-yy")));
		}
		return fileNames;
	}

	@Override
	protected String getLocalFilePath() {
		return "/tmp/";
	}

	@Override
	protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException {
		if (StringUtils.containsIgnoreCase(line, "url,user_agent,response_code,timestamp")) {
			log.info("SKIP header line : "+line);
			return null;
		}
		List<CSVRecord> csvRecords;
		try {
			CSVParser csvParser = CSVParser.parse(line, csvFormat);
			csvRecords = csvParser.getRecords();
			if (csvRecords.size() > 1) {
				log.error("Lines format error : "+line);
				return null;
			}
			CSVRecord csvRecord = csvRecords.get(0);

			String ip = "0.0.0.0";
			String dateStr = csvRecord.get(3);
			String[] dateArr = dateStr.split(" ");
			String date = dateArr[0]+" "+StringUtils.substringBefore(dateArr[1], ".");
			String uri = csvRecord.get(0);
			if (UrlFilterUtil.shouldSkipUrl(uri)) {
				return null;
			}
			String status = csvRecord.get(2);
			String userAgent = csvRecord.get(1);
			userAgent = StringUtils.removeStartIgnoreCase(userAgent, "HTTP Request User-Agent: ");
			userAgent = StringUtils.substringBefore(userAgent, "Screaming Frog SEO SpiderRobots User-Agent:");
			userAgent = StringUtils.trim(userAgent);

			int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

			Date time = formatter.parse(date);
			long timestamp = time.getTime();

			if(StringUtils.isEmpty(ip) ||
					StringUtils.isEmpty(domainName) ||
					StringUtils.isEmpty(status) ||
					StringUtils.isEmpty(userAgent) ||
					StringUtils.isEmpty(uri) ||
					StringUtils.isEmpty(String.valueOf(timestamp))) {
				System.out.println(ip+"\n"+date+"\n"+uri+"\n"+status+"\n"+userAgent+"\n"+time+"\n"+timestamp);
				return null;
			}

			domainName = FormatUtils.getDomainByUrl(uri, false);
			uri = FormatUtils.getUriFromUrl(uri);

			BotJsonVO jsonVO = new BotJsonVO();
			BotDataVO dataVO = new BotJsonVO().new BotDataVO();
			dataVO.setUaGroupId(uaGroupId);
			dataVO.setOriginIP(ip);
			dataVO.setReqHost(domainName);
			dataVO.setStatus(status);
			dataVO.setUserAgent(userAgent);
			dataVO.setUserAgentStr(userAgent);
			dataVO.setReqPath(uri);
			dataVO.setVersion(version);
			
			//Leo - https://www.wrike.com/open.htm?id=186725979
			dataVO.setDate(FormatUtils.formatDate(time, "yyyy-MM-dd HH:mm:ss"));
			dataVO.setTimestamp(String.valueOf(timestamp));
			if (null != domainId && domainId > 0) {
				dataVO.setDomainId(domainId);
			}
			dataVO.setDisableTimeZone(true);

			jsonVO.setData(dataVO);
			return jsonVO;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Error line : "+line);
			return null;
		}
	}

	@Override
	protected String getLocalGzFilePath() {
		return localPath;
	}

	private String removeHostPotocal(String host) {
		return host.replaceAll("^http[s]://", "");
	}

	@Override
	protected boolean isMultiFile() {
		return true;
	}

}
