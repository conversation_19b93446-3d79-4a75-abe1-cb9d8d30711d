package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO.BotDataVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.Extract7135Part2BotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2018-12-04"
 */
@CommonsLog
public class Extract7135Part3BotData extends AbstractExtractBotCommonJsonFile {

    CSVFormat csvFormat = CSVFormat.DEFAULT;
    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static Integer version = 0;

    public Extract7135Part3BotData() {
        super();
        domainMaps.put(7135, "www.lowes.com");
        domainFileNameMap.put(7135, Arrays.asList("bottraffic_Node_CQ_report.%s"));
        fileNamePatten = "MM-dd-yyyy";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 7135P3, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, false);
        } else {
            bot.startProcess(false, true);
        }
    }

    private final SimpleDateFormat formatter = new SimpleDateFormat("dd/MMM/yyyy:HH:mm:ss", Locale.ENGLISH);

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return "/home/<USER>/source/botGroupUpload/clarity-backend-scripts/tmp";
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.containsIgnoreCase(line, "URL,ResponseCode,UserAgents,Timestamp")) {
            log.info("SKIP header line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String ip = "0.0.0.0";
            String dateStr = csvRecord.get(3);
            String uri = csvRecord.get(0);
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }
            String status = csvRecord.get(1);
            String userAgent = csvRecord.get(2);

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            Date time = formatter.parse(dateStr);
            long timestamp = time.getTime();

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + time + "\n" + timestamp);
                return null;
            }

            domainName = FormatUtils.getDomainByUrl(uri, false);
            uri = FormatUtils.getUriFromUrl(uri);

            BotJsonVO jsonVO = new BotJsonVO();
            BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);

            dataVO.setDate(FormatUtils.formatDate(time, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);

            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return true;
    }

}
