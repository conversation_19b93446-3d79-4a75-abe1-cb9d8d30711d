package com.actonia.subserver.tools.upload.newbot;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.text.ParseException;
import java.util.*;

/**
 * teepublic.com 11739 | Bot log integration
 * https://www.wrike.com/open.htm?id=1051206336
 * https://www.wrike.com/open.htm?id=1100092654 (new)
 * com.actonia.subserver.tools.upload.newbot.Extract11739BotData
 *      file: cloudflare/dt=20230426/hour=00/archive_000000.7754.qQkh6x7vQ56-jdAc3lPAXg.json.gz
 *          {"date":"2023-03-29T20:29:56.331Z","attributes":{"ClientRequestUserAgent":"Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)","ClientIPClass":"searchEngine","OriginResponseDurationMs":215,"ClientRequestBytes":2232,"ClientRequestURI":"/t-shirt/14488771-yellow-dragon-red?feed_sku=14488771D1V20G213A7C21S&feed_country=US&utm_source=google&utm_medium=shopping&utm_campaign=%5BG%5D+%5BG.NAM%5D+%5BL.ENG%5D+%5BGEN%5D+%5BC.WomensTShirts%5D+%5BPLF%5D&utm_id=notset&utm_content=mixed+martial+arts","EdgeStartTimestamp":1.680121796079E12,"network":{"bytes_written":78244,"client":{"geoip":{"continent":{"code":"NA","name":"North America"},"country":{"name":"United States","iso_code":"US"},"subdivision":{"name":"South Carolina"},"as":{"number":"AS15169","route":"***********/20","domain":"google.com","name":"Google LLC","type":"hosting"},"city":{"name":"Columbia"},"timezone":"America/New_York","ipAddress":"*************","location":{"latitude":34.00071,"longitude":-81.03481}},"ip":"*************","asn":15169}},"duration":2.52E8,"EdgeTimeToFirstByteMs":221,"RayID":"7afade2979c12094","EdgeServerIP":"*************","OriginDNSResponseTimeMs":0,"ParentRayID":"00","SecurityLevel":"med","OriginResponseStatus":200,"http":{"status_code":200,"url_details":{"path":"/t-shirt/14488771-yellow-dragon-red","host":"www.teepublic.com","queryString":{"utm_id":"notset","utm_campaign":"%5BG%5D+%5BG.NAM%5D+%5BL.ENG%5D+%5BGEN%5D+%5BC.WomensTShirts%5D+%5BPLF%5D","utm_medium":"shopping","feed_sku":"14488771D1V20G213A7C21S","feed_country":"US","utm_source":"google","utm_content":"mixed+martial+arts"}},"method":"GET","status_category":"OK","status_class":"2xx","useragent_details":{"os":{"family":"Other"},"browser":{"major":"2","minor":"1","family":"Googlebot"},"device":{"model":"Desktop","family":"Spider","category":"Bot","brand":"Spider"}}},"CacheCacheStatus":"dynamic","EdgeEndTimestamp":1.680121796331E12},"_id":"AYcvEKS1AABFeu-1Ao4PzwKg","source":"cloudflare","status":"ok","tags":["source:cloudflare","env:production"]}
 */
@CommonsLog
public class Extract11739BotData extends AbstractExtractBotCommonJsonFile {

    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static String domain = "www.teepublic.com";

    private static Integer version = 0;

    public Extract11739BotData() {
        super();

        s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
        s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
        s3BusketName = "tp-seo-clarity-logs-production";

        domainMaps.put(11739, domain);
        domainFileNameMap.put(11739, Collections.singletonList("cloudflare/dt=%s"));
        fileNamePatten = "yyyyMMdd";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;

        /**
         * Data processing needs to start after 8pm daily.
         *
         * 20230218/20230218T000001Z_20230218T000031Z_5fe71648.log.gz => last mdfTime: 2023-02-18 08:00:32
         * 20230218/20230218T235958Z_20230219T000028Z_e2957358.log.gz => last mdfTime: 2023-02-19 08:00:21
         */
        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 11739, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected boolean isDownloadFromS3() {
        return true;
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return true;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        try {
            JSONObject jsonObject = JSONObject.parseObject(line);
            JSONObject attributes = jsonObject.getJSONObject("attributes");

            String userAgent = attributes.getString("ClientRequestUserAgent");
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            int status = attributes.getIntValue("OriginResponseStatus");
            if (status == 0) {
                return null;
            }

            String ip = "0.0.0.0";
            String dateStr = jsonObject.getString("date");
            Date date = DateUtils.parseDate(dateStr, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            long timestamp = date.getTime();

            String uri = attributes.getString("ClientRequestURI");
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(String.valueOf(status)) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(dateStr)) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp+ "\n" + domainName+ "\n");
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(String.valueOf(status));
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setUseFileContextDate(true);
            dataVO.setDomainId(domainId);
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (ParseException e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            errorCnt++;
        }
        return null;
    }

    protected boolean isFolder() {
        return true;
    }
}
