package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;


/**
 * com.actonia.subserver.tools.upload.newbot.Extract5671BotData
 *
 * ewain 20220824
 *
 *          example:
 *                 "2022-08-20    /api/SearchKeyword    200    Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/104.0.5112.79 Safari/537.36",
 *                 "2022-08-20    /api/SearchKeyword    200    Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/104.0.5112.79 Safari/537.36",
 *                 "2022-08-20    /api/AutoFilterKeyword    200    Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/104.0.5112.79 Safari/537.36",
 *                 "2022-08-20    /amp/Microphones/SubCategory/ID-122    200    Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, likeGecko) Chrome/104.0.5112.79 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
 *                 "2022-08-20    /p/2S7-07JP-286R7    200    Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)"
 */
@CommonsLog
public class Extract5671BotData extends AbstractExtractBotCommonJsonFile {

    private static final SimpleDateFormat dataFormatter = new SimpleDateFormat("yyyy-MM-dd");

    private static String domain = "www.newegg.com";
    private static int domainId = 5671;
    private static Integer version = 0;

    private static String localPath = "/opt/bot/";
    private static Date logDate;

    public Extract5671BotData() {
        super();
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        domainMaps.put(5671, domain);
        domainFileNameMap.put(5671, Arrays.asList("neweggcom_bot_%s.txt"));
        fileNamePatten = "yyyy-MM-dd";

        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 5671, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            LocalTime now = LocalTime.now();
            if (now.getHour() < 4) {
                return;
            }
            bot.startProcess(false, true);
        }
    }


    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId;
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {

        try {
            String[] columnArr = line.split("    ");

            String ip = "0.0.0.0";
            String dateStr = columnArr[0];
            Date date = dataFormatter.parse(dateStr);
            String status = columnArr[2];
            String userAgent = columnArr[3];
            String uri = columnArr[1];
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }
            long timestamp = date.getTime();
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setDomainId(domainId);
            dataVO.setDisableTimeZone(true);
            dataVO.setUseFileContextDate(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (ParseException e) {
            log.error("error line: " + line);
            return null;
        }

    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return false;
    }

}
