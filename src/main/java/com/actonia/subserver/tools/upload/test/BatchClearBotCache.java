package com.actonia.subserver.tools.upload.test;


import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class BatchClearBotCache {

    private static final List<Integer> domains1909 = Arrays.asList(13046,12914,13067,13059,13095,13054,13099,13082,13109,13065,13092,10050,13108,13070,12967,12952,13047,12985,10020,12942,13043,13018,12956,12921,12927,12994,12894,12997,12898,13035,13022,13020,12971,12660,12972,12944,12906,10680,13033,10679,12933,13012,12937,12945,13006,12904,12926,12907,12953,12966,13036,13004,12911,12913,13034,13015,12943,12999,12987,13055,13063,13085,13079,13075,13081,13061,13104,13060,13087,10113,13073,10045,2221,8987,2219,13083,10352,10059,10049,10221,4354,13050,5063,10913,10914,5725,10912,10558,13001,12948,13005,12912,13007,12932,13014,12950,12963,13037,12916,13097,13100,13111,13076,13072,13090,2327,12938,10028,10487,12902,12893,2323,2213,10019,2318,2317,10034,12081,10039,10038,2169,2311,2211,13098,10488,12918,2210,13089,2209,2208,13102,13096,10559,10023,2363,2671,13051,2299,9009,5097,5919,11315,2205,13078,2204,2202,2358,10058,2201,2200,2199,10068,12080,2288,2287,10036,10035,5255,2281,12901,10560,2198,2197,2276,2196,13008,2195,10030,2194,2270,2269,2268,2193,2212,12922,13080,2264,2338,2191,10714,2260,10024,2190,2189,5635,2188,10031,10026,12079,10037,4711,2185,2394,10047,13071,5098,10017,12979,12947,12919,12934,12977,12930,12959,12925,2181,10025,13040,13019,12924,12905,12968,13013,12931,12900,13002,12892,13025,12957,13038,12988,12955,13091,13023,13044,13030,12981,12940,12965,12982,13003,12909,12929,13031,12983,13039,12910,12973,12960,12978,13009,12920,12996,12951,12980,12992,12941,13062,10015,13010,12908,12917,13028,12998,13041,12970,13088,10489,13017,12964,13027,13049,13066,13053,10046,13086,13101,9633,13069,13064,11382,13052,10055,13094,13032,13024,12928,12962,12991,12975,12946,12923,12899,12993,12936,12939,12995,13029,12958,13016,12974,13045,13026,13000,12935,12903,12986,13021,13048,12989,13084,10222,12174,12954,12969,12976,13068,10563,10219,12990,10021);
    private static final List<Integer> domainsExpedia = Arrays.asList(550,551,550,551,522,593,560,561,553,555,552,562,558,554,898,557,556,1180,5085,5084,4754,4746,4762,4749,4741,4742,4728,5070,5071,4727,4730,4738,4739,4765,4753,4744,4747,4764,4734,4735,4737,4740,4763,4750,4736,4751,4752,4743,4761,4755,4729,4733,4756,5157,4732,4745,4748,4758,4731,3458,1508,5903,5461,9275,9276,9279,9286,9287,9295,9296,9297,9298,9299,9300,9301,9303,9304,9305,9306,9307,9308,9309,9331,9332,9281,9283,9311,9277,8644,9290,9294,9271,9288,8649,8650,9529,8743,9522,8645,8647,8744,8648,5671,5672,5673,5674,5675,5676,5677,5678,5679,5680,5681,10666,12830,12287,12288,12839,12822,12823,12824,12825,12826,12827,12828,12829,12832,12831,9333,9278,8646,8651,9523,9524,9525,9526,9527,9528,9532,9534,9535,9536,9537,9538,9540,9541,9543,12286,12835,12836);
    private static final String CACHE_SERVICE_URL = "https://s11-dev.seoclarity.dev/seoClarity/cacheService/cleanCache?access_token=c09yxv13-opr3-d745-9734-8pu48420nj67&type=bot&oid=";
    private static final String SUMMARY_SERVICE_URL = "https://s11-dev.seoclarity.dev/seoClarity/cacheService/cleanSummaryCache?access_token=c09yxv13-opr3-d745-9734-8pu48420nj67&type=bot&oid=";

    public static void main(String[] args) {
        BatchClearBotCache batchClearBotCache = new BatchClearBotCache();
        batchClearBotCache.process(1);
//        batchClearBotCache.process(2);
    }

    /**
     *
     * @param processType 0: 1909 bot domain 1: expedia domain
     */
    private void process(int processType) {
        if (processType == 0) {
            startProcess(domains1909);
        } else if (processType == 1) {
            startProcess(domainsExpedia);
        }

    }

    private void startProcess(List<Integer> oids) {
        // Create HttpClient
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            for (int oid : oids) {
                // Construct URLs
                String cacheUrl = CACHE_SERVICE_URL + oid;
                String summaryUrl = SUMMARY_SERVICE_URL + oid;

                // Send requests
                sendRequest(client, cacheUrl);
                sendRequest(client, summaryUrl);
                System.out.println("===========================");
                try {
                    TimeUnit.MILLISECONDS.sleep(300);
                }catch (Exception ee) {
                    ee.printStackTrace();
                }

            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void sendRequest(CloseableHttpClient client, String url) {
        HttpGet request = new HttpGet(url);

        try (CloseableHttpResponse response = client.execute(request)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // Print response (for debugging purposes)
                String result = EntityUtils.toString(entity);
                System.out.println("Response for URL " + url + ": " + response.getStatusLine().getStatusCode());
                System.out.println(result);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}


