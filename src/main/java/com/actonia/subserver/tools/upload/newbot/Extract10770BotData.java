package com.actonia.subserver.tools.upload.newbot;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.S3Utils;

import java.text.ParseException;
import java.util.*;

/**
 * teepublic.com 10770 | Bot log integration
 * https://www.wrike.com/open.htm?id=1621161330
 * com.actonia.subserver.tools.upload.newbot.Extract10770BotData
 *          {"UA":"Mozilla/5.0%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko);%20compatible;%20ChatGPT-User/1.0;%20+https://openai.com/bot","bytes":"59437","cliIP":"**************","country":"US","proto":"HTTPS/1.1","queryStr":"pageName=PGP\u0026categoryId=42904\u0026categoryNameFromURL=Dress+shirts\u0026resolvedUrl=%2Fc%2Fmens-clothing%2Fdress-shirts%2Ff%2Ffit%3Dexecutive-fit%3Fsrsltid%3DAfmBOoo4-A0RA3qGiinV0POCBYhWOO7_n9nRetC5Js0g4RodkfWq-rao","referer":"www.menswearhouse.com/c/mens-clothing/dress-shirts/f/fit=executive-fit?srsltid=AfmBOoo4-A0RA3qGiinV0POCBYhWOO7_n9nRetC5Js0g4RodkfWq-rao","reqHost":"www.menswearhouse.com","reqMethod":"GET","reqPath":"api/v1/pagecomposer/page","reqPort":"443","reqTimeSec":"1744833088.996","state":"Virginia","statusCode":"200","version":"2"}
 */
@CommonsLog
public class Extract10770BotData extends AbstractExtractBotCommonJsonFile {

    private static final long ONE_HOUR = 1000 * 60 * 60;
    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static String domain = "www.mooresclothing.ca";
    private static int domainId = 10770;
    private static String split = "!_!";

    private static Integer version = 0;
    private static String roleArn = "arn:aws:iam::222481505021:role/TB-AWS-Akamai-SEOClarity-Prod";
    private static String roleSessionName = "mooresclothing-session-name";
    private static String externalId = "seoclarityDev-externalId-assumeRole";

    private Map<String, String> s3KeyPTimeMap = new HashMap<>(); // key: s3 key type name， value: key!_!timestamp
    private Map<String, Integer> domainNamePOidMap = new HashMap<>();

    public Extract10770BotData() {
        super();

        long timestamp = System.currentTimeMillis();
        String keyStr = S3Utils.getTmpKeyByAssumeRole(
                AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey(),
                AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey(),
                roleArn,
                roleSessionName,
                externalId);

        System.out.println("==getKeyInfo domainId:" + domainId + " domain:" + domain + " keyStr:" + keyStr);

        if (StringUtils.isEmpty(keyStr)) {
            System.out.println("==emptyS3Key domainId:" + domainId + " domain:" + domain);
            throw new RuntimeException("==specialS3InitError emptyS3Key domainId:" + domainId + " domain:" + domain);
        }

        String[] keyArr = keyStr.split(split);
        s3AccessKey = keyArr[0];
        s3SecretKey = keyArr[1];
        s3Token = keyArr[2];

        s3KeyPTimeMap.put("accessKey", s3AccessKey + split + timestamp);
        s3KeyPTimeMap.put("secretKey", s3SecretKey + split + timestamp);
        s3KeyPTimeMap.put("token", s3Token + split + timestamp);

        s3BusketName = "akamai-bot-logs-prod";

        domainMaps.put(domainId, domain);
        domainFileNameMap.put(domainId, Collections.singletonList("logs/%s"));
        fileNamePatten = "yyyy/MM/dd";

        domainNamePOidMap.put(domain, domainId);
        domainNamePOidMap.put("www.josbank.com", 9374);
        domainNamePOidMap.put("www.menswearhouse.com", 9376);

    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 11739, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected boolean isDownloadFromS3() {
        return true;
    }

    @Override
    protected boolean isSpecialS3() {
        return true;
    }

    @Override
    protected void getLatestS3Key() throws Exception {
        if (s3KeyPTimeMap.containsKey("accessKey")
                && s3KeyPTimeMap.containsKey("secretKey")
                && s3KeyPTimeMap.containsKey("token")) {
            String keyStr = s3KeyPTimeMap.get("accessKey");
            long timestamp = Long.parseLong(keyStr.split(split)[1].trim());

            if (System.currentTimeMillis() - timestamp >= ONE_HOUR) {
                System.out.println("==tokenExpired domainId:" + domainId + " domain:" + domain + " will gen new token");
                genS3Key();
            } else {
                System.out.println("==tokenNotExpired domainId:" + domainId + " domain:" + domain + " accessKey:" + s3AccessKey + " secretKey:" + s3SecretKey + " token:" + s3Token);
            }
        } else {
            genS3Key();
        }
    }

    private void genS3Key() throws Exception {
        long timestamp = System.currentTimeMillis();
        String keyInfoStr = S3Utils.getTmpKeyByAssumeRole(
                AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey(),
                AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey(),
                roleArn,
                roleSessionName,
                externalId);

        if (StringUtils.isEmpty(keyInfoStr)) {
            throw new Exception("==genS3KeyFail domainId:" + domainId + " domain:" + domain);
        }

        String[] keyArr = keyInfoStr.split(split);
        s3AccessKey = keyArr[0];
        s3SecretKey = keyArr[1];
        s3Token = keyArr[2];

        s3KeyPTimeMap.put("accessKey", s3AccessKey + split + timestamp);
        s3KeyPTimeMap.put("secretKey", s3SecretKey + split + timestamp);
        s3KeyPTimeMap.put("token", s3Token + split + timestamp);
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isFolder() {
        return true;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        try {
            JSONObject jsonObject = JSONUtil.parseObj(line);

            String tmpDomainName = jsonObject.getStr("reqHost");
            if (!domainNamePOidMap.containsKey(tmpDomainName)) {
                return null;
            }

            String userAgent = FormatUtils.decodeAndEscapeHtml(jsonObject.getStr("UA"));
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String url = jsonObject.getStr("referer");
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }
            String uri = FormatUtils.getUriFromUrl(url);

            String status = jsonObject.getStr("statusCode");

            String ip = jsonObject.getStr("cliIP");
            String timestamp = StringUtils.replace(jsonObject.getStr("reqTimeSec"), ".", "");
            Date date = new Date(Long.parseLong(timestamp));

            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }

            if (StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(timestamp) ||
                    StringUtils.isEmpty(tmpDomainName)) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp+ "\n" + domainName+ "\n");
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(tmpDomainName);
            dataVO.setDomainId(domainNamePOidMap.get(tmpDomainName));
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(timestamp);
            dataVO.setUseFileContextDate(true);
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            errorCnt++;
        }
        return null;
    }
}
