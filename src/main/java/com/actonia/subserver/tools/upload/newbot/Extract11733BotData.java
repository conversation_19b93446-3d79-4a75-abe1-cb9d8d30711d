package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.util.*;

/**
 * yelp.com 11733
 * https://www.wrike.com/open.htm?id=1145008428
 * com.actonia.subserver.tools.upload.newbot.Extract11733BotData
 *      file:
 *          10.65.112.79 - - [29/May/2023:23:59:55 -0700] "GET /biz/capistrano-dental-group-san-juan-capistrano-8 HTTP/1.1" 200 1025892 "-" "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)" 29057 www.yelp.com 80 3595324 66a861f99fd4e977 c9c716163bfb041c 1685429995688167
 */
@CommonsLog
public class Extract11733BotData extends AbstractExtractBotCommonJsonFile {

    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static String domain = "www.yelp.com";

    private static String pattern = "dd/MM/yyyy:HH:mm:ss";

    private static Integer version = 0;

    public Extract11733BotData() {
        super();
        domainMaps.put(11733, domain);
        domainFileNameMap.put(11733, Collections.singletonList("googlebot_logs_%s.txt"));
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;

        /**
         * Data processing needs to start after 8pm daily.
         *
         * 20230218/20230218T000001Z_20230218T000031Z_5fe71648.log.gz => last mdfTime: 2023-02-18 08:00:32
         * 20230218/20230218T235958Z_20230219T000028Z_e2957358.log.gz => last mdfTime: 2023-02-19 08:00:21
         */
        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 11733, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        try {
            if (StringUtils.isEmpty(line) || StringUtils.isBlank(line)) {
                return null;
            }

            String[] dataArr = line.split("\"");
            String userAgent = dataArr[5].trim();
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String[] ipDateArr = dataArr[0].trim().split(" ");
            String ip = ipDateArr[0].trim();

            String dateStr = getDateStr(ipDateArr[3].substring(1).trim());
            Date date = DateUtils.parseDate(dateStr, pattern);
            long timestamp = date.getTime();

            String[] urlArr = dataArr[1].trim().split(" ");
            String uri = urlArr[1].trim();
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }

            String[] statusArr = dataArr[2].trim().split(" ");
            String status = statusArr[0].trim();

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(dateStr)) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp+ "\n" + domainName+ "\n");
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setUseFileContextDate(true);
            dataVO.setDomainId(domainId);
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (ParseException e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            errorCnt++;
        }
        return null;
    }


    public static String getMonthNumber(String abbreviation) {
        switch (abbreviation.toLowerCase()) {
            case "jan":
                return "01";
            case "feb":
                return "02";
            case "mar":
                return "03";
            case "apr":
                return "04";
            case "may":
                return "05";
            case "jun":
                return "06";
            case "jul":
                return "07";
            case "aug":
                return "08";
            case "sep":
                return "09";
            case "oct":
                return "10";
            case "nov":
                return "11";
            case "dec":
                return "12";
            default:
                throw new IllegalArgumentException("Invalid month abbreviation: " + abbreviation);
        }
    }

    public static String getDateStr(String abbreviation) {
        String result = "";
        String[] split = abbreviation.split("/");
        String monthNumber = getMonthNumber(split[1].toLowerCase());
        result = split[0] + "/" + monthNumber + "/" + split[2];
        return result;
    }

}
