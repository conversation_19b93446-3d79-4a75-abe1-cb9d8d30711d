package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * com.actonia.subserver.tools.upload.newbot.Extract5303BotData
 * example:
 *  66.249.79.93	-	07/Jul/2022:00:00:02 +0900	GET /books/86296/reviews?page=251&sort=helpful HTTP/1.1	200	23525	-	Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.53 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)	66.249.79.93	1.592
 *  66.249.79.160	-	07/Jul/2022:00:00:05 +0900	GET /free/mechamaga/tags/columns?page=3 HTTP/1.1	301	162	-	Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)	66.249.79.160	0.000
 */
@CommonsLog
public class Extract5303BotData extends AbstractExtractBotCommonJsonFile {

    private static final CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',');
    private static final SimpleDateFormat dataFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static String domain = "mechacomic.jp";
    private static int domainId = 5303;
    private static Integer version = 0;

    private static String localPath = "/opt/bot/";
    private static Date logDate;

    public Extract5303BotData() {
        super();
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        domainMaps.put(5303, domain);
        domainFileNameMap.put(5303, Arrays.asList("comics_googlebot_access_logs_comics_googlebot_access_logs_%s"));
        fileNamePatten = "yyyyMMdd";

        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 5303, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }


    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) {
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error!");
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String type = csvRecord.get(0);
            if (!StringUtils.equalsIgnoreCase(type, "h2")) {
                return null;
            }

            String userAgent = csvRecord.get(13);
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String url = csvRecord.get(12).split(" ")[1];
            url = Extract2466BotData.removePortFromUrl(url);
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }

            String dateStr = csvRecord.get(1).split("\\.")[0].trim();
            Date date = dataFormatter.parse(dateStr);

            String ip = csvRecord.get(3).split(":")[0];

            String status = csvRecord.get(9);

            String uri = FormatUtils.getUriFromUrl(url);
            long timestamp = date.getTime();


            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setDomainId(domainId);
            dataVO.setDisableTimeZone(true);
            dataVO.setUseFileContextDate(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            try {
                Thread.sleep(5);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            log.error("Error line : " + line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return false;
    }

}
