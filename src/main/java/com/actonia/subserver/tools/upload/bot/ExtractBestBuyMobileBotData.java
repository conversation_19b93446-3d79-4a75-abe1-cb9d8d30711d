package com.actonia.subserver.tools.upload.bot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.ExtractBestBuyMobileBotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2017-11-21"
 */
@CommonsLog
public class ExtractBestBuyMobileBotData extends AbstractExtractBotCommonJsonFile {

	private final String IP_PATTERN = "^(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
	private final String DATE_PATTERN = "\\[.*\\]";
	private static final String URL_PATTERN = " \"(-|((https|http|ftp|rtsp|mms)?:\\/\\/)[^\\s]+)\" ";
	private static final String URI_PATTERN = "(GET |POST |HEAD |PUT |OPTIONS |DELETE)[^\\s]{0,} ";
	private static final String RM_URI_PATTERN = "^POST|^GET|^HEAD|^PUT|^OPTIONS|^DELETE";
	private final String STATUS_PATTERN = " \\d{3} ";
	private static final String USER_AGENT_PATTERN = " \\d{3} (\\d+|-)"+URL_PATTERN+"\"([^\"]{0,}|-)\"";
	private static final String RM_USER_AGENT_PATTERN = " \\d{3} (\\d+|-)"+URL_PATTERN;
	private static final SimpleDateFormat formatter = new SimpleDateFormat("dd/MMM/yyyy:hh:mm:ss", Locale.ENGLISH);

	private static Date processDate = new Date();
	private static Date logDate;
	private static String localPath = "/opt/bot/";


	private static BotDetailDao botDetailDao;

	public ExtractBestBuyMobileBotData() {
		super();
		domainMaps.put(6457, "www.bestbuy.ca");
//		domainFileNameMap.put(6457, "Mdot_GoogleBots_786548.esclf_S.%s0000-2400-0.gz");
//		domainFileNameMap.put(6457, Arrays.asList("Mdot_GoogleBots_786548.esclf_S.%s0000-%s0700-%s-99.99c-0.gz"));

		List<String> fileNames = new ArrayList<>();
		for (int i = 0; i < 23; i++) {
			fileNames.add("Mdot_GoogleBots_786548.esclf_S.%s0000-%s0"+i+"00-%s-99.99c-0.gz");
			fileNames.add("Mdot_GoogleBots_786548.esclf_S.%s0000-%s0"+i+"00-%s-99.99c-1.gz");
			fileNames.add("Mdot_GoogleBots_786548.esclf_S.%s0000-%s0"+i+"00-%s-99.99c-2.gz");
		}
		domainFileNameMap.put(6457, fileNames);
	}

	public static void main(String[] args) {
//		String uri = getUserAgent("66.249.73.7 - - [07/Dec/2018:04:05:16 +0000] \"GET /bbc-ecommwebapp-prod2.trafficmanager.net/en-ca/product/aaxa-aaxa-led-pico-projector-kp-101-01-kp-101-01/10288222 HTTP/1.1\" 200 25486 \"-\" \"Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.96 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)\" \"-\"");
//
//		System.out.println(uri);
//		System.exit(1);
		ExtractBestBuyMobileBotData extractMechaBotData = new ExtractBestBuyMobileBotData();
		if (args != null & args.length > 0) {
			if(StringUtils.containsIgnoreCase(args[0], ",")) {
				Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
				Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
				if (args.length > 1) {
					localPath = args[1];
				}
				while(sDate.compareTo(eDate) <= 0) {
					logDate = sDate;
					processDate = logDate;
					extractMechaBotData.startProcess(true, false);
					sDate = DateUtils.addDays(sDate, 1);
				}
			} else {
				logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
				processDate = logDate;
				if (args.length > 1) {
					localPath = args[1];
				}
				extractMechaBotData.startProcess(true, false);
			}
			extractMechaBotData.waitForThreadPool();
		} else {
            logDate = DateUtils.addDays(new Date(), -1);
			processDate = logDate;
			extractMechaBotData.startProcess(true);
        }
	}

	@Override
	protected boolean isMultiFile() {
		return true;
	}

	@Override
	protected Date getBotLogDate() {
		return logDate;
	}

	@Override
	protected String getRemoteFilePath(int domainId) {
		return "/home/<USER>/"+domainId+"/logs/Microsite";
	}

	@Override
	protected List<String> getRemoteFileNames(int domainId, Date logDate) {
		List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
		String date1 = FormatUtils.formatDate(logDate, "yyyyMMdd");
		String date2 = FormatUtils.formatDate(DateUtils.addDays(logDate, 1), "yyyyMMdd");
		for (String fileName : domainFileNameMap.get(domainId)) {
			fileNames.add(String.format(fileName, date1, date2, date1));
		}
		return fileNames;
	}

	@Override
	protected String getLocalFilePath() {
		return "/tmp/";
	}

	@Override
	protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException {

		String uri = getUri(line);
		if (StringUtils.isBlank(uri) || StringUtils.equalsIgnoreCase(uri, "-")) {
			log.error("error getting uri : "+line);
			return null;
		}
		if (StringUtils.startsWithIgnoreCase(uri, "/")) {
			uri = StringUtils.removeStartIgnoreCase(uri, "/");
			uri = "/"+StringUtils.substringAfter(uri, "/").trim();
		}
		if (UrlFilterUtil.shouldSkipUrl(uri)) {
			return null;
		}

		String ip = getValueByPattern(IP_PATTERN, line).trim();
		String date = getValueByPattern(DATE_PATTERN, line).trim().replaceAll("\\[|]","");
		String status = getValueByPattern(STATUS_PATTERN, line).trim();
		String userAgent = getUserAgent(line);

		int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
		if (uaGroupId < 0) {
			return null;
		}

		date = StringUtils.split(date, " ")[0];
		Date time = formatter.parse(date);
		long timestamp = time.getTime();

		if(StringUtils.isEmpty(ip) ||
				StringUtils.isEmpty(domainName) ||
				StringUtils.isEmpty(status) ||
				StringUtils.isEmpty(userAgent) ||
				StringUtils.isEmpty(uri) ||
				StringUtils.isEmpty(String.valueOf(timestamp))) {
			System.out.println(line);
			System.out.println(ip+"\n"+date+"\n"+uri+"\n"+status+"\n"+userAgent+"\n"+time+"\n"+timestamp);
			return null;
		}

		BotJsonVO jsonVO = new BotJsonVO();
		BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
		dataVO.setUaGroupId(uaGroupId);
		dataVO.setOriginIP(ip);
		dataVO.setReqHost(domainName);
		dataVO.setStatus(status);
		dataVO.setUserAgent(userAgent);
		dataVO.setUserAgentStr(userAgent);
		dataVO.setReqPath(uri);
		//Leo - https://www.wrike.com/open.htm?id=186725979
		dataVO.setDate(FormatUtils.formatDate(time, "yyyy-MM-dd HH:mm:ss"));
		dataVO.setTimestamp(String.valueOf(timestamp));
		if (null != domainId && domainId > 0) {
			dataVO.setDomainId(domainId);
		}
		dataVO.setDisableTimeZone(true);

		jsonVO.setData(dataVO);

		return jsonVO;
	}

	@Override
	protected String getLocalGzFilePath() {
		return localPath;
	}

//	private static String getUri(String str) {
//		String uri = getValueByPattern(URL_PATTERN, str);
//		if(StringUtils.isNotEmpty(uri)) {
//			return uri.replaceAll("\"", "").trim();
//		}
//		return uri;
//	}
	private static String getUri(String str) {
		String uri = getValueByPattern(URI_PATTERN, str);
		if(StringUtils.isNotEmpty(uri)) {
			return uri.replaceAll(RM_URI_PATTERN, "").trim();
		}
		return uri;
	}

	private static String getUserAgent(String str) {
		String agent = getValueByPattern(USER_AGENT_PATTERN, str);
		if(StringUtils.isNotEmpty(agent)) {
			return agent.replaceAll(RM_USER_AGENT_PATTERN, "").replaceAll("\"", "").trim();
		}
		return agent;
	}

	private static String getValueByPattern(String pattern, String str) {
		Pattern r = Pattern.compile(pattern);
		Matcher m = r.matcher(str);
		while (m.find()) {
			return m.group();
		}
		return null;
	}
}
