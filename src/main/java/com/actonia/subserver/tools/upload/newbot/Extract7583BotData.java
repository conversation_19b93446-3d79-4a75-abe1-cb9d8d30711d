package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * www.healthgrades.com - 7583
 * https://www.wrike.com/open.htm?id=**********
 * com.actonia.subserver.tools.upload.newbot.Extract7583BotData
 *      file: www.healthgrades.com.bot-logs.2023-05-08.gz
 *         head: date time x-edge-location sc-bytes c-ip cs-method cs(Host) cs-uri-stem sc-status cs(Referer) cs(User-Agent) cs-uri-query cs(Cookie) x-edge-result-type x-edge-request-id x-host-header cs-protocol cs-bytes time-taken x-forwarded-for ssl-protocol ssl-cipher x-edge-response-result-type cs-protocol-version fle-status fle-encrypted-fields c-port time-to-first-byte x-edge-detailed-result-type sc-content-type sc-content-len sc-range-start sc-range-end
 *         row:  2023-05-08	00:00:00	ATL58-P1	947	***********	GET	d2pt7lj7tmjga9.cloudfront.net	/api4/ProviderProfile/ProviderStrengths	200	https://www.healthgrades.com/providers/ahmed-mohyeldin-4xn36	Mozilla/5.0%20(Linux;%20Android%206.0.1;%20Nexus%205X%20Build/MMB29P)%20AppleWebKit/537.36%20(KHTML,%20like%20Gecko)%20Chrome/112.0.5615.142%20Mobile%20Safari/537.36%20(compatible;%20Googlebot/2.1;%20+http://www.google.com/bot.html)	pwids=yrjv9,ypqyx,9866z,xsm6k,xmrqx&items=1	-	Miss	juVetIzjN8YVvCaVL-FAaNiCw1BXoUWbeMND-FGl50hMy7dzHwKGJQ==	www.healthgrades.com	https	2344	0.280	-	TLSv1.3	TLS_AES_128_GCM_SHA256	Miss	HTTP/2.0	-	-	40509	0.280	Miss	application/json;%20charset=utf-8	-	-	-
 */
@CommonsLog
public class Extract7583BotData extends AbstractExtractBotCommonJsonFile {

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true);

    private static final SimpleDateFormat dataDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static String domain = "www.healthgrades.com";

    private static Integer version = 0;

    public Extract7583BotData() {
        super();

        s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
        s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
        s3BusketName = "hgw-prod-bot-cf-logs";

        domainMaps.put(7583, domain);
        domainFileNameMap.put(7583, Collections.singletonList("www.healthgrades.com.bot-logs.%s"));
        fileNamePatten = "yyyy-MM-dd";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;

        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 7583, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected boolean isDownloadFromS3() {
        return true;
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.containsIgnoreCase(line, "#Version:")
                || StringUtils.containsIgnoreCase(line, "#Fields:")) {
            log.info("SKIP header line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }

            CSVRecord csvRecord = csvRecords.get(0);

            String userAgent = csvRecord.get(10);

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String url = csvRecord.get(9);
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }
            String date = csvRecord.get(0).trim();
            String time = csvRecord.get(1).trim();
            String dateStr = date + " " + time;
            Date datetime = dataDateFormatter.parse(dateStr);

            String ip = csvRecord.get(4);

            String status = csvRecord.get(8);

            String hostName = csvRecord.get(15);
            if (StringUtils.equalsIgnoreCase(hostName, "-")) {
                domainName = hostName;
            }


            String uri = FormatUtils.getUriFromUrl(url);

            long timestamp = datetime.getTime();
            String reqProtocol = FormatUtils.getProtocolFromUrl(url);

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.equalsIgnoreCase(status, "0") ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.endsWithIgnoreCase(userAgent, "-") ||
                    StringUtils.isEmpty(url) ||
                    StringUtils.endsWithIgnoreCase(url,"-") ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + domainName + "\n" + dateStr + "\n"
//                        + url + "\n" + status + "\n" + userAgent + "\n" + datetime + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(dateStr);
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setReqProtocol(reqProtocol);

            dataVO.setUseFileContextDate(true);

            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            return null;
        }
    }
}
