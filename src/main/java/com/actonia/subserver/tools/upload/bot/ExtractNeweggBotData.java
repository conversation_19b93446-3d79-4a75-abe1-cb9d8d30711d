package com.actonia.subserver.tools.upload.bot;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.ExtractNeweggBotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2017-11-21"
 */
public class ExtractNeweggBotData extends AbstractExtractBotCommonJsonFile {

	private static Date processDate = new Date();
	private static Date logDate;
	private static String localPath = "/opt/bot/";

	public ExtractNeweggBotData() {
		super();
		domainMaps.put(5671, "www.newegg.com");
		domainMaps.put(5672, "m.newegg.com");
		domainMaps.put(5673, "promotions.newegg.com");
		domainMaps.put(5674, "flash.newegg.com");
		domainMaps.put(5675, "kb.newegg.com");
		domainMaps.put(5676, "community.newegg.com");
		domainMaps.put(5677, "blog.newegg.com");
		domainMaps.put(5678, "www.gamecrate.com");
		domainMaps.put(5679, "unlocked.newegg.com");
		domainMaps.put(5680, "www.neweggbusiness.com");
		domainMaps.put(5681, "blog.neweggbusiness.com");
//		domainMaps.put(5682, "www.newegg.ca");
//		domainMaps.put(5683, "www.newegg.com");
//		domainMaps.put(5684, "www.newegg.com");

		domainFileNameMap.put(5671, Arrays.asList("www.newegg.com-B2C-%s-bot-new2.csv"));
		domainFileNameMap.put(5672, Arrays.asList("m.newegg.com-M.B2C-%s-bot-new2.csv"));
		domainFileNameMap.put(5673, Arrays.asList("promotions.newegg.com-Promo\\ B2C-%s-bot-new2.csv"));
		domainFileNameMap.put(5674, Arrays.asList("flash.newegg.com-Flash-%s-bot-new2.csv"));
		domainFileNameMap.put(5675, Arrays.asList("kb.newegg.com-KB-%s-bot-new2.csv"));
		domainFileNameMap.put(5676, Arrays.asList("community.newegg.com-Community-%s-bot-new2.csv"));
		domainFileNameMap.put(5677, Arrays.asList("blog.newegg.com-B2C\\ Blog-%s-bot-new2.csv"));
		domainFileNameMap.put(5678, Arrays.asList("www.gamecrate.com-Gamecrate-%s-bot-new2.csv"));
		domainFileNameMap.put(5679, Arrays.asList("unlocked.newegg.com-Unlocked-%s-bot-new2.csv"));
		domainFileNameMap.put(5680, Arrays.asList("www.neweggbusiness.com-B2B-%s-bot-new2.csv"));
		domainFileNameMap.put(5681, Arrays.asList("blog.neweggbusiness.com-B2B\\ Blog-%s-bot-new2.csv"));
		domainFileNameMap.put(5682, Arrays.asList("www.newegg.ca-NE\\ Canada-%s-bot-new2.csv"));
		domainFileNameMap.put(5683, Arrays.asList("www.newegg.com-B2C\\ UK-%s-bot-new2.csv"));
		domainFileNameMap.put(5684, Arrays.asList("www.newegg.com-B2C\\ AU-%s-bot-new2.csv"));

	}

	public static void main(String[] args) throws Exception {

		ExtractNeweggBotData walgreensBotData = new ExtractNeweggBotData();
		if (args != null & args.length > 0) {
			if(StringUtils.containsIgnoreCase(args[0], ",")) {
				Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
				Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
				if (args.length > 1) {
					localPath = args[1];
				}
				while(sDate.compareTo(eDate) <= 0) {
					logDate = sDate;
					processDate = logDate;
					walgreensBotData.startProcess(true, false);
					sDate = DateUtils.addDays(sDate, 1);
				}
			} else {
				logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
				processDate = logDate;
				if (args.length > 1) {
					localPath = args[1];
				}
				walgreensBotData.startProcess(true, false);
			}
			walgreensBotData.waitForThreadPool();
		} else {
            logDate = DateUtils.addDays(new Date(), -1);
			processDate = logDate;
			walgreensBotData.startProcess(false);
        }
	}

	@Override
	protected Date getBotLogDate() {
		return logDate;
	}

	@Override
	protected String getRemoteFilePath(int domainId) {
		return "/home/<USER>/"+domainId;
	}

	@Override
	protected List<String> getRemoteFileNames(int domainId, Date logDate) {
		List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
		for (String fileName : domainFileNameMap.get(domainId)) {
			fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, "yyyyMMdd")));
		}
		return fileNames;
	}

	@Override
	protected String getLocalFilePath() {
		return "/tmp/";
	}

	@Override
	protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException {
		String[] datas = StringUtils.split(line, "\t");
		if (datas.length != 5) {
			System.out.println("data length is not 5. skip");
			return null;
		}

		String userAgent = datas[1];
		int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
		if (uaGroupId < 0) {
			return null;
		}

		String ip = datas[2];
		if(StringUtils.isEmpty(ip) || "-".equalsIgnoreCase(ip)) {
			System.out.println("ip is error : "+ip);
			return null;
		}
		ip = ip.trim();

		String uri = datas[3];
		if (StringUtils.equalsIgnoreCase(uri, "\\N")) {
			return null;
		}
		if (UrlFilterUtil.shouldSkipUrl(uri)) {
			return null;
		}
		String host = domainName;
		if (!StringUtils.startsWithIgnoreCase(uri, "/")) {
			host = StringUtils.substringBefore(uri, "/");
			if (StringUtils.isEmpty(host)) {
				host = domainName;
			}
			uri = "/"+StringUtils.substringAfter(uri, "/");
		}


		String status = datas[0];

		String date = datas[4];
//		String dates = StringUtils.split(date, " ")[0]+" "+StringUtils.split(date, " ")[1];
		Date time = FormatUtils.toDate(date, "yyyy-MM-dd HH:mm:ss");
		long timestamp = time.getTime();

		if(StringUtils.isEmpty(ip) ||
				StringUtils.isEmpty(host) ||
				StringUtils.isEmpty(status) ||
				StringUtils.isEmpty(userAgent) ||
				StringUtils.isEmpty(uri) ||
				StringUtils.isEmpty(String.valueOf(timestamp))) {
			System.out.println(line);
			System.out.println(ip+"\n"+date+"\n"+uri+"\n"+status+"\n"+userAgent+"\n"+time+"\n"+timestamp);
			return null;
		}

		BotJsonVO jsonVO = new BotJsonVO();
		BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
		dataVO.setUaGroupId(uaGroupId);
		dataVO.setOriginIP(ip);
		dataVO.setReqHost(host);
		dataVO.setStatus(status);
		dataVO.setUserAgent(userAgent);
		dataVO.setUserAgentStr(userAgent);
		dataVO.setReqPath(uri);
		//Leo - https://www.wrike.com/open.htm?id=186725979
		dataVO.setDate(FormatUtils.formatDate(time, "yyyy-MM-dd HH:mm:ss"));
		dataVO.setTimestamp(String.valueOf(timestamp));
		if (null != domainId && domainId > 0) {
			dataVO.setDomainId(domainId);
		}

		jsonVO.setData(dataVO);

		return jsonVO;
	}

	@Override
	protected String getLocalGzFilePath() {
		return localPath;
	}
}
