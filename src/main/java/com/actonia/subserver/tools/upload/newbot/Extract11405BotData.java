package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * www.speedwaymotors.com - 11405
 * https://www.wrike.com/open.htm?id=1116471669
 * com.actonia.subserver.tools.upload.newbot.Extract11405BotData
 *      file: www.speedwaymotors.com_bot_logs_2023-05-04.gz
 *         head: date time c-ip cs-method cs-uri-stem sc-status cs(Referer) cs(User-Agent) cs-uri-query x-host-header cs-protocol sc-content-type
 *         row:  2023-05-04	20:17:27	***********	GET	/Mar-K-100879HB-Bed-Strips-Stainless-Hidden-Fasteners-46-Chevy,502767.html	200	-	Mozilla/5.0%20(compatible;%20Googlebot/2.1;%20+http://www.google.com/bot.html)	utm_medium=CSEGoogle&utm_source=CSE&utm_campaign=CSEGOOGLE	www.speedwaymotors.com	https	text/html;%20charset=utf-8
 */
@CommonsLog
public class Extract11405BotData extends AbstractExtractBotCommonJsonFile {

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true);

    private static final SimpleDateFormat dataDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static String domain = "www.speedwaymotors.com";

    private static Integer version = 0;

    public Extract11405BotData() {
        super();

        s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
        s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
        s3BusketName = "seoclarity-cf-bot-logs";

        domainMaps.put(11405, domain);
        domainFileNameMap.put(11405, Collections.singletonList("www.speedwaymotors.com_bot_logs_%s"));
        fileNamePatten = "yyyy-MM-dd";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;

        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 11405, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected boolean isDownloadFromS3() {
        return true;
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.containsIgnoreCase(line, "#Version:")
                || StringUtils.containsIgnoreCase(line, "#Fields:")) {
            log.info("SKIP header line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }

            CSVRecord csvRecord = csvRecords.get(0);

            String userAgent = csvRecord.get(7);
            if (StringUtils.isEmpty(userAgent) || StringUtils.equalsIgnoreCase(userAgent, "-")) {
                return null;
            }
            userAgent = URLDecoder.decode(userAgent, "UTF-8");

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String uri = csvRecord.get(4);
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }
            String param = csvRecord.get(8);
            if (StringUtils.isNotEmpty(param) && StringUtils.isNotBlank(param) && !StringUtils.equalsIgnoreCase(param, "-")) {
                uri = uri + "?" + param;
            }

            String date = csvRecord.get(0).trim();
            String time = csvRecord.get(1).trim();
            String dateStr = date + " " + time;
            Date datetime = dataDateFormatter.parse(dateStr);

            String ip = csvRecord.get(2);

            String status = csvRecord.get(5);

            String hostName = csvRecord.get(9);
            if (StringUtils.equalsIgnoreCase(hostName, "-")) {
                domainName = hostName;
            }

            String reqProtocol = csvRecord.get(10);
            if (StringUtils.isEmpty(reqProtocol) || StringUtils.equalsIgnoreCase(reqProtocol, "-")) {
                System.out.println("===errorProtocol will skip this line. ");
                return null;
            }


            long timestamp = datetime.getTime();

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.equalsIgnoreCase(status, "0") ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.endsWithIgnoreCase(userAgent, "-") ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.endsWithIgnoreCase(uri,"-") ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + domainName + "\n" + dateStr + "\n"
//                        + "\n" + status + "\n" + userAgent + "\n" + datetime + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(dateStr);
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setReqProtocol(reqProtocol);

            dataVO.setUseFileContextDate(true);

            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            return null;
        }
    }
}
