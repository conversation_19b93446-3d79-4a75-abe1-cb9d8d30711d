package com.actonia.subserver.tools.upload.newbot;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import seoclarity.backend.dao.actonia.bot.HostVerificationDAO;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;


//mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.newbot.BotParseTest" -Dexec.cleanupDaemonThreads=false -Dexec.args=""

public class BotParseTest {
    private HostVerificationDAO hostVerificationDAO;
    public BotParseTest() {
        hostVerificationDAO = SpringBeanFactory.getBean("hostVerificationDAO");
    }

    public static int cnt = 0;
    public static void main(String[] args) {
        BotParseTest b = new BotParseTest();
//        processFile();
        processStr();
    }

    private static void processStr() {
        List<String> list = Arrays.asList("");
        for (String str : list) {
            parseLine(str);
        }
    }

    private static void parseLine(String line){
        if (line == null) {
            System.out.println("error line...");
            return;
        }
        CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',');
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            System.out.println(csvRecords);
            cnt++;
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (cnt++ >= 5) {
            return;
        }
    }
}
