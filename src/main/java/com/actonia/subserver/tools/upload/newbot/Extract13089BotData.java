package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * shop.skysports.com 13089
 * https://www.wrike.com/open.htm?id=1361225783
 * com.actonia.subserver.tools.upload.newbot.Extract13089BotData
 *      file -> aws s3 ls s3://fanatics.prod.partner.regulated.botclarity/2024-04-26/shop.skysports.com_2024-04-26
 *      https://www.fanatics.com/nfl/tampa-bay-buccaneers/sweatshirts/tampa-bay-buccaneers-starter-extreme-pullover-hoodie-red/orange/o-25+t-3451+d-8019+f-200138693?utm_medium=cse&_s=GPA_CA&sku=200563199,200,Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html),66.249.65.228
 */
@CommonsLog
public class Extract13089BotData extends AbstractExtractBotCommonJsonFile {

    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',');
    private static final SimpleDateFormat dataFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static Integer version = 0;
    private static Date logDate;

    private static String localPath = "/opt/bot/";
    private static String domain = "shop.skysports.com";
    private static int domainId = 13089;
    private static String defaultDateStr; // 文件没有bot date， 将文件名的日期作为bot date

    //todo 需要集成多domain
    public Extract13089BotData() {
        super();

        s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
        s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
        s3BusketName = "fanatics.prod.partner.regulated.botclarity";

        domainMaps.put(domainId, domain);
        domainFileNameMap.put(domainId, Arrays.asList("%s/shop.skysports.com_%s"));
        fileNamePatten = "yyyy-MM-dd";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;

        defaultDateStr = dataFormatter.format(date); // 文件没有bot date， 将文件名的日期作为bot date

        if (isTestFlag) {
            tempFileFolder = "/home/<USER>/source/radeL/tmp_file/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot " + domainId + ", current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/bot_logs/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten), FormatUtils.formatDate(logDate, "yyyy-MM-dd")), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return false;
    }

    @Override
    protected boolean isDownloadFromS3() {
        return true;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.startsWith(line, "URL,Response_Code,User_Agent,IP_Address")) {
            log.info("SKIP special line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error!");
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String url = csvRecord.get(0).trim();
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }

            String userAgent = csvRecord.get(2).trim();
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }
            String uri = FormatUtils.getUriFromUrl(url);
            String status =  csvRecord.get(1).trim();
            String ip = csvRecord.get(3).trim();
            Date date = dataFormatter.parse(defaultDateStr);
            long timestamp = date.getTime();

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(url) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(defaultDateStr);
            dataVO.setTimestamp(String.valueOf(timestamp));

            dataVO.setDomainId(domainId);
            dataVO.setDisableTimeZone(true);
            dataVO.setUseFileContextDate(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            try {
                Thread.sleep(10);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            log.error("Error line : " + line);
            return null;
        }
    }
}
