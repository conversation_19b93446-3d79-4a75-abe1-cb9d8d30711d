package com.actonia.subserver.tools.upload.newbot;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR> on 2019/10/14.
 * https://www.wrike.com/open.htm?id=407627904
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.newbot.Extract8422BotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2019-10-01"
 */
@Deprecated
@CommonsLog
public class Extract8422Part2BotData extends AbstractExtractBotCommonJsonFile {

    private static final String S3_FILE_NAME_ADS = "adsbot";
    private static final String S3_FILE_NAME_OTHERS = "otherbots";
    private static Integer version = 0;
    private static Date processDate = new Date();
    private static Date logDate;
    private static String localPath = "/opt/bot/";

    public Extract8422Part2BotData() {
		super();
        s3AccessKey = "********************";
        s3SecretKey = "tPVSGJLtDjlf2f5ANtmMujoZRw9Hz5EqzJMqnnBs";
//        s3BusketName = "tf-spokeo-production-seoclarity-logs";
        s3BusketName = "tf-spokeo-production-vendor-logs";//https://www.wrike.com/open.htm?id=566745873
		domainMaps.put(8422, "www.spokeo.com");
		domainFileNameMap.put(8422, Arrays.asList("logs-%s"));
        fileNamePatten = "yyyy-MM-dd";
        emailTo = "<EMAIL>";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 8422, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

//	@Override
//	protected boolean isDynamicFileName() {
//		return true;
//	}


    private String downloadFileFromS3(int domainId, String key, String fileName) {

        log.info("===downloadFile key: " + key);

        String filePath = getRemoteFilePath(domainId) + fileName;
        try {
            AWSCredentials credentials = new BasicAWSCredentials(s3AccessKey, s3SecretKey);
            AmazonS3 s3client = new AmazonS3Client(credentials);
//            ObjectListing objectListing = s3client.listObjects(s3BusketName);
//            System.out.println("===" + JSON.toJSONString(objectListing));

            GetObjectRequest rangeObjectRequest = new GetObjectRequest(s3BusketName, key);
            S3Object objectPortion = s3client.getObject(rangeObjectRequest);
            if (objectPortion != null && objectPortion.getObjectContent() != null) {

                InputStream objectData = objectPortion.getObjectContent();
                File outFile = new File(filePath);
                FileOutputStream fos = new FileOutputStream(outFile);
                byte[] readBuf = new byte[1024];
                int readLen;
                while ((readLen = objectData.read(readBuf)) > 0) {
                    fos.write(readBuf, 0, readLen);
                }
                objectData.close();
                fos.close();

                System.out.println("===downloadFile success : " + outFile.getAbsolutePath());
            }

        } catch (Exception e1) {
            e1.printStackTrace();
            return null;
        }

        return filePath;
    }


    private static Map<String, String> getS3Map(Date processDate) {

        Map<String, String> resultMap = new HashMap<>();

        int dateInt = FormatUtils.formatDateToYyyyMmDd(processDate);

        String year = String.format("%tY", processDate);
        String month = String.format("%tm", processDate);

        String googleFileName =  dateInt + "-" + S3_FILE_NAME_ADS + ".txt.gz";
        String googleKey = year + "/" + month + "/" + googleFileName;

        String bingFileName =  dateInt + "-" + S3_FILE_NAME_OTHERS + ".txt.gz";
        String bingKey = year + "/" + month + "/" + bingFileName;

        resultMap.put("googleFileName" , googleFileName);
        resultMap.put("googleKey" , googleKey);

        resultMap.put("bingFileName" , bingFileName);
        resultMap.put("bingKey" , bingKey);

        return resultMap;
    }

    @Override
    protected boolean isDownloadFromS3(){
        return true;
    }

    @Override
	protected Date getBotLogDate() {
		return logDate;
	}

	@Override
    protected String getRemoteFilePath(int domainId) {
//        return "D:\\Extract\\bot\\";
        return "/home/<USER>/" + domainId + "/" + "BotLogs/";
    }

	@Override
	protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
//		List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
//		for (String fileName : domainFileNameMap.get(domainId)) {
//			fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)));
//		}
//		return fileNames;
        return null;
	}

	@Override
	protected String getLocalFilePath() {
		return "/tmp/";
	}

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        CSVParser csvParser = null;
        try {
            csvParser = CSVParser.parse(line, CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true));
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<CSVRecord> recordList = null;
        try {
            recordList = csvParser.getRecords();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(recordList)) {
            log.error("Error line : " + line);
            return null;
        }
        for (CSVRecord csvRecord : recordList) {
            try {
                String userAgent = URLDecoder.decode(URLDecoder.decode(csvRecord.get(4), "utf-8"), "utf-8");
                String ip = csvRecord.get(3);
                ip = ip.replaceAll(":.*$", "");
                int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
                if (uaGroupId < 0) {
                    return null;
                }
                String dateTime = csvRecord.get(0);
//                String time = csvRecord.get(1);
//                String dateTime = date + " " + time;
                Date d = FormatUtils.toDate(dateTime, "yyyy-MM-dd HH:mm:ss");

                long timestamp = d.getTime();

                String dateStr = FormatUtils.formatDate(d, "yyyy-MM-dd HH:mm:ss");
                String uri = csvRecord.get(1);
                uri = uri.replaceAll("^POST|^GET|^HEAD|^PUT|^OPTIONS", "");
                uri = StringUtils.removeEndIgnoreCase(uri, " HTTP/1.1");
                if (UrlFilterUtil.shouldSkipUrl(uri)) {
                    return null;
                }
                String status = csvRecord.get(2);
                String u = FormatUtils.getUriFromUrl(uri);

//                if (StringUtils.isEmpty(ip) ||
//                        StringUtils.isEmpty(status) ||
//                        StringUtils.isEmpty(userAgent) ||
//                        StringUtils.isEmpty(u)) {
//                    log.error("skip line : " + csvRecord.toString());
//                    continue;
//                }

                if(StringUtils.isEmpty(ip) ||
                        StringUtils.isEmpty(domainName) ||
                        StringUtils.isEmpty(status) ||
                        StringUtils.isEmpty(userAgent) ||
                        StringUtils.isEmpty(uri) ||
                        StringUtils.isEmpty(String.valueOf(timestamp))) {
//                    System.out.println(line);
//                    System.out.println(ip+"\n"+dateTime+"\n"+uri+"\n"+status+"\n"+userAgent+"\n"+d+"\n"+timestamp);
                    return null;
                }

                BotJsonVO jsonVO = new BotJsonVO();
                BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
                dataVO.setUaGroupId(uaGroupId);
                dataVO.setOriginIP(ip);
                dataVO.setReqHost(domainName);
                dataVO.setStatus(status);
                dataVO.setUserAgent(userAgent);
                dataVO.setUserAgentStr(userAgent);
                dataVO.setReqPath(u);
                dataVO.setDate(dateStr);
                dataVO.setTimestamp(String.valueOf(timestamp));
                dataVO.setVersion(version);

                dataVO.setDomainId(domainId);
                jsonVO.setData(dataVO);
                return jsonVO;
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }

        }
        return null;
    }

	@Override
	protected String getLocalGzFilePath() {
		return localPath;
	}

    @Override
    protected boolean isMultiFile() {
        return true;
    }

    @Override
    protected boolean isSendEmail() {
        return true;
    }
}
