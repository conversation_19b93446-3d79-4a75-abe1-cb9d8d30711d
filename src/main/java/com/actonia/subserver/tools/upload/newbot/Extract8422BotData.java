package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.IOException;
import java.net.URLDecoder;
import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR> on 2019/10/14.
 */
@CommonsLog
public class Extract8422BotData extends AbstractExtractBotCommonJsonFile {

    private static Integer version = 0;
    private static Date logDate;
    private static String localPath = "/opt/bot/";

    public Extract8422BotData() {
        super();
//        s3AccessKey = "********************";
//        s3SecretKey = "tPVSGJLtDjlf2f5ANtmMujoZRw9Hz5EqzJMqnnBs";

        s3AccessKey = "********************";
        s3SecretKey = "vnIhwoLtzEBVMZ80lw1Ab1a9chjKaOCJp3ETaa2z";

//        s3BusketName = "tf-spokeo-production-seoclarity-logs";
        s3BusketName = "tf-spokeo-production-vendor-logs";//https://www.wrike.com/open.htm?id=566745873
        domainMaps.put(8422, "www.spokeo.com");
        domainFileNameMap.put(8422, Arrays.asList("%s-bingbot.txt.gz","%s-bing-adsbot.txt.gz","%s-googlebot.txt.gz",
                "%s-google-adsbot.txt.gz","%s-otherbots.txt.gz","%s-adsbot.txt.gz"));
        fileNamePatten = "yyyy/MM/yyyyMMdd";
        emailTo = "<EMAIL>";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 8422, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            //测试的时候为true
            //isTestFlag = true;
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected boolean isDownloadFromS3() {
        return true;
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/" + "BotLogs/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        CSVParser csvParser = null;
        try {
            csvParser = CSVParser.parse(line, CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true));
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<CSVRecord> recordList = null;
        try {
            recordList = csvParser.getRecords();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(recordList) || recordList.size() > 1) {
            log.error("Error line : " + line);
            return null;
        }
        CSVRecord csvRecord = recordList.get(0);
        try {
            String userAgent = URLDecoder.decode(URLDecoder.decode(csvRecord.get(4), "utf-8"), "utf-8");
            String ip = csvRecord.get(3);
            ip = ip.replaceAll(":.*$", "");

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }
            String dateTime = csvRecord.get(0);
            Date d = DateUtils.parseDate(dateTime, new String[]{"yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"});
            long timestamp = d.getTime();
            String dateStr = FormatUtils.formatDate(d, "yyyy-MM-dd HH:mm:ss");
            String uri = csvRecord.get(1);
            String url = csvRecord.get(1);
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }
            uri = uri.replaceAll("^POST|^GET|^HEAD|^PUT|^OPTIONS", "");
            uri = StringUtils.removeEndIgnoreCase(uri, " HTTP/1.1");
            String status = csvRecord.get(2);
            String u = FormatUtils.getUriFromUrl(uri);
            String reqProtocol = FormatUtils.getProtocolFromUrl(url);

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
                //System.out.println(line);
                //System.out.println(ip + "\n" + dateTime + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + d + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(u);
            dataVO.setDate(dateStr);
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setVersion(version);
            dataVO.setReqProtocol(reqProtocol);

            dataVO.setDomainId(domainId);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return true;
    }

    @Override
    protected boolean isSendEmail() {
        return true;
    }
}
