package com.actonia.subserver.tools.upload.newbot;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.summary.ClarityDBBotDailyUploadThreadMain;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * One-time script
 *
 * example:
 *      useragent,start_time,path,status_code,sanitized_url
 *
 *      1. Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html),2021-01-06 21:37:25,/login,200,https://airtable.com/login?continue=*
 *      2. Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html),2021-01-06 08:35:43,/templates/:categorySlug,200,https://airtable.com/templates/sales-and-customers
 *
 */
@CommonsLog
public class Import9834BotDataToClarityDB {
    private ClarityDBBotDailyUploadThreadMain uploadThreadMain = new ClarityDBBotDailyUploadThreadMain();
    private Gson gson = new Gson();
    private static String domain = "airtable.com";
    private static int domainId = 9834;
    private static Integer version = 0;
    CSVFormat csvFormat = CSVFormat.DEFAULT.withIgnoreEmptyLines(true);
    // example: 2021-01-06 11:30:05
    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM-dd");
    private String getJSONFilePath(){
        return "/home/<USER>/source/ewain/formal-clarity-backend-scripts/bot_log_data/json_file/";
    }

    public static void main(String[] args) {
        Import9834BotDataToClarityDB bot = new Import9834BotDataToClarityDB();
        bot.process();
    }

    private void process(){
        Map<String,String> map = new HashMap<>();
        map.put("2021-01-06", "/home/<USER>/source/ewain/formal-clarity-backend-scripts/bot_log_data/9834/google_bot_traffic-google_bot_traffic-76fbf2b04941-2021-01-08-18-33-17.csv");
        Set<String> stringSet = map.keySet();
        for (String dateStr : stringSet) {
            String filePath = map.get(dateStr);
            Date logDate = null;
            try {
                logDate = formatter2.parse(dateStr);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            File localLogFile = new File(filePath);
            extractJsonFileAndLoad(localLogFile, domain, domainId, logDate);
        }
        try {
            uploadThreadMain.waitForThreadPool();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) {
        if (StringUtils.containsIgnoreCase(line, "useragent,start_time,path,")) {
            log.info("SKIP header line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);
            String userAgent = csvRecord.get(0);
            if (userAgent.startsWith("\"")){
                userAgent = userAgent.substring(1);
            }
            if (userAgent.endsWith("\"")) {
                userAgent = userAgent.substring(0, userAgent.length() - 1);
            }
            String dateStr = csvRecord.get(1);
            String ip = "0.0.0.0";
            String status = csvRecord.get(3);
            String url = csvRecord.get(4);
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }
            String uri;
            if (StringUtils.isEmpty(url)) {
                uri = "";
            }else {
                uri = FormatUtils.getUriFromUrl(url);
            }

            Date datetime = formatter.parse(dateStr);
            long timestamp = datetime.getTime();

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }
            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + datetime + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(datetime, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));

            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            log.error("Error line : " + line);
            return null;
        }
    }

    private void extractJsonFileAndLoad(File logFile, String domainName, int domainId, Date logDate) {
        if (logDate == null){
            return;
        }
        List<String> outPutJsonFilePaths = new ArrayList<>();
        File localJsonFile;
        FileOutputStream outputStream = null;
        try (FileInputStream inputStream = new FileInputStream(logFile);
             Scanner sc = new Scanner(inputStream, "UTF-8")) {
            int records = 0;
            while (sc.hasNextLine()) {
                String line = sc.nextLine();
                BotJsonVO botVO = parserLineToVO(line, domainName, domainId);
                if (botVO != null) {
                    if (records == 0 || records % 500000 == 0) {
                        if (outputStream != null) {
                            outputStream.flush();
                            outputStream.close();
                        }
                        localJsonFile = new File(getJSONFilePath() + "/" + StringUtils.replace(logFile.getName(), "\\", "") + "_" + records + ".json");
                        log.info(records + " for path2 : " + localJsonFile.getAbsolutePath());
                        outPutJsonFilePaths.add(localJsonFile.getAbsolutePath());
                        outputStream = new FileOutputStream(localJsonFile);
                    }
                    IOUtils.write(gson.toJson(botVO) + "\n", outputStream);
                    records++;
                }
            }
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (sc.ioException() != null) {
                throw sc.ioException();
            }
            log.info("Total count: " + records);
        } catch (Exception e) {
            e.printStackTrace();
        }

        log.info("finished processing file : " + StringUtils.replace(logFile.getName(), "\\", ""));
        //Insert into Database
        for (String outPutJsonFilePath : outPutJsonFilePaths) {
                System.out.println("执行文件导入");
                uploadThreadMain.loadTargetFile(logDate, outPutJsonFilePath);
        }



    }
}
