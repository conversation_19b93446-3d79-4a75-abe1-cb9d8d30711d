package com.actonia.subserver.tools.upload.bot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.Extract6982BotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2019-08-07"
 */
@CommonsLog
public class Extract6982BotData extends AbstractExtractBotCommonJsonFile {

	private static Date processDate = new Date();
	private static Date logDate;
	private static String localPath = "/opt/bot/";


	public Extract6982BotData() {
		super();
		domainMaps.put(6982, "www.autogravity.com");
		domainFileNameMap.put(6982, Arrays.asList("logs-%s"));
	}

	@Override
    protected boolean isDynamicFileName() {
        return true;
    }

	public static void main(String[] args) {
		Extract6982BotData extractMechaBotData = new Extract6982BotData();
		if (args != null & args.length > 0) {
			if(StringUtils.containsIgnoreCase(args[0], ",")) {
				Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
				Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
				if (args.length > 1) {
					localPath = args[1];
				}
				while(sDate.compareTo(eDate) <= 0) {
					logDate = sDate;
					processDate = logDate;
					extractMechaBotData.startProcess(true, false);
					sDate = DateUtils.addDays(sDate, 1);
				}
			} else {
				logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
				processDate = logDate;
				if (args.length > 1) {
					localPath = args[1];
				}
				extractMechaBotData.startProcess(true, false);
			}
			extractMechaBotData.waitForThreadPool();
		} else {
            logDate = new Date();
			processDate = logDate;
			extractMechaBotData.startProcess(false);
        }
	}

	@Override
	protected Date getBotLogDate() {
		return logDate;
	}

	@Override
	protected String getRemoteFilePath(int domainId) {
		return "/home/<USER>/"+domainId+"/";
	}

	@Override
	protected List<String> getRemoteFileNames(int domainId, Date logDate) {
		List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
		for (String fileName : domainFileNameMap.get(domainId)) {
			fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, "yyyy-MM-dd")));
		}
		return fileNames;
	}

	@Override
	protected String getLocalFilePath() {
		return "/tmp/";
	}

	@Override
	protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException {
		CSVParser csvParser = null;
		try {
			csvParser = CSVParser.parse(line, CSVFormat.DEFAULT.withDelimiter(' ').withIgnoreEmptyLines(true));
		} catch (IOException e) {
			e.printStackTrace();
		}
		List<CSVRecord> recordList = null;
		try {
			recordList = csvParser.getRecords();
		} catch (IOException e) {
			e.printStackTrace();
		}
		if(CollectionUtils.isEmpty(recordList)) {
            log.error("Error line : "+line);
            return null;
        }
		for (CSVRecord csvRecord : recordList) {
			String userAgent = csvRecord.get(12);
			String ip = csvRecord.get(2);
			ip = ip.replaceAll(":.*$", "");
			int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
			if (uaGroupId < 0) {
				return null;
			}

			String date = csvRecord.get(0);
			date = date.replaceAll("\\..*$", "");
			date = date.replaceAll("T", " ");
			Date d = FormatUtils.toDate(date, "yyyy-MM-dd HH:mm:ss");
			String day = DateFormatUtils.format(d, "yyyyMMdd");
			String dateStr = FormatUtils.formatDate(d, "yyyy-MM-dd HH:mm:ss");
			String uri = csvRecord.get(11);
			uri = uri.replaceAll("^POST|^GET|^HEAD|^PUT|^OPTIONS", "");
			uri = StringUtils.removeEndIgnoreCase(uri, " HTTP/1.1");
			uri = uri.replace(":443/", "/");
			String status = csvRecord.get(7);
			if (UrlFilterUtil.shouldSkipUrl(uri)) {
				return null;
			}
			String u = FormatUtils.getUriFromUrl(uri);

			if(StringUtils.isEmpty(ip) ||
					StringUtils.isEmpty(status) ||
					StringUtils.isEmpty(userAgent) ||
					StringUtils.isEmpty(u)) {
				log.error("skip line : "+csvRecord.toString());
				continue;
			}

			BotJsonVO jsonVO = new BotJsonVO();
			BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
			dataVO.setUaGroupId(uaGroupId);
			dataVO.setOriginIP(ip);
			dataVO.setReqHost(domainName);
			dataVO.setStatus(status);
			dataVO.setUserAgent(userAgent);
			dataVO.setUserAgentStr(userAgent);
			dataVO.setReqPath(u);
			dataVO.setDate(dateStr);

			dataVO.setDomainId(domainId);
			jsonVO.setData(dataVO);

			return jsonVO;
		}
		return null;
	}

	@Override
	protected String getLocalGzFilePath() {
		return localPath;
	}

}
