package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * com.actonia.subserver.tools.upload.newbot.Extract5776BotData
 *
 * "2022-03-25T10:00:00.000+0000","/uk/search/41mm-Maize-White-Sport-Loop?amp=&src=aos&tab=accessories",200,1
 * "2022-03-25T10:00:00.000+0000","/uk/search/41mm-<PERSON><PERSON>-White-Sport-Loop?amp=&src=aos&tab=explore",200,1
 * "2022-03-25T10:00:00.000+0000","/uk/search/41mm-Maize-White-Sport-Loop?amp=&src=aos&tab=retail",200,1
 */
@CommonsLog
public class Extract5776BotData extends AbstractExtractBotCommonJsonFile {

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',').withIgnoreEmptyLines(true);
    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static String domain = "www.apple.com";

    private static Integer version = 0;

    public Extract5776BotData() {
        super();
        domainMaps.put(5776, domain);
        domainFileNameMap.put(5776, Arrays.asList("Googlebot_origin_by_uri_stts_%s"));
        fileNamePatten = "MMM_dd";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;

        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 5776, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, false);
        } else {
            bot.startProcess(false, true);
        }
    }

    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (line.startsWith("\"_time")) {
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String ip = "0.0.0.0";
            String dateStr = csvRecord.get(0);
            String uri = csvRecord.get(1);
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }
            String status = csvRecord.get(2);
            String userAgent = "Googlebot/2.1 (+http://www.google.com/bot.html)";
//            String protocolFlag = csvRecord.get(5);

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            Date datetime = formatter.parse(dateStr);
            long timestamp = datetime.getTime();

//            String protocol;
//            if (protocolFlag.equals("on")) {
//                protocol = "https";
//            }else if (protocolFlag.equals("off")) {
//                protocol = "http";
//            }else {
//                protocol = null;
//            }

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "~" + dateStr + "~" + uri + "~" + status + "~" + userAgent + "~" + datetime + "~" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(datetime, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
//            dataVO.setReqProtocol(protocol);

            dataVO.setUseFileContextDate(true);
            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);

            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return false;
    }

}
