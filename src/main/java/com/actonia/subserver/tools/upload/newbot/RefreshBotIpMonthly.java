package com.actonia.subserver.tools.upload.newbot;

import seoclarity.backend.dao.actonia.bot.HostVerificationDAO;
import seoclarity.backend.entity.actonia.bot.HostVerificationEntity;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.net.UnknownHostException;
import java.util.List;

public class RefreshBotIpMonthly {

    private HostVerificationDAO hostVerificationDAO;

    public RefreshBotIpMonthly() {
        hostVerificationDAO = SpringBeanFactory.getBean("hostVerificationDAO");
    }
    public static void main(String[] args) {
        RefreshBotIpMonthly refreshBotIpMonthly = new RefreshBotIpMonthly();
        refreshBotIpMonthly.process();
    }

    public void process() {
        System.out.println(" start process refresh ip");

        List<HostVerificationEntity> HostVerificationEntityList = hostVerificationDAO.getAllOrderByIp();
        for (HostVerificationEntity entity : HostVerificationEntityList) {
            String ip = entity.getIp();
            Integer isBotCurr = entity.getIsbot();
            Integer type = entity.getType();

            String hostName = getHostName(ip);
            if (ip.equals("0.0.0.0") || hostName == null){
                continue;
            }
            int isBot = 0;
            int botType = 0;
            if (FormatUtils.validateRegularEx(hostName, BotUtils.googleHostRegEx)) {
                isBot = 1;
                botType = BotUtils.GOOGLE_TYPE;
            }else if (FormatUtils.validateRegularEx(hostName, BotUtils.baiduHostRegEx)) {
                isBot = 1;
                botType = BotUtils.BAIDU_TYPE;
            }else if (FormatUtils.validateRegularEx(hostName, BotUtils.bingHostRegEx)) {
                isBot = 1;
                botType = BotUtils.BING_TYPE;
            }else if (FormatUtils.validateRegularEx(hostName, BotUtils.yandexHostRegEx)) {
                isBot = 1;
                botType = BotUtils.YANDEX_TYPE;
            }else if (FormatUtils.validateRegularEx(hostName, BotUtils.yahooHostRegEx)) {
                isBot = 1;
                botType = BotUtils.YAHOO_TYPE;
            }

            System.out.println("match => ip: " + ip + ", isBot: " + isBot + ", botType: " + botType + ", isBotCurr: " +isBotCurr + ", type: " + type);

            if (isBotCurr != isBot || (isBotCurr == 1 && isBot == 1 && botType != type)) {
                hostVerificationDAO.update(ip, isBot, botType);
                System.out.println("update ip: " + ip + ", isBotCurr: "
                        + isBotCurr + ", isBot: " + isBot + ", botType: " + botType + ", type: " + type);
            }

        }

        System.out.println(" end process refresh ip");

    }

    private String getHostName(String ipAddress) {
        try {
            String hostName = FormatUtils.getHostName(ipAddress);
            System.out.println("getHostName => ip: " + ipAddress + ", hostName: " + hostName);
            return hostName;
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return null;
        }
    }

}
