package com.actonia.subserver.tools.upload.bot;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.entity.clickhouse.bot.BotVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ImportBradfordexchangeData { //extends UploadBotDataBase

	// https://www.wrike.com/open.htm?id=16452953
	private static final int HEADER_LINES = 0;
	
	private static final int PROCESSING_DOMAIN_ID = 5461;
	
	private static final String IP_PATTERN = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";
	private static final String DATE_PATTERN = "\\[.*\\]";
	private static final String URL_PATTERN = "(GET |POST |HEAD |PUT )[^\\s]{0,} ";
	private static final String RM_URL_PATTERN = "^POST|^GET|^HEAD|^PUT";
	private static final String STATUS_PATTERN = " \\d{3} ";
	private static final String USER_AGENT_PATTERN = " \\d{3} (\\d+|-) \"(-|http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?)\" \"([^\"]{0,}|-)\"";
	private static final String RM_USER_AGENT_PATTERN = " \\d{3} (\\d+|-) \"(-|http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?)\" ";
	private static final String DOMAIN_PATTERN = " [^\"|^\\s]+ - ";

	private static final String URL_PATTERN_2 = "\"(-|http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?)\"";
	
	private Gson gson = new GsonBuilder().disableHtmlEscaping().create();
	
	public static void main(String[] args) {
		
//		String str = "10.0.0.193 66.249.71.64 kango.mynavi.jp - [01/Mar/2017:20:51:29 +0900] https 443 \"GET /r//?page=1161 HTTP/1.1\" 200 15178 \"-\" \"Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)\" 1917745";
//		String str = "10.0.0.193 66.249.79.127 kango.mynavi.jp - [01/Mar/2017:20:50:51 +0900] https 443 \"GET /contents/nurseplus/wp-content/themes/nurseplus/commons/stylesheets/app.css HTTP/1.1\" 200 27603 \"http://jallalhamani.com/index.php/32-pugoa/TboneSteak/12725.htm\" \"Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)\" 6905";
//		System.out.println(getValueByPattern(USER_AGENT_PATTERN, str));
//		System.out.println(getUserAgent(str));

		ImportBradfordexchangeData test = new ImportBradfordexchangeData();
		List<String> lines = null;
		try {
			File folder = new File("D:\\TEMP\\bradfordexchange\\log\\");
			for (File file : folder.listFiles()) {
				System.out.println("start file : "+file.getName());
				lines = org.apache.commons.io.FileUtils.readLines(file);
				for (int i = 0; i < lines.size(); i++) {
					test.processLine(i, lines.get(i));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

//    	botWorkDirectory = "D:\\My Documents\\Desktop\\新建文件夹";
//    	test.configuredOwnDomainId = 185;
//    	try {
//    		test.processFileContent("D:\\My Documents\\Desktop\\新建文件夹", "a.zip");
//    	} catch (Exception e) {
//    		e.printStackTrace();
//    	}
	}
	
	protected int getStartDataLineNo(List<String> lines) {
		return HEADER_LINES;
	}

	protected BotVO processLine(int lineNO, String line) throws Exception {


		String host = getDomain(line);
		if (!StringUtils.equalsIgnoreCase(host, "kango.mynavi.jp")
				&& ! StringUtils.equalsIgnoreCase(host, "m.kango.mynavi.jp")) {
			System.out.println("not correct domain, skip.");
			return null;
		}

		String ip = getValueByPattern(IP_PATTERN, line).trim();
		String date = getValueByPattern(DATE_PATTERN, line).trim().replaceAll("\\[|]","");
		String uri = getUri(line);
		String status = getValueByPattern(STATUS_PATTERN, line).trim();
		String userAgent = getUserAgent(line);

		if (UrlFilterUtil.shouldSkipUrl(uri)) {
			return null;
		}

		int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
		if (uaGroupId < 0) {
			return null;
		}
		SimpleDateFormat formatter = new SimpleDateFormat("dd/MMM/yyyy:hh:mm:ss Z", Locale.ENGLISH);

		Date time = formatter.parse(date);
		String day = DateFormatUtils.format(time, "yyyyMMdd");
		long timestamp = time.getTime();

		if(StringUtils.isEmpty(uri)
				|| StringUtils.startsWithIgnoreCase(uri, "http://")
				|| StringUtils.startsWithIgnoreCase(uri, "https://")) {
			System.out.println("uri is URL, error, uri:" + uri);
			System.out.println("skip line : "+lineNO);
			System.out.println(line);
			return null;
		}
		
		String url = getUrl(line);
		if (StringUtils.isBlank(url) || StringUtils.equalsIgnoreCase(url, "-")) {
			System.out.println("skip line : "+lineNO);
			System.out.println("!!!! skip : url '" + url + "' is not correct");
			return null;
		}

		if(StringUtils.isEmpty(ip) ||
				StringUtils.isEmpty(host) ||
				StringUtils.isEmpty(status) ||
				StringUtils.isEmpty(userAgent) ||
				StringUtils.isEmpty(uri) ||
				StringUtils.isEmpty(String.valueOf(timestamp))) {
			System.out.println("skip line : "+lineNO);
			System.out.println(line);
			System.out.println(ip+"\n"+date+"\n"+uri+"\n"+status+"\n"+userAgent+"\n"+time+"\n"+timestamp);
			return null;
		}

		BotJsonVO jsonVO = new BotJsonVO();
		BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
		dataVO.setUaGroupId(uaGroupId);
		dataVO.setOriginIP(ip);
		dataVO.setReqHost(host);
		dataVO.setStatus(status);
		dataVO.setUserAgent(userAgent);
		dataVO.setUserAgentStr(userAgent);
		dataVO.setReqPath(uri);
		dataVO.setTimestamp(String.valueOf(timestamp));
		
		dataVO.setDomainId(PROCESSING_DOMAIN_ID);
		jsonVO.setData(dataVO);

		String json = gson.toJson(jsonVO);
//		System.out.println(json);

		File base = new File("D:\\TEMP\\bradfordexchange\\daily\\"+day+"\\00\\");
		if (!base.exists()) {
			base.mkdirs();
		}

		File f = new File("D:\\TEMP\\bradfordexchange\\daily\\"+day+"\\00\\"+day+"_log.json");
//		if(f.exists()) {
//			boolean d = f.delete();
//			System.out.println("delete exist file : "+d);
//			if (!d) {
//				System.exit(1);
//			}
//		}

		org.apache.commons.io.FileUtils.write(f, json+"\n", true);

		return null;
	}

	private String getDomain(String str) {
		String domain = getValueByPattern(DOMAIN_PATTERN, str);
		domain = StringUtils.removeEndIgnoreCase(domain, " - ").trim();
		return domain;
	}

	private String getUri(String str) {
		String uri = getValueByPattern(URL_PATTERN, str);
		if(StringUtils.isNotEmpty(uri)) {
			return uri.replaceAll(RM_URL_PATTERN, "").trim();
		}
		return uri;
	}
	
	private String getUrl(String str) {
		String url = getValueByPattern(URL_PATTERN_2, str);
		if(StringUtils.isNotEmpty(url)) {
			return url.replaceAll(RM_USER_AGENT_PATTERN, "").replaceAll("\"", "").trim();
		}
		return url;
	}

	private static String getUserAgent(String str) {
		String agent = getValueByPattern(USER_AGENT_PATTERN, str);
		if(StringUtils.isNotEmpty(agent)) {
			return agent.replaceAll(RM_USER_AGENT_PATTERN, "").replaceAll("\"", "").trim();
		}
		return agent;
	}

	private static String getValueByPattern(String pattern, String str) {
		Pattern r = Pattern.compile(pattern);
		Matcher m = r.matcher(str);
		while (m.find()) {
			return m.group();
		}
		return null;
	}

}
