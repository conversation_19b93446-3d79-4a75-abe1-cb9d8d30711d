package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *  https://www.wrike.com/open.htm?id=764906454
 */
@CommonsLog
public class Extract10467BotData extends AbstractExtractBotCommonJsonFile {

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(' ').withIgnoreEmptyLines(true);
    // example: 2021-09-28 14:59:56
    private static final SimpleDateFormat dataDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static int domainId_10467 = 10467;
    private static int domainId_10534 = 10534;

    private static String domain_10467 = "www.totallypromotional.com";
    private static String domain_10534 = "www.totallyweddingkoozies.com";
    private static Integer version = 0;

    public Extract10467BotData() {
        super();

        domainMaps.put(10467, domain_10467);
        domainFileNameMap.put(10467, Arrays.asList("u_ex%s"));
        fileNamePatten = "yyMMdd";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 10467, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/"+domainId+"/logs/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        int maxIndex = 23;
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            for(int i=0;i<=maxIndex;i++){
                String index = String.valueOf(i).length() == 1 ? "0" + i : "" + i;
                fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten) + index), fileName + "" + index);
            }
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.containsIgnoreCase(line, "#Software:")
                || StringUtils.containsIgnoreCase(line, "#Version:")
                || StringUtils.containsIgnoreCase(line, "#Date:")
                || StringUtils.containsIgnoreCase(line, "#Fields:")) {
            log.info("SKIP header line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }

            CSVRecord csvRecord = csvRecords.get(0);
            String ip = csvRecord.get(8).trim();;//c-ip
            String date = csvRecord.get(0).trim();
            String time = csvRecord.get(1).trim();
            String dateStr = date + " " + time;
            String userAgent = csvRecord.get(9);
            String status = csvRecord.get(11);
            String url = csvRecord.get(10);

            if (url.equals("-")) {
                return null;
            }

            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }

            String uri = FormatUtils.getUriFromUrl(url);
            Date datetime = dataDateFormatter.parse(dateStr);
            long timestamp = datetime.getTime();
            String reqProtocol = FormatUtils.getProtocolFromUrl(url);

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            if (url.contains(domain_10467)) {
                domainName = domain_10467;
                domainId = domainId_10467;
            }else if (url.contains(domain_10534)) {
                domainName = domain_10534;
                domainId = domainId_10534;
            }else {
                return null;
            }

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.endsWithIgnoreCase(userAgent, "-") ||
                    StringUtils.isEmpty(url) ||
                    StringUtils.endsWithIgnoreCase(url,"-") ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + domainName + "\n" + dateStr + "\n"
//                        + url + "\n" + status + "\n" + userAgent + "\n" + datetime + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domainName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(dateStr);
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setReqProtocol(reqProtocol);

            dataVO.setUseFileContextDate(true);

            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return true;
    }

}
