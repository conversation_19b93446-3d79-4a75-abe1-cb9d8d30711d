package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * hub.yamaha.com 9696
 * https://www.wrike.com/open.htm?id=1187763020
 * com.actonia.subserver.tools.upload.newbot.Extract9696BotData
 *      file: /home/<USER>/9694/hub.yamaha.com/hub.yamaha.com_2023-08-11.log
 *      198.143.43.153 - - [11/Aug/2023:00:00:42 +0000] "GET /music-educators/40-under-40/2023/burch-emily-williams-dr/feed/ HTTP/1.1" 304 - "https://hub.yamaha.com/music-educators/40-under-40/2023/burch-emily-williams-dr/feed" "Mozilla/5.0 (compatible; Qwantify-dev8395/1.0; +https://help.qwant.com/bot/)"
 */
@CommonsLog
public class Extract9696BotData extends AbstractExtractBotCommonJsonFile {

    CSVFormat csvFormat = CSVFormat.DEFAULT.withQuote('"').withDelimiter(' ').withTrim();

    private static final SimpleDateFormat inputFormatter = new SimpleDateFormat("dd/MMM/yyyy:HH:mm:ss Z", Locale.ENGLISH);

    private static Integer version = 0;
    private static Date logDate;

    private static String localPath = "/opt/bot/";
    private static String domain = "hub.yamaha.com";
    private static int domainId = 9696;
    private static int domainId_remote_file = 9694; // note: this domain id is 9696 but this bot file is stored in 9694 so this domain id is 9694

    public Extract9696BotData() {
        super();
        domainMaps.put(domainId, domain);
        domainFileNameMap.put(domainId, Arrays.asList("hub.yamaha.com_%s"));
        fileNamePatten = "yyyy-MM-dd";
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;

        this.isTestFlag = false;
        if (isTestFlag) {
            tempFileFolder = "/home/<USER>/source/radeL/tmp_file/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 9696, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId_remote_file + "/hub.yamaha.com/"; // note: this domain id is 9696 but this bot file is stored in 9694 so this domain id is 9694
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return false;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            List<CSVRecord> csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String userAgent = csvRecord.get(9).trim();
            if (StringUtils.isEmpty(userAgent) || "-".equals(userAgent)) {
                return null;
            }

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String uri = csvRecord.get(5).trim();

            if (StringUtils.isEmpty(uri) || "-".equals(uri)) {
                return null;
            }
            String[] uriArr = uri.split(" ");
            if (uriArr.length >= 3) {
                uri = uriArr[1].trim();
            }
            if (UrlFilterUtil.shouldSkipUrl(uri)) {
                return null;
            }

            String ip = "0.0.0.0";
            String dateStr = csvRecord.get(3).trim() + " " + csvRecord.get(4).trim();
            Date date = inputFormatter.parse(dateStr.replace("[", "").replace("]", ""));
            long timestamp = date.getTime();
            String status = csvRecord.get(6).trim();
            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.equalsIgnoreCase(status, "0") ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(uri) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setDomainId(domainId);
            dataVO.setDisableTimeZone(true);
            dataVO.setUseFileContextDate(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            try {
                Thread.sleep(10);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            log.error("Error line : " + line);
            return null;
        }
    }
}
