package com.actonia.subserver.tools.upload.test.bot;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import ru.yandex.clickhouse.ClickHouseArray;
import ru.yandex.clickhouse.domain.ClickHouseDataType;
import seoclarity.backend.clarity360.Clarity360EmbeddingCrawlerCommand;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360LwebVector05DAO;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDoc1Entity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.vectordb.EmbeddingEntity;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.clarity360.Clarity360EmbeddingCrawler" -Dexec.args=""
public class Clarity360EmbeddingCrawlerByFile {

	private final CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',').withIgnoreEmptyLines(true);
	private static Integer pageSize = 500;
	public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	private static final String databaseName = "vector_db";
	private static final String finalTableName = "dis_vector_table";
	private Clarity360LwebVector05DAO clarity360LwebVector05DAO;

	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;

	public Clarity360EmbeddingCrawlerByFile() {

	}
	
	public static void main(String[] args) {
		Clarity360EmbeddingCrawlerByFile Clarity360EmbeddingCrawler = new Clarity360EmbeddingCrawlerByFile();
		Clarity360EmbeddingCrawler.processByFile(args[0], Integer.parseInt(args[1]), Integer.parseInt(args[2]));
	}


	private void processByFile(String filePath, int ownDomainId, int crawlRequestLogId) {
		List<DisSiteCrawlDoc1Entity> resultList = new ArrayList<>();
		try {
			LineIterator iterator = new LineIterator(new BufferedReader(new FileReader(filePath)));
			try {
				while (iterator.hasNext()) {
					String line = iterator.nextLine();
					DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity = genEntityByLine(line, ownDomainId, crawlRequestLogId);
					if (disSiteCrawlDoc1Entity != null) {
						resultList.add(disSiteCrawlDoc1Entity);
						if (resultList.size() >= pageSize) {
//							getEmbedding(resultList);
							resultList.clear();
						}
					}
				}
				if (!resultList.isEmpty()) {
//					getEmbedding(resultList);
					resultList.clear();
				}
			} finally {
				LineIterator.closeQuietly(iterator);
			}
		} catch (IOException e) {
			System.err.println("Error reading the file: " + e.getMessage());
		}
	}

	private DisSiteCrawlDoc1Entity genEntityByLine(String line, int domainId, int crawlLogId) {
		if (StringUtils.isEmpty(line)) {
			return null;
		}
		try {
			CSVParser csvParser = CSVParser.parse(line, csvFormat);
			List<CSVRecord> csvRecords = csvParser.getRecords();
			if (csvRecords.size() > 1) {
				System.out.println("Lines format error!");
				return null;
			}
			CSVRecord record = csvRecords.get(0);
			DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity = new DisSiteCrawlDoc1Entity();
			disSiteCrawlDoc1Entity.setDomainId(domainId);
			disSiteCrawlDoc1Entity.setCrawlRequestId(crawlLogId);
			disSiteCrawlDoc1Entity.setUrl(record.get(0));
			disSiteCrawlDoc1Entity.setDescription(record.get(1));
			disSiteCrawlDoc1Entity.setTitle(record.get(2));
			String[] h1Arr = getStringArr(record.get(3));
			if (h1Arr != null && h1Arr.length > 0) {
				disSiteCrawlDoc1Entity.setH1(new ClickHouseArray(ClickHouseDataType.String, h1Arr));
			}
			String[] h2Arr = getStringArr(record.get(4));
			if (h2Arr != null && h2Arr.length > 0) {
				disSiteCrawlDoc1Entity.setH2(new ClickHouseArray(ClickHouseDataType.String, h2Arr));
			}
			return disSiteCrawlDoc1Entity;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}

	private void getEmbedding(List<DisSiteCrawlDoc1Entity> resultList) {
		try {
			threadPool.init();
			CommonUtils.initThreads(100);

			DisSiteCrawlDoc1Entity[] disSiteCrawlDoc1EntityArray = resultList.toArray(new DisSiteCrawlDoc1Entity[resultList.size()]);

			do {

				String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
				if (ipAddress == null) {
					Thread.sleep(1000);
					continue;
				}

				if (disSiteCrawlDoc1EntityArray == null || disSiteCrawlDoc1EntityArray.length == 0) {
					break;
				}

				DisSiteCrawlDoc1Entity[] commandData = (DisSiteCrawlDoc1Entity[]) ArrayUtils.subarray(disSiteCrawlDoc1EntityArray, 0, 1);
				disSiteCrawlDoc1EntityArray = (DisSiteCrawlDoc1Entity[]) ArrayUtils.remove(disSiteCrawlDoc1EntityArray, 0);

				Clarity360EmbeddingCrawlerCommand crawCommand = getUpdateCommand(ipAddress, commandData[0]);
				try {
					threadPool.execute(crawCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}

			} while (true);

			do {
				try {
					Thread.sleep(5000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			} while (threadPool.getThreadPool().getActiveCount() > 0);

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
		}

		List<EmbeddingEntity> embeddingEntities = CacheModleFactory.getInstance().getEmbeddingMap();
		if (CollectionUtils.isNotEmpty(embeddingEntities)) {

			try {
				if (CollectionUtils.isNotEmpty(embeddingEntities)) {
					clarity360LwebVector05DAO.insertBatch(embeddingEntities, finalTableName);
					System.out.println("finish insert for left count :" + embeddingEntities.size());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}

			embeddingEntities.clear();
			CacheModleFactory.getInstance().clearEmbeddingMapCache();
		}
	}

	/*private void process() {

		List<SemanticSimilarityProject> resultList = semanticSimilarityProjectDAO.getProjectList();

		if (CollectionUtils.isEmpty(resultList)) {
			System.out.println("No task need to process!");
			return ;
		}
		SemanticSimilarityProject semanticSimilarityProject = resultList.get(0);

		semanticSimilarityProjectDAO.updateStartProcess(semanticSimilarityProject.getId());

		processOne(semanticSimilarityProject.getDomainId(), semanticSimilarityProject.getCrawlRequestId());

		semanticSimilarityProjectDAO.updateProcessDone(semanticSimilarityProject.getId());

		System.out.println("======= process Done! projectId: " + semanticSimilarityProject.getId());
	}
	
	public void processOne(Integer ownDomainId, Integer crawlRequestLogId) {
		
		
		Integer pageNum = 1;
		while (true) {
			
			System.out.println("===== processing ownDomainId:" + ownDomainId);
			System.out.println("===== processing crawlRequestLogId:" + crawlRequestLogId);
			System.out.println("===== processing pageSize:" + pageSize);
			System.out.println("===== processing pageNum:" + pageNum);
			// Alps: only query for desktop and national
			List<DisSiteCrawlDoc1Entity> resultList = disSiteCrawlDoc1Dao.getByDomainIdAndCrawlRequestId(
					ownDomainId, crawlRequestLogId, pageSize, pageNum);
			
			System.out.println("resultList size:" + resultList.size());

			if (resultList == null || resultList.size() == 0) {
				break;
			}
			
			pageNum++;
			System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize + ", size:" + resultList.size());

			try {
				threadPool.init();
				CommonUtils.initThreads(100);
				
				DisSiteCrawlDoc1Entity[] disSiteCrawlDoc1EntityArray = resultList.toArray(new DisSiteCrawlDoc1Entity[resultList.size()]);
				
			 	do {
		
					String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
					if (ipAddress == null) {
						Thread.sleep(1 * 1000);
						continue;
					}
					
					if (disSiteCrawlDoc1EntityArray == null || disSiteCrawlDoc1EntityArray.length == 0) {
						break;
					}
					
					DisSiteCrawlDoc1Entity[] commandData = (DisSiteCrawlDoc1Entity[]) ArrayUtils.subarray(disSiteCrawlDoc1EntityArray, 0, 1);
					disSiteCrawlDoc1EntityArray = (DisSiteCrawlDoc1Entity[]) ArrayUtils.remove(disSiteCrawlDoc1EntityArray, 0);
				
					Clarity360EmbeddingCrawlerCommand crawCommand = getUpdateCommand(ipAddress, commandData[0]);
					try {
						threadPool.execute(crawCommand); 
					} catch (Exception e) {
						e.printStackTrace();
					}
		
				} while (true);
				
				do {
					try {
						Thread.sleep(5000);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				} while (threadPool.getThreadPool().getActiveCount() > 0);
				
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				threadPool.destroy();
			}
			
			List<EmbeddingEntity> embeddingEntities = CacheModleFactory.getInstance().getEmbeddingMap();
			if (CollectionUtils.isNotEmpty(embeddingEntities)) {
				
				try {
					if (CollectionUtils.isNotEmpty(embeddingEntities)) {
		          		clarity360LwebVector05DAO.insertBatch(embeddingEntities, finalTableName);
		              	System.out.println("finish insert for left count :" + embeddingEntities.size());
		          	}
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				embeddingEntities.clear();
				CacheModleFactory.getInstance().clearEmbeddingMapCache();
			}
			
		}
	}*/
	
	private Clarity360EmbeddingCrawlerCommand getUpdateCommand(String ipAddress, DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity) {
		Clarity360EmbeddingCrawlerCommand crawlCommand = new Clarity360EmbeddingCrawlerCommand(ipAddress, disSiteCrawlDoc1Entity);
		crawlCommand.setStatus(true);
		return crawlCommand;
	}

	private String[] getStringArr(String str) {
		if (StringUtils.isBlank(str) || !str.contains("[") || !str.contains("]") || StringUtils.equals(str, "[]")) {
			return null;
		}

		String[] array = str.substring(1, str.length() - 2).split(",");
		String[] stringArray = new String[array.length];

		for (int i = 0; i < array.length; i++) {
			stringArray[i] = array[i].replaceAll("'", "").trim();
		}
		return stringArray;
	}

	private List<String> getStringList(String str) {
		if (StringUtils.isBlank(str) || !str.contains("[") || !str.contains("]") || StringUtils.equals(str, "[]")) {
			return null;
		}

		String[] array = str.substring(1, str.length() - 2).split(",");
		List<String> stringArray = new ArrayList<>();

		for (int i = 0; i < array.length; i++) {
			stringArray.add(array[i].replaceAll("'", "").trim());
		}
		return stringArray;
	}
	

}
