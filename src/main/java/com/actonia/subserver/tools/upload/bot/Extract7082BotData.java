package com.actonia.subserver.tools.upload.bot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO.BotDataVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.Extract765BotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2018-12-04"
 */
@CommonsLog
public class Extract7082BotData extends AbstractExtractBotCommonJsonFile {

    CSVFormat csvFormat = CSVFormat.DEFAULT;
    private static Date processDate = new Date();
    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static String domain = "career.nikkei.co.jp";

    private static Integer version = 0;

    public Extract7082BotData() {
        super();
        //@TODO first 7082
        domainMaps.put(7082, domain);
        domainFileNameMap.put(7082, Arrays.asList("ncn_iis_googlebot_%s.csv"));
    }

    public static void main(String[] args) {
        Extract7082BotData extract7082BotData = new Extract7082BotData();
        if (args != null & args.length > 0) {
            if (StringUtils.containsIgnoreCase(args[0], ",")) {
                Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
                Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
                if (args.length > 1) {
                    localPath = args[1];
                }
                while (sDate.compareTo(eDate) <= 0) {
                    processDate = logDate = sDate;
                    extract7082BotData.startProcess(true, false);
                    sDate = DateUtils.addDays(sDate, 1);
                }
            } else {
                processDate = logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
                if (args.length > 1) {
                    localPath = args[1];
                }
                extract7082BotData.startProcess(true, false);
            }

            if (args.length > 2) {
                version = NumberUtils.toInt(args[2]);
            }
            extract7082BotData.waitForThreadPool();
        } else {
            processDate = logDate = DateUtils.addDays(new Date(), -1);
            extract7082BotData.startProcess(false, true, null, null, false);
        }
    }

    //@TODO second DateFormat
    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId + "/";
    }

    @Override
    protected List<String> getRemoteFileNames(int domainId, Date logDate) {
        List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            //@TODO Third filepath
            fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, "yyyyMMdd")));
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return "/tmp/";
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException {
        if (StringUtils.containsIgnoreCase(line, "date,time,s-ip,")) {
            log.info("SKIP header line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error : " + line);
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            //@TODO
            String ip = csvRecord.get(8);
            if (null == ip || "".equals(ip)) {
                ip = "0.0.0.0";
            }
            String date = csvRecord.get(0);
            String time = csvRecord.get(1);
            String url = csvRecord.get(11);
            //@TODO
            if (null == url || "".equals(url)) {
                url = csvRecord.get(4);
            }
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }
            String status = csvRecord.get(12);
            String userAgent = csvRecord.get(9);


            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            //@TODO
            Date datetime = formatter.parse(date + " " + time);
            long timestamp = datetime.getTime();

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(url) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
                System.out.println(ip + "\n" + date + "\n" + url + "\n" + status + "\n" + userAgent + "\n" + time + "\n" + timestamp);
                return null;
            }

            /*domainName = FormatUtils.getDomainByUrl(uri, false);*/

            String uri = FormatUtils.getUriFromUrl(url);

            BotJsonVO jsonVO = new BotJsonVO();
            BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);

            dataVO.setVersion(version);

            dataVO.setDate(FormatUtils.formatDate(datetime, "yyyy-MM-dd HH:mm:ss"));

            dataVO.setTimestamp(String.valueOf(timestamp));
            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);

            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error line : " + line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return true;
    }

}
