package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * com.actonia.subserver.tools.upload.newbot.Extract10769BotData
 * example:
 *  "2022-10-02 00:00:00 198.55.42.16 GET /content/images/bg-texture-light.jpg - 443 - 72.132.215.157 Mozilla/5.0+(Macintosh;+Intel+Mac+OS+X+10_15_7)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/103.0.0.0+Safari/537.36 https://www.theparkingspot.com/content/reserve.min.a25a469a.css 200 0 0 67",
 *  "2022-10-02 00:00:00 198.55.42.16 GET /content/images/radio-check-mark-un-checked.jpg - 443 - 72.132.215.157 Mozilla/5.0+(Macintosh;+Intel+Mac+OS+X+10_15_7)+AppleWebKit/537.36+(KHTML,+like+Gecko)+Chrome/103.0.0.0+Safari/537.36 https://www.theparkingspot.com/content/reserve.min.a25a469a.css 200 0 0 68"
 */
@CommonsLog
public class Extract10769BotData extends AbstractExtractBotCommonJsonFile {

    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(' ');
    private static final SimpleDateFormat dataFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static String domain = "www.theparkingspot.com";
    private static int domainId = 10769;
    private static Integer version = 0;

    private static String localPath = "/opt/bot/";
    private static Date logDate;

    public Extract10769BotData() {
        super();
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        domainMaps.put(10769, domain);
        domainFileNameMap.put(10769, Arrays.asList("u_ex%s_theparkingspot_com_weblog.log"));
        fileNamePatten = "yyMMdd";

        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot 10769, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            bot.startProcess(false, true);
        }
    }


    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/" + domainId;
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        List<Map<String, String>> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
        for (String fileName : domainFileNameMap.get(domainId)) {
            Map<String, String> fileNameMap = new HashMap<>();
            fileNameMap.put(String.format(fileName, FormatUtils.formatDate(logDate, fileNamePatten)), fileName);
            fileNames.add(fileNameMap);
        }
        return fileNames;
    }

    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.startsWith(line, "#")) {
            log.info("SKIP special line : " + line);
            return null;
        }
        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
                log.error("Lines format error!");
                return null;
            }
            CSVRecord csvRecord = csvRecords.get(0);

            String ip = csvRecord.get(8).trim();
            String dateStr = csvRecord.get(0).trim();
            String timeStr = csvRecord.get(1).trim();
            String dateTimeStr = dateStr + " " + timeStr;
            Date date = dataFormatter.parse(dateTimeStr);
            String status = csvRecord.get(11);
            String userAgent = csvRecord.get(9);
            String url = csvRecord.get(4);
            if (url.equals("-")) {
                return null;
            }
            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }
            long timestamp = date.getTime();
            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                return null;
            }

            String uri = FormatUtils.getUriFromUrl(url);
            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(dateStr) ||
                    StringUtils.isEmpty(domainName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.isEmpty(url) ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + dateStr + "\n" + uri + "\n" + status + "\n" + userAgent + "\n" + date + "\n" + timestamp);
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(domain);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            if (null != domainId && domainId > 0) {
                dataVO.setDomainId(domainId);
            }
            dataVO.setDisableTimeZone(true);
            dataVO.setUseFileContextDate(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            e.printStackTrace();
            try {
                Thread.sleep(10);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            log.error("Error line : " + line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return false;
    }

}
