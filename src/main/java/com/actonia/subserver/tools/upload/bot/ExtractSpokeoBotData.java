package com.actonia.subserver.tools.upload.bot;

import cn.hutool.core.date.DateUtil;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.ExtractMechaBotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2017-11-21"
 */
@CommonsLog
public class ExtractSpokeoBotData extends AbstractExtractBotCommonJsonFile {

	private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss", Locale.ENGLISH);

	private static Date logDate;
	private static String localPath = "/opt/bot/";
	private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t');


	private static BotDetailDao botDetailDao;

	public ExtractSpokeoBotData() {
		super();
		domainMaps.put(8422, "www.spokeo.com");
		domainFileNameMap.put(8422, Arrays.asList("bot_access_log_%s"));

	}

	public static void main(String[] args) {
		ExtractSpokeoBotData extractMechaBotData = new ExtractSpokeoBotData();
		
		for(int i = 69; i < 100; i ++) {
			
			String fileName = "/home/<USER>/bingbot-" + (i < 10 ? "0" + i : i);
			
			System.out.println(" processing on : " + fileName);
			try {
				List<String> lines = IOUtils.readLines(new FileInputStream(fileName));
//				List<String> lines = IOUtils.readLines(new FileInputStream("/home/<USER>/Googlebot.txt"));
//				List<String> lines = IOUtils.readLines(new FileInputStream("/home/<USER>/bingbot.txt"));
				String basePath = "/home/<USER>/spokeo/";
				for (String line : lines) {
//					if (StringUtils.startsWithIgnoreCase(line, "eventtime,timestamp,statusCode")) {
//						System.out.println("skip for line : " + line);
//						continue;
//					}
					
					BotJsonVO botJsonVO = extractMechaBotData.parserLineToVO(line, "www.spokeo.com", 8422);
					if (null == botJsonVO) {
						continue;
					}
					String json = new Gson().toJson(botJsonVO);
					String day = DateUtil.parse(botJsonVO.getData().getDate(), "yyyy-MM-dd HH:mm:ss").toString("yyyyMMdd");
					File base = new File(basePath+day+"/00/");
					if (!base.exists()) {
						base.mkdirs();
					}

					File f = new File(basePath+day+"/00/"+day+"_"+7135+"_log.json");
					FileUtils.write(f, json+"\n", true);
				}

			} catch (IOException e) {
				e.printStackTrace();
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		

//		if (args != null & args.length > 0) {
//			if(StringUtils.containsIgnoreCase(args[0], ",")) {
//				Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
//				Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
//				if (args.length > 1) {
//					localPath = args[1];
//				}
//				while(sDate.compareTo(eDate) <= 0) {
//					logDate = sDate;
//					processDate = logDate;
//					extractMechaBotData.startProcess(true, false);
//					sDate = DateUtils.addDays(sDate, 1);
//				}
//			} else {
//				logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
//				processDate = logDate;
//				if (args.length > 1) {
//					localPath = args[1];
//				}
//				extractMechaBotData.startProcess(true, false);
//			}
//			extractMechaBotData.waitForThreadPool();
//		} else {
//            logDate = DateUtils.addDays(new Date(), -1);
//			processDate = logDate;
//			extractMechaBotData.startProcess(false);
//        }
	}

	@Override
	protected Date getBotLogDate() {
		return logDate;
	}

	@Override
	protected String getRemoteFilePath(int domainId) {
		return "/home/<USER>/"+domainId;
	}

	@Override
	protected List<String> getRemoteFileNames(int domainId, Date logDate) {
		List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
		for (String fileName : domainFileNameMap.get(domainId)) {
			fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, "yyyyMMdd")));
		}
		return fileNames;
	}

	@Override
	protected String getLocalFilePath() {
		return "/tmp/";
	}

	@Override
	protected BotJsonVO parserLineToVO(String rawLine, String domainName, Integer domainId) throws ParseException {
		
		List<CSVRecord> csvRecords;
		try {
			CSVParser csvParser = CSVParser.parse(rawLine, csvFormat);
			csvRecords = csvParser.getRecords();
			if (csvRecords.size() > 1) {
				log.error("Lines format error : "+rawLine);
				return null;
			}
			CSVRecord csvRecord = csvRecords.get(0);

			String date = csvRecord.get(0);
			String time = csvRecord.get(1);
			String ip = csvRecord.get(4);
			String uri = csvRecord.get(7);
			String status = csvRecord.get(8);
			String userAgent = csvRecord.get(10);
			String domain = csvRecord.get(15);
			
			userAgent = FormatUtils.decodeKeyword(FormatUtils.decodeKeyword(userAgent));

			int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
			if (uaGroupId < 0) {
				return null;
			}
			if (UrlFilterUtil.shouldSkipUrl(uri)) {
				return null;
			}

			Date dateTime = formatter.parse(date + " " + time);
			long timestamp = dateTime.getTime();

			if(StringUtils.isEmpty(ip) ||
					StringUtils.isEmpty(domain) ||
					StringUtils.isEmpty(status) ||
					StringUtils.isEmpty(userAgent) ||
					StringUtils.isEmpty(uri) ||
					StringUtils.isEmpty(String.valueOf(timestamp))) {
				System.out.println(ip+"\n"+date+"\n"+uri+"\n"+status+"\n"+userAgent+"\n"+dateTime+"\n"+timestamp);
				return null;
			}
			
			BotJsonVO jsonVO = new BotJsonVO();
			BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
			dataVO.setUaGroupId(uaGroupId);
			dataVO.setOriginIP(ip);
			dataVO.setReqHost(domain);
			dataVO.setStatus(status);
			dataVO.setUserAgent(userAgent);
			dataVO.setUserAgentStr(userAgent);
			dataVO.setReqPath(uri);
			//Leo - https://www.wrike.com/open.htm?id=186725979
			dataVO.setDate(FormatUtils.formatDate(dateTime, "yyyy-MM-dd HH:mm:ss"));
			dataVO.setTimestamp(String.valueOf(timestamp));
			if (null != domainId && domainId > 0) {
				dataVO.setDomainId(domainId);
			}
			dataVO.setDisableTimeZone(true);

			jsonVO.setData(dataVO);
			return jsonVO;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("Error line : "+rawLine);
			return null;
		}
	}

	@Override
	protected String getLocalGzFilePath() {
		return localPath;
	}


	private String getValueByPattern(String pattern, String str) {
		Pattern r = Pattern.compile(pattern);
		Matcher m = r.matcher(str);
		while (m.find()) {
			return m.group();
		}
		return null;
	}
}
