package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;

/**
 * com.actonia.subserver.tools.upload.newbot.Extract888HoldingsBotData
 * @Desc Load bot file for 888 Holdings, the bot log file is saved in s3
 *
 */

@CommonsLog
public class Extract888HoldingsBotData extends AbstractExtractBotCommonJsonFile {

    private static final SimpleDateFormat dataDateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static Date logDate;
    private static String localPath = "/opt/bot/";
    private static int domainId = 1884;

    private static String domain = "888 Holdings";
    private static Integer version = 0;
    private static Map<String, Integer> _888DomainMap = new HashMap<>();

    public Extract888HoldingsBotData() {
        super();

        s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
        s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
        s3BusketName = "com.prod.eu-west-1.web.real.siteinfra-logs.bucket";

        domainMaps.put(domainId, domain);
        domainFileNameMap.put(domainId, Arrays.asList("%s-"));
        fileNamePatten = "yyyy-MM-dd";

        getDomainMap();
    }

    private void getDomainMap() {
        List<OwnDomainEntity> basedCompanyNameList = ownDomainEntityDAO.getDomainListBasedCompanyName(domain);
        System.out.println(basedCompanyNameList);
        for (OwnDomainEntity ownDomainEntity : basedCompanyNameList) {
            String domainNameFromDB = ownDomainEntity.getDomain();
            Integer id = ownDomainEntity.getId();
            if (!_888DomainMap.containsKey(domainNameFromDB)) {
                _888DomainMap.put(domainNameFromDB, id);
            }
        }
    }

    @Override
    public void initParam(Date date, int version) {
        this.logDate = date;
        this.version = version;
        this.isTestFlag = false;
        if (isTestFlag) {
            /**
             * custom temporary file path
             */
            tempFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
        }
    }

    @Override
    public void exec(boolean backProcess, AbstractExtractBotCommonJsonFile bot) {
        log.info("start process bot for 888 Holdings, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (backProcess) {
            bot.startProcess(true, true);
        } else {
            // for 888, we should process the day before yesterday's bot log after 14 o'clock every day
            LocalTime now = LocalTime.now();
            if (now.getHour() < 14) {
                return;
            }
            bot.startProcess(false, true);
        }
    }

    @Override
    protected boolean isDownloadFromS3(){
        return true;
    }

    protected boolean isFolder() {
        return true;
    }

    @Override
    protected Date getBotLogDate() {
        return logDate;
    }

    @Override
    protected boolean isDynamicFileName() {
        return true;
    }

    @Override
    protected String getRemoteFilePath(int domainId) {
        return "/home/<USER>/"+domainId+"/";
    }

    @Override
    protected List<Map<String, String>> getRemoteFileNames(int domainId, Date logDate) {
        Map<String, String> folderMap = getFolderMap();
        List<Map<String, String>> list = new ArrayList<>();
//        list.add(folderMap);
        for (String fileFolder : folderMap.keySet()) {
            Map<String, String> result = new HashMap<>();
            String symbol = folderMap.get(fileFolder);
            System.out.println(symbol);
            result.put(String.format(symbol, FormatUtils.formatDate(logDate, fileNamePatten)), symbol);
            list.add(result);
        }
        System.out.println(list);
        return list;
    }


    @Override
    protected String getLocalFilePath() {
        return tempFileFolder;
    }

    @Override
    protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId, Date logDate) throws ParseException {
        if (StringUtils.containsIgnoreCase(line, "#Version") || StringUtils.containsIgnoreCase(line, "#Fields")) {
            log.info("SKIP header line : " + line);
            return null;
        }

        CSVFormat csvFormat;

        if (line.contains("\t")) {
            csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true);
        } else {
            csvFormat = CSVFormat.DEFAULT.withDelimiter(' ').withIgnoreEmptyLines(true);
            line = formatLine(line);
        }

        List<CSVRecord> csvRecords;
        try {
            CSVParser csvParser = CSVParser.parse(line, csvFormat);
            csvRecords = csvParser.getRecords();
            if (csvRecords.size() > 1) {
//                log.error("Lines format error : " + line);
                skipCnt2++;
                return null;
            }

            CSVRecord csvRecord = csvRecords.get(0);
            String ip = csvRecord.get(4);
            String dateStr = csvRecord.get(0) + " " + csvRecord.get(1);
            String uri = csvRecord.get(7);
            String userAgent = csvRecord.get(10);
            String status = csvRecord.get(8);
            String url = csvRecord.get(9);

            Date datetime = dataDateFormatter.parse(dateStr);
            long timestamp = datetime.getTime();

            if (url.equals("-")) {
                skipCnt1++;
//                log.info("url eq -, skip, cnt: " + skipCnt1);
                return null;
            }

            if (UrlFilterUtil.shouldSkipUrl(url)) {
                return null;
            }

            String protocol = FormatUtils.getProtocolFromUrl(url);

            int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
            if (uaGroupId < 0) {
                skipCnt4++;
//                log.info("ua not match, skip, cnt: " + skipCnt4);
                return null;
            }

            List<String> domainNameListForCurrentFolder = getDomainNameListByCurrentFolder(currFolder);

            if (null == domainNameListForCurrentFolder || domainNameListForCurrentFolder.size() == 0) {
                skipCnt2++;
//                log.info("domain not match, skip, cnt: " + skipCnt2);
                return null;
            }

            boolean existInClientCfDomainName = false;
            for (String clientCfDomainName : domainNameListForCurrentFolder) {
                if (url.contains(clientCfDomainName)) {
                    existInClientCfDomainName = true;
                }
            }
            if (!existInClientCfDomainName) {
//                System.out.println("url not contains list domain: " + url);
                skipCnt2++;
//                log.info("domain not match2, skip, cnt: " + skipCnt2);
                return null;
            }

            int oid = 0;
            String oName = "";

            boolean existInDBDomain = false;
            for (String domainNameFromDB : _888DomainMap.keySet()) {
                if (url.contains(domainNameFromDB)) {
                    existInDBDomain = true;
                    oName = domainNameFromDB;
                    oid = _888DomainMap.get(domainNameFromDB);
                }
            }

            if (!existInDBDomain) {
//                System.out.println("url not contains db domain: " + url);
                skipCnt2++;
//                log.info("domain not match3, skip, cnt: " + skipCnt2);
                return null;
            }

            if (StringUtils.isEmpty(ip) ||
                    StringUtils.isEmpty(oName) ||
                    StringUtils.isEmpty(status) ||
                    StringUtils.isEmpty(userAgent) ||
                    StringUtils.endsWithIgnoreCase(userAgent, "-") ||
                    StringUtils.isEmpty(url) ||
                    StringUtils.endsWithIgnoreCase(url,"-") ||
                    StringUtils.isEmpty(String.valueOf(timestamp))) {
//                System.out.println(ip + "\n" + domainName + "\n" + dateStr + "\n"
//                        + url + "\n" + status + "\n" + userAgent + "\n" + datetime + "\n" + timestamp);
                skipCnt3++;
//                log.info("missing data, skip, cnt: " + skipCnt3);
//                if (skipCnt3 <= 100) {
//                    log.info("missing data line: " + line);
//                }
                return null;
            }

            BotJsonVO jsonVO = new BotJsonVO();
            BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
            dataVO.setUaGroupId(uaGroupId);
            dataVO.setOriginIP(ip);
            dataVO.setReqHost(oName);
            dataVO.setStatus(status);
            dataVO.setUserAgent(userAgent);
            dataVO.setUserAgentStr(userAgent);
            dataVO.setReqPath(uri);
            dataVO.setVersion(version);
            dataVO.setDate(FormatUtils.formatDate(datetime, "yyyy-MM-dd HH:mm:ss"));
            dataVO.setTimestamp(String.valueOf(timestamp));
            dataVO.setReqProtocol(protocol);
            dataVO.setUseFileContextDate(true);

            if (oid > 0) {
                dataVO.setDomainId(oid);
            }else {
                return null;
            }
            dataVO.setDisableTimeZone(true);
            jsonVO.setData(dataVO);
            return jsonVO;
        } catch (Exception e) {
            errorCnt++;
            e.printStackTrace();
//            log.error("Error line : " + line);
            return null;
        }
    }

    @Override
    protected String getLocalGzFilePath() {
        return localPath;
    }

    @Override
    protected boolean isMultiFile() {
        return true;
    }

    private Map<String, String> getFolderMap() {
        Map<String, String> map = new HashMap<>();
        // 文件夹->前缀
        map.put("consumed-cloudfront-logs/prod-casino-mirror/", "consumed-cloudfront-logs/prod-casino-mirror/E137J4077BV6EB.%s-");
        map.put("consumed-cloudfront-logs/prod-casino/", "consumed-cloudfront-logs/prod-casino/EF1EBN87GKDBZ.%s-");
        map.put("consumed-cloudfront-logs/prod-nooff-mirror/", "consumed-cloudfront-logs/prod-nooff-mirror/E2KFO67LJBJTJ5.%s-");
        map.put("consumed-cloudfront-logs/prod-nooff/", "consumed-cloudfront-logs/prod-nooff/E2G699HPLC9RVS.%s-");
        map.put("consumed-cloudfront-logs/prod-poker-mirror/", "consumed-cloudfront-logs/prod-poker-mirror/E3TX87MDZRQ098.%s-");
        map.put("consumed-cloudfront-logs/prod-poker/", "consumed-cloudfront-logs/prod-poker/E1SDCJ42KR4S06.%s-");
        map.put("consumed-cloudfront-logs/prod-sport-mirror/", "consumed-cloudfront-logs/prod-sport-mirror/E9NO5PT8CSRNG.%s-");
        map.put("consumed-cloudfront-logs/prod-sport/", "consumed-cloudfront-logs/prod-sport/E2GBX3FXHKL91L.%s-");
        map.put("consumed-cloudfront-logs/siteedit-casino/", "consumed-cloudfront-logs/siteedit-casino/EP0XCG75X47GH.%s-");
        map.put("consumed-cloudfront-logs/siteedit-nooff/", "consumed-cloudfront-logs/siteedit-nooff/ESAMPLR8WYM9W.%s-");
        map.put("consumed-cloudfront-logs/siteedit-poker/", "consumed-cloudfront-logs/siteedit-poker/E3PAM0ECQIBPR4.%s-");
        map.put("consumed-cloudfront-logs/siteedit-sport/", "consumed-cloudfront-logs/siteedit-sport/EMNNIHVSZPYOD.%s-");
        map.put("consumed-cloudfront-logs/stage-casino/", "consumed-cloudfront-logs/stage-casino/E80FFM9B4BTPY.%s-");
        map.put("consumed-cloudfront-logs/stage-nooff/", "consumed-cloudfront-logs/stage-nooff/E34KU3DE64Y481.%s-");
        map.put("consumed-cloudfront-logs/stage-poker/", "consumed-cloudfront-logs/stage-poker/E286QNF9HCH4W9.%s-");
        map.put("consumed-cloudfront-logs/stage-sport/", "consumed-cloudfront-logs/stage-sport/E2B8JIR2X2RK8I.%s-");
        return map;
    }

    private List<String> getDomainNameListByCurrentFolder(String currFolder){

        Map<String, List<String>> folderPDomainSetMap = new HashMap<>();
        List<String> prodCasinoDomainSet = Arrays.asList(
                "888casino.it","www.888casino.ro","888casino.com","888casino.dk","fi.888casino.com","777.com","jp.888casino.com",
                "fi.777.com","fr.888casino.com","sv.888casino.com","aws-no.777.com","cns.888casino.com","pt.888casino.com",
                "ru.888casino.com","no.777.com","aws-fi.777.com","th.888casino.com","sv.777.com","www.777.com","aws-www.777.com",
                "play-www.777.com","aws-de.777.com","pl.888casino.com","es.888casino.com","de.888casino.com","no.888casino.com",
                "tw.888casino.com","hu.888casino.com","in.888casino.com","ph.888casino.com","br.888casino.com","de.777.com",
                "el.888casino.com","ar.888casino.com","aws-sv.777.com","ca.888casino.com","play-de.777.com","888slots.de",
                "www.888slots.de","bg.888casino.com","mailer.888casino.com","confirmation.888casino.com","ro.888casino.com",
                "lv.888casino.com","cz.888casino.com","e.888casino.com","it.888casino.com","info.888casino.com","da.888casino.com",
                "nl.888casino.com","rs.888casino.com","cs.888casino.com","lt.888casino.com","se.888casino.com","play-ar.777.com",
                "ar.777.com","888casino.ca","www.888casino.ca","casino.888.ca","dr-www.888casino.com","888casino.nl","www.888casino.nl"
        );
        folderPDomainSetMap.put("consumed-cloudfront-logs/prod-casino/", prodCasinoDomainSet);

        List<String> prodPokerDomainSet = Arrays.asList(
                "www.888poker.se","888poker.com","www.888poker.fr","poker.888.pt","888poker.it","888poker.es","ca.888poker.com",
                "fr.888poker.com","ro.888poker.com","ru.888poker.com","no.888poker.com","de.888poker.com","jp.888poker.com",
                "es.888poker.com","ua.888poker.com","sv.888poker.com","it.888poker.com","zh.888poker.com","rs.888poker.com",
                "cns.888poker.com","cs.888poker.com","br.888poker.com","fi.888poker.com","pt.888poker.com","hu.888poker.com",
                "888poker.net","www.888poker.net","mailer.888poker.com","de.888poker.net","es.888poker.net","br.888poker.net",
                "pl.888poker.com","my.888poker.com","el.888poker.com","free.888poker.com","ru.888poker.net","ua.888poker.net",
                "au.888poker.com","ja.888poker.com","uk.888poker.com","nl.888poker.com","fr.888poker.net","bg.888poker.com",
                "cz.888poker.com","ca.888poker.net","da.888poker.com","888poker.de","www.888poker.de","888poker.ca","www.888poker.ca",
                "poker.888.ca","www.888poker.nl","888poker.nl"
        );
        folderPDomainSetMap.put("consumed-cloudfront-logs/prod-poker/", prodPokerDomainSet);

        List<String> prodSportDomainSet = Arrays.asList(
                "888sport.dk","888sport.es","888sport.ro","poker.888sport.com","www.888sport.com","www.888sport.de","888sport.de",
                "mobile.888sport.com","spectate-www.888sport.com","new-www.888sport.com","ios.888sport.com","nuevo-www.888sport.es",
                "sport.888.ca","www.888sport.nl","888sport.nl","888sport.ca","www.888sport.ca","kambi-www.888sport.es"
        );
        folderPDomainSetMap.put("consumed-cloudfront-logs/prod-sport/", prodSportDomainSet);

        List<String> prodNooffDomainSet = Arrays.asList(
                "it.888.com","ru.888.com","hu.888.com","fi.888.com","www.888.se","888.pt","jp.888.com","888.de","uk.888.com","es.888.com","ar.888.com","sv.888.com","pl.888.com","888.com","nl.888.com","el.888.com","de.888.com","fr.888.com","www.888.it","www.888.es","lt.888.com","lv.888.com","ro.888.com","888.se","da.888.com","888.ro","www.888.ro","winkslots.com","www.winkslots.com","888vipcasinoclub.com","affiliates.888.com","au.888.com","888responsible.com","br.888vipcasinoclub.com","blog.888.com","bonus.888.com","ca.888.com","claim.888.com","gln1.888.com","de.888responsible.com","fi.payoutscentral.com","de.888vipcasinoclub.com","gr.888.com","fr.888vipcasinoclub.com","fr.payoutscentral.com","es.payoutscentral.com","da.payoutscentral.com","confirmation.888.com","de.payoutscentral.com","dragonfishtech.com","dk.888.com","es.888responsible.com","cz.888.com","es.888vipcasinoclub.com","download.888.com","cns.888vipcasinoclub.com","fr.888responsible.com","ee.888.com","cs.888.com","helppages-jp.safe-installation.com","ru.payoutscentral.com","worldsnooker.888.com","helppages-da.safe-installation.com","it.payoutscentral.com","pt.888vipcasinoclub.com","www101.888.com","www4.888.com","la.888.com","helppages-ro.safe-installation.com","helppages-cs.safe-installation.com","invite.888.com","ww1.888.com","www77.888.com","helppages-nl.safe-installation.com","www.payoutscentral.com","nl.payoutscentral.com","pt.888.com","helppages-de.safe-installation.com","mails.888.com","videoslots.888.com","helppages-es.safe-installation.com","np.payoutscentral.com","payoutscentral.com","rsbit.888.com","mx.888.com","www3.888.com","www.dragonfishtech.com","helppages-it.safe-installation.com","helppages-el.safe-installation.com","ro.payoutscentral.com","q.888.com","helppages-no.safe-installation.com","snooker.888.com","pt.payoutscentral.com","helppages-pt.safe-installation.com","se.888.com","www2.888.com","it.888responsible.com","latin.888.com","sv.888vipcasinoclub.com","helppages-ru.safe-installation.com","www.888responsible.com","java3.888.com","helppages-sv.safe-installation.com","ru.888vipcasinoclub.com","www102.888.com","helppages-cns.safe-installation.com","jp.888vipcasinoclub.com","shop.888.com","www.888vipcasinoclub.com","sv.payoutscentral.com","www55.888.com","helppages-fr.safe-installation.com","www1.888.com","helppages-www.safe-installation.com","at.888.com","no.payoutscentral.com","cz.payoutscentral.com","cns.payoutscentral.com","el.payoutscentral.com","pl.payoutscentral.com","jp.payoutscentral.com","temp-www.888.de","888.ca","888.nl","www.888.ca","www.888.nl"
        );
        folderPDomainSetMap.put("consumed-cloudfront-logs/prod-nooff/", prodNooffDomainSet);


        List<String> prodCasinoMirrorDomainSet = Arrays.asList(
                "cs.888casino-promotions.com","de.888casino-promotions.com","ar.arabic-triple-eight.com","ru.888online-casino-games.com","ar.triple-eight-games.live","casino-ru.888poker.com","ar.888slot-games.com","ca.888casino-promotions.com","ar.888casinoparty.com","casino-pt.888poker.com","ar.888casinoarabic.net","ar.arabiccasino888.com","ar.play-triple8.com","casino.888.com","th.888casino-promotions.com","casino-www.888sport.com","ar.playat888online.com","pt.888casino-promotions.com","ar.888-slots.net","casino-ru.888sport.com","el.888casino-promotions.com","casino-jp.888sport.com","casino-sv.888poker.com","ar.888-casinoarabic.com","casino-ca.888poker.com","ar.play-casino-now.com","casino-in.888poker.com","casino-fr.888poker.com","casino-ca.888sport.com","casino-es.888sport.com","ar.arabic-live-games.com","ar.fungames-888.com","no.888casino-promotions.com","casino-no.888poker.com","ru.888casinofreespin.com","ru.888realcasino.com","ar.playat888-games.com","casino-de.888poker.com","ar.888casinofun.com","casino-in.888sport.com","ru.888casinonow.com","casino-sv.888sport.com","sv.888casino-promotions.com","casino-br.888sport.com","casino-cns.888sport.com","casino.888.es","casino-tw.888poker.com","casino-de.888sport.com","casino-no.888sport.com","pl.888casino-promotions.com","www.888casino-promotions.com","casino-es.888poker.com","ar.888-uae.com","official-888.com","ar.tripleeight.live","ar.live-table-games.net","ar.888slots-uae.com","casino-tw.888sport.com","ar.888games-uae.com","jp.888casino-promotions.com","ru.888casino-game.com","ru.888casinoonline.org","ar.a7la-games.com","ar.888casino-games.com","casino-br.888poker.com","casino-fr.888sport.com","ar.triple-eight-games.com","casino-fi.888poker.com","casino-ph.888poker.com","casino-th.888sport.com","888casino-promotions.com","casino-cns.888poker.com","ar.casinoarabic888.com","ru.888best-casino.com","casino.888poker.com","es.888casino-promotions.com","casino-th.888poker.com","casino-fi.888sport.com","ro.888casino-promotions.com","ar.triple-eight-games.net","ar.a7la-games.net","ar.888casino-uae.com","casino.888sport.com","cns.888casino-promotions.com","ar.888casino-arabic.com","casino-ar.888sport.com","casino-ar.888poker.com","www.888casino-promociones.es","888casino-promociones.es","888casino-kampanjer.se","www.888casino-kampanjer.se","ar.play777home.com","ar.online7777ar.com","ar.777arabic.com","ar.777online-gaming.com","ar.777arabic-game.com","ar.777arab-online.com","ar.777gamesonline.com","ar.online777arab.com","ar.777partygames.com","ar.play777web.com","ar.777arabgames.com","ar.777arab.com","ar.play777star.com","ar.play777now.com","ar.online777arzone.com","ar.play777world.com","ar.777online-games.com","ar.777fungame.com","ar.web777ar.com","ar.online77ar.com"
        );
        folderPDomainSetMap.put("consumed-cloudfront-logs/prod-casino-mirror/", prodCasinoMirrorDomainSet);


        List<String> prodPokerMirrorDomainSet = Arrays.asList(
                "de.888pokereu.com","ru.888pokerall.com","poker.888.es","ru.our888poker.com","888poker-play.com","ru.888pokerfun.com","www.888pokerworld.com","ru.instantdraw.com","ru.winat888poker.com","ru.888pokerlogo.com","ru.888pokerremote.com","fr.888pokerworld.com","888poker.sn","ru.game888poker.com","ru.888pokerclub.com","ru.house888poker.com","ru.fun888poker.com","www.888poker.sv","888pokerworld.com","ru.888pokerblue.com","ru.ru888poker.com","ro.888mega.com","www.888poker-play.com","www.888poker.sn","ru.888pokerroom.com","ru.888pokerworld.com","temp-www.888poker.se","www.888mega.com","ru.888pokerglobal.com","ru.super888poker.com","ru.win888poker.com","ru.888mega.com","www.mark888poker.com","ru.play-888poker.com","www.playon888poker.com","www.memo888poker.com","ru.goplay888poker.com","ru.pokerpoint.com","ru.max888poker.com","ru.888pokerace.com","ru.888pokernetwork.com","ru.888pokertours.com","ru.play888poker.com","ru.888pokerhands.com","ru.official888poker.com","888poker-ar.com","ru.love888poker.com","ru.poker247.com","www.888pokereu.com","ru.enjoy888poker.com","www.pokerpoint.com","ru.888mobilepoker.com","ru.888pokercams.com","ru.joy888poker.com","es.888pokerworld.com","ru.888pokerfame.com","888mega.com","ru.888pokereu.com","www.playon-888poker.com","de.888pokerworld.com","ru.888pokerwin.com","ru.club888poker.com","sv.888pokerworld.com","www.888poker-promotions.com","www.goplay888poker.com","ru.888pokercontrol.com","www.888poker-ar.com","ru.playon888poker.com","www.play888poker.com","ru.bonus888poker.com","ru.team888poker.com","888poker-promotions.com","temp-www.888poker.dk","ru.888pokerrush.com","ru.888poker247.com","ru.888pokerweek.com","ru.888betpoker.com","ru.pokerled.com","ru.888pokeryear.com","ru.888pokerhand.com","ru.888pokernight.com","ru.888pokerforall.com","ru.888pokerday.com","ru.highwaypoker.com","ru.888pokerbets.com","ru.888poker-promotions.com"
        );
        folderPDomainSetMap.put("consumed-cloudfront-logs/prod-poker-mirror/", prodPokerMirrorDomainSet);


        List<String> prodSportMirrorDomainSet = Arrays.asList(
                "www.tripleeightssport.com","sport.888.com","www.eighteighteightsport.com","www.tamanyatamanyatamanyasport.com","tripleeightssport.com","www.x3tamanyasport.com","www.tripletamanya.com","sport.888.es"
        );
        folderPDomainSetMap.put("consumed-cloudfront-logs/prod-sport-mirror/", prodSportMirrorDomainSet);


        List<String> prodNooffMirrorDomainSet = Arrays.asList(
                "www3.cassava.net","www.eighteighteight.com","www.wagerguide.com","www.888.fm"
        );
        folderPDomainSetMap.put("consumed-cloudfront-logs/prod-nooff-mirror/", prodNooffMirrorDomainSet);

        if (currFolder == null) {
            return null;
        }

        for (String listFolder : folderPDomainSetMap.keySet()) {
            if (currFolder.contains(listFolder)) {
                return folderPDomainSetMap.get(listFolder);
            }
        }

        return null;
    }

    private static String formatLine(String line){
        char[] chars = line.toCharArray();
        char lastCh = '*';
        StringBuilder sbd = new StringBuilder();
        for (char aChar : chars) {
            if (aChar == ' ' && lastCh == ' ') {

            } else {
                sbd.append(aChar);
            }
            lastCh = aChar;
        }
        return sbd.toString();
    }

}


/**
 *
 * file prefix
 *
 * PRE prod-casino-mirror/
 * 	consumed-cloudfront-logs/prod-casino-mirror/E137J4077BV6EB.2022-03-25-23.56073bd8.gz
 * PRE prod-casino/
 * 	consumed-cloudfront-logs/prod-casino/EF1EBN87GKDBZ.2022-03-25-23.18f1f7df.gz
 * PRE prod-nooff-mirror/
 * 	consumed-cloudfront-logs/prod-nooff-mirror/E2KFO67LJBJTJ5.2022-03-26-00.4b4db503.gz
 * PRE prod-nooff/
 * 	consumed-cloudfront-logs/prod-nooff/E2G699HPLC9RVS.2022-03-25-23.90610ac8.gz
 * PRE prod-poker-mirror/
 * 	consumed-cloudfront-logs/prod-poker-mirror/E3TX87MDZRQ098.2022-03-25-23.413a9e37.gz
 * PRE prod-poker/
 * 	consumed-cloudfront-logs/prod-poker/E1SDCJ42KR4S06.2022-03-25-23.49d4cc0f.gz
 * PRE prod-sport-mirror/
 * 	consumed-cloudfront-logs/prod-sport-mirror/E9NO5PT8CSRNG.2022-03-25-23.3f7a2c10.gz
 * PRE prod-sport/
 * 	consumed-cloudfront-logs/prod-sport/E2GBX3FXHKL91L.2022-03-25-23.02fda63d.gz
 * PRE siteedit-casino/
 * 	consumed-cloudfront-logs/siteedit-casino/EP0XCG75X47GH.2022-03-26-01.d78322d8.gz
 * PRE siteedit-nooff/
 * 	consumed-cloudfront-logs/siteedit-nooff/ESAMPLR8WYM9W.2022-03-26-02.5eefbdf2.gz
 * PRE siteedit-poker/
 * 	consumed-cloudfront-logs/siteedit-poker/E3PAM0ECQIBPR4.2022-03-26-00.19d1a91c.gz
 * PRE siteedit-sport/
 * 	consumed-cloudfront-logs/siteedit-sport/EMNNIHVSZPYOD.2022-03-26-00.091b66a6.gz
 * PRE stage-casino/
 * 	consumed-cloudfront-logs/stage-casino/E80FFM9B4BTPY.2022-03-25-23.ec7d1499.gz
 * PRE stage-nooff/
 * 	consumed-cloudfront-logs/stage-nooff/E34KU3DE64Y481.2022-03-25-23.b97a73fb.gz
 * PRE stage-poker/
 * 	consumed-cloudfront-logs/stage-poker/E286QNF9HCH4W9.2022-03-25-23.76f4b329.gz
 * PRE stage-sport/
 * 	consumed-cloudfront-logs/stage-sport/E2B8JIR2X2RK8I.2022-03-25-23.3a937074.gz
 */