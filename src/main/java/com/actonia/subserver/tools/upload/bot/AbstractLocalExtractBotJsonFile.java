package com.actonia.subserver.tools.upload.bot;

import cn.hutool.core.lang.Assert;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @create 2018-06-10 19:43
 **/
@CommonsLog
public abstract class AbstractLocalExtractBotJsonFile {

    protected Gson gson = new Gson();
//    private String sepa = java.io.File.separator;


    protected abstract String[] getInputDirPath();
    protected abstract String getOutPutDirPath();
    protected abstract int getDomainId();

    protected abstract String getIP(String line);
    protected abstract String getUserAgent(String line);
    protected abstract String getStatus(String line);
    protected abstract String getUri(String line);
    protected abstract Date getDate(String line) throws ParseException;
    protected abstract String getHost(String line);
//    protected abstract String pretreatment(String line);

    protected boolean isBotAgent(String userAgent) {
        return StringUtils.containsIgnoreCase(userAgent, "bot")
                || StringUtils.containsIgnoreCase(userAgent, "spider")
                || StringUtils.containsIgnoreCase(userAgent, "crawler");
    }

    protected String getValueByPattern(String pattern, String str) {
        Pattern r = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
        Matcher m = r.matcher(str);
        while (m.find()) {
            return m.group();
        }
        return null;
    }

    public void process() throws IOException {

        for (String dirPath : getInputDirPath()) {
            File dir = new File(dirPath);
            Assert.isTrue(dir.isDirectory());
            Assert.notNull(dir.listFiles());

            for (File file : dir.listFiles()) {
                log.info("Processing :"+file.getName());
                if (!file.isFile()) {
                    continue;
                }
                List<String> lines = IOUtils.readLines(new FileInputStream(file));
                if(CollectionUtils.isEmpty(lines)) {
                    log.error("EMPTY file : "+file.getAbsolutePath());
                    continue;
                }
                log.info("Total lines: "+lines.size());
                int skipCount = 0;
                for (String line : lines) {
//                    line = pretreatment(line);
                    String userAgent = getUserAgent(line);
                    int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
                    if (uaGroupId < 0) {
                        skipCount++;
                        continue;
                    }


                    String ip = getIP(line);
                    Date date = null;
                    try {
                        date = getDate(line);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    String day = DateFormatUtils.format(date, "yyyyMMdd");
                    String dateStr = FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss");

                    String uri = getUri(line);
                    if (UrlFilterUtil.shouldSkipUrl(uri)) {
                        continue;
                    }
                    String status = getStatus(line);

                    if(StringUtils.isEmpty(uri)
                            || StringUtils.startsWithIgnoreCase(uri, "http://")
                            || StringUtils.startsWithIgnoreCase(uri, "https://")) {
                        log.error("uri is URL, error, uri:" + uri);
                        skipCount++;
                        continue;
                    }

                    if(StringUtils.isEmpty(ip) ||
                            StringUtils.isEmpty(status) ||
                            StringUtils.isEmpty(userAgent) ||
                            StringUtils.isEmpty(uri)) {
                        log.error("skip line : "+line);
                        skipCount++;
                        continue;
                    }

                    BotJsonVO jsonVO = new BotJsonVO();
                    BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
                    dataVO.setUaGroupId(uaGroupId);
                    dataVO.setOriginIP(ip);
                    dataVO.setReqHost(getHost(line));
                    dataVO.setStatus(status);
                    dataVO.setUserAgent(userAgent);
                    dataVO.setUserAgentStr(userAgent);
                    dataVO.setReqPath(uri);
                    dataVO.setDate(dateStr);

                    dataVO.setDomainId(getDomainId());
                    jsonVO.setData(dataVO);

                    String json = gson.toJson(jsonVO);

                    File base = new File(getOutPutDirPath()+day+"/00/");
                    if (!base.exists()) {
                        base.mkdirs();
                    }

                    File f = new File(getOutPutDirPath()+day+"/00/"+day+"_"+getDomainId()+"_log.json");
                    FileUtils.write(f, json+"\n", true);
                }
                log.info("Total count: "+lines.size()+"## skip :"+skipCount);
            }
        }
    }

}
