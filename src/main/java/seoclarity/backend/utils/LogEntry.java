/**
 * Autogenerated by Thrift
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 */
package seoclarity.backend.utils;

import java.util.Collections;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

import org.apache.thrift.TBase;
import org.apache.thrift.TBaseHelper;
import org.apache.thrift.TException;
import org.apache.thrift.TFieldIdEnum;
import org.apache.thrift.TFieldRequirementType;
import org.apache.thrift.meta_data.FieldMetaData;
import org.apache.thrift.meta_data.FieldValueMetaData;
import org.apache.thrift.protocol.TField;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.protocol.TProtocolUtil;
import org.apache.thrift.protocol.TStruct;
import org.apache.thrift.protocol.TType;

public class LogEntry implements TBase<LogEntry._Fields>, java.io.Serializable, Cloneable, Comparable<LogEntry> {
	private static final TStruct STRUCT_DESC = new TStruct("LogEntry");

	private static final TField CATEGORY_FIELD_DESC = new TField("category", TType.STRING, (short) 1);
	private static final TField MESSAGE_FIELD_DESC = new TField("message", TType.STRING, (short) 2);

	public String category;
	public String message;

	/**
	 * The set of fields this struct contains, along with convenience methods
	 * for finding and manipulating them.
	 */
	public enum _Fields implements TFieldIdEnum {
		CATEGORY((short) 1, "category"), MESSAGE((short) 2, "message");

		private static final Map<Integer, _Fields> byId = new HashMap<Integer, _Fields>();
		private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

		static {
			for (_Fields field : EnumSet.allOf(_Fields.class)) {
				byId.put((int) field._thriftId, field);
				byName.put(field.getFieldName(), field);
			}
		}

		/**
		 * Find the _Fields constant that matches fieldId, or null if its not
		 * found.
		 */
		public static _Fields findByThriftId(int fieldId) {
			return byId.get(fieldId);
		}

		/**
		 * Find the _Fields constant that matches fieldId, throwing an exception
		 * if it is not found.
		 */
		public static _Fields findByThriftIdOrThrow(int fieldId) {
			_Fields fields = findByThriftId(fieldId);
			if (fields == null)
				throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
			return fields;
		}

		/**
		 * Find the _Fields constant that matches name, or null if its not
		 * found.
		 */
		public static _Fields findByName(String name) {
			return byName.get(name);
		}

		private final short _thriftId;
		private final String _fieldName;

		_Fields(short thriftId, String fieldName) {
			_thriftId = thriftId;
			_fieldName = fieldName;
		}

		public short getThriftFieldId() {
			return _thriftId;
		}

		public String getFieldName() {
			return _fieldName;
		}
	}

	// isset id assignments

	public static final Map<_Fields, FieldMetaData> metaDataMap = Collections.unmodifiableMap(new EnumMap<_Fields, FieldMetaData>(_Fields.class) {
		{
			put(_Fields.CATEGORY, new FieldMetaData("category", TFieldRequirementType.DEFAULT, new FieldValueMetaData(TType.STRING)));
			put(_Fields.MESSAGE, new FieldMetaData("message", TFieldRequirementType.DEFAULT, new FieldValueMetaData(TType.STRING)));
		}
	});

	static {
		FieldMetaData.addStructMetaDataMap(LogEntry.class, metaDataMap);
	}

	public LogEntry() {
	}

	public LogEntry(String category, String message) {
		this();
		this.category = category;
		this.message = message;
	}

	/**
	 * Performs a deep copy on <i>other</i>.
	 */
	public LogEntry(LogEntry other) {
		if (other.isSetCategory()) {
			this.category = other.category;
		}
		if (other.isSetMessage()) {
			this.message = other.message;
		}
	}

	public LogEntry deepCopy() {
		return new LogEntry(this);
	}

	@Deprecated
	public LogEntry clone() {
		return new LogEntry(this);
	}

	public String getCategory() {
		return this.category;
	}

	public LogEntry setCategory(String category) {
		this.category = category;
		return this;
	}

	public void unsetCategory() {
		this.category = null;
	}

	/**
	 * Returns true if field category is set (has been asigned a value) and
	 * false otherwise
	 */
	public boolean isSetCategory() {
		return this.category != null;
	}

	public void setCategoryIsSet(boolean value) {
		if (!value) {
			this.category = null;
		}
	}

	public String getMessage() {
		return this.message;
	}

	public LogEntry setMessage(String message) {
		this.message = message;
		return this;
	}

	public void unsetMessage() {
		this.message = null;
	}

	/**
	 * Returns true if field message is set (has been asigned a value) and false
	 * otherwise
	 */
	public boolean isSetMessage() {
		return this.message != null;
	}

	public void setMessageIsSet(boolean value) {
		if (!value) {
			this.message = null;
		}
	}

	public void setFieldValue(_Fields field, Object value) {
		switch (field) {
		case CATEGORY:
			if (value == null) {
				unsetCategory();
			} else {
				setCategory((String) value);
			}
			break;

		case MESSAGE:
			if (value == null) {
				unsetMessage();
			} else {
				setMessage((String) value);
			}
			break;

		}
	}

	public void setFieldValue(int fieldID, Object value) {
		setFieldValue(_Fields.findByThriftIdOrThrow(fieldID), value);
	}

	public Object getFieldValue(_Fields field) {
		switch (field) {
		case CATEGORY:
			return getCategory();

		case MESSAGE:
			return getMessage();

		}
		throw new IllegalStateException();
	}

	public Object getFieldValue(int fieldId) {
		return getFieldValue(_Fields.findByThriftIdOrThrow(fieldId));
	}

	/**
	 * Returns true if field corresponding to fieldID is set (has been asigned a
	 * value) and false otherwise
	 */
	public boolean isSet(_Fields field) {
		switch (field) {
		case CATEGORY:
			return isSetCategory();
		case MESSAGE:
			return isSetMessage();
		}
		throw new IllegalStateException();
	}

	public boolean isSet(int fieldID) {
		return isSet(_Fields.findByThriftIdOrThrow(fieldID));
	}

	@Override
	public boolean equals(Object that) {
		if (that == null)
			return false;
		if (that instanceof LogEntry)
			return this.equals((LogEntry) that);
		return false;
	}

	public boolean equals(LogEntry that) {
		if (that == null)
			return false;

		boolean this_present_category = true && this.isSetCategory();
		boolean that_present_category = true && that.isSetCategory();
		if (this_present_category || that_present_category) {
			if (!(this_present_category && that_present_category))
				return false;
			if (!this.category.equals(that.category))
				return false;
		}

		boolean this_present_message = true && this.isSetMessage();
		boolean that_present_message = true && that.isSetMessage();
		if (this_present_message || that_present_message) {
			if (!(this_present_message && that_present_message))
				return false;
			if (!this.message.equals(that.message))
				return false;
		}

		return true;
	}

	@Override
	public int hashCode() {
		return 0;
	}

	public int compareTo(LogEntry other) {
		if (!getClass().equals(other.getClass())) {
			return getClass().getName().compareTo(other.getClass().getName());
		}

		int lastComparison = 0;
		LogEntry typedOther = (LogEntry) other;

		lastComparison = Boolean.valueOf(isSetCategory()).compareTo(isSetCategory());
		if (lastComparison != 0) {
			return lastComparison;
		}
		lastComparison = TBaseHelper.compareTo(category, typedOther.category);
		if (lastComparison != 0) {
			return lastComparison;
		}
		lastComparison = Boolean.valueOf(isSetMessage()).compareTo(isSetMessage());
		if (lastComparison != 0) {
			return lastComparison;
		}
		lastComparison = TBaseHelper.compareTo(message, typedOther.message);
		if (lastComparison != 0) {
			return lastComparison;
		}
		return 0;
	}

	public void read(TProtocol iprot) throws TException {
		TField field;
		iprot.readStructBegin();
		while (true) {
			field = iprot.readFieldBegin();
			if (field.type == TType.STOP) {
				break;
			}
			_Fields fieldId = _Fields.findByThriftId(field.id);
			if (fieldId == null) {
				TProtocolUtil.skip(iprot, field.type);
			} else {
				switch (fieldId) {
				case CATEGORY:
					if (field.type == TType.STRING) {
						this.category = iprot.readString();
					} else {
						TProtocolUtil.skip(iprot, field.type);
					}
					break;
				case MESSAGE:
					if (field.type == TType.STRING) {
						this.message = iprot.readString();
					} else {
						TProtocolUtil.skip(iprot, field.type);
					}
					break;
				}
				iprot.readFieldEnd();
			}
		}
		iprot.readStructEnd();

		// check for required fields of primitive type, which can't be checked
		// in the validate method
		validate();
	}

	public void write(TProtocol oprot) throws TException {
		validate();

		oprot.writeStructBegin(STRUCT_DESC);
		if (this.category != null) {
			oprot.writeFieldBegin(CATEGORY_FIELD_DESC);
			oprot.writeString(this.category);
			oprot.writeFieldEnd();
		}
		if (this.message != null) {
			oprot.writeFieldBegin(MESSAGE_FIELD_DESC);
			oprot.writeString(this.message);
			oprot.writeFieldEnd();
		}
		oprot.writeFieldStop();
		oprot.writeStructEnd();
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder("LogEntry(");
		boolean first = true;

		sb.append("category:");
		if (this.category == null) {
			sb.append("null");
		} else {
			sb.append(this.category);
		}
		first = false;
		if (!first)
			sb.append(", ");
		sb.append("message:");
		if (this.message == null) {
			sb.append("null");
		} else {
			sb.append(this.message);
		}
		first = false;
		sb.append(")");
		return sb.toString();
	}

	public void validate() throws TException {
		// check for required fields
	}

}
