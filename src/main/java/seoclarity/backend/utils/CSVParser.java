package seoclarity.backend.utils;

/**
 Copyright 2005 Bytecode Pty Ltd.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 */

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.json.CDL;
import org.jsoup.helper.StringUtil;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * A very simple CSV parser released under a commercial-friendly license.
 * This just implements splitting a single line into fields.
 *
 * <AUTHOR> Smith
 * <AUTHOR> Pruy
 */
public class CSVParser {

    private static final String CONS_DOMAIN = "domain";
    private static final String CONS_RANK = "Rank";
    private final char separator;

    private final char quotechar;

    private final char escape;

    private final boolean strictQuotes;

    private String pending;
    private boolean inField = false;

    private final boolean ignoreLeadingWhiteSpace;

    /**
     * The default separator to use if none is supplied to the constructor.
     */
    public static final char DEFAULT_SEPARATOR = ',';

    public static final int INITIAL_READ_SIZE = 128;

    /**
     * The default quote character to use if none is supplied to the
     * constructor.
     */
    public static final char DEFAULT_QUOTE_CHARACTER = '"';


    /**
     * The default escape character to use if none is supplied to the
     * constructor.
     */
    public static final char DEFAULT_ESCAPE_CHARACTER = '\\';

    /**
     * The default strict quote behavior to use if none is supplied to the
     * constructor
     */
    public static final boolean DEFAULT_STRICT_QUOTES = false;

    /**
     * The default leading whitespace behavior to use if none is supplied to the
     * constructor
     */
    public static final boolean DEFAULT_IGNORE_LEADING_WHITESPACE = true;

    /**
     * This is the "null" character - if a value is set to this then it is ignored.
     * I.E. if the quote character is set to null then there is no quote character.
     */
    public static final char NULL_CHARACTER = '\0';

    /**
     * Constructs CSVParser using a comma for the separator.
     */
    public CSVParser() {
        this(DEFAULT_SEPARATOR, DEFAULT_QUOTE_CHARACTER, DEFAULT_ESCAPE_CHARACTER);
    }

    /**
     * Constructs CSVParser with supplied separator.
     *
     * @param separator the delimiter to use for separating entries.
     */
    public CSVParser(char separator) {
        this(separator, DEFAULT_QUOTE_CHARACTER, DEFAULT_ESCAPE_CHARACTER);
    }


    /**
     * Constructs CSVParser with supplied separator and quote char.
     *
     * @param separator the delimiter to use for separating entries
     * @param quotechar the character to use for quoted elements
     */
    public CSVParser(char separator, char quotechar) {
        this(separator, quotechar, DEFAULT_ESCAPE_CHARACTER);
    }

    /**
     * Constructs CSVReader with supplied separator and quote char.
     *
     * @param separator the delimiter to use for separating entries
     * @param quotechar the character to use for quoted elements
     * @param escape    the character to use for escaping a separator or quote
     */
    public CSVParser(char separator, char quotechar, char escape) {
        this(separator, quotechar, escape, DEFAULT_STRICT_QUOTES);
    }

    /**
     * Constructs CSVReader with supplied separator and quote char.
     * Allows setting the "strict quotes" flag
     *
     * @param separator    the delimiter to use for separating entries
     * @param quotechar    the character to use for quoted elements
     * @param escape       the character to use for escaping a separator or quote
     * @param strictQuotes if true, characters outside the quotes are ignored
     */
    public CSVParser(char separator, char quotechar, char escape, boolean strictQuotes) {
        this(separator, quotechar, escape, strictQuotes, DEFAULT_IGNORE_LEADING_WHITESPACE);
    }

    /**
     * Constructs CSVReader with supplied separator and quote char.
     * Allows setting the "strict quotes" and "ignore leading whitespace" flags
     *
     * @param separator               the delimiter to use for separating entries
     * @param quotechar               the character to use for quoted elements
     * @param escape                  the character to use for escaping a separator or quote
     * @param strictQuotes            if true, characters outside the quotes are ignored
     * @param ignoreLeadingWhiteSpace if true, white space in front of a quote in a field is ignored
     */
    public CSVParser(char separator, char quotechar, char escape, boolean strictQuotes, boolean ignoreLeadingWhiteSpace) {
        if (anyCharactersAreTheSame(separator, quotechar, escape)) {
            throw new UnsupportedOperationException("The separator, quote, and escape characters must be different!");
        }
        if (separator == NULL_CHARACTER) {
            throw new UnsupportedOperationException("The separator character must be defined!");
        }
        this.separator = separator;
        this.quotechar = quotechar;
        this.escape = escape;
        this.strictQuotes = strictQuotes;
        this.ignoreLeadingWhiteSpace = ignoreLeadingWhiteSpace;
    }

    private boolean anyCharactersAreTheSame(char separator, char quotechar, char escape) {
        return isSameCharacter(separator, quotechar) || isSameCharacter(separator, escape) || isSameCharacter(quotechar, escape);
    }

    private boolean isSameCharacter(char c1, char c2) {
        return c1 != NULL_CHARACTER && c1 == c2;
    }

    /**
     * @return true if something was left over from last call(s)
     */
    public boolean isPending() {
        return pending != null;
    }

    public String[] parseLineMulti(String nextLine) throws IOException {
        return parseLine(nextLine, true);
    }

    public String[] parseLine(String nextLine) throws IOException {
        return parseLine(nextLine, false);
    }

    // https://www.wrike.com/open.htm?id=1247023765
    public static String parseLineDomainKeyword(String nextLine,List<String> domains,List<String> monthList,List<String> rank_monthList)  {
        try{
            System.out.println("=====nextLine:"+nextLine);
            System.out.println("=====domains:"+domains);
            System.out.println("=====monthList:"+monthList);
            System.out.println("=====rank_monthList:"+rank_monthList);
            JSONObject object = JSONUtil.parseObj(nextLine);
            LinkedHashMap<String,Object> linkMap = new LinkedHashMap<>();
            List<LinkedHashMap> listMonth = new ArrayList<>();
            for (int j=monthList.size() - 1;j>=0;j--){
                LinkedHashMap month = new LinkedHashMap();
                month.put("yearmonth",monthList.get(j));
                month.put("monthly_volume",object.get("Volume"+monthList.get(j) ));
                listMonth.add(month);
            }

            for (int j=0;j< monthList.size();j++){
                object.remove("Volume"+monthList.get(j));
            }

            if(object!=null){
                List<HashMap> listDomain = new ArrayList<>();
                for(int i=0;i < domains.size();i++){
                    HashMap<String,Object> domainMap = new HashMap<>();
                    LinkedHashMap<String,Object> monthMap = new LinkedHashMap<>();
                    for (int j=0;j< rank_monthList.size();j++){
                        String tempPathRank = CONS_DOMAIN + i+ CONS_RANK+j;
                        Integer rank = Integer.parseInt(object.getByPath(tempPathRank).toString());
                        if(rank > 100){
                            continue;
                        }
                        String tempPathUrl = CONS_DOMAIN + i+ "Url"+j;
                        String tempPathLabel = CONS_DOMAIN + i+ "Label"+j;
                        String tempPathMeta = CONS_DOMAIN + i+ "Meta"+j;

                        monthMap.put("Url",object.getByPath(tempPathUrl).toString());
                        monthMap.put("Rank",rank);
                        monthMap.put("Label",object.getByPath(tempPathLabel).toString());
                        monthMap.put("Meta",object.getByPath(tempPathMeta).toString());
                    }
                    domainMap.put(domains.get(i),monthMap);
                    listDomain.add(domainMap);
                }

                for(int i=0;i < domains.size();i++){
                    for (int j=0;j< rank_monthList.size();j++){
                        String tempPathRank = "domain" + i+ "Rank"+j;
                        String tempPathUrl = CONS_DOMAIN + i+ "Url"+j;
                        String tempPathLabel = CONS_DOMAIN + i+ "Label"+j;
                        String tempPathMeta = CONS_DOMAIN + i+ "Meta"+j;
                        //System.out.println("=====tempPath:"+tempPath);
                        object.remove(tempPathRank);
                        object.remove(tempPathUrl);
                        object.remove(tempPathLabel);
                        object.remove(tempPathMeta);
                    }
                }
                object.set("monthly_volumes",listMonth);
                object.set("Rank",listDomain);
                return  object.toString();
            }
            return object.toString();
        }catch (Exception ex){
            ex.printStackTrace();
            return nextLine;
        }
    }

    public static String parseLine_json_GrDetail(String nextLine,List<String> monthList) throws Exception  {
        try{
            System.out.println("=====nextLine:"+nextLine);
//            System.out.println("=====domains:"+domains);
//            System.out.println("=====monthList:"+monthList);
            JSONObject object = JSONUtil.parseObj(nextLine);
            LinkedHashMap<String,Object> linkMap = new LinkedHashMap<>();
            List<LinkedHashMap> listMonth = new ArrayList<>();
            for (int j=0; j< monthList.size() ;j++){
                LinkedHashMap month = new LinkedHashMap();
                month.put("yearmonth",monthList.get(j));
                month.put("monthly_volume",object.get("Volume"+monthList.get(j) ));
                listMonth.add(month);
            }

            for (int j=0;j< monthList.size();j++){
                object.remove("Volume"+monthList.get(j));
            }
            object.set("monthly_volumes",listMonth);
            return object.toString();
        }catch (Exception ex){
            ex.printStackTrace();
            return nextLine;
        }
    }

    public static String parseJsonToCsv_topicexploere(String jsonStr,List<String> headers,List<String> headersCast,List<String> monthList)  {
        try{
            cn.hutool.json.JSONObject json =JSONUtil.parseObj(jsonStr);
            List<LinkedHashMap> listMonth = new ArrayList<>();
            for (int j=monthList.size() - 1;j>=0;j--){
                LinkedHashMap month = new LinkedHashMap();
                month.put("yearmonth",monthList.get(j));
                month.put("monthly_volume",json.get(monthList.get(j)+" Search Volume" ));
                listMonth.add(month);
            }
            for (int j=0;j< monthList.size();j++){
                json.remove(monthList.get(j)+" Search Volume");
            }
            json.set("monthly_volumes",listMonth);
            org.json.JSONArray array = new org.json.JSONArray();
            array.put(json);
            CSVFormat format = CSVFormat.newFormat('\t');
            Writer out = new StringWriter();
            try (CSVPrinter printer = new CSVPrinter(out, format)) {
                for (String header: headersCast) {
                    printer.print(json.get(header).toString());
                }
            }
            String csv = out.toString();
            return csv;
        }catch (Exception ex){
            ex.printStackTrace();
            return jsonStr;
        }
    }


    public static String parseJsonToCsv_grip(String jsonStr,List<String> headers)  {
        try{
            cn.hutool.json.JSONObject json =JSONUtil.parseObj(jsonStr);
            CSVFormat format = CSVFormat.newFormat('\t');
            Writer out = new StringWriter();
            try (CSVPrinter printer = new CSVPrinter(out, format)) {
                for (String header: headers) {
                    printer.print(json.get(header).toString());
                }
            }
            String csv = out.toString();
            return csv;
        }catch (Exception ex){
            ex.printStackTrace();
            return jsonStr;
        }
    }


    public static String parseJson_topicexploere(String jsonStr,List<String> headers,List<String> headersCast,List<String> monthList) throws Exception {

//            System.out.println("=====headersCast"+headersCast);
//            System.out.println("=====jsonStr:"+jsonStr);
            //org.json.JSONObject json =new org.json.JSONObject(jsonStr);
            cn.hutool.json.JSONObject json =JSONUtil.parseObj(jsonStr);
            //org.json.JSONObject json = new org.json.JSONObject(jsonStr);
            System.out.println("=====json:"+JSONUtil.toJsonStr(json));
            List<LinkedHashMap> listMonth = new ArrayList<>();
            for (int j=monthList.size() - 1;j>=0;j--){
                LinkedHashMap month = new LinkedHashMap();
                month.put("yearmonth",monthList.get(j));
                //System.out.println("====="+"Volume"+monthList.get(j));
                month.put("monthly_volume",json.get(monthList.get(j)+" Search Volume"));
                listMonth.add(month);
            }

            for (int j=0;j< monthList.size();j++){
                json.remove(monthList.get(j)+" Search Volume");
            }
            cn.hutool.json.JSONObject newJson = new JSONObject();
            for (String newHeader:headersCast) {
                if(StringUtils.equals("monthly_volumes",newHeader)){
                    newJson.set("monthly_volumes",listMonth);
                }else{
                    newJson.set(newHeader,json.get(newHeader)) ;
                }
            }
            return newJson.toString();
    }

    public static String parseJsonToCsv_researchGrid_detail(String jsonStr,List<String> headers,List<String> headersCast,List<String> monthList) throws Exception {
        try{
            cn.hutool.json.JSONObject json =JSONUtil.parseObj(jsonStr);
            //org.json.JSONObject json = new org.json.JSONObject(jsonStr);
            //System.out.println("=====json:"+JSONUtil.toJsonStr(json));
            List<LinkedHashMap> listMonth = new ArrayList<>();
            for (int j=0;j<monthList.size();j++){
                LinkedHashMap month = new LinkedHashMap();
                month.put("yearmonth",monthList.get(j));
                //System.out.println("====="+"Volume"+monthList.get(j));
                month.put("monthly_volume",json.get("Volume"+monthList.get(j)));
                listMonth.add(month);
            }

            for (int j=0;j< monthList.size();j++){
                json.remove("Volume"+monthList.get(j));
            }

            json.set("monthly_volumes",listMonth);
            org.json.JSONArray array = new org.json.JSONArray();
            array.put(json);
            CSVFormat format = CSVFormat.newFormat('\t');
            Writer out = new StringWriter();
            try (CSVPrinter printer = new CSVPrinter(out, format)) {
                for (String header: headersCast) {
                        printer.print(json.get(header));
                }
            }
            String csv = out.toString();
            return csv;
        }catch (Exception ex){
            ex.printStackTrace();
            return jsonStr;
        }
    }

    public static List<String>  parseJsonToCsv_researchGridDetail_header(List<String> headers,List<String> monthList) throws Exception {
        try{
            List<String> newHeaders = new ArrayList<>(headers);
            for (int j=0;j< monthList.size();j++){
                newHeaders.remove("Volume"+monthList.get(j));
            }
            newHeaders.add(11,"monthly_volumes");
            return newHeaders;
        }catch (Exception ex){
            ex.printStackTrace();
            return headers;
        }
    }

    public static List<String>  parseJsonToCsv_monthly_volumes_header(List<String> headers,List<String> monthList) throws Exception {
        try{
            List<String> newHeaders = new ArrayList<>(headers);
            for (int j=0;j< monthList.size();j++){
                newHeaders.remove(monthList.get(j)+" Search Volume");
            }
            newHeaders.add(6,"monthly_volumes");
            return newHeaders;
        }catch (Exception ex){
            ex.printStackTrace();
            return headers;
        }
    }

    public static String parseJsonToCsv(String jsonStr,List<String> headers)  {
        try{
            org.json.JSONObject json = new org.json.JSONObject(jsonStr);
            org.json.JSONArray array = new org.json.JSONArray();
            array.put(json);
            CSVFormat format = CSVFormat.newFormat('\t');
            Writer out = new StringWriter();
            try (CSVPrinter printer = new CSVPrinter(out, format)) {
                for (String header: headers) {
                    printer.print(json.get(header));
                }
            }
            String csv = out.toString();
            return csv;
        }catch (Exception ex){
            ex.printStackTrace();
            return jsonStr;
        }
    }

    // https://www.wrike.com/open.htm?id=1247023765
    public static String parseLineTopxKeyword_v1(List<String> lines,List<String> months) throws Exception  {
        try{
            //System.out.println("=====parseLineTopxKeyword:"+JSONUtil.toJsonStr(lines));
            LinkedHashMap<String,Object> map = new LinkedHashMap<>();
            List<LinkedHashMap> listLandingpages = new ArrayList<>();
            int i = 0;
            for (String line:lines) {
                JSONObject object = JSONUtil.parseObj(line);
                if (object != null) {
                    if (i == 0) {
                        map.put("Name", object.get("Name"));
                        map.put("Volume", object.get("Volume"));
                        map.put("CPC", object.get("CPC"));
                        map.put("TrueDemand", object.get("TrueDemand"));
                        map.put("UserIntent", object.get("UserIntent"));
                        map.put("DifficultyScore", object.get("DifficultyScore"));
                        List<LinkedHashMap> listMonth = new ArrayList<>();
                        for (int j = 0 ; j < months.size(); j++){
                        String temp = months.get(j);
                        LinkedHashMap monthMap = new LinkedHashMap();
                        monthMap.put("yearmonth", temp);
                        //System.out.println("====="+"Volume"+monthList.get(j));
                        monthMap.put("monthly_volume", object.get("Volume" + temp));
                        listMonth.add(monthMap);
                    }

                    map.put("monthly_volumes" , listMonth);
                        LinkedHashMap<String, Object> landingpagesMap = null ;
                        landingpagesMap = new LinkedHashMap<>();
                        landingpagesMap.put("url", object.get("url"));
                        landingpagesMap.put("rank", object.get("rank"));
                        landingpagesMap.put("label", object.get("label"));
                        landingpagesMap.put("meta", object.get("meta"));
                        listLandingpages.add(landingpagesMap);
                    } else {
                        LinkedHashMap<String, Object> landingpagesMap =null;
                        landingpagesMap = new LinkedHashMap<>();
                        landingpagesMap.put("url", object.get("url"));
                        landingpagesMap.put("rank", object.get("rank"));
                        landingpagesMap.put("label", object.get("label"));
                        landingpagesMap.put("meta", object.get("meta"));
                        listLandingpages.add(landingpagesMap);
                    }
                }
                map.put("Landingpages",listLandingpages);
            }
            return JSONUtil.toJsonStr(map);
        }catch (Exception ex){
            ex.printStackTrace();
            return "=====";
        }
    }

    /**
     * Parses an incoming String and returns an array of elements.
     *
     * @param nextLine the string to parse
     * @param multi
     * @return the comma-tokenized list of elements, or null if nextLine is null
     * @throws IOException if bad things happen during the read
     */
    private String[] parseLine(String nextLine, boolean multi) throws IOException {

        if (!multi && pending != null) {
            pending = null;
        }

        if (nextLine == null) {
            if (pending != null) {
                String s = pending;
                pending = null;
                return new String[]{s};
            } else {
                return null;
            }
        }

        List<String> tokensOnThisLine = new ArrayList<String>();
        StringBuilder sb = new StringBuilder(INITIAL_READ_SIZE);
        boolean inQuotes = false;
        if (pending != null) {
            sb.append(pending);
            pending = null;
            inQuotes = true;
        }
        for (int i = 0; i < nextLine.length(); i++) {

            char c = nextLine.charAt(i);
            if (c == this.escape) {
                if (isNextCharacterEscapable(nextLine, inQuotes || inField, i)) {
                    sb.append(nextLine.charAt(i + 1));
                    i++;
                }
            } else if (c == quotechar) {
                if (isNextCharacterEscapedQuote(nextLine, inQuotes || inField, i)) {
                    sb.append(nextLine.charAt(i + 1));
                    i++;
                } else {
                    //inQuotes = !inQuotes;

                    // the tricky case of an embedded quote in the middle: a,bc"d"ef,g
                    if (!strictQuotes) {
                        if (i > 2 //not on the beginning of the line
                                && nextLine.charAt(i - 1) != this.separator //not at the beginning of an escape sequence
                                && nextLine.length() > (i + 1) &&
                                nextLine.charAt(i + 1) != this.separator //not at the	end of an escape sequence
                                ) {

                            if (ignoreLeadingWhiteSpace && sb.length() > 0 && isAllWhiteSpace(sb)) {
                                sb.setLength(0);  //discard white space leading up to quote
                            } else {
                                sb.append(c);
                                //continue;
                            }

                        }
                    }

                    inQuotes = !inQuotes;
                }
                inField = !inField;
            } else if (c == separator && !inQuotes) {
                tokensOnThisLine.add(sb.toString());
                sb.setLength(0); // start work on next token
                inField = false;
            } else {
                if (!strictQuotes || inQuotes) {
                    sb.append(c);
                    inField = true;
                }
            }
        }
        // line is done - check status
        if (inQuotes) {
            if (multi) {
                // continuing a quoted section, re-append newline
                sb.append("\n");
                pending = sb.toString();
                sb = null; // this partial content is not to be added to field list yet
            } else {
                throw new IOException("Un-terminated quoted field at end of CSV line");
            }
        }
        if (sb != null) {
            tokensOnThisLine.add(sb.toString());
        }
        return tokensOnThisLine.toArray(new String[tokensOnThisLine.size()]);

    }

    /**
     * precondition: the current character is a quote or an escape
     *
     * @param nextLine the current line
     * @param inQuotes true if the current context is quoted
     * @param i        current index in line
     * @return true if the following character is a quote
     */
    private boolean isNextCharacterEscapedQuote(String nextLine, boolean inQuotes, int i) {
        return inQuotes  // we are in quotes, therefore there can be escaped quotes in here.
                && nextLine.length() > (i + 1)  // there is indeed another character to check.
                && nextLine.charAt(i + 1) == quotechar;
    }

    /**
     * precondition: the current character is an escape
     *
     * @param nextLine the current line
     * @param inQuotes true if the current context is quoted
     * @param i        current index in line
     * @return true if the following character is a quote
     */
    protected boolean isNextCharacterEscapable(String nextLine, boolean inQuotes, int i) {
        return inQuotes  // we are in quotes, therefore there can be escaped quotes in here.
                && nextLine.length() > (i + 1)  // there is indeed another character to check.
                && (nextLine.charAt(i + 1) == quotechar || nextLine.charAt(i + 1) == this.escape);
    }

    /**
     * precondition: sb.length() > 0
     *
     * @param sb A sequence of characters to examine
     * @return true if every character in the sequence is whitespace
     */
    protected boolean isAllWhiteSpace(CharSequence sb) {
        boolean result = true;
        for (int i = 0; i < sb.length(); i++) {
            char c = sb.charAt(i);

            if (!Character.isWhitespace(c)) {
                return false;
            }
        }
        return result;
    }

    public static void main(String[] args) {
        System.out.println("=====");
    }
}
