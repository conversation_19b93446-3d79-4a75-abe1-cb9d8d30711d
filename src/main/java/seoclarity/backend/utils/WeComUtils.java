package seoclarity.backend.utils;

import com.alibaba.fastjson2.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/25 17:05
 */
public class WeComUtils {

    private static final String WE_COM_ROBOT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=";
    // monitoring group key
    private static final String WE_COM_MONITORING_ROBOT = "WE_COM_MONITORING_ROBOT";
    private static final String WE_COM_CALL = "http://67.228.121.78:9999/wx/public/test?ids=";
    
    private static final String CUSTOM_MESSAGE_TO = "@All";
    private static final String MESSAGE_TYPE_TEXT = "text";

    public static String getCustomMessageTo() {
    	return CUSTOM_MESSAGE_TO;
    }

    public static String sendTextMessageToMonitorGroup(String content) {
        String s = WE_COM_ROBOT_URL + System.getenv().get(WE_COM_MONITORING_ROBOT);
        System.out.println(s);
        return groupMessage(MESSAGE_TYPE_TEXT, content, WE_COM_ROBOT_URL + System.getenv().get(WE_COM_MONITORING_ROBOT));
    }

    public static String call() {
        return httpGet(WE_COM_CALL + "WeiLeJieMuXiaoGuo,DongWeiBing", null, "utf-8");
    }

    private static String groupMessage(String type, String content, String url) {
        JSONObject params = new JSONObject();
        params.put("msgtype", type);
        JSONObject text = new JSONObject();
        text.put("content", content);
//        text.put("mentioned_list", Arrays.asList("@all"));
        params.put(type, text);
        return httpPost(url, params.toJSONString(), null, "utf-8");
    }

    private static String httpGet(String url, Map<String,String> headers, String encode) {
        if(encode == null){
            encode = "utf-8";
        }
        String content = null;
        CloseableHttpResponse httpResponse = null;
        try (CloseableHttpClient closeableHttpClient = HttpClientBuilder.create().build()) {
            HttpGet httpGet = new HttpGet(url);
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpGet.setHeader(entry.getKey(),entry.getValue());
                }
            }
            httpResponse = closeableHttpClient.execute(httpGet);
            HttpEntity entity = httpResponse.getEntity();
            content = EntityUtils.toString(entity, encode);
        } catch (Exception e) {
            e.printStackTrace();
        } finally{
            try {
                httpResponse.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return content;
    }

    private static String httpPost(String url, String stringJson, Map<String,String> headers, String encode) {
        if(encode == null){
            encode = "utf-8";
        }
        String content = null;
        CloseableHttpResponse  httpResponse = null;
        try (CloseableHttpClient closeableHttpClient = HttpClientBuilder.create().build()) {
            HttpPost httPost = new HttpPost(url);
            httPost.setHeader("Content-type", "application/json");
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httPost.setHeader(entry.getKey(),entry.getValue());
                }
            }
            StringEntity stringEntity = new StringEntity(stringJson, encode);
            httPost.setEntity(stringEntity);
            httpResponse = closeableHttpClient.execute(httPost);
            HttpEntity entity = httpResponse.getEntity();
            content = EntityUtils.toString(entity, encode);
        } catch (Exception e) {
            e.printStackTrace();
        } finally{
            try {
                httpResponse.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return content;
    }

}
