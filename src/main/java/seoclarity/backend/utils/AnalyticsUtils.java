package seoclarity.backend.utils;

import seoclarity.backend.entity.actonia.SearchvolRankcheckidEntity;
import seoclarity.backend.entity.aurora.TopCompetitorAuroraSummaryEntity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


public class AnalyticsUtils {
	public static final String SPLIT_FIELD = "	";
	private static final String UTF_8 = "utf-8";
	
	private static String convertForInt(Integer value) {
		if (value != null) {
			return String.valueOf(value.intValue());
		}
		return "\\N";
	}

	private static String convertForFloat(Float value) {
		if (value != null) {
			return value.toString();
		}
		return "\\N";
	}
	
	public static List<String> converToTopCompetitorsAuroraLines(Collection<TopCompetitorAuroraSummaryEntity>  topCompetitorSummaryList) {
		List<String> lines = new ArrayList<String>();
		for (TopCompetitorAuroraSummaryEntity topEntity : topCompetitorSummaryList) {
			StringBuffer line = new StringBuffer();
			line.append(topEntity.getOwnDomainId()).append(SPLIT_FIELD);
			line.append(topEntity.getTagId()).append(SPLIT_FIELD);
			line.append(topEntity.getEngineId()).append(SPLIT_FIELD);
			line.append(topEntity.getLanguageId()).append(SPLIT_FIELD);
			line.append(convertForInt(topEntity.getLogDate())).append(SPLIT_FIELD);
			line.append(convertForInt(topEntity.getType())).append(SPLIT_FIELD);
			line.append(topEntity.getCompetitorName()).append(SPLIT_FIELD);
			
			line.append(topEntity.getCount_keyword_rank1()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank2()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank3()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank4()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank5()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank6()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank7()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank8()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank9()).append(SPLIT_FIELD);
			line.append(topEntity.getCount_keyword_rank10()).append(SPLIT_FIELD);
			
			line.append(topEntity.getKeyword_sum_searchvol_top3()).append(SPLIT_FIELD);
			line.append(topEntity.getKeyword_sum_searchvol_top10()).append(SPLIT_FIELD);
			line.append(topEntity.getKeyword_sum_searchvol_top30()).append(SPLIT_FIELD);
			line.append(topEntity.getKeyword_sum_searchvol_top50()).append(SPLIT_FIELD);
			line.append(topEntity.getKeyword_sum_searchvol_top100()).append(SPLIT_FIELD);
			
			line.append(convertForInt(topEntity.getKeyword_count_rank_top3())).append(SPLIT_FIELD);
			line.append(convertForInt(topEntity.getKeyword_count_rank_top10())).append(SPLIT_FIELD);
			line.append(convertForInt(topEntity.getKeyword_count_rank_top30())).append(SPLIT_FIELD);
			line.append(convertForInt(topEntity.getKeyword_count_rank_top50())).append(SPLIT_FIELD);
			line.append(convertForInt(topEntity.getKeyword_count_rank_top100())).append(SPLIT_FIELD);
			
			line.append(topEntity.getRank_in_page1()).append(SPLIT_FIELD);
			line.append(topEntity.getRank_in_page2()).append(SPLIT_FIELD);
			line.append(topEntity.getRank_in_page3()).append(SPLIT_FIELD);
			line.append(topEntity.getRank_in_page4()).append(SPLIT_FIELD);
			line.append(topEntity.getRank_in_page5()).append(SPLIT_FIELD);

			line.append(topEntity.getSearch_volume_rank1()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank2()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank3()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank4()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank5()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank6()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank7()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank8()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank9()).append(SPLIT_FIELD);
			line.append(topEntity.getSearch_volume_rank10()).append(SPLIT_FIELD);

			line.append(convertForFloat(topEntity.getWtd_avg_rank())).append(SPLIT_FIELD);
			line.append(convertForFloat(topEntity.getAvg_rank())).append(SPLIT_FIELD);
			line.append(convertForFloat(topEntity.getWtd_avg_rank_with101())).append(SPLIT_FIELD);
			line.append(convertForFloat(topEntity.getAvg_rank_with101())).append(SPLIT_FIELD);
			
			line.append(topEntity.getTotal_keywords()).append(SPLIT_FIELD);
			line.append(topEntity.getTotal_searchvol()).append(SPLIT_FIELD);
			
			line.append(topEntity.getSum_of_rank_inc101()).append(SPLIT_FIELD);
			line.append(topEntity.getSum_of_rank_exc101()).append(SPLIT_FIELD);
			line.append(topEntity.getWtd_searchvol_inc101()).append(SPLIT_FIELD);
			line.append(topEntity.getWtd_searchvol_exc101());
			
			lines.add(line.toString());
		}
		return lines;
	}
	
	public static List<String> converTosearchvolRankcheckidLines(Collection<SearchvolRankcheckidEntity>  seRankcheckidEntities) {
		List<String> lines = new ArrayList<String>();
		for (SearchvolRankcheckidEntity topEntity : seRankcheckidEntities) {
			StringBuffer line = new StringBuffer();
			line.append(topEntity.getKeywordRankcheckId()).append(SPLIT_FIELD);
			line.append(topEntity.getOwnDomainId()).append(SPLIT_FIELD);
			line.append(topEntity.getAvgSearchVolume()).append(SPLIT_FIELD);
			line.append(topEntity.getSearchVolumneDate());
			
			lines.add(line.toString());
		}
		return lines;
	}
}
