package seoclarity.backend.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class FileExtension {

	private static final String GZIP_FILE_HEX = "1f8b08000000000000ff";
	private static final String GZIP_FILE_EXTESION = "gz";
	
    private final static Map<String, String> FILE_TYPE_MAP = new HashMap<String, String>();
    
    private static void initFileExtensionMap(){
        FILE_TYPE_MAP.put("1f8b08000000000000ff", "csv");
        FILE_TYPE_MAP.put("ffd8ffe000104a464946", "jpg"); //JPEG (jpg)
        FILE_TYPE_MAP.put("89504e470d0a1a0a0000", "png"); //PNG (png)
        FILE_TYPE_MAP.put("47494638396126026f01", "gif"); //GIF (gif)
        FILE_TYPE_MAP.put("49492a00227105008037", "tif"); //TIFF (tif)
        FILE_TYPE_MAP.put("424d228c010000000000", "bmp"); //16 bit (bmp)
        FILE_TYPE_MAP.put("424d8240090000000000", "bmp"); //24 bit (bmp)
        FILE_TYPE_MAP.put("424d8e1b030000000000", "bmp"); //256 bit (bmp)
        FILE_TYPE_MAP.put("41433130313500000000", "dwg"); //CAD (dwg)
        FILE_TYPE_MAP.put("3c21444f435459504520", "html"); //HTML (html)
        FILE_TYPE_MAP.put("3c21646f637479706520", "htm"); //HTM (htm)
        FILE_TYPE_MAP.put("48544d4c207b0d0a0942", "css"); //css
        FILE_TYPE_MAP.put("696b2e71623d696b2e71", "js"); //js
        FILE_TYPE_MAP.put("7b5c727466315c616e73", "rtf"); //Rich Text Format (rtf)
        FILE_TYPE_MAP.put("38425053000100000000", "psd"); //Photoshop (psd)
        FILE_TYPE_MAP.put("46726f6d3a203d3f6762", "eml"); //Email [Outlook Express 6] (eml)
        FILE_TYPE_MAP.put("d0cf11e0a1b11ae10000", "doc"); //MS Excel word,msi,excel
        FILE_TYPE_MAP.put("d0cf11e0a1b11ae10000", "vsd"); //Visio
        FILE_TYPE_MAP.put("5374616E64617264204A", "mdb"); //MS Access (mdb)
        FILE_TYPE_MAP.put("252150532D41646F6265", "ps");
        FILE_TYPE_MAP.put("255044462d312e360d25", "pdf"); //Adobe Acrobat (pdf)
        FILE_TYPE_MAP.put("2e524d46000000120001", "rmvb"); //rmvb/rm
        FILE_TYPE_MAP.put("464c5601050000000900", "flv"); //flv,f4v
        FILE_TYPE_MAP.put("00000020667479706973", "mp4");
        FILE_TYPE_MAP.put("49443303000000000f76", "mp3");
        FILE_TYPE_MAP.put("000001ba210001000180", "mpg"); //
        FILE_TYPE_MAP.put("3026b2758e66cf11a6d9", "wmv"); //wmv,asf
        FILE_TYPE_MAP.put("524946464694c9015741", "wav"); //Wave (wav)
        FILE_TYPE_MAP.put("52494646d07d60074156", "avi");
        FILE_TYPE_MAP.put("4d546864000000060001", "mid"); //MIDI (mid)
        FILE_TYPE_MAP.put("504b0304140000000800", "zip");
        FILE_TYPE_MAP.put("526172211a0700cf9073", "rar");
        FILE_TYPE_MAP.put("235468697320636f6e66", "ini");
        FILE_TYPE_MAP.put("504b03040a0000000000", "jar");
        FILE_TYPE_MAP.put("4d5a9000030000000400", "exe");
        FILE_TYPE_MAP.put("3c25402070616765206c", "jsp");//jsp
        FILE_TYPE_MAP.put("4d616e69666573742d56", "mf");//MF
        FILE_TYPE_MAP.put("3c3f786d6c2076657273", "xml");//xml
        FILE_TYPE_MAP.put("efbbbf2f2a0d0a53514c", "sql");//xml
        FILE_TYPE_MAP.put("7061636b616765207765", "java");//java
        FILE_TYPE_MAP.put("406563686f206f66660d", "bat");//bat
        FILE_TYPE_MAP.put("1f8b0800000000000000", "gz");//gz
        FILE_TYPE_MAP.put("6c6f67346a2e726f6f74", "properties");//bat
        FILE_TYPE_MAP.put("cafebabe0000002e0041", "class");//bat
        FILE_TYPE_MAP.put("49545346030000006000", "chm");//bat
        FILE_TYPE_MAP.put("04000000010000001300", "mxp");//bat
        FILE_TYPE_MAP.put("504b0304140006000800", "docx");//docx
        FILE_TYPE_MAP.put("d0cf11e0a1b11ae10000", "wps");//WPS wps,et、dps
        FILE_TYPE_MAP.put("6431303a637265617465", "torrent");
        FILE_TYPE_MAP.put("494d4b48010100000200", "264");
        FILE_TYPE_MAP.put("6D6F6F76", "mov"); //Quicktime (mov)
        FILE_TYPE_MAP.put("FF575043", "wpd"); //WordPerfect (wpd)
        FILE_TYPE_MAP.put("CFAD12FEC5FD746F", "dbx"); //Outlook Express (dbx)
        FILE_TYPE_MAP.put("2142444E", "pst"); //Outlook (pst)
        FILE_TYPE_MAP.put("AC9EBD8F", "qdf"); //Quicken (qdf)
        FILE_TYPE_MAP.put("E3828596", "pwl"); //Windows Password (pwl)
        FILE_TYPE_MAP.put("2E7261FD", "ram"); //Real Audio (ram)
    }
    
    public static boolean isGzipFile(String fullPathFilename) {
    	String extensionName = checkFileExtension(new File(fullPathFilename));
    	if (extensionName != null && GZIP_FILE_EXTESION.equalsIgnoreCase(extensionName)) {
    		return true;
    	}
    	return false;
    }

    private static String checkFileExtension(File file) {
        byte[] b = new byte[0];
        FileInputStream fs = null;
        try {
            fs = new FileInputStream(file);
            b = new byte[10];
            fs.read(b,0,b.length);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fs!=null) {
                try {
                    fs.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        String hexString = bytesToHexString(b);
        
        String fileExtension = null;
        if (GZIP_FILE_HEX.equalsIgnoreCase(hexString)) { // TODO
        	fileExtension = GZIP_FILE_EXTESION;
        }
        System.out.println("  ==checkFileExt file:" + file.getName() + " hexString:" + hexString + " fileType:" + fileExtension);
        return fileExtension;
        //return checkFileExtension(hexString);
    }

    private static String bytesToHexString(byte[] b){
        StringBuilder stringBuilder = new StringBuilder();
        if (b == null || b.length <= 0) {
            return null;
        }
        for (int i = 0; i < b.length; i++) {
            int v = b[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }
    
    private static String checkFileExtension(String hexStr) {
        if (FILE_TYPE_MAP == null) {
            initFileExtensionMap();
        }
        return FILE_TYPE_MAP.getOrDefault(hexStr, null);
    }
}