package seoclarity.backend.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;

public class DingTools {

//    private final static String DING_PATH = "https://oapi.dingtalk.com/robot/send?access_token=e94572aff89005509ca6223e6d83e0277e7b304559735781e07e65b1a544ac16";

    private final static String DING_PATH = "https://oapi.dingtalk.com/robot/send?access_token=76e5393af67120663e02fc9c4b2bf34f141453880fd3ecc3106cd51f0901c40a";

    public static void sendMessage(String text) {
        JSONObject object = new JSONObject();
        object.set("msgtype", "text");
        JSONObject textObj = new JSONObject();
        textObj.set("content", text);
        object.set("text", textObj);
        JSONObject at = new JSONObject();
        at.set("isAtAll", true);
        textObj.set("at", at);
        System.out.println(object.toString());

        String body = HttpUtil.createGet(DING_PATH)
                .contentType("application/json")
                .body(object.toString()).execute().body();
        System.out.println("DingTalk 发送结果: "+body);
    }

}
