/**
 * 
 */
package seoclarity.backend.utils;

import cn.hutool.core.io.IoUtil;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.Enumeration;
import java.util.zip.*;

/**
 * com.actonia.subserver.utils.GZipUtil.java
 * 
 * <AUTHOR>
 * 
 * @version $Revision: 99735 $ $Author: miaopengfei@SHINETECHCHINA $
 */
public class GZipUtil {

	public static final String GZFile_POSTFIX = ".gz";

	private static final int EXPECTED_COMPRESSION_RATIO = 5;

	private static final int BUF_SIZE = 40960;

	/** Postfix of zip file. */
	public static final String ZIPFile_POSTFIX = ".zip";

	/**
	 * Compress srcFilePath to srcFilePath.gz
	 * 
	 * @param srcFilePath
	 * @return
	 * @throws Exception
	 */
	public static void zipFile(String srcFilePath) throws Exception {
		zip(srcFilePath, srcFilePath + ZIPFile_POSTFIX);
	}

	public static void zip(String srcFilePath) throws Exception {
		zip(srcFilePath, srcFilePath + GZFile_POSTFIX);
	}
	
	public static String zipWithFileName(String srcFilePath) throws Exception {
		zip(srcFilePath, srcFilePath + GZFile_POSTFIX);
		return srcFilePath + GZFile_POSTFIX;
	}

	public static final byte[] unzip(byte[] in) throws IOException {
		// decompress using GZIPInputStream
		ByteArrayOutputStream outStream = new ByteArrayOutputStream(EXPECTED_COMPRESSION_RATIO * in.length);

		GZIPInputStream inStream = new GZIPInputStream(new ByteArrayInputStream(in));

		byte[] buf = new byte[BUF_SIZE];
		while (true) {
			int size = inStream.read(buf);
			if (size <= 0)
				break;
			outStream.write(buf, 0, size);
		}
		outStream.close();
		inStream.close();
		return outStream.toByteArray();
	}

	public static File unGzipBigFile(byte[] in, File outPutFile) throws IOException {
		GZIPInputStream inStream = null;
		FileOutputStream fileOutputStream = null;
		try {
			inStream = new GZIPInputStream(new ByteArrayInputStream(in));
			fileOutputStream = new FileOutputStream(outPutFile);
			IoUtil.copy(inStream, fileOutputStream);
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			IoUtil.close(inStream);
			IoUtil.close(fileOutputStream);
		}
		return outPutFile;
	}

	/**
	 * Compress srcFilePath to srcFilePath.gz
	 * 
	 * @param srcFilePath
	 * @return
	 * @throws Exception
	 */
	public static void zipFile(String srcFilePath, String postFix) throws Exception {
		// zip(srcFilePath, srcFilePath + postFix);
		createZip(srcFilePath, srcFilePath + postFix);
	}

	/**
	 * Compress srcFilePath to destFilePath
	 * 
	 * @param srcFilePath
	 * @param destFilePath
	 * @return
	 * @throws Exception
	 */
	public static void zip(String srcFilePath, String destFilePath) throws Exception {
		FileInputStream fin = null;
		FileOutputStream fout = null;
		GZIPOutputStream gzout = null;
		try {
			fin = new FileInputStream(srcFilePath);
			fout = new FileOutputStream(destFilePath);
			gzout = new GZIPOutputStream(fout);
			byte[] buf = new byte[1024];

			int number = 0;
			while ((number = fin.read(buf)) != -1) {
				gzout.write(buf, 0, number);
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			if (gzout != null) {
				gzout.close();
			}
			if (fout != null) {
				fout.close();
			}
			if (fin != null) {
				fin.close();
			}
		}
	}

	public static void createZip(String srcFilePath, String destFilePath) throws Exception {
		try {
			byte[] buffer = new byte[18024];
			ZipOutputStream out = new ZipOutputStream(new FileOutputStream(destFilePath));
			out.setLevel(Deflater.DEFAULT_COMPRESSION);
			FileInputStream in = new FileInputStream(srcFilePath);
			File file = new File(srcFilePath);
			out.putNextEntry(new ZipEntry(file.getName()));
			int len;
			while ((len = in.read(buffer)) > 0) {
				out.write(buffer, 0, len);
			}
			out.closeEntry();
			in.close();
			out.close();
			//System.out.println("Your file is zipped");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 归档
	 * @param entry
	 * @throws IOException
	 * <AUTHOR>
	 * @return
	 * @date 2017年5月27日下午1:48:23
	 */
	public static String archive(String entry) throws IOException {
		File file = new File(entry);

		TarArchiveOutputStream tos = new TarArchiveOutputStream(new FileOutputStream(file.getAbsolutePath() + ".tar"));
		String base = file.getName();
		if(file.isDirectory()) {
			archiveDir(file, tos, base);
		} else {
			archiveHandle(tos, file, null);
		}
		tos.close();
		return file.getAbsolutePath() + ".tar";
	}

	/**
	 * 递归处理，准备好路径
	 * @param file
	 * @param tos
	 * @param basePath
	 * @throws IOException
	 * <AUTHOR>
	 * @date 2017年5月27日下午1:48:40
	 */
	private static void archiveDir(File file, TarArchiveOutputStream tos, String basePath) throws IOException {
		File[] listFiles = file.listFiles();
		for(File fi : listFiles){
			if(fi.isDirectory()){
				archiveDir(fi, tos, basePath + File.separator + fi.getName());
			}else{
				archiveHandle(tos, fi, basePath);
			}
		}
	}

	/**
	 * 具体归档处理（文件）
	 * @param tos
	 * @param fi
	 * @param basePath
	 * @throws IOException
	 * <AUTHOR>
	 * @date 2017年5月27日下午1:48:56
	 */
	private static void archiveHandle(TarArchiveOutputStream tos, File fi, String basePath) throws IOException {
		TarArchiveEntry tEntry = new TarArchiveEntry((StringUtils.isBlank(basePath) ? "" : basePath + File.separator) + fi.getName());
		tEntry.setSize(fi.length());
		tos.putArchiveEntry(tEntry);
		BufferedInputStream bis = new BufferedInputStream(new FileInputStream(fi));

		byte[] buffer = new byte[1024];
		int read = -1;
		while((read = bis.read(buffer)) != -1){
			tos.write(buffer, 0 , read);
		}
		bis.close();
		tos.closeArchiveEntry();
	}

	/**
	 * zip mutil files
	 * <AUTHOR>
	 * */
	public static void createZip(String[] srcFilePaths, String destFilePath) throws Exception{
		try {
			int buffer = 18024;
			FileOutputStream outFile = new FileOutputStream(destFilePath);
			CheckedOutputStream csum = new CheckedOutputStream(outFile, new Adler32());
			ZipOutputStream zos = new ZipOutputStream(csum);
			zos.setLevel(Deflater.DEFAULT_COMPRESSION);
			BufferedOutputStream out = new BufferedOutputStream(zos, buffer);
			for (String s : srcFilePaths) {
		                System.out.println("zip file " + s);
		                BufferedReader bin = new BufferedReader(new FileReader(s));
		                zos.putNextEntry(new ZipEntry(new File(s).getName()));
		                int c;
		                while ((c = bin.read()) != -1) {
		                    out.write(c);
		                }
		                bin.close();
		                out.flush();
		            }
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		}
	}

	public static void createGz(String[] srcFilePaths, String destFilePath) throws Exception{
		try {

			File outputFile = new File(destFilePath); // 压缩后的文件名及路径
			try (GZIPOutputStream gzos = new GZIPOutputStream(new FileOutputStream(outputFile))) {
				for (String fileName : srcFilePaths) {
					try (InputStream is = new FileInputStream(fileName)) {
						byte[] buffer = new byte[4096];
						int bytesRead;
						while ((bytesRead = is.read(buffer)) != -1) {
							gzos.write(buffer, 0, bytesRead);
						}
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		}
	}

	public static void unZip(File srcFile, String destDirPath) throws RuntimeException {
		long start = System.currentTimeMillis();
		if (!srcFile.exists()) {
			throw new RuntimeException(srcFile.getPath() + "file not exist");
		}
		ZipFile zipFile = null;
		try {
			zipFile = new ZipFile(srcFile);
			Enumeration<?> entries = zipFile.entries();
			while (entries.hasMoreElements()) {
				ZipEntry entry = (ZipEntry) entries.nextElement();
				System.out.println("unzip : " + entry.getName());
				if (entry.isDirectory()) {
					String dirPath = destDirPath + "/" + entry.getName();
					File dir = new File(dirPath);
					dir.mkdirs();
				} else {
					File targetFile = new File(destDirPath + "/" + entry.getName());

					if(!targetFile.getParentFile().exists()){
						targetFile.getParentFile().mkdirs();
					}
					targetFile.createNewFile();

					InputStream is = zipFile.getInputStream(entry);
					FileOutputStream fos = new FileOutputStream(targetFile);
					int len;
					byte[] buf = new byte[BUF_SIZE];
					while ((len = is.read(buf)) != -1) {
						fos.write(buf, 0, len);
					}
					fos.close();
					is.close();
				}
			}
			long end = System.currentTimeMillis();
			System.out.println("finish unzip ，elapsed time ：" + (end - start) +" ms");
		} catch (Exception e) {
			throw new RuntimeException("unzip error from ZipUtils", e);
		} finally {
			if(zipFile != null){
				try {
					zipFile.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	public static String unGzipFile(String inFileName) {

		String outFilePath = "";
		try {
			if (!getExtension(inFileName).equalsIgnoreCase("gz")) {
				System.err.println("File name must have extension of \".gz\"");
				return null;
			}

			System.out.println("Opening the compressed file.");
			GZIPInputStream in = null;
			try {
				in = new GZIPInputStream(new FileInputStream(inFileName));
			} catch(FileNotFoundException e) {
				System.err.println("File not found. " + inFileName);
				return null;
			}

			System.out.println("Open the output file.");
			outFilePath = getFileName(inFileName);
			FileOutputStream out = null;
			try {
				out = new FileOutputStream(outFilePath);
			} catch (FileNotFoundException e) {
				System.err.println("Could not write to file. " + outFilePath);
				System.exit(1);
			}

			System.out.println("Transfering bytes from compressed file to the output file.");
			byte[] buf = new byte[1024];
			int len;
			while((len = in.read(buf)) > 0) {
				out.write(buf, 0, len);
			}

			System.out.println("Closing the file and stream");
			in.close();
			out.close();

		} catch (IOException e) {
			System.out.println("unGzip failed !! ");
			e.printStackTrace();
		}

		return outFilePath;
	}

	/**
	 * Used to extract and return the extension of a given file.
	 * @param f Incoming file to get the extension of
	 * @return <code>String</code> representing the extension of the incoming
	 *         file.
	 */
	public static String getExtension(String f) {
		String ext = "";
		int i = f.lastIndexOf('.');

		if (i > 0 &&  i < f.length() - 1) {
			ext = f.substring(i+1);
		}
		return ext;
	}

	/**
	 * Used to extract the filename without its extension.
	 * @param f Incoming file to get the filename
	 * @return <code>String</code> representing the filename without its
	 *         extension.
	 */
	public static String getFileName(String f) {
		String fname = "";
		int i = f.lastIndexOf('.');

		if (i > 0 &&  i < f.length() - 1) {
			fname = f.substring(0,i);
		}
		return fname;
	}



	public static void main(String[] args) throws Exception {
		String arName = GZipUtil.archive("E:\\TEMP\\us2.txt");
		GZipUtil.createZip(arName, arName+".gz");
	}
	
	 public static void zipFiles(String sourceFilePath, String targetFilePath) {
		 File srcfile = new File(sourceFilePath);
		  
        ZipOutputStream out = null;  
        try {  
            out = new ZipOutputStream(new FileOutputStream(targetFilePath));  
              
            if(srcfile.isFile()){  
                zipFile(srcfile, out, "");  
            } else{  
                File[] list = srcfile.listFiles();  
                for (int i = 0; i < list.length; i++) {  
                    compress(list[i], out, "");  
                }  
            }  
              
            System.out.println("zip done!");  
        } catch (Exception e) {  
            e.printStackTrace();  
        } finally {  
            try {  
                if (out != null)  
                    out.close();  
            } catch (IOException e) {  
                e.printStackTrace();  
            }  
        }  
    }  
  
    private static void compress(File file, ZipOutputStream out, String basedir) {  
        if (file.isDirectory()) {  
            zipDirectory(file, out, basedir);  
        } else {  
            zipFile(file, out, basedir);  
        }  
    }  
    
    public static void zipFile(File srcfile, ZipOutputStream out, String basedir) {  
        if (!srcfile.exists())  
            return;  
  
        byte[] buf = new byte[1024];  
        FileInputStream in = null;  
  
        try {  
            int len;  
            in = new FileInputStream(srcfile);  
            out.putNextEntry(new ZipEntry(basedir + srcfile.getName()));  
  
            while ((len = in.read(buf)) > 0) {  
                out.write(buf, 0, len);  
            }  
        } catch (Exception e) {  
            e.printStackTrace();  
        } finally {  
            try {  
                if (out != null)  
                    out.closeEntry();  
                if (in != null)  
                    in.close();  
            } catch (IOException e) {  
                e.printStackTrace();  
            }  
        }  
    }  
    
    public static void zipDirectory(File dir, ZipOutputStream out, String basedir) {  
        if (!dir.exists())  
            return;  
  
        File[] files = dir.listFiles();  
        for (int i = 0; i < files.length; i++) {  
            compress(files[i], out, basedir + dir.getName() + "/");  
        }  
    }
}
