package seoclarity.backend.utils;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.io.output.FileWriterWithEncoding;

import com.csvreader.CsvWriter;

/**
 * <AUTHOR>
 * @version 2015.12.16
 * */
public class CommonCsvExport {
	public static final char DEFAULT_SEPARATOR = ',';
	public static final String DEFAULT_CHARSET = "UTF-8";
	
	public interface IBatchExecute{
		void batch(CsvWriter wr) throws Exception;
	}

	public static CsvWriter getCsvWriter(String filePath, Character separator, String charSet) throws Exception{
		charSet = charSet == null ? DEFAULT_CHARSET : charSet;
		separator = separator == null  ? DEFAULT_SEPARATOR : separator;
		return new CsvWriter(filePath, separator, 	Charset.forName(charSet));
	}
	
	public static CsvWriter getAppendCsvWriter(String filePath, Character separator, String charSet) throws Exception{
		charSet = charSet == null ? DEFAULT_CHARSET : charSet;
		separator = separator == null  ? DEFAULT_SEPARATOR : separator;
		return new CsvWriter(new FileWriterWithEncoding(filePath, "UTF-8", true), separator);
	}
	
	/**
	 * write all data-list one-time
	 * */
	public static void writeCsv(String filePath, List<Object[]> rows, Character separator, String charSet) throws Exception{
		CsvWriter wr = null;
		try {
			wr = getCsvWriter(filePath, separator, charSet);
			if (rows != null) {
				for (Object[] row : rows) {
					try {
						wr.writeRecord((String[])row);
					} catch (Exception e) {
						String[] line = new String[row.length];
						for (int i = 0; i < line.length; i++) {
							line[i] = row[i] == null ? "" : String.valueOf(row[i]);
						}
						wr.writeRecord(line);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			wr.close();
		}
	}
	
	public static void writeCsvListString(String filePath, List<String> lines, Character separator, String charSet) throws Exception{
		
		CsvWriter wr = null;
		try {
			wr = getCsvWriter(filePath, separator, charSet);
			if (lines != null) {
				
				List<Object[]> rows = new ArrayList<Object[]>();
				Object[] obj = new Object[lines.size()];
				int pos = 0;
				for (String item : lines) {
					obj[pos++] = item;
				}
				rows.add(obj);
				
				for (Object[] row : rows) {
					try {
						wr.writeRecord((String[])row);
					} catch (Exception e) {
						String[] line = new String[row.length];
						for (int i = 0; i < line.length; i++) {
							line[i] = row[i] == null ? "" : String.valueOf(row[i]);
						}
						wr.writeRecord(line);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			wr.close();
		}
	}
	
	/**
	 * write all data-list one-time
	 * */
	public static void writeCsv(String filePath,  String[] headerList, Collection<Object[]> rows, Character separator, String charSet) throws Exception{
		CsvWriter wr = null;
		try {
			wr = getCsvWriter(filePath, separator, charSet);
			if (headerList != null && headerList.length > 0) {
				wr.writeRecord(headerList);
			}
			if (rows != null) {
				for (Object[] row : rows) {
					try {
						wr.writeRecord((String[])row);
					} catch (Exception e) {
						String[] line = new String[row.length];
						for (int i = 0; i < line.length; i++) {
							line[i] = row[i] == null ? "" : String.valueOf(row[i]);
						}
						wr.writeRecord(line);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			wr.close();
		}
	}
	
	public static void writeCsvStringList(String filePath,  String[] headerList, Collection<String[]> rows, Character separator, String charSet) throws Exception{
		CsvWriter wr = null;
		try {
			wr = getCsvWriter(filePath, separator, charSet);
			if (headerList != null && headerList.length > 0) {
				wr.writeRecord(headerList);
			}
			if (rows != null) {
				for (Object[] row : rows) {
					try {
						wr.writeRecord((String[])row);
					} catch (Exception e) {
						String[] line = new String[row.length];
						for (int i = 0; i < line.length; i++) {
							line[i] = row[i] == null ? "" : String.valueOf(row[i]);
						}
						wr.writeRecord(line);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			wr.close();
		}
	}
	
	/**
	 * write all data-list one-time
	 * @param filePath
	 * @param headerList
	 * @param rows
	 * @param separator
	 * @param charSet
	 * @throws Exception
	 */
	public static void writeCsv2(String filePath,  String[] headerList, List<List<Object>> rows, Character separator, String charSet) throws Exception{
		CsvWriter wr = null;
		try {
			wr = getCsvWriter(filePath, separator, charSet);
			if (headerList != null && headerList.length > 0) {
				wr.writeRecord(headerList);
			}
			if (rows != null) {
				for (List<Object> rowList : rows) {
					Object[] row = rowList.toArray(new Object[rowList.size()]);
					try {
						wr.writeRecord((String[])row);
					} catch (Exception e) {
						String[] line = new String[row.length];
						for (int i = 0; i < line.length; i++) {
							line[i] = row[i] == null ? "" : String.valueOf(row[i]);
						}
						wr.writeRecord(line);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			wr.close();
		}
	}
	
	public static void writeCsvStringList(String filePath,  String[] headerList, List<String[]> rows, Character separator, String charSet) throws Exception{
		CsvWriter wr = null;
		try {
			wr = getCsvWriter(filePath, separator, charSet);
			if (headerList != null && headerList.length > 0) {
				wr.writeRecord(headerList);
			}
			if (rows != null) {
				for (Object[] row : rows) {
					try {
						wr.writeRecord((String[])row);
					} catch (Exception e) {
						String[] line = new String[row.length];
						for (int i = 0; i < line.length; i++) {
							line[i] = row[i] == null ? "" : String.valueOf(row[i]);
						}
						wr.writeRecord(line);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			wr.close();
		}
	}

	/**
	 * @param headerList header
	 * @param separator default ','
	 * @param charSet default UTF-8
	 * */
	public static void writeCsv(String filePath, String[] headerList, Character separator, String charSet, IBatchExecute batch) throws Exception{
		CsvWriter wr = null;
		try {
			wr = getCsvWriter(filePath, separator, charSet);
			wr.writeRecord(headerList);
			if (batch != null) {
				batch.batch(wr);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (wr != null) {
				wr.close();
			}
		}
	}
	
	public static void appendCsv(String filePath, String[] headerList, Character separator, String charSet, IBatchExecute batch) throws Exception{
		CsvWriter wr = null;
		try {
			wr = getAppendCsvWriter(filePath, separator, charSet);
			wr.writeRecord(headerList);
			if (batch != null) {
				batch.batch(wr);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (wr != null) {
				wr.close();
			}
		}
	}
	
	/**
	 * An example for Bach export
	 * */
	public static void writeForBatchExample(String filePath, String[] headerList, Character separator, String charSet, final List<String[]> lines) {
		try {
			writeCsv(filePath, headerList, separator, charSet, new IBatchExecute() {
				@Override
				public void batch(CsvWriter wr) throws Exception {
					for (String[] line : lines) {
						wr.writeRecord(line);
						wr.flush();
					}
				}
			});
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
