package seoclarity.backend.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import org.apache.commons.io.IOUtils;
import org.apache.commons.io.output.FileWriterWithEncoding;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

/**
 * 
 * <AUTHOR>
 * @date 2017-06-16
 * com.actonia.subserver.export.clarityDB.ExtractClarityDBsqlResult
 */
public class ExtractClarityDBsqlResult {
	public static final int CLARITYDB_MONTHLY_RANKING = 1;
	public static final int CLARITYDB_CLARITY_BOT = 2;
	public static final int CLARITY_DAILY_RANKING = 3;
	public static final int CLARITY_GSC = 4;
	
	public static boolean getToFile(String url, String fileName, String[] header) {
		HttpClient httpclient = new DefaultHttpClient();
		boolean complete = true;
		try {
    		HttpGet httpget = new HttpGet(url);
    		HttpResponse response = httpclient.execute(httpget);
    		final HttpEntity entity = response.getEntity();
    		
			File tmpFile =  new File(fileName);
			BufferedWriter bw = new BufferedWriter(new FileWriterWithEncoding(tmpFile, "UTF-8"));
			if (header != null && header.length > 0) {
				String headerLine = StringUtils.join(header, ',');
				if (StringUtils.isNotBlank(headerLine)) {
					bw.write(headerLine + "\n");
				}
			}
			IOUtils.copy(entity.getContent(), bw);
			bw.close();
		} catch (Exception e) {
			e.printStackTrace();
			complete = false;
		}
		return complete;
	}
	
	public static String post(String url,String sql) { 
		HttpClient httpclient=new DefaultHttpClient();
        String resStr = "";
        HttpPost httppost = new HttpPost(url);
        try {
        	httppost.setEntity(new StringEntity(sql,"utf-8"));
            HttpResponse response;
            response = httpclient.execute(httppost);
            HttpEntity entity = response.getEntity();  
            if (entity != null) {  
                resStr = EntityUtils.toString(entity,"utf-8");
            }  
        } catch (ClientProtocolException e) {  
            e.printStackTrace();  
        } catch (UnsupportedEncodingException e1) {  
            e1.printStackTrace();  
        } catch (IOException e) {  
            e.printStackTrace();  
        } finally {  
            httpclient.getConnectionManager().shutdown();  
        }  
        return resStr;  
    }
	
}
