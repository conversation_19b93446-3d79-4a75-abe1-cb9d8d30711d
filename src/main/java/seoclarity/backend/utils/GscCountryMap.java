package seoclarity.backend.utils;

import java.util.LinkedHashMap;
import java.util.Map;

public class GscCountryMap {

    public static Map<String, String> GSC_COUNTRY_MAP = new LinkedHashMap<String, String>();

    static {
        GSC_COUNTRY_MAP.put("afg","Afghanistan");
        GSC_COUNTRY_MAP.put("alb","Albania");
        GSC_COUNTRY_MAP.put("dza","Algeria");
        GSC_COUNTRY_MAP.put("asm","American Samoa");
        GSC_COUNTRY_MAP.put("and","Andorra");
        GSC_COUNTRY_MAP.put("ago","Angola");
        GSC_COUNTRY_MAP.put("aia","Ang<PERSON><PERSON>");
        GSC_COUNTRY_MAP.put("ata","Antarctica");
        GSC_COUNTRY_MAP.put("atg","Antigua and Barbuda");
        GSC_COUNTRY_MAP.put("arg","Argentina");
        GSC_COUNTRY_MAP.put("arm","Armenia");
        GSC_COUNTRY_MAP.put("abw","Aruba");
        GSC_COUNTRY_MAP.put("aus","Australia");
        GSC_COUNTRY_MAP.put("aut","Austria");
        GSC_COUNTRY_MAP.put("aze","Azerbaijan");
        GSC_COUNTRY_MAP.put("bhs","Bahamas");
        GSC_COUNTRY_MAP.put("bhr","Bahrain");
        GSC_COUNTRY_MAP.put("bgd","Bangladesh");
        GSC_COUNTRY_MAP.put("brb","Barbados");
        GSC_COUNTRY_MAP.put("blr","Belarus");
        GSC_COUNTRY_MAP.put("bel","Belgium");
        GSC_COUNTRY_MAP.put("blz","Belize");
        GSC_COUNTRY_MAP.put("ben","Benin");
        GSC_COUNTRY_MAP.put("bmu","Bermuda");
        GSC_COUNTRY_MAP.put("btn","Bhutan");
        GSC_COUNTRY_MAP.put("bol","Bolivia");
        GSC_COUNTRY_MAP.put("bes","Bonaire");
        GSC_COUNTRY_MAP.put("bih","Bosnia and Herzegovina");
        GSC_COUNTRY_MAP.put("bwa","Botswana");
        GSC_COUNTRY_MAP.put("bvt","Bouvet Island");
        GSC_COUNTRY_MAP.put("bra","Brazil");
        GSC_COUNTRY_MAP.put("iot","British Indian Ocean Territory");
        GSC_COUNTRY_MAP.put("brn","Brunei Darussalam");
        GSC_COUNTRY_MAP.put("bgr","Bulgaria");
        GSC_COUNTRY_MAP.put("bfa","Burkina Faso");
        GSC_COUNTRY_MAP.put("bdi","Burundi");
        GSC_COUNTRY_MAP.put("khm","Cambodia");
        GSC_COUNTRY_MAP.put("cmr","Cameroon");
        GSC_COUNTRY_MAP.put("can","Canada");
        GSC_COUNTRY_MAP.put("cpv","Cape Verde");
        GSC_COUNTRY_MAP.put("cym","Cayman Islands");
        GSC_COUNTRY_MAP.put("caf","Central African Republic");
        GSC_COUNTRY_MAP.put("tcd","Chad");
        GSC_COUNTRY_MAP.put("chl","Chile");
        GSC_COUNTRY_MAP.put("chn","China");
        GSC_COUNTRY_MAP.put("cxr","Christmas Island");
        GSC_COUNTRY_MAP.put("cck","Cocos (Keeling) Islands");
        GSC_COUNTRY_MAP.put("col","Colombia");
        GSC_COUNTRY_MAP.put("com","Comoros");
        GSC_COUNTRY_MAP.put("cog","Congo");
        GSC_COUNTRY_MAP.put("cod","Democratic Republic of the Congo");
        GSC_COUNTRY_MAP.put("cok","Cook Islands");
        GSC_COUNTRY_MAP.put("cri","Costa Rica");
        GSC_COUNTRY_MAP.put("hrv","Croatia");
        GSC_COUNTRY_MAP.put("cub","Cuba");
        GSC_COUNTRY_MAP.put("cuw","Curacao");
        GSC_COUNTRY_MAP.put("cyp","Cyprus");
        GSC_COUNTRY_MAP.put("cze","Czech Republic");
        GSC_COUNTRY_MAP.put("civ","Cote d'Ivoire");
        GSC_COUNTRY_MAP.put("dnk","Denmark");
        GSC_COUNTRY_MAP.put("dji","Djibouti");
        GSC_COUNTRY_MAP.put("dma","Dominica");
        GSC_COUNTRY_MAP.put("dom","Dominican Republic");
        GSC_COUNTRY_MAP.put("ecu","Ecuador");
        GSC_COUNTRY_MAP.put("egy","Egypt");
        GSC_COUNTRY_MAP.put("slv","El Salvador");
        GSC_COUNTRY_MAP.put("gnq","Equatorial Guinea");
        GSC_COUNTRY_MAP.put("eri","Eritrea");
        GSC_COUNTRY_MAP.put("est","Estonia");
        GSC_COUNTRY_MAP.put("eth","Ethiopia");
        GSC_COUNTRY_MAP.put("flk","Falkland Islands (Malvinas)");
        GSC_COUNTRY_MAP.put("fro","Faroe Islands");
        GSC_COUNTRY_MAP.put("fji","Fiji");
        GSC_COUNTRY_MAP.put("fin","Finland");
        GSC_COUNTRY_MAP.put("fra","France");
        GSC_COUNTRY_MAP.put("guf","French Guiana");
        GSC_COUNTRY_MAP.put("pyf","French Polynesia");
        GSC_COUNTRY_MAP.put("atf","French Southern Territories");
        GSC_COUNTRY_MAP.put("gab","Gabon");
        GSC_COUNTRY_MAP.put("gmb","Gambia");
        GSC_COUNTRY_MAP.put("geo","Georgia");
        GSC_COUNTRY_MAP.put("deu","Germany");
        GSC_COUNTRY_MAP.put("gha","Ghana");
        GSC_COUNTRY_MAP.put("gib","Gibraltar");
        GSC_COUNTRY_MAP.put("grc","Greece");
        GSC_COUNTRY_MAP.put("grl","Greenland");
        GSC_COUNTRY_MAP.put("grd","Grenada");
        GSC_COUNTRY_MAP.put("glp","Guadeloupe");
        GSC_COUNTRY_MAP.put("gum","Guam");
        GSC_COUNTRY_MAP.put("gtm","Guatemala");
        GSC_COUNTRY_MAP.put("ggy","Guernsey");
        GSC_COUNTRY_MAP.put("gin","Guinea");
        GSC_COUNTRY_MAP.put("gnb","Guinea-Bissau");
        GSC_COUNTRY_MAP.put("guy","Guyana");
        GSC_COUNTRY_MAP.put("hti","Haiti");
        GSC_COUNTRY_MAP.put("hmd","Heard Island and McDonald Mcdonald Islands");
        GSC_COUNTRY_MAP.put("vat","Holy See (Vatican City State)");
        GSC_COUNTRY_MAP.put("hnd","Honduras");
        GSC_COUNTRY_MAP.put("hkg","Hong Kong");
        GSC_COUNTRY_MAP.put("hun","Hungary");
        GSC_COUNTRY_MAP.put("isl","Iceland");
        GSC_COUNTRY_MAP.put("ind","India");
        GSC_COUNTRY_MAP.put("idn","Indonesia");
        GSC_COUNTRY_MAP.put("irn","Iran, Islamic Republic of");
        GSC_COUNTRY_MAP.put("irq","Iraq");
        GSC_COUNTRY_MAP.put("irl","Ireland");
        GSC_COUNTRY_MAP.put("imn","Isle of Man");
        GSC_COUNTRY_MAP.put("isr","Israel");
        GSC_COUNTRY_MAP.put("ita","Italy");
        GSC_COUNTRY_MAP.put("jam","Jamaica");
        GSC_COUNTRY_MAP.put("jpn","Japan");
        GSC_COUNTRY_MAP.put("jey","Jersey");
        GSC_COUNTRY_MAP.put("jor","Jordan");
        GSC_COUNTRY_MAP.put("kaz","Kazakhstan");
        GSC_COUNTRY_MAP.put("ken","Kenya");
        GSC_COUNTRY_MAP.put("kir","Kiribati");
        GSC_COUNTRY_MAP.put("prk","Korea, Democratic People's Republic of");
        GSC_COUNTRY_MAP.put("kor","Korea, Republic of");
        GSC_COUNTRY_MAP.put("kwt","Kuwait");
        GSC_COUNTRY_MAP.put("kgz","Kyrgyzstan");
        GSC_COUNTRY_MAP.put("lao","Lao People's Democratic Republic");
        GSC_COUNTRY_MAP.put("lva","Latvia");
        GSC_COUNTRY_MAP.put("lbn","Lebanon");
        GSC_COUNTRY_MAP.put("lso","Lesotho");
        GSC_COUNTRY_MAP.put("lbr","Liberia");
        GSC_COUNTRY_MAP.put("lby","Libya");
        GSC_COUNTRY_MAP.put("lie","Liechtenstein");
        GSC_COUNTRY_MAP.put("ltu","Lithuania");
        GSC_COUNTRY_MAP.put("lux","Luxembourg");
        GSC_COUNTRY_MAP.put("mac","Macao");
        GSC_COUNTRY_MAP.put("mkd","Macedonia, the Former Yugoslav Republic of");
        GSC_COUNTRY_MAP.put("mdg","Madagascar");
        GSC_COUNTRY_MAP.put("mwi","Malawi");
        GSC_COUNTRY_MAP.put("mys","Malaysia");
        GSC_COUNTRY_MAP.put("mdv","Maldives");
        GSC_COUNTRY_MAP.put("mli","Mali");
        GSC_COUNTRY_MAP.put("mlt","Malta");
        GSC_COUNTRY_MAP.put("mhl","Marshall Islands");
        GSC_COUNTRY_MAP.put("mtq","Martinique");
        GSC_COUNTRY_MAP.put("mrt","Mauritania");
        GSC_COUNTRY_MAP.put("mus","Mauritius");
        GSC_COUNTRY_MAP.put("myt","Mayotte");
        GSC_COUNTRY_MAP.put("mex","Mexico");
        GSC_COUNTRY_MAP.put("fsm","Micronesia, Federated States of");
        GSC_COUNTRY_MAP.put("mda","Moldova, Republic of");
        GSC_COUNTRY_MAP.put("mco","Monaco");
        GSC_COUNTRY_MAP.put("mng","Mongolia");
        GSC_COUNTRY_MAP.put("mne","Montenegro");
        GSC_COUNTRY_MAP.put("msr","Montserrat");
        GSC_COUNTRY_MAP.put("mar","Morocco");
        GSC_COUNTRY_MAP.put("moz","Mozambique");
        GSC_COUNTRY_MAP.put("mmr","Myanmar");
        GSC_COUNTRY_MAP.put("nam","Namibia");
        GSC_COUNTRY_MAP.put("nru","Nauru");
        GSC_COUNTRY_MAP.put("npl","Nepal");
        GSC_COUNTRY_MAP.put("nld","Netherlands");
        GSC_COUNTRY_MAP.put("ncl","New Caledonia");
        GSC_COUNTRY_MAP.put("nzl","New Zealand");
        GSC_COUNTRY_MAP.put("nic","Nicaragua");
        GSC_COUNTRY_MAP.put("ner","Niger");
        GSC_COUNTRY_MAP.put("nga","Nigeria");
        GSC_COUNTRY_MAP.put("niu","Niue");
        GSC_COUNTRY_MAP.put("nfk","Norfolk Island");
        GSC_COUNTRY_MAP.put("mnp","Northern Mariana Islands");
        GSC_COUNTRY_MAP.put("nor","Norway");
        GSC_COUNTRY_MAP.put("omn","Oman");
        GSC_COUNTRY_MAP.put("pak","Pakistan");
        GSC_COUNTRY_MAP.put("plw","Palau");
        GSC_COUNTRY_MAP.put("pse","Palestine, State of");
        GSC_COUNTRY_MAP.put("pan","Panama");
        GSC_COUNTRY_MAP.put("png","Papua New Guinea");
        GSC_COUNTRY_MAP.put("pry","Paraguay");
        GSC_COUNTRY_MAP.put("per","Peru");
        GSC_COUNTRY_MAP.put("phl","Philippines");
        GSC_COUNTRY_MAP.put("pcn","Pitcairn");
        GSC_COUNTRY_MAP.put("pol","Poland");
        GSC_COUNTRY_MAP.put("prt","Portugal");
        GSC_COUNTRY_MAP.put("pri","Puerto Rico");
        GSC_COUNTRY_MAP.put("qat","Qatar");
        GSC_COUNTRY_MAP.put("rou","Romania");
        GSC_COUNTRY_MAP.put("rus","Russian Federation");
        GSC_COUNTRY_MAP.put("rwa","Rwanda");
        GSC_COUNTRY_MAP.put("reu","Reunion");
        GSC_COUNTRY_MAP.put("blm","Saint Barthelemy");
        GSC_COUNTRY_MAP.put("shn","Saint Helena");
        GSC_COUNTRY_MAP.put("kna","Saint Kitts and Nevis");
        GSC_COUNTRY_MAP.put("lca","Saint Lucia");
        GSC_COUNTRY_MAP.put("maf","Saint Martin (French part)");
        GSC_COUNTRY_MAP.put("spm","Saint Pierre and Miquelon");
        GSC_COUNTRY_MAP.put("vct","Saint Vincent and the Grenadines");
        GSC_COUNTRY_MAP.put("wsm","Samoa");
        GSC_COUNTRY_MAP.put("smr","San Marino");
        GSC_COUNTRY_MAP.put("stp","Sao Tome and Principe");
        GSC_COUNTRY_MAP.put("sau","Saudi Arabia");
        GSC_COUNTRY_MAP.put("sen","Senegal");
        GSC_COUNTRY_MAP.put("srb","Serbia");
        GSC_COUNTRY_MAP.put("syc","Seychelles");
        GSC_COUNTRY_MAP.put("sle","Sierra Leone");
        GSC_COUNTRY_MAP.put("sgp","Singapore");
        GSC_COUNTRY_MAP.put("sxm","Sint Maarten (Dutch part)");
        GSC_COUNTRY_MAP.put("svk","Slovakia");
        GSC_COUNTRY_MAP.put("svn","Slovenia");
        GSC_COUNTRY_MAP.put("slb","Solomon Islands");
        GSC_COUNTRY_MAP.put("som","Somalia");
        GSC_COUNTRY_MAP.put("zaf","South Africa");
        GSC_COUNTRY_MAP.put("sgs","South Georgia and the South Sandwich Islands");
        GSC_COUNTRY_MAP.put("ssd","South Sudan");
        GSC_COUNTRY_MAP.put("esp","Spain");
        GSC_COUNTRY_MAP.put("lka","Sri Lanka");
        GSC_COUNTRY_MAP.put("sdn","Sudan");
        GSC_COUNTRY_MAP.put("sur","Suriname");
        GSC_COUNTRY_MAP.put("sjm","Svalbard and Jan Mayen");
        GSC_COUNTRY_MAP.put("swz","Swaziland");
        GSC_COUNTRY_MAP.put("swe","Sweden");
        GSC_COUNTRY_MAP.put("che","Switzerland");
        GSC_COUNTRY_MAP.put("syr","Syrian Arab Republic");
        GSC_COUNTRY_MAP.put("twn","Taiwan, Province of China");
        GSC_COUNTRY_MAP.put("tjk","Tajikistan");
        GSC_COUNTRY_MAP.put("tza","United Republic of Tanzania");
        GSC_COUNTRY_MAP.put("tha","Thailand");
        GSC_COUNTRY_MAP.put("tls","Timor-Leste");
        GSC_COUNTRY_MAP.put("tgo","Togo");
        GSC_COUNTRY_MAP.put("tkl","Tokelau");
        GSC_COUNTRY_MAP.put("ton","Tonga");
        GSC_COUNTRY_MAP.put("tto","Trinidad and Tobago");
        GSC_COUNTRY_MAP.put("tun","Tunisia");
        GSC_COUNTRY_MAP.put("tur","Turkey");
        GSC_COUNTRY_MAP.put("tkm","Turkmenistan");
        GSC_COUNTRY_MAP.put("tca","Turks and Caicos Islands");
        GSC_COUNTRY_MAP.put("tuv","Tuvalu");
        GSC_COUNTRY_MAP.put("uga","Uganda");
        GSC_COUNTRY_MAP.put("ukr","Ukraine");
        GSC_COUNTRY_MAP.put("are","United Arab Emirates");
        GSC_COUNTRY_MAP.put("gbr","United Kingdom");
        GSC_COUNTRY_MAP.put("usa","United States");
        GSC_COUNTRY_MAP.put("umi","United States Minor Outlying Islands");
        GSC_COUNTRY_MAP.put("ury","Uruguay");
        GSC_COUNTRY_MAP.put("uzb","Uzbekistan");
        GSC_COUNTRY_MAP.put("vut","Vanuatu");
        GSC_COUNTRY_MAP.put("ven","Venezuela");
        GSC_COUNTRY_MAP.put("vnm","Viet Nam");
        GSC_COUNTRY_MAP.put("vgb","British Virgin Islands");
        GSC_COUNTRY_MAP.put("vir","US Virgin Islands");
        GSC_COUNTRY_MAP.put("wlf","Wallis and Futuna");
        GSC_COUNTRY_MAP.put("esh","Western Sahara");
        GSC_COUNTRY_MAP.put("yem","Yemen");
        GSC_COUNTRY_MAP.put("zmb","Zambia");
        GSC_COUNTRY_MAP.put("zwe","Zimbabwe");
        GSC_COUNTRY_MAP.put("zzz","Unknown country");
        GSC_COUNTRY_MAP.put("xkk","Kosovo");
    }

}
