package seoclarity.backend.utils;

import com.alibaba.fastjson2.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/11 17:40
 */
public class ZohoCliqUtils {

    private static final String ZOHO_CLIQ_URL = "https://workflow.seoclarity.dev/v1/workflows/run";
    private static final String HEAD_KEY = "Authorization";
    private static final String ZOHO_CLIQ_MONITORING_ROBOT = "ZOHO_CLIQ_MONITORING_ROBOT";
    private static final String MONITOR_GROUP = "notification";
    private static final String STORM_ROBOT = "notificationstorm";

    public static String sendTextMessageToMonitorGroup(String content) {
        String s = System.getenv().get(ZOHO_CLIQ_MONITORING_ROBOT);
        System.out.println(s);
        return groupMessage(content, System.getenv().get(ZOHO_CLIQ_MONITORING_ROBOT), MONITOR_GROUP);
    }

    public static String sendTextMessageToStormGroup(String content) {
        String s = System.getenv().get(ZOHO_CLIQ_MONITORING_ROBOT);
        System.out.println(s);
        return groupMessage(content, System.getenv().get(ZOHO_CLIQ_MONITORING_ROBOT), STORM_ROBOT);
    }

    private static String groupMessage(String content, String headValue, String group) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HEAD_KEY, headValue);
        Map<String, Object> resultData = new HashMap<>();
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("msg", content);
        inputs.put("channel", group);
        resultData.put("inputs", inputs);
        resultData.put("response_mode", "blocking");
        resultData.put("user", "nbot");
        return httpPost(ZohoCliqUtils.ZOHO_CLIQ_URL, JSONObject.toJSONString(resultData), headers, "utf-8");
    }

    public static String httpPost(String url, String stringJson, Map<String,String> headers, String encode) {
        if (encode == null) {
            encode = "utf-8";
        }
        String content = null;
        CloseableHttpResponse httpResponse = null;
        try (CloseableHttpClient closeableHttpClient = HttpClientBuilder.create().build()) {
            HttpPost httPost = new HttpPost(url);
            httPost.setHeader("Content-type", "application/json");
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httPost.setHeader(entry.getKey(),entry.getValue());
                }
            }
            StringEntity stringEntity = new StringEntity(stringJson, encode);
            httPost.setEntity(stringEntity);
            httpResponse = closeableHttpClient.execute(httPost);
            HttpEntity entity = httpResponse.getEntity();
            content = EntityUtils.toString(entity, encode);
        } catch (Exception e) {
            e.printStackTrace();
        } finally{
            try {
                httpResponse.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return content;
    }

}
