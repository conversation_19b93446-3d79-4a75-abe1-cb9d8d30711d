package seoclarity.backend.utils;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.NumberTool;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.ui.velocity.VelocityEngineUtils;
import org.springframework.util.Assert;
import seoclarity.backend.entity.AgencyInfoEntity;

import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

public class ZeptoMailSenderComponent {

    public static final Log logger = LogFactory.getLog(ZeptoMailSenderComponent.class);

    //https://www.wrike.com/open.htm?id=1029955987
    public static int FUNCTION_TYPE_BACKEND_INTERNAL = 1;
    public static int FUNCTION_TYPE_DOWNLOAD_ALL = 2;
    public static int FUNCTION_TYPE_QUEUEBASE = 3;
    public static int FUNCTION_TYPE_ALERTS = 4;
    public static int FUNCTION_TYPE_EXTRACT = 5;


    private static String DEFAULT_USER_NAME = "emailapikey";
    private static String TOKEN_BACKEND_INTERNAL = "wSsVR61++RekXacvyDKqIeY8mVtSDw/wQEx/igb1uX/+GKzC9sc/l0bLBFT2FfNKEm9oQWFGoL0vyxZWgGIGiogtzVlSACiF9mqRe1U4J3x17qnvhDzNXGxUmhaAJYMLzwxik2BnFsEh+g==";
    private static String TOKEN_DOWNLOAD_ALL = "wSsVR60i+kT3C6Z7yTWvdepqylsBAVn3HUx93Van7X/4S6uX9Mc4lkKdBlT1TfcfRTNpFDsbrO0skR0Bg2IHiNV/ylhSDiiF9mqRe1U4J3x17qnvhDzNXWxemhWJKY4Jxg9snmBkFsEi+g==";
    private static String TOKEN_QUEUEBASE = "wSsVR60nrxGmWPwuzj2sL+05m11RAQ/1QEsp31Cj7nSpFvrLosdqlUyaDVOkSqBJEWY8EjUV9b8skE8CgTcK24wtnF9RASiF9mqRe1U4J3x17qnvhDzIXW9YlhWMJIoPwgxjn2dnGs4h+g==";
    private static String TOKEN_ALERTS = "wSsVR61+/UahXPt0z2Krc7trkVkAAQ+jQ0ov0AH3unCuT/7L9cc5nxKcAwakTvZNGWBoETsbrO8vzksGgTAJ3NoszFgIWyiF9mqRe1U4J3x17qnvhDzIWm9UmhqAKIkBwgRsk2JpG8Ej+g==";
    private static String TOKEN_EXTRACT = "wSsVR60i+kT3C6Z7yTWvdepqylsBAVn3HUx93Van7X/4S6uX9Mc4lkKdBlT1TfcfRTNpFDsbrO0skR0Bg2IHiNV/ylhSDiiF9mqRe1U4J3x17qnvhDzNXWxemhWJKY4Jxg9snmBkFsEi+g==";

    public static final String AGENCY_INFO_KEY = "agencyInfo";
    private String from;
    private String replyTo;
    static final String SERVER_HOST_NAME;
    private VelocityEngine velocityEngine;

    public static ZeptoMailSenderComponent zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");

    static {
        String hostName = null;
        try {
            InetAddress addr = InetAddress.getLocalHost();
            hostName = addr.getHostName();

            hostName = StringUtils.removeStartIgnoreCase(hostName, "http://");
            hostName = StringUtils.replace(hostName, ".", "");

        } catch (UnknownHostException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        SERVER_HOST_NAME = hostName;
    }

    /**
     * https://www.wrike.com/open.htm?id=1029457733
     * @param sendTo
     * @param bccTo
     * @param mailSubject
     * @param plainTextTemplateName
     * @param htmlTemplateName
     * @param templateModel
     * @param agencyInfo
     * @param functionType  1：后台监控提醒等脚本(内部使用);2:download all;3:queuebase;4:alert(包括发给客户的提醒邮件);5:导出脚本
     * @param attachmentFilenames
     * @param files
     * @return
     */
    public MimeMessage sendMimeMultiPartZeptoMailAndBccByFunctionType(String sendTo, String[] bccTo, String mailSubject,
                                                                    String plainTextTemplateName, String htmlTemplateName,
                                                                    Map templateModel, AgencyInfoEntity agencyInfo,
                                                                    Integer functionType,String[] attachmentFilenames, File[] files){

        String userName = DEFAULT_USER_NAME;
        String token;
        if(functionType == null){
            logger.error("===FunctionType NULL!!");
            return null;
        }else if(functionType == FUNCTION_TYPE_BACKEND_INTERNAL){
            token = TOKEN_BACKEND_INTERNAL;
        }else if(functionType == FUNCTION_TYPE_DOWNLOAD_ALL){
            token = TOKEN_DOWNLOAD_ALL;
        }else if(functionType == FUNCTION_TYPE_QUEUEBASE){
            token = TOKEN_QUEUEBASE;
        }else if(functionType == FUNCTION_TYPE_ALERTS){
            token = TOKEN_ALERTS;
        }else if(functionType == FUNCTION_TYPE_EXTRACT){
            token = TOKEN_EXTRACT;
        }else {
            logger.error("***not find FunctionType:" + functionType);
            return null;
        }

        return sendMimeMultiPartZeptoMailAndBcc(sendTo, bccTo, mailSubject, plainTextTemplateName, htmlTemplateName, templateModel,
                agencyInfo, userName, token, attachmentFilenames, files);
    }



    public MimeMessage sendMimeMultiPartZeptoMailAndBccByFunctionType(String[] sendTo, String[] bccTo, String mailSubject,
                                                                      String plainTextTemplateName, String htmlTemplateName,
                                                                      Map templateModel, AgencyInfoEntity agencyInfo,
                                                                      Integer functionType ,String[] attachmentFilenames, File[] files){

        String userName = DEFAULT_USER_NAME;
        String token;
        if(functionType == null){
            logger.error("===FunctionType NULL!!");
            return null;
        }else if(functionType == FUNCTION_TYPE_BACKEND_INTERNAL){
            token = TOKEN_BACKEND_INTERNAL;
        }else if(functionType == FUNCTION_TYPE_DOWNLOAD_ALL){
            token = TOKEN_DOWNLOAD_ALL;
        }else if(functionType == FUNCTION_TYPE_QUEUEBASE){
            token = TOKEN_QUEUEBASE;
        }else if(functionType == FUNCTION_TYPE_ALERTS){
            token = TOKEN_ALERTS;
        }else if(functionType == FUNCTION_TYPE_EXTRACT){
            token = TOKEN_EXTRACT;
        }else {
            logger.error("***not find FunctionType:" + functionType);
            return null;
        }

        return sendMimeMultiPartZeptoMailAndBcc(sendTo, bccTo, mailSubject, plainTextTemplateName, htmlTemplateName, templateModel,
                agencyInfo, userName, token, attachmentFilenames, files);
    }


    public static void sendEmailReportForRv(Date startTime, String subject, String message, String errorMsg) {
        String emailTo = "<EMAIL>";
        String[] ccTo = new String[] {"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
        sendEmailReport(startTime, subject, message, errorMsg, emailTo, ccTo);
    }

    public static void sendEmailReport(Date startTime, String subject, String message, String errorMsg) {
        String emailTo = "<EMAIL>";
        String[] ccTo = new String[] {"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
        sendEmailReport(startTime, subject, message, errorMsg, emailTo, ccTo);
    }

    public static void sendEmailReport(Date startTime, String subject, String message, String errorMsg, String emailTo, String[] ccTo) {
        try {
            Map<String, Object> reportMap = new HashMap<String, Object>();
            reportMap.put("userName", "Mitul");
            reportMap.put("dateString", DateFormatUtils.format(startTime, "MM/dd/yyyy"));
            reportMap.put("startTime", DateFormatUtils.format(startTime, "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));

            List<String> infos = new ArrayList<String>();
            infos.add(message);
            reportMap.put("info", infos);
            reportMap.put("title", subject);
            if (StringUtils.isEmpty(errorMsg)) {
                reportMap.put("errormessage", "");
            } else {
                reportMap.put("errormessage", errorMsg);
            }

            zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_common.txt", "mail_common.html", reportMap, null,
                    ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private MimeMessage sendMimeMultiPartZeptoMailAndBcc(String sendTo, String[] bccTo, String mailSubject,
                                                        String plainTextTemplateName, String htmlTemplateName,
                                                        Map templateModel, AgencyInfoEntity agencyInfo,
                                                         String userName, String token,
                                                         String[] attachmentFilenames, File[] files) {
        Assert.notNull(plainTextTemplateName);
        Assert.notNull(htmlTemplateName);
        Assert.notNull(sendTo);

        setCommonParams(templateModel);

        Properties properties = System.getProperties();
        properties.setProperty("mail.smtp.host", "smtp.zeptomail.com");
        properties.put("mail.smtp.port", "587");
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.starttls.enable", "true");
        properties.put("mail.smtp.from", "fromaddress");
        properties.put("mail.smtp.ssl.protocols", "TLSv1.2");
        Session session = Session.getDefaultInstance(properties);

        MimeMessage mimeMessage = new MimeMessage(session);
        MimeMessageHelper mimeMessageHelper;
        try {
            mimeMessageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            mimeMessageHelper.setTo(sendTo);
            if (bccTo != null && bccTo.length > 0) {
                mimeMessageHelper.setBcc(bccTo);
            }

            populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
            populateAgencyInfoToMap(templateModel, agencyInfo);

            mimeMessageHelper.setSubject(mailSubject);
            mimeMessageHelper.setSentDate(new Date());

            mimeMessageHelper.setText(
                    getContentByTemplate(plainTextTemplateName, templateModel),
                    getContentByTemplate(htmlTemplateName, templateModel));

            if (ArrayUtils.isNotEmpty(files)) {
                for (int i = 0; i < files.length; i++) {
                    String attachmentFilename = "attachment";
                    if (ArrayUtils.isNotEmpty(attachmentFilenames)) {
                        attachmentFilename = attachmentFilenames[i];
                    }
                    mimeMessageHelper.addAttachment(attachmentFilename, files[i]);
                }
            }

            Transport transport = session.getTransport("smtp");
            transport.connect("smtp.zeptomail.com", 587, userName, token);
            transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
            transport.close();

            logger.info("Mail successfully sent");

        } catch (MessagingException e) {
            logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
        }
        return null;
    }

    private MimeMessage sendMimeMultiPartZeptoMailAndBcc(String[] sendTo, String[] bccTo, String mailSubject,
                                                         String plainTextTemplateName, String htmlTemplateName,
                                                         Map templateModel, AgencyInfoEntity agencyInfo,
                                                         String userName, String token,
                                                         String[] attachmentFilenames, File[] files) {
        Assert.notNull(plainTextTemplateName);
        Assert.notNull(htmlTemplateName);
        Assert.notNull(sendTo);

        setCommonParams(templateModel);

        Properties properties = System.getProperties();
        properties.setProperty("mail.smtp.host", "smtp.zeptomail.com");
        properties.put("mail.smtp.port", "587");
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.starttls.enable", "true");
        properties.put("mail.smtp.from", "fromaddress");
        properties.put("mail.smtp.ssl.protocols", "TLSv1.2");
        Session session = Session.getDefaultInstance(properties);

        MimeMessage mimeMessage = new MimeMessage(session);
        MimeMessageHelper mimeMessageHelper;
        try {
            mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
            mimeMessageHelper.setTo(sendTo);
            if (bccTo != null && bccTo.length > 0) {
                mimeMessageHelper.setBcc(bccTo);
            }

            populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
            populateAgencyInfoToMap(templateModel, agencyInfo);

            mimeMessageHelper.setSubject(mailSubject);
            mimeMessageHelper.setSentDate(new Date());

            mimeMessageHelper.setText(
                    getContentByTemplate(plainTextTemplateName, templateModel),
                    getContentByTemplate(htmlTemplateName, templateModel));

            if (ArrayUtils.isNotEmpty(files)) {
                for (int i = 0; i < files.length; i++) {
                    String attachmentFilename = "attachment";
                    if (ArrayUtils.isNotEmpty(attachmentFilenames)) {
                        attachmentFilename = attachmentFilenames[i];
                    }
                    mimeMessageHelper.addAttachment(attachmentFilename, files[i]);
                }
            }

            Transport transport = session.getTransport("smtp");
            transport.connect("smtp.zeptomail.com", 587, userName, token);
            transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
            transport.close();

            logger.info("Mail successfully sent");

        } catch (MessagingException e) {
            logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
        }
        return null;
    }

    public String getContentByTemplate(String templateName, Map templateModel) {
        if (templateModel != null) {
            //velocity tools
            //$!dateFormatter.format('yyyy-M-d', ${currDate})
            templateModel.put("dateFormatter", new DateTool());
            //$!numberFormatter.format('integer', ${aint})
            //$!numberFormatter.format('number', ${afloat})
            templateModel.put("numberFormatter", new NumberTool());
        }
        return VelocityEngineUtils.mergeTemplateIntoString(velocityEngine, templateName, "UTF-8", templateModel);
    }
    private void setCommonParams(Map templateModel) {
        if (templateModel != null) {
            templateModel.put("hostName", SERVER_HOST_NAME);
        }
    }

    private void populateEmailFromReplyto(MimeMessageHelper mimeMessageHelper, AgencyInfoEntity agencyInfo) throws MessagingException {
        if (agencyInfo != null) {
            mimeMessageHelper.setReplyTo(agencyInfo.getEmailReplyto());
            if (StringUtils.isNotBlank(agencyInfo.getEmailNickname())) {
                String senderNickname = null;
                try {
                    senderNickname = javax.mail.internet.MimeUtility.encodeText(agencyInfo.getEmailNickname());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                mimeMessageHelper.setFrom(new InternetAddress(senderNickname + " <" + agencyInfo.getEmailFrom() + ">"));
            } else {
                mimeMessageHelper.setFrom(agencyInfo.getEmailFrom());
            }
        } else {
            mimeMessageHelper.setReplyTo(replyTo);
            mimeMessageHelper.setFrom(from);
        }
    }

    private void populateAgencyInfoToMap(Map templateModel, AgencyInfoEntity agencyInfo) {
        if (templateModel == null) {
            templateModel = new HashMap();
        }
        if(agencyInfo != null){
            templateModel.put(AGENCY_INFO_KEY, agencyInfo);
        }
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public VelocityEngine getVelocityEngine() {
        return velocityEngine;
    }

    public void setVelocityEngine(VelocityEngine velocityEngine) {
        this.velocityEngine = velocityEngine;
    }


    public static void sendEmailReport(String userName, Date startTime, String subject, List<String> infos, String errorMsg, String emailTo) {
        try {
            Map<String, Object> reportMap = new HashMap<>();
            reportMap.put("userName", userName);
            reportMap.put("dateString", DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
            reportMap.put("startTime", DateFormatUtils.format(startTime, "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("info", infos);
            reportMap.put("title", subject);

            if (StringUtils.isEmpty(errorMsg)) {
                reportMap.put("errormessage", "");
            } else {
                reportMap.put("errormessage", errorMsg);
            }
            // String emailTo = "<EMAIL>";
            zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, null, subject, "mail_common.txt", "mail_common.html", reportMap, null,
                    ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
