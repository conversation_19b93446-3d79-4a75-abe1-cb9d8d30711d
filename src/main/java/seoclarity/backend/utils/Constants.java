/**
 * 
 */
package seoclarity.backend.utils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang.time.DateUtils;

/**
 * com.actonia.saas.constant.Constants.java
 * 
 * @version $Revision: 502 $ $Author: guof@SHINETECHCHINA $
 */
public class Constants {
	
	/*public static final int[][] AMERICA_ENGINE_LANGUAGES = new int[][] {
		{1,1}, {3,3}, {3,4}, {255,1}, {255,3}, {255,26}, {255,16}, {255,27}, {255,6}, {255,15}, 
		{255,17}, {255,7}, {255,25}, {255,9}, {255,24}, {255,8}, {255,19}, {255,28}, {255,29}, {255,5}, 
		{255,31}, {255,32}, {255,33}, {255,35}, {255,36}, {255,37}, {255,34}, {255,14}, {255,13}, {255,18},
		{255,11}, {255,10}, {255,12}, {255,30}, {255,20}, {100,1}, {100,8}, {15,16}, {25,26}, {26,27}, 
		{27,28},  {28,29}, {11,12}, {44,44}, 
		{100,16}, {100,3}
	};
	public static final int[][] EUROPE_ENGINE_LANGUAGES = new int[][] {
		{40,40}, {41,41}, {42,42}, {19,20}, {6,8}, {13,14}, {12,13}, {9,10}, {14,15}, {16,17}, 
		{10,11}, {4,7}, {8,9}, {17,18}, {5,8}, {22,23}, {21,22}, {20,21}, {29,30}, {39,39}, 
		{43,43}, {45,45}, {45,46}, {46,47}, {47,48},
		{100,15}, {100,7}, {100,22},
		{100,17}, {100,9},  
	};
	public static final int[][] ASIA_ENGINE_LANGUAGES = new int[][] {
		{18,19}, {34,34}, {36,36}, {35,35}, {33,33}, {23,24}, {24,25}, {37,37}, {32,32}, {31,31}, 
		{2,5}, {38,38}, {150,6}, {150,1}, {30,6}, {120,39}, {160,24},
		{100,34}, {100,24}, {100,19}, {100,36}, {100,37}, {100,35}, {100,31}, {100,33},
		{100,32}, {100,31}, {100,25}, {100,6}, {100,5},
	};*/
	
	public static final String CONTINENT_AMERICA = "america";
	public static final String CONTINENT_ASIA = "asia";
	public static final String CONTINENT_EUROPE = "europe";

//    public static final String QUEUE_NAME_GOOGLE_USEN = "KEYWROD_RANK_QUEUE_GOOGLE_COM_USEN";
//
//    public static final String QUEUE_NAME_FAILED_QUEUE ="KEYWORD_RANK_FAIL_QUEUE_USEN";
//
//    public static final String QUEUE_NAME_GOOGLE_MOBILE_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_MOBILE_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_MOBILE_COMMON_CRAWL_INTL = "QUEUE_NAME_GOOGLE_MOBILE_COMMON_CRAWL_INTL";
//
//    public static final String QUEUE_NAME_BAIDU_COMMON_CRAWL = "QUEUE_NAME_BAIDU_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_YANDEX_COMMON_CRAWL = "QUEUE_NAME_YANDEX_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_BING_USEN = "KEYWROD_RANK_QUEUE_BING_USEN";
//
//    public static final String QUEUE_NAME_YAHOO_USEN = "KEYWROD_RANK_QUEUE_YAHOO_USEN";
//
//    public static final String QUEUE_NAME_GOOGLE_USEN_WEEKLY = "KEYWROD_RANK_QUEUE_GOOGLE_USEN_WEEKLY";
//
//    public static final String QUEUE_NAME_GOOGLE_USEN_MONTHLY = "KEYWROD_RANK_QUEUE_GOOGLE_USEN_MONTHLY";
//    public static final String QUEUE_NAME_GOOGLE_USEN_BARZIL = "KEYWROD_RANK_QUEUE_GOOGLE_COM_BARZIL";
//    public static final String QUEUE_NAME_GOOGLE_USEN_UK_ALL = "KEYWROD_RANK_QUEUE_GOOGLE_CO_UK_ALL";
//
//    public static final String QUEUE_NAME_BING_USEN_COMMON_CRAWL = "CLOUD_QUEUE_NAME_BING_USEN_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_YAHOO_USEN_COMMON_CRAWL = "CLOUD_QUEUE_NAME_YAHOO_USEN_COMMON_CRAWL";
//    public static final String QUEUE_NAME_YAHOO_JAPAN_COMMON_CRAWL = "CLOUD_QUEUE_NAME_YAHOO_JAPAN_COMMON_CRAWL";
//    public static final String CLOUD_QUEUE_NAME_YAHOO_INTL_COMMON_CRAWL = "CLOUD_QUEUE_NAME_YAHOO_INTL_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_CITY_RANK_GOOGLE_USEN = "CLOUD_QUEUE_NAME_CITY_RANK_GOOGLE_USEN";
//
//    public static final String QUEUE_NAME_GOOGLE_LL_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_LL_COMMON_CRAWL";
//
//    public static final String CLOUD_QUEUE_NAME_MONTHLY_RANK_GOOGLE_USEN = "CLOUD_QUEUE_NAME_MONTHLY_RANK_GOOGLE_USEN";
//    public static final String CLOUD_QUEUE_NAME_MONTHLY_RANK_GOOGLE_INTL = "CLOUD_QUEUE_NAME_MONTHLY_RANK_GOOGLE_INTL";
//    public static final String CLOUD_QUEUE_NAME_MONTHLY_RANK_GOOGLE_INTL_MOBILE = "CLOUD_QUEUE_NAME_MONTHLY_RANK_GOOGLE_INTL_MOBILE";
//    public static final String CLOUD_QUEUE_NAME_MONTHLY_RANK_GOOGLE_USEN_MOBILE = "CLOUD_QUEUE_NAME_MONTHLY_RANK_GOOGLE_USEN_MOBILE";
//
//    public static final String QUEUE_NAME_360_SEARCH = "QUEUE_NAME_360_SEARCH";
//
//    public static final String QUEUE_NAME_REALTIME_KEYWORD_RANK = "QUEUE_NAME_REALTIME_KEYWORD_RANK";
//
//    public static final String QUEUE_NAME_REALTIME_TARGETURL = "QUEUE_NAME_REALTIME_TARGETURL";
//
//    public static final String CLOUD_QUEUE_NAME_MONTHLY_RANK_BAIDU_CN = "CLOUD_QUEUE_NAME_MONTHLY_RANK_BAIDU_CN";
//
//    public static final String CLOUD_QUEUE_NAME_URLMETRICS_WEEKLY = "CLOUD_QUEUE_NAME_URLMETRICS_WEEKLY";
//
//    public static final String CLOUD_QUEUE_NAME_URLMETRICS_DAILY = "CLOUD_QUEUE_NAME_URLMETRICS_DAILY";
//
//    public static final String QUEUE_NAME_GOOGLE_EU_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_EU_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_EU_UK_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_EU_UK_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_EU_FR_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_EU_FR_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_EU_DE_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_EU_DE_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_EU_EXT_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_EU_EXT_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_ASIAN_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_ASIAN_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_ASIAN_HONGKONG_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_ASIAN_HONGKONG_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_ASIAN_SINGAPORE_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_ASIAN_SINGAPORE_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_ASIAN_SYDNEY_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_ASIAN_SYDNEY_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_ASIAN_EXT_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_ASIAN_EXT_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_CA_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_CA_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_GOOGLE_SOUTH_AMERICA_COMMON_CRAWL = "QUEUE_NAME_GOOGLE_SOUTH_AMERICA_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_DOMAIN_NAME_COMMON_CRAWL = "QUEUE_NAME_DOMAIN_NAME_COMMON_CRAWL";
//
//    public static final String CLOUD_QUEUE_NAME_YAHOO_TW_COMMON_CRAWL = "CLOUD_QUEUE_NAME_YAHOO_TW_COMMON_CRAWL";
//
//    //by Cee
//    public static final int[] IMPORTANT_DOMAINS = new int[]{4, 1535, 263, 954, 765, 551, 2052, 2032, 2166};
//
//    //by Cee
//    public static final String DEV_TEAM_EMAIL = "<EMAIL>";
//
//    // personal email address
//    public static final String DEV_TEAM_EMAIL_ALPS = "<EMAIL>";
//    public static final String DEV_TEAM_EMAIL_WILBER = "<EMAIL>";
//
//    // public static final String QUEUE_NAME_GOOGLE_USEN_COMMON_CRAWL =
//    // "QUEUE_NAME_GOOGLE_USEN_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_ADHOC_COMMON_CRAWL = "QUEUE_NAME_ADHOC_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_CITY_WEEKLY_COMMON_CRAWL = "QUEUE_NAME_CITY_WEEKLY_COMMON_CRAWL";
//
//    public static final String QUEUE_NAME_COMPETITORURL_COMMON_CRAWL_FOR_DOM = "QUEUE_NAME_COMPETITORURL_COMMON_CRAWL_FOR_DOM";
//    public static final String QUEUE_NAME_TARGETURL_COMMON_CRAWL = "QUEUE_NAME_TARGETURL_COMMON_CRAWL";
//    public static final String QUEUE_NAME_PARTNERURL_COMMON_CRAWL = "QUEUE_NAME_PARTNERURL_COMMON_CRAWL";
//    public static final String QUEUE_NAME_COMPETITORURL_COMMON_CRAWL = "QUEUE_NAME_COMPETITORURL_COMMON_CRAWL";
//
//    // //////////////////////////////////
//    public static final String QUEUE_NAME_ON_DEMAND_CRAWL_TARGETURL = "ON_DEMAND_CRAWL_TARGETURL";
//
//    public static final String QUEUE_NAME_ON_DEMAND_CRAWL_PARTNERURL = "ON_DEMAND_CRAWL_PARTNERURL";
//
//    public static final String QUEUE_NAME_ON_DEMAND_CRAWL_COMPETITORURLURL = "ON_DEMAND_CRAWL_COMPETITORURLURL";
//    // /////////////////////////////////
//    public static final String QUEUE_NAME_CONTENT_IDEAS_COMMON_CRAWL_MONTHLY = "QUEUE_NAME_CONTENT_IDEAS_COMMON_CRAWL_MONTHLY";
//    public static final String QUEUE_NAME_CONTENT_IDEAS_COMMON_CRAWL_DAILY = "QUEUE_NAME_CONTENT_IDEAS_COMMON_CRAWL_DAILY";
//
//    // ////////////////////////////
//    public static final String QUEUE_NAME_GOOGLE_REGION_USA = "QUEUE_NAME_GOOGLE_REGION_USA";
//    public static final String QUEUE_NAME_BING_REGION_USA = "QUEUE_NAME_BING_REGION_USA";
//    public static final String QUEUE_NAME_YAHOO_REGION_USA = "QUEUE_NAME_YAHOO_REGION_USA";
//
//    public static final String QUEUE_NAME_GOOGLE_REGION_EUROPE = "QUEUE_NAME_GOOGLE_REGION_EUROPE";
//    public static final String QUEUE_NAME_BING_REGION_EUROPE = "QUEUE_NAME_BING_REGION_EUROPE";
//
//    public static final String QUEUE_NAME_GOOGLE_REGION_ASIA = "QUEUE_NAME_GOOGLE_REGION_ASIA";
//    public static final String QUEUE_NAME_BING_REGION_ASIA = "QUEUE_NAME_BING_REGION_ASIA";
//    public static final String QUEUE_NAME_NAVER_REGION_ASIA = "QUEUE_NAME_NAVER_REGION_ASIA";
//
//    public static final String QUEUE_NAME_GOOGLE_REGION_AUSTRLIA = "QUEUE_NAME_GOOGLE_REGION_AUSTRLIA";
//    public static final String QUEUE_NAME_BING_REGION_AUSTRLIA = "QUEUE_NAME_BING_REGION_AUSTRLIA";
//    // ///////////////////////////
//
//
//    // ////////////// TimeZone ////////////////////
//    public static final String QUEUE_NAME_GROUP_AUSTRALIA = "QUEUE_NAME_GROUP_AUSTRALIA";
//
//    public static final String QUEUE_NAME_GROUP_USA = "QUEUE_NAME_GROUP_USA";
//
//    public static final String QUEUE_NAME_GROUP_UK = "QUEUE_NAME_GROUP_UK";
//
//    public static final String QUEUE_NAME_GROUP_CHINA = "QUEUE_NAME_GROUP_CHINA";
//
//    public static final int GROUP_USA = 1;
//
//    public static final int GROUP_UK = 2;
//
//    public static final int GROUP_AUSTRALIA = 3;
//
//    public static final int GROUP_CHINA = 4;
//
//    public static final int KEYWORDRANKTYPE_REGULAR = 1;
//
//    public static final int KEYWORDRANKTYPE_MOBILE = 2;
//
//    public static final int KEYWORDRANKTYPE_CITY = 3;
//
//    // //////////////////////////////////
//
//    public static final int TARGET_PARTNER_RELATION_ADD_BY_CRAWL_PARTNER_URL = 2001;
//    public static final int TARGET_PARTNER_RELATION_ADD_BY_IMPORT_FILE = 2002;
//    public static final int TARGET_PARTNER_RELATION_ADD_BY_ASSOCIATE_ACQUISITION = 2003;
//
//    public static final int TYPE_PARTNERURL = 2;
//    public static final int TYPE_TARGETURL = 1;
//    public static final int TYPE_COMPETITORURL = 3;
//    public static final int TYPE_COMPETITOR_BACKLINK = 4;
//    public static final int TYPE_PARTNER = 5;
    public static final int TARGET_URL_STATUS_ACTIVE = 1;
//    public static final int TARGET_URL_STATUS_INACTIVE = 2;
    public static final int TARGET_URL_TYPE_ADDED_BY_USER = 1;
//    public static final int COMPETITOR_URL_TYPE_ADDED_BY_USER = 1;
//    public static final int SCRAPE_PAGE_TYPE_TITLE = 1;
//    public static final String META_KEYWORD = "Keywords";
//    public static final int SCRAPE_PAGE_TYPE_METAK_EYWORD = 2;
//    public static final int SCRAPE_PAGE_TYPE_CANONICALTAG = 100;
//    public static final int SCRAPE_PAGE_TYPE_HTMLCONTENT = 101;
//    public static final String META_DESCRIPTION = "description";
//    public static final int SCRAPE_PAGE_TYPE_META_DESCRIPTION = 3;
//    public static final int SCRAPE_PAGE_TYPE_H1 = 4;
//    public static final int SCRAPE_PAGE_TYPE_H2 = 5;
//    public static final int SCRAPE_PAGE_TYPE_CUSTOMER_DIV_ID = 6;
//    public static final int SCRAPE_PAGE_TYPE_CUSTOMER_DIV_CLASS = 7;
//    public static final int LOG_TYPE_OF_PARTNERURL = 2;
//    public static final int LOG_TYPE_OF_TARGET_URL_PAGE = 5;
//    // public static final int LOG_TYPE_OF_TARGET_URL_AND_TARGET_URL_PAGE = 6;
//    public static final int LOG_TYPE_COMPETITOR_URL_PAGE_CONTENT = 31;
//    public static final int DIV_TRACKIDIN_OPTION = 3;
//    public static final int DIV_TRACKCLASSIN_OPTION = 4;
//    public static final int SCRAPE_PAGETYPE_DIVID = 6;
//    public static final int SCRAPE_PAGETYPE_DIVCLASS = 7;
//    public static final String SCRAPE_PAGETYPE_DIVID_STRING = "id";
//    public static final String SCRAPE_PAGETYPE_DIVCLASS_STRING = "class";
//    public static final int PARTNER_ADD_BY_USER = 1;
//    public static final int PARTNER_DISABLE_BY_NO_ACTIVE_PARTNERURL = 2;
//    public static final int PARTNER_ADD_BY_ANALYTICS = 101;
//    public static final int PARTNER_URL_STATUS_ACTIVE = 1;
//    public static final int COMPETITORURL_ADD_BY_USER = 1;
//    public static final int TARGETURL_TYPE = 1;
//    public static final int PARTNERURL_TYPE = 2;
//    public static final int COMPEITIORURL_TYPE = 3;
//    public static final int PARTNERURLSTATUSACTIVE = 1;
//
//    public static final int PARTNERURLSTATUSPOTENTIAL = 2;
//
//    public static final int PARTNERURL_STATUS_ENDED = 3;
//    public static final int TARGETURL_ADDBY_ANALYTICS = 4;
//    public static final int TARGETURL_ADDBY_MOBILE_ANALYTICS = 5;
//    public static final int TARGETURL_ADDBY_MACYS_SPECIAL = 99;
//    public static final int TARGETURL_ADDBY_PARSER_LOG = 100;
    public static final int KEYWORD_ADDEDBY_USER = 1;

//    public static int WaitProcessNumberFlag = -99;

    public static Date WaitProcessDateFlag = null;

//    public static int WaitProcessInstantNumberFlag = -88;

    public static Date WaitProcessInstantDateFlag = null;

    public static Date ErrorCachedDate = null;

    public static int KEYWORD_RANKCHECK_FLAG = 1;

    public static Date NOT_CACHED_DATE;

    public static Date SERVER_BLOCK_DATE;

    public static Date NOT_RESPONSE_DATE;

//    public static final int ELEMENT_TYPE_KEYWORD = 1;
//
//    public static final int ELEMENT_TYPE_TRAGET_URL = 2;
//
//    public static final int ELEMENT_TYPE_PARTNER = 3;
//
//    public static final int ELEMENT_TYPE_PARTNER_URL = 4;
//
//    public static final int ELEMENT_TYPE_COMPETITOR = 5;
//
//    public static final int ELEMENT_TYPE_COMPETITOR_URL = 6;
//
//    public static final int ELEMENT_TYPE_TAG = 7;
//
//    public static final int ELEMENT_TYPE_DOMAIN = 8;
//
//    public static int AddedBySystem = 0;
//
//    public static int NotificationStatusActive = 1;
//
//    public static int NotificationTypePaymentReminder = 1;
//
//    public static int NotificationTypeGPRDeclined = 2;
//
//    public static int NotificationTypeUrlNA = 3;
//
//    public static int NotificationTypeNoTargetUrl = 4;
//
//    public static int NotificationTypeNoDefinedTargetUrl = 5;
//
//    public static int NotificationTypeNoDefinedTargetUrlAnchortext = 6;
//
//    public static int NotificationTypeCantCapturePR = 7;
//
//    public static int NotificationTypeTargetUrlTrafficDataAbnormalUpper = 8;
//
//    public static int NotificationTypeKeywordTrafficDataAbnormalUpper = 9;
//
//    public static int NotificationTypeTargetUrlTrafficDataAbnormalLower = 10;
//
//    public static int NotificationTypeKeywordTrafficDataAbnormalLower = 11;
//
//    public static int NotificationTypeTargetUrlContentChanged = 12;
//
//    public static int NOTIFICATION_TYPE_MOVE_INTO_TOP_20 = 21;
//
//    public static int NOTIFICATION_TYPE_MOVE_OUT_TOP_20 = 22;
//
//    public static int NOTIFICATION_TYPE_HIGHEST_RANKING_TARGET_URL_CHANGED = 23;
//
//    public static int NOTIFICATION_DOMAIN_WITH_SAME_IP = 30;
//
//    // event job
//    public static final int EVENT_ELEMENT_KEYWORD = 1;
//
//    public static final int EVENT_ELEMENT_TARGETURL = 2;
//
//    public static final int EVENT_ELEMENT_PARTNERDOMAIN = 3;
//
//    public static final int EVENT_ELEMENT_PARTNERURL = 4;
//
//    public static final int EVENT_RESPONSE_CHANGED = 4;
//
//    public static final int EVENT_TYPE_HIGHER_TRAFFIC_ALERT_URL = 5;
//
//    public static final int EVENT_TYPE_LOWER_TRAFFIC_ALERT_URL = 6;
//
//    public static final int EVENT_TYPE_HIGHER_TRAFFIC_ALERT_KEYWORD = 7;
//
//    public static final int EVENT_TYPE_LOWER_TRAFFIC_ALERT_KEYWORD = 8;
//
//    public static final int EVENT_TYPE_MOVE_OUT_TOP_20 = 9;
//
//    public static final int EVENT_TYPE_MOVE_INTO_TOP_20 = 10;
//
//    public static final int EVENT_TYPE_SUB_OPTIMAL_URL = 12;
//
//    public static final int EVENT_TYPE_DOMAIN_WITH_SAME_IP = 13;
//
//    public static final int EVENT_TYPE_URL_NA = 14;
//
//    public static final int EVENT_TYPE_NO_TARGETURL = 15;
//
//    public static final int EVENT_TYPE_NODEFINED_TARGETURL = 16;
//
//    public static final int EVENT_TYPE_NODEFINED_TURLANCHORTEXT = 17;
//
//    public static final int EVENT_TYPE_LINKCLARITY_LINKS_ACQUIRED = 18;
//
//    public static final int EVENT_TYPE_LINKS_EXPIRING = 19;
//
//    public static final int JOB_NOTISERVICE_ENGINE = 3;
//
//    //https://www.wrike.com/open.htm?id=15916670
//    public static final String TYPE_NEW = "new";
//
//    //https://www.wrike.com/open.htm?id=15916670
//	public static final String TYPE_LOST = "lost";
//
//	//https://www.wrike.com/open.htm?id=13153033
//    public static int LogTypeOfTargetUrlAndTargetUrlPage=6;
//
//    //https://www.wrike.com/open.htm?id=13153033
//    public static int LogTypeOfTargetUrlPage=5;
//
//	//https://www.wrike.com/open.htm?id=13153033
//	public static int ScrapePageTypeTitle=1;
//
//	//https://www.wrike.com/open.htm?id=17878193
//	public static final String TABLE_NAME_USER_BACKLINK = "usr_backlink_";
//	public static final String TABLE_NAME_USER_PARTNER_DOMAIN = "usr_partner_domain_";
//
//	//https://www.wrike.com/open.htm?id=21610064
//	//by sunny
//	public static final String GWM_METRUC_COLUMN_IMPRESSIONS = "impressions";
//	public static final String GWM_METRUC_COLUMN_CLICKS = "clicks";
//	public static final String GWM_METRUC_COLUMN_CTR = "ctr";
//	public static final String GWM_METRUC_COLUMN_AVG_POSITION = "avg_position";
//
//	public static final String QUEUE_NAME_CRAWL_URLS_DAILY_HTML = "CRAWL_URLS_DAILY_HTML_";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_HTML = "CLOUD_CRAWL_URLS_DAILY_HTML_";
//	public static final String QUEUE_NAME_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES = "CRAWL_URLS_DAILY_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CRAWL_URLS_DAILY_HTML_LANGUAGES = "CRAWL_URLS_DAILY_HTML_LANGUAGES";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES = "CLOUD_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_HTML_LANGUAGES = "CLOUD_CRAWL_URLS_DAILY_HTML_LANGUAGES";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_HTML_STOP_CRAWL = "CLOUD_CRAWL_URLS_DAILY_HTML_STOP_CRAWL";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_SOCIAL_QUEUE_NAMES = "CLOUD_CRAWL_URLS_DAILY_SOCIAL_QUEUE_NAMES";
//	public static final String SQS_ACCESS_KEY = "accessKey";
//	public static final String SQS_SECRET_KEY = "secretKey";
//	public static final String UNDERSCORE = "_";
//    public static final String SQS_LOCATION_OREGON = "sqs.us-west-2.amazonaws.com";
//    public static final int CRAWL_HTML = 1;
//    public static final int CRAWL_SOCIAL = 2;
//    public static final String ALL = "all";
//    public static final String HTML = "html";
//    public static final String STOP_HTML = "stop-html";
//    public static final String SOCIAL = "social";
//    public static final String STOP_SOCIAL = "stop-social";
//    public static final String QUEUE_NAME = "queueName";
//	public static final String MAX_CONCURRENT_THREADS = "maxConcurrentThreads";
//	public static final String DELAY_IN_SECONDS = "delayInSeconds";
//	public static final String USER_AGENT = "userAgent";
//	public static final String LANGUAGE_CODE = "languageCode";
//	public static final String STOP_CRAWL = "stopCrawl";
//	public static final String QUEUE_NAME_CRAWL_COMPETITOR_URLS_HTML = "CRAWL_COMPETITOR_URLS_HTML_";
//	public static final String QUEUE_NAME_CRAWL_COMPETITOR_URLS_SOCIAL = "CRAWL_COMPETITOR_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_CRAWL_COMPETITOR_URLS_HTML_QUEUE_NAMES = "CRAWL_COMPETITOR_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CRAWL_COMPETITOR_URLS_HTML_LANGUAGES = "CRAWL_COMPETITOR_URLS_HTML_LANGUAGES";
//	public static final String QUEUE_NAME_CRAWL_COMPETITOR_URLS_SOCIAL_QUEUE_NAMES = "CRAWL_COMPETITOR_URLS_SOCIAL_QUEUE_NAMES";
//	public static final String AWS_CREDENTIALS_PROPERTIES_FILE_NAME = "/AwsCredentials.properties";
//
//	//https://www.wrike.com/open.htm?id=25493593
//	//by cee
//	public static final int UNTAGGED_FLAG = -9;
    
    public static final String FILTER_TABLE_NAME_PREFIX = "mgd_keyword_daily_summary_";
	public static final int FILTER_PERIOD_INDICATOR_YESTERDAY = 1;
	public static final int FILTER_PERIOD_INDICATOR_DAY_BEFORE_YESTERDAY = 2;
	public static final int FILTER_PERIOD_INDICATOR_LAST_WEEK = 3;
	public static final int FILTER_PERIOD_INDICATOR_LAST_MONTH = 4;
	public static final BigDecimal DECIMAL_100 = new BigDecimal(100);
	public static final int FILTER_GROUP_BY_FUNCTION_INCREASE_MEANS_LARGER_VALUE = 1;
	public static final int FILTER_GROUP_BY_FUNCTION_INCREASE_MEANS_SMALLER_VALUE = 2;	
	public static final String FILTER_COLUMN_NAME_ESTIMATED_ENTRANCE = "estimate_entrance";
	public static final String FILTER_COLUMN_NAME_HIGHEST_TRUE_RANK = "highest_true_rank";
	public static final String FILTER_COLUMN_NAME_HIGHEST_WEB_RANK = "highest_web_rank";
	public static final String FILTER_COLUMN_NAME_AVERAGE_SEARCH_VOLUME = "avg_search_volume";
	public static final String FILTER_GROUP_BY_COLUMN_NAME_KEYWORD_ID = " keyword_id ";
	public static final String FILTER_PERIOD_YESTERDAY_DESC = "Yesterday";
	public static final String FILTER_PERIOD_DAY_BEFORE_YESTERDAY_DESC = "Day Before Yesterday";
	public static final String FILTER_PERIOD_LAST_WEEK_DESC = "Last Week";
	public static final String FILTER_PERIOD_LAST_MONTH_DESC = "Last Month";
	public static final int FILTER_CRITERIA_VERSION_KEYWORD_DAILY_SUMMARY = 2;
	public static final int KEYWORD_DAILY_SUMMARY_FILTER_VERSION = 2;
	public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
	public static final String DATE_FORMAT_YYYY_MM_DD_HHMMSS = "yyyy-MM-dd HH:mm:ss";	
	public static final String DATE_FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
	public static final String EMPTY_STRING = " ";
	public static final String PERCENT_TWO_ZERO = "%20";
	public static final String DATE_FORMAT_YYYYMM = "yyyyMM";
	public static final String NULL_STRING = "";	
	public static final String APOS = "'";	
	public static final String PERCENT_SIGN = "%";
	public static final String DATE_FORMAT_YYYY_MM_DD_E = "yyyy-MM-dd (E.)";
	public static final String DATE_FORMAT_DD = "dd";
	public static final String DATE_FORMAT_MM_DD_YY = "MM/dd/yy";
	public static final String SQL_QUERY_REGULAR_EXPRESSION_BEGIN_WITH = "^";
	public static final String UTF_8 = "utf-8";
	
    static {
        try {
            NOT_CACHED_DATE = DateUtils.parseDate("8881-01-01 ", new String[] { "yyyy-MM-dd " });
            SERVER_BLOCK_DATE = DateUtils.parseDate("8882-01-01 ", new String[] { "yyyy-MM-dd " });
            NOT_RESPONSE_DATE = DateUtils.parseDate("8883-01-01 ", new String[] { "yyyy-MM-dd " });
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar date = Calendar.getInstance();
        date.set(Calendar.YEAR, 9999);
        date.set(Calendar.MONTH, 9);
        date.set(Calendar.DATE, 9);
        WaitProcessDateFlag = date.getTime();

        Calendar date1 = Calendar.getInstance();
        date1.set(Calendar.YEAR, 8888);
        date1.set(Calendar.MONTH, 8);
        date1.set(Calendar.DATE, 8);
        WaitProcessInstantDateFlag = date1.getTime();

        Calendar date2 = Calendar.getInstance();
        date2.set(Calendar.YEAR, 1111);
        date2.set(Calendar.MONTH, 1);
        date2.set(Calendar.DATE, 1);
        ErrorCachedDate = date2.getTime();
    }
    
	/*public static boolean searchEngineLanguageBelongToContinent(int searchEngineId, int searchLanguageId, String continent) {
		if (CONTINENT_AMERICA.equalsIgnoreCase(continent)) {
			return belongToContinent(AMERICA_ENGINE_LANGUAGES, searchEngineId, searchLanguageId);
		} else if (CONTINENT_ASIA.equalsIgnoreCase(continent)) {
			return belongToContinent(ASIA_ENGINE_LANGUAGES, searchEngineId, searchLanguageId);
		} else if (CONTINENT_EUROPE.equalsIgnoreCase(continent)) {
			return belongToContinent(EUROPE_ENGINE_LANGUAGES, searchEngineId, searchLanguageId);
		}
		return false;
	}
	private static boolean belongToContinent(int[][] engineLanguages, int searchEngineId, int searchLanguageId) {
		if (engineLanguages == null) {
			return false;
		}		
		for (int[] engineLanguage : engineLanguages) {
			if (engineLanguage[0] == searchEngineId && engineLanguage[1] == searchLanguageId) {
				return true;
			}
		}	
		return false;
	}*/

	// keyword daily summary filter - begins
//	public static final String FILTER_TABLE_NAME_PREFIX = "mgd_keyword_daily_summary_";
//	public static final int FILTER_PERIOD_INDICATOR_YESTERDAY = 1;
//	public static final int FILTER_PERIOD_INDICATOR_DAY_BEFORE_YESTERDAY = 2;
//	public static final int FILTER_PERIOD_INDICATOR_LAST_WEEK = 3;
//	public static final int FILTER_PERIOD_INDICATOR_LAST_MONTH = 4;
//	public static final BigDecimal DECIMAL_100 = new BigDecimal(100);
//	public static final int FILTER_GROUP_BY_FUNCTION_INCREASE_MEANS_LARGER_VALUE = 1;
//	public static final int FILTER_GROUP_BY_FUNCTION_INCREASE_MEANS_SMALLER_VALUE = 2;
//	public static final String FILTER_COLUMN_NAME_ESTIMATED_ENTRANCE = "estimate_entrance";
//	public static final String FILTER_COLUMN_NAME_HIGHEST_TRUE_RANK = "highest_true_rank";
//	public static final String FILTER_COLUMN_NAME_HIGHEST_WEB_RANK = "highest_web_rank";
//	public static final String FILTER_COLUMN_NAME_AVERAGE_SEARCH_VOLUME = "avg_search_volume";
//	public static final String FILTER_GROUP_BY_COLUMN_NAME_KEYWORD_ID = " keyword_id ";
//	public static final String FILTER_PERIOD_YESTERDAY_DESC = "Yesterday";
//	public static final String FILTER_PERIOD_DAY_BEFORE_YESTERDAY_DESC = "Day Before Yesterday";
//	public static final String FILTER_PERIOD_LAST_WEEK_DESC = "Last Week";
//	public static final String FILTER_PERIOD_LAST_MONTH_DESC = "Last Month";
//	public static final int FILTER_CRITERIA_VERSION_KEYWORD_DAILY_SUMMARY = 2;
//	public static final int KEYWORD_DAILY_SUMMARY_FILTER_VERSION = 2;
//	public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
//	public static final String DATE_FORMAT_YYYY_MM_DD_HHMMSS = "yyyy-MM-dd HH:mm:ss";
//	public static final String DATE_FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
//	public static final String EMPTY_STRING = " ";
//	public static final String PERCENT_TWO_ZERO = "%20";
	public static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
//	public static final String DATE_FORMAT_YYYYMM = "yyyyMM";
//	public static final String NULL_STRING = "";
//	public static final String APOS = "'";
//	public static final String PERCENT_SIGN = "%";
//	public static final String DATE_FORMAT_YYYY_MM_DD_E = "yyyy-MM-dd (E.)";
//	public static final String DATE_FORMAT_DD = "dd";
	// keyword daily summary filter - ends
	
	// rank type
//	public static final int NORMAL_KEYWORD_RANK = 0;
//	public static final int MOBILE_KEYWORD_RANK = 1;
//
//	public static final String MEDIUM_ORGANIC_MOBILE = "organic-mobile";
//
//	public static final String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_SOCIAL = "CLOUD_CRAWL_URLS_DAILY_SOCIAL_";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_SOCIAL_STOP_CRAWL = "CLOUD_CRAWL_URLS_DAILY_SOCIAL_STOP_CRAWL";
//
//	public static final String QUEUE_NAME_TEST_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES = "TEST_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_TEST_CRAWL_URLS_DAILY_HTML_LANGUAGES = "TEST_CRAWL_URLS_DAILY_HTML_LANGUAGES";
//	public static final String QUEUE_NAME_TEST_CRAWL_URLS_DAILY_HTML = "TEST_CRAWL_URLS_DAILY_HTML_";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_URLS_DAILY_SOCIAL = "TEST_CLOUD_CRAWL_URLS_DAILY_SOCIAL_";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_URLS_DAILY_SOCIAL_QUEUE_NAMES = "TEST_CLOUD_CRAWL_URLS_DAILY_SOCIAL_QUEUE_NAMES";
//
//	// for weekly associated URLs crawl
//	public static final String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "CLOUD_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CRAWL_ASSOCIATED_URLS_HTML = "CRAWL_ASSOCIATED_URLS_HTML_";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_HTML = "CLOUD_CRAWL_ASSOCIATED_URLS_HTML_";
//	public static final String QUEUE_NAME_CRAWL_ASSOCIATED_URLS_SOCIAL = "CRAWL_ASSOCIATED_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL = "CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_STOP_CRAWL = "CLOUD_CRAWL_ASSOCIATED_URLS_HTML_STOP_CRAWL";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL = "CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL";
//
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_";
//	public static final String QUEUE_NAME_TEST_CRAWL_ASSOCIATED_URLS_HTML = "TEST_CRAWL_ASSOCIATED_URLS_HTML_";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_TEST_CRAWL_ASSOCIATED_URLS_SOCIAL = "TEST_CRAWL_ASSOCIATED_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_STOP_CRAWL = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_STOP_CRAWL";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_TEST_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "TEST_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
//	public static final String QUEUE_NAME_TEST_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "TEST_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
//
//	public static final String HTTP_COLON_SLASH_SLASH = "http://";

//	public static final String ID = "id";
////	public static final String NORMALIZED_URL_HASH_CD = "normalized_url_hash_cd";
//	public static final String TRACK_DATE = "track_date";
//	public static final String COLON = ":";
//	public static final String FB_SHARE = "fb_share";
//	public static final String FB_LIKE = "fb_like";
//	public static final String FB_COMMENT = "fb_comment";
//	public static final String FB_TOTAL = "fb_total";
//	public static final String FB_CLICK = "fb_click";
//	public static final String TW_TWEETS = "tw_tweets";
//	public static final String GOOGLE_PLUS = "google_plus";
//	public static final String PINTEREST_COUNT = "pinterest_count";
//	public static final String LINKEDIN_COUNT = "linkedIn_count";
//	public static final String RESPONSE_CODE = "response_code";
////	public static final String SOLR_CONNECTION_TEST_HASH_CODE = "abcde";
////	public static final String SOLR_CONNECTION_TEST_DOC_ID = "1";
//	public static final String TITLE = "title";
//	public static final String META_DESC = "meta_desc";
//	public static final String H1 = "h1";
//	public static final String H2 = "h2";
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_CONNECTION_TEST = new String[] { ID };
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_SOCIAL_SUBSET_1 = new String[] { TRACK_DATE, FB_SHARE, FB_LIKE, FB_COMMENT, FB_TOTAL,
//			FB_CLICK, TW_TWEETS, GOOGLE_PLUS, PINTEREST_COUNT, LINKEDIN_COUNT };
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_LINKEDIN_COUNT = new String[] { LINKEDIN_COUNT };
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_1 = new String[] { TRACK_DATE, RESPONSE_CODE };
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_2 = new String[] { TRACK_DATE, RESPONSE_CODE, TITLE, META_DESC};
//	public static final String SOLR_CORE_NAME_SOCIAL = "social";
//	public static final String COMMA = ",";
//	public static final String URL = "url";
//	public static final String NORMALIZED_URL = "normalized_url";
	public static final String MAJESTIC_INDEX_ITEM_INFO = "majestic_index_item_info";
//	public static final String AND_TRACK_DATE_ASTERISK_TO = " AND track_date:[* TO ";
//	public static final String CLOSE_BRACKET = "]";
	public static final int MAX_SOLR_RETRY_COUNT = 5;
	public static final String DATE_FORMAT_YYYYWW = "YYYYww";
//	public static final String WEEKLY = "weekly";
//	public static final String DAILY = "daily";
//	public static final String SOLR_SELECTION_CRITERON_ALL = "*:*";
//	public static final String SOLR_CORE_NAME_ASSOCIATED_URLS_HTML = "associated_urls_html";
//	public static final String NO = "No";
//	public static final String ZERO = "0";
//
//	// for daily associated URLs crawl
//	public static final String QUEUE_NAME_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CRAWL_DAILY_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "CRAWL_DAILY_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
//	public static final String QUEUE_NAME_CRAWL_DAILY_ASSOCIATED_URLS_HTML = "CRAWL_DAILY_ASSOCIATED_URLS_HTML_";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML = "CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_";
//	public static final String QUEUE_NAME_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL = "CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL = "CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_STOP_CRAWL = "CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_STOP_CRAWL";
//	public static final String QUEUE_NAME_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL = "CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL";
//
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML = "TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_";
//	public static final String QUEUE_NAME_TEST_CRAWL_DAILY_ASSOCIATED_URLS_HTML = "TEST_CRAWL_DAILY_ASSOCIATED_URLS_HTML_";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL = "TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_TEST_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL = "TEST_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_STOP_CRAWL = "TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_STOP_CRAWL";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL = "TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_TEST_CRAWL_DAILY_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "TEST_CRAWL_DAILY_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
//	public static final String QUEUE_NAME_TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "TEST_CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
//	public static final String QUEUE_NAME_TEST_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "TEST_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
//
//	public static final String DATE_FORMAT_MMDD = "MMdd";
//	public static final String LOGGLY_TAG_POLITE_CRAWL = "politecrawl";
//	public static final String SUBSERVER_SOLR_CONNECTION_URL = "subserver.solr.connection.url";
//	public static final String SUBSERVER_SOLR_CONNECTION_URL_READONLY = "subserver.solr.connection.url.read.only";
//	public static final Integer INTEGER_ZERO = new Integer(0);
//	public static final BigDecimal DECIMAL_ZERO = new BigDecimal(0);
//
//	public static final String DISAVOWED_BACKLINKS_UPLOAD_FOLDER = "subserver.disavowed.backlinks.upload.folder";
//	public static final String USER_BACKLINKS_UPLOAD_FOLDER = "subserver.user.backlinks.upload.folder";
//	public static final String MAJESTIC_BACKLINKS_UPLOAD_FOLDER = "subserver.majestic.backlinks.upload.folder";
//	public static final String META_KEYWORDS = "meta_keywords";
//	public static final String CANONICAL_URL = "canonical_url";
//	public static final String CONTENT = "content";
//	public static final String DIV_ID_UNDERSCORE = "div_id_";
//	public static final String DIV_ID_TEXT_UNDERSCORE = "div_id_text_";
//	public static final String DIV_CLASS_UNDERSCORE = "div_class_";
//	public static final String DIV_CLASS_TEXT_UNDERSCORE = "div_class_text_";
//	public static final String WEEK_OF_YEAR = "week_of_year";
//	public static final String MD5 = "MD5";
//	public static final String STRING_ZERO = "0";
//	public static final String HASH_CODES = "hash_codes";
	public static final String JA = "ja";
//	public static final String TIME_FORMAT_HH_MM_SS = "HH:mm:ss";
//	public static final String[] SOCIAL_CRAWL_SOLR_OUTPUT_FIELD_NAME_ARRAY = new String[] { TRACK_DATE, FB_SHARE, FB_LIKE, FB_COMMENT, FB_TOTAL, FB_CLICK,
//			TW_TWEETS, GOOGLE_PLUS, PINTEREST_COUNT, LINKEDIN_COUNT, MAJESTIC_INDEX_ITEM_INFO, WEEK_OF_YEAR };
//
//	// for tracking additional content
//	public static final String DOMAIN_ID = "domain_id";
//	public static final String PATTERN = "pattern";
//	public static final String ATTRIBUTE = "attribute";
//	public static final String DISPLAY_NAME = "display_name";
//	public static final int GENESIS_TRAFFIC_TYPE_NON_MOBILE = 1;
//	public static final int GENESIS_TRAFFIC_TYPE_MOBILE = 2;
//	public static final String TITLE_JA = "title_ja";
//	public static final String META_KEYWORDS_JA = "meta_keywords_ja";
//	public static final String META_DESC_JA = "meta_desc_ja";
//	public static final String CONTENT_JA = "content_ja";
//	public static final String H1_JA = "h1_ja";
//	public static final String H2_JA = "h2_ja";
//	public static final String HTTPS_COLON_SLASH_SLASH = "https://";
//	public static final String SLASH_POUND_SLASH = "/#/";
//	public static final String SLASH = "/";
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_4 = new String[] { TITLE, META_KEYWORDS, META_DESC, H1, H2 };
//	public static final String INTERNAL_LINKS = "internal_links";
//	public static final String OUTBOUND_LINKS = "outbound_links";
//	public static final String PAGE_RANK = "page_rank";
//	public static final String PAGE_AUTHORITY = "page_authority";
//	public static final String SEOMOZ_ACESS_ID = "seoMozAccessId";
//	public static final String SEOMOZ_SECRET_KEY = "seoMozSecretKey";
//	public static final String FLOAT_FORMAT_ONE_DECIMAL_PLACE = "%.01f";
//	public static final int RESP_CODE_200 = 200;
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_6 = new String[] { ID, RESPONSE_CODE, PAGE_RANK };
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_7 = new String[] { RESPONSE_CODE, INTERNAL_LINKS,
//		OUTBOUND_LINKS, TITLE};
//	public static final String UTF_8 = "utf-8";
//
//	public static final int BACKLINK_SRC_IND_MAJESTIC = 0;
//	public static final int BACKLINK_SRC_IND_USER = 1;
//	public static final int BACKLINK_SRC_IND_GOOGLE_WEBMASTER_TOOLS = 2;
//	public static final int BACKLINK_SRC_IND_AHREFS = 4;
//	public static final String BACKLINK_SRC_DESC_UNKNOWN = "-";
//	public static final String BACKLINK_SRC_DESC_MAJESTIC = "Majestic";
//	public static final String BACKLINK_SRC_DESC_USER = "User";
//	public static final String BACKLINK_SRC_DESC_GOOGLE_WEBMASTER_TOOLS = "GWT";
//	public static final String BACKLINK_SRC_DESC_AHREFS = "Ahrefs";

//	public static final String META_ROBOTS = "meta_robots";
//	public static final String PATTERN_COLON = "Pattern:";
//	public static final String ATTRIBUTE_COLON = ", Attribute:";
//	public static final String DISPLAY_NAME_COLON = ", Display Name:";
//	public static final String DIV_ID_COLON = "DIV ID:";
//	public static final String DIV_CLASS_COLON = "DIV Class:";
//	public static final String TRUE = "true";
//	public static final String FALSE = "false";
//	public static final String NOT_AVAILABLE = "n/a";
//	public static final String EXACT_MATCH = "Exact";
//	public static final String PARTIAL_MATCH = "Partial";
//	public static final String NO_MATCH = "No";
//	public static final String OPEN_PARENTHESES = "(";
//	public static final String CLOSE_PARENTHESES = ")";
//
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_5 = new String[] { ID, TRACK_DATE, RESPONSE_CODE, INTERNAL_LINKS,
//		OUTBOUND_LINKS, PAGE_RANK, PAGE_AUTHORITY, TITLE, META_KEYWORDS, META_DESC, H1, H2, META_ROBOTS };
//
//	// when the search volume SOLR document does not have a "last_update_timestamp_s" field,
//	// it was converted from Jeremy's historical data
//	//public static final int SEARCH_VOLUME_SRC_IND_JEREMY_HISTORICAL = 1;
//	public static final int SEARCH_VOLUME_SRC_IND_JEREMY_DAILY = 2;
//	public static final int SEARCH_VOLUME_SRC_IND_ADWORDS_HISTORICAL = 3;
//	public static final int SEARCH_VOLUME_SRC_IND_ADWORDS_DAILY = 4;
//
//	public static final String SOLR_FIELD_KEYWORD_STRING = "keyword_string";
//	public static final String SOLR_FIELD_KEYWORD = "keyword";
//	public static final String SOLR_FIELD_COMPETETION = "competetion";
//	public static final String SOLR_FIELD_LOCAL_MONTHLY_SEARCH = "local_monthly_search";
//	public static final String SOLR_FIELD_CPC = "cpc";
//	public static final String SOLR_FIELD_MONTH_1 = "month_1";
//	public static final String SOLR_FIELD_MONTH_2 = "month_2";
//	public static final String SOLR_FIELD_MONTH_3 = "month_3";
//	public static final String SOLR_FIELD_MONTH_4 = "month_4";
//	public static final String SOLR_FIELD_MONTH_5 = "month_5";
//	public static final String SOLR_FIELD_MONTH_6 = "month_6";
//	public static final String SOLR_FIELD_MONTH_7 = "month_7";
//	public static final String SOLR_FIELD_MONTH_8 = "month_8";
//	public static final String SOLR_FIELD_MONTH_9 = "month_9";
//	public static final String SOLR_FIELD_MONTH_10 = "month_10";
//	public static final String SOLR_FIELD_MONTH_11 = "month_11";
//	public static final String SOLR_FIELD_MONTH_12 = "month_12";
//	public static final String SOLR_FIELD_ESTIMATED_CLICKS = "Estimated_clicks";
//	public static final String SOLR_FIELD_ESTIMATED_IMPRESSIONS = "Estimated_Impressions";
//	public static final String SOLR_FIELD_ESTIMATED_COST = "Estimated_Cost";
//	public static final String SOLR_FIELD_ESTIMATED_CTR = "Estimated_CTR";
//	public static final String SOLR_FIELD_ESTIMATED_AVERAGE_CPC = "Estimated_Average_CPC";
//	public static final String SOLR_FIELD_ESTIMATED_AVERAGE_POSITION = "Estimated_Average_Position";
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_3 = new String[] { TRACK_DATE, RESPONSE_CODE, TITLE, META_KEYWORDS, META_DESC, H1, H2, META_ROBOTS, CANONICAL_URL};
//	public static final String FILE_EXTENSION_TXT = ".txt";
//	public static final String REDIRECT_FINAL_URL = "redirect_final_url";
//	public static final String REDIRECT_FINAL_URL_RESP_CODE= "redirect_final_url_resp_code";
//	public static final String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_8 = new String[] { TRACK_DATE, RESPONSE_CODE, H1, H2, REDIRECT_FINAL_URL, REDIRECT_FINAL_URL_RESP_CODE};
//	public static final int SITE_CRAWL_SOLR_SERVERS_CUTOFF_DATE_NUMBER = 20160626;
//	public static final int SEND_ALERT_IND_TRUE = 1;
//	public static final int GROUP_TAG_FILTER_IND_TRUE = 1;
//	public static final String FILTER_CRITERIA_HIGHEST_TRUE_RANK = "Highest True Rank";
//	public static final String FILTER_CRITERIA_HIGHEST_WEB_RANK = "Highest Web Rank";
	
	public static final String ESCAPED_ENCODED_META_CHAR = "\\\\";
	public static final String META_CHAR_BACKSLASH = "\\";
	public static final String META_CHAR_CARET = "^";
	public static final String META_CHAR_DOLLAR_SIGN = "$";
	public static final String META_CHAR_DOT = ".";
	public static final String META_CHAR_PIPE = "|";
	public static final String META_CHAR_QUESTION_MARK = "?";
	public static final String META_CHAR_ASTERISK = "*";
	public static final String META_CHAR_PLUS_SIGN = "+";
	public static final String META_CHAR_OPENING_PARENTHESIS = "(";
	public static final String META_CHAR_CLOSING_PARENTHESIS = ")";
	public static final String META_CHAR_OPENING_SQUARE_BRACKET = "[";
	public static final String META_CHAR_OPENING_CURLY_BRACE = "{";
//	public static final String SQL_QUERY_REGULAR_EXPRESSION_BEGIN_WITH = "^";
//	public static final Integer FALSE_NUMERIC = 0;
//	public static final Integer TRUE_NUMERIC = 1;

	public static final char[] NON_STANDARD_SYMBOLS_SEARCH_VOLUME = new char[]{',', '!', '@', '%', '^', '(', ')', '=', '{', '}', ';', '~', '`', '<', '>', '?', '\\', '|'};
    public static final char[] ILLEGAL_CHARACTERS = new char[]{
            '¡', '¢', '£', '¤', '¥', '¦', '§', '¨', '©', 'ª', '«', '¬', '®', '¯', '–', '—',
            '‘', '’', '‚', '“', '”', '„', '†', '‡', '•', '…', '‰', '€', '™', '°', '±',
            '²', '³', '´', 'µ', '¶', '·', '¸', '¹', 'º', '»', '¼', '½', '¾', '¿', '�', '×'};

	public static final String DEV_TEAM_EMAIL = "<EMAIL>";

}
