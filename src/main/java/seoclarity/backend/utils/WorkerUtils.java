package seoclarity.backend.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import org.apache.commons.lang.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@CommonsLog
public class WorkerUtils {

    public static final String WORKER_BASE_URL = "https://event.seoclarity.workers.dev/";
    public static final String CACHE_LIST_URL = "https://event.seoclarity.workers.dev/list/";
    public static final String VALUE_URL = "https://event.seoclarity.workers.dev/key/";
    public static final String DELETE_CACHE_URL_PREFIX = "https://event.seoclarity.workers.dev/delete/";
    public static final String KEY_SECURITY_HEADER = "seoclarity-internal-token";
    public static final String VALUE_SECURITY_HEADER = "6603b708dd0bf24b0e7a1e68408c454e";

    public static String doGet(String url, String headerKey, String headerValue) throws Exception{
        String resultString = "";
        Response response = null;
        try {
            log.info("==get url:" + url);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .method("GET", null)
                    .addHeader(headerKey, headerValue)
                    .build();
            response = client.newCall(request).execute();
            if(!response.isSuccessful()){
                throw new IOException("Unexcepted code:" + response);
            }
            resultString = response.body().string();
            log.info("====response:" + response);
            return resultString;
        }catch (Exception e){
            log.info("=====Failed doGet:" + url);
            e.printStackTrace();
            throw e;
        }finally {
            if(response != null){
                response.body().close();
            }
        }

    }

    public static String doDelete(String urlToRead, String headerKey, String headerValue) throws Exception {
        String resultString = "";
        Response response = null;
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("text/plain");
            RequestBody body = RequestBody.create(mediaType, "");
            Request request = new Request.Builder()
                    .url(urlToRead)
                    .method("DELETE", body)
                    .addHeader(headerKey, headerValue)
                    .build();
            response = client.newCall(request).execute();
            if(!response.isSuccessful()){
                throw new IOException("Unexcepted code:" + response);
            }
            resultString = response.body().string();
            System.out.println("====response:" + response);
            return resultString;
        }catch (Exception e){
            System.out.println("=====Failed doDelete:" + urlToRead);
            e.printStackTrace();
            throw e;
        }finally {
            if(response != null){
                response.body().close();
            }
        }
    }


    public static void syncEventKeyToWorkers(String workerUrl, String key, String value){
        int tryCnt = 3;
        CloseableHttpResponse execute;
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager();
        manager.setMaxTotal(100);
        manager.setDefaultMaxPerRoute(30);
        CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(manager).build();
        while (tryCnt > 0) {
            try {
                HttpPost httpPost = new HttpPost(workerUrl);
                //data
                List<NameValuePair> nameValuePairList = new ArrayList<>();
                NameValuePair nameValuePair1 = new BasicNameValuePair("key", key);
                NameValuePair nameValuePair2 = new BasicNameValuePair("value", value);
                nameValuePairList.add(nameValuePair1);
                nameValuePairList.add(nameValuePair2);
                UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(nameValuePairList, "utf-8");
                httpPost.setEntity(urlEncodedFormEntity);
                RequestConfig requestConfig = RequestConfig
                        .custom()
                        .setSocketTimeout(10 * 1000)
                        .setConnectTimeout(10 * 1000)
                        .setConnectionRequestTimeout(10 * 1000)
                        .build();
                httpPost.setConfig(requestConfig);
                httpPost.setHeader(KEY_SECURITY_HEADER, VALUE_SECURITY_HEADER);
                execute = httpClient.execute(httpPost);

                if (execute.getStatusLine().getStatusCode() == 200) {
                    String msg = "send data(key: "+ key +", value: "+ value +") to workers success!";
                    System.out.println(msg);
                    break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (httpClient != null) {
                    try {
                        httpClient.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            String msg = "send data(key: "+ key +", value: "+ value +") to workers failed!";
            System.out.println(msg);
            tryCnt --;
        }
    }

    public static List<String> getCacheList(String url) throws Exception {
        if (StringUtils.isBlank(url)) {
            log.error("==urlEmpty");
            return null;
        }
        List<String> resultList = new ArrayList<>();
        log.info("===worker get:" + url);
        String adhocCacheList = WorkerUtils.doGet(url, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        String keys = new Gson().fromJson(adhocCacheList, Map.class).get("keys").toString();
        List<String> nameList = new Gson().fromJson(keys, List.class);
        JSONArray jsonArray = JSONObject.parseArray(JSON.toJSONString(nameList));
        List<Map> mapList = jsonArray.toJavaList(Map.class);
        System.out.println("===mapList :" + JSON.toJSONString(mapList));
        for (Map map : mapList) {
            System.out.println("===name :" + map.get("name"));
            resultList.add(map.get("name").toString());
        }
        return resultList;
    }

    public static String getValueByKey(String url) throws Exception {
        if (StringUtils.isBlank(url)) {
            log.error("===urlEmpty");
            return null;
        }
        List<String> resultList = new ArrayList<>();
        log.info("===worker get:" + url);
        String adhocCacheList = WorkerUtils.doGet(url, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        log.info("value: " + adhocCacheList);
        String value = new Gson().fromJson(adhocCacheList, Map.class).get("value").toString();
        return value;
    }

}
