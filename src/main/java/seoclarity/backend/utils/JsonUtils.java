package seoclarity.backend.utils;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.MissingNode;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.service.JsonMapper;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created on 14/03/2018 17:48
 *
 * <AUTHOR>
 */
public class JsonUtils {

    private JsonUtils() {}

    private static final JsonMapper DEFAULT_MAPPER = new JsonMapper();

    public static final JavaType JSON_LIST_LONG_TYPE = DEFAULT_MAPPER.contructCollectionType(List.class, Long.class);
    public static final JavaType JSON_LIST_INTEGER_TYPE = DEFAULT_MAPPER.contructCollectionType(List.class, Integer.class);
    public static final JavaType JSON_LIST_STRING_TYPE = DEFAULT_MAPPER.contructCollectionType(List.class, String.class);

    public static JsonMapper getMapper() {
        return DEFAULT_MAPPER;
    }

	public static JsonNode get(JsonNode nodeTree, String fieldName) {
		return nodeTree.get(fieldName) != null ? nodeTree.get(fieldName) : MissingNode.getInstance();
	}


    //Cee - https://www.wrike.com/open.htm?id=300904253
	public static Object getJsonMapData(String[] keyPath, Map jsonMap) {
		if (jsonMap == null || ArrayUtils.isEmpty(keyPath)) {
			return null;
		}
		
		Object jsonResult = null;
		Map<String, Object> tmpMap = jsonMap;
		for (String key : keyPath) {
			
			if (jsonResult != null) {
				tmpMap = (Map<String, Object>)jsonResult;
			}
			
			if (tmpMap.containsKey(key)) {
				jsonResult = tmpMap.get(key);
			}
		}
		
		return jsonResult;
	}
	
	//Cee - https://www.wrike.com/open.htm?id=300904253
	public static void setJsonMapData(String[] keyPath, Map jsonMap, String mapKey, Object mapValue) {
		if (jsonMap == null) {
			return;
		}
		
		Map<String, Object> tmpMap = jsonMap;
		if (keyPath != null && keyPath.length > 0) {
			for (String key : keyPath) {
				if (tmpMap.containsKey(key)) {
					tmpMap = (Map<String, Object>)tmpMap.get(key);
				} else {
					tmpMap.put(key, new HashMap<String, Object>());
					tmpMap = (Map<String, Object>)tmpMap.get(key);
				}
			}
		}
		if (tmpMap != null && mapKey != null) {
			tmpMap.put(mapKey, mapValue);
		}
	}
    
}
