package seoclarity.backend.utils;

import lombok.extern.apachecommons.CommonsLog;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @autor ewain
 */
@CommonsLog
public class CollectionSplitUtils {

    public static final int BATCH_MEMORY_LIMIT_SIZE = 20 * 1000;
    public static final int MYSQL_DB_INSERT = 500;
    public static final int MYSQL_DB_BATCH_UPDATE = 500;
    public static final int MYSQL_DB_QUERY_WITH_STRING = 200;
    public static final int MYSQL_DB_QUERY_WITH_HASH = 500;
    public static final int MYSQL_DB_OPERATE_WITH_NUMBER = 500;
    public static final int MONITOR_BATCH_FOR_LOG = 10000;
    public static final int CLARITY_DB_HASH_QUERY = 200;
    public static final int CLARITY_DB_RE_TRY_COUNT = 3;
    /**
     * 集合等量分割
     * 将source等量分割成n个list
     *
     * @param source 源数据
     * @param num    子集合数量
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitCollectionByNumber(List<T> source, int num) {
        List<List<T>> result = new ArrayList<>();
        int remainder = source.size() % num;
        int number = source.size() / num;
        int offset = 0;
        for (int i = 0; i < num; i++) {
            List<T> value;
            if (remainder > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remainder--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }

    /**
     * 集合等量分割
     * 将source按每个集合大小为size等量分割
     *
     * @param source
     * @param size
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitCollectionBySize(List<T> source, int size) {
        List<List<T>> result = new ArrayList<>();
        if (size > source.size()) {
            result.add(source);
        } else {
            int remainder = source.size() % size;
            int num = (source.size() - remainder) / size;
            for (int i = 0; i < num; i++) {
                result.add(source.subList(i * size, (i + 1) * size));
            }
            List<T> ts = source.subList(num * size, num * size + remainder);
            if (ts != null && ts.size() > 0) {
                result.add(ts);
            }
        }
        return result;
    }

    /**
     * 集合等量分割
     * 将source按每个集合大小为size等量分割
     * TODO 并行stream流 需要jdk8支持
     *
     * @param source
     * @param size
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitCollectionBySizeWithStream(List<T> source, int size) {
        int limit = (source.size() + size - 1) / size;
        List<List<T>> result = Stream.iterate(0, n -> n + 1)
                .limit(limit).parallel()
                .map(a -> source.stream().skip(a * size).limit(size).parallel().collect(Collectors.toList()))
                .collect(Collectors.toList());
        return result;
    }

    /**
     * list转map,键为list元素某个属性,值为全部包含此属性的元素list
     *
     * @param list          源数据
     * @param keyMethodName 获取键的方法
     * @param clazz         类名
     * @param <K>           键泛型
     * @param <V>           元数据泛型
     * @return
     */
    public static <K, V> Map<K, List<V>> listToMapGroupByKeyMethod(List<V> list, String keyMethodName, Class<V> clazz) {
        Map<K, List<V>> map = new HashMap<>();
        if (list != null) {
            try {
                Method keyMethod = clazz.getMethod(keyMethodName);
                K key;
                for (V item : list) {
                    key = (K) keyMethod.invoke(item);
                    if (map.containsKey(key)) {
                        map.get(key).add(item);
                    } else {
                        List<V> childList = new ArrayList<>();
                        childList.add(item);
                        map.put(key, childList);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return map;
    }

    /**
     * list转map,键为list元素的某一属性,值为元素本身
     *
     * @param list          源数据
     * @param keyMethodName 获取键的方法
     * @param clazz         类名
     * @param <K>           键泛型
     * @param <V>           元数据泛型
     * @return
     */
    public static <K, V> Map<K, V> listToMapByKeyMethod(List<V> list, String keyMethodName, Class<V> clazz) {
        Map<K, V> map = new HashMap<>();
        if (list != null) {
            try {
                Method keyMethod = clazz.getMethod(keyMethodName);
                for (V item : list) {
                    map.put((K) keyMethod.invoke(item), item);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return map;
    }

    /**
     * 提取集合中的元素的某一属性构建新的集合
     * @param list          源数据
     * @param keyMethodName 获取键的方法
     * @param clazz         类名
     * @param <K>           键泛型
     * @param <V>           元数据泛型
     * @return
     */
    public static <K, V> List<K> collectListByValueMethod(List<V> list, String keyMethodName, Class<V> clazz, Class<K> kClass) {
        List<K> result = new ArrayList<K>();
        if (list != null) {
            try {
                Method keyMethod = clazz.getMethod(keyMethodName);
                for (V item : list) {
                    result.add((K) keyMethod.invoke(item));
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return result;
    }


    /**
     * 类名
     * example:
     *      com.actonia.subserver.utils.CollectionSplitUtils
     * 如果存在内部类主类和内部类之间用 $ 分割 CollectionSplitUtils$innerClassName
     * @return class name,
     */
    public static String getClassName() {
        StackTraceElement[] stackInfo = Thread.currentThread().getStackTrace();
        return stackInfo[2].getClassName();
    }

    /**
     * 方法名
     * example:
     *      getMethodName
     * @return method name
     */
    public static String getMethodName() {
        StackTraceElement[] stackInfo = Thread.currentThread().getStackTrace();
        return stackInfo[2].getMethodName();
    }

    /**
     * 文件名
     * example:
     *      CollectionSplitUtils.java
     * @return filename
     */
    public static String getFileName() {
        StackTraceElement[] stackInfo = Thread.currentThread().getStackTrace();
        return stackInfo[2].getFileName();
    }

    /**
     * 行数
     * example:
     *      100
     * @return
     */
    public static int getLineNumber() {
        StackTraceElement[] stackInfo = Thread.currentThread().getStackTrace();
        return stackInfo[2].getLineNumber();
    }


    public static String getErrorMsg(Exception e) {
        String msg;
        try {
            msg = e.toString() + "=>className" + e.getStackTrace()[0].getClassName() + ", lineNum:" + e.getStackTrace()[0].getLineNumber();
        } catch (Exception exception) {
            msg = "known error, please check!!!";
        }
        return msg;
    }
}
