package seoclarity.backend.utils.keywordTokenizer;

import java.io.IOException;
import java.io.StringReader;

import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.cn.smart.SmartChineseAnalyzer;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.JiebaSegmenter.SegMode;

/**
 * <AUTHOR>
 * @date 2019-05-05
 * @path seoclarity.backend.utils.keywordTokenizer.Test
 * 
 */
public class Test {

	public static String Seg(String sentence) throws IOException {
		long a = System.currentTimeMillis();
		String text = "";
		Analyzer anal = new SmartChineseAnalyzer(true);
		
		String str = ZhConverterUtil.convertToSimple(sentence);
		boolean isSimple = str.equals(sentence);
		if (!isSimple) {
			sentence = str;
		}
		
		
		
		StringReader reader = new StringReader(sentence);
		TokenStream ts = anal.tokenStream("", reader);
		CharTermAttribute term = ts.getAttribute(CharTermAttribute.class);
		
		ts.reset();
		
		// 遍历分词数据
		while (ts.incrementToken()) {
			String s = term.toString();
			if(!isSimple) {
				String temp = ZhConverterUtil.convertToTraditional(s);
				s = temp;
			}
			text += s + "|";
		}
		ts.end();
		ts.close();
//		System.out.println("cost:" + (System.currentTimeMillis() - a));
		return text.trim() + "\n";
	}
	
	public static String Seg2(String str) {
		JiebaSegmenter jieba = new JiebaSegmenter();
		return  jieba.process(str, SegMode.SEARCH).toString();
	}
 
	public static void main(String[] args) throws IOException {
		String[] strArr =  new String[] {
				"外匯期貨契約",
				"cme nymex light sweet crude oil futures contract",
				"胜利 英文",
				"胜利 韩国",
				"胜利大逃亡",
				"胜利之吻",
				"胜利之光",
				"胜利精密",
				"胜利",
		};
		
		for (String str : strArr) {
			System.out.println("strArr:" + str);
			System.out.println("ik:" + Seg(str));
			System.out.println("jieba:" + Seg2(str));
			System.out.println("============");
		}
	}

}
