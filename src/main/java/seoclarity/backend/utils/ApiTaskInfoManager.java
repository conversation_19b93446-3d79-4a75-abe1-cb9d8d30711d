package seoclarity.backend.utils;


import org.joda.time.DateTime;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ApiTaskInfoEntityDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ApiTaskInfoManager {
    private static ApiTaskInfoEntityDAO apiTaskInfoEntityDAO;
    private static List< ApiTaskInfoEntity> apiTaskInfos=null;
    private static DateTime apiTaskInfoMapCacheDateTime=null;
    static
    {
        apiTaskInfoEntityDAO = SpringBeanFactory.getBean("apiTaskInfoEntityDAO");
    }
   public static List< ApiTaskInfoEntity> getApiTaskInfos(List<String> taskGroups)
    {
        if (apiTaskInfos==null)
        {
            refreshCache();
        }
     if(DateTime.now().plusHours(-3).compareTo(apiTaskInfoMapCacheDateTime)>0)
     {
         refreshCache();
     }

     return  apiTaskInfos.stream().filter(apiTaskInfo->taskGroups.stream().anyMatch(taskGroup->taskGroup.equals( apiTaskInfo.getTaskGroup()+"")) ).collect(Collectors.toList());
    }
    private static void refreshCache()
    {
        System.out.println("###ApiTaskInfoManager:refreshCache()");
        apiTaskInfos= apiTaskInfoEntityDAO.getApiTaskInfos();
        if(apiTaskInfos==null) {
            apiTaskInfos = new ArrayList<>();
        }
        System.out.println("####apiTaskInfoCache Created:"+apiTaskInfos.size());
        apiTaskInfoMapCacheDateTime=DateTime.now();
    }
}
