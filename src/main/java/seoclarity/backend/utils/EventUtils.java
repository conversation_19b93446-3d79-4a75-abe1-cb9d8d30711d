package seoclarity.backend.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.gson.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

public class EventUtils {
    private static final int MAX_LIST_SIZE = 1000;
    private static final int GET_TIME_OUT = 10 * 1000;
    private static final String END_POINT = "https://event.seoclarity.workers.dev";//https://event.seoclarity.workers.dev
    private static final String LIST_BY_KEY_WITH_CURSOR = "/cursor/{cursor}?{prefix}";
    private static final String GET_BY_KEY = "/key/{key_id}";
    private static final String LIST_BY_KEY = "/list/{prefix}";
    private static final String DELETE_BY_KEY = "/delete/{key_id}";

    private static final String HEADER_TOKEN_KEY = "seoclarity-internal-token";
    private static final String HEADER_TOKEN_VALUE = "6603b708dd0bf24b0e7a1e68408c454e";

    private static final Map<String, String> CACHE_HEADER_MAP = new HashMap<>();
    static {
        CACHE_HEADER_MAP.put(HEADER_TOKEN_KEY, HEADER_TOKEN_VALUE);
    }
    /*
    key: seoclarity-internal-token
value: 6603b708dd0bf24b0e7a1e68408c454e
    * GET
/task/{task_id}. Retrieve task body data by task_id
/list/
/list/  get top 1000 tasks.
/list/{prefix} get top 1000 keys by prefix
POST
use form-data
task_id : required,task_id
task_body: required , task_body
expirationTtl : optional , delete task info after certain amount of seconds, the value must greater than 60s. default value is 7 days, in most of cases we should NOT use this parameter.
DELETE
/delete/{task_id}
    * */
    private static String parseCacheResponseVal(String response) {
        try {
            if (StringUtils.isNotBlank(response)) {
                JsonParser parser = new JsonParser();
                JsonObject obj = parser.parse(response).getAsJsonObject();
                if (obj.has("value") && !obj.get("value").isJsonNull()) {
                    String str = obj.get("value").getAsString();
                    return StringUtils.isNotBlank(str) && !StringUtils.equalsIgnoreCase(str, "null") ? str : null;
                }
            }
        } catch (Exception e) {
            System.out.println("===parse cache response value failed. response:" + response);
            e.printStackTrace();
        }
        return null;
    }

    public static String  get(byte[] key) {
        String keyStr = new String(key);
        String url = END_POINT + StringUtils.replace(GET_BY_KEY, "{key_id}", keyStr);
        // System.out.println("=====get cache key:" + url);
        return parseCacheResponseVal(HttpRequestUtils.simpleGet(url, null, GET_TIME_OUT, CACHE_HEADER_MAP));
    }

    public static Map<String, String> getMapValue(byte[] key) {
        String keyStr = new String(key);
        String url = END_POINT + StringUtils.replace(GET_BY_KEY, "{key_id}", keyStr);
        String str = HttpRequestUtils.simpleGet(url, null, GET_TIME_OUT, CACHE_HEADER_MAP);

        System.out.println("===cache url:" + url + ", response:" + str);

        if (StringUtils.isNotBlank(str)) {
            try {
                return JSONObject.parseObject(str, new TypeReference<Map<String, String>>(){});
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static void setValue(byte[] key, String value) {
        setValue(key, value, 0);
    }

    public static void setValue(byte[] key, String value, int expireTimeSec) {
        // String keyStr = Hex.encodeHexString(key);
        String keyStr = new String(key);
        Map<String, String> map = new HashMap<>();
        map.put("key", keyStr);
        map.put("value", value);
        if (expireTimeSec > 0) {
            map.put("expirationTtl", String.valueOf(expireTimeSec));
        }
        try {
            String response = HttpRequestUtils.simplePost(END_POINT, map, GET_TIME_OUT, CACHE_HEADER_MAP);
            System.out.println("===setValue cache keyStr:" + keyStr + ", response:" + response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public  static int deleteKey(String key) {
        String url = END_POINT + StringUtils.replace(DELETE_BY_KEY, "{key_id}", key);
        String response = null;
        try {
            response = HttpRequestUtils.simpleDelete(url, GET_TIME_OUT, CACHE_HEADER_MAP);
        } catch (Exception e) {
            e.printStackTrace();
        }
        boolean icComplete = StringUtils.isNotBlank(response) && StringUtils.containsIgnoreCase(response, "cache has been purged");
        System.out.println("=delete key:" + key + ", icComplete:" + icComplete);
        return  icComplete ? 1 : 0;
    }

    public static int deleteAll(String prefix, String...patterns) {
        List<String> keyList = listKeys(prefix, true, patterns);
        System.out.println("===delete keys prefix:" + prefix + ", delete size:" + keyList.size());
        return keyList.size();
    }

    public static byte[] byteMergeAll(byte[]... values) {
        int lengthByte = 0;
        for (int i = 0; i < values.length; i++) {
            lengthByte += values[i].length;
        }
        byte[] allByte = new byte[lengthByte];
        int countLength = 0;
        for (int i = 0; i < values.length; i++) {
            byte[] b = values[i];
            System.arraycopy(b, 0, allByte, countLength, b.length);
            countLength += b.length;
        }
        return allByte;
    }


    private static ListResponseVO parseListResponse(String response) {
        ListResponseVO vo = new ListResponseVO();
        try {
            if (StringUtils.isNotBlank(response)) {
                List<String> keyList = new ArrayList<>(0);
                Map<String, String> metaMap = new HashMap<>();


                JsonParser parser = new JsonParser();
                JsonObject obj = parser.parse(response).getAsJsonObject();
                JsonArray array = obj.getAsJsonArray("keys");

                if (array != null) {
                    for (int i = 0; i < array.size(); i++) {
                        JsonObject row = array.get(i).getAsJsonObject();
                        // key name
                        String name = row.get("name").getAsString();
                        keyList.add(name);
                        // meta
                        if (row.has("metadata") && !row.get("metadata").isJsonNull()) {
                            JsonObject meta = row.get("metadata").getAsJsonObject();
                            String metaValue = meta == null ? null : meta.get("id").getAsString();
                            metaMap.put(name, metaValue);
                        }
                    }
                }
                boolean listComplete = !obj.has("list_complete") || obj.get("list_complete").getAsBoolean();
                String cursor = obj.has("cursor") ? obj.get("cursor").getAsString() : null;
                vo.setKeyList(keyList);
                vo.setMetaMap(metaMap);
                vo.setList_complete(listComplete);
                vo.setCursor(cursor);
            }
        } catch (Exception e) {
            System.out.println("===parse response failed, response:" + response);
            e.printStackTrace();
        }
        return vo;
    }

    private static ListResponseVO listKeysWithCursor(String prefix, String lastCursor) {
        if (StringUtils.isBlank(lastCursor)) {
            return null;
        }
        String url = END_POINT + StringUtils.replace(LIST_BY_KEY_WITH_CURSOR, "{prefix}", prefix);
        url = StringUtils.replace(url, "{cursor}", lastCursor);

        String response = HttpRequestUtils.simpleGet(url, null, GET_TIME_OUT, CACHE_HEADER_MAP);
        ListResponseVO vo = parseListResponse(response);

        // System.out.println("===list with cursor url:" + url + ", size:" + (vo.getKeyList() == null ? 0 : vo.getKeyList().size()) + ", isComplete:" + vo.getList_complete() + ", lastCursor:" + lastCursor + ", next cursor:" + vo.getCursor());
        System.out.println("===list with cursor prefix:" + prefix + ", size:" + (vo.getKeyList() == null ? 0 : vo.getKeyList().size()) + ", isComplete:" + vo.getList_complete() + ", next vo:" + vo.getCursor());
        return vo;
    }

    public static List<String> listKeys(String prefix, String... extendPattern) {
        return listKeys(prefix, false, extendPattern);
    }

    public static List<String> listKeys(String prefix, boolean needDelete, String... extendPattern) {
        List<String> filteredList = new ArrayList<>(0);
        String url = END_POINT + StringUtils.replace(LIST_BY_KEY, "{prefix}", prefix);
        String response = HttpRequestUtils.simpleGet(url, null, GET_TIME_OUT, CACHE_HEADER_MAP);
        ListResponseVO vo = parseListResponse(response);
        String cursor = vo.getCursor();
        boolean isComplete = vo.getList_complete() || cursor == null;
        // System.out.println("===list url:" + url + ", size:" + (vo.getKeyList() == null ? 0 : vo.getKeyList().size()) + ", isComplete:" + vo.getList_complete() + ", next cursor:" + vo.getCursor());
        System.out.println("===list cache key, prefix:" + prefix + ", size:" + (vo.getKeyList() == null ? 0 : vo.getKeyList().size()) + ", isComplete:" + vo.getList_complete() + ", next cursor:" + cursor);

        filteredList.addAll(filterKeys(vo, extendPattern).stream().filter(key ->!needDelete || deleteKey(key) > 0).collect(Collectors.toList()));

        while(!isComplete) {
            vo = listKeysWithCursor(prefix, cursor);

            cursor = vo == null ? null : vo.getCursor();
            isComplete = vo == null || vo.getList_complete() || cursor == null;

            filteredList.addAll(filterKeys(vo, extendPattern).stream().filter(key ->!needDelete || deleteKey(key) > 0).collect(Collectors.toList()));
        }

        System.out.println("===list keys for prefix:" + prefix + ", size:" + (vo != null ? vo.getKeyList().size() : 0) + ", filteredList:" + filteredList.size());
        return filteredList;
    }

    private static List<String> filterKeys(ListResponseVO vo, String[] extendPattern) {
        if (extendPattern == null || extendPattern.length == 0) {
            return vo.getKeyList();
        }
        List<String> numberFilterList = Arrays.stream(extendPattern).filter(str -> str.matches("^\\d+$")).collect(Collectors.toList());
        List<String> wordsFilterList = new ArrayList<>(Arrays.asList(extendPattern));
        List<String> monthList = numberFilterList.stream().filter(str -> str.length() == 6).collect(Collectors.toList());
        if (numberFilterList.size() > 0) {
            wordsFilterList.removeAll(numberFilterList);
            wordsFilterList.addAll(monthList);
        }
        BiFunction<List<String>, String, Boolean> func = (patternList, key) -> {
            if (patternList == null || patternList.size() == 0) {
                return true;
            } else {
                for (String pattern : patternList) {
                    if (StringUtils.containsIgnoreCase(key, pattern) || (vo.getMetaMap() != null && StringUtils.containsIgnoreCase(vo.getMetaMap().get(key), pattern))) {
                        return true;
                    }
                }
                return false;
            }
        };
        List<String> filteredList = vo.getKeyList().stream()
                .filter(key -> func.apply(wordsFilterList, key))
                .filter(key -> func.apply(numberFilterList, key))
                .collect(Collectors.toList());
        return filteredList;
    }

    public static void main(String[] args) {

    }

}
