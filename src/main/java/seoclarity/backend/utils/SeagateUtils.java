package seoclarity.backend.utils;

import cn.hutool.json.JSONUtil;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.upload.XferMgrProgress;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.Instant;

public class SeagateUtils { // https://www.wrike.com/open.htm?id=**********

	public static final String SEAGATE_DEFAULT_BUCKET_NAME = "temporary-files-central"; // TODO
	public static final String SIGNATURE_HOSTNAME = "https://cloudv2.seoclarity.net";
	public static final String SEAGATE_SERVICE_ENDPOINT = "https://s3.clarity1.lyve.seagate.com";
	public static final String SEAGATE_SIGNING_REGION_US_EAST_1 = "us-east-1"; // "seagate"

	public static final String SEAGATE_PRODUCT_POP_RANK_BUCKET_NAME = "walmart-poprank";

	public static final long DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS = 1000 * 60 * 60 * 24 * 7; // 7 days = 604800 seconds
	public static final int SIGNATURE_DEFAULT_RETRY_COUNT = 3;
	public static final int SIGNATURE_BUCKET_TYPE_TEMPORARY_FILES = 1;
	public static final int SIGNATURE_BUCKET_TYPE_TEMPORARY_FILES_30 = 2;
	public static final int SIGNATURE_BUCKET_TYPE_SITE_AUDIT_4XX = 3;
	private static String seagateAccessKey;
	private static String seagateSecretKey;
	private static AWSCredentials credentials;

	public static void main(String[] args) throws Exception {
    	saveFileToDefaultSeagate(4, "/home/<USER>/4/adhocExtract/testSeagate_0612_4_2024-06-13_adhoc.txt");
		getDefaultSeagatePresignedUrl(4, "testSeagate_0612_4_2024-06-13_adhoc.txt");
	}


	public static boolean saveFileToDefaultSeagate(int ownDomainId, String fullPathFileName) {
		return saveFileToDefaultSeagate(SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fullPathFileName);
	}

	public static boolean saveFileToDefaultSeagate(String bucketName, int ownDomainId, String fullPathFileName) {
		getCredentialsByEnv(SIGNATURE_BUCKET_TYPE_TEMPORARY_FILES);
		return saveFileToSeagate(SEAGATE_SERVICE_ENDPOINT, SEAGATE_SIGNING_REGION_US_EAST_1, bucketName, ownDomainId, fullPathFileName, SIGNATURE_DEFAULT_RETRY_COUNT);
	}

	public static boolean saveFileToSeagateByBucketType(String bucketName, int ownDomainId, String fullPathFileName, Integer bucketType) {
		getCredentialsByEnv(bucketType);
		return saveFileToSeagate(SEAGATE_SERVICE_ENDPOINT, SEAGATE_SIGNING_REGION_US_EAST_1, bucketName, ownDomainId, fullPathFileName, SIGNATURE_DEFAULT_RETRY_COUNT);
	}

	public static String getDefaultSeagatePresignedUrl(int ownDomainId, String fileName) throws Exception {
		getCredentialsByEnv(SIGNATURE_BUCKET_TYPE_TEMPORARY_FILES);
		return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fileName,
				DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS, SIGNATURE_DEFAULT_RETRY_COUNT);
	}

	public static String getDefaultSeagatePresignedUrl(int ownDomainId, String fileName, long durationSeconds) throws Exception {
		getCredentialsByEnv(SIGNATURE_BUCKET_TYPE_TEMPORARY_FILES);
		return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fileName, durationSeconds,
				SIGNATURE_DEFAULT_RETRY_COUNT);
	}

	public static String getDefaultSeagatePresignedUrlByBucketType(int ownDomainId, String fileName, long durationSeconds, Integer bucketType) throws Exception {
		getCredentialsByEnv(bucketType);
		return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fileName, durationSeconds,
				SIGNATURE_DEFAULT_RETRY_COUNT);
	}

	public static String getDefaultSeagatePresignedUrl(String bucketName, int ownDomainId, String fileName) throws Exception {
		getCredentialsByEnv(SIGNATURE_BUCKET_TYPE_TEMPORARY_FILES);
		return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fileName,
				DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS, SIGNATURE_DEFAULT_RETRY_COUNT);
	}

	public static String getSeagatePresignedUrlByBucketType(String bucketName, int ownDomainId, String fileName, Integer bucketType) throws Exception {
		getCredentialsByEnv(bucketType);
		return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, bucketName, ownDomainId, fileName,
				DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS, SIGNATURE_DEFAULT_RETRY_COUNT);
	}

	public static String getSeagatePresignedUrlByBucketType(String signingRegion, String bucketName, int ownDomainId, String fileName, Integer bucketType) throws Exception {
		getCredentialsByEnv(bucketType);
		return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, signingRegion, bucketName, ownDomainId, fileName,
				DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS, SIGNATURE_DEFAULT_RETRY_COUNT);
	}

	public static boolean saveFileToSeagate(String serviceEndpoint, String signingRegion, String bucketName, int ownDomainId, String fullPathFileName, int retryCount) {
		File tempFile = new File(fullPathFileName);
		String fileName = tempFile.getName();
		String objectKey = ownDomainId + "/" + fileName; // TODO
		for (int triedCount = 0; triedCount < retryCount; triedCount++) {
			try {
				AmazonS3 s3client = AmazonS3ClientBuilder.standard()
						.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(serviceEndpoint, signingRegion))
						.withCredentials(new AWSStaticCredentialsProvider(credentials)).build();
				TransferManager transferManager = TransferManagerBuilder.standard().withS3Client(s3client).build();
				Upload uploader = transferManager.upload(new PutObjectRequest(bucketName, objectKey, tempFile));
				// loop with Transfer.isDone()
				XferMgrProgress.showTransferProgress(uploader);
				//  or block with Transfer.waitForCompletion()
				XferMgrProgress.waitForCompletion(uploader);
				transferManager.shutdownNow();
				System.out.println(" ==SavedFileToSeagate bucket:" + bucketName + " objKey:" + objectKey + " file:" + fullPathFileName);
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println(" ##FailedToSaveFileToSeagate retryCnt:" + triedCount);
				try {
					Thread.sleep((triedCount + 1) * 60 * 1000);
				} catch (Exception exp) {
					exp.printStackTrace();
				}
			}
		}
		return false;
	}

	// Sample parameters: signatureHostName:"https://cloud.seoclarity.net", signingRegion:"us-east-1", bucketName:SEAGATE_DEFAULT_BUCKET_NAME, fileName:"test.txt"
	public static String getSeagatePresignedUrl(String signatureHostName, String signingRegion, String bucketName, int ownDomainId, String fileName, long durationSeconds,
												int retryCount) throws Exception {
        boolean isNeedReplaceUrl = StringUtils.equals(signatureHostName, SIGNATURE_HOSTNAME);
        signatureHostName = isNeedReplaceUrl ? SEAGATE_SERVICE_ENDPOINT : signatureHostName;
		int triedCount = 0;
		while (true) {
			try {
				AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
						.withCredentials(new AWSStaticCredentialsProvider(credentials))
						.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(signatureHostName, signingRegion)).build();
				java.util.Date expiration = new java.util.Date();
				long expTimeMillis = Instant.now().toEpochMilli();
				expiration.setTime(expTimeMillis + durationSeconds);
				String key = bucketName + "/" + ownDomainId + "/" + fileName; // TODO
				System.out.println(" ==CreatingPresignedURL bucket:" + bucketName + " OID:" + ownDomainId + " file:" + fileName + "->objKey:" + key);
				GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest("", key).withMethod(HttpMethod.GET).withExpiration(expiration);

                URL url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);
                String downloadUrl = isNeedReplaceUrl ? StringUtils.replace(url.toString(), SEAGATE_SERVICE_ENDPOINT, SIGNATURE_HOSTNAME) : url.toString();
				System.out.println(" ==CreatedPreSignedURL:" + url.toString());
				System.out.println(" ==isNeedReplaceUrl:" + isNeedReplaceUrl + ", downloadUrl:" + downloadUrl);
				return downloadUrl;
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println(" ##FailedToGetPresignedUrl retryCnt:" + ++triedCount);
				if (triedCount > retryCount) {
					throw e;
				}
				try {
					Thread.sleep((triedCount + 1) * 60 * 1000);
				} catch (Exception exp) {
					exp.printStackTrace();
				}
			}
		}
	}

	/**
	 *
	 * @param bucketType 1: temporary files, 2: temporary files30, 3: site audit 4xx , default: temporary files
	 * @return
	 */
	private static void getCredentialsByEnv(Integer bucketType) {
		if(bucketType == null){
			seagateAccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getSeagateTemporaryFilesAccessKey();
			seagateSecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getSeagateTemporaryFilesDecryptedSecretKey();
		}else if(bucketType != null && bucketType == 1){
			seagateAccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getSeagateTemporaryFilesAccessKey();
			seagateSecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getSeagateTemporaryFilesDecryptedSecretKey();
		}else if(bucketType != null && bucketType == 2){
			seagateAccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getSeagateTemporaryFiles30AccessKey();
			seagateSecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getSeagateTemporaryFiles30DecryptedSecretKey();
		}else if(bucketType != null && bucketType == 3){
			seagateAccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getSeagateSiteAudit4xxAccessKey();
			seagateSecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getSeagateSiteAudit4xxDecryptedSecretKey();
		}else {
			throw new RuntimeException("bucketType not find.");
		}
//		System.out.println("bucketType:"+bucketType+" seagateAccessKey:"+seagateAccessKey+" seagateSecretKey:"+seagateSecretKey);
		credentials = new BasicAWSCredentials(seagateAccessKey, seagateSecretKey);
	}


	public static String getSpecifiedStorageFileUrl(CommonParamEntity commonParamEntity, String filename) {
		if (commonParamEntity != null) {
			if (org.apache.commons.lang3.StringUtils.equals(commonParamEntity.getFuncName(), "SetFtp")) {
				String host = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "host").toString();
				String port = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "port").toString();
				String protocol = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "protocol").toString();
				String folder = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "folder").toString();
				return protocol + "://" + host + ":" + port + folder + filename;
			} else if (org.apache.commons.lang3.StringUtils.equals(commonParamEntity.getFuncName(), "SetS3")) {
				String bucket = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "bucket").toString();
				String path = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "path").toString();
				return "s3://" + bucket + path + filename;
			}
		}
		return null;
	}

	public static void downloadFile(String urlString, String filePath) throws IOException {
		System.out.println("====downloadURL:" + urlString);
		URL url = new URL(urlString);
		InputStream inputStream = url.openStream();
		FileOutputStream fileOutputStream = new FileOutputStream(filePath);
		byte[] buffer = new byte[4096];
		int bytesRead;
		while ((bytesRead = inputStream.read(buffer)) != -1) {
			fileOutputStream.write(buffer, 0, bytesRead);
		}
		fileOutputStream.close();
		inputStream.close();
	}

	/**
	 *
	 * @param uiSeagatePath eg: seagate://us-east-1/ui-downloads/12283/12283_20250511_214_j1XWxT9950_ad_hoc_12283.txt
	 * @return
	 */
	public static String getDownloadUrlFromUIPath(String uiSeagatePath) throws Exception{
		if(StringUtils.isBlank(uiSeagatePath)){
			return null;
		}
		if(StringUtils.startsWith(uiSeagatePath, "seagate:")){//download from seagate
			String[] parts = uiSeagatePath.split("/");
			System.out.println("=====region: " + parts[2] + ", bucket: " + parts[3] + "  ,oid:  " + parts[4] + "  ,fileName:  " + parts[5]);
			String region = parts[2];
			String bucket = parts[3];
			String oid = parts[4];
			String fileName = parts[5];

			return SeagateUtils.getSeagatePresignedUrlByBucketType(region, bucket, Integer.parseInt(oid), fileName, SIGNATURE_BUCKET_TYPE_TEMPORARY_FILES);
		}else {
			return null;
		}

	}

}