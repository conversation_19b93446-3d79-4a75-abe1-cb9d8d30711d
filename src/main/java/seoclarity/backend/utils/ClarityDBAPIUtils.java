package seoclarity.backend.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

/**
 * <AUTHOR>
 * @date 2020-09-16
 * @path com.actonia.monitor.util.ClarityDBAPIUtils
 * 
 */
public class ClarityDBAPIUtils {

	//https://www.wrike.com/open.htm?id=936486286
	public static final String API_ENDPOINT_IBM_INTRANET = "http://api-internal-lb-190333-dal13.clb.appdomain.cloud/";

	public static final String API_ENDPOINT = API_ENDPOINT_IBM_INTRANET + "seoClarity";
	public static final String API_ENDPOINT_DEV = "http://10.186.101.149:8183/seoClarity";
	
	public static final String API_RI_FOLDER = "/dailyrankingv2";
	public static final String API_RI_PLP_FOLDER = "/preferLanding";
	public static final String API_RI_TREND_SUMMARY = "/summary";
	public static final String API_RI_SERP_FEATURE_SUMMARY = "/universalSummary";
	public static final String API_RI_URL_SERP_FEATURE_TOP10_KEYWORD_LEVEL_SUMMARY = "/urlSerpFeatureTop10TrendSummary";
	public static final String API_RI_URL_SERP_FEATURE_RANMKING_COMPARE_SUMMARY = "/rankingUrlSerpFeatureCompareCount";
	public static final String API_RI_URL_SERP_FEATURE_RANMKING_COMPARE_TABLE = "/rankingUrlSerpFeatureCompare";

	public static final String ACCESS_TOKEN_NAME = "access_token";
	public static final String ACCESS_TOKEN_VALUE = "c09yxv13-opr3-d745-9734-8pu48420nj67";
	
	private static final int SOCKET_TIMEOUT = 3 * 60 * 1000;
	private static final int CONNECT_TIMEOUT = 5 * 60 * 1000;
	private static final int CONNECTION_REQUEST_TIMEOUT = 3 * 60 * 1000;
	private static final int TRY_CNT = 3;
	private static final long TRY_TIME_INTERVAL = 2000;
	
	public static String simpleGet(String urlStr) throws Exception{
		CloseableHttpClient httpclient = HttpClients.custom().build();
		CloseableHttpResponse response = null;
		
		URL url = new URL(urlStr);
		URI uri = new URI(url.getProtocol(), url.getHost(), url.getPath(), url.getQuery(), null);
		
		HttpGet get = new HttpGet(uri);
		
		RequestConfig requestCfg = RequestConfig.custom()
				.setSocketTimeout(1 * 60 * 1000)
				.setConnectTimeout(1 * 60 * 1000)
				.build();
		get.setConfig(requestCfg);
		String resStr = "";
		try {
			try {
				response = httpclient.execute(get);
				HttpEntity entity = response.getEntity();
				if(entity != null){
					InputStream in = entity.getContent();
					BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
					StringBuilder strber= new StringBuilder();
					String line = null;
					while((line = br.readLine())!=null){
						strber.append(line+'\n');
					}
					br.close();
					in.close();
					resStr = strber.toString();
				}
			} catch (ClientProtocolException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (response != null) {
				response.close();
			}
			httpclient.close();
		}
		
		return resStr;
	}
	
	public static String simpleGet(String url, Map<String, String> paramMap) throws Exception{
		String resStr = null;
		CloseableHttpClient httpclient = HttpClients.custom().build();
		CloseableHttpResponse response = null;
		
		HttpGet get = new HttpGet(url);
		
		try {
			List<NameValuePair> params = new ArrayList<NameValuePair>();
			if (paramMap != null) {
				for (String key : paramMap.keySet()) {
					if (!key.equalsIgnoreCase(ACCESS_TOKEN_NAME)) {
						params.add(new BasicNameValuePair(key, paramMap.get(key)));
					}
				}
			}
			params.add(new BasicNameValuePair(ACCESS_TOKEN_NAME, ACCESS_TOKEN_VALUE));
			
			String param = URLEncodedUtils.format(params, "UTF-8");
			
			get.setURI(URI.create(url + "?" + param));
			try {
				response = httpclient.execute(get);
				HttpEntity entity = response.getEntity();
				if(entity != null){
					InputStream in = entity.getContent();
					BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
					StringBuilder strber= new StringBuilder();
					String line = null;
					while((line = br.readLine())!=null){
						strber.append(line+'\n');
					}
					br.close();
					in.close();
					resStr = strber.toString();
				}
			} catch (ClientProtocolException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (response != null) {
				response.close();
			}
			httpclient.close();
		}
		
		return resStr;
	}
	
	public static String simplePost(String url, String jsonStr) throws Exception{
		String resStr = null;
		CloseableHttpClient httpclient = HttpClients.custom().build();
		CloseableHttpResponse response = null;
		
		HttpPost httppost = new HttpPost(url);
		
		RequestConfig requestCfg = RequestConfig.custom()
				.setSocketTimeout(SOCKET_TIMEOUT)
				.setConnectTimeout(CONNECT_TIMEOUT)
				.build();
		
		StringEntity stringEntity = new StringEntity(jsonStr, "UTF-8");
		httppost.setEntity(stringEntity);
		httppost.setConfig(requestCfg);
		
		try {
			int tryCnt = 0;
			do {
				try {
					response = httpclient.execute(httppost);
					HttpEntity entity = response.getEntity();
					if (entity != null) {
						resStr = EntityUtils.toString(entity, "utf-8");
					}
					break;
				} catch (Exception e) {
					tryCnt++;
					System.out.println("=Post query failed, tryCnt:" + tryCnt + ", url:" + url);
					if (tryCnt >= TRY_CNT) {
						e.printStackTrace();
					} else {
						Thread.sleep(TRY_TIME_INTERVAL);
					}
				}
			} while(tryCnt < TRY_CNT);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (response != null) {
				response.close();
			}
			httpclient.close();
		}
		return resStr;
	}

	public static String simplePost(String url, Map<String, String> paramMap) throws Exception {
		String resStr = null;
		CloseableHttpClient httpclient = HttpClients.custom().build();
		CloseableHttpResponse response = null;
		
		HttpPost httppost = new HttpPost(url);
		
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		for (String key : paramMap.keySet()) {
			if (!key.equalsIgnoreCase(ACCESS_TOKEN_NAME)) {
				params.add(new BasicNameValuePair(key, paramMap.get(key)));
			}
		}
		params.add(new BasicNameValuePair(ACCESS_TOKEN_NAME, ACCESS_TOKEN_VALUE));

		RequestConfig requestCfg = RequestConfig.custom()
				.setSocketTimeout(SOCKET_TIMEOUT)
				.setConnectTimeout(CONNECT_TIMEOUT)
				.build();
		
		UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(params, "UTF-8");
		httppost.setEntity(formEntity);
		httppost.setConfig(requestCfg);
		
		try {
			int tryCnt = 0;
			do {
				try {
					response = httpclient.execute(httppost);
					HttpEntity entity = response.getEntity();
					if (entity != null) {
						resStr = EntityUtils.toString(entity, "utf-8");
					}
					break;
				} catch (Exception e) {
					tryCnt++;
					System.out.println("=Post query failed, tryCnt:" + tryCnt + ", url:" + url + ", paramMap:" + paramMap);
					if (tryCnt >= TRY_CNT) {
						e.printStackTrace();
					} else {
						Thread.sleep(TRY_TIME_INTERVAL);
					}
				}
			} while(tryCnt < TRY_CNT);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (response != null) {
				response.close();
			}
			httpclient.close();
		}
		return resStr;
	}
	
}
