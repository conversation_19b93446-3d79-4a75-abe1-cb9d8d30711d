package seoclarity.backend.utils;

import java.util.regex.Pattern;

// https://www.wrike.com/open.htm?id=1135983604
public class UrlFilterUtil {

    public static boolean shouldSkipUrl(String url) {
        return containsPattern(url, "/api/") ||
                containsPattern(url, "graphql") ||
                containsPattern(url, ".js") ||
                containsPattern(url, "trvl-px") ||
                containsPattern(url, ".php") ||
                containsPattern(url, "/botOrNot/") ||
                containsPattern(url, "/cgp/simple/") ||
                containsPattern(url, "/gdpr") ||
                matchesRegex(url, "\\/v\\d\\/");
    }

    private static boolean containsPattern(String input, String pattern) {
        return input != null && input.contains(pattern);
    }

    private static boolean matchesRegex(String input, String regex) {
        return input != null && Pattern.matches(regex, input);
    }
}
