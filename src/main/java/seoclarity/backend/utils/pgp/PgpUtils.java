package seoclarity.backend.utils.pgp;

import org.bouncycastle.openpgp.PGPException;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.NoSuchProviderException;

public class PgpUtils {

    private static boolean isArmored = false;
    private static boolean integrityCheck = true;

    public static void encrypt(String pubKeyFile, String outPutFIlePath, String originalFilePath) throws NoSuchProviderException, IOException, PGPException {
        FileInputStream pubKeyIs = new FileInputStream(pubKeyFile);
        FileOutputStream cipheredFileIs = new FileOutputStream(outPutFIlePath);
        PgpHelper.getInstance().encryptFile(cipheredFileIs, originalFilePath, PgpHelper.getInstance().readPublicKey(pubKeyIs), isArmored, integrityCheck);
        cipheredFileIs.close();
        pubKeyIs.close();
    }

}
