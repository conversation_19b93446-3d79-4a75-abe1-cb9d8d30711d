package seoclarity.backend.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.aliyuncs.AcsRequest;
import com.aliyuncs.AcsResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecs.model.v20140526.*;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.compute.Compute;
import com.google.api.services.compute.model.*;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.entity.CloudEngineMachineEntity;
import seoclarity.backend.entity.actonia.EC2UserDataEntity;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * rm -f ./src/main/java/seoclarity/backend/onetime/LocalTest.java && vi ./src/main/java/seoclarity/backend/onetime/LocalTest.java
 *
 * mvn clean compile
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.utils.GoogleCloudUtils" -Dexec.cleanupDaemonThreads=false -Dexec.args="START_ALI DAILY_US_DESKTOP 1" > ali_cloud_start.log &
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.utils.GoogleCloudUtils" -Dexec.cleanupDaemonThreads=false -Dexec.args="START_GOOGLE DAILY_US_DESKTOP 1" > google_cloud_start.log &
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.utils.GoogleCloudUtils" -Dexec.cleanupDaemonThreads=false -Dexec.args="MONITOR" > google-cloud-monitor.log &
 */
@CommonsLog
public class GoogleCloudUtils {

    static String vmType = "f1-micro";
    static String googleDomain ="https://www.googleapis.com";
    static Map<String, String> regionMap;
    static String projectId = "cloud-crawler-258821";
    static String image = "google-rank-test4";
    static Compute computeService;
    static final String DEFAULT_PREFIX = "google-";
    private final static String ALI_KEY = "******************************";
    private final static String ALI_ID = "LTAI4GJ9187pDUA7dyKYSqBW";
    private static final String ALI_REGION_ID = "us-west-1";
    private static Map<String, IAcsClient> aliClientMap = new HashMap<>();

    private static IAcsClient getAliClient(String regionId) {
        if(aliClientMap.get(regionId) == null) {
            IClientProfile profile = DefaultProfile.getProfile(regionId, ALI_ID, ALI_KEY);
            aliClientMap.put(regionId, new DefaultAcsClient(profile));
        }
        return aliClientMap.get(regionId);
    }

    public static void main(String args[]) throws IOException, GeneralSecurityException {

        regionMap = new HashMap<>();
        regionMap.put("us-east1", "us-east1-b");
        regionMap.put("us-east4", "us-east4-b");
        regionMap.put("us-west1", "us-west1-b");
        regionMap.put("us-west2", "us-west2-b");
        regionMap.put("us-west4", "us-west4-b");
        computeService = createComputeService();

        String command = args[0];
        if(StrUtil.equalsIgnoreCase(command, "STOP")) {
            deleteDailyJob();
        } else if(StrUtil.startWithIgnoreCase(command, "START")) {
            EC2UserDataEntity dataEntity = new EC2UserDataEntity();
            int sleepTime = 49;
            String type = args[1];
            if(StringUtils.equalsIgnoreCase(type, "MONTHLY_US_DESKTOP")) {
                dataEntity.setSpotInstance(true);
                dataEntity.setThreadCount(2);
                dataEntity.setSleepTime(sleepTime);
                dataEntity.setKeywordQueueName("MONTHLY_US_DESKTOP");
                dataEntity.setS3BucketName(null);
                dataEntity.setS3KeyPath(null);
                dataEntity.setScribeServers(new String[] {"184.154.155.94", "108.178.44.126"});
                dataEntity.setScribePath("monthly_commoncrawl_keywordRank");
                dataEntity.setUule("a+cm9sZTogQ1VSUkVOVF9MT0NBVElPTgpwcm9kdWNlcjogREVWSUNFX0xPQ0FUSU9OCnRpbWVzdGFtcDogMTU3NjU3NDM2NDg1ODAwMApsYXRsbmcgewogIGxhdGl0dWRlX2U3OiA0MDcxMjc3NTMKICBsb25naXR1ZGVfZTc6IC03NDAwNTk3MjgKfQpyYWRpdXM6IDkzMDAwCg==");
                dataEntity.setNewCookie(false);
                dataEntity.setProvider("google-cloud-monthly-desktop");
                dataEntity.setMobile(false);
                dataEntity.setSleepForBlock(0);
                dataEntity.setRandomDelay(0);
                dataEntity.setSolveCaptcha(false);
                dataEntity.setClickResult(false);
            }

            if(StringUtils.equalsIgnoreCase(type, "MONTHLY_US_MOBILE")) {
                dataEntity = new EC2UserDataEntity();
                dataEntity.setSpotInstance(true);
                dataEntity.setThreadCount(2);
                dataEntity.setSleepTime(sleepTime);
                dataEntity.setKeywordQueueName("MONTHLY_US_MOBILE");
                dataEntity.setS3BucketName(null);
                dataEntity.setS3KeyPath(null);
                dataEntity.setScribeServers(new String[] {"184.154.155.94", "108.178.44.126"});
                dataEntity.setScribePath("monthly_mobile_commoncrawl_keywordRank");
                dataEntity.setUule("a+cm9sZTogQ1VSUkVOVF9MT0NBVElPTgpwcm9kdWNlcjogREVWSUNFX0xPQ0FUSU9OCnRpbWVzdGFtcDogMTU3NjU3NDM2NDg1ODAwMApsYXRsbmcgewogIGxhdGl0dWRlX2U3OiA0MDcxMjc3NTMKICBsb25naXR1ZGVfZTc6IC03NDAwNTk3MjgKfQpyYWRpdXM6IDkzMDAwCg==");
                dataEntity.setNewCookie(false);
                dataEntity.setProvider("google-cloud-monthly-mobile");
                dataEntity.setMobile(true);
                dataEntity.setSleepForBlock(0);
                dataEntity.setRandomDelay(0);
                dataEntity.setSolveCaptcha(false);
                dataEntity.setClickResult(false);
            }

            if(StringUtils.equalsIgnoreCase(type, "DAILY_US_MOBILE")) {
                dataEntity = new EC2UserDataEntity();
                dataEntity.setSpotInstance(true);
                dataEntity.setThreadCount(2);
                dataEntity.setSleepTime(sleepTime);
                dataEntity.setKeywordQueueName("QUEUE_NAME_GOOGLE_MOBILE_COMMON_CRAWL");
                dataEntity.setS3BucketName("daily-html-virginia");
                dataEntity.setS3KeyPath("${today}/mobile-${engineId}-${languageId}/${keywordText}");
                dataEntity.setScribeServers(new String[] {"184.154.13.230", "169.60.132.23"});
                dataEntity.setScribePath("mobile_commoncrawl_keywordRank");
                dataEntity.setUule("w+CAIQICINVW5pdGVkIFN0YXRlcw");
                dataEntity.setNewCookie(false);
                dataEntity.setProvider("google-cloud-daily-mobile");
                dataEntity.setMobile(true);
                dataEntity.setSleepForBlock(0);
                dataEntity.setRandomDelay(0);
                dataEntity.setSolveCaptcha(false);
                dataEntity.setClickResult(false);
            }

            if(StringUtils.equalsIgnoreCase(type, "DAILY_US_DESKTOP")) {
                dataEntity = new EC2UserDataEntity();
                dataEntity.setSpotInstance(true);
                dataEntity.setThreadCount(2);
                dataEntity.setSleepTime(sleepTime);
                dataEntity.setKeywordQueueName("KEYWROD_RANK_QUEUE_GOOGLE_COM_USEN");
                dataEntity.setS3BucketName("daily-html-virginia");
                dataEntity.setS3KeyPath("${today}/${engineId}-${languageId}/${keywordText}");
                dataEntity.setScribeServers(new String[] {"184.154.13.230", "169.60.132.23"});
                dataEntity.setScribePath("commoncrawl_keywordRank");
                dataEntity.setUule("w+CAIQICINVW5pdGVkIFN0YXRlcw");
                dataEntity.setNewCookie(false);
                dataEntity.setProvider("google-cloud-daily-desktop");
                dataEntity.setMobile(false);
                dataEntity.setSleepForBlock(0);
                dataEntity.setRandomDelay(0);
                dataEntity.setSolveCaptcha(false);
                dataEntity.setClickResult(false);
            }

            int count = NumberUtil.parseInt(args[2]);
            if(count <= 0) {
                log.info("start count is 0");
                return;
            }
            if(StrUtil.equalsIgnoreCase(command, "START_GOOGLE")) {
                startGoogleDailyJob(JSONUtil.toJsonPrettyStr(dataEntity), count);
            } else if(StrUtil.equalsIgnoreCase(command, "START_ALI")) {
                dataEntity.setProvider(StrUtil.replace(dataEntity.getProvider(), "google-cloud", "ali-cloud"));
                startAliDailyJob(JSONUtil.toJsonPrettyStr(dataEntity), count);
            }

        } else if(StrUtil.equalsIgnoreCase(command, "MONITOR")) {
            MonitorDailyJob();
        }
    }

    public static void startGoogleDailyJob(String userData, int count) {
        int leftCount = count;
        for (Map.Entry<String, String> stringStringEntry : regionMap.entrySet()) {
            int startCount = count / regionMap.size() <= 0 ? 1 : count / regionMap.size();
            Thread thread = new Thread(() -> {
                for (int i = 0; i < startCount; i++) {
                    getInstance(computeService, DEFAULT_PREFIX, stringStringEntry.getKey(), stringStringEntry.getValue(), userData, projectId, image, vmType);
                }
            });
            thread.start();
            leftCount = leftCount - startCount;
            if(leftCount <= 0) {
                break;
            }
        }
    }

    public static void startAliDailyJob(String userData, int count) {
        int leftCount = count;

        while (leftCount > 0) {
            int startCount = Math.min(leftCount, 100);
            RunInstancesRequest runInstancesRequest = new RunInstancesRequest();
            runInstancesRequest.setSysRegionId(ALI_REGION_ID);
            runInstancesRequest.setInstanceType("ecs.xn4.small");
            runInstancesRequest.setInstanceChargeType("PostPaid");
            runInstancesRequest.setImageId("m-rj9ehoz0d2dfo7zhzavd");
            runInstancesRequest.setSecurityGroupId("sg-rj9587eky8bks1gh7h23");
            runInstancesRequest.setPeriod(1);
            runInstancesRequest.setPeriodUnit("Hourly");
            runInstancesRequest.setZoneId("us-west-1a");
            runInstancesRequest.setInternetChargeType("PayByTraffic");
            runInstancesRequest.setVSwitchId("vsw-rj9i8rqkkebls51ewzmdl");
            runInstancesRequest.setInstanceName("launch-advisor-"+ DateUtil.date().toString("yyyyMMdd"));
            runInstancesRequest.setAmount(startCount);
            runInstancesRequest.setInternetMaxBandwidthOut(5);
            runInstancesRequest.setIoOptimized("optimized");
            runInstancesRequest.setUserData(Base64.encode(userData));
            runInstancesRequest.setSpotStrategy("SpotAsPriceGo");
            runInstancesRequest.setSystemDiskSize("20");
            runInstancesRequest.setSystemDiskCategory("cloud_efficiency");

            log.info("Starting "+startCount+" servers...");
            callOpenApi(runInstancesRequest, getAliClient(ALI_REGION_ID));
            leftCount -= startCount;

        }

    }

    public static void MonitorDailyJob() {
        AmazonSQS amazonSqs = SQSUtils.getAmazonSQS();
        String googleQueryUrl = SQSUtils.createQueue("GOOGLE_VM_ENGINE", amazonSqs);
        String aliQueryUrl = SQSUtils.createQueue("ALI_VM_ENGINE", amazonSqs);

        while (true) {
            List<Message> list = SQSUtils.getMessageFromQueue(amazonSqs, googleQueryUrl, 10, 3600);
            for (Message message : list) {
                log.info("google message : "+message.getBody()+" => "+message.getMessageId());
                final CloudEngineMachineEntity engineMachineEntity = JSONUtil.toBean(message.getBody(), CloudEngineMachineEntity.class);
                String zone = StrUtil.subAfter(engineMachineEntity.getZone(), "/", true);
                String region = StrUtil.subBefore(zone, "-", true);
                log.info("Find region : " + region + " zone :" + zone+" => "+message.getMessageId());
                Thread t = new Thread(() -> {
                    if (engineMachineEntity.getTerminate() != null && engineMachineEntity.getTerminate()) {
                        try {
                            log.info("Delete Google VM => "+message.getMessageId());
                            Compute.Instances.Delete delete = computeService.instances().delete(engineMachineEntity.getProjectId(), zone, engineMachineEntity.getName());
                            delete.execute();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    } else if (engineMachineEntity.getDelete() != null && engineMachineEntity.getDelete()) {
                        try {
                            log.info("Restart this Google VM => "+message.getMessageId());
//                        Compute.Instances.Delete delete = computeService.instances().delete(engineMachineEntity.getProjectId(), zone, engineMachineEntity.getName());
//                        delete.execute();
                            ThreadUtil.sleep(2, TimeUnit.MINUTES);
                            Compute.Instances.Start start = computeService.instances().start(engineMachineEntity.getProjectId(), zone, engineMachineEntity.getName());
                            Operation operation = start.execute();
                            blockUntilComplete(computeService, operation, projectId, 5 * 60 * 1000);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        while (true) {
                            log.info("find need restart vm : " + engineMachineEntity.getName()+" => "+message.getMessageId());
                            try {
                                Compute.Instances.Stop stop = computeService.instances().stop(engineMachineEntity.getProjectId(), zone, engineMachineEntity.getName());
                                Operation operation = stop.execute();
                                blockUntilComplete(computeService, operation, projectId, 5 * 60 * 1000);
                                System.out.println("Google Restart Stop Opt: "+operation.getStatus());
//                                ThreadUtil.sleep(2, TimeUnit.MINUTES);
                                Compute.Instances.Get get = computeService.instances().get(engineMachineEntity.getProjectId(), zone, engineMachineEntity.getName());
                                Instance instance = get.execute();
                                //STAGING, RUNNING, STOPPING, STOPPED, SUSPENDING, SUSPENDED, and TERMINATED
                                String status = instance.getStatus();
                                while (StrUtil.equalsIgnoreCase(status, "TERMINATED") == false) {
                                    System.out.println("Google VM "+engineMachineEntity.getName()+" status is "+status+" waiting for STOPPED");
                                    ThreadUtil.sleep(10000);
                                    instance = get.execute();
                                    status = instance.getStatus();
                                }

                                Compute.Instances.Start start = computeService.instances().start(engineMachineEntity.getProjectId(), zone, engineMachineEntity.getName());
                                operation = start.execute();
                                blockUntilComplete(computeService, operation, projectId, 5 * 60 * 1000);
                                System.out.println("Google Restart Start Opt: "+operation.getStatus());
                            } catch (GoogleJsonResponseException e) {
                                log.info(e.getDetails().toString());
                                if(e.getDetails().getCode() == 404) {
                                    break;
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                ThreadUtil.sleep(5, TimeUnit.MINUTES);
                            }
                        }
                    }
                });
                t.start();

                SQSUtils.deleteMessageFromQueue(amazonSqs, googleQueryUrl, message);
            }

            list = SQSUtils.getMessageFromQueue(amazonSqs, aliQueryUrl, 10, 3600);
			for (Message message : list) {
                log.info("find need restart vm : "+message.getBody()+" => "+message.getMessageId());
                final CloudEngineMachineEntity engineMachineEntity = JSONUtil.toBean(message.getBody(), CloudEngineMachineEntity.class);
				String regionId = StrUtil.subPre(engineMachineEntity.getZone(), engineMachineEntity.getZone().length()-1);
				log.info("Find region : "+regionId+" => "+message.getMessageId());
				Thread t = new Thread(() -> {
					IAcsClient iAcsClient = getAliClient(regionId);
                    if(null != engineMachineEntity.getTerminate() && engineMachineEntity.getTerminate()) {
                        DeleteInstanceRequest deleteInstanceRequest = new DeleteInstanceRequest();
                        deleteInstanceRequest.setInstanceId(engineMachineEntity.getInstanceId());
                        deleteInstanceRequest.setForce(true);
                        callOpenApi(deleteInstanceRequest, iAcsClient);
                        log.info("Delete Ali ECS done => "+message.getMessageId());
                    } else if(null == engineMachineEntity.getDelete() || engineMachineEntity.getDelete() == false) {
                        RebootInstanceRequest rebootInstanceRequest = new RebootInstanceRequest();
                        rebootInstanceRequest.setForceStop(true);
                        rebootInstanceRequest.setInstanceId(engineMachineEntity.getInstanceId());
                        callOpenApi(rebootInstanceRequest, iAcsClient);
                        log.info("Reboot Ali ECS done => "+message.getMessageId());
                    } else {
                        DescribeInstancesRequest describeInstancesRequest = new DescribeInstancesRequest();
                        JSONArray instancesIds = new JSONArray();
                        instancesIds.put(engineMachineEntity.getInstanceId());
                        String idStr = JSONUtil.toJsonStr(instancesIds);
                        log.info(idStr);
                        describeInstancesRequest.setInstanceIds(idStr);
                        DescribeInstancesResponse describeInstancesResponse = callOpenApi(describeInstancesRequest, iAcsClient);
                        if(CollectionUtil.isNotEmpty(describeInstancesResponse.getInstances())) {
                            return;
                        }
                        for (DescribeInstancesResponse.Instance instance : describeInstancesResponse.getInstances()) {
                            RunInstancesRequest runInstancesRequest = new RunInstancesRequest();
                            runInstancesRequest.setDryRun(false);
                            runInstancesRequest.setInstanceType(instance.getInstanceType());
                            runInstancesRequest.setInstanceChargeType(instance.getInstanceChargeType());
                            runInstancesRequest.setImageId(instance.getImageId());
                            runInstancesRequest.setSecurityGroupId(instance.getSecurityGroupIds().get(0));
                            runInstancesRequest.setPeriod(1);
                            runInstancesRequest.setPeriodUnit("Hourly");
                            runInstancesRequest.setZoneId(instance.getZoneId());
                            runInstancesRequest.setInternetChargeType(instance.getInternetChargeType());
                            runInstancesRequest.setVSwitchId("vsw-rj9i8rqkkebls51ewzmdl");
                            runInstancesRequest.setInstanceName(instance.getInstanceName());
                            runInstancesRequest.setAmount(1);
                            runInstancesRequest.setInternetMaxBandwidthOut(5);
                            runInstancesRequest.setHostName(instance.getHostName());
                            runInstancesRequest.setUniqueSuffix(true);
                            runInstancesRequest.setIoOptimized("optimized");

                            DescribeUserDataRequest describeUserDataRequest = new DescribeUserDataRequest();
                            describeUserDataRequest.setInstanceId(instance.getInstanceId());
                            DescribeUserDataResponse describeUserDataResponse = callOpenApi(describeUserDataRequest, iAcsClient);
                            runInstancesRequest.setUserData(describeUserDataResponse.getUserData());
                            runInstancesRequest.setSpotStrategy(instance.getSpotStrategy());
                            runInstancesRequest.setSystemDiskSize("20");
                            runInstancesRequest.setSystemDiskCategory("cloud_efficiency");

                            DeleteInstanceRequest deleteInstanceRequest = new DeleteInstanceRequest();
                            deleteInstanceRequest.setInstanceId(instance.getInstanceId());
                            deleteInstanceRequest.setForce(true);
                            callOpenApi(deleteInstanceRequest, iAcsClient);

                            callOpenApi(runInstancesRequest, iAcsClient);
                            log.info("Delete and Restart Ali ECS done => "+message.getMessageId());
                        }
                    }
				});
                t.start();
				SQSUtils.deleteMessageFromQueue(amazonSqs, aliQueryUrl, message);
			}

            ThreadUtil.sleep(60000);
        }
    }

    /**
     * 调用OpenAPI的方法，这里进行了错误处理
     *
     * @param request AcsRequest, Open API的请求
     * @param <T> AcsResponse 请求所对应返回值
     * @return 返回OpenAPI的调用结果，如果调用失败，则会返回null
     */
    private static <T extends AcsResponse> T  callOpenApi(AcsRequest<T> request, IAcsClient client) {
        try {
            T response = client.getAcsResponse(request, true, 50);
            log.info(String.format("Success. OpenAPI Action: %s call successfully.", request.getActionName()));
            return response;
        } catch (ServerException e) {
            log.info(String.format("Fail. Something with your connection with Aliyun go incorrect. ErrorCode: %s",
                    e.getErrCode()));
        } catch (ClientException e) {
            log.info(String.format("Fail. Business error. ErrorCode: %s, RequestId: %s",
                    e.getErrCode(), e.getRequestId()));
        }
        return null;
    }

    public static void deleteDailyJob() {
        for (Map.Entry<String, String> stringStringEntry : regionMap.entrySet()) {
            try {
                delete(computeService, stringStringEntry.getValue());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        AmazonSQS amazonSqs = SQSUtils.getAmazonSQS();
        String googleQueryUrl = SQSUtils.createQueue("GOOGLE_VM_ENGINE", amazonSqs);
        SQSUtils.purgeQueue(amazonSqs, googleQueryUrl);

        IAcsClient iAcsClient = getAliClient(ALI_REGION_ID);

        DescribeInstancesRequest describeInstancesRequest = new DescribeInstancesRequest();
        describeInstancesRequest.setSysRegionId(ALI_REGION_ID);
        describeInstancesRequest.setPageSize(100);

        int pageNumber = 1;
        do {
            describeInstancesRequest.setPageNumber(pageNumber);
            log.info("Deleting Ali Page : "+pageNumber);
            DescribeInstancesResponse describeInstancesResponse = callOpenApi(describeInstancesRequest, iAcsClient);
            if(describeInstancesResponse == null || CollectionUtil.isEmpty(describeInstancesResponse.getInstances())) {
                break;
            }
            for (DescribeInstancesResponse.Instance instance : describeInstancesResponse.getInstances()) {
                DeleteInstanceRequest deleteInstanceRequest = new DeleteInstanceRequest();
                deleteInstanceRequest.setInstanceId(instance.getInstanceId());
                deleteInstanceRequest.setForce(true);
                callOpenApi(deleteInstanceRequest, iAcsClient);
            }
        } while (true);

        String aliQueryUrl = SQSUtils.createQueue("ALI_VM_ENGINE", amazonSqs);
        SQSUtils.purgeQueue(amazonSqs, aliQueryUrl);

    }


    public  static void delete(Compute compute, String zone) throws IOException {
        Compute.Instances.List instances = compute.instances().list(projectId, zone);
        InstanceList instanceList = instances.execute();
        if(CollectionUtil.isEmpty(instanceList.getItems())) {
            return;
        }
        for (Instance item : instanceList.getItems()) {
            if(StrUtil.startWithIgnoreCase(item.getName(), DEFAULT_PREFIX)) {
                log.info("Closing "+item.getName());
                Compute.Instances.Delete delete = compute.instances().delete(projectId, zone, item.getName());
                delete.execute();
            }
        }
    }

    private static Operation blockUntilComplete(Compute compute, Operation operation, String projectId, long timeoutMil) throws Exception {
        long start = System.currentTimeMillis();
        final long pollInterval = 3 * 1000;
        String zone = operation.getZone();  // null for global/regional operations
        if (zone != null) {
            String[] bits = zone.split("/");
            zone = bits[bits.length - 1];
        }

        String region = operation.getRegion();
        if (region!=null){
            String[] bits = region.split("/");
            region = bits[bits.length - 1];
        }
        String status = operation.getStatus();
        String opId = operation.getName();
        while (operation != null && !status.equals("DONE")) {
            Thread.sleep(pollInterval);
            long elapsed = System.currentTimeMillis() - start;
            if (elapsed >= timeoutMil) {
                System.out.println("Timed out waiting for operation to complete");
                break;
            }

            if (zone != null) {
                Compute.ZoneOperations.Get get = compute.zoneOperations().get(projectId, zone, opId);
                operation = get.execute();
            } else if(region!=null){
                Compute.RegionOperations.Get get = compute.regionOperations().get(projectId, region, opId);
                operation = get.execute();
            }else {
                Compute.GlobalOperations.Get get = compute.globalOperations().get(projectId, opId);
                operation = get.execute();
            }
            if (operation != null) {
                status = operation.getStatus();
            }
            System.out.println("Now status "+status);
        }
        return operation;
    }

    public static Instance getInstance(Compute compute, String vmPrefix, String region, String zone, String userData, String projectId, String image, String vmType) {

        String imageId = "projects/"+projectId+"/global/images/"+image;
        long osDiskSize = 10;
        String osDiskType = "pd-standard";
        String vmName = vmPrefix + "-" + IdUtil.simpleUUID();

        try {
            Instance instance = new Instance();
            instance.setName(vmName);
            instance.setZone("projects/"+projectId+"/zones/"+zone);
            instance.setKind("compute#instance");
            instance.setMachineType("projects/"+projectId+"/zones/" + zone + "/machineTypes/" + vmType);
            instance.setCanIpForward(false);
            instance.setDeletionProtection(false);

            //set userData
            Metadata metadata = new Metadata();
            Metadata.Items items = new Metadata.Items();
            items.setKey("userData");
            items.setValue(userData);
            metadata.setItems(Collections.singletonList(items));
            instance.setMetadata(metadata);

            //set Network info
            NetworkInterface networkInterface = new NetworkInterface();
            networkInterface.setKind("compute#networkInterface");
            networkInterface.setSubnetwork("projects/"+projectId+"/regions/"+region+"/subnetworks/default");

            AccessConfig config = new AccessConfig();
            config.setKind("compute#accessConfig");
            config.setType("ONE_TO_ONE_NAT");
            config.setName("External NAT");
            config.setNetworkTier("PREMIUM");
            networkInterface.setAccessConfigs(Collections.singletonList(config));
            instance.setNetworkInterfaces(Collections.singletonList(networkInterface));

            //set Disk info
            AttachedDisk osDisk = new AttachedDisk();
            osDisk.setBoot(true);
            osDisk.setAutoDelete(true);
            osDisk.setMode("READ_WRITE");
            osDisk.setType("PERSISTENT");
            osDisk.setKind("compute#attachedDisk");

            AttachedDiskInitializeParams osParams = new AttachedDiskInitializeParams();
            osParams.setSourceImage(imageId);
            osParams.setDiskType("zones/" + zone + "/diskTypes/" + osDiskType);
            osParams.setDiskSizeGb(osDiskSize);
            osDisk.setInitializeParams(osParams);
            instance.setDisks(Collections.singletonList(osDisk));

            // start spot instance
            Scheduling scheduling = new Scheduling();
            scheduling.setPreemptible(true);
            scheduling.setOnHostMaintenance("TERMINATE");
            scheduling.setAutomaticRestart(false);
            instance.setScheduling(scheduling);

            //account setting
            ServiceAccount account = new ServiceAccount();
            account.setEmail("<EMAIL>");
            List<String> scopes = new ArrayList<>();
            scopes.add(googleDomain + "/auth/devstorage.read_only");
            scopes.add(googleDomain + "/auth/logging.write");
            scopes.add(googleDomain + "/auth/monitoring.write");
            scopes.add(googleDomain + "/auth/servicecontrol");
            scopes.add(googleDomain + "/auth/service.management.readonly");
            scopes.add(googleDomain + "/auth/trace.append");
            account.setScopes(scopes);
            instance.setServiceAccounts(Collections.singletonList(account));

            Compute.Instances.Insert insert = compute.instances().insert(projectId, zone, instance);
            Operation operation = insert.execute();
            operation = blockUntilComplete(compute, operation, projectId, 5 * 60 * 1000);

            if (operation != null && operation.getError() != null) {
                log.info(operation.getError().toString());
                log.info(operation.getHttpErrorMessage());
                log.info(operation.getHttpErrorStatusCode());
            } else {
                log.info("Start a new Google VM.");
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static Compute createComputeService() throws IOException, GeneralSecurityException {
        HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
        JsonFactory jsonFactory = JacksonFactory.getDefaultInstance();
        GoogleCredential credential = GoogleCredential.fromStream(GoogleCloudUtils.class.getClassLoader().getResourceAsStream("googleCloudKey.json"));
        if (credential.createScopedRequired()) {
            credential = credential.createScoped(Arrays.asList("https://www.googleapis.com/auth/cloud-platform"));
        }
        return new Compute.Builder(httpTransport, jsonFactory, credential)
                .setApplicationName("Google-ComputeSample/0.1")
                .build();
    }
}
