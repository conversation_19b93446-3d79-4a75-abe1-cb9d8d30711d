package seoclarity.backend.utils;

import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.NumberTool;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.ui.velocity.VelocityEngineUtils;
import org.springframework.util.Assert;

import seoclarity.backend.entity.AgencyInfoEntity;

/**
 * 
 * com.actonia.subserver.utils.EmailSenderComponent.java
 *
 * @version $Revision: 116591 $
 *          $Author: limt@SHINETECHCHINA $
 */
@Deprecated
//@Component
public class EmailSenderComponent {
	
//	public static final Log logger = LogFactory.getLog(EmailSenderComponent.class);
//
//	public static final String AGENCY_INFO_KEY = "agencyInfo";
//	private static final String DEFAULT_ALERT_CSS_PARAM = "width:700px; word-break: break-word;";
//
//	//by cee
//	public enum UniqueCode {
//		DailyAlertReportBackend("DARB"),
//		FilterAlertReportEmail("FARE");
//
//		private final String prefix;
//
//		UniqueCode(String prefix) {
//			this.prefix = prefix;
//		}
//
//		public String getPrefix() {
//			return this.prefix;
//		}
//
//		public String getUniCode() {
//			String mmdd = FormatUtils.formatDate(new Date(), "MMdd");
//			return "#" + this.prefix + mmdd;
//		}
//	}
//
//	private String from;
//
//	private String replyTo;
//
//	private VelocityEngine velocityEngine;
//
//	private JavaMailSender javaMailSender;
//
//	public static EmailSenderComponent emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
//
//
//	static final String SERVER_HOST_NAME;
//	static {
//		String hostName = null;
//		try {
//			InetAddress addr = InetAddress.getLocalHost();
//			hostName = addr.getHostName();
//
//			hostName = StringUtils.removeStartIgnoreCase(hostName, "http://");
//			hostName = StringUtils.replace(hostName, ".", "");
//
//		} catch (UnknownHostException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		SERVER_HOST_NAME = hostName;
//	}
//
//	/**
//	 * Send plain text email to somebody
//	 * @param sendTo
//	 * @param mailSubject
//	 * @param templateName
//	 * @param templateModel
//	 * @return
//	 */
//	public SimpleMailMessage sendEmail(String sendTo, String mailSubject, String templateName, Map templateModel) {
//		Assert.notNull(templateName);
//		Assert.notNull(sendTo);
//
//		SimpleMailMessage simpleMessage = new SimpleMailMessage();
//        simpleMessage.setTo(sendTo);
//        simpleMessage.setSubject(mailSubject);
//
//        simpleMessage.setText(getContentByTemplate(templateName, templateModel));
//
//        simpleMessage.setSentDate(new Date());
//        simpleMessage.setFrom(from);
//        simpleMessage.setReplyTo(replyTo);
//        javaMailSender.send(simpleMessage);
//
//		return simpleMessage;
//	}
//
//	public MimeMessage sendMimeMail(String sendTo, String mailSubject, String templateName, Map templateModel) {
//		Assert.notNull(templateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage);
//		try {
//			mimeMessageHelper.setTo(sendTo);
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//			mimeMessageHelper.setText(getContentByTemplate(templateName, templateModel), true);
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMail(String sendTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailAndBcc(String sendTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		Properties properties = System.getProperties();
//		properties.setProperty("mail.smtp.host", "smtp.zeptomail.com");
//		properties.put("mail.smtp.port", "587");
//		properties.put("mail.smtp.auth", "true");
//		properties.put("mail.smtp.starttls.enable", "true");
//		properties.put("mail.smtp.from", "fromaddress");
//		properties.put("mail.smtp.ssl.protocols", "TLSv1.2");
//		Session session = Session.getDefaultInstance(properties);
//
//		MimeMessage mimeMessage = new MimeMessage(session);
//		MimeMessageHelper mimeMessageHelper = null;
//
//		setCommonParams(templateModel);
//
////		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
////		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setFrom(new InternetAddress("<EMAIL>"));
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
////			javaMailSender.send(mimeMessage);
//
//			Transport transport = session.getTransport("smtp");
//			transport.connect("smtp.zeptomail.com", 587, "emailapikey", "wSsVR60i+kT3C6Z7yTWvdepqylsBAVn3HUx93Van7X/4S6uX9Mc4lkKdBlT1TfcfRTNpFDsbrO0skR0Bg2IHiNV/ylhSDiiF9mqRe1U4J3x17qnvhDzNXWxemhWJKY4Jxg9snmBkFsEi+g==");
//			transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
//			transport.close();
//			System.out.println("Mail successfully sent");
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailAndBcc(String sendTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel, AgencyInfoEntity agencyInfo) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailAndBcc(String[] sendTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel, AgencyInfoEntity agencyInfo) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMail(String sendTo, String[] ccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//			if (ccTo != null && ccTo.length > 0) {
//				logger.error("and CC to ");
//				for (String ccEmail : ccTo) {
//					logger.error(ccEmail + "; ");
//				}
//			}
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMail(String sendTo, String[] ccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel, AgencyInfoEntity agencyInfo) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//			if (ccTo != null && ccTo.length > 0) {
//				logger.error("and CC to ");
//				for (String ccEmail : ccTo) {
//					logger.error(ccEmail + "; ");
//				}
//			}
//		}
//		return null;
//	}
//
//	// https://www.wrike.com/open.htm?id=46629102	TODO -- by floyd
//	public MimeMessage sendMimeMultiPartMailAndBcc(String[] sendTo, String[] ccTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailAndBcc(String[] sendTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	//https://www.wrike.com/open.htm?id=30002654
//	//by cee
//	public MimeMessage sendMimeMultiPartMailAndBccWithNickname(String nickName, String[] sendTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
//			if (StringUtils.isNotBlank(nickName)) {
//				try {
//					nickName = javax.mail.internet.MimeUtility.encodeText(nickName);
//		        } catch (Exception e) {
//		            e.printStackTrace();
//		        }
//				mimeMessageHelper.setFrom(new InternetAddress(nickName + " <" + from + ">"));
//			} else {
//				mimeMessageHelper.setFrom(from);
//			}
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailAndBccWithNickname(String nickName, String[] sendTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel, AgencyInfoEntity agencyInfo) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		Properties properties = System.getProperties();
//		properties.setProperty("mail.smtp.host", "smtp.zeptomail.com");
//		properties.put("mail.smtp.port", "587");
//		properties.put("mail.smtp.auth", "true");
//		properties.put("mail.smtp.starttls.enable", "true");
//		properties.put("mail.smtp.from", "fromaddress");
//		properties.put("mail.smtp.ssl.protocols", "TLSv1.2");
//		Session session = Session.getDefaultInstance(properties);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = new MimeMessage(session);
//		MimeMessageHelper mimeMessageHelper = null;
//
////		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
////		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setFrom(new InternetAddress("<EMAIL>"));
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
////			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
////			javaMailSender.send(mimeMessage);
//			Transport transport = session.getTransport("smtp");
//			transport.connect("smtp.zeptomail.com", 587, "emailapikey", "wSsVR60i+kT3C6Z7yTWvdepqylsBAVn3HUx93Van7X/4S6uX9Mc4lkKdBlT1TfcfRTNpFDsbrO0skR0Bg2IHiNV/ylhSDiiF9mqRe1U4J3x17qnvhDzNXWxemhWJKY4Jxg9snmBkFsEi+g==");
//			transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
//			transport.close();
//			System.out.println("Mail successfully sent");
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailAndBccWithNickname(String nickName, String sendTo, String[] ccTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName, Map templateModel, AgencyInfoEntity agencyInfo) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMail(
//			String sendTo,
//			String[] ccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	//https://www.wrike.com/open.htm?id=6422890
//	//by sunny
//	public MimeMessage sendMimeMultiPartMail(
//			String sendTo,
//			String[] ccTo,
//			String[] bccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMail(
//			String sendTo,
//			String[] ccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file,
//			AgencyInfoEntity agencyInfo) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//
//	public MimeMessage sendMimeMultiPartMail(
//			String sendTo,
//			String[] ccTo,
//			String[] bccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String[] attachmentFilenames,
//			File[] files,
//			AgencyInfoEntity agencyInfo) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (ArrayUtils.isNotEmpty(files)) {
//
//				for (int i = 0; i < files.length; i++) {
//
//					String attachmentFilename = "attachment";
//					if (ArrayUtils.isNotEmpty(attachmentFilenames)) {
//						attachmentFilename = attachmentFilenames[i];
//					}
//					mimeMessageHelper.addAttachment(attachmentFilename, files[i]);
//				}
//
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//
//	public MimeMessage sendMimeMultiPartMail(
//			String[] sendTo,
//			String[] ccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file,
//			AgencyInfoEntity agencyInfo) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailWithMultiAttachment(
//			String[] sendTo,
//			String[] ccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			List<File> attachmentFileList) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (attachmentFileList != null && !attachmentFileList.isEmpty()) {
//
//				for (File attachmentFile : attachmentFileList) {
//
//					if(attachmentFile != null) {
//						String attachmentFilename = attachmentFile.getName();
//						mimeMessageHelper.addAttachment(attachmentFilename, attachmentFile);
//					}
//
//				}
//
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailWithBCC(
//			String sendTo,
//			String[] bccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailWithBCCWithNickName(
//			String nickName,
//			String sendTo,
//			String[] bccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file,
//			AgencyInfoEntity agencyInfo) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
////			mimeMessageHelper.setFrom(from);
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public String getContentByTemplate(String templateName, Map templateModel) {
//		if (templateModel != null) {
//			//velocity tools
//			//$!dateFormatter.format('yyyy-M-d', ${currDate})
//			templateModel.put("dateFormatter", new DateTool());
//			//$!numberFormatter.format('integer', ${aint})
//			//$!numberFormatter.format('number', ${afloat})
//			templateModel.put("numberFormatter", new NumberTool());
//		}
//		return VelocityEngineUtils.mergeTemplateIntoString(velocityEngine, templateName, templateModel);
////		return VelocityEngineUtils.mergeTemplateIntoString(velocityEngine, templateName, "UTF-8", templateModel);
//	}
//
//	private void setCommonParams(Map templateModel) {
//		if (templateModel != null) {
//			templateModel.put("hostName", SERVER_HOST_NAME);
//		}
//	}
//
//	//https://www.wrike.com/open.htm?id=22650274
//	//by cee
//	public static String formatSubjectUtf8(String subject) {
//		subject = new String(Base64.encodeBase64(subject.getBytes()));
//		return "=?UTF8?B?" + subject + "?=";
////		return "=?GB2312?B?" + subject + "?=";
//	}
//
//	public JavaMailSender getJavaMailSender() {
//		return javaMailSender;
//	}
//
//	public void setJavaMailSender(JavaMailSender javaMailSender) {
//		this.javaMailSender = javaMailSender;
//	}
//
//	public String getFrom() {
//		return from;
//	}
//
//	public void setFrom(String from) {
//		this.from = from;
//	}
//
//	public String getReplyTo() {
//		return replyTo;
//	}
//
//	public void setReplyTo(String replyTo) {
//		this.replyTo = replyTo;
//	}
//
//	public VelocityEngine getVelocityEngine() {
//		return velocityEngine;
//	}
//
//	public void setVelocityEngine(VelocityEngine velocityEngine) {
//		this.velocityEngine = velocityEngine;
//	}
//
//	public MimeMessage sendMimeMultiPartMailWithBCC(
//			String[] sendTo,
//			String[] bccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//			mimeMessageHelper.setFrom(from);
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	//Cee - https://www.wrike.com/open.htm?id=59034165
//	private void populateEmailFromReplyto(MimeMessageHelper mimeMessageHelper, AgencyInfoEntity agencyInfo) throws MessagingException {
//		if (agencyInfo != null) {
//			mimeMessageHelper.setReplyTo(agencyInfo.getEmailReplyto());
//			if (StringUtils.isNotBlank(agencyInfo.getEmailNickname())) {
//				String senderNickname = null;
//				try {
//					senderNickname = javax.mail.internet.MimeUtility.encodeText(agencyInfo.getEmailNickname());
//		        } catch (Exception e) {
//		            e.printStackTrace();
//		        }
//				mimeMessageHelper.setFrom(new InternetAddress(senderNickname + " <" + agencyInfo.getEmailFrom() + ">"));
//			} else {
//				mimeMessageHelper.setFrom(agencyInfo.getEmailFrom());
//			}
//		} else {
//			mimeMessageHelper.setReplyTo(replyTo);
//			mimeMessageHelper.setFrom(from);
//		}
//	}
//
//	private void populateAgencyInfoToMap(Map templateModel, AgencyInfoEntity agencyInfo) {
//		if (templateModel == null) {
//			templateModel = new HashMap();
//		}
//		templateModel.put(AGENCY_INFO_KEY, agencyInfo);
//		templateModel.put("DEFAULT_ALERT_CSS_PARAM", DEFAULT_ALERT_CSS_PARAM);
//	}
//
//	public MimeMessage sendMimeMultiPartMailWithBCC(
//			String[] sendTo,
//			String[] bccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file,
//			AgencyInfoEntity agencyInfo) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
//			//Cee - https://www.wrike.com/open.htm?id=59034165
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailWithCC(
//			String[] sendTo,
//			String[] ccTo,
//			String mailSubject,
//			String plainTextTemplateName,
//			String htmlTemplateName,
//			Map templateModel,
//			String attachmentFilename,
//			File file,
//			AgencyInfoEntity agencyInfo) {
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (ccTo != null && ccTo.length > 0) {
//				mimeMessageHelper.setCc(ccTo);
//			}
//
//			//Cee - https://www.wrike.com/open.htm?id=59034165
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailAndBccAttachment(String[] sendTo, String[] bccTo, String mailSubject, String plainTextTemplateName, String htmlTemplateName,
//															 Map templateModel, String attachmentFilename, File file, AgencyInfoEntity agencyInfo) {
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			if (file != null) {
//				if (StringUtils.isBlank(attachmentFilename)) {
//					attachmentFilename = "attachment";
//				}
//				mimeMessageHelper.addAttachment(attachmentFilename, file);
//			}
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//
//	public static void sendEmailReport(String userName, Date startTime, String subject, List<String> infos, String errorMsg, String emailTo) {
//		try {
//			Map<String, Object> reportMap = new HashMap<>();
//			reportMap.put("userName", userName);
//			reportMap.put("dateString", DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
//			reportMap.put("startTime", DateFormatUtils.format(startTime, "MM/dd/yyyy HH:mm:ss"));
//			reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
//			reportMap.put("info", infos);
//			reportMap.put("title", subject);
//
//			if (StringUtils.isEmpty(errorMsg)) {
//				reportMap.put("errormessage", "");
//			} else {
//				reportMap.put("errormessage", errorMsg);
//			}
//			// String emailTo = "<EMAIL>";
//			emailSenderComponent.sendMimeMultiPartMail(emailTo, null, subject, "mail_common.txt", "mail_common.html", reportMap);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//
//	public static void sendEmailReport(Date startTime, String subject, String message, String errorMsg) {
//		String emailTo = "<EMAIL>";
//		String[] ccTo = new String[] {"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
//		sendEmailReport(startTime, subject, message, errorMsg, emailTo, ccTo);
//	}
//
//	public static void sendEmailReport(Date startTime, String subject, String message, String errorMsg, String emailTo, String[] ccTo) {
//		try {
//			Map<String, Object> reportMap = new HashMap<String, Object>();
//			reportMap.put("userName", "Mitul");
//			reportMap.put("dateString", DateFormatUtils.format(startTime, "MM/dd/yyyy"));
//			reportMap.put("startTime", DateFormatUtils.format(startTime, "MM/dd/yyyy HH:mm:ss"));
//			reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
//
//			List<String> infos = new ArrayList<String>();
//			infos.add(message);
//			reportMap.put("info", infos);
//			reportMap.put("title", subject);
//			if (StringUtils.isEmpty(errorMsg)) {
//				reportMap.put("errormessage", "");
//			} else {
//				reportMap.put("errormessage", errorMsg);
//			}
//
//			emailSenderComponent.sendMimeMultiPartMail(emailTo, ccTo, subject, "mail_common.txt", "mail_common.html", reportMap);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//
//	// https://www.wrike.com/open.htm?id=521118052
//	// by Meo
//	public MimeMessage sendMimeMultiPartMailAndBccForRankingAlert(String sendTo, String[] bccTo, String mailSubject,
//			Map templateModel, AgencyInfoEntity agencyInfo) {
//		String plainTextTemplateName = "mail_customer_ranking_alert.txt";
//		String htmlTemplateName = "mail_customer_ranking_alert.html";
//
//		Assert.notNull(plainTextTemplateName);
//		Assert.notNull(htmlTemplateName);
//		Assert.notNull(sendTo);
//
//		setCommonParams(templateModel);
//
//		MimeMessage mimeMessage = javaMailSender.createMimeMessage();
//		MimeMessageHelper mimeMessageHelper = null;
//		try {
//			mimeMessageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
//			mimeMessageHelper.setTo(sendTo);
//			if (bccTo != null && bccTo.length > 0) {
//				mimeMessageHelper.setBcc(bccTo);
//			}
//
//			populateEmailFromReplyto(mimeMessageHelper, agencyInfo);
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//
//			mimeMessageHelper.setSubject(mailSubject);
//			mimeMessageHelper.setSentDate(new Date());
//
//			mimeMessageHelper.setText(
//					getContentByTemplate(plainTextTemplateName, templateModel),
//					getContentByTemplate(htmlTemplateName, templateModel));
//
//			javaMailSender.send(mimeMessage);
//			return mimeMessage;
//
//		} catch (MessagingException e) {
//			logger.error("Send Email to " + sendTo + " Error." + e.getMessage());
//		}
//		return null;
//	}
//
//	public MimeMessage sendMimeMultiPartMailAndBccForRankingAlertTest(String sendTo, String[] bccTo, String mailSubject,
//			Map templateModel, AgencyInfoEntity agencyInfo) {
//		String plainTextTemplateName = "mail_customer_ranking_alert.txt";
//		String htmlTemplateName = "mail_customer_ranking_alert.html";
//
//		setCommonParams(templateModel);
//
//			populateAgencyInfoToMap(templateModel, agencyInfo);
//			getContentByTemplate(plainTextTemplateName, templateModel);
//			System.out.println(getContentByTemplate(htmlTemplateName, templateModel));
//
//
//		return null;
//	}


}
