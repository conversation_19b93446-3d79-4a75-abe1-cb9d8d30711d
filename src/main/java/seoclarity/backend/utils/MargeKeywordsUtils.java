package seoclarity.backend.utils;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;

public class MargeKeywordsUtils {

    private static final String SPLIT = "\t";
    private static String uploadFilePath = "/home/<USER>/source/jason/extract/";
//    private static String uploadFilePath = "D:\\Extract\\marge\\test\\";
    private static String fileName;

    public static void main(String[] args) {

        fileName = args[0];
//        fileName = "111.csv";
        try {
            MargeKeywordsUtils margeKeywordsUtils = new MargeKeywordsUtils();
            margeKeywordsUtils.margeKeywordsByFile(uploadFilePath + fileName);
//            margeKeywordsUtils.margeKeywordsByFile("D:\\Extract\\v13.txt");
//            margeKeywordsUtils.margeKeywordsByFile("D:\\Extract\\marge\\marge1.txt");
//            margeKeywordsUtils.margeKeywordsByFile("D:\\Extract\\marge\\margeKeywords.csv");
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private String margeKeywordsByFile(String filePath) throws Exception {

        Long startTime = System.currentTimeMillis();

        Map<Integer, LinkedHashSet<String>> processMap = new HashMap<>();

        LinkedHashSet<String> lineSet = new LinkedHashSet<>();
        LinkedHashSet<String> result = new LinkedHashSet<>();
        try {
            File file = new File(filePath);
            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(file));
            BufferedReader bf = new BufferedReader(inputReader);
            String str;
            int mapCount = 0;
            while ((str = bf.readLine()) != null) {

                String[] keywords = str.split(SPLIT);
//                lineSet.add(keywords[1]);
                lineSet.add(str);
//                System.out.println("------ line : " + JSON.toJSONString(keywords));
            }

            bf.close();
            inputReader.close();

        } catch (IOException ex) {
            ex.printStackTrace();
        }
//        System.out.println("------ processMap : " + JSON.toJSONString(processMap));

        Map<String, Integer> keywordCountMap = separateKeywords(lineSet);

        Map<String, Set<String>> resultMap = getNeedMargeKeywords(lineSet, keywordCountMap);
        System.out.println(" ############ getNeedMargeKeywords es Time :  " + (System.currentTimeMillis() - startTime) / 1000 + " s ");

        Set<String> margedKeywords = resultMap.get("margedKeywords");

        LinkedHashSet<String> startProcessSet = new LinkedHashSet<>();
        startProcessSet.addAll(margedKeywords);
        int count = margedKeywords.size();
        while (true) {

            System.out.println("------ startProcessSet size: " + startProcessSet.size());
            startProcessSet = processMarge(startProcessSet);
//            System.out.println("------ startProcessSet : " + JSON.toJSONString(startProcessSet));

            if (startProcessSet.size() == count) {
                result.addAll(resultMap.get("noMargedKeywords"));
                break;
            } else {

                System.out.println("------ end ProcessSet size : " + startProcessSet.size());

                result.addAll(resultMap.get("noMargedKeywords"));
//                result.addAll(startProcessSet);

                keywordCountMap = separateKeywords(startProcessSet);
                resultMap = getNeedMargeKeywords(startProcessSet, keywordCountMap);

                System.out.println("------ resultMap.get(\"noMargedKeywords\") size : " + resultMap.get("noMargedKeywords").size());
                System.out.println("------ resultMap.get(\"margedKeywords\") size : " + resultMap.get("margedKeywords").size());



                if (resultMap.get("margedKeywords").size() == 0) {
                    break;
                }
//
                margedKeywords = resultMap.get("margedKeywords");
                startProcessSet.clear();
                startProcessSet.addAll(margedKeywords);
                count = startProcessSet.size();
            }

        }

        result.addAll(startProcessSet);
//        result.addAll(resultMap.get("noMargedKeywords"));

//        System.out.println("------ result : " + JSON.toJSONString(result));
        System.out.println("------ result size : " + result.size());

        Long endTime = System.currentTimeMillis();
        System.out.println(" ############ es Time :  " + (endTime - startTime) / 1000 + " s ");

        List<String> extractList = new ArrayList<>();
        extractList.addAll(result);
        File outFile = new File(uploadFilePath + "marge_" + fileName);
        if (outFile.exists()) {
            outFile.delete();
        }
        FileUtils.writeLines(outFile, "UTF-8", extractList, true);

        return null;
    }

    /**
     * separate Keywords by frequency of occurrence
     *
     * @param needMargeSet
     * @return
     */
    private Map<String, Integer> separateKeywords(LinkedHashSet<String> needMargeSet) {

        Map<String, Integer> keywordCountMap = new TreeMap<String, Integer>();

        int index = 0;
        Iterator<String> it = needMargeSet.iterator();
        while (it.hasNext()) {

//            System.out.println("===separateKeywords : " + index);
            String keywordJson = it.next();

            if (StringUtils.isEmpty(keywordJson)) {
                it.remove();
                continue;
            }

            List list = JSON.parseArray(keywordJson);
            for (int i = 0; i < list.size(); i++) {
                String keyword = list.get(i).toString();

                if (!keywordCountMap.containsKey(keyword)) {
                    keywordCountMap.put(keyword, 1);
                } else {
                    Integer count = keywordCountMap.get(keyword) + 1;
                    keywordCountMap.put(keyword, count);
                }

            }
            index++;
        }
//        System.out.println("1111111 keywordCountMap " + JSON.toJSONString(keywordCountMap));
        Map<String, Integer> resultMap = sortByValueDescending(keywordCountMap);
//        System.out.println("2222222 map " + JSON.toJSONString(map));

        return resultMap;
    }

    //sort map value desc
    public static <K, V extends Comparable<? super V>> Map<K, V> sortByValueDescending(Map<K, V> map) {
        List<Map.Entry<K, V>> list = new LinkedList<Map.Entry<K, V>>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<K, V>>() {
            @Override
            public int compare(Map.Entry<K, V> o1, Map.Entry<K, V> o2) {
                int compare = (o1.getValue()).compareTo(o2.getValue());
                return -compare;
            }
        });

        Map<K, V> result = new LinkedHashMap<K, V>();
        for (Map.Entry<K, V> entry : list) {
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }

    public Map<String, Set<String>> getNeedMargeKeywords(LinkedHashSet<String> needMargeSet, Map<String, Integer> keywordCountMap) {
        Map<String, Set<String>> resultMap = new HashMap<>();

        Set<String> noMargedKeywords = new HashSet<>();
        Set<String> margedKeywords = new HashSet<>();
        int index = 0;
        System.out.println("======keywordCountMap size : " + keywordCountMap.size());
        for (String keyword : keywordCountMap.keySet()) {
            System.out.println("===getNeedMargeKeywords : " + index);
            for (String keywordJson : needMargeSet) {

                if (keywordJson.contains(keyword)) {
                    if (keywordCountMap.get(keyword) > 1 && !noMargedKeywords.contains(keywordJson)) {
                        margedKeywords.add(keywordJson);
                    } else if (keywordCountMap.get(keyword) <= 1 && !margedKeywords.contains(keywordJson)) {

                        List list = JSON.parseArray(keywordJson);
                        Set<String> linedSet = new HashSet<>();
                        for (int i = 0; i < list.size(); i++) {
                            String formatKeyword = list.get(i).toString();
                            linedSet.add(formatKeyword);
                        }
                        noMargedKeywords.add(JSON.toJSONString(linedSet));
                    }
                }
            }
            index++;
        }

//        System.out.println("3333333333 noMargedKeywords : " + JSON.toJSONString(noMargedKeywords));
//        System.out.println("4444444444 margedKeywords : " + JSON.toJSONString(margedKeywords));

        resultMap.put("noMargedKeywords", noMargedKeywords);
        resultMap.put("margedKeywords", margedKeywords);
        return resultMap;
    }

    private LinkedHashSet<String> margeList(Set<String> rest) {
//        List<String> result = new ArrayList<>();

        LinkedHashSet<String> result = new LinkedHashSet<>();

        Set<String> keywordSet = new HashSet<>();
        Set<String> remainSet = new HashSet<>();


        Iterator<String> it = rest.iterator();
        while (it.hasNext()) {

            String gsonStr = it.next();

            if (StringUtils.isEmpty(gsonStr)) {
                it.remove();
                continue;
            }
            Set<String> linedSet = new HashSet<>();

            List list = JSON.parseArray(gsonStr);

            for (int i = 0; i < list.size(); i++) {
                String keyword = list.get(i).toString();
                linedSet.add(keyword);
            }

            remainSet.retainAll(linedSet);


            if (keywordSet.size() == 0 || remainSet.size() > 0) {
                keywordSet.addAll(linedSet);
                it.remove();
            } else {
                result.add(JSON.toJSONString(linedSet));
            }

            remainSet.addAll(keywordSet);
        }

        result.add(JSON.toJSONString(keywordSet));

        return result;
    }

    private LinkedHashSet<String> processMarge(LinkedHashSet<String> lineList) {

        LinkedHashSet<String> result = margeList(lineList);
//        System.out.println("00000000000 result : " + JSON.toJSONString(result));
        System.out.println("00000000000 result size : " + result.size());

        LinkedHashSet<String> roundSet = new LinkedHashSet<>();
        roundSet.addAll(result);

        int resultSize = result.size();
        int retryCount = 0;
        int RoundCount = 0;
        int start = 0;
        while (true) {

            start++;
            result = margeList(result);

            if (result.size() == resultSize) {
                try {
                    if (RoundCount <= result.size()) {

                        Iterator<String> it = result.iterator();
                        while (it.hasNext()) {
                            String str = it.next();
//                            System.out.println("First : " + str);
                            it.remove();
                            break;
                        }
                        result.addAll(roundSet);
                        RoundCount++;
                        System.out.println(" 888888888 RoundCount: " + RoundCount);
                    } else {

                        retryCount++;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            } else {
                resultSize = result.size();
                roundSet.clear();
                roundSet.addAll(result);
            }

            if (result.size() == 1 || retryCount == 3) {
                break;
            }

        }

        return result;
    }

}
