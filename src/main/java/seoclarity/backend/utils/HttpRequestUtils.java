package seoclarity.backend.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.config.SocketConfig;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.util.Assert;

import com.google.gson.Gson;

import seoclarity.backend.entity.Message;

public class HttpRequestUtils {

    public static final Gson gson = new Gson();

    public static enum WordPressEndpoint {
        posts,
        categories,
        tags,
        pages,
        users,
    }


    public static String queryWebServiceFunctionByMethod(String webServiceUrl, String method, Map<String, String> paramMap) {

        Assert.hasText(webServiceUrl);

        System.out.println("API-GET: " + webServiceUrl + ", Method: " + method);

        HttpURLConnection conn = null;
        InputStream inputStream = null;
        try {
            URL url = new URL(webServiceUrl);

            conn = (HttpURLConnection) url.openConnection();

            conn.setRequestMethod(method);
            conn.setConnectTimeout(290000);
            conn.setReadTimeout(290000);
            conn.setInstanceFollowRedirects(false);
            conn.setRequestProperty("contentType", "UTF-8");
            conn.setRequestProperty("acceptCharset", "UTF-8");
            conn.setRequestProperty("Accept-Charset", "UTF-8");

            if (MapUtils.isNotEmpty(paramMap)) {
                for (String key : paramMap.keySet()) {
                    conn.setRequestProperty(key, paramMap.get(key));
                }
            }

            inputStream = conn.getInputStream();
            String xml = IOUtils.toString(inputStream, "UTF-8");

            return xml;

        } catch (MalformedURLException e) {
            System.out.println("API-GET Init URL error. " + webServiceUrl);
            e.printStackTrace();
        } catch (IOException e) {
            System.out.println("API-GET Open URL error. " + webServiceUrl);
            e.printStackTrace();

            Message message = new Message();
            message.setTitle("API-ERROR:Connection failed");
            message.addError(e.getLocalizedMessage());
            return gson.toJson(message);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (conn != null) {
                conn.disconnect();
                conn = null;
            }
        }
        return null;
    }


    public static HttpPost postStringBody(String url, Map<String, String> header,
                                          String stringBody, String userName, String password) {

        String authentication = base64Encode(userName, password);
        if (StringUtils.isNotBlank(authentication)) {
            header.put("Authorization", "Basic " + authentication);
        } else {
            System.out.println("Unauthorization was found!!!");
        }

        if (StringUtils.isBlank(header.get("Content-Type"))) {
            //default set content-type to be application/x-www-form-urlencoded if it's emtyp
            header.put("Content-Type", "application/x-www-form-urlencoded");
        }

        HttpPost post = new HttpPost(url);
        for (String key : header.keySet()) {
            post.addHeader(key, header.get(key));
        }

        try {
            StringEntity str = new StringEntity(stringBody, "UTF-8");
            post.setEntity(str);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return post;
    }

    public static String base64Encode(String userName, String password) {

        try {
            Base64 base64 = new Base64();
            String encodedText = base64.encodeToString((userName + ":" + password).getBytes());
            return encodedText;
        } catch (Exception e) {
            // TODO: handle exception
        }

        return "";

    }


    public static HttpPost postStringBody(String url, Map<String, String> header, String stringBody, List<NameValuePair> paramMap) {
        HttpPost post = new HttpPost(url);
        for (String key : header.keySet()) {
            post.addHeader(key, header.get(key));
        }

        try {

            if (StringUtils.isNotBlank(stringBody)) {
                StringEntity str = new StringEntity(stringBody, "UTF-8");
                post.setEntity(str);
            } else if (CollectionUtils.isNotEmpty(paramMap)) {
                post.setEntity(new UrlEncodedFormEntity(paramMap, "UTF-8"));
            }

//        	System.out.println("param:" + gson.toJson(post.getEntity()));

        } catch (Exception e) {
            e.printStackTrace();
        }

        return post;
    }


    private static String queryWebServiceByPost(HttpPost post) {

        if (post == null) {
            return "";
        }

        InputStream inputStream = null;
        HttpResponse response = null;
        HttpClient client = HttpClientBuilder.create().build();
        try {
            response = client.execute(post);

            int responseCode = response.getStatusLine().getStatusCode();
//            System.out.println("API-POST resp code:" + responseCode);

            inputStream = response.getEntity().getContent();
            String html = IOUtils.toString(inputStream, "UTF-8");
            if (StringUtils.isNotBlank(html)) {
                return html;
            }

            if (responseCode == 404) {
                System.out.println("API-POST RespCode : " + responseCode + "; (No Data)");
                return "";
            }


        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("API-POST: " + e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (post != null) {
                post.releaseConnection();
            }
            if (response != null) {
                HttpClientUtils.closeQuietly(response);
            }
            if (client != null) {
                HttpClientUtils.closeQuietly(client);
            }
        }

        return "";
    }


    private static Map<String, String> queryWebServiceByPostMap(
            HttpPost post) {

        Map<String, String> resultMap = new HashMap<>();

        if (post == null) {
            return null;
        }

        InputStream inputStream = null;
        HttpResponse response = null;
        HttpClient client = HttpClientBuilder.create().build();
        try {
            response = client.execute(post);

            int responseCode = response.getStatusLine().getStatusCode();
            resultMap.put("responseCode", responseCode + "");
//			 System.out.println("API-POST resp code:" + responseCode);

            inputStream = response.getEntity().getContent();
            String html = IOUtils.toString(inputStream, "UTF-8");
            if (responseCode == 404) {
                System.out.println("API-POST RespCode : " + responseCode + "; (No Data)");
            }

            resultMap.put("response", html);
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("API-POST: " + e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (post != null) {
                post.releaseConnection();
            }
            if (response != null) {
                HttpClientUtils.closeQuietly(response);
            }
            if (client != null) {
                HttpClientUtils.closeQuietly(client);
            }
        }

        return null;
    }

    private static HttpPost postDate(String url, Map<String, String> params, Map<String, String> header) {
        HttpPost post = new HttpPost(url);
        for (String key : header.keySet()) {
            post.addHeader(key, header.get(key));
        }
        List<NameValuePair> paramList = new ArrayList<NameValuePair>();
        for (String key : params.keySet()) {
            paramList.add(new BasicNameValuePair(key, params.get(key)));
        }
        try {
            post.setEntity(new UrlEncodedFormEntity(paramList, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return post;
    }

    public static String queryWebServiceFunctionPost(String webServiceUrl, String stringBody, Map<String, String> headerMap, int maxTryCnt, int timeout) {
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");

        if (headerMap != null && CollectionUtils.isNotEmpty(headerMap.keySet())) {
            for (String key : headerMap.keySet()) {
                header.put(key, headerMap.get(key));
            }
        }
        System.out.println("API-POST: " + webServiceUrl);
        System.out.println("API-POST Headers:" + header);
        System.out.println("API-POST Body: " + stringBody);

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(timeout).setConnectTimeout(timeout).setConnectionRequestTimeout(timeout).build();
        SocketConfig socketConfig = SocketConfig.custom().setSoTimeout(timeout).build();

        HttpPost post = postStringBody(webServiceUrl, header, stringBody, new ArrayList<>(0));
        post.setConfig(requestConfig);

        InputStream inputStream = null;
        HttpResponse response = null;
        HttpClient client = HttpClientBuilder.create().setDefaultRequestConfig(requestConfig).setDefaultSocketConfig(socketConfig).build();

        int tryCnt = 0;
        while(tryCnt <= maxTryCnt) {
            tryCnt++;
            try {
                response = client.execute(post);
                int responseCode = response.getStatusLine().getStatusCode();
                inputStream = response.getEntity().getContent();
                String responseStr = IOUtils.toString(inputStream, "UTF-8");
                if (responseCode >= 200 && responseCode < 300) {
                    return responseStr;
                } else {
                    System.out.println("API-POST FAILED, responseCode: " + responseCode + ", response:" + responseStr);
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("API-POST FAILED: " + e.getMessage());
            }
        }
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        post.releaseConnection();
        if (response != null) {
            HttpClientUtils.closeQuietly(response);
        }
        if (client != null) {
            HttpClientUtils.closeQuietly(client);
        }
        return "";
    }

    public static String queryWebServiceFunctionPost(String apiURL, Map<String, String> expandHeader, Map<String, String> params) {
        if (params == null) {
            params = new HashMap<String, String>();
        }
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        if (expandHeader != null) {
            header.putAll(expandHeader);
        }

        System.out.println("API-POST: " + apiURL);

        HttpPost post = postDate(apiURL, params, header);

        return queryWebServiceByPost(post);
    }

	// https://www.wrike.com/open.htm?id=1126922403
	public static String queryWebServiceFunctionPostWithJsonParams(String webServiceUrl, String stringBody, Map<String, String> expandHeader) {
		Map<String, String> headers = new HashMap<>();
		headers.put("Content-Type", "application/json");
		if (expandHeader != null && expandHeader.size() > 0) {
			headers.putAll(expandHeader);
		}
		return queryWebServiceFunctionPost(webServiceUrl, stringBody, null, headers);
	}

    public static String queryWebServiceFunctionPost(
            String webServiceUrl,
            String stringBody,
            List<NameValuePair> paramMap,
            Map<String, String> headerMap) {

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");

        if (headerMap != null && CollectionUtils.isNotEmpty(headerMap.keySet())) {
            for (String key : headerMap.keySet()) {
                header.put(key, headerMap.get(key));
            }
        }

        System.out.println("API-POST: " + webServiceUrl);
        System.out.println("API-POST Headers:" + header);
        System.out.println("API-POST Body: " + stringBody);

        HttpPost post = postStringBody(webServiceUrl, header, stringBody, paramMap);

        return queryWebServiceByPost(post);
    }

    public static Map<String, String> queryWebServiceFunctionPostMap(
            String webServiceUrl,
            String stringBody,
            List<NameValuePair> paramMap,
            Map<String, String> headerMap) {

        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");

        if (headerMap != null && CollectionUtils.isNotEmpty(headerMap.keySet())) {
            for (String key : headerMap.keySet()) {
                header.put(key, headerMap.get(key));
            }
        }

//        System.out.println("API-POST: " + webServiceUrl);
//        System.out.println("API-POST Headers:" + header);
//        System.out.println("API-POST Body: " + stringBody);

        HttpPost post = postStringBody(webServiceUrl, header, stringBody, paramMap);

        return queryWebServiceByPostMap(post);
    }

    public static String simpleGet(String url, Map<String, String> paramMap, int timeOut, Map<String, String> headerMap) {
        String resStr = null;
        CloseableHttpClient httpclient = HttpClients.custom().build();
        CloseableHttpResponse response = null;
        int MAX = 3;

        HttpGet get = new HttpGet(url);
        get.setConfig(RequestConfig.custom().setConnectTimeout(timeOut).setSocketTimeout(timeOut).build());
        if (headerMap != null) {
            headerMap.entrySet().forEach(entry -> get.addHeader(entry.getKey(), entry.getValue()));
        }

        try {
            List<NameValuePair> params = new ArrayList<NameValuePair>();
            if (paramMap != null) {
                for (String key : paramMap.keySet()) {
                    params.add(new BasicNameValuePair(key, paramMap.get(key)));
                }
                String param = URLEncodedUtils.format(params, "UTF-8");

                get.setURI(URI.create(url + "?" + param));
            }

            int cnt = 0;
            while (cnt < MAX) {
                cnt++;
                try {
                    response = httpclient.execute(get);
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        InputStream in = entity.getContent();
                        resStr = IOUtils.toString(in, "UTF-8");
                        in.close();
                    }
                    break;
                } catch (Exception e) {
                    System.out.println("===get failed, cnt:" + cnt + ", url:" + url + ", msg:" + e.getMessage());
                    if (cnt >= MAX) {
                        e.printStackTrace();
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpclient.close();
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }

        return resStr;
    }

    public static String simpleGetV2(String url, Map<String, String> paramMap, int timeOut, Map<String, String> headerMap) {
        String resStr = null;
        CloseableHttpClient httpclient = HttpClients.custom().build();
        CloseableHttpResponse response = null;
        int MAX = 3;

        HttpGet get = new HttpGet(url);
        get.setConfig(RequestConfig.custom().setConnectTimeout(timeOut).setSocketTimeout(timeOut).build());
        if (headerMap != null) {
            headerMap.entrySet().forEach(entry -> get.addHeader(entry.getKey(), entry.getValue()));
        }

        try {
            List<NameValuePair> params = new ArrayList<NameValuePair>();
            if (paramMap != null) {
                for (String key : paramMap.keySet()) {
                    params.add(new BasicNameValuePair(key, paramMap.get(key)));
                }
                String param = URLEncodedUtils.format(params, "UTF-8");

                get.setURI(URI.create(url + "?" + param));
            }

            int cnt = 0;
            while (cnt < MAX) {
                cnt++;
                try {
                    response = httpclient.execute(get);
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        InputStream in = entity.getContent();
                        resStr = IOUtils.toString(in, "UTF-8");
                        in.close();

//						System.out.println(get.getURI());

                        if (StringUtils.contains(resStr, "\"error\"") && !StringUtils.contains(resStr, "\"error\":1,")) {

                            if (cnt >= MAX) {
                                System.out.println("Not get data from API! resStr:" + resStr);
                                break;
                            }
                            System.out.println("Retrying, error:" + resStr);
                            Thread.sleep(5 * 1000);
                            continue;
                        }
                    }
                    break;
                } catch (Exception e) {
                    System.out.println("===get failed, cnt:" + cnt + ", url:" + url + ", msg:" + e.getMessage());
                    if (cnt >= MAX) {
                        e.printStackTrace();
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpclient.close();
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }

        return resStr;
    }

    public static String simplePost(String url, Map<String, String> paramMap, int timeOut, Map<String, String> headerMap) throws Exception {
        int TRY_TIME_INTERVAL = 1000;
        int TRY_CNT = 3;
        String resStr = null;
        CloseableHttpClient httpclient = HttpClients.custom().build();
        CloseableHttpResponse response = null;

        HttpPost httppost = new HttpPost(url);

        List<NameValuePair> params = new ArrayList<NameValuePair>();
        for (String key : paramMap.keySet()) {
            params.add(new BasicNameValuePair(key, paramMap.get(key)));
        }
        UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(params, "UTF-8");
        httppost.setEntity(formEntity);
        if (headerMap != null) {
            headerMap.entrySet().forEach(entry -> httppost.addHeader(entry.getKey(), entry.getValue()));
        }

        httppost.setConfig(RequestConfig.custom()
                .setSocketTimeout(timeOut)
                .setConnectTimeout(timeOut)
                .build());

        try {
            int tryCnt = 0;
            do {
                try {
                    response = httpclient.execute(httppost);
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        resStr = EntityUtils.toString(entity, "utf-8");
                    }
                    break;
                } catch (Exception e) {
                    tryCnt++;
                    System.out.println("=Post query failed, tryCnt:" + tryCnt + ", url:" + url + ", msg:" + e.getMessage());
                    if (tryCnt >= TRY_CNT) {
                        e.printStackTrace();
                    } else {
                        Thread.sleep(TRY_TIME_INTERVAL);
                    }
                }
            } while (tryCnt < TRY_CNT);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
        return resStr;
    }

    public static String simpleDelete(String url, int timeOut, Map<String, String> headerMap) throws Exception {
        int TRY_TIME_INTERVAL = 1000;
        int TRY_CNT = 3;
        String resStr = null;
        CloseableHttpClient httpclient = HttpClients.custom().build();
        CloseableHttpResponse response = null;

        HttpDelete httppost = new HttpDelete(url);

        httppost.setConfig(RequestConfig.custom()
                .setSocketTimeout(timeOut)
                .setConnectTimeout(timeOut)
                .build());

        httppost.setConfig(RequestConfig.custom().setConnectTimeout(timeOut).setSocketTimeout(timeOut).build());
        if (headerMap != null) {
            headerMap.entrySet().forEach(entry -> httppost.addHeader(entry.getKey(), entry.getValue()));
        }

        try {
            int tryCnt = 0;
            do {
                try {
                    response = httpclient.execute(httppost);
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        resStr = EntityUtils.toString(entity, "utf-8");
                    }
                    break;
                } catch (Exception e) {
                    tryCnt++;
                    System.out.println("=Post query failed, tryCnt:" + tryCnt + ", url:" + url + ", msg:" + e.getMessage());
                    if (tryCnt >= TRY_CNT) {
                        e.printStackTrace();
                    } else {
                        Thread.sleep(TRY_TIME_INTERVAL);
                    }
                }
            } while (tryCnt < TRY_CNT);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
        return resStr;
    }
    
    
    //tiktok crawler, please dont catch exception inside for this method
	public static String httpByMethod(String webServiceUrl, String requestBody, String accessToken, String method) throws IOException {
		Assert.hasText(webServiceUrl);
		
		System.out.println("API-PUT: " + webServiceUrl);
		System.out.println("RequestMethod : " + method);
		
		//establish connection and push policy to snc controller
        URL url = new URL(webServiceUrl);
        HttpURLConnection conn = (HttpURLConnection)url.openConnection();
    
         conn.setRequestMethod(method);
         conn.setDoInput(true);
         conn.setDoOutput(true);
         conn.setRequestProperty("Authorization", "Bearer " + accessToken);
		
         OutputStream os = conn.getOutputStream();     
         os.write(requestBody.getBytes("UTF-8")); 
         os.flush();
         os.close();         
         BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
         String line = "";
         String result = "";
         while( (line =br.readLine()) != null ){
             result += line;
         }
         br.close();
         conn.disconnect();
         return result;
    }

}
