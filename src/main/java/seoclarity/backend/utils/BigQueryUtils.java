package seoclarity.backend.utils;

import com.alibaba.fastjson.JSON;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.bigquery.Bigquery;
import com.google.api.services.bigquery.model.*;
import lombok.extern.apachecommons.CommonsLog;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * https://www.wrike.com/open.htm?id=522920708
 */
@CommonsLog
public class BigQueryUtils {

    private static final List<String> SCOPE = Arrays.asList("https://www.googleapis.com/auth/bigquery");
    private static final HttpTransport TRANSPORT = new NetHttpTransport();
    private static final JsonFactory JSON_FACTORY = new JacksonFactory();

    private String projectId = "seoclarity-rank-intelligence";
    private String dataSetId = "seoclarity_demo_dataset";
    private String tableId = "test_partition_table_20201211";

    private static Bigquery bigquery;

    private String accountId = "<EMAIL>";

    private String privateKayPath = "/home/<USER>/source/extractScripts/clarity-backend-scripts/HM-seoclarity-rank-intelligence-31f7058161c9.p12";

    private BigQueryUtils() {

    }

    public BigQueryUtils(String projectId, String dataSetId, String tableId, String accountId, String privateKayPath) {
        this.projectId = projectId;
        this.dataSetId = dataSetId;
        this.tableId = tableId;
        this.accountId = accountId;
        this.privateKayPath = privateKayPath;
    }

    public static void main(String[] args) throws Exception {

        String domainId = args[0];
        String AccountId = "<EMAIL>";
        String projectId = "hd-global-analytics-platform";
        String dataSetId = "raw_seo_clarity";
        String tableId = "seoclarity_extract_" + domainId;
        String PRIVATE_KEY_PATH = "/home/<USER>/source/extractScripts/clarity-backend-scripts/hd-global-analytics-platform-02ac70f1ac08.p12";

        BigQueryUtils bigQueryUtils = new BigQueryUtils(projectId, dataSetId, tableId, AccountId, PRIVATE_KEY_PATH);
        String query = "SELECT date,count(1) FROM `hd-global-analytics-platform.raw_seo_clarity." + tableId + "` "; // Your SQL query
        query += " where date >= '2024-03-15' and domain_id =" + domainId;
        query += " group by date order by date";
        System.out.println("====SQL:" + query);
        bigQueryUtils.selectTableData(query);
    }


    public void createTableByRestApi() throws Exception {

        log.info("===projectId:" + projectId + ",dataSetId:" + dataSetId + ",tableId:" + tableId + ",accountId:" + accountId + ",privateKayPath:" + privateKayPath);

        GoogleCredential credential = new GoogleCredential.Builder().setTransport(TRANSPORT)
                .setJsonFactory(JSON_FACTORY)
                .setServiceAccountId(accountId)
                .setServiceAccountScopes(SCOPE)
                .setServiceAccountPrivateKeyFromP12File(new File(privateKayPath)).build();

        bigquery = new Bigquery.Builder(TRANSPORT, JSON_FACTORY, credential).setApplicationName("BigQuery-Service-Accounts/0.1")
                .setHttpRequestInitializer(credential).build();

//        createTable();
        createPartitionedTable();
    }


    public void createTableByRestApiBySchema(TableSchema schema, String tableId) throws Exception {

        log.info("===projectId:" + projectId + ",dataSetId:" + dataSetId + ",tableId:" + tableId + ",accountId:" + accountId + ",privateKayPath:" + privateKayPath);

        GoogleCredential credential = new GoogleCredential.Builder().setTransport(TRANSPORT)
                .setJsonFactory(JSON_FACTORY)
                .setServiceAccountId(accountId)
                .setServiceAccountScopes(SCOPE)
                .setServiceAccountPrivateKeyFromP12File(new File(privateKayPath)).build();

        bigquery = new Bigquery.Builder(TRANSPORT, JSON_FACTORY, credential).setApplicationName("BigQuery-Service-Accounts/0.1")
                .setHttpRequestInitializer(credential).build();

        createTableBySchema(schema, getDestTableByTableName(tableId));
    }

    public void createTableForBrainlabs() throws Exception {

        log.info("===projectId:" + projectId + ",dataSetId:" + dataSetId + ",tableId:" + tableId + ",accountId:" + accountId + ",privateKayPath:" + privateKayPath);

        GoogleCredential credential = new GoogleCredential.Builder().setTransport(TRANSPORT)
                .setJsonFactory(JSON_FACTORY)
                .setServiceAccountId(accountId)
                .setServiceAccountScopes(SCOPE)
                .setServiceAccountPrivateKeyFromP12File(new File(privateKayPath)).build();

        bigquery = new Bigquery.Builder(TRANSPORT, JSON_FACTORY, credential).setApplicationName("BigQuery-Service-Accounts/0.1")
                .setHttpRequestInitializer(credential).build();

        createPartitionedTableForBrainlabs();
    }

    public TableReference getDestTable() {
        TableReference destTable = new TableReference();
        destTable.setProjectId(projectId);
        destTable.setDatasetId(dataSetId);
        destTable.setTableId(tableId);

        return destTable;
    }

    public TableReference getDestTableByTableName(String tableId) {
        TableReference destTable = new TableReference();
        destTable.setProjectId(projectId);
        destTable.setDatasetId(dataSetId);
        destTable.setTableId(tableId);

        return destTable;
    }


    private void createTableBySchema(TableSchema schema, TableReference destTable) throws Exception {
        long a = System.currentTimeMillis();
        // this is for CSV template. no need to merge for now

        Table table = new Table();
        table.setSchema(schema);
        table.setTableReference(destTable);

        Bigquery.Tables.Insert insert = bigquery.tables().insert(projectId, dataSetId, table);
        TableReference tableReference = insert.execute().getTableReference();
        System.out.println("create table");

        long b = System.currentTimeMillis();
        System.out.println("Uploading Data to BigQuery Used : " + (b - a) * 1.0 / (1000 * 60) + " mins.");
    }

    private void createTable() throws Exception {
        long a = System.currentTimeMillis();
        TableSchema schema = getJsonRankTableSchema();
        // this is for CSV template. no need to merge for now
//		TableSchema schema = getCSVRankTableSchema();

        TableReference destTable = getDestTable();
//        JobConfigurationLoad configLoad = getConfigLoad(destTable, schema, fileName);

        Table table = new Table();
        table.setSchema(schema);
        table.setTableReference(destTable);

        Bigquery.Tables.Insert insert = bigquery.tables().insert(projectId, dataSetId, table);
        TableReference tableReference = insert.execute().getTableReference();
        System.out.println("create table");

//        com.google.api.services.bigquery.model.Job job = new com.google.api.services.bigquery.model.Job();
//        com.google.api.services.bigquery.model.JobConfiguration config = new com.google.api.services.bigquery.model.JobConfiguration();
//        config.setLoad(configLoad);
//        job.setConfiguration(config);

//        System.out.println("data uploading");

//        Bigquery.Jobs.Insert insert = bigquery.jobs().insert(projectId, job);
//        insert.setProjectId(projectId);
//        JobReference jobRef = insert.execute().getJobReference();
//        System.out.println("data loaded");

//        com.google.api.services.bigquery.model.Job returnJob = checkQueryResults(projectId, jobRef);
//        System.out.println("data verified");

        long b = System.currentTimeMillis();
        System.out.println("Uploading Data to BigQuery Used : " + (b - a) * 1.0 / (1000 * 60) + " mins.");
    }


    public void createPartitionedTable() {
        try {
            long a = System.currentTimeMillis();
            TableSchema schema = getJsonRankTableSchema();

            TableReference destTable = getDestTable();

            Table table = new Table();
            table.setSchema(schema);
            table.setTableReference(destTable);

            TimePartitioning timePartitioning = new TimePartitioning();
            timePartitioning.setField("date");
            table.setTimePartitioning(timePartitioning);

            Bigquery.Tables.Insert insert = bigquery.tables().insert(projectId, dataSetId, table);
            TableReference tableReference = insert.execute().getTableReference();
            System.out.println("create table");

            long b = System.currentTimeMillis();
            System.out.println("Uploading Data to BigQuery Used : " + (b - a) * 1.0 / (1000 * 60) + " mins.");
        } catch (Exception e) {
            System.out.println("Partitioned table was not created. \n" + e.toString());
        }
    }

    public void selectTableData(String sqlQuery) {
        try {
            log.info("===projectId:" + projectId + ",dataSetId:" + dataSetId + ",tableId:" + tableId + ",accountId:" + accountId + ",privateKayPath:" + privateKayPath);

            GoogleCredential credential = new GoogleCredential.Builder().setTransport(TRANSPORT)
                    .setJsonFactory(JSON_FACTORY)
                    .setServiceAccountId(accountId)
                    .setServiceAccountScopes(SCOPE)
                    .setServiceAccountPrivateKeyFromP12File(new File(privateKayPath)).build();

            bigquery = new Bigquery.Builder(TRANSPORT, JSON_FACTORY, credential).setApplicationName("BigQuery-Service-Accounts/0.1")
                    .setHttpRequestInitializer(credential).build();

            QueryRequest queryRequest = new QueryRequest().setUseLegacySql(false).setQuery(sqlQuery);
            QueryResponse response = bigquery.jobs().query(projectId, queryRequest).execute();

            if (response.getErrors() != null) {
                System.out.println("Error running query: " + JSON.toJSONString(response.getErrors()));
            } else if (response.getRows() != null) {
                for (TableRow row : response.getRows()) {
                    List<TableCell> cells = row.getF();
                    for (TableCell cell : cells) {
                        System.out.print("value:" + cell.getV() + "\t");
                    }
                    System.out.println("========");
                }
            } else {
                System.out.println("No data found.");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void createPartitionedTableForBrainlabs() {
        try {
            long a = System.currentTimeMillis();
            TableSchema schema = getBrainlabsJsonRankTableSchema();

            TableReference destTable = getDestTable();

            Table table = new Table();
            table.setSchema(schema);
            table.setTableReference(destTable);

            TimePartitioning timePartitioning = new TimePartitioning();
            timePartitioning.setField("date");
            table.setTimePartitioning(timePartitioning);

            Bigquery.Tables.Insert insert = bigquery.tables().insert(projectId, dataSetId, table);
            TableReference tableReference = insert.execute().getTableReference();
            System.out.println("create table");

            long b = System.currentTimeMillis();
            System.out.println("Uploading Data to BigQuery Used : " + (b - a) * 1.0 / (1000 * 60) + " mins.");
        } catch (Exception e) {
            System.out.println("Partitioned table was not created. \n" + e.toString());
        }
    }

    public static TableSchema getJsonRankTableSchemaLoveHolidayBigQuery() {
        TableSchema schema = new TableSchema();

        List<TableFieldSchema> fields = new ArrayList<TableFieldSchema>();

        TableFieldSchema domainId = new TableFieldSchema();
        domainId.setName("domain_id");
        domainId.setType("INTEGER");
        domainId.setMode("REQUIRED");

        TableFieldSchema domainName = new TableFieldSchema();
        domainName.setName("domain_name");
        domainName.setType("STRING");
        domainName.setMode("REQUIRED");

        TableFieldSchema date = new TableFieldSchema();
        date.setName("ranking_date");
        date.setType("DATE");
        date.setMode("REQUIRED");

        TableFieldSchema engine = new TableFieldSchema();
        engine.setName("engine");
        engine.setType("STRING");
        engine.setMode("REQUIRED");

        TableFieldSchema contentType = new TableFieldSchema();
        contentType.setName("content_type");
        contentType.setType("STRING");
        contentType.setMode("NULLABLE");

        TableFieldSchema wtdAvgRank = new TableFieldSchema();
        wtdAvgRank.setName("wtd_avg_rank");
        wtdAvgRank.setType("STRING");
        wtdAvgRank.setMode("NULLABLE");

        TableFieldSchema language = new TableFieldSchema();
        language.setName("language");
        language.setType("STRING");
        language.setMode("REQUIRED");

        // // ////////////////////////////
        TableFieldSchema device = new TableFieldSchema();
        device.setName("device");
        device.setType("STRING");
        device.setMode("REQUIRED");

        TableFieldSchema som = new TableFieldSchema();
        som.setName("som");
        som.setType("String");
        som.setMode("NULLABLE");

        fields.add(domainId);
        fields.add(domainName);
        fields.add(contentType);
        fields.add(date);
        fields.add(engine);
        fields.add(language);
        fields.add(device);
        fields.add(wtdAvgRank);
        fields.add(som);

        schema.setFields(fields);

        return schema;
    }


    public static TableSchema getJsonRankTableSchema() {
        TableSchema schema = new TableSchema();

        List<TableFieldSchema> fields = new ArrayList<TableFieldSchema>();

        TableFieldSchema domainId = new TableFieldSchema();
        domainId.setName("domain_id");
        domainId.setType("INTEGER");
        domainId.setMode("REQUIRED");

        TableFieldSchema domainName = new TableFieldSchema();
        domainName.setName("domain_name");
        domainName.setType("STRING");
        domainName.setMode("REQUIRED");

        TableFieldSchema kName = new TableFieldSchema();
        kName.setName("keyword");
        kName.setType("STRING");
        kName.setMode("REQUIRED");

        TableFieldSchema type = new TableFieldSchema();
        type.setName("type");
        type.setType("STRING");
        type.setMode("REQUIRED");

        TableFieldSchema date = new TableFieldSchema();
        date.setName("date");
        date.setType("DATE");
        date.setMode("REQUIRED");

        TableFieldSchema topkey = new TableFieldSchema();
        topkey.setName("topkey");
        topkey.setType("STRING");
        topkey.setMode("NULLABLE");

        TableFieldSchema locale = new TableFieldSchema();
        locale.setName("locale");
        locale.setType("STRING");
        locale.setMode("NULLABLE");

        TableFieldSchema geo = new TableFieldSchema();
        geo.setName("geo");
        geo.setType("STRING");
        geo.setMode("NULLABLE");

        TableFieldSchema browser = new TableFieldSchema();
        browser.setName("browser");
        browser.setType("STRING");
        browser.setMode("NULLABLE");

        TableFieldSchema urls = new TableFieldSchema();
        urls.setName("urls");
        urls.setType("RECORD");
        urls.setMode("REPEATED");

        // // ////////////////////////////
        TableFieldSchema domain = new TableFieldSchema();
        domain.setName("domain");
        domain.setType("STRING");
        domain.setMode("NULLABLE");

        TableFieldSchema rank = new TableFieldSchema();
        rank.setName("rank");
        rank.setType("INTEGER");
        rank.setMode("NULLABLE");

        TableFieldSchema url = new TableFieldSchema();
        url.setName("url");
        url.setType("STRING");
        url.setMode("NULLABLE");

        TableFieldSchema title = new TableFieldSchema();
        title.setName("title");
        title.setType("STRING");
        title.setMode("NULLABLE");

        TableFieldSchema details = new TableFieldSchema();
        details.setName("details");
        details.setType("STRING");
        details.setMode("NULLABLE");

        TableFieldSchema https = new TableFieldSchema();
        https.setName("https");
        https.setType("BOOLEAN");
        https.setMode("NULLABLE");

        TableFieldSchema sub_rank = new TableFieldSchema();
        sub_rank.setName("sub_rank");
        sub_rank.setType("RECORD");
        sub_rank.setMode("REPEATED");

        List<TableFieldSchema> subRankFileds = new ArrayList<TableFieldSchema>();
        subRankFileds.add(domain);
        subRankFileds.add(url);
        subRankFileds.add(rank);
        subRankFileds.add(https);
        subRankFileds.add(title);
        sub_rank.setFields(subRankFileds);

        List<TableFieldSchema> childrenFileds = new ArrayList<TableFieldSchema>();
        childrenFileds.add(domain);
        childrenFileds.add(url);
        childrenFileds.add(https);
        childrenFileds.add(rank);
        childrenFileds.add(type);
        childrenFileds.add(sub_rank);
        childrenFileds.add(title);
        childrenFileds.add(details);

        // /////////////////////////
        urls.setFields(childrenFileds);

        TableFieldSchema people_aslo_ask_flg = new TableFieldSchema();
        people_aslo_ask_flg.setName("people_aslo_ask_flg");
        people_aslo_ask_flg.setType("BOOLEAN");
        people_aslo_ask_flg.setMode("NULLABLE");

        TableFieldSchema answerbox_flg = new TableFieldSchema();
        answerbox_flg.setName("answerbox_flg");
        answerbox_flg.setType("BOOLEAN");
        answerbox_flg.setMode("NULLABLE");

        TableFieldSchema avg_search_vol = new TableFieldSchema();
        avg_search_vol.setName("avg_search_vol");
        avg_search_vol.setType("FLOAT");
        avg_search_vol.setMode("NULLABLE");

        ///
        TableFieldSchema tags = new TableFieldSchema();
        tags.setName("tags");
        tags.setType("RECORD");
        tags.setMode("REPEATED");

        TableFieldSchema tagIndex = new TableFieldSchema();
        tagIndex.setName("index");
        tagIndex.setType("INTEGER");
        tagIndex.setMode("NULLABLE");

        TableFieldSchema tagName = new TableFieldSchema();
        tagName.setName("name");
        tagName.setType("STRING");
        tagName.setMode("NULLABLE");

        List<TableFieldSchema> tagsFileds = new ArrayList<TableFieldSchema>();
        tagsFileds.add(tagIndex);
        tagsFileds.add(tagName);

        tags.setFields(tagsFileds);

        fields.add(domainId);
        fields.add(domainName);
        fields.add(kName);
        fields.add(type);
        fields.add(date);
        fields.add(topkey);
        fields.add(locale);
        fields.add(geo);
        fields.add(browser);
        fields.add(urls);
        fields.add(people_aslo_ask_flg);
        fields.add(answerbox_flg);
        fields.add(avg_search_vol);
        fields.add(tags);

        schema.setFields(fields);

        return schema;
    }

    public static TableSchema getBrainlabsJsonRankTableSchema() {
        TableSchema schema = new TableSchema();

        List<TableFieldSchema> fields = new ArrayList<TableFieldSchema>();

        TableFieldSchema domainId = new TableFieldSchema();
        domainId.setName("domain_id");
        domainId.setType("INTEGER");
        domainId.setMode("REQUIRED");

        TableFieldSchema domainName = new TableFieldSchema();
        domainName.setName("domain_name");
        domainName.setType("STRING");
        domainName.setMode("REQUIRED");

        TableFieldSchema kName = new TableFieldSchema();
        kName.setName("keyword");
        kName.setType("STRING");
        kName.setMode("REQUIRED");

        TableFieldSchema type = new TableFieldSchema();
        type.setName("type");
        type.setType("STRING");
        type.setMode("REQUIRED");

        TableFieldSchema date = new TableFieldSchema();
        date.setName("date");
        date.setType("DATE");
        date.setMode("REQUIRED");

        TableFieldSchema topkey = new TableFieldSchema();
        topkey.setName("topkey");
        topkey.setType("STRING");
        topkey.setMode("NULLABLE");

        TableFieldSchema locale = new TableFieldSchema();
        locale.setName("locale");
        locale.setType("STRING");
        locale.setMode("NULLABLE");

        TableFieldSchema geo = new TableFieldSchema();
        geo.setName("geo");
        geo.setType("STRING");
        geo.setMode("NULLABLE");

        TableFieldSchema browser = new TableFieldSchema();
        browser.setName("browser");
        browser.setType("STRING");
        browser.setMode("NULLABLE");

        TableFieldSchema urls = new TableFieldSchema();
        urls.setName("urls");
        urls.setType("RECORD");
        urls.setMode("REPEATED");

        // // ////////////////////////////
        TableFieldSchema domain = new TableFieldSchema();
        domain.setName("domain");
        domain.setType("STRING");
        domain.setMode("NULLABLE");

        TableFieldSchema rank = new TableFieldSchema();
        rank.setName("rank");
        rank.setType("INTEGER");
        rank.setMode("NULLABLE");

        TableFieldSchema url = new TableFieldSchema();
        url.setName("url");
        url.setType("STRING");
        url.setMode("NULLABLE");

        TableFieldSchema title = new TableFieldSchema();
        title.setName("title");
        title.setType("STRING");
        title.setMode("NULLABLE");

        TableFieldSchema details = new TableFieldSchema();
        details.setName("details");
        details.setType("STRING");
        details.setMode("NULLABLE");

        TableFieldSchema https = new TableFieldSchema();
        https.setName("https");
        https.setType("BOOLEAN");
        https.setMode("NULLABLE");

        TableFieldSchema sub_rank = new TableFieldSchema();
        sub_rank.setName("sub_rank");
        sub_rank.setType("RECORD");
        sub_rank.setMode("REPEATED");

        List<TableFieldSchema> subRankFileds = new ArrayList<TableFieldSchema>();
        subRankFileds.add(domain);
        subRankFileds.add(url);
        subRankFileds.add(rank);
        subRankFileds.add(https);
        subRankFileds.add(title);
        sub_rank.setFields(subRankFileds);

        List<TableFieldSchema> childrenFileds = new ArrayList<TableFieldSchema>();
        childrenFileds.add(domain);
        childrenFileds.add(url);
        childrenFileds.add(https);
        childrenFileds.add(rank);
        childrenFileds.add(type);
        childrenFileds.add(sub_rank);
        childrenFileds.add(title);
        childrenFileds.add(details);

        // /////////////////////////
        urls.setFields(childrenFileds);

        TableFieldSchema people_aslo_ask_flg = new TableFieldSchema();
        people_aslo_ask_flg.setName("people_aslo_ask_flg");
        people_aslo_ask_flg.setType("BOOLEAN");
        people_aslo_ask_flg.setMode("NULLABLE");

        TableFieldSchema answerbox_flg = new TableFieldSchema();
        answerbox_flg.setName("answerbox_flg");
        answerbox_flg.setType("BOOLEAN");
        answerbox_flg.setMode("NULLABLE");

        TableFieldSchema avg_search_vol = new TableFieldSchema();
        avg_search_vol.setName("avg_search_vol");
        avg_search_vol.setType("FLOAT");
        avg_search_vol.setMode("NULLABLE");

        ///
        TableFieldSchema tags = new TableFieldSchema();
        tags.setName("tags");
        tags.setType("RECORD");
        tags.setMode("REPEATED");

        TableFieldSchema tagIndex = new TableFieldSchema();
        tagIndex.setName("index");
        tagIndex.setType("INTEGER");
        tagIndex.setMode("NULLABLE");

        TableFieldSchema tagName = new TableFieldSchema();
        tagName.setName("name");
        tagName.setType("STRING");
        tagName.setMode("NULLABLE");

        List<TableFieldSchema> tagsFileds = new ArrayList<TableFieldSchema>();
        tagsFileds.add(tagIndex);
        tagsFileds.add(tagName);

        tags.setFields(tagsFileds);

        TableFieldSchema freeFroductListingFlag = new TableFieldSchema();
        freeFroductListingFlag.setName("free_product_listing_flag");
        freeFroductListingFlag.setType("BOOLEAN");
        freeFroductListingFlag.setMode("NULLABLE");

        TableFieldSchema jobFlag = new TableFieldSchema();
        jobFlag.setName("job_flag");
        jobFlag.setType("BOOLEAN");
        jobFlag.setMode("NULLABLE");

        TableFieldSchema knogFlg = new TableFieldSchema();
        knogFlg.setName("knog_flg");
        knogFlg.setType("BOOLEAN");
        knogFlg.setMode("NULLABLE");

        TableFieldSchema llFlg = new TableFieldSchema();
        llFlg.setName("ll_flg");
        llFlg.setType("BOOLEAN");
        llFlg.setMode("NULLABLE");

        TableFieldSchema newsFlg = new TableFieldSchema();
        newsFlg.setName("news_flg");
        newsFlg.setType("BOOLEAN");
        newsFlg.setMode("NULLABLE");

        TableFieldSchema productFlg = new TableFieldSchema();
        productFlg.setName("product_flg");
        productFlg.setType("BOOLEAN");
        productFlg.setMode("NULLABLE");

        TableFieldSchema shoppingFlg = new TableFieldSchema();
        shoppingFlg.setName("shopping_flg");
        shoppingFlg.setType("BOOLEAN");
        shoppingFlg.setMode("NULLABLE");

        TableFieldSchema videoFlg = new TableFieldSchema();
        videoFlg.setName("video_flg");
        videoFlg.setType("BOOLEAN");
        videoFlg.setMode("NULLABLE");

        fields.add(domainId);
        fields.add(domainName);
        fields.add(kName);
        fields.add(type);
        fields.add(date);
        fields.add(topkey);
        fields.add(locale);
        fields.add(geo);
        fields.add(browser);
        fields.add(urls);
        fields.add(people_aslo_ask_flg);
        fields.add(answerbox_flg);
        fields.add(avg_search_vol);
        fields.add(tags);
        fields.add(freeFroductListingFlag);
        fields.add(jobFlag);
        fields.add(knogFlg);
        fields.add(llFlg);
        fields.add(newsFlg);
        fields.add(productFlg);
        fields.add(shoppingFlg);
        fields.add(videoFlg);

        schema.setFields(fields);

        return schema;
    }
}
