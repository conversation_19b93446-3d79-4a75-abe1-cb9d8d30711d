/**
 * Autogenerated by Thrift
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 */
package seoclarity.backend.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.thrift.TApplicationException;
import org.apache.thrift.TBase;
import org.apache.thrift.TBaseHelper;
import org.apache.thrift.TException;
import org.apache.thrift.TFieldIdEnum;
import org.apache.thrift.TFieldRequirementType;
import org.apache.thrift.TProcessor;
import org.apache.thrift.meta_data.EnumMetaData;
import org.apache.thrift.meta_data.FieldMetaData;
import org.apache.thrift.meta_data.ListMetaData;
import org.apache.thrift.meta_data.StructMetaData;
import org.apache.thrift.protocol.TField;
import org.apache.thrift.protocol.TList;
import org.apache.thrift.protocol.TMessage;
import org.apache.thrift.protocol.TMessageType;
import org.apache.thrift.protocol.TProtocol;
import org.apache.thrift.protocol.TProtocolUtil;
import org.apache.thrift.protocol.TStruct;
import org.apache.thrift.protocol.TType;

public class Scribe {

	public interface Iface extends com.facebook.fb303.FacebookService.Iface {

		public ResultCode Log(List<LogEntry> messages) throws TException;

	}

	public static class Client extends com.facebook.fb303.FacebookService.Client implements Iface {
		public Client(TProtocol prot) {
			this(prot, prot);
		}

		public Client(TProtocol iprot, TProtocol oprot) {
			super(iprot, oprot);
		}

		public ResultCode Log(List<LogEntry> messages) throws TException {
			send_Log(messages);
			return recv_Log();
		}

		public void send_Log(List<LogEntry> messages) throws TException {
			oprot_.writeMessageBegin(new TMessage("Log", TMessageType.CALL, seqid_));
			Log_args args = new Log_args();
			args.messages = messages;
			args.write(oprot_);
			oprot_.writeMessageEnd();
			oprot_.getTransport().flush();
		}

		public ResultCode recv_Log() throws TException {
			TMessage msg = iprot_.readMessageBegin();
			if (msg.type == TMessageType.EXCEPTION) {
				TApplicationException x = TApplicationException.read(iprot_);
				iprot_.readMessageEnd();
				throw x;
			}
			Log_result result = new Log_result();
			result.read(iprot_);
			iprot_.readMessageEnd();
			if (result.isSetSuccess()) {
				return result.success;
			}
			throw new TApplicationException(TApplicationException.MISSING_RESULT, "Log failed: unknown result");
		}

	}

	public static class Processor extends com.facebook.fb303.FacebookService.Processor implements TProcessor {

		public Processor(Iface iface) {
			super(iface);
			iface_ = iface;
			processMap_.put("Log", new Log());
		}

		private Iface iface_;

		public boolean process(TProtocol iprot, TProtocol oprot) throws TException {
			TMessage msg = iprot.readMessageBegin();
			ProcessFunction fn = processMap_.get(msg.name);
			if (fn == null) {
				TProtocolUtil.skip(iprot, TType.STRUCT);
				iprot.readMessageEnd();
				TApplicationException x = new TApplicationException(TApplicationException.UNKNOWN_METHOD, "Invalid method name: '" + msg.name + "'");
				oprot.writeMessageBegin(new TMessage(msg.name, TMessageType.EXCEPTION, msg.seqid));
				x.write(oprot);
				oprot.writeMessageEnd();
				oprot.getTransport().flush();
				return true;
			}
			fn.process(msg.seqid, iprot, oprot);
			return true;
		}

		private class Log implements ProcessFunction {
			public void process(int seqid, TProtocol iprot, TProtocol oprot) throws TException {
				Log_args args = new Log_args();
				args.read(iprot);
				iprot.readMessageEnd();
				Log_result result = new Log_result();
				result.success = iface_.Log(args.messages);
				oprot.writeMessageBegin(new TMessage("Log", TMessageType.REPLY, seqid));
				result.write(oprot);
				oprot.writeMessageEnd();
				oprot.getTransport().flush();
			}

		}

	}

	public static class Log_args implements TBase<Log_args._Fields>, java.io.Serializable, Cloneable, Comparable<Log_args> {
		private static final TStruct STRUCT_DESC = new TStruct("Log_args");

		private static final TField MESSAGES_FIELD_DESC = new TField("messages", TType.LIST, (short) 1);

		public List<LogEntry> messages;

		/**
		 * The set of fields this struct contains, along with convenience
		 * methods for finding and manipulating them.
		 */
		public enum _Fields implements TFieldIdEnum {
			MESSAGES((short) 1, "messages");

			private static final Map<Integer, _Fields> byId = new HashMap<Integer, _Fields>();
			private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

			static {
				for (_Fields field : EnumSet.allOf(_Fields.class)) {
					byId.put((int) field._thriftId, field);
					byName.put(field.getFieldName(), field);
				}
			}

			/**
			 * Find the _Fields constant that matches fieldId, or null if its
			 * not found.
			 */
			public static _Fields findByThriftId(int fieldId) {
				return byId.get(fieldId);
			}

			/**
			 * Find the _Fields constant that matches fieldId, throwing an
			 * exception if it is not found.
			 */
			public static _Fields findByThriftIdOrThrow(int fieldId) {
				_Fields fields = findByThriftId(fieldId);
				if (fields == null)
					throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
				return fields;
			}

			/**
			 * Find the _Fields constant that matches name, or null if its not
			 * found.
			 */
			public static _Fields findByName(String name) {
				return byName.get(name);
			}

			private final short _thriftId;
			private final String _fieldName;

			_Fields(short thriftId, String fieldName) {
				_thriftId = thriftId;
				_fieldName = fieldName;
			}

			public short getThriftFieldId() {
				return _thriftId;
			}

			public String getFieldName() {
				return _fieldName;
			}
		}

		// isset id assignments

		public static final Map<_Fields, FieldMetaData> metaDataMap = Collections.unmodifiableMap(new EnumMap<_Fields, FieldMetaData>(_Fields.class) {
			{
				put(_Fields.MESSAGES, new FieldMetaData("messages", TFieldRequirementType.DEFAULT, new ListMetaData(TType.LIST, new StructMetaData(
				        TType.STRUCT, LogEntry.class))));
			}
		});

		static {
			FieldMetaData.addStructMetaDataMap(Log_args.class, metaDataMap);
		}

		public Log_args() {
		}

		public Log_args(List<LogEntry> messages) {
			this();
			this.messages = messages;
		}

		/**
		 * Performs a deep copy on <i>other</i>.
		 */
		public Log_args(Log_args other) {
			if (other.isSetMessages()) {
				List<LogEntry> __this__messages = new ArrayList<LogEntry>();
				for (LogEntry other_element : other.messages) {
					__this__messages.add(new LogEntry(other_element));
				}
				this.messages = __this__messages;
			}
		}

		public Log_args deepCopy() {
			return new Log_args(this);
		}

		@Deprecated
		public Log_args clone() {
			return new Log_args(this);
		}

		public int getMessagesSize() {
			return (this.messages == null) ? 0 : this.messages.size();
		}

		public java.util.Iterator<LogEntry> getMessagesIterator() {
			return (this.messages == null) ? null : this.messages.iterator();
		}

		public void addToMessages(LogEntry elem) {
			if (this.messages == null) {
				this.messages = new ArrayList<LogEntry>();
			}
			this.messages.add(elem);
		}

		public List<LogEntry> getMessages() {
			return this.messages;
		}

		public Log_args setMessages(List<LogEntry> messages) {
			this.messages = messages;
			return this;
		}

		public void unsetMessages() {
			this.messages = null;
		}

		/**
		 * Returns true if field messages is set (has been asigned a value) and
		 * false otherwise
		 */
		public boolean isSetMessages() {
			return this.messages != null;
		}

		public void setMessagesIsSet(boolean value) {
			if (!value) {
				this.messages = null;
			}
		}

		public void setFieldValue(_Fields field, Object value) {
			switch (field) {
			case MESSAGES:
				if (value == null) {
					unsetMessages();
				} else {
					setMessages((List<LogEntry>) value);
				}
				break;

			}
		}

		public void setFieldValue(int fieldID, Object value) {
			setFieldValue(_Fields.findByThriftIdOrThrow(fieldID), value);
		}

		public Object getFieldValue(_Fields field) {
			switch (field) {
			case MESSAGES:
				return getMessages();

			}
			throw new IllegalStateException();
		}

		public Object getFieldValue(int fieldId) {
			return getFieldValue(_Fields.findByThriftIdOrThrow(fieldId));
		}

		/**
		 * Returns true if field corresponding to fieldID is set (has been
		 * asigned a value) and false otherwise
		 */
		public boolean isSet(_Fields field) {
			switch (field) {
			case MESSAGES:
				return isSetMessages();
			}
			throw new IllegalStateException();
		}

		public boolean isSet(int fieldID) {
			return isSet(_Fields.findByThriftIdOrThrow(fieldID));
		}

		@Override
		public boolean equals(Object that) {
			if (that == null)
				return false;
			if (that instanceof Log_args)
				return this.equals((Log_args) that);
			return false;
		}

		public boolean equals(Log_args that) {
			if (that == null)
				return false;

			boolean this_present_messages = true && this.isSetMessages();
			boolean that_present_messages = true && that.isSetMessages();
			if (this_present_messages || that_present_messages) {
				if (!(this_present_messages && that_present_messages))
					return false;
				if (!this.messages.equals(that.messages))
					return false;
			}

			return true;
		}

		@Override
		public int hashCode() {
			return 0;
		}

		public int compareTo(Log_args other) {
			if (!getClass().equals(other.getClass())) {
				return getClass().getName().compareTo(other.getClass().getName());
			}

			int lastComparison = 0;
			Log_args typedOther = (Log_args) other;

			lastComparison = Boolean.valueOf(isSetMessages()).compareTo(isSetMessages());
			if (lastComparison != 0) {
				return lastComparison;
			}
			lastComparison = TBaseHelper.compareTo(messages, typedOther.messages);
			if (lastComparison != 0) {
				return lastComparison;
			}
			return 0;
		}

		public void read(TProtocol iprot) throws TException {
			TField field;
			iprot.readStructBegin();
			while (true) {
				field = iprot.readFieldBegin();
				if (field.type == TType.STOP) {
					break;
				}
				_Fields fieldId = _Fields.findByThriftId(field.id);
				if (fieldId == null) {
					TProtocolUtil.skip(iprot, field.type);
				} else {
					switch (fieldId) {
					case MESSAGES:
						if (field.type == TType.LIST) {
							{
								TList _list0 = iprot.readListBegin();
								this.messages = new ArrayList<LogEntry>(_list0.size);
								for (int _i1 = 0; _i1 < _list0.size; ++_i1) {
									LogEntry _elem2;
									_elem2 = new LogEntry();
									_elem2.read(iprot);
									this.messages.add(_elem2);
								}
								iprot.readListEnd();
							}
						} else {
							TProtocolUtil.skip(iprot, field.type);
						}
						break;
					}
					iprot.readFieldEnd();
				}
			}
			iprot.readStructEnd();

			// check for required fields of primitive type, which can't be
			// checked in the validate method
			validate();
		}

		public void write(TProtocol oprot) throws TException {
			validate();

			oprot.writeStructBegin(STRUCT_DESC);
			if (this.messages != null) {
				oprot.writeFieldBegin(MESSAGES_FIELD_DESC);
				{
					oprot.writeListBegin(new TList(TType.STRUCT, this.messages.size()));
					for (LogEntry _iter3 : this.messages) {
						_iter3.write(oprot);
					}
					oprot.writeListEnd();
				}
				oprot.writeFieldEnd();
			}
			oprot.writeFieldStop();
			oprot.writeStructEnd();
		}

		@Override
		public String toString() {
			StringBuilder sb = new StringBuilder("Log_args(");
			boolean first = true;

			sb.append("messages:");
			if (this.messages == null) {
				sb.append("null");
			} else {
				sb.append(this.messages);
			}
			first = false;
			sb.append(")");
			return sb.toString();
		}

		public void validate() throws TException {
			// check for required fields
		}

	}

	public static class Log_result implements TBase<Log_result._Fields>, java.io.Serializable, Cloneable, Comparable<Log_result> {
		private static final TStruct STRUCT_DESC = new TStruct("Log_result");

		private static final TField SUCCESS_FIELD_DESC = new TField("success", TType.I32, (short) 0);

		/**
		 * 
		 * @see ResultCode
		 */
		public ResultCode success;

		/**
		 * The set of fields this struct contains, along with convenience
		 * methods for finding and manipulating them.
		 */
		public enum _Fields implements TFieldIdEnum {
			/**
			 * 
			 * @see ResultCode
			 */
			SUCCESS((short) 0, "success");

			private static final Map<Integer, _Fields> byId = new HashMap<Integer, _Fields>();
			private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

			static {
				for (_Fields field : EnumSet.allOf(_Fields.class)) {
					byId.put((int) field._thriftId, field);
					byName.put(field.getFieldName(), field);
				}
			}

			/**
			 * Find the _Fields constant that matches fieldId, or null if its
			 * not found.
			 */
			public static _Fields findByThriftId(int fieldId) {
				return byId.get(fieldId);
			}

			/**
			 * Find the _Fields constant that matches fieldId, throwing an
			 * exception if it is not found.
			 */
			public static _Fields findByThriftIdOrThrow(int fieldId) {
				_Fields fields = findByThriftId(fieldId);
				if (fields == null)
					throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
				return fields;
			}

			/**
			 * Find the _Fields constant that matches name, or null if its not
			 * found.
			 */
			public static _Fields findByName(String name) {
				return byName.get(name);
			}

			private final short _thriftId;
			private final String _fieldName;

			_Fields(short thriftId, String fieldName) {
				_thriftId = thriftId;
				_fieldName = fieldName;
			}

			public short getThriftFieldId() {
				return _thriftId;
			}

			public String getFieldName() {
				return _fieldName;
			}
		}

		// isset id assignments

		public static final Map<_Fields, FieldMetaData> metaDataMap = Collections.unmodifiableMap(new EnumMap<_Fields, FieldMetaData>(_Fields.class) {
			{
				put(_Fields.SUCCESS, new FieldMetaData("success", TFieldRequirementType.DEFAULT, new EnumMetaData(TType.ENUM, ResultCode.class)));
			}
		});

		static {
			FieldMetaData.addStructMetaDataMap(Log_result.class, metaDataMap);
		}

		public Log_result() {
		}

		public Log_result(ResultCode success) {
			this();
			this.success = success;
		}

		/**
		 * Performs a deep copy on <i>other</i>.
		 */
		public Log_result(Log_result other) {
			if (other.isSetSuccess()) {
				this.success = other.success;
			}
		}

		public Log_result deepCopy() {
			return new Log_result(this);
		}

		@Deprecated
		public Log_result clone() {
			return new Log_result(this);
		}

		/**
		 * 
		 * @see ResultCode
		 */
		public ResultCode getSuccess() {
			return this.success;
		}

		/**
		 * 
		 * @see ResultCode
		 */
		public Log_result setSuccess(ResultCode success) {
			this.success = success;
			return this;
		}

		public void unsetSuccess() {
			this.success = null;
		}

		/**
		 * Returns true if field success is set (has been asigned a value) and
		 * false otherwise
		 */
		public boolean isSetSuccess() {
			return this.success != null;
		}

		public void setSuccessIsSet(boolean value) {
			if (!value) {
				this.success = null;
			}
		}

		public void setFieldValue(_Fields field, Object value) {
			switch (field) {
			case SUCCESS:
				if (value == null) {
					unsetSuccess();
				} else {
					setSuccess((ResultCode) value);
				}
				break;

			}
		}

		public void setFieldValue(int fieldID, Object value) {
			setFieldValue(_Fields.findByThriftIdOrThrow(fieldID), value);
		}

		public Object getFieldValue(_Fields field) {
			switch (field) {
			case SUCCESS:
				return getSuccess();

			}
			throw new IllegalStateException();
		}

		public Object getFieldValue(int fieldId) {
			return getFieldValue(_Fields.findByThriftIdOrThrow(fieldId));
		}

		/**
		 * Returns true if field corresponding to fieldID is set (has been
		 * asigned a value) and false otherwise
		 */
		public boolean isSet(_Fields field) {
			switch (field) {
			case SUCCESS:
				return isSetSuccess();
			}
			throw new IllegalStateException();
		}

		public boolean isSet(int fieldID) {
			return isSet(_Fields.findByThriftIdOrThrow(fieldID));
		}

		@Override
		public boolean equals(Object that) {
			if (that == null)
				return false;
			if (that instanceof Log_result)
				return this.equals((Log_result) that);
			return false;
		}

		public boolean equals(Log_result that) {
			if (that == null)
				return false;

			boolean this_present_success = true && this.isSetSuccess();
			boolean that_present_success = true && that.isSetSuccess();
			if (this_present_success || that_present_success) {
				if (!(this_present_success && that_present_success))
					return false;
				if (!this.success.equals(that.success))
					return false;
			}

			return true;
		}

		@Override
		public int hashCode() {
			return 0;
		}

		public int compareTo(Log_result other) {
			if (!getClass().equals(other.getClass())) {
				return getClass().getName().compareTo(other.getClass().getName());
			}

			int lastComparison = 0;
			Log_result typedOther = (Log_result) other;

			lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(isSetSuccess());
			if (lastComparison != 0) {
				return lastComparison;
			}
			lastComparison = TBaseHelper.compareTo(success, typedOther.success);
			if (lastComparison != 0) {
				return lastComparison;
			}
			return 0;
		}

		public void read(TProtocol iprot) throws TException {
			TField field;
			iprot.readStructBegin();
			while (true) {
				field = iprot.readFieldBegin();
				if (field.type == TType.STOP) {
					break;
				}
				_Fields fieldId = _Fields.findByThriftId(field.id);
				if (fieldId == null) {
					TProtocolUtil.skip(iprot, field.type);
				} else {
					switch (fieldId) {
					case SUCCESS:
						if (field.type == TType.I32) {
							this.success = ResultCode.findByValue(iprot.readI32());
						} else {
							TProtocolUtil.skip(iprot, field.type);
						}
						break;
					}
					iprot.readFieldEnd();
				}
			}
			iprot.readStructEnd();

			// check for required fields of primitive type, which can't be
			// checked in the validate method
			validate();
		}

		public void write(TProtocol oprot) throws TException {
			oprot.writeStructBegin(STRUCT_DESC);

			if (this.isSetSuccess()) {
				oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
				oprot.writeI32(this.success.getValue());
				oprot.writeFieldEnd();
			}
			oprot.writeFieldStop();
			oprot.writeStructEnd();
		}

		@Override
		public String toString() {
			StringBuilder sb = new StringBuilder("Log_result(");
			boolean first = true;

			sb.append("success:");
			if (this.success == null) {
				sb.append("null");
			} else {
				String success_name = success.name();
				if (success_name != null) {
					sb.append(success_name);
					sb.append(" (");
				}
				sb.append(this.success);
				if (success_name != null) {
					sb.append(")");
				}
			}
			first = false;
			sb.append(")");
			return sb.toString();
		}

		public void validate() throws TException {
			// check for required fields
		}

	}

}
