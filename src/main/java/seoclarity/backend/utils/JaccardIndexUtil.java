package seoclarity.backend.utils;

import java.util.*;
import java.util.stream.Collectors;


public class JaccardIndexUtil {


        // Function to return the
// intersection set of s1 and s2
        public static HashSet<String> intersection(HashSet<String> a, HashSet<String> b)
        {

            // Find the intersection of the two sets
            HashSet<String> intersect = new HashSet<String>();
            for (String n : a)
            {
                if (b.contains(n))
                    intersect.add(n);
            }

            return intersect;
        }

        public static Set<String> baseFilter(HashSet<String> a, HashSet<String> b) {
            return a.stream()
                    .filter(b::contains)
                    .collect(Collectors.toSet());
        }

        // Function to return the Jaccard index of two sets
        public static double jaccard_index(HashSet<String> s1, HashSet<String> s2)
        {
            // Sizes of both the sets
            int size_s1 = s1.size();
            int size_s2 = s2.size();

            // Get the intersection set
            HashSet<String> intersect = intersection(s1, s2);

            // Size of the intersection set
            int size_in = intersect.size();

            // Calculate the Jaccard index
            // using the formula
            double jaccard_in = (double)size_in / (double)(size_s1 + size_s2 - size_in);

            // Return the Jaccard index
            return jaccard_in;
        }

        // Elements of the 1st set
        public static void main(String[] args)
        {
            HashSet<String> s2 = new HashSet<String>();
            s2.add("50B62BDD72A09C467B9F1C229A4EC529ACF2A95155A61DA7C5DB9B53427576AC7CFE4DA1E59D4D41925AA688486B43908EB6E99D65D8343B67BDE820420B0374");
            s2.add("879FB7E77FF8578116F20679076E93B16F801190284B22C05489B66D9E56DE4CD31C8CBA0BF990282DA9C44640598BE3AB69DF0C72714CB68C8249D9441A67C5");
            s2.add("F35E544B0E5A178795DCBEF8EC6569111FA5E2A377EC59CA90C291B9E647D3B84B6CBBA5AFF99C4128F2D986FD1C5E3D2808191C8B4E645BB2199CE740D1EEB4");
            s2.add("64AE5D3CBD53274A41D6F6DCC381B26776E57470725DD5244BA938CAC534ED06F38F2AA331140C3984CA626E32D734AD0C82E423519AA3EBB101B37693A07224");
            s2.add("57F4BAFD418992B440D30DA2D07710574D020B9321C86B0CBBE6385D9EA34FE61BEA13DC6D84F981E3249C68A19CE6EB2BF5C43A63909F44834CC54058C5FB40");
            s2.add("15AC05B8D314A78EB787EA7DC0E5FD9612170821A23EFF84BFC517DEDDE3A54657BA8604E76232881E283B54AABC54846D595DB6662D7D3E3F881FFA667376BD");
            s2.add("A00354681B45CD2036CC26E92AAF681C655E5F06E3C1C8EA936480AE7875D59A6AE75B2243CDBACE91FA9D6B66016260BE5FC542E9CF03A91D1613D161559B8A");
            s2.add("CBFF7096F221CBA309E3B694A484B619304565AC212F319C3C7B80BF1C69B2DC867D7D32CAEDAFDA8347C7B92F2D5B09865B00DB7509CB959A919955C5A886E8");
            s2.add("0BDB40C0FE40D848E390075506463512E3B11249B6DC7E3ECFFEAEC8C5EC51B44E37FB69C08AA24FF48BD898381E66E474CE51195AA4503338AD144CF1BE4713");
            s2.add("097326082CA6E1ABD5EB5662705F529AC6327EAB1CF881D35075725DB7B306E9452DF8F7C394D5FC325B7170B22D088416A4B0A38DD18C05893884210F681A39");



            // Elements of the 2nd set
            HashSet<String> s1 = new HashSet<String>();
            s1.add("50B62BDD72A09C467B9F1C229A4EC529ACF2A95155A61DA7C5DB9B53427576AC7CFE4DA1E59D4D41925AA688486B43908EB6E99D65D8343B67BDE820420B0374");
            s1.add("879FB7E77FF8578116F20679076E93B16F801190284B22C05489B66D9E56DE4CD31C8CBA0BF990282DA9C44640598BE3AB69DF0C72714CB68C8249D9441A67C5");
            s1.add("F35E544B0E5A178795DCBEF8EC6569111FA5E2A377EC59CA90C291B9E647D3B84B6CBBA5AFF99C4128F2D986FD1C5E3D2808191C8B4E645BB2199CE740D1EEB4");
            s1.add("64AE5D3CBD53274A41D6F6DCC381B26776E57470725DD5244BA938CAC534ED06F38F2AA331140C3984CA626E32D734AD0C82E423519AA3EBB101B37693A07224");
            s1.add("57F4BAFD418992B440D30DA2D07710574D020B9321C86B0CBBE6385D9EA34FE61BEA13DC6D84F981E3249C68A19CE6EB2BF5C43A63909F44834CC54058C5FB40");
            s1.add("15AC05B8D314A78EB787EA7DC0E5FD9612170821A23EFF84BFC517DEDDE3A54657BA8604E76232881E283B54AABC54846D595DB6662D7D3E3F881FFA667376BD");
            s1.add("A00354681B45CD2036CC26E92AAF681C655E5F06E3C1C8EA936480AE7875D59A6AE75B2243CDBACE91FA9D6B66016260BE5FC542E9CF03A91D1613D161559B8A");
            s1.add("CBFF7096F221CBA309E3B694A484B619304565AC212F319C3C7B80BF1C69B2DC867D7D32CAEDAFDA8347C7B92F2D5B09865B00DB7509CB959A919955C5A886E8");
            s1.add("0BDB40C0FE40D848E390075506463512E3B11249B6DC7E3ECFFEAEC8C5EC51B44E37FB69C08AA24FF48BD898381E66E474CE51195AA4503338AD144CF1BE4713");
            s1.add("097326082CA6E1ABD5EB5662705F529AC6327EAB1CF881D35075725DB7B306E9452DF8F7C394D5FC325B7170B22D088416A4B0A38DD18C05893884210F681A39");

            double jaccardIndex = jaccard_index(s2, s1);

            // Print the Jaccard index and Jaccard distance
            System.out.println("Jaccard index = " + jaccardIndex);
        }
    }
