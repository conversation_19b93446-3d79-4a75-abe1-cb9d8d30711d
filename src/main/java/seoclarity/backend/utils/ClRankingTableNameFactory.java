package seoclarity.backend.utils;

public class ClRankingTableNameFactory {

    public static final int DEFAULT_DATEINT = 0;

    public static String getKeywordInfoTableName(boolean mobileRanking, int engineId, int languageId, int dateInt, boolean isHot, boolean isAll) {
        if (isHot) {
            if (mobileRanking) {
                if (engineId == 1 && languageId == 1) {
                    return "m_ranking_info_us_" + dateInt;
                } else {
                    return "m_ranking_info_intl_" + dateInt;
                }
            } else {
                if (engineId == 1 && languageId == 1) {
                    return "d_ranking_info_us_" + dateInt;
                } else {
                    return "d_ranking_info_intl_" + dateInt;
                }
            }
        }else {
            if (isAll) {
                if (mobileRanking) {
                    if (engineId == 1 && languageId == 1) {
                        return "all_in_one_m_info_us";
                    } else {
                        return "all_in_one_m_info_intl";
                    }
                } else {
                    if (engineId == 1 && languageId == 1) {
                        return "all_in_one_d_info_us";
                    } else {
                        return "all_in_one_d_info_intl";
                    }
                }
            }else {
                if (mobileRanking) {
                    if (engineId == 1 && languageId == 1) {
                        return "m_ranking_info_us";
                    } else {
                        return "m_ranking_info_intl";
                    }
                } else {
                    if (engineId == 1 && languageId == 1) {
                        return "d_ranking_info_us";
                    } else {
                        return "d_ranking_info_intl";
                    }
                }
            }
        }
    }

    public static String getKeywordDetailTableName(boolean mobileRanking, int engineId, int languageId, int dateInt, boolean isHot, boolean isAll) {
        if (isHot) {
            if (mobileRanking) {
                if (engineId == 1 && languageId == 1) {
                    return "m_ranking_detail_us_" + dateInt;
                } else {
                    return "m_ranking_detail_intl_" + dateInt;
                }
            } else {
                if (engineId == 1 && languageId == 1) {
                    return "d_ranking_detail_us_" + dateInt;
                } else {
                    return "d_ranking_detail_intl_" + dateInt;
                }
            }
        }else {
            if (isAll) {
                if (mobileRanking) {
                    if (engineId == 1 && languageId == 1) {
                        return "all_in_one_m_detail_us";
                    } else {
                        return "all_in_one_m_detail_intl";
                    }
                } else {
                    if (engineId == 1 && languageId == 1) {
                        return "all_in_one_d_detail_us";
                    } else {
                        return "all_in_one_d_detail_intl";
                    }
                }
            }else {
                if (mobileRanking) {
                    if (engineId == 1 && languageId == 1) {
                        return "m_ranking_detail_us";
                    } else {
                        return "m_ranking_detail_intl";
                    }
                } else {
                    if (engineId == 1 && languageId == 1) {
                        return "d_ranking_detail_us";
                    } else {
                        return "d_ranking_detail_intl";
                    }
                }
            }
        }
    }

    public static String getKeywordSubRankTableName(boolean mobileRanking, int engineId, int languageId, int dateInt, boolean isHot, boolean isAll) {
        if (isHot) {
            if (mobileRanking) {
                if (engineId == 1 && languageId == 1) {
                    return "m_ranking_subrank_us_" + dateInt;
                } else {
                    return "m_ranking_subrank_intl_" + dateInt;
                }
            } else {
                if (engineId == 1 && languageId == 1) {
                    return "d_ranking_subrank_us_" + dateInt;
                } else {
                    return "d_ranking_subrank_intl_" + dateInt;
                }
            }
        }else {
            if (isAll) {
                if (mobileRanking) {
                    if (engineId == 1 && languageId == 1) {
                        return "all_in_one_m_subrank_us";
                    } else {
                        return "all_in_one_m_subrank_intl";
                    }
                } else {
                    if (engineId == 1 && languageId == 1) {
                        return "all_in_one_d_subrank_us";
                    } else {
                        return "all_in_one_d_subrank_intl";
                    }
                }
            }else {
                if (mobileRanking) {
                    if (engineId == 1 && languageId == 1) {
                        return "m_ranking_subrank_us";
                    } else {
                        return "m_ranking_subrank_intl";
                    }
                } else {
                    if (engineId == 1 && languageId == 1) {
                        return "d_ranking_subrank_us";
                    } else {
                        return "d_ranking_subrank_intl";
                    }
                }
            }
        }
    }
}
