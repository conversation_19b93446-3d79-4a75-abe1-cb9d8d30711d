package seoclarity.backend.InstantDownloadTask;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Data;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import seoclarity.backend.dao.actonia.ApiTaskInstanceEntityDAO;
import seoclarity.backend.dao.actonia.ExportInfoDAO;
import seoclarity.backend.dao.actonia.InstantDownloadInstanceDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
import seoclarity.backend.entity.actonia.ApiTaskInstanceEntity;
import seoclarity.backend.entity.actonia.InstantDownloadInstanceEntity;
import seoclarity.backend.entity.actonia.TExportInfoEntity;
import seoclarity.backend.utils.*;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class InstantDownloadProcessor {

    private final static String SEND_TO = "<EMAIL>";
    //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/1081638150
    private final static String[] CC_TOS = new String[]{};//, "<EMAIL>"
    private final static String Event_KEY_PREFIX_INSTANT = "event_downloadall_instant_ftp_";
    private final static String Event_KEY_PREFIX_TRADITIONAL = "event_downloadall_traditional_ftp_";


    private final static String FILE_PATH_PREFIX = "https://downloads.seoclarity.net/";
    private final static Integer ADD_DAYS = -31;
    private final static Integer SLEEP_TIMES = 5 * 60 * 1000;
    //private final static Integer SLEEP_TIMES = 10 * 1000;
    private InstantDownloadInstanceDAO instantDownloadInstanceDAO;
    private ExportInfoDAO exportInfoDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public InstantDownloadProcessor() {
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        instantDownloadInstanceDAO = SpringBeanFactory.getBean("instantDownloadInstanceDAO");
        exportInfoDAO = SpringBeanFactory.getBean("exportInfoDAO");
    }

    public static void main(String[] args) {
        InstantDownloadProcessor instantDownloadProcessor = new InstantDownloadProcessor();
        while (true) {
            try {
                instantDownloadProcessor.process(Event_KEY_PREFIX_INSTANT);
                instantDownloadProcessor.process(Event_KEY_PREFIX_TRADITIONAL);
                Thread.sleep(SLEEP_TIMES);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    void process(String eventKeyPrefix ) {
        List<String> succKeys = new ArrayList<>();
        List<String> failKeys = new ArrayList<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String createdDateStr = sdf.format(DateUtils.addDays(date, ADD_DAYS));
        List<String> keys = EventUtils.listKeys(eventKeyPrefix);
        if (keys.size() == 0) {
            System.out.println("####noEvents("+eventKeyPrefix+")");
            return;
        }
        for (String key : keys) {
            try {
                String subject = null;
                String resultStr = null;
                String eventJson = EventUtils.get(key.getBytes(StandardCharsets.UTF_8));
                //
                if (StringUtils.isBlank(eventJson)) {
                    System.out.println("####noevent in cache:" + key);
                    EventUtils.deleteKey(key);
                    continue;
                }
                System.out.println("eventJson:" + eventJson);
                Event event = null;
                try {
                    event = JSONObject.parseObject(eventJson, Event.class);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    EventUtils.deleteKey(key);
                    failKeys.add(key);
                    subject = "Error_ParseJson";
                    resultStr = "key:" + key + " eventJson:" + eventJson;

                    sendMail(subject, resultStr);
                    continue;
                }
                switch (eventKeyPrefix)
                {
                    case Event_KEY_PREFIX_INSTANT:
                        updateInstantResultCount(event,key,succKeys,failKeys);
                        break;
                    case Event_KEY_PREFIX_TRADITIONAL:
                        updateTraditionalResultCount(event,key,succKeys,failKeys);
                        break;
                }



                /////////////
                EventUtils.deleteKey(key);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        System.out.println("######eventKeyPrefix:" + eventKeyPrefix + " keysCount:" + keys.size() + " keys:" + String.join(",", keys) +
                " succKeysCount:" + succKeys.size() + " succKeys:" + String.join(",", succKeys) + " failKeysCount:" + failKeys.size() + "  failKeys:" +
                String.join(",", failKeys));
    }
    private void updateTraditionalResultCount(Event event,String key, List<String> succKeys,List<String> failKeys)
    {

        String fileName =event.getFile_name();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        date = DateUtils.addDays(date, ADD_DAYS);
        TExportInfoEntity tExportInfoEntity = exportInfoDAO.getQueryByFileName_CreatedDate(fileName,sdf.format(date) );
        String subject=null;
        String resultStr=null;
        String eventJson=JSONObject.toJSONString(event);
        if (tExportInfoEntity == null) {
                 /*
                Send failure mail(Subject:"Error:NotFoundDownloadInstanceToUpdate") with key name and message info(so we can check the cause),
                then purge the key from CF worker
                * */
            subject = "Error_NotFoundExportInfoToUpdate";
            resultStr = "key:" + key + " eventJson:" + eventJson;
            failKeys.add(key);
            System.out.println("####NotFoundExportInfoToUpdate: " + resultStr);

        } else if (tExportInfoEntity.getResultCount() == null || tExportInfoEntity.getResultCount() == 0) {
            exportInfoDAO.update(tExportInfoEntity.getId(), event.getResult_count());
            succKeys.add(key);
            System.out.println("###succProcKey:"+key);
        } else {
                /*
                Send failure mail(Subject:"Error:DupDownloadInstanceWithResultCount:?") with key name and message info(so we can check the cause),
                * */
            subject = "Error_DupExportInfoWithResultCount_" + tExportInfoEntity.getResultCount();
            resultStr = "key:" + key + " eventJson:" + eventJson + "  ExportInfoEntity:" + JSONObject.toJSONString(tExportInfoEntity);
            failKeys.add(key);
            System.out.println("####DupExportInfoWithResultCount: " + tExportInfoEntity.getResultCount() + "  " + resultStr);
        }
        if (StringUtils.isNotBlank(subject)) {
            sendMail(subject, resultStr);
        }
    }
    private void updateInstantResultCount(Event event,String key, List<String> succKeys,List<String> failKeys)
    {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String createdDateStr = sdf.format(DateUtils.addDays(date, ADD_DAYS));

        String filePath = FILE_PATH_PREFIX + event.getFile_name();
        InstantDownloadInstanceEntity instantDownloadInstanceEntity = instantDownloadInstanceDAO.getQueryByFilePath_CreatedDate(filePath, createdDateStr);
        String subject=null;
        String resultStr=null;
        String eventJson=JSONObject.toJSONString(event);
        if (instantDownloadInstanceEntity == null) {
                 /*
                Send failure mail(Subject:"Error:NotFoundDownloadInstanceToUpdate") with key name and message info(so we can check the cause),
                then purge the key from CF worker
                * */
            subject = "Error_NotFoundDownloadInstanceToUpdate";
            resultStr = "key:" + key + " eventJson:" + eventJson;
            failKeys.add(key);
            System.out.println("####NotFoundDownloadInstanceToUpdate: " + resultStr);

        } else if (instantDownloadInstanceEntity.getResultCount() == null || instantDownloadInstanceEntity.getResultCount() == 0) {
            instantDownloadInstanceDAO.update(instantDownloadInstanceEntity.getId(), event.getResult_count());
            succKeys.add(key);
            System.out.println("###succProcKey:"+key);
        } else {
                /*
                Send failure mail(Subject:"Error:DupDownloadInstanceWithResultCount:?") with key name and message info(so we can check the cause),
                * */
            subject = "Error_DupDownloadInstanceWithResultCount_" + instantDownloadInstanceEntity.getResultCount();
            resultStr = "key:" + key + " eventJson:" + eventJson + "  instantDownloadInstanceEntity:" + JSONObject.toJSONString(instantDownloadInstanceEntity);
            failKeys.add(key);
            System.out.println("####DupDownloadInstanceWithResultCount: " + instantDownloadInstanceEntity.getResultCount() + "  " + resultStr);
        }
        if (StringUtils.isNotBlank(subject)) {
            sendMail(subject, resultStr);
        }
    }
    void sendMail(String subject, String resultStr) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("result", resultStr);
        map.put("dateString", getTodayDateString());
        System.out.println("####sendMail");
//        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(SEND_TO, CC_TOS,
//                subject, "mail_upload_report.txt", "mail_upload_report.html",
//                map, null, ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, null, null);
    }

    private String getTodayDateString() {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        return dateFormat.format(new Date());
    }
}

@Data
class Event {
    private String create_time;
    private String file_name;
    private Integer result_count;
}
