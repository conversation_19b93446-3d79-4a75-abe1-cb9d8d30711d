package seoclarity.backend.tools.sitehealth;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.actonia.gscbigquery.GscBqBulkExportInstanceDAO;
import seoclarity.backend.dao.clickhouse.prodclarity.DisCrawlStatsDao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDocDao;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.summary.SiteHealthErrorPageProcess;
import seoclarity.backend.utils.*;

import java.math.BigInteger;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

// s20
// # check site health upload status
// 37 */1 * * * /home/<USER>/source/checkSiteHealthUploadStatus/clarity-backend-scripts/checkUploadStatus.sh >> /home/<USER>/source/checkSiteHealthUploadStatus/clarity-backend-scripts/log/checkUploadStatus_`date '+\%Y\%m\%d'`.log 2>&1
/**
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.tools.sitehealth.CheckUploadStatusTools" -Dexec.args="" -Dexec.cleanupDaemonThreads=false>log/checkUploadStatusTools-20240104.log 2>&1 &
 */
@CommonsLog
public class CheckUploadStatusTools {

    public static final int FUNC_CL_SITE_AUDIT = 510007;
    public static final int FUNC_CL_INTERNAL_LINKS = 510008;
    public static final int FUNC_CL_SITEMAPS = 510009;

    public static List<Integer> FUNC_ID_LIST = Arrays.asList(FUNC_CL_SITE_AUDIT, FUNC_CL_INTERNAL_LINKS, FUNC_CL_SITEMAPS);
    public static Map<Integer, OwnDomainSettingEntity> ownDomainSettingEntityMap = new HashMap<>();

    public static int MAX_TRY_CNT = 5;

    public static final List<Integer> TEST_DOMAIN_LIST = Arrays.asList(4, 6060, 7741, 8414, 256, 12283);

    public CrawlRequestLogDAO crawlRequestLogDAO;
    public DisCrawlStatsDao disCrawlStatsDao;
    public DisSiteCrawlDocDao disSiteCrawlDocDao;
    public OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    public Clarity360ProjectEntityDAO clarity360ProjectEntityDAO;
    public Clarity360ReportEntityDAO clarity360ReportEntityDAO;
    public GroupRelationshipEntityDAO groupRelationshipEntityDAO;
    public GroupRelationshipFilterEntityDAO groupRelationshipFilterEntityDAO;
    public GscBqBulkExportInstanceDAO gscBqBulkExportInstanceDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    public FunctionRelationDAO functionRelationDAO;
    public OwnDomainGroupRelEntityDao ownDomainGroupRelEntityDao;
    public SiteMapInfoDao siteMapInfoDao;
    public CheckUploadStatusTools() {
        crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
        disSiteCrawlDocDao = SpringBeanFactory.getBean("disSiteCrawlDocDao");
        disCrawlStatsDao = SpringBeanFactory.getBean("disCrawlStatsDao");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        clarity360ReportEntityDAO = SpringBeanFactory.getBean("clarity360ReportEntityDAO");
        clarity360ProjectEntityDAO = SpringBeanFactory.getBean("clarity360ProjectEntityDAO");
        groupRelationshipEntityDAO = SpringBeanFactory.getBean("groupRelationshipEntityDAO");
        groupRelationshipFilterEntityDAO = SpringBeanFactory.getBean("groupRelationshipFilterEntityDAO");
        gscBqBulkExportInstanceDAO = SpringBeanFactory.getBean("gscBqBulkExportInstanceDAO");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        functionRelationDAO = SpringBeanFactory.getBean("functionRelationDAO");
        ownDomainGroupRelEntityDao = SpringBeanFactory.getBean("ownDomainGroupRelEntityDao");
        siteMapInfoDao = SpringBeanFactory.getBean("siteMapInfoDao");
    }

    public static void main(String[] args) {
        String execDomainList = null;
        if (args.length > 0) {
            execDomainList = args[0];
            if (execDomainList.equalsIgnoreCase("all")) {
                execDomainList= null;
            }
        }
        CheckUploadStatusTools tools = new CheckUploadStatusTools();
        tools.process(execDomainList);
    }

    /**
     * 1. 查询当前时间前 1 年的数据
     * 2. 检查和更新
     *  先检查 stats表, eoc数据存在将 upload_status设置为2
     *  eoc数据不存在检查 doc表, doc数据存在将 upload_status设置为 1, doc数据不存在将 upload_status设置为 0;
     */
    private void process(String execDomainList) {
        log.info("$EC=>Start check crawl request upload status!");
        List<Integer> crawlStatusList = Arrays.asList(CrawlRequestLog.CRAWL_STARTED, CrawlRequestLog.CRAWL_COMPLETED, CrawlRequestLog.CRAWL_PROCESSING);
        Date lastYearDate = DateUtils.addYears(new Date(), -1);
        int maxCrawlRequestDate = Integer.parseInt(DateFormatUtils.format(lastYearDate, "yyyyMMdd"));
        List<CrawlRequestLog> crawlRequestLogList =  crawlRequestLogDAO.getNotUploadCompletedCrawlRequestLog(crawlStatusList, maxCrawlRequestDate);
        if (execDomainList != null) {
            String[] split = execDomainList.split(",");
            List<Integer> filteredDomainList = Arrays.stream(split).map(Integer::parseInt).collect(Collectors.toList());
            if (!filteredDomainList.isEmpty()) {
                log.info("execDomainList: " + execDomainList + ", filteredDomainList: " + filteredDomainList);
                crawlRequestLogList = crawlRequestLogList.stream().filter(var->filteredDomainList.contains(var.getOwnDomainId())).collect(Collectors.toList());
            }
        }
        log.info("$EC=>Find " + crawlRequestLogList.size() + " crawl need to be check upload status.");
        for (CrawlRequestLog crawlRequestLog : crawlRequestLogList) {
            Integer ownDomainId = crawlRequestLog.getOwnDomainId();
            Integer crawlRequestLogId = crawlRequestLog.getId();
            BigInteger crawlRequestProjectId = crawlRequestLog.getProjectId();
            log.info("$EC=>Start check crawl: " + crawlRequestLogId + ", OID: " + ownDomainId);
            boolean isEndOfCrawl = getCrawlAPICompletedStatus(crawlRequestLogId);
            log.info("$EC=>Check crawl: " + crawlRequestLogId + ",  end status: " + isEndOfCrawl);
            if (isEndOfCrawl) {
                crawlRequestLogDAO.updateUploadStatus(crawlRequestLogId, CrawlRequestLog.UPLOAD_STATUS_COMPLETED);
                log.info("$EC=>updated crawl: " + crawlRequestLogId);
                try {
                    Date crawlCreateTime;
                    if (crawlRequestLog.getCreationTimestamp() != null) {
                        crawlCreateTime = crawlRequestLog.getCreationTimestamp();
                    } else {
                        crawlCreateTime = DateUtils.parseDate(String.valueOf(crawlRequestLog.getCrawlRequestDate()), "yyyyMMdd");
                    }
                    boolean processedC360Report = processC360Report(ownDomainId, crawlRequestLogId, crawlRequestProjectId, crawlCreateTime);

                    if (!processedC360Report) {
                        try {
                            check360Report(crawlRequestLog, crawlCreateTime);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    String subject = "Update clarity 360 report params error";
                    sendEmail(ownDomainId, crawlRequestLogId, crawlRequestProjectId, e.getMessage(), subject);
                }

                // https://www.wrike.com/open.htm?id=**********
                try {
                    SiteHealthErrorPageProcess.insertErrorPageInfo(ownDomainId, crawlRequestLogId);
                } catch (Exception e) {
                    e.printStackTrace();
                    String subject = "Insert clarity 360 ErrorPage error";
                    sendEmail(ownDomainId, crawlRequestLogId, crawlRequestProjectId, e.getMessage(), subject);
                }
            } else {
                boolean isExists;
                int tryCntDoc = 0;
                while(true){
                    try {
                        isExists = disSiteCrawlDocDao.checkIsExists(ownDomainId, crawlRequestLogId);
                        break;
                    } catch (Exception e) {
                        String errorMsg = CollectionSplitUtils.getErrorMsg(e);
                        if (tryCntDoc > MAX_TRY_CNT) {
                            throw new RuntimeException("ClarityDB DOC Exception LIMIT, errorMsg: " + errorMsg);
                        }
                        try {
                            Thread.sleep(2 * 1000);
                        } catch (InterruptedException ex) {
                            throw new RuntimeException(ex);
                        }
                    }
                }

                log.info("$EC=>Check crawl: " + crawlRequestLogId + ", crawl doc exist: " + isExists);
                if (isExists) {
                    crawlRequestLogDAO.updateUploadStatus(crawlRequestLogId, CrawlRequestLog.UPLOAD_STATUS_UPLOADING);
                } else {
                    crawlRequestLogDAO.updateUploadStatus(crawlRequestLogId, CrawlRequestLog.UPLOAD_STATUS_NOT_START);
                }
            }
            try {
                Thread.sleep(5);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        log.info("$EC=>Check crawl request upload status end, exiting...");
    }

    private void sendEmail(Integer ownDomainId, Integer crawlRequestLogId, BigInteger crawlRequestProjectId, String message, String subject) {
        String title = subject + ", please check s20: /home/<USER>/source/checkSiteHealthUploadStatus/clarity-backend-scripts/";
        try {
            Map<String, Object> reportMap = new HashMap<>();
            reportMap.put("userName", "Ewain");
            reportMap.put("dateString", org.apache.commons.lang.time.DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
            reportMap.put("title", title);

            if (org.apache.commons.lang.StringUtils.isEmpty(message)) {
                reportMap.put("errormessage", "");
            } else {
                reportMap.put("errormessage", message + "=== OID: " + ownDomainId + ", crawlRequestLogId: " + crawlRequestLogId);
            }
             String emailTo = "<EMAIL>";
            zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, null, subject, "mail_common.txt", "mail_common.html", reportMap, null,
                    ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean processC360Report(Integer ownDomainId, Integer crawlRequestLogId, BigInteger crawlRequestProjectId, Date crawlCreateTime) {
        Date now = new Date();

        Date allowWithinDate = DateUtils.addMonths(now , -6);

        // 360 Report
        Clarity360ReportEntity clarity360ReportEntity = getAssociate360Report(ownDomainId, crawlRequestLogId, crawlRequestProjectId);

        if (clarity360ReportEntity == null) {
            log.info("REPORT no report need to be update!");
            return false;
        }

        Date siteAuditLastDataDate = getSiteAuditLastDataDate(ownDomainId, crawlRequestLogId);

        /**
         * generateBy = 0: skip
         * generateBy = 1:
         *      DateRange: use clarity_360_report.metricsDateRange
         *          paramJson contains: {{startDate}}/{{endDate}}: 查询所有数据源, 获取最早的完成时间.
         *              update processStatus, startDate, endDate, siteAuditCompletedDate, paramJson;
         *          paramJson not contains: {{startDate}}/{{endDate}}: 不查询其他数据源, 仅使用SiteAudit最后完成时间.
         *              update processStatus, startDate, endDate, siteAuditCompletedDate
         * generateBy = 2:
         *      update processStatus, siteAuditCompletedDate, startDate, endDate
         *      use default date range: 30
         *
         */
        clarity360ReportEntityDAO.updateProcessStatus(clarity360ReportEntity.getId(), clarity360ReportEntity.getOwnDomainId(), Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS, siteAuditLastDataDate);

        int generateBy = clarity360ReportEntity.getGenerateBy();
        if(generateBy == Clarity360ReportEntity.GENERATE_BY_SITE_AUDIT_GLOBAL) {
            //use default date range: 30
            //from M: by default the date range for all Clarity360 light reports should be start date of the crawl - 30, So if crawl started on May 20th, the date for the C360 Light report shoudl be set as April 20th to May 20th
            //from Scott: as discussed, the summary script will contains both start date and end date, so it should use start date - (30 -1)
            Date startDate = DateUtils.addDays(crawlCreateTime, -(30 - 1));
            clarity360ReportEntityDAO.updateSDateAndEDate(clarity360ReportEntity.getId(), clarity360ReportEntity.getOwnDomainId(), startDate, crawlCreateTime);
            log.info("360 light report: update sDate/eDate use default date range, s: " + startDate + ", e: " + crawlCreateTime);
            return true;
        }

        String paramJson = clarity360ReportEntity.getParamJson();
        if (!paramJson.contains("{{startDate}}") || !paramJson.contains("{{endDate}}")) {
            log.info("REPORT: ==no paramJson need to be update! Just need update processStatus!");
            Date needProcessDate;
            try {
                needProcessDate = DateUtils.parseDate("1970-01-01", "yyyy-MM-dd");
            } catch (ParseException e) {
                String subject = "Check upload status tools: Parser date error";
                sendEmail(ownDomainId, crawlRequestLogId, crawlRequestProjectId, e.getMessage(), subject);
                throw new RuntimeException(e);
            }
            Date startDateDB = clarity360ReportEntity.getStartDate();
            Date endDateDB = clarity360ReportEntity.getEndDate();
            log.info("REPORT: == checkDate == startDateDB: " + startDateDB + ", endDateDB: " + endDateDB + ", siteAuditLastDataDate: " + siteAuditLastDataDate);
            if (siteAuditLastDataDate != null && DateUtils.isSameDay(startDateDB, needProcessDate) && DateUtils.isSameDay(endDateDB, needProcessDate)) {
                int uiDateRange = Clarity360ReportEntity.DEFAULT_SITE_AUDIT_DATE_RANGE;
                Integer metricsDateRange = clarity360ReportEntity.getMetricsDateRange();
                if (metricsDateRange != null && metricsDateRange != 0) {
                    uiDateRange = metricsDateRange;
                }
                Date startDate = DateUtils.addDays(siteAuditLastDataDate, -uiDateRange+1);
                clarity360ReportEntityDAO.updateSDateAndEDate(clarity360ReportEntity.getId(), clarity360ReportEntity.getOwnDomainId(), startDate, siteAuditLastDataDate);
                log.info("Report: only UPDATE startDate, endDate...");
            }
            return true;
        } else {
            log.info("REPORT: ==get report id: " + clarity360ReportEntity.getId() + ", paramJson: " + paramJson);
        }

        Map<String, Boolean> map = checkDatasourceAvailable(paramJson);

        log.info("query map: " + map);

        boolean isBigQuery = isBigQueryFor360Report(map);
        boolean isSkipGsc = false;
        // relation ids
        List<Integer> relIdList = getGscRelIdsFrom360Report(paramJson);
        if (relIdList.isEmpty()) {
            isSkipGsc = true;
        }
        log.info("GSC relIdList: " + relIdList);

        List<Integer> gscBaseDomainIds = new ArrayList<>();
        // gsc base domain
        List<OwnDomainSettingEntity> gscBaseDomains = ownDomainSettingEntityDAO.getGscBaseDomain(ownDomainId);
        if (gscBaseDomains.isEmpty()) {
            log.info("GSC no gscBaseDomain!");
            gscBaseDomainIds.add(ownDomainId);
        } else {
            gscBaseDomainIds.addAll(gscBaseDomains.stream().map(OwnDomainSettingEntity::getOwnDomainId).collect(Collectors.toList()));
        }
        log.info("GSC gsaBaseDomainIds: " + gscBaseDomainIds);

        Date gaLastDataDate = getGaLastDataDate(ownDomainId, allowWithinDate);

        Date gscLastDataDate;
        if (isBigQuery) {
            gscLastDataDate = getGscBigQueryLastDataDate(ownDomainId, allowWithinDate);
        } else {
            gscLastDataDate = getGscLastDataDate(isSkipGsc, ownDomainId, gscBaseDomainIds, relIdList, allowWithinDate);;
        }

        Date lastDataDate = getLastDate(map, now, allowWithinDate, gscLastDataDate, gaLastDataDate, siteAuditLastDataDate);

        updateC360Report(ownDomainId, lastDataDate, siteAuditLastDataDate, crawlCreateTime, clarity360ReportEntity);

        return true;
    }

    // map keys: ga/gsc/gscbigquery
    private Map<String, Boolean> checkDatasourceAvailable(String paramJson) {

        Map<String, Boolean> result = new HashMap<>();
        result.put("ga", false);
        result.put("gsc", false);
        result.put("gscbigquery", false);

        JSONObject jsonObject = JSONObject.parseObject(paramJson);
        if (jsonObject.containsKey("sources")) {
            JSONArray sources = jsonObject.getJSONArray("sources");
            for (Object source : sources) {
                JSONObject sourceJsonObj = JSONObject.parseObject(source.toString());
                // GSC
                if (sourceJsonObj.containsKey("searchAnalytics")) {
                    JSONObject searchAnalyticsObj = sourceJsonObj.getJSONObject("searchAnalytics");
                    if (searchAnalyticsObj.containsKey("bigQuery")) {
                        Boolean bigQuery = searchAnalyticsObj.getBoolean("bigQuery");
                        if (bigQuery != null && bigQuery) {
                            result.put("gscbigquery", true);
                        } else {
                            result.put("gsc", true);
                        }
                    }
                }

                //GA
                if (sourceJsonObj.containsKey("siteAnalytics")) {
                    result.put("ga", true);
                }
            }
        }
        return result;
    }

    private void updateC360Report(Integer ownDomainId, Date lastDataDate, Date siteAuditCompletedDate, Date crawlCreateTime, Clarity360ReportEntity clarity360ReportEntity) {
        Integer metricsDateRange = clarity360ReportEntity.getMetricsDateRange();
        if (metricsDateRange != null) {
            /**
             * from scoot
             * 如果 metricsDateRange = 7
             * 现在start date - end date 日期范围计算出来时8天。。
             * reportid  3011
             * 可能是 用的 start date - metricsDateRange
             * 但是 summary 的正常逻辑是会包含 start date 和 enddate
             * 所以就变成了8天
             */
            Date startDate = DateUtils.addDays(lastDataDate, -metricsDateRange+1);
            Date endDate = lastDataDate;

            // https://www.wrike.com/open.htm?id=1460475489
            if (metricsDateRange == Clarity360ReportEntity.DATE_RANGE_LAST_MONTH) {
                // crawlCreateTime: 2024-08-12 16:34:01
                // ceiling: 2024-09-01 00:00:00
                Date ceiling = DateUtils.ceiling(crawlCreateTime, Calendar.MONTH);
                System.out.println(DateFormatUtils.format(ceiling, "yyyy-MM-dd HH:mm:ss.SSS"));

                // startDate: 2024-07-01 00:00:00
                // endDate: 2024-07-31 00:00:00
                startDate = DateUtils.addMonths(ceiling, -2);
                endDate = DateUtils.addDays(DateUtils.addMonths(ceiling, -1),-1);
            }
            
            String startDateStr = DateFormatUtils.format(startDate, "yyyy-MM-dd");
            String endDateStr = DateFormatUtils.format(endDate, "yyyy-MM-dd");

            String initialDateStr  = "1970-01-01";

            Date startDateDB = clarity360ReportEntity.getStartDate();
            Date endDateDB = clarity360ReportEntity.getEndDate();

            String startDateDBStr;
            if (startDateDB != null) {
                startDateDBStr = DateFormatUtils.format(startDateDB, "yyyy-MM-dd");
            } else {
                startDateDBStr = null;
            }

            String endDateDBStr;
            if (endDateDB != null) {
                endDateDBStr = DateFormatUtils.format(endDateDB, "yyyy-MM-dd");
            } else {
                endDateDBStr = null;
            }

            String paramJson = clarity360ReportEntity.getParamJson();
            String replaced = StringUtils.replace(paramJson, "{{startDate}}", startDateStr);
            replaced = StringUtils.replace(replaced, "{{endDate}}", endDateStr);

            if (siteAuditCompletedDate == null) {
                siteAuditCompletedDate = new Date();
            }

            log.info("updateC360Report id:" + clarity360ReportEntity.getId() + ", paramJson: " + replaced
                    + ", startDateStr: " + startDateStr + ", endDate: " + endDateStr
                    + ", startDateDB: " + startDateDB + ", endDateDB: " + endDateDB
                    + ", ownDomainId: " + ownDomainId);

            if ((startDateDB == null && endDateDB == null) || (startDateDB != null && endDateDB != null && startDateDBStr.equals(initialDateStr) && endDateDBStr.equals(initialDateStr))) {
                log.info("updateC360Report json and date");
                clarity360ReportEntityDAO.updateParamJsonAndDate(clarity360ReportEntity.getId(), ownDomainId, replaced, startDate, endDate,
                        Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS, siteAuditCompletedDate);
            } else {
                log.info("updateC360Report json");
                clarity360ReportEntityDAO.updateParamJson(clarity360ReportEntity.getId(), ownDomainId, replaced,
                        Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS, siteAuditCompletedDate);
            }
        }
    }

    private boolean isBigQueryFor360Report(Map<String, Boolean> map) {
        Boolean gsc = map.get("gsc");
        Boolean gscbigquery = map.get("gscbigquery");
        if (gscbigquery && gsc) {
            log.info("Error gsc type!!");
            return false;
        } else if (gscbigquery && !gsc) {
            return true;
        } else if (!gscbigquery && gsc){
            return false;
        } else {
            return false;
        }
    }

    private List<Integer> getGscRelIdsFrom360Report(String paramJson) {
        List<Integer> result = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(paramJson);
        if (jsonObject.containsKey("sources")) {
            JSONArray sources = jsonObject.getJSONArray("sources");
            for (Object source : sources) {
                JSONObject sourceJsonObj = JSONObject.parseObject(source.toString());
                if (sourceJsonObj.containsKey("searchAnalytics")) {
                    JSONObject searchAnalyticsObj = sourceJsonObj.getJSONObject("searchAnalytics");
                    if (searchAnalyticsObj.containsKey("relIds")) {
                        JSONArray relIds = searchAnalyticsObj.getJSONArray("relIds");
                        result.addAll(relIds.toJavaList(Integer.class));
                        return result;
                    } else {
                        log.info("GSC: NO relIds!");
                        return null;
                    }
                }
            }
        } else {
            log.info("GSC: NO sources!");
        }
        return result;
    }

    public boolean getCrawlAPICompletedStatus(Integer crawlRequestLogId) {
        String url = "http://*************:8182/seoClarity/siteClarity/getCrawlerStats?crawl_request_log_id_i=" + crawlRequestLogId + "&clusterCrawl=true&accessToken=c09yxv13-opr3-d745-9734-8pu48420nj67";
        int timeout = 5000;
        String respStr = HttpRequestUtils.simpleGetV2(url, null, timeout, null);
        if (respStr != null) {
            JSONObject jsonObject = JSONObject.parseObject(respStr);
            if (jsonObject.containsKey("completed")) {
                try {
                    return jsonObject.getBoolean("completed");
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("Request api error, please check.");
                }
            }
        }
        return false;
    }

    private Clarity360ReportEntity getAssociate360Report(Integer ownDomainId, Integer crawlRequestLogId, BigInteger crawlRequestProjectId) {
        return clarity360ReportEntityDAO.get360ReportByCrawlRequest(ownDomainId, crawlRequestLogId, crawlRequestProjectId);
    }

    private Date getGscLastDataDate(boolean isSkipGsc, Integer ownDomainId, List<Integer> gsaBaseDomainIds, List<Integer> relIdList, Date allowWithinDate) {
        if (isSkipGsc) {
            return null;
        }

        Integer[] acrossDomainIds = gsaBaseDomainIds.toArray(new Integer[0]);
        Integer[] relIds = relIdList.toArray(new Integer[0]);
        String startDate1 = DateFormatUtils.format(allowWithinDate, "yyyy-MM-dd");

        String gscQueryUrl = "http://*************:8183/seoClarity/gsc/lastFinalDate";

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("accessToken", "c09yxv13-opr3-d745-9734-8pu48420nj67");
        paramMap.put("bigQueryFlg", "false");
        paramMap.put("dataType", 1);
        paramMap.put("ownDomainId", ownDomainId);
        paramMap.put("acrossDomainIds", acrossDomainIds);
        paramMap.put("startDate1", startDate1);
        paramMap.put("relIds", relIds);

        log.info("GSC Request, URL: " + gscQueryUrl + ", Params: " + new Gson().toJson(paramMap));

        String apiResponse = null;
        try {
            apiResponse = ClarityDBAPIUtils.simplePost(gscQueryUrl, new Gson().toJson(paramMap));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        if (apiResponse != null && !apiResponse.isEmpty()) {
            JSONObject jsonObject = JSONObject.parseObject(apiResponse);
            if (jsonObject.containsKey("data")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    JSONObject dateObj = jsonArray.getJSONObject(0);
                    if (dateObj.containsKey("logDate")) {
                        String logDateStr = dateObj.getString("logDate");
                        if (logDateStr != null && !logDateStr.contains("1970")) {
                            Date logDate = null;
                            try {
                                logDate = DateUtils.parseDate(logDateStr, "yyyy-MM-dd");
                            } catch (ParseException e) {
                                e.printStackTrace();
                                return null;
                            }
                            log.info("GSC lastAvailableDate: " + logDate);
                            return logDate;
                        }
                    }
                }
            }
        }
        return null;
    }

    private Date getGaLastDataDate(Integer ownDomainId, Date allowWithinDate) {
        Integer groupId = null;
        GroupRelationshipEntity groupRelationshipEntity = groupRelationshipEntityDAO.get(ownDomainId);
        if (groupRelationshipEntity != null) {
            groupId = groupRelationshipEntity.getGroupId();
        }

        String filter = null;
        GroupRelationshipFilterEntity filterEntity = groupRelationshipFilterEntityDAO.getByDomainIdStatus(ownDomainId, GroupRelationshipFilterEntity.STATUS_ACTIVE);
        if (filterEntity != null) {
            filter = filterEntity.getFilter();
        }

        OwnDomainSettingEntity settingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);
        Integer trafficType = settingEntity.getTrafficType();
        if (trafficType == null) {
            return null;
        }

        int dataSourceType = trafficType % 10;

        String gaQueryUrl = "http://*************:8183/seoClarity/ga/domainInfo";
        String startDate = DateFormatUtils.format(allowWithinDate, "yyyy-MM-dd");

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("accessToken", "c09yxv13-opr3-d745-9734-8pu48420nj67");
        paramMap.put("analyticsGroupId", groupId);
        paramMap.put("analyticsGroupFilter", filter);
        paramMap.put("dataSourceType", dataSourceType);
        paramMap.put("domainId", ownDomainId);
        paramMap.put("startDate", startDate);
        paramMap.put("ignoreFreshDataFlag", true);

        log.info("GA Request, URL: " + gaQueryUrl + ", Params: " + new Gson().toJson(paramMap));

        String apiResponse = null;
        try {
            apiResponse = ClarityDBAPIUtils.simplePost(gaQueryUrl, new Gson().toJson(paramMap));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        if (apiResponse != null && !apiResponse.isEmpty()) {
            JSONObject jsonObject = JSONObject.parseObject(apiResponse);
            if (jsonObject.containsKey("data")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    JSONObject dateObj = jsonArray.getJSONObject(0);
                    if (dateObj.containsKey("lastAvailableDate")) {
                        String lastAvailableDateStr = dateObj.getString("lastAvailableDate");
                        if (lastAvailableDateStr != null && !lastAvailableDateStr.contains("1970")) {
                            Date lastAvailableDate = null;
                            try {
                                lastAvailableDate = DateUtils.parseDate(lastAvailableDateStr, "yyyy-MM-dd");
                            } catch (ParseException e) {
                                e.printStackTrace();
                                return null;
                            }
                            log.info("GA lastAvailableDate: " + lastAvailableDate);
                            return lastAvailableDate;
                        }
                    }
                }
            }
        }
        return null;
    }

    private Date getGscBigQueryLastDataDate(Integer ownDomainId, Date allowWithinDate) {
        String lastDataDate = gscBqBulkExportInstanceDAO.getLastDataDateByDomainId(ownDomainId);
        if (lastDataDate != null) {
            Date date = null;
            try {
                date = DateUtils.parseDate(lastDataDate, "yyyy-MM-dd");
            } catch (ParseException e) {
                e.printStackTrace();
            }
            return date;
        }
        return null;
    }

    private Date getSiteAuditLastDataDate(Integer ownDomainId, Integer crawlRequestLogId) {
        String maxCreateTime = disSiteCrawlDocDao.getLastDataRequestTime(ownDomainId, crawlRequestLogId);
        if (!StringUtils.isEmpty(maxCreateTime)) {
            try {
                return DateUtils.parseDate(maxCreateTime, "yyyy-MM-dd HH:mm:ss");
            } catch (ParseException e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    // map keys: ga/gsc/gscbigquery
    private Date getLastDate(Map<String, Boolean> map, Date now, Date allowWithinDate, Date gscLastDataDate, Date gaLastDataDate, Date siteAuditLastDataDate) {
        log.info("start compare date=> " +
                "now: " + (now == null ? null : DateFormatUtils.format(now, "yyyy-MM-dd HH:mm:ss")) + ", " +
                "allowWithinDate: " + (allowWithinDate == null ? null : DateFormatUtils.format(allowWithinDate, "yyyy-MM-dd HH:mm:ss")) + ", " +
                "gscLastDataDate: " + (gscLastDataDate == null ? null : DateFormatUtils.format(gscLastDataDate, "yyyy-MM-dd HH:mm:ss")) + ", " +
                "gaLastDataDate: " + (gaLastDataDate == null ? null : DateFormatUtils.format(gaLastDataDate, "yyyy-MM-dd HH:mm:ss")) + ", " +
                "siteAuditLastDataDate: " + (siteAuditLastDataDate == null ? null : DateFormatUtils.format(siteAuditLastDataDate, "yyyy-MM-dd HH:mm:ss")));

        Date earlistDataDate = now;

        if (gscLastDataDate != null && (map.get("gsc") || map.get("gscbigquery"))) {
            earlistDataDate = earlistDataDate.compareTo(gscLastDataDate) < 0 ? earlistDataDate : gscLastDataDate;
        }

        if (gaLastDataDate != null && map.get("ga")) {
            earlistDataDate = earlistDataDate.compareTo(gaLastDataDate) < 0 ? earlistDataDate : gaLastDataDate;
        }

        if (siteAuditLastDataDate != null) {
            earlistDataDate = earlistDataDate.compareTo(siteAuditLastDataDate) < 0 ? earlistDataDate : siteAuditLastDataDate;
        }

        if (earlistDataDate.equals(now)) {
            earlistDataDate = allowWithinDate;
        }

        return earlistDataDate;
    }

    public void check360Report(CrawlRequestLog crawlRequestLog, Date endDate) {
        int domainId = crawlRequestLog.getOwnDomainId();
        int crawlId = crawlRequestLog.getId();
        log.info("Start check 360 report, domainId: " + domainId + ", crawlId: " + crawlId);

        boolean needCheck360Report = false;

        boolean isC360LightEnabledSiteAudit = false;
        boolean isC360LightEnabledInternalLink = false;
        boolean isC360LightEnabledSitemap = false;

        log.info("Start check group func, domainId: " + domainId);
        List<OwnDomainGroupRelEntity> ownDomainGroupRelEntities = ownDomainGroupRelEntityDao.checkBelongGroup(domainId);
        if (ownDomainGroupRelEntities != null && !ownDomainGroupRelEntities.isEmpty()) {
            OwnDomainGroupRelEntity ownDomainGroupRelEntity = ownDomainGroupRelEntities.get(0);
            long groupId = ownDomainGroupRelEntity.getGroupId();
            log.info("Find domains group, domainId: " + domainId + ", groupId: " + groupId);
            List<FunctionRelationEntity_Test> functionRelationEntityList = functionRelationDAO.checkFuncRel(groupId, FunctionRelationEntity_Test.FUNC_RESOURCE_TYPE_GROUP, FUNC_ID_LIST);
            if (!functionRelationEntityList.isEmpty()) {
                Map<Integer, String> funcMap = functionRelationEntityList.stream().collect(Collectors.toMap(FunctionRelationEntity_Test::getFuncId, FunctionRelationEntity_Test::getValue));
                log.info("Find funcMap for domainId: " + domainId + "== groupId: " + groupId + ", funcMap: " + funcMap);
                if (funcMap.containsKey(FUNC_CL_SITE_AUDIT)) {
                    needCheck360Report = true;
                    for (Integer funcId : funcMap.keySet()) {
                        String funcValue = funcMap.get(funcId);
                        switch (funcId) {
                            case FUNC_CL_SITE_AUDIT:
                                isC360LightEnabledSiteAudit = Boolean.parseBoolean(funcValue); break;
                            case FUNC_CL_INTERNAL_LINKS:
                                isC360LightEnabledInternalLink = Boolean.parseBoolean(funcValue); break;
                            case FUNC_CL_SITEMAPS:
                                isC360LightEnabledSitemap = Boolean.parseBoolean(funcValue); break;
                            default:

                        }
                    }
                }
            }
        }
        log.info("End check group func, domainId: " + domainId + ", needCheck360Report: " + needCheck360Report + ", isC360LightEnabledSiteAudit: " + isC360LightEnabledSiteAudit + ", isC360LightEnabledInternalLink: " + isC360LightEnabledInternalLink + ", isC360LightEnabledSitemap: " + isC360LightEnabledSitemap);

        log.info("Start check domain func, domainId: " + domainId);
        List<FunctionRelationEntity_Test> functionRelationEntityList = functionRelationDAO.checkFuncRel(domainId, FunctionRelationEntity_Test.FUNC_RESOURCE_TYPE_DOMAIN, FUNC_ID_LIST);
        if (!functionRelationEntityList.isEmpty()) {
            Map<Integer, String> funcMap = functionRelationEntityList.stream().collect(Collectors.toMap(FunctionRelationEntity_Test::getFuncId, FunctionRelationEntity_Test::getValue));
            log.info("Find funcMap for domainId: " + domainId + ", funcMap: " + funcMap);
            if (funcMap.containsKey(FUNC_CL_SITE_AUDIT)) {
                needCheck360Report = true;
                for (Integer funcId : funcMap.keySet()) {
                    String funcValue = funcMap.get(funcId);
                    switch (funcId) {
                        case FUNC_CL_SITE_AUDIT:
                            isC360LightEnabledSiteAudit = Boolean.parseBoolean(funcValue); break;
                        case FUNC_CL_INTERNAL_LINKS:
                            isC360LightEnabledInternalLink = Boolean.parseBoolean(funcValue); break;
                        case FUNC_CL_SITEMAPS:
                            isC360LightEnabledSitemap = Boolean.parseBoolean(funcValue); break;
                        default:

                    }
                }
            } else {
                needCheck360Report = false;
            }
        }
        log.info("End check domain func, domainId: " + domainId + ", needCheck360Report: " + needCheck360Report + ", isC360LightEnabledSiteAudit: " + isC360LightEnabledSiteAudit + ", isC360LightEnabledInternalLink: " + isC360LightEnabledInternalLink + ", isC360LightEnabledSitemap: " + isC360LightEnabledSitemap);

        if (!needCheck360Report) {
            return;
        }

        Date siteAuditLastDataDate = getSiteAuditLastDataDate(domainId, crawlId);

        Date startDate = DateUtils.addDays(endDate, -30);

        List<SiteMapInfoEntity> siteMapList = siteMapInfoDao.getSiteMapList(domainId, Integer.parseInt(DateFormatUtils.format(getSitemapLastAvailableDate(), "yyyyMMdd")));
        log.info("Find siteMapList, domainId: " + domainId + ", size: " + siteMapList.size());

        OwnDomainSettingEntity ownDomainSettingEntity;
        if (ownDomainSettingEntityMap.containsKey(domainId)) {
            ownDomainSettingEntity = ownDomainSettingEntityMap.get(domainId);
        } else {
            ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);
            ownDomainSettingEntityMap.put(domainId, ownDomainSettingEntity);
        }
        createClarity360LightReport(crawlRequestLog, isC360LightEnabledSiteAudit, isC360LightEnabledInternalLink, isC360LightEnabledSitemap, siteMapList, startDate, endDate, siteAuditLastDataDate, ownDomainSettingEntity);

    }

    public Date getSitemapLastAvailableDate() {
        Date today = new Date();
        Date currMonday = FormatUtils.getDateByStartDayOfWeek(today, 1);
        Date previousMonday = DateUtils.addDays(currMonday, -7); //Cdb date is Monday
        Date previousSunday = DateUtils.addDays(previousMonday, -1); //MySQL date is Sunday
        return previousSunday;
    }

    public Clarity360ReportEntity createClarity360LightReport(CrawlRequestLog crawlLog,
                                                        boolean isC360LightEnabledSiteAudit,
                                                        boolean isC360LightEnabledInternalLink,
                                                        boolean isC360LightEnabledSitemap,
                                                        List<SiteMapInfoEntity> siteMapList,
                                                        Date startDate,
                                                        Date endDate,
                                                        Date siteAuditCompletedDate,
                                                        OwnDomainSettingEntity ownDomainSetting) {

        if (!isC360LightEnabledSiteAudit) {
            return null;
        }
        int ownDomainId = crawlLog.getOwnDomainId();
        int userId = crawlLog.getUserId();
        String projectTitle = crawlLog.getProjectName();
        Integer siteAuditCrawlId = crawlLog.getId();
        Long siteAuditProjectId = crawlLog.getProjectId() == null ? 0 : crawlLog.getProjectId().longValue();
        Integer hasInternalLinks = crawlLog.getGeneratePageLinks();

        log.info("crawlLog: ownDomainId:" + crawlLog.getOwnDomainId() + ", userId: " + crawlLog.getUserId() + ", projectTitle: " + crawlLog.getProjectName() + ", siteAuditCrawlId: " + crawlLog.getId() + ", siteAuditProjectId: " + crawlLog.getProjectId().longValue() + ", hasInternalLinks: " + crawlLog.getGeneratePageLinks());

        //generate uiJson / paramJson
        Clarity360ReportEntity jsonTemplate = generateC360LightReportJSON(
                ownDomainId,
                projectTitle,
                isC360LightEnabledSiteAudit,
                isC360LightEnabledInternalLink,
                isC360LightEnabledSitemap,
                hasInternalLinks,
                siteMapList);

        String crawlTime = FormatUtils.formatDate(new Date(), "HH:mm:ss.SSS");
        String crawlDate = FormatUtils.formatYyyyMMddToStr(crawlLog.getCrawlRequestDate());
        String c360ReportTitle = crawlDate + " " + crawlTime;

        //=======================================================================
        //genrate 360 report uiJson
        String uiJson = jsonTemplate.getUiJson();
        uiJson = StringUtils.replace(uiJson, "{{siteAuditProjectId}}", String.valueOf(siteAuditProjectId));
        uiJson = StringUtils.replace(uiJson,  "{{siteAuditCrawlId}}", String.valueOf(siteAuditCrawlId));

        //genrate 360 report paramJson
        String paramJson = jsonTemplate.getParamJson();
        paramJson = StringUtils.replace(paramJson,  "{{siteAuditCrawlId}}", String.valueOf(siteAuditCrawlId));

        String paramHash = FormatUtils.md5Hex(paramJson + DateFormatUtils.format(new Date(), "MM/dd/yyyy") + siteAuditCrawlId + c360ReportTitle);

        //to get or create 360 project ---------------
        Clarity360ReportEntity baseReport = new Clarity360ReportEntity();
        baseReport.setOwnDomainId(ownDomainId);
        baseReport.setSiteAuditProjectId(siteAuditProjectId);
        baseReport.setCreateUserId(userId);
        baseReport.setTitle(projectTitle);
        baseReport.setProjectTitle(projectTitle);
        baseReport.setUiJson(uiJson);
        baseReport.setParamJson(paramJson);
        baseReport.setSiteAuditCrawlId(crawlLog.getId());

        Clarity360ProjectEntity clarity360Project = createOrGet360ProjectForSiteAudit(ownDomainId, baseReport, true);

        //create 360 report ---------------
        Clarity360ReportEntity report = new Clarity360ReportEntity();
        report.setOwnDomainId(ownDomainId);
        report.setCreateUserId(userId);
        report.setGenerateBy(Clarity360ReportEntity.GENERATE_BY_360_LIMITED_TASK);
        report.setTitle(c360ReportTitle);
        report.setProjectTitle(projectTitle);
        report.setProcessStatus(Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS);
        report.setMetricsDateRange(0);
        report.setSiteAuditCrawlId(siteAuditCrawlId);
        report.setSiteAuditProjectId(siteAuditProjectId);
        report.setStartDate(startDate);
        report.setEndDate(endDate);
        report.setUiJson(uiJson);
        report.setParamJson(paramJson);
        report.setParamHash(paramHash);
        report.setSiteAuditCompletedDate(siteAuditCompletedDate);
        report.setProjectId(clarity360Project.getId());

        setupC360ReportStageEnabled(report, ownDomainSetting);

        String hashType = "std"; //default
        if (ownDomainSetting != null && StringUtils.equals(ownDomainSetting.getClarity360SummaryHashType(), "norm")) {
            hashType = "norm";
        }


        report.setClarity360SummaryHashType(hashType);

        return createNewReport(report);
    }

    public Clarity360ProjectEntity createOrGet360ProjectForSiteAudit(int ownDomainId, Clarity360ReportEntity report, boolean forC360Light) {

        Clarity360ProjectEntity c360project = clarity360ProjectEntityDAO.getByTitle(ownDomainId, report.getProjectTitle());
        if (c360project != null) {
            log.info("c360project is not null. ownDomainId: " + ownDomainId + ", crawlId: " + report.getSiteAuditCrawlId() + ", title: " + report.getProjectTitle());
            if (!c360project.isEnabled()) {
                log.info("c360project is not null and c360project not enable. ownDomainId: " + ownDomainId + ", crawlId: " + report.getSiteAuditCrawlId() + ", title: " + report.getProjectTitle());
                clarity360ProjectEntityDAO.enableProject(ownDomainId, c360project.getId());
            }
            return c360project;
        }

        Clarity360ProjectEntity newProject = new Clarity360ProjectEntity();
        newProject.setOwnDomainId(ownDomainId);
        newProject.setSiteAuditProjectId(report.getSiteAuditProjectId());
        newProject.setTitle(report.getProjectTitle());
        newProject.setUiJson(report.getUiJson());
        newProject.setParamJson(report.getParamJson());
        newProject.setCreateUser(report.getCreateUserId());

        log.info("c360project is null, create new project. ownDomainId: " + ownDomainId + ", crawlId: " + report.getSiteAuditCrawlId() + ", title: " + report.getProjectTitle());
        int newProjectId = clarity360ProjectEntityDAO.insert(newProject);
        newProject.setId(newProjectId);

        return newProject;
    }



    public Clarity360ReportEntity generateC360LightReportJSON(int ownDomainId,
                                                        String projectTitle,
                                                        boolean isC360LightEnabledSiteAudit,
                                                        boolean isC360LightEnabledInternalLink,
                                                        boolean isC360LightEnabledSitemap,
                                                        Integer hasInternalLinks,
                                                        List<SiteMapInfoEntity> siteMapList) {

        if (hasInternalLinks == null || hasInternalLinks < 1) {
            isC360LightEnabledInternalLink = false;
        }

        // ui JSON template ----------------------------------------
        StringBuilder uiJsonTemp = new StringBuilder();
        uiJsonTemp.append("{");
        uiJsonTemp.append("\"primaryDatasource\":{\"enabled\":true,\"source\":\"siteHealth\"},");
        uiJsonTemp.append("\"universalFilter\":{\"url\":{\"checked\":false,\"value\":[{\"action\":\"ct\",\"value\":\"\"}]},\"urlType\":{\"checked\":false,\"value\":[]}},");
        uiJsonTemp.append("\"name\":\"{{projectTitle}}\",");
        uiJsonTemp.append("\"dateRange\":{\"type\":\"dynamic\",\"range\":\"7day\"},");
        uiJsonTemp.append("\"startDate\":\"01/01/1970\",");
        uiJsonTemp.append("\"endDate\":\"01/01/1970\",");
        if (isC360LightEnabledInternalLink) {
            uiJsonTemp.append("\"internalLink\":{\"projectId\":\"{{siteAuditProjectId}}\",\"crawlId\":\"{{siteAuditCrawlId}}\",\"filter\":{\"url\":{\"checked\":false,\"value\":[{\"action\":\"ct\",\"value\":\"\"}]},\"urlType\":{\"checked\":false,\"value\":[]}},\"crawlStatus\":3},");
        }
        if (isC360LightEnabledSitemap) {
            uiJsonTemp.append("\"siteMap\":{\"sitemapHashList\":{{sitemapHashListJson}},\"urlList\":{{sitemapURLMapListJson}},\"filter\":{\"url\":{\"checked\":false,\"value\":[{\"action\":\"ct\",\"value\":\"\"}]},\"urlType\":{\"checked\":false,\"value\":[]}}},");
        }
        uiJsonTemp.append("\"siteHealth\":{\"projectId\":\"{{siteAuditProjectId}}\",\"crawlId\":\"{{siteAuditCrawlId}}\",\"filter\":{\"url\":{\"checked\":false,\"value\":[{\"action\":\"ct\",\"value\":\"\"}]},\"urlType\":{\"checked\":false,\"value\":[]}},\"crawlStatus\":3},");
        uiJsonTemp.append("\"sources\":[\"siteHealth\"");
        if (isC360LightEnabledInternalLink) {
            uiJsonTemp.append(",\"internalLink\"");
        }
        if (isC360LightEnabledSitemap) {
            uiJsonTemp.append(",\"siteMap\"");
        }
        uiJsonTemp.append("]");
        uiJsonTemp.append("}");


        // param JSON template ----------------------------------------
        StringBuilder paramJsonTemp = new StringBuilder();
        paramJsonTemp.append("{");
        paramJsonTemp.append("\"domainId\":\"{{ownDomainId}}\",");
        paramJsonTemp.append("\"primaryDatasource\":{\"enabled\":true,\"source\":\"siteHealth\"},");
        paramJsonTemp.append("\"sources\":[");
        if (isC360LightEnabledInternalLink) {
            paramJsonTemp.append("{\"internalLink\":{\"crawlRequestId\":\"{{siteAuditCrawlId}}\",\"contentTypeFilters\":[],\"leftNaviFilters\":[],\"crawlStatus\":3}},");
        }
        if (isC360LightEnabledSitemap) {
            paramJsonTemp.append("{\"siteMap\":{\"sitemapHashList\":{{sitemapHashListJson}},\"urlList\":{{sitemapURLListJson}},\"contentTypeFilters\":[],\"leftNaviFilters\":[]}},");
        }
        paramJsonTemp.append("{\"siteHealth\":{\"crawlRequestId\":\"{{siteAuditCrawlId}}\",\"contentTypeFilters\":[],\"leftNaviFilters\":[],\"crawlStatus\":3}}");
        paramJsonTemp.append("]");
        paramJsonTemp.append("}");


        String uiJson = StringUtils.replace(uiJsonTemp.toString(), "{{projectTitle}}", projectTitle);
        String paramJson = StringUtils.replace(paramJsonTemp.toString(), "{{ownDomainId}}", String.valueOf(ownDomainId));

        //Setup Sitemap ----------------------------------------
        if (isC360LightEnabledSitemap && siteMapList != null) {
            List<String> sitemapHashList = new ArrayList<>();
            List<String> sitemapURLList = new ArrayList<>();
            List<Map> sitemapURLMapList = new ArrayList<>();
            for (SiteMapInfoEntity sitemapInfoVO : siteMapList) {
                Map<String, String> sitemapMap = new HashMap<>();
                sitemapMap.put("id", String.valueOf(sitemapInfoVO.getId()));
                sitemapMap.put("text", sitemapInfoVO.getSitemap());
                sitemapURLMapList.add(sitemapMap);

                sitemapHashList.add(sitemapInfoVO.getSitemap_murmur3hash());

                sitemapURLList.add(sitemapInfoVO.getSitemap());
            }
            uiJson = StringUtils.replace(uiJson, "{{sitemapHashListJson}}", JsonUtils.getMapper().toJson(sitemapHashList));
            uiJson = StringUtils.replace(uiJson, "{{sitemapURLMapListJson}}", JsonUtils.getMapper().toJson(sitemapURLMapList));

            paramJson = StringUtils.replace(paramJson, "{{sitemapHashListJson}}", JsonUtils.getMapper().toJson(sitemapHashList));
            paramJson = StringUtils.replace(paramJson, "{{sitemapURLListJson}}", JsonUtils.getMapper().toJson(sitemapURLList));
        }
        Clarity360ReportEntity report = new Clarity360ReportEntity();
        report.setUiJson(uiJson);
        report.setParamJson(paramJson);

        if (ownDomainId == 12283 || ownDomainId == 4 || ownDomainId == 6060 || ownDomainId == 8414 || ownDomainId == 7741) {
            log.info("End generate json, domainId: " + ownDomainId + ", projectTitle: " + projectTitle + ",\nuiJson: " + uiJson + "\nparamJson: " + paramJson);
        }
        return report;
    }

    private Clarity360ReportEntity createNewReport(Clarity360ReportEntity report) {
        Clarity360ReportEntity reportEntity = clarity360ReportEntityDAO.getByTitle(report.getOwnDomainId(), report.getProjectId(), report.getTitle());
        if (reportEntity != null) {
            log.info("c360report is not null, re-start it. ownDomainId: " + report.getOwnDomainId() + ", crawlId: " + report.getSiteAuditCrawlId() + ", title: " + report.getTitle());
            processC360Report(report.getOwnDomainId(), report.getSiteAuditCrawlId(), new BigInteger(String.valueOf(report.getSiteAuditProjectId())), report.getCreateDate());
        } else {
            log.info("c360report is null. create new, ownDomainId: " + report.getOwnDomainId() + ", crawlId: " + report.getSiteAuditCrawlId() + ", title: " + report.getTitle());
            Integer reportId = clarity360ReportEntityDAO.insert(report);
            reportEntity = clarity360ReportEntityDAO.getReportById(reportId, report.getOwnDomainId());
        }
        return reportEntity;
    }


    public void setupC360ReportStageEnabled(Clarity360ReportEntity report, OwnDomainSettingEntity ownDomainSettingEntity) {
        if (report == null || StringUtils.isBlank(report.getUiJson())) {
            return;
        }
        JsonNode root = JsonUtils.getMapper().readTree(report.getUiJson());
        if (root == null) {
            return;
        }
        JsonNode sources = root.get("sources");
        if (sources == null) {
            return;
        }

        log.info("Find source: " + sources);

        boolean hasInternalLink = false;
        for (JsonNode item : sources) {
            if (StringUtils.equalsIgnoreCase(item.asText(), "internalLink")) {
                hasInternalLink = true;
                break;
            }
        }

        log.info("is hasInternalLink: " + hasInternalLink);
        if (!hasInternalLink) {
            report.setStageEnabled(false);
            return;
        }

        log.info("StageEnable: " + ownDomainSettingEntity.getC360AppendPostprocessing());
        if (ownDomainSettingEntity.getC360AppendPostprocessing() != null) {
            report.setStageEnabled(ownDomainSettingEntity.getC360AppendPostprocessing());
        } else {
            report.setStageEnabled(false);
        }

    }




}
