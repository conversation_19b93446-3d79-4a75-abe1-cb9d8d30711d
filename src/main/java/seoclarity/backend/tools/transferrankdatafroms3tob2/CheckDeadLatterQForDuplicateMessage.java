package seoclarity.backend.tools.transferrankdatafroms3tob2;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ewain
 * 20220222 222222222222222222222222222222222222222
 * seoclarity.backend.transferrankdatafroms3tob2.CheckDeadLatterQForDuplicateMessage
 *
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.transferrankdatafroms3tob2.CheckDeadLatterQForDuplicateMessage" -Dexec.cleanupDaemonThreads=false -Dexec.args=""
 */

@CommonsLog
public class CheckDeadLatterQForDuplicateMessage {
    private static final String deadLatterQPrefix = "S3TOB2_DEAD_LATTER_S3TOB2_S3_FILE_INFO_";
    private static AmazonSQS amazonSQS;
    static {
        amazonSQS = SQSUtils.getAmazonSQS();
    }

    public static void main(String[] args) {
        CheckDeadLatterQForDuplicateMessage tool = new CheckDeadLatterQForDuplicateMessage();
        tool.process();
    }

    private void process() {
        List<String> dateList = Arrays.asList("20170924");
        for (String date : dateList) {
            String qName = deadLatterQPrefix + date;
            String queueUrl = SQSUtils.createQueue(qName, amazonSQS);
            processForUrl(amazonSQS, queueUrl);
        }
    }

    private void processForUrl(AmazonSQS amazonSQS, String queryUrl) {
        Set<String> messageSet = new HashSet<>();
        int tryCnt = 3;
        flagWh:
        while (true) {
            List<Message> result = SQSUtils.getMessageFromQueue(amazonSQS, queryUrl, 10, 600);
            if (result != null && result.size() > 0) {
                messageSet.addAll(result.stream().map(var->var.getBody()).collect(Collectors.toSet()));
                tryCnt = 3;
            } else {
                try {
                    log.info("$EC=>" + Thread.currentThread().getName() + "=>Q message is null. wait 2s!");
                    Thread.sleep(2 * 1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (tryCnt-- < 0) {
                    try {
                        Thread.sleep(10 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if (NumberUtils.createInteger(SQSUtils.getApproximateNumberOfMessages(amazonSQS, queryUrl)) == 0) {
                        log.info(" q 中没有可读消息");
                        System.out.println("queryUrl: " + queryUrl + ", message size: "+messageSet.size());
                        break flagWh;
                    } else {
                        log.info(" error q 中有可读消息, 检查 ");
                    }
                }
            }
        }
    }
}
