package seoclarity.backend.tools.transferrankdatafroms3tob2.onetime;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.BackBlazeSourceFileS3EntityDao;
import seoclarity.backend.dao.clickhouse.s3file.S3FileDetailClarityDBDAO;
import seoclarity.backend.entity.BackBlazeSourceFileS3Entity;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.cityhash.CityHashUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * seoclarity.backend.transferrankdatafroms3tob2.onetime.FindLostDataFromS3AndClarityDB
 */
@CommonsLog
public class FindLostDataFromS3AndClarityDB {

    private S3FileDetailClarityDBDAO s3FileDetailClarityDBDAO;
    private BackBlazeSourceFileS3EntityDao backBlazeSourceFileS3EntityDao;

    public FindLostDataFromS3AndClarityDB() {
        s3FileDetailClarityDBDAO = SpringBeanFactory.getBean("s3FileDetailClarityDBDAO");
        backBlazeSourceFileS3EntityDao = SpringBeanFactory.getBean("backBlazeSourceFileS3EntityDao");
    }

    public static void main(String[] args) {
        FindLostDataFromS3AndClarityDB tool = new FindLostDataFromS3AndClarityDB();
        tool.process();
    }

    private void process() {
        int maxDate = 20160605;
        List<LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo> result = s3FileDetailClarityDBDAO.getList(maxDate);
        List<BackBlazeSourceFileS3Entity> backBlazeSourceFileS3EntityList = backBlazeSourceFileS3EntityDao.findAllByTimeInterval();

        System.out.println("result size: " + result.size());
        System.out.println("backBlazeSourceFileS3EntityList size: " + backBlazeSourceFileS3EntityList.size());

        Set<String> hashSet = backBlazeSourceFileS3EntityList.stream().map(var -> {
            String path = var.getFullPathFolder() + var.getSourceFileName();
            path = StringUtils.replace(path, "daily-html-virginia/", "");
            String urlHashForString = CityHashUtil.getUrlHashForString(path.toLowerCase());
            return urlHashForString;
        }).collect(Collectors.toSet());

        for (LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo s3FileDetailVo : result) {
            String pathEncode = s3FileDetailVo.getRankDate() + "/" + s3FileDetailVo.getFolder() + "/" + s3FileDetailVo.getFileName();
            String decodedPath;
            try {
                decodedPath = StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(pathEncode, "UTF-8")));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
                continue;
            }
            String hash = CityHashUtil.getUrlHashForString(decodedPath.toLowerCase());
            if (!hashSet.contains(hash)) {
                log.info(" find lost data=> pathEncode: " + pathEncode + ", decodedPath: " + decodedPath);
            }
        }
    }
}
