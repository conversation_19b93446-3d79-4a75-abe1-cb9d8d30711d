package seoclarity.backend.tools.transferrankdatafroms3tob2.onetime;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.BackBlazeKeywordStoreEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeMetaFileEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeMetaFolderEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeSourceFileS3EntityDao;
import seoclarity.backend.entity.BackBlazeKeywordStoreEntity;
import seoclarity.backend.tools.transferrankdatafroms3tob2.S3ToB2FilePusher;
import seoclarity.backend.utils.SpringBeanFactory;

import java.util.List;

/**
 * ewain
 * seoclarity.backend.transferrankdatafroms3tob2.onetime.UpdateOldKeywordTypeOnetime
 */
@CommonsLog
public class UpdateOldKeywordTypeOnetime {

    private BackBlazeKeywordStoreEntityDao backBlazeKeywordStoreEntityDao;
    private BackBlazeMetaFileEntityDao backBlazeMetaFileEntityDao;
    private BackBlazeMetaFolderEntityDao backBlazeMetaFolderEntityDao;
    private BackBlazeSourceFileS3EntityDao backBlazeSourceFileS3EntityDao;

    public UpdateOldKeywordTypeOnetime() {
        backBlazeKeywordStoreEntityDao = SpringBeanFactory.getBean("backBlazeKeywordStoreEntityDao");
        backBlazeMetaFileEntityDao = SpringBeanFactory.getBean("backBlazeMetaFileEntityDao");
        backBlazeMetaFolderEntityDao = SpringBeanFactory.getBean("backBlazeMetaFolderEntityDao");
        backBlazeSourceFileS3EntityDao = SpringBeanFactory.getBean("backBlazeSourceFileS3EntityDao");
    }

    public static void main(String[] args) {
        UpdateOldKeywordTypeOnetime updateOldKeywordTypeOnetime = new UpdateOldKeywordTypeOnetime();
        updateOldKeywordTypeOnetime.process();
    }

    private void process() {
        int size = 100000;
        int index = 0;
        int processCnt = 0;
        List<BackBlazeKeywordStoreEntity> entityList;
        while (true) {
            entityList = backBlazeKeywordStoreEntityDao.getListForUpdateKeywordType(index, size);
            log.info("$EC=>Get data from db, size: " + entityList.size());
            if (entityList != null && entityList.size() > 0) {
                for (BackBlazeKeywordStoreEntity backBlazeKeywordStoreEntity : entityList) {
                    String fullFolderPath = StringUtils.removeStart(backBlazeKeywordStoreEntity.getFullPathFolder(), "serp-html/");
                    String keywordName = backBlazeKeywordStoreEntity.getKeywordName();
                    String key = fullFolderPath + keywordName;
                    S3ToB2FilePusher pusher = new S3ToB2FilePusher();
                    S3ToB2FilePusher.FileInfo fileInfo;
                    try {
                        fileInfo = pusher.getFileInfo(key);
                    } catch (Exception e) {
                        e.printStackTrace();
                        continue;
                    }
                    if (backBlazeKeywordStoreEntity.getKeywordType().intValue() != fileInfo.getInfoData().getKeywordType()) {
                        // update
                        backBlazeKeywordStoreEntityDao.updateKeywordTypeById(backBlazeKeywordStoreEntity.getId(),
                                backBlazeKeywordStoreEntity.getKeywordType().intValue(),
                                fileInfo.getInfoData().getKeywordType());
                        log.info("$EC=>UPD BKS ID: " + backBlazeKeywordStoreEntity.getId() + " KW TYPE: " + backBlazeKeywordStoreEntity.getKeywordType().intValue() + "-->" + fileInfo.getInfoData().getKeywordType() + ", FULLPATH :" + fullFolderPath);
                        processCnt ++;
//                        if (processCnt >= 5) {
//                            return;
//                        }
                    }
                }
                entityList.clear();
                index++;
            }else {
                break;
            }
        }

        log.info("$EC=> Process success! processCnt: " + processCnt);
    }
}
