package seoclarity.backend.tools.transferrankdatafroms3tob2.onetime;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.services.s3.model.StorageClass;
import lombok.Data;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.clickhouse.s3file.S3FileDetailClarityDBDAO;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.tools.transferrankdatafroms3tob2.S3ToB2FilePusher;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@CommonsLog
public class LoadDailyRankingKeyFromS3ToClarityDB {

    private static int threadCount = 10;
    private static ExecutorService threadPool = new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), new BackendThreadFactory());

    // TODO: 2022/1/20
    private static String s3SummaryFilePath = "/home/<USER>/s3Summary2/";

    private static final int s3FileListSizeForThread = 10 * 10000;
    /**
     * amazon s3
     */
    private final static String s3BucketName = "daily-html-virginia";
    private final static String s3SummaryFileBucketName = "alps-distcp";
    private final static String s3AccessKey = "********************";
    private final static String s3SecretKey = "v7Qts+qswF9qDojbKZQ1BofRbWcQLJu8CLM+eXrf";

    /**
     * backblaze
     */
    private final static String bbBucketName = "serp-html";
    private final static String APP_KEY_ID = "FlIOnZmgKUzDQa2VqdQZNbTw";
    private final static String APP_KEY = "5RloZnhbCoxGrQXTH3yVO2NQhi3l7bozTavvORl";
    private final static String endpoint = "s3.us-east-1.aws.flexify.io";
    private final static String region = "us-east-1";

    private static AmazonS3 s3;
    private static AmazonS3 bb;

    static {
        /*连接 s3 */
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(s3AccessKey, s3SecretKey);
        s3 = AmazonS3ClientBuilder
                .standard()
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials)).withRegion("us-east-1").build();

    }

    private S3FileDetailClarityDBDAO s3FileDetailClarityDBDAO;

    public LoadDailyRankingKeyFromS3ToClarityDB() {
        // TODO: 2022/1/20  
        s3FileDetailClarityDBDAO = SpringBeanFactory.getBean("s3FileDetailClarityDBDAO");
    }

    public static void main(String[] args) {

        // TODO: 2022/1/18
//        String manifestFilePath = null;
//        String startPointKeyForSummaryFile = null;
//        String endPointKeyForSummaryFile = null;
//        if (args.length > 0) {
//            manifestFilePath = args[0];
//        } else {
//            log.error("$EC=>parameter error!, please provide a correct file path");
//            return;
//        }
//
//        if (args.length > 1) {
//            startPointKeyForSummaryFile = args[1];
//            if (startPointKeyForSummaryFile.equals("NULL")) {
//                startPointKeyForSummaryFile = null;
//            }
//        }
//
//        if (args.length > 2) {
//            endPointKeyForSummaryFile = args[2];
//            if (endPointKeyForSummaryFile.equals("NULL")) {
//                endPointKeyForSummaryFile = null;
//            }
//        }
        LoadDailyRankingKeyFromS3ToClarityDB tool = new LoadDailyRankingKeyFromS3ToClarityDB();
//        tool.process(manifestFilePath, startPointKeyForSummaryFile, endPointKeyForSummaryFile);

        tool.processForGlacierData();
    }


    private void process(String manifestFilePath) {
        Map<String, String> map = new HashMap<>();
        map.put("/home/<USER>/source/ewain/dev-clarity-backend-scripts/local_file/s3tobb/manifest1.json", "/home/<USER>/s3Summary/");
        map.put("/home/<USER>/source/ewain/dev-clarity-backend-scripts/local_file/s3tobb/manifest2.json", "/home/<USER>/s3Summary2/");
        Set<String> stringSet = map.keySet();
        for (String minFilePath : stringSet) {
            File manifestFile = new File(minFilePath);
            String folder = map.get(minFilePath);
            S3ToB2FilePusher.Manifest manifest = null;
            try {
                manifest = JSONObject.parseObject(FileUtils.readFileToByteArray(manifestFile), S3ToB2FilePusher.Manifest.class);
            } catch (IOException e) {
                log.error("$EC=>parse manifest error, msg: " + CollectionSplitUtils.getErrorMsg(e));
                return;
            }
            List<String> keyList = manifest.getFiles().stream().map(var -> var.getKey()).collect(Collectors.toList());
            for (String key : keyList) {
                String fullPath = folder + key;
                File file = new File(fullPath);
                if (!file.exists()) {
                    log.error("$EC=>file not exist! key: " + key + ", fullPath: " + fullPath);
                    return;
                }
            }
        }


        for (String minFilePath : stringSet) {
            File manifestFile = new File(minFilePath);
            String folder = map.get(minFilePath);
            S3ToB2FilePusher.Manifest manifest = null;
            try {
                manifest = JSONObject.parseObject(FileUtils.readFileToByteArray(manifestFile), S3ToB2FilePusher.Manifest.class);
            } catch (IOException e) {
                log.error("$EC=>parse manifest error, msg: " + CollectionSplitUtils.getErrorMsg(e));
                return;
            }
            List<String> keyList = manifest.getFiles().stream().map(var -> var.getKey()).collect(Collectors.toList());
            for (String key : keyList) {
                String fullPath = folder + key;
                log.info("start process " + fullPath);
                File localFile = new File(fullPath);
                File outputFile = new File("/home/<USER>/s3Summary3/" + StringUtils.replace(localFile.getName(), ".gz", ""));
                try {
                    GZipUtil.unGzipBigFile(FileUtils.readFileToByteArray(localFile), outputFile);
                } catch (IOException e) {
                    e.printStackTrace();
                }

                int filePosition = 0;
                FileInputStream inputStream = null;
                BufferedReader bufferedReader = null;
                final CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',');
                try {
                    inputStream = new FileInputStream(outputFile);
                    bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                    String line;
                    while ((line = bufferedReader.readLine()) != null) {
                        CSVParser csvParser = CSVParser.parse(line, csvFormat);
                        List<CSVRecord> csvRecords = csvParser.getRecords();
                        if (csvRecords.size() != 1) {
                            continue;
                        }
                        CSVRecord record = csvRecords.get(0);
                        String bucketName = record.get(0);
                        String s3FilePath = record.get(1);
                        String s3StorageType = record.get(2);
                        if (bucketName == null || s3FilePath == null || s3StorageType == null || s3FilePath.equals("")) {
                            continue;
                        }
                        if (s3StorageType.equals(StorageClass.Glacier.toString())) {
                            continue;
                        }
                        if (s3FilePath.equals("20200105/img-mobile-3-3/zoom%2Bh4n")) {
                            log.info("find this file!, position: " + filePosition);
                        }
                        filePosition++;
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    try {
                        inputStream.close();
                        bufferedReader.close();
                    } catch (IOException e) {
                        log.info("关闭流异常");
                        e.printStackTrace();
                    }
                }
                outputFile.delete();
            }
        }
    }


    private void process(String manifestFilePath, String startPointKeyForSummaryFile, String endPointKeyForSummaryFile) {
        // TODO: 2022/1/20
        processSummaryFile(
                manifestFilePath,
                startPointKeyForSummaryFile,
                endPointKeyForSummaryFile);
//        processSingleFile("E:\\work\\work-seoclarity\\download from s3\\transfer ranking data to bb\\sum\\daily-html-virginia\\key\\data\\8cfcdc29-b8d5-4ce5-94c4-a211ecefd400.csv_bc");
        while (!threadPool.isShutdown()) {
            ThreadUtil.sleep(15 * 1000);
            int aliveCount = ((ThreadPoolExecutor) threadPool).getActiveCount();
            if (aliveCount == 0) {
                threadPool.shutdown();
            }
        }
    }

    private void processSummaryFile(String manifestFilePath,
                                    String startingPointKeyForSummaryFile,
                                    String endPointKeyForSummaryFile) {
        File manifestFile = new File(manifestFilePath);
        S3ToB2FilePusher.Manifest manifest = null;
        try {
            manifest = JSONObject.parseObject(FileUtils.readFileToByteArray(manifestFile), S3ToB2FilePusher.Manifest.class);
        } catch (IOException e) {
            log.error("$EC=>parse manifest error, msg: " + CollectionSplitUtils.getErrorMsg(e));
            return;
        }

        if (manifest == null || manifest.getFiles().size() == 0) {
            return;
        }

        List<String> keyList = manifest.getFiles().stream().map(var -> var.getKey()).collect(Collectors.toList());

        Integer start = null;
        if (startingPointKeyForSummaryFile != null) {
            start = keyList.indexOf(startingPointKeyForSummaryFile);
        }
        Integer end = null;
        if (endPointKeyForSummaryFile != null) {
            end = keyList.indexOf(endPointKeyForSummaryFile);
        }

        int fileCnt = 0;

        flagFor:
        for (int i = 0; i < keyList.size(); i++) {
            String gzFilePath = keyList.get(i);

            if (start != null && i < start.intValue()) {
                log.info("$EC=>skip processed summary file: " + gzFilePath);
                continue;
            }
            if (end != null && i > end.intValue()) {
                log.info("$EC=>end process summary file, last summary file: " + endPointKeyForSummaryFile + ", next summary file: " + gzFilePath);
                log.info("$EC=> process end, fileCnt: " + fileCnt);
                return;
            }

            File localFile = null;
            int downloadTryCnt = 3;
            flagWh:
            while (downloadTryCnt > 0) {
                try {
                    S3Object o = s3.getObject(s3SummaryFileBucketName, gzFilePath);
                    S3ObjectInputStream s3is = o.getObjectContent();
                    localFile = new File(s3SummaryFilePath + "/" + gzFilePath);
                    if (!localFile.exists()) {
                        String folder = localFile.getParent();
                        File folderFile = new File(folder);
                        if (!folderFile.exists()) {
                            folderFile.mkdirs();
                        }
                    }
                    FileOutputStream fos = new FileOutputStream(localFile);
                    byte[] read_buf = new byte[1024];
                    int read_len = 0;
                    while ((read_len = s3is.read(read_buf)) > 0) {
                        fos.write(read_buf, 0, read_len);
                    }
                    s3is.close();
                    fos.close();
                    break flagWh;
                } catch (Exception e) {
                    downloadTryCnt--;
                    log.error("$EC=>download file exception, try again! key: " + gzFilePath + ", msg: " + CollectionSplitUtils.getErrorMsg(e));
                }
            }

            if (localFile == null) {
                log.error("$EC=>download s3 file failed! please check! key: " + gzFilePath);
                return;
            }

            String localFileFolder = localFile.getParent();
            File outputFile = new File(localFileFolder + "/" + StringUtils.replace(localFile.getName(), ".gz", ""));
            try {
                GZipUtil.unGzipBigFile(FileUtils.readFileToByteArray(localFile), outputFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
            int singleFileCnt = processSingleFile(outputFile.getAbsolutePath());
            fileCnt += singleFileCnt;
            outputFile.delete();
        }

        log.info("$EC=> process end, fileCnt: " + fileCnt);
    }


    private void processForGlacierData() {
        String gzFilePath1 = "/home/<USER>/s3Summary/daily-html-virginia/key/data";
        String gzFilePath2 = "/home/<USER>/s3Summary2/daily-html-virginia/key/data";

        processForGlacierDataUnGz(gzFilePath1);
        processForGlacierDataUnGz(gzFilePath2);
    }

    private void processForGlacierDataUnGz(String path) {
        File gzFilePath = new File(path);
        File[] files = gzFilePath.listFiles();
        for (File file : files) {
            if (!file.getName().endsWith(".gz")) {
                System.out.println("not process: " + file);
                continue;
            }
            String localFileFolder = file.getParent();
            File outputFile = new File(localFileFolder + "/" + StringUtils.replace(file.getName(), ".gz", ""));
            try {
                GZipUtil.unGzipBigFile(FileUtils.readFileToByteArray(file), outputFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
            processSingleFile(outputFile.getAbsolutePath());
            outputFile.delete();
        }
    }

    private int processSingleFile(String s3KeyListFilePath) {
        int fileCnt = 0;
        FileInputStream inputStream = null;
        BufferedReader bufferedReader = null;
        final CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',');
        try {
            inputStream = new FileInputStream(s3KeyListFilePath);
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            List<S3FileDetailVo> s3FileDetailVos = new ArrayList<>();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                CSVParser csvParser = CSVParser.parse(line, csvFormat);
                List<CSVRecord> csvRecords = csvParser.getRecords();
                if (csvRecords.size() != 1) {
                    continue;
                }
                CSVRecord record = csvRecords.get(0);
                String bucketName = record.get(0);
                String s3FilePath = record.get(1);
                String s3StorageType = record.get(2);
                if (bucketName == null || s3FilePath == null || s3StorageType == null || s3FilePath.equals("")) {
                    continue;
                }
                if (!s3StorageType.equals(StorageClass.Glacier.toString())) {
                    continue;
                }
                S3FileDetailVo vo = getS3FileDetail(s3FilePath);
                s3FileDetailVos.add(vo);
                fileCnt++;
                if (s3FileDetailVos.size() >= s3FileListSizeForThread) {
                    createThread(s3FileDetailVos);
                    s3FileDetailVos.clear();
                }
            }
            if (s3FileDetailVos.size() > 0) {
                createThread(s3FileDetailVos);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                inputStream.close();
                bufferedReader.close();
            } catch (IOException e) {
                log.info("关闭流异常");
                e.printStackTrace();
            }
        }
        log.info("$EC=>process " + s3KeyListFilePath + " completed. cnt: " + fileCnt);
        return fileCnt;
    }

    private S3FileDetailVo getS3FileDetail(String s3FilePath) {
        int i = s3FilePath.indexOf("/");
        String dateStr = s3FilePath.substring(0, i);
        int j = s3FilePath.lastIndexOf("/");
        String fileName = s3FilePath.substring(j + 1);
        String folder = s3FilePath.substring(i + 1, j);
        S3FileDetailVo vo = new S3FileDetailVo();
        vo.setRankDate(Integer.parseInt(dateStr));
        vo.setFileName(fileName);
        vo.setFolder(folder);
        return vo;
    }

    private void createThread(List<S3FileDetailVo> s3FileDetailVos) {
        threadPool.execute(new LoadDailyRankingKeyFromS3ToClarityDB.TransferToolThread(s3FileDetailVos));
    }

    class TransferToolThread implements Runnable {
        private List<S3FileDetailVo> s3FileDetailVos = new ArrayList<>();

        public TransferToolThread(List<S3FileDetailVo> s3FileDetailVos) {
            super();
            this.s3FileDetailVos.addAll(s3FileDetailVos);
        }

        @Override
        public void run() {
            doGet();
        }

        public void doGet() {
            if (s3FileDetailVos == null && s3FileDetailVos.size() == 0) {
                return;
            }
            // TODO: 2022/1/20  
            s3FileDetailClarityDBDAO.insertBatchForGlacier(s3FileDetailVos);
            log.info(Thread.currentThread().getName() + " insert into clarityDB size: " + s3FileDetailVos.size());
            s3FileDetailVos.clear();
        }
    }

    @Data
    static class Manifest {
        private String sourceBucket;
        private List<S3ToB2FilePusher.Manifest.Key> files = new ArrayList();

        @Data
        class Key {
            private String key;
            private int size;
            private String md5checksum;
        }
    }

    @Data
    static public class S3FileDetailVo {
        private int rankDate;
        private String folder;
        private String fileName;
        private Date createDate;
        private String fullstring;
    }

}
