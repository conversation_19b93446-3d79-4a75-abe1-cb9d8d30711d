package seoclarity.backend.tools.groudworks;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.entity.actonia.KeywordSuggest;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.RankIndexParamEntity;
import seoclarity.backend.keywordexpand.utils.ExportDataUtil;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1176521183
 * click stream query memory is 15G(15000000000)
 */
@CommonsLog
public class CollectRGSeedKeyword {

//    private static List<Integer> runDomainIds = Arrays.asList(11895);
    private static List<Integer> runDomainIds = new ArrayList<>();
    private static final int RANK_DAYS = 2;
    private static final int GSC_CLICK_STREAM_DAYS = 45;
    private static final long MAX_EXTERNAL_GROUP_BY = 15000000000L;
    private static final long MAX_EXTERNAL_SORT = 15000000000L;
    private static final int MAX_RANK_CHECK = 9;
    private static final int MIN_RANK_CHECK = 1;
    private static final String DEFAULT_PARENT_FILE_PATH = "/home/<USER>/source/radeL/bot_project/tmp_file";
    private static final String GSC_CLICK_STREAM_FILE_NAME = "gsc_clickstream_";
    private static final String RANKING_FILE_NAME = "ranking_detail_";
    private static final String MERGER_FILE_NAME = "/merge_file_kw.txt";
    private static final String RESULT_FILE_NAME = "/final_result_kw_";

    private static final String CH_DB_RG_URL = "http://23.105.174.58:8123";
    private static final String CH_DB_RG = "gsc_clickstream";
    private static final String CH_DB_RKD = "monthly_ranking";
    private static final String USER = "default";
    private static final String PSW = "clarity99!";
    private static final Date TODAY_DATE = new Date();
    private static final String KW_SPLIT = "!_!";
    private static final String FILE_SPLIT = "_";
    private static final String SPLIT_PATH = "/";
    private static final String FILE_TYPE_TXT = ".txt";

    private static final Random random = new Random();
    private OwnDomainEntityDAO ownDomainDAO;
    private KeywordEntityDAO keywordDAO;
    private KeywordSuggestDAO keywordSuggestDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingDAO;
    private RankIndexParamEntityDAO rankIndexParamDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private GscClickSteamDAO gscClickSteamDAO;
    private String parentFilePath;

    public CollectRGSeedKeyword() {
        ownDomainDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        keywordSuggestDAO = SpringBeanFactory.getBean("keywordSuggestDAO");
        ownDomainSettingDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        rankIndexParamDAO = SpringBeanFactory.getBean("rankIndexParamEntityDAO");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
    }

    /**
     * args[0]: domainIds -> split by comma
     * args[1]: parentFilePath
     * @param args
     */
    public static void main(String[] args) {
        new CollectRGSeedKeyword().process(args);
    }

    private void process(String[] args) {
        initParentFilePath(args);
        List<Integer> domainIdList = getRunDomainIds(args);
        if (domainIdList.size() <= 0) {
            System.out.println("====nothing to run!!!!!!");
            return;
        }
        List<Integer> engineIdList = new ArrayList<>();
        List<Integer> languageIdList = new ArrayList<>();
        List<String> filePathList = new ArrayList<>();
        Map<Integer, String> domainIdPSEMap = new HashMap<>();
        Map<Integer, String> domainIdPNameMap = new HashMap<>();
        Map<String, String> searchEngineAndLanguagePCountryCodeMap = new HashMap<>();
        List<OwnDomainEntity> domainList = ownDomainDAO.getDomainByIdList(domainIdList);
        for (OwnDomainEntity ownDomainEntity : domainList) {
            int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
            engineIdList.add(engineId);
            int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
            languageIdList.add(languageId);
            domainIdPSEMap.put(ownDomainEntity.getId(), engineId + KW_SPLIT + languageId);
            domainIdPNameMap.put(ownDomainEntity.getId(), ownDomainEntity.getDomain());
        }
        engineIdList = engineIdList.stream().distinct().collect(Collectors.toList());
        languageIdList = languageIdList.stream().distinct().collect(Collectors.toList());

        //todo split enginedId and languageId
        String filePath = getLocalFilePath(engineIdList.get(0) + KW_SPLIT + languageIdList.get(0)  + FILE_SPLIT + getRandom());
        filePathList.add(filePath);

        // get google suggest
        List<KeywordSuggest> suggestList = keywordSuggestDAO.getKeywordByDomainList(domainIdList, engineIdList, languageIdList); // todo add to kwList
        if (suggestList != null && suggestList.size() > 0) {
            System.out.println("====suggestCount:" + suggestList.size());
            try {
                List<String> strings = suggestList.stream().map(KeywordSuggest::getSuggestText).collect(Collectors.toList());
                FileUtils.writeLines(new File(filePath), strings, true);
            } catch (IOException e) {
                System.out.println("====exportSuggestError file:" + filePath);
                e.printStackTrace();
            }
        } else {
            System.out.println("====suggestCount:0");
        }

        // get managed keywords
        List<String> kwList = keywordDAO.geKeywordByDomainList(domainIdList, engineIdList, languageIdList, MIN_RANK_CHECK, MAX_RANK_CHECK); // todo load into file
        if (kwList != null && kwList.size() > 0) {
            System.out.println("====tkWCount:" + kwList.size());
            try {
                List<String> newKwList = new ArrayList<>();
                for (String s : kwList) {
                    String[] kwArr = s.split(KW_SPLIT);
                    String keyword = kwArr[2];
                    newKwList.add(keyword.replace("+", " "));
                }
                FileUtils.writeLines(new File(filePath), newKwList, true);
            } catch (IOException e) {
                System.out.println("====exportTkWordError file:" + filePath);
                e.printStackTrace();
            }
        } else {
            System.out.println("====tkWCount:0");
        }

        // Retrieve top 10 competitors from RI and own domain:
        Map<Integer, List<String>> domainPCompetitorDomainMap = getTopCompetitorByDomainId(domainIdList, domainIdPNameMap);
        String logDate = FormatUtils.formatDate(getBeforeDate(TODAY_DATE, GSC_CLICK_STREAM_DAYS, false), "yyyy-MM-dd");
        Set<String> uniuneDomainList = new HashSet<>();
        int totalDomain = 0;
        for (Integer domainId : domainPCompetitorDomainMap.keySet()) {
            String countryCode = "";
            String key = domainIdPSEMap.get(domainId);
            String[] seArr = key.split(KW_SPLIT);
            int engineId = Integer.parseInt(seArr[0]);
            int languageId = Integer.parseInt(seArr[1]);
            if (searchEngineAndLanguagePCountryCodeMap.containsKey(key)) {
                countryCode = searchEngineAndLanguagePCountryCodeMap.get(key);
            } else {
                List<String> countryCodeList = gscClickSteamDAO.getCountryCode(engineId, languageId);
                if (CollectionUtils.isEmpty(countryCodeList)) {
                    System.out.println("====countryCodeNotExits: engineId:" + engineId + " languageId:" + languageId);
                    continue;
                }
                countryCode = countryCodeList.get(0);
                searchEngineAndLanguagePCountryCodeMap.put(key, countryCode);
            }
            List<String> domainNameList = domainPCompetitorDomainMap.get(domainId);
            totalDomain += domainNameList.size();
            System.out.println("=====RetrieveTopCompetitorInfo: domainId:" + domainId + " domain:" + domainIdPNameMap.get(domainId) + " engineId:" + engineId + " languageId:" + languageId + " country:" + countryCode + " domainList:" + domainNameList);
            for (String domainName : domainNameList) {
                if (uniuneDomainList.contains(domainName)) {
                    continue;
                }
                uniuneDomainList.add(domainName);
                // query gsc keyword
                genKeywordFileFromGscClickStream(engineId, languageId, countryCode, logDate, domainName, filePathList);
            }
        }
        System.out.println("======domainCount: totalDomain: " + totalDomain + " uniuneDomain:" + uniuneDomainList.size());
        // Query reserved keyword set with own domain's URL
        for (Integer domainId : domainIdPNameMap.keySet()) {
            String domainName = domainIdPNameMap.get(domainId);
            String engineAndLanguageId = domainIdPSEMap.get(domainId);
            String[] seArr = engineAndLanguageId.split(KW_SPLIT);
            int engineId = Integer.parseInt(seArr[0]);
            int languageId = Integer.parseInt(seArr[1]);
            genKeywordFileFromRankingDetail(engineId, languageId, domainName, filePathList);
        }
        mergerKeywordFile(filePathList);
        uniqueKeyword(engineIdList.get(0), languageIdList.get(0));
    }

    private void mergerKeywordFile(List<String> filePathList) {
        File mergeFile = new File(parentFilePath + MERGER_FILE_NAME);
        int gscKwCount = 0;
        int rankDetailKwCount = 0;
        int suggestAndManagedKwCount = 0;
        int totalKwCount = 0;
        for (String oldFilePath : filePathList) {
            try {
                File file = new File(oldFilePath);
                String fileName = file.getName();
                List<String> kwList = FileUtils.readLines(file, "UTF-8");
                if (kwList != null && kwList.size() > 0) {
                    FileUtils.writeLines(mergeFile, kwList, true);

                    // compute kw count info
                    if (StringUtils.contains(fileName, GSC_CLICK_STREAM_FILE_NAME)) {
                        gscKwCount += kwList.size();
                    } else if (StringUtils.contains(fileName, RANKING_FILE_NAME)) {
                        rankDetailKwCount += kwList.size();
                    } else {
                        suggestAndManagedKwCount += kwList.size();
                    }
                }
            } catch (IOException e) {
                System.out.println("=======uniuneKeywordFileError: file:" + oldFilePath);
                e.printStackTrace();
            }
        }
        totalKwCount = gscKwCount + rankDetailKwCount + suggestAndManagedKwCount;
        System.out.println("=====mergerKeywordFile -> totalKwCount:" + totalKwCount + " suggestAndManagedKwCount:" + suggestAndManagedKwCount + " rankDetailKwCount:" + rankDetailKwCount + " gscKwCount:" + gscKwCount + " pathCount:" + filePathList.size());
    }

    private void uniqueKeyword(int engineId, int languageId) {
        try {
            List<String> kwList = FileUtils.readLines(new File(parentFilePath + MERGER_FILE_NAME), "UTF-8");
            Set<String> uniqueKeyword = new HashSet<>(kwList);
            String fileName = parentFilePath + RESULT_FILE_NAME + engineId + FILE_SPLIT + languageId + FILE_TYPE_TXT;
            FileUtils.writeLines(new File(fileName), uniqueKeyword, true);
            System.out.println("=====beforeUniqueKeyword:" + kwList.size() + " afterUniqueKeyword:" + uniqueKeyword.size());
        } catch (IOException e) {
            System.out.println("====uniqueKeywordError: file:" + parentFilePath + MERGER_FILE_NAME);
            e.printStackTrace();
        }
    }

    private void genKeywordFileFromGscClickStream(int engineId, int languageId, String countryCode, String logDate, String domainName, List<String> filePathList) {
        String fileName = GSC_CLICK_STREAM_FILE_NAME + engineId + FILE_SPLIT + languageId + FILE_SPLIT + getRandom();
        String sql = getGscClickStreamQuerySql(countryCode, logDate, domainName);
        String filePath = getLocalFilePath(fileName);
        filePathList.add(filePath);
        System.out.println("=====kwGscSql:" + sql + " domain:" + domainName + " file:" + fileName);
        try {
            ExportDataUtil.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, sql, filePath, true, false, new String[]{""});
        } catch (Exception e) {
            System.out.println("=======genKwGscFileError: engineId:" + engineId + " languageId:" + languageId + " country:" + countryCode + " sql:" + sql + " file:" + fileName + " error:" + e.getMessage());
            e.printStackTrace();
        }
    }

    private String getGscClickStreamQuerySql(String countryCode, String logDate, String domainName) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select distinct keyword_name ");
        sqlBuilder.append("from dis_gsc_clickstream ");
        sqlBuilder.append("where country_cd = '" + countryCode + "' and position < 11 and log_date >= '" + logDate + "' ");
        sqlBuilder.append("and domain(url) = '" + domainName + "' ");
        sqlBuilder.append("SETTINGS max_bytes_before_external_group_by = " + MAX_EXTERNAL_GROUP_BY + ", max_bytes_before_external_sort = " + MAX_EXTERNAL_SORT + " ");
        return sqlBuilder.toString();
    }

    private void genKeywordFileFromRankingDetail(int engineId, int languageId, String domainName, List<String> filePathList) {
        String fileName = RANKING_FILE_NAME + engineId + FILE_SPLIT + languageId + FILE_SPLIT + getRandom();
        String sql = getRankingDetailQuerySql(engineId, languageId, domainName);
        String filePath = getLocalFilePath(fileName);
        filePathList.add(filePath);
        System.out.println("=====kwRankingSql:" + sql + " domain:" + domainName + " file:" + fileName);
        try {
            ExportDataUtil.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RKD, USER, PSW, sql, filePath, true, false, new String[]{""});
        } catch (Exception e) {
            System.out.println("=======genKwRankingFileError: engineId:" + engineId + " languageId:" + languageId + " sql:" + sql + " file:" + fileName + " error:" + e.getMessage());
            e.printStackTrace();
        }
    }

    private String getRankingDetailQuerySql(int engineId, int languageId, String domainName) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select distinct keyword_name ");
//        sqlBuilder.append("from monthly_ranking.").append(getTableName(engineId, languageId));
        sqlBuilder.append("from ").append(getTableName(engineId, languageId)).append(" ");
        sqlBuilder.append("where own_domain_id = 0 and location_id = 0 ");
        sqlBuilder.append("and engine_id = ").append(engineId).append(" ");
        sqlBuilder.append("and language_id = ").append(languageId).append(" ");
        sqlBuilder.append("and domain_reverse = '").append(reverseDomain(domainName)).append("' ");
        sqlBuilder.append("SETTINGS max_bytes_before_external_group_by = " + MAX_EXTERNAL_GROUP_BY + ", max_bytes_before_external_sort = " + MAX_EXTERNAL_SORT);
        return sqlBuilder.toString();
    }

    private String getLocalFilePath(String fileName) {
        String localFilePath = parentFilePath + SPLIT_PATH + fileName + FILE_TYPE_TXT;
        log.info("==>kwFilePath:" + localFilePath);
        return localFilePath;
    }

    private String getTableName (int engineId, int languageId) {
        String tableName = "";
        if (engineId == 1 && languageId == 1) {
            tableName = "d_ranking_detail_us";
        } else {
            tableName = "d_ranking_detail_intl";
        }
        return tableName;
    }

    private List<Integer> getRunDomainIds(String[] args) {
        List<Integer> domainIdList = new ArrayList<>();
        if (args.length == 1) {
            String ids = args[0].trim();
            if (StringUtils.isNotBlank(ids)) {
                String[] domainIdArr = ids.split(",");
                for (String domainId : domainIdArr) {
                    int id = Integer.parseInt(domainId.trim());
                    domainIdList.add(id);
                }
            }
        }
        if (runDomainIds.size() > 0) {
            domainIdList.addAll(runDomainIds);
        }
        domainIdList = domainIdList.stream().distinct().collect(Collectors.toList());
        System.out.println("======domainIdList:" + domainIdList);
        return domainIdList;
    }

    private void initParentFilePath(String[] args) {
        if (args.length == 2) {
            parentFilePath = args[1].trim();
        } else {
            parentFilePath = DEFAULT_PARENT_FILE_PATH;
        }
        System.out.println("======parentFilePath:" + parentFilePath);
    }

    private Map<Integer, SettingVo> getSettingMapByDomainList(List<Integer> domainIdList) {
        List<Map<String, Object>> settingByDomainList = ownDomainSettingDAO.getSettingByDomainList(domainIdList);
        if (settingByDomainList == null || settingByDomainList.isEmpty()) {
            return null;
        }
        Map<Integer, SettingVo> domainIdPSettingMap = settingByDomainList.stream().map(settingMap -> {
            SettingVo settingVo = new SettingVo();
            settingVo.setDomainId((int) settingMap.get("id"));
            settingVo.setDomain((String) settingMap.get("domain"));
            boolean sepGeo = Objects.isNull(settingMap.get("sepGeo")) ? false : (boolean) settingMap.get("sepGeo");
            settingVo.setSepGeo(sepGeo);
            boolean diffSE = Objects.isNull(settingMap.get("diffSE")) ? false : (boolean) settingMap.get("diffSE");
            settingVo.setDiffSE(diffSE);
            settingVo.setDeviceType((String) settingMap.get("deviceType"));
            settingVo.setSE((String) settingMap.get("SE"));
            return settingVo;
        }).collect(Collectors.toMap(SettingVo::getDomainId, Function.identity()));
        return domainIdPSettingMap;
    }

    private Map<Integer, List<String>> getTopCompetitorByDomainId(List<Integer> domainIdList, Map<Integer, String> domainIdPNameMap) {
        Map<Integer, List<String>> resultMap = new HashMap<>();
        Map<Integer, String> rankParamMap = getRankParam(domainIdList);
        Map<Integer, SettingVo> settingMap = getSettingMapByDomainList(domainIdList);
        if (Objects.isNull(settingMap)) {
            return null;
        }
        for (Integer domainId : domainIdList) {
            List<String> domainList = new ArrayList<>();
            SettingVo settingVo = settingMap.get(domainId);
            String paramValue = rankParamMap.get(domainId);
            String domainName = domainIdPNameMap.get(domainId);
            String reverseDomain = reverseDomain(domainName);
            boolean isWithSes = getIsWithSes(settingVo.getSepGeo(), settingVo.getDiffSE());
            String rankDate = FormatUtils.formatDate(getBeforeDate(TODAY_DATE, RANK_DAYS, false), "yyyy-MM-dd");
            String se = settingVo.getSE().trim();
            String[] seArr = se.split("_");
            String enginId = seArr[0].trim();
            String languageId = seArr[1].trim();
            String device = seArr[2].trim();
            String rankTableName = getRankTableName(enginId, languageId, device);
            System.out.println("=====rankTableName:" + rankTableName);
            String querySql = getRankIntelligenceQuerySql(domainId, Integer.parseInt(enginId), Integer.parseInt(languageId), device, paramValue, rankDate, rankTableName, reverseDomain, isWithSes);
            List<Map<String, Object>> competitorList = clDailyRankingEntityDao.getTopCompetitorByQuerySql(querySql);
            if (competitorList != null && !competitorList.isEmpty()) {
                domainList = competitorList.stream().map(map -> (String) map.get("domain")).collect(Collectors.toList());
            }
            // add own domain
            domainList.add(domainName);
            resultMap.put(domainId, domainList);
        }
        return resultMap;
    }

    private Map<Integer, String> getRankParam(List<Integer> domainIdList) {
        Map<Integer, String> resultMap = new HashMap<>();
        // get default rank param
        String defaultParam = getParamValue(null);
        List<RankIndexParamEntity> rankParamList = rankIndexParamDAO.getRankIndexParamsByIds(domainIdList);
        if (rankParamList != null && rankParamList.size() > 0) {
            Map<Integer, List<RankIndexParamEntity>> domainGroup = rankParamList.stream().collect(Collectors.groupingBy(RankIndexParamEntity::getOwnDomainId));
            domainIdList.forEach(domainId -> {
                if (domainGroup.containsKey(domainId)) {
                    List<RankIndexParamEntity> domainRankParamList = domainGroup.get(domainId);
                    String paramValue = getParamValue(domainRankParamList);
                    resultMap.put(domainId, paramValue);
                } else {
                    resultMap.put(domainId, defaultParam);
                }
            });
        } else {
            // all domain use default param
            domainIdList.forEach(domainId -> {
                resultMap.put(domainId, defaultParam);
            });
        }
        return resultMap;
    }

    private String getParamValue(List<RankIndexParamEntity> domainRankParamList) {
        if (domainRankParamList == null) {
            domainRankParamList = rankIndexParamDAO.getRankIndexParams(0, 0);
        }
        String paramValue = "";
        List<Float> valueList = domainRankParamList.stream().map(RankIndexParamEntity::getParamValue).collect(Collectors.toList());
        paramValue = "["+ StringUtils.join(valueList, ",") + "]";
        return paramValue;
    }

    private boolean getIsWithSes(Boolean separateGeo, Boolean diffSE) {
        return (separateGeo != null && separateGeo) || (diffSE != null && diffSE);
    }

    public static Date getBeforeDate(Date date, int days, boolean isAdd) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (!isAdd) {
            days = days * -1;
        }
        calendar.add(Calendar.DAY_OF_YEAR, days);
        return calendar.getTime();
    }

    private String getRankTableName(String engineId, String languageId, String device) {
        // ^d_ranking_detail_us_202308$
        String dateMonth = FormatUtils.formatDate(TODAY_DATE, "yyyyMM");
        String engineScope = ("1".equals(engineId) && "1".equals(languageId)) ? "us" : "intl";
        return "^" + device + "_ranking_detail_" + engineScope + "_" + dateMonth + "$";
    }

    private String reverseDomain(String domain) {
        String[] parts = domain.split("\\.");
        StringBuilder reversed = new StringBuilder();
        for (int i = parts.length - 1; i >= 0; i--) {
            if (i != parts.length - 1) {
                reversed.append(".");
            }
            reversed.append(parts[i]);
        }
        return reversed.toString();
    }

    private String getRankIntelligenceQuerySql(int domainId, int engineId, int languageId, String device, String paramValue, String rankDate,
                                               String rankTableName, String reverseDomain, boolean isWithSes) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT domain,domain_reverse as reverseDomain,round(estdTraffic,2) as traffic from ( ");
        sql.append("SELECT arrayStringConcat(arrayReverse(splitByChar('.', domain_reverse)), '.') as domain, domain_reverse, ");
        sql.append(paramValue + " AS ctr, ");
        sql.append("SUM(if(web_rank > 30, 0, (avg_search_volume * (ctr[web_rank])))) AS estdTraffic,count() ");
        sql.append("FROM merge(seo_daily_ranking, '").append(rankTableName).append("') ");
        sql.append("WHERE hrd = 1 AND own_domain_id = " + domainId +" AND engine_id = " + engineId + " AND language_id = " + languageId + " AND location_id = 0  ");
        if (isWithSes) {
            sql.append("AND dictGetUInt32('file_dic_tracked_keyword_with_ses', 'own_domain_id', (toUInt32(" + domainId + "), toUInt32(engine_id), toUInt32(language_id), toString('" + device + "'), toUInt64(URLHash(keyword_name)), toUInt32(location_id))) > 0 ");
        } else {
            sql.append("AND dictGetUInt32('file_dic_tracked_keyword_without_ses', 'own_domain_id', (toUInt32(" + domainId + "), toUInt64(URLHash(keyword_name)), dictGetUInt32('geo_master_mapping', 'geoMasterId', tuple(location_id)))) > 0 ");
        }
        sql.append("AND ranking_date = '" + rankDate + "'  AND web_rank > 0 and web_rank<=10 AND domain_reverse != '" + reverseDomain + "' ");
        sql.append("GROUP BY domain_reverse ORDER BY estdTraffic DESC LIMIT 10) ");
        System.out.println("=======getRankIntelligenceQuerySql:" + sql);
        return sql.toString();
    }

    private int getRandom() {
        return random.nextInt(900000) + 100000;
    }

    @Data
    @NoArgsConstructor
    public static class SettingVo {
        private int domainId;
        private String domain;
        private Boolean sepGeo;
        private Boolean diffSE;
        private String deviceType;
        private String SE;
    }
}
