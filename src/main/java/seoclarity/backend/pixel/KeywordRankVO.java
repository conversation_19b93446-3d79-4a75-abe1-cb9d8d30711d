package seoclarity.backend.pixel;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;

public class KeywordRankVO implements Serializable {
	private static final long serialVersionUID = 1351401185672317490L;

	private Integer searchLanguage;
	private Integer searchEngine;
	private String keyword;
	private String queryDate;
	private int qeuryState;
	private Integer firstPageSize;
	private String plaFlg;
	private String ppcFlg;
	private String researchFlg;
	private String createDate;
	private long searchVol;
	private float cpc;
	private Integer id;
	private String googleRecommend;
	private String googleResultCnt;
	private String[] domainList;
	private List<String> relatedSearch;
	private int sendToQDate;
	private boolean emptyHtml = false;
	private boolean notRealSearchVolume = false;
	private String rightFreeShop;
	private HashSet<String> triggerCodes;
	private String ossKeyPath;
	private List<PixelRankEntityVO> pixelRankList;
	
	private Integer cityId;
	private String cityName;


	public Integer getSearchLanguage() {
		return searchLanguage;
	}

	public void setSearchLanguage(Integer searchLanguage) {
		this.searchLanguage = searchLanguage;
	}

	public Integer getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(Integer searchEngine) {
		this.searchEngine = searchEngine;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getQueryDate() {
		return queryDate;
	}

	public void setQueryDate(String queryDate) {
		this.queryDate = queryDate;
	}

	public int getQeuryState() {
		return qeuryState;
	}

	public void setQeuryState(int qeuryState) {
		this.qeuryState = qeuryState;
	}

	public Integer getFirstPageSize() {
		return firstPageSize;
	}

	public void setFirstPageSize(Integer firstPageSize) {
		this.firstPageSize = firstPageSize;
	}

	public String getPlaFlg() {
		return plaFlg;
	}

	public void setPlaFlg(String plaFlg) {
		this.plaFlg = plaFlg;
	}

	public String getPpcFlg() {
		return ppcFlg;
	}

	public void setPpcFlg(String ppcFlg) {
		this.ppcFlg = ppcFlg;
	}

	public String getResearchFlg() {
		return researchFlg;
	}

	public void setResearchFlg(String researchFlg) {
		this.researchFlg = researchFlg;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public long getSearchVol() {
		return searchVol;
	}

	public void setSearchVol(long searchVol) {
		this.searchVol = searchVol;
	}

	public float getCpc() {
		return cpc;
	}

	public void setCpc(float cpc) {
		this.cpc = cpc;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getGoogleRecommend() {
		return googleRecommend;
	}

	public void setGoogleRecommend(String googleRecommend) {
		this.googleRecommend = googleRecommend;
	}

	public String getGoogleResultCnt() {
		return googleResultCnt;
	}

	public void setGoogleResultCnt(String googleResultCnt) {
		this.googleResultCnt = googleResultCnt;
	}

	public String[] getDomainList() {
		return domainList;
	}

	public void setDomainList(String[] domainList) {
		this.domainList = domainList;
	}

	public List<String> getRelatedSearch() {
		return relatedSearch;
	}

	public void setRelatedSearch(List<String> relatedSearch) {
		this.relatedSearch = relatedSearch;
	}

	public int getSendToQDate() {
		return sendToQDate;
	}

	public void setSendToQDate(int sendToQDate) {
		this.sendToQDate = sendToQDate;
	}

	public boolean isEmptyHtml() {
		return emptyHtml;
	}

	public void setEmptyHtml(boolean emptyHtml) {
		this.emptyHtml = emptyHtml;
	}

	public boolean isNotRealSearchVolume() {
		return notRealSearchVolume;
	}

	public void setNotRealSearchVolume(boolean notRealSearchVolume) {
		this.notRealSearchVolume = notRealSearchVolume;
	}

	public String getRightFreeShop() {
		return rightFreeShop;
	}

	public void setRightFreeShop(String rightFreeShop) {
		this.rightFreeShop = rightFreeShop;
	}

	public HashSet<String> getTriggerCodes() {
		return triggerCodes;
	}

	public void setTriggerCodes(HashSet<String> triggerCodes) {
		this.triggerCodes = triggerCodes;
	}

	public String getOssKeyPath() {
		return ossKeyPath;
	}

	public void setOssKeyPath(String ossKeyPath) {
		this.ossKeyPath = ossKeyPath;
	}

	public List<PixelRankEntityVO> getPixelRankList() {
		return pixelRankList;
	}

	public void setPixelRankList(List<PixelRankEntityVO> pixelRankList) {
		this.pixelRankList = pixelRankList;
	}

	public Integer getCityId() {
		
		if (cityId == null) {
			return 0;
		}
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}
	
	
}
