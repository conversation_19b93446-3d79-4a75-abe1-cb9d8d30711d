package seoclarity.backend.pixel;

import java.util.List;

public class PixelRankEntityVO {

	private Integer pixelRank;
	private String cssType;
	private String cssPath;

	private List<String> typeList;
	private Integer rank;
	private Integer type;
	private Double offsetTop;
	private Double offsetBottom;
	private Double height;
	private Integer fold;
	private Integer foldRank;

	private String landingPage;
	private String domain;
	private String label;
	private String metaDesc;

	public Integer getPixelRank() {
		return pixelRank;
	}

	public void setPixelRank(Integer pixelRank) {
		this.pixelRank = pixelRank;
	}

	public String getCssType() {
		return cssType;
	}

	public void setCssType(String cssType) {
		this.cssType = cssType;
	}

	public String getCssPath() {
		return cssPath;
	}

	public void setCssPath(String cssPath) {
		this.cssPath = cssPath;
	}

	public List<String> getTypeList() {
		return typeList;
	}

	public void setTypeList(List<String> typeList) {
		this.typeList = typeList;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Double getOffsetTop() {
		return offsetTop;
	}

	public void setOffsetTop(Double offsetTop) {
		this.offsetTop = offsetTop;
	}

	public Double getOffsetBottom() {
		return offsetBottom;
	}

	public void setOffsetBottom(Double offsetBottom) {
		this.offsetBottom = offsetBottom;
	}

	public Double getHeight() {
		return height;
	}

	public void setHeight(Double height) {
		this.height = height;
	}

	public Integer getFold() {
		return fold;
	}

	public void setFold(Integer fold) {
		this.fold = fold;
	}

	public Integer getFoldRank() {
		return foldRank;
	}

	public void setFoldRank(Integer foldRank) {
		this.foldRank = foldRank;
	}

	public String getLandingPage() {
		return landingPage;
	}

	public void setLandingPage(String landingPage) {
		this.landingPage = landingPage;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getMetaDesc() {
		return metaDesc;
	}

	public void setMetaDesc(String metaDesc) {
		this.metaDesc = metaDesc;
	}

}
