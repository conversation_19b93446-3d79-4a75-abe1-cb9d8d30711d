package seoclarity.backend.pixel.crawl;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.ecs.model.v20140526.*;
import com.amazonaws.services.sqs.AmazonSQS;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.pixelcrawl.PixelCrawlDetailDao;
import seoclarity.backend.dao.actonia.pixelcrawl.PixelCrawlInstanceDao;
import seoclarity.backend.dao.mdbkeywordsuggest.RankQcInfoEntityDAO;
import seoclarity.backend.entity.actonia.pixelcrawl.PixelCrawlDetailEntity;
import seoclarity.backend.entity.actonia.pixelcrawl.PixelCrawlInstanceEntity;
import seoclarity.backend.entity.actoniamonitor.RankQcInfoEntity;
import seoclarity.backend.pixel.crawl.service.CallerApiService;
import seoclarity.backend.pixel.crawl.service.InstanceParam;
import seoclarity.backend.utils.DateUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * https://www.wrike.com/open.htm?id=1324784317
 */
@Slf4j
public class PixelDailyCrawlStart {

    private static final String SQS_US_DESKTOP = "SQS_OUT_PIXEL3_GOOGLE_DESKTOP_US";
    private static final String SQS_INTL_DESKTOP = "SQS_OUT_PIXEL3_GOOGLE_DESKTOP_INTL";
    private static final String SQS_US_MOBILE = "SQS_OUT_PIXEL3_GOOGLE_MOBILE_US";
    private static final String SQS_INTL_MOBILE = "SQS_OUT_PIXEL3_GOOGLE_MOBILE_INTL";
    private static final String[] instanceNameArr = {"ecs.t5-lc1m2.small","ecs.t6-c1m1.large","ecs.n1.small","ecs.n4.small","ecs.mn4.small","ecs.n4.large","ecs.sn1.medium","ecs.hfc5.large","ecs.u1-c1m2.large"};
    private static final String DEFAULT_DISK_TYPE = "cloud_efficiency"; // 默认磁盘类型
    private static final String SPECIAL_SYSTEM_DISK_TYPE = "cloud_auto"; // 只有ecs.u1-c1m2.large 类型使用  其它类型使用cloud_efficiency
    private static final String SPECIAL_SYSTEM_INSTANCE_TYPE = "ecs.u1-c1m2.large"; // 特殊实例
    private static final float LIMIT_PRICE_UNIT = 0.002f;
    private static final int SQS_INSTANCE_LIMIT_NUM = 400;
    private static final int IMAGE_TYPE = 1; // 1: vs3     2: vs2
    private static final Date currentDate = new Date();
    private static final Integer[] processList = {PixelCrawlInstanceEntity.FREQ_DAILY_US_DESKTOP, PixelCrawlInstanceEntity.FREQ_DAILY_US_MOBILE, PixelCrawlInstanceEntity.FREQ_DAILY_INTL_DESKTOP, PixelCrawlInstanceEntity.FREQ_DAILY_INTL_MOBILE}; // 1: us desktop 2: us mobile 3: intl desktop 4: intl mobile
    private static final int rankDate = Integer.parseInt(FormatUtils.formatDate(currentDate, "yyyyMMdd"));
    private static final int CRAWL_UNIT = 10000;
    protected String emailTo = "<EMAIL>";
    private static final String[] bccTo = null;
//    private static final String[] bccTo = {"<EMAIL>", "<EMAIL>", "<EMAIL>"};

    private static final String REGION_TYPE_US = "us";
    private static final String REGION_TYPE_INTL = "intl";
    private static final String SLASH = "/";
    private static final String UNDERLINE = "_";
    private static final String FILE_TYPE_TXT = ".txt";

    private boolean isTest; // true: test false: prod
    private boolean isFirstStart = false; // only use for first send email
    private String parentFilePath = "/home/<USER>/source/pixel_crawl_start/tmp_file";
    private InstanceParam instanceParamCommon;
    private String usMobileQueueUrl;
    private String usDesktopQueueUrl;
    private String intlMobileQueueUrl;
    private String intlDesktopQueueUrl;
    private Map<String, String> instanceIdFileMap;
    private AmazonSQS amazonSQS;
    public ZeptoMailSenderComponent zeptoMailSenderComponent;
    private PixelCrawlInstanceDao pixelCrawlInstanceDao;
    private PixelCrawlDetailDao pixelCrawlDetailDao;
    private CallerApiService callerApiService;


    public PixelDailyCrawlStart() {
        amazonSQS = SQSUtils.getAmazonSQS();
        callerApiService = new CallerApiService();
        pixelCrawlInstanceDao = SpringBeanFactory.getBean("pixelCrawlInstanceDao");
        pixelCrawlDetailDao = SpringBeanFactory.getBean("pixelCrawlDetailDao");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    public static void main(String[] args) {
        new PixelDailyCrawlStart().startProcess(args);
    }

    private void startProcess(String[] args) {
        if (args == null || args.length < 2) {
            log.info("input parameters count should be 2.");
            return;
        }
        initParam(args);
        if (!isTest) {
            for (int i = 0; i < processList.length; i++) {
                instanceParamCommon.setSystemDiskCategory(DEFAULT_DISK_TYPE);
                try {
                    process(processList[i]);
                } catch (Exception e) {
                    // todo 修改email
                    sendExceptionAlertEmail(RankQcInfoEntity.DEVICE_DESKTOP, e.getMessage());
                    e.printStackTrace();
                }
            }
        }
    }

    private void process(int processType) {
        String device = (processType == 1 || processType == 3) ? RankQcInfoEntity.DEVICE_DESKTOP : RankQcInfoEntity.DEVICE_MOBILE;
        String queueName = "";
        if (processType == 1 || processType == 3) {
            device = RankQcInfoEntity.DEVICE_DESKTOP;
            if (processType == 1) {
                queueName = SQS_US_DESKTOP;
            } else {
                queueName = SQS_INTL_DESKTOP;
            }
        } else if (processType == 2 || processType == 4) {
            device = RankQcInfoEntity.DEVICE_MOBILE;
            if (processType == 2) {
                queueName = SQS_US_MOBILE;
            } else {
                queueName = SQS_INTL_MOBILE;
            }
        }
        PixelCrawlInstanceEntity pixelCrawlInstance = genInstanceRecord(device, queueName, processType);
        startCrawl(pixelCrawlInstance, device, processType, queueName);
    }

    private void startCrawl(PixelCrawlInstanceEntity pixelCrawlInstance, String device, int processType, String queueName) {
        int crawlInstanceId = pixelCrawlInstance.getId();
        String sqsUrl = "";
        switch (processType) {
            case 1: sqsUrl = usDesktopQueueUrl; break;
            case 2: sqsUrl = usMobileQueueUrl; break;
            case 3: sqsUrl = intlDesktopQueueUrl; break;
            case 4: sqsUrl = intlMobileQueueUrl; break;
        }
        // getKwCountInQueue
        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, sqsUrl);
        int inFlight = map.get("MESSAGES_IN_FLIGHT");
        int kwCnt = map.get("MESSAGES_AVAILABLE");
        log.info("===currentKwInQueue process:{} crawlInstanceId:{} kwCnt:{} inFlight:{} device:{}", processType, crawlInstanceId, kwCnt, inFlight, device);
        int crawlAmount = 0;
        if (isFirstCrawl(crawlInstanceId)) {
            if (inFlight == 0 && kwCnt == 0) {
                if (isFirstStart) {
                    String subject = "DailyCrawl-pixel crawl checks no keyword in queue";
                    String content = "The number of keywords in the queue is 0. <br>" + "crawlInstanceId:" + crawlInstanceId + " device: " + device + " queueName:" + queueName;
                    sendErrorAlertEmail(subject, content);
                }
                log.info("no kw in queue device: {}", device);
                return;
            }
            if (kwCnt % CRAWL_UNIT == 0) {
                crawlAmount = kwCnt / CRAWL_UNIT;
            } else {
                crawlAmount = kwCnt / CRAWL_UNIT + 1;
            }
            batchCreateInstance(device, processType, pixelCrawlInstance, sqsUrl, crawlAmount);
        } else {
            if (kwCnt == 0 && inFlight == 0) {
                pixelCrawlInstanceDao.updateStatus(PixelCrawlInstanceEntity.STATUS_CRAWL_SUCCESS, pixelCrawlInstance.getId());
                stopCrawl(pixelCrawlInstance, device, processType);
            } else {
                int runningInstanceNum = getRunningInstanceNum(processType, device);
                log.info("===instanceNum num:{} crawlInstanceId:{} device: {}", runningInstanceNum, crawlInstanceId, device);
                // need start crawl
                if (kwCnt > 0 && inFlight == 0) {
                    if (runningInstanceNum >20) {
                        String subject = "DailyCrawl-pixel " + getProcessNameByDevice(device, processType) + " crawl instance status exception";
                        String content = "Running instance num is " + runningInstanceNum + " kwInQueue is " + kwCnt + " inFlight is 0." + ". <br>" + "crawlInstanceId:" + crawlInstanceId + " device: " + device + " queueName:" + queueName;
                        sendErrorAlertEmail(subject, content);
                        return;
                    } else {// < 20 将旧的停掉 然后再根据kw数重新启动
                        if (runningInstanceNum > 0) {
                            log.info("deadInstance device:{} instanceNum:{}", getProcessNameByDevice(device, processType), runningInstanceNum);
                            stopCrawl(pixelCrawlInstance, device, processType);
                        }
                    }

                    if (kwCnt % CRAWL_UNIT == 0) {
                        crawlAmount = kwCnt / CRAWL_UNIT;
                    } else {
                        crawlAmount = kwCnt / CRAWL_UNIT + 1;
                    }
                    if (crawlAmount > 100) {
                        if (crawlAmount >= SQS_INSTANCE_LIMIT_NUM) {
                            crawlAmount = SQS_INSTANCE_LIMIT_NUM;
                        }
                    }
                    if (crawlAmount > 0) {
                        batchCreateInstance(device, processType, pixelCrawlInstance, sqsUrl, crawlAmount);
                    }
                }
            }
        }
    }

    private void stopCrawl(PixelCrawlInstanceEntity pixelCrawlInstance, String device, int processType) {
        String filePath = "";
        for (int i = 0; i < instanceNameArr.length; i++) {
            filePath = getInstanceIdFilePath(processType, device, i);
            File file = new File(filePath);
            if (!file.exists()) {
                log.info("==stopInstanceSkip instanceId:{} device:{} instanceType:{} not created.", pixelCrawlInstance.getId(), device, instanceNameArr[i]);
                continue;
            }
            List<String> idList = null;
            try {
                idList = FileUtils.readLines(new File(filePath));
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (idList == null || idList.isEmpty()) {
                log.info("===noInstanceTosStop device: {} crawlInstanceId:{} instanceIdFile:{}", device, pixelCrawlInstance.getId(), filePath);
                return;
            }
            List<DescribeInstanceStatusResponse.InstanceStatus> totalInstanceStatus = new ArrayList<>();
            List<String> deleteInstanceIdList = new ArrayList<>();
            List<String> deleteFailureInstanceIdList = new ArrayList<>();
            List<String> stopFailureInstanceIdList = new ArrayList<>();
            List<List<String>> splitIdList = splitList(idList, 50);

            for (List<String> instanceIdList : splitIdList) {
                DescribeInstanceStatusResponse statusResponse = callerApiService.getInstanceStatus(instanceParamCommon, instanceIdList);
                List<DescribeInstanceStatusResponse.InstanceStatus> instanceStatuses = statusResponse.getInstanceStatuses();
                totalInstanceStatus.addAll(instanceStatuses);
            }
            if (!totalInstanceStatus.isEmpty()) {
                Map<String, List<String>> statusPIdListMap = new HashMap<>();
                for (DescribeInstanceStatusResponse.InstanceStatus instanceStatus : totalInstanceStatus) {
                    String status = instanceStatus.getStatus();
                    String instanceId = instanceStatus.getInstanceId();
                    if (statusPIdListMap.containsKey(status)) {
                        statusPIdListMap.get(status).add(instanceId);
                    } else {
                        List<String> instanceIdList = new ArrayList<>();
                        instanceIdList.add(instanceId);
                        statusPIdListMap.put(status, instanceIdList);
                    }
                }
                log.info("===statusMap:{}", statusPIdListMap);
                if (!statusPIdListMap.isEmpty()) {
                    for (String string : statusPIdListMap.keySet()) {
                        List<String> instanceIdList = statusPIdListMap.get(string);
                        List<List<String>> lists = splitList(instanceIdList, 100);
                        if (StringUtils.equalsIgnoreCase(string, "Stopped")) {
                            for (List<String> list : lists) {
                                DeleteInstancesResponse deleteInstancesResponse = callerApiService.batchDeleteInstance(instanceParamCommon, list);
                                if (deleteInstancesResponse != null) {
                                    String requestId = deleteInstancesResponse.getRequestId();
                                    if (StringUtils.isNotBlank(requestId)) {
                                        deleteInstanceIdList.addAll(list);
                                    } else {
                                        deleteFailureInstanceIdList.addAll(list);
                                    }
                                } else {
                                    deleteFailureInstanceIdList.addAll(list);
                                }
                                try {
                                    TimeUnit.SECONDS.sleep(5);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                            }
                        } else if (!StringUtils.equalsIgnoreCase(string, "Stopping")) {
                            for (List<String> list : lists) {
                                int stopCnt = 0;
                                List<String> tmpList = list;
                                int currentStopCnt = 0;
                                List<String> deleteList = new ArrayList<>();
                                while (true) {
                                    StopInstancesResponse stopInstancesResponse = callerApiService.batchStopInstances(instanceParamCommon, tmpList);
                                    if (stopInstancesResponse != null) {
                                        List<StopInstancesResponse.InstanceResponse> instanceResponses = stopInstancesResponse.getInstanceResponses();
                                        if (!instanceResponses.isEmpty()) {
                                            for (StopInstancesResponse.InstanceResponse instanceRespons : instanceResponses) {
                                                if (StringUtils.equalsIgnoreCase(instanceRespons.getCode(), "200")) {
                                                    tmpList.remove(instanceRespons.getInstanceId());
                                                    deleteList.add(instanceRespons.getInstanceId());
                                                    stopCnt++;
                                                }
                                            }
                                        }
                                    }

                                    if (stopCnt == list.size() || tmpList.isEmpty()) {
                                        break;
                                    } else {
                                        currentStopCnt++;
                                        if (currentStopCnt > 5) {
                                            stopFailureInstanceIdList.addAll(tmpList);
                                            break;
                                        }
                                    }
                                    try {
                                        TimeUnit.SECONDS.sleep(20);
                                    } catch (InterruptedException e) {
                                        e.printStackTrace();
                                    }
                                }
                                log.info("===stopCnt:{} tmpList:{} deleteListSize:{} deleteList:{}", stopCnt, tmpList.size(), deleteList.size(), deleteList);
                                try {
                                    TimeUnit.MINUTES.sleep(2);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }

                                if (!deleteList.isEmpty()) {
                                    DeleteInstancesResponse deleteInstancesResponse = callerApiService.batchDeleteInstance(instanceParamCommon, deleteList);
                                    if (deleteInstancesResponse != null) {
                                        String requestId = deleteInstancesResponse.getRequestId();
                                        if (StringUtils.isNotBlank(requestId)) {
                                            deleteInstanceIdList.addAll(deleteList);
                                        } else {
                                            deleteFailureInstanceIdList.addAll(deleteList);
                                        }
                                    } else {
                                        deleteFailureInstanceIdList.addAll(deleteList);
                                    }
                                    try {
                                        TimeUnit.SECONDS.sleep(5);
                                    } catch (InterruptedException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                        } else if (StringUtils.equalsIgnoreCase(string, "Stopping")) {
                            try {
                                TimeUnit.SECONDS.sleep(60);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            for (List<String> list : lists) {
                                DeleteInstancesResponse deleteInstancesResponse = callerApiService.batchDeleteInstance(instanceParamCommon, list);
                                if (deleteInstancesResponse != null) {
                                    String requestId = deleteInstancesResponse.getRequestId();
                                    if (StringUtils.isNotBlank(requestId)) {
                                        deleteInstanceIdList.addAll(list);
                                    } else {
                                        deleteFailureInstanceIdList.addAll(list);
                                    }
                                } else {
                                    deleteFailureInstanceIdList.addAll(list);
                                }
                                try {
                                    TimeUnit.SECONDS.sleep(5);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }
            }
            int beforeDeleteCount = idList.size() - deleteInstanceIdList.size() - deleteFailureInstanceIdList.size() - stopFailureInstanceIdList.size();
            String info = "===stopCrawlInfo total:" + idList.size() + " autoDelete:" + beforeDeleteCount + " delete:" + deleteInstanceIdList.size() + " notDelete:" + deleteFailureInstanceIdList.size() + " stopFailure:" + stopFailureInstanceIdList.size();
            log.info("info{}", info);
            if (!stopFailureInstanceIdList.isEmpty() || !deleteFailureInstanceIdList.isEmpty()) {
                log.info("===stopFailureInstanceIdList:{}",JSON.toJSONString(stopFailureInstanceIdList));
                log.info("===deleteFailureInstanceIdList:{}",JSON.toJSONString(deleteFailureInstanceIdList));
                String subject = "DailyCrawl-part instance delete failure";
                String content = "part instance delete failure. <br>stopFailureInstanceIdList:" + stopFailureInstanceIdList + "<br>deleteFailureInstanceIdList:" + deleteFailureInstanceIdList + "<br>" + info + "<br>";
                sendErrorAlertEmail(subject, content);
            }
        }
    }

    private void initParam(String[] args) {
        isTest = Boolean.parseBoolean(args[0]);
        parentFilePath = args[1];
        log.info("==startParam isTest:{} parentFilePath:{}", isTest, parentFilePath);
        int createCount = 0;
        while (true) {
            instanceParamCommon = callerApiService.generateInstanceParam(isTest, IMAGE_TYPE);
            if (instanceParamCommon != null) {
                break;
            } else {
                createCount++;
                if (createCount > 2) {
                    break;
                }
            }
        }
        if (instanceParamCommon == null) {
            log.info("instanceParamCommon is null");
            String subject = "DailyCrawl-Instance parameter creation failed";
            String content = "Failed to construct parameters for starting the daily crawler instance.";
            sendErrorAlertEmail(subject, content);
            throw new RuntimeException("instanceParamCommon is null");
        }

        usDesktopQueueUrl = SQSUtils.createQueue(SQS_US_DESKTOP, amazonSQS);
        usMobileQueueUrl = SQSUtils.createQueue(SQS_US_MOBILE, amazonSQS);
        intlDesktopQueueUrl = SQSUtils.createQueue(SQS_INTL_DESKTOP, amazonSQS);
        intlMobileQueueUrl = SQSUtils.createQueue(SQS_INTL_MOBILE, amazonSQS);

        instanceIdFileMap = new HashMap<>();

        for (int i = 0; i < instanceNameArr.length; i++) {
            String desktopUsFilePath = parentFilePath + SLASH + REGION_TYPE_US + UNDERLINE + RankQcInfoEntity.DEVICE_DESKTOP + UNDERLINE + i + UNDERLINE + rankDate + FILE_TYPE_TXT;
            instanceIdFileMap.put(REGION_TYPE_US + UNDERLINE + RankQcInfoEntity.DEVICE_DESKTOP + i, desktopUsFilePath);

            String mobileUsFilePath = parentFilePath + SLASH + REGION_TYPE_US + UNDERLINE + RankQcInfoEntity.DEVICE_MOBILE + UNDERLINE + i + UNDERLINE + rankDate + FILE_TYPE_TXT;
            instanceIdFileMap.put(REGION_TYPE_US + UNDERLINE + RankQcInfoEntity.DEVICE_MOBILE + i, mobileUsFilePath);

            String desktopFileIntlPath = parentFilePath + SLASH + REGION_TYPE_INTL + UNDERLINE + RankQcInfoEntity.DEVICE_DESKTOP + UNDERLINE + i + UNDERLINE + rankDate + FILE_TYPE_TXT;
            instanceIdFileMap.put(REGION_TYPE_INTL + UNDERLINE + RankQcInfoEntity.DEVICE_DESKTOP + i, desktopFileIntlPath);

            String mobileFileIntlPath = parentFilePath + SLASH + REGION_TYPE_INTL + UNDERLINE + RankQcInfoEntity.DEVICE_MOBILE + UNDERLINE + i + UNDERLINE + rankDate + FILE_TYPE_TXT;
            instanceIdFileMap.put(REGION_TYPE_INTL + UNDERLINE + RankQcInfoEntity.DEVICE_MOBILE + i, mobileFileIntlPath);
        }
    }

    private PixelCrawlInstanceEntity genInstanceRecord(String device, String queueName, int processType) { // processType is equal to frequence
        isFirstStart = false;
        PixelCrawlInstanceEntity instanceEntity = pixelCrawlInstanceDao.getByUniqueKey(rankDate, processType, device);
        if (instanceEntity == null) {
            isFirstStart = true;
            instanceEntity = new PixelCrawlInstanceEntity();
            instanceEntity.setRankDate(rankDate);
            instanceEntity.setFrequence(processType);
            instanceEntity.setDevice(device);
            instanceEntity.setSqsName(queueName);
            instanceEntity.setStatus(PixelCrawlInstanceEntity.STATUS_NOT_STARTED);
            int kwCount = getKwCountInQueue(processType, device);
            instanceEntity.setTotalKeywordCount(kwCount);
            instanceEntity.setCreateDate(currentDate);
            pixelCrawlInstanceDao.insert(instanceEntity);
            instanceEntity = pixelCrawlInstanceDao.getByUniqueKey(rankDate, processType, device);
        }
        return instanceEntity;
    }

    private int getKwCountInQueue(int processType, String device) {
        String sqsUrl = "";
        switch (processType) {
            case 1: sqsUrl = usDesktopQueueUrl; break;
            case 2: sqsUrl = usMobileQueueUrl; break;
            case 3: sqsUrl = intlDesktopQueueUrl; break;
            case 4: sqsUrl = intlMobileQueueUrl; break;
        }
        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, sqsUrl);
        Integer messagesAvailable = map.get("MESSAGES_AVAILABLE");
        Integer inFlight = map.get("MESSAGES_IN_FLIGHT");
        log.info("==kwInQueue processType:{} msgCnt:{} inFlight:{} device:{}", processType, messagesAvailable, inFlight, device);
        return messagesAvailable;
    }

    private String getTime() {
        return FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    private void sendExceptionAlertEmail(String device, String errorMsg) {
        String subject = "DailyCrawl-Failed to start crawler instance";
        String content = "Failed to start crawler instance for device: " + device + ". detail: " + errorMsg;
        Map<String, Object> reportMap = new HashMap<>();
        reportMap.put("userName", "ALL");
        reportMap.put("errMsg", "&nbsp;&nbsp;&nbsp;&nbsp;" + content + ". time:" + getTime());
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, null, subject, "mail_alert_missing_bot_files.txt", "mail_alert_missing_bot_files.html",
                reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private void sendErrorAlertEmail(String emailSubject, String msg) {
        Map<String, Object> reportMap = new HashMap<>();
        reportMap.put("userName", "ALL");
        reportMap.put("errMsg", "&nbsp;&nbsp;&nbsp;&nbsp;" + msg + " time:" + getTime());
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, emailSubject, "mail_alert_missing_bot_files.txt", "mail_alert_missing_bot_files.html",
                reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private boolean isFirstCrawl(int instanceId) {
        int detailCnt = pixelCrawlDetailDao.getCountByPixelCrawlId(instanceId);
        return detailCnt <= 0;
    }

    private void batchCreateInstance(String device, int processType, PixelCrawlInstanceEntity pixelCrawlInstance, String sqsUrl, Integer crawlAmount) {
        String userData = getUserData(processType);
        instanceParamCommon.setUserData(userData);
        int crawlStartAmount = 0;
        int batchNum = 0;
        if (!isTest) {
            crawlStartAmount = crawlAmount;
            batchNum = crawlStartAmount / 100;
        } else {
            crawlStartAmount = 2;
            batchNum = 1;
        }
        log.info("===prepareCreateInstanceToStartCrawl processType:{} instanceId:{} rankDate:{} needCreateInstanceCnt:{}", processType, pixelCrawlInstance.getId(), rankDate, crawlStartAmount);
        int totalCreatCnt = 0;
        List<String> instanceIdList = new ArrayList<>();
        for (int index = 0; index < instanceNameArr.length; index++) {
            if (totalCreatCnt >= crawlStartAmount) {
                break;
            }
            String currentInstanceType = instanceNameArr[index];
            instanceParamCommon.setInstanceType(currentInstanceType);
            if (StringUtils.equalsIgnoreCase(currentInstanceType, SPECIAL_SYSTEM_INSTANCE_TYPE)) {
                instanceParamCommon.setSystemDiskCategory(SPECIAL_SYSTEM_DISK_TYPE);
            } else {
                instanceParamCommon.setSystemDiskCategory(DEFAULT_DISK_TYPE);
            }
            /*if (!checkInstanceStock()) {
                log.info("====outOfStock processType: {} instanceId:{} rankDate:{} instanceType:{}", processType, pixelCrawlInstance.getId(), rankDate, instanceParamCommon.getInstanceType());
                Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, sqsUrl);
                int inFlight = map.get("MESSAGES_IN_FLIGHT");
                int kwCnt = map.get("MESSAGES_AVAILABLE");

                String deviceStr = StringUtils.equalsIgnoreCase(device, RankQcInfoEntity.DEVICE_DESKTOP) ? "desktop" : "mobile";
                String emailSubject = "DailyCrawl-out of stock " + deviceStr + " crawlers.";
                String emailMsg = "insufficient inventory when creating instances for " + deviceStr + " crawlers. PixelCrawlInstanceId: " + pixelCrawlInstance.getId() + " msgInSQS:" + kwCnt + " inFlight:" + inFlight + " rankDate: " + rankDate + ".";
                sendErrorAlertEmail(emailSubject, emailMsg);
                return;
            }*/
            if (!checkInstanceStock()) {
                log.info("====outOfStock processType: {} instanceId:{} rankDate:{} instanceType:{}", processType, pixelCrawlInstance.getId(), rankDate, instanceParamCommon.getInstanceType());
                continue;
            }
            float latestPrice = callerApiService.getLatestPrice(instanceParamCommon);
            instanceParamCommon.setSpotPriceLimit(latestPrice + LIMIT_PRICE_UNIT);
            if (batchNum > 0) {
                for (int i = 0; i < batchNum; i++) {
                    RunInstancesResponse runInstancesResponse = null;
                    int currentRetryCount = 0;
                    while (true) {
                        runInstancesResponse = callerApiService.batchCreateInstance(instanceParamCommon);
                        if (runInstancesResponse != null) {
                            break;
                        } else {
                            currentRetryCount++;
                            if (currentRetryCount > 3) {
                                break;
                            }
                            try {
                                TimeUnit.MINUTES.sleep(2);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    if (runInstancesResponse == null) {
                        log.info("===runInstancesResponse is null loopIndex:{} createInstanceCnt:{} ", i, instanceIdList.size());
                        String subject = "DailyCrawl-Failed to create instance";
                        String msg = "Batch creation failed. Successfully created: " + instanceIdList.size() + ", still need to create: " + (crawlStartAmount - totalCreatCnt) + ", device: " + device + " instanceId: " + pixelCrawlInstance.getId() + " rankDate: " + rankDate + ".";
                        sendErrorAlertEmail(subject, msg);

                        createPixelInstanceDetail(sqsUrl, pixelCrawlInstance.getId(), 0, 0F, PixelCrawlDetailEntity.DETAIL_STATUS_START_FAILURE);
                        if (!instanceIdList.isEmpty()) {
                            writerFile(instanceIdList, processType, device, pixelCrawlInstance, index);
                            totalCreatCnt += instanceIdList.size();
                            instanceIdList.clear();
                        }
                        break;
                    }
                    List<String> instanceIdSets = runInstancesResponse.getInstanceIdSets();
                    log.info("===createInstanceId:{}", instanceIdSets);
                    instanceIdList.addAll(instanceIdSets);
                    Float tradePrice = runInstancesResponse.getTradePrice();
                    // create instanceDetail
                    createPixelInstanceDetail(sqsUrl, pixelCrawlInstance.getId(), instanceIdSets.size(), tradePrice, PixelCrawlDetailEntity.DETAIL_STATUS_START_NEW_INSTANCE);
                    log.info("===currentCreateInstanceCnt:{} tradePrice:{} loopIndex:{} needLoopCnt:{} device:{}", instanceIdSets.size(), tradePrice, (i + 1), batchNum, device);
                    try {
                        TimeUnit.MILLISECONDS.sleep(100);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                // create instanceDetail
                createPixelInstanceDetail(sqsUrl, pixelCrawlInstance.getId(), instanceIdList.size(), latestPrice, PixelCrawlDetailEntity.DETAIL_STATUS_START_NEW_INSTANCE);
            }
            totalCreatCnt += instanceIdList.size();
            batchNum = crawlStartAmount - totalCreatCnt;
            if (!instanceIdList.isEmpty()) {
                writerFile(instanceIdList, processType, device, pixelCrawlInstance, index);
                instanceIdList.clear();
            }
        }

        /*if (crawlStartAmount % instanceParamCommon.getAmount() != 0) {
            int newCrawlAmount = crawlStartAmount % instanceParamCommon.getAmount();
            log.info("===leadingZeros:{}", newCrawlAmount);
            instanceParamCommon.setAmount(newCrawlAmount);
            for (int index = 0; index < instanceNameArr.length; index++) {
                if (totalCreatCnt >= crawlStartAmount) {
                    break;
                }
                String currentInstanceType = instanceNameArr[index];
                instanceParamCommon.setInstanceType(currentInstanceType);
                if (StringUtils.equalsIgnoreCase(currentInstanceType, SPECIAL_SYSTEM_INSTANCE_TYPE)) {
                    instanceParamCommon.setSystemDiskCategory(SPECIAL_SYSTEM_DISK_TYPE);
                }
                if (!checkInstanceStock()) {
                    System.out.println("===currentInstanceTypeStockOut instanceType:" + instanceParamCommon.getInstanceType());
                    continue;
                }
                RunInstancesResponse runInstancesResponse = callerApiService.batchCreateInstance(instanceParamCommon);
                if (runInstancesResponse != null) {
                    List<String> instanceIdSets = runInstancesResponse.getInstanceIdSets();
                    Float tradePrice = runInstancesResponse.getTradePrice();
                    instanceIdList.addAll(instanceIdSets);
                    // create instanceDetail
                    createPixelInstanceDetail(sqsUrl, pixelCrawlInstance.getId(), instanceIdSets.size(), tradePrice, PixelCrawlDetailEntity.DETAIL_STATUS_START_NEW_INSTANCE);
                }
                totalCreatCnt += instanceIdList.size();
                writerFile(instanceIdList, processType, device, pixelCrawlInstance, index);
                instanceIdList.clear();
            }
        }*/


        if (totalCreatCnt == crawlStartAmount) {
            log.info("===startCrawlSuccess device: {} pixelInstanceId: {} crawlStartAmount: {}", device, pixelCrawlInstance.getId(), totalCreatCnt);
        } else {
            int needStartCrawlCnt = crawlStartAmount - totalCreatCnt;
            log.info("===startCrawlPartSuccess device: {} pixelInstanceId: {} totalInstanceCnt: {} crawlAmount: {} needCrawlCnt: {}", device, pixelCrawlInstance.getId(), crawlStartAmount, totalCreatCnt, needStartCrawlCnt);
            String deviceStr = StringUtils.equalsIgnoreCase(device, RankQcInfoEntity.DEVICE_DESKTOP) ? "desktop" : "mobile";
            String emailSubject = "DailyCrawl-Insufficient instances created";
            String emailMsg = deviceStr + " insufficient crawler instances started. Number to start: " + crawlStartAmount + ". Actual number started: " + totalCreatCnt + ".Remaining instances to start:" + (crawlStartAmount - totalCreatCnt) + ". Please note.";
            sendErrorAlertEmail(emailSubject, emailMsg);
        }
        log.info("===startInstanceInfo device: {} pixelInstanceId: {} crawlStartAmount: {}", device, pixelCrawlInstance.getId(), totalCreatCnt);
        instanceIdList.clear();
        pixelCrawlInstanceDao.updateStatus(PixelCrawlInstanceEntity.STATUS_PROCESSING, pixelCrawlInstance.getId());
    }

    private boolean checkInstanceStock() {
        boolean checkRes = false;
        DescribeAvailableResourceResponse availableResource = null;
        int retryCount = 0;
        while (true) {
            availableResource = callerApiService.getAvailableResource(instanceParamCommon);
            if (availableResource != null) {
                break;
            } else {
                retryCount++;
                if (retryCount > 3) {
                    break;
                }
            }
            try {
                TimeUnit.SECONDS.sleep(30);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        if (availableResource == null) {
            return false;
        }
        List<DescribeAvailableResourceResponse.AvailableZone> availableZones = availableResource.getAvailableZones();
        log.info("===checkInstanceStock availableZones: {}", JSON.toJSONString(availableZones));
        if (availableZones != null && !availableZones.isEmpty()) {
            for (DescribeAvailableResourceResponse.AvailableZone availableZone : availableZones) {
                List<DescribeAvailableResourceResponse.AvailableZone.AvailableResource> availableResources = availableZone.getAvailableResources();
                if (availableResources != null && !availableResources.isEmpty()) {
                    for (DescribeAvailableResourceResponse.AvailableZone.AvailableResource resource : availableResources) {
                        List<DescribeAvailableResourceResponse.AvailableZone.AvailableResource.SupportedResource> supportedResources = resource.getSupportedResources();
                        if (supportedResources != null && !supportedResources.isEmpty()) {
                            for (DescribeAvailableResourceResponse.AvailableZone.AvailableResource.SupportedResource supportedResource : supportedResources) {
                                String status = supportedResource.getStatus();
                                String statusCategory = availableZone.getStatusCategory();
                                if (StringUtils.equalsIgnoreCase(status, "Available") && StringUtils.equalsIgnoreCase(statusCategory, "WithStock")) {
                                    checkRes = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        } else {
            return false;
        }
        return checkRes;
    }

    private void writerFile(List<String> instanceIdList, int processType, String device, PixelCrawlInstanceEntity pixelCrawlInstance, int index) {
        String filePath = getInstanceIdFilePath(processType, device, index);
        try {
            log.info("===writerFile processType:{} pixelInstanceId: {} filePath: {} idList: {}", processType, pixelCrawlInstance.getId(), filePath, instanceIdList);
            FileUtils.writeLines(new File(filePath), instanceIdList, true);
        } catch (IOException e) {
            log.error("====savaInstanceIdError processType: {} pixelInstanceId: {} filePath: {} idList: {}", processType, pixelCrawlInstance.getId(), filePath, instanceIdList);
            e.printStackTrace();
        }
    }

    public <T> List<List<T>> splitList(List<T> originalList, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        int fromIndex = 0;

        while (fromIndex < originalList.size()) {
            int toIndex = Math.min(fromIndex + batchSize, originalList.size());
            batches.add(originalList.subList(fromIndex, toIndex));
            fromIndex = toIndex;
        }

        return batches;
    }

    private void createPixelInstanceDetail(String sqsUrl, int instanceId, Integer instanceCnt, Float tradePrice, int status) {
        PixelCrawlDetailEntity pixelCrawlDetailEntity = generatePixelCrawlDetailEntity(instanceId, instanceCnt, tradePrice, status);
        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, sqsUrl);
        int inFlight = map.get("MESSAGES_IN_FLIGHT");
        int kwCnt = map.get("MESSAGES_AVAILABLE");
        pixelCrawlDetailEntity.setMessagesInFlight(inFlight);
        pixelCrawlDetailEntity.setMessagesInSQS(kwCnt);
        pixelCrawlDetailDao.insert(pixelCrawlDetailEntity);
    }

    private PixelCrawlDetailEntity generatePixelCrawlDetailEntity(Integer pixelCrawlInstanceId, Integer instanceCnt, Float tradePrice, int status) {
        PixelCrawlDetailEntity pixelCrawlDetailEntity = new PixelCrawlDetailEntity();
        pixelCrawlDetailEntity.setInstanceId(pixelCrawlInstanceId);
        pixelCrawlDetailEntity.setInstanceCount(instanceCnt);
        if (tradePrice != null) {
            pixelCrawlDetailEntity.setInstanceUnitPrice(tradePrice);
        } else {
            float latestPrice = callerApiService.getLatestPrice(instanceParamCommon);
            pixelCrawlDetailEntity.setInstanceUnitPrice(latestPrice);
        }
        pixelCrawlDetailEntity.setStatus(status);
        pixelCrawlDetailEntity.setCreateDate(new Date());
        return pixelCrawlDetailEntity;
    }

    private String getUserData(int processType) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("spotInstance", true);
        jsonObject.addProperty("screenshotVersion", String.valueOf(rankDate));
        jsonObject.addProperty("threadCount", 1);
        jsonObject.addProperty("sqsAccessKey", AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextSQSAccessKey());
        jsonObject.addProperty("sqsSecretKey", AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey());
        jsonObject.addProperty("s3AccessKey", AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey());
        jsonObject.addProperty("s3SecretKey", AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey());
        if (!isTest) {
            switch (processType) {
                case 1: {
                    jsonObject.addProperty("keywordQueueName", SQS_US_DESKTOP);
                    jsonObject.addProperty("keywordOutQueueName", "SQS_UPLOAD_PIXEL3_GOOGLE_DESKTOP_US");
                    jsonObject.addProperty("provider", "vs3-desktop");
                    break;
                }
                case 2: {
                    jsonObject.addProperty("keywordQueueName", SQS_US_MOBILE);
                    jsonObject.addProperty("keywordOutQueueName", "SQS_UPLOAD_PIXEL3_GOOGLE_MOBILE_US");
                    jsonObject.addProperty("provider", "vs3-mobile");
                    break;
                }
                case 3: {
                    jsonObject.addProperty("keywordQueueName", SQS_INTL_DESKTOP);
                    jsonObject.addProperty("keywordOutQueueName", "SQS_UPLOAD_PIXEL3_GOOGLE_DESKTOP_INTL");
                    jsonObject.addProperty("provider", "vs3-desktop");
                    break;
                }
                case 4: {
                    jsonObject.addProperty("keywordQueueName", SQS_INTL_MOBILE);
                    jsonObject.addProperty("keywordOutQueueName", "SQS_UPLOAD_PIXEL3_GOOGLE_MOBILE_INTL");
                    jsonObject.addProperty("provider", "vs3-mobile");
                    break;
                }
            }
        } else {
            jsonObject.addProperty("keywordQueueName", "SQS_IN_PIXEL_WEEKLY_MOBILE_TEST");
            jsonObject.addProperty("keywordCrawlQueueName", "SQS_IN_CRAWL_PIXEL_WEEKLY_MOBILE_TEST");
            jsonObject.addProperty("keywordOutQueueNameUS", "SQS_OUT_PIXEL_GOOGLE_MOBILE_US_TEST");
            jsonObject.addProperty("keywordOutQueueNameINTL", "SQS_OUT_PIXEL_GOOGLE_MOBILE_INTL_TEST");
            jsonObject.addProperty("provider", "vs3-mobile");
        }

        return jsonObject.toString();

    }

    private String getProcessNameByDevice(String device, int processType) {
        String res = "VS 3-";
        switch (processType) {
            case 1:
            case 2:
                res += "US-";
                break;
            case 3:
            case 4:
                res += "INTL-";
                break;
        }
        if (StringUtils.equalsIgnoreCase(device, RankQcInfoEntity.DEVICE_DESKTOP)) {
            res += "Desktop";
        } else {
            res += "Mobile";
        }
        return res;
    }

    private int getRunningInstanceNum(int processType, String device) {
        int resultNum = 0;
        for (int index = 0; index < instanceNameArr.length; index++) {
            String instanceType = instanceNameArr[index];
            instanceParamCommon.setInstanceType(instanceType);
            int totalCount = callerApiService.getRunningInstanceNum(instanceParamCommon);
            if (totalCount <= 0) {
                log.info("==getRunningInstanceNum processType:{} device:{} totalCount:0 instanceType:{}", processType, device, instanceType);
                continue;
            }
            int totalPage = 0;
            if (totalCount % 100 == 0) {
                totalPage = totalCount / 100;
            } else {
                totalPage = totalCount / 100 + 1;
            }
            Set<String> runInstanceIdSet = new HashSet<>();
            for (int i = 1; i <= totalPage; i++) {
                instanceParamCommon.setPage(i);
                List<String> instanceIdList = callerApiService.getInstanceIdList(instanceParamCommon);
                if (instanceIdList != null && !instanceIdList.isEmpty()) {
                    runInstanceIdSet.addAll(instanceIdList);
                }
                sleep(200);
            }
            String instanceIdFilePath = getInstanceIdFilePath(processType, device, index);
            List<String> currentInstanceIdList = null;
            File file = new File(instanceIdFilePath);
            if (!file.exists()) {
                log.info("===getRunningInstanceSkip device:{} instanceType:{} not created.", device, instanceNameArr[index]);
                continue;
            }
            try {
                currentInstanceIdList = FileUtils.readLines(file);
            } catch (IOException e) {
                log.info("===readLinesError filePath:{} processType:{} device:{} error:{}", instanceIdFilePath, processType, device, e.getMessage());
                e.printStackTrace();
            }

            if (currentInstanceIdList == null || currentInstanceIdList.isEmpty()) {
                log.info("==currentInstanceIdList is empty processType:{} device:{} totalCount:{} totalPage:{} filePath{}", processType, device, totalCount, totalPage, instanceIdFilePath);
                continue;
            }
            long count = currentInstanceIdList.stream().filter(runInstanceIdSet::contains).count();
            resultNum += (int) count;
            log.info("==getRunningInstanceNum processType:{} device:{} totalCount:{} totalPage:{} currentInstanceNum:{} filePath{}", processType, device, totalCount, totalPage, count, instanceIdFilePath);
            instanceParamCommon.setPage(0);
        }
        return resultNum;
    }

    private void sleep(long time) {
        try {
            TimeUnit.MILLISECONDS.sleep(time);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private String getInstanceIdFilePath(int processType, String device, int index) {
        String filePath = "";
        switch (processType) {
            case 1:
            case 2:
                filePath = instanceIdFileMap.get(REGION_TYPE_US + UNDERLINE + device + index);
                break;
            case 3:
            case 4:
                filePath = instanceIdFileMap.get(REGION_TYPE_INTL + UNDERLINE + device + index);
                break;
        }
        return filePath;
    }
}
