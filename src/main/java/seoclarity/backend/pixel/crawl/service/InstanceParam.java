package seoclarity.backend.pixel.crawl.service;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class InstanceParam {

    private String zoneId; // cn-hangzhou-b
    private String regionId; // cn-hangzhou
    private String destinationResource; // InstanceType
    private String instanceType; // ecs.g5.large
    private String resourceType; // instance
    private String securityGroupId; // sg-bp11nhf94ivkdxwb2***
    private String networkCategory; // vpc
    private String networkType; // vpc
    private String imageId;
    private String imageName; // m-bp146shijn7hujkui9***
    private String vSwitchId; // vsw-bp164cyonthfudn9kj***
    private String systemDiskCategory; // cloud_essd，cloud_ssd，cloud_efficiency，cloud。
    private Integer systemDiskSize; // 40
    private Integer amount; // 200
    private String chargeType; // PostPaid
    private String spotStrategy; // SpotWithPriceLimit
    private Float spotPriceLimit; // 0.25F
    private int spotDuration; // 0
    private Integer internetMaxBandwidthOut; // 5
    private Integer internetMaxBandwidthIn; // 5
    private String networkInterfaceName;
    private String keyPairName;
    private String instanceName;
    private String hostName;
    private String userData;
    private Integer pageSize = 10;
    private Integer page;
    private String creditSpecification; // Standard Unlimited 突发性能实例的运行模式 默认Standard：标准模式  Unlimited：无性能约束模式

    private String instanceNameSuffix; // eg:vs3-*, vs2-*
}
