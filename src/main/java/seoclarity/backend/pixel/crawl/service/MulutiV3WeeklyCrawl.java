package seoclarity.backend.pixel.crawl.service;

import com.aliyuncs.ecs.model.v20140526.*;
import com.amazonaws.services.sqs.AmazonSQS;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.entity.actoniamonitor.RankQcInfoEntity;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.DateUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MulutiV3WeeklyCrawl {

    private static final String PARENT_FILE_PATH = "/Users/<USER>/Desktop/check_issue";
//    private static final String SQS_NAME_D = "SQS_IN_PIXEL_WEEKLY_DESKTOP_TMP202408";
//    private static final String SQS_NAME_M = "SQS_IN_PIXEL_WEEKLY_MOBILE_TMP202408";
    private static final String SQS_NAME_D = "SQS_IN_PIXEL_WEEKLY_DESKTOP";
    private static final String SQS_NAME_M = "SQS_IN_PIXEL_WEEKLY_MOBILE";
//    private static final String filePath = "/home/<USER>/source/radeL/bot_project/tmp_file/pixel_id_list/idList.txt";
    private static final String filePath = "/Users/<USER>/Desktop/instance/idList12.txt";
    private static final String SPECIAL_SYSTEM_INSTANCE_TYPE = "ecs.u1-c1m2.large"; // 特殊实例
    private static final String SPECIAL_SYSTEM_DISK_TYPE = "cloud_auto"; // 只有ecs.u1-c1m2.large 类型使用  其它类型使用cloud_efficiency
    private static final String DEFAULT_DISK_TYPE = "cloud_efficiency"; // 默认磁盘类型
    private static final CallerApiService callerApiService = new CallerApiService();
    private static final String[] instanceNameArr = {"ecs.t5-lc1m2.small", "ecs.t6-c1m1.large","ecs.n1.small","ecs.n4.small","ecs.mn4.small","ecs.n4.large","ecs.sn1.medium","ecs.hfc5.large","ecs.u1-c1m2.large"};
//    private static final String[] instanceNameArr = {"ecs.n1.small"};
    private final AmazonSQS amazonSQS = SQSUtils.getAmazonSQS();;
    private static final int limitNum = 1000;
    private static final int pageSize = 100;
    private int currentNum = 0;
    private int rankDate = 20240811;
    private String mobileQueueUrl;
    private String desktopQueueUrl;
    private float addPrice = 0.01f;


    private boolean isTest; // true: test false: prod
    private int processType; // 1: new pixel crawl 2: old pixel crawl
    private Map<String, String> instanceIdFileMap;
    private InstanceParam instanceParamCommon;


    public MulutiV3WeeklyCrawl() {
    }

    public static void main(String[] args) {
        new MulutiV3WeeklyCrawl().queueCheck();
//        new MulutiV3WeeklyCrawl().process();
    }

    private void process() {

        desktopQueueUrl = SQSUtils.createQueue(SQS_NAME_D, amazonSQS);
        mobileQueueUrl = SQSUtils.createQueue(SQS_NAME_M, amazonSQS);

        startCrawl(2, 200);
//        startCrawl2();
    }

    private void queueCheck() {
//        check();
        getRunningInstanceNum(1, "vs3D*");
        getRunningInstanceNum(1, "vs3M*");
        getMsgInQueue(1);
        System.out.println("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
//        callerApiService.getMsgInOutQueue(1);
//        callerApiService.getMsgInOutQueue(2);
//        System.out.println("============================================================================================================================================");
        getRunningInstanceNum(2, "vs2D*");
        getRunningInstanceNum(2, "vs2M*");
        getMsgInQueue(2);
        System.out.println("====================================");
//        getInstancePrice(1, null);


        // 停止实例
        // vs3 desktop
        /*stopInstance(1, "vs3D*");*/
        // vs3 mobile
        /*stopInstance(1, "vs3M*");*/
        // vs2 desktop
        /*stopInstance(2, "vs2D*");*/
        // vs2 mobile
        /*stopInstance(2, "vs2M*");*/
        // 停止实例


    }

    private String getUserData(String device, int rankDate, int processType) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("spotInstance", true);
        jsonObject.addProperty("sqsAccessKey", AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextSQSAccessKey());
        jsonObject.addProperty("sqsSecretKey", AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey());
        jsonObject.addProperty("s3AccessKey", AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey());
        jsonObject.addProperty("s3SecretKey", AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey());
        if (!isTest) {
            if (StringUtils.equalsIgnoreCase(device, RankQcInfoEntity.DEVICE_DESKTOP)) {
                if (processType == 1) {
                    jsonObject.addProperty("screenshotVersion", String.valueOf(rankDate));
                    jsonObject.addProperty("keywordQueueName", SQS_NAME_D);
                    jsonObject.addProperty("keywordCrawlQueueName", "SQS_IN_CRAWL_PIXEL_WEEKLY_DESKTOP");
                    jsonObject.addProperty("keywordOutQueueNameUS", "SQS_OUT_PIXEL_GOOGLE_DESKTOP_US");
                    jsonObject.addProperty("keywordOutQueueNameINTL", "SQS_OUT_PIXEL_GOOGLE_DESKTOP_INTL");
                    jsonObject.addProperty("provider", "vs3-desktop");
                } else {
                    jsonObject.addProperty("threadCount", 1);
                    jsonObject.addProperty("keywordQueueName", "FPR_US_COMMON_PIXEL_V2");
                    jsonObject.addProperty("keywordOutQueueName", "FPR_US_COMMON_PIXEL_V2_OUT");
                    jsonObject.addProperty("provider", "ec2-pixel-desktop");
                }
            } else if (StringUtils.equalsIgnoreCase(device, RankQcInfoEntity.DEVICE_MOBILE)) {
                if (processType == 1) {
                    jsonObject.addProperty("screenshotVersion", String.valueOf(rankDate));
                    jsonObject.addProperty("keywordQueueName", SQS_NAME_M);
                    jsonObject.addProperty("keywordCrawlQueueName", "SQS_IN_CRAWL_PIXEL_WEEKLY_MOBILE");
                    jsonObject.addProperty("keywordOutQueueNameUS", "SQS_OUT_PIXEL_GOOGLE_MOBILE_US");
                    jsonObject.addProperty("keywordOutQueueNameINTL", "SQS_OUT_PIXEL_GOOGLE_MOBILE_INTL");
                    jsonObject.addProperty("provider", "vs3-mobile");
                } else {
                    jsonObject.addProperty("threadCount", 1);
                    jsonObject.addProperty("keywordQueueName", "FPR_US_COMMON_PIXEL_MOBILE_V2");
                    jsonObject.addProperty("keywordOutQueueName", "FPR_US_COMMON_PIXEL_MOBILE_V2_OUT");
                    jsonObject.addProperty("provider", "ec2-pixel-mobile");
                }
            }
        } else {
            jsonObject.addProperty("keywordQueueName", "SQS_IN_PIXEL_WEEKLY_MOBILE_TEST");
            jsonObject.addProperty("keywordCrawlQueueName", "SQS_IN_CRAWL_PIXEL_WEEKLY_MOBILE_TEST");
            jsonObject.addProperty("keywordOutQueueNameUS", "SQS_OUT_PIXEL_GOOGLE_MOBILE_US_TEST");
            jsonObject.addProperty("keywordOutQueueNameINTL", "SQS_OUT_PIXEL_GOOGLE_MOBILE_INTL_TEST");
            jsonObject.addProperty("provider", "vs3-mobile");
        }

        return jsonObject.toString();

    }

    private void initParam() {
        isTest = false;
        processType = 1;

        System.out.println("===startParam: isTest:" + isTest + " processType:" + processType + " time:" + getTime());

        int createCount = 0;
        while (true) {
            instanceParamCommon = callerApiService.generateInstanceParam(isTest, processType);
            if (instanceParamCommon != null) {
                break;
            } else {
                createCount++;
                if (createCount > 2) {
                    break;
                }
            }
        }
        if (instanceParamCommon == null) {
            System.out.println("instanceParamCommon is null");
            String subject = "Instance parameter creation failed";
            String content = "Failed to construct parameters for starting the crawler instance.";
            throw new RuntimeException("instanceParamCommon is null");
        }

        desktopQueueUrl = SQSUtils.createQueue(SQS_NAME_D, amazonSQS);
        mobileQueueUrl = SQSUtils.createQueue(SQS_NAME_M, amazonSQS);

        instanceIdFileMap = new HashMap<>();
        Date lastSunday = DateUtils.getLastSunday(new Date());
        int rankDate = Integer.parseInt(FormatUtils.formatDate(lastSunday, "yyyyMMdd"));

        for (int i = 0; i < instanceNameArr.length; i++) {
            String desktopFilePath = PARENT_FILE_PATH + "/" + RankQcInfoEntity.DEVICE_DESKTOP + "_" + i + "_" + rankDate + ".txt";
            instanceIdFileMap.put(RankQcInfoEntity.DEVICE_DESKTOP + i, desktopFilePath);

            String mobileFilePath = PARENT_FILE_PATH + "/" + RankQcInfoEntity.DEVICE_MOBILE + "_" + i + "_" + rankDate + ".txt";
            instanceIdFileMap.put(RankQcInfoEntity.DEVICE_MOBILE + i, mobileFilePath);

            /*String desktopFileOldPath = PARENT_FILE_PATH + "/" + RankQcInfoEntity.DEVICE_DESKTOP + "_" + i + "_old_" + rankDate + ".txt";
            instanceIdFileMap.put(RankQcInfoEntity.DEVICE_DESKTOP + i + "_old", desktopFileOldPath);

            String mobileFileOldPath = PARENT_FILE_PATH + "/" + RankQcInfoEntity.DEVICE_MOBILE + "_" + i + "_old_" + rankDate + ".txt";
            instanceIdFileMap.put(RankQcInfoEntity.DEVICE_MOBILE + i + "_old", mobileFileOldPath);*/
        }
    }

    private int getRunningInstanceNum(String device) {
        int resultNum = 0;
        for (int index = 0; index < instanceNameArr.length; index++) {
            String instanceType = instanceNameArr[index];
            instanceParamCommon.setInstanceType(instanceType);
            int totalCount = callerApiService.getRunningInstanceNum(instanceParamCommon);
            if (totalCount <= 0) {
                log.info("==getRunningInstanceNum processType:{} device:{} totalCount:0", processType, device);
                continue;
            }
            int totalPage = 0;
            if (totalCount % 100 == 0) {
                totalPage = totalCount / 100;
            } else {
                totalPage = totalCount / 100 + 1;
            }
            Set<String> runInstanceIdSet = new HashSet<>();
            for (int i = 1; i <= totalPage; i++) {
                instanceParamCommon.setPage(i);
                List<String> instanceIdList = callerApiService.getInstanceIdList(instanceParamCommon);
                if (instanceIdList != null && !instanceIdList.isEmpty()) {
                    runInstanceIdSet.addAll(instanceIdList);
                }
            }
            String instanceIdFilePath;
            if (processType == 1) {
                instanceIdFilePath = instanceIdFileMap.get(device + index);
            } else {
                instanceIdFilePath = instanceIdFileMap.get(device + index + "_old");
            }
            List<String> currentInstanceIdList = null;
            File file = new File(instanceIdFilePath);
            if (!file.exists()) {
                continue;
            }
            try {
                currentInstanceIdList = FileUtils.readLines(file);
            } catch (IOException e) {
                log.info("===readLinesError filePath:{} processType:{} device:{} error:{}", instanceIdFilePath, processType, device, e.getMessage());
                e.printStackTrace();
            }

            if (currentInstanceIdList == null || currentInstanceIdList.isEmpty()) {
                log.info("==currentInstanceIdList is empty processType:{} device:{} totalCount:{} totalPage:{} filePath{}", processType, device, totalCount, totalPage, instanceIdFilePath);
                continue;
            }
            long count = 0;
            for (String s : currentInstanceIdList) {
                if (runInstanceIdSet.contains(s)) {
                    count++;
                }
            }
//            count = currentInstanceIdList.stream().filter(runInstanceIdSet::contains).count();
            resultNum += (int) count;
            log.info("==getRunningInstanceNum processType:{} device:{} totalCount:{} totalPage:{} currentInstanceNum:{} filePath{}", processType, device, totalCount, totalPage, count, instanceIdFilePath);
            instanceParamCommon.setPage(0);
        }
        return resultNum;
    }

    private void check() {
        for (int i = 0; i < instanceNameArr.length; i++) {
            InstanceParam instanceParam = callerApiService.generateInstanceParam(true, 1);
            if (StringUtils.equalsIgnoreCase(instanceNameArr[i], "ecs.u1-c1m2.large")) {
                instanceParam.setSystemDiskCategory("cloud_auto");
            }
            instanceParam.setInstanceType(instanceNameArr[i]);
            float latestPrice = callerApiService.getLatestPrice(instanceParam);
            int runningInstanceNum = callerApiService.getRunningInstanceNum(instanceParam);
            callerApiService.getAvailableResource(instanceParam);
            currentNum += runningInstanceNum;
        }
        System.out.println("=================" + currentNum + "===============");
    }

    /**
     *
     * @param processType 1: desktop 2: mobile
     * @param startNum
     */
    private void startCrawl(int processType, int startNum) {
        while (true) {
            for (int i = 0; i < instanceNameArr.length; i++) {
                InstanceParam instanceParam = callerApiService.generateInstanceParam(true, 1);
                String currentInstanceType = instanceNameArr[i];
                instanceParam.setInstanceType(currentInstanceType);
                if (StringUtils.equalsIgnoreCase(currentInstanceType, SPECIAL_SYSTEM_INSTANCE_TYPE)) {
                    instanceParam.setSystemDiskCategory(SPECIAL_SYSTEM_DISK_TYPE);
                }  else {
                    instanceParam.setSystemDiskCategory(DEFAULT_DISK_TYPE);
                }
                int runningInstanceNum = callerApiService.getRunningInstanceNum(instanceParam);
                System.out.println(runningInstanceNum);
                currentNum += runningInstanceNum;
                mulutiV3Crawl(processType, instanceNameArr[i], instanceParam);
                System.out.println();
            }
            System.out.println("===currentTotal:" + currentNum);
            String device = processType == 1 ? RankQcInfoEntity.DEVICE_DESKTOP : RankQcInfoEntity.DEVICE_MOBILE;
            if (currentNum >= startNum || checkStop(device)) {
                currentNum = 0;
                break;
            }
            callerApiService.getMsgInQueue(1);
            System.out.println("=========================================="+ currentNum + "==============================================");
            currentNum = 0;
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        System.out.println("===endMobileCrawl");
    }

    private void startCrawl2() {
        System.out.println("====start desktop crawl====");
        InstanceParam instanceParam = callerApiService.generateInstanceParam(false, 1);
        while (true) {
            for (int i = 0; i < instanceNameArr.length; i++) {
                mulutiV3Crawl(1, instanceNameArr[i], instanceParam);
            }
            System.out.println("===currentTotal:" + currentNum);
            if (currentNum >= limitNum || checkStop("d")) {
                currentNum = 0;
                break;
            }
            callerApiService.getMsgInQueue(1);
            System.out.println("=========================================="+ currentNum + "==============================================");
            currentNum = 0;
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private boolean checkStop(String device) {
        boolean stopFlag = false;
        String sqsUrl = StringUtils.equalsIgnoreCase(device, RankQcInfoEntity.DEVICE_DESKTOP) ? desktopQueueUrl : mobileQueueUrl;
        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, sqsUrl);
        Integer messagesAvailable = map.get("MESSAGES_AVAILABLE");
        System.out.println("===kwCountInQueue messagesAvailable: " + messagesAvailable + " inFlight: " + map.get("MESSAGES_IN_FLIGHT") + " device: " + device);
        if (messagesAvailable == 0) {
            stopFlag = true;
        }
        return stopFlag;
    }


    private void mulutiV3Crawl(int startType, String instancename, InstanceParam instanceParam) {
        instanceParam.setInstanceType(instancename);
        float latestPrice = callerApiService.getLatestPrice(instanceParam);
        latestPrice += addPrice;
        instanceParam.setSpotPriceLimit(latestPrice);

        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("spotInstance", true);
        jsonObject.addProperty("sqsAccessKey", AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextSQSAccessKey());
        jsonObject.addProperty("sqsSecretKey", AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey());
        jsonObject.addProperty("s3AccessKey", AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey());
        jsonObject.addProperty("s3SecretKey", AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey());
        if (startType == 1) {
            jsonObject.addProperty("screenshotVersion", String.valueOf(rankDate));
            jsonObject.addProperty("keywordQueueName", SQS_NAME_D);
            jsonObject.addProperty("keywordCrawlQueueName", "SQS_IN_CRAWL_PIXEL_WEEKLY_DESKTOP");
            jsonObject.addProperty("keywordOutQueueNameUS", "SQS_OUT_PIXEL_GOOGLE_DESKTOP_US");
            jsonObject.addProperty("keywordOutQueueNameINTL", "SQS_OUT_PIXEL_GOOGLE_DESKTOP_INTL");
            jsonObject.addProperty("provider", "vs3-desktop");
            instanceParam.setUserData(jsonObject.toString());
            RunInstancesResponse runInstancesResponse1 = callerApiService.batchCreateInstance(instanceParam);
            processRes(runInstancesResponse1);
            System.out.println("runDesktop:" + runInstancesResponse1);
        } else {
            jsonObject.addProperty("screenshotVersion", String.valueOf(rankDate));
            jsonObject.addProperty("keywordQueueName", SQS_NAME_M);
            jsonObject.addProperty("keywordCrawlQueueName", "SQS_IN_CRAWL_PIXEL_WEEKLY_MOBILE");
            jsonObject.addProperty("keywordOutQueueNameUS", "SQS_OUT_PIXEL_GOOGLE_MOBILE_US");
            jsonObject.addProperty("keywordOutQueueNameINTL", "SQS_OUT_PIXEL_GOOGLE_MOBILE_INTL");
            jsonObject.addProperty("provider", "vs3-mobile");
            instanceParam.setUserData(jsonObject.toString());
            RunInstancesResponse runInstancesResponse2 = callerApiService.batchCreateInstance(instanceParam);
            processRes(runInstancesResponse2);
            System.out.println("runMobile:" + runInstancesResponse2);
        }
        int runningInstanceNum = callerApiService.getRunningInstanceNum(instanceParam);
        currentNum += runningInstanceNum;
    }

    private void processRes(RunInstancesResponse response) {
        if (response != null) {
            List<String> instanceIdSets = response.getInstanceIdSets();
            if (instanceIdSets != null && !instanceIdSets.isEmpty()) {
                try {
                    FileUtils.writeLines(new File(filePath), instanceIdSets, true);
                } catch (IOException e) {
                    System.out.println(instanceIdSets);
                    e.printStackTrace();
                }
            }
        }

    }

    private String getTime() {
        return FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    // new stop start
    private void stopInstance(int processType, String instanceNameSuffix) {
        InstanceParam instanceParam = callerApiService.generateInstanceParam(true, processType);
        instanceParam.setPageSize(pageSize);
        instanceParam.setInstanceNameSuffix(instanceNameSuffix);
        DescribeInstancesResponse runningInfo = callerApiService.getRunningInstanceByInstanceName(instanceParam);
        if (runningInfo == null) {
            System.out.println("====nothing instance running====");
            return;
        }
        int totalNum = runningInfo.getTotalCount();
        List<DescribeInstancesResponse.Instance> instances = runningInfo.getInstances();
        List<String> instanceIds = new ArrayList<>();
        if (instances != null && !instances.isEmpty()) {
            for (DescribeInstancesResponse.Instance instance : instances) {
                instanceIds.add(instance.getInstanceId());
            }
        }
        if (totalNum > pageSize) {
            int pageNum = totalNum / pageSize + (totalNum % pageSize == 0 ? 0 : 1);
            for (int i = 2; i <= pageNum; i++) {
                instanceParam.setPage(i);
                DescribeInstancesResponse subRunningInfo = callerApiService.getRunningInstanceByInstanceName(instanceParam);
                if (subRunningInfo != null) {
                    List<DescribeInstancesResponse.Instance> subInstances = subRunningInfo.getInstances();
                    if (subInstances != null && !subInstances.isEmpty()) {
                        for (DescribeInstancesResponse.Instance instance : subInstances) {
                            instanceIds.add(instance.getInstanceId());
                        }
                    }
                }
            }
        }

        if (instanceIds.isEmpty()) {
            System.out.println("===nothing to stop");
            return;
        }
        System.out.println("===instanceIds size:" + instanceIds.size());

        List<DescribeInstanceStatusResponse.InstanceStatus> totalInstanceStatus = new ArrayList<>();
        List<List<String>> splitIdList = CollectionSplitUtils.splitCollectionBySize(instanceIds, 50);
        List<String> deleteInstanceIdList = new ArrayList<>();
        List<String> deleteFailureInstanceIdList = new ArrayList<>();
        List<String> stopFailureInstanceIdList = new ArrayList<>();

        for (List<String> instanceIdList : splitIdList) {
            DescribeInstanceStatusResponse statusResponse = callerApiService.getInstanceStatus(instanceParam, instanceIdList);
            List<DescribeInstanceStatusResponse.InstanceStatus> instanceStatuses = statusResponse.getInstanceStatuses();
            totalInstanceStatus.addAll(instanceStatuses);
        }

        if (!totalInstanceStatus.isEmpty()) {
            Map<String, List<String>> statusPIdListMap = new HashMap<>();
            for (DescribeInstanceStatusResponse.InstanceStatus instanceStatus : totalInstanceStatus) {
                String status = instanceStatus.getStatus();
                String instanceId = instanceStatus.getInstanceId();
                statusPIdListMap.computeIfAbsent(status, k -> new ArrayList<>()).add(instanceId);
            }
            System.out.println("===statusMap:" + statusPIdListMap);
            if (!statusPIdListMap.isEmpty()) {
                for (String status : statusPIdListMap.keySet()) {
                    List<String> instanceIdList = new ArrayList<>(statusPIdListMap.get(status));
                    List<List<String>> lists = CollectionSplitUtils.splitCollectionBySize(instanceIdList, 100);

                    if (StringUtils.equalsIgnoreCase(status, "Stopped")) {
                        handleStoppedInstances(instanceParam, lists, deleteInstanceIdList, deleteFailureInstanceIdList);
                    } else if (!StringUtils.equalsIgnoreCase(status, "Stopping")) {
                        handleRunningInstances(instanceParam, lists, stopFailureInstanceIdList, deleteFailureInstanceIdList, deleteInstanceIdList);
                    } else {
                        handleStoppingInstances(instanceParam, lists, deleteFailureInstanceIdList, deleteInstanceIdList);
                    }
                }
            }
        }

        System.out.println();
    }

    private void handleStoppedInstances(InstanceParam instanceParam, List<List<String>> lists, List<String> deleteInstanceIdList, List<String> deleteFailureInstanceIdList) {
        for (List<String> list : lists) {
            DeleteInstancesResponse deleteInstancesResponse = callerApiService.batchDeleteInstance(instanceParam, list);
            processDeleteResponse(deleteInstancesResponse, list, deleteInstanceIdList, deleteFailureInstanceIdList);
            sleep(5);
        }
    }

    private void handleRunningInstances(InstanceParam instanceParam, List<List<String>> lists, List<String> stopFailureInstanceIdList, List<String> deleteFailureInstanceIdList, List<String> deleteInstanceIdList) {
        for (List<String> list : lists) {
            List<String> tmpList = new ArrayList<>(list);
            List<String> deleteList = new ArrayList<>();
            int stopCnt = 0, currentStopCnt = 0;

            while (true) {
                StopInstancesResponse stopInstancesResponse = callerApiService.batchStopInstances(instanceParam, tmpList);
                stopCnt = processStopResponse(stopInstancesResponse, tmpList, deleteList, stopCnt);

                if (stopCnt == list.size() || tmpList.isEmpty()) break;

                if (++currentStopCnt > 5) {
                    stopFailureInstanceIdList.addAll(tmpList);
                    break;
                }
                sleep(20);
            }

            System.out.println("===stopCnt:" + stopCnt + " tmpList:" + tmpList.size() + " deleteListSize:" + deleteList.size() + " deleteList:" + deleteList);

            sleep(120);
            if (!deleteList.isEmpty()) {
                DeleteInstancesResponse deleteInstancesResponse = callerApiService.batchDeleteInstance(instanceParam, deleteList);
                processDeleteResponse(deleteInstancesResponse, deleteList, deleteInstanceIdList, deleteFailureInstanceIdList);
                sleep(5);
            }
        }
    }

    private void handleStoppingInstances(InstanceParam instanceParam, List<List<String>> lists, List<String> deleteFailureInstanceIdList, List<String> deleteInstanceIdList) {
        sleep(60);
        for (List<String> list : lists) {
            DeleteInstancesResponse deleteInstancesResponse = callerApiService.batchDeleteInstance(instanceParam, list);
            processDeleteResponse(deleteInstancesResponse, list, deleteInstanceIdList, deleteFailureInstanceIdList);
            sleep(5);
        }
    }

    private void processDeleteResponse(DeleteInstancesResponse response, List<String> list, List<String> successList, List<String> failureList) {
        if (response != null && StringUtils.isNotBlank(response.getRequestId())) {
            successList.addAll(list);
        } else {
            failureList.addAll(list);
        }
    }

    private int processStopResponse(StopInstancesResponse response, List<String> tmpList, List<String> deleteList, int stopCnt) {
        if (response != null && !response.getInstanceResponses().isEmpty()) {
            for (StopInstancesResponse.InstanceResponse instanceResponse : response.getInstanceResponses()) {
                if (StringUtils.equalsIgnoreCase(instanceResponse.getCode(), "200")) {
                    tmpList.remove(instanceResponse.getInstanceId());
                    deleteList.add(instanceResponse.getInstanceId());
                    stopCnt++;
                }
            }
        }
        return stopCnt;
    }

    private void sleep(int seconds) {
        try {
            TimeUnit.SECONDS.sleep(seconds);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    // new stop end

    private void getRunningInstanceNum(int processType, String instanceNameSuffix) {
        InstanceParam instanceParam = callerApiService.generateInstanceParam(true, processType);
        instanceParam.setPageSize(pageSize);
        instanceParam.setInstanceNameSuffix(instanceNameSuffix);
        DescribeInstancesResponse runningInfo = callerApiService.getRunningInstanceByInstanceName(instanceParam);
        System.out.println("===runningNum " + instanceNameSuffix.replace("*", "") + ":" + runningInfo.getTotalCount());
    }

    private void getInstancePrice(int processType, Integer index) {
        InstanceParam instanceParam = callerApiService.generateInstanceParam(true, processType);
        instanceParam.setPageSize(pageSize);
        if (index != null) {
            instanceParam.setInstanceType(instanceNameArr[index]);
            float latestPrice = callerApiService.getLatestPrice(instanceParam);
            System.out.println("===instancePriceInfo instance:" + instanceParam.getInstanceType() + " latestPrice:" + latestPrice);
        } else {
            for (int i = 0; i < instanceNameArr.length; i++) {
                String instanceName = instanceNameArr[i];
                instanceParam.setInstanceType(instanceName);

                if (StringUtils.equalsIgnoreCase(instanceName, SPECIAL_SYSTEM_INSTANCE_TYPE)) {
                    instanceParam.setSystemDiskCategory(SPECIAL_SYSTEM_DISK_TYPE);
                }  else {
                    instanceParam.setSystemDiskCategory(DEFAULT_DISK_TYPE);
                }

                float latestPrice = callerApiService.getLatestPrice(instanceParam);
                System.out.println("===instancePriceInfo index:" + i +  " instance:" + instanceParam.getInstanceType() + " latestPrice:" + latestPrice);
            }
        }
    }


    public void getMsgInQueue(int processType) {
        AmazonSQS amazonSQS = SQSUtils.getAmazonSQS();
        if (processType == 1) {
            desktopQueueUrl = SQSUtils.createQueue(SQS_NAME_D, amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue(SQS_NAME_M, amazonSQS);
        } else {
            desktopQueueUrl = SQSUtils.createQueue("FPR_US_COMMON_PIXEL_V2", amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue("FPR_US_COMMON_PIXEL_MOBILE_V2", amazonSQS);
        }

        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, desktopQueueUrl);
        int inFlight = map.get("MESSAGES_IN_FLIGHT");
        int kwCnt = map.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueDesktop kwCnt:" + kwCnt + " inFlight:" + inFlight);
        Map<String, Integer> mapM = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, mobileQueueUrl);
        int inFlightM = mapM.get("MESSAGES_IN_FLIGHT");
        int kwCntM = mapM.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueMobile kwCnt:" + kwCntM + " inFlight:" + inFlightM);
    }
}
