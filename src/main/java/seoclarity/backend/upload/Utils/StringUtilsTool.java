package seoclarity.backend.upload.Utils;

import org.apache.commons.lang.StringUtils;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.upload.Utils
 * @author: cil
 * @date: 2021-07-09 13:44
 **/
public class StringUtilsTool {


    public static String replaceSingleQuote(String decodeKeywordName) {
        if (StringUtils.contains(decodeKeywordName, "'")) {
            try {
                String tmpKW = decodeKeywordName;
                StringBuffer sb = new StringBuffer();
                int pos = 0;
                int startPos = 0;
                while (true) {
                    if (pos > 0) {
                        pos = tmpKW.indexOf("'", pos + 1);
                    } else {
                        pos = tmpKW.indexOf("'");
                    }
                    if (pos >= 0) {
                        if (pos == 0) {
                            sb.append("\\'");
                            pos = pos + 1;
                            startPos = pos;
                        } else if ("\\".equals(tmpKW.substring(pos - 1, pos))) {
                            sb.append(tmpKW, startPos, pos);
                            startPos = pos;
                        } else {
                            sb.append(tmpKW, startPos, pos).append("\\'");
                            startPos = pos + 1;
                        }
                    }
                    if (pos < 0 || pos >= tmpKW.length() - 1) {
                        sb.append(tmpKW, startPos, tmpKW.length());
                        break;
                    }
                }
                System.out.println("replaceSingleQuote:" + decodeKeywordName + "->" + sb.toString());
                return sb.toString();
            } catch (Exception exp) {
                exp.printStackTrace();
                return StringUtils.replace(decodeKeywordName, "'", "\\'");
            }
        }
        return decodeKeywordName;
    }
}
