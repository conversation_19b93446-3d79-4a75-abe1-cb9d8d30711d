package seoclarity.backend.upload;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.securitytoken.AWSSecurityTokenServiceClient;
import com.amazonaws.services.securitytoken.model.AssumeRoleRequest;
import com.amazonaws.services.securitytoken.model.AssumeRoleResult;
import com.google.gson.Gson;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import ru.yandex.clickhouse.ClickHouseArray;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.youtube.YoutubeSummaryDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.ExtractTestYoutubeDataFromClarityDB" -Dexec.args="2023-10-07"
 *
 */
public class ExtractTestYoutubeDataFromClarityDB {
    private static final String LOCAL_OUTPUT_FOLDER = "/home/<USER>/";
    private static final String KEY_DELIMITER = "/";

    private static final String YOUTUBE_S3_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
    private static final String YOUTUBE_S3_SECRET_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
    private static final String YOUTUBE_S3_ROLE_ARN ="arn:aws:iam::054715827583:role/opticon_seoclarity_export_s3_role";
    private static final String YOUTUBE_S3_BUCKET_NAME = "opticon-seoclarity-export-bucket-production-us-east-1";
    private static final int S3_SESSION_DURATION_SECONDS = 3600;
    private static final int S3_RETRY_COUNT = 10;

    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

//    private static final String DEVICE_DESKTOP = "d";
//    private static final String DEVICE_MOBILE = "m";
//    private static final String DEVICE_EXPORT_STR_DESKTOP = "desktop";
//    private static final String DEVICE_EXPORT_STR_MOBILE = "mobile";
//    private static final String BAD_CHAR = "�";


//    public static final Integer PROTOCOL_HTTP = 0;
//    public static final Integer PROTOCOL_HTTPS = 1;


    private YoutubeSummaryDao youtubeSummaryDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;

    private static final String S3_BUCKET_KEY_PREFIX = "top_keywords";
    private Gson gson = new Gson();


    private static Set<String> processedFileName = new HashSet<>();

    public ExtractTestYoutubeDataFromClarityDB() {
        youtubeSummaryDao = SpringBeanFactory.getBean("youtubeSummaryDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");

    }

//    private static String[] headers = new String[] {
//            "Keyword_Name","Country","Language","Date","Search_Volume","CPC","Rank","Title","Published_Time","Type","Verified","Video_Length",
//            "Subscriber_Count","Description_Snippet","Author","Video_Tag","View_Count","Video_Count","Id","browseId","AD_Text","AD_Url","Canonical_Base_Url","Canonical_Base_Url_Type","Canonical_Base_Url_Nname"
//    };

    CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("Keyword_Name","Country","Language","Date","Search_Volume","CPC","Rank","Title","Published_Time","Type","Verified","Video_Length",
            "Subscriber_Count","Description_Snippet","Author","Video_Tag","View_Count","Video_Count","Id","browseId","AD_Text","AD_Url","Canonical_Base_Url","Canonical_Base_Url_Type","Canonical_Base_Url_Nname").withDelimiter('\t');

    public static void main(String[] args){

        System.out.println("start process " + new Gson().toJson(args));
        ExtractTestYoutubeDataFromClarityDB extractYoutubeDate = new ExtractTestYoutubeDataFromClarityDB();
        if (args != null && args.length == 1) {
            String[] dateArr = args[0].split(",");
            for (String dateStr : dateArr) {
                try {
                    extractYoutubeDate.process(DateUtil.parse(dateStr, "yyyy-MM-dd"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else if (args == null || args.length == 0) {
            try {
                extractYoutubeDate.process(DateUtil.offsetDay(new Date(), -1));
            } catch (Exception e){
                e.printStackTrace();
            }
        }

//        if (processedFileName != null && processedFileName.size() > 0) {
//            extractYoutubeDate.saveFilesToDestination(processedFileName);
//        }

    }


    public void process(DateTime dateTime) throws IOException {
        CSVPrinter printer = null;
        try {

            String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
            String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

            logglyVO.setoId("RVYoutube");
            logglyVO.setName("ExtractYoutubeDataFromClarityDB");
            logglyVO.setDevice("RVYoutube");

            logglyVO.setpDate(pDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_CUSTOMIZED_EXTRACT);
            logglyVO.setGroups(groupList);

//            processedFileName = new HashSet<>();

//            String localPath = LOCAL_OUTPUT_FOLDER+"YoutubeRankTags.csv";
//            File f = new File(localPath);
//            List<String> stringList = FileUtil.readLines(f,"utf-8");
//            List<String> keywordList = new ArrayList<>();
            Map<String,String> keywordMap = new HashMap<>();
//            for (String str : stringList) {
//                String[] arr  = str.split(",");
//                if(arr.length <= 1) {
//                    continue;
//                }
////                keywordList.add(arr[0]);
//                keywordMap.put(arr[0],arr[1]);
//            }


            System.out.println("======ProcessDate:" + dateTime);
            String processDateStr = dateTime.toString("yyyy-MM-dd");
            String dateStr = DateUtil.format(dateTime, "MM-dd-yyyy");
            String localOutputFilename = LOCAL_OUTPUT_FOLDER + File.separator + "youtube_rankings"+ File.separator + "youtube-" + dateStr + ".csv";


            //domain
            List<Integer> domainList = new ArrayList<>();
//            List<OwnDomainEntity> domainEntityList = ownDomainEntityDAO.getDomainListBasedCompanyName("Red Ventures LLC");
//            for (OwnDomainEntity ownDomainEntity : domainEntityList) {
//                domainList.add(ownDomainEntity.getId());
//            }

            domainList.add(4);

            List<Map<String,Object>> csvList = youtubeSummaryDao.getYoutubeIndoAsc(processDateStr, domainList);

            List<String[]> list = new ArrayList<>();
            totalCnt = csvList.size();
            for (Map<String, Object> map : csvList) {
                String videoTag = "[]";
                try {
                    ClickHouseArray clickHouseArray = (ClickHouseArray) map.get("Video_Tag");
                    videoTag = JSONUtil.toJsonStr(clickHouseArray.getArray());
                } catch (Exception e) {
                    e.printStackTrace();
                }

//                String tag = "";
//                if(keywordMap.containsKey(map.get("Keyword_Name").toString())){
//                    tag = keywordMap.get(map.get("Keyword_Name").toString());
//                }
                String[] arr = new String[] {
                        getMapString(map.get("Keyword_Name"), ""),
                        getMapString(map.get("Country"), ""),
                        getMapString(map.get("Language"), ""),
                        DateUtil.format((Date) map.get("Date"),"yyyy-MM-dd"),
                        getMapString(map.get("Search_Volume"), ""),
                        getMapString(map.get("CPC"), ""),
                        getMapString(map.get("Rank"), ""),
                        getMapString(map.get("Title"), ""),
                        getMapString(map.get("Published_Time"), ""),
                        getMapString(map.get("Type"), ""),
                        getMapString(map.get("Verified"), ""),
                        getMapString(map.get("Video_Length"), ""),
                        getMapString(map.get("Subscriber_Count"), ""),
                        getMapString(map.get("Description_Snippet"), ""),
                        getMapString(map.get("Author"), ""),
                        videoTag,
                        getMapString(map.get("View_Count"), ""),
                        getMapString(map.get("Video_Count"), ""),
                        getMapString(map.get("Id"), ""),
                        getMapString(map.get("browseId"), ""),
                        getMapString(map.get("AD_Text"), ""),
                        getMapString(map.get("AD_Url"), ""),
                        getMapString(map.get("Canonical_Base_Url"), ""),
                        getMapString(map.get("Canonical_Base_Url_Type"), ""),
                        getMapString(map.get("Canonical_Base_Url_Nname"), "")
                };
                list.add(arr);
            }


            printer = new CSVPrinter(new FileWriter(localOutputFilename),csvFormat);
            printer.printRecords(list);

            printer.flush();
            printer.close();

            processedFileName.add(localOutputFilename);

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(stTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

        } catch (Exception e) {
            e.printStackTrace();
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        }finally {
            if (printer != null) {
                printer.close();
            }
        }

    }

    private static String getMapString(Object value, String defaultValue) {
        if(value == null) {
            return defaultValue;
        }
        return value.toString();
    }

    private void saveFilesToDestination(Set<String> localFileSet) {
        boolean savedFilesToS3 = true;
        try {
//            List<String> fileList = new ArrayList<String>(localFileSet);
            for (String localOutputFilename : localFileSet) {
                System.out.println(" ==localOutputFilename:" + localOutputFilename);
                File outFile = new File(localOutputFilename);
//                File zipFile = new File(outFile.getAbsolutePath() + ".gz");
//                GZipUtil.zip(outFile.getAbsolutePath(), zipFile.getAbsolutePath());
//                ZipUtil.zip(outFile.getAbsolutePath(), zipFile.getAbsolutePath());
//                System.out.println("======OutGzipFile:" + outFile.getAbsolutePath());
                // FTPUtils.saveFileToFTP(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, 0 , zipFile.getAbsolutePath(), FTP_FOLDER);
                String s3key = "youtube_rankings/"+outFile.getName();
                System.out.println("s3key："+s3key);
                boolean savedToS3 = saveFileToS3ByKey(outFile.getAbsolutePath(), s3key);
                if (savedToS3 == false) {
                    savedFilesToS3 = false;
                }
//                if (savedToS3) {
//                    System.out.println(" ======DeleteFileAfterSentOK:" + localOutputFilename);
//                    outFile.delete();
//                }
            }
        } catch (Exception e) {
            e.printStackTrace();
//            sendEmailReport(new Date(), "ExtractYoutubeData Error", "ExtractYoutubeData error occurred", e.getMessage());
        }

        try {
            if (savedFilesToS3) {
//                sendEmailReport(new Date(), "ExtractYoutubeData Success", "ExtractYoutubeData sent file to S3 ", null);
            }
        } catch (Exception exp) {
            exp.printStackTrace();
        }
    }

    public static boolean saveFileToS3ByKey(String fullPathFileName, String fileName) {
        System.out.println(" ==Save file to s3 Key:" + fileName + " file:" + fullPathFileName);
        return sendFileToS3WithRoleArn(YOUTUBE_S3_ACCESS_KEY, YOUTUBE_S3_SECRET_KEY, Regions.US_EAST_1, YOUTUBE_S3_ROLE_ARN, YOUTUBE_S3_BUCKET_NAME, fileName,
                fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
    }

    public static boolean sendFileToS3WithRoleArn(String accessKey, String secretKey, Regions awsRegion, String roleArn, String bucketName, String s3Key,
                                                  String fullPathFileName, int durationSeconds, int retryCount) {
        int triedCount = 0;
        for (int i = 0; i < retryCount; i++) {
            try {
                AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
                AWSSecurityTokenServiceClient stsClient = new AWSSecurityTokenServiceClient(credentials);
                AssumeRoleRequest assumeRequest = new AssumeRoleRequest()
                        .withRoleArn(roleArn)
                        .withDurationSeconds(durationSeconds)
                        .withRoleSessionName("seoclarity");
                AssumeRoleResult assumeResult = stsClient.assumeRole(assumeRequest);
                BasicSessionCredentials sessionCredentials = new BasicSessionCredentials(
                        assumeResult.getCredentials().getAccessKeyId(),
                        assumeResult.getCredentials().getSecretAccessKey(),
                        assumeResult.getCredentials().getSessionToken());
                AmazonS3 s3client = AmazonS3ClientBuilder.standard().withRegion(awsRegion).withCredentials(new AWSStaticCredentialsProvider(sessionCredentials)).build();

                s3client.putObject(new PutObjectRequest(bucketName, s3Key, new File(fullPathFileName)));
                System.out.println(" ==SavedFileToS3 bucketName:" + bucketName + " key:" + s3Key + " file:" + fullPathFileName);
                return true;
            } catch (Exception e) {
                triedCount++;
                System.out.println(" #SaveFileToS3Failure,retryCnt:" + triedCount);
                try {
                    Thread.sleep(2 * 60 * 1000);
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
                continue;
            }
        }
        return false;
    }

}
