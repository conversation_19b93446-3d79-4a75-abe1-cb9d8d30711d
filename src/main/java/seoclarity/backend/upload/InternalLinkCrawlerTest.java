package seoclarity.backend.upload;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.LongDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;

import com.google.gson.Gson;

import seoclarity.backend.utils.FormatUtils;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.InternalLinkCrawlerTest" -Dexec.args=""
public class InternalLinkCrawlerTest {
	//scott - 12 Scott change consumer to use clarity-page-links-v6
	private final static String TOPIC = "clarity-page-links-v7";
	private final static String BOOTSTRAP_SERVERS = "173.236.58.194:9092,173.236.41.250:9092";
	
	public static String crawlingFolderPath = "/home/<USER>/internalLink/crawling";
    public static String needUploadFolderPath = "/home/<USER>/internalLink/needUpload";
    public static String uploadingFolder = "/home/<USER>/internalLink/uploading/" + FormatUtils.formatDate(new Date(), "yyyyMMddHH") + "/";    
    public static String backUpFolder = "/home/<USER>/internalLink/backUpFolder";
    public static String errorFolder = "/home/<USER>/internalLink/error";
    public static String duplicateFolder = "/home/<USER>/internalLink/duplicate";
    
	
	private static Gson gson = new Gson();
	
	public static final char FILE_SPLIT = '\t';
	public static final String ENCODING = "UTF-8";
//	public static final String[] DB_COLUMNS = new String[]{
//		"link_nofollow", "domain_id_i", "analyzed_url_s", "source_domain", "canonical_type",
//		"canonical_flg", "anchor_text", "destination_domain", "destination_root_domain", "crawl_date_long",
//		"source_url", "page_robots_meta_index", "page_robots_meta_follow", "page_robots_meta_archive", "destination_url",
//		"title_md5", "popularity", "title", "canonical", "source_root_domain",
//		"analyzed_url_flg_s", "title_flg", "crawl_request_log_id_i", "canonical_string", "destination_folder_level_1",
//		"destination_folder_level_2", "source_folder_level_1", "source_folder_level_2", "today", "source_url_response_code",
//		"sourceurl_anchortext_hash", "destinationurl_anchortext_hash"
//	};
	
	public static void main(String... args) throws Exception {
		
		runConsumer();
	}

	private static Consumer<Long, String> createConsumer() {
		final Properties props = new Properties();
		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
		props.put(ConsumerConfig.GROUP_ID_CONFIG, "KafkaTestV4Consumer");
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, LongDeserializer.class.getName());
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());

		props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
		props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "True");
		
		// Create the consumer using props.
		final Consumer<Long, String> consumer = new KafkaConsumer<>(props);

		// Subscribe to the topic.
		consumer.subscribe(Collections.singletonList(TOPIC));
		return consumer;
	}

	static void runConsumer() throws InterruptedException {
		final Consumer<Long, String> consumer = createConsumer();

		
		File crawlingFolder = new File(crawlingFolderPath);
		if (crawlingFolder == null || !crawlingFolder.exists() || !crawlingFolder.isDirectory()) {
			
			System.out.println("folder crawlering is not exist, creating now!! ");
			crawlingFolder.mkdirs();
		}
		
		File needUploadFolder = new File(needUploadFolderPath);
		if (needUploadFolder == null || !needUploadFolder.exists() || !needUploadFolder.isDirectory()) {
			
			System.out.println("folder needUpload is not exist, creating now!! ");
			needUploadFolder.mkdirs();
		}
		
		
		try {
			int recordCnt = 0;
			
			String prefix = "internal_link_" + FormatUtils.formatDateToYyyyMmDd(new Date()) + "_";
			File tempfile = File.createTempFile(prefix, ".processing", crawlingFolder);
			System.out.println("Create file:" + tempfile.getAbsolutePath());
			String fileName = tempfile.getAbsolutePath();
			
			// add header
			List<String> crawlIdList = new ArrayList<>();
//			lines.add(StringUtils.join(DB_COLUMNS, FILE_SPLIT));
//			FileUtils.writeLines(tempfile, ENCODING, lines, true);
			
			System.out.println(" ======= file:" + fileName);
			List<String> entityList;
			
			while (true) {
				final ConsumerRecords<Long, String> consumerRecords = consumer.poll(1000);
				
				if (consumerRecords.count() == 0) {
					
					try {
						//sleep 10 second if no incoming
						Thread.sleep(10000);
						continue;
					} catch (Exception e) {
						e.printStackTrace();
					}

				}
				
				entityList = new ArrayList<String>();
				
				for(ConsumerRecord<Long, String> record : consumerRecords) {
//					System.out.printf("Consumer Record:(%d, %s, %d, %d)\n", record.key(), record.value(),
//							record.partition(), record.offset());
					
					
					
					entityList.add(record.value());
					
				}
				
				try {
					if (entityList.size() > 0) {
						
						recordCnt = recordCnt + entityList.size();
						
						for(String content : entityList) {
							Map<String, Object> resultMap = gson.fromJson(content, Map.class);
							
							String crawlRequestLogId = StringUtils.replace(String.valueOf(resultMap.get("crawl_request_log_id_i")), ".0", "");
							System.out.println("cur id:" + crawlRequestLogId);
							if (!crawlIdList.contains(crawlRequestLogId)) {
								crawlIdList.add(crawlRequestLogId);
								List<String> requestLogIdList = new ArrayList<>();
								requestLogIdList.add(crawlRequestLogId);
								output(tempfile, requestLogIdList);
							}
						}
						System.out.println("adding records count:" + entityList.size() + ", total records ： " + recordCnt);
					}
					
				} catch (Exception e) {
					e.printStackTrace();
				}

				
//				if (recordCnt >= 30000) {
//					
//					System.out.println("@@@ import crawlIdList:" + gson.toJson(crawlIdList));
//					crawlIdList = new ArrayList<>();
//					
//					//rename file from .processing to .txt and transfor to needUpload folder
//					moveFileFromCrawlingToNeedUpload(tempfile);
//					
//					//create a new temp file
//					tempfile = File.createTempFile(prefix, ".processing", crawlingFolder);
//					System.out.println(" ======= store in new file:" + tempfile.getAbsolutePath());
//					recordCnt = 0;
//					
//				}

				consumer.commitAsync();
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		
//		consumer.close();
//		System.out.println("DONE");
	}
	
	private static void output(File file, List<String> outLines) throws Exception{
		
		FileUtils.writeLines(file, "utf-8", outLines, true);
	}
	
	
	public static void moveFileFromCrawlingToNeedUpload(File tempfile) {
		try {
			if (tempfile != null && tempfile.exists() && tempfile.isFile()) {
				
				String targetFileName = StringUtils.replace(tempfile.getName(), ".processing", ".txt");
				
				System.out.println("targetFileName:" + targetFileName);
				
					
				File doneFolder = new File(needUploadFolderPath);
				if (doneFolder == null || !doneFolder.isDirectory()) {
					System.out.println("Target folder is not exist!!! folder:" + needUploadFolderPath);
					doneFolder.mkdirs();
				}
				
				File targetFile = new File(needUploadFolderPath + "/" + targetFileName);
				
				FileUtils.moveFile(tempfile, targetFile);
				
				System.out.println("Rename file from " + doneFolder.getAbsolutePath() + " to " + targetFile.getAbsolutePath());
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
  
//	public static void output(File file, InternalLinkEntity internalLinkEntity) throws Exception{
//		
//		//TODO parse from entity to line
//		String[] line = new String[DB_COLUMNS.length];
//		
//		line[0] = String.valueOf(internalLinkEntity.isLink_nofollow());
//		line[1] = String.valueOf(internalLinkEntity.getDomain_id_i());
//		line[2] = String.valueOf(internalLinkEntity.getAnalyzed_url_s());
//		line[3] = String.valueOf(internalLinkEntity.getSource_domain());
//		line[4] = String.valueOf(internalLinkEntity.getCanonical_type());
//		line[5] = String.valueOf(internalLinkEntity.isCanonical_flg());
//		line[6] = String.valueOf(internalLinkEntity.getAnchor_text());
//		line[7] = String.valueOf(internalLinkEntity.getDestination_domain());
//		line[8] = String.valueOf(internalLinkEntity.getDestination_root_domain());
//		line[9] = String.valueOf(internalLinkEntity.getCrawl_date_long());
//		line[10] = String.valueOf(internalLinkEntity.getSource_url());
//		line[11] = String.valueOf(internalLinkEntity.getPage_robots_meta_index());
//		line[12] = String.valueOf(internalLinkEntity.getPage_robots_meta_follow());
//		line[13] = String.valueOf(internalLinkEntity.getPage_robots_meta_archive());
//		line[14] = String.valueOf(internalLinkEntity.getDestination_url());
//		line[15] = String.valueOf(internalLinkEntity.getTitle_md5());
//		line[16] = String.valueOf(internalLinkEntity.getPopularity());
//		line[17] = String.valueOf(internalLinkEntity.getTitle());
//		line[18] = String.valueOf(internalLinkEntity.getCanonical());
//		line[19] = String.valueOf(internalLinkEntity.getSource_root_domain());
//		line[20] = String.valueOf(internalLinkEntity.isAnalyzed_url_flg_s());
//		line[21] = String.valueOf(internalLinkEntity.isTitle_flg());
//		line[22] = String.valueOf(internalLinkEntity.getCrawl_request_log_id_i());
//		line[23] = String.valueOf(internalLinkEntity.getCanonical_string());
//		line[24] = String.valueOf(internalLinkEntity.getDestination_folder_level_1());
//		line[25] = String.valueOf(internalLinkEntity.getDestination_folder_level_2());
//		line[26] = String.valueOf(internalLinkEntity.getSource_folder_level_1());
//		line[27] = String.valueOf(internalLinkEntity.getSource_folder_level_2());
//		line[28] = String.valueOf(FormatUtils.formatDate(new Date(), "yyyy-MM-dd"));
//		line[29] = String.valueOf(internalLinkEntity.getSource_url_response_code());
//		line[30] = String.valueOf(internalLinkEntity.getSourceurl_anchortext_hash());
//		line[31] = String.valueOf(internalLinkEntity.getDestinationurl_anchortext_hash());
//		
//		FileUtils.write(file, "utf-8", StringUtils.join(line, FILE_SPLIT), true);
//	}
}
