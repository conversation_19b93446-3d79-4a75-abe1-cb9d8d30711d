package seoclarity.backend.upload.onetime;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.hadoop.fs.Path;
import org.apache.parquet.example.data.Group;
import org.apache.parquet.hadoop.ParquetReader;
import org.apache.parquet.hadoop.example.GroupReadSupport;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.KeywordStreamDAO;
import seoclarity.backend.entity.actoniamonitor.UploadFileMonitorEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickSteamEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.service.ScKeywordSearchVolumeManager;
import seoclarity.backend.upload.GSCClickSteamUpload;
import seoclarity.backend.upload.MonthlyKeywordTokenizerUploadV2;
import seoclarity.backend.upload.WeeklyKeywordStreamUpload;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.io.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@CommonsLog
public class DisKeywordStreamUpload {

    public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
    private static final int LOAD_COUNT = 50000;

    private ScKeywordSearchVolumeManager scKeywordSearchVolumeManager;

    public DisKeywordStreamUpload(){
        scKeywordSearchVolumeManager = SpringBeanFactory.getBean("scKeywordSearchVolumeManager");
    }

    private void loadParquetFile(String filePath) throws Exception {
        log.info("================filePath:" + filePath);
        threadPool.init();

        File file = new File(filePath);
        InputStreamReader inputReader = null;
        BufferedReader bf = null;
        try {

            inputReader = new InputStreamReader(new FileInputStream(file));
            bf = new BufferedReader(inputReader);

            List<String> groupList = new ArrayList<>();
            String line;
            int linNo = 0;
            while ((line = bf.readLine()) != null) {

                linNo++;
                groupList.add(line);

                if (groupList != null && groupList.size() >= LOAD_COUNT) {

                    System.out.println("==== uploading ===== size :" + groupList.size() + ",linNo:" + linNo);
                    try {
                        processUpload(groupList);
                    } catch (Exception e) {
                        throw e;
                    }

                    groupList = new ArrayList<>();
                }

            }
            if (!CollectionUtils.isEmpty(groupList)) {
                try {
                    processUpload(groupList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            do {
                try {
                    Thread.sleep(100);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } while (threadPool.getThreadPool().getActiveCount() > 0);
            threadPool.destroy();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            bf.close();
            inputReader.close();
        }

    }

    private void processUpload(List<String> groupList) throws Exception{
        String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
        while (ipAddress == null) {
            try {
                Thread.sleep(100);
                ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
            } catch (Exception e) {
                throw e;
            }
        }
        DisKeywordStreamUpload.Command cmd = new DisKeywordStreamUpload.Command(ipAddress, groupList);
        cmd.setStatus(true);
        try {
            threadPool.execute(cmd);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    class Command extends BaseThreadCommand {
        private String ipAddress;
        private List<String> groupList;
        private KeywordStreamDAO keywordStreamDAO;

        public Command(String ipAddress, List<String> groupList) {
            this.ipAddress = ipAddress;
            this.groupList = groupList;
            keywordStreamDAO = SpringBeanFactory.getBean("keywordStreamDAO");
        }

        @Override
        protected void execute() throws Exception {
            long a = System.currentTimeMillis();

            List<KeywordStreamEntity> processList = new ArrayList<>();
            log.info("====execute gscClickSteamList:" + groupList.size());
            for (String line : groupList) {
                try {
                    String[] arr = line.split("\t");
                    String engine = arr[0];
                    String language = arr[1];
                    String country = arr[2];
                    String keywordName = arr[3];
                    String kwName = ClickStreamUtils.formatForAnalyzer(keywordName);

                    if(StringUtils.isBlank(engine)){
                        log.error("==111:" + line);
                        continue;
                    }
                    if(StringUtils.isBlank(language)){
                        log.error("==222:" + line);
                        continue;
                    }
                    if(StringUtils.isBlank(country)){
                        log.error("==333:" + line);
                        continue;
                    }
                    if(StringUtils.isBlank(keywordName)){
                        log.error("==444:" + line);
                        continue;
                    }
                    if(StringUtils.isBlank(kwName)){
                        log.error("==555:" + line);
                        continue;
                    }

                    String[] params = ClickStreamUtils.COUNTRY_MAP.get(country);
                    if(params == null || params.length == 0){
                        log.error("==666:" + line);
                        continue;
                    }
                    String code = params[0];
                    String languageName = params[1];

                    KeywordStreamEntity keywordStreamEntity = new KeywordStreamEntity();
                    keywordStreamEntity.setKeywordName(kwName);
                    keywordStreamEntity.setCountryCd(country);
                    keywordStreamEntity.setEngineId(Integer.parseInt(engine));
                    keywordStreamEntity.setLanguageId(Integer.parseInt(language));

                    ////分词
                    List<String> word = new ArrayList<String>();
                    List<String> stream = new ArrayList<String>();

                    List<String> keywordVariationOneword = new ArrayList<String>();
                    List<String> keywordVariationNgram = new ArrayList<String>();

                    if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
                        word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, "ar"));
                    } else {
                        word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, code));
                    }
                    stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(kwName, languageName));
                    keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(kwName));
                    keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(kwName, false));

                    keywordStreamEntity.setWord(word);
                    keywordStreamEntity.setStream(stream);

                    if (stream.size() == 0) {
                        stream.add(kwName);
                    }

                    keywordStreamEntity.setKeywordVariationOneword(keywordVariationOneword);
                    keywordStreamEntity.setKeywordVariationNgram(MonthlyKeywordTokenizerUploadV2.setPlaceHolder(keywordVariationNgram));

                    processList.add(keywordStreamEntity);
                    if (processList.size() >= 50000) {
                        log.info("====insert insetList:" + processList.size());
                        keywordStreamDAO.insertStreamForBatch(processList);
                        processList = new ArrayList<>();
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (CollectionUtils.isNotEmpty(processList)) {
                log.info("====insert2 insetList:" + processList.size());
                keywordStreamDAO.insertStreamForBatch(processList);
            }

            long b = System.currentTimeMillis();
            System.out.println("ipAddress" + ipAddress + ",End command Cost time: " + (b - a) * 1.0 / 1000 + "s");
            CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
        }

        @Override
        protected void undo() throws Exception {

        }

    }

    public static void main(String args[]) throws Exception {

        CommonUtils.initThreads(5);
        if(args == null || args.length < 1){
            log.info("===param error,exit!");
            return;
        }

        DisKeywordStreamUpload disKeywordStreamUpload = new DisKeywordStreamUpload();
        String path = args[0];
        File folder = new File(path);
        if (folder.isDirectory()) {
            for (File file1 : folder.listFiles()) {
                disKeywordStreamUpload.loadParquetFile(file1.getAbsolutePath());
            }
        } else {
            disKeywordStreamUpload.loadParquetFile(path);
        }

    }


}
