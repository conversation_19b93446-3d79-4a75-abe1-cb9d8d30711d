package seoclarity.backend.upload;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.yarn.webapp.hamlet.Hamlet;
import seoclarity.backend.dao.actonia.SeoSysLogEntityDAO;
import seoclarity.backend.dao.clickhouse.fortest.LocalKeywordSummaryAnnualBigDAO;
import seoclarity.backend.dao.clickhouse.fortest.LocalKeywordSummaryAnnualNormalDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.actonia.ContentJsonPOJO;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.upload.Utils.WriteUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.upload
 * @author: cil
 * @date: 2021-07-08 10:33
 **/
public class RGKeywordExpansionV2 {

    private SeoSysLogEntityDAO seoSysLogEntityDAO;
    //        private KeywordSummaryAnnualBigDAO keywordSummaryAnnualBigDAO;
//    private KeywordSummaryAnnualNormalDAO keywordSummaryAnnualNormalDAO;
    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
    private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;


    //new datasource local test
    private LocalKeywordSummaryAnnualBigDAO keywordSummaryAnnualBigDAO;
    private LocalKeywordSummaryAnnualNormalDAO keywordSummaryAnnualNormalDAO;


    private static final String ENCODE_FILE_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/uk_jp_rgExpandForSV_0720.txt";
    private static final String DECODE_FILE_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/uk_jp_rgExpand_0720.txt";
    private static final String SEARCH_AND_COUNTRY_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/uk_jp_search_and_country_0720.txt";
    private static final String SURVIVOR_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/uk_jp_survivor_0720.txt";

    private static final String SPILT = "!_!";

    private static Map<String, String[]> COUNTRY_AND_LANGUAGE = new HashMap<>(16);
    private static Map<String, Object> BIG_MAP = new HashMap<>(8);
    private static Map<String, String> LOCATION_MAP = new HashMap<>(16);

    public RGKeywordExpansionV2() {
        seoSysLogEntityDAO = SpringBeanFactory.getBean("seoSysLogEntityDAO");
//        keywordSummaryAnnualBigDAO = SpringBeanFactory.getBean("keywordSummaryAnnualBigDAO");
//        keywordSummaryAnnualNormalDAO = SpringBeanFactory.getBean("keywordSummaryAnnualNormalDAO");
        //for test
        keywordSummaryAnnualBigDAO = SpringBeanFactory.getBean("localKeywordSummaryAnnualBigDAO");
        keywordSummaryAnnualNormalDAO = SpringBeanFactory.getBean("localKeywordSummaryAnnualNormalDAO");

        seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
        //value : country_cd	languageName	engineId	languageId
        COUNTRY_AND_LANGUAGE.put("ae", new String[]{"are", "English", "45", "46"});
        COUNTRY_AND_LANGUAGE.put("au", new String[]{"aus", "English", "2", "5"});
        COUNTRY_AND_LANGUAGE.put("ca", new String[]{"can", "English", "3", "3"});
        COUNTRY_AND_LANGUAGE.put("de", new String[]{"deu", "German", "14", "15"});
        COUNTRY_AND_LANGUAGE.put("fr", new String[]{"fra", "French", "4", "7"});
        COUNTRY_AND_LANGUAGE.put("in", new String[]{"ind", "English", "24", "25"});
        COUNTRY_AND_LANGUAGE.put("it", new String[]{"ita", "Italian", "8", "9"});
        COUNTRY_AND_LANGUAGE.put("jp", new String[]{"jpn", "Japanese", "18", "19"});
        COUNTRY_AND_LANGUAGE.put("nl", new String[]{"nld", "Dutch", "17", "18"});
        COUNTRY_AND_LANGUAGE.put("ro", new String[]{"rou", "English", "71", "72"});
        COUNTRY_AND_LANGUAGE.put("uk", new String[]{"gbr", "English", "6", "8"});
        COUNTRY_AND_LANGUAGE.put("us", new String[]{"usa", "English", "1", "1"});
        COUNTRY_AND_LANGUAGE.put("at", new String[]{"aut", "English", "29", "30"});
        COUNTRY_AND_LANGUAGE.put("ie", new String[]{"irl", "German", "20", "21"});

        Object o = new Object();
        BIG_MAP.put("gbr", o);
        BIG_MAP.put("bra", o);
        BIG_MAP.put("deu", o);
        BIG_MAP.put("usa", o);
        BIG_MAP.put("can", o);
        BIG_MAP.put("ind", o);

        LOCATION_MAP.put("AE", "2784");
        LOCATION_MAP.put("AT", "2040");
        LOCATION_MAP.put("AU", "2036");
        LOCATION_MAP.put("CA", "2124");
        LOCATION_MAP.put("DE", "2276");
        LOCATION_MAP.put("FR", "2250");
        LOCATION_MAP.put("UK", "2826");
        LOCATION_MAP.put("IE", "2372");
        LOCATION_MAP.put("IN", "2356");
        LOCATION_MAP.put("IT", "2380");
        LOCATION_MAP.put("JP", "2392");
        LOCATION_MAP.put("NL", "2528");
        LOCATION_MAP.put("RO", "2642");
        LOCATION_MAP.put("US", "2840");

    }

    public static void main(String[] args) {
        System.out.println("startTime :" + new Date());
        RGKeywordExpansionV2 ins = new RGKeywordExpansionV2();

        ins.process();
    }


    private void process() {
        //get all content
        List<String> jsonStrList = getAllContent();
        //parse to jsonPOJO
        List<ContentJsonPOJO> jsonPOJOList = stringToJsonPOJO(jsonStrList);

        System.out.println("countSize =====" + jsonPOJOList.size());
        int num = 0;

        Collections.sort(jsonPOJOList, Comparator.comparingInt(o -> {
            if ("us".equals(o.getCountry())) {
                return Integer.MIN_VALUE;
            }
            return o.getCountry().hashCode();
        }));

        doSomething(jsonPOJOList, num);

    }

    public void doSomething(List<ContentJsonPOJO> jsonPOJOList, int num) {
        Map<String, Set<String>> rawKwMap = new HashMap();
        Set<String> usSet = new HashSet<>();
        for (ContentJsonPOJO pojo :
                jsonPOJOList) {
            String country = pojo.getCountry();
            if ("us".equals(country)) {
                usSet.add(pojo.getSearch());
            } else {
                if (rawKwMap.containsKey(country)) {
                    rawKwMap.get(country).add(pojo.getSearch());
                } else {
                    HashSet<String> searchSet = new HashSet<String>();
                    searchSet.add(pojo.getSearch());
                    rawKwMap.put(country, searchSet);
                }
            }
        }
        System.out.println("rawSearchCnt:" + jsonPOJOList.size() + " CountryCnt:" + rawKwMap.size());

        doByCountry(usSet, "us", num);

        for (Map.Entry<String, Set<String>> entry :
                rawKwMap.entrySet()) {

            Set<String> searchSet = entry.getValue();
            String country = entry.getKey();

            doByCountry(searchSet, country, num);
        }

    }

    private void doByCountry(Set<String> searchSet, String country, int num) {
        Map<String, Set<Map<String, String>>> chKWMap = new HashMap();
        //value : country_cd	languageName	engineId	languageId
        String[] countryAndLanguage = COUNTRY_AND_LANGUAGE.get(country);
        if (countryAndLanguage == null)
            return;
        String countryCode = countryAndLanguage[0];
        Integer engineId = Integer.valueOf(countryAndLanguage[2]);
        Integer languageId = Integer.valueOf(countryAndLanguage[3]);
        Integer locationId = Integer.valueOf(LOCATION_MAP.get(country.toUpperCase()));
        System.out.println("======proccesingCountry:" + country
                + " SE:" + engineId + "/" + languageId + "/" + locationId
                + " searchSetCnt:" + searchSet.size());

        for (String searchText :
                searchSet) {
            List<String> wordList = getWord(searchText, countryAndLanguage[1]);
            if (wordList == null || wordList.size() < 1) continue;
            if (wordList.size() == 1) {
                if (wordList.get(0).length() == 1)
                    continue;
            }

            boolean flag = true;
            for (String word : wordList) {
                if (!isNumeric(word)) {
                    flag = false;
                    break;
                }

            }
            if (flag) continue;


            //write file for survivor
            WriteUtils.write(SURVIVOR_PATH, countryCode, searchText, StringUtils.join(wordList, ","));

            if (wordList.size() > 9) {
                System.out.println("skipLongSearchTextKW:" + searchText);
                continue;
            } else {
                WriteUtils.write(SEARCH_AND_COUNTRY_PATH, country, searchText);
                List<String> strings = SnowBallAndNgramForForeignLanguages.wordTokenizer(searchText, "usa");
                String join = StringUtils.join(strings, SPILT);

                String chKeywordName =searchText;
                Map<String, String> entity = new HashMap();
                entity.put("chKeywordName", chKeywordName);

                entity.put("searchText", searchText);
                System.out.println("searchText:" + searchText + "=>" + chKeywordName);
                if (chKWMap.containsKey(join)) {
                    chKWMap.get(join).add(entity);
                } else {
                    HashSet<Map<String, String>> groupStreamSet = new HashSet<>();
                    groupStreamSet.add(entity);
                    chKWMap.put(join, groupStreamSet);
                }

                    System.out.println("searchText:" + searchText + "=>" + searchText + " join:" + join
                            + " groupStreamSet:" + chKWMap.get(join) == null ? 0 : chKWMap.get(join).size());
                System.out.println("Idx:" + num++ + " processingSearchTextKW:" + searchText + " countStream :" + chKWMap.size());

//                List<KeywordStreamEntity> streamEntityList = null;
//                try {
//                    streamEntityList = getKeywordStream(countryCode, wordList);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//
//                System.out.println("Idx:" + num++ + " processingSearchTextKW:" + searchText + " countStream :" + streamEntityList.size());
//
//
//                for (KeywordStreamEntity streamEntity : streamEntityList) {
//                    String chKeywordName = streamEntity.getKeywordName();
//                    Map<String, String> entity = new HashMap();
//                    entity.put("chKeywordName", chKeywordName);
//                    entity.put("avgSearchVolume", String.valueOf(streamEntity.getAvgSearchVolume()));
//                    entity.put("dayOfCount", String.valueOf(streamEntity.getWeekIndex()));
//                    entity.put("searchText", searchText);
//                    System.out.println("searchText:" + searchText + "=>" + chKeywordName
//                            + " SV:" + streamEntity.getAvgSearchVolume()
//                            + " COD:" + streamEntity.getWeekIndex());
//
//                    List<String> strings = SnowBallAndNgramForForeignLanguages.wordTokenizer(chKeywordName, streamEntity.getCountryCd());
//                    streamEntity.setWord(strings);
//                    Collections.sort(strings);
//                    String join = StringUtils.join(strings, SPILT);
//
//                    if (chKWMap.containsKey(join)) {
//                        chKWMap.get(join).add(entity);
//                    } else {
//                        HashSet<Map<String, String>> groupStreamSet = new HashSet<>();
//                        groupStreamSet.add(entity);
//                        chKWMap.put(join, groupStreamSet);
//                    }
//                    try {
//                        System.out.println("searchText:" + searchText + "=>" + chKeywordName + " join:" + join
//                                + " groupStreamSet:" + chKWMap.get(join) == null ? 0 : chKWMap.get(join).size());
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }


            }
        }

        for (Map.Entry<String, Set<Map<String, String>>> chKWMapEntry :
                chKWMap.entrySet()) {
            String key = chKWMapEntry.getKey();
            Set<Map<String, String>> groupSet = chKWMapEntry.getValue();

            List<Map<String, String>> rankChackKeywordList = new LinkedList();
            for (Map<String, String> entity :
                    groupSet) {
                try {
                    entity.put("chKeywordName", CommonDataService.encodeQueueBaseKeyword(entity.get("chKeywordName")));
                    rankChackKeywordList.add(entity);

                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
            List<String> rankChackKeywords = new LinkedList<>();
            for (Map<String, String> map :
                    rankChackKeywordList) {
                rankChackKeywords.add(map.get("chKeywordName"));
            }


            List<String> rgKWList = getRepeatKeywordId(engineId,
                    languageId, rankChackKeywords);


            if (rgKWList.size() > 0) {
                System.out.println("processingJoinKey:" + key + " country:" + country + " se:" + engineId + "/" + languageId
                        + " groupSet:" + groupSet.size()
                        + " decodeList:" + rankChackKeywordList.size() + " rgKWListSize:" + rgKWList.size() + " skipKW");
                for (Map<String, String> rekw :
                        groupSet) {
                    System.out.print("  kw:\"" + rekw.get("chKeywordName") + "\"");
                }
                System.out.println();
            } else {

                //groupSet De-duplication
                ArrayList<Map<String, String>> arr = groupSet.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(
                                        () -> new TreeSet<>(Comparator.comparing(m -> m.get("chKeywordName")))
                                ), ArrayList::new
                        )
                );


                Map<String, String> candidateKW = null;
                for (Map<String, String> groupStr :
                        arr) {
                    candidateKW = groupStr;
                    break;
                }
                System.out.println("processingJoinKey:" + key + " country:" + country + " se:" + engineId + "/" + languageId
                        + " groupSet:" + groupSet.size()
                        + " decodeList:" + rankChackKeywordList.size() + /*" rgKWListSize:" + rgKWList.size() +*/ " candidateKW:" + candidateKW);
                for (Map<String, String> rekw :
                        arr) {
                    System.out.print("  kw:\"" + rekw.get("chKeywordName") + "\"");
                }
                System.out.println();
                writeEncode(ENCODE_FILE_PATH, candidateKW.get("chKeywordName"), engineId, languageId, locationId);
                String s = null;
                try {
                    s = StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(candidateKW.get("chKeywordName"), "UTF-8")));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                //String filePath, String text, String country, String search,String avgSearchVolume,String countofdays
                writeDecode(DECODE_FILE_PATH, candidateKW.get("searchText"), country, s, candidateKW.get("avgSearchVolume"), candidateKW.get("dayOfCount"));
            }
        }
//        }
    }


    private List<String> getRepeatKeywordId(int engine, int language, List<String> keywordTextList) {
        return seoClarityKeywordEntityDAO.getMonthlyRelationsKeywordIdList(engine, language, keywordTextList);
    }


    private List<SeoClarityKeywordEntity> getKeywordIdByName(List<String> keywordNameList) {
        return seoClarityKeywordEntityDAO.getJoinKeywordEntityListByNames(keywordNameList);
    }

    private List<String> getEncodeList(List<KeywordStreamEntity> entities) {
        List<String> encodeList = new LinkedList<>();
        for (KeywordStreamEntity keywordStreamEntity : entities) {
            try {
                keywordStreamEntity.setKeywordHash(CommonDataService.encodeQueueBaseKeyword(keywordStreamEntity.getKeywordName()));
                encodeList.add(keywordStreamEntity.getKeywordHash());
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return encodeList;

    }

    private List<ContentJsonPOJO> stringToJsonPOJO(List<String> jsonStrList) {
        Set<ContentJsonPOJO> jsonPOJOset = new HashSet<>();
        for (String jsonStr : jsonStrList) {
            Gson gson = new Gson();
            JsonElement element = gson.fromJson(jsonStr, JsonElement.class);
            JsonObject jsonObj = element.getAsJsonObject();
            ContentJsonPOJO contentJsonPOJO = new ContentJsonPOJO();

            if (jsonObj.get("country") != null && jsonObj.get("search") != null) {

                contentJsonPOJO.setCountry(jsonObj.get("country").getAsString());
                contentJsonPOJO.setSearch(jsonObj.get("search").getAsString());
                jsonPOJOset.add(contentJsonPOJO);
            }
        }

        List<ContentJsonPOJO> jsonPOJOList = new LinkedList<>(jsonPOJOset);

        Collections.sort(jsonPOJOList);
        return jsonPOJOList;
    }


    private List<String> getWord(String searchText, String languageName) {
        List<String> word = new ArrayList<>();
//        if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
//            word.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(entity.getSearch(), "ar"));
//        } else {
        word.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(searchText.toLowerCase(), languageName, true));
//        }
        return word;
    }


    private List<String> getAllContent() {
        return seoSysLogEntityDAO.getAllContent();
    }

    private List<KeywordStreamEntity> getKeywordStream(String countryCode, List<String> wordList) throws InterruptedException {
        try {

            if (BIG_MAP.get(countryCode) != null)
                return keywordSummaryAnnualBigDAO.getKeywordNameAndAvgSearchVolumeByPrarm(countryCode, wordList);
            else
                return keywordSummaryAnnualNormalDAO.getKeywordNameAndAvgSearchVolumeByPrarm(countryCode, wordList);

        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(30000);
            return getKeywordStream(countryCode, wordList);
        }
    }

    /**
     * //keyword,engineId,languageId,locationId
     *
     * @param filePath
     * @param
     * @param
     */
    public static void writeEncode(String filePath, String candidateKW, Integer engineId, Integer languageId, Integer locationId) {

        try {
            File file = new File(filePath);
            FileOutputStream fos = null;
            if (!file.exists()) {
                file.createNewFile();
                fos = new FileOutputStream(file);
            } else {

                fos = new FileOutputStream(file, true);
            }

            OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);

            //keyword,engineId,languageId,locationId
            osw.write(candidateKW + "," + engineId + "," + languageId + "," + locationId);


            osw.write("\r\n");


            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("^-?[0-9]+");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    public static void writeDecode(String filePath, String text, String country, String search, String avgSearchVolume, String weekIndex) {

        try {
            File file = new File(filePath);
            FileOutputStream fos = null;
            if (!file.exists()) {
                file.createNewFile();
                fos = new FileOutputStream(file);
            } else {

                fos = new FileOutputStream(file, true);
            }

            OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);

            osw.write(search + "\t" + country + "\t" + text/* + "\t" + avgSearchVolume + "\t" + weekIndex*/);
            osw.write("\r\n");
            osw.close();
        } catch (
                Exception e) {
            e.printStackTrace();
        }
    }
}
