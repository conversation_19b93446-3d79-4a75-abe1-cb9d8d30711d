package seoclarity.backend.upload.script;

import lombok.Data;

import java.io.*;
import java.sql.Array;
import java.util.*;
import java.util.stream.Collectors;


public class RemoveDupFile {

//    private static String localFilelist = "/home/<USER>/source/hao/master/clarity-backend-scripts/dooutfile2/missFile.txt";
//    private static final String notinb2File = "/home/<USER>/source/hao/master/clarity-backend-scripts/dooutfile2/reloadMissFile.txt";
    private static String localFilelist = "i:\\missFile.txt";
    private static final String notinb2File = "i:\\reloadMissFile.txt";
    public static void main(String[] args) {

        List<String> lineList = new ArrayList<>();
        Map<String,String> mp = new HashMap<>();
        try {
            File file = new File(localFilelist);
            System.out.println(" 开始 ");
            System.out.println(file.getName());
            BufferedReader bf = new BufferedReader(new FileReader(file));
            String line = null;
            while ((line = bf.readLine()) != null) {
                if (!"".equals(line)) {
                    lineList.add(line);
                }
            }
            bf.readLine();
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (String str : lineList){
            if (str.contains(",")){
                String[] key = str.split(",");
                String name = key[1];
                mp.put(name,str);
            }
        }


        System.out.println(" lineset : " + mp.size());
        List<String> endlist = new ArrayList<>();
        for (String s : mp.keySet()){
            endlist.add(mp.get(s));
        }
        System.out.println(" endlist : " + endlist.size());
        RemoveDupFile removeDupFile = new RemoveDupFile();
        removeDupFile.process(endlist);


    }

    private void process(List<String> lineset) {
        List<FileEntity> fileEntityList = new ArrayList<>();
        for (String fir : lineset) {
            String bucketName = fir.split(",")[0];
            String key = fir.split(",")[1];
            String kwName = key.substring(key.lastIndexOf("/") + 1) + ".zip";
            String[] folder;
            if (key.contains("/")) {
                folder = key.split("/");
            } else {
                folder = key.split("\\\\");
            }
            String date = folder[0].toString();
            String[] enginelanguage = folder[1].split("-");
            String engine;
            String language;
            String device;
            System.out.println("fir ：" + fir);
            if (folder[1].contains("mobile")) {
                device = "1";
                engine = enginelanguage[1];
                language = enginelanguage[2];
            } else {
                device = "2";
                engine = enginelanguage[0];
                language = enginelanguage[1];
            }
            System.out.println(" engine :" + engine +
                    "language : " + language +
                    "date : " + date);
            FileEntity entity = new FileEntity();
            entity.setDate(Integer.parseInt(date));
            entity.setEngine(Integer.parseInt(engine));
            entity.setLanguage(Integer.parseInt(language));
            entity.setBody(fir);
            entity.setDevice(Integer.parseInt(device));
            fileEntityList.add(entity);
        }

        System.out.println(" fileEntityList : " + fileEntityList.size());
        List<FileEntity> li = fileEntityList.stream().sorted(
                Comparator.comparing(FileEntity::getDevice).
                thenComparing(FileEntity::getDate)
                .thenComparing(FileEntity::getEngine)
                .thenComparing(FileEntity::getLanguage)).collect(Collectors.toList());
        try {
            BufferedWriter notinb2 = new BufferedWriter(new FileWriter(notinb2File, true));
            System.out.println(" 文件去重完成 ： " + li.size());
            for (FileEntity entity : li) {
                System.out.println(" entity.getBody() : " + entity.getBody());
                writeToTxt(notinb2, entity.getBody());
            }
            notinb2.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }

    @Data
    public class FileEntity {
        private int date;
        private int engine;
        private int language;
        private String body;
        private Integer device;

        public Integer getDevice() {
            return device;
        }

        public void setDevice(Integer device) {
            this.device = device;
        }

        public int getDate() {
            return date;
        }

        public void setDate(int date) {
            this.date = date;
        }

        public int getEngine() {
            return engine;
        }

        public void setEngine(int engine) {
            this.engine = engine;
        }

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getBody() {
            return body;
        }

        public void setBody(String body) {
            this.body = body;
        }
    }
}
