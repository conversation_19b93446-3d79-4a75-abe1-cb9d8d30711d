package seoclarity.backend.upload.script;

import cn.hutool.core.text.csv.CsvWriteConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.utils.*;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=1269345112
 * --hao
 * weekly job every Friday
 *s15  /home/<USER>/source/adhocExtract/clarity-backend-scripts/DashboardDownload_11235.sh
 *
 * 0 20 1 * * 5 /home/<USER>/source/adhocExtract/clarity-backend-scripts/DashboardDownload_11235.sh >>
 * /home/<USER>/source/adhocExtract/clarity-backend-scripts/log/DashboardDownload_11235_`date '+\%Y\%m\%d'`.log 2>&1
 *
 */
public class DashboardDownload_11235 {

    private static Integer domainId = 11235;
    private static final String FILE_NAME_TEMPLE_TABLE = "Non-Brand GSC Summary - Wtd Avg Position_%DATE_INDEX%.csv";
    private static final String FILE_NAME_TEMPLE_TREND = "Non-Brand GSC Trend - Wtd Avg Position_%DATE_INDEX%.csv";
    private static final String LOCAL_OUT_FILE_PATH = "/home/<USER>/11235/dashBoardDownload/";
    private static final String FTP_PATH = "/home/<USER>/11235/dashBoardDownload";

    private static final String API_URL_KEYWORD_TABLE = "http://**************:8183/seoClarity/gsc/keywordTable";
    private static final String API_URL_METRICS_TREND = "http://**************:8183/seoClarity/gsc/metricsTrend";
    private static final String API_URL_APP = "http://**************:8182/seoClarity/dailyrankingv2/getKeywordTagTrendTable";

    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public DashboardDownload_11235() {
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }
    CSVFormat csvFullFormatKeywordTable = CSVFormat.DEFAULT.withHeader(
                    "Keyword",
                    "Wtd Average Position"
            )
            .withDelimiter(',');

    CSVFormat csvFullFormatTrend = CSVFormat.DEFAULT.withHeader(
                    "Date",
                    "Wtd Average Position"
            )
            .withDelimiter(',');

    public static void main(String[] args) throws ParseException {
        System.out.println("===========================  gogogo ========================================");
        DashboardDownload_11235 ins = new DashboardDownload_11235();
        try {
            ins.process();
        } catch (Exception e) {
            String message = "Dashboard Download for Oid : 11235 Failed ! Please check !";
            ins.sendMailReport( "Error", message);
            e.printStackTrace();
        }
    }

    private void process() {
        // download keywordtable 
        keywordTableDown();

        //download trend 
        trendDown();
        String message = "Dashboard Download for Oid : 11235 Success ! Please check file from FTP: /home/<USER>/11235/dashBoardDownload/ ";
        sendMailReport( "Success", message);
    }

    private void keywordTableDown() {
        // check folder
        File folder = new File(LOCAL_OUT_FILE_PATH);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        String today = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        String fileName = createDaysForFiles(today, LOCAL_OUT_FILE_PATH, FILE_NAME_TEMPLE_TABLE);
        System.out.println(" ===###filename keyword : : " + fileName);

        String response = getKeywordTableRequest();
        try {
            downloadKeywordTableToFtp(response, fileName);

            System.out.println("====###删除本地文件 ================================");
            deleteFiles(fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void downloadKeywordTableToFtp(String response, String fileName) throws Exception {
        List<Map<String, Object>> dateRowMap = new ArrayList<>();

        String cleanjson = clearJson(response);
//        String ecodejson = StringEscapeUtils.unescapeJavaScript(cleanjson);

        JSONObject jsonResponse = JSONObject.parseObject(cleanjson);

        if (StringUtils.isNotBlank(jsonResponse.get("data").toString())) {
            JSONArray responseArray = jsonResponse.getJSONArray("data");
            parseLinesKw(responseArray, dateRowMap);
        }
        System.out.println("===###KeywordFileCount : " + dateRowMap.size());
        if (dateRowMap.size() > 0) {
            exportKWTableToFile(dateRowMap, fileName);
            FTPUtils.saveFileToFTP(domainId, fileName, FTP_PATH);
        }
    }

    private String getKeywordTableRequest() {
        String requestString = "{\"accessToken\":\"c09yxv13-opr3-d745-9734-8pu48420nj67\",\"managedUrlOnly\":false,\"ignoreFreshDataFlag\":false,\"managedKeywordOnly\":false,\"unmanagedKeywordOnly\":false,\"typeFilters\":[{\"id\":160670,\"name\":\"Nonbrand\",\"filterType\":\"KwTextType\",\"type\":null,\"filter\":{\"action\":\"npt\",\"value\":\"\",\"leaf\":false,\"items\":[{\"action\":\"npt\",\"value\":\"matalan|matlan|mataln|matalam|maralan|matakan|materlan|matslan|mayalan\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1},{\"action\":\"npt\",\"value\":\"mata|meta|merta|matta|msta|mtal|msts\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1},{\"action\":\"npt\",\"value\":\"atalan|atelan|artalan|alatan\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1},{\"action\":\"npt\",\"value\":\"athalan|atslsn|atterlan|aalan\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1},{\"action\":\"npt\",\"value\":\"antalan|atlaan|atql|athlan|attlan|arakan|aysl|atln\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1}],\"cond\":\"and\",\"caseSensitive\":false,\"level\":0},\"filtersVO\":null}],\"dwm\":\"day\",\"kwTextTypeIds\":[\"160670\"],\"urlTextTypeIds\":[],\"leftNaviFilters\":[],\"paginationQueryVO\":{\"page\":1,\"pageSize\":500000,\"sortBy\":\"wtdPositionAvg\",\"sortByDesc\":true,\"sort\":[[\"wtdPositionAvg\",\"desc\"]],\"sortValue\":\" wtdPositionAvg desc\",\"startIndex\":0,\"endIndex\":500000},\"weeklyDates\":[\"2023-11-25\",\"2023-11-26\",\"2023-11-27\",\"2023-11-28\",\"2023-11-29\",\"2023-11-30\",\"2023-12-01\",\"2023-12-02\",\"2023-12-03\",\"2023-12-04\",\"2023-12-05\",\"2023-12-06\",\"2023-12-07\",\"2023-12-08\",\"2023-12-09\",\"2023-12-10\",\"2023-12-11\",\"2023-12-12\",\"2023-12-13\",\"2023-12-14\",\"2023-12-15\",\"2023-12-16\",\"2023-12-17\",\"2023-12-18\",\"2023-12-19\",\"2023-12-20\",\"2023-12-21\",\"2023-12-22\",\"2023-12-23\",\"2023-12-24\"],\"compFlg\":false,\"downLoadAll\":false,\"separateGeoType\":[1,3],\"includeSitemaps\":false,\"excludeSitemaps\":false,\"top5types\":false,\"useHierarchy\":false,\"useTagTrace\":false,\"forceRefresh\":false,\"enableParentChildRel\":true,\"sampledSummary\":true,\"apiInternalDownload\":false,\"enableUrlDecode\":false,\"customSearchVol\":false,\"metricsList\":[\"wtdPositionAvg\"],\"ownDomainId\":11235,\"acrossDomainIds\":[11235],\"relIds\":[10268],\"startDate1\":\"2023-11-25\",\"endDate1\":\"2023-12-24\",\"dataType\":1,\"disableSearchAppearance\":false,\"onlyManagedKeyword\":false,\"onlyManagedURL\":false,\"lastAvailableDate\":\"2023-12-23\",\"bigQueryFlg\":false,\"needBigQueryTableFilter\":false,\"complexPatternMap\":{\"ctr\":{\"sqlPattern\":\"ROUND(if(%s = 0, 0, %s / %s), 4)\",\"columnNames\":[\"sum(impressions)\",\"sum(clicks)\",\"sum(impressions)\"]},\"ctrAvg\":{\"sqlPattern\":\"ROUND(if(%s = 0, 0, %s / %s), 4)\",\"columnNames\":[\"sum(impressions)\",\"sum(clicks)\",\"sum(impressions)\"]},\"wtdPositionAvg\":{\"sqlPattern\":\"ROUND(%s / %s, 2)\",\"columnNames\":[\"sum(position * impressions)\",\"impressions\"]},\"positionAvg\":{\"sqlPattern\":\"ROUND(%s, 2)\",\"columnNames\":[\"avg(position)\"]},\"position\":{\"sqlPattern\":\"ROUND(%s, 2)\",\"columnNames\":[\"avg(position)\"]}},\"enableParentChildUrlWithDataType\":true}";
        JSONObject requestJson = JSON.parseObject(requestString);

        Calendar cal1 = Calendar.getInstance();
        cal1.add(Calendar.DAY_OF_MONTH, -1);
        String date1 = new SimpleDateFormat("yyyy-MM-dd").format(cal1.getTime());

        Calendar cal2 = Calendar.getInstance();
        cal2.add(Calendar.DAY_OF_MONTH, -31);
        String date2 = new SimpleDateFormat("yyyy-MM-dd").format(cal2.getTime());
        System.out.println("startDate1 : " + date1 + ", endDate1 " + date2);

        List<String> allDate = DateUtils.getAllDateBetweenDate1Date2(date2, date1, "yyyy-MM-dd");
        requestJson.remove("weeklyDates");
        requestJson.put("weeklyDates", allDate);
        requestJson.remove("startDate1");
        requestJson.put("startDate1", date2);
        requestJson.remove("endDate1");
        requestJson.put("endDate1", date1);
        requestJson.remove("lastAvailableDate");
        requestJson.put("lastAvailableDate", date1);
        String response = post(requestJson, API_URL_KEYWORD_TABLE);
        return response;
    }

    private String getTrendRequest() {
        String requestString = "{\"accessToken\":\"c09yxv13-opr3-d745-9734-8pu48420nj67\",\"managedUrlOnly\":false,\"ignoreFreshDataFlag\":false,\"managedKeywordOnly\":false,\"unmanagedKeywordOnly\":false,\"typeFilters\":[{\"id\":160670,\"name\":\"Nonbrand\",\"filterType\":\"KwTextType\",\"type\":null,\"filter\":{\"action\":\"npt\",\"value\":\"\",\"leaf\":false,\"items\":[{\"action\":\"npt\",\"value\":\"matalan|matlan|mataln|matalam|maralan|matakan|materlan|matslan|mayalan\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1},{\"action\":\"npt\",\"value\":\"mata|meta|merta|matta|msta|mtal|msts\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1},{\"action\":\"npt\",\"value\":\"atalan|atelan|artalan|alatan\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1},{\"action\":\"npt\",\"value\":\"athalan|atslsn|atterlan|aalan\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1},{\"action\":\"npt\",\"value\":\"antalan|atlaan|atql|athlan|attlan|arakan|aysl|atln\",\"leaf\":true,\"items\":[],\"cond\":\"and\",\"caseSensitive\":false,\"level\":1}],\"cond\":\"and\",\"caseSensitive\":false,\"level\":0},\"filtersVO\":null}],\"dwm\":\"day\",\"kwTextTypeIds\":[\"160670\"],\"urlTextTypeIds\":[],\"leftNaviFilters\":[],\"weeklyDates\":[\"2023-12-14\",\"2023-12-15\",\"2023-12-16\",\"2023-12-17\",\"2023-12-18\",\"2023-12-19\",\"2023-12-20\"],\"compFlg\":false,\"downLoadAll\":false,\"separateGeoType\":[1,3],\"includeSitemaps\":false,\"excludeSitemaps\":false,\"top5types\":false,\"useHierarchy\":false,\"useTagTrace\":false,\"forceRefresh\":false,\"enableParentChildRel\":true,\"sampledSummary\":true,\"apiInternalDownload\":false,\"enableUrlDecode\":false,\"customSearchVol\":false,\"metricsList\":[\"wtdPositionAvg\"],\"ownDomainId\":11235,\"acrossDomainIds\":[11235],\"relIds\":[10268],\"startDate1\":\"2023-12-14\",\"endDate1\":\"2023-12-20\",\"dataType\":1,\"disableSearchAppearance\":false,\"onlyManagedKeyword\":false,\"onlyManagedURL\":false,\"lastAvailableDate\":\"2023-12-23\",\"bigQueryFlg\":false,\"needBigQueryTableFilter\":false,\"complexPatternMap\":{\"ctr\":{\"sqlPattern\":\"ROUND(if(%s = 0, 0, %s / %s), 4)\",\"columnNames\":[\"sum(impressions)\",\"sum(clicks)\",\"sum(impressions)\"]},\"ctrAvg\":{\"sqlPattern\":\"ROUND(if(%s = 0, 0, %s / %s), 4)\",\"columnNames\":[\"sum(impressions)\",\"sum(clicks)\",\"sum(impressions)\"]},\"wtdPositionAvg\":{\"sqlPattern\":\"ROUND(%s / %s, 2)\",\"columnNames\":[\"sum(position * impressions)\",\"impressions\"]},\"positionAvg\":{\"sqlPattern\":\"ROUND(%s, 2)\",\"columnNames\":[\"avg(position)\"]},\"position\":{\"sqlPattern\":\"ROUND(%s, 2)\",\"columnNames\":[\"avg(position)\"]}},\"enableParentChildUrlWithDataType\":true}";
        JSONObject requestJson = JSON.parseObject(requestString);

        Calendar cal1 = Calendar.getInstance();
        cal1.add(Calendar.DAY_OF_MONTH, -1);
        String date1 = new SimpleDateFormat("yyyy-MM-dd").format(cal1.getTime());

        Calendar cal2 = Calendar.getInstance();
        cal2.add(Calendar.DAY_OF_MONTH, -31);
        String date2 = new SimpleDateFormat("yyyy-MM-dd").format(cal2.getTime());
        System.out.println("startDate1 : " + date1 + ", endDate1 " + date2);

        List<String> allDate = DateUtils.getAllDateBetweenDate1Date2(date2, date1, "yyyy-MM-dd");
        requestJson.remove("weeklyDates");
        requestJson.put("weeklyDates", allDate);
        requestJson.remove("startDate1");
        requestJson.put("startDate1", date2);
        requestJson.remove("endDate1");
        requestJson.put("endDate1", date1);
        requestJson.remove("lastAvailableDate");
        requestJson.put("lastAvailableDate", date1);
        String response = post(requestJson, API_URL_METRICS_TREND);
        return response;
    }

    private String post(JSONObject jsonParam, String apiUrl) {
        Map<String, String> expandHeader = new HashMap<>();
//            expandHeader.put("org.restlet.http.headers", INTERNAL_TYK_HEADER);
        int tryCnt = 0;
        do {
            tryCnt++;
            try {
                String response = HttpRequestUtils.queryWebServiceFunctionPostWithJsonParams(apiUrl, jsonParam.toString(), null);
                if (StringUtils.isNotBlank(response) && !StringUtils.contains(response, "502 Bad Gateway")) {
                    return response;
                } else if (tryCnt <= 5) {
                    Thread.sleep(3000);
                    System.out.println("=resposne is empty, will re-try, tryCnt:" + tryCnt);
                }
            } catch (Exception e) {
                System.out.println("=api query faild, will re-try, tryCnt:" + tryCnt + ", error:" + e.getMessage());
                try {
                    Thread.sleep(3000);
                } catch (Exception e1) {
                }
            }
        } while (tryCnt <= 5);
        return null;
    }

    private void trendDown() {
        // check folder
        File folder = new File(LOCAL_OUT_FILE_PATH);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        String today = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        String fileName = createDaysForFiles(today, LOCAL_OUT_FILE_PATH, FILE_NAME_TEMPLE_TREND);
        System.out.println(" ===###filename Trend : " + fileName);
        String response = getTrendRequest();
        try {
            downloadTrendToFtp(response, fileName);
            System.out.println("====###删除本地文件 ================================");
            deleteFiles(fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    private void downloadTrendToFtp(String response, String fileName) throws Exception {
        List<Map<String, Object>> dateRowMap = new ArrayList<>();

        String cleanjson = clearJson(response);
        String ecodejson = StringEscapeUtils.unescapeJavaScript(cleanjson);
        JSONObject jsonResponse = JSONObject.parseObject(ecodejson);
        if (StringUtils.isNotBlank(jsonResponse.get("data").toString())) {
            JSONArray responseArray = jsonResponse.getJSONArray("data");
            parseLinesTrend(responseArray, dateRowMap);
        }
        System.out.println("===###TrendFileCount : " + dateRowMap.size());
        if (dateRowMap.size() > 0) {
            exportTrendToFile(dateRowMap, fileName);
            FTPUtils.saveFileToFTP(domainId, fileName, FTP_PATH);
        }
    }


    private List<String> exportKWTableToFile(List<Map<String, Object>> dateRowMap, String fileName) {
        List<String> exportInfoList = new ArrayList<>();
        CsvWriteConfig config = new CsvWriteConfig();
        config.setAlwaysDelimitText(true);
        try {
            CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(fileName)), csvFullFormatKeywordTable);
            for (Map<String, Object> objectMap : dateRowMap) {
//                DecimalFormat df = new DecimalFormat("0.00%");
                csvPrinter.printRecord(
                        objectMap.get("Keyword"),
                        objectMap.get("wtdPositionAvg")
//                        df.format(Double.parseDouble(objectMap.get("wtdPositionAvg").toString()))
                );
            }
            csvPrinter.flush();
            csvPrinter.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return exportInfoList;
    }

    private List<String> exportTrendToFile(List<Map<String, Object>> dateRowMap, String fileName) {
        List<String> exportInfoList = new ArrayList<>();
        CsvWriteConfig config = new CsvWriteConfig();
        config.setAlwaysDelimitText(true);
        try {
            CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(fileName)), csvFullFormatTrend);
            for (Map<String, Object> objectMap : dateRowMap) {
//                DecimalFormat df = new DecimalFormat("0.00%");
                csvPrinter.printRecord(
                        objectMap.get("date"),
                        objectMap.get("wtdPositionAvg")
//                        df.format(Double.parseDouble(objectMap.get("wtdPositionAvg").toString()))
                );
            }
            csvPrinter.flush();
            csvPrinter.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return exportInfoList;
    }


    private void parseLinesKw(JSONArray responseArray, List<Map<String, Object>> dateRowMap) throws
            Exception {
        for (Object line : responseArray) {
            Map<String, Object> dataMap = new HashMap<>();
            JSONObject lineObj = (JSONObject) JSONObject.toJSON(line);
            String Keyword = lineObj.get("keywordName").toString();
            dataMap.put("Keyword", Keyword);
            String wtdPositionAvg = lineObj.get("wtdPositionAvg").toString();
            dataMap.put("wtdPositionAvg", wtdPositionAvg);
            dateRowMap.add(dataMap);
        }
    }

    private void parseLinesTrend(JSONArray responseArray, List<Map<String, Object>> dateRowMap) throws
            Exception {
        for (Object line : responseArray) {
            Map<String, Object> dataMap = new HashMap<>();
            JSONObject lineObj = (JSONObject) JSONObject.toJSON(line);
            String date = lineObj.get("date").toString();
            dataMap.put("date", date);
            String wtdPositionAvg = lineObj.get("wtdPositionAvg").toString();
            dataMap.put("wtdPositionAvg", wtdPositionAvg);
            dateRowMap.add(dataMap);
        }
    }

    private String createDaysForFiles(String queryDate, String domainFolder, String name) {
        String idx = StringUtils.replace(queryDate, "-", "");
        name = StringUtils.replace(name, "%DATE_INDEX%", idx);
        String fileName = domainFolder + name;
        return fileName;
    }

    private static String clearJson(String json) {
        json = StringUtils.removeStart(json, "\"");
        json = StringUtils.removeEnd(json, "\"");
        return json;
    }

    private void deleteFiles(String fileName) {
        File localFile = new File(fileName);
        FileUtils.deleteFile(localFile);
    }

    private void sendMailReport( String status, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "hao");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String subject = "Dashboard Download "  + status;
        String[] ccTo = new String[]{"<EMAIL>"};
        ccTo = null;

//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap,
                null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

}
