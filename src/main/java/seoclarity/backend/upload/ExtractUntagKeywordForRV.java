package seoclarity.backend.upload;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.time.DateUtils;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.kpcold.ClColdDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public class ExtractUntagKeywordForRV {
	
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private CommonDataService commonDataService;

	private static String fileStorePath = "/home/<USER>/RedVentures_extract/untag/";
	
	private static String rankingDate;
	public ExtractUntagKeywordForRV() {
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		commonDataService = SpringBeanFactory.getBean("commonDataService");
	}

	private static final String S3_BUCKET_KEY_PREFIX = "keyword_untagged";
	
	public static void main(String[] args) throws IOException {
		
		if (args != null && args.length == 1) {
			rankingDate = args[0];
		} else {
			rankingDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -1), "yyyy-MM-dd");
		}
		
		ExtractUntagKeywordForRV extractUntagKeywordForRV = new ExtractUntagKeywordForRV();
		extractUntagKeywordForRV.process();
		
	}
	
	private void process() throws IOException {
		
		String filePath = fileStorePath + "/" + "untag_" + rankingDate + ".txt";
		
		File file = new File(filePath);
		if (file != null && file.exists()) {
			
			System.out.println("===== File existed, delete current file!!!");
			file.delete();
		}
		
		List<Integer> ownDomainIdList = new ArrayList<>();
//        List<OwnDomainEntity> domainEntityList = ownDomainEntityDAO.getDomainListBasedCompanyName("Red Ventures LLC");
        List<OwnDomainEntity> domainEntityList = commonDataService.getRVTargetDomainList(); // https://www.wrike.com/open.htm?id=1344553888

    	for (OwnDomainEntity ownDomainEntity : domainEntityList) {
	       
        	ownDomainIdList.add(ownDomainEntity.getId());
        }
		
		List<CLRankingDetailEntity> usResultList = clDailyRankingEntityDao.queryForUnTagKeyword(ownDomainIdList, 1, 1, rankingDate);
		
		FileWriter fWriter = new FileWriter(file, true);
		fWriter.write("Domain ID " + "\t" + "Keyword " + "\t" + "Keyword rankcheck iD " + "\n");
		
		System.out.println("usResultList size : " + usResultList.size());

		for(CLRankingDetailEntity entity : usResultList) {
			
			fWriter.write(entity.getOwnDomainId() + "\t" + entity.getKeywordName() + "\t" + entity.getKeywordRankcheckId() + "\n");
		}
		
		
		List<CLRankingDetailEntity> intlResultList = clDailyRankingEntityDao.queryForUnTagKeyword(ownDomainIdList, 6, 8, rankingDate);
		System.out.println("intlResultList size : " + intlResultList.size());

		for(CLRankingDetailEntity entity : intlResultList) {
			
			fWriter.write(entity.getOwnDomainId() + "\t" + entity.getKeywordName() + "\t" + entity.getKeywordRankcheckId() + "\n");
		}
		fWriter.close();
		
		Date date = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setFirstDayOfWeek(Calendar.MONDAY);
		calendar.setTime(date);
		
		ExtractRedVenturesKeywords.saveFileToS3ForUntagByTransferManager(FormatUtils.formatDate(new Date(), "yyyy") + "/" + calendar.get(Calendar.WEEK_OF_YEAR), file.getAbsolutePath(), S3_BUCKET_KEY_PREFIX);
		
	}

}
