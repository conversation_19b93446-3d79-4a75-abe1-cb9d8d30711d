package seoclarity.backend.upload;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;

import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

/**
 * <AUTHOR>
 * @create 2020-01-30 14:54
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.UploadFileToS3ForRVDomains" -Dexec.args="20220919 /home/<USER>/RedVentures_extract/rv_daily_2022-09-19_US_en_extract_tabsplit.csv"
 **/
public class UploadFileToS3ForRVDomains {

    private static final String S3_BUCKET_KEY_PREFIX = "top_keywords";

    public UploadFileToS3ForRVDomains() {
    }
    
    private static String RV_S3_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
    private static String RV_S3_SECRET_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
    private static String RV_S3_BUCKET = "seoclarity-rvu";
    private static AWSCredentials rvCredentials = new BasicAWSCredentials(RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY);
 
    public static void main(String[] args) throws IOException {
    	Set<String> fileSet = new HashSet<>();
    	String processDateStr = "";
    	if (args != null && args.length >= 2) {
    		processDateStr = args[0];
    		fileSet.add(args[1]);
		} else {
			System.out.println(" param is not correct!! ");
			return;
		}
       
    	System.out.println("File count : " + fileSet.size());
    	UploadFileToS3ForRVDomains uploadFileToS3ForRVDomains = new UploadFileToS3ForRVDomains();
    	uploadFileToS3ForRVDomains.saveFilesToDestination(processDateStr, fileSet);
    	
//    	sentFileToS3(new File(args[1]));
        
    }
    
    public static void sentFileToS3(File file) {
    	
		try {
			System.out.println("===send to s3 : " + file.getName());
			ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(file.getName(), file.getAbsolutePath(), RV_S3_BUCKET, RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY);
		} catch (Exception e) {
			e.printStackTrace();
		}

    }
    
    private void saveFilesToDestination(String processDateStr, Set<String> localFileSet) {
      boolean savedFilesToS3 = true;
      try {
          List<String> fileList = new ArrayList<String>(localFileSet);
          for (String localOutputFilename : fileList) {
              System.out.println(" ==localOutputFilename:" + localOutputFilename);
              File outFile = new File(localOutputFilename);
              savedFilesToS3 = ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(processDateStr, outFile.getAbsolutePath(), S3_BUCKET_KEY_PREFIX, false);

              try {
          		ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(processDateStr, outFile.getAbsolutePath(), S3_BUCKET_KEY_PREFIX, true);
				} catch (Exception e) {
					// TODO: handle exception
				}
          }
      } catch (Exception e) {
          e.printStackTrace();
          ZeptoMailSenderComponent.sendEmailReport(new Date(), "ExtractFlatDataForRV Error", "ExtractFlatDataForRV error occurred", e.getMessage());
      }

      try {
          if (savedFilesToS3) {
              ZeptoMailSenderComponent.sendEmailReport(new Date(), "ExtractFlatDataForRV Success", "ExtractFlatDataForRV sent file to S3 and FTP", null);
          } else {
              ZeptoMailSenderComponent.sendEmailReport(new Date(), "ExtractFlatDataForRV sent file to S3/FTP Error",
                      "ExtractFlatDataForRV did not send to s3", null);
          }
      } catch (Exception exp) {
          exp.printStackTrace();
      }
  }
    
    
}