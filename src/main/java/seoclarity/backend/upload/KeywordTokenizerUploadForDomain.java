package seoclarity.backend.upload;

/**
 * <AUTHOR>
 * @date 2018-10-17
 * seoclarity.backend.upload.KeywordTokenizerUploadForDomain
 * https://www.wrike.com/open.htm?id=279122673
 *
 * client can sepcify a/mulit domain id. we need to get all managed keywords from t-keyword table. and create a extract file with only 2 columns (TAB separator).
1. original keywords.
2. variation word list.
 */
@Deprecated
public class KeywordTokenizerUploadForDomain {
//	private OwnDomainEntityDAO ownDomainEntityDAO;
//	private KeywordEntityDAO keywordEntityDAO;
//	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
//	private static final String INSERT_SQL = "insert into actonia.keyword_variation (own_domain_id, keyword, keyword_variation_list) values (?,?,?)";
//	private static String extractPath;
//	private Connection connection;
//
//	private static int minKid = 0;
//
//	public KeywordTokenizerUploadForDomain() {
//		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
//		keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
//		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
//		try {
//			connection = ClarityDBConnection.getDBConnectionWithoutPWD("173.236.42.186:8123");
//		} catch (SQLException e) {
//			e.printStackTrace();
//		}
//	}
//
//	public static void main(String[] args) {
//		String domains = args[0];
////		extractPath = args[1];
//		System.out.println("domains:" + domains + ", extractPath:" + extractPath);
//
//		KeywordTokenizerUploadForDomain ins = new KeywordTokenizerUploadForDomain();
////		for (String oid : StringUtils.split(domains, ',')) {
////			try {
////				ins.process(Integer.valueOf(oid), extractPath);
////			} catch (Exception e) {
////				e.printStackTrace();
////			}
////		}
//		try {
//			minKid = Integer.valueOf(args[0]);
//			ins.processFoeMonth(1, 1);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		ClarityDBConnection.closeConnection(ins.connection);
////		String str = "'please divide, this sentence into' shingles";
////		setPlaceHolder(Ngram_for_dev.wordTokenizer(str));
////		str = "ccc bb&bb bbb";
////		setPlaceHolder(Ngram_for_dev.wordTokenizer(str));
////		str = "1-hour history ebooks taxonomysearch_experiment_us";
////		setPlaceHolder(Ngram_for_dev.wordTokenizer(str));
////		str = "b b c";
////		setPlaceHolder(Ngram_for_dev.wordTokenizer(str));
//	}
//
//	private void processFoeMonth(int engine, int language) throws Exception{
//		int oid = 0;
//		//batchInsert
//		int uploadSize = 1000;
//		int uploadCount = 0;
//		int minId = 0;
//		System.out.println("minKid:" + minKid);
//		do {
////			List<SeoClarityKeywordEntity> kwList = seoClarityKeywordEntityDAO.getKeywordsGoogleMonthlyRankingQuery(minId, 0, uploadSize, language, engine);
//			long a = System.currentTimeMillis();
//			List<Map<String, Object>> kidList = seoClarityKeywordEntityDAO.getMonthlyRelationsKeywordId(engine, language, minKid, minId, uploadSize);
//			if (kidList == null || kidList.size() == 0) {
//				break;
//			}
//			List<Integer> processKIDList = new ArrayList<Integer>();
//			for (Map<String, Object> kw : kidList) {
//				processKIDList.add(Long.valueOf(kw.get("keyword_id").toString()).intValue());
//			}
//			List<SeoClarityKeywordEntity> kwList = seoClarityKeywordEntityDAO.getKeywordByIdList(processKIDList);
//			minId = Long.valueOf(kidList.get(kidList.size() - 1).get("id").toString()).intValue();
//			uploadCount += kwList.size();
//			System.out.println("===OID:" + oid + ", Current upload:" + uploadCount + ", maxId:" + minId + (", COST:" + (System.currentTimeMillis() - a)));
//			try {
//				processSave(kwList, oid);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		} while(true);
//	}
//
//	private void processSave(List<SeoClarityKeywordEntity> kwList, int oid) {
//		List<KeywordVariationEntity> list = new ArrayList<KeywordVariationEntity>();
//		for (SeoClarityKeywordEntity kwEntity : kwList) {
//			List<String> wordList = Ngram_for_dev.wordTokenizer(kwEntity.getDecoderKeyword());
//			// add "-" as placeholder
//			List<String> array = setPlaceHolder(wordList);
//			KeywordVariationEntity entity = new KeywordVariationEntity(oid, kwEntity.getDecoderKeyword(), array.toArray(new String[array.size()]));
//			list.add(entity);
//		}
//		try {
//			insert(list);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//
//	private void process(int oid, String extractPath) throws Exception{
//		Set<String> processList = new HashSet<String>();
////		String prefix = oid + "_extract_keywords_";
////		File extractFile = File.createTempFile(prefix, ".processing", new File(extractPath));
//
//		// get keywords
//		processList = getKeywordsForDomain(oid);
//
//		// extract to file
////		CSVFormat format = CSVFormat.DEFAULT.withDelimiter(',');
////		FileWriter writer = new FileWriter(extractFile, true);
////		CSVPrinter printer = new CSVPrinter(writer, format);
////		for (String str : processList) {
////			if (StringUtils.contains(str, "'")) {
////				str = StringUtils.replace(str, "'", "\'");
////			}
////			List<String> array = Ngram_for_dev.wordTokenizer(str);
////			if (array != null && array.size() > 0) {
////				Object[] line = new String[] {
////						String.valueOf(oid), str, "['" + StringUtils.join(array, "','") + "']"
////				};
////				try {
////					printer.printRecord(line);
////				} catch (IOException e) {
////					e.printStackTrace();
////				}
////			} else {
////				System.out.println("Skip empty kw, OID:" + oid + ", kw:" + str);
////			}
////		}
////		printer.close();
////		writer.close();
////		String finalFile = StringUtils.replaceOnce(extractFile.getAbsolutePath(), ".processing", ".csv");
////		extractFile.renameTo(new File(finalFile));
////		System.out.println("File:" + finalFile);
//
//		//batchInsert
//		int uploadSize = 1000;
//		int uploadCount = 0;
//		List<KeywordVariationEntity> list = new ArrayList<KeywordVariationEntity>();
//		for (String str : processList) {
//			List<String> wordList = Ngram_for_dev.wordTokenizer(str);
//			// add "-" as placeholder
//			List<String> array = setPlaceHolder(wordList);
//			KeywordVariationEntity entity = new KeywordVariationEntity(oid, str, array.toArray(new String[array.size()]));
//			list.add(entity);
//			if (list.size() >= uploadSize) {
//				try {
//					insert(list);
//					uploadCount += list.size();
//					System.out.println("===OID:" + oid + ", Current upload:" + uploadCount);
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
//				list.clear();
//			}
//		}
//		if (list.size() > 0) {
//			try {
//				insert(list);
//				uploadCount += list.size();
//				System.out.println("===OID:" + oid + ", Current upload:" + uploadCount);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//	}
//
//	private Set<String> getKeywordsForDomain(int oid) {
//		Set<String> processList = new HashSet<String>();
//		OwnDomainEntity domain = ownDomainEntityDAO.getById(oid);
//		if (domain != null && domain.getStatus() != null && domain.getStatus() == OwnDomainEntity.STATE_ACTIVE) {
//			List<KeywordEntity> kwList = null;
//			int pageSize = 1000;
//			long minId = 0l;
//			int totalCount = 0;
//			do {
//				kwList = keywordEntityDAO.getKeywordByDomainId(oid, minId, pageSize);
//				if (kwList != null && kwList.size() > 0) {
//					totalCount += kwList.size();
//					minId = kwList.get(kwList.size() - 1).getId();
//					System.out.println("=OID:" + oid + ", Current:" + kwList.size() + ", totalCount:" + totalCount + ", maxId:" + minId);
//					for (KeywordEntity kw : kwList) {
//						try {
//							String str = kw.getKeywordName().toLowerCase();
//							String decodedKw = URLDecoder.decode(str, "UTF-8").toLowerCase();
//							processList.add(decodedKw);
//						} catch (Exception e) {
//							System.out.println("Decode kw failed. OID:" + oid + ", kw:" + kw.getId() + ", str:" + kw.getKeywordName());
//							e.printStackTrace();
//						}
//					}
//				} else {
//					break;
//				}
//			} while(kwList.size() > 0);
//			System.out.println("OID:" + oid + ", totalCount:" + totalCount + ", processList:" + processList.size());
//		}
//
//		return processList;
//	}
//
//	/***
//	 * please divide this sentence into shingles
//	 please divide -> please divide _
//	 please divide this - > please divide this_
//	 divide this -> _ divide this _
//	 divide this sentence -> _ divide this sentence _
//	 sentence into - > _ sentence into _
//	 into shingles - > _ into shingles
//	 */
//	private static List<String> setPlaceHolder(List<String> array) {
//		String holder = "_";
//		if (array.size() <= 2) {
//			return array;
//		}
//		List<String> result = new ArrayList<String>();
//
//		String startWordStr = array.get(0);
//		String endWordStr = array.get(array.size() - 1);
//
//		for (int i = 0; i < array.size(); i++) {
//			String words = array.get(i);
//			if ((StringUtils.startsWith(startWordStr, words) || StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
//				words = words + " " + holder;
//				result.add(words);
//				continue;
//			}
//			if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
//				words = holder + " "  + words + " " + holder;
//				result.add(words);
//				continue;
//			}
//			if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (StringUtils.endsWith(endWordStr, words) || StringUtils.endsWith(words, endWordStr))) {
//				words = holder + " " + words;
//				result.add(words);
//				continue;
//			}
//			result.add(words);
//		}
////		System.out.println(array + "->" + result);
//		return result;
//	}
//
//	private void insert(List<KeywordVariationEntity> list) {
//		PreparedStatement statement = null;
//		try {
//			statement = connection.prepareStatement(INSERT_SQL);
//
//			for (KeywordVariationEntity entity : list) {
//				int parameterIndex = 1;
//				statement.setInt(parameterIndex++, entity.getOwnDomainId());
//				statement.setString(parameterIndex++, entity.getKeyword());
//				//begin xxxxyyyy
//				statement.setArray(parameterIndex++, new ClickHouseArray(ClickHouseDataType.String, entity.getKeywordVariationList()));
////end xxxxyyyy
//				statement.addBatch();
//			}
//			statement.executeBatch();
//		} catch (Exception e) {
//			e.printStackTrace();
//		} finally {
//			if (statement != null) {
//				try {
//					statement.close();
//				} catch (SQLException e) {
//					e.printStackTrace();
//				}
//			}
//		}
//	}
//
//	class KeywordVariationEntity {
//		private int ownDomainId;
//		private String keyword;
//		private String[] keywordVariationList;
//
//		public KeywordVariationEntity(int oid, String keyword, String[] array) {
//			this.ownDomainId = oid;
//			this.keyword = keyword;
//			this.keywordVariationList = array;
//		}
//
//		public int getOwnDomainId() {
//			return ownDomainId;
//		}
//
//		public void setOwnDomainId(int ownDomainId) {
//			this.ownDomainId = ownDomainId;
//		}
//
//		public String getKeyword() {
//			return keyword;
//		}
//
//		public void setKeyword(String keyword) {
//			this.keyword = keyword;
//		}
//
//		public String[] getKeywordVariationList() {
//			return keywordVariationList;
//		}
//
//		public void setKeywordVariationList(String[] keywordVariationList) {
//			this.keywordVariationList = keywordVariationList;
//		}
//	}
}
