package seoclarity.backend.upload;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer5Dao;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.clickhouse.internallink.InternalLinkClarityDBEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * 
 * <AUTHOR> @date 
 * seoclarity.backend.upload.InternalLinkUploaderV2
 * upload to clarityDB
 */
public class InternalLinkUploaderV3 {
	
		
	private static Integer startHour;
	private static Date processDate = new Date();
	
	private static final String databaseName = "actonia_internal_link";
	private static final String finalTableName = "distributed_internal_link_view_test_v2";
	
	private final int maxInsertCount = 100000;
	
	private static Gson gson = new Gson();
    
	public static final Log logger = LogFactory.getLog(InternalLinkUploaderV3.class);

	
    public static String storeDoneFilePath = "/home/<USER>/internalLink/needUpload";
    public static String storeTempFilePath = "/home/<USER>/internalLink/uploading/" + FormatUtils.formatDate(new Date(), "yyyyMMddHH") + "/";    
    public static String storeBKFilePath = "/home/<USER>/internalLink/backUpFolder";
    public static String storeDuplicateFilePath = "/home/<USER>/internalLink/duplicate";
   

    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n').withHeader(headers);
	
	public static final String[] headers = new String[]{
		"link_nofollow", "domain_id_i", "analyzed_url_s", "source_domain", "canonical_type",
		"canonical_flg", "anchor_text", "destination_domain", "destination_root_domain", "crawl_date_long",
		"source_url", "page_robots_meta_index", "page_robots_meta_follow", "page_robots_meta_archive", "destination_url",
		"title_md5", "popularity", "title", "canonical", "source_root_domain",
		"analyzed_url_flg_s", "title_flg", "crawl_request_log_id_i", "canonical_string", "destination_folder_level_1",
		"destination_folder_level_2", "source_folder_level_1", "source_folder_level_2", "today", "source_url_response_code",
		"sourceurl_anchortext_hash", "destinationurl_anchortext_hash"
	};
		
	private InternalLinkNewClusterServer5Dao internalLinkNewClusterServer5Dao;

	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	
	private static List<String> processingFileList = new ArrayList<String>();
	
	private static Integer logId;

	public InternalLinkUploaderV3() {
		
		internalLinkNewClusterServer5Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer5Dao");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		
	}

	public static void main(String args[]) {
		
		if (args.length > 0 && StringUtils.isNotBlank(args[0])) {
			try {
				processDate = FormatUtils.toDate(args[0], "yyyyMMddHH");
			} catch (Exception e) {
				System.out.println("Date format failed, format should be yyyyMMddHH, exit!!!! : " + args[0]);
				return;
			}
		}
		
		if (args.length > 1) {
			storeDoneFilePath = args[1];
		}
		
		InternalLinkUploaderV3 internalLinkUploaderV3 = new InternalLinkUploaderV3();
		System.out.println("--- storeDoneFilePath:" + storeDoneFilePath + ", 30s to continue...");
		
		Calendar cal = Calendar.getInstance();
		cal.setTime(processDate);
		startHour = cal.get(Calendar.HOUR_OF_DAY);
		
		if (internalLinkUploaderV3.checkIsProcessingUpload()) {
			System.out.println("There found more than one processing still not finished, exit !!!");
			System.exit(-1);
		}
		
		File doneFolder = new File(storeDoneFilePath);
		if (doneFolder != null && doneFolder.isDirectory()) {
			boolean hasFilesNeedProcess = false;
			
			for (File file : doneFolder.listFiles()) {
				if (StringUtils.startsWith(file.getName(), "internal_linkv2_") && file.isFile()) {
					
					System.out.println("Found file " + file.getName() + ", start to process !");
					hasFilesNeedProcess = true;
					break;
				}
			}
			
			if (!hasFilesNeedProcess) {
				
				internalLinkUploaderV3.insertEmptyLog();
				System.out.println("There do not have any files need to be process, skiped+++");
				System.exit(-1);
			}

		}
		
		
		try {
			Thread.sleep(5000);
			internalLinkUploaderV3.process();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	//if no files need to be upload, then insert one record with final status = success(2) and upload status = success(2)
	private void insertEmptyLog(){
		
		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
		
		clarityDBUploadLogEntity.setTmpTableName("");
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName("test");
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadDailyRows(0);
		clarityDBUploadLogEntity.setFinalTableUploadRows(0);
		clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK);
		
		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadEndTime(new Date());
		clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}
	
	private boolean checkIsProcessingUpload(){
		
//		List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK);
//		if (CollectionUtils.isNotEmpty(list)) {
//			return true;
//		}
		return false;
	}
	
	private void process() {
		
		long startTime = System.currentTimeMillis();
		
		File duplicateFolder = new File(storeDuplicateFilePath);
		if (duplicateFolder == null || !duplicateFolder.isDirectory()) {
			duplicateFolder.mkdir();
		}
		
		moveFilesToProcessingFolder();
		
		// insert into temp table
			
		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
		
		clarityDBUploadLogEntity.setTmpTableName("test");
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName("test");
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
		clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK);
		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);
		
		logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
		System.out.println("===add log for claritydb_upload_log, id:" + logId);
		
		//process normal
		File doneFolder = new File(storeDoneFilePath);
		if (doneFolder == null || !doneFolder.isDirectory()) {
			System.out.println("Folder is not exist :" + doneFolder);
			long endTime = System.currentTimeMillis();
			int elapsedSeconds = (int) (endTime - startTime);

			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0, elapsedSeconds, logId);
			return;
		}
		try {
			
			System.out.println("processingFileList size:" + processingFileList.size());
			
			File tmpFile;
			for (String internalLinkFileFullPath : processingFileList) {
				tmpFile = new File(internalLinkFileFullPath);
				
				if (tmpFile == null || !tmpFile.isFile()) {
					System.out.println("File is not exist or is a folder : " + internalLinkFileFullPath);
					continue;
				}
				
				System.out.println("=====processing file:" + tmpFile.getAbsolutePath());
				processFile(tmpFile);
			}
			
			long endTime = System.currentTimeMillis();
			int elapsedSeconds = (int) (endTime - startTime);
			
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0, elapsedSeconds, logId);

		} catch (Exception e) {
			System.out.println("Updated status in log table to " + ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE);
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
			System.out.println("Insert table failed, table: " + finalTableName + ", folder: "  + storeDoneFilePath);
			
			moveFilesBackProcessingFolder();
			e.printStackTrace();
			return ;
		}
		
		
		try {
			moveFileAndZip();
		} catch (Exception e) {
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS_MOVE_FILE_FAIL, logId);
		} finally {
			deleteTempProcessingFolder();
		}
		
			
		
	}
	
	private void moveFilesToProcessingFolder(){
		
		File targetFolder = new File(storeTempFilePath);
		if (targetFolder == null || !targetFolder.isDirectory()) {
			System.out.println("Folder is not exist, mkdir: " + storeTempFilePath);
			targetFolder.mkdir();
		}
		
		File doneFolder = new File(storeDoneFilePath);
		
		System.out.println("====moving files to processing folder!! from " + storeDoneFilePath + " to " + storeTempFilePath);
		
		for (File file : doneFolder.listFiles()) {
			try {
				if (StringUtils.startsWith(file.getName(), "internal_linkv2_") && StringUtils.endsWith(file.getName(), ".txt") &&file.isFile()) {
					FileUtils.moveFile(file, new File(storeTempFilePath + "/" + file.getName()));
					processingFileList.add(storeTempFilePath + "/" + file.getName());
				}
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
	}
	
	private void moveFilesBackProcessingFolder(){
		
		File processingFolder = new File(storeTempFilePath);
		
		System.out.println("====moving files back from processing folder !! from " + storeTempFilePath + " to " + storeDoneFilePath);
		
		for (File file : processingFolder.listFiles()) {
			try {
				FileUtils.moveFile(file, new File(storeDoneFilePath + "/" + file.getName()));
				processingFileList.add(storeDoneFilePath + "/" + file.getName());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		deleteTempProcessingFolder();
	}
	
	private void moveFileAndZip(){
		
		File bkFolder = new File(storeBKFilePath);
		if (bkFolder == null || !bkFolder.isDirectory()) {
			System.out.println("BK folder is not exist, mkdir: " + storeBKFilePath);
			bkFolder.mkdir();
		}
		
		File tmpFile;
		File targetFile;
		
		for (String fileFullPath : processingFileList) {
			// zip file
			try {
				tmpFile = new File(fileFullPath);
				targetFile = new File(bkFolder + "/" + tmpFile.getName());
				
				FileUtils.moveFile(tmpFile, targetFile);
				
				System.out.println("zipped file : " + targetFile.getAbsolutePath());
	            GZipUtil.zipFile(targetFile.getAbsolutePath());
	            
	            targetFile.delete();
	            System.out.println("delete file : [" + fileFullPath + "]");
	        } catch (Exception e) {
	        	System.out.println("delete file failed. file: [" + fileFullPath + "]");
	            e.printStackTrace();
	        }
			
		}
		
		deleteTempProcessingFolder();
	}
	
	private void deleteTempProcessingFolder(){
		//deleted the temp processing folder
		File tempFolder = new File(storeTempFilePath);
		
		if (tempFolder != null && tempFolder.isDirectory()) {
			tempFolder.delete();
		}
	}

	
	private void processFile(File file) throws Exception {

		List<InternalLinkClarityDBEntity> internalLinkEntities = new ArrayList<>();
		BigDecimal crawl_date_long ;
		try {

			BufferedReader bf = new BufferedReader(new FileReader(file));
			String content = "";
			
//			int i = 0;

			while (content != null) {
				content = bf.readLine();
				
//				if (i == 0) {
//					System.out.println("Skip the title!!");
//					i++;
//					continue;
//				}

				if (content == null) {
					break;
				}
				
				String today = FormatUtils.formatDate(new Date(), "yyyy-MM-dd");
				
				
				InternalLinkClarityDBEntity internalLinkClarityDBEntity = new InternalLinkClarityDBEntity();
				
				
				try {
					Map<String, Object> result = gson.fromJson(content, Map.class);
					ResponseVO resultMap = gson.fromJson(content, ResponseVO.class);
					
					//in case the fromJson transfor the data from integer to double and using scientific notation
					crawl_date_long = new BigDecimal(String.valueOf(result.get("crawl_date_long")));
					
					internalLinkClarityDBEntity.setAnalyzed_url_flg_s(resultMap.getAnalyzed_url_flg_s());
					internalLinkClarityDBEntity.setAnalyzed_url_s(resultMap.getAnalyzed_url_s());
					internalLinkClarityDBEntity.setAnchor_text(resultMap.getAnchor_text());
					internalLinkClarityDBEntity.setCanonical(resultMap.getCanonical());
					internalLinkClarityDBEntity.setCanonical_flg(resultMap.getCanonical_flg());
					internalLinkClarityDBEntity.setCanonical_string(resultMap.getCanonical_string());
					internalLinkClarityDBEntity.setCanonical_type(resultMap.getCanonical_type());
					internalLinkClarityDBEntity.setCrawl_date_long(NumberUtils.toLong(crawl_date_long.toPlainString()));
					internalLinkClarityDBEntity.setCrawl_request_log_id_i(resultMap.getCrawl_request_log_id_i());
					internalLinkClarityDBEntity.setDestination_domain(resultMap.getDestination_domain());
					internalLinkClarityDBEntity.setDestination_folder_level_1(resultMap.getDestination_folder_level_1());
					internalLinkClarityDBEntity.setDestination_folder_level_2(resultMap.getDestination_folder_level_2());
					internalLinkClarityDBEntity.setDestination_root_domain(resultMap.getDestination_root_domain());
					internalLinkClarityDBEntity.setDestination_url(StringUtils.trim(resultMap.getDestination_url()));
//					internalLinkClarityDBEntity.setDestinationurl_anchortext_hash(NumberUtils.toLong(String.valueOf(resultMap.get("destinationurl_anchortext_hash"))));
					internalLinkClarityDBEntity.setDomain_id_i(resultMap.getDomain_id_i());
					internalLinkClarityDBEntity.setLink_nofollow(resultMap.getLink_nofollow());
					internalLinkClarityDBEntity.setPage_robots_meta_archive(resultMap.getPage_robots_meta_archive());
					internalLinkClarityDBEntity.setPage_robots_meta_follow(resultMap.getPage_robots_meta_follow());
					internalLinkClarityDBEntity.setPage_robots_meta_index(resultMap.getPage_robots_meta_index());
					internalLinkClarityDBEntity.setPopularity(resultMap.getPopularity());
					internalLinkClarityDBEntity.setSource_domain(resultMap.getSource_domain());
					internalLinkClarityDBEntity.setSource_folder_level_1(resultMap.getSource_folder_level_1());
					internalLinkClarityDBEntity.setSource_folder_level_2(resultMap.getSource_folder_level_2());
					internalLinkClarityDBEntity.setSource_root_domain(resultMap.getSource_root_domain());
					internalLinkClarityDBEntity.setSource_url(StringUtils.trim(resultMap.getSource_url()));
					internalLinkClarityDBEntity.setSource_url_response_code(resultMap.getSource_url_response_code());
//					internalLinkClarityDBEntity.setSourceurl_anchortext_hash(NumberUtils.toLong(String.valueOf(resultMap.get("sourceurl_anchortext_hash"))));
					internalLinkClarityDBEntity.setTitle_flg(resultMap.getTitle_flg());
					internalLinkClarityDBEntity.setToday(FormatUtils.formatDate(new Date(), "yyyy-MM-dd"));
					
					if (result.get("title") instanceof ArrayList) {
						String[] titleArray = gson.fromJson(gson.toJson(result.get("title")), String[].class);
						internalLinkClarityDBEntity.setTitle(titleArray[0]);
					} else if(result.get("title") instanceof String) {
						internalLinkClarityDBEntity.setTitle(String.valueOf(result.get("title")));
					}
					
					if (result.get("title_md5") instanceof ArrayList) {
						String[] titleMd5Array = gson.fromJson(gson.toJson(result.get("title_md5")), String[].class);
						internalLinkClarityDBEntity.setTitle_md5(titleMd5Array[0]);
					} else if(result.get("title_md5") instanceof String) {
						internalLinkClarityDBEntity.setTitle_md5(String.valueOf(result.get("title_md5")));
					}
					
					internalLinkEntities.add(internalLinkClarityDBEntity);
				} catch (Exception e) {
					System.out.println("====== content can not be parse to entity, content: " + content);
				}

//				try {
//					InternalLinkEntity internalLinkEntity = gson.fromJson(content, InternalLinkEntity.class);
//					
//					internalLinkEntity.setToday(today);
//					internalLinkEntities.add(InternalLinkClarityDBEntity.convertFromInternalLinkEntity(internalLinkEntity));
//				} catch (JsonSyntaxException e) {
//					try {
//						InternalLinkClarityDBEntity internalLinkEntity = gson.fromJson(content, InternalLinkClarityDBEntity.class);
//						internalLinkEntity.setToday(today);
//						internalLinkEntities.add(internalLinkEntity);
//					} catch (Exception e2) {
//						System.out.println("====== content can not be parse to entity, content: " + content);
//					}
//					
//				} catch (Exception e) {
//					e.printStackTrace();
//				}

				if (internalLinkEntities.size() >= maxInsertCount) {
					internalLinkNewClusterServer5Dao.insertForBatch(internalLinkEntities, finalTableName);
					System.out.println("finish insert for top : " + maxInsertCount + " for file :" + file.getName());
					internalLinkEntities.clear();
				}
			}

			System.out.println("=====list count:" + (internalLinkEntities != null ? internalLinkEntities.size() : 0));

			if (CollectionUtils.isNotEmpty(internalLinkEntities)) {
				internalLinkNewClusterServer5Dao.insertForBatch(internalLinkEntities, finalTableName);
				System.out.println("finish insert for left count :" + internalLinkEntities.size());
				internalLinkEntities.clear();
			}

			bf.close();

		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
	}
	 
	public Set<Integer> getDomainSet(String domainIds) {
		Set<Integer> returnDomain = new HashSet<Integer>();
		String[] dIds = domainIds.split(",");

		if (dIds != null && dIds.length > 0) {
			for (int i = 0; i < dIds.length; i++) {
				try {
					returnDomain.add(Integer.parseInt(dIds[i]));
				} catch (Exception e) {
					
				}
			}
		}
		return returnDomain;
	}
	
	
	public class ResponseVO{
		
		private Boolean analyzed_url_flg_s;
		private String analyzed_url_s;
		private String anchor_text;
		private String canonical;
		private Boolean canonical_flg;
		private String canonical_string;
		private String canonical_type;
		private Integer crawl_request_log_id_i;
		private String destination_domain;
		private String destination_folder_level_1;
		private String destination_folder_level_2;
		private String destination_root_domain;
		private String destination_url;
		private Integer domain_id_i;
		private Boolean link_nofollow;
		private String page_robots_meta_archive;
		private String page_robots_meta_follow;
		private String page_robots_meta_index;
		private Integer popularity;
		private String source_domain;
		private String source_folder_level_1;
		private String source_folder_level_2;
		private String source_root_domain;
		private String source_url;
		private Integer source_url_response_code;
		private Boolean title_flg;
		
		public Boolean getAnalyzed_url_flg_s() {
			return analyzed_url_flg_s;
		}
		public void setAnalyzed_url_flg_s(Boolean analyzed_url_flg_s) {
			this.analyzed_url_flg_s = analyzed_url_flg_s;
		}
		public String getAnalyzed_url_s() {
			return analyzed_url_s;
		}
		public void setAnalyzed_url_s(String analyzed_url_s) {
			this.analyzed_url_s = analyzed_url_s;
		}
		public String getAnchor_text() {
			return anchor_text;
		}
		public void setAnchor_text(String anchor_text) {
			this.anchor_text = anchor_text;
		}
		public String getCanonical() {
			return canonical;
		}
		public void setCanonical(String canonical) {
			this.canonical = canonical;
		}
		public Boolean getCanonical_flg() {
			return canonical_flg;
		}
		public void setCanonical_flg(Boolean canonical_flg) {
			this.canonical_flg = canonical_flg;
		}
		public String getCanonical_string() {
			return canonical_string;
		}
		public void setCanonical_string(String canonical_string) {
			this.canonical_string = canonical_string;
		}
		public String getCanonical_type() {
			return canonical_type;
		}
		public void setCanonical_type(String canonical_type) {
			this.canonical_type = canonical_type;
		}
		public Integer getCrawl_request_log_id_i() {
			return crawl_request_log_id_i;
		}
		public void setCrawl_request_log_id_i(Integer crawl_request_log_id_i) {
			this.crawl_request_log_id_i = crawl_request_log_id_i;
		}
		public String getDestination_domain() {
			return destination_domain;
		}
		public void setDestination_domain(String destination_domain) {
			this.destination_domain = destination_domain;
		}
		public String getDestination_folder_level_1() {
			return destination_folder_level_1;
		}
		public void setDestination_folder_level_1(String destination_folder_level_1) {
			this.destination_folder_level_1 = destination_folder_level_1;
		}
		public String getDestination_folder_level_2() {
			return destination_folder_level_2;
		}
		public void setDestination_folder_level_2(String destination_folder_level_2) {
			this.destination_folder_level_2 = destination_folder_level_2;
		}
		public String getDestination_root_domain() {
			return destination_root_domain;
		}
		public void setDestination_root_domain(String destination_root_domain) {
			this.destination_root_domain = destination_root_domain;
		}
		public String getDestination_url() {
			return destination_url;
		}
		public void setDestination_url(String destination_url) {
			this.destination_url = destination_url;
		}
		public Integer getDomain_id_i() {
			return domain_id_i;
		}
		public void setDomain_id_i(Integer domain_id_i) {
			this.domain_id_i = domain_id_i;
		}
		public Boolean getLink_nofollow() {
			return link_nofollow;
		}
		public void setLink_nofollow(Boolean link_nofollow) {
			this.link_nofollow = link_nofollow;
		}
		public String getPage_robots_meta_archive() {
			return page_robots_meta_archive;
		}
		public void setPage_robots_meta_archive(String page_robots_meta_archive) {
			this.page_robots_meta_archive = page_robots_meta_archive;
		}
		public String getPage_robots_meta_follow() {
			return page_robots_meta_follow;
		}
		public void setPage_robots_meta_follow(String page_robots_meta_follow) {
			this.page_robots_meta_follow = page_robots_meta_follow;
		}
		public String getPage_robots_meta_index() {
			return page_robots_meta_index;
		}
		public void setPage_robots_meta_index(String page_robots_meta_index) {
			this.page_robots_meta_index = page_robots_meta_index;
		}
		public Integer getPopularity() {
			return popularity;
		}
		public void setPopularity(Integer popularity) {
			this.popularity = popularity;
		}
		public String getSource_domain() {
			return source_domain;
		}
		public void setSource_domain(String source_domain) {
			this.source_domain = source_domain;
		}
		public String getSource_folder_level_1() {
			return source_folder_level_1;
		}
		public void setSource_folder_level_1(String source_folder_level_1) {
			this.source_folder_level_1 = source_folder_level_1;
		}
		public String getSource_folder_level_2() {
			return source_folder_level_2;
		}
		public void setSource_folder_level_2(String source_folder_level_2) {
			this.source_folder_level_2 = source_folder_level_2;
		}
		public String getSource_root_domain() {
			return source_root_domain;
		}
		public void setSource_root_domain(String source_root_domain) {
			this.source_root_domain = source_root_domain;
		}
		public String getSource_url() {
			return source_url;
		}
		public void setSource_url(String source_url) {
			this.source_url = source_url;
		}
		public Integer getSource_url_response_code() {
			return source_url_response_code;
		}
		public void setSource_url_response_code(Integer source_url_response_code) {
			this.source_url_response_code = source_url_response_code;
		}
		public Boolean getTitle_flg() {
			return title_flg;
		}
		public void setTitle_flg(Boolean title_flg) {
			this.title_flg = title_flg;
		}
		
		
	}
	
}
