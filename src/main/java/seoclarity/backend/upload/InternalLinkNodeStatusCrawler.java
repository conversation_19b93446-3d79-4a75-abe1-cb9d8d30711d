package seoclarity.backend.upload;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.LongDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.InternalLinkNodeStatusDAO;
import seoclarity.backend.entity.actonia.InternalLinkNodeStatusVO;
import seoclarity.backend.utils.SpringBeanFactory;

//nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.InternalLinkNodeStatusCrawler" -Dexec.args="" > InternalLinkNodeStatusCrawler_20210303.log 2>&1 &
public class InternalLinkNodeStatusCrawler {
	
	private final static String TOPIC = "clarity-page-links-ack-v1";
	private final static String BOOTSTRAP_SERVERS = "173.236.58.194:9092,173.236.41.250:9092";
	
	public static final char FILE_SPLIT = '\t';
	public static final String ENCODING = "UTF-8";
	
	private InternalLinkNodeStatusDAO internalLinkNodeStatusDAO;
	
	public InternalLinkNodeStatusCrawler() {
		internalLinkNodeStatusDAO = SpringBeanFactory.getBean("internalLinkNodeStatusDAO");
	}
	
	public static void main(String... args) throws Exception {
		
		InternalLinkNodeStatusCrawler internalLinkNodeStatusCrawler = new InternalLinkNodeStatusCrawler();
		internalLinkNodeStatusCrawler.runConsumer();
	}

	private static Consumer<Long, String> createConsumer() {
		final Properties props = new Properties();
		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
		props.put(ConsumerConfig.GROUP_ID_CONFIG, "test_group_7");
		props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, LongDeserializer.class.getName());
		props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
		
		props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
		props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "True");

		// Create the consumer using props.
		final Consumer<Long, String> consumer = new KafkaConsumer<>(props);

		// Subscribe to the topic.
		consumer.subscribe(Collections.singletonList(TOPIC));
		return consumer;
	}

	void runConsumer() throws InterruptedException {
		final Consumer<Long, String> consumer = createConsumer();
		try {
			
			// add header
			List<String> lines = new ArrayList<String>();
//			lines.add(StringUtils.join(DB_COLUMNS, FILE_SPLIT));
//			FileUtils.writeLines(tempfile, ENCODING, lines, true);
			
			
			while (true) {
				final ConsumerRecords<Long, String> consumerRecords = consumer.poll(1000);
				
				if (consumerRecords.count() == 0) {
					
					try {
						//sleep 100 second if no incoming
						Thread.sleep(100000);
						continue;
					} catch (Exception e) {
						e.printStackTrace();
					}

				}
				
				for(ConsumerRecord<Long, String> record : consumerRecords) {
					System.out.printf("Consumer Record:(%d, %s, %d, %d)\n", record.key(), record.value(),
							record.partition(), record.offset());
//					entityList.add(record.value());
					if (StringUtils.isNotBlank(record.value())) {
						
						try {
							save(record.value());
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
				
				consumer.commitAsync();
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		
	}
	
	private void save(String outputLine) throws Exception{
		
		InternalLinkNodeStatusVO vo = new Gson().fromJson(outputLine, InternalLinkNodeStatusVO.class);
		
		internalLinkNodeStatusDAO.insert(vo, outputLine);
		
	}
	
}
