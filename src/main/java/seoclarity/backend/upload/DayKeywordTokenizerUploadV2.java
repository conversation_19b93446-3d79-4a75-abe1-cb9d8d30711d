package seoclarity.backend.upload;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.dao.actonia.ResourceSyncInfoEntityDAO;
import seoclarity.backend.dao.clickhouse.kp207.DisManagedSearchvolumeDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsExpandedEntityNewDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsExpandedLogDAO;
import seoclarity.backend.entity.actonia.ResourceSyncInfoEntity;
import seoclarity.backend.entity.KeywordStreamSearchengineCountryMappingEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.DisManagedSearchvolume;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsExpandedEntityNew;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.upload
 * @author: cil
 * @date: 2021-06-17 15:51
 **/
@Deprecated
public class DayKeywordTokenizerUploadV2 {
	/*
    private static final int RESOURCE_TYPE = 603;
    public static final int KEYWORD_TYPE_NATIONAL = 1; // cityId = 0
    public static final int KEYWORD_TYPE_GEO = 2; // cityId > 0
    private static final long MIN_ID = 132223000; // default start log id

    private static final String DELIMITER = "!_!";
    private static final SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyyMMdd");

    private static final int INSERT_SIZE = 2000;
    private static final int SKIP_SIZE = 5000;
    private static final int INSERT_LOG_SIZE = 1000000;

    private static Map<String, KeywordStreamSearchengineCountryMappingEntity> countryAndLanguageFullNameMap = null;
    
    private Map<Integer, Map<Integer, Integer>> monthMap = new HashMap<Integer, Map<Integer, Integer>>();

    private KeywordAdwordsExpandedEntityNewDAO keywordAdwordsExpandedEntityNewDAO;
    private KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;
    private DisManagedSearchvolumeDAO disManagedSearchvolumeDAO;
    private ResourceSyncInfoEntityDAO resourceSyncInfoEntityDAO;
    private KeywordAdwordsExpandedLogDAO keywordAdwordsExpandedLogDAO;

    public DayKeywordTokenizerUploadV2() {
        resourceSyncInfoEntityDAO = SpringBeanFactory.getBean("resourceSyncInfoEntityDAO");
        keywordAdwordsExpandedEntityNewDAO = SpringBeanFactory.getBean("keywordAdwordsExpandedEntityNewDAO");
        disManagedSearchvolumeDAO = SpringBeanFactory.getBean("disManagedSearchvolumeDAO");
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");
        keywordAdwordsExpandedLogDAO = SpringBeanFactory.getBean("keywordAdwordsExpandedLogDAO");
    }

    public static void main(String[] args) {
        // init countryAndLanguageFullNameMap
        DayKeywordTokenizerUploadV2 ins = new DayKeywordTokenizerUploadV2();
        List<KeywordStreamSearchengineCountryMappingEntity> countryAndLanguageFullName = ins.getCountryAndLanguageFullName();
        countryAndLanguageFullNameMap = countryAndLanguageFullName.stream().collect(Collectors.toMap(v1 -> String.join(DELIMITER, v1.getEngineId() + "", v1.getLanguageId() + ""), v2 -> v2, (v_old, v_new) -> v_new));

        Date date = new Date();
        int logDate = Integer.valueOf(YYYYMMDD.format(date));
        Long maxId = ins.getMaxId();
        Long pocMinId = ins.getMinId();
        System.out.println("===logDate:" + logDate + " logIdRange:" + pocMinId + "-" + maxId +
                " clfMap:" + countryAndLanguageFullNameMap.size() + " clfList:" + countryAndLanguageFullName.size());

        ResourceSyncInfoEntity resSyncInfoEntity = constructResourceSyncInfoEntity(date);
        ins.process(KEYWORD_TYPE_NATIONAL, maxId, pocMinId, logDate, resSyncInfoEntity);
        ins.process(KEYWORD_TYPE_GEO, maxId, pocMinId, logDate, resSyncInfoEntity);
        System.out.println("Finish upload");
    }

    private List<KeywordAdwordsExpandedEntityNew> findToday(long minId, long maxId, int keywordType) {
        try {
            return keywordAdwordsExpandedEntityNewDAO.findToday(minId, maxId, keywordType);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList<>();
    }

    private List<KeywordStreamSearchengineCountryMappingEntity> getCountryAndLanguageFullName() {
        try {
            return keywordStreamSearchengineCountryMappingEntityDAO.findAllEnabledEngineCountryMapping();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList<>();
    }

    private void process(int keywordType, Long pocMaxId, Long pocMinId, int logDate, ResourceSyncInfoEntity syncEntity) {
        List<DisManagedSearchvolume> insertList = new LinkedList<>();
        long currentTimeMillis = System.currentTimeMillis();
        int insertCount = 0;
        long oldId = pocMinId;
        int logCnt = 0;
        long thisTurnMaxId;
        List<KeywordAdwordsExpandedEntityNew> entityList;
        if (pocMaxId < 1) {
            System.out.println("today no log !");
            return;
        }
        do {
            thisTurnMaxId = pocMinId + SKIP_SIZE;
            entityList = findToday(pocMinId, thisTurnMaxId, keywordType);
            System.out.println("process maxID:" + thisTurnMaxId + " entityList:" + entityList.size());
            logCnt += entityList.size();

            Map<String, HashSet<Integer>> keywordAdwordsExpandedMap = new HashMap<>();
            for (KeywordAdwordsExpandedEntityNew entityNew : entityList) {
                // decode
                try {
                    entityNew.setKeyword_name(StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(entityNew.getKeyword_name(), "UTF-8"))));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (entityNew.getKeyword_name().contains("\t")) {
                    System.out.println("process skip word:" + entityNew.getKeyword_name() + "  id = " + entityNew.getKeywordId());
                    continue;
                }
                StringBuffer sb = new StringBuffer();
                sb.append(entityNew.getSearchEngineId()).append("-").append(entityNew.getLanguageId())
                        .append("-").append(entityNew.getLocationId());
                String key = sb.toString();
                if (keywordAdwordsExpandedMap.get(key) == null) {
                    keywordAdwordsExpandedMap.put(key, new HashSet<>());
                }
                keywordAdwordsExpandedMap.get(key).add(entityNew.getKeywordId());
            }
            // compare with database
            if (keywordAdwordsExpandedMap.size() < 1) {
                pocMinId = thisTurnMaxId;
                continue;
            }
            List<DisManagedSearchvolume> existDis = queryExist(keywordAdwordsExpandedMap);
            Map<String, DisManagedSearchvolume> keyMap = existDis.stream().
                    collect(Collectors.toMap(v1 -> String.join(DELIMITER, v1.getEngine_id() + "", v1.getLanguage_id() + "",
                            v1.getLocation_id() + "", v1.getKeyword_rankcheck_id() + ""), v2 -> v2));
            entityList = entityList.stream().filter(e -> !keyMap.containsKey(String.join(DELIMITER, e.getSearchEngineId() + "",
                    e.getLanguageId() + "", e.getLocationId() + "", e.getKeywordId() + ""))).collect(Collectors.toList());

            if (entityList.size() < 1) {
                pocMinId = thisTurnMaxId;
                continue;
            }
            // put to map
            Map<String, List<KeywordAdwordsExpandedEntityNew>> keyWordMap = new HashMap(128);
            for (KeywordAdwordsExpandedEntityNew entityNew : entityList) {
                StringBuffer sb = new StringBuffer();
                sb.append(entityNew.getSearchEngineId()).append(DELIMITER).append(entityNew.getLanguageId())
                        .append(DELIMITER).append(entityNew.getLocationId()).append(DELIMITER).append(entityNew.getKeywordId()).append(DELIMITER).append(entityNew.getKeyword_name());
                String key = sb.toString();
                if (keyWordMap.get(key) != null && keyWordMap.get(key).size() >= 1) {
                    keyWordMap.get(key).add(entityNew);
                } else {
                    List entityNewList = new LinkedList();
                    if (entityNew.getMonth() != null) {
                        entityNewList.add(entityNew);
                    }
                    keyWordMap.put(key, entityNewList);
                }
            }
            // if map is null, continue
            if (keyWordMap.entrySet() == null) {
                System.out.println("process keyWordMap.entrySet():null");
                pocMinId = thisTurnMaxId;
                continue;
            }
            // loop map
            for (Map.Entry<String, List<KeywordAdwordsExpandedEntityNew>> entry : keyWordMap.entrySet()) {
                DisManagedSearchvolume entity;
                if (entry.getValue() != null && entry.getValue().size() >= 1) {
                    entity = getDisManagedSearchvolume(entry.getValue());
                } else {
                    entity = getDisManagedSearchvolume(entry.getKey());
                }
                if (entity == null || entity.getMonthlySvAttr_value().length <= 0) {
                    continue;
                }
                KeywordStreamSearchengineCountryMappingEntity keywordStreamSearchengineCountryMappingEntity = countryAndLanguageFullNameMap.get(String.join(DELIMITER, entity.getEngine_id() + "", entity.getLanguage_id() + ""));
                if (keywordStreamSearchengineCountryMappingEntity == null) {
                    continue;
                }
                getWord(entity, keywordStreamSearchengineCountryMappingEntity.getLanguageFullName(), keywordStreamSearchengineCountryMappingEntity.getCountryCode());
                insertList.add(entity);
            }

            if (insertList.size() >= INSERT_SIZE) {
                insert(insertList, 0);
                insertCount += insertList.size();
                System.out.println("insertList:" + insertList.size() + " logCnt:" + logCnt + " insertCount:" + insertCount);
                if (insertCount >= INSERT_LOG_SIZE) {
                    insertLog(logDate, oldId, thisTurnMaxId, logCnt, insertCount);
                    oldId = thisTurnMaxId + 1;
                    logCnt = 0;
                    insertCount = 0;
                }
                insertList = new LinkedList<>();
            }
            System.out.println(" cost time: " + (System.currentTimeMillis() - currentTimeMillis) / 1000 +
                    "s \r\nthisTurnMaxId:" + thisTurnMaxId + "   pocMaxId: " + pocMaxId + " pocMinId: " + pocMinId);
            pocMinId = thisTurnMaxId;
        } while (thisTurnMaxId < pocMaxId);
        if (insertList.size() > 0) {
            insert(insertList, 0);
        }
        syncEntity.setFromLogId(Long.min(syncEntity.getFromLogId(), oldId));
        syncEntity.setToLogId(Long.max(syncEntity.getToLogId(), pocMaxId));
        syncEntity.setLogCnt(syncEntity.getLogCnt() + logCnt);
        syncEntity.setLoadedCnt(syncEntity.getLoadedCnt() + insertList.size());
        writeLog(syncEntity);
    }

    private DisManagedSearchvolume getDisManagedSearchvolume(String str) {
        String[] split = str.split(DELIMITER);
        String keywordName = split[4];
        int locationId = Integer.valueOf(split[2]);
        int keywordId = Integer.valueOf(split[3]);
        int searchEngineId = Integer.valueOf(split[0]);
        int languageId = Integer.valueOf(split[1]);

        DisManagedSearchvolume entity = new DisManagedSearchvolume();
        entity.setKeyword_name(keywordName);
        entity.setHas_sv(0);
        entity.setCpc(0f);
        entity.setAvg_search_volume(0);
        entity.setLocation_id(locationId);
        entity.setKeyword_rankcheck_id(keywordId);
        entity.setSign(1);
        entity.setVersioning(0);
        entity.setEngine_id(searchEngineId);
        entity.setLanguage_id(languageId);

        entity.setMonthly_search_volume1(0);
        entity.setMonthly_search_volume2(0);
        entity.setMonthly_search_volume3(0);
        entity.setMonthly_search_volume4(0);
        entity.setMonthly_search_volume5(0);
        entity.setMonthly_search_volume6(0);
        entity.setMonthly_search_volume7(0);
        entity.setMonthly_search_volume8(0);
        entity.setMonthly_search_volume9(0);
        entity.setMonthly_search_volume10(0);
        entity.setMonthly_search_volume11(0);
        entity.setMonthly_search_volume12(0);
        List<Integer> monthlySvAttr_key = new LinkedList<>();
        monthlySvAttr_key.add(0);
        entity.setMonthlySvAttr_key(monthlySvAttr_key);
        entity.setMonthlySvAttr_value(monthlySvAttr_key);
        entity.setLastRefreshMonth(0);
        entity.setCategory(monthlySvAttr_key);
        return entity;
    }

    private DisManagedSearchvolume getDisManagedSearchvolume(List<KeywordAdwordsExpandedEntityNew> adwird) {
        if (adwird == null || adwird.size() < 1) {
            return null;
        }
        int size = adwird.size();
        Collections.sort(adwird);
        DisManagedSearchvolume entity = new DisManagedSearchvolume();

        KeywordAdwordsExpandedEntityNew lastOne = adwird.get(adwird.size() - 1);
        entity.setHas_sv(1);
        entity.setCpc(lastOne.getCostPerClick() == null ? 0 : lastOne.getCostPerClick());
        entity.setKeyword_name(lastOne.getKeyword_name());
        entity.setAvg_search_volume(lastOne.getAvgMonthlySearchVolume());
        entity.setLocation_id(lastOne.getLocationId());
        entity.setKeyword_rankcheck_id(lastOne.getKeywordId());
        entity.setSign(1);
        entity.setVersioning(0);
        entity.setEngine_id(lastOne.getSearchEngineId());
        entity.setLanguage_id(lastOne.getLanguageId());

//        entity.setMonthly_search_volume1(size > 0 ? adwird.get(0).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume2(size > 1 ? adwird.get(1).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume3(size > 2 ? adwird.get(2).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume4(size > 3 ? adwird.get(3).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume5(size > 4 ? adwird.get(4).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume6(size > 5 ? adwird.get(5).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume7(size > 6 ? adwird.get(6).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume8(size > 7 ? adwird.get(7).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume9(size > 8 ? adwird.get(8).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume10(size > 9 ? adwird.get(9).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume11(size > 10 ? adwird.get(10).getMonthlySearchVolume() : 0);
//        entity.setMonthly_search_volume12(size > 11 ? adwird.get(11).getMonthlySearchVolume() : 0);
        
        AllKeywordTokenizerUploadV2.setMonthlySV(adwird.get(size - 1).getMonth(), adwird, entity); // https://www.wrike.com/open.htm?id=969374712

        List<Integer> monthlySvAttr_key = new LinkedList<>();
        List<Integer> monthlySvAttr_value = new LinkedList<>();
        for (KeywordAdwordsExpandedEntityNew adw : adwird) {
            monthlySvAttr_key.add(adw.getMonth() == null ? 0 : adw.getMonth());
            monthlySvAttr_value.add(adw.getMonthlySearchVolume() == null ? 0 : adw.getMonthlySearchVolume());
        }

        entity.setMonthlySvAttr_key(monthlySvAttr_key);
        if (monthlySvAttr_key.size() == 0) {
            monthlySvAttr_key.add(0);
            entity.setMonthlySvAttr_key(monthlySvAttr_key);
        }
        entity.setMonthlySvAttr_value(monthlySvAttr_value);
        if (monthlySvAttr_value.size() == 0) {
            monthlySvAttr_value.add(0);
            entity.setMonthlySvAttr_key(monthlySvAttr_value);
        }

        entity.setLastRefreshMonth(adwird.get(adwird.size() - 1).getMonth());
        if (lastOne.getCategory() != null) {
            List<String> result = Arrays.asList(lastOne.getCategory().split("#"));
            List<Integer> codesInteger = result.stream().filter(s -> !"".equals(s)).map(Integer::parseInt).collect(Collectors.toList());
            entity.setCategory(codesInteger);
        }
        return entity;
    }

    private List<DisManagedSearchvolume> queryExist(Map<String, HashSet<Integer>> keywordAdwordsExpandedMap) {
        if (keywordAdwordsExpandedMap.size() < 1) {
            return null;
        }
        return disManagedSearchvolumeDAO.queryExist(keywordAdwordsExpandedMap);
    }

    private long getMaxId() {
        Long maxId = keywordAdwordsExpandedLogDAO.getMaxLogId();
        return maxId == null ? 0L : maxId;
    }

    private void writeLog(ResourceSyncInfoEntity syncEntity) {
        Date date = new Date();
        syncEntity.setCreateDate(date);
        syncEntity.setProcessEndDate(date);
        System.out.println("writeLog fromLogId: " + syncEntity.getFromLogId() + " toLogId: " + syncEntity.getToLogId());
        resourceSyncInfoEntityDAO.insert(syncEntity);
    }

    private long getMinId() {
        Long id = resourceSyncInfoEntityDAO.getMaxLogIdByResourceType(RESOURCE_TYPE);
        return id == null || id == 0 ? MIN_ID : id + 1;   //need add 1
    }

    private void getWord(DisManagedSearchvolume entity, String languageName, String countryCode) {
        List<String> word = new ArrayList<String>();
        List<String> stream = new ArrayList<String>();
        List<String> keywordVariationOneword = new ArrayList<String>();
        List<String> keywordVariationNgram = new ArrayList<String>();

        if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(entity.getKeyword_name(), "ar"));
        } else {
            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(entity.getKeyword_name(), countryCode));
        }
        stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(entity.getKeyword_name(), languageName));
        keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(entity.getKeyword_name(), true));
        keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(entity.getKeyword_name(), false));

        entity.setWord(word);
        entity.setStream(stream);
        entity.setKeyword_variation_oneword(keywordVariationOneword);
        entity.setKeyword_variation_ngram(setPlaceHolder(keywordVariationNgram));
    }

    public static List<String> setPlaceHolder(List<String> array) {
        String holder = "_";
        if (array.size() <= 2) {
            return array;
        }
        List<String> result = new ArrayList<String>();
        String startWordStr = array.get(0);
        String endWordStr = array.get(array.size() - 1);

        for (int i = 0; i < array.size(); i++) {
            String words = array.get(i);
            if ((StringUtils.startsWith(startWordStr, words) || StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
                words = words + " " + holder;
                result.add(words);
                continue;
            }
            if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
                words = holder + " " + words + " " + holder;
                result.add(words);
                continue;
            }
            if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (StringUtils.endsWith(endWordStr, words) || StringUtils.endsWith(words, endWordStr))) {
                words = holder + " " + words;
                result.add(words);
                continue;
            }
            result.add(words);
        }
        return result;
    }

    private int[] insert(List<DisManagedSearchvolume> list, int flag) {
        while (true) {
            try {
                return disManagedSearchvolumeDAO.insertBatch(list);
            } catch (Exception exception) {
                exception.printStackTrace();
                AllKeywordTokenizerUploadV2.write(list);
                System.out.println(Thread.currentThread().getName() + " " + flag + " times try");
                try {
                    Thread.sleep(30000);
                    flag++;
                    if (flag > 1000)
                        break;
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    private static ResourceSyncInfoEntity constructResourceSyncInfoEntity(Date date) {
        int logDate = Integer.valueOf(YYYYMMDD.format(date));
        ResourceSyncInfoEntity entity = new ResourceSyncInfoEntity();
        entity.setResourceType(RESOURCE_TYPE);
        entity.setResourceAddDate(logDate);
        entity.setFromLogId(Long.MAX_VALUE);
        entity.setToLogId(0);
        entity.setStatus(2);
        entity.setLogCnt(0);
        entity.setLoadedCnt(0);
        entity.setProcessEndDate(date);
        entity.setCreateDate(date);
        return entity;
    }

    private void insertLog(int logDate, Long fromId, Long toLogId, int logCnt, int loadedCnt) {
        ResourceSyncInfoEntity entity = new ResourceSyncInfoEntity();
        entity.setResourceType(RESOURCE_TYPE);
        entity.setResourceAddDate(logDate);
        entity.setFromLogId(fromId);
        entity.setToLogId(toLogId);
        entity.setStatus(2);
        entity.setLogCnt(logCnt);
        entity.setLoadedCnt(loadedCnt);
        Date date = new Date();
        entity.setProcessEndDate(date);
        entity.setCreateDate(date);

        writeLog(entity);
    }*/
}