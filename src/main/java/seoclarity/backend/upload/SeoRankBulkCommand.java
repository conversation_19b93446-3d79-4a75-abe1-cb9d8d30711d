package seoclarity.backend.upload;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.google.common.net.InternetDomainName;

import ru.yandex.clickhouse.ClickHouseArray;
import ru.yandex.clickhouse.domain.ClickHouseDataType;
import seoclarity.backend.dao.actonia.RankingDailyMonitorEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.KeywordSubRankEntityVO;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.utils.ClarityDBConnection;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public class SeoRankBulkCommand extends BaseThreadCommand {
	private final String INSERT_KWD_INFO_SQL = "insert into seo_daily_ranking.{0} (keyword_name ,own_domain_id ,keyword_rankcheck_id,engine_id, language_id ,location_id,questions ,answerbox_url ,ll_flg, pla_flg ,ppc_flg ,top_ppc_cnt ,ranking_date , avg_search_volume , cpc ,knog_flg ,count_of_search ,sign ) values (?, ?, ?, ?, ?,? ,? , ? ,? ,? ,? ,? ,? ,? ,? ,?, ?, ?)";
	private final String INSERT_KWD_DETAIL_SQL = "insert into seo_daily_ranking.{0} (keyword_name ,own_domain_id ,keyword_rankcheck_id,engine_id, language_id ,location_id,domain_reverse ,root_domain_reverse  ,uri ,url, protocol ,true_rank ,web_rank ,type ,avg_search_volume ,cpc ,hrd ,hrrd ,ranking_date ,rating ,wtd_vol_tr ,est_traffic_tr ,wtd_vol_wr ,est_traffic_wr, folder_level1, folder_level2, folder_level3,sign , attrs.key,attrs.value, label ,meta , lable_len ,meta_len ) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	private final String INSERT_KWD_SUBRANK_SQL = "insert into seo_daily_ranking.{0} (keyword_name ,own_domain_id ,keyword_rankcheck_id,engine_id, language_id ,location_id,ranking_date, domain_reverse ,uri ,url,protocol ,sub_rank ,rank ,ranking_type, sign, url_type,root_domain_reverse, avg_search_volume ) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?, ?,?,?)";

	private String ipAddress;

	private KeywordRankVO[] rankData;

	private int searchEngine;

	private int language;

	private boolean mobileRanking;

	private boolean hotCluster;

	// private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;

	private RankingDailyMonitorEntityDAO rankingDailyMonitorEntityDAO;

	private Connection connection;

	private boolean checkKeywordExistsInMonitorWithSendToQDate = false;

	List<String> attrsGroup = new ArrayList<String>();

	public static final String MONITOR_TABLE_PREFIX = "daily_ranking_imp_monitor_";
	public static final String MONITOR_TABLE_HOT = "hot_";
	public static final String MONITOR_TABLE_COLD = "col_";
	public static final int TYPE_REGULAR = 1;
	public static final int TYPE_MOBILE = 2;

	public SeoRankBulkCommand(String ipAddress, KeywordRankVO[] rankData, int searchEngine, int language,
							  boolean mobileRanking, boolean hotCluster, Connection connection) {
		super();

		this.ipAddress = ipAddress;
		this.rankData = rankData;
		this.searchEngine = searchEngine;
		this.language = language;
		this.mobileRanking = mobileRanking;
		this.hotCluster = hotCluster;
		// this.seoClarityKeywordEntityDAO =
		// SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		rankingDailyMonitorEntityDAO = SpringBeanFactory.getBean("rankingDailyMonitorEntityDAO");

		attrsGroup.add("answerbox_flg");
		attrsGroup.add("ll_flg");
		attrsGroup.add("pla_flg");
		attrsGroup.add("ppc_flg");
		attrsGroup.add("peoplealsoask_flg");
		attrsGroup.add("app_flg");
		attrsGroup.add("apm_flg");
		attrsGroup.add("img_flg");
		attrsGroup.add("news_flg");
		attrsGroup.add("video_flg");
		attrsGroup.add("is_apm");
		attrsGroup.add("price_flg");
		attrsGroup.add("stock_flg");
		attrsGroup.add("is_price");
		attrsGroup.add("is_stock");
		attrsGroup.add("ratingnumber");
		attrsGroup.add("job_link");
		attrsGroup.add("apple_category");
	}

	private String getMonitorTableName() {
		String date = DateFormatUtils.format(CacheModleFactory.getInstance().getRankingDate(), "yyyyMMdd");
		String rankingTableName = MONITOR_TABLE_PREFIX + (hotCluster ? MONITOR_TABLE_HOT : MONITOR_TABLE_COLD)
				+ date;
		return rankingTableName;
	}

	// by Meo
	// use the keyword sendToQDate as the current keyword ranking date
	private boolean isKeywordExistsInMonitorBySendToQDate(KeywordRankVO keywordRankVO, int ownDomainId) {
		int type = mobileRanking ? TYPE_MOBILE : TYPE_REGULAR;
		String rankingTableName = null;
		try {
			if (keywordRankVO == null || keywordRankVO.getSendToQDate() == null || keywordRankVO.getSendToQDate() <= 0) {
				System.out.println("Wrong!!! Wrong sendToQDate. kName: " + keywordRankVO.getKeyword() + " , engine: "
						+ searchEngine + " , language: " + language  + ", mobileRanking:" + mobileRanking + ", type:" + type
						+ ", ownDomainId:" + ownDomainId + ", sendToDate:" + keywordRankVO.getSendToQDate());
				return true;
			}
			Integer sendToDate = keywordRankVO.getSendToQDate();
			String sendToQDate2 =  DateFormatUtils.format(keywordRankVO.getSendToQDateAsDate(), "yyyyMMdd");

			if (!StringUtils.equals(sendToQDate2, String.valueOf(sendToDate))) {
				System.out.println("Wrong!!! Wrong sendToQDate. kName: " + keywordRankVO.getKeyword() + " , engine: "
						+ searchEngine + " , language: " + language  + ", mobileRanking:" + mobileRanking + ", type:" + type
						+ ", ownDomainId:" + ownDomainId + ", sendToDate:" + sendToDate);
				return  true;
			}

			rankingTableName = MONITOR_TABLE_PREFIX + (hotCluster ? MONITOR_TABLE_HOT : MONITOR_TABLE_COLD)
					+ sendToDate.toString();
		} catch (Exception e) {
			System.out.println("Wrong!!! Check Kw Exists Failed. kName: " + keywordRankVO.getKeyword() + " , engine: "
					+ searchEngine + " , language: " + language  + ", mobileRanking:" + mobileRanking + ", type:" + type
					+ ", ownDomainId:" + ownDomainId + ", sendToDate:" + keywordRankVO.getSendToQDate());
			e.printStackTrace();
			return true;
		}
		return isKeywordExistsInMonitor(keywordRankVO, rankingTableName, ownDomainId);
	}

	// by Meo
	private boolean isKeywordExistsInMonitor(KeywordRankVO keywordRankVO, String tableName, int ownDomainId) {
		try {
			Integer kid = keywordRankVO.getId();
			if (kid == null){
				return false;
			}
			int type = mobileRanking ? TYPE_MOBILE : TYPE_REGULAR;
			// check exists
			int cityId = keywordRankVO.getCityId() == null ? 0 : keywordRankVO.getCityId();
			int frequency = 1;
			boolean isExists = rankingDailyMonitorEntityDAO.keywordExists(tableName, kid, searchEngine, language, cityId, frequency, type, ownDomainId);
			if (isExists) {
				System.out.println("== existsing alert, kid:" + keywordRankVO.getId() + ", kName: " + keywordRankVO.getKeyword() + " , engine: "
						+ searchEngine + " , language: " + language + ", mobileRanking:" + mobileRanking + ", type:" + type + " ,ownDomainId: " + ownDomainId
						+ (checkKeywordExistsInMonitorWithSendToQDate ? (", sendToQueueDate:" + keywordRankVO.getSendToQDate()) : ""));
			} else {
				try {
					rankingDailyMonitorEntityDAO.insert(tableName, kid, searchEngine, language, cityId, frequency, type, ownDomainId);
				} catch (Exception e) {
					System.out.println(tableName+"== EXCEPTION kName: " + keywordRankVO.getKeyword() + " , engine: "
							+ searchEngine + " , language: " + language  + ", mobileRanking:" + mobileRanking + ", type:" + type+ ", ownDomainId:" + ownDomainId);
					e.printStackTrace();
					return true;
				}
			}
			return isExists;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	@Override
	protected void execute() throws Exception {
		long a = System.currentTimeMillis();
		insertData();
		long b = System.currentTimeMillis();
		System.out.println("End command IP: " + ipAddress + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
		CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
	}

	private void insertData() throws Exception {
		String rankingDate = DateFormatUtils.format(CacheModleFactory.getInstance().getRankingDate(), "yyyy-MM-dd");
		connection = ClarityDBConnection.getDBConnection("************:8123");
		if (hotCluster) {
			connection = null;
		}
		PreparedStatement keywordLevelStatement = null;
		PreparedStatement keywordDetailStatement = null;
		PreparedStatement keywordSubRankStatement = null;

		try {
			keywordLevelStatement = connection.prepareStatement(
					StringUtils.replace(INSERT_KWD_INFO_SQL, "{0}", getKeyowrdInfoTableName(searchEngine, language)));
			keywordDetailStatement = connection.prepareStatement(StringUtils.replace(INSERT_KWD_DETAIL_SQL, "{0}",
					getKeyowrdDetailTableName(searchEngine, language)));
			keywordSubRankStatement = connection.prepareStatement(StringUtils.replace(INSERT_KWD_SUBRANK_SQL, "{0}",
					getKeyowrdSubRankTableName(searchEngine, language)));

			String monitorTableName = getMonitorTableName();

			for (KeywordRankVO keywordRankVO : rankData) {
				parerLineForChild(keywordRankVO, rankingDate, keywordLevelStatement, keywordDetailStatement,
						keywordSubRankStatement, monitorTableName);
			}

			keywordLevelStatement.executeBatch();
			keywordDetailStatement.executeBatch();
			keywordSubRankStatement.executeBatch();
		} catch (SQLException e1) {
			e1.printStackTrace();
		} finally {
			if (keywordLevelStatement != null) {
				keywordLevelStatement.close();
			}
			if (keywordDetailStatement != null) {
				keywordDetailStatement.close();
			}
			if (keywordSubRankStatement != null) {
				keywordSubRankStatement.close();
			}
			if (connection != null) {
				ClarityDBConnection.closeConnection(connection);
			}
		}
	}

	private void parerLineForChild(KeywordRankVO keywordRankVO, String date, PreparedStatement keywordLevelStatement,
								   PreparedStatement keywordDetailStatement, PreparedStatement keywordSubRankStatement, String monitorTableName) {
		int kId = 0;
		if (keywordRankVO.getId() == null) {
			// SeoClarityKeywordEntity keywordEntity =
			// seoClarityKeywordEntityDAO.getByKeyword(keywordRankVO.getKeyword());
			// if (keywordEntity != null) {
			// kId = keywordEntity.getId();
			// }
		} else {
			kId = keywordRankVO.getId();
		}

		if (kId == 0) {
			System.out.println("@@@kId can't be null, engine: " + searchEngine + " ,language: " + language);
			return;
		}
		if (keywordRankVO.getDomainList() == null || keywordRankVO.getDomainList().size() == 0) {
			System.out.println("@@@ownDomainId can't be null, engine: " + searchEngine + " ,language: " + language);
			return;
		}


		boolean hasPeopleAlsoAsk = false;
		boolean hasAnswerBox = false;
		boolean hasLL = false;
		boolean hasPla = false;
		boolean hasPpc = false;
		boolean hasApp = false;
		boolean hasAmp = false;
		boolean hasImg = false;
		boolean hasNews = false;
		boolean hasVideo = false;
		boolean hasPrice = false;
		boolean hasStock = false;

		if(StringUtils.isNotBlank(keywordRankVO.getQuestions())) {
			hasPeopleAlsoAsk = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getAnswerBox())) {
			hasAnswerBox = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getLlFlg()) && StringUtils.equalsIgnoreCase(keywordRankVO.getLlFlg(), "y")) {
			hasLL = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getPlaFlg()) && StringUtils.equalsIgnoreCase(keywordRankVO.getPlaFlg(), "y")) {
			hasPla = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getPpcFlg()) && StringUtils.equalsIgnoreCase(keywordRankVO.getPpcFlg(), "y")) {
			hasPpc = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getAppFlg()) && StringUtils.equalsIgnoreCase(keywordRankVO.getAppFlg(), "y")) {
			hasApp = true;
		}

		if (keywordRankVO.getDomainList() != null && keywordRankVO.getDomainList().size() > 0) {
			for (String domainId : keywordRankVO.getDomainList()) {
				// by Meo
				if (!checkKeywordExistsInMonitorWithSendToQDate) {
					if (isKeywordExistsInMonitor(keywordRankVO, monitorTableName, NumberUtils.toInt(domainId))) {
						continue;
					}
				} else {
					if (isKeywordExistsInMonitorBySendToQDate(keywordRankVO, NumberUtils.toInt(domainId))) {
						continue;
					}
				}


				try {
					int index = 1;
					keywordLevelStatement.setString(index++, StringUtils.lowerCase(keywordRankVO.getKeywordForHtml()));
					keywordLevelStatement.setInt(index++, NumberUtils.toInt(domainId));
					keywordLevelStatement.setInt(index++, kId);
					keywordLevelStatement.setInt(index++, searchEngine);
					keywordLevelStatement.setInt(index++, language);
					keywordLevelStatement.setInt(index++, keywordRankVO.getCityId());
					keywordLevelStatement.setString(index++, keywordRankVO.getQuestions());
					keywordLevelStatement.setString(index++, keywordRankVO.getAnswerBox());
					keywordLevelStatement.setInt(index++, convertValue(keywordRankVO.getLlFlg()));
					keywordLevelStatement.setInt(index++, convertValue(keywordRankVO.getPlaFlg()));
					keywordLevelStatement.setInt(index++, convertValue(keywordRankVO.getPpcFlg()));
					keywordLevelStatement.setInt(index++, keywordRankVO.getTopPPCCnt());
					if (!checkKeywordExistsInMonitorWithSendToQDate) {
						keywordLevelStatement.setDate(index++,
								new Date(CacheModleFactory.getInstance().getRankingDate().getTime()));
					} else {
						keywordLevelStatement.setDate(index++, new Date(keywordRankVO.getSendToQDateAsDate().getTime()));
					}
					keywordLevelStatement.setLong(index++, Long.parseLong(keywordRankVO.getSearchVol()));
					keywordLevelStatement.setFloat(index++, Float.parseFloat(keywordRankVO.getCpc()));
					keywordLevelStatement.setInt(index++, convertValue(keywordRankVO.getKnogTag()));
					keywordLevelStatement.setInt(index++, 0);
					keywordLevelStatement.setInt(index++, 1);

					keywordLevelStatement.addBatch();

					Set<String> uniqueDomainSet = new HashSet<String>();
					Set<String> uniqueDomainIncludeSDomainSet = new HashSet<String>();
					int webRankingCount = 0;


					for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
						if(keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_IMGAGE) {
							hasImg = true;
						}
						if(keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_NEWS) {
							hasNews = true;
						}
						if(keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_VIDEO) {
							hasVideo = true;
						}
						if(StringUtils.isNotBlank(keywordRankEntityVO.getAmpFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getAmpFlg(), "y")) {
							hasAmp = true;
						}
						if (StringUtils.isNotBlank(keywordRankEntityVO.getStockFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getStockFlg(),"y")) {
							hasStock = true;
						}
						if (StringUtils.isNotBlank(keywordRankEntityVO.getPriceFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getPriceFlg(),"y")) {
							hasPrice = true;
						}
					}


					for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
						if (keywordRankEntityVO.getRank() > 100) {
							continue;
						}
						index = 1;

						String url = keywordRankEntityVO.getLandingPage();
						if (StringUtils.equalsIgnoreCase(url, "#") || StringUtils.equalsIgnoreCase(url, ";")) {
							url = "http://instantanswers.google.com/";
						}
						//
						String[] domainUrlList = CommonUtils.splitString(url);
						if (domainUrlList == null) {
							continue;
						}

						// domain
						String domainName = domainUrlList[0];
						// url
						String uriPattern = domainUrlList[1];
						// protocol
						String protocol = domainUrlList[2];

						// add start
						boolean urlhaschanged = false;
						String convertedUrl = null;
						String[] reprocessUri = CommonUtils.getDomainReverseAndUriFromUrl(uriPattern, domainName);
						if (reprocessUri != null && reprocessUri.length >= 2) {
							convertedUrl = CommonUtils.getConvertUrl(uriPattern, domainName);
							domainName = reprocessUri[0];
							uriPattern = reprocessUri[1];
							urlhaschanged = true;
							if (StringUtils.containsIgnoreCase(domainName, "com.googleusercontent.webcache")) {
								String[] reprocessUri2 = CommonUtils.getDomainReverseAndUriFromUrl(uriPattern);
								if (reprocessUri2 != null && reprocessUri2.length >= 2) {
									domainName = reprocessUri2[0];
									// if it contain webcache. we have to decode
									// twice
									uriPattern = URLDecoder.decode(URLDecoder.decode(reprocessUri2[1], "utf-8"),
											"utf-8");
								}
							}
						}
						// add end

						// to solve google map issue.
						if (StringUtils.containsIgnoreCase(domainName, ".google.www")
								&& keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_WEB_RESOURCE
								&& StringUtils.startsWithIgnoreCase(uriPattern, "/maps/")) {
							keywordRankEntityVO.setType(KeywordRankEntityVO.TYPE_ADDRESS);
						}

						String newDomainName = "";
						if (StringUtils.containsIgnoreCase(domainName, ".google.www")
								&& keywordRankEntityVO.getType() != KeywordRankEntityVO.TYPE_WEB_RESOURCE) {
							if (keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_ADDRESS) {
								newDomainName = "com.google.maps";
							} else if (keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_IMGAGE) {
								newDomainName = "com.google.images";
							} else if (keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_NEWS) {
								newDomainName = "com.google.news";
							} else if (keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_SHOPPING) {
								newDomainName = "com.google.shopping";
							} else if (keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_VIDEO) {
								newDomainName = "com.google.video";
							} else {
								newDomainName = domainName;
							}
						} else {
							newDomainName = domainName;
						}

						String rootDomain = getRootDomain(newDomainName);

						keywordDetailStatement.setString(index++, StringUtils.lowerCase(keywordRankVO.getKeywordForHtml()));
						keywordDetailStatement.setInt(index++, NumberUtils.toInt(domainId));
						keywordDetailStatement.setInt(index++, kId);
						keywordDetailStatement.setInt(index++, searchEngine);
						keywordDetailStatement.setInt(index++, language);
						keywordDetailStatement.setInt(index++, keywordRankVO.getCityId());
						keywordDetailStatement.setString(index++, newDomainName);
						keywordDetailStatement.setString(index++, rootDomain);
						keywordDetailStatement.setString(index++, uriPattern);
						if(urlhaschanged && convertedUrl !=null) {
							keywordDetailStatement.setString(index++, convertedUrl);
						} else {
							keywordDetailStatement.setString(index++, url);
						}
						keywordDetailStatement.setInt(index++, NumberUtils.toInt(protocol));
						keywordDetailStatement.setInt(index++, keywordRankEntityVO.getRank());

						// universal rank
						boolean isUniversalRank = isUniversalUrl(url, keywordRankEntityVO.getType());
						int webRank = 0;
						if (!isUniversalRank) {
							webRank = keywordRankEntityVO.getRank() - webRankingCount;
						} else {
							webRankingCount++;
						}
						keywordDetailStatement.setInt(index++, webRank);
						keywordDetailStatement.setInt(index++, keywordRankEntityVO.getType());
						keywordDetailStatement.setLong(index++, Long.parseLong(keywordRankVO.getSearchVol()));
						keywordDetailStatement.setFloat(index++, Float.valueOf(keywordRankVO.getCpc()));
						// hrd
						if (uniqueDomainSet.contains(domainName)) {
							keywordDetailStatement.setInt(index++, 0);
						} else {
							uniqueDomainSet.add(domainName);
							keywordDetailStatement.setInt(index++, 1);
						}

						// hrrd (root domain)
						if (uniqueDomainIncludeSDomainSet.contains(rootDomain)) {
							keywordDetailStatement.setInt(index++, 0);
						} else {
							uniqueDomainIncludeSDomainSet.add(rootDomain);
							keywordDetailStatement.setInt(index++, 1);
						}
						if (!checkKeywordExistsInMonitorWithSendToQDate) {
							keywordDetailStatement.setDate(index++,
									new Date(CacheModleFactory.getInstance().getRankingDate().getTime()));
						} else {
							keywordDetailStatement.setDate(index++,
									new Date(keywordRankVO.getSendToQDateAsDate().getTime()));
						}

						keywordDetailStatement.setInt(index++, convertValue(keywordRankEntityVO.getRating()));

						keywordDetailStatement.setLong(index++,
								keywordRankEntityVO.getRank() * NumberUtils.toLong(keywordRankVO.getSearchVol()));
						keywordDetailStatement.setLong(index++,
								getTraffic(NumberUtils.toLong(keywordRankVO.getSearchVol()),
										keywordRankEntityVO.getRank()).longValue());

						keywordDetailStatement.setLong(index++,
								webRank * NumberUtils.toLong(keywordRankVO.getSearchVol()));
						keywordDetailStatement.setLong(index++,
								getTraffic(NumberUtils.toLong(keywordRankVO.getSearchVol()), webRank).longValue());

						HashSet<String> topFolders = getetDirectory(uriPattern);
						if (topFolders != null && topFolders.size() > 0) {
							if (topFolders.size() == 3) {
								for (String folder : topFolders) {
									keywordDetailStatement.setString(index++, folder);
								}
							} else if (topFolders.size() == 2) {
								for (String folder : topFolders) {
									keywordDetailStatement.setString(index++, folder);
								}
								keywordDetailStatement.setString(index++, "");
							} else if (topFolders.size() == 1) {
								for (String folder : topFolders) {
									keywordDetailStatement.setString(index++, folder);
								}
								keywordDetailStatement.setString(index++, "");
								keywordDetailStatement.setString(index++, "");
							}
						} else {
							keywordDetailStatement.setString(index++, "");
							keywordDetailStatement.setString(index++, "");
							keywordDetailStatement.setString(index++, "");
						}

						keywordDetailStatement.setInt(index++, 1);
//begin xxxxyyyy
						keywordDetailStatement.setArray(index++,
								new ClickHouseArray(ClickHouseDataType.String, attrsGroup.toArray(new String[attrsGroup.size()])));
//end xxxxyyyy
						List<String> attrsValue = new ArrayList<String>();
						attrsValue.add(hasAnswerBox ? "1" : "0");
						attrsValue.add(hasLL ? "1" : "0");
						attrsValue.add(hasPla ? "1" : "0");
						attrsValue.add(hasPpc ? "1" : "0");
						attrsValue.add(hasPeopleAlsoAsk ? "1" : "0");
						attrsValue.add(hasApp ? "1" : "0");
						attrsValue.add(hasAmp ? "1" : "0");
						attrsValue.add(hasImg ? "1" : "0");
						attrsValue.add(hasNews ? "1" : "0");
						attrsValue.add(hasVideo ? "1" : "0");
						if(StringUtils.isNotBlank(keywordRankEntityVO.getAmpFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getAmpFlg(), "y")) {
							attrsValue.add("1");
						} else {
							attrsValue.add("0");
						}
						attrsValue.add(hasPrice ? "1" : "0");
						attrsValue.add(hasStock ? "1" : "0");

						if (StringUtils.isNotBlank(keywordRankEntityVO.getPriceFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getPriceFlg(),"y")) {
							attrsValue.add("1");
						} else {
							attrsValue.add("0");
						}
						if (StringUtils.isNotBlank(keywordRankEntityVO.getStockFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getStockFlg(),"y")) {
							attrsValue.add("1");
						} else {
							attrsValue.add("0");
						}
						attrsValue.add(keywordRankEntityVO.getRatingNumber());

						// job url
						if(StringUtils.isNotBlank(keywordRankVO.getJobLink())) {
							String[] jobSources = keywordRankVO.getJobLink().split("@_@");
							if(jobSources.length == 2 && StringUtils.isNotBlank(jobSources[1])) {
								attrsValue.add(jobSources[1]);
							} else {
								attrsValue.add("-");
							}
						} else {
							attrsValue.add("-");
						}

						// apple category
						if (StringUtils.isNotBlank(keywordRankVO.getCategory())) {
							if (StringUtils.equalsIgnoreCase(keywordRankVO.getCategory(), "explore")) {
								attrsValue.add("1");
							} else if (StringUtils.equalsIgnoreCase(keywordRankVO.getCategory(), "accessories")) {
								attrsValue.add("2");
							} else if (StringUtils.equalsIgnoreCase(keywordRankVO.getCategory(), "support")) {
								attrsValue.add("3");
							} else if (StringUtils.equalsIgnoreCase(keywordRankVO.getCategory(), "apples store")) {
								attrsValue.add("4");
							} else {
								attrsValue.add("0");
							}
						} else {
							attrsValue.add("0");
						}
//begin xxxxyyyy
						keywordDetailStatement.setArray(index++,
								new ClickHouseArray(ClickHouseDataType.String, attrsValue.toArray(new String[attrsValue.size()])));
//end xxxxyyyy
						// https://www.wrike.com/open.htm?id=221768860
//						if( keywordRankEntityVO.getRank() <= 20) {
						if((CommonUtils.getDayOfWeek(CacheModleFactory.getInstance().getRankingDate()) == 7
								|| (checkKeywordExistsInMonitorWithSendToQDate && CommonUtils.getDayOfWeek(keywordRankVO.getSendToQDateAsDate()) == 7))) {
							keywordDetailStatement.setString(index++, keywordRankEntityVO.getLabel());
							keywordDetailStatement.setString(index++, keywordRankEntityVO.getMetaDesc());
							keywordDetailStatement.setInt(index++, keywordRankEntityVO.getLabelLen());
							keywordDetailStatement.setInt(index++, keywordRankEntityVO.getMetaDescLen());
						} else {
							keywordDetailStatement.setString(index++, "");
							keywordDetailStatement.setString(index++, "");
							keywordDetailStatement.setInt(index++, 0);
							keywordDetailStatement.setInt(index++, 0);
						}
						keywordDetailStatement.addBatch();

						if (keywordRankEntityVO.getSubRankVOs() != null
								&& keywordRankEntityVO.getSubRankVOs().size() > 0) {

							int subRank = 1;
							for (KeywordSubRankEntityVO subVO : keywordRankEntityVO.getSubRankVOs()) {
								String subUrl = subVO.getLandingPage();
								String[] subdomainUrlList = CommonUtils.splitString(subUrl);

								String subURL = getUrlForSubRankViaDecode(subdomainUrlList[1]);

								// for sub rank's domainReverse, uri and
								// protocol
								if (StringUtils.equals(subURL, "/")) {
									subdomainUrlList[1] = "/";
								} else {
									subdomainUrlList = CommonUtils.splitString(subURL);
								}

								if (subdomainUrlList == null) {
									continue;
								}

								// domain
								domainName = subdomainUrlList[0];
								// url
								uriPattern = subdomainUrlList[1];
								// protocol
								protocol = subdomainUrlList[2];

								// https://www.wrike.com/open.htm?id=39341515
								if (StringUtils.containsIgnoreCase(domainName, ".google.www")
										&& StringUtils.equals(uriPattern, "/")) {
									continue;
								}

								rootDomain = getRootDomain(domainName);

								index = 1;
								keywordSubRankStatement.setString(index++,
										StringUtils.lowerCase(keywordRankVO.getKeywordForHtml()));
								keywordSubRankStatement.setInt(index++, NumberUtils.toInt(domainId));
								keywordSubRankStatement.setInt(index++, kId);
								keywordSubRankStatement.setInt(index++, searchEngine);
								keywordSubRankStatement.setInt(index++, language);
								keywordSubRankStatement.setInt(index++, keywordRankVO.getCityId());
								if (!checkKeywordExistsInMonitorWithSendToQDate) {
									keywordSubRankStatement.setDate(index++,
											new Date(CacheModleFactory.getInstance().getRankingDate().getTime()));
								} else {
									keywordSubRankStatement.setDate(index++,
											new Date(keywordRankVO.getSendToQDateAsDate().getTime()));
								}
								keywordSubRankStatement.setString(index++, domainName);
								keywordSubRankStatement.setString(index++, uriPattern);
								keywordSubRankStatement.setString(index++, subUrl);
								keywordSubRankStatement.setInt(index++, Integer.parseInt(protocol));
								keywordSubRankStatement.setInt(index++, subRank++);
								keywordSubRankStatement.setInt(index++, keywordRankEntityVO.getRank());
								// 0:subrank , 1: locallisting
								keywordSubRankStatement.setInt(index++, 0);
								keywordSubRankStatement.setInt(index++, 1);

								keywordSubRankStatement.setInt(index++, keywordRankEntityVO.getType());
								keywordSubRankStatement.setString(index++, rootDomain);
								keywordSubRankStatement.setLong(index++, Long.parseLong(keywordRankVO.getSearchVol()));

								keywordSubRankStatement.addBatch();
							}

						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	private BigDecimal getTraffic(long searchVolume, int rank) {
		double traffic = 0;
		if (rank == 1) {
			traffic = searchVolume * 0.3;
		} else if (rank == 2) {
			traffic = searchVolume * 0.24;
		} else if (rank == 3) {
			traffic = searchVolume * 0.15;
		} else if (rank == 4) {
			traffic = searchVolume * 0.1;
		} else if (rank == 5) {
			traffic = searchVolume * 0.08;
		} else if (rank == 6) {
			traffic = searchVolume * 0.05;
		} else if (rank == 7) {
			traffic = searchVolume * 0.04;
		} else if (rank == 8) {
			traffic = searchVolume * 0.03;
		} else if (rank == 9) {
			traffic = searchVolume * 0.01;
		} else if (rank == 10) {
			traffic = searchVolume * 0;
		}

		BigDecimal bDec = new BigDecimal(traffic);
		return bDec.setScale(0, BigDecimal.ROUND_HALF_UP);
	}

	public static boolean isUniversalUrl(String url, int urlType) {
		if (RankTypeManager.isWebRank(url, urlType)) {
			return false;
		}
		return true;
	}

//	public static boolean isUniversalUrl(String url, int urlType) {
//		if (StringUtils.isBlank(url)) {
//			return false;
//		}
//		// maps.google.it/
//		if (urlType == KeywordRankEntityVO.TYPE_ADDRESS || StringUtils.containsIgnoreCase(url, "maps.google.com")
//				|| StringUtils.containsIgnoreCase(url, "maps.google.")) {
//			return true;
//		} else if (urlType == KeywordRankEntityVO.TYPE_IMGAGE
//				|| StringUtils.containsIgnoreCase(url, "images.google.com")
//				|| StringUtils.containsIgnoreCase(url, "images.google.")) {
//			return true;
//		} else if (urlType == KeywordRankEntityVO.TYPE_NEWS || StringUtils.containsIgnoreCase(url, "news.google.com")
//				|| StringUtils.containsIgnoreCase(url, "news.google.")) {
//			return true;
//		} else if (urlType == KeywordRankEntityVO.TYPE_SHOPPING
//				|| StringUtils.containsIgnoreCase(url, "www.google.com/shopping")) {
//			return true;
//		} else if (urlType == KeywordRankEntityVO.TYPE_VIDEO) {
//			return true;
//		} else if (StringUtils.containsIgnoreCase(url, "www.google.com")
//				|| StringUtils.containsIgnoreCase(url, "www.google.")
//                || urlType == KeywordRankEntityVO.TYPE_TWITTER
////                || urlType == KeywordRankEntityVO.TYPE_ANSWERBOX
//                ) {
//			return true;
//		} else if (urlType == KeywordRankEntityVO.TYPE_JOB_URL) {
//			return true;
//		}
////		else if (urlType == KeywordRankEntityVO.TYPE_TWITTER) {
////			return true;
////		} else if (urlType == KeywordRankEntityVO.TYPE_ANSWERBOX) {
////			return true;
////		}
//
//
//		return false;
//	}

	private String getRootDomain(String fullDomain) {
		try {
			String domainName = StringUtils.reverseDelimited(fullDomain, '.');
			return StringUtils.reverseDelimited(InternetDomainName.from(domainName).topPrivateDomain().toString(), '.');
		} catch (Exception e) {
		}
		return null;
	}

	private String getUrlForSubRankViaDecode(String url) {

		if (StringUtils.containsIgnoreCase(url, "https")) {

			url = extractSubUrl(url, "https");

			url = decodeHttpUrl(url);
			url = decodeHttpUrl(url);
			url = decodeHttpUrl(url);
		} else if (StringUtils.containsIgnoreCase(url, "http")) {

			url = extractSubUrl(url, "http");

			url = decodeHttpUrl(url);
			url = decodeHttpUrl(url);
			url = decodeHttpUrl(url);
		} else {
			url = "/";
		}

		return url;
	}

	private String extractSubUrl(String url, String protocol) {

		int i = StringUtils.indexOf(url, protocol);
		url = StringUtils.substring(url, i);

		int j = StringUtils.indexOf(url, "&");
		url = StringUtils.substring(url, 0, j);

		return url;
	}

	private String decodeHttpUrl(String urlStr) {
		try {
			return decode(urlStr, Charset.forName("UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return urlStr;
	}

	private String decode(String source, Charset charset) {
		int length = source.length();
		ByteArrayOutputStream bos = new ByteArrayOutputStream(length);
		boolean changed = false;
		for (int i = 0; i < length; i++) {
			int ch = source.charAt(i);
			if (ch == '%') {
				if ((i + 2) < length) {
					char hex1 = source.charAt(i + 1);
					char hex2 = source.charAt(i + 2);
					int u = Character.digit(hex1, 16);
					int l = Character.digit(hex2, 16);
					if (u == -1 || l == -1) {
						throw new IllegalArgumentException("Invalid encoded sequence \"" + source.substring(i) + "\"");
					}
					bos.write((char) ((u << 4) + l));
					i += 2;
					changed = true;
				} else {
					throw new IllegalArgumentException("Invalid encoded sequence \"" + source.substring(i) + "\"");
				}
			} else {
				bos.write(ch);
			}
		}
		return (changed ? new String(bos.toByteArray(), charset) : source);
	}

	@Override
	protected void undo() throws Exception {
	}

	private Integer convertValue(String value) {
		if (StringUtils.equalsIgnoreCase(value, "y")) {
			return 1;
		}
		return 0;
	}

	private String getKeyowrdInfoTableName(int engineId, int languageId) {
		if (mobileRanking) {
			if (searchEngine == 1 && language == 1) {
				return "m_ranking_info_us";
			} else {
				return "m_ranking_info_intl";
			}
		} else {
			if (searchEngine == 1 && language == 1) {
				return "d_ranking_info_us";
			} else {
				return "d_ranking_info_intl";
			}
		}
	}

	private String getKeyowrdDetailTableName(int engineId, int languageId) {
		if (mobileRanking) {
			if (searchEngine == 1 && language == 1) {
				return "m_ranking_detail_us";
			} else {
				return "m_ranking_detail_intl";
			}
		} else {
			if (searchEngine == 1 && language == 1) {
				return "d_ranking_detail_us";
			} else {
				return "d_ranking_detail_intl";
			}
		}
	}

	private String getKeyowrdSubRankTableName(int engineId, int languageId) {
		if (mobileRanking) {
			if (searchEngine == 1 && language == 1) {
				return "m_ranking_subrank_us";
			} else {
				return "m_ranking_subrank_intl";
			}
		} else {
			if (searchEngine == 1 && language == 1) {
				return "d_ranking_subrank_us";
			} else {
				return "d_ranking_subrank_intl";
			}
		}
	}

	public HashSet<String> getetDirectory(String url) throws Exception {
		url = StringUtils.substringBeforeLast(url, "/");

		String[] uriArraries = url.replaceFirst("/", "").split("/");

		String firstDepth = "";
		String secondDepth = "";
		String thirdDepth = "";
		LinkedHashSet<String> top3Directories = new LinkedHashSet<String>();

		if (uriArraries != null) {
			for (int i = 0; i < uriArraries.length; i++) {
				String directory = uriArraries[i];
				if (i == 0) {
					firstDepth = directory;
				} else if (i == 1) {
					secondDepth = firstDepth + "/" + directory;
				} else if (i == 2) {
					thirdDepth = secondDepth + "/" + directory;
				}
			}
		}

		if (StringUtils.isNotBlank(firstDepth)) {
			top3Directories.add(firstDepth);
		}
		if (StringUtils.isNotBlank(secondDepth)) {
			top3Directories.add(secondDepth);
		}
		if (StringUtils.isNotBlank(thirdDepth)) {
			top3Directories.add(thirdDepth);
		}

		return top3Directories;
	}

	public void setCheckKeywordExistsInMonitorWithSendToQDate(boolean checkKeywordExistsInMonitorWithSendToQDate) {
		this.checkKeywordExistsInMonitorWithSendToQDate = checkKeywordExistsInMonitorWithSendToQDate;
	}

}
