package seoclarity.backend.upload;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.eclipse.jetty.util.BlockingArrayQueue;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.dao.clickhouse.ng007.DisManagedSearchvolumeNj007DAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsExpandedEntityNewDAO;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.KeywordStreamSearchengineCountryMappingEntity;
import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.DisManagedSearchvolume;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsExpandedEntityNew;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

/**
 * @program: backend
 * @description: all keyword upload
 * @packagename: seoclarity.backend.upload
 * @author: cil
 * @date: 2021-06-09 17:37
 **/
public class AllKeywordTokenizerUploadV2 {
	
	private static final String TARGET_RI_TABLE = "dis_managed_searchvolume_notd"; // TODO

	private static final Integer[] EXCLUDE_ENGINE_ID_ARR = new Integer[] { 39, 150, 236, 237, 254 }; // TODO
	public static final List<Integer> EXCLUDE_ENGINE_ID_LIST = Arrays.asList(EXCLUDE_ENGINE_ID_ARR); 
    
//    private static final String LOCALTABLE = "local_managed_searchvolume";
//    private static final String DISTABLE = "dis_managed_searchvolume";
//    private static final String OLD = "_backup";
    private static final String FLAG_TXT = "flag.txt";
    private static final String FLAG_TXT_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/";
//    private static final SimpleDateFormat SDF = new SimpleDateFormat("_yyyyMMddHH");
    private static final String DELIMITER = "!_!";

    public static Map<Integer, Long> MIN_ID_MAP = new HashMap<>();
    private static int THREAD_COUNT = 5;
    private final int NUM = 20000;
    private static final int KEYWORD_ID_BATCH_SIZE = 300; // TODO
    
    private static final SimpleDateFormat SDF_YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
	private static final SimpleDateFormat SDF_YYYYMM = new SimpleDateFormat("yyyyMM");
	private static final SimpleDateFormat SDF_MM = new SimpleDateFormat("MM");

    static private long maxId;
    static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 5, 10000, TimeUnit.SECONDS, new BlockingArrayQueue<>(10));
    public static Map<String, String[]> processCountryMap = new LinkedHashMap<String, String[]>();
    public static Map<String, String> specialCountryCodeMap = new LinkedHashMap<String, String>();
    private static Map<String, KeywordStreamSearchengineCountryMappingEntity> countryAndLanguageFullNameMap = null;
    private static Map<Integer, Map<Integer, Integer>> monthMap = new HashMap<Integer, Map<Integer, Integer>>();
    private Map<String, String> countryCodeMap = new HashMap<String, String>();

    private final KeywordAdwordsExpandedEntityNewDAO keywordAdwordsExpandedEntityNewDAO;
    private final KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;
    private final EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
    
    // https://www.wrike.com/open.htm?id=1498326881
//    private final DisManagedSearchvolumeDAO disManagedSearchvolumeDAO;
//    //201
//    private final ManagedSearchvolume201DAO managedSearchvolume201DAO;
//    //202
//    private final ManagedSearchvolume202DAO managedSearchvolume202DAO;
//    //203
//    private final ManagedSearchvolume203DAO managedSearchvolume203DAO;
//    //204
//    private final ManagedSearchvolume204DAO managedSearchvolume204DAO;
//    //205
//    private final ManagedSearchvolume205DAO managedSearchvolume205DAO;
//    //206
//    private final ManagedSearchvolume206DAO managedSearchvolume206DAO;
     
    private final DisManagedSearchvolumeNj007DAO disManagedSearchvolumeNj007DAO;
//    //201
//    private final ManagedSearchvolumeRiNj001DAO managedSearchvolumeRiNj001DAO;
//    //202
//    private final ManagedSearchvolumeRiNj002DAO managedSearchvolumeRiNj002DAO;
//    //203
//    private final ManagedSearchvolumeRiNj003DAO managedSearchvolumeRiNj003DAO;
//    //204
//    private final ManagedSearchvolumeRiNj004DAO managedSearchvolumeRiNj004DAO;
//    //205
//    private final ManagedSearchvolumeRiNj005DAO managedSearchvolumeRiNj005DAO;
//    //206
//    private final ManagedSearchvolumeRiNj006DAO managedSearchvolumeRiNj006DAO;

    static {
        specialCountryCodeMap.put("CA-2", "FR");
        specialCountryCodeMap.put("US-2", "US");
        processCountryMap.put("US", new String[]{"English", String.valueOf(SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE)});
    }

    synchronized long getMinId(int keywordType) {
        if (!MIN_ID_MAP.containsKey(keywordType))
            MIN_ID_MAP.put(keywordType, 0L);
        Long thisId = MIN_ID_MAP.get(keywordType);
        MIN_ID_MAP.put(keywordType, thisId + 200);
        return thisId;
    }

    synchronized long getMaxId() {
        return maxId;
    }

    public AllKeywordTokenizerUploadV2() {
//        managedSearchvolume201DAO = SpringBeanFactory.getBean("managedSearchvolume201DAO");
//        managedSearchvolume202DAO = SpringBeanFactory.getBean("managedSearchvolume202DAO");
//        managedSearchvolume203DAO = SpringBeanFactory.getBean("managedSearchvolume203DAO");
//        managedSearchvolume204DAO = SpringBeanFactory.getBean("managedSearchvolume204DAO");
//        managedSearchvolume205DAO = SpringBeanFactory.getBean("managedSearchvolume205DAO");
//        managedSearchvolume206DAO = SpringBeanFactory.getBean("managedSearchvolume206DAO");
        keywordAdwordsExpandedEntityNewDAO = SpringBeanFactory.getBean("keywordAdwordsExpandedEntityNewDAO");
//        disManagedSearchvolumeDAO = SpringBeanFactory.getBean("disManagedSearchvolumeDAO");
        
//        managedSearchvolumeRiNj001DAO = SpringBeanFactory.getBean("managedSearchvolumeRiNj001DAO");
//        managedSearchvolumeRiNj002DAO = SpringBeanFactory.getBean("managedSearchvolumeRiNj002DAO");
//        managedSearchvolumeRiNj003DAO = SpringBeanFactory.getBean("managedSearchvolumeRiNj003DAO");
//        managedSearchvolumeRiNj004DAO = SpringBeanFactory.getBean("managedSearchvolumeRiNj004DAO");
//        managedSearchvolumeRiNj005DAO = SpringBeanFactory.getBean("managedSearchvolumeRiNj005DAO");
//        managedSearchvolumeRiNj006DAO = SpringBeanFactory.getBean("managedSearchvolumeRiNj006DAO");
        disManagedSearchvolumeNj007DAO = SpringBeanFactory.getBean("disManagedSearchvolumeNj007DAO");
        
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");

        maxId = findMaxId();
    }

    public static void main(String[] args) {
        //init countryAndLanguageFullNameMap
        AllKeywordTokenizerUploadV2 ins = new AllKeywordTokenizerUploadV2();
        List<KeywordStreamSearchengineCountryMappingEntity> countryAndLanguageFullName = ins.getCountryAndLanguageFullName();
        countryAndLanguageFullNameMap = countryAndLanguageFullName.stream().collect(Collectors.toMap(v1 -> String.join(DELIMITER, v1.getEngineId() + "", v1.getLanguageId() + ""), v2 -> v2, (v_old, v_new) -> v_new));

        System.out.println("===main targetRITable:" + TARGET_RI_TABLE + " skipSEs:" + EXCLUDE_ENGINE_ID_LIST + " clfMap:" + countryAndLanguageFullNameMap.size() + 
        	" clfList:" + countryAndLanguageFullName.size() + " kwIdRange:0-" + maxId);
//        String suffix = SDF.format(new Date());
        // create local table for 201-207
//        ins.createLocalTable(LOCALTABLE + suffix, LOCALTABLE); // https://www.wrike.com/open.htm?id=1498326881
        // create dis_manage table
//        ins.disManagedSearchvolumeDAO.createDisManagedSearchvolumeTable(LOCALTABLE, DISTABLE + suffix, LOCALTABLE + suffix);
//        ins.disManagedSearchvolumeNj007DAO.createDisManagedSearchvolumeTable(LOCALTABLE, DISTABLE + suffix, LOCALTABLE + suffix);
        for (int i = 0; i < THREAD_COUNT; i++) {
            threadPoolExecutor.execute(() -> ins.poccess(RcKeywordSeRelEntity.KEYWORD_TYPE_NATIONAL, TARGET_RI_TABLE));
            threadPoolExecutor.execute(() -> ins.poccess(RcKeywordSeRelEntity.KEYWORD_TYPE_GEO, TARGET_RI_TABLE));
        }
        threadPoolExecutor.shutdown();
        while (!threadPoolExecutor.isTerminated())
            try {
                Thread.sleep(1000);
            } catch (Exception ex) {
            }
        System.out.println("data load end");
//        ins.swapTableName(suffix); // Do not switch tables as need to merge true demand by shell script
    }

    private void poccess(int keywordType, String tableName) {
        List<DisManagedSearchvolume> insertList = new LinkedList<>();
        long currentTimeMillis = System.currentTimeMillis();
        long pocMinId, pocMaxId;
        List<KeywordAdwordsExpandedEntityNew> entityList;
        do {
            pocMinId = getMinId(keywordType);
            pocMaxId = getMaxId();
            // get 30 KeywordAdwordsExpandedEntityNew
            // entityList = find30ById(keywordType, pocMinId);
            entityList = keywordAdwordsExpandedEntityNewDAO.getAdwordsListByKeywordIdRange(keywordType, pocMinId, pocMinId + KEYWORD_ID_BATCH_SIZE, EXCLUDE_ENGINE_ID_LIST);
            int cnt = entityList != null ? entityList.size() : 0;
            System.out.println(" =SVList " + pocMinId + "-" + (pocMinId + KEYWORD_ID_BATCH_SIZE) + "(" + keywordType + " " + cnt + ")");
            if (cnt == 0) {
                continue;
            }
            //put to map
            Map<String, List<KeywordAdwordsExpandedEntityNew>> keyWordMap = new HashMap(128);
            for (KeywordAdwordsExpandedEntityNew entityNew : entityList) {
                // decode
                try {
                    entityNew.setKeyword_name(StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(entityNew.getKeyword_name(), "UTF-8"))));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (entityNew.getKeyword_name().contains("\t")) {
                    System.out.println("poccess skip keyword:" + entityNew.getKeyword_name() + "  id:" + entityNew.getKeywordId() + " keywordType:" + keywordType);
                    continue;
                }
                StringBuffer sb = new StringBuffer();
                sb.append(entityNew.getSearchEngineId()).append(DELIMITER).append(entityNew.getLanguageId())
                        .append(DELIMITER).append(entityNew.getLocationId()).append(DELIMITER).append(entityNew.getKeywordId()).append(DELIMITER).append(entityNew.getKeyword_name());
                String key = sb.toString();

                if (keyWordMap.get(key) != null && keyWordMap.get(key).size() >= 1) {
                    keyWordMap.get(key).add(entityNew);
                } else {
                    keyWordMap.put(key, new LinkedList());
                    if (entityNew.getMonth() != null)
                        keyWordMap.get(key).add(entityNew);
                }
            }
            //if map is null, continue
            if (keyWordMap.entrySet() == null)
                continue;
            //loop map
            for (Map.Entry<String, List<KeywordAdwordsExpandedEntityNew>> entry : keyWordMap.entrySet()) {
                DisManagedSearchvolume entity;
                if (entry.getValue() != null && entry.getValue().size() >= 1) {
                    entity = getDisManagedSearchvolume(entry.getValue());
                } else {
                    entity = getDisManagedSearchvolume(entry.getKey());
                }
                if (entity == null) {
                    continue;
                }
                if (entity.getMonthlySvAttr_value().length < 1) {
                    continue;
                }
                KeywordStreamSearchengineCountryMappingEntity keywordStreamSearchengineCountryMappingEntity = countryAndLanguageFullNameMap.get(String.join(DELIMITER, entity.getEngine_id() + "", entity.getLanguage_id() + ""));
                // https://www.wrike.com/open.htm?id=1321817144
                String languageFullName = null;
                String countryCode = null;
                if (keywordStreamSearchengineCountryMappingEntity != null) {
                	languageFullName = keywordStreamSearchengineCountryMappingEntity.getLanguageFullName();
                	countryCode = keywordStreamSearchengineCountryMappingEntity.getCountryCode();
                } else {
                	languageFullName = "English";
                	countryCode = getCountryCode(entity.getEngine_id(), entity.getLanguage_id());
                }
                getWord(entity, languageFullName, countryCode);
                insertList.add(entity);
            }
            if (insertList.size() >= NUM) {
                insert(insertList, 0, tableName);
                System.out.println(Thread.currentThread().getName() + " keywordType:" + keywordType + " pocMaxId:" + pocMaxId + "  minId:" + pocMinId + " list.size: " + entityList.size() + " insertList1.size = " + insertList.size() +
                        "\r\n ues time:" + ((System.currentTimeMillis() - currentTimeMillis) / 1000 + "s !"));
                insertList = new LinkedList<>();
            }
        } while (pocMinId < pocMaxId);
        if (insertList.size() > 0) {
            insert(insertList, 0, tableName);
        }
    }
    
    private String getCountryCode(int engineId, int languageId) {
    	String seKey = engineId + DELIMITER + languageId;
    	String countryCdInMap = countryCodeMap.get(seKey);
    	if (countryCdInMap != null) {
    		return countryCdInMap;
    	} else {
    		EngineCountryLanguageMappingEntity engineLanguageMappingEntity = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageIdAndRankFrom(engineId, languageId, EngineCountryLanguageMappingEntity.RANK_FROM_ALL);
    		String countryCode = null;
    		int mappingId = 0;
    		if (engineLanguageMappingEntity != null) {
    			countryCode = engineLanguageMappingEntity.getCountryQueryName().toLowerCase();
				mappingId = engineLanguageMappingEntity.getId();
			}
			System.out.println(" ==getContryCd:" + countryCode + " SE:" + engineId + "_" + languageId + " mappingId:" + mappingId);
			countryCodeMap.put(seKey, countryCode);
			return countryCode;
    	}
    }

    private DisManagedSearchvolume getDisManagedSearchvolume(String str) {
        String[] split = str.split(DELIMITER);
        String keywordName = split[4];
        int locationId = Integer.valueOf(split[2]);
        int keywordId = Integer.valueOf(split[3]);
        int searchEngineId = Integer.valueOf(split[0]);
        int languageId = Integer.valueOf(split[1]);
        DisManagedSearchvolume entity = new DisManagedSearchvolume();
        entity.setHas_sv(0);
        entity.setCpc(0f);
        entity.setKeyword_name(keywordName);
        entity.setAvg_search_volume(0);
        entity.setLocation_id(locationId);
        entity.setKeyword_rankcheck_id(keywordId);
        entity.setSign(1);
        entity.setVersioning(0);
        entity.setEngine_id(searchEngineId);
        entity.setLanguage_id(languageId);

        entity.setMonthly_search_volume1(0);
        entity.setMonthly_search_volume2(0);
        entity.setMonthly_search_volume3(0);
        entity.setMonthly_search_volume4(0);
        entity.setMonthly_search_volume5(0);
        entity.setMonthly_search_volume6(0);
        entity.setMonthly_search_volume7(0);
        entity.setMonthly_search_volume8(0);
        entity.setMonthly_search_volume9(0);
        entity.setMonthly_search_volume10(0);
        entity.setMonthly_search_volume11(0);
        entity.setMonthly_search_volume12(0);

        List<Integer> monthlySvAttr_key = new LinkedList<>();
        monthlySvAttr_key.add(0);
        entity.setMonthlySvAttr_key(monthlySvAttr_key);
        entity.setMonthlySvAttr_value(monthlySvAttr_key);
        entity.setLastRefreshMonth(0);
        entity.setCategory(monthlySvAttr_key);
        return entity;
    }

    private DisManagedSearchvolume getDisManagedSearchvolume(List<KeywordAdwordsExpandedEntityNew> adwird) {
        DisManagedSearchvolume entity = new DisManagedSearchvolume();
        int size = adwird.size();
        if (adwird == null || size < 1) {
            return null;
        } else {
            Collections.sort(adwird);
            KeywordAdwordsExpandedEntityNew lastOne = adwird.get(size - 1);

            entity.setHas_sv(1);
            entity.setCpc(lastOne.getCostPerClick() == null ? 0f : lastOne.getCostPerClick());
            entity.setKeyword_name(lastOne.getKeyword_name());
            entity.setAvg_search_volume(lastOne.getAvgMonthlySearchVolume() == null ? 0 : lastOne.getAvgMonthlySearchVolume());
            entity.setLocation_id(lastOne.getLocationId());
            entity.setKeyword_rankcheck_id(lastOne.getKeywordId());
            entity.setSign(1);
            entity.setVersioning(0);
            entity.setEngine_id(lastOne.getSearchEngineId());
            entity.setLanguage_id(lastOne.getLanguageId());

//            entity.setMonthly_search_volume1(size > 0 ? adwird.get(0).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume2(size > 1 ? adwird.get(1).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume3(size > 2 ? adwird.get(2).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume4(size > 3 ? adwird.get(3).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume5(size > 4 ? adwird.get(4).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume6(size > 5 ? adwird.get(5).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume7(size > 6 ? adwird.get(6).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume8(size > 7 ? adwird.get(7).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume9(size > 8 ? adwird.get(8).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume10(size > 9 ? adwird.get(9).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume11(size > 10 ? adwird.get(10).getMonthlySearchVolume() : 0);
//            entity.setMonthly_search_volume12(size > 11 ? adwird.get(11).getMonthlySearchVolume() : 0);
            setMonthlySV(adwird.get(size - 1).getMonth(), adwird, entity); // https://www.wrike.com/open.htm?id=969374712

            List<Integer> monthlySvAttr_key = new LinkedList<>();
            List<Integer> monthlySvAttr_value = new LinkedList<>();
            for (KeywordAdwordsExpandedEntityNew adw : adwird) {
                monthlySvAttr_key.add(adw.getMonth() == null ? 0 : adw.getMonth());
                monthlySvAttr_value.add(adw.getMonthlySearchVolume() == null ? 0 : adw.getMonthlySearchVolume());
            }
            entity.setMonthlySvAttr_key(monthlySvAttr_key);
            if (monthlySvAttr_key.size() == 0) {
                monthlySvAttr_key.add(0);
                entity.setMonthlySvAttr_key(monthlySvAttr_key);
            }
            entity.setMonthlySvAttr_value(monthlySvAttr_value);
            if (monthlySvAttr_value.size() == 0) {
                monthlySvAttr_value.add(0);
                entity.setMonthlySvAttr_key(monthlySvAttr_value);
            }
            entity.setLastRefreshMonth(adwird.get(size - 1).getMonth());
            if (lastOne.getCategory() != null) {
                List<String> result = Arrays.asList(lastOne.getCategory().split("#"));
                List<Integer> codesInteger = result.stream().filter(s -> !"".equals(s)).map(Integer::parseInt).collect(Collectors.toList());
                entity.setCategory(codesInteger);
            }
        }
        return entity;
    }
    
    static void setMonthlySV(int lastRefreshMonth, List<KeywordAdwordsExpandedEntityNew> expandList, DisManagedSearchvolume targetEntity) {
    	try {
    		targetEntity.setMonthly_search_volume1(0);
    		targetEntity.setMonthly_search_volume2(0);
    		targetEntity.setMonthly_search_volume3(0);
    		targetEntity.setMonthly_search_volume4(0);
    		targetEntity.setMonthly_search_volume5(0);
    		targetEntity.setMonthly_search_volume6(0);
    		targetEntity.setMonthly_search_volume7(0);
    		targetEntity.setMonthly_search_volume8(0);
    		targetEntity.setMonthly_search_volume9(0);
    		targetEntity.setMonthly_search_volume10(0);
    		targetEntity.setMonthly_search_volume11(0);
    		targetEntity.setMonthly_search_volume12(0);
    		Map<Integer, Integer> subMap = getMonthMap(monthMap, lastRefreshMonth);
        	for (KeywordAdwordsExpandedEntityNew expandedEntity : expandList) {
        		int month = expandedEntity.getMonth();
        		if (subMap.get(month) != null) {
        			int mm = subMap.get(month);
        			int monthlySV = expandedEntity.getMonthlySearchVolume() != null ? expandedEntity.getMonthlySearchVolume().intValue() : 0;
        			switch (mm) {
    	    			case 1:
    	    				targetEntity.setMonthly_search_volume1(monthlySV);
    	    				break;
    	    			case 2:
    	    				targetEntity.setMonthly_search_volume2(monthlySV);
    	    				break;
    	    			case 3:
    	    				targetEntity.setMonthly_search_volume3(monthlySV);
    	    				break;
    	    			case 4:
    	    				targetEntity.setMonthly_search_volume4(monthlySV);
    	    				break;
    	    			case 5:
    	    				targetEntity.setMonthly_search_volume5(monthlySV);
    	    				break;
    	    			case 6:
    	    				targetEntity.setMonthly_search_volume6(monthlySV);
    	    				break;
    	    			case 7:
    	    				targetEntity.setMonthly_search_volume7(monthlySV);
    	    				break;
    	    			case 8:
    	    				targetEntity.setMonthly_search_volume8(monthlySV);
    	    				break;
    	    			case 9:
    	    				targetEntity.setMonthly_search_volume9(monthlySV);
    	    				break;
    	    			case 10:
    	    				targetEntity.setMonthly_search_volume10(monthlySV);
    	    				break;
    	    			case 11:
    	    				targetEntity.setMonthly_search_volume11(monthlySV);
    	    				break;
    	    			case 12:
    	    				targetEntity.setMonthly_search_volume12(monthlySV);
    	    				break;
        			}
        		}
        	}
    	} catch (Exception exp) {
    		exp.printStackTrace();
    	}
    }
    
    static Map<Integer, Integer> getMonthMap(Map<Integer, Map<Integer, Integer>> paramMap, int lastRefreshMonth) throws Exception {
		if (paramMap.get(lastRefreshMonth) != null) {
			return paramMap.get(lastRefreshMonth);
		}
		
		Map<Integer, Integer> subMap = new HashMap<Integer, Integer>();
		Date lastMonth = SDF_YYYYMMDD.parse(lastRefreshMonth + "01");
		for (int k = 0; k < 12; k++) {
			subMap.put(Integer.parseInt(SDF_YYYYMM.format(lastMonth)), Integer.parseInt(SDF_MM.format(lastMonth)));
			lastMonth = DateUtils.addMonths(lastMonth, -1);
		}
		paramMap.put(lastRefreshMonth, subMap);
		
		StringBuffer sb = new StringBuffer();
		sb.append(" ==MonthMap lastRefreshMonth:" + lastRefreshMonth);
		for (Integer key : subMap.keySet()) {
			sb.append(" yyyyMM:" + key).append("->" + subMap.get(key));
		}
		System.out.println(sb.toString());
		return subMap;
	}

    private void getWord(DisManagedSearchvolume entity, String languageName, String countryCode) {
        List<String> word = new ArrayList<>();
        List<String> stream = new ArrayList<>();

        List<String> keywordVariationOneword = new ArrayList<>();
        List<String> keywordVariationNgram = new ArrayList<>();

        if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(entity.getKeyword_name(), "ar"));
        } else {
            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(entity.getKeyword_name(), countryCode));
        }
        stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(entity.getKeyword_name(), languageName));
        keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(entity.getKeyword_name()));
        keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(entity.getKeyword_name(), false));

        entity.setWord(word);
        entity.setStream(stream);
        entity.setKeyword_variation_oneword((keywordVariationOneword));
        entity.setKeyword_variation_ngram(setPlaceHolder(keywordVariationNgram));
    }

    public static List<String> setPlaceHolder(List<String> array) {
        String holder = "_";
        if (array.size() <= 2) {
            return array;
        }
        List<String> result = new ArrayList<String>();
        String startWordStr = array.get(0);
        String endWordStr = array.get(array.size() - 1);

        for (int i = 0; i < array.size(); i++) {
            String words = array.get(i);
            if ((StringUtils.startsWith(startWordStr, words) || StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
                words = words + " " + holder;
                result.add(words);
                continue;
            }
            if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
                words = holder + " " + words + " " + holder;
                result.add(words);
                continue;
            }
            if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (StringUtils.endsWith(endWordStr, words) || StringUtils.endsWith(words, endWordStr))) {
                words = holder + " " + words;
                result.add(words);
                continue;
            }
            result.add(words);
        }
        return result;
    }

    private Long findMaxId() {
        return keywordAdwordsExpandedEntityNewDAO.getMaxId();
    }

    private List<KeywordAdwordsExpandedEntityNew> find30ById(int keywordType, Long id) {
        try {
            return keywordAdwordsExpandedEntityNewDAO.select30Byid(keywordType, id);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList<>();
    }

    private List<KeywordStreamSearchengineCountryMappingEntity> getCountryAndLanguageFullName() {
        try {
            return keywordStreamSearchengineCountryMappingEntityDAO.findAllEnabledEngineCountryMapping();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList<>();
    }

    private void insert(List<DisManagedSearchvolume> list, int flag, String tableName) {
        System.out.println(Thread.currentThread().getName() + " insert " + tableName + " rows:" + list.size());
        // Only upload to master RI cluster(need merge true demand by shell, copy data to backup cluster manually after merge true demand)
        while (true) {
            try {
            	disManagedSearchvolumeNj007DAO.insertBatchByName(list, tableName);
                break;
            } catch (Exception exception) {
                exception.printStackTrace();
                flag++;
                System.out.println(Thread.currentThread().getName() + "===========retryTimes:" + flag);
                try {
                    Thread.sleep(30 * 1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (flag >= 10)
                    break;
            }
        }
//        try {
//        	disManagedSearchvolumeDAO.insertBatchByName(list, tableName);
//        } catch (Exception exception) {
//            exception.printStackTrace();
//        }
        if (flag != 0)
            write(list);
    }

/*    private void createLocalTable(String newTableName, String oldTableName) {
        managedSearchvolume201DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume202DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume203DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume204DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume205DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume206DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        disManagedSearchvolumeDAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        
		managedSearchvolumeRiNj001DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolumeRiNj002DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolumeRiNj003DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolumeRiNj004DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolumeRiNj005DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolumeRiNj006DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        disManagedSearchvolumeNj007DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
    }*/

//    private void swapTableName(String suffix) {
//        //rename old table
//        managedSearchvolume201DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolume202DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolume203DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolume204DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolume205DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolume206DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        disManagedSearchvolumeDAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        //rename new table
//        managedSearchvolume201DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolume202DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolume203DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolume204DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolume205DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolume206DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        disManagedSearchvolumeDAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        
//        //rename old table
//        managedSearchvolumeRiNj001DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolumeRiNj002DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolumeRiNj003DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolumeRiNj004DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolumeRiNj005DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        managedSearchvolumeRiNj006DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        disManagedSearchvolumeNj007DAO.renameTable(LOCALTABLE, LOCALTABLE + suffix + OLD);
//        //rename new table
//        managedSearchvolumeRiNj001DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolumeRiNj002DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolumeRiNj003DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolumeRiNj004DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolumeRiNj005DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        managedSearchvolumeRiNj006DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//        disManagedSearchvolumeNj007DAO.renameTable(LOCALTABLE + suffix, LOCALTABLE);
//    }

    public synchronized static void write(List<DisManagedSearchvolume> list) {
        String fullFileName = String.join(File.separator, FLAG_TXT_PATH, FLAG_TXT);
        try {
            File dir = new File(FLAG_TXT_PATH);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            File file = new File(fullFileName);
            FileOutputStream fos = null;
            if (!file.exists()) {
                file.createNewFile();
                fos = new FileOutputStream(file, true);
            } else {
                fos = new FileOutputStream(file, true);
            }

            OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
            for (DisManagedSearchvolume disManagedSearchvolume : list) {
                osw.write(disManagedSearchvolume.toString());
                osw.write("\r\n");
            }
            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}