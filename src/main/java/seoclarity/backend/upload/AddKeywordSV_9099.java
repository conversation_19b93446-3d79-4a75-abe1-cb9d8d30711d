package seoclarity.backend.upload;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.SearchVolRankcheckIdDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.actonia.SearchvolRankcheckidEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class AddKeywordSV_9099 {

    private static String localFile = "files/9099Files/";
    private static int domainId = 9099;
    private static List<SearchvolRankcheckidEntity> dataList = new ArrayList<>();

    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
    private SearchVolRankcheckIdDAO searchVolRankcheckIdDAO;

    public AddKeywordSV_9099() {
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
        searchVolRankcheckIdDAO = SpringBeanFactory.getBean("searchVolRankcheckIdDAO");
    }

    public static void main(String[] args) {
        String folder = "";
        if (args != null & args.length > 0) {
            folder = args[0] + "/";
            localFile = localFile + folder;
        }
        System.out.println(" localFile: " + localFile);
        AddKeywordSV_9099 in = new AddKeywordSV_9099();
        try {
            in.process();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void process() throws Exception {
        for (File file : new File(localFile).listFiles()) {
            System.out.println("filename : " + file.getName());
            if (file.getName().contains(".csv")) {
                parseExcel(file);
            } else if (file.getName().contains(".xlsx")) {
                parseExcelxlsx(file);
            }
        }

    }

    private void parseExcel(File file) throws IOException {

        List<String> lines = FileUtils.readLines(new File(file.getAbsolutePath()), "UTF-8");
        CSVParser parser = new CSVParser(',');
        int n = 0;
        Map<String, Object> k = new HashMap<>();
        StringBuffer sb = new StringBuffer();
        for (String line : lines) {
            n++;
            try {
                String[] cols = parser.parseLine(line);

                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword")) {
                    System.out.println(" 第一行");
                } else {
                    String kwStr = cols[0];
                    kwStr = StringUtils.lowerCase(FormatUtils.encodeKeyword(kwStr));
                    System.out.println("kwStr : " + kwStr);
                    SeoClarityKeywordEntity keywordEntity = seoClarityKeywordEntityDAO.getByKeyword(kwStr);
                    String sv = cols[2];
                    parseEntity(keywordEntity.getId(), sv);
                }
            } catch (Exception e) {

            }
        }

        System.out.println("===###dataList ：" + dataList.size());
        if (dataList.size() > 0) {
            for (SearchvolRankcheckidEntity entity : dataList) {
                try {
                    SearchvolRankcheckidEntity en = searchVolRankcheckIdDAO.getExistEntity(domainId, entity.getKeywordRankcheckId().intValue(), entity.getLocationId());
                    if (en == null) {
                        searchVolRankcheckIdDAO.insertEntity(entity);
                    } else {
                        System.out.println(" 重复数据 ");
                        System.out.println(" " + entity.getKeywordRankcheckId());
                        System.out.println(" " + entity.getAvgSearchVolume());
                        System.out.println(" " + entity.getOwnDomainId());
                        System.out.println(" " + entity.getLocationId());
                        System.out.println(" " + entity.getSearchVolumneDate());
                    }

                } catch (Exception e) {
                    System.out.println(" " + entity.getKeywordRankcheckId());
                    System.out.println(" " + entity.getAvgSearchVolume());
                    System.out.println(" " + entity.getOwnDomainId());
                    System.out.println(" " + entity.getLocationId());
                    System.out.println(" " + entity.getSearchVolumneDate());
                }

            }
        }

    }

    private void parseExcelxlsx(File file) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        for (int n = start; n <= end; n++) {
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }

            try {
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword")) {
                    System.out.println(" 第一行");
                } else {
                    String kwStr = cols[0];
                    kwStr = StringUtils.lowerCase(FormatUtils.encodeKeyword(kwStr));
                    SeoClarityKeywordEntity keywordEntity = seoClarityKeywordEntityDAO.getByKeyword(kwStr);
                    if (null != keywordEntity && keywordEntity.getId() >0) {
                        String sv = cols[1];
                        Double svD = Double.parseDouble(sv);
                        Long svl = svD.longValue();
                        sv = svl.toString();
                        System.out.println("kwStr  ： " + kwStr + " sv ： " + sv);
                        try {
                            parseEntity(keywordEntity.getId(), sv);
                        }catch (Exception e){
                            e.printStackTrace();
                        }

                    }
                }
            } catch (Exception e) {

            }
        }

        System.out.println("===###dataList ：" + dataList.size());
        if (dataList.size() > 0) {
            for (SearchvolRankcheckidEntity entity : dataList) {
                try {
                    SearchvolRankcheckidEntity en = searchVolRankcheckIdDAO.getExistEntity(domainId, entity.getKeywordRankcheckId().intValue(), entity.getLocationId());
                    if (en == null) {
                        searchVolRankcheckIdDAO.insertEntity(entity);
                    } else {
                        System.out.println(" 已存在sv 更新数据=== ");
                        searchVolRankcheckIdDAO.updateEntity(entity);
                    }

                } catch (Exception e) {
                    System.out.println(" " + entity.getKeywordRankcheckId());
                    System.out.println(" " + entity.getAvgSearchVolume());
                    System.out.println(" " + entity.getOwnDomainId());
                    System.out.println(" " + entity.getLocationId());
                    System.out.println(" " + entity.getSearchVolumneDate());
                }

            }

        }

    }

    private void parseEntity(Integer id, String sv) {
        SearchvolRankcheckidEntity rankcheckidEntity = new SearchvolRankcheckidEntity();
        rankcheckidEntity.setKeywordRankcheckId(id.longValue());
        rankcheckidEntity.setOwnDomainId(domainId);
        rankcheckidEntity.setLocationId(0);
        rankcheckidEntity.setAvgSearchVolume(Long.parseLong(sv));
        String day = DateFormatUtils.format(new Date(), "yyyyMMdd");
        rankcheckidEntity.setSearchVolumneDate(20230531);
        dataList.add(rankcheckidEntity);
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

}
