package seoclarity.backend.upload;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.util.Assert;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.ResourceAddDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceAddInfoEntityDAO;
import seoclarity.backend.dao.actonia.qvc.QvcProductDAO;
import seoclarity.backend.dao.clickhouse.kpcold.ClColdDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@CommonsLog
public class QvcUpload {

    private static final String SPLIT_FIELD = "\t";
    private static final String FILE_FOLDER = "/home/<USER>/";
    private static final int BATCH_INSERT_SIZE = 200;
    private static final int DEFAULF_USER = 214;
    private static final SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
    public static final String MD5_SPLIT = "	";
    private static final String TAG_PREFIX = "keyword-outofstock-";
    private static final int PID_SPLIT_SIZE = 100;

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private QvcProductDAO qvcProductDAO;
    private ResourceAddInfoEntityDAO resourceAddInfoEntityDAO;
    protected ResourceAddDetailEntityDAO resourceAddDetailEntityDAO;
    private ClColdDailyRankingEntityDao clColdDailyRankingEntityDao;
//    private ClDailyRankingEntityDao clDailyRankingEntityDao;


    public QvcUpload() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        qvcProductDAO = SpringBeanFactory.getBean("qvcProductDAO");
        resourceAddInfoEntityDAO = SpringBeanFactory.getBean("resourceAddInfoEntityDAO");
        resourceAddDetailEntityDAO = SpringBeanFactory.getBean("resourceAddDetailEntityDAO");
        clColdDailyRankingEntityDao = SpringBeanFactory.getBean("clColdDailyRankingEntityDao");
    }


    private void uploadFile(int domainId, Date processDate) throws Exception {

        log.info("=======processing uploadFile domainId:" + domainId + ",processDate:" + processDate);
        try {

            Integer count = qvcProductDAO.getCountByDate(FormatUtils.formatDateToYyyyMmDd(processDate), domainId);
            if (count != null && count > 0) {
                log.info("=== domainId:" + domainId + " already processed Date:" + processDate);
                return;
            }
            String localFolder = FILE_FOLDER + domainId + File.separator + "QVC";
            File folder = new File(localFolder);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            String remoteFilePath = getFile(6802, processDate);
            File remoteFile = new File(remoteFilePath);
            String localFilePath = localFolder + File.separator + remoteFile.getName();
            log.info("localFilePath:" + localFilePath);
            FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, remoteFilePath, localFolder, 1, 3);


            if (localFilePath.contains(".zip")) {
                File unzipFileFolder = ZipUtil.unzip(localFilePath);
                System.out.println("====unzip file:" + unzipFileFolder.getAbsolutePath());
                File[] files = unzipFileFolder.listFiles();
                for (File file : files) {

                    try {
                        processFile(domainId, processDate, file);
                    } catch (Exception e) {
                        log.error("==process error file :" + file.getAbsolutePath());
                        e.printStackTrace();
                        // TODO: 2021/3/2 send email
                    }
                }
            }

        } catch (Exception e) {
//            e.printStackTrace();
            throw e;
        }


    }

    private void processFile(int domainId, Date processDate, File file) throws Exception {
        log.info("=======processing domainId:" + domainId + ",processDate:" + processDate + ",file:" + file.getAbsolutePath());

        InputStreamReader inputReader = new InputStreamReader(new FileInputStream(file));
        BufferedReader bf = new BufferedReader(inputReader);
        String str;

        List<QvcProductEntity> qvcProductList = new ArrayList<>();
        while ((str = bf.readLine()) != null) {
            if (StringUtils.isBlank(str)) {
                System.out.println("==SkipEmptyLine");
                continue;
            }

            String[] arr = str.split(SPLIT_FIELD);
            if (arr != null && arr.length > 0) {

                if (arr[0].equals("ID") && arr[1].equals("Title") && arr[2].equals("Link")) {
                    log.info("====skip header!");
                    continue;
                }

                if (StringUtils.isBlank(arr[0])) {
                    log.warn("====empty fId, skip!");
                    continue;
                }

                String pId = null;
                try {
                    pId = arr[0].split("-")[0];
                } catch (Exception e) {
                    log.error("===split fId error:" + arr[0]);
                    continue;
                }

                QvcProductEntity qvcProductEntity = new QvcProductEntity();
                qvcProductEntity.setOwnDomainId(domainId);
                qvcProductEntity.setStockDate(FormatUtils.formatDateToYyyyMmDd(processDate));
                qvcProductEntity.setfId(arr[0]);
                qvcProductEntity.setpId(pId);
                qvcProductEntity.setTitle(arr[1]);
                qvcProductEntity.setLink(arr[5]);
                qvcProductEntity.setDescription(arr[2]);
                qvcProductEntity.setBrand(arr[12]);
                qvcProductEntity.setGtin(arr[13]);
                qvcProductEntity.setGoogleProductCategory(arr[3]);

                qvcProductList.add(qvcProductEntity);

                if (qvcProductList.size() >= BATCH_INSERT_SIZE) {
                    qvcProductDAO.batchInsert(qvcProductList);
                    log.info("====batchInsert:" + qvcProductList.size());
                    qvcProductList = new ArrayList<>();
                }
            }
        }

        if (CollectionUtils.isNotEmpty(qvcProductList)) {
            qvcProductDAO.batchInsert(qvcProductList);
            log.info("====batchInsert:" + qvcProductList.size());
        }

        bf.close();
        inputReader.close();
    }

    private String getFile(int domainId, Date processDate) {

        String filePath = "";
        if (domainId == 6802) {
            filePath = FILE_FOLDER + domainId + File.separator + "shopping" + File.separator + "QVC_SeoClarity" + FormatUtils.formatDate(processDate, "ddMMyyyy") + ".zip";
        }
        return filePath;
    }

    private void createTag(int domainId, Date processDate) {
        log.info("=======processing createTag domainId:" + domainId + ",processDate:" + processDate);

        int date = FormatUtils.formatDateToYyyyMmDd(processDate);
        String tag = TAG_PREFIX + date;
        List<String> tagList = new ArrayList<>();
        tagList.add(tag);
        try {
            addTags(domainId, tagList, GroupTagEntity.TAG_TYPE_KEYWORD);
        } catch (Exception e) {
            log.error("===add tag to queuebase error domainId:" + domainId + ",date:" + date + ",tag:" + tag);
            e.printStackTrace();
            //todo send email
        }


    }

    private void addKeywordsForTag(int domainId, Date processDate) {
        log.info("=======processing addKeywordsForTag domainId:" + domainId + ",processDate:" + processDate);

        Date yesterday = DateUtils.addDays(processDate, -3);
        int yesterdayStockDate = FormatUtils.formatDateToYyyyMmDd(yesterday);
        int processStockDate = FormatUtils.formatDateToYyyyMmDd(processDate);
        String yesterdayRankingDate = FormatUtils.formatDate(yesterday, FormatUtils.DATE_PATTERN_2);
        String processRankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityById(domainId);
        Assert.notNull(ownDomainEntity, "OwnDomain is Null, skip.");
        if (ownDomainEntity.isMobileDomain()) {
            log.error("SKIP!! Mobile domain.");
            return;
        }

        int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        boolean isMobile = false;
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

        List<String> pIdList = qvcProductDAO.getOutOfStockPidList(domainId, yesterdayStockDate, processStockDate);
        if (CollectionUtils.isEmpty(pIdList)) {
            log.info("===pIdList empty.");
            return;
        }
        log.info("===pIdList size:" + pIdList.size());

        List<List<String>> splitLists = CollectionUtil.split(pIdList, PID_SPLIT_SIZE);
        List<Long> kwRankCheckIdList = new ArrayList<>();
        for (List<String> pidList : splitLists) {

            /**
             *
            List<Long> uniqueKwList = clColdDailyRankingEntityDao.getQvcByDomain(domainId, engineId, languageId, isMobile,
                    pidList, yesterdayRankingDate, processRankingDate, isBroadMatch, domainReverse, rootDomainReverse);
            if (CollectionUtils.isNotEmpty(uniqueKwList)) {
                log.info("===uniqueKwList size:" + uniqueKwList.size());
                log.info("===uniqueKwList:" + JSON.toJSONString(uniqueKwList));
                kwRankCheckIdList.addAll(uniqueKwList);
            }
             */

        }

        log.info("====kwRankCheckIdList:" + kwRankCheckIdList.size());
        log.info("===kwRankCheckIdList:" + JSON.toJSONString(kwRankCheckIdList));
        List<String> kwList = new ArrayList<>();
        //check kw
        if (CollectionUtils.isNotEmpty(kwRankCheckIdList)) {
            for (Long rankCheckId : kwRankCheckIdList) {
                try {
                    Thread.sleep(1000 * 2);
                } catch (Exception e) {

                }

                /**
                 *
                 List<CLRankingDetailEntity> detailList = clColdDailyRankingEntityDao.getQvcKwUrl(domainId, engineId, languageId, isMobile,
                        rankCheckId, yesterdayRankingDate, processRankingDate, isBroadMatch, domainReverse, rootDomainReverse);
//                        log.info("===detailList:" + JSON.toJSONString(detailList));
                boolean isExist = false;
                String keywordName = null;
                for (CLRankingDetailEntity detail : detailList) {
                    if (detailList.size() > 1) {
                        String url = detail.getUrl();
                        String pid = url.substring(0, url.length() - 5);
                        pid = pid.substring(pid.lastIndexOf(".") + 1, pid.length());
                        log.info("===pid:" + pid);
                        Integer pidCount = qvcProductDAO.getCountByPid(processStockDate, domainId, pid);
                        if (pidCount != null && pidCount > 0) {
                            log.warn("===exist pid:" + pid + ",kw:" + detail.getKeywordName() + ",rankcheckId:" + rankCheckId + ",url:" + url);
                            isExist = true;
                        } else {
                            keywordName = detail.getKeywordName();
                        }
                    } else {
                        keywordName = detail.getKeywordName();
                    }

                }

                if (!isExist && StringUtils.isNotBlank(keywordName)) {
                    kwList.add(keywordName);
                }
                 */
            }
        }

        log.info("====kwList:" + kwList.size());
        log.info("===kwList:" + JSON.toJSONString(kwList));

        String tag = TAG_PREFIX + processStockDate;
        if (CollectionUtils.isNotEmpty(kwList)) {

            List<String[]> kwTagList = new ArrayList<String[]>();
            for (String kw : kwList) {
                kwTagList.add(new String[]{kw, tag});
            }
            try {
                log.info("====addKeywordTagToQueuebase :" + kwTagList.size());
                addKeywordTagRelation(kwTagList, domainId);
            } catch (Exception e) {
                log.error("===add kwTag to queuebase error domainId:" + domainId + ",tag:" + tag);
                e.printStackTrace();
            }

        }

    }

    public void addTags(int oid, List<String> tagList, int tagType) throws Exception {
        List<ResourceAddDetailEntity> processList = new ArrayList<ResourceAddDetailEntity>();
        if (tagList.size() == 0) {
            return;
        }
        for (String tag : tagList) {
            ResourceAddDetailEntity detail = new ResourceAddDetailEntity();
            detail.setOwnDomainId(oid);
            detail.setOperationType(ResourceAddInfoEntity.OPERATION_TYPE_TAG);
            detail.setResourceMain(tag);
            if (tagType == GroupTagEntity.TAG_TYPE_KEYWORD || tagType == GroupTagEntity.TAG_TYPE_TARGET_URL) {
                detail.setResourceSubordinate(String.valueOf(tagType));
            } else {
                System.out.println("###unknown tag type:" + tagType);
                return;
            }

            processList.add(detail);
        }
        System.out.println("###detail list:" + processList.size());
        batchAdd(processList, oid, ResourceAddInfoEntity.OPERATION_TYPE_TAG);
    }

    public void addKeywordTagRelation(List<String[]> relations, int oid) throws Exception {
        List<ResourceAddDetailEntity> addList = new ArrayList<ResourceAddDetailEntity>();
        for (String[] rel : relations) {
            String kw = rel[0];
            String tag = rel[1];
            if (StringUtils.isNotBlank(kw) && StringUtils.isNotBlank(tag)) {
                ResourceAddDetailEntity detail = new ResourceAddDetailEntity();
                detail.setResourceMain(kw);
                detail.setResourceSubordinate(tag);
                detail.setOwnDomainId(oid);
                addList.add(detail);
            }
        }
        batchAdd(addList, oid, ResourceAddInfoEntity.OPERATION_TYPE_KEYWORD_TAG);
    }

    public ResourceAddInfoEntity batchAdd(List<ResourceAddDetailEntity> addList, int oid, int operationType) throws Exception {
        ResourceAddInfoEntity info = addResourceAddInfo(oid, operationType);
        this.addAddDetail(addList, info.getId());
        System.out.println("===info ID:" + info.getId() + ", size:" + addList.size());
        return info;
    }

    protected ResourceAddInfoEntity addResourceAddInfo(int ownDomainId, int operationType) {
        ResourceAddInfoEntity info = new ResourceAddInfoEntity();
        info.setOperationType(operationType);
        info.setOwnDomainId(ownDomainId);
        info.setUserId(DEFAULF_USER);
        info.setCreateDate(new Date());
        info.setStatus(ResourceAddInfoEntity.STATUS_NEWLY_CREATED);
        int id = resourceAddInfoEntityDAO.insert(info);
        info.setId(id);
        System.out.println("Create addInfo, id:" + id + ", OID:" + ownDomainId);
        return info;
    }

    protected void addAddDetail(List<ResourceAddDetailEntity> detailList, int infoId) throws Exception {
        int createDate = Integer.valueOf(sf.format(new Date()));
        int count = 0;
        for (ResourceAddDetailEntity detail : detailList) {
            detail.setResourceInfoId(infoId);
            detail.setCreateDate(createDate);
            if (detail.getUserId() == null) {
                detail.setUserId(DEFAULF_USER);
            }
            try {
                if (StringUtils.isEmpty(detail.getResource_md5())) {
                    String md5 = calculateMd5(String.valueOf(detail.getResourceMain()), detail.getResourceSubordinate() + "");
                    detail.setResource_md5(md5);
                }
                if (isExistsDetail(detail.getOwnDomainId(), detail.getResource_md5(), detail.getCreateDate())) {
                    System.out.println("Exists detail, infoId:" + detail.getResourceInfoId() + ", OID:" + detail.getOwnDomainId()
                            + ", resourceMain:" + detail.getResourceMain()
                            + " resourceSub:" + detail.getResourceSubordinate() + ", OperationType" + detail.getOperationType() + ", md5:" + detail.getResource_md5()
                            + ", createDate:" + detail.getCreateDate());
                } else {
                    resourceAddDetailEntityDAO.insertIgnoreDup(detail);
                }
                count++;
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }
        }
        System.out.println("Create add details, infoId:" + infoId + ", insert count:" + count);
    }

    public static String calculateMd5(String valueMain, String valueSubordinate) {
        String input = valueMain;
        if (StringUtils.isNotBlank(valueSubordinate)) {
            input = input + MD5_SPLIT + valueSubordinate;
        }
        return FormatUtils.hexDigest(input.getBytes());
    }

    protected boolean isExistsDetail(int oid, String resourceMD5, int createDate) {
        ResourceAddDetailEntity detail = resourceAddDetailEntityDAO.checkExists(oid, resourceMD5, createDate);
        if (detail == null) {
            return false;
        }
        return true;
    }


    public static void main(String[] args) throws Exception{

        QvcUpload qvcUpload = new QvcUpload();
        Date processDate = FormatUtils.toDate("2023-05-31", FormatUtils.DATE_PATTERN_2);
        File processFile = new File("/home/<USER>/CVS_googleshopping_05312023.txt");
        qvcUpload.processFile(10619, processDate, processFile);//https://www.wrike.com/open.htm?id=1129579688

        /**
         *
        String[] domainIds = null;

        if (args != null && args.length > 0) {
            if (args[0].contains(",")) {
                domainIds = args[0].split(",");
            } else {
                domainIds = new String[]{args[0]};
            }
        } else {
            System.out.println("===param error!!");
            return;
        }

        String startDate;
        String endDate;
        Date sDate = null;
        Date eDate = null;
        if (args != null && args.length > 2 && !args[1].equalsIgnoreCase("null") && !args[2].equalsIgnoreCase("null")) {
            startDate = args[1];
            endDate = args[2];
            sDate = FormatUtils.toDate(args[1], FormatUtils.DATE_PATTERN_2);
            eDate = FormatUtils.toDate(args[2], FormatUtils.DATE_PATTERN_2);
        } else {
            Date sTime = FormatUtils.getYesterday(true);
            startDate = FormatUtils.formatDate(sTime, FormatUtils.DATE_PATTERN_2);
            endDate = FormatUtils.formatDate(sTime, FormatUtils.DATE_PATTERN_2);
            sDate = sTime;
            eDate = sTime;
        }


        System.out.println("=====domainIds: " + JSON.toJSONString(domainIds) + ",startDate: " + startDate + ",endDate: " + endDate);

        QvcUpload qvcUpload = new QvcUpload();

        for (String domainIdStr : domainIds) {
            int domainId = Integer.parseInt(domainIdStr);
            while (sDate.compareTo(eDate) <= 0) {
                try {
                    qvcUpload.uploadFile(domainId, sDate);
//                    qvcUpload.createTag(domainId, sDate);
                    qvcUpload.addKeywordsForTag(domainId, sDate);
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
                sDate = DateUtils.addDays(sDate, 1);
            }
        }
         */
    }

}
