package seoclarity.backend.upload.gabigquery;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.jets3t.service.impl.rest.httpclient.GoogleStorageService;
import org.jets3t.service.model.GSObject;
import org.jets3t.service.security.GSCredentials;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.bigquery.Bigquery;
import com.google.api.services.bigquery.model.QueryRequest;
import com.google.api.services.bigquery.model.QueryResponse;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.BigQueryOptions;
import com.google.cloud.bigquery.Dataset;
import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.gabigquery.GaBqBulkExportInfoDAO;
import seoclarity.backend.dao.actonia.gabigquery.GaBqBulkExportInstanceDAO;
import seoclarity.backend.entity.actonia.GaBigQueryMessageVO;
import seoclarity.backend.entity.actonia.gabigquery.GaBqBulkExportInstanceEntity;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.SQSUtils;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.gabigquery.GaV4BigQueryCrawler" -Dexec.args=""
public class GaV4BigQueryCrawler {
    //	private static boolean isBackProcess = false;
    private static int specialVersion = 0;


    private String gaFileName = null;
    
    private HttpTransport TRANSPORT = new NetHttpTransport();
    private JsonFactory JSON_FACTORY = new JacksonFactory();
    private List<String> SCOPE = Arrays.asList("https://www.googleapis.com/auth/bigquery");
//    private String privateKeyPath = "/home/<USER>/seoclarity.net_seoclarity-3c405556df2b.p12";
    private String privateJsonKeyPath = "/home/<USER>/seoclarity.net_seoclarity-fa1df79871e0.json";

    public static int VisibilityTimeout = 2 * 60 * 60;
    private GaBigQueryMessageVO gaBigQueryMessageVO;

    public static final Log logger = LogFactory.getLog(GaV4BigQueryCrawler.class);

//    static final String GOOGLE_OAUTH2_CLIENT_ID = "228245862190.apps.googleusercontent.com";
//    static final String GOOGLE_OAUTH2_CLIENT_SECRET = "GcNHSqm63s_HqAAqIe5eJe4u";
    static final String NOT_SET = "(not set)";
    static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    static final SimpleDateFormat gsdf = new SimpleDateFormat("yyyyMMdd");
    static final SimpleDateFormat bsdf = new SimpleDateFormat("yyyyMMddHH");
    
    private String googleCloudFullPath = "gs://seoclarity-ga4-bq-bucket/%s/%s/%s_file*.csv";
    private String googleCloudKeyPath = "%s/%s/%s_file*.csv";
    private static String accessKey = "GOOGKIPHII7B2RAKQQIB";
    private static String secretKey = "IlJnU4vLPX+3e+vJUG+ks/+k2g7YNdMPxCFiNaax";
    private final static String GA_GOOGLE_CLOUD_BUCKET = "seoclarity-ga4-bq-bucket";
    private static String storeDoneFilePath = "/home/<USER>/gaBigQuery/needUpload/";
    private static String storeTempFilePath = "/home/<USER>/gaBigQuery/crawling/";

    static final int OFF_SET = 100000;


    public static final int TARGETURL_ADDEDBY_GA = 3;

    public static final int TARGETURL_ADDEDBY_CONVERSION = 99;

    private String queryUrl;
    private AmazonSQS amazonSQS;
    
    boolean shouldCrawlForToday;
    
    private GaBqBulkExportInstanceDAO gaBqBulkExportInstanceDAO;
    private GaBqBulkExportInfoDAO gaBqBulkExportInfoDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    private static String GA_BIGQUERY_DAILY_QUEUE = "GA_BIGQUERY_DAILY_QUEUE";
    
    private static String EXPORT_ORGANIC_SQL_TEMPLATE =
    		" #standardSQL \n  EXPORT DATA OPTIONS (uri = '%s', format = 'CSV', overwrite=true,header = true,  field_delimiter = '\t') " +
                    "AS (SELECT "
        			+ " t.event_name,"
        			+ " count(t.event_name) as event_count,"
        			+ " t.geo.country as country,"
        			+ " t.device.web_info.hostname as hostname,"
        			+ " countIf( event_name = \"screen_view\" or event_name ='page_view' ) as pageviews,"
        			+ " concat(t.platform,'/', t.device.category) as platformDeviceCat,"
        			+ " t.traffic_source.source as source,"
        			+ " COUNT(DISTINCT CONCAT(user_pseudo_id, (SELECT value.int_value FROM UNNEST(event_params) WHERE key = 'ga_session_id'))) AS unique_sessoions,"
//        			+ " (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'page_location' and event_name = \"session_start\" ) as url ,"
        			+ " (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'page_location' ) as url ,"
        			+ " sum(t.ecommerce.purchase_revenue) as purchaseRevenue,"
        			+ " count(t.ecommerce.transaction_id) as transactions,"
        			+ " count(distinct case when (select value.string_value from unnest(event_params) where key = 'session_engaged') = '1' then concat(user_pseudo_id,(select value.int_value from unnest(event_params) where key = 'ga_session_id')) end) as engaged_sessions,"
        			+ " safe_divide(count(distinct case when (select value.string_value from unnest(event_params) where key = 'session_engaged') = '1' then concat(user_pseudo_id,(select value.int_value from unnest(event_params) where key = 'ga_session_id')) end),count(distinct concat(user_pseudo_id,(select value.int_value from unnest(event_params) where key = 'ga_session_id')))) as engagement_rate, "
        			+ " sum((SELECT COALESCE(value.int_value, value.float_value, value.double_value) FROM UNNEST(event_params) WHERE key = 'value')) as event_value "
        			+ " FROM `%s` t"
        			+ " where "
        			+ " t.traffic_source.medium = 'organic' "
        			+ " group by "
        			+ " t.event_name,"
        			+ " t.geo.country,"
        			+ " platformdevicecat,"
        			+ " t.traffic_source.source,"
        			+ " t.device.web_info.hostname,"
        			+ " url )";
    
    private static String EXPORT_MIX_TRAFFIC_SQL_TEMPLATE =
    		" #standardSQL \n  EXPORT DATA OPTIONS (uri = '%s', format = 'CSV', overwrite=true,header = true,  field_delimiter = '\t') " +
                    "AS (SELECT "
        			+ " t.event_name,"
        			+ " count(t.event_name) as event_count,"
        			+ " t.geo.country as country,"
        			+ " t.device.web_info.hostname as hostname,"
        			+ " countIf( event_name = \"screen_view\" or event_name ='page_view' ) as pageviews,"
        			+ " concat(t.platform,'/', t.device.category) as platformDeviceCat,"
        			+ " t.traffic_source.source as source,"
        			+ " COUNT(DISTINCT CONCAT(user_pseudo_id, (SELECT value.int_value FROM UNNEST(event_params) WHERE key = 'ga_session_id'))) AS unique_sessoions,"
//        			+ " (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'page_location' and event_name = \"session_start\" ) as url ,"
        			+ " (SELECT value.string_value FROM UNNEST(event_params) WHERE key = 'page_location' ) as url ,"
        			+ " sum(t.ecommerce.purchase_revenue) as purchaseRevenue,"
        			+ " count(t.ecommerce.transaction_id) as transactions,"
        			+ " count(distinct case when (select value.string_value from unnest(event_params) where key = 'session_engaged') = '1' then concat(user_pseudo_id,(select value.int_value from unnest(event_params) where key = 'ga_session_id')) end) as engaged_sessions, "
        			+ " safe_divide(count(distinct case when (select value.string_value from unnest(event_params) where key = 'session_engaged') = '1' then concat(user_pseudo_id,(select value.int_value from unnest(event_params) where key = 'ga_session_id')) end),count(distinct concat(user_pseudo_id,(select value.int_value from unnest(event_params) where key = 'ga_session_id')))) as engagement_rate, "
        			+ " sum((SELECT COALESCE(value.int_value, value.float_value, value.double_value) FROM UNNEST(event_params) WHERE key = 'value')) as event_value, "
        			+ " t.traffic_source.medium  "
        			+ " FROM `%s` t"
        			+ " where "
        			+ " t.traffic_source.medium != 'organic' "
        			+ " group by "
        			+ " t.event_name,"
        			+ " t.geo.country,"
        			+ " platformdevicecat,"
        			+ " t.traffic_source.source,"
        			+ " t.device.web_info.hostname,"
        			+ " t.traffic_source.medium,"
        			+ " url )";
    

    public GaV4BigQueryCrawler() {
    	gaBqBulkExportInstanceDAO = SpringBeanFactory.getBean("gaBqBulkExportInstanceDAO");
    	gaBqBulkExportInfoDAO = SpringBeanFactory.getBean("gaBqBulkExportInfoDAO");
    	zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    public static void main(String args[]) {
        if (args.length >= 1) {
        	GA_BIGQUERY_DAILY_QUEUE = args[0];
            if (args.length >= 2) {
                if (StringUtils.isNotBlank(args[1])) {
                    storeDoneFilePath = args[1];
                }
            }
        }
        

        GaV4BigQueryCrawler crawlGoogalAnalyticsEngine = new GaV4BigQueryCrawler();
//		System.out.println("--- isBackProcess: " + isBackProcess + ", specialVersion:" + specialVersion + ", use version:" + version + ", storeFilePath:" + storeFilePath);
        System.out.println("--- specialVersion:" + specialVersion + ", storeFilePath:"
                + storeDoneFilePath + ", QueueName: " + GA_BIGQUERY_DAILY_QUEUE);
        crawlGoogalAnalyticsEngine.process();
    }

    private void process() {

        try {
            // init AmazonSQS and Queues
            initAmazonSQS();
            initNormalQueue();

            System.out.println("======Processing Normal Queue!!" + queryUrl);
            processNormalQueue();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initAmazonSQS() throws Exception {

		try{
		
			AwsCredentialsEnvKeyConstructor awsCredentialsEnvKeyConstructor = new AwsCredentialsEnvKeyConstructor();
			String accessKeyId = awsCredentialsEnvKeyConstructor.getPlainTextSQSAccessKey();
			String secretAccessKey = awsCredentialsEnvKeyConstructor.getSQSDecryptedSecretKey();
            amazonSQS = SQSUtils.getAmazonSQS(accessKeyId, secretAccessKey);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Init AmazonSQS failed!");
            return;
        }
        if (amazonSQS == null) {
            System.out.println("Init AmazonSQS failed!");
            return;
        }

    }

    private void initNormalQueue() throws Exception {

        try {
            queryUrl = SQSUtils.createQueue(GA_BIGQUERY_DAILY_QUEUE, amazonSQS);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Init Normal Queue failed!");
            return;
        }

        if (StringUtils.isBlank(queryUrl) || amazonSQS == null) {
            System.out.println("Init Normal Queue failed!");
            return;
        }

    }

    private void processNormalQueue() {

        int num = 0;

        while (true) {
            List<Message> messages = SQSUtils.getMessageFromQueue(amazonSQS, queryUrl, 1, VisibilityTimeout);
            System.out.println(" message size: " + messages.size() + " ,VisibilityTimeout: " + VisibilityTimeout);

            if (messages != null && messages.size() > 0) {
            	num = 0;

                for (Message m : messages) {

                    try {
//                    	Map<String, String> attrMap = m.getAttributes();
//                    	Integer approximateReceiveCount = NumberUtils.toInt(attrMap.get("ApproximateReceiveCount"));
                    	gaBigQueryMessageVO = new Gson().fromJson(m.getBody(), GaBigQueryMessageVO.class);
                    	
                        System.out.println("PDate:" + gaBigQueryMessageVO.getProcessDate());
                        crawlEntrance(queryUrl, m);
                        
                    } catch (Exception e) {
                    	e.printStackTrace();

						SQSUtils.deleteMessageFromQueue(amazonSQS, queryUrl, m, "0");
						gaBqBulkExportInstanceDAO.updateExtractStatus(gaBigQueryMessageVO.getInstanceId(), GaBqBulkExportInstanceEntity.EXTRACT_STATUS_ERROR, "");
 
                        if (StringUtils.isNotBlank(gaFileName)) {
                            File tmpFile = new File(gaFileName);
                            if (tmpFile != null && tmpFile.exists()) {
                                System.out.println("===Delete tmp file :" + gaFileName);
                                tmpFile.delete();
                            } else {
                                System.out.println("===file is not exist :" + gaFileName);
                            }
                        }
                        continue;
                    }
                    gaFileName = "";
                    
                }
            } else {
                num ++;
                
                try {
					Thread.sleep(5 * 1000);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
                
                if (num >= 50 ) {
                	System.out.println("======All domain process done!!!");
					break;
				} 
            }
        }
    }


    private void crawlEntrance(String queueUrl, Message m) {

        System.out.println(new Gson().toJson(gaBigQueryMessageVO));

        processMessage(gaBigQueryMessageVO, queueUrl, m);
    }

    private void processMessage(GaBigQueryMessageVO vo, String queueUrl, Message m) {

        Integer domainId = vo.getOwnDomainId();
        String tableName = vo.getTableName();
        String processDate = vo.getProcessDate();
        
        String filePrefix = "ga_bq_" + (vo.getIsOrganic() ? GaBqBulkExportInstanceEntity.TRAFFIC_TYPE_ORGANIC : GaBqBulkExportInstanceEntity.TRAFFIC_TYPE_MIX_TRAFFIC) 
        		+ "_" + domainId + "_" + processDate;
        
        try {
        	// gs://seoclarity-ga4-bq-bucket/%s/%s/%s_files*.csv
            String fileFullPath = String.format(googleCloudFullPath, domainId, processDate, filePrefix);
            String keyPath = String.format(googleCloudKeyPath, domainId, processDate, filePrefix);
            String bqLogSql = String.format((vo.getIsOrganic() ? EXPORT_ORGANIC_SQL_TEMPLATE : EXPORT_MIX_TRAFFIC_SQL_TEMPLATE), fileFullPath, tableName);

            System.out.println("#bqLogSql:" + bqLogSql);
            String prefix = StringUtils.substringBeforeLast(keyPath, "*");
            QueryResponse queryResponse = getBQExportLogData(domainId, bqLogSql, vo.getProjectId(), vo.getDatabaseId());
            boolean isSuccess = downLoadFileFromGoogleCloud(domainId, prefix, processDate, vo.getIsOrganic());

            SQSUtils.deleteMessageFromQueue(amazonSQS, queryUrl, m, "0");
            if (isSuccess) {
            	gaBqBulkExportInstanceDAO.updateExtractStatus(gaBigQueryMessageVO.getInstanceId(), GaBqBulkExportInstanceEntity.EXTRACT_STATUS_COMPLETE, prefix);
			} else {
				gaBqBulkExportInstanceDAO.updateExtractStatus(gaBigQueryMessageVO.getInstanceId(), GaBqBulkExportInstanceEntity.EXTRACT_STATUS_ERROR, prefix);
			}
            
//            uploadToCDBFromGoogleCloud(domainId, vo.getInfoId(), fileFullPath, keyPath, processDate, vo.getIsOrganic());
        } catch (GoogleJsonResponseException gex) {
        	
        	if (StringUtils.contains(gex.getMessage(), "Not found: Table")) {
				System.out.println("table not exist, will try next hour!");
			} else {
				int errorCode = gex.getDetails().getCode();
	            String errorTrace = gex.getDetails().getMessage();
	            if (errorTrace.length() >= 1020) {
	                errorTrace = gex.getDetails().getMessage().substring(0, 1020);
	            }
	            String errorMessage = errorCode + gex.getDetails().getErrors().get(0).getReason();
	            if (errorMessage.length() >= 125) {
	                errorMessage = errorMessage.substring(0, 125);
	            }
	            
	            SQSUtils.deleteMessageFromQueue(amazonSQS, queryUrl, m, "0");
	            gaBqBulkExportInfoDAO.updateErrorStatus(vo.getInfoId(), errorMessage, errorTrace);
				gaBqBulkExportInstanceDAO.updateExtractStatus(gaBigQueryMessageVO.getInstanceId(), GaBqBulkExportInstanceEntity.EXTRACT_STATUS_ERROR, "");
			}

        } catch (Exception e) {
            e.printStackTrace();
            String errorMessage = e.getMessage();
            if (errorMessage.length() >= 125) {
                errorMessage = errorMessage.substring(0, 125);
            }
            String errorTrace = e.getMessage();
            if (errorMessage.length() >= 1020) {
                errorTrace = errorTrace.substring(0, 1020);
            }
            
            SQSUtils.deleteMessageFromQueue(amazonSQS, queryUrl, m, "0");
			gaBqBulkExportInstanceDAO.updateExtractStatus(gaBigQueryMessageVO.getInstanceId(), GaBqBulkExportInstanceEntity.EXTRACT_STATUS_ERROR, "");
            gaBqBulkExportInfoDAO.updateErrorStatus(vo.getInfoId(), errorMessage, errorTrace);
            String subject = "Filed saveLogBy GSCBigquery domainId:" + domainId + ",infoId:" + vo.getInfoId();
            String message = "Filed Upload GSCBigquery domainId:" + domainId + ",infoId:" + vo.getInfoId();
            sendMailReport(subject, message);
        }

        try {

        	System.out.println("Sleeping 5 secs");
            Thread.sleep(5 * 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    
    public boolean downLoadFileFromGoogleCloud(int oid, String prefix, String processDate, Boolean isOrganic) throws Exception {
    	System.out.println("*******downLoadFile:" + prefix + ",fileName:" + prefix);
        GSCredentials gsCredentials = new GSCredentials(accessKey, secretKey);
        GoogleStorageService gsService = new GoogleStorageService(gsCredentials);

        try {
            long a = System.currentTimeMillis();
            
            List<String> fileList = new ArrayList<String>();
            GSObject[] gsObjects = gsService.listObjects(GA_GOOGLE_CLOUD_BUCKET, prefix, null);
            
            if (ArrayUtils.isEmpty(gsObjects)) {
				System.out.println("Files no found!");
				return false;
			} else {
				System.out.println(" Total file cnt : " + gsObjects.length);
			}
            
            for (GSObject gsObject : gsObjects) {
                System.out.println("====gsKey:" + gsObject.getKey());
                try {
                    gsObject = gsService.getObject(GA_GOOGLE_CLOUD_BUCKET, gsObject.getKey());
                    InputStream gsObjectDataInputStream = gsObject.getDataInputStream();
                    System.out.println("===gsObjectDataInputStream:" + gsObjectDataInputStream);
                    File localFolder = new File(storeTempFilePath);
                    if (!localFolder.exists()) {
                        localFolder.mkdirs();
                    }
                    
                    String downloadFileName = StringUtils.substringAfterLast(gsObject.getKey(), "/");
                    File localFile = new File(storeTempFilePath + downloadFileName);//可能是多个gcs文件写入
                    
                    if(localFile.exists()){
                        localFile.delete();
                    }
                    
                    FileOutputStream fos = new FileOutputStream(localFile, true);
                    byte[] read_buf = new byte[1024];
                    int read_len = 0;
                    while ((read_len = gsObjectDataInputStream.read(read_buf)) > 0) {
                        fos.write(read_buf, 0, read_len);
                    }
                    gsObjectDataInputStream.close();
                    fos.close();
                    System.out.println(localFile.length());

                    long b = System.currentTimeMillis();
                    System.out.println("downUpload from GoogleCloudStorage: " + (b - a) * 1.0 / 1000 + " s ");

                    fileList.add(localFile.getAbsolutePath());

                } catch (IOException e) {
                    throw e;
                }
            }
            
            for(String filePath : fileList) {
            	renameFileAfterProcessDone(filePath);
            }
            
            return true;

        } catch (Exception e) {
            throw e;
        }

    }
    
    private void sendMailReport(String subject, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Scott");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String[] ccTo = new String[]{"<EMAIL>", "<EMAIL>"};
//        String[] ccTo = null;
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }
    
    private QueryResponse getBQExportLogData(int domainId, String sql, String projectId, String databaseId) throws Exception {

        try {
            Bigquery bigquery;
            GoogleCredential credential = GoogleCredential.fromStream(new FileInputStream(privateJsonKeyPath))
                    .createScoped(SCOPE);

            bigquery = new Bigquery.Builder(TRANSPORT, JSON_FACTORY, credential)
                    .setApplicationName("BigQuery-Service-Accounts/0.1").setHttpRequestInitializer(credential).build();

            QueryRequest queryRequest = new QueryRequest();
            queryRequest.setQuery(sql);

            String location = getLocation(projectId, databaseId);
            if(StringUtils.isNotBlank(location)){
                queryRequest.setLocation(location);
            }

            Bigquery.Jobs.Query query = bigquery.jobs().query(projectId, queryRequest);
//            System.out.println("======1111111 : " + query.execute());
            QueryResponse queryResponse = query.execute();

//            System.out.println("======1111111 : " + query.execute());
            return queryResponse;

        } catch (GoogleJsonResponseException gex) {
            gex.printStackTrace();
//            System.out.println("=====1= " + JSON.toJSONString(gex.getDetails()));
//            System.out.println("=====2= " + JSON.toJSONString(gex.getDetails().getCode()));
            throw gex;
        } catch (Exception e) {
            throw e;
        }
    }
    
    private String getLocation(String projectId, String databaseId){

        try {
            ServiceAccountCredentials credentials =
                    ServiceAccountCredentials.fromStream(new FileInputStream(privateJsonKeyPath));
            BigQueryOptions options = BigQueryOptions.newBuilder()
                    .setCredentials(credentials)
                    .setProjectId(projectId)
                    .build();
            // Initialize BigQuery client
            BigQuery bigquery = options.getService();
            // Define dataset and table IDs
            Dataset dataset = bigquery.getDataset(databaseId);
            String location = dataset.getLocation();
            System.out.println("***databaseLocation: " + location);
            return location;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }

    }
    
    private void renameFileAfterProcessDone(String fileFullPath) {

        File processFile = new File(fileFullPath);

        if (processFile == null || !processFile.isFile()) {
            System.out.println("File is not exist!!! file:" + fileFullPath);
            return;
        }

        String fileName = processFile.getName();

        System.out.println("targetFileName:" + fileName);

        try {

            File doneFolder = new File(storeDoneFilePath);
            if (doneFolder == null || !doneFolder.isDirectory()) {
                System.out.println("Target folder is not exist!!! folder:" + fileFullPath);
                doneFolder.mkdirs();
            }

            File targetFile = new File(doneFolder + "/" + fileName);

            FileUtils.moveFile(processFile, targetFile);

            System.out.println("Moved file from " + doneFolder.getAbsolutePath() + " to " + targetFile.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }



}
