
package seoclarity.backend.upload.gabigquery;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.gabigquery.GaBqBulkExportInstanceDAO;
import seoclarity.backend.dao.clickhouse.ga.GaClarityDBEntityDAO;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.gabigquery.GaBqBulkExportInstanceEntity;
import seoclarity.backend.entity.actonia.gscbigquery.GscBqBulkExportInstanceEntity;
import seoclarity.backend.entity.clickhouse.ga.GoogleAnalyticsV4Entity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.gabigquery.GaV4BigQueryUploader" -Dexec.args="1"
//mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.gabigquery.GaV4BigQueryUploader" -Dexec.args="2"
public class GaV4BigQueryUploader {
	private static int specialVersion = 0;

	private static Integer startHour;
	private static Date processDate = new Date();
	private static boolean isBkProcess = false;

	private static final String databaseName = "actonia_site_analytics";
	private static String organicFinalTableName = "dis_analytics_ga4bq_raw";
	private static String mixTrafficFinalTableName = "dis_mix_ga4bq_raw";
	
	static final String NOT_SET = "(not set)";
	private static final int RETRY_TIMES = 3;
	private static final String SPLIT_FIELD = "\t";
	
	private String bqSummaryTable = "";
	private String bqMixTrafficSummaryTable = "";

	public static final Log logger = LogFactory.getLog(GaV4BigQueryUploader.class);

	private static String basePath = "/home/<USER>/gaBigQuery";

	private static String storeDoneFilePath;
	private static String storeTempFilePath;
	private static String storeBKFilePath;
	private static String storeDuplicateFilePath;

	public static final int TARGETURL_ADDEDBY_GA = 3;

	public static final int TARGETURL_ADDEDBY_CONVERSION = 99;

	private GaClarityDBEntityDAO gaClarityDBEntityDAO;

	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private GaBqBulkExportInstanceDAO gaBqBulkExportInstanceDAO;

	private static List<String> processingFileList = new ArrayList<String>();

	private static Integer logId;

	public GaV4BigQueryUploader() {

		gaClarityDBEntityDAO = SpringBeanFactory.getBean("gaClarityDBEntityDAO");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		gaBqBulkExportInstanceDAO = SpringBeanFactory.getBean("gaBqBulkExportInstanceDAO");

	}

	private static Integer processType = 0;

	private static Integer uploadType = ClarityDBUploadLogEntity.UPLOAD_TYPE_GA4_BIGQUERY;

	private static String filePrefix = "ga_bq_" + processType;

	public static void main(String args[]) {

		if (args.length > 0) {
			processType = NumberUtils.toInt(args[0]);
			if (processType == 1) {
				uploadType = ClarityDBUploadLogEntity.UPLOAD_TYPE_GA4_BIGQUERY;
			} else if (processType == 2) {
				uploadType = ClarityDBUploadLogEntity.UPLOAD_TYPE_MIX_TRAFFIC_GA4_BIGQUERY;
			} else {
				System.out.println("Param Incorrect! processType:" + processType);
				return ;
			}
		} else {
			System.out.println("Missing Param! ");
			return ;
		}
		
		filePrefix = "ga_bq_" + processType;
		
		System.out.println("processType:" +  processType + ", filePrefix:" + filePrefix);

		storeDoneFilePath = basePath + "/needUpload/";
		storeTempFilePath = basePath + "/uploading/";
		storeBKFilePath = basePath + "/backUpFolder/";
		storeDuplicateFilePath = basePath + "/duplicate/";

		storeTempFilePath = storeTempFilePath + "/" + FormatUtils.formatDate(processDate, "yyyyMMddHH") + "_" + processType + "/";

		GaV4BigQueryUploader crawlGoogalAnalyticsEngine = new GaV4BigQueryUploader();
		System.out.println("--- specialVersion:" + specialVersion + ", storeDoneFilePath:"
				+ storeDoneFilePath + ", 30s to continue...");

		Calendar cal = Calendar.getInstance();
		cal.setTime(processDate);
		startHour = cal.get(Calendar.HOUR_OF_DAY);

		if (crawlGoogalAnalyticsEngine.checkIsProcessingUpload()) {
			System.out.println("There found more than one processing still not finished, exit !!!");
			System.exit(-1);
		}
		
		File doneFolder = new File(storeDoneFilePath);
		if (doneFolder != null && doneFolder.isDirectory()) {
			boolean hasFilesNeedProcess = false;

			for (File file : doneFolder.listFiles()) {
				if (StringUtils.startsWith(file.getName(), filePrefix) && file.isFile()) {

					System.out.println("Found file " + file.getName() + ", start to process !");
					hasFilesNeedProcess = true;
					break;
				}
			}

			if (!hasFilesNeedProcess) {

				crawlGoogalAnalyticsEngine.insertEmptyLog();
				System.out.println("There do not have any files need to be process, skiped+++");
				System.exit(-1);
			}

		}

		try {
			Thread.sleep(5000);
			if (crawlGoogalAnalyticsEngine.checkTableForCurrentHour(processDate)) {
				crawlGoogalAnalyticsEngine.process();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// if no files need to be upload, then insert one record with final status =
	// success(2) and upload status = success(2)
	private void insertEmptyLog() {

		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();

		clarityDBUploadLogEntity.setTmpTableName("");
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName(getFinalTableNameByProcessType());
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadDailyRows(0);
		clarityDBUploadLogEntity.setFinalTableUploadRows(0);
		clarityDBUploadLogEntity.setUploadType(getUploadTypeByProcessType());

		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}

		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadEndTime(new Date());
		clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}
	
	private String getFinalTableNameByProcessType() {
		if (processType == 1) {
			return  organicFinalTableName;
		} else {
			return mixTrafficFinalTableName;
		}
	}
	
	private Integer getUploadTypeByProcessType() {
		if (processType == 1) {
			return ClarityDBUploadLogEntity.UPLOAD_TYPE_GA4_BIGQUERY;
		} else {
			return ClarityDBUploadLogEntity.UPLOAD_TYPE_MIX_TRAFFIC_GA4_BIGQUERY;
		}
	}
	
	private String getTempTableNameByProcessType() {
		if (processType == 1) {
			return bqSummaryTable;
		} else {
			return bqMixTrafficSummaryTable;
		}
	}

	private boolean checkIsProcessingUpload() {

		List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(uploadType);
		if (CollectionUtils.isNotEmpty(list)) {
			return true;
		}
		return false;
	}

	private void process() {

		long startTime = System.currentTimeMillis();

		File duplicateFolder = new File(storeDuplicateFilePath);
		if (duplicateFolder == null || !duplicateFolder.isDirectory()) {
			duplicateFolder.mkdir();
		}

		moveFilesToProcessingFolder();

		// insert into temp table
		if (StringUtils.isNotBlank(getTempTableNameByProcessType())) {

			ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();

			clarityDBUploadLogEntity.setTmpTableName(getTempTableNameByProcessType());
			clarityDBUploadLogEntity.setDatabaseName(databaseName);
			clarityDBUploadLogEntity.setFinalTableName(getFinalTableNameByProcessType());
			clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
			clarityDBUploadLogEntity.setUploadType(getUploadTypeByProcessType());
			try {
				clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}

			clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
			clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);

			logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
			System.out.println("===add log for ga_upload_log, id:" + logId);

			// process normal
			File doneFolder = new File(storeDoneFilePath);
			if (doneFolder == null || !doneFolder.isDirectory()) {
				System.out.println("Folder is not exist :" + doneFolder);
				long endTime = System.currentTimeMillis();
				int elapsedSeconds = (int) (endTime - startTime);

				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0,
						elapsedSeconds, logId);
				return;
			}
			try {

				List<File> fileList = new ArrayList<File>();
				for (String uniqueKey : fileMap.keySet()) {
					
					fileList = fileMap.get(uniqueKey);
					
					if(CollectionUtils.isEmpty(fileList)) {
						System.out.println("File list is empty, key:" + uniqueKey);
						continue;
					}
					
					String[] arrays = StringUtils.split(uniqueKey, "_");

					Integer trafficType = NumberUtils.toInt(arrays[0]);
					Integer ownDomainId = NumberUtils.toInt(arrays[1]);
					Date trafficDate = FormatUtils.toDate(arrays[2], "yyyy-MM-dd");
					
					GaBqBulkExportInstanceEntity gaBqBulkExportInstance = gaBqBulkExportInstanceDAO.getInstanceByDate(ownDomainId, 
							FormatUtils.formatDate(trafficDate, "yyyy-MM-dd"), processType);
					
					
					Integer count = gaClarityDBEntityDAO.checkExist(getFinalTableNameByProcessType(), ownDomainId, trafficDate, 1);

					if (count > 0) {
						System.out.println("==== data exist in clarityDB : OID:" + ownDomainId + ", trafficDate:" + trafficDate
								+ ", version:" + 1 + ", file size:" + fileList.size());

						fileList.stream().forEach(file -> {
							try {
								FileUtils.moveFile(file, new File(storeDuplicateFilePath + "/" + file.getName()));
							} catch (IOException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
						});
						
						if (gaBqBulkExportInstance != null && gaBqBulkExportInstance.getUploadStatus() != GscBqBulkExportInstanceEntity.UPLOAD_STATUS_COMPLETE) {
							gaBqBulkExportInstanceDAO.updateUploadStatus(gaBqBulkExportInstance.getId(), GscBqBulkExportInstanceEntity.UPLOAD_STATUS_COMPLETE);
			            }
						
						continue;
					}
					
					for(File file : fileList) {

						if (file == null || !file.isFile()) {
							System.out.println("File is not exist or is a folder : " + file.getAbsolutePath());
							continue;
						}

						System.out.println("=====processing file:" + file.getAbsolutePath());
						processFile(file, trafficType, ownDomainId, trafficDate);
					}
					
					if (gaBqBulkExportInstance != null && gaBqBulkExportInstance.getUploadStatus() != GscBqBulkExportInstanceEntity.UPLOAD_STATUS_COMPLETE) {
						gaBqBulkExportInstanceDAO.updateUploadStatus(gaBqBulkExportInstance.getId(), GscBqBulkExportInstanceEntity.UPLOAD_STATUS_COMPLETE);
		            }
				}

				long endTime = System.currentTimeMillis();
				int elapsedSeconds = (int) (endTime - startTime);

				Integer totalCount = gaClarityDBEntityDAO.getTotalCountForBigQuery(getTempTableNameByProcessType());
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS,
						totalCount, elapsedSeconds, logId);

			} catch (Exception e) {
				System.out
						.println("Updated status in log table to " + ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE);
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
				System.out.println("Insert table failed, table: " + getTempTableNameByProcessType() + ", folder: " + storeDoneFilePath);

				moveFilesBackProcessingFolder();
				e.printStackTrace();
				return;
			}

			try {
				moveFileAndZip();
			} catch (Exception e) {
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS_MOVE_FILE_FAIL, logId);
			} finally {
				deleteTempProcessingFolder();
			}

		} else {
			System.out.println("====== tmpTable is empty : " + getTempTableNameByProcessType());
		}

	}
	
	private Map<String, List<File>> fileMap = new HashMap<String, List<File>>();

	private void moveFilesToProcessingFolder() {


		File targetFolder = new File(storeTempFilePath);
		if (targetFolder == null || !targetFolder.isDirectory()) {
			System.out.println("Folder is not exist, mkdir: " + storeTempFilePath);
			targetFolder.mkdir();
		} else {
			System.out.println("Folder already exist! Skip " + storeTempFilePath);
			return;
		}

		File doneFolder = new File(storeDoneFilePath);

		List<File> fileList = new ArrayList<File>();
		

		for (File gaFile : doneFolder.listFiles()) {
			try {
				if (StringUtils.startsWith(gaFile.getName(), filePrefix) && gaFile.isFile()) {

					// String prefix = "ga_v4_" + ownDomainId + "_" + gaInfoVO.getTrafficDate() +
					// "_" + gaInfoVO.getVersion() + "_" + (isBackProcess ? "bk_" : startHour +
					// "_");
					
					String[] arrays = StringUtils.split(gaFile.getName(), "_");

					String trafficType = arrays[2];
					String ownDomainId = arrays[3];
					String trafficDate = arrays[4];
					
					String uniqueKey = trafficType + "_"  + ownDomainId + "_" + trafficDate;
					
					fileList = fileMap.get(uniqueKey);
					if(CollectionUtils.isEmpty(fileList)) {
						fileList = new ArrayList<File>();
					}
					
					File uploadingFile = new File(storeTempFilePath + "/" + gaFile.getName());
					System.out.println("trafficType:" + trafficType + ", ownDomainId:" + ownDomainId + ", trafficDate:" + trafficDate);
					FileUtils.moveFile(gaFile, uploadingFile);
					
					fileList.add(uploadingFile);
					fileMap.put(uniqueKey, fileList);
					
					processingFileList.add(storeTempFilePath + "/" + gaFile.getName());

				}
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		System.out.println("====moved files to processing folder, total file:" + fileList.size() + "!! from "
				+ storeDoneFilePath + " to " + storeTempFilePath);
		

	}

	private void moveFilesBackProcessingFolder() {

		File processingFolder = new File(storeTempFilePath);

		System.out.println("====moving files back from processing folder, total file:" + processingFolder.length()
				+ "!! from " + storeTempFilePath + " to " + storeDoneFilePath);

		for (File gaFile : processingFolder.listFiles()) {
			try {
				FileUtils.moveFile(gaFile, new File(storeDoneFilePath + "/" + gaFile.getName()));
				processingFileList.add(storeDoneFilePath + "/" + gaFile.getName());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		deleteTempProcessingFolder();
	}

	private void moveFileAndZip() {

		File bkFolder = new File(storeBKFilePath);
		if (bkFolder == null || !bkFolder.isDirectory()) {
			System.out.println("BK folder is not exist, mkdir: " + storeBKFilePath);
			bkFolder.mkdir();
		}

		File tmpFile;
		File targetFile;

		for (String fileFullPath : processingFileList) {
			// zip file
			try {
				tmpFile = new File(fileFullPath);
				targetFile = new File(bkFolder + "/" + tmpFile.getName());

				FileUtils.moveFile(tmpFile, targetFile);

				System.out.println("zipped file : " + targetFile.getAbsolutePath());
				GZipUtil.zipFile(targetFile.getAbsolutePath());

				targetFile.delete();
				System.out.println("delete file : [" + fileFullPath + "]");
			} catch (Exception e) {
				System.out.println("delete file failed. file: [" + fileFullPath + "]");
				e.printStackTrace();
			}

		}

		deleteTempProcessingFolder();
	}

	private void deleteTempProcessingFolder() {
		// deleted the temp processing folder
		File tempFolder = new File(storeTempFilePath);

		if (tempFolder != null && tempFolder.isDirectory()) {
			tempFolder.delete();
		}
	}

	private final int maxInsertCount = 100000;

	private void processFile(File tmpFile, Integer trafficType, Integer ownDomainId, Date trafficDate) throws Exception {

		//int domainId, String filePath, String processDate, Boolean isOrganic
		readFileAndUploadToCDB(ownDomainId, tmpFile.getAbsolutePath(), FormatUtils.formatDate(trafficDate, "yyyy-MM-dd"), (processType == 1));
	}
	
	private void readFileAndUploadToCDB(int domainId, String filePath, String processDate, Boolean isOrganic) throws Exception {
    	
    	String domainName = "";
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
    	if (ownDomainEntity != null) {
    		domainName = ownDomainEntity.getDomain();
    		
    		if (StringUtils.isNotBlank(domainName) && StringUtils.contains(domainName, "/")) {
    			domainName = StringUtils.substringBefore(domainName, "/");
			}
		}
    	
    	System.out.println("*************loadFile:" + filePath + ",oid:" + domainId);
        //todo read file and upload
        List<GoogleAnalyticsV4Entity> gaBqEntityList = new ArrayList<>();
        try {
            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(filePath));
            BufferedReader bf = new BufferedReader(inputReader);
            String str;
            GoogleAnalyticsV4Entity googleAnalyticsV4Entity;
            int linNo = 0;
            
            try {
            	 while ((str = bf.readLine()) != null) {
                     linNo++;
                     try {
                    	 if (StringUtils.isBlank(str)) {
                             System.out.println("==SkipEmptyLine:" + linNo);
                             continue;
                         }
                         String[] arr = str.split(SPLIT_FIELD, -1);
                         if (arr[0].equalsIgnoreCase("event_name") || str.contains("f0_")) {
                        	 System.out.println("===skip Header!");
                             continue;
                         }
                         
                         if ((isOrganic && arr.length < 14) || (!isOrganic && arr.length < 15)) {
                        	 System.out.println("====skip line:" + linNo + ", Length:" + arr.length + ",  str:" + str);
                        	 continue;
                 		 }
                         
                         //String[] values, int domainId, String processDate, Integer batchNum, Boolean isOrganic
                         googleAnalyticsV4Entity = getGaEntity(arr, domainId, processDate, FormatUtils.formatDateToYyyyMmDd(new Date()), isOrganic, domainName, linNo, ownDomainEntity.getDomainProtocol());
                         gaBqEntityList.add(googleAnalyticsV4Entity);

                         if(gaBqEntityList.size() >= maxInsertCount){
                             int retry = 0;
                             System.out.println("===save in CDB");
                             while (true){
                                 try {
                                 	gaClarityDBEntityDAO.insertForGaV4Batch(gaBqEntityList, (isOrganic ? bqSummaryTable : bqMixTrafficSummaryTable));
                                     break;
                                 }catch (Exception e){
                                 	System.out.println("===retry:" +retry);
                                     e.printStackTrace();
                                     if(retry >= RETRY_TIMES){
                                     	System.out.println("===siteOVERREARY:" + retry);
                                         throw e;
                                     }
                                     try {
                                         Thread.sleep(1000 * 60);
                                     } catch (Exception ex) {
                                         ex.printStackTrace();
                                     }
                                     retry ++;
                                 }
                             }
                             gaBqEntityList = new ArrayList<>();
                         }

                        
                     } catch (Exception e) {
                         e.printStackTrace();
                         System.out.println("***** error line:" + linNo + "," + str);
                         throw e;
                     }
                 }
                 
			} catch (Exception e) {
				throw e;
			} finally {
				bf.close();
			}

            if (CollectionUtils.isNotEmpty(gaBqEntityList)) {
                int retry = 0;
                System.out.println("===save in CDB");
                while (true){
                    try {
                    	gaClarityDBEntityDAO.insertForGaV4Batch(gaBqEntityList, (isOrganic ? bqSummaryTable : bqMixTrafficSummaryTable));
                        break;
                    }catch (Exception e){
                    	System.out.println("===retry:" +retry);
                        e.printStackTrace();
                        if(retry >= RETRY_TIMES){
                        	System.out.println("===URLOVERREARY2:" + retry);
                            throw e;
                        }
                        try {
                            Thread.sleep(1000 * 60);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        retry ++;
                    }
                }
            }
           

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }


	private GoogleAnalyticsV4Entity getGaEntity(String[] values, int domainId, String processDate, Integer batchNum, Boolean isOrganic, String domainName, Integer linNo, String protocol) {
		
		GoogleAnalyticsV4Entity gaEntity = new GoogleAnalyticsV4Entity();

		String platFormDevice = values[5];
		String platform = "";
		String device = "";
		if (StringUtils.contains(platFormDevice, "/")) {
			device = StringUtils.lowerCase(StringUtils.trim(StringUtils.substringAfter(platFormDevice, "/")));
			platform = StringUtils.lowerCase(StringUtils.trim(StringUtils.substringBefore(platFormDevice, "/")));

		} else {
			System.out.println("==== device can not be split! platFormDevice:" + platFormDevice);
			device = platFormDevice;
		}
		
		String url = cleanupUrl(values[8]);
		
		String uri = "";
		
		String hostName = values[3];
		
		if (StringUtils.isBlank(hostName) || NOT_SET.equals(hostName)) {
            hostName = domainName;
        } else {
            hostName = values[3];
        }
		
		if (StringUtils.isBlank(url)) {
        	uri = "/null";
        } else if(NOT_SET.equals(url)){
        	uri = "/(not set)";
        } else {
        	uri = FormatUtils.getUriFromUrl(url);
        }
		
		if(StringUtils.isBlank(url)) {
			url = protocol + "://" + hostName + "/" + StringUtils.removeStart(uri, "/");
		}

		gaEntity.setDomainId(domainId);
		gaEntity.setLogDate(processDate);
		gaEntity.setKeywordText("");
		gaEntity.setHostName(hostName);
		gaEntity.setUri(uri);
		gaEntity.setFullPageUrl(hostName + uri);
		gaEntity.setSessionMedium((isOrganic? "organic" : values[14]));
		gaEntity.setSessionSource(values[6]);
		gaEntity.setDeviceCategory(device);
		gaEntity.setPlatform(platform);
		
		String country = values[2];
		
		if (StringUtils.isBlank(country) || StringUtils.equals(country, "\"\"")) {
			country = NOT_SET;
		}
		gaEntity.setCountry(country);
		gaEntity.setEventName(values[0]);
		gaEntity.setSessions(NumberUtils.toInt(values[7]));
		gaEntity.setEcommercePurchases(NumberUtils.toFloat(values[9]));
		gaEntity.setTransactions(NumberUtils.toInt(values[10]));
		gaEntity.setEngagedSessions(NumberUtils.toInt(values[11]));
		gaEntity.setEngagementRate(NumberUtils.toFloat(values[12]));
		
		Integer eventCount = NumberUtils.toInt(values[1]);
		if (eventCount < 0) {
			eventCount = 0;
		}
		
		gaEntity.setEventCount(eventCount);
		gaEntity.setEventValue(NumberUtils.toFloat(values[13]));
		gaEntity.setVersoin(1);
		gaEntity.setDataSourceType(1);
		gaEntity.setBatchNo(batchNum);
		gaEntity.setGoalId(0);
//		gaEntity.setPageTitle(csvRecord.get("page_title"));
		gaEntity.setUrl(url);
		gaEntity.setScreenPageviews(NumberUtils.toInt(values[4]));

        return gaEntity;
    }
	
    private String cleanupUrl(String url) {
    	
    	if (StringUtils.isBlank(url)) {
			return url;
		}
    	
		if (StringUtils.contains(url, "?")) {
			url = StringUtils.substringBefore(url, "?");
		}
		
		if (StringUtils.contains(url, "#")) {
			url = StringUtils.substringBefore(url, "#");
		}
		
		return url;
    }
	
	public Set<Integer> getDomainSet(String domainIds) {
		Set<Integer> returnDomain = new HashSet<Integer>();
		String[] dIds = domainIds.split(",");

		if (dIds != null && dIds.length > 0) {
			for (int i = 0; i < dIds.length; i++) {
				try {
					returnDomain.add(Integer.parseInt(dIds[i]));
				} catch (Exception e) {

				}
			}
		}
		return returnDomain;
	}

	private boolean checkTableForCurrentHour(Date date) {

		List<String> tableList = gaClarityDBEntityDAO.getTableListForBigQuery(date, (processType == 1));
		if (CollectionUtils.isNotEmpty(tableList)) {
			System.out.println("Table is already exist, need check manually!!! " + tableList.get(0));
			return false;
		}

		int tryCount = 3;
		int cnt = 0;

		while (!createClarityTempLocalTable(date) && cnt < tryCount) {
			cnt++;
			
			if(processType == 1) {
				bqSummaryTable = "";
			} else {
				bqMixTrafficSummaryTable = "";
			}
			
		}
		if (StringUtils.isNotBlank(getTempTableNameByProcessType())) {
			return true;
		} else {
			return false;
		}
	}

	private boolean createClarityTempLocalTable(Date date) {
		try {
			
			if(processType == 1) {
				bqSummaryTable = gaClarityDBEntityDAO.createTableForBigQuery(date, (processType == 1));
			} else {
				bqMixTrafficSummaryTable = gaClarityDBEntityDAO.createTableForBigQuery(date, (processType == 1));
			}
			
			System.out.println("Current no other script process, create new table:" + getTempTableNameByProcessType());
		} catch (Exception e) {
			System.out.println("CreateTbaleFailed, startHour:" + startHour + ", statTableName:" + getTempTableNameByProcessType());
			e.printStackTrace();
			return false;
		}
		return true;
	}

}
