package seoclarity.backend.upload.gabigquery;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.CollectionUtils;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.DeleteQueueRequest;
import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.gabigquery.GaBqBulkExportInfoDAO;
import seoclarity.backend.dao.actonia.gabigquery.GaBqBulkExportInstanceDAO;
import seoclarity.backend.dao.clickhouse.ga.GaClarityDBEntityDAO;
import seoclarity.backend.entity.actonia.GaBigQueryMessageVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.gabigquery.GaBqBulkExportInstanceEntity;
import seoclarity.backend.entity.actonia.gabigquery.GaBqBulkexportInfoEntity;
import seoclarity.backend.entity.actonia.gscbigquery.GscBqBulkExportInstanceEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.SQSUtils;

//  mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.gabigquery.SendGA4BigQueryToQueue" -Dexec.args="true 2025-04-01 2025-04-01 false GA_BIGQUERY_BACKPROCESS_20250408"
public class SendGA4BigQueryToQueue {
    private static final SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");

    private static final String GA_BIGQUERY_DAILY_QUEUE = "GA_BIGQUERY_DAILY_QUEUE";
    
    private static String specified_sqs_name = null;
    private static final int MAX_RETRY_COUNT = 10;
    private static final int MAX_SENDER_COUNT = 10000 * 13;
    private static final int MIN_COPY_DAYS_COUNT = 10;
    private static final String SPLIT = "-";

    private static String sDate;
    private static String eDate;
    
	private static final String databaseName = "actonia_site_analytics";
	private static String organicFinalTableName = "dis_analytics_ga4bq_raw";
	private static String mixTrafficFinalTableName = "dis_mix_ga4bq_raw";

    private static boolean isNeedDeleteQueue = false;
//	private static boolean isBackProcess = false;
//	private static String GSC_DOMAIN_QNAME_BACKPROCESS = "";

    
    public final static String DOMAIN_STRING_REGEX = ",";
    public final static String DOMAIN_PROFILE_STRING_REGEX = "_";
    public final static int GROUP_CONCAT_MAX_LENGTH = 512;
    
    private static boolean backprocessFlag = false;
    
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private GaBqBulkExportInfoDAO gaBqBulkExportInfoDAO;
    private GaBqBulkExportInstanceDAO gaBqBulkExportInstanceDAO;
    private GaClarityDBEntityDAO gaClarityDBEntityDAO;
    
    public SendGA4BigQueryToQueue() {
    	ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    	gaBqBulkExportInfoDAO = SpringBeanFactory.getBean("gaBqBulkExportInfoDAO");
    	gaBqBulkExportInstanceDAO = SpringBeanFactory.getBean("gaBqBulkExportInstanceDAO");
    	gaClarityDBEntityDAO = SpringBeanFactory.getBean("gaClarityDBEntityDAO");
    	
    }

    public static void main(String[] args) {
    	
    	if (args != null && args.length >= 1) {
    		backprocessFlag = BooleanUtils.toBoolean(args[0]);
    	}

        if (args != null && args.length >= 4 && StringUtils.isNotBlank(args[3])) {
            isNeedDeleteQueue = Boolean.valueOf(args[3]); 
        }
        if (args != null && args.length >= 5 && StringUtils.isNotBlank(args[4])) {
            specified_sqs_name = args[4];
        }
        
        if (args != null && args.length >= 3 && StringUtils.isNotBlank(args[1]) && StringUtils.isNotBlank(args[2])) {
            sDate = args[1];
            eDate = args[2];
        } else {
   		 	sDate = df.format(DateUtils.addDays(new Date(), -1));
            eDate = df.format(DateUtils.addDays(new Date(), -1));
		}
        

        SendGA4BigQueryToQueue instance = new SendGA4BigQueryToQueue();
        System.out.println("======Parameter sDate:" + sDate + ", endDate:" + eDate + ", isNeedDeleteQueue:" + isNeedDeleteQueue + ", specified_sqs_name:" + specified_sqs_name + ", backprocessFlag:" + backprocessFlag);
        try {
            instance.init();
        } catch (Exception e) {
            e.printStackTrace();
        }
        
    }
    
    private static Map<Integer, GaBqBulkexportInfoEntity> GaBqBulkexportInfoEntityMap = new HashMap<Integer, GaBqBulkexportInfoEntity>();


    public void init() throws Exception {
        Properties prop = new Properties();
        try {
            prop.load(SendGA4BigQueryToQueue.class.getResourceAsStream("/domain.properties"));
        } catch (Exception e) {
            System.out.println("no properties file found");
        }
        String execDomainIds = prop.getProperty("exec.domain");
        String notExecDomainIds = prop.getProperty("notexec.domain");
        List<Integer> execDomainIdsList = new ArrayList<Integer>();
        List<Integer> notExecDomainIdsList = new ArrayList<Integer>();
        if (StringUtils.isNotBlank(execDomainIds)) {
            for (String oid : StringUtils.split(execDomainIds, ',')) {
                execDomainIdsList.add(Integer.valueOf(oid));
            }
        }
        if (StringUtils.isNotBlank(notExecDomainIds)) {
            for (String oid : StringUtils.split(notExecDomainIds, ',')) {
                notExecDomainIdsList.add(Integer.valueOf(oid));
            }
        }

        AwsCredentialsEnvKeyConstructor awsCredentialsEnvKeyConstructor = new AwsCredentialsEnvKeyConstructor();
		String accessKeyId = awsCredentialsEnvKeyConstructor.getPlainTextSQSAccessKey();
		String secretAccessKey = awsCredentialsEnvKeyConstructor.getSQSDecryptedSecretKey();

		System.out.println("accessKeyId:" + accessKeyId);
		System.out.println("secretAccessKey:" + secretAccessKey);

        // init SQS
        AmazonSQS amazonSQS = null;
        String queryUrlPro1 = null;
        int retryCount = 0;
        while (true) {
            try {
            	amazonSQS = SQSUtils.getAmazonSQS(accessKeyId, secretAccessKey);
                if (StringUtils.isBlank(specified_sqs_name)) {
                	queryUrlPro1 = SQSUtils.createQueue(GA_BIGQUERY_DAILY_QUEUE, amazonSQS);
                    System.out.println("###You have 2 mins to decide to stop this course or not, date:" + new Date()
                            + ", GA_BIGQUERY_DAILY_QUEUE:" +GA_BIGQUERY_DAILY_QUEUE
                            + ", isNeedDeleteQueue:" + isNeedDeleteQueue);
                } else {
                	queryUrlPro1 = SQSUtils.createQueue(specified_sqs_name, amazonSQS);
                    System.out.println("###You have 2 mins to decide to stop this course or not, date:" + new Date()
                            + ", GA_BIGQUERY_DAILY_QUEUE:" +specified_sqs_name
                            + ", isNeedDeleteQueue:" + isNeedDeleteQueue);
                }

                if (isNeedDeleteQueue) {
                    System.out.println("Start to delete queryUrl:" + queryUrlPro1);
                    amazonSQS.deleteQueue(new DeleteQueueRequest(queryUrlPro1));
                    Thread.sleep(120000);
                }
                break;
            } catch (Exception e) {
                e.printStackTrace();
                retryCount++;
                System.out.println("======Send domains to AmazonSQS failed(SSLException) retryCount:" + retryCount);
                if (retryCount >= MAX_RETRY_COUNT) {
                    System.out.println("======Send domains to AmazonSQS failed(SSLException) and Exit!");
                    return;
                }
            }
        }

        System.out.println("======retryCount:" + retryCount);
        System.out.println("query url pro1:" + queryUrlPro1);
        System.out.println("1111111111111111111111111111111111111111111111111111111111111111111111111111");
        
        Date startDate = df.parse(sDate);
        Date endDate = df.parse(eDate);

        List<GaBqBulkexportInfoEntity> bulkExportInfoEntities = gaBqBulkExportInfoDAO.getBulkExportInfoList();
        if (CollectionUtils.isEmpty(bulkExportInfoEntities)) {
            System.out.println("====no bulkExportInfoEntities,exit");
            return;
        }
        
        List<Integer> ownDomainIdList = bulkExportInfoEntities.stream().map(GaBqBulkexportInfoEntity::getDomainId).collect(Collectors.toList());
        GaBqBulkexportInfoEntityMap = bulkExportInfoEntities.stream().collect(Collectors.toMap(GaBqBulkexportInfoEntity::getDomainId, var -> var));
        
        List<Integer> processOwnDomainIdList = new ArrayList<Integer>();
        
        //---------------------------------- Domain with profile --------------------------------
        System.out.println("========domainAndProfileList size:" + ownDomainIdList.size());
        for (Integer ownDomainId : ownDomainIdList) {
            if (execDomainIdsList.size() > 0 && !execDomainIdsList.contains(ownDomainId)) {
                continue;
            }
            if (notExecDomainIdsList.size() > 0 && notExecDomainIdsList.contains(ownDomainId)) {
                continue;
            }
            processOwnDomainIdList.add(ownDomainId);
        }
        System.out.println("========processDomainsList size:" + processOwnDomainIdList.size());

        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);
        while (cal.getTime().getTime() <= endDate.getTime()) {
            System.out.println("========Current process date:" + cal.getTime());
            try {
                process(processOwnDomainIdList, df.format(cal.getTime()), amazonSQS, queryUrlPro1);

            } catch (Exception e) {
                e.printStackTrace();
            }
            cal.add(Calendar.DAY_OF_MONTH, 1);
        }

    }
    

    public void process(List<Integer> processDomainsList, String processDate, AmazonSQS amazonSQS, String queryUrlPro) throws Exception {

    	System.out.println("queryUrlPro:" + queryUrlPro);
    	
    	List<GaBigQueryMessageVO> crawlInfos = new ArrayList<GaBigQueryMessageVO>();
        for (Integer ownDomainId : processDomainsList) {
            try {
            	
            	GaBqBulkexportInfoEntity gaBqBulkexportInfoEntity = GaBqBulkexportInfoEntityMap.get(ownDomainId);
            	if (gaBqBulkexportInfoEntity == null) {
                    System.out.println("=== bulkExportInfo not exist oid:" + ownDomainId);
                    return;
                }

            	System.out.println("====== processing on organic");
                GaBigQueryMessageVO gaBigQueryOrganicMessageVO = getCrawlInfo(ownDomainId, gaBqBulkexportInfoEntity, processDate, true);
                if (gaBigQueryOrganicMessageVO != null) {
                	crawlInfos.add(gaBigQueryOrganicMessageVO);
				}
                
                System.out.println("====== processing on traffic mix");
                GaBigQueryMessageVO gaBigQueryMixTrafficMessageVO = getCrawlInfo(ownDomainId, gaBqBulkexportInfoEntity, processDate, false);
                if (gaBigQueryMixTrafficMessageVO != null) {
                	crawlInfos.add(gaBigQueryMixTrafficMessageVO);
				}

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        System.out.println("messageMap size :" + crawlInfos.size());
		
		for(GaBigQueryMessageVO gaBigQueryMessageVO: crawlInfos) {
			try {
				SQSUtils.sendMessageToQueue(amazonSQS, queryUrlPro, new Gson().toJson(gaBigQueryMessageVO));
	            
	        } catch (Exception e) {
	            e.printStackTrace();
	        }
		}
		
		System.out.println("========= Total send to Queue:" + specified_sqs_name + ", size:" + crawlInfos.size());
        
    }
    
    private final static String TABLE_NAME_TEMPLATE = "%s.events_%s";
    
    private GaBigQueryMessageVO getCrawlInfo(Integer ownDomainId, GaBqBulkexportInfoEntity gaBqBulkexportInfoEntity, String processDate, Boolean isOrganic) {
    	
        int infoId = gaBqBulkexportInfoEntity.getId();
        String dateSetId = gaBqBulkexportInfoEntity.getDataSetId();
		String dateFormat = gaBqBulkexportInfoEntity.getDateFormat();
		
		if (StringUtils.isBlank(dateFormat)) {
			dateFormat = "yyyyMMdd";
		}
		
		String tableName = String.format(TABLE_NAME_TEMPLATE, dateSetId, FormatUtils.formatDate(FormatUtils.toDate(processDate, "yyyy-MM-dd"), dateFormat));
		
		String[] array = StringUtils.split(tableName, "\\.");
		
		if (ArrayUtils.isEmpty(array) || array.length != 3) {
			System.out.println("The dataSetId is incorrect! Skip, dateSetId ：" + dateSetId);
			return null;
		}

		System.out.println("tableName:" + tableName);
        String projectId = tableName.split("\\.")[0];
        String databaseId = tableName.split("\\.")[1];
        String tableId = tableName.split("\\.")[2];
        
    	String domainName = "";
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
    	if (ownDomainEntity != null) {
    		domainName = ownDomainEntity.getDomain();
    		
    		if (StringUtils.isNotBlank(domainName) && StringUtils.contains(domainName, "/")) {
    			domainName = StringUtils.substringBefore(domainName, "/");
			}
		}
    	
    	GaBqBulkExportInstanceEntity gaBqBulkExportInstanceEntity = gaBqBulkExportInstanceDAO.getInstanceByDate(ownDomainId, processDate, getTrafficTypeByBoolean(isOrganic));
        
    	//1. verfy if data exist in CDB
    	Integer count = gaClarityDBEntityDAO.checkExist((isOrganic ? organicFinalTableName : mixTrafficFinalTableName), ownDomainId, 
    			FormatUtils.toDate(processDate, "yyyy-MM-dd"), 1);

		if (count > 0) {
			System.out.println("==== data exist in clarityDB : OID:" + ownDomainId + ", trafficDate:" + processDate);
			
			if (gaBqBulkExportInstanceEntity != null) {
				if (gaBqBulkExportInstanceEntity.getExtractStatus() != GscBqBulkExportInstanceEntity.EXTRACT_STATUS_COMPLETE) {
					System.out.print("====update extract status to finished");
					gaBqBulkExportInstanceDAO.updateExtractStatus(gaBqBulkExportInstanceEntity.getId(), GscBqBulkExportInstanceEntity.EXTRACT_STATUS_COMPLETE, "");
				}
				
				if (gaBqBulkExportInstanceEntity.getUploadStatus() != GscBqBulkExportInstanceEntity.UPLOAD_STATUS_COMPLETE) {
					System.out.print("====update upload status to finished");
					gaBqBulkExportInstanceDAO.updateUploadStatus(gaBqBulkExportInstanceEntity.getId(), GscBqBulkExportInstanceEntity.UPLOAD_STATUS_COMPLETE);
				}
            }
			return null;
		} else {
			System.out.println("No data found in CDB!");
		}
    	
    	//confirmed with Alps, the data should be full data for one delay day
    	//2. verfy if data exist in DB
    	if (!backprocessFlag && gaBqBulkExportInstanceEntity != null && 
        		(gaBqBulkExportInstanceEntity.getExtractStatus() == GaBqBulkExportInstanceEntity.EXTRACT_STATUS_COMPLETE
        		|| gaBqBulkExportInstanceEntity.getExtractStatus() == GaBqBulkExportInstanceEntity.EXTRACT_STATUS_RUNNING)) {
        	System.out.println("======Found task processed, skip! OID:" + ownDomainId + ", tableName：" + tableName + ", processDate:" + processDate);
        	return null;
		} else {
			
			Integer instanceId;
            if (gaBqBulkExportInstanceEntity == null) {
            	gaBqBulkExportInstanceEntity = new GaBqBulkExportInstanceEntity();
            	gaBqBulkExportInstanceEntity.setDomainId(ownDomainId);
            	gaBqBulkExportInstanceEntity.setBqNamespace(tableName);
            	gaBqBulkExportInstanceEntity.setBqDataDate(processDate);
            	gaBqBulkExportInstanceEntity.setExtractStatus(GscBqBulkExportInstanceEntity.EXTRACT_STATUS_PENDING);
            	gaBqBulkExportInstanceEntity.setUploadStatus(GscBqBulkExportInstanceEntity.UPLOAD_STATUS_PENDING);
                gaBqBulkExportInstanceEntity.setTrafficType(getTrafficTypeByBoolean(isOrganic));
                instanceId = gaBqBulkExportInstanceDAO.insert(gaBqBulkExportInstanceEntity);
            } else {
                instanceId = gaBqBulkExportInstanceEntity.getId();
                gaBqBulkExportInstanceDAO.updateExtractStatus(instanceId, GscBqBulkExportInstanceEntity.EXTRACT_STATUS_PENDING, null);
            }
            
            System.out.println("======instanceId:" + instanceId);
            GaBigQueryMessageVO gaBigQueryOrganicMessageVO = generateMessageVO(projectId, databaseId, tableId, 
            		tableName, processDate, ownDomainId, infoId, isOrganic, instanceId, domainName);
			    	                
			return gaBigQueryOrganicMessageVO;
		}
        
    }
    
    private Integer getTrafficTypeByBoolean(Boolean isOrganic) {
    	return isOrganic ? GaBqBulkExportInstanceEntity.TRAFFIC_TYPE_ORGANIC :  GaBqBulkExportInstanceEntity.TRAFFIC_TYPE_MIX_TRAFFIC;
    }
    
    private GaBigQueryMessageVO generateMessageVO(String projectId, String databaseId, String tableId, String tableName,
    		String processDate, Integer ownDomainId, Integer infoId, Boolean isOrganic,
    		Integer instanceId, String domainName) {
    	
    	GaBigQueryMessageVO gaBigQueryOrganicMessageVO = new GaBigQueryMessageVO();
		gaBigQueryOrganicMessageVO.setProjectId(projectId);
		gaBigQueryOrganicMessageVO.setDatabaseId(databaseId);
		gaBigQueryOrganicMessageVO.setTableId(tableId);
		gaBigQueryOrganicMessageVO.setTableName(tableName);
		
		gaBigQueryOrganicMessageVO.setProcessDate(processDate);
		gaBigQueryOrganicMessageVO.setOwnDomainId(ownDomainId);
		gaBigQueryOrganicMessageVO.setInfoId(infoId);
		gaBigQueryOrganicMessageVO.setIsOrganic(isOrganic);
		gaBigQueryOrganicMessageVO.setInstanceId(instanceId);
		gaBigQueryOrganicMessageVO.setDomainName(domainName);
		return gaBigQueryOrganicMessageVO;
    }
    
}
