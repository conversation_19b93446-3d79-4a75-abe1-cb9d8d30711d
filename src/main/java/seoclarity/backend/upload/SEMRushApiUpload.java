package seoclarity.backend.upload;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.URLConnectionUtils;

import java.util.*;

public class SEMRushApiUpload {

//    private final static String REQUEST_API = "https://api.semrush.com/reports/v1/projects/608905/tracking?key=a9631514089749a309b8c24fb2c1fe2c&action=report&type=tracking_position_organic&top_filter=top_100&url=*.discover.com/*";
//    private final static String REQUEST_API_V2 = "https://api.semrush.com/reports/v1/projects/573923/tracking?key=a9631514089749a309b8c24fb2c1fe2c&action=report&type=tracking_position_organic&top_filter=top_100&url=*www.discover.com/*";

    public static final Map<Integer, String> PROJECT_MAP = new HashMap();

    private int processDomainId = 6994;

    static {
        PROJECT_MAP.put(608905, "https://api.semrush.com/reports/v1/projects/608905/tracking?key=a9631514089749a309b8c24fb2c1fe2c&action=report&type=tracking_position_organic&top_filter=top_100&url=*.discover.com/*");
        PROJECT_MAP.put(573923, "https://api.semrush.com/reports/v1/projects/573923/tracking?key=a9631514089749a309b8c24fb2c1fe2c&action=report&type=tracking_position_organic&top_filter=top_100&url=*.discover.com/*");
    }

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ScKeywordRankManager scKeywordRankManager;

    public SEMRushApiUpload() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
    }

    public static void main(String[] args) {

        SEMRushApiUpload semRushApiUpload = new SEMRushApiUpload();
        semRushApiUpload.process();

    }

    private void process() {

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(processDomainId);
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);

        Integer beginDate = 20190812;
        Integer endDate = 20190812;
        int page = 0;
        int limit = 1000;


        System.out.println("========page : " + page);
        List<String> keywordList = new ArrayList<>();
        List<InsertVo> insertVoList = new ArrayList<>();

        for(Integer projectId : PROJECT_MAP.keySet()){
            int totalCount = 0;
            insertVoList.addAll(getDataByApi(PROJECT_MAP.get(projectId), beginDate, endDate, page, limit));
            totalCount += insertVoList.size();
            System.out.println("===totalCount : " + totalCount);

        }

        Map<String, InsertVo> insertVoMap = new HashMap<>();

        for(InsertVo insertVo : insertVoList){

            String keywordName = insertVo.getKeywordName();
            Integer rankDate = insertVo.getRankDate();
            String key = rankDate + "!_!" + keywordName;
            insertVoMap.put(key,insertVo);

            keywordList.add(keywordName);
        }

        List<String> existKeywordList = clDailyRankingEntityDao.checkExistsFromInfoTableForKeywordNameList(processDomainId,engineId,languageId, 0,
                beginDate.toString(),endDate.toString(),false, keywordList);

        for(String key : insertVoMap.keySet()){
            if(existKeywordList.contains(key)){

                continue;
            }

            //todo insert

        }


    }

    private List<InsertVo> getDataByApi(String url, int beginDate, int endDate, int page, int limit) {

//        String url = REQUEST_API + "&date_begin=" + beginDate + "&date_end=" + endDate + "&display_limit=" + limit + "&display_offset=" + page;
        url = url + "&date_begin=" + beginDate + "&date_end=" + endDate + "&display_limit=" + limit + "&display_offset=" + page;

        System.out.println("===request url : " + url);

        String result = URLConnectionUtils.sendGet(url, null);

        Map resultMap = JSONObject.toJavaObject(JSONObject.parseObject(result), Map.class);
        if (resultMap == null) {
            return null;
        }
        Map dataMap = JSONObject.toJavaObject(JSONObject.parseObject(resultMap.get("data").toString()), Map.class);
        if (dataMap == null) {
            return null;
        }


        List<InsertVo> insertVoList = new ArrayList<>();
        Set<String> rankDateSet = new HashSet<>();

        Iterator<Map.Entry<String, Object>> it = dataMap.entrySet().iterator();
        while (it.hasNext()) {


            Map.Entry<String, Object> entry = it.next();
//            System.out.println("key:" + entry.getKey() + "  value:" + entry.getValue());

            DetailDataVo detailDataVo = new Gson().fromJson(entry.getValue().toString(), DetailDataVo.class);
//            System.out.println("===detailDataVo : " + JSON.toJSONString(detailDataVo));

            detailDataVo.getDt();

            Map rankDateMap = new Gson().fromJson(new Gson().toJson(detailDataVo.getDt()), Map.class);

            Iterator<String> iter = rankDateMap.keySet().iterator();
            while (iter.hasNext()) {
                String rankDate = iter.next();
                rankDateSet.add(rankDate);
//                System.out.println("rankDate:" + rankDate);
            }

            Map rankingUrlMap = new Gson().fromJson(new Gson().toJson(detailDataVo.getLu()), Map.class);

            for (String rankDate : rankDateSet) {
                Map rankingUrlDetailMap = new Gson().fromJson(new Gson().toJson(rankingUrlMap.get(rankDate)), Map.class);
                Iterator<String> rankingUrlIter = rankingUrlDetailMap.keySet().iterator();
                while (rankingUrlIter.hasNext()) {
                    String rankUrl = rankingUrlDetailMap.get(rankingUrlIter.next()).toString();
//                    System.out.println("rankUrl:" + rankUrl);

                    InsertVo insertVo = new InsertVo();
                    insertVo.setKeywordName(detailDataVo.getPh());
                    if (NumberUtils.isNumber(detailDataVo.getNq())) {
                        insertVo.setSv(Integer.parseInt(detailDataVo.getNq()));
                    } else {
                        System.out.println("=====not number Nq : " + detailDataVo.getNq());
                        continue;
                    }
                    if (NumberUtils.isNumber(detailDataVo.getCp())) {
                        insertVo.setCpc(detailDataVo.getCp());
                    } else {
                        System.out.println("=====not number Cp : " + detailDataVo.getCp());
                        continue;
                    }

                    insertVo.setRankDate(Integer.parseInt(rankDate));
                    insertVo.setRankUrl(rankUrl);
                    insertVoList.add(insertVo);
                }

            }

        }
        System.out.println("insertVoList size:" + insertVoList.size());
        System.out.println("insertVoList:" + JSON.toJSONString(insertVoList));

        return insertVoList;
    }


    private class InsertVo {

        private String keywordName;
        private String cpc;
        private Integer sv;
        private Integer rankDate;
        private String rankUrl;

        public String getKeywordName() {
            return keywordName;
        }

        public void setKeywordName(String keywordName) {
            this.keywordName = keywordName;
        }

        public String getCpc() {
            return cpc;
        }

        public void setCpc(String cpc) {
            this.cpc = cpc;
        }

        public Integer getSv() {
            return sv;
        }

        public void setSv(Integer sv) {
            this.sv = sv;
        }

        public Integer getRankDate() {
            return rankDate;
        }

        public void setRankDate(Integer rankDate) {
            this.rankDate = rankDate;
        }

        public String getRankUrl() {
            return rankUrl;
        }

        public void setRankUrl(String rankUrl) {
            this.rankUrl = rankUrl;
        }
    }

    private class DetailDataVo {

        private String Ph;
        private String Cp;
        private String Nq;

        private Map<String, Object> Dt;
        private Map<String, Object> Lu;

        public String getPh() {
            return Ph;
        }

        public void setPh(String ph) {
            Ph = ph;
        }

        public String getCp() {
            return Cp;
        }

        public void setCp(String cp) {
            Cp = cp;
        }

        public String getNq() {
            return Nq;
        }

        public void setNq(String nq) {
            Nq = nq;
        }

        public Map<String, Object> getDt() {
            return Dt;
        }

        public void setDt(Map<String, Object> dt) {
            Dt = dt;
        }

        public Map<String, Object> getLu() {
            return Lu;
        }

        public void setLu(Map<String, Object> lu) {
            Lu = lu;
        }
    }

}
