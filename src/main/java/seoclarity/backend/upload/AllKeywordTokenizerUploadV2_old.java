package seoclarity.backend.upload;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.eclipse.jetty.util.BlockingArrayQueue;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.dao.actonia.MonthlySenderInfoEntityDAO;
import seoclarity.backend.dao.clickhouse.kp201.ManagedSearchvolume201DAO;
import seoclarity.backend.dao.clickhouse.kp202.ManagedSearchvolume202DAO;
import seoclarity.backend.dao.clickhouse.kp203.ManagedSearchvolume203DAO;
import seoclarity.backend.dao.clickhouse.kp204.ManagedSearchvolume204DAO;
import seoclarity.backend.dao.clickhouse.kp205.ManagedSearchvolume205DAO;
import seoclarity.backend.dao.clickhouse.kp206.ManagedSearchvolume206DAO;
import seoclarity.backend.dao.clickhouse.kp207.DisManagedSearchvolumeDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsExpandedEntityNewDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.KeywordStreamSearchengineCountryMappingEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.DisManagedSearchvolume;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsExpandedEntityNew;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @program: backend
 * @description: all keyword upload
 * @packagename: seoclarity.backend.upload
 * @author: cil
 * @date: 2021-06-09 17:37
 **/
@Deprecated
public class AllKeywordTokenizerUploadV2_old {
	/*
    static ThreadPoolExecutor threadPoolExecutor =
            new ThreadPoolExecutor(5, 5, 10000, TimeUnit.SECONDS, new BlockingArrayQueue<>(10));

    //   private static final int MIN_REL_ID = 0;
//    private static final int START_KID_RANGE = 200;
//    private static final int MAX_KID_RANGE = 100000;
//    private static final int MAX_THREAD = 5;
//    private static final int PAGE_SIZE = 1000;
//    private static final int lastRefreshMonth = 202012;
//    private static final boolean isLoadEmptySvKeywords = false;
//    private static final boolean isCheckDirtyKeywords = true;
//    private static final String UNRANKCHECK_TABLE = "local_searchvolume_v2_20210425_ext";
//    private static final String DEFAULT_TOPIC_EXPLORER_TABLE = "dis_searchvolume_v2";
    public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

    public static Map<String, String[]> processCountryMap = new LinkedHashMap<String, String[]>();


    public static Map<String, String> specialCountryCodeMap = new LinkedHashMap<String, String>();

    private final SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
    //	private SeoClarityKeywordEntityDAOBackup seoClarityKeywordEntityDAOBackup;
    private final MonthlySenderInfoEntityDAO monthlySenderInfoEntityDAO;

    private final KeywordAdwordsExpandedEntityNewDAO keywordAdwordsExpandedEntityNewDAO;
    private final KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;
    private final DisManagedSearchvolumeDAO disManagedSearchvolumeDAO;
    //201
    private final ManagedSearchvolume201DAO managedSearchvolume201DAO;
    //202
    private final ManagedSearchvolume202DAO managedSearchvolume202DAO;
    //203
    private final ManagedSearchvolume203DAO managedSearchvolume203DAO;
    //204
    private final ManagedSearchvolume204DAO managedSearchvolume204DAO;
    //205
    private final ManagedSearchvolume205DAO managedSearchvolume205DAO;
    //206
    private final ManagedSearchvolume206DAO managedSearchvolume206DAO;

    private static List<DisManagedSearchvolume> insertList1 = Collections.synchronizedList(new LinkedList<>());
    private static List<DisManagedSearchvolume> insertList2 = Collections.synchronizedList(new LinkedList<>());
    private final int num;
    private static final String spilt = "!_!";
    //todo
    private static final String localTable = "local_managed_searchvolume";
    private static final String disTable = "dis_managed_searchvolume";
    private static final String old = "_backup";

    static {
        specialCountryCodeMap.put("CA-2", "FR");
        specialCountryCodeMap.put("US-2", "US");
        processCountryMap.put("US", new String[]{"English", String.valueOf(SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE)});

    }
    //ket_entity min and max id

    public static long minId1, minId2;
    static private long maxId;
    static private int count = 10;

    synchronized long getMinId1() {
        long thisId = minId1;
        minId1 += 200;
        return thisId;
    }


    synchronized long getMinId2() {
        long thisId = minId2;
        minId2 += 200;
        return thisId;
    }


    synchronized long getMaxId() {
        return maxId;
    }

    public AllKeywordTokenizerUploadV2_old() {
        num = 20000;
//        num = 200;
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
//		seoClarityKeywordEntityDAOBackup = SpringBeanFactory.getBean("seoClarityKeywordEntityDAOBackup");
        monthlySenderInfoEntityDAO = SpringBeanFactory.getBean("monthlySenderInfoEntityDAO");

        managedSearchvolume201DAO = SpringBeanFactory.getBean("managedSearchvolume201DAO");
        managedSearchvolume202DAO = SpringBeanFactory.getBean("managedSearchvolume202DAO");
        managedSearchvolume203DAO = SpringBeanFactory.getBean("managedSearchvolume203DAO");
        managedSearchvolume204DAO = SpringBeanFactory.getBean("managedSearchvolume204DAO");
        managedSearchvolume205DAO = SpringBeanFactory.getBean("managedSearchvolume205DAO");
        managedSearchvolume206DAO = SpringBeanFactory.getBean("managedSearchvolume206DAO");

        keywordAdwordsExpandedEntityNewDAO = SpringBeanFactory.getBean("keywordAdwordsExpandedEntityNewDAO");
        disManagedSearchvolumeDAO = SpringBeanFactory.getBean("disManagedSearchvolumeDAO");
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");

        minId1 = 0L;
        minId2 = 0L;
        maxId = 500L;// findMaxId();

    }

    //search order ：EngineId => LanguageId
    static Map<Integer, Map<Integer, KeywordStreamSearchengineCountryMappingEntity>> countryAndLanguageFullNameMap = new HashMap(128);


    public static void main(String[] args) {
        CountDownLatch latch = new CountDownLatch(count);

        //init countryAndLanguageFullNameMap
        AllKeywordTokenizerUploadV2_old ins = new AllKeywordTokenizerUploadV2_old();
        List<KeywordStreamSearchengineCountryMappingEntity> countryAndLanguageFullName = ins.getCountryAndLanguageFullName();
        for (KeywordStreamSearchengineCountryMappingEntity entity :
                countryAndLanguageFullName) {
            if (countryAndLanguageFullNameMap.get(entity.getEngineId()) == null) {
                Map<Integer, KeywordStreamSearchengineCountryMappingEntity> map = new HashMap<>();
                map.put(entity.getLanguageId(), entity);
                countryAndLanguageFullNameMap.put(entity.getEngineId(), map);
            } else {
                countryAndLanguageFullNameMap.get(entity.getEngineId()).put(entity.getLanguageId(), entity);
            }
        }
        System.out.println("countryAndLanguageFullNameMap size ==== " + countryAndLanguageFullNameMap.size());
        System.out.println("countryAndLanguageFullName size = " + countryAndLanguageFullName.size());


        System.out.println("min id = " + minId1);
        System.out.println("max id = " + maxId);
        System.out.println("begin load data ===================================");

        //create new table
        SimpleDateFormat sdf = new SimpleDateFormat("_yyyyMMddHH");
        String suffix = sdf.format(new Date());


        //create local table for 201-207
        ins.createLocalTable(localTable + suffix, localTable);
        //create dis_manage table
        ins.disManagedSearchvolumeDAO.createDisManagedSearchvolumeTable(localTable, disTable + suffix, localTable + suffix);

        Runnable runnable1 = () -> ins.poccess1(ins, latch, disTable + suffix);
        Runnable runnable2 = () -> ins.poccess2(ins, latch, disTable + suffix);

        threadPoolExecutor.execute(runnable1);
        threadPoolExecutor.execute(runnable2);
//        threadPoolExecutor.execute(runnable1);
//        threadPoolExecutor.execute(runnable2);
//        threadPoolExecutor.execute(runnable1);
//        threadPoolExecutor.execute(runnable2);
//        threadPoolExecutor.execute(runnable1);
//        threadPoolExecutor.execute(runnable2);
//        threadPoolExecutor.execute(runnable1);
//        threadPoolExecutor.execute(runnable2);
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("all data load end");

        //swap name
        System.out.println("begin change table name");
        ins.swapTableName(suffix);
        // drop table
        System.out.println("begin drop table ");
//        ins.dorpTable(suffix);

        threadPoolExecutor.shutdown();
    }

    private void poccess1(AllKeywordTokenizerUploadV2_old ins, CountDownLatch latch, String tableName) {
        List<DisManagedSearchvolume> allDatas = new ArrayList<>();
        long currentTimeMillis = System.currentTimeMillis();
        int keywordType = 1;
        int totalCount = 0;


        long pocMinId, pocMaxId;
        List<KeywordAdwordsExpandedEntityNew> entityList;

        do {
            pocMinId = getMinId1();
            pocMaxId = getMaxId();
            //get 30 KeywordAdwordsExpandedEntityNew
            entityList = ins.find30ById(keywordType, pocMinId);

            if (entityList.size() < 1) {
                continue;
            }
            //put to map
            Map<String, List<KeywordAdwordsExpandedEntityNew>> keyWordMap = new HashMap(128);
            for (KeywordAdwordsExpandedEntityNew entityNew :
                    entityList) {
                //decode
                try {
                    entityNew.setKeyword_name(StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(entityNew.getKeyword_name(), "UTF-8"))));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (entityNew.getKeyword_name().contains("\t")) {
                    System.out.println("skip key word =====" + entityNew.getKeyword_name() + "  id = " + entityNew.getKeywordId());
                    continue;
                }
                StringBuffer sb = new StringBuffer();
                sb.append(entityNew.getSearchEngineId()).append(spilt).append(entityNew.getLanguageId())
                        .append(spilt).append(entityNew.getLocationId()).append(spilt).append(entityNew.getKeywordId()).append(spilt).append(entityNew.getKeyword_name());
                String key = sb.toString();

                if (keyWordMap.get(key) != null && keyWordMap.get(key).size() >= 1) {
                    keyWordMap.get(key).add(entityNew);
                } else {
                    List entityNewList = new LinkedList();
                    if (entityNew.getMonth() != null) {
                        entityNewList.add(entityNew);
                    }

                    keyWordMap.put(key, entityNewList);
                }
            }
            //if map is null, continue
            if (keyWordMap.entrySet() == null)
                continue;
            //loop map
            for (Map.Entry<String, List<KeywordAdwordsExpandedEntityNew>> entry :
                    keyWordMap.entrySet()) {
                DisManagedSearchvolume entity;
                if (entry.getValue() != null && entry.getValue().size() >= 1) {
                    entity = ins.getDisManagedSearchvolume(entry.getValue());
                } else {
                    entity = ins.getDisManagedSearchvolume(entry.getKey());
                }

                if (entity == null) {
                    continue;
                }
                if (entity.getMonthlySvAttr_value().length < 1) {
                    continue;
                }
//                System.out.println("entity ==== " + entity.getKeyword_name());
//                System.out.println("Engine_id ====== " + entity.getEngine_id());
//                write(entry.getKey());
                KeywordStreamSearchengineCountryMappingEntity keywordStreamSearchengineCountryMappingEntity;

                if (countryAndLanguageFullNameMap.get(entity.getEngine_id()) == null)
                    continue;
                keywordStreamSearchengineCountryMappingEntity = countryAndLanguageFullNameMap.get(entity.getEngine_id()).get(entity.getLanguage_id());
                if (keywordStreamSearchengineCountryMappingEntity == null) continue;

                ins.getWord(entity, keywordStreamSearchengineCountryMappingEntity.getLanguageFullName(), keywordStreamSearchengineCountryMappingEntity.getCountryCode());
                synchronized (insertList1) {
//                    write(entity.toString());
                    insertList1.add(entity);
                }
            }
//            setMinId1(entityList.get(entityList.size() - 1).getKeywordId());
            System.out.println(Thread.currentThread().getName() + "this turn max id (1)= " + pocMinId + "this turn list size (1)= " + entityList.size());
            System.out.println(Thread.currentThread().getName() + "============   insertList1 size = " + insertList1.size());
            synchronized (insertList1) {
                if (insertList1.size() >= num) {
                    totalCount += insertList1.size();
                    ins.insert(insertList1, 0, tableName);
                    allDatas.addAll(insertList1);
                    insertList1 = Collections.synchronizedList(new LinkedList<>());
                }
            }
            System.out.println(Thread.currentThread().getName() + " ues time ===== " + ((System.currentTimeMillis() - currentTimeMillis) / 1000 + "s !"));
        } while (pocMinId < pocMaxId);
        synchronized (insertList1) {
            totalCount += insertList1.size();
            ins.insert(insertList1, 0, tableName);
            allDatas.addAll(insertList1);
            insertList1 = Collections.synchronizedList(new LinkedList<>());
        }
        latch.countDown();
        System.out.println("finish# keywordType:" + keywordType + " totalCount:" + totalCount);
        writeAllDataToFile(keywordType, allDatas);
    }


    private void poccess2(AllKeywordTokenizerUploadV2_old ins, CountDownLatch latch, String tableName) {
        List<DisManagedSearchvolume> allDatas = new ArrayList<>();
        int keywordType = 2;
        int totalCount = 0;

        long pocMinId, pocMaxId;

        List<KeywordAdwordsExpandedEntityNew> entityList;
        do {
            pocMinId = getMinId2();
            pocMaxId = getMaxId();
            //get 30 KeywordAdwordsExpandedEntityNew
            entityList = ins.find30ById(keywordType, pocMinId);

            if (entityList.size() < 1) {
                continue;
            }
            //put to map
            Map<String, List<KeywordAdwordsExpandedEntityNew>> keyWordMap = new HashMap(128);
            for (KeywordAdwordsExpandedEntityNew entityNew :
                    entityList) {
                //decode
                try {
                    entityNew.setKeyword_name(StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(entityNew.getKeyword_name(), "UTF-8"))));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (entityNew.getKeyword_name().contains("\t")) {
                    System.out.println("skip key word =====" + entityNew.getKeyword_name() + "  id = " + entityNew.getKeywordId());
                    continue;
                }
                StringBuffer sb = new StringBuffer();
                sb.append(entityNew.getSearchEngineId()).append(spilt).append(entityNew.getLanguageId())
                        .append(spilt).append(entityNew.getLocationId()).append(spilt).append(entityNew.getKeywordId()).append(spilt).append(entityNew.getKeyword_name());
                String key = sb.toString();

                if (keyWordMap.get(key) != null && keyWordMap.get(key).size() >= 1) {
                    keyWordMap.get(key).add(entityNew);
                } else {
                    List entityNewList = new LinkedList();
                    if (entityNew.getMonth() != null) {
                        entityNewList.add(entityNew);
                    }

                    keyWordMap.put(key, entityNewList);
                }
            }
            if (keyWordMap.entrySet() == null)
                continue;

            for (Map.Entry<String, List<KeywordAdwordsExpandedEntityNew>> entry :
                    keyWordMap.entrySet()) {
                DisManagedSearchvolume entity;
                if (entry.getValue() != null && entry.getValue().size() >= 1) {
                    entity = ins.getDisManagedSearchvolume(entry.getValue());
                } else {
                    //todo  String keywordName, int locationId, int keywordId, int searchEngineId, int languageId
                    entity = ins.getDisManagedSearchvolume(entry.getKey());
                }

                if (entity.getMonthlySvAttr_value().length < 1) {
                    continue;
                }
//                System.out.println("entity ==== " + entity.getKeyword_name());
//                System.out.println("Engine_id ====== " + entity.getEngine_id());
//                write(entry.getKey());
                KeywordStreamSearchengineCountryMappingEntity keywordStreamSearchengineCountryMappingEntity;
                try {
                    keywordStreamSearchengineCountryMappingEntity = countryAndLanguageFullNameMap.get(entity.getEngine_id()).get(entity.getLanguage_id());
                    if (keywordStreamSearchengineCountryMappingEntity == null) continue;
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }

                ins.getWord(entity, keywordStreamSearchengineCountryMappingEntity.getLanguageFullName(), keywordStreamSearchengineCountryMappingEntity.getCountryCode());
                synchronized (insertList2) {
//                    write(entity.toString());
                    insertList2.add(entity);
                }
            }
//            setMinId2(entityList.get(entityList.size() - 1).getKeywordId());
            System.out.println(Thread.currentThread().getName() + "this turn max id (2)= " + pocMinId + "this turn list size (2)= " + entityList.size());

            System.out.println(Thread.currentThread().getName() + "============   insertList2 size = " + insertList2.size());
            synchronized (insertList2) {
                if (insertList2.size() >= num) {
                    totalCount += insertList2.size();
                    ins.insert(insertList2, 0, tableName);
                    allDatas.addAll(insertList2);
                    insertList2 = Collections.synchronizedList(new LinkedList<>());
                }
            }
        } while (pocMinId < pocMaxId);
        synchronized (insertList2) {
            totalCount += insertList2.size();
            allDatas.addAll(insertList2);
            ins.insert(insertList2, 0, tableName);
        }
        insertList2 = Collections.synchronizedList(new LinkedList<>());
        latch.countDown();
        System.out.println("finish# keywordType:" + keywordType + " totalCount:" + totalCount);
        writeAllDataToFile(keywordType, allDatas);
    }

    private void writeAllDataToFile(int keywordType, List<DisManagedSearchvolume> allDatas) {
        String path = "/home/<USER>/clarity-backend-scripts/";
        String fullFileName = path + keywordType + "_all_old.txt";
        File file = new File(fullFileName);
        try {
            if (!file.exists())
                file.createNewFile();
            allDatas.sort(new Comparator<DisManagedSearchvolume>() {
                @Override
                public int compare(DisManagedSearchvolume o1, DisManagedSearchvolume o2) {
                    String key1 = String.join("_", o1.getKeyword_rankcheck_id() + "", o1.getEngine_id() + "",
                            o1.getLocation_id() + "", o1.getLanguage_id() + "");
                    String key2 = String.join("_", o2.getKeyword_rankcheck_id() + "", o2.getEngine_id() + "",
                            o2.getLocation_id() + "", o2.getLanguage_id() + "");
                    return key1.compareTo(key2);

                }
            });
            StringBuilder sb = new StringBuilder();
            allDatas.forEach(e -> {
                sb.append(e.getKeyword_rankcheck_id() + "\t").append(e.getEngine_id() + "\t")
                        .append(e.getLocation_id() + "\t").append(e.getLanguage_id() + "\t").append(e.getAvg_search_volume() + "\r\n");
            });
            FileWriter fw = new FileWriter(file, false);
            fw.write(sb.toString());
            fw.close();
        } catch (Exception ex) {

        }


    }

    private DisManagedSearchvolume getDisManagedSearchvolume(String str) {
        System.out.println("str ============:    " + str);
        String[] split = str.split(spilt);
        String keywordName = split[4];
        int locationId = Integer.valueOf(split[2]);
        int keywordId = Integer.valueOf(split[3]);
        int searchEngineId = Integer.valueOf(split[0]);
        int languageId = Integer.valueOf(split[1]);
        System.out.println("===== keywordName : " + keywordName);
        DisManagedSearchvolume entity = new DisManagedSearchvolume();
        entity.setHas_sv(0);
        entity.setCpc(0f);
        entity.setKeyword_name(keywordName);
        entity.setAvg_search_volume(0);
        entity.setLocation_id(locationId);
        entity.setKeyword_rankcheck_id(keywordId);
        entity.setSign(1);
        entity.setVersioning(0);
        entity.setEngine_id(searchEngineId);
        entity.setLanguage_id(languageId);

        entity.setMonthly_search_volume1(0);
        entity.setMonthly_search_volume2(0);
        entity.setMonthly_search_volume3(0);
        entity.setMonthly_search_volume4(0);
        entity.setMonthly_search_volume5(0);
        entity.setMonthly_search_volume6(0);
        entity.setMonthly_search_volume7(0);
        entity.setMonthly_search_volume8(0);
        entity.setMonthly_search_volume9(0);
        entity.setMonthly_search_volume10(0);
        entity.setMonthly_search_volume11(0);
        entity.setMonthly_search_volume12(0);


        List<Integer> monthlySvAttr_key = new LinkedList<>();

        monthlySvAttr_key.add(0);
        entity.setMonthlySvAttr_key(monthlySvAttr_key);
        entity.setMonthlySvAttr_value(monthlySvAttr_key);
        entity.setLastRefreshMonth(0);
//        List<Integer> codesInteger = new LinkedList<>();
//        codesInteger.add(-1);
        entity.setCategory(monthlySvAttr_key);

        return entity;

    }

    private DisManagedSearchvolume getDisManagedSearchvolume(List<KeywordAdwordsExpandedEntityNew> adwird) {
        DisManagedSearchvolume entity = new DisManagedSearchvolume();

        int size = adwird.size();
        if (adwird == null || size < 1) {
            return null;
        } else {
            Collections.sort(adwird);

            KeywordAdwordsExpandedEntityNew lastOne = adwird.get(size - 1);

            entity.setHas_sv(1);
            entity.setCpc(lastOne.getCostPerClick() == null ? 0f : lastOne.getCostPerClick());
            entity.setKeyword_name(lastOne.getKeyword_name());
            entity.setAvg_search_volume(lastOne.getAvgMonthlySearchVolume() == null ? 0 : lastOne.getAvgMonthlySearchVolume());
            entity.setLocation_id(lastOne.getLocationId());
            entity.setKeyword_rankcheck_id(lastOne.getKeywordId());
            entity.setSign(1);
            entity.setVersioning(0);
            entity.setEngine_id(lastOne.getSearchEngineId());
            entity.setLanguage_id(lastOne.getLanguageId());
            //System.out.println(Thread.currentThread().getName() + " =================   adwird.size() = " + adwird == null ? "adwird is null" : size);
            try {
                entity.setMonthly_search_volume1(size > 0 ? adwird.get(0).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume1(0);
            }
            try {
                entity.setMonthly_search_volume2(size > 1 ? adwird.get(1).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume2(0);
            }
            try {
                entity.setMonthly_search_volume3(size > 2 ? adwird.get(2).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume3(0);
            }
            try {
                entity.setMonthly_search_volume4(size > 3 ? adwird.get(3).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume4(0);
            }
            try {
                entity.setMonthly_search_volume5(size > 4 ? adwird.get(4).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume5(0);
            }
            try {
                entity.setMonthly_search_volume6(size > 5 ? adwird.get(5).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume6(0);
            }
            try {
                entity.setMonthly_search_volume7(size > 6 ? adwird.get(6).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume7(0);
            }
            try {
                entity.setMonthly_search_volume8(size > 7 ? adwird.get(7).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume8(0);
            }
            try {
                entity.setMonthly_search_volume9(size > 8 ? adwird.get(8).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume9(0);
            }
            try {
                entity.setMonthly_search_volume10(size > 9 ? adwird.get(9).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume10(0);
            }
            try {
                entity.setMonthly_search_volume11(size > 10 ? adwird.get(10).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume11(0);
            }
            try {
                entity.setMonthly_search_volume12(size > 11 ? adwird.get(11).getMonthlySearchVolume() : 0);
            } catch (java.lang.Exception e) {
                entity.setMonthly_search_volume12(0);
            }

            List<Integer> monthlySvAttr_key = new LinkedList<>();
            List<Integer> monthlySvAttr_value = new LinkedList<>();
            for (KeywordAdwordsExpandedEntityNew adw :
                    adwird) {
                monthlySvAttr_key.add(adw.getMonth() == null ? 0 : adw.getMonth());
                try {
                    monthlySvAttr_value.add(adw.getMonthlySearchVolume() == null ? 0 : adw.getMonthlySearchVolume());
                } catch (java.lang.Exception e) {
                    monthlySvAttr_value.add(0);
                }
            }

            entity.setMonthlySvAttr_key(monthlySvAttr_key);
            if (monthlySvAttr_key.size() == 0) {
                System.out.println("monthlySvAttr_key.size = 0 !");
                monthlySvAttr_key.add(0);
                entity.setMonthlySvAttr_key(monthlySvAttr_key);
            }
            entity.setMonthlySvAttr_value(monthlySvAttr_value);
            if (monthlySvAttr_value.size() == 0) {
                System.out.println("monthlySvAttr_value.size = 0 !");
                monthlySvAttr_value.add(0);
                entity.setMonthlySvAttr_key(monthlySvAttr_value);
            }

            entity.setLastRefreshMonth(adwird.get(size - 1).getMonth());
            if (lastOne.getCategory() != null) {
                List<String> result = Arrays.asList(lastOne.getCategory().split("#"));
                List<Integer> codesInteger = result.stream().filter(s -> !"".equals(s)).map(Integer::parseInt).collect(Collectors.toList());
                entity.setCategory(codesInteger);
            }
        }


        return entity;

    }

    private void getWord(DisManagedSearchvolume entity, String languageName, String countryCode) {

        List<String> word = new ArrayList<String>();
        List<String> stream = new ArrayList<String>();

        List<String> keywordVariationOneword = new ArrayList<String>();
        List<String> keywordVariationNgram = new ArrayList<String>();

        if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(entity.getKeyword_name(), "ar"));
        } else {
            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(entity.getKeyword_name(), countryCode));
        }
        stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(entity.getKeyword_name(), languageName));
        keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(entity.getKeyword_name()));
        keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(entity.getKeyword_name(), false));


        entity.setWord(word);
        entity.setStream(stream);


        entity.setKeyword_variation_oneword((keywordVariationOneword));
        entity.setKeyword_variation_ngram(setPlaceHolder(keywordVariationNgram));

    }


    public static List<String> setPlaceHolder(List<String> array) {
        String holder = "_";
        if (array.size() <= 2) {
            return array;
        }
        List<String> result = new ArrayList<String>();

        String startWordStr = array.get(0);
        String endWordStr = array.get(array.size() - 1);

        for (int i = 0; i < array.size(); i++) {
            String words = array.get(i);
            if ((StringUtils.startsWith(startWordStr, words) || StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
                words = words + " " + holder;
                result.add(words);
                continue;
            }
            if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
                words = holder + " " + words + " " + holder;
                result.add(words);
                continue;
            }
            if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (StringUtils.endsWith(endWordStr, words) || StringUtils.endsWith(words, endWordStr))) {
                words = holder + " " + words;
                result.add(words);
                continue;
            }
            result.add(words);
        }
//		System.out.println(array + "->" + result);
        return result;
    }


    private List<SeoClarityKeywordEntity> parseToKeywordEntity(List<Map<String, Object>> relList, int engine, int language) {
        Map<String, List<Map<String, Object>>> kwMap = new HashMap<String, List<Map<String, Object>>>();
        for (Map<String, Object> map : relList) {
            int kwId = Integer.valueOf(map.get("kwId").toString());
            String key = String.valueOf(kwId);
            if (kwMap.containsKey(key)) {
                kwMap.get(key).add(map);
            } else {
                List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                list.add(map);
                kwMap.put(key, list);
            }
        }
        List<SeoClarityKeywordEntity> processList = new ArrayList<SeoClarityKeywordEntity>();
        for (String key : kwMap.keySet()) {
            boolean isParseFailed = false;
            List<Map<String, Object>> list = kwMap.get(key);
            list.sort(new Comparator<Map<String, Object>>() {
                @Override
                public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                    int month1 = Integer.valueOf(o1.get("month").toString());
                    int month2 = Integer.valueOf(o2.get("month").toString());
                    if (month1 > month2) {
                        return 1;
                    } else if (month1 < month2) {
                        return -1;
                    }
                    return 0;
                }
            });
            List<SeoClarityKeywordEntity.MonthlySearchVolume> svList = new ArrayList<SeoClarityKeywordEntity.MonthlySearchVolume>();
            SeoClarityKeywordEntity kw = new SeoClarityKeywordEntity();
            for (int i = 0; i < list.size(); i++) {
                Map<String, Object> map = list.get(i);
                int kwId = Integer.valueOf(map.get("kwId").toString());
                try {
                    int month = Integer.valueOf(map.get("month").toString());
                    int avg_monthly_search_volume = Integer.valueOf(map.get("avg_monthly_search_volume") == null ? "0" : map.get("avg_monthly_search_volume").toString());
                    int monthly_search_volume = Integer.valueOf(map.get("monthly_search_volume") == null ? "0" : map.get("monthly_search_volume").toString());
                    String cost_per_click = map.get("cost_per_click") == null ? "0" : map.get("cost_per_click").toString();
                    SeoClarityKeywordEntity.MonthlySearchVolume sv = kw.new MonthlySearchVolume(month, avg_monthly_search_volume, monthly_search_volume, cost_per_click);
                    svList.add(sv);
                } catch (Exception e) {
                    isParseFailed = true;
                    System.out.println("=parse month trend failed. map:" + map + ", engine:" + engine + ", language:" + language + ", remove kid:" + kwId);
                    e.printStackTrace();
                }
            }
            if (!isParseFailed) {
                kw.setId(Integer.valueOf(key));
                kw.setMonthlySearchvolumeTrends(svList);
                processList.add(kw);
            }
        }

        return processList;
    }


    private Long findMaxId() {
        return keywordAdwordsExpandedEntityNewDAO.getMaxId();
    }

    private List<KeywordAdwordsExpandedEntityNew> find30ById(int keywordType, Long id) {
        return keywordAdwordsExpandedEntityNewDAO.select30Byid(keywordType, id);
    }

    private List<KeywordStreamSearchengineCountryMappingEntity> getCountryAndLanguageFullName() {
        return keywordStreamSearchengineCountryMappingEntityDAO.findAllEnabledEngineCountryMapping();
    }

    private int[] insert(List<DisManagedSearchvolume> list, int flag, String tableName) {
        if (1 == 1)
            return new int[0];
        System.out.println(Thread.currentThread().getName() + "insert into : " + list.size() + "rows" + "table name = " + tableName);

        write(list);

        try {
            return disManagedSearchvolumeDAO.insertBatchByName(list, tableName);
        } catch (Exception exception) {
            exception.printStackTrace();
            System.out.println(Thread.currentThread().getName() + "===========" + flag + " times try =============== ");
            try {
                Thread.sleep(30000);
                return insert(list, flag + 1, tableName);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        return null;
    }

    private void createLocalTable(String newTableName, String oldTableName) {
        if (1 == 1)
            return;
        managedSearchvolume201DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume202DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume203DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume204DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume205DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        managedSearchvolume206DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
        disManagedSearchvolumeDAO.createManagedSearchvolumeTable(newTableName, oldTableName);
    }

    private void swapTableName(String suffix) {
        if (1 == 1)
            return;
        //rename old table
        managedSearchvolume201DAO.renameTable(localTable, localTable + suffix + old);
        managedSearchvolume202DAO.renameTable(localTable, localTable + suffix + old);
        managedSearchvolume203DAO.renameTable(localTable, localTable + suffix + old);
        managedSearchvolume204DAO.renameTable(localTable, localTable + suffix + old);
        managedSearchvolume205DAO.renameTable(localTable, localTable + suffix + old);
        managedSearchvolume206DAO.renameTable(localTable, localTable + suffix + old);
        disManagedSearchvolumeDAO.renameTable(localTable, localTable + suffix + old);

        //rename new table
        managedSearchvolume201DAO.renameTable(localTable + suffix, localTable);
        managedSearchvolume202DAO.renameTable(localTable + suffix, localTable);
        managedSearchvolume203DAO.renameTable(localTable + suffix, localTable);
        managedSearchvolume204DAO.renameTable(localTable + suffix, localTable);
        managedSearchvolume205DAO.renameTable(localTable + suffix, localTable);
        managedSearchvolume206DAO.renameTable(localTable + suffix, localTable);
        disManagedSearchvolumeDAO.renameTable(localTable + suffix, localTable);

        //rename dis table
//        disManagedSearchvolumeDAO.renameTable(disTable, disTable + suffix + old);
//        disManagedSearchvolumeDAO.renameTable(disTable + suffix, disTable);


    }


    private void dorpTable(String suffix) {
        managedSearchvolume201DAO.dropTable(localTable + suffix + old);
        managedSearchvolume202DAO.dropTable(localTable + suffix + old);
        managedSearchvolume203DAO.dropTable(localTable + suffix + old);
        managedSearchvolume204DAO.dropTable(localTable + suffix + old);
        managedSearchvolume205DAO.dropTable(localTable + suffix + old);
        managedSearchvolume206DAO.dropTable(localTable + suffix + old);
        disManagedSearchvolumeDAO.dropTable(localTable + suffix + old);

        disManagedSearchvolumeDAO.dropTable(disTable + suffix + old);

    }

    public static void write(List<DisManagedSearchvolume> list) {
        String filePath = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/flag.txt";
        try {
            File file = new File(filePath);
            FileOutputStream fos = null;
            if (!file.exists()) {
                file.createNewFile();
                fos = new FileOutputStream(file);
            } else {
                fos = new FileOutputStream(file, false);
            }

            OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
            for (DisManagedSearchvolume disManagedSearchvolume :
                    list) {
                osw.write(disManagedSearchvolume.toString());
                osw.write("\r\n");
            }

            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/
}
