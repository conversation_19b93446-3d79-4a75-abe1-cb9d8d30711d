package seoclarity.backend.upload;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.api.client.http.*;
import com.google.api.client.http.javanet.NetHttpTransport;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.KgKeywordDao;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.*;

@CommonsLog
public class GoogleKGSearchApiMain {

    private static int threadCount = 7;
    private static ExecutorService threadPool = new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), new BackendThreadFactory());

    private static final String filepath = "/home/<USER>/usKeyword20210305.csv";
    private static final String missKwFilepath = "/home/<USER>/usMissKw.csv";
    private static final int pageSize = 50;
    private static int filePosition = 0;
    private static int memoryPosition = 0; // last: 192200 //761123 //  1159110

    private static int DESC_MAX_LENGTH;

    private static List<String> keyList = new CopyOnWriteArrayList<>();
    private static List<String> keyLimitList = new CopyOnWriteArrayList<>();

    private KgKeywordDao kgKeywordDao;

    public GoogleKGSearchApiMain() {
        kgKeywordDao = SpringBeanFactory.getBean("kgKeywordDao");
    }

    public static void main(String[] args) {
        GoogleKGSearchApiMain main = new GoogleKGSearchApiMain();
        main.process();
    }

    private void process() {
        initKeyLimitList();
        List<String> keywordList = new ArrayList<>();
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(new File(missKwFilepath));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        Scanner scanner = new Scanner(fileInputStream, "UTF-8");
        while (scanner.hasNextLine()) {
            String keyword = scanner.nextLine();
            if (filePosition >= memoryPosition) {
                String[] s = keyword.split(" ");
                if (s.length <= 3) {
                    keywordList.add(keyword);
                    if (keywordList.size() == pageSize) {
                        //thread
                        createThread(keywordList);
                        keywordList = new ArrayList<>();
                    }
                }
            }
            filePosition++;
        }

        try {
            fileInputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        // threadPool close
        while (!threadPool.isShutdown()) {
            ThreadUtil.sleep(15 * 1000);
            int aliveCount = ((ThreadPoolExecutor) threadPool).getActiveCount();
            System.out.println("Thread aliveCount : " + aliveCount);
            if (aliveCount == 0) {
                threadPool.shutdown();
            }
        }

        log.info("DESC_MAX_LENGTH: " + DESC_MAX_LENGTH + "filePosition: " + filePosition);
    }

    private void initKeyLimitList() {
        keyLimitList.add("AIzaSyD9m_0SNVEIpFdPUJhwTScx2wUXVX2NSaI");
        keyLimitList.add("AIzaSyC0_IlF2PwQNUi6cR1vdD7SGAffBL0Wqjg");
        keyLimitList.add("AIzaSyAYkFMH3iWcDTozQMHpkmWm4Gy-d6A2kK0");
        keyLimitList.add("AIzaSyAppmn_BQPyoAcqOCJYHGiT_mdmpjgUusw");
        keyLimitList.add("AIzaSyA17wZlIiGuFq0bH73uUoJU7uQHcsKWixk");
        keyLimitList.add("AIzaSyD-FXCtPb-1KzSWmUfCAnfLMZOKttayDtU");
        keyLimitList.add("AIzaSyA4JSs0sKoZHn3jgaC84TDed-V-L5sc1Fs");

        if (!keyList.contains("AIzaSyD9m_0SNVEIpFdPUJhwTScx2wUXVX2NSaI")) {
            keyList.add("AIzaSyD9m_0SNVEIpFdPUJhwTScx2wUXVX2NSaI");
        }

        if (!keyList.contains("AIzaSyC0_IlF2PwQNUi6cR1vdD7SGAffBL0Wqjg")) {
            keyList.add("AIzaSyC0_IlF2PwQNUi6cR1vdD7SGAffBL0Wqjg");
        }

        if (!keyList.contains("AIzaSyAYkFMH3iWcDTozQMHpkmWm4Gy-d6A2kK0")) {
            keyList.add("AIzaSyAYkFMH3iWcDTozQMHpkmWm4Gy-d6A2kK0");
        }

        if (!keyList.contains("AIzaSyAppmn_BQPyoAcqOCJYHGiT_mdmpjgUusw")) {
            keyList.add("AIzaSyAppmn_BQPyoAcqOCJYHGiT_mdmpjgUusw");
        }

        if (!keyList.contains("AIzaSyA17wZlIiGuFq0bH73uUoJU7uQHcsKWixk")) {
            keyList.add("AIzaSyA17wZlIiGuFq0bH73uUoJU7uQHcsKWixk");
        }

        if (!keyList.contains("AIzaSyD-FXCtPb-1KzSWmUfCAnfLMZOKttayDtU")) {
            keyList.add("AIzaSyD-FXCtPb-1KzSWmUfCAnfLMZOKttayDtU");
        }

        if (!keyList.contains("AIzaSyA4JSs0sKoZHn3jgaC84TDed-V-L5sc1Fs")) {
            keyList.add("AIzaSyA4JSs0sKoZHn3jgaC84TDed-V-L5sc1Fs");
        }
    }

    private void createThread(List<String> keywordList) {
        do {
            int aliveCount = ((ThreadPoolExecutor) threadPool).getActiveCount();
            if (aliveCount < threadCount && keyList.size() > 0 && keyLimitList.size() > 0) {
                String key = keyList.get(0);
                if (keyLimitList.contains(key)) {
                    keyList.remove(key);
                    threadPool.execute(new GoogleKGSearchApiMain.GoogleKGSearchApiThread(keywordList, key));
                    break;
                }else {
                    keyList.remove(key);
                    try {
                        System.out.println("found bug!!!! keyList size: " + keyList.size() + ", keyLimitList size: " + keyLimitList.size() + " sleep 10s");
                        Thread.sleep(10 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                if (keyLimitList.size() == 0) {
                    System.out.println(" createThread => keyLimitList over limit, sleep 2h! aliveCount: " + aliveCount + ", keyList size: " + keyList.size() + ", keyLimitList size: " + keyLimitList.size() + ", DESC_MAX_LENGTH: " + DESC_MAX_LENGTH);
                    try {
                        Thread.sleep(2 * 60 * 60 * 1000);
                        initKeyLimitList();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }else {
                    try {
                        System.out.println("aliveCount: " + aliveCount + ", keyList size: " + keyList.size() + ", keyLimitList size: " + keyLimitList.size() + " sleep 10s" + ", DESC_MAX_LENGTH: " + DESC_MAX_LENGTH);
                        Thread.sleep(10 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        } while (true);
    }

    class GoogleKGSearchApiThread implements Runnable {
        List<String> keywordList;
        String key;

        public GoogleKGSearchApiThread(List<String> keywordList, String key) {
            this.keywordList = keywordList;
            this.key = key;
        }

        @Override
        public void run() {
            doGet();
        }

        public void doGet() {
            if (key == null || key == "" || keywordList == null || keywordList.size() == 0) {
                return;
            }

            List<GoogleKGEntity> result = new ArrayList<>();
            for (String keyword : keywordList) {
                try {
                    HttpTransport httpTransport = new NetHttpTransport();
                    HttpRequestFactory requestFactory = httpTransport.createRequestFactory();
                    GenericUrl url = new GenericUrl("https://kgsearch.googleapis.com/v1/entities:search");
                    url.put("query", keyword);
                    url.put("limit", "100");
                    url.put("key", key);
                    HttpRequest request = requestFactory.buildGetRequest(url);
                    HttpResponse httpResponse = request.execute();
                    String jsonStr = httpResponse.parseAsString().replace("@id", "id").replace("@type", "type").replace("@context", "context");
                    ParseEntity parseEntity = JSONObject.parseObject(jsonStr, ParseEntity.class);
                    List<GoogleKGEntity> sqlEntities = new ArrayList<>();
                    List<ParseEntity.ItemListElement> itemListElement = parseEntity.getItemListElement();
                    for (ParseEntity.ItemListElement element : itemListElement) {
                        if (element == null || element.getResult() == null || element.getResult().getId() == null) {
                            //边界
                            continue;
                        }
                        GoogleKGEntity googleKGEntity = new GoogleKGEntity();
                        googleKGEntity.setOriginalKeyword(keyword);
                        googleKGEntity.setKgId(element.getResult().getId());
                        googleKGEntity.setKgType(StringUtils.join(element.getResult().getResultTypeList(), "!_!"));
                        googleKGEntity.setKgSubType(element.getElementType());
                        googleKGEntity.setKgDesc(element.getResult().getDescription());
                        if (element.getResult().getDetailedDescription() != null) {
                            googleKGEntity.setDetailedLicense(element.getResult().getDetailedDescription().getLicense());
                            googleKGEntity.setDetailedUrl(element.getResult().getDetailedDescription().getUrl());
                            String articleBody = element.getResult().getDetailedDescription().getArticleBody();
                            googleKGEntity.setDetailedArticleBody(articleBody);
                            if (DESC_MAX_LENGTH < articleBody.length()) { DESC_MAX_LENGTH = articleBody.length(); }
                        }
                        googleKGEntity.setKgName(element.getResult().getName());
                        googleKGEntity.setKgUrl(parseEntity.getUrl());
                        googleKGEntity.setCreateDate(new Date());
                        sqlEntities.add(googleKGEntity);
                    }
                    result.addAll(sqlEntities);
                } catch (Exception e) {
                    if (e.getMessage().contains("429 unknown")) {
                        keyLimitList.remove(key);
                        log.info("doGet => error: 429, keyLimitList remove " + key);
                        List<String> lines = new ArrayList<String>();
                        for (String kw : keywordList) {
                            StringBuffer line = new StringBuffer();
                            line.append(kw);
                            lines.add(line.toString());
                        }
                        try {
                            FileUtils.writeLines(new File(missKwFilepath), lines, true);
                            lines.clear();
                        } catch (IOException ioe) {
                            log.error("append data error!");
                            ioe.printStackTrace();
                        }
                        return;
                    }
                }
                try {
                    Thread.sleep(300);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            if (result.size() > 0) {
                kgKeywordDao.insertBatch(result);
            }
            log.info("Thread-" + Thread.currentThread().getName() + " process finished! key: " + key + ", keywordList size: " + keywordList.size() + ", sqlEntity size: " + result.size() + ", filePosition: " + filePosition);
            keyList.add(key);
        }
    }

    @Data
    private static class ParseEntity {
        private Context context;
        @Data
        private class Context {
            private String kg;
        }
        private String url;
        private List<ItemListElement> itemListElement = new ArrayList<>();
        @Data
        private class ItemListElement {
            @JSONField(name = "type")
            private String elementType;
            private Double resultScore;
            private Result result;
            @Data
            private class Result {
                @JSONField(name = "type")
                private List<String> resultTypeList;
                //name 返回值首字母是大写的
                private String name;
                private String id;
                private String description;
                private DetailedDescription detailedDescription;
                @Data
                private class DetailedDescription {
                    private String license;
                    private String articleBody;
                    private String url;
                }
            }
        }
    }

    @Data
    public static class GoogleKGEntity {
        private long id;
        private String originalKeyword;
        private String kgId;
        private String kgType;
        private String kgSubType;
        private String kgDesc;
        private String detailedLicense;
        private String detailedUrl;
        private String detailedArticleBody;
        private String kgUrl;
        private String kgName;
        private Date createDate;
    }
}
