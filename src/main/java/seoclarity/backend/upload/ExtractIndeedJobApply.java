package seoclarity.backend.upload;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.QuoteMode;
import seoclarity.backend.utils.DingTools;
import seoclarity.backend.utils.FTPUtil;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.ExtractIndeedJobApply" -Dexec.cleanupDaemonThreads=false -Dexec.args="2022-02-28"
 */
public class ExtractIndeedJobApply {

    private static final String SCRIBE_PATH = "/dat/scribeKeyword/applyon_job_mobile_commoncrawl_keywordRank_119_1/";
    private static final String FTP_PATH = "/home/<USER>/8711/";

    public static void main(String[] args) throws IOException {
        System.out.println("start process " + JSONUtil.toJsonStr(args));
        ExtractIndeedJobApply extractIndeedJobApply = new ExtractIndeedJobApply();
        if (args != null && args.length == 1) {
            String[] dateArr = args[0].split(",");
            for (String dateStr : dateArr) {
                extractIndeedJobApply.process(DateUtil.parse(dateStr, "yyyy-MM-dd"));
            }
        } else if (args == null || args.length == 0) {
            extractIndeedJobApply.process(DateUtil.yesterday());
        }

    }

    public void process(DateTime dateTime) throws IOException {
        String targetPath = "/home/<USER>/indeed_job/";
        if(FileUtil.exist(targetPath) == false) {
            FileUtil.mkdir(targetPath);
        }

        String dateStr = dateTime.toString("yyyy-MM-dd");
        Map<String, JSONObject> resultMap = new HashMap<>();
        File[] files = FileUtil.ls(SCRIBE_PATH);
        JSONConfig jsonConfig = JSONConfig.create().setOrder(true);
        for (File file : files) {
            if(StrUtil.containsIgnoreCase(file.getName(), dateStr) == false) {
                continue;
            }
            System.out.println("Processing : "+file.getName());
            List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
            for (String line : lines) {
                JSONObject jsonObject = JSONUtil.parseObj(line, jsonConfig);
                String keyword = jsonObject.getStr("keyword");
                JSONArray ranks = jsonObject.getJSONArray("keywordRankEntityVOs");
                JSONObject targetObj = resultMap.get(keyword);
                if(targetObj != null && ranks != null && ranks.size() > 0) {
                    JSONArray targetRanks = targetObj.getJSONArray("keywordRankEntityVOs");
                    if(targetRanks == null || targetRanks.isEmpty()) {
                        resultMap.put(keyword, jsonObject);
                    }
                } else if (targetObj == null) {
                    resultMap.put(keyword, jsonObject);
                }
            }
        }

        if(resultMap.size() > 0) {
            //开始去重
            File outputFile = new File(targetPath+"www.indeed.com_GoogleJob_"+dateStr+"_Mobile.csv");
            if(outputFile.exists()) {
                outputFile.delete();
            }
//            CSVPrinter csvPrinter = new CSVPrinter(new FileWriter(outputFile), CSVFormat.DEFAULT.withDelimiter('\t'));
            FileUtil.appendString("Keyword\tRanking URL\tRank\tApply Buttons\n", outputFile, StandardCharsets.UTF_8);
//            csvPrinter.printRecord("Keyword", "Ranking URL", "Rank", "Apply Buttons");
            for (JSONObject jobEntity : resultMap.values()) {
                String kName = jobEntity.getStr("keyword");
                JSONArray ranks = jobEntity.getJSONArray("jobEntityVOs");
                if(ranks == null) {
                    FileUtil.appendString(FormatUtils.decodeKeyword(kName)+"\t-\t-\t-\n", outputFile, StandardCharsets.UTF_8);
//                    csvPrinter.printRecord(FormatUtils.decodeKeyword(kName), "-", "-", "-");
                    continue;
                }
                for (int i = 0; i < ranks.size(); i++) {
                    if(i == 10) {
                        break;
                    }
                    JSONObject rank = ranks.getJSONObject(i);
                    String link = rank.getStr("jobPrimaryJobLink");
                    JSONObject applyOnList = rank.getJSONObject("applyOnList");
//                    JSONArray applyOnList = rank.getJSONArray("applyOnList");
                    FileUtil.appendString(FormatUtils.decodeKeyword(kName)+"\t"+link+"\t"+(i+1)+"\t"+(applyOnList == null ? "[]" : JSONUtil.toJsonStr(applyOnList.keySet()))+"\n", outputFile, StandardCharsets.UTF_8);
//                    FileUtil.appendString(FormatUtils.decodeKeyword(kName)+"\t"+link+"\t"+(i+1)+"\t"+(applyOnList == null ? "[]" : applyOnList.toString())+"\n", outputFile, StandardCharsets.UTF_8);
//                    csvPrinter.printRecord(FormatUtils.decodeKeyword(kName),
//                            link,
//                            i+1,
//                            applyOnList == null ? "[]" : applyOnList.toString());

                }
            }
//            csvPrinter.flush();
//            csvPrinter.close();

            FTPUtils.saveFileToFTP(8711, outputFile.getAbsolutePath(), FTP_PATH);
        } else {
            DingTools.sendMessage("[Indeed监控] Job Weekly extract size is 0");
        }

    }

}
