package seoclarity.backend.upload;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.clickhouse.linksga.LinksGaClarityDBEntityDAO;
import seoclarity.backend.entity.ClarityDBConstants;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;
//mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.LinksGAUpload" -Dexec.args=""
public class LinksGAUpload {
	private static int version = 1;
	private static int specialVersion = 0;
	
	private static Date processDate = new Date();
	private static boolean isBkProcess = false;
	
	private static final String databaseName = "siteclarity";
	private static final String finalTableName = "dis_siteclarity_ga";
	

	public static final Log logger = LogFactory.getLog(LinksGAUpload.class);

	private static String storeDoneFilePath = "/home/<USER>/linkGa/needUpload/";
//	private static String backProcessStoreDoneFilePath = "/home/<USER>/linkGa_backprocess/upload/";
	private static String storeTempFilePath = "/home/<USER>/linkGa/uploading/";
	private static String storeBKFilePath = "/home/<USER>/linkGa/backUpFolder/";
	private static String storeDuplicateFilePath = "/home/<USER>/linkGa/duplicate/";


	private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

	public static final int TARGETURL_ADDEDBY_GA = 3;

	public static final int TARGETURL_ADDEDBY_CONVERSION = 99;
	
	private LinksGaClarityDBEntityDAO linksGaClarityDBEntityDAO;
	
	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	
	private static List<String> processingFileList = new ArrayList<String>();
	
	private static Integer logId;

	public LinksGAUpload() {
		
		linksGaClarityDBEntityDAO = SpringBeanFactory.getBean("linksGaClarityDBEntityDAO");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		
	}

	public static void main(String args[]) {
		
		if (args.length > 0 && StringUtils.isNotBlank(args[0])) {
			try {
				processDate = FormatUtils.toDate(args[0], "yyyyMMddHH");
			} catch (Exception e) {
				System.out.println("Date format failed, format should be yyyyMMddHH, exit!!!! : " + args[0]);
				return;
			}
			isBkProcess = true;
		}
		
		if (args.length > 1) {
			storeDoneFilePath = args[1];
		}
		
		storeTempFilePath = storeTempFilePath + "/" + FormatUtils.formatDate(processDate, "yyyyMMddHH") + "/";
		
		threadPool.init();
		CommonUtils.initThreads(10);
		LinksGAUpload crawlGoogalAnalyticsEngine = new LinksGAUpload();
		System.out.println("--- specialVersion:" + specialVersion + ", use version:" + version + ", storeDoneFilePath:" + storeDoneFilePath + ", 30s to continue...");
		
		Calendar cal = Calendar.getInstance();
		cal.setTime(processDate);
		
		if (crawlGoogalAnalyticsEngine.checkIsProcessingUpload()) {
			System.out.println("There found more than one processing still not finished, exit !!!");
			System.exit(-1);
		}
		
		File doneFolder = new File(storeDoneFilePath);
		if (doneFolder != null && doneFolder.isDirectory()) {
			boolean hasFilesNeedProcess = false;
			
			for (File file : doneFolder.listFiles()) {
				if (StringUtils.startsWith(file.getName(), "ga_") && file.isFile()) {
					
					System.out.println("Found file " + file.getName() + ", start to process !");
					hasFilesNeedProcess = true;
					break;
				}
			}
			
			if (!hasFilesNeedProcess) {
				
				crawlGoogalAnalyticsEngine.insertEmptyLog();
				System.out.println("There do not have any files need to be process, skiped+++");
				System.exit(-1);
			}

		}
		
		
		try {
			Thread.sleep(5000);
			crawlGoogalAnalyticsEngine.process();
		} catch (Exception e) {
			e.printStackTrace();
		}
		threadPool.destroy();
	}
	
	//if no files need to be upload, then insert one record with final status = success(2) and upload status = success(2)
	private void insertEmptyLog(){
		
		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
		
		clarityDBUploadLogEntity.setTmpTableName("");
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName(finalTableName);
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadDailyRows(0);
		clarityDBUploadLogEntity.setFinalTableUploadRows(0);
		clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_LINKS_GA);
		
		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadEndTime(new Date());
		clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}
	
	private boolean checkIsProcessingUpload(){
		
		List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(ClarityDBUploadLogEntity.UPLOAD_TYPE_LINKS_GA);
		if (CollectionUtils.isNotEmpty(list)) {
//			return true;
		}
		return false;
	}
	
	private void process() {
		
		long startTime = System.currentTimeMillis();
		
		File duplicateFolder = new File(storeDuplicateFilePath);
		if (duplicateFolder == null || !duplicateFolder.isDirectory()) {
			duplicateFolder.mkdir();
		}
		
		moveFilesToProcessingFolder();
		
		// insert into temp table
		if (StringUtils.isNotBlank(finalTableName)) {
			
			ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
			
			clarityDBUploadLogEntity.setTmpTableName(finalTableName);
			clarityDBUploadLogEntity.setDatabaseName(databaseName);
			clarityDBUploadLogEntity.setFinalTableName(finalTableName);
			clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
			clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_LINKS_GA);
			try {
				clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			
			clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
			clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);
			
			logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
			System.out.println("===add log for ga_upload_log, id:" + logId);
			
			//process normal
			File doneFolder = new File(storeDoneFilePath);
			if (doneFolder == null || !doneFolder.isDirectory()) {
				System.out.println("Folder is not exist :" + doneFolder);
				long endTime = System.currentTimeMillis();
				int elapsedSeconds = (int) (endTime - startTime);

				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0, elapsedSeconds, logId);
				return;
			}
			try {
				
				File tmpFile;
				for (String gaFileFullPath : processingFileList) {
					tmpFile = new File(gaFileFullPath);
					
					if (tmpFile == null || !tmpFile.isFile()) {
						System.out.println("File is not exist or is a folder : " + gaFileFullPath);
						continue;
					}
					
					//String prefix = "ga_" + ownDomainId + "_" + gaInfoVO.getTrafficDate() + "_"  + gaInfoVO.getVersion() + "_" + (isBackProcess ? "bk_" : startHour + "_");
					
					System.out.println("=====processing file:" + tmpFile.getAbsolutePath());
					postFile(finalTableName, tmpFile);
				}
				
				long endTime = System.currentTimeMillis();
				int elapsedSeconds = (int) (endTime - startTime);
				
				for (String gaFileFullPath : processingFileList) {
					tmpFile = new File(gaFileFullPath);
					
					if (tmpFile == null || !tmpFile.isFile()) {
						System.out.println("File is not exist or is a folder : " + gaFileFullPath);
						continue;
					}
					
				}
				
				
//				Integer totalCount = linksGaClarityDBEntityDAO.getTotalCount(processDate, isBkProcess);
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0, elapsedSeconds, logId);
				
			} catch (Exception e) {
				System.out.println("Updated status in log table to " + ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE);
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
				System.out.println("Insert table failed, table: " + finalTableName + ", folder: "  + storeDoneFilePath);
				
				moveFilesBackProcessingFolder();
				e.printStackTrace();
				return ;
			}
			
			
			try {
				moveFileAndZip();
			} catch (Exception e) {
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS_MOVE_FILE_FAIL, logId);
			} finally {
				deleteTempProcessingFolder();
			}
			
			
		} else {
			System.out.println("====== tmpTable is empty : " + finalTableName);
		}
		
	}
	
	private void moveFilesToProcessingFolder(){
		
		List<String> uniqueKeyList = new ArrayList<>();
		
		File targetFolder = new File(storeTempFilePath);
		if (targetFolder == null || !targetFolder.isDirectory()) {
			System.out.println("Folder is not exist, mkdir: " + storeTempFilePath);
			targetFolder.mkdir();
		}
		
		File doneFolder = new File(storeDoneFilePath);
		
		System.out.println("====moving files to processing folder, total file:" + doneFolder.length() + "!! from " + storeDoneFilePath + " to " + storeTempFilePath);
		String uniqueKey = "";
		
		for (File gaFile : doneFolder.listFiles()) {
			try {
				if (StringUtils.startsWith(gaFile.getName(), "ga_") && gaFile.isFile()) {
					
					
					//String prefix = "ga_" + ownDomainId + "_" + gaInfoVO.getTrafficDate() + "_"  + gaInfoVO.getVersion() + "_" + (isBackProcess ? "bk_" : startHour + "_");
					String fileName = gaFile.getName();
					String[] arrays = StringUtils.split(fileName, "_");
					
					Integer ownDomainId = NumberUtils.toInt(arrays[1]);
					Integer crawlRequestId = NumberUtils.toInt(arrays[2]);
					Integer trafficDate = NumberUtils.toInt(arrays[3]);
					Integer version = NumberUtils.toInt(arrays[4]);
					
					uniqueKey = ownDomainId + "-" + crawlRequestId + "-" + trafficDate + "-" + version;
					
					if (CollectionUtils.isNotEmpty(uniqueKeyList) && uniqueKeyList.contains(uniqueKey)) {
						
						System.out.println("file is exist with the unique key:" + uniqueKey + ", moving file to duplicate folder, file:" + gaFile.getName());
						FileUtils.moveFile(gaFile, new File(storeDuplicateFilePath + "/" + gaFile.getName()));
						continue;
						
					} else {
						uniqueKeyList.add(uniqueKey);
						System.out.println("Adding to unique key list:" + uniqueKey);
						FileUtils.moveFile(gaFile, new File(storeTempFilePath + "/" + gaFile.getName()));
						processingFileList.add(storeTempFilePath + "/" + gaFile.getName());
					}
				}
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
	}
	
	private void moveFilesBackProcessingFolder(){
		
		File processingFolder = new File(storeTempFilePath);
		
		System.out.println("====moving files back from processing folder, total file:" + processingFolder.length() + "!! from " + storeTempFilePath + " to " + storeDoneFilePath);
		
		for (File gaFile : processingFolder.listFiles()) {
			try {
				FileUtils.moveFile(gaFile, new File(storeDoneFilePath + "/" + gaFile.getName()));
				processingFileList.add(storeDoneFilePath + "/" + gaFile.getName());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		deleteTempProcessingFolder();
	}
	
	private void moveFileAndZip(){
		
		File bkFolder = new File(storeBKFilePath);
		if (bkFolder == null || !bkFolder.isDirectory()) {
			System.out.println("BK folder is not exist, mkdir: " + storeBKFilePath);
			bkFolder.mkdir();
		}
		
		File tmpFile;
		File targetFile;
		
		for (String fileFullPath : processingFileList) {
			// zip file
			try {
				tmpFile = new File(fileFullPath);
				targetFile = new File(bkFolder + "/" + tmpFile.getName());
				
				FileUtils.moveFile(tmpFile, targetFile);
				
				System.out.println("zipped file : " + targetFile.getAbsolutePath());
	            GZipUtil.zipFile(targetFile.getAbsolutePath());
	            
	            targetFile.delete();
	            System.out.println("delete file : [" + fileFullPath + "]");
	        } catch (Exception e) {
	        	System.out.println("delete file failed. file: [" + fileFullPath + "]");
	            e.printStackTrace();
	        }
			
		}
		
		deleteTempProcessingFolder();
	}
	
	private void deleteTempProcessingFolder(){
		//deleted the temp processing folder
		File tempFolder = new File(storeTempFilePath);
		
		if (tempFolder != null && tempFolder.isDirectory()) {
			tempFolder.delete();
		}
	}

	
	private void postFile(String tableName, File tmpFile) throws Exception{
		if (tmpFile != null && tmpFile.exists()) {
			
			String sql = linksGaClarityDBEntityDAO.getInsertSql(finalTableName);
			
			String fileName = tmpFile.getName();
			String[] arrays = StringUtils.split(fileName, "_");
			
			Integer ownDomainId = NumberUtils.toInt(arrays[1]);
			Integer crawlRequestId = NumberUtils.toInt(arrays[2]);
			Date trafficDate = FormatUtils.toDate(arrays[3], "yyyyMMdd");
			Integer version = NumberUtils.toInt(arrays[4]);
			
			//TODO query from clarityDB and find if there had data for that day.
			String checkExitSql = " select count() from dis_siteclarity_ga where crawl_request_log_id = " + crawlRequestId 
					+ " and versoin = " + version + " and domain_id = " + ownDomainId;
			
			System.out.println("SQL" + checkExitSql);
			
			//=======================================  clusterA ========================================
			String response = ClarityDBUtils.executeQueryCSV(ClarityDBConstants.NEW_LINKS_GA_HOST_07, checkExitSql);
			Integer count = 0;
			
			if (StringUtils.isNotBlank(response)) {
				try {
					count = NumberUtils.toInt(StringUtils.split(response, "\n")[0]);
					System.out.println("OID:" + ownDomainId + ", GA API response: count:" + count);
					
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			if (count > 0) {
				System.out.println("==== data exist in clarityDB : OID:" + ownDomainId + ", trafficDate:" + trafficDate + 
						", version:" + version + ", moving file to " + storeDuplicateFilePath + "/" + tmpFile.getName());
				
				FileUtils.moveFile(tmpFile, new File(storeDuplicateFilePath + "/" + tmpFile.getName()));
				
			} else {
				String str = ClarityDBUtils.postBatchInsert(ClarityDBConstants.NEW_LINKS_GA_HOST_07, sql, tmpFile.getAbsolutePath());
				System.out.println(str);
				
				if (StringUtils.contains(str, "DB::Exception")) {
					System.out.println(sql);
					throw new Exception(str);
				}
			}
			
		} else {
			System.out.println("File not exists:" + tmpFile.getName());
		}
	}

	

	public Set<Integer> getDomainSet(String domainIds) {
		Set<Integer> returnDomain = new HashSet<Integer>();
		String[] dIds = domainIds.split(",");

		if (dIds != null && dIds.length > 0) {
			for (int i = 0; i < dIds.length; i++) {
				try {
					returnDomain.add(Integer.parseInt(dIds[i]));
				} catch (Exception e) {
					
				}
			}
		}
		return returnDomain;
	}

}
