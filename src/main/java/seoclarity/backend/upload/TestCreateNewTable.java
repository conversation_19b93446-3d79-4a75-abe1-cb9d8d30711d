//package seoclarity.backend.upload;
//
//import seoclarity.backend.dao.clickhouse.kp201.ManagedSearchvolume201DAO;
//import seoclarity.backend.dao.clickhouse.kp202.ManagedSearchvolume202DAO;
//import seoclarity.backend.dao.clickhouse.kp203.ManagedSearchvolume203DAO;
//import seoclarity.backend.dao.clickhouse.kp204.ManagedSearchvolume204DAO;
//import seoclarity.backend.dao.clickhouse.kp205.ManagedSearchvolume205DAO;
//import seoclarity.backend.dao.clickhouse.kp206.ManagedSearchvolume206DAO;
//import seoclarity.backend.dao.clickhouse.kp207.DisManagedSearchvolumeDAO;
//import seoclarity.backend.utils.SpringBeanFactory;
//
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.concurrent.*;
//
///**
// * @program: backend
// * @description: test to create new table
// * @packagename: seoclarity.backend.upload
// * @author: cil
// * @date: 2021-07-02 09:55
// **/
//public class TestCreateNewTable {
//    //201
//    private final ManagedSearchvolume201DAO managedSearchvolume201DAO;
//    //202
//    private final ManagedSearchvolume202DAO managedSearchvolume202DAO;
//    //203
//    private final ManagedSearchvolume203DAO managedSearchvolume203DAO;
//    //204
//    private final ManagedSearchvolume204DAO managedSearchvolume204DAO;
//    //205
//    private final ManagedSearchvolume205DAO managedSearchvolume205DAO;
//    //206
//    private final ManagedSearchvolume206DAO managedSearchvolume206DAO;
//    //207
//    private final DisManagedSearchvolumeDAO disManagedSearchvolumeDAO;
//
//    private static final String localTable = "local_test_tttt";
//    private static final String disTable = "dis_test_tttt";
//    private static final String old = "_old";
//
//    public TestCreateNewTable() {
//        managedSearchvolume201DAO = SpringBeanFactory.getBean("managedSearchvolume201DAO");
//        managedSearchvolume202DAO = SpringBeanFactory.getBean("managedSearchvolume202DAO");
//        managedSearchvolume203DAO = SpringBeanFactory.getBean("managedSearchvolume203DAO");
//        managedSearchvolume204DAO = SpringBeanFactory.getBean("managedSearchvolume204DAO");
//        managedSearchvolume205DAO = SpringBeanFactory.getBean("managedSearchvolume205DAO");
//        managedSearchvolume206DAO = SpringBeanFactory.getBean("managedSearchvolume206DAO");
//        disManagedSearchvolumeDAO = SpringBeanFactory.getBean("disManagedSearchvolumeDAO");
//
//    }
//
//
//    public static void main(String[] args) {
//        TestCreateNewTable test = new TestCreateNewTable();
//        test.tableTest();
//
//    }
//
//    public void tableTest() {
//        SimpleDateFormat sdf = new SimpleDateFormat("_yyyyMMdd");
//        String suffix = sdf.format(new Date());
//        //create dis_manage table
//        disManagedSearchvolumeDAO.createDisManagedSearchvolumeTable(disTable, disTable + suffix);
//
//        //create local table for 201-207
//        createLocalTable(localTable + suffix, localTable);
//
//
//        //load data....
//        CountDownLatch countDownLatch = new CountDownLatch(10);
//        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 10, 1000, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10));
//        Runnable runnable = () -> {
//            int i = (int) (Math.random() * 10000);
//            System.out.println("need time " + i);
//            try {
//                Thread.currentThread().sleep(i);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            countDownLatch.countDown();
//            System.out.println(Thread.currentThread().getName() + " load data end");
//        };
//        for (int i = 0; i < 10; i++) {
//            threadPoolExecutor.execute(runnable);
//        }
//        try {
//            countDownLatch.await();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        System.out.println("all data load end");
//
//        //swap name
//        System.out.println("begin change table name");
//        swapTableName(suffix);
//        // drop table
//        dorpTable(suffix);
//
//
//    }
//
//
//    private void createLocalTable(String newTableName, String oldTableName) {
//        managedSearchvolume201DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
//        managedSearchvolume202DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
//        managedSearchvolume203DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
//        managedSearchvolume204DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
//        managedSearchvolume205DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
//        managedSearchvolume206DAO.createManagedSearchvolumeTable(newTableName, oldTableName);
//        disManagedSearchvolumeDAO.createManagedSearchvolumeTable(newTableName, oldTableName);
//    }
//
//
//    private void swapTableName(String suffix) {
//        //rename old table
//        managedSearchvolume201DAO.renameTable(localTable, localTable + suffix + old);
//        managedSearchvolume202DAO.renameTable(localTable, localTable + suffix + old);
//        managedSearchvolume203DAO.renameTable(localTable, localTable + suffix + old);
//        managedSearchvolume204DAO.renameTable(localTable, localTable + suffix + old);
//        managedSearchvolume205DAO.renameTable(localTable, localTable + suffix + old);
//        managedSearchvolume206DAO.renameTable(localTable, localTable + suffix + old);
//        disManagedSearchvolumeDAO.renameTable(localTable, localTable + suffix + old);
//
//        //rename new table
//        managedSearchvolume201DAO.renameTable(localTable + suffix, localTable);
//        managedSearchvolume202DAO.renameTable(localTable + suffix, localTable);
//        managedSearchvolume203DAO.renameTable(localTable + suffix, localTable);
//        managedSearchvolume204DAO.renameTable(localTable + suffix, localTable);
//        managedSearchvolume205DAO.renameTable(localTable + suffix, localTable);
//        managedSearchvolume206DAO.renameTable(localTable + suffix, localTable);
//        disManagedSearchvolumeDAO.renameTable(localTable + suffix, localTable);
//
//        //rename dis table
//        disManagedSearchvolumeDAO.renameTable(disTable, disTable + suffix + old);
//        disManagedSearchvolumeDAO.renameTable(disTable + suffix, disTable);
//
//
//    }
//
//
//    private void dorpTable(String suffix) {
//        managedSearchvolume201DAO.dropTable(localTable + suffix + old);
//        managedSearchvolume202DAO.dropTable(localTable + suffix + old);
//        managedSearchvolume203DAO.dropTable(localTable + suffix + old);
//        managedSearchvolume204DAO.dropTable(localTable + suffix + old);
//        managedSearchvolume205DAO.dropTable(localTable + suffix + old);
//        managedSearchvolume206DAO.dropTable(localTable + suffix + old);
//        disManagedSearchvolumeDAO.dropTable(localTable + suffix + old);
//
//        disManagedSearchvolumeDAO.dropTable(disTable + suffix + old);
//
//    }
//}
