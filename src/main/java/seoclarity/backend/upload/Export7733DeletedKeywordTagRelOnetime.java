package seoclarity.backend.upload;

import org.apache.commons.io.FileUtils;
import seoclarity.backend.dao.actonia.CdbTrackedKeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.CdbTrackedKeywordEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class Export7733DeletedKeywordTagRelOnetime {

    private CdbTrackedKeywordEntityDAO cdbTrackedKeywordEntityDAO;

    public Export7733DeletedKeywordTagRelOnetime(){
        cdbTrackedKeywordEntityDAO = SpringBeanFactory.getBean("cdbTrackedKeywordEntityDAO");
    }

    public static void main(String[] args) {

        Export7733DeletedKeywordTagRelOnetime onetime = new Export7733DeletedKeywordTagRelOnetime();
        onetime.process();

    }

    private void process() {
        File file = new File("/home/<USER>/source/ewain/dev-clarity-backend-scripts/file/7733-keyword-tag-1027329662.txt");
        List<CdbTrackedKeywordEntity> cdbListFor7733 = cdbTrackedKeywordEntityDAO.getCdbListFor7733();
        for (CdbTrackedKeywordEntity cdbTrackedKeywordEntity : cdbListFor7733) {
            String str = cdbTrackedKeywordEntity.getRawKeywordName() + "\t" + cdbTrackedKeywordEntity.getTagName() + "\n";
            try {
                FileUtils.write(file, str, "UTF-8",true);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
