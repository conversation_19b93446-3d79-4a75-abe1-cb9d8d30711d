package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;

import org.apache.log4j.Logger;
import seoclarity.backend.upload.siteclarity.vendor.Constants;

import java.util.HashMap;
import java.util.Map;

public class Hreflang<PERSON>ieldParser extends AbstractFieldParser {

    private static Logger logger = Logger.getLogger(HreflangFieldParser.class.getName());

    @Override
    public Map<String, String> getFieldsToCopy() {
        Map<String,String> fieldMapping = new HashMap<>();
        fieldMapping.put(Constants.HREF,Constants.HREFLANG_ARRAY_FIELD);
        fieldMapping.put(Constants.LANG,Constants.HREFLANG_LANG_FIELD);
        fieldMapping.put(Constants.HREFLANG_TYPE,Constants.HREFLANG_TYPE_FIELD);
        return fieldMapping;
    }
}
