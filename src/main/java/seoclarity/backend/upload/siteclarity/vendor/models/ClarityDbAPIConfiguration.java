package seoclarity.backend.upload.siteclarity.vendor.models;

public class ClarityDbAPIConfiguration {

    private String clarityUserName;
    private String clarityPassword;
    private String clarityDbEndpoint;
    private String clarityDatabase;
    private String jdbc;

    public String getJdbc() {
        return jdbc;
    }

    public void setJdbc(String jdbc) {
        this.jdbc = jdbc;
    }

    public String getClarityUserName() {
        return clarityUserName;
    }

    public void setClarityUserName(String clarityUserName) {
        this.clarityUserName = clarityUserName;
    }

    public String getClarityDbEndpoint() {
        return clarityDbEndpoint;
    }

    public void setClarityDbEndpoint(String clarityDbEndpoint) {
        this.clarityDbEndpoint = clarityDbEndpoint;
    }

    public String getClarityPassword() {
        return clarityPassword;
    }

    public void setClarityPassword(String clarityPassword) {
        this.clarityPassword = clarityPassword;
    }

    public String getClarityDatabase() {
        return clarityDatabase;
    }

    public void setClarityDatabase(String clarityDatabase) {
        this.clarityDatabase = clarityDatabase;
    }
}
