package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;


import seoclarity.backend.upload.siteclarity.vendor.Constants;

import java.util.HashMap;
import java.util.Map;

public class PaginationLinkFieldParser extends AbstractFieldParser {


    @Override
    public Map<String, String> getFieldsToCopy() {
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put(Constants.PAGINATION_LINK_DIRECTION_ES_FIELD, Constants.PAGINATION_LINK_DIRECTION_FIELD);
        return fieldMapping;
    }


}
