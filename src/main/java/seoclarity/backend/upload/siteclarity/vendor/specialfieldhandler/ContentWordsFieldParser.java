package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;

import org.json.JSONArray;
import org.json.JSONObject;
import seoclarity.backend.upload.siteclarity.vendor.Constants;
import seoclarity.backend.upload.siteclarity.vendor.Helper;
import seoclarity.backend.upload.siteclarity.vendor.models.DbMigrationPropertiesModel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ContentWordsFieldParser implements FieldParser {

    @Override
    public Map<String, List<String>> extractDataToLists(Object fieldData) throws Exception {
        DbMigrationPropertiesModel dbMigrationProperties = DbMigrationPropertiesStore.getInstance().getModel();
        Map<String, List<String>> extractedData = new HashMap<>();
        JSONObject jsonObject = new JSONObject(fieldData.toString());
        List<String> fieldNames = dbMigrationProperties.getContentWordsFirstLevelProperties();
        List<String> keynames = dbMigrationProperties.getContentWordsSecondLevelProperties();
        for (String fieldname : fieldNames) {
            JSONArray jsonArray = jsonObject.getJSONArray(fieldname);
            for (int i = Constants.DEFAULT_START_INDEX; i < jsonArray.length(); i++) {
                JSONObject individualElement = jsonArray.getJSONObject(i);
                for (String keyname : keynames) {
                    String stringifiedJSONField = String.format(Constants.CONTENT_WORDS_NESTED_FIELD_TEMPLATE, fieldname, keyname);
                    if (!extractedData.containsKey(stringifiedJSONField)){
                        extractedData.put(stringifiedJSONField, new ArrayList<>());
                    }
                    Helper.addJSONValueToList(individualElement, keyname, extractedData.get(stringifiedJSONField));
                }
            }
        }
        return extractedData;
    }
}
