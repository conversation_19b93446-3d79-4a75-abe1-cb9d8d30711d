package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;

import org.json.JSONArray;
import org.json.JSONObject;
import seoclarity.backend.upload.siteclarity.vendor.Constants;
import seoclarity.backend.upload.siteclarity.vendor.Helper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AbstractFieldParser implements  FieldParser{
    @Override
    public Map<String, List<String>> extractDataToLists(Object fieldData) throws Exception {
        Map<String, List<String>> extractedData = new HashMap<>();
        JSONArray jsonArray = new JSONArray(fieldData.toString());
        Map<String,String> fieldMapping = getFieldsToCopy();
        for (String targetFieldName: fieldMapping.values()) {
            extractedData.put(targetFieldName, new ArrayList<>());
        }
        for (int i = Constants.DEFAULT_START_INDEX; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            for(Map.Entry<String, String> entry: fieldMapping.entrySet()) {
                Helper.addJSONValueToList(jsonObject, entry.getKey(), extractedData.get(entry.getValue()));
            }
        }
        return extractedData;
    }

    public abstract Map<String,String> getFieldsToCopy();

}
