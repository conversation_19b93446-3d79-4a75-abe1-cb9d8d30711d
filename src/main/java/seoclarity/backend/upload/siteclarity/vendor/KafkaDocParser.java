package seoclarity.backend.upload.siteclarity.vendor;

import org.apache.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class KafkaDocParser {

    private static Logger logger = Logger.getLogger(KafkaDocParser.class.getName());

    public static Map<String, Object> getSimpleValuesFromKafkaDoc(List<String> fields, JSONObject dataJSON, JSONObject mapping) {
        Map<String, Object> sqlObject = new HashMap<>();

        for (String field : fields) {
            if (!mapping.has(field)) {
                continue;
            }
            Object value = getValueFromKafkaDoc(dataJSON, mapping, field);
            sqlObject.put(field, value);
        }
        return sqlObject;
    }

    public static Object getValueFromKafkaDoc(JSONObject dataJSON, JSONObject mapping, String field) {
        Object value;
        String esFieldName = field;

        JSONObject typeObject = mapping.getJSONObject(field);
        if (typeObject.has(Constants.FIELD_MAPPING_COPY_FROM_FIELD)) {
            esFieldName = typeObject.getString(Constants.FIELD_MAPPING_COPY_FROM_FIELD);
        }
        if (!dataJSON.has(esFieldName)) {
            if (typeObject.has(Constants.FIELD_MAPPING_CREATE_FROM_FIELD)) {
                String sourceField = typeObject.getString(Constants.FIELD_MAPPING_CREATE_FROM_FIELD);
                value = createValueFromESField(sourceField, dataJSON, typeObject);
            } else {
                value = getDefaultValue(esFieldName, typeObject, dataJSON);
            }
        } else {
            value = dataJSON.get(esFieldName);
        }
        if (value == null) {
            logger.info("Value is null |  field: " + field);
            value = getDefaultValue(esFieldName, typeObject, dataJSON);
        } else if (value == JSONObject.NULL || value.getClass().getName().startsWith(Constants.JSON_ORG_PREFIX)) {
            value = Constants.DEFAULT_STRING_VALUE;
        }
        return value;
    }

    private static Object createValueFromESField(String sourceField, JSONObject dataJSON, JSONObject typeObject) {
        String typeObjectString = typeObject.getString(Constants.FIELD_MAPPING_TYPE_FIELD);
        if (typeObjectString.equals(Constants.FIELD_MAPPING_DATE_TYPE)) {
            if (dataJSON.has(sourceField)) {
                Object value = dataJSON.get(sourceField);
                if (value != null) {
                    if (typeObject.has("sourceFormat")) {
                        try {
                            return Helper.convertTimeStringFromUTCToCST(value.toString(), typeObject.get("sourceFormat").toString(), "yyyy-MM-dd");
                        } catch (ParseException e) {
                            logger.error("Failed to parse DateField | ES Field : " + sourceField + " | value : " + value);
                        }
                    } else {
                        return Helper.getCrawlRequestDateFromTimestamp(Long.parseLong(value.toString()));
                    }
                }
            }
            return new Date(0); // for date, clickhouse will throw parseException for emptyStrings and null
        }
        return null;
    }

    private static Object getDefaultValue(String esFieldName, JSONObject typeObject, JSONObject dataJSON) {
        String type = typeObject.getString(Constants.FIELD_MAPPING_TYPE_FIELD);
        switch (type) {
            case Constants.FIELD_MAPPING_STRING_TYPE:
                return Constants.DEFAULT_STRING_VALUE;
            case Constants.ARRAY_STRING:
                return new String[Constants.EMPTY_COL_SIZE];
            case "Array(UInt64)":
            case "Array(Int32)":
            case "Array(Int8)":
            case "Array(UInt32)":
                return new Integer[Constants.EMPTY_COL_SIZE];
            case "Int8":
            case "Int32":
            case "UInt32":
            case "UInt64":
                return getIntegerValueOrDefault(esFieldName, dataJSON);
            case "Float64":
                return getFloatValueOrDefault(esFieldName, dataJSON);
        }
        return null;
    }

    private static Object getIntegerValueOrDefault(String esFieldName, JSONObject dataJSON) {
        logger.debug("Setting default int value for ES field : " + esFieldName);
        try {
            return dataJSON.getInt(esFieldName);
        } catch (JSONException e) {
            return Constants.DEFAULT_INT_VALUE;
        }
    }

    private static Float getFloatValueOrDefault(String esFieldName, JSONObject dataJSON) {
        try {
            return Float.parseFloat(dataJSON.get(esFieldName).toString());
        } catch (JSONException e) {
            return (float) 0;
        }
    }
}
