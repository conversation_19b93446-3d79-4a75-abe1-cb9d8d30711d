package seoclarity.backend.upload.siteclarity.vendor;

import org.json.JSONObject;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class Helper {

    public static void addJSONValueToList(JSONObject jsonObject, String key, List<String> list) {
        if (jsonObject.has(key)) {
            list.add(jsonObject.get(key).toString());
        }
    }

    public static String getCrawlRequestDateFromTimestamp(long timestamp) {
        Date date = new Date(timestamp * 1000L);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        format.setTimeZone(TimeZone.getTimeZone("CST"));
        return format.format(date);
    }

    public static String convertTimeStringFromUTCToCST(String dateString, String sourceFormat, String toFormat) throws ParseException {
        SimpleDateFormat from = new SimpleDateFormat(sourceFormat);
        from.setTimeZone(TimeZone.getTimeZone("GMT"));
        Date dt = from.parse(dateString);
        SimpleDateFormat to = new SimpleDateFormat(toFormat);
        to.setTimeZone(TimeZone.getTimeZone("GMT-6"));
        return to.format(dt);
    }

    public static boolean isEndOfCrawlDoc(JSONObject kafkaDocument) {
        String eocIndexName = "seo-crawl-stats";
        return kafkaDocument.has("indexname") && kafkaDocument.getString("indexname").equals(eocIndexName);
    }

    public static Map<String, Integer> getAdditionFieldLength(Map<String, String[]> additionalFields) {
        Map<String, Integer> additionalFieldsLength = new HashMap<>();
        additionalFieldsLength.put(Constants.POST_PROCESSING_ISSUE_COUNT,
                additionalFields.containsKey(Constants.POST_PROCESSING_ISSUES_ARRAY) ? additionalFields.get(Constants.POST_PROCESSING_ISSUES_ARRAY).length : Constants.DEFAULT_INT_VALUE);
        return additionalFieldsLength;
    }
}
