package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler.subTableHandler;

import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import seoclarity.backend.upload.siteclarity.vendor.Constants;
import seoclarity.backend.upload.siteclarity.vendor.Helper;
import seoclarity.backend.upload.siteclarity.vendor.KafkaDocParser;
import seoclarity.backend.utils.cityhash.CityHashUtil;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StructuredSchemaTRG implements SubTableRecordGenerator {

    Logger logger = Logger.getLogger(this.getClass().getName());

    @Override
    public List<Map<String, Object>> getSQLRecords(List<String> fields, JSONObject kafkaDocument, JSONObject fieldMapping) {
        List<Map<String, Object>> sqlRecords = new ArrayList<>();
        JSONArray jsonDataArray = null;

        JSONObject structuredDataField = kafkaDocument.getJSONObject(Constants.STRUCTURED_DATA);
        logger.debug("Structured data " + structuredDataField);
        JSONObject jsonObjectData = structuredDataField.getJSONObject(Constants.DATA);
        if (jsonObjectData == null || jsonObjectData.length() == 0) {
//            logger.warn("Structured data is empty for document" + kafkaDocument.optString(Constants.URL));
            return sqlRecords;
        }

        if (jsonObjectData.has(Constants.FULL_EXTRACT) && jsonObjectData.optJSONArray(Constants.FULL_EXTRACT) != null && jsonObjectData.optJSONArray(Constants.FULL_EXTRACT).length() > 0) {
            logger.debug("Found full_extract");
            jsonDataArray = jsonObjectData.getJSONArray(Constants.FULL_EXTRACT);
        } else if (jsonObjectData.has(Constants.VALIDATE_STRUCTURED_DATA) && jsonObjectData.optJSONArray(Constants.VALIDATE_STRUCTURED_DATA) != null && jsonObjectData.optJSONArray(Constants.VALIDATE_STRUCTURED_DATA).length() > 0) {
            logger.debug("Found validate_structured_data");
            jsonDataArray = jsonObjectData.getJSONArray(Constants.VALIDATE_STRUCTURED_DATA);
        } else if (jsonObjectData.has(Constants.CHECK_STRUCTURED_DATA) && jsonObjectData.optJSONArray(Constants.CHECK_STRUCTURED_DATA) != null && jsonObjectData.optJSONArray(Constants.CHECK_STRUCTURED_DATA).length() > 0) {
            logger.debug("Found check_structured_data");
            jsonDataArray = jsonObjectData.getJSONArray(Constants.CHECK_STRUCTURED_DATA);
        }

        if (jsonDataArray != null && jsonDataArray != null && jsonDataArray.length() > 0) {
            String url = kafkaDocument.getString(Constants.URL);
            BigInteger urlMurmurHash = new BigInteger(MurmurHashUtils.getMurmurHash3_64(url));
            BigInteger urlHash = CityHashUtil.getUrlHashForBigIntegerLowercase(url);
            for (int i = 0; i < jsonDataArray.length(); i++) {
                if (kafkaDocument.has(Constants.CRAWL_REQUEST_LOG_ID_I) || kafkaDocument.has(Constants.URL)) {
                    Map<String, Object> structuredData = KafkaDocParser.getSimpleValuesFromKafkaDoc(fields, kafkaDocument, fieldMapping);
                    structuredData.put(Constants.URL_MURMUR_HASH, urlMurmurHash);
                    structuredData.put(Constants.URL_HASH, urlHash);
                    JSONObject jsonData = jsonDataArray.getJSONObject(i);
                    logger.debug("Processing StructuredData element: " + jsonData.toMap());
                    sqlRecords.add(getStructuredData(jsonData, structuredData));
                }
            }
        }
        return sqlRecords;
    }

    @Override
    public List<String> getAdditionalFields() {
        return null;
    }

    @Override
    public Boolean isDocumentParsable(JSONObject kafkaDocument) {
        return kafkaDocument.getString(Constants.ES_INDEX_TYPE).equals(Constants.DOC) && !Helper.isEndOfCrawlDoc(kafkaDocument)
                && kafkaDocument.optJSONObject(Constants.STRUCTURED_DATA) != null;
    }

    private Map<String, Object> getStructuredData(JSONObject jsonData, Map<String, Object> structuredData) {

        String encoding = jsonData.optString(Constants.ENCODING, null);
        String dataType = jsonData.optString(Constants.DATA_TYPE, null);
        structuredData.put(Constants.ENCODING, encoding);
        structuredData.put(Constants.SCHEMA_TYPE, dataType);

        JSONArray jsonDataArray = jsonData.optJSONArray(Constants.DATA);
        if (jsonDataArray != null && jsonDataArray.length() > 0) {
            structuredData.put(Constants.MARKUP, JSONObject.valueToString(jsonDataArray));
        } else {
            structuredData.put(Constants.MARKUP, "");
        }

        JSONArray jsonErrorArray = jsonData.optJSONArray(Constants.ERRORS);
        if (jsonErrorArray != null && jsonErrorArray.length() > 0) {
            structuredData.put(Constants.VALIDATION_ERRORS, JSONObject.valueToString(jsonErrorArray));
        } else {
            structuredData.put(Constants.VALIDATION_ERRORS, "");
        }

        Map<String, List<String>> warningDetails = getSeverityTypeDetails(jsonErrorArray, Constants.WARNING);
        Map<String, List<String>> errorDetails = getSeverityTypeDetails(jsonErrorArray, Constants.ERROR);

        structuredData.put(Constants.ERROR_COUNT, getSeverityTypeCount(jsonErrorArray, Constants.ERROR));
        structuredData.put(Constants.WARNING_COUNT, getSeverityTypeCount(jsonErrorArray, Constants.WARNING));

        structuredData.put(Constants.WARNING_MESSAGE, warningDetails.get(Constants.MESSAGE).toArray(new String[Constants.EMPTY_COL_SIZE]));
        structuredData.put(Constants.WARNING_PATH, warningDetails.get(Constants.PATH).toArray(new String[Constants.EMPTY_COL_SIZE]));
        structuredData.put(Constants.WARNING_TYPE, warningDetails.get(Constants.TYPE).toArray(new String[Constants.EMPTY_COL_SIZE]));

        structuredData.put(Constants.ERROR_MESSAGE, errorDetails.get(Constants.MESSAGE).toArray(new String[Constants.EMPTY_COL_SIZE]));
        structuredData.put(Constants.ERROR_PATH, errorDetails.get(Constants.PATH).toArray(new String[Constants.EMPTY_COL_SIZE]));
        structuredData.put(Constants.ERROR_TYPE, errorDetails.get(Constants.TYPE).toArray(new String[Constants.EMPTY_COL_SIZE]));


        return structuredData;
    }

    private int getSeverityTypeCount(JSONArray jsonErrorArray, String severityType) {
        int severityTypeCount = 0;
        if (jsonErrorArray == null) {
            return severityTypeCount;
        }
        for (int i = 0; i < jsonErrorArray.length(); i++) {
            JSONObject jsonError = jsonErrorArray.getJSONObject(i);
            String severity = jsonError.getString(Constants.SEVERITY);
            if (severity.equals(severityType)) {
                severityTypeCount++;
            }
        }
        return severityTypeCount;
    }

    private Map<String, List<String>> getSeverityTypeDetails(JSONArray jsonErrorArray, String severityType) {
        Map<String, List<String>> severityTypeDetails = new HashMap<>();
        List<String> messageList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();
        List<String> pathList = new ArrayList<>();
        if (jsonErrorArray != null) {

            for (int i = 0; i < jsonErrorArray.length(); i++) {
                JSONObject jsonError = jsonErrorArray.getJSONObject(i);
                JSONArray pathArray = jsonError.getJSONArray(Constants.PATH);
                if (jsonError.getString(Constants.SEVERITY).equals(severityType)) {
                    messageList.add(jsonError.getString(Constants.MESSAGE));
                    typeList.add(jsonError.getString(Constants.TYPE));
                    pathList.add(pathArray.join(Constants.FORWARD_SLASH_STRING));
                }
            }
        }
        severityTypeDetails.put(Constants.MESSAGE, messageList);
        severityTypeDetails.put(Constants.TYPE, typeList);
        severityTypeDetails.put(Constants.PATH, pathList);
        return severityTypeDetails;
    }
}
