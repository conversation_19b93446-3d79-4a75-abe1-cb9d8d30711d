package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;


import org.apache.log4j.Logger;
import seoclarity.backend.upload.siteclarity.vendor.Helper;
import seoclarity.backend.upload.siteclarity.vendor.models.DbMigrationPropertiesModel;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class HreflangSpecialFieldHandler extends DocSpecialFieldHandler {

    Logger logger = Logger.getLogger(HreflangSpecialFieldHandler.class.getName());

    public HreflangSpecialFieldHandler() {
        super();
    }

    @Override
    public List<String> getAdditionalFields() throws IOException {
        DbMigrationPropertiesModel dbMigrationProperties = DbMigrationPropertiesStore.getInstance().getModel();
        return dbMigrationProperties.getHreflangAdditionalFields();
    }

    @Override
    public Map<String, Integer> getAdditionalFieldsLength(Map<String, String[]> additionalFields) {
        return Helper.getAdditionFieldLength(additionalFields);
    }
}
