package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;

import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import seoclarity.backend.upload.siteclarity.vendor.Constants;
import seoclarity.backend.upload.siteclarity.vendor.Utilities;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StructuredSchemaFieldParser implements FieldParser{
    static Logger logger=Logger.getLogger(StructuredSchemaFieldParser.class.getName());


    @Override
    public Map<String, List<String>> extractDataToLists(Object fieldData) {
        try {
            return this.extractDataToListsWithException(fieldData);
        }
        catch(Exception  e){
            logger.warn("Exception in structured schema parsing " +  fieldData.toString() + "\n"
                    + Utilities.convertTracebackString(e));
        }
        return new HashMap<String, List<String>>();
    }

    public Map<String, List<String>> extractDataToListsWithException(Object fieldData) {
        String formatJsonArrayPath = "";
        String errorList = "errors";
        List<String> schemaTypes = new ArrayList<>();
        List<String> schemaEncodings = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();
        List<String> errorPaths = new ArrayList<>();
        List<String> errorSchemaTypes = new ArrayList<>();
        List<String> errorTypes = new ArrayList<>();
        List<String> errorEncodings = new ArrayList<>();
        List<String> errorInfo = new ArrayList<>();
        List<String> warningMessages = new ArrayList<>();
        List<String> warningPaths = new ArrayList<>();
        List<String> warningSchemaTypes = new ArrayList<>();
        List<String> warningTypes = new ArrayList<>();
        List<String> warningEncodings = new ArrayList<>();
        List<String> warningInfo = new ArrayList<>();
        Map<String, List<String>> extractedData = new HashMap<>();
        String type;
        String badFieldPath;
        JSONObject jsonObject = new JSONObject(fieldData.toString());
        if(!(jsonObject.isNull("data"))) {
            JSONObject jsonObjectData = jsonObject.getJSONObject("data");
            if(!(jsonObjectData.isNull("validate_structured_data"))) {
                JSONArray jsonArrayError;
                JSONArray jsonArrayPath;
                JSONArray jsonArray = jsonObjectData.getJSONArray("validate_structured_data");
                for (int i = Constants.DEFAULT_START_INDEX; i < jsonArray.length(); i++) {
                    JSONObject jsonObjectStructuredData = jsonArray.getJSONObject(i);
                    if (!(jsonObjectStructuredData.isNull(errorList))
                    && !(jsonObjectStructuredData.isNull("data_type"))
                    )   {
                        jsonArrayError = jsonObjectStructuredData.getJSONArray(errorList);
                        for (int j = Constants.DEFAULT_START_INDEX; j < jsonArrayError.length(); j++) {
                            formatJsonArrayPath = "";
                            if(jsonArrayError.getJSONObject(j).get("severity").equals("error")) {
                                errorSchemaTypes.add(jsonObjectStructuredData.getString("data_type"));
                                errorEncodings.add(jsonObjectStructuredData.getString("encoding"));
                                errorMessages.add((jsonObjectStructuredData.getString("data_type")) + ";" + (jsonArrayError.getJSONObject(j).getString("message")));
                                jsonArrayPath = jsonArrayError.getJSONObject(j).getJSONArray("path");
                                for (int k = Constants.DEFAULT_START_INDEX; k < jsonArrayPath.length(); k++) {
                                    formatJsonArrayPath = formatJsonArrayPath + jsonArrayPath.getString(k) + "/";
                                }
                                errorPaths.add(formatJsonArrayPath);
                                type = jsonArrayError.getJSONObject(j).getString(Constants.STRUCTURED_SCHEMA_TYPE);
                                errorTypes.add(type);
                                if(type.toLowerCase().equals(Constants.STRUCTURED_SCHEMA_TYPE_BAD_FIELD)){
                                    errorInfo.add(addInfo(formatJsonArrayPath,jsonArrayError.getJSONObject(j).getString(Constants.STRUCTURED_SCHEMA_FIELD)));
                                }
                                else {
                                    errorInfo.add(jsonArrayError.getJSONObject(j).getString(Constants.STRUCTURED_SCHEMA_MESSAGE));
                                }

                            }
                            else if (jsonArrayError.getJSONObject(j).get("severity").equals("warning"))
                            {
                                warningSchemaTypes.add(jsonObjectStructuredData.getString("data_type"));
                                warningEncodings.add(jsonObjectStructuredData.getString("encoding"));
                                warningMessages.add((jsonObjectStructuredData.getString("data_type"))+";"+(jsonArrayError.getJSONObject(j).getString("message")));
                                jsonArrayPath = jsonArrayError.getJSONObject(j).getJSONArray("path");
                                for (int k = Constants.DEFAULT_START_INDEX; k < jsonArrayPath.length(); k++) {
                                    formatJsonArrayPath = formatJsonArrayPath + jsonArrayPath.getString(k) + "/";
                                }
                                warningPaths.add(formatJsonArrayPath);
                                type = jsonArrayError.getJSONObject(j).getString(Constants.STRUCTURED_SCHEMA_TYPE);
                                warningTypes.add(type);
                                if(type.toLowerCase().equals(Constants.STRUCTURED_SCHEMA_TYPE_BAD_FIELD)){
                                    warningInfo.add(addInfo(formatJsonArrayPath,jsonArrayError.getJSONObject(j).getString(Constants.STRUCTURED_SCHEMA_FIELD)));
                                }
                                else {
                                    warningInfo.add(jsonArrayError.getJSONObject(j).getString(Constants.STRUCTURED_SCHEMA_MESSAGE));
                                }
                            }
                        }
                    }
                }
            }
            extractedData.put("structure_schema_errors.schema_type", errorSchemaTypes);
            extractedData.put("structure_schema_errors.encoding", errorEncodings);
            extractedData.put("structure_schema_errors.message", errorMessages);
            extractedData.put("structure_schema_errors.path", errorPaths);
            extractedData.put("structure_schema_errors.error_type", errorTypes);
            extractedData.put("structure_schema_errors.info", errorInfo);

            extractedData.put("structure_schema_warnings.schema_type", warningSchemaTypes);
            extractedData.put("structure_schema_warnings.encoding", warningEncodings);
            extractedData.put("structure_schema_warnings.message", warningMessages);
            extractedData.put("structure_schema_warnings.path", warningPaths);
            extractedData.put("structure_schema_warnings.warning_type", warningTypes);
            extractedData.put("structure_schema_warnings.info", warningInfo);

            if (!(jsonObjectData.isNull("check_structured_data"))) {
                JSONArray jsonArraySchema = jsonObjectData.getJSONArray("check_structured_data");
                for (int i = Constants.DEFAULT_START_INDEX; i < jsonArraySchema.length(); i++) {
                    if(jsonArraySchema.getJSONObject(i).isNull("data_type")){
                        continue;
                    }
                    schemaTypes.add(jsonArraySchema.getJSONObject(i).getString("data_type"));
                    schemaEncodings.add(jsonArraySchema.getJSONObject(i).getString("encoding"));
                }
            }
            extractedData.put("structured_schema.type", schemaTypes);
            extractedData.put("structured_schema.encoding", schemaEncodings);
        }
        return extractedData;
    }

    private String addInfo(String path, String field)
    {
            String  badFieldPath = path.replace(Constants.PATH_DELIMITER, Constants.PATH_SEPERATOR);
            if(!(path.contains(field))) {
                badFieldPath = badFieldPath + field;
            }
            else {
                badFieldPath =badFieldPath.substring(Constants.DEFAULT_START_INDEX,path.length()-Constants.ONE);
            }
            return String.format(Constants.STRUCTURED_SCHEMA_BADFIELD_INFO_TEMPLATE,badFieldPath);
    }
}