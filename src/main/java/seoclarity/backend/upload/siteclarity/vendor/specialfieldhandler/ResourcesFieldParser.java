package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;

import org.apache.log4j.Logger;
import org.json.JSONArray;
import seoclarity.backend.upload.siteclarity.vendor.Constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResourcesFieldParser implements FieldParser {

    static Logger logger = Logger.getLogger(ResourcesFieldParser.class.getName());

    @Override
    public Map<String, List<String>> extractDataToLists(Object fieldData) {
        Map<String, List<String>> extractedData = new HashMap<>();
        List<String> resourceUrls = new ArrayList<>();
        List<String> resourceStatusCodes = new ArrayList<>();
        List<String> resourceBlockedStatus = new ArrayList<>();
        List<String> resourceRequestTypes = new ArrayList<>();
        List<String> resourceFromMemoryCache = new ArrayList<>();
        List<String> resourceTypes = new ArrayList<>();
        JSONArray resources = (JSONArray) fieldData;
        if (resources != null && resources.length() > 0) {
            for (int i = Constants.DEFAULT_START_INDEX; i < resources.length(); i++) {
                resourceUrls.add(resources.getJSONObject(i).getString(Constants.RESOURCES_URL));
                resourceStatusCodes.add(resources.getJSONObject(i).get(Constants.RESOURCES_STATUS_CODE).toString().equals(Constants.NULL_STRING) ?
                        Constants.STATUS_CODE_NULL:
                        resources.getJSONObject(i).get(Constants.RESOURCES_STATUS_CODE).toString() );
                resourceBlockedStatus.add(resources.getJSONObject(i).get(Constants.RESOURCES_BLOCKED_STATUS).toString().toLowerCase().equals(Constants.TRUE_LOWERCASE) ?
                        Constants.INT_TRUE :
                        Constants.INT_FALSE);
                if(resources.getJSONObject(i).has(Constants.RESOURCES_REQUEST_TYPE)) {
                    resourceRequestTypes.add(resources.getJSONObject(i).getString(Constants.RESOURCES_REQUEST_TYPE));
                }
                else {
                    resourceRequestTypes.add(Constants.DEFAULT_STRING_VALUE);
                }
                if(resources.getJSONObject(i).has(Constants.RESOURCES_FROM_MEMORY_CACHE)) {
                    resourceFromMemoryCache.add(resources.getJSONObject(i).get(Constants.RESOURCES_FROM_MEMORY_CACHE).toString().toLowerCase().equals(Constants.TRUE_LOWERCASE) ?
                            Constants.INT_TRUE :
                            Constants.INT_FALSE);
                }
                else {
                    resourceFromMemoryCache.add(Constants.INT_FALSE);
                }
                if(resources.getJSONObject(i).has(Constants.RESOURCES_TYPE)) {
                    resourceTypes.add(resources.getJSONObject(i).getString(Constants.RESOURCES_TYPE));
                }
                else {
                    resourceTypes.add(Constants.DEFAULT_STRING_VALUE);
                }
            }
        }
        extractedData.put(String.format(Constants.RESOURCES_NESTED_FIELD_TEMPLATE,Constants.RESOURCES_URL),resourceUrls);
        extractedData.put(String.format(Constants.RESOURCES_NESTED_FIELD_TEMPLATE,Constants.RESOURCES_STATUS_CODE),resourceStatusCodes);
        extractedData.put(String.format(Constants.RESOURCES_NESTED_FIELD_TEMPLATE,Constants.RESOURCES_BLOCKED_FIELD),resourceBlockedStatus);
        extractedData.put(String.format(Constants.RESOURCES_NESTED_FIELD_TEMPLATE,Constants.RESOURCES_REQUEST_TYPE),resourceRequestTypes);
        extractedData.put(String.format(Constants.RESOURCES_NESTED_FIELD_TEMPLATE,Constants.RESOURCES_FROM_MEMORY_CACHE_FIELD),resourceFromMemoryCache);
        extractedData.put(String.format(Constants.RESOURCES_NESTED_FIELD_TEMPLATE,Constants.RESOURCES_TYPES_FIELD),resourceTypes);
        return extractedData;
    }
}
