package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;


import seoclarity.backend.upload.siteclarity.vendor.Constants;

import java.util.HashMap;
import java.util.Map;

public class OgMarkupFieldParser extends AbstractFieldParser {


    @Override
    public Map<String, String> getFieldsToCopy() {
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put(Constants.OG_MARKUP_CONTENT_ES_FIELD, Constants.OG_MARKUP_CONTENT_FIELD);
        fieldMapping.put(Constants.OG_MARKUP_PROPERTY_ES_FIELD, Constants.OG_MARKUP_PROPERTY_FIELD);
        return fieldMapping;
    }
}
