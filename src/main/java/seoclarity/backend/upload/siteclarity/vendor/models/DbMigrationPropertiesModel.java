package seoclarity.backend.upload.siteclarity.vendor.models;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "docAdditionalFields",
        "canonicalAdditionalFields",
        "hreflangAdditionalFields",
        "endOfCrawlAdditionalFields",
        "fieldMapping",
        "hreflang_page_analysis_rules",
        "canonical_page_analysis_rules",
        "docTable",
        "canonicalTable",
        "hreflangTable",
        "endOfCrawlDocTable",
        "structuredDataSubTable",
        "customDataNestedProperties",
        "contentWordsFirstLevelProperties",
        "contentWordsSecondLevelProperties",
        "docFieldsToBeParsed",
        "stemmedFields",
        "endOfCrawlStoreAsStringFields",
        "structuredSchemaAdditionalFields"
})
public class DbMigrationPropertiesModel {

    @JsonProperty("docAdditionalFields")
    private List<String> docAdditionalFields;
    @JsonProperty("endOfCrawlAdditionalFields")
    private List<String> endOfCrawlAdditionalFields;
    @JsonProperty("canonicalAdditionalFields")
    private List<String> canonicalAdditionalFields;
    @JsonProperty("hreflangAdditionalFields")
    private List<String> hreflangAdditionalFields;
    @JsonProperty("customDataNestedProperties")
    private List<String> customDataNestedProperties;
    @JsonProperty("contentWordsFirstLevelProperties")
    private List<String> contentWordsFirstLevelProperties;
    @JsonProperty("contentWordsSecondLevelProperties")
    private List<String> contentWordsSecondLevelProperties;
    @JsonProperty("docFieldsToBeParsed")
    private List<String> docFieldsToBeParsed;
    @JsonProperty("stemmedFields")
    private List<String> stemmedFields;
    @JsonProperty("murmur3HashFields")
    private List<String> murmur3HashFields;
    @JsonProperty("fieldMapping")
    private Map<String, String> fieldMapping;
    @JsonProperty("endOfCrawlStoreAsStringFields")
    private List<String> endOfCrawlStoreAsStringFields;
    @JsonProperty("structuredSchemaAdditionalFields")
    private List<String> structuredSchemaAdditionalFields;
    @JsonProperty
    private List<String> hreflang_page_analysis_rules;
    @JsonProperty
    private List<String> canonical_page_analysis_rules;
    @JsonProperty
    private String docTable;
    @JsonProperty
    private String canonicalTable;
    @JsonProperty
    private String hreflangTable;
    @JsonProperty
    private String endOfCrawlDocTable;
    @JsonProperty
    private String structuredDataSubTable;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("murmur3HashFields")
    public List<String> getMurmur3HashFields() {
        return murmur3HashFields;
    }

    @JsonProperty("murmur3HashFields")
    public void setMurmur3HashFields(List<String> murmur3HashFields) {
        this.murmur3HashFields = murmur3HashFields;
    }

    @JsonProperty("stemmedFields")
    public List<String> getStemmedFields() {
        return stemmedFields;
    }

    @JsonProperty("stemmedFields")
    public void setStemmedFields(List<String> stemmedFields) {
        this.stemmedFields = stemmedFields;
    }

    public List<String> getDocFieldsToBeParsed() {
        return docFieldsToBeParsed;
    }

    public void setDocFieldsToBeParsed(List<String> docFieldsToBeParsed) {
        this.docFieldsToBeParsed = docFieldsToBeParsed;
    }

    public List<String> getContentWordsFirstLevelProperties() {
        return contentWordsFirstLevelProperties;
    }

    public void setContentWordsFirstLevelProperties(List<String> contentWordsFirstLevelProperties) {
        this.contentWordsFirstLevelProperties = contentWordsFirstLevelProperties;
    }

    public List<String> getContentWordsSecondLevelProperties() {
        return contentWordsSecondLevelProperties;
    }

    public void setContentWordsSecondLevelProperties(List<String> contentWordsSecondLevelProperties) {
        this.contentWordsSecondLevelProperties = contentWordsSecondLevelProperties;
    }

    public List<String> getCustomDataNestedProperties() {
        return customDataNestedProperties;
    }

    public void setCustomDataNestedProperties(List<String> customDataNestedProperties) {
        this.customDataNestedProperties = customDataNestedProperties;
    }

    public List<String> getCanonicalAdditionalFields() {
        return canonicalAdditionalFields;
    }

    public void setCanonicalAdditionalFields(List<String> canonicalAdditionalFields) {
        this.canonicalAdditionalFields = canonicalAdditionalFields;
    }

    public List<String> getHreflangAdditionalFields() {
        return hreflangAdditionalFields;
    }

    public void setHreflangAdditionalFields(List<String> hreflangAdditionalFields) {
        this.hreflangAdditionalFields = hreflangAdditionalFields;
    }

    public String getDocTable() {
        return docTable;
    }

    public void setDocTable(String docTable) {
        this.docTable = docTable;
    }

    public String getCanonicalTable() {
        return canonicalTable;
    }

    public void setCanonicalTable(String canonicalTable) {
        this.canonicalTable = canonicalTable;
    }

    public String getHreflangTable() {
        return hreflangTable;
    }

    public void setHreflangTable(String hreflangTable) {
        this.hreflangTable = hreflangTable;
    }

    public List<String> getHreflang_page_analysis_rules() {
        return hreflang_page_analysis_rules;
    }

    public void setHreflang_page_analysis_rules(List<String> hreflang_page_analysis_rules) {
        this.hreflang_page_analysis_rules = hreflang_page_analysis_rules;
    }

    public List<String> getCanonical_page_analysis_rules() {
        return canonical_page_analysis_rules;
    }

    public void setCanonical_page_analysis_rules(List<String> canonical_page_analysis_rules) {
        this.canonical_page_analysis_rules = canonical_page_analysis_rules;
    }

    public Map<String, String> getFieldMapping() {
        return fieldMapping;
    }

    public void setFieldMapping(Map<String, String> fieldMapping) {
        this.fieldMapping = fieldMapping;
    }

    @JsonProperty("docAdditionalFields")
    public List<String> getDocAdditionalFields() {
        return docAdditionalFields;
    }

    @JsonProperty("docAdditionalFields")
    public void setDocAdditionalFields(List<String> docAdditionalFields) {
        this.docAdditionalFields = docAdditionalFields;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    public String getEndOfCrawlDocTable() {
        return endOfCrawlDocTable;
    }

    public void setEndOfCrawlDocTable(String endOfCrawlDocTable) {
        this.endOfCrawlDocTable = endOfCrawlDocTable;
    }

    public List<String> getEndOfCrawlStoreAsStringFields() {
        return endOfCrawlStoreAsStringFields;
    }

    public void setEndOfCrawlStoreAsStringFields(List<String> endOfCrawlStoreAsStringFields) {
        this.endOfCrawlStoreAsStringFields = endOfCrawlStoreAsStringFields;
    }

    public List<String> getEndOfCrawlAdditionalFields() {
        return endOfCrawlAdditionalFields;
    }

    public void setEndOfCrawlAdditionalFields(List<String> endOfCrawlAdditionalFields) {
        this.endOfCrawlAdditionalFields = endOfCrawlAdditionalFields;
    }

    @JsonAnyGetter
    public List<String> getStructuredSchemaAdditionalFields() {
        return structuredSchemaAdditionalFields;
    }

    @JsonAnySetter
    public void setStructuredSchemaAdditionalFields(List<String> structuredSchemaAdditionalFields) {
        this.structuredSchemaAdditionalFields = structuredSchemaAdditionalFields;
    }

    public String getStructuredDataSubTable() {
        return structuredDataSubTable;
    }

    public void setStructuredDataSubTable(String structuredDataSubTable) {
        this.structuredDataSubTable = structuredDataSubTable;
    }
}
