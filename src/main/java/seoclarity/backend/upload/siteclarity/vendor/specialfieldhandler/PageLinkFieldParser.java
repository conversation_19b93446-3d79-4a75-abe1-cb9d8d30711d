package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;

import seoclarity.backend.upload.siteclarity.vendor.Constants;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PageLinkFieldParser extends AbstractFieldParser {

    @Override
    public Map<String, String> getFieldsToCopy() {
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put(Constants.PAGE_LINK_DEST_URL_ES_FIELD, Constants.PAGE_LINK_DESTINATION_URL_FIELD);
        return fieldMapping;
    }

    @Override
    public Map<String, List<String>> extractDataToLists(Object fieldData) throws Exception {
        Map<String, List<String>> stringListMap = super.extractDataToLists(fieldData);
        List<String> pageLinks = stringListMap.get(Constants.PAGE_LINK_DESTINATION_URL_FIELD);
        if (pageLinks != null && pageLinks.size() > Constants.ONE) {
            //remove duplicates from page_link_destination_urls
            stringListMap.put(Constants.PAGE_LINK_DESTINATION_URL_FIELD, pageLinks.stream().distinct().collect(Collectors.toList()));
        }
        return stringListMap;

    }
}
