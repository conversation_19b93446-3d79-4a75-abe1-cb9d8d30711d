package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;


import seoclarity.backend.upload.siteclarity.vendor.Constants;

import java.util.HashMap;
import java.util.Map;

public enum FieldParserType {

    HREFLANG_LINKS(Constants.HREFLANG_LINKS_ES_FIELD),
    PAGE_LINK(Constants.PAGE_LINKS_ES_FIELD),
    H1(Constants.H1_ES_FIELD),
    H2(Constants.H2_ES_FIELD),
    PAGINATION_LINKS(Constants.PAGINATION_LINK_ES_FIELD),
    OG_MARKUP(Constants.OG_MARKUP_ES_FIELD),
    TWITTER_MARKUP(Constants.TWITTER_MARKUP_ES_FIELD),
    CONTENT_WORDS(Constants.CONTENT_WORDS_ES_FIELD),
    CUSTOM_DATA(Constants.CUSTOM_DATA_ES_FIELD),
    STRUCUTED_DATA(Constants.STRUCUTED_DATA_FIELD),
    RESOURCES_DATA(Constants.RESOURCES_DATA_FIELD);


    final String fieldName;
    private static final Map<String, FieldParserType> BY_FIELDNAME = new HashMap<>();


    FieldParserType(String fieldName) {
        this.fieldName = fieldName;
    }

    static {
        for (FieldParserType fieldParserType : FieldParserType.values()) {
            BY_FIELDNAME.put(fieldParserType.fieldName, fieldParserType);
        }
    }

    public static FieldParserType valueOfField(String fieldName) {
        return BY_FIELDNAME.get(fieldName);
    }
}
