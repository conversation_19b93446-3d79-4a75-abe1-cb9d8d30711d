package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;


import org.apache.log4j.Logger;
import org.json.JSONObject;
import seoclarity.backend.upload.siteclarity.vendor.SpecialFieldHandler;
import seoclarity.backend.upload.siteclarity.vendor.models.DbMigrationPropertiesModel;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EndOfCrawlDocSpecialFieldHandler implements SpecialFieldHandler {

    Logger logger = Logger.getLogger(EndOfCrawlDocSpecialFieldHandler.class.getName());

    DbMigrationPropertiesModel dbMigrationProperties;


    public EndOfCrawlDocSpecialFieldHandler() throws IOException {
        super();
        dbMigrationProperties = DbMigrationPropertiesStore.getInstance().getModel();
    }

    @Override
    public List<String> getAdditionalFields() throws IOException {
        DbMigrationPropertiesModel dbMigrationProperties = DbMigrationPropertiesStore.getInstance().getModel();
        return dbMigrationProperties.getEndOfCrawlAdditionalFields();
    }

    @Override
    public Map<String, String[]> extractAdditionalFields(JSONObject document) throws IOException {
        return null;
    }

    @Override
    public Map<String, Object> convertAdditionalFielsBasedonType(Map<String, String[]> additionalFields, JSONObject mapping) {
        return null;
    }

    @Override
    public Map<String, Integer> getAdditionalFieldsLength(Map<String, String[]> additionalFields) {
        return null;
    }

    @Override
    public Map<String, Object> getCustomFields(JSONObject dataJSON, Map<String, Object> sqlObject, JSONObject mapping) throws Exception {
        Map<String, Object> output = new HashMap<>();
        for (String fieldName : dbMigrationProperties.getEndOfCrawlStoreAsStringFields()) {
            JSONObject value = dataJSON.optJSONObject(fieldName);
            if (value != null) {
                output.put(fieldName, value.toString());
            }
        }
        output.putAll(convertCompletedCoountBreakDown(dataJSON, mapping));
        return output;
    }

    private Map<String, Object> convertCompletedCoountBreakDown(JSONObject dataJSON, JSONObject mapping) {
        Map<String, Object> output = new HashMap<>();
        JSONObject jsonObject = dataJSON.getJSONObject("completed_count_breakdown");
        if (jsonObject != null) {
            output.put("completed_count_breakdown_amphtml", jsonObject.get("amphtml"));
            output.put("completed_count_breakdown_canonical", jsonObject.get("canonical"));
            output.put("completed_count_breakdown_doc", jsonObject.get("doc"));
            output.put("completed_count_breakdown_external", jsonObject.get("external"));
            output.put("completed_count_breakdown_hreflang", jsonObject.get("hreflang"));
            output.put("completed_count_breakdown_pagination", jsonObject.get("pagination"));
            output.put("completed_count_breakdown_relational", jsonObject.get("relational"));
        }

        return output;
    }
}
