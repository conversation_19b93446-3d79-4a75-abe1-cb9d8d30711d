package seoclarity.backend.upload.siteclarity.vendor.specialfieldhandler;

import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import seoclarity.backend.upload.siteclarity.vendor.Constants;
import seoclarity.backend.upload.siteclarity.vendor.Helper;
import seoclarity.backend.upload.siteclarity.vendor.models.DbMigrationPropertiesModel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomDataFieldParser implements FieldParser {

    private static Logger logger = Logger.getLogger(CustomDataFieldParser.class.getName());

    @Override
    public Map<String, List<String>> extractDataToLists(Object fieldData) throws Exception {
        Map<String, List<String>> extractedData = new HashMap<>();
        DbMigrationPropertiesModel dbMigrationProperties = DbMigrationPropertiesStore.getInstance().getModel();
        try{
            if (fieldData!=null){
                JSONArray jsonArray = new JSONArray(fieldData.toString());
                List<String> keynames = dbMigrationProperties.getCustomDataNestedProperties();
                for (int i = Constants.DEFAULT_START_INDEX; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    for (String keyname : keynames) {
                        String targetFieldName = String.format(Constants.CUSTOM_DATA_NESTED_FIELD_TEMPLATE, keyname);
                        if (!extractedData.containsKey(targetFieldName)){
                            extractedData.put(targetFieldName, new ArrayList<>());
                        }
                        Helper.addJSONValueToList(jsonObject, keyname, extractedData.get(targetFieldName));
                    }
                }
            }
        }catch (JSONException e){
            logger.debug("CustomData Field Parser Missing fieldValue: " + fieldData);
        }
        return extractedData;
    }
}
