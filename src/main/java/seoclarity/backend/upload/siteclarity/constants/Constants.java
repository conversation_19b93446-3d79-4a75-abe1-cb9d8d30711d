package seoclarity.backend.upload.siteclarity.constants;

public class Constants {

    //kafka
    public static long KAFKA_POLL_TIMEOUT = 3 * 1000;

//    public static String BASE_PATH = "/home/<USER>/source/ewain/dev-clarity-backend-scripts";

    // need parse file
    public static String TEMP_FILE_FOLDER = "/kafka/";
    public static String TEMP_FILE_PREFIX = "site_clarity_";

    // parsed file
    public static String DONE_FILE_FOLDER = "/done/";
    public static String FAILED_FILE_FOLDER = "/failed/";


    /**
     * test
     */

//    //kafka
//    public static long KAFKA_POLL_TIMEOUT = 3 * 1000;
//
//    public static String PROJECT_ABS_PATH = "D:\\syncBd\\BaiduSyncdisk\\seoClarity\\work记录\\20220609kafka site clarity";
//
//    // need parse file
//    public static String TEMP_FILE_FOLDER = PROJECT_ABS_PATH + "/file/kafka/";
//    public static String TEMP_FILE_PREFIX = "site_clarity_";
//
//    // parsed file
//    public static String DONE_FILE_FOLDER = PROJECT_ABS_PATH + "/file/done/";
//    public static String FAILED_FILE_FOLDER = PROJECT_ABS_PATH + "/file/failed/";




    public static final String TIME_FORMAT = "yyyy/MM/dd HH:mm:ss";
    public static final String RES_CODE_2XX = "200";
    public static final String DATABASE = "database";
    public static final String DB_USER = "db_user";
    public static final String DB_PASS = "db_pass";
    public static final String DB_END_POINT = "db_url";
    public static final String JDBC_DRIVER = "jdbc_driver";
    public static final int CODE_16 = 16;
}
