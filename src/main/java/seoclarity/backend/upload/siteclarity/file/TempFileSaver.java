package seoclarity.backend.upload.siteclarity.file;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.upload.siteclarity.constants.Constants;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

public class TempFileSaver {

    public static String getTempFileName(String basePath) {
        String fileNameSuffix = DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS");
        String fileName = basePath + Constants.TEMP_FILE_FOLDER + Constants.TEMP_FILE_PREFIX + fileNameSuffix;
        return fileName;
    }

    public static String saveFile(String fileName, List<String> content) {
        File file = new File(fileName);
        try {
            FileUtils.writeLines(file, "UTF-8", content, true);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file.getAbsolutePath();
    }

}
