package seoclarity.backend.upload.siteclarity.claritydb;

import seoclarity.backend.dao.clickhouse.siteclarity.ClSiteClarityDao;
import seoclarity.backend.upload.siteclarity.vendor.Constants;
import seoclarity.backend.upload.siteclarity.vendor.DocumentBatch;
import seoclarity.backend.utils.SpringBeanFactory;

import java.util.List;

public class ClarityDBHandler {

    private ClSiteClarityDao clSiteClarityDao;

    public ClarityDBHandler(){
        clSiteClarityDao = SpringBeanFactory.getBean("clSiteClarityDao");
    }

    public String buildMarkers(List<String> fields) {
        int size = fields.size();
        StringBuilder markers = new StringBuilder(Constants.DEFAULT_STRING_VALUE);
        for (int i = Constants.DEFAULT_START_INDEX; i < size; i++) {
            if (i == size - Constants.ONE) {
                markers.append(Constants.MARKER);
            } else {
                markers.append(Constants.MARKER_WITH_DELIMITER);
            }
        }
        return markers.toString();
    }

    public void insertBatch(DocumentBatch documentBatch) {
        clSiteClarityDao.batchInsert(documentBatch);
    }

}
