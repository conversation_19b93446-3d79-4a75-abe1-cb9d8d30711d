package seoclarity.backend.upload;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.alibaba.druid.sql.ast.statement.SQLIfStatement.Else;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.WriteChannel;
import com.google.cloud.storage.BlobId;
import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;
import com.google.gson.Gson;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriteConfig;
import cn.hutool.core.text.csv.CsvWriter;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.mdbkeywordsuggest.RankQcInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.GeoMasterEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityCityEntityDAO;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.KeywordSubRankEntityVO;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.RankQcStateEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.rankcheck.GeoMasterEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityCityEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

/**
 * <AUTHOR>
 * @create 2020-01-30 14:54
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.ExtractFlatDataForRVFromClarityDBV2" -Dexec.args="1_1 2023-04-09 0"
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.ExtractFlatDataForRVFromClarityDBV2" -Dexec.args="6_8"
 **/
public class ExtractFlatDataForRVFromClarityDBV2 {
	
    private static final String LOCAL_OUTPUT_FOLDER = "/home/<USER>/RedVentures_extract/";
    private static final String storeDoneFilePath = "/home/<USER>/RedVentures_extract/backup/";
    private static final String S3_BUCKET_KEY_PREFIX = "top_keywords";
    
    private static String[] headers = new String[] {
            "search_metadata - id","search_metadata - status","search_metadata - date","search_parameters - engine","search_parameters - google_domain","search_parameters - gl","search_parameters - hl","search_parameters - device","search_parameters - q","search_parameters - location_requested","search_information - total_results","search_information - app_flag","search_information - local_map_flag","search_information - ads_flag","search_information - answer_box_flag","search_information - cpc","search_information - search_volume","search_information - true_demand","search_information - commercial_flag","search_information - flights_flag","search_information - google_research_flag","search_information - top_ppc_count","search_information - social_in_knowledge","search_information - pla_flag","search_information - knowledge_graph_flag","answer_box_url","google_recommend","refine_by - title","refine_by - details","google_job - link","google_job - details","local_map - link","local_map - title","local_map - places - rank","local_map - places - title","inline_videos - rank","inline_videos - title","inline_videos - link","inline_images - rank","inline_images - title","inline_images - link","top_stories - rank","top_stories - title","top_stories - link","people_also_ask - title","people_also_ask - link","organic_results - true rank","organic_results - web rank","organic_results - type","organic_results - title","organic_results - snippet","organic_results - link","organic_results - rating_number","organic_results - mobile_friendly"
    };
//    private static String[] headers = new String[] { "search_metadata - id","search_metadata - status","search_metadata - date","search_parameters - engine","search_parameters - google_domain","search_parameters - gl","search_parameters - hl","search_parameters - device","search_parameters - q","search_parameters - location_requested","search_information - total_results","search_information - app_flag","search_information - local_map_flag","search_information - ads_flag","search_information - answer_box_flag","search_information - cpc","search_information - search_volume","search_information - commercial_flag","search_information - flights_flag","search_information - google_research_flag","search_information - top_ppc_count","search_information - social_in_knowledge","search_information - pla_flag","search_information - knowledge_graph_flag","answer_box_url","google_recommend","refine_by - title","refine_by - details","google_job - link","google_job - details","local_map - link","local_map - title","local_map - places - rank","local_map - places - title","inline_videos - rank","inline_videos - title","inline_videos - link","inline_images - rank","inline_images - title","inline_images - link","top_stories - rank","top_stories - title","top_stories - link","people_also_ask - title","people_also_ask - link","organic_results - true rank","organic_results - web rank","organic_results - type","organic_results - title","organic_results - snippet","organic_results - link","organic_results - rating_number","organic_results - mobile_friendly"
//    };
//    private static final String FTP_FOLDER = "/home/<USER>/8879/";
//    private static final int FTP_RETRY_COUNT = 30;

    private static final String DEVICE_DESKTOP = "d";
    private static final String DEVICE_MOBILE = "m";
    private static final String DEVICE_EXPORT_STR_DESKTOP = "desktop";
    private static final String DEVICE_EXPORT_STR_MOBILE = "mobile";
    private static final String BAD_CHAR = "�";
    public static final Integer PROTOCOL_HTTP = 0;
	public static final Integer PROTOCOL_HTTPS = 1;
	
    private static Map<String, String> CITY_MAP_NEED_TO_REPLACE = new HashMap<>();
    private static Map<Integer, String> OLD_CITY_NAME_MAP = new HashMap<>();
    static {
    	CITY_MAP_NEED_TO_REPLACE.put("Houston,Texas", "Houston, TX");
    	CITY_MAP_NEED_TO_REPLACE.put("Dallas,Texas", "Dallas, TX");
    }
	
    private static Integer processEngineId;
    private static Integer processLanguageId;
    private static boolean needFileUpload = true; // TODO
    
    private static int totalCnt = 0;
    private DateTime processDate;
    private String processDateStr;
    private String processDateIntStr;
    private static Set<String> processedFileName = new HashSet<>();
    private static Set<Integer> usedCityIdSet = new HashSet<Integer>();
    
    private EngineCountryLanguageMappingEntityDAO mappingEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ScKeywordRankManager scKeywordRankManager;
    private GeoMasterEntityDAO geoMasterEntityDAO;
	private RankQcInfoEntityDAO rankQcInfoEntityDAO;
//    private ClColdDailyRankingEntityDao clColdDailyRankingEntityDao;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private SeoClarityCityEntityDAO seoClarityCityEntityDAO;
    private CommonDataService commonDataService;
//	private ZeptoMailSenderComponent zeptoMailSenderComponent;
     
    private Gson gson = new Gson();
	private static LogglyVO logglyVO = new LogglyVO();
	
	private String emailTo = "<EMAIL>";
	private String[] ccTo = new String[] {"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
    
    public ExtractFlatDataForRVFromClarityDBV2() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        mappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
//        clColdDailyRankingEntityDao = SpringBeanFactory.getBean("clColdDailyRankingEntityDao");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        geoMasterEntityDAO = SpringBeanFactory.getBean("geoMasterEntityDAO");
		rankQcInfoEntityDAO = SpringBeanFactory.getBean("rankQcInfoEntityDAO");
        seoClarityCityEntityDAO = SpringBeanFactory.getBean("seoClarityCityEntityDAO");
        commonDataService = SpringBeanFactory.getBean("commonDataService");
//		zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }
 
    public static void main(String[] args) throws IOException {
    	createWorkFolderIfNotExist();
    	
    	System.out.println("======ParametersRaw:" + new Gson().toJson(args));
        if(args != null && args.length >= 2) {
        	String engineLanguageStr = args[0];
            if (StringUtils.indexOf(engineLanguageStr, "_") != -1) {
            	processEngineId = NumberUtils.toInt(StringUtils.split(engineLanguageStr, "_")[0]);
				processLanguageId = NumberUtils.toInt(StringUtils.split(engineLanguageStr, "_")[1]);
			} else {
				System.out.println("Wrong parameters");
				return;
			}
            
        	String[] dateArr = args[1].split(",");
            if (args.length >= 3) {
        		needFileUpload = BooleanUtils.toBoolean(args[2]);
			}
            
            System.out.println("======Parameters SE:" + processEngineId + "_" + processLanguageId + " needFileUpload:" + needFileUpload);
            for (String dateStr : dateArr) {
                ExtractFlatDataForRVFromClarityDBV2 ins = new ExtractFlatDataForRVFromClarityDBV2();
				String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
				logglyVO.setoId("RV");
				logglyVO.setName("ExtractFlatDataForRVFromClarityDBV2");
				logglyVO.setDevice("desktop+mobile");
				logglyVO.setpDate(dateStr);
				List<String> groupList = new ArrayList<>();
				groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
				logglyVO.setGroups(groupList);

                try {
                	ins.deleteFileIfExist(dateStr);
                	ins.process(DateUtil.parse(dateStr, "yyyy-MM-dd"));

					logglyVO.setStatus(LogglyVO.STATUS_OK);
					logglyVO.setsTime(stTime);
					logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
					logglyVO.setRows(String.valueOf(totalCnt));
					String body = new Gson().toJson(logglyVO);
					LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                } catch (Exception e) {
                    e.printStackTrace();
//                    EmailSenderComponent.sendEmailReport(new Date(), "ExtractFlatDataForRV Error", "ExtractFlatDataForRV error occurred", e.getMessage());
					ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV Error", "ExtractFlatDataForRV error occurred", e.getMessage());
					logglyVO.setStatus(LogglyVO.STATUS_NG);
					String body = new Gson().toJson(logglyVO);
					LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
				}
            }
        } else if(args != null && args.length == 1) {
            ExtractFlatDataForRVFromClarityDBV2 ins = new ExtractFlatDataForRVFromClarityDBV2();
            String engineLanguageStr = args[0];
            if (StringUtils.indexOf(engineLanguageStr, "_") != -1) {
            	processEngineId = NumberUtils.toInt(StringUtils.split(engineLanguageStr, "_")[0]);
				processLanguageId = NumberUtils.toInt(StringUtils.split(engineLanguageStr, "_")[1]);
			} else {
				System.out.println("Wrong parameters");
				return;
			}
            DateTime dateTime = DateUtil.offsetDay(new Date(), -1);
            
            ins.deleteFileIfExist(dateTime.toString("yyyy-MM-dd"));
            System.out.println("======Parameters SE:" + processEngineId + "_" + processLanguageId);
            ins.process(dateTime);
        } else {
        	System.out.println("No engine language was set, exit!!");
        }
    }
    
    private void deleteFileIfExist(String processDate) {
//    	List<OwnDomainEntity> domainEntityList = ownDomainEntityDAO.getDomainListBasedCompanyName("Red Ventures LLC");
    	List<OwnDomainEntity> domainEntityList = commonDataService.getRVTargetDomainList(); // https://www.wrike.com/open.htm?id=1344553888
    	OwnDomainEntity ownDomainEntity = null;
      	for (OwnDomainEntity entity : domainEntityList) {
      		int engineId = ScKeywordRankManager.getSearchEngineId(entity);
  	        int languageId = ScKeywordRankManager.getSearchLanguageId(entity);
  	        if (engineId != processEngineId || languageId != processLanguageId) {
        		continue;
			}
  	        ownDomainEntity = entity;
  	      	break;
        }

      	if (ownDomainEntity == null) {
			return;
		}
      	
    	String country = ownDomainEntity.getSearchEngineCountry();
        String languageStr = ownDomainEntity.getLanguage();
        try {
        	System.out.println(" Checking file if exist, file:" + LOCAL_OUTPUT_FOLDER + File.separator + "rv_daily_" + processDate + "_" + country + "_" + languageStr + "_extract_tabsplit.csv");
        	File file = new File(LOCAL_OUTPUT_FOLDER + File.separator + "rv_daily_" + processDate + "_" + country + "_" + languageStr + "_extract_tabsplit.csv");
        	if (file != null && file.isFile()) {
        		System.out.println("File exist, deleted! file:" + LOCAL_OUTPUT_FOLDER + File.separator + "rv_daily_" + processDate + "_" + country + "_" + languageStr + "_extract_tabsplit.csv");
        		file.delete();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    private Map<Integer, String> cityMap = new HashMap<>();
    
    private static Integer totalExtractCnt = 0;
    private static Integer totalErrorCnt = 0;
    private static Integer totalUSCnt = 0;
    private static Integer totalBRCnt = 0;
    private static Integer totalUKCnt = 0;
    private static String countryCode = "";
    
    private void initRawCacheTable(List<Integer> domainIdList, Integer engineId, Integer languageId, String rankingDate, boolean isMobile) {
    	/**
    	  insert into seo_daily_ranking.local_rv_keyword_raw(ranking_date, engine_id, language_id, device, own_domain_id, 
    	  keyword_rankcheck_id, location_id, keyword_name) select ranking_date, engine_id, language_id, 'd' as device, 
    	  own_domain_id, keyword_rankcheck_id, location_id, keyword_name from seo_daily_ranking.d_ranking_info_intl 
    	  where own_domain_id in(XXX) AND ranking_date='2022-03-22'  AND engine_id=15 AND language_id=16;
    	 */
    	System.out.println("load raw " + engineId + "-" + languageId + "-" + (isMobile ? "m":"d") + "-" + rankingDate);
    	clDailyRankingEntityDao.insertIntoRvRawTable(domainIdList, engineId, languageId, rankingDate, isMobile);
    }
    
    private void initUniqueCacheTable(String rankingDate) {
    	List<CLRankingDetailEntity> uniqueKeyList = clDailyRankingEntityDao.getUniqueKeyListForRv(rankingDate);
    	/**
    	  insert into seo_daily_ranking.local_rv_keyword_raw(ranking_date, engine_id, language_id, device, own_domain_id, 
    	  keyword_rankcheck_id, location_id, keyword_name) select ranking_date, engine_id, language_id, 'd' as device, 
    	  own_domain_id, keyword_rankcheck_id, location_id, keyword_name from seo_daily_ranking.d_ranking_info_intl 
    	  where own_domain_id in(XXX) AND ranking_date='2022-03-22'  AND engine_id=15 AND language_id=16;
    	 */
    	for(CLRankingDetailEntity cLRankingDetailEntity : uniqueKeyList) {
        	System.out.println("load unique " + cLRankingDetailEntity.getOwnDomainId() + "-" + cLRankingDetailEntity.getEngine() + "-" 
        			+ cLRankingDetailEntity.getLanguage() + "-" + cLRankingDetailEntity.getDevice() + "-" + rankingDate);
        	clDailyRankingEntityDao.insertIntoRvUniqueTable(cLRankingDetailEntity.getOwnDomainId(), 
        			cLRankingDetailEntity.getEngine(), cLRankingDetailEntity.getLanguage(), rankingDate, cLRankingDetailEntity.getDevice());
    	}
    }
    
    private void init(String rankingDate) {
    	List<Integer> usDomainIdList = new ArrayList<>();
    	List<Integer> ukDomainIdList = new ArrayList<>();
    	List<Integer> brDomainIdList = new ArrayList<>();
    	
//    	List<OwnDomainEntity> domainEntityList = ownDomainEntityDAO.getDomainListBasedCompanyName("Red Ventures LLC");
    	List<OwnDomainEntity> domainEntityList = commonDataService.getRVTargetDomainList();
    	for (OwnDomainEntity ownDomainEntity : domainEntityList) {
    		int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
	        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
	        if (engineId == 1 && languageId == 1) {
	        	usDomainIdList.add(ownDomainEntity.getId());
			} else if (engineId == 6 && languageId == 8) {
				ukDomainIdList.add(ownDomainEntity.getId());
			} else if (engineId == 15 && languageId == 16) {
				brDomainIdList.add(ownDomainEntity.getId());
			}
        }
    	
    	System.out.println("Total OIDs:" + domainEntityList.size() + " US:" + usDomainIdList.size() + " UK:" + ukDomainIdList.size() + " BR:" + brDomainIdList.size()) ;
    	System.out.println("======Init raw table!");
    	initRawCacheTable(usDomainIdList, 1, 1, rankingDate, true);
    	initRawCacheTable(usDomainIdList, 1, 1, rankingDate, false);
    	initRawCacheTable(ukDomainIdList, 6, 8, rankingDate, true);
    	initRawCacheTable(ukDomainIdList, 6, 8, rankingDate, false);
    	initRawCacheTable(brDomainIdList, 15, 16, rankingDate, true);
    	initRawCacheTable(brDomainIdList, 15, 16, rankingDate, false);
    	
    	System.out.println("======Init unique table!");
    	initUniqueCacheTable(rankingDate);
    }
    
    public void process(DateTime dateTime) throws IOException {
    	try {
			if (processEngineId == 1 && processLanguageId == 1) {
				countryCode = "US";
			} else if (processEngineId == 6 && processLanguageId == 8) {
				countryCode = "UK";
			} else if (processEngineId == 15 && processLanguageId == 16) {
				countryCode = "BR";
			}
    		processedFileName = new HashSet<>();
        	processDate = dateTime;
            processDateStr = processDate.toString("yyyy-MM-dd");
            processDateIntStr = processDate.toString("yyyyMMdd");
            System.out.println("======ProcessDate:" + processDateIntStr + " SE:" + processEngineId + "_" + processLanguageId + " country:" + countryCode);
            
            //init Cache
            init(processDateStr);
            
            cacheTotalPageCnt(processDateStr, processEngineId, processLanguageId);
            
            List<Integer> ownDomainIdList = new ArrayList<>();
            List<Integer> allOwnDomainIdList = new ArrayList<>();
            List<OwnDomainEntity> ownDomainList = new ArrayList<>();
            List<OwnDomainEntity> domainEntityList = commonDataService.getRVTargetDomainList();
        	for (OwnDomainEntity ownDomainEntity : domainEntityList) {
        		int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
    	        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
    	        if (engineId != processEngineId || languageId != processLanguageId) {
    	        	System.out.println(" SkipOID:" + ownDomainEntity.getId() + " SE:" + engineId + "_" + languageId);
    	        	continue;
				} else {
					System.out.println(" ProceedOID:" + ownDomainEntity.getId() + " SE:" + engineId + "_" + languageId);
				}
    	        ownDomainList.add(ownDomainEntity);
            	ownDomainIdList.add(ownDomainEntity.getId());
            }
        	
        	// check QC: if QC can not pass, do not upload file to S3
        	if (needFileUpload) {
        		List<Integer> ngDomainIdList = rankQcInfoEntityDAO.getNgDomainId(FormatUtils.formatDateToYyyyMmDd(processDate), ownDomainIdList, processEngineId,
            			processLanguageId, RankQcStateEntity.FREQUENCE_DAILY);
            	if (ngDomainIdList != null && ngDomainIdList.size() > 0) {
            		System.out.println("NG Domain size:" + ngDomainIdList.size() + " list:" + gson.toJson(ngDomainIdList));
    				ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV Error", "ExtractFlatDataForRV Domain Missing Keyword",
    						"NG Domain List:" + gson.toJson(ngDomainIdList));
            		return;
    			}
			}
        	
        	for (OwnDomainEntity ownDomainEntity : domainEntityList) {
        		allOwnDomainIdList.add(ownDomainEntity.getId());
            }
        	System.out.println("processing domainId:" + new Gson().toJson(ownDomainIdList));
        	
        	if(CollectionUtils.isEmpty(domainEntityList)) {
                System.out.println("Can't find RV domain.");
                return;
            }
        	
            List<GeoMasterEntity> cityEntities = geoMasterEntityDAO.getAllGeoMaster();
            for (GeoMasterEntity cityEntity : cityEntities) {
                cityMap.put(cityEntity.getId(), cityEntity.getCityName());
            }
            
            List<SeoClarityCityEntity> oldCityEntities = seoClarityCityEntityDAO.getAllCity();
            for (SeoClarityCityEntity cityEntity : oldCityEntities) {
                cityMap.put(cityEntity.getId(), cityEntity.getCityName());
            }
        	

        	List<Integer> processedDomainIdList = new ArrayList<>();
        	
//        	if (processEngineId == 1 && processLanguageId == 1) {
//        		//exclude 9018 manually
//        		processedDomainIdList.add(9018);
//			}
            int processDomainNum = 0;
            
            if (ownDomainList == null || ownDomainList.size() == 0) {
            	
            	String country = "";
            	String languageStr = "";
            	
            	if(processEngineId == 1) {
            		country = "US";
            		languageStr = "en";
            	} else if(processEngineId == 6) {
            		country = "UK";
            		languageStr = "en";
            	} else if(processEngineId == 15) {
            		country = "BR";
            		languageStr = "en";
            	}
            	
            	 String localOutputFilename = LOCAL_OUTPUT_FOLDER + File.separator + "rv_daily_" + processDateStr + "_" + country + "_" + languageStr + "_extract_tabsplit.csv";
                 File outFile = new File(localOutputFilename);
                 
                 CsvWriteConfig csvWriteConfig = CsvWriteConfig.defaultConfig();
                 csvWriteConfig.setFieldSeparator('\t');
                 CsvWriter csvWriter = CsvUtil.getWriter(outFile, Charset.forName("utf-8"), true, csvWriteConfig);
                 
             	// if is new file, then need to add header
                 if (!processedFileName.contains(localOutputFilename)) {
         			processedFileName.add(localOutputFilename);
         			csvWriter.write(headers);
         		}
				
			} else {
				 for (OwnDomainEntity ownDomainEntity : ownDomainList) {
	            	processDomainNum ++;
	            	if (processDomainNum <= 1) {
	            		String emailTo = "<EMAIL>";
	            		String[] ccTo = new String[] {"<EMAIL>", "<EMAIL>", "<EMAIL>" };
//		            		EmailSenderComponent.sendEmailReport(new Date(), "ExtractFlatDataForRV Start Processing", "ExtractFlatDataForRV started processing!!!", null, emailTo, ccTo);
	    			}
	            	
	            	System.out.println("processingOID:" + ownDomainEntity.getId());
	            	try {
						processForDomain(ownDomainEntity, processedDomainIdList, processDateStr);
					} catch (Exception e){
	            		e.printStackTrace();
					}
	                processedDomainIdList.add(ownDomainEntity.getId());
	            }
			}
           
            //
    		//============================================
            String dailyCntMessage = "";
    		// for All domains
            
            try {
            	System.out.println("======== 1.All companys ========");
        		dailyCntMessage += "======== 1.All companys ========" + "<br>" + getUniqueTotalCountMsg(allOwnDomainIdList) + "<br>";
         		
         		// for different company name
         		List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(CommonDataService.RV_COMPANY_NAME);
         		List<Integer> domainIdList = new ArrayList<Integer>();
         		if (domainList != null) {
         			domainIdList = domainList.stream().map(var -> var.getId()).collect(Collectors.toList());
         			System.out.println("======== 2.Company: " + CommonDataService.RV_COMPANY_NAME + " ========");
         			dailyCntMessage += "======== 2.Company: " + CommonDataService.RV_COMPANY_NAME + " ========" + "<br>" + getUniqueTotalCountMsg(domainIdList) + "<br>";
         		}
         		List<OwnDomainEntity> secondaryDomainList = ownDomainEntityDAO.getDomainListBasedCompanyName(CommonDataService.RV_SECONDARY_OMPANY_NAME);
         		if (secondaryDomainList != null) {
         			domainIdList = secondaryDomainList.stream().map(var -> var.getId()).collect(Collectors.toList());
         			System.out.println("======== 3.Company: " + CommonDataService.RV_SECONDARY_OMPANY_NAME + " ========");
         			dailyCntMessage += "======== 3.Company: " + CommonDataService.RV_SECONDARY_OMPANY_NAME + " ========" + "<br>" + getUniqueTotalCountMsg(domainIdList) + "<br>";
         		}
         		List<OwnDomainEntity> thirdDomainList = ownDomainEntityDAO.getDomainListBasedCompanyName(CommonDataService.RV_THIRD_COMPANY_NAME);
         		if (thirdDomainList != null) {
         			domainIdList = thirdDomainList.stream().map(var -> var.getId()).collect(Collectors.toList());
         			System.out.println("======== 4.Company: " + CommonDataService.RV_THIRD_COMPANY_NAME + " ========");
         			dailyCntMessage += "======== 4.Company: " + CommonDataService.RV_THIRD_COMPANY_NAME + " ========" + "<br>" + getUniqueTotalCountMsg(domainIdList) + "<br>";
         		}
			} catch (Exception e) {
				e.printStackTrace();
			}
            
    		//=================================================================================================
             

    		
            if (totalErrorCnt == 0) {
            	if (needFileUpload) {
            		saveFilesToDestination(processedFileName, dailyCntMessage);
				}
            	
//            	String snsMessage = "{" + 
//                 		" \"event_type\": \"usage_report\"," + 
//                 		" \"status\": \"completed\"," + 
//                 		" \"detail\": {" + 
//                 		"  \"report_date\": \"" + processDateStr + "\"," + 
//                 		"  \"us_desktop_national_unique_keyword_cnt\": " + usDestkopNationalCnt + "," + 
//                 		"  \"us_mobile_national_unique_keyword_cnt\": " + usMobileNationalCnt + "," + 
//                 		"  \"uk_desktop_national_unique_keyword_cnt\": " + ukDestkopNationalCnt + "," + 
//                 		"  \"uk_mobile_national_unique_keyword_cnt\": " + ukMobileNationalCnt + "," + 
//                 		"  \"br_desktop_national_unique_keyword_cnt\": " + brDestkopNationalCnt + "," + 
//                 		"  \"br_mobile_national_unique_keyword_cnt\": " + brMobileNationalCnt + "," + 
//                 		"  \"us_desktop_geo_unique_keyword_cnt\": " + usDestkopGeoCnt + "," + 
//                 		"  \"us_mobile_geo_unique_keyword_cnt\": " + usMobileGeoCnt + "," + 
//                 		"  \"uk_desktop_geo_unique_keyword_cnt\": " + ukDestkopGeoCnt + "," + 
//                 		"  \"uk_mobile_geo_unique_keyword_cnt\": " + ukMobileGeoCnt + "," + 
//                 		"  \"br_desktop_geo_unique_keyword_cnt\": " + brDestkopGeoCnt + "," + 
//                 		"  \"br_mobile_geo_unique_keyword_cnt\": " + brMobileGeoCnt + "," + 
//                 		"  \"total_unique_keyword_cnt\": " + (usDestkopNationalCnt + usMobileNationalCnt + ukDestkopNationalCnt
//                 				+ ukMobileNationalCnt + brDestkopNationalCnt + brMobileNationalCnt + usDestkopGeoCnt + usMobileGeoCnt + 
//                 				ukDestkopGeoCnt + ukMobileGeoCnt + brDestkopGeoCnt + brMobileGeoCnt) + "" + 
//                 		" }" + 
//                 		"} ";
//            	 https://www.wrike.com/open.htm?id=1374159648
//                if (processEngineId == 1 && processLanguageId == 1) {
//                 	DomainNoticeRelEntity domainNoticeRelEntity = NoticeService.getNoticeDomainNoticeRel(NoticeConfigEntity.CATEGORY_KEYWORD_RANK,
//             				NoticeConfigEntity.FUNCTION_NAME_ADHOCRANK, 9016);
//                 	NoticeService.sendSnsMessage(domainNoticeRelEntity, snsMessage);
//     			}
    		} else {
				ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV Error", "ExtractFlatDataForRV error occurred", "error count more than 0, need check!");
			}
		} catch (Exception e) {
			ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV Error", "ExtractFlatDataForRV get Error!!! Message : " + e.getMessage(), null);
			e.printStackTrace();
		}
    }
    
    private static Map<String, Integer> domainTotalPageCntMap = new HashMap<>();
    
    private void cacheTotalPageCnt(String rankingDate, Integer engineId, Integer languageId) {
    	List<CLRankingDetailEntity> result = clDailyRankingEntityDao.getTotalCountForRv(rankingDate, engineId, languageId);
    	if (result == null || result.size() == 0) {
    		System.out.println(" total page size not setup correctly!!! ");
			return ;
		}
    	
    	System.out.println("Total cached OIDs:" + result.size());
    	for(CLRankingDetailEntity cLRankingDetailEntity : result) {
    		domainTotalPageCntMap.put(cLRankingDetailEntity.getOwnDomainId() + "-" + cLRankingDetailEntity.getDevice(), cLRankingDetailEntity.getCnt());
    		System.out.println("===TotalPages OID:" + cLRankingDetailEntity.getOwnDomainId() + "-" + cLRankingDetailEntity.getDevice() + " TPS:" + cLRankingDetailEntity.getCnt());
    	}
    }
    
    private String getUniqueTotalCountMsg(List<Integer> allOwnDomainIdList) {
    	String dailyCntMessage = "";
    	try {
    		Integer usDestkopCnt = clDailyRankingEntityDao.getTotalCount(1, 1, processDateStr, false, allOwnDomainIdList);
     		Integer usMobileCnt = clDailyRankingEntityDao.getTotalCount(1, 1, processDateStr, true, allOwnDomainIdList);
     		Integer intlDestkopCnt = clDailyRankingEntityDao.getTotalCount(15, 16, processDateStr, false, allOwnDomainIdList);
     		Integer intlMobileCnt = clDailyRankingEntityDao.getTotalCount(15, 16, processDateStr, true, allOwnDomainIdList);
             
     		Integer usDestkopNationalCnt = clDailyRankingEntityDao.getTotalCount(1, 1, processDateStr, false, false, allOwnDomainIdList);
     		Integer usMobileNationalCnt = clDailyRankingEntityDao.getTotalCount(1, 1, processDateStr, true, false, allOwnDomainIdList);
     		Integer ukDestkopNationalCnt = clDailyRankingEntityDao.getTotalCount(6, 8, processDateStr, false, false, allOwnDomainIdList);
     		Integer ukMobileNationalCnt = clDailyRankingEntityDao.getTotalCount(6, 8, processDateStr, true, false, allOwnDomainIdList);
     		Integer brDestkopNationalCnt = clDailyRankingEntityDao.getTotalCount(15, 16, processDateStr, false, false, allOwnDomainIdList);
     		Integer brMobileNationalCnt = clDailyRankingEntityDao.getTotalCount(15, 16, processDateStr, true, false, allOwnDomainIdList);
     		
     		Integer usDestkopGeoCnt = clDailyRankingEntityDao.getTotalCount(1, 1, processDateStr, false, true,allOwnDomainIdList);
     		Integer usMobileGeoCnt = clDailyRankingEntityDao.getTotalCount(1, 1, processDateStr, true, true,allOwnDomainIdList);
     		Integer ukDestkopGeoCnt = clDailyRankingEntityDao.getTotalCount(6, 8, processDateStr, false, true,allOwnDomainIdList);
     		Integer ukMobileGeoCnt = clDailyRankingEntityDao.getTotalCount(6, 8, processDateStr, true, true,allOwnDomainIdList);
     		Integer brDestkopGeoCnt = clDailyRankingEntityDao.getTotalCount(15, 16, processDateStr, false, true,allOwnDomainIdList);
     		Integer brMobileGeoCnt = clDailyRankingEntityDao.getTotalCount(15, 16, processDateStr, true, true, allOwnDomainIdList);
            
            System.out.println("usDestkopNationalCnt:" + usDestkopNationalCnt);
            System.out.println("usMobileNationalCnt:" + usMobileNationalCnt);
            System.out.println("ukDestkopNationalCnt:" + ukDestkopNationalCnt);
            System.out.println("ukMobileNationalCnt:" + ukMobileNationalCnt);
            System.out.println("brDestkopNationalCnt:" + brDestkopNationalCnt);
            System.out.println("brMobileNationalCnt:" + brMobileNationalCnt);
            System.out.println("usDestkopGeoCnt:" + usDestkopGeoCnt);
            System.out.println("usMobileGeoCnt:" + usMobileGeoCnt);
            System.out.println("ukDestkopGeoCnt:" + ukDestkopGeoCnt);
            System.out.println("ukMobileGeoCnt:" + ukMobileGeoCnt);
            System.out.println("brDestkopGeoCnt:" + brDestkopGeoCnt);
            System.out.println("brMobileGeoCnt:" + brMobileGeoCnt);
            System.out.println("Total count:" + (usDestkopNationalCnt + usMobileNationalCnt + ukDestkopNationalCnt
    				+ ukMobileNationalCnt + brDestkopNationalCnt + brMobileNationalCnt + usDestkopGeoCnt + usMobileGeoCnt + 
    				ukDestkopGeoCnt + ukMobileGeoCnt + brDestkopGeoCnt + brMobileGeoCnt));
            
            dailyCntMessage = "usDestkopNationalCnt:" + usDestkopNationalCnt + ", " + "usMobileNationalCnt:" + usMobileNationalCnt + ", " + 
            		"ukDestkopNationalCnt:" + ukDestkopNationalCnt +  ", " + "ukMobileNationalCnt:" + ukMobileNationalCnt +  ", " + 
            		"brDestkopNationalCnt:" + brDestkopNationalCnt +  ", " + "brMobileNationalCnt:" + brMobileNationalCnt +  ", " + 
            		"usDestkopGeoCnt:" + usDestkopGeoCnt +  ", " + "usMobileGeoCnt:" + usMobileGeoCnt +  ", " + 
            		"ukDestkopGeoCnt:" + ukDestkopGeoCnt + 	 ", " + "ukMobileGeoCnt:" + ukMobileGeoCnt +  ", " + 
            		"brDestkopGeoCnt:" + brDestkopGeoCnt + 	 ", " + "brMobileGeoCnt:" + brMobileGeoCnt + ", Total:" + 
            		(usDestkopNationalCnt + usMobileNationalCnt + ukDestkopNationalCnt + ukMobileNationalCnt + brDestkopNationalCnt + 
            			brMobileNationalCnt + usDestkopGeoCnt + usMobileGeoCnt + ukDestkopGeoCnt + ukMobileGeoCnt + brDestkopGeoCnt + brMobileGeoCnt);
            
		} catch (Exception e) {
			e.printStackTrace();
		}
    	
        return dailyCntMessage;
    }

    private void saveFilesToDestination(Set<String> localFileSet, String dailyCntMessage) {
//        boolean savedFilesToFTP = true;
        boolean savedFilesToS3 = true;
        List<String> fileList = new ArrayList<String>(localFileSet);
        try {
            for (String localOutputFilename : fileList) {
                System.out.println(" ==saveFileToDestination localFile:" + localOutputFilename);
                File outFile = new File(localOutputFilename);
//                File zipFile = new File(outFile.getAbsolutePath()+".gz");
//                if (zipFile!= null && zipFile.exists() && zipFile.isFile()) {
//                	System.out.println("file already exist, deleted!!!");
//                	zipFile.delete();
//				}
//                
//                GZipUtil.zip(outFile.getAbsolutePath(), zipFile.getAbsolutePath());
//                ZipUtil.zip(outFile.getAbsolutePath(), zipFile.getAbsolutePath());
//                System.out.println("======OutGzipFile:" + zipFile.getAbsolutePath());
                // FTPUtils.saveFileToFTP(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, 0 , zipFile.getAbsolutePath(), FTP_FOLDER);
                savedFilesToS3 = ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(processDateIntStr, outFile.getAbsolutePath(), S3_BUCKET_KEY_PREFIX, false);

                //https://www.wrike.com/open.htm?id=961371346
                try {
            		ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(processDateIntStr, outFile.getAbsolutePath(), S3_BUCKET_KEY_PREFIX, true);
				} catch (Exception e) {
					e.printStackTrace();
					ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV Error", "ExtractFlatDataForRV saveFileToS3ByKeyByTransferManager error", e.getMessage());
				}
                
                // boolean savedToS3 = ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(processDateIntStr, zipFile.getAbsolutePath(), S3_BUCKET_KEY_PREFIX);
//                boolean savedToFTP = FTPUtils.saveFileToFTPWithRetryCount(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, 0 ,
//                		zipFile.getAbsolutePath(), FTP_FOLDER, FTP_RETRY_COUNT);
//                if (savedToFTP == false) {
//                    savedFilesToFTP = false;
//                }
//                if (savedFilesToS3 == true) {
//                	renameFileAfterProcessDone(outFile.getAbsolutePath());
//                }
//                if (savedToS3 && savedToFTP) {
//                    System.out.println(" ======DeleteFileAfterSentOK:" + localOutputFilename);
//                    outFile.delete();
//                    zipFile.delete();
//                }
            }
        } catch (Exception e) {
            e.printStackTrace();
			ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV Error", "ExtractFlatDataForRV error occurred", e.getMessage());
		}

        try {
            if (savedFilesToS3) {
				ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV Success", "ExtractFlatDataForRV sent file to S3 and FTP, " + dailyCntMessage, null);
			} else {
				ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV sent file to S3/FTP Error",
					"ExtractFlatDataForRV sent to s3:" + savedFilesToS3 + ", total extract count : " + totalExtractCnt + ", total error count : " + totalErrorCnt, null);
			}
        } catch (Exception exp) {
            exp.printStackTrace();
        }
        
        if (processEngineId == 6 && processLanguageId == 8) { // TODO for UK, send file to google cloud
        	for (String localOutputFilename : fileList) {
                System.out.println(" ======SaveUKFileToAdditionalStorages localFile:" + localOutputFilename);
                sentFileToS3(new File(localOutputFilename));
                sentFileToGoogleCloadStorage(new File(localOutputFilename));
        	}
		}
    }
    
    private static String credentialFilePath = "/home/<USER>/rvu-seo-seoclarity-74a2a327064b.json";
    private static String bucketName = "rvu-seo-seoclarity-import";
    private void sentFileToGoogleCloadStorage(File file) {
        try {System.out.println("");
        	String year = processDateIntStr.substring(0, 4);
    		String month = processDateIntStr.substring(4, 6);
    		String day = processDateIntStr.substring(6);
    		String fileName = file.getName();
    		String s3Key = year + ExtractRedVenturesKeywords.KEY_DELIMITER + month 
    				+ ExtractRedVenturesKeywords.KEY_DELIMITER + day + ExtractRedVenturesKeywords.KEY_DELIMITER + fileName;
    		System.out.println(" ==GoogleCloudStorage Key:" + s3Key + " file:" + fileName);

            Storage storage = StorageOptions.newBuilder().setCredentials(ServiceAccountCredentials.fromStream(new FileInputStream(credentialFilePath))).build().getService();
            long a = System.currentTimeMillis();

            //upload
            BlobId blobId = BlobId.of(bucketName, s3Key);
            BlobInfo blobInfo = BlobInfo.newBuilder(blobId).build();
//            storage.create(blobInfo, Files.readAllBytes(Paths.get(file.getAbsolutePath())));
            
            uploadToStorage(storage, file, blobInfo);
            
            long b = System.currentTimeMillis();
            System.out.println("Upload to GoogleCloudStorage:" + (b - a) * 1.0 / 1000 + " s ");
        } catch (Exception e){
            e.printStackTrace();
			ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ExtractFlatDataForRV Send to Google Cloud failed", "ExtractFlatDataForRV Send to Google Cloud failed", e.getMessage());
        }
    }
    
    private static String RV_S3_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
    private static String RV_S3_SECRET_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
    private static String RV_S3_BUCKET = "seoclarity-rvu";
//    private static AWSCredentials rvCredentials = new BasicAWSCredentials(RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY);
 
    private void sentFileToS3(File file) {
		try {
			System.out.println("===sendFileToS3:" + file.getName());
			ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(file.getName(), file.getAbsolutePath(), RV_S3_BUCKET, RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY);
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
    private void uploadToStorage(Storage storage, File uploadFrom, BlobInfo blobInfo) throws IOException {
        // For small files:
        if (uploadFrom.length() < 1_000_000) {
            byte[] bytes = Files.readAllBytes(uploadFrom.toPath());
            storage.create(blobInfo, bytes);
            return;
        }

        // For big files:
        // When content is not available or large (1MB or more) it is recommended to write it in chunks via the blob's chaxnnel writer.
        try (WriteChannel writer = storage.writer(blobInfo)) {
            byte[] buffer = new byte[10_240];
            try (InputStream input = Files.newInputStream(uploadFrom.toPath())) {
                int limit;
                while ((limit = input.read(buffer)) >= 0) {
                    writer.write(ByteBuffer.wrap(buffer, 0, limit));
                }
            }
        }
    }

    private void processForDomain(OwnDomainEntity ownDomainEntity, List<Integer> excludeDomainIdList, String rankingDate) throws Exception {
        int domainId = ownDomainEntity.getId();
        System.out.println(" ===CheckingOID:" + domainId);
        int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String device = DEVICE_DESKTOP;
        if (ownDomainEntity.isMobileDomain()) {
            device = DEVICE_MOBILE;
            System.out.println(" MobileDomain:" + domainId);
        }

        //-------------------------- create csv writer --------------------------------
        String country = ownDomainEntity.getSearchEngineCountry();
        String countryName = "United States";
        List<EngineCountryLanguageMappingEntity> mappingEntities = mappingEntityDAO.getByEngineLanguageIdForCountry(engineId, languageId, country);
        if(!mappingEntities.isEmpty()) {
            countryName = mappingEntities.get(0).getCountryDisplayName();
            System.out.println(" FoundCountryName:" + countryName + " OID:" + domainId + " SE:" + engineId + "_" + languageId + " country:" + country);
        } else {
            System.out.println(" NotFoundCountryName, useDefault:" + countryName + " OID:" + domainId + " SE:" + engineId + "_" + languageId + " country:" + country);
        }

        String languageStr = ownDomainEntity.getLanguage();
        
        //hard code, checked with Wilber
        if (StringUtils.equalsIgnoreCase(country, "br") && StringUtils.equalsIgnoreCase(languageStr, "pt")) {
        	languageStr = "en";
		}

        String localOutputFilename = LOCAL_OUTPUT_FOLDER + File.separator + "rv_daily_" + processDateStr + "_" + country + "_" + languageStr + "_extract_tabsplit.csv";
        File outFile = new File(localOutputFilename);
        
        CsvWriteConfig csvWriteConfig = CsvWriteConfig.defaultConfig();
        csvWriteConfig.setFieldSeparator('\t');
        CsvWriter csvWriter = CsvUtil.getWriter(outFile, Charset.forName("utf-8"), true, csvWriteConfig);
        
    	// if is new file, then need to add header
        if (!processedFileName.contains(localOutputFilename)) {
			processedFileName.add(localOutputFilename);
			csvWriter.write(headers);
		}
        
        //-------------------------- create csv writer --------------------------------
        processForEngineLanguage(ownDomainEntity, engineId, languageId, rankingDate, device, excludeDomainIdList, csvWriter);
        
        List<DomainSearchEngineRelEntity> domainSearchEngineRelList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
        if (domainSearchEngineRelList != null && domainSearchEngineRelList.size() > 0) {
            for (DomainSearchEngineRelEntity seRelEntity : domainSearchEngineRelList) {
                System.out.println(" SecondSE:" + ownDomainEntity.getId() +  " rel:" + seRelEntity.getId() + " " + seRelEntity.getSearchEngineId() + "/" + seRelEntity.getSearchEngineLanguageid() +
                        "-->" + seRelEntity.getRankcheckSearchEngineId() + "/" + seRelEntity.getRankcheckSearchLanguageid() + "/" + seRelEntity.getDevice());
                processForEngineLanguage(ownDomainEntity, seRelEntity.getRankcheckSearchEngineId(), 
                		seRelEntity.getRankcheckSearchLanguageid(), rankingDate, seRelEntity.getDevice(), excludeDomainIdList, csvWriter);
            }
        }
        
        if (csvWriter != null) {
    	    try {
    	        csvWriter.close();
    	    } catch (Exception exp) {
    	        exp.printStackTrace();
    	    }
    	}
    }
    
    private static Integer pageSize = 10000;
	
    private void processForEngineLanguage(OwnDomainEntity ownDomainEntity, Integer engineId, Integer languageId, String rankingDate, 
    		String device, List<Integer> excludeDomainIdList, CsvWriter csvWriter) throws Exception{
		Integer pageNum = 0;
		List<CLRankingDetailEntity> rankList = new ArrayList<>();
		boolean finished = false;
		int num = 0;
		Integer currKeywordRankCheckId = 0;
    	Integer currCityId = 0;
    	Integer currentRank = 0;
    	KeywordRankVO keywordRankVO = new KeywordRankVO();
    	KeywordRankEntityVO keywordRankEntityVO = new KeywordRankEntityVO();
    	List<KeywordRankEntityVO> keywordRankEntityVOs ;
    	KeywordSubRankEntityVO keywordSubRankEntityVO;
    	
    	String country = ownDomainEntity.getSearchEngineCountry();
    	String countryName = "United States";
        List<EngineCountryLanguageMappingEntity> mappingEntities = mappingEntityDAO.getByEngineLanguageIdForCountry(engineId, languageId, country);
        if(!mappingEntities.isEmpty()) {
            countryName = mappingEntities.get(0).getCountryDisplayName();
            System.out.println(" FindCountryName:" + countryName + " OID:" + ownDomainEntity.getId() + " SE:" + engineId + "_" + languageId + " country:" + country);
        } else {
            System.out.println(" UseDefaultCountryName:" + countryName + " OID:" + ownDomainEntity.getId() + " SE:" + engineId + "_" + languageId + " country:" + country);
        }
        String location = "";
        
        Integer totalPageCnt = domainTotalPageCntMap.get(ownDomainEntity.getId() + "-" + device);
        System.out.println(" ==ProcessSE OID:" + ownDomainEntity.getId() + "_" + engineId + "_" + languageId + "_" + device + 
    		"(" + rankingDate + ") pages:" + totalPageCnt + " excludeOIDs:" + excludeDomainIdList);
		if (totalPageCnt == null || totalPageCnt <= 0) {
			System.out.println(" ====TPS not found!!!");
//			num = 0;
		} else {
			while (!finished) {
				long a = System.currentTimeMillis();
				if (pageNum == totalPageCnt) {
					keywordRankVO = new KeywordRankVO();
	        		finished = true;
	    			break;
	    		}
				
				int retryNum = 0;
				while (retryNum <= 3) {
					retryNum++;
					try {
						rankList = clDailyRankingEntityDao.exportForRVWithTrueDemand(ownDomainEntity.getId(), engineId, languageId, rankingDate, 
								StringUtils.equalsIgnoreCase(DEVICE_MOBILE, device), pageNum, countryCode, totalPageCnt, 100);
//						rankList = clDailyRankingEntityDao.exportForRV(ownDomainEntity.getId(), engineId, languageId, 
//								rankingDate, StringUtils.equalsIgnoreCase(DEVICE_MOBILE, device), pageNum, countryCode, totalPageCnt, 100);
						break;
					} catch (Exception e) {
						System.out.println(" RetryCnt:" + retryNum);
						e.printStackTrace();
						try {
							System.out.println(" Sleep " + retryNum + " minutes");
	                        Thread.sleep(retryNum * 60 * 1000);
	                    } catch (Exception e1) {}
					}
				}
				long b = System.currentTimeMillis();
				pageNum ++ ;
				long c = System.currentTimeMillis();
				totalCnt += rankList.size();
				System.out.println(" GetCHData OID:" + ownDomainEntity.getId() + "_" + engineId + "_" + languageId + "_" + device +
					" pN:" + pageNum + "_" + rankList.size() + "(" + totalCnt + ") time:" + (b - a) * 1.0 / 1000 + "s");
	    		for(CLRankingDetailEntity detailEntity : rankList) {
	        		if (currKeywordRankCheckId == null || currKeywordRankCheckId == 0 
	        				|| currKeywordRankCheckId.intValue() != detailEntity.getKeywordRankcheckId().intValue()
	        				|| currCityId.intValue() != detailEntity.getLocationId()) {
	        			if (currKeywordRankCheckId > 0) {
//	            				System.out.println("currCityId.intValue():" + currCityId.intValue() + ", detailEntity.getLocationId():" + detailEntity.getLocationId());
//	            				if (num % 1000 == 0) {
//	    							System.out.println("processing on :" + num);
////	    						System.out.println(gson.toJson(keywordRankVO));
//	    						}
	        				location = keywordRankVO.getCityId() != 0 ? keywordRankVO.getCityName() : countryName;
	        				
	        				if (OLD_CITY_NAME_MAP.keySet().contains(keywordRankVO.getCityId())) {
	                    		System.out.println("@@@ replace city name for city ID:" + keywordRankVO.getCityId());
	                    		location = OLD_CITY_NAME_MAP.get(keywordRankVO.getCityId());
	        				}
	        				
	        				if (CITY_MAP_NEED_TO_REPLACE.keySet().contains(location)) {
	        					System.out.println(" REPLACECity" + location + "->" + CITY_MAP_NEED_TO_REPLACE.get(location));
								location = CITY_MAP_NEED_TO_REPLACE.get(location);
							}
	        				
	        				if (StringUtils.isBlank(location) || StringUtils.equals(location, "Unknown City")) {
	        					System.out.println("======TranslateUnknownCity to " + cityMap.get(keywordRankVO.getCityId()));
	        					if (StringUtils.isNotBlank(cityMap.get(keywordRankVO.getCityId()))) {
	        						location = cityMap.get(keywordRankVO.getCityId());
								}
	        					
	        					if (StringUtils.isBlank(location)) {
	        						System.out.println(" IncorrectCityName:" + keywordRankVO.getCityId());
	        					} else {
	        						System.out.println(" CityName:" + location + " Cid:" + keywordRankVO.getCityId());
	        					}
							}
	        				processSignalKeyword(keywordRankVO, csvWriter, ownDomainEntity, device, location);
	        				num++;
	    				}
	        			
	        			keywordRankVO = new KeywordRankVO();
	        			
	        			currKeywordRankCheckId = detailEntity.getKeywordRankcheckId().intValue();
	        			currCityId = detailEntity.getLocationId();
	        			
	        			keywordRankVO.setId(detailEntity.getKeywordRankcheckId().intValue());
	        			keywordRankVO.setCpc(detailEntity.getCpc() + "");
	        			
	        			//from ALps: one more thing. if sv is 0 , you have to check additional flg to ensure its indeed 0.
	        			//if not_realSearchVolume =1 means we dont have SV for this keywords. we should put -99 instead of 0
	        			if (NumberUtils.toInt(detailEntity.getNotRealSearchVolume()) == 1 && detailEntity.getAvgSearchVolume() == 0L) {
	        				keywordRankVO.setSearchVol("-99");
	        			} else {
	        				keywordRankVO.setSearchVol(detailEntity.getAvgSearchVolume()+"");
	        			}
	        			
	        			// https://www.wrike.com/open.htm?id=1359315662
	        			keywordRankVO.setTrueDemand(String.valueOf(detailEntity.getTrueDemand() == null ? 0 : detailEntity.getTrueDemand().intValue()));
	        			
	        			if (detailEntity.getLocationId() > 0) {
	            			String cityName = cityMap.get(detailEntity.getLocationId());
	        				keywordRankVO.setCityName(cityName);
						}
	        			keywordRankVO.setKeyword(detailEntity.getKeywordName());
	        			keywordRankVO.setAppFlg(NumberUtils.toInt(detailEntity.getAppFlg()) == 1 ? "Y" : "N");
	        			keywordRankVO.setLlFlg(NumberUtils.toInt(detailEntity.getLlFlg()) == 1 ? "Y" : "N");
	        			keywordRankVO.setPpcFlg(NumberUtils.toInt(detailEntity.getPpcFlg()) == 1 ? "Y" : "N");
	        			keywordRankVO.setCommercialFlg(NumberUtils.toInt(detailEntity.getCommercialFlg()) == 1 ? "Y" : "N");
	        			keywordRankVO.setFlightSearchFlg(NumberUtils.toInt(detailEntity.getFlightSearchFlg()) == 1 ? "Y" : "N");
	        			keywordRankVO.setResearchFlg(NumberUtils.toInt(detailEntity.getResearchFlg()) == 1 ? "Y" : "N");
	        			keywordRankVO.setTopPpcCnt(detailEntity.getTopPpcCnt()+ "");
	        			keywordRankVO.setSocialInKg(NumberUtils.toInt(detailEntity.getSocial_in_kg()) == 1 ? "Y" : "N");
	        			keywordRankVO.setPlaFlg(NumberUtils.toInt(detailEntity.getPla_flg()) == 1 ? "Y" : "N");
	        			keywordRankVO.setKnogTag(NumberUtils.toInt(detailEntity.getKnog_flg()) == 1 ? "Y" : "N");
	        			
	        			String answerboxUrl = "";
	        			if (StringUtils.isNotBlank(detailEntity.getAnswerboxUrl())) {
	        				answerboxUrl = detailEntity.getAnswerboxUrl();
	        				
	        				if (StringUtils.isNotBlank(detailEntity.getAdditionalAb())) {
	        					answerboxUrl = detailEntity.getAnswerboxUrl() + "!_!" + detailEntity.getAdditionalAb();
							}
						}
	        			
	        			keywordRankVO.setAnswerBox(answerboxUrl);
	        			keywordRankVO.setGoogleRecommend(detailEntity.getGoogleRecommend());
	        			keywordRankVO.setRefineBy(detailEntity.getRefineBy());
	        			keywordRankVO.setJobLink(detailEntity.getJobLink());
	        			keywordRankVO.setCityId(detailEntity.getLocationId());
	        			
	        			keywordRankVO.setAdditionalQuestions(detailEntity.getAdditionalQuestions());
	        			keywordRankVO.setQuestions(detailEntity.getQuestions());
	    			}
	        		
	        		//means this record is not a subrank
	        		if (currentRank == 0 || currentRank != detailEntity.getTrueRank()) {
	        			currentRank = detailEntity.getTrueRank();
	        			
	        			keywordRankEntityVO = new KeywordRankEntityVO();
	        			keywordRankEntityVO.setLabel(detailEntity.getLabel());
	        			keywordRankEntityVO.setMetaDesc(detailEntity.getMeta());
	        			
	        			String landingPage = detailEntity.getUrl();
	        			keywordRankEntityVO.setLandingPage(landingPage);
	        			keywordRankEntityVO.setRank(detailEntity.getTrueRank());
	        			keywordRankEntityVO.setRating(NumberUtils.toInt(detailEntity.getRating()) == 1 ? "Y" : "N");
	        			keywordRankEntityVO.setRatingNumber(detailEntity.getRatingNumber());
	        			keywordRankEntityVO.setType(detailEntity.getType());
	        			if (keywordRankVO.getKeywordRankEntityVOs() == null ) {
	        				keywordRankEntityVOs = new ArrayList<>();
	        				keywordRankVO.setKeywordRankEntityVOs(keywordRankEntityVOs);
	        			} else {
	        				keywordRankEntityVOs = keywordRankVO.getKeywordRankEntityVOs();
	        			}
	        			
	        			keywordRankEntityVOs.add(keywordRankEntityVO);
	    			}
	        		
	        		if (detailEntity.getType() == KeywordRankEntityVO.TYPE_LOCALLISTING) {
//	            			System.out.println(" ======= subRank for locallisting");
	        			List<String> localListingList ;
	        			if (keywordRankVO.getLocalListing() == null ) {
	        				localListingList = new ArrayList<>();
	        				
	    				} else {
	    					localListingList = keywordRankVO.getLocalListing();
	    				}
	        			
	        			localListingList.add(detailEntity.getSubDomainRev() + "!_!" + detailEntity.getSubRankUrl());
	    				keywordRankVO.setLocalListing(localListingList);
	    				continue;
	    			}
	    			
	    			//======================  subrank ========================
	    			if ((detailEntity.getType() == KeywordRankEntityVO.TYPE_NEWS 
	    					|| detailEntity.getType() == KeywordRankEntityVO.TYPE_IMGAGE
	    					|| detailEntity.getType() == KeywordRankEntityVO.TYPE_VIDEO)) {
	    				List<KeywordSubRankEntityVO> subrankList;
	    				
	    				if (keywordRankEntityVO.getSubRankVOs() == null ) {
	    					subrankList = new ArrayList<>();
	        				
	    				} else {
	    					subrankList = keywordRankEntityVO.getSubRankVOs();
	    				}
	        			
	    				String subRankLandingPage = detailEntity.getSubRankUrl();
	    				keywordSubRankEntityVO = new KeywordSubRankEntityVO();
	    				
	    				keywordSubRankEntityVO.setRank(detailEntity.getTrueRank());
	    				keywordSubRankEntityVO.setSubRank(detailEntity.getSubRank());
	    				keywordSubRankEntityVO.setLabel(detailEntity.getSubRankLabel());
	    				keywordSubRankEntityVO.setLandingPage(subRankLandingPage);
	    				
	    				subrankList.add(keywordSubRankEntityVO);
	    				
	    				keywordRankEntityVO.setSubRankVOs(subrankList);
	    			}
	    			//======================  subrank ========================
	        	}
	    		
	    		if (keywordRankVO != null && keywordRankVO.getId() != null && keywordRankVO.getId() > 0) {
	    			//process for the last keyword
	        		processSignalKeyword(keywordRankVO, csvWriter, ownDomainEntity, device, location);
	        		keywordRankVO = new KeywordRankVO();
	        		currKeywordRankCheckId = 0;
	        		currCityId = 0;
	        		num++;
	    		}
	    		System.out.println("processing num:" + num);
//	        		System.out.println("Total result count: " + rankList.size());
//	    		if (rankList.size() < pageSize) {
//	        		finished = true;
//	    			break;
//	    		}
	    		long d = System.currentTimeMillis();
			    System.out.println("Write to file cost : " + (d - c) * 1.0 / 1000 + "s");
			}
		}
		
//		if (keywordRankVO != null && keywordRankVO.getId() != null && keywordRankVO.getId() > 0) {
//    		processSignalKeyword(keywordRankVO, csvWriter, ownDomainEntity, device, location); //process for the last keyword
//    		num++;
//		}
		
		if (num > 0) {
			totalExtractCnt += num;
			System.out.println("Total processed : " + num + ", OID:" + ownDomainEntity.getId() + ", engine:" + engineId + ", languageId:" + languageId + ", device:" + device + ", totalExtractCnt:" + totalExtractCnt);
			num = 0;
		}
    }
    
    private void processSignalKeyword(KeywordRankVO keywordRankVO, CsvWriter csvWriter, OwnDomainEntity ownDomainEntity, String device, 
    		String location) {   	
    	String[] defaultArr = new String[headers.length];
    	String engineStr = ownDomainEntity.getSearchEngine();
        String country = ownDomainEntity.getSearchEngineCountry();
         String languageStr = ownDomainEntity.getLanguage();
        String deviceStr = DEVICE_EXPORT_STR_DESKTOP;
        if (DEVICE_MOBILE.equalsIgnoreCase(device)) {
            deviceStr = DEVICE_EXPORT_STR_MOBILE;
        }
        
        logGeoLocations(keywordRankVO.getCityId(), location);
        
    	try {
    		if (StringUtils.equalsIgnoreCase(ownDomainEntity.getSearchEngineCountry(), "us")) {
    			totalUSCnt ++;
			} else if (StringUtils.equalsIgnoreCase(ownDomainEntity.getSearchEngineCountry(), "uk")) {
				totalUKCnt ++;
			} else if (StringUtils.equalsIgnoreCase(ownDomainEntity.getSearchEngineCountry(), "br")) {
				totalBRCnt ++;
			}
    		
            for (int i = 0; i < headers.length; i++) {
                defaultArr[i] = "";
            }
            Integer id = keywordRankVO.getId();
            String sv = StringUtils.isNotBlank(keywordRankVO.getSearchVol()) ? keywordRankVO.getSearchVol() : "";
            sv = sv.replaceAll(",", "");
            String cpc = StringUtils.isNotBlank(keywordRankVO.getCpc()) ? keywordRankVO.getCpc() : "";
            cpc = cpc.replaceAll(",", "");
            
            defaultArr[0] = getStringValue(id); // search_metadata - id
            defaultArr[1] = "Success"; // search_metadata - status
            defaultArr[2] = processDateStr;

            String qKeyword = keywordRankVO.getKeyword();
            defaultArr[2+1] = "google"; // search_parameters - engine
            defaultArr[3+1] = engineStr; // search_parameters - google_domain
            defaultArr[4+1] = country; // search_parameters - gl
            defaultArr[5+1] = languageStr; // search_parameters - hl
            defaultArr[6+1] = deviceStr; // search_parameters - device
            defaultArr[7+1] = qKeyword; // search_parameters - q
            defaultArr[8+1] = location; // search_parameters - location_requested

            String totalResult = keywordRankVO.getGoogleResultCnt();
            totalResult = StringUtils.isBlank(totalResult) ? "" : totalResult.replaceAll(",", "").replaceAll("\\.", "");
            defaultArr[9+1] = getStringValue(totalResult); // search_information - total_results
            defaultArr[10+1] = StringUtils.isNotBlank(keywordRankVO.getAppFlg()) ? keywordRankVO.getAppFlg() : "N"; // search_information - app_flag
            defaultArr[11+1] = getStringValue(keywordRankVO.getLlFlg()); // search_information - local_map_flag
            defaultArr[12+1] = getStringValue(keywordRankVO.getPpcFlg()); // search_information - ads_flag
            defaultArr[13+1] = StringUtils.isNotBlank(keywordRankVO.getAnswerBox()) ? "Y" : "N"; // search_information - answer_box_flag
            defaultArr[14+1] = getStringValue(cpc); // search_information - cpc
            defaultArr[15+1] = getStringValue(sv); // search_information - search_volume
            
            // https://www.wrike.com/open.htm?id=1359315662
            defaultArr[16+1] = keywordRankVO.getTrueDemand(); // search_information - true_demand
            
            defaultArr[16+2] = getStringValue(keywordRankVO.getCommercialFlg()); // search_information - commercial_flag
            defaultArr[17+2] = getStringValue(keywordRankVO.getFlightSearchFlg()); // search_information - flights_flag
            defaultArr[18+2] = getStringValue(keywordRankVO.getResearchFlg()); // search_information - google_research_flag
            defaultArr[19+2] = getStringValue(StringUtils.isBlank(keywordRankVO.getTopPpcCnt()) ? "0" : keywordRankVO.getTopPpcCnt()); // search_information - top_ppc_count
            defaultArr[20+2] = StringUtils.isNotBlank(keywordRankVO.getSocialInKg()) ? keywordRankVO.getSocialInKg() : "N"; // search_information - social_in_knowledge
            defaultArr[21+2] = StringUtils.isNotBlank(keywordRankVO.getPlaFlg()) ? keywordRankVO.getPlaFlg() : "N"; // search_information - pla_flag
            defaultArr[22+2] = getStringValue(keywordRankVO.getKnogTag()); // search_information - lpu

            if(StringUtils.isNotBlank(keywordRankVO.getAnswerBox()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getAnswerBox(), "null")) {
                defaultArr[23+2] = keywordRankVO.getAnswerBox(); // answer_box_url
            }

            if(StringUtils.isNotBlank(keywordRankVO.getGoogleRecommend()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getGoogleRecommend(), "-")) {
                defaultArr[24+2] = keywordRankVO.getGoogleRecommend(); // google_recommend
            }

            if(StringUtils.isNotBlank(keywordRankVO.getRefineBy()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getRefineBy(), "-")) {
                String[] arr = StringUtils.split(keywordRankVO.getRefineBy(), "!_!");
                if(arr != null && arr.length >= 2) {
                	String[] sarray = arr[1].split(" ");
                	if(sarray != null && sarray.length >= 2) {
                		for (String s : sarray) {
                			defaultArr[25+2] = getStringValue(arr[0]); // refine_by - title
                            defaultArr[26+2] = getStringValue(s); // refine_by - details
                            csvWriter.write(defaultArr);
                		}
                	}
                    defaultArr[25+2] = "";
                    defaultArr[26+2] = "";
                }
            }

            if(StringUtils.isNotBlank(keywordRankVO.getJobLink()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getJobLink(), "-")) {
                String[] arr = StringUtils.split(keywordRankVO.getJobLink(), "@_@");
                if(arr != null && arr.length >= 2) {
                	String[] sarray = arr[1].split(" ");
                	if(sarray != null && sarray.length >= 2) {
                		for (String s : sarray) {
                			 defaultArr[27+2] = getStringValue(arr[0]); // google_job - link
                             defaultArr[28+2] = getStringValue(s); // google_job - details
                             csvWriter.write(defaultArr);
                		}
                	}
                    defaultArr[27+2] = "";
                    defaultArr[28+2] = "";
                }
            }

            List<KeywordRankEntityVO> tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_LOCALLISTING);
            if(CollectionUtils.isNotEmpty(tmpList)) {
                defaultArr[29+2] = tmpList.get(0).getLandingPage(); // local_map - link
                defaultArr[30+2] = tmpList.get(0).getLabel(); // local_map - title

                if(CollectionUtils.isNotEmpty(keywordRankVO.getLocalListing())) {
                    for (int i = 0; i < keywordRankVO.getLocalListing().size(); i++) {
                        String[] s = keywordRankVO.getLocalListing().get(i).split("!_!");
                        if(s.length == 0 || StringUtils.isBlank(s[0])) {
                            continue;
                        }
                        defaultArr[31+2] = String.valueOf(i+1); // local_map - places - rank
                        defaultArr[32+2] = getStringValue(s[0]); // local_map - places - title
                        csvWriter.write(defaultArr);
                    }
                    defaultArr[31+2] = "";
                    defaultArr[32+2] = "";
                }
                defaultArr[29+2] = "";
                defaultArr[30+2] = "";
            }

            
            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_VIDEO);
            if(CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if(CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[33+2] = String.valueOf(keywordSubRankEntityVO.getSubRank()); // inline_videos - rank
                        defaultArr[34+2] =  getStringValue(keywordSubRankEntityVO.getLabel()); // inline_videos - title
                        defaultArr[35+2] = getStringValue(keywordSubRankEntityVO.getLandingPage()); // inline_videos - link
                        csvWriter.write(defaultArr);
                    }
                }
                defaultArr[33+2] = "";
                defaultArr[34+2] = "";
                defaultArr[35+2] = "";
            }

            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_IMGAGE);
            if(CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if(CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[36+2] = String.valueOf(keywordSubRankEntityVO.getSubRank()); // inline_images - rank
                        defaultArr[37+2] =  getStringValue(keywordSubRankEntityVO.getLabel()); // inline_images - title
                        defaultArr[38+2] = getStringValue(keywordSubRankEntityVO.getLandingPage()); // inline_images - link
                        csvWriter.write(defaultArr);
                    }
                }
                defaultArr[36+2] = "";
                defaultArr[37+2] = "";
                defaultArr[38+2] = "";
            }

            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_NEWS);
            if(CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if(CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[39+2] = String.valueOf(keywordSubRankEntityVO.getSubRank()); // top_stories - rank
                        defaultArr[40+2] =  getStringValue(keywordSubRankEntityVO.getLabel()); // top_stories - title
                        defaultArr[41+2] = getStringValue(keywordSubRankEntityVO.getLandingPage()); // top_stories - link
                        csvWriter.write(defaultArr);
                    }
                }
                defaultArr[39+2] = "";
                defaultArr[40+2] = "";
                defaultArr[41+2] = "";
            }

            if(StringUtils.isNotBlank(keywordRankVO.getAdditionalQuestions()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getAdditionalQuestions(), "-")) {
                String[] qArr = keywordRankVO.getAdditionalQuestions().split("@_@");
                if(qArr.length > 0) {
                    for (String s : qArr) {
                        String[] tArr = s.split("!_!");
                        if(tArr != null && tArr.length >= 2) {
                        	 defaultArr[42+2] = getStringValue(tArr[1]); // people_also_ask - title
                             defaultArr[43+2] = getStringValue(tArr[0]); // people_also_ask - link
                             csvWriter.write(defaultArr);
                        }
                    }
                }
            } else if(StringUtils.isNotBlank(keywordRankVO.getQuestions()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getQuestions(), "-")) {
                String[] qArr = keywordRankVO.getQuestions().split("!_!");
                if(qArr.length > 0) {
                    for (String s : qArr) {
                        defaultArr[42+2] = getStringValue(s); // related_questions - title
                        defaultArr[43+2] = ""; // related_questions - link
                        csvWriter.write(defaultArr);
                    }
                }
            }
            defaultArr[42+2] = "";
            defaultArr[43+2] = "";

            int webRank = 1;
            if (keywordRankVO != null && keywordRankVO.getKeywordRankEntityVOs() != null ) {
            	for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
                    String type = "Web Url";
                    String url = keywordRankEntityVO.getLandingPage();
                    if(url.contains(".google.com")) {
                        switch (keywordRankEntityVO.getType()) {
                            case KeywordRankEntityVO.TYPE_IMGAGE:
                                url = "images.google.com"; type = "Google Images"; break;
                            case KeywordRankEntityVO.TYPE_VIDEO:
                                url = "videos.google.com";type = "Google Videos";  break;
                            case KeywordRankEntityVO.TYPE_NEWS:
                                url = "news.google.com"; type = "Google News"; break;
                            case KeywordRankEntityVO.TYPE_LOCALLISTING:
                                url = "maps.google.com"; type = "Google Map"; break;
                            case KeywordRankEntityVO.TYPE_FLIGHTS:
                                url = "flights.google.com"; type = "Google Flights"; break;
                            default:
                                url = FormatUtils.getDomainByUrl(url); break;
                        }
                    }

                    defaultArr[44+2] = String.valueOf(keywordRankEntityVO.getRank()); // organic_results - rank
                    defaultArr[44+3] = String.valueOf(keywordRankEntityVO.getType() != KeywordRankEntityVO.TYPE_WEB_RESOURCE ? 0 : webRank); // organic_results - web rank
                    defaultArr[44+4] = type; // organic_results - type
                    defaultArr[45+4] = getStringValue(keywordRankEntityVO.getLabel()); // organic_results - title
                    defaultArr[46+4] = getStringValue(keywordRankEntityVO.getMetaDesc()); // organic_results - snippet
                    defaultArr[47+4] = getStringValue(url); // organic_results - link

                    if(StringUtils.equalsIgnoreCase(keywordRankEntityVO.getRating(), "Y")) {
                        defaultArr[48+4] = getStringValue(keywordRankEntityVO.getRatingNumber()); // organic_results - rating_number
                    }
                    if(StringUtils.isNotBlank(keywordRankEntityVO.getAmpFlg())) {
                        defaultArr[49+4] = getStringValue(keywordRankEntityVO.getAmpFlg()); // organic_results - mobile_friendly
                    }
                    csvWriter.write(defaultArr);
                    defaultArr[48+4] = "";
                    defaultArr[49+4] = "";
                    if(keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_WEB_RESOURCE) {
                        webRank++;
                    }
                }
			} else {
				System.out.println("Json:" + new Gson().toJson(keywordRankVO));
			}
            csvWriter.flush();
        } catch (Exception e) {
            e.printStackTrace();
            totalErrorCnt ++;
        }
    }
    
    private void logGeoLocations(Integer cityId, String location) {
    	if (cityId != null && cityId.intValue() > 0) {
    		if (!usedCityIdSet.contains(cityId)) {
    			usedCityIdSet.add(cityId);
    			System.out.println(" CityId:" + cityId + " name:" + location);
        	}
    	}
    }

    private List<KeywordRankEntityVO> getTargetType(KeywordRankVO keywordRankVO, int type) {
        List<KeywordRankEntityVO> list = new ArrayList<>();
        if (keywordRankVO == null || keywordRankVO.getKeywordRankEntityVOs() == null) {
        	 return list;
		}
        
        for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
            if(keywordRankEntityVO.getType() == type) {
                list.add(keywordRankEntityVO);
            }
        }
        return list;
    }

    private String getStringValue(Object str) {
        return (str == null || StringUtils.isBlank(String.valueOf(str))) ? "" : str.toString().replaceAll(BAD_CHAR, "");
    }
    
    static {
    	OLD_CITY_NAME_MAP.put(300073, "Buffalo, NY");
    	OLD_CITY_NAME_MAP.put(300097, "Albany, NY");
    	OLD_CITY_NAME_MAP.put(300099, "Fort Wayne, IN");
    	OLD_CITY_NAME_MAP.put(300396, "Illinois");
    	OLD_CITY_NAME_MAP.put(300457, "Lafayette, IN");
    	OLD_CITY_NAME_MAP.put(300492, "Tampa, FL");
    	OLD_CITY_NAME_MAP.put(300562, "Jacksonville, FL");
    	OLD_CITY_NAME_MAP.put(300681, "Louisiana");
    	OLD_CITY_NAME_MAP.put(300719, "Garden Grove, CA");
    	OLD_CITY_NAME_MAP.put(300805, "Huntington Beach, CA");
    	OLD_CITY_NAME_MAP.put(300809, "Statesboro, GA");
    	OLD_CITY_NAME_MAP.put(300887, "Arlington, VA");
    	OLD_CITY_NAME_MAP.put(300899, "Sacramento,California");
    	OLD_CITY_NAME_MAP.put(301039, "Long Island, NY");
    	OLD_CITY_NAME_MAP.put(301271, "Salt Lake City,Utah");
    	OLD_CITY_NAME_MAP.put(301374, "California");
    	OLD_CITY_NAME_MAP.put(301631, "Portland,Oregon");
    	OLD_CITY_NAME_MAP.put(301746, "Pennsylvania");
    	OLD_CITY_NAME_MAP.put(301871, "Detroit, MI");
    	OLD_CITY_NAME_MAP.put(301928, "Austin, TX");
    	OLD_CITY_NAME_MAP.put(302019, "Indianapolis, Indiana");
    	OLD_CITY_NAME_MAP.put(302079, "San Diego, CA");
    	OLD_CITY_NAME_MAP.put(302321, "Miami,Florida");
    	OLD_CITY_NAME_MAP.put(302381, "Georgia");
    	OLD_CITY_NAME_MAP.put(302406, "Texas");
    	OLD_CITY_NAME_MAP.put(302407, "Syracuse, NY");
    	OLD_CITY_NAME_MAP.put(302451, "South Carolina");
    	OLD_CITY_NAME_MAP.put(302529, "Jersey City, NJ");
    	OLD_CITY_NAME_MAP.put(302606, "New Jersey");
    	OLD_CITY_NAME_MAP.put(302702, "Winston-Salem, North Carolina");
    	OLD_CITY_NAME_MAP.put(302903, "Cleveland,Ohio");
    	OLD_CITY_NAME_MAP.put(302997, "Seatlle,Washington");
    	OLD_CITY_NAME_MAP.put(303344, "Michigan");
    	OLD_CITY_NAME_MAP.put(303348, "New London, CT");
    	OLD_CITY_NAME_MAP.put(303537, "Wilmington,North Carolina");
    	OLD_CITY_NAME_MAP.put(303651, "Oregon");
    	OLD_CITY_NAME_MAP.put(303703, "Louisville, KY");
    	OLD_CITY_NAME_MAP.put(303715, "Richmond, VA");
    	OLD_CITY_NAME_MAP.put(303721, "Orlando,Florida");
    	OLD_CITY_NAME_MAP.put(303887, "Raleigh, NC");
    	OLD_CITY_NAME_MAP.put(304005, "Alexandria, VA");
    	OLD_CITY_NAME_MAP.put(304088, "Providence, RI");
    	OLD_CITY_NAME_MAP.put(304170, "Florida");
    	OLD_CITY_NAME_MAP.put(304236, "New Hampshire");
    	OLD_CITY_NAME_MAP.put(304256, "Muskegon, MI");
    	OLD_CITY_NAME_MAP.put(304328, "Norfolk, VA");
    	OLD_CITY_NAME_MAP.put(304333, "Washington");
    	OLD_CITY_NAME_MAP.put(304390, "Huntington, WV");
    	OLD_CITY_NAME_MAP.put(304497, "Durham, NC");
    	OLD_CITY_NAME_MAP.put(304671, "Baltimore, MD");
    	OLD_CITY_NAME_MAP.put(304751, "Maryland");
    	OLD_CITY_NAME_MAP.put(304923, "New Mexico");
    	OLD_CITY_NAME_MAP.put(305147, "Massachusetts");
    	OLD_CITY_NAME_MAP.put(305212, "Connecticut");
    	OLD_CITY_NAME_MAP.put(305214, "Kentucky");
    	OLD_CITY_NAME_MAP.put(305286, "West Virginia");
    	OLD_CITY_NAME_MAP.put(305712, "Long Beach, CA");
    	OLD_CITY_NAME_MAP.put(305853, "North Carolina");
    	OLD_CITY_NAME_MAP.put(306047, "Elkhart, IN");
    	OLD_CITY_NAME_MAP.put(306078, "Colorado");
    	OLD_CITY_NAME_MAP.put(306197, "Indiana");
    	OLD_CITY_NAME_MAP.put(306417, "Ohio");
    	OLD_CITY_NAME_MAP.put(306494, "Maine");
    	OLD_CITY_NAME_MAP.put(306545, "Denver,Colorado");
    	OLD_CITY_NAME_MAP.put(306728, "Rochester, NY");
    	OLD_CITY_NAME_MAP.put(306816, "Delaware");
    	OLD_CITY_NAME_MAP.put(307056, "Minnesota");
    	OLD_CITY_NAME_MAP.put(307159, "Hagerstown, MD");
    	OLD_CITY_NAME_MAP.put(307420, "Plano, TX");
    	OLD_CITY_NAME_MAP.put(307485, "Nevada");
    	OLD_CITY_NAME_MAP.put(308019, "Brandon, FL");
    	OLD_CITY_NAME_MAP.put(308086, "Milwaukee, WI");
    	OLD_CITY_NAME_MAP.put(308285, "Bullhead City, AZ");
    	OLD_CITY_NAME_MAP.put(308971, "District of Columbia, USA");
    	OLD_CITY_NAME_MAP.put(309254, "New York, USA");
    }
    
    private static void createWorkFolderIfNotExist() {
    	try {
    		File extractFolder = new File(LOCAL_OUTPUT_FOLDER);
        	if (extractFolder == null || !extractFolder.isDirectory()) {
        		extractFolder.mkdirs();
        		System.out.println("Create Folder: " + LOCAL_OUTPUT_FOLDER);
    		}
        	
        	File extractBackupFolder = new File(storeDoneFilePath);
        	if (extractBackupFolder == null || !extractBackupFolder.isDirectory()) {
        		extractBackupFolder.mkdirs();
        		System.out.println("Create Folder: " + storeDoneFilePath);
    		}
		} catch (Exception e) {
			e.printStackTrace();
		}
    }
    
	private void renameFileAfterProcessDone(String fileFullPath) {
		File processFile = new File(fileFullPath);
		if (processFile == null || !processFile.isFile()) {
			System.out.println("File is not exist!!! file:" + fileFullPath);
			return;
		}

		String fileName = processFile.getName();
		String targetFileName = StringUtils.replace(fileName, ".csv", ".csv_backup" + FormatUtils.formatDate(new Date(), "yyyyMMddHHmmss"));
		System.out.println("targetFileName:" + targetFileName);

		try {
			File doneFolder = new File(storeDoneFilePath);
			if (doneFolder == null || !doneFolder.isDirectory()) {
				System.out.println("Target folder is not exist!!! folder:" + fileFullPath);
				doneFolder.mkdirs();
			}

			File targetFile = new File(doneFolder + "/" + targetFileName);
			FileUtils.moveFile(processFile, targetFile);
			System.out.println("Moved file from " + doneFolder.getAbsolutePath() + " to " + targetFile.getAbsolutePath());
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
    /*public void processForDomain(OwnDomainEntity ownDomainEntity) {
	    int domainId = ownDomainEntity.getId();
	    int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
	    int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
	    List<KeywordEntity> keywordList = keywordEntityDAO.getAllKeywordNameByDomainId(domainId);
	    if(CollectionUtils.isEmpty(keywordList)) {
	        System.out.println("keywordList is empty.");
	        return;
	    }
	    String key = engineId+"_"+languageId;
	    if(!engineKeywords.containsKey(key)) {	
	        engineKeywords.put(key, new HashSet<String>());
	    }
	    HashSet<String> existKeywords = engineKeywords.get(key);
	    for (KeywordEntity keywordEntity : keywordList) {
	        existKeywords.add(keywordEntity.getKeywordName());
	    }
	    engineKeywords.put(key, existKeywords);
	    engineInfo.put(key, ownDomainEntity);
    	System.out.println("  get "+key+" ==> "+ownDomainEntity.getSearchEngine()+", "+ownDomainEntity.getSearchEngineCountry()+", "+ownDomainEntity.getLanguage());
	}*/
    
    /*private void processSignalKeyword(KeywordRankVO keywordRankVO, CsvWriter csvWriter, OwnDomainEntity ownDomainEntity, String device, String location) {   	
    	String[] defaultArr = new String[headers.length];
    	String engineStr = ownDomainEntity.getSearchEngine();
        String country = ownDomainEntity.getSearchEngineCountry();
         String languageStr = ownDomainEntity.getLanguage();
        String deviceStr = DEVICE_EXPORT_STR_DESKTOP;
        if (DEVICE_MOBILE.equalsIgnoreCase(device)) {
            deviceStr = DEVICE_EXPORT_STR_MOBILE;
        }
        
        if (keywordRankVO.getCityId() > 0) {
			System.out.println(" City Id:" + keywordRankVO.getCityId() + ", Location:" + location);
		}
        
    	try {
    		if (StringUtils.equalsIgnoreCase(ownDomainEntity.getSearchEngineCountry(), "us")) {
    			totalUSCnt ++;
			} else if (StringUtils.equalsIgnoreCase(ownDomainEntity.getSearchEngineCountry(), "uk")) {
				totalUKCnt ++;
			} else if (StringUtils.equalsIgnoreCase(ownDomainEntity.getSearchEngineCountry(), "br")) {
				totalBRCnt ++;
			}
    		
            for (int i = 0; i < headers.length; i++) {
                defaultArr[i] = "";
            }
//            System.out.println(" MatchKW:" + keywordRankVO.getKeyword());
//        SeoClarityKeywordEntity clarityKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(keywordRankVO.getKeyword());
//        if(null == clarityKeywordEntity) {
//            System.out.println("ERROR : can't find rankcheck id : "+keywordRankVO.getKeyword());
//            continue;
//        }
            Integer id = keywordRankVO.getId();
//        SeoClarityKeywordAdwordsEntity adwordsEntity = seoClarityKeywordAdwordsEntityDAO.getExistedEntity(id, 99, languageId, 0);
            String sv = StringUtils.isNotBlank(keywordRankVO.getSearchVol()) ? keywordRankVO.getSearchVol() : "";
            sv = sv.replaceAll(",", "");
            String cpc = StringUtils.isNotBlank(keywordRankVO.getCpc()) ? keywordRankVO.getCpc() : "";
            cpc = cpc.replaceAll(",", "");

            // search_metadata - id
            // search_metadata - status
            defaultArr[0] = getStringValue(id);
            defaultArr[1] = "Success";
            defaultArr[2] = processDateStr;

//            String qKeyword = FormatUtils.decodeKeyword(keywordRankVO.getKeyword());
            String qKeyword = keywordRankVO.getKeyword();
            // search_parameters - engine
            // search_parameters - google_domain
            // search_parameters - gl
            // search_parameters - hl
            // search_parameters - device
            // search_parameters - q
            // search_parameters - location_requested
            defaultArr[2+1] = "google";
            defaultArr[3+1] = engineStr;
            defaultArr[4+1] = country;
            defaultArr[5+1] = languageStr;
            defaultArr[6+1] = deviceStr;
            defaultArr[7+1] = qKeyword;
            defaultArr[8+1] = location;

            String totalResult = keywordRankVO.getGoogleResultCnt();
            totalResult = StringUtils.isBlank(totalResult) ? "" : totalResult.replaceAll(",", "").replaceAll("\\.", "");
            // search_information - total_results
            // search_information - app_flag
            // search_information - local_map_flag
            // search_information - shop_flag
            // search_information - ads_flag
            // search_information - answer_box_flag
            // search_information - cpc
            // search_information - search_volume
            // search_information - commercial_flag
            // search_information - flights_flag
            // search_information - google_research_flag
            // search_information - top_ppc_count
            // search_information - social_in_knowledge
            // search_information - pla_flag
            // search_information - lpu
            defaultArr[9+1] = getStringValue(totalResult);
            defaultArr[10+1] = StringUtils.isNotBlank(keywordRankVO.getAppFlg()) ? keywordRankVO.getAppFlg() : "N";
            defaultArr[11+1] = getStringValue(keywordRankVO.getLlFlg());
            defaultArr[12+1] = getStringValue(keywordRankVO.getPpcFlg());
            defaultArr[13+1] = StringUtils.isNotBlank(keywordRankVO.getAnswerBox()) ? "Y" : "N";
            defaultArr[14+1] = getStringValue(cpc);
            defaultArr[15+1] = getStringValue(sv);
            defaultArr[16+1] = getStringValue(keywordRankVO.getCommercialFlg());
            defaultArr[17+1] = getStringValue(keywordRankVO.getFlightSearchFlg());
            defaultArr[18+1] = getStringValue(keywordRankVO.getResearchFlg());
            defaultArr[19+1] = getStringValue(StringUtils.isBlank(keywordRankVO.getTopPpcCnt()) ? "0" : keywordRankVO.getTopPpcCnt());
            defaultArr[20+1] = StringUtils.isNotBlank(keywordRankVO.getSocialInKg()) ? keywordRankVO.getSocialInKg() : "N";
            defaultArr[21+1] = StringUtils.isNotBlank(keywordRankVO.getPlaFlg()) ? keywordRankVO.getPlaFlg() : "N";
            defaultArr[22+1] = getStringValue(keywordRankVO.getKnogTag());

            if(StringUtils.isNotBlank(keywordRankVO.getAnswerBox()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getAnswerBox(), "null")) {
                defaultArr[23+1] = keywordRankVO.getAnswerBox();
                // answer_box_url
            }

            if(StringUtils.isNotBlank(keywordRankVO.getGoogleRecommend()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getGoogleRecommend(), "-")) {
                defaultArr[24+1] = keywordRankVO.getGoogleRecommend();
                // google_recommend
            }

            if(StringUtils.isNotBlank(keywordRankVO.getRefineBy()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getRefineBy(), "-")) {
                String[] arr = StringUtils.split(keywordRankVO.getRefineBy(), "!_!");
                // refine_by - title
                // refine_by - details
                if(arr != null && arr.length >= 2) {
                	String[] sarray = arr[1].split(" ");
                	if(sarray != null && sarray.length >= 2) {
                		for (String s : sarray) {
                			defaultArr[25+1] = getStringValue(arr[0]);
                            defaultArr[26+1] = getStringValue(s);
                            csvWriter.write(defaultArr);
                		}
                	}
                    defaultArr[25+1] = "";
                    defaultArr[26+1] = "";
                }
            }

            if(StringUtils.isNotBlank(keywordRankVO.getJobLink()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getJobLink(), "-")) {
                String[] arr = StringUtils.split(keywordRankVO.getJobLink(), "@_@");
                if(arr != null && arr.length >= 2) {
                    // google_job - link
                    // google_job - details
                	String[] sarray = arr[1].split(" ");
                	if(sarray != null && sarray.length >= 2) {
                		for (String s : sarray) {
                			 defaultArr[27+1] = getStringValue(arr[0]);
                             defaultArr[28+1] = getStringValue(s);
                             csvWriter.write(defaultArr);
                		}
                	}
                    defaultArr[27+1] = "";
                    defaultArr[28+1] = "";
                }
            }

            List<KeywordRankEntityVO> tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_LOCALLISTING);
            if(CollectionUtils.isNotEmpty(tmpList)) {
                defaultArr[29+1] = tmpList.get(0).getLandingPage();
                defaultArr[30+1] = tmpList.get(0).getLabel();

                if(CollectionUtils.isNotEmpty(keywordRankVO.getLocalListing())) {
                    for (int i = 0; i < keywordRankVO.getLocalListing().size(); i++) {
                        String[] s = keywordRankVO.getLocalListing().get(i).split("!_!");
                        if(s.length == 0 || StringUtils.isBlank(s[0])) {
                            continue;
                        }
                        defaultArr[31+1] = String.valueOf(i+1);
                        defaultArr[32+1] = getStringValue(s[0]);
                        csvWriter.write(defaultArr);
                    }
                    defaultArr[31+1] = "";
                    defaultArr[32+1] = "";
                }
                // local_map - link
                // local_map - title
                // local_map - places - rank
                // local_map - places - title
                defaultArr[29+1] = "";
                defaultArr[30+1] = "";
            }

            
            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_VIDEO);
            if(CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if(CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[33+1] = String.valueOf(keywordSubRankEntityVO.getSubRank());
                        defaultArr[34+1] =  getStringValue(keywordSubRankEntityVO.getLabel());
                        defaultArr[35+1] = getStringValue(keywordSubRankEntityVO.getLandingPage());
                        csvWriter.write(defaultArr);
                    }
                }
                defaultArr[33+1] = "";
                defaultArr[34+1] = "";
                defaultArr[35+1] = "";
                // inline_videos - rank
                // inline_videos - title
                // inline_videos - link
            }

            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_IMGAGE);
            if(CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if(CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[36+1] = String.valueOf(keywordSubRankEntityVO.getSubRank());
                        defaultArr[37+1] =  getStringValue(keywordSubRankEntityVO.getLabel());
                        defaultArr[38+1] = getStringValue(keywordSubRankEntityVO.getLandingPage());
                        csvWriter.write(defaultArr);
                    }
                }
                // inline_images - rank
                // inline_images - title
                // inline_images - link
                defaultArr[36+1] = "";
                defaultArr[37+1] = "";
                defaultArr[38+1] = "";
            }

            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_NEWS);
            if(CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if(CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[39+1] = String.valueOf(keywordSubRankEntityVO.getSubRank());
                        defaultArr[40+1] =  getStringValue(keywordSubRankEntityVO.getLabel());
                        defaultArr[41+1] = getStringValue(keywordSubRankEntityVO.getLandingPage());
                        csvWriter.write(defaultArr);
                    }
                }
                // top_stories - rank
                // top_stories - title
                // top_stories - link
                defaultArr[39+1] = "";
                defaultArr[40+1] = "";
                defaultArr[41+1] = "";
            }

            if(StringUtils.isNotBlank(keywordRankVO.getAdditionalQuestions()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getAdditionalQuestions(), "-")) {
                String[] qArr = keywordRankVO.getAdditionalQuestions().split("@_@");
                if(qArr.length > 0) {
                    for (String s : qArr) {
                        String[] tArr = s.split("!_!");
                        
                        if(tArr != null && tArr.length >= 2) {
                        	 defaultArr[42+1] = getStringValue(tArr[1]);
                             defaultArr[43+1] = getStringValue(tArr[0]);
                             csvWriter.write(defaultArr);
                        }
                    }
                }
            } else if(StringUtils.isNotBlank(keywordRankVO.getQuestions()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getQuestions(), "-")) {
                String[] qArr = keywordRankVO.getQuestions().split("!_!");
                if(qArr.length > 0) {
                    for (String s : qArr) {
                        defaultArr[42+1] = getStringValue(s);
                        defaultArr[43+1] = "";
                        csvWriter.write(defaultArr);
                    }
                }
            }
            // related_questions - title
            // related_questions - link
            defaultArr[42+1] = "";
            defaultArr[43+1] = "";

            int webRank = 1;
            
            if (keywordRankVO != null && keywordRankVO.getKeywordRankEntityVOs() != null ) {
            	for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
                    String type = "Web Url";
                    String url = keywordRankEntityVO.getLandingPage();
                    if(url.contains(".google.com")) {
                        switch (keywordRankEntityVO.getType()) {
                            case KeywordRankEntityVO.TYPE_IMGAGE:
                                url = "images.google.com"; type = "Google Images"; break;
                            case KeywordRankEntityVO.TYPE_VIDEO:
                                url = "videos.google.com";type = "Google Videos";  break;
                            case KeywordRankEntityVO.TYPE_NEWS:
                                url = "news.google.com"; type = "Google News"; break;
                            case KeywordRankEntityVO.TYPE_LOCALLISTING:
                                url = "maps.google.com"; type = "Google Map"; break;
                            case KeywordRankEntityVO.TYPE_FLIGHTS:
                                url = "flights.google.com"; type = "Google Flights"; break;
                            default:
                                url = FormatUtils.getDomainByUrl(url); break;
                        }
                    }

                    defaultArr[44+1] = String.valueOf(keywordRankEntityVO.getRank());
                    defaultArr[44+2] = String.valueOf(keywordRankEntityVO.getType() != KeywordRankEntityVO.TYPE_WEB_RESOURCE ? 0 : webRank);
                    defaultArr[44+3] = type;
                    defaultArr[45+3] = getStringValue(keywordRankEntityVO.getLabel());
                    defaultArr[46+3] = getStringValue(keywordRankEntityVO.getMetaDesc());
                    defaultArr[47+3] = getStringValue(url);

                    if(StringUtils.equalsIgnoreCase(keywordRankEntityVO.getRating(), "Y")) {
                        defaultArr[48+3] = getStringValue(keywordRankEntityVO.getRatingNumber());
                    }
                    if(StringUtils.isNotBlank(keywordRankEntityVO.getAmpFlg())) {
                        defaultArr[49+3] = getStringValue(keywordRankEntityVO.getAmpFlg());
                    }
                    csvWriter.write(defaultArr);
                    defaultArr[48+3] = "";
                    defaultArr[49+3] = "";
                    if(keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_WEB_RESOURCE) {
                        webRank++;
                    }
                }
                // organic_results - rank
                // organic_results - web rank
                // organic_results - type
                // organic_results - title
                // organic_results - snippet
                // organic_results - link
                // organic_results - rating_number
                // organic_results - mobile_friendly
			} else {
				System.out.println("Json:" + new Gson().toJson(keywordRankVO));
			}
            
//        String serpJson = JSONUtil.toJsonStr(object);
            csvWriter.flush();
        } catch (Exception e) {
            e.printStackTrace();
            
            totalErrorCnt ++;
        }
    }*/
}