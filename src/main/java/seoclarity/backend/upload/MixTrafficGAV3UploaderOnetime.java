package seoclarity.backend.upload;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import seoclarity.backend.dao.actonia.AutoRunInstanceEntityDAO;
import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.clickhouse.ga.GaClarityDBEntityDAO;
import seoclarity.backend.entity.ClarityDBConstants;
import seoclarity.backend.entity.actonia.AutorunInfoEntity;
import seoclarity.backend.entity.actonia.AutorunInstanceEntity;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.clickhouse.ga.GoogleAnalyticsEntity;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * 
 * <AUTHOR>
 * @date 2017-12-29
 * seoclarity.backend.upload.MixTrafficGAV3Uploader
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.MixTrafficGAV3Uploader" -Dexec.args=""
 * upload to clarityDB
 */
public class MixTrafficGAV3UploaderOnetime {
	
	private static final String databaseName = "actonia_site_analytics";
	private static final String finalTableName = "test_local_20240524";
	

	public static final Log logger = LogFactory.getLog(MixTrafficGAV3UploaderOnetime.class);

	private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

	public static final int TARGETURL_ADDEDBY_GA = 3;

	public static final int TARGETURL_ADDEDBY_CONVERSION = 99;
	
	private GaClarityDBEntityDAO gaClarityDBEntityDAO;
	
	public MixTrafficGAV3UploaderOnetime() {
		
		gaClarityDBEntityDAO = SpringBeanFactory.getBean("gaClarityDBEntityDAO");
		
	}

	public static void main(String args[]) {
		
		
		threadPool.init();
		CommonUtils.initThreads(10);
		MixTrafficGAV3UploaderOnetime crawlGoogalAnalyticsEngine = new MixTrafficGAV3UploaderOnetime();
		
		try {
			Thread.sleep(5000);
			crawlGoogalAnalyticsEngine.process();
		} catch (Exception e) {
			e.printStackTrace();
		}
		threadPool.destroy();
	}
	
	
	private void process() {
		
		File tmpFile = new File("/home/<USER>/6631_traffic_mix.txt");
		
		System.out.println("=====processing file:" + tmpFile.getAbsolutePath());
		try {
			processFile(tmpFile);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
			
		
	}
	
	
    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n').withHeader(GaClarityDBEntityDAO.columns);
    private final int maxInsertCount = 100000;

	private void processFile(File tmpFile) throws Exception {

      try {
          FileReader fr = new FileReader(tmpFile);
          CSVParser csvParser = new CSVParser(fr, csvFormat);

          List<GoogleAnalyticsEntity> gaEntities = new ArrayList<>();
          // don't read header
          List<CSVRecord> csvRecords = csvParser.getRecords();
          for (int i = 1; i < csvRecords.size(); i++) {
              CSVRecord csvRecord = csvRecords.get(i);
              GoogleAnalyticsEntity gaEntity;
              try {
                  gaEntity = getGaEntity(csvRecord);
              } catch (Exception e) {
                  System.out.println("line i : " + i);
                  e.printStackTrace();
                  continue;
              }
              gaEntities.add(gaEntity);
              if (gaEntities.size() >= maxInsertCount) {
            	  gaClarityDBEntityDAO.insertForBatch(gaEntities, finalTableName);
                  System.out.println("finish insert for top : " + maxInsertCount + " for file :" + tmpFile.getName());
                  gaEntities.clear();
              	}
          	}
          	if (CollectionUtils.isNotEmpty(gaEntities)) {
          		gaClarityDBEntityDAO.insertForBatch(gaEntities, finalTableName);
              	System.out.println("finish insert for left count :" + gaEntities.size());
              	gaEntities.clear();
          	}

          	csvParser.close();
          	fr.close();
      } catch (Exception e) {
      		e.printStackTrace();
      		return;
      }



	}
	
	
	private GoogleAnalyticsEntity getGaEntity(CSVRecord csvRecord) throws ParseException {
		GoogleAnalyticsEntity gaEntity = new GoogleAnalyticsEntity();
		
		gaEntity.setDomainId(NumberUtils.toInt(csvRecord.get("domain_id")));
        gaEntity.setLogDate(csvRecord.get("log_date"));
        gaEntity.setKeywordText(csvRecord.get("keyword_text"));
        gaEntity.setHostName(csvRecord.get("host_name"));
        gaEntity.setUri(csvRecord.get("uri"));
        gaEntity.setMedium(csvRecord.get("medium"));
        gaEntity.setSource(csvRecord.get("source"));
        gaEntity.setDevice(csvRecord.get("device"));
        gaEntity.setVersoin(NumberUtils.toInt(csvRecord.get("versoin")));
        gaEntity.setCountry(csvRecord.get("country"));
        gaEntity.setEntrances(NumberUtils.toLong(csvRecord.get("entrances")));
        gaEntity.setPageviews(NumberUtils.toLong(csvRecord.get("pageviews")));
        gaEntity.setExits(NumberUtils.toLong(csvRecord.get("exits")));
        gaEntity.setBounces(NumberUtils.toLong(csvRecord.get("bounces")));
        gaEntity.setSessionDuration(NumberUtils.toFloat(csvRecord.get("session_duration")));
        gaEntity.setTimeOnPage(NumberUtils.toFloat(csvRecord.get("time_on_page")));
        gaEntity.setTransactions(NumberUtils.toLong(csvRecord.get("transactions")));
        gaEntity.setItemRevenue(NumberUtils.toFloat(csvRecord.get("item_revenue")));
		gaEntity.setGoal1completions(NumberUtils.toLong(csvRecord.get("goal1completions")));
        gaEntity.setGoal2completions(NumberUtils.toLong(csvRecord.get("goal2completions")));
        gaEntity.setGoal3completions(NumberUtils.toLong(csvRecord.get("goal3completions")));
        gaEntity.setGoal4completions(NumberUtils.toLong(csvRecord.get("goal4completions")));
        gaEntity.setGoal5completions(NumberUtils.toLong(csvRecord.get("goal5completions")));
        gaEntity.setGoal6completions(NumberUtils.toLong(csvRecord.get("goal6completions")));
        gaEntity.setGoal7completions(NumberUtils.toLong(csvRecord.get("goal7completions")));
        gaEntity.setGoal8completions(NumberUtils.toLong(csvRecord.get("goal8completions")));
        gaEntity.setGoal9completions(NumberUtils.toLong(csvRecord.get("goal9completions")));
        gaEntity.setGoal10completions(NumberUtils.toLong(csvRecord.get("goal10completions")));
        gaEntity.setGoal11completions(NumberUtils.toLong(csvRecord.get("goal11completions")));
        gaEntity.setGoal12completions(NumberUtils.toLong(csvRecord.get("goal12completions")));
        gaEntity.setGoal13completions(NumberUtils.toLong(csvRecord.get("goal13completions")));
        gaEntity.setGoal14completions(NumberUtils.toLong(csvRecord.get("goal14completions")));
        gaEntity.setGoal15completions(NumberUtils.toLong(csvRecord.get("goal15completions")));
        gaEntity.setGoal16completions(NumberUtils.toLong(csvRecord.get("goal16completions")));
        gaEntity.setGoal17completions(NumberUtils.toLong(csvRecord.get("goal17completions")));
        gaEntity.setGoal18completions(NumberUtils.toLong(csvRecord.get("goal18completions")));
        gaEntity.setGoal19completions(NumberUtils.toLong(csvRecord.get("goal19completions")));
        gaEntity.setGoal20completions(NumberUtils.toLong(csvRecord.get("goal20completions")));
        gaEntity.setGoal1Value(NumberUtils.toFloat(csvRecord.get("goal1Value")));
        gaEntity.setGoal2Value(NumberUtils.toFloat(csvRecord.get("goal2Value")));
        gaEntity.setGoal3Value(NumberUtils.toFloat(csvRecord.get("goal3Value")));
        gaEntity.setGoal4Value(NumberUtils.toFloat(csvRecord.get("goal4Value")));
        gaEntity.setGoal5Value(NumberUtils.toFloat(csvRecord.get("goal5Value")));
        gaEntity.setGoal6Value(NumberUtils.toFloat(csvRecord.get("goal6Value")));
        gaEntity.setGoal7Value(NumberUtils.toFloat(csvRecord.get("goal7Value")));
        gaEntity.setGoal8Value(NumberUtils.toFloat(csvRecord.get("goal8Value")));
        gaEntity.setGoal9Value(NumberUtils.toFloat(csvRecord.get("goal9Value")));
        gaEntity.setGoal10Value(NumberUtils.toFloat(csvRecord.get("goal10Value")));
        gaEntity.setGoal11Value(NumberUtils.toFloat(csvRecord.get("goal11Value")));
        gaEntity.setGoal12Value(NumberUtils.toFloat(csvRecord.get("goal12Value")));
        gaEntity.setGoal13Value(NumberUtils.toFloat(csvRecord.get("goal13Value")));
        gaEntity.setGoal14Value(NumberUtils.toFloat(csvRecord.get("goal14Value")));
        gaEntity.setGoal15Value(NumberUtils.toFloat(csvRecord.get("goal15Value")));
        gaEntity.setGoal16Value(NumberUtils.toFloat(csvRecord.get("goal16Value")));
        gaEntity.setGoal17Value(NumberUtils.toFloat(csvRecord.get("goal17Value")));
        gaEntity.setGoal18Value(NumberUtils.toFloat(csvRecord.get("goal18Value")));
        gaEntity.setGoal19Value(NumberUtils.toFloat(csvRecord.get("goal19Value")));
        gaEntity.setGoal20Value(NumberUtils.toFloat(csvRecord.get("goal20Value")));
        gaEntity.setDataSourceType(NumberUtils.toInt(csvRecord.get("data_source_type")));
        gaEntity.setUrl(csvRecord.get("url"));
		
        return gaEntity;
    }
	

	

	public Set<Integer> getDomainSet(String domainIds) {
		Set<Integer> returnDomain = new HashSet<Integer>();
		String[] dIds = domainIds.split(",");

		if (dIds != null && dIds.length > 0) {
			for (int i = 0; i < dIds.length; i++) {
				try {
					returnDomain.add(Integer.parseInt(dIds[i]));
				} catch (Exception e) {
					
				}
			}
		}
		return returnDomain;
	}
	
	

}
