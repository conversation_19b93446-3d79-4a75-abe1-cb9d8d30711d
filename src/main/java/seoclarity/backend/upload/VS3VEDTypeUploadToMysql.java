package seoclarity.backend.upload;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.Vs3vedTypeInfoDAO;
import seoclarity.backend.entity.actonia.Vs3VedTypeInfoEntity;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 每天6点执行
 * <p>
 * s15  ：  0 6 * * *  /home/<USER>/source/adhocExtract/clarity-backend-scripts/VS3VEDDailyUpload.sh
 * nohup mvn  exec:java -Dexec.mainClass="seoclarity.backend.upload.VS3VEDTypeUploadToMysql" -Dexec.args="screenshot/20240324" -Dexec.cleanupDaemonThreads=false >> 20240324.log&
 * tail -f 20240324.log
 */
public class VS3VEDTypeUploadToMysql {

    public static final String ALI_ACCESS_KEY = "LTAI4GGYqGWn99UKbJsBgyQ9";
    public static final String ALI_SECRET_KEY = "******************************";
    public static final String ALI_BUCKET_NAME = "search-engine-template";
    private static AWSCredentials aliCredentials = new BasicAWSCredentials(ALI_ACCESS_KEY, ALI_SECRET_KEY);
    private static AmazonS3 aliS3Client;
    //    private String defaultPrefix = "screenshot/20230326/1-1";
    private String defaultPrefix = "screenshot/20240121";
    private static SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
    //Path : screenshot/20231204/1-1/desktop/115260/5/006cd791-ffde-48d6-98df-dedae0eed70e.jpg
    //screenshot/{version}/{engine}-{language}/{device}/{type_main_key}/{type}/{fileName}

    private Vs3vedTypeInfoDAO vs3vedTypeInfoDAO;

    public VS3VEDTypeUploadToMysql() {
        vs3vedTypeInfoDAO = SpringBeanFactory.getBean("vs3vedTypeInfoDAO");
    }

    private void process(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            prefix = defaultPrefix;
        }
        System.out.println("prefix :   " + prefix);

        aliS3Client = AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(aliCredentials))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration("oss-us-east-1.aliyuncs.com", "oss")).build();

//        File outFile = new File("i:\\ved\\aliyun_0316.txt");

        try {
            String nextMarker = null;
            ObjectListing objectListing;

            do {
                List<String> lines = new ArrayList<>();
                ListObjectsRequest listObjectsRequest = new ListObjectsRequest().withBucketName(ALI_BUCKET_NAME).withPrefix(
                        prefix).withMarker(nextMarker);
                objectListing = aliS3Client.listObjects(listObjectsRequest);

                List<S3ObjectSummary> sums = objectListing.getObjectSummaries();
                List<Vs3VedTypeInfoEntity> insertList = new ArrayList<>();
                for (S3ObjectSummary s : sums) {
                    // 下载keyword
                    System.out.println("\t" + s.getKey()); //screenshot/20240114/1-1/desktop/10041/14/008f9d16-a597-46b4-8880-bd9b074395e1.jpg
                    lines.add(s.getKey());
                    String key = s.getKey();
                    System.out.println("===key:" + key);
                    String[] arr = key.split("/");
                    String keyPrefix = arr[0];
                    String version = arr[1];
                    String engineLanguage = arr[2];
                    String[] enginelanguageStr = engineLanguage.split("-");
                    int engineId = Integer.parseInt(enginelanguageStr[0]);
                    int languageId = Integer.parseInt(enginelanguageStr[1]);
                    String device = arr[3];
                    if (StringUtils.isBlank(device)) {
                        device = "dm";
                    } else if (device.equals("desktop")) {
                        device = "d";
                    } else if (device.equals("mobile")) {
                        device = "m";
                    }
                    ;
                    String type_main_key = arr[4];
                    String type = arr[5];
                    String imageName = arr[6];

                    if (StringUtils.isBlank(type_main_key)) {
                        System.out.println("===skip key:" + key);
                        continue;
                    }
                    String typeArray = type_main_key.replaceAll("-", ",");

//                    String[] typeArray = type_main_key.split("-");
//                    List<String> typeList = new ArrayList<>(typeArray.length);
//                    Collections.addAll(typeList, typeArray);

                    /**
                     * 获取文件

                     GetObjectRequest rangeObjectRequest = new GetObjectRequest(ALI_BUCKET_NAME, key);
                     S3Object objectPortion = aliS3Client.getObject(rangeObjectRequest);
                     Map<String, String> metaMap = objectPortion.getObjectMetadata().getUserMetadata();
                     String osskeypath = metaMap.get("osskeypath");
                     log.info("====osskeypath:" + osskeypath);
                     String base64Rank = metaMap.get("rank");
                     byte[] textByte = base64Rank.getBytes("UTF-8");
                     String decodeRank = new String(Base64.getDecoder().decode(textByte), "UTF-8");
                     log.info("====decodeRank:" + decodeRank);

                     key = key.replace("/", "-");
                     String filePath = "D:\\workspace\\extract\\s3\\" + key;
                     File outFile = new File(filePath);
                     if (!outFile.exists()) {
                     outFile.createNewFile();
                     }
                     if (objectPortion != null && objectPortion.getObjectContent() != null) {
                     InputStream objectData = objectPortion.getObjectContent();
                     FileOutputStream fos = new FileOutputStream(outFile);
                     byte[] readBuf = new byte[1024];
                     int readLen;
                     while ((readLen = objectData.read(readBuf)) > 0) {
                     fos.write(readBuf, 0, readLen);
                     }
                     objectData.close();
                     fos.close();
                     System.out.println("===downloadFile success : " + outFile.getAbsolutePath());
                     }
                     */

                    Vs3VedTypeInfoEntity vedTypeInfoEntity = new Vs3VedTypeInfoEntity();
                    vedTypeInfoEntity.setVersion(version);
                    vedTypeInfoEntity.setDevice(device);
                    vedTypeInfoEntity.setImageFileName(imageName);
                    String imageNameMurmurHash = MurmurHashUtils.getMurmurHash3_64(imageName);
                    vedTypeInfoEntity.setImageFileNameMurmurHash(imageNameMurmurHash);
                    vedTypeInfoEntity.setTypeMainKey(typeArray);
                    vedTypeInfoEntity.setType(Integer.parseInt(type));
                    vedTypeInfoEntity.setEngineId(engineId);
                    vedTypeInfoEntity.setLanguageId(languageId);
                    vedTypeInfoEntity.setCreateDate(new Date());
                    String murmurHash = MurmurHashUtils.getMurmurHash3_64(typeArray);
                    vedTypeInfoEntity.setVedTypeMurmurHash(murmurHash);
                    System.out.println("===keyPrefix:" + keyPrefix + ",version:" + version + " , engineLanguage : " + engineLanguage + ",device:" + device
                            + ",TypeMainKey:" + typeArray + "type :  " + type + ",imageName:" + imageName);
                    insertList.add(vedTypeInfoEntity);

                }
                System.out.println("===insertList size:" + insertList.size());
                vs3vedTypeInfoDAO.insertBatch(insertList);
                nextMarker = objectListing.getNextMarker();
                System.out.println("===nextMarker:" + nextMarker);
//                FileUtils.writeLines(outFile, lines, true);

//                break;
            } while (objectListing.isTruncated());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (aliS3Client != null) {
                aliS3Client.shutdown();
            }
        }

    }

    public static void main(String[] args) {
        VS3VEDTypeUploadToMysql vs3VEDTypeUpload = new VS3VEDTypeUploadToMysql();
        boolean checkVersion = true;
        String prefix = "";
        if (null != args && args.length > 0 && StringUtils.isNotBlank(args[0])) {
            prefix = args[0];
        } else {
//            Calendar cal = Calendar.getInstance();
//            cal.add(Calendar.DAY_OF_MONTH, -1);
//            String yesterday = YYYYMMDD.format(cal.getTime());
            String lastSunday = getLastSunday(); // 5/23 修改 每周日跑上个周日的数据
            prefix = "screenshot/" + lastSunday;
            System.out.println("===###获取上个周日作为version  , version : " + prefix);
//            System.out.println("===###比较 昨今天的version 是否大于数据库中的最大version ,");
//            checkVersion = vs3VEDTypeUpload.checkVersion(prefix);
        }
        System.out.println("===###VEDType : " + prefix + " , checkVersion :" + checkVersion);


        if (checkVersion) {
            vs3VEDTypeUpload.process(prefix);
        } else {
            System.out.println("===###Version : 当前version 不需要更新");
        }

    }

    private boolean checkVersion(String prefix) {
        String maxVersion = vs3vedTypeInfoDAO.checkMaxVersion();
        System.out.println("===###Version : 当前version 为 ： " + maxVersion);
        String date = prefix.substring(prefix.lastIndexOf("/") + 1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate localMaxVersion = LocalDate.parse(maxVersion, formatter);
        LocalDate localVersion = LocalDate.parse(date, formatter);
        return localVersion.isAfter(localMaxVersion);

    }


    public static String getLastSunday() {
        // 获取当前日期的Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 将日期调整到本周的周日
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        // 如果今天就是周日，我们需要再减去7天以到达上个周日
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            calendar.add(Calendar.WEEK_OF_YEAR, -1);
        }

//        // 创建一个SimpleDateFormat对象用于格式化日期
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        // 返回上个周日的日期字符串
        return YYYYMMDD.format(calendar.getTime());
    }

}
