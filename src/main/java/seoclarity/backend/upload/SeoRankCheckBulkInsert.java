package seoclarity.backend.upload;

import java.sql.Connection;

import org.apache.commons.lang.ArrayUtils;

import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;

public class SeoRankCheckBulkInsert {
	public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

	public static int getTotlaPageCount(int totalCount, int pageSize) {
		if (totalCount % pageSize == 0) {
			return totalCount / pageSize;
		} else {
			return totalCount / pageSize + 1;
		}
	}

	public void seoInsertKeywordRank(KeywordRankVO[] dataList, int searchEngine, int language, boolean mobileRanking,
			boolean hotCluster, Connection connection) throws Exception {
		System.out.println("Start Multi-Thread, List size:" + dataList.length);

		int pageNum = 1;
		int pageSize = 150;
		int totalCount = dataList.length;
		int totalPageCount = getTotlaPageCount(totalCount, pageSize);

		do {
			if (totalPageCount == 0) {
				System.out.println("no reocrdd");
				return;
			}

			String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}

			if (dataList.length < pageSize) {
				pageSize = dataList.length;
			}

			KeywordRankVO[] commandData = (KeywordRankVO[]) ArrayUtils.subarray(dataList, 0, pageSize);
			for (int removeIndex = 0; removeIndex <= pageSize - 1; removeIndex++) {
				dataList = (KeywordRankVO[]) ArrayUtils.remove(dataList, 0);
			}
			SeoRankBulkCommand crawCommand = getCommand(ipAddress, commandData, searchEngine, language, mobileRanking,
					hotCluster, connection);

			try {
				threadPool.execute(crawCommand);
			} catch (Exception e) {
				e.printStackTrace();
			}

			pageNum++;

		} while (pageNum <= totalPageCount);

		do {
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
	}

	private SeoRankBulkCommand getCommand(String ip, KeywordRankVO[] rankData, int searchEngine, int language,
			boolean mobileRanking, boolean hotCluster, Connection connection) {
		SeoRankBulkCommand crawlCommand = new SeoRankBulkCommand(ip, rankData, searchEngine, language, mobileRanking,
				hotCluster, connection);
		crawlCommand.setStatus(true);
		return crawlCommand;
	}
}
