package seoclarity.backend.upload;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.dao.actonia.SeoSysLogEntityDAO;
import seoclarity.backend.dao.clickhouse.fortest.LocalKeywordSummaryAnnualBigDAO;
import seoclarity.backend.dao.clickhouse.fortest.LocalKeywordSummaryAnnualNormalDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.RGKeywordStreamDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.RGKeywordStreamEntity;
import seoclarity.backend.dao.rankcheck.AdwordsGeoIdDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.KeywordStreamSearchengineCountryMappingEntity;
import seoclarity.backend.entity.actonia.AdwordsGeoIdTableEntity;
import seoclarity.backend.entity.actonia.ContentJsonPOJO;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.upload
 * @author: cil
 * @date: 2021-07-08 10:33
 **/
public class RGKeywordExpansionV4 {

    private SeoSysLogEntityDAO seoSysLogEntityDAO;
    //    private KeywordSummaryAnnualBigDAO keywordSummaryAnnualBigDAO;
//    private KeywordSummaryAnnualNormalDAO keywordSummaryAnnualNormalDAO;
    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
    private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;

    //new datasource local test
    private LocalKeywordSummaryAnnualBigDAO keywordSummaryAnnualBigDAO;
    private LocalKeywordSummaryAnnualNormalDAO keywordSummaryAnnualNormalDAO;
    private RGKeywordStreamDAO rgKeywordStreamDAO;
    private AdwordsGeoIdDAO adwordsGeoIdDAO;
    private static final String ENCODE_FILE_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/SEO_ExpandForSV_20210726.txt";
    private static final String DECODE_FILE_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/SEO_Expand_20210726.txt";
    private static final String SEARCH_AND_COUNTRY_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/search_and_country_20210721.txt";
    private static final String SURVIVOR_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/CA_UK_GE_survivor_20210722.txt";

    private static final String SPILT = "!_!";

    private static Map<String, String[]> COUNTRY_AND_LANGUAGE = new HashMap<>(16);
    private static Map<String, Object> BIG_MAP = new HashMap<>(8);
    private static Map<String, String> LOCATION_MAP = new HashMap<>(16);
    private static Map<String, String> COUNTRY_CODE = new HashMap<>();

    public RGKeywordExpansionV4() {
        seoSysLogEntityDAO = SpringBeanFactory.getBean("seoSysLogEntityDAO");
//        keywordSummaryAnnualBigDAO = SpringBeanFactory.getBean("keywordSummaryAnnualBigDAO");
//        keywordSummaryAnnualNormalDAO = SpringBeanFactory.getBean("keywordSummaryAnnualNormalDAO");
        //for test
        keywordSummaryAnnualBigDAO = SpringBeanFactory.getBean("localKeywordSummaryAnnualBigDAO");
        keywordSummaryAnnualNormalDAO = SpringBeanFactory.getBean("localKeywordSummaryAnnualNormalDAO");
        rgKeywordStreamDAO = SpringBeanFactory.getBean("RGKeywordStreamDAO");
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");
        seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
        adwordsGeoIdDAO = SpringBeanFactory.getBean("adwordsGeoIdDAO");
        //value : country_cd	languageName	engineId	languageId
        COUNTRY_AND_LANGUAGE.put("ae", new String[]{"are", "English", "45", "46"});
        COUNTRY_AND_LANGUAGE.put("au", new String[]{"aus", "English", "2", "5"});
        COUNTRY_AND_LANGUAGE.put("ca", new String[]{"can", "English", "3", "3"});
        COUNTRY_AND_LANGUAGE.put("de", new String[]{"deu", "German", "14", "15"});
        COUNTRY_AND_LANGUAGE.put("fr", new String[]{"fra", "French", "4", "7"});
        COUNTRY_AND_LANGUAGE.put("in", new String[]{"ind", "English", "24", "25"});
        COUNTRY_AND_LANGUAGE.put("it", new String[]{"ita", "Italian", "8", "9"});
        COUNTRY_AND_LANGUAGE.put("jp", new String[]{"jpn", "Japanese", "18", "19"});
        COUNTRY_AND_LANGUAGE.put("nl", new String[]{"nld", "Dutch", "17", "18"});
        COUNTRY_AND_LANGUAGE.put("ro", new String[]{"rou", "English", "71", "72"});
        COUNTRY_AND_LANGUAGE.put("uk", new String[]{"gbr", "English", "6", "8"});
        COUNTRY_AND_LANGUAGE.put("us", new String[]{"usa", "English", "1", "1"});
        COUNTRY_AND_LANGUAGE.put("at", new String[]{"aut", "English", "29", "30"});
        COUNTRY_AND_LANGUAGE.put("ie", new String[]{"irl", "German", "20", "21"});

        Object o = new Object();
        BIG_MAP.put("gbr", o);
        BIG_MAP.put("bra", o);
        BIG_MAP.put("deu", o);
        BIG_MAP.put("usa", o);
        BIG_MAP.put("can", o);
        BIG_MAP.put("ind", o);

        LOCATION_MAP.put("AE", "2784");
        LOCATION_MAP.put("AT", "2040");
        LOCATION_MAP.put("AU", "2036");
        LOCATION_MAP.put("CA", "2124");
        LOCATION_MAP.put("DE", "2276");
        LOCATION_MAP.put("FR", "2250");
        LOCATION_MAP.put("UK", "2826");
        LOCATION_MAP.put("IE", "2372");
        LOCATION_MAP.put("IN", "2356");
        LOCATION_MAP.put("IT", "2380");
        LOCATION_MAP.put("JP", "2392");
        LOCATION_MAP.put("NL", "2528");
        LOCATION_MAP.put("RO", "2642");
        LOCATION_MAP.put("US", "2840");

    }

    public static void main(String[] args) {
        System.out.println("startTime :" + new Date());
        RGKeywordExpansionV4 ins = new RGKeywordExpansionV4();
        ins.process();

    }


    private void process() {
        //get all content
        List<String> jsonStrList = getAllContent();
        //get all content Fusion
        List<String> jsonStrListContentFusion = getAllContentFusion();
        //parse to jsonPOJO
        List<ContentJsonPOJO> jsonPOJOList = stringToJsonPOJO(jsonStrList);
        //parse to jsonPOJO
        List<ContentJsonPOJO> fusionJsonPOJOList = fusionStringToJsonPOJO(jsonStrListContentFusion);
        System.out.println("ContentSize:" + jsonPOJOList.size() + " FusionSize:" + fusionJsonPOJOList.size());

        jsonPOJOList.removeAll(fusionJsonPOJOList);
        jsonPOJOList.addAll(fusionJsonPOJOList);
        System.out.println("countSize =====" + jsonPOJOList.size());


//        Collections.sort(jsonPOJOList, Comparator.comparingInt(o -> {
//            if ("us".equals(o.getCountry().toLowerCase())) {
//                return Integer.MIN_VALUE;
//            }
//            return o.getCountry().hashCode();
//        }));

        doSomething(jsonPOJOList);

    }


    public void doSomething(List<ContentJsonPOJO> jsonPOJOList) {
        int num = 0;
        Map<String, Set<String>> rawKwMap = new HashMap();
        Set<String> usSet = new HashSet<>();
        for (ContentJsonPOJO pojo :
                jsonPOJOList) {
            String country = pojo.getCountry();
            if ("us".equals(country.toLowerCase())) {

                usSet.add(pojo.getSearch());
            } else {
                if (rawKwMap.containsKey(country)) {
                    rawKwMap.get(country).add(pojo.getSearch());
                } else {
                    HashSet<String> searchSet = new HashSet<String>();
                    searchSet.add(pojo.getSearch());
                    rawKwMap.put(country, searchSet);
                }
            }
        }
        System.out.println("searchSet:" + usSet.size() + " country:" + "us");
        doByCountry(usSet, "us", num);
//
        for (Map.Entry<String, Set<String>> entry :
                rawKwMap.entrySet()) {

            Set<String> searchSet = entry.getValue();
            String country = entry.getKey();
            System.out.println("searchSet:" + searchSet.size() + " country:" + country);
            num = doByCountry(searchSet, country, num);
        }

    }

    private int doByCountry(Set<String> searchSet, String country, int num) {
        int skipNum = 0;
        int skipLongKW = 0;
//        Map<String, Set<RGKeywordStreamEntity>> chKWMap = new HashMap();
        //value : country_cd	languageName	engineId	languageId
        List<KeywordStreamSearchengineCountryMappingEntity> keywordStreamSearchengineCountryMappingEntities = selectByCountry(country);
        System.out.println("country:" + country + " rawSearchCnt:" + keywordStreamSearchengineCountryMappingEntities.size());

        if (keywordStreamSearchengineCountryMappingEntities == null
                || keywordStreamSearchengineCountryMappingEntities.size() < 1) {
            System.out.println("skipCountry:" + country);
            return num;
        }

        KeywordStreamSearchengineCountryMappingEntity keywordStreamSearchengineCountryMappingEntity
                = keywordStreamSearchengineCountryMappingEntities.get(0);
        long dataSize = 0;
        if (keywordStreamSearchengineCountryMappingEntities.size() > 1) {
            for (KeywordStreamSearchengineCountryMappingEntity keywordStreamSearchengineCountryMapping :
                    keywordStreamSearchengineCountryMappingEntities) {
                long countData = getDataSize(keywordStreamSearchengineCountryMapping.getEngineId(),
                        keywordStreamSearchengineCountryMapping.getLanguageId());


                if (countData >= dataSize) {
                    dataSize = countData;
                    keywordStreamSearchengineCountryMappingEntity = keywordStreamSearchengineCountryMapping;
                }
            }
        }
//        KeywordStreamSearchengineCountryMappingEntity keywordStreamSearchengineCountryMappingEntity = keywordStreamSearchengineCountryMappingEntities.get(0);
        String countryCode = country;
        Integer engineId = keywordStreamSearchengineCountryMappingEntity.getEngineId();
        Integer languageId = keywordStreamSearchengineCountryMappingEntity.getLanguageId();
        String languageFullName = keywordStreamSearchengineCountryMappingEntity.getLanguageFullName();
        AdwordsGeoIdTableEntity adwordsGeoIdTableEntity = selectLocationId(country.toUpperCase());

        if ("us".equals(country.toLowerCase())) {
            engineId = 1;
            languageId = 1;
        }
        if (adwordsGeoIdTableEntity == null) {
            System.out.println("NotFindGeo:" + country);
            return num;
        }
        Integer locationId = Integer.valueOf(adwordsGeoIdTableEntity.getCriteriaId());


        System.out.println("======proccesingCountry:" + country
                + " SE:" + engineId + "/" + languageId + "/" + locationId
                + " searchSetCnt:" + searchSet.size()
                + " IDX:" + num);

        num++;
//        Set<String> keySet = new HashSet<>(512);

        Set<RGKeywordStreamEntity> keywordStreamEntities = new HashSet<>(10000);

        for (String searchText :
                searchSet) {
            List<String> wordList = getWord(searchText, languageFullName);
            if (wordList == null || wordList.size() < 1) continue;

            //write file for survivor
//            WriteUtils.write(SURVIVOR_PATH, searchText, countryCode, StringUtils.join(wordList, ","));
            if (wordList.size() > 9) {
                skipLongKW++;
                System.out.println("skipLongSearchTextKW:" + searchText);
                continue;
            } else {
                //insert last data
                RGKeywordStreamEntity entity = new RGKeywordStreamEntity();
                List<String> strings = SnowBallAndNgramForForeignLanguages.wordTokenizer(searchText, country);
                entity.setKeywordName(searchText);
                entity.setSearchText(searchText);
                String join = StringUtils.join(strings, SPILT);
                entity.setKey(join);
                entity.setCountryCode(countryCode);
                keywordStreamEntities.add(entity);
//                keySet.add(join);
                if (keywordStreamEntities.size() >= 10000) {
                    insertIntoKeywordStream(keywordStreamEntities);
                    keywordStreamEntities = new HashSet<>(10000);
                }
//                List<KeywordStreamEntity> streamEntityList = null;
//                try {
//                    streamEntityList = getKeywordStream(countryCode, wordList);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//
//                System.out.println("Idx:" + num++ + " processingSearchTextKW:" + searchText + " countStream :" + streamEntityList.size());

                //for insert

//                for (KeywordStreamEntity streamEntity : searchText) {
//                    String chKeywordName = streamEntity.getKeywordName();
//                    RGKeywordStreamEntity entity = new RGKeywordStreamEntity();
//                    entity.setKeywordName(chKeywordName);
//                    entity.setAvgSearchVolme(streamEntity.getAvgSearchVolume());
//                    entity.setDayOfCount(streamEntity.getWeekIndex());
//                    entity.setSearchText(searchText);
//                    System.out.println("searchText:" + searchText + "=>" + chKeywordName
//                            + " SV:" + streamEntity.getAvgSearchVolume()
//                            + " COD:" + streamEntity.getWeekIndex());
//
//                    List<String> strings = SnowBallAndNgramForForeignLanguages.wordTokenizer(chKeywordName, streamEntity.getCountryCd());
//                    streamEntity.setWord(strings);
//                    Collections.sort(strings);
//                    String join = StringUtils.join(strings, SPILT);
//                    entity.setKey(join);
//                    keywordStreamEntities.add(entity);
//                    keySet.add(join);
//
//                    if (keywordStreamEntities.size() >= 1000) {
//                        insertIntoKeywordStream(keywordStreamEntities);
//                    }
//
//                    keywordStreamEntities = new HashSet<>(1000);
//

//                    if (chKWMap.containsKey(join)) {
//                        chKWMap.get(join).add(entity);
//                    } else {
//                        HashSet<RGKeywordStreamEntity> groupStreamSet = new HashSet<>();
//                        groupStreamSet.add(entity);
//                        chKWMap.put(join, groupStreamSet);
//                    }
//                    try {
//                        System.out.println("searchText:" + searchText + "=>" + chKeywordName + " join:" + join
//                                + " groupStreamSet:" + chKWMap.get(join) == null ? 0 : chKWMap.get(join).size());
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }

            }

        }
        if (keywordStreamEntities.size() > 0) {
            insertIntoKeywordStream(keywordStreamEntities);
            keywordStreamEntities = new HashSet<>(10000);
        }

//        for (String key : keySet) {
        List<Integer> keyHashList = findKeyHash(countryCode);
        for (int keyHash :
                keyHashList) {
            List<RGKeywordStreamEntity> groupSet = findByKeyHash(keyHash, countryCode);

            Map<String, List<RGKeywordStreamEntity>> map = new HashMap();
            for (RGKeywordStreamEntity entity :
                    groupSet) {
                try {
                    if (map.get(entity.getKey()) == null) {
                        List<RGKeywordStreamEntity> rankChackKeywordList = new LinkedList();
                        entity.setKeywordName(CommonDataService.encodeQueueBaseKeyword(entity.getKeywordName()));
                        System.out.println("KW:" + entity.getSearchText() + "=>" + entity.getKeywordName());
                        rankChackKeywordList.add(entity);
                        map.put(entity.getKey(), rankChackKeywordList);
                    } else {
                        entity.setKeywordName(CommonDataService.encodeQueueBaseKeyword(entity.getKeywordName()));
                        map.get(entity.getKey()).add(entity);
                    }


                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }


            HashSet<String> mapKey = new HashSet<>(100);
            List<String> rankChackKeywords = new LinkedList<>();
            int flag = 1;

            System.out.println("loopMap:" + map.size());
            for (Map.Entry<String, List<RGKeywordStreamEntity>> entry :
                    map.entrySet()) {
                List<RGKeywordStreamEntity> rankChackKeywordList = entry.getValue();


                for (RGKeywordStreamEntity entity :
                        rankChackKeywordList) {
                    rankChackKeywords.add(entity.getKeywordName());
                }
                mapKey.add(entry.getKey());
                flag++;

                if (flag > 100) {
                    System.out.println("rankChackKeywords:" + rankChackKeywords.size() + " rankChackKeywordList:" + rankChackKeywordList.size());
                    skipNum = new IsWrite(country, engineId, languageId, locationId, groupSet, map, mapKey, rankChackKeywords, rankChackKeywordList).invoke(skipNum);
                    mapKey = new HashSet<>(100);
                    rankChackKeywords = new LinkedList<>();
                    flag = 1;
                }
            }
            System.out.println("KeyCNT:" + mapKey.size() + " flag:" + flag);
//            List<RGKeywordStreamEntity> rankChackKeywordList =null;
//            if (rankChackKeywordList!=null) {
            skipNum = new IsWrite(country, engineId, languageId, locationId, groupSet, map, mapKey, rankChackKeywords, null).invoke(skipNum);
//            }
        }
        System.out.println("skipLongKW:" + skipLongKW + " skipNum:" + skipNum + " country:" + country);
        return num;
    }

    private List<String> getRepeatKeywordId(int engine, int language, List<String> keywordTextList) {
        return seoClarityKeywordEntityDAO.getMonthlyRelationsKeywordIdList(engine, language, keywordTextList);
    }

    private long getDataSize(int engine, int language) {
        return seoClarityKeywordEntityDAO.countData(engine, language);
    }

    private List<SeoClarityKeywordEntity> getKeywordIdByName(List<String> keywordNameList) {
        return seoClarityKeywordEntityDAO.getJoinKeywordEntityListByNames(keywordNameList);
    }

    private List<String> getEncodeList(List<KeywordStreamEntity> entities) {
        List<String> encodeList = new LinkedList<>();
        for (KeywordStreamEntity keywordStreamEntity : entities) {
            try {
                keywordStreamEntity.setKeywordHash(CommonDataService.encodeQueueBaseKeyword(keywordStreamEntity.getKeywordName()));
                encodeList.add(keywordStreamEntity.getKeywordHash());
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return encodeList;

    }

    private List<ContentJsonPOJO> stringToJsonPOJO(List<String> jsonStrList) {
        Set<ContentJsonPOJO> jsonPOJOset = new HashSet<>();
        for (String jsonStr : jsonStrList) {
            Gson gson = new Gson();
            JsonElement element = gson.fromJson(jsonStr, JsonElement.class);
            JsonObject jsonObj = element.getAsJsonObject();
            ContentJsonPOJO contentJsonPOJO = new ContentJsonPOJO();

            if (jsonObj.get("country") != null && jsonObj.get("search") != null) {

                contentJsonPOJO.setCountry(jsonObj.get("country").getAsString());
                contentJsonPOJO.setSearch(jsonObj.get("search").getAsString());
                jsonPOJOset.add(contentJsonPOJO);
            }
        }

        List<ContentJsonPOJO> jsonPOJOList = new LinkedList<>(jsonPOJOset);

        Collections.sort(jsonPOJOList);
        return jsonPOJOList;
    }

    private List<ContentJsonPOJO> fusionStringToJsonPOJO(List<String> jsonStrList) {
        Set<ContentJsonPOJO> jsonPOJOset = new HashSet<>();
        for (String jsonStr : jsonStrList) {
            Gson gson = new Gson();
            JsonElement element = gson.fromJson(jsonStr, JsonElement.class);
            JsonObject jsonObj = element.getAsJsonObject();
            ContentJsonPOJO contentJsonPOJO = new ContentJsonPOJO();
            JsonElement obj = jsonObj.get("Obj");
            if (obj != null){
                JsonObject object = obj.getAsJsonObject();
                if (object.get("keyword") != null && object.get("locationCode") != null) {
                    contentJsonPOJO.setCountry(COUNTRY_CODE.get(object.get("locationCode").getAsString()) == null
                            ? getCountryIsoCode(object.get("locationCode").getAsString()) : COUNTRY_CODE.get(object.get("locationCode").getAsString()));
                    contentJsonPOJO.setSearch(object.get("keyword").getAsString());

                    jsonPOJOset.add(contentJsonPOJO);
                }
        }
    }
        List<ContentJsonPOJO> jsonPOJOList = new LinkedList<>(jsonPOJOset);

        Collections.sort(jsonPOJOList);
        return jsonPOJOList;
    }


    private List<String> getWord(String searchText, String languageName) {
        List<String> word = new ArrayList<>();
//        if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
//            word.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(entity.getSearch(), "ar"));
//        } else {
        word.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(searchText.toLowerCase(), languageName, true));
//        }
        return word;
    }

    private void insertIntoKeywordStream(Set<RGKeywordStreamEntity> keywordStreamEntitySet) {
        rgKeywordStreamDAO.insertKeywordStreamForBatch(keywordStreamEntitySet);
    }


    private List<Integer> findKeyHash(String countryCd) {
        return rgKeywordStreamDAO.findKeyHash(countryCd);
    }

    private List<RGKeywordStreamEntity> findByKeyHash(int keyHash, String countryCd) {
        return rgKeywordStreamDAO.findByKeyHash(keyHash, countryCd);
    }


    private List<KeywordStreamSearchengineCountryMappingEntity> selectByCountry(String country) {
        return keywordStreamSearchengineCountryMappingEntityDAO.selectByCountry(country);
    }

    private AdwordsGeoIdTableEntity selectLocationId(String countryCode) {
        if ("UK".equals(countryCode.toUpperCase()))
            countryCode = "GB";
        return adwordsGeoIdDAO.findByCountry(countryCode);
    }

    private List<String> getAllContent() {
        return seoSysLogEntityDAO.getAllContent();
    }

    private List<String> getAllContentFusion() {
        return seoSysLogEntityDAO.getAllContentFusion();
    }

    private String getCountryIsoCode(String countryCode) {
        return seoSysLogEntityDAO.getCountryIsoCode(countryCode);
    }

    private List<KeywordStreamEntity> getKeywordStream(String countryCode, List<String> wordList) throws InterruptedException {
        try {

            if (BIG_MAP.get(countryCode) != null)
                return keywordSummaryAnnualBigDAO.getKeywordNameAndAvgSearchVolumeByPrarm(countryCode, wordList);
            else
                return keywordSummaryAnnualNormalDAO.getKeywordNameAndAvgSearchVolumeByPrarm(countryCode, wordList);

        } catch (Exception e) {
            e.printStackTrace();
            Thread.sleep(30000);
            return getKeywordStream(countryCode, wordList);
        }
    }


    /**
     * //keyword,engineId,languageId,locationId
     *
     * @param filePath
     * @param
     * @param
     */
    public static void writeEncode(String filePath, String candidateKW, Integer engineId, Integer languageId, Integer locationId) {

        try {
            File file = new File(filePath);
            FileOutputStream fos = null;
            if (!file.exists()) {
                file.createNewFile();
                fos = new FileOutputStream(file);
            } else {

                fos = new FileOutputStream(file, true);
            }

            OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);

            //keyword,engineId,languageId,locationId
            osw.write(candidateKW + "," + engineId + "," + languageId + "," + locationId);


            osw.write("\r\n");


            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void writeDecode(String filePath, String text, String country, String search) {

        try {
            File file = new File(filePath);
            FileOutputStream fos = null;
            if (!file.exists()) {
                file.createNewFile();
                fos = new FileOutputStream(file);
            } else {

                fos = new FileOutputStream(file, true);
            }

            OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);

            osw.write(search + "\t" + country + "\t" + text);
            osw.write("\r\n");
            osw.close();
        } catch (
                Exception e) {
            e.printStackTrace();
        }
    }

    private class IsWrite {
        private int INDEX = 0;
        private String country;
        private Integer engineId;
        private Integer languageId;
        private Integer locationId;
        private List<RGKeywordStreamEntity> groupSet;
        private Map<String, List<RGKeywordStreamEntity>> map;
        private HashSet<String> mapKey;
        private List<String> rankChackKeywords;
        private List<RGKeywordStreamEntity> rankChackKeywordList;
        private int flag;

        public IsWrite(String country, Integer engineId, Integer languageId,
                       Integer locationId, List<RGKeywordStreamEntity> groupSet,
                       Map<String, List<RGKeywordStreamEntity>> map, HashSet<String> mapKey,
                       List<String> rankChackKeywords, List<RGKeywordStreamEntity> rankChackKeywordList
        ) {
            this.country = country;
            this.engineId = engineId;
            this.languageId = languageId;
            this.locationId = locationId;
            this.groupSet = groupSet;
            this.map = map;
            this.mapKey = mapKey;
            this.rankChackKeywords = rankChackKeywords;
            this.rankChackKeywordList = rankChackKeywordList;

        }

        public HashSet<String> getMapKey() {
            return mapKey;
        }

        public List<String> getRankChackKeywords() {
            return rankChackKeywords;
        }

        public int getFlag() {
            return flag;
        }

        public int invoke(int skipNum) {
            long time = System.currentTimeMillis();
            if (INDEX % 5000 == 0) {


                System.out.print("engineId:" + engineId + " languageId:" + languageId + " rankChackKeywords:  ");
                for (String string :
                        rankChackKeywords) {
                    System.out.print(string + ",");
                }
                System.out.print("mapKey:");
                for (String string :
                        mapKey) {
                    System.out.print(string + ",");
                }
            }

            INDEX++;
            List<String> rgKWList = getRepeatKeywordId(engineId,
                    languageId, rankChackKeywords);
            System.out.println("getRepeatUseTime:" + (System.currentTimeMillis() - time));

            if (rgKWList.size() > 0) {
                System.out.println("rgKWList:" + rgKWList.size());

                for (String key :
                        mapKey) {
                    List<RGKeywordStreamEntity> rgKeywordStreamEntities = map.get(key);
                    boolean b = true;
                    OUT:
                    for (RGKeywordStreamEntity entity :
                            rgKeywordStreamEntities) {

                        if (rgKWList.contains(entity.getKeywordName())) {
                                System.out.println("processingJoinKey:" + groupSet.get(0).getKey() + " country:" + country + " skipKW:" + rgKeywordStreamEntities.size()
                                    + " se:" + engineId + "/" + languageId
                                    + " groupSet:" + groupSet.size()
                                    /*  + " decodeList:" + rankChackKeywordList.size()*/ + " rgKWListSize:" + rgKWList.size()
                                    + " key:" + key);
                            skipNum += rgKeywordStreamEntities.size();
                            b = false;
                            break OUT;
                        }
                    }
                    if (b) {
                        RGKeywordStreamEntity candidateKW = rgKeywordStreamEntities.get(0);
                        writeEncode(ENCODE_FILE_PATH, candidateKW.getKeywordName(), engineId, languageId, locationId);

                        //String filePath, String text, String country, String search,String avgSearchVolume,String countofdays
                        writeDecode(DECODE_FILE_PATH, candidateKW.getSearchText(), country, candidateKW.getKeywordName());
                    }
                }
            } else {
                //if size < 1 all is only
                for (String key :
                        mapKey) {
                    RGKeywordStreamEntity candidateKW = map.get(key).get(0);

                    System.out.println("processingJoinKey:" + candidateKW.getKey() + " country:" + country + " se:" + engineId + "/" + languageId
                            + " groupSet:" + groupSet.size()
                            /*+ " decodeList:" + rankChackKeywordList.size() */ + " rgKWListSize:" + rgKWList.size() + " candidateKW:" + candidateKW.getKeywordName()
                            + " key:" + key);
//                        for (RGKeywordStreamEntity rekw :
//                                groupSet) {
//                            System.out.print("  kw:\"" + rekw.getKeywordName() + "\"");
//                        }
                    System.out.println();
                    writeEncode(ENCODE_FILE_PATH, candidateKW.getKeywordName(), engineId, languageId, locationId);

                    //String filePath, String text, String country, String search,String avgSearchVolume,String countofdays
                    writeDecode(DECODE_FILE_PATH, candidateKW.getSearchText(), country, candidateKW.getKeywordName());
                }
            }


            flag = 0;
            mapKey = new HashSet<>(100);
            rankChackKeywords = new LinkedList<>();
            return skipNum;
        }
    }
}
