package seoclarity.backend.upload.rgupload;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordMonthlyRecommendDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.KeywordStreamSearchengineCountryMappingEntity;
import seoclarity.backend.entity.rankcheck.KeywordMonthlyRecommend;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.keywordexpand.utils.KeywordCleanUpUtils;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;
import seoclarity.backend.utils.keywordTokenizer.StopKeywordsConstants;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ewain 20231020
 * seoclarity.backend.upload.rgupload.RgKeywordCleaningUpAndTokenizationTools
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.rgupload.RgKeywordCleaningUpAndTokenizationToolsWithXlsCD" -Dexec.cleanupDaemonThreads=false -Dexec.args="PROD /home/<USER>/rg-file/20231113_AJG KeywordExpansionwww.ajg.com_2_5_desktop_2023-11-13.txt" > log/RgKeywordCleaningUpAndTokenizationTools-20231113-01.log 2>&1 &
 */
@CommonsLog
public class RgKeywordCleaningUpAndTokenizationToolsWithXlsCD {

    private static final String SPLIT_TAB = "	";
    private static final String SPLIT_UNDERSCORE = "_";
    private static final String SPLIT_DOT = ".";
    private static final String SPLIT_DOT_FOR_SPLIT = "\\.";
    private static final String ENVI_TEST = "TEST";
    private static final String ENVI_PROD = "PROD";

    private static final String prefix = "filtered_cleaned_";

    // params
    private boolean IS_ENVI_TEST;
    private String BASE_FOLDER;
    private List<String> IN_FILE_NAME_LIST;
    private Map<String, String> engineStrPLanguageFullNameMap;
    private Set<String> stopKeywordSet;

    private String CLEANUP_OK_KW_FILE;
    private String CLEANUP_SKIP_KW_FILE;
    private String RG_EXIST_KW_FILE;
    private String RG_OK_KW_FILE;
    private String TOKENIZED_OK_KW_FILE;

    private Map<String, Integer> countryCodePEngineIdMap = new HashMap<>();
    private Map<Integer, List<Integer>> engineIdPLanguageIdMap = new HashMap<>();

    private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private KeywordMonthlyRecommendDAO keywordMonthlyRecommendDAO;
    private KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;

    public RgKeywordCleaningUpAndTokenizationToolsWithXlsCD() {
        seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
        keywordMonthlyRecommendDAO = SpringBeanFactory.getBean("keywordMonthlyRecommendDAO");
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");
    }

    public static void main(String[] args) {
        RgKeywordCleaningUpAndTokenizationToolsWithXlsCD tool = new RgKeywordCleaningUpAndTokenizationToolsWithXlsCD();
        if (args.length < 3) {
            log.error("Invalid parameters, exiting..");
        }
        tool.process(args);
    }

    private void process(String[] args) {

        // init
        initGlobalParams(args);

        // loop file
        for (String infileName : IN_FILE_NAME_LIST) {
            String infileFullName = BASE_FOLDER + "/" + infileName;
            log.info("Initiating processing of the file: " + infileFullName);

            XSSFWorkbook wb = null;
            try {
                wb = new XSSFWorkbook(infileFullName);
            } catch (IOException e) {
                e.printStackTrace();
            }
            int numberOfSheets = wb.getNumberOfSheets();

            sheetFor:
            for (int i = 0; i < numberOfSheets; i++) {
                XSSFSheet sheet = wb.getSheetAt(i);
                String sheetName = sheet.getSheetName().trim();
                String countryCode = StringUtils.removeStart(sheetName, prefix);
                int engineId = countryCodePEngineIdMap.get(countryCode);
                List<Integer> languageIds = engineIdPLanguageIdMap.get(engineId);
                int lastRowNum = sheet.getLastRowNum();

                List<String> items = new ArrayList<>();
                for (int j = 0; j < lastRowNum + 1; j++) {
                    Row row = sheet.getRow(j);
                    Cell cell = row.getCell(0);
                    if (cell == null) {
                        log.info("error keyword name");
                        continue sheetFor;
                    }
                    String keyword = cell.toString().trim();
                    if (keyword.equalsIgnoreCase("Clean Keyword")) {
                        continue;
                    }
                    items.add(keyword);
                }

                System.out.println("items collect=> countryCode: " + countryCode + ", engineId: " + engineId + ", all kw size: " + items.size());

                List<List<String>> itemLists = CollectionSplitUtils.splitCollectionBySizeWithStream(items, CollectionSplitUtils.BATCH_MEMORY_LIMIT_SIZE);
                int batchCnt = 1;
                for (List<String> itemList : itemLists) {
                    process(itemList, countryCode, CLEANUP_OK_KW_FILE, CLEANUP_SKIP_KW_FILE, engineId, languageIds, RG_EXIST_KW_FILE, RG_OK_KW_FILE, TOKENIZED_OK_KW_FILE);
                    log.info("memory limit batch size: " + (batchCnt * CollectionSplitUtils.BATCH_MEMORY_LIMIT_SIZE));
                    batchCnt++;
                }
            }
        }
    }

    private void process(List<String> lines, String countryCode, String CLEANUP_OK_KW_FILE, String CLEANUP_SKIP_KW_FILE,
                         int engineId, List<Integer> languageIds, String RG_EXIST_KW_FILE, String RG_OK_KW_FILE, String TOKENIZED_OK_KW_FILE) {

        log.info("variable collect=>countryCode: " + countryCode + ", engineId: " + engineId + ", languageIds: " + languageIds + ", kw size: " + lines.size()
                + "\n       CLEANUP_OK_KW_FILE: " + CLEANUP_OK_KW_FILE
                + ", \n     CLEANUP_SKIP_KW_FILE: " + CLEANUP_SKIP_KW_FILE
                + ", \n     RG_EXIST_KW_FILE: " + RG_EXIST_KW_FILE
                + ", \n     RG_OK_KW_FILE: " + RG_OK_KW_FILE
                + ", \n     TOKENIZED_OK_KW_FILE: " + TOKENIZED_OK_KW_FILE);

        // clean up keyword.
        List<String> cleanUpOkKwList = new ArrayList<>();
        Map<String, Integer> kwPImpressionsMap = new HashMap<>();
        processCleanUp(countryCode, lines, cleanUpOkKwList, kwPImpressionsMap, CLEANUP_OK_KW_FILE, CLEANUP_SKIP_KW_FILE);

        processRgCheckAndTokens(countryCode, engineId, languageIds, kwPImpressionsMap, cleanUpOkKwList, RG_EXIST_KW_FILE, RG_OK_KW_FILE);

    }

    private void initGlobalParams(String[] args) {
        if (args[0].equals(ENVI_TEST)) {
            IS_ENVI_TEST = true;
        } else if (args[0].equals(ENVI_PROD)) {
            IS_ENVI_TEST = false;
        } else {
            throw new RuntimeException("Invalid envi parameter: " + args[0] + "!");
        }

        // init filepath
        BASE_FOLDER = args[1];
        IN_FILE_NAME_LIST = Arrays.asList(args).subList(2, args.length);

        CLEANUP_OK_KW_FILE = BASE_FOLDER + "/" + "TR-cleanup_ok.txt";
        CLEANUP_SKIP_KW_FILE = BASE_FOLDER + "/" + "TR-cleanup_skip.txt";
        RG_EXIST_KW_FILE = BASE_FOLDER + "/" + "TR-rg_exist.txt";
        RG_OK_KW_FILE = BASE_FOLDER + "/" + "TR-rg_ok.txt";
        TOKENIZED_OK_KW_FILE = BASE_FOLDER + "/" + "TR-tokenized_ok.txt";

        // init engineId language map
        // 1-1->English
        initEngineCountryCodeMap();
        initFileCountryCodeEngineMap();
        initFileEngineIdLanguageIdMap();
        //init stop keyword set
        stopKeywordSet = new HashSet<>(Arrays.asList(StopKeywordsConstants.STOP_WORDS_ARR));


    }

    private void initEngineCountryCodeMap() {
        List<KeywordStreamSearchengineCountryMappingEntity> allEnabledEngineCountryMapping = keywordStreamSearchengineCountryMappingEntityDAO.findAllEnabledEngineCountryMapping();
        engineStrPLanguageFullNameMap = allEnabledEngineCountryMapping.stream()
                .filter(var -> var.getMappingType() == KeywordStreamSearchengineCountryMappingEntity.MAPPING_TYPE_CODE_FROM_ENGINE_ID)
                .collect(Collectors.toMap(var1 -> var1.getEngineId() + SPLIT_UNDERSCORE + var1.getLanguageId(), KeywordStreamSearchengineCountryMappingEntity::getLanguageFullName));
    }


    private void initFileCountryCodeEngineMap() {
        countryCodePEngineIdMap.put("US",1);
        countryCodePEngineIdMap.put("AU",2);
        countryCodePEngineIdMap.put("FR",4);
        countryCodePEngineIdMap.put("UK",6);
        countryCodePEngineIdMap.put("IT",8);
        countryCodePEngineIdMap.put("DK",9);
        countryCodePEngineIdMap.put("NO",12);
        countryCodePEngineIdMap.put("SE",13);
        countryCodePEngineIdMap.put("DE",14);
        countryCodePEngineIdMap.put("ES",16);
        countryCodePEngineIdMap.put("NL",17);
        countryCodePEngineIdMap.put("JP",18);
        countryCodePEngineIdMap.put("PT",19);
        countryCodePEngineIdMap.put("IE",20);
        countryCodePEngineIdMap.put("BE",21);
        countryCodePEngineIdMap.put("KR",23);
        countryCodePEngineIdMap.put("MY",32);
        countryCodePEngineIdMap.put("TW",34);
        countryCodePEngineIdMap.put("VN",36);
        countryCodePEngineIdMap.put("PL",42);
        countryCodePEngineIdMap.put("GR",61);
    }
    private void initFileEngineIdLanguageIdMap() {
        engineIdPLanguageIdMap.put(1, Arrays.asList(1));
        engineIdPLanguageIdMap.put(2, Arrays.asList(5));
        engineIdPLanguageIdMap.put(4, Arrays.asList(7));
        engineIdPLanguageIdMap.put(6, Arrays.asList(8));
        engineIdPLanguageIdMap.put(8, Arrays.asList(9));
        engineIdPLanguageIdMap.put(9, Arrays.asList(10));
        engineIdPLanguageIdMap.put(12, Arrays.asList(13,144));
        engineIdPLanguageIdMap.put(13, Arrays.asList(14));
        engineIdPLanguageIdMap.put(14, Arrays.asList(15));
        engineIdPLanguageIdMap.put(16, Arrays.asList(17));
        engineIdPLanguageIdMap.put(17, Arrays.asList(18));
        engineIdPLanguageIdMap.put(18, Arrays.asList(19));
        engineIdPLanguageIdMap.put(19, Arrays.asList(20));
        engineIdPLanguageIdMap.put(20, Arrays.asList(21));
        engineIdPLanguageIdMap.put(21, Arrays.asList(22,96));
        engineIdPLanguageIdMap.put(23, Arrays.asList(24));
        engineIdPLanguageIdMap.put(32, Arrays.asList(32));
        engineIdPLanguageIdMap.put(34, Arrays.asList(34));
        engineIdPLanguageIdMap.put(36, Arrays.asList(36,128));
        engineIdPLanguageIdMap.put(42, Arrays.asList(42));
        engineIdPLanguageIdMap.put(61, Arrays.asList(62));
    }

    private void processCleanUp(String countryCode, List<String> lines,
                                List<String> cleanUpOkKwList, Map<String, Integer> kwPImpressionsMap, String cleanUpOkFileName, String cleanUpSkipFileName) {
        List<String> cleanUpSkipKwList = new ArrayList<>();

        for (String line : lines) {
            String[] lineSplit = line.split(SPLIT_TAB);
            Integer engineIdFromFile = null;
            Integer languageIdFromFile = null;
            String keywordName;
            int impressions = 0;

            //parse from line
            if (lineSplit.length == 4) {
                String engineIdStrInFile = lineSplit[0];
                String languageIdStrInFile = lineSplit[1];
                if (StringUtils.isNumeric(engineIdStrInFile)) {
                    engineIdFromFile = Integer.parseInt(engineIdStrInFile);
                }
                if (StringUtils.isNumeric(languageIdStrInFile)) {
                    languageIdFromFile = Integer.parseInt(languageIdStrInFile);
                }
                keywordName = lineSplit[2].trim();
                String impressionsStr = lineSplit[3].trim().split(SPLIT_DOT_FOR_SPLIT)[0];
                if (StringUtils.isNumeric(impressionsStr)) {
                    impressions = Integer.parseInt(impressionsStr);
                } else {
                    log.error("The parsing of impressions has failed, please check. line: " + line);
                    continue;
                }
            } else if (lineSplit.length == 2) {
                keywordName = lineSplit[0].trim();
                String impressionsWithDotStr = lineSplit[1].trim();
                String impressionsStr;
                if (impressionsWithDotStr.contains(SPLIT_DOT)) {
                    impressionsStr = impressionsWithDotStr.split(SPLIT_DOT_FOR_SPLIT)[0];
                } else {
                    impressionsStr = impressionsWithDotStr;
                }
                if (StringUtils.isNumeric(impressionsStr)) {
                    impressions = Integer.parseInt(impressionsStr);
                } else {
                    log.error("The parsing of impressions has failed, please check. line: " + line);
                    continue;
                }
            } else if (lineSplit.length == 1) {
                keywordName = lineSplit[0].trim();
            } else {
                log.error("Parsing row data error, please check. line: " + line);
                throw new RuntimeException();
            }

            // clean up
            if (keywordName.contains("...")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            if (keywordName.contains("\t")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            String cleanUpKw = null;
            try {
                cleanUpKw = KeywordCleanUpUtils.cleanUp(keywordName);
            } catch (Exception e) {
                log.info("====== keywordName: " + keywordName + ", cleanUpKw: " + cleanUpKw);
                throw new RuntimeException(e);
            }
            if (cleanUpKw != null) {
                if (StringUtils.containsAny(cleanUpKw, KeywordCleanUpUtils.ILLEGAL_CHAR_ARRAY)) {
                    cleanUpSkipKwList.add(cleanUpKw);
                    log.info("Skip keyword with bad char, keywordName" + keywordName + ", cleanUpKw: " + cleanUpKw + " from countryCode: " + countryCode);
                    continue;
                }
                cleanUpOkKwList.add(cleanUpKw);

                if (kwPImpressionsMap.containsKey(cleanUpKw)) {
                    Integer impressionsExisting = kwPImpressionsMap.get(cleanUpKw);
                    if (impressions > impressionsExisting) {
                        kwPImpressionsMap.put(cleanUpKw, impressions);
                    }
                    log.info("Find dup keyword, keywordName: " + keywordName + " from countryCode: " + countryCode);
                } else {
                    kwPImpressionsMap.put(cleanUpKw, impressions);
                }

            } else {
                cleanUpSkipKwList.add(keywordName);
            }
        }

        log.info("countryCode: " + countryCode + " processing completed, cleanUpOkKwList size: " + cleanUpOkKwList.size() + ", cleanUpOkKwSet size: " + new HashSet<>(cleanUpOkKwList).size() + ", cleanUpSkipKwList size: " + cleanUpSkipKwList.size());

        if (IS_ENVI_TEST) {
            return;
        }

        try {
            FileUtils.writeLines(new File(cleanUpOkFileName), "UTF-8", cleanUpOkKwList, true);
            FileUtils.writeLines(new File(cleanUpSkipFileName), "UTF-8", cleanUpSkipKwList, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void processRgCheckAndTokens(String countryCode, int engineId, List<Integer> languageIds, Map<String, Integer> kwPImpressionsMap,
                                         List<String> cleanUpOkKwList, String rgExistKwFileName, String rgFilteredOkFileName) {

        for (Integer languageId : languageIds) {

            Map<String, String> encodeKeywordNamePCleanUpKwMap = cleanUpOkKwList.stream().collect(Collectors.toMap(var1 -> {
                try {
                    return CommonDataService.encodeQueueBaseKeyword(var1);
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }, var2 -> var2, (var3, var4) -> var3));

            Set<String> encodeKeywordNameSet = encodeKeywordNamePCleanUpKwMap.keySet();
            List<SeoClarityKeywordMonthlySearchEngineRelationEntity> existInSeRel = new ArrayList<>();
            List<KeywordMonthlyRecommend> existInKMR = new ArrayList<>();

            List<List<String>> encodeKeywordNameLists = CollectionSplitUtils.splitCollectionBySizeWithStream(new ArrayList<>(encodeKeywordNameSet), CommonDataService.MYSQL_DB_QUERY_WITH_STRING);
            for (List<String> encodeKeywordNameListChild : encodeKeywordNameLists) {
                List<SeoClarityKeywordMonthlySearchEngineRelationEntity> seoClarityKeywordMonthlySearchEngineRelationEntities = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.checkExist(engineId, languageId, SeoClarityKeywordMonthlySearchEngineRelationEntity.DEVICE_DESKTOP, encodeKeywordNameListChild);
                if (!seoClarityKeywordMonthlySearchEngineRelationEntities.isEmpty()) {
                    existInSeRel.addAll(seoClarityKeywordMonthlySearchEngineRelationEntities);
                }
                List<KeywordMonthlyRecommend> keywordMonthlyRecommends = keywordMonthlyRecommendDAO.checkExists(engineId, languageId, encodeKeywordNameListChild);
                if (!encodeKeywordNameListChild.isEmpty()) {
                    existInKMR.addAll(keywordMonthlyRecommends);
                }
            }

            List<String> rgOkKwList = new ArrayList<>();

            Set<String> _rgExistKeywordSet = existInSeRel.stream().map(var->var.getKeywordText().toLowerCase()).collect(Collectors.toSet());
            Set<String> _kmrExistKeywordSet = existInKMR.stream().map(var->var.getKeywordText().toLowerCase()).collect(Collectors.toSet());
            encodeKeywordNamePCleanUpKwMap.forEach((encodeKw, originalKw)->{
                if (_rgExistKeywordSet.contains(encodeKw.toLowerCase())) {
                    log.info("keyword exists in rg rel tbl, engineId: " + engineId + ", languageId: " + languageId + ", kw: " + originalKw);
                } else if (_kmrExistKeywordSet.contains(encodeKw.toLowerCase())) {
                    log.info("keyword exists in rg recommend tbl, engineId: " + engineId + ", languageId: " + languageId + ", kw: " + originalKw);
                } else {
                    rgOkKwList.add(originalKw);
                }
            });

            Set<String> rgExistsKwSet = new HashSet<>();
            rgExistsKwSet.addAll(_rgExistKeywordSet);
            rgExistsKwSet.addAll(_kmrExistKeywordSet);

            if (IS_ENVI_TEST) {
                // tokenize
                processTokens(countryCode, engineId, languageId, rgOkKwList, kwPImpressionsMap, TOKENIZED_OK_KW_FILE);
                continue;
            }

            try {
                FileUtils.writeLines(new File(rgFilteredOkFileName), "UTF-8", rgOkKwList, true);
                FileUtils.writeLines(new File(rgExistKwFileName), "UTF-8", rgExistsKwSet, true);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            // tokenize
            processTokens(countryCode, engineId, languageId, rgOkKwList, kwPImpressionsMap, TOKENIZED_OK_KW_FILE);
        }
    }

    private void processTokens(String countryCode, int engineId, Integer languageId, List<String> rgOkKwList, Map<String, Integer> kwPImpressionsMap, String tokenizedOkFileName) {
        // default: English
        String fullLanguageName = "English";
        String engineStr = engineId + SPLIT_UNDERSCORE + languageId;
        if (engineStrPLanguageFullNameMap.containsKey(engineStr)) {
            fullLanguageName = engineStrPLanguageFullNameMap.get(engineStr);
        } else if (engineStr.equalsIgnoreCase("36_36")) {
            fullLanguageName = "Vietnamese";
        } else {
            log.info("####can not find fullLanguageName from engineStrPLanguageFullNameMap, engineStr: " + engineStr);
        }

        List<String> tokenizedKwList = new ArrayList<>();
        for (String keywordName : rgOkKwList) {
            List<String> tokenizedPhrase = SnowBallAndNgramForForeignLanguages.wordTokenizerWithFullLanguage(keywordName.toLowerCase(), fullLanguageName);
            // from meo: 存储阶段不使用停止词筛选
            // streamList = streamList.stream().filter(var->!stopKeywordSet.contains(var.toLowerCase())).collect(Collectors.toList());
            List<String> cleanedTokenizedPhraseList = new ArrayList<>();
            if (!tokenizedPhrase.isEmpty()) {
                for (String word : tokenizedPhrase) {
                    if (word.contains("'")) {
                        word = word.replaceAll("'", "");
                    }
                    if (word.contains("	")) {
                        word = word.replaceAll("	", "");
                    }
                    cleanedTokenizedPhraseList.add(word);
                }
            } else {
                log.error("Get tokenizedPhrase error! keyword name: " + keywordName + ", engineId: " + engineId + ", languageId: " + languageId);
            }

            if (kwPImpressionsMap.containsKey(keywordName)) {
                Integer impressions = kwPImpressionsMap.get(keywordName);
                StringBuilder sbd = new StringBuilder();
                sbd.append(engineId).append(SPLIT_TAB).append(languageId).append(SPLIT_TAB).append(keywordName).append(SPLIT_TAB);
                sbd.append("['").append(StringUtils.join(cleanedTokenizedPhraseList, "','")).append("']").append(SPLIT_TAB);
                sbd.append(impressions);
                tokenizedKwList.add(sbd.toString());
            } else {
                log.error("Unable to retrieve impressions, engine: " + engineId + ", language: " + languageId + ", keywordName: " + keywordName);
                throw new RuntimeException();
            }
        }

        if (IS_ENVI_TEST) {
            return;
        }

        try {
            FileUtils.writeLines(new File(tokenizedOkFileName), "UTF-8", tokenizedKwList, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void collect() {

    }

    public static void test() {
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("I've already opened it's", "English"));
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("I already went home yesterday", "English"));
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("They will be taking a plane and leaving tomorrow", "English"));
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("Have you ever bragged", "English"));
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("flying", "English"));
//
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordTokenizer("flying", "English"));

        String[] split = "1013.0".split(SPLIT_DOT);
        for (String s : split) {
            System.out.println(s);
        }

        System.out.println("1013.0".contains("\\."));
    }
}
