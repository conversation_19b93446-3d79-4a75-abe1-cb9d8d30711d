
package seoclarity.backend.upload;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.ogema.core.model.array.IntegerArrayResource;

import com.alibaba.druid.sql.visitor.functions.If;

import seoclarity.backend.dao.actonia.AutoRunInstanceEntityDAO;
import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.clickhouse.ga.GaClarityDBEntityDAO;
import seoclarity.backend.entity.actonia.AutorunInfoEntity;
import seoclarity.backend.entity.actonia.AutorunInstanceEntity;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.clickhouse.ga.GoogleAnalyticsV4Entity;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 *
 * <AUTHOR>
 * @date 2021-05-13 seoclarity.backend.upload.GaUploadV4Historical 
 *       //daily  mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.GaUploadV4Historical" -Dexec.args="1"
 *       //fresh  mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.GaUploadV4Historical" -Dexec.args="2"
 *       // upload to clarityDB
 */
public class GaUploadV4Historical {
	private static int version = 1;
	private static int specialVersion = 0;

	private static String tmpSummaryTable = "";

	private static Integer startHour;
	private static Date processDate = new Date();
	private static boolean isBkProcess = false;

	private static final String databaseName = "actonia_site_analytics";
	private static String finalTableName = "dis_analytics_ga4_rawNew202407";

	public static final Log logger = LogFactory.getLog(GaUploadV4Historical.class);

	private static String basePath = "/home/<USER>/ga_backprocess";

	private static String storeDoneFilePath;
	private static String storeTempFilePath;
	private static String storeBKFilePath;
	private static String storeDuplicateFilePath;

	public static final int TARGETURL_ADDEDBY_GA = 3;

	public static final int TARGETURL_ADDEDBY_CONVERSION = 99;

	private GaClarityDBEntityDAO gaClarityDBEntityDAO;

	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	private AutoRunInstanceEntityDAO autoRunInstanceEntityDAO;

	private static List<String> processingFileList = new ArrayList<String>();

	private static Integer logId;

	public GaUploadV4Historical() {

		gaClarityDBEntityDAO = SpringBeanFactory.getBean("gaClarityDBEntityDAO");
		autoRunInstanceEntityDAO = SpringBeanFactory.getBean("autoRunInstanceEntityDAO");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");

	}

	public static Integer TYPE_GA_4_DAILY = 1;

	private static Integer processType = 0;

	private static Integer uploadType = ClarityDBUploadLogEntity.UPLOAD_TYPE_GA_V4_HISTORICAL;

	public static void main(String args[]) {

		if (args.length > 0) {
			processType = NumberUtils.toInt(args[0]);
		}

		if (processType == TYPE_GA_4_DAILY) {

			basePath = "/home/<USER>/ga_backprocess";
			finalTableName = "dis_analytics_ga4_rawNew202407";
		} else {
			System.out.println("=== prcoess type is empty! args[0]:" + args[0]);
			return;
		}

		storeDoneFilePath = basePath + "/needUpload/";
		storeTempFilePath = basePath + "/uploading/";
		storeBKFilePath = basePath + "/backUpFolder/";
		storeDuplicateFilePath = basePath + "/duplicate/";

		storeTempFilePath = storeTempFilePath + "/" + FormatUtils.formatDate(processDate, "yyyyMMddHH") + "/";

		GaUploadV4Historical crawlGoogalAnalyticsEngine = new GaUploadV4Historical();
		System.out.println("--- specialVersion:" + specialVersion + ", use version:" + version + ", storeDoneFilePath:"
				+ storeDoneFilePath + ", 30s to continue...");

		Calendar cal = Calendar.getInstance();
		cal.setTime(processDate);
		startHour = cal.get(Calendar.HOUR_OF_DAY);

		if (crawlGoogalAnalyticsEngine.checkIsProcessingUpload()) {
			System.out.println("There found more than one processing still not finished, exit !!!");
			System.exit(-1);
		}

		File doneFolder = new File(storeDoneFilePath);
		if (doneFolder != null && doneFolder.isDirectory()) {
			boolean hasFilesNeedProcess = false;

			for (File file : doneFolder.listFiles()) {
				if (StringUtils.startsWith(file.getName(), "ga_v4_") && file.isFile()) {

					System.out.println("Found file " + file.getName() + ", start to process !");
					hasFilesNeedProcess = true;
					break;
				}
			}

			if (!hasFilesNeedProcess) {

				crawlGoogalAnalyticsEngine.insertEmptyLog();
				System.out.println("There do not have any files need to be process, skiped+++");
				System.exit(-1);
			}

		}

		try {
			Thread.sleep(5000);
			if (crawlGoogalAnalyticsEngine.checkTableForCurrentHour(processDate)) {
				crawlGoogalAnalyticsEngine.process();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// if no files need to be upload, then insert one record with final status =
	// success(2) and upload status = success(2)
	private void insertEmptyLog() {

		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();

		clarityDBUploadLogEntity.setTmpTableName("");
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName(finalTableName);
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadDailyRows(0);
		clarityDBUploadLogEntity.setFinalTableUploadRows(0);
		clarityDBUploadLogEntity.setUploadType(uploadType);

		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}

		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadEndTime(new Date());
		clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}

	private boolean checkIsProcessingUpload() {

		List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(uploadType);
		if (CollectionUtils.isNotEmpty(list)) {
			return true;
		}
		return false;
	}

	private void process() {

		long startTime = System.currentTimeMillis();

		File duplicateFolder = new File(storeDuplicateFilePath);
		if (duplicateFolder == null || !duplicateFolder.isDirectory()) {
			duplicateFolder.mkdir();
		}

		moveFilesToProcessingFolder();

		// insert into temp table
		if (StringUtils.isNotBlank(tmpSummaryTable)) {

			ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();

			clarityDBUploadLogEntity.setTmpTableName(tmpSummaryTable);
			clarityDBUploadLogEntity.setDatabaseName(databaseName);
			clarityDBUploadLogEntity.setFinalTableName(finalTableName);
			clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
			clarityDBUploadLogEntity.setUploadType(uploadType);
			try {
				clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}

			clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
			clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);

			logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
			System.out.println("===add log for ga_upload_log, id:" + logId);

			// process normal
			File doneFolder = new File(storeDoneFilePath);
			if (doneFolder == null || !doneFolder.isDirectory()) {
				System.out.println("Folder is not exist :" + doneFolder);
				long endTime = System.currentTimeMillis();
				int elapsedSeconds = (int) (endTime - startTime);

				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0,
						elapsedSeconds, logId);
				return;
			}
			try {

				File tmpFile;
				for (String gaFileFullPath : processingFileList) {
					tmpFile = new File(gaFileFullPath);

					if (tmpFile == null || !tmpFile.isFile()) {
						System.out.println("File is not exist or is a folder : " + gaFileFullPath);
						continue;
					}

					// String prefix = "ga_" + ownDomainId + "_" + gaInfoVO.getTrafficDate() + "_" +
					// gaInfoVO.getVersion() + "_" + (isBackProcess ? "bk_" : startHour + "_");

					System.out.println("=====processing file:" + tmpFile.getAbsolutePath());
					processFile(tmpFile);
				}

				long endTime = System.currentTimeMillis();
				int elapsedSeconds = (int) (endTime - startTime);

				for (String gaFileFullPath : processingFileList) {
					tmpFile = new File(gaFileFullPath);

					if (tmpFile == null || !tmpFile.isFile()) {
						System.out.println("File is not exist or is a folder : " + gaFileFullPath);
						continue;
					}

					// String prefix = "ga_v4_" + ownDomainId + "_" + gaInfoVO.getTrafficDate() +
					// "_" + gaInfoVO.getVersion() + "_" + (isBackProcess ? "bk_" : startHour +
					// "_");
					String fileName = tmpFile.getName();
					String[] arrays = StringUtils.split(fileName, "_");

					Integer ownDomainId = NumberUtils.toInt(arrays[2]);
					Integer trafficDate = NumberUtils.toInt(arrays[3]);
					Integer version = NumberUtils.toInt(arrays[4]);

					try {
						boolean isUpdateSuccess = autoRunInstanceEntityDAO.updateInstanceForUploader(
								AutorunInfoEntity.CATEGORY_GA_V4, ownDomainId, AutorunInstanceEntity.DATA_TYPE_GA,
								trafficDate, version, AutorunInstanceEntity.STATUS_FINISH_WITHOUT_ERROR, new Date(), 0,
								0, 0);

						if (!isUpdateSuccess) {
							System.out.println(" ===== update failed, not update " + AutorunInfoEntity.CATEGORY_GA_V4
									+ "-" + ownDomainId + "-" + AutorunInstanceEntity.DATA_TYPE_GA + "-" + trafficDate
									+ "-" + version);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}

				Integer totalCount = gaClarityDBEntityDAO.getTotalCountV4(processDate, isBkProcess, false, false);
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS,
						totalCount, elapsedSeconds, logId);

			} catch (Exception e) {
				System.out.println("Updated status in log table to " + ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE);
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
				System.out.println("Insert table failed, table: " + tmpSummaryTable + ", folder: " + storeDoneFilePath);

				moveFilesBackProcessingFolder();
				e.printStackTrace();
				return;
			}

			try {
				moveFileAndZip();
			} catch (Exception e) {
				clarityDBUploadLogDAO
						.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS_MOVE_FILE_FAIL, logId);
			} finally {
				deleteTempProcessingFolder();
			}

		} else {
			System.out.println("====== tmpTable is empty : " + tmpSummaryTable);
		}

	}

	private void moveFilesToProcessingFolder() {

		List<String> uniqueKeyList = new ArrayList<>();

		File targetFolder = new File(storeTempFilePath);
		if (targetFolder == null || !targetFolder.isDirectory()) {
			System.out.println("Folder is not exist, mkdir: " + storeTempFilePath);
			targetFolder.mkdir();
		}

		File doneFolder = new File(storeDoneFilePath);

		System.out.println("====moving files to processing folder, total file:" + doneFolder.length() + "!! from "
				+ storeDoneFilePath + " to " + storeTempFilePath);
		String uniqueKey = "";
		
		Integer number = 0;

		for (File gaFile : doneFolder.listFiles()) {
			try {
				if (StringUtils.startsWith(gaFile.getName(), "ga_v4_") && gaFile.isFile()) {

					// String prefix = "ga_v4_" + ownDomainId + "_" + gaInfoVO.getTrafficDate() +
					// "_" + gaInfoVO.getVersion() + "_" + (isBackProcess ? "bk_" : startHour +
					// "_");
					String fileName = gaFile.getName();
					String[] arrays = StringUtils.split(fileName, "_");

					Integer ownDomainId = NumberUtils.toInt(arrays[2]);
					Integer trafficDate = NumberUtils.toInt(arrays[3]);
					Integer version = NumberUtils.toInt(arrays[4]);

					uniqueKey = ownDomainId + "-" + trafficDate + "-" + version;

					if (CollectionUtils.isNotEmpty(uniqueKeyList) && uniqueKeyList.contains(uniqueKey)) {

						System.out.println("file is exist with the unique key:" + uniqueKey
								+ ", moving file to duplicate folder, file:" + gaFile.getName());
						FileUtils.moveFile(gaFile, new File(storeDuplicateFilePath + "/" + gaFile.getName()));
						continue;

					} else {
						uniqueKeyList.add(uniqueKey);
						number++;
						
						System.out.println("Adding to unique key list:" + uniqueKey);
						FileUtils.moveFile(gaFile, new File(storeTempFilePath + "/" + gaFile.getName()));
						processingFileList.add(storeTempFilePath + "/" + gaFile.getName());
						
						if(number >= 6000) {
							break;
						}
					}

				}
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

	}

	private void moveFilesBackProcessingFolder() {

		File processingFolder = new File(storeTempFilePath);

		System.out.println("====moving files back from processing folder, total file:" + processingFolder.length()
				+ "!! from " + storeTempFilePath + " to " + storeDoneFilePath);

		for (File gaFile : processingFolder.listFiles()) {
			try {
				FileUtils.moveFile(gaFile, new File(storeDoneFilePath + "/" + gaFile.getName()));
				processingFileList.add(storeDoneFilePath + "/" + gaFile.getName());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		deleteTempProcessingFolder();
	}

	private void moveFileAndZip() {

		File bkFolder = new File(storeBKFilePath);
		if (bkFolder == null || !bkFolder.isDirectory()) {
			System.out.println("BK folder is not exist, mkdir: " + storeBKFilePath);
			bkFolder.mkdir();
		}

		File tmpFile;
		File targetFile;

		for (String fileFullPath : processingFileList) {
			// zip file
			try {
				tmpFile = new File(fileFullPath);
				targetFile = new File(bkFolder + "/" + tmpFile.getName());

				FileUtils.moveFile(tmpFile, targetFile);

				System.out.println("zipped file : " + targetFile.getAbsolutePath());
				GZipUtil.zipFile(targetFile.getAbsolutePath());

				targetFile.delete();
				System.out.println("delete file : [" + fileFullPath + "]");
			} catch (Exception e) {
				System.out.println("delete file failed. file: [" + fileFullPath + "]");
				e.printStackTrace();
			}

		}

		deleteTempProcessingFolder();
	}

	private void deleteTempProcessingFolder() {
		// deleted the temp processing folder
		File tempFolder = new File(storeTempFilePath);

		if (tempFolder != null && tempFolder.isDirectory()) {
			tempFolder.delete();
		}
	}

//      private void postFile(String tableName, File tmpFile) throws Exception{
//              if (tmpFile != null && tmpFile.exists()) {
//
//                      String sql = gaClarityDBEntityDAO.getInsertSqlV4(tmpSummaryTable);
//
//                      String fileName = tmpFile.getName();
//                      String[] arrays = StringUtils.split(fileName, "_");
//
//                      Integer ownDomainId = NumberUtils.toInt(arrays[2]);
//                      Date trafficDate = FormatUtils.toDate(arrays[3], "yyyyMMdd");
//                      Integer version = NumberUtils.toInt(arrays[4]);
//
//                      //TODO query from clarityDB and find if there had data for that day.
//                      String checkExitSql = " select count() from dis_analytics_ga4_raw where log_date = '" + FormatUtils.formatDate(trafficDate, "yyyy-MM-dd") +
//                                      "' and versoin = " + version + " and domain_id = " + ownDomainId;
//
//                      System.out.println("SQL" + checkExitSql);
//
//                      //=======================================  clusterA ========================================
//                      String response = ClarityDBUtils.executeQueryCSV(ClarityDBConstants.NEW_GAHOST_04, checkExitSql);
//                      Integer count = 0;
//
//                      if (StringUtils.isNotBlank(response)) {
//                              try {
//                                      count = NumberUtils.toInt(StringUtils.split(response, "\n")[0]);
//                                      System.out.println("OID:" + ownDomainId + ", GA API response: count:" + count);
//
//                              } catch (Exception e) {
//                                      e.printStackTrace();
//                              }
//                      }
//
//                      if (count > 0) {
//                              System.out.println("==== data exist in clarityDB : OID:" + ownDomainId + ", trafficDate:" + trafficDate +
//                                              ", version:" + version + ", moving file to " + storeDuplicateFilePath + "/" + tmpFile.getName());
//
//                              FileUtils.moveFile(tmpFile, new File(storeDuplicateFilePath + "/" + tmpFile.getName()));
//
//                      } else {
//                              String str = ClarityDBUtils.postBatchInsert(ClarityDBConstants.NEW_GAHOST_04, sql, tmpFile.getAbsolutePath());
//                              System.out.println(str);
//
//                              if (StringUtils.contains(str, "DB::Exception")) {
//                                      System.out.println(sql);
//                                      throw new Exception(str);
//                              }
//                      }
//
//              } else {
//                      System.out.println("File not exists:" + tmpFile.getName());
//              }
//      }

	private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n')
			.withHeader(GaClarityDBEntityDAO.columnsV4);
	private final int maxInsertCount = 100000;

	private void processFile(File tmpFile) throws Exception {
		Integer batchNum = NumberUtils.toInt(StringUtils.removeStart(tmpSummaryTable, "local_analytics_ga4_historical_raw_"));

		int totalSize = 0;
		String fileName = tmpFile.getName();
		String[] arrays = StringUtils.split(fileName, "_");

		Integer ownDomainId = NumberUtils.toInt(arrays[2]);
		Date trafficDate = FormatUtils.toDate(arrays[3], "yyyyMMdd");
		Integer version = NumberUtils.toInt(arrays[4]);

		Integer count = gaClarityDBEntityDAO.checkExist(finalTableName, ownDomainId, trafficDate, version);

		if (count > 0) {
			System.out.println("==== data exist in clarityDB : OID:" + ownDomainId + ", trafficDate:" + trafficDate
					+ ", version:" + version + ", moving file to " + storeDuplicateFilePath + "/" + tmpFile.getName());

			FileUtils.moveFile(tmpFile, new File(storeDuplicateFilePath + "/" + tmpFile.getName()));
			return;
		}

		try {
			FileReader fr = new FileReader(tmpFile);
			CSVParser csvParser = new CSVParser(fr, csvFormat);

			List<GoogleAnalyticsV4Entity> gaEntities = new ArrayList<>();
			// don't read header
			List<CSVRecord> csvRecords = csvParser.getRecords();
			for (int i = 1; i < csvRecords.size(); i++) {
				CSVRecord csvRecord = csvRecords.get(i);
				GoogleAnalyticsV4Entity gaEntity;
				try {
					gaEntity = getGaEntity(csvRecord, batchNum);
				} catch (Exception e) {
					System.out.println("line i : " + i);
					e.printStackTrace();
					continue;
				}
				gaEntities.add(gaEntity);
				if (gaEntities.size() >= maxInsertCount) {
					gaClarityDBEntityDAO.insertForGaV4Batch(gaEntities, tmpSummaryTable);
					System.out.println("finish insert for top : " + maxInsertCount + " for file :" + tmpFile.getName());
					gaEntities.clear();
				}
			}
			if (CollectionUtils.isNotEmpty(gaEntities)) {
				gaClarityDBEntityDAO.insertForGaV4Batch(gaEntities, tmpSummaryTable);
				System.out.println("finish insert for left count :" + gaEntities.size());
				totalSize = gaEntities.size();
				gaEntities.clear();
			}

			csvParser.close();
			fr.close();
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}

	}

	private GoogleAnalyticsV4Entity getGaEntity(CSVRecord csvRecord, Integer batchNum) throws ParseException {
		GoogleAnalyticsV4Entity gaEntity = new GoogleAnalyticsV4Entity();

		String platFormDevice = csvRecord.get("device_category");
		String platform = "";
		String device = "";
		if (StringUtils.contains(platFormDevice, "/")) {
			device = StringUtils.trim(StringUtils.substringAfter(platFormDevice, "/"));
			platform = StringUtils.trim(StringUtils.substringBefore(platFormDevice, "/"));

		} else {
			System.out.println("==== device can not be split! platFormDevice:" + platFormDevice);
			device = platFormDevice;
		}

		gaEntity.setDomainId(NumberUtils.toInt(csvRecord.get("domain_id")));
		gaEntity.setLogDate(csvRecord.get("log_date"));
		gaEntity.setKeywordText(csvRecord.get("keyword_text"));
		gaEntity.setHostName(csvRecord.get("host_name"));
		gaEntity.setUri(csvRecord.get("uri"));
		gaEntity.setFullPageUrl(csvRecord.get("full_page_url"));
		gaEntity.setSessionMedium(csvRecord.get("session_medium"));
		gaEntity.setSessionSource(csvRecord.get("session_source"));
		gaEntity.setDeviceCategory(device);
		gaEntity.setPlatform(platform);
		gaEntity.setCountry(csvRecord.get("country"));
		gaEntity.setEventName(csvRecord.get("event_name"));
		gaEntity.setSessions(NumberUtils.toInt(csvRecord.get("sessions")));
		gaEntity.setEcommercePurchases(NumberUtils.toFloat(csvRecord.get("ecommerce_purchases")));
		gaEntity.setTransactions(NumberUtils.toInt(csvRecord.get("transactions")));
		gaEntity.setEngagedSessions(NumberUtils.toInt(csvRecord.get("engaged_sessions")));
		gaEntity.setEngagementRate(NumberUtils.toFloat(csvRecord.get("engagement_rate")));
		
		Integer eventCount = NumberUtils.toInt(csvRecord.get("event_count"));
		if (eventCount < 0) {
			eventCount = 0;
		}
		
		gaEntity.setEventCount(eventCount);
		gaEntity.setEventValue(NumberUtils.toFloat(csvRecord.get("event_value")));
		gaEntity.setVersoin(NumberUtils.toInt(csvRecord.get("versoin")));
		gaEntity.setDataSourceType(NumberUtils.toInt(csvRecord.get("data_source_type")));
		gaEntity.setBatchNo(batchNum);
		gaEntity.setGoalId(NumberUtils.toInt(csvRecord.get("goal_id")));
		gaEntity.setPageTitle(csvRecord.get("page_title"));
		gaEntity.setUrl(csvRecord.get("url"));
		gaEntity.setScreenPageviews(NumberUtils.toInt(csvRecord.get("screen_pageviews")));

		return gaEntity;
	}

	public Set<Integer> getDomainSet(String domainIds) {
		Set<Integer> returnDomain = new HashSet<Integer>();
		String[] dIds = domainIds.split(",");

		if (dIds != null && dIds.length > 0) {
			for (int i = 0; i < dIds.length; i++) {
				try {
					returnDomain.add(Integer.parseInt(dIds[i]));
				} catch (Exception e) {

				}
			}
		}
		return returnDomain;
	}

	private boolean checkTableForCurrentHour(Date date) {

		List<String> tableList = gaClarityDBEntityDAO.getTableListV4(date, isBkProcess, false, false);
		if (CollectionUtils.isNotEmpty(tableList)) {
			System.out.println("Table is already exist, need check manually!!! " + tableList.get(0));
			return false;
		}

		int tryCount = 3;
		int cnt = 0;

		while (!createClarityTempLocalTable(date) && cnt < tryCount) {
			cnt++;
			tmpSummaryTable = "";
		}
		if (StringUtils.isNotBlank(tmpSummaryTable)) {
			return true;
		} else {
			return false;
		}
	}

	private boolean createClarityTempLocalTable(Date date) {
		try {
			tmpSummaryTable = gaClarityDBEntityDAO.createTableV4(date, isBkProcess, false, false);

			System.out.println("Current no other script process, create new table:" + tmpSummaryTable);
		} catch (Exception e) {
			System.out.println("CreateTbaleFailed, startHour:" + startHour + ", statTableName:" + tmpSummaryTable);
			e.printStackTrace();
			return false;
		}
		return true;
	}

	private int getVersion(int ownDomianId, Date queryDate) {
		if (specialVersion > 0) {
			return specialVersion;
		} else {
			// TODO
			return version;
		}
	}

}
