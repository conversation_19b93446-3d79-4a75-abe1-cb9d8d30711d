package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.*;

/**
 * lawnstarter.com - 13297 |  upload geo locations
 * https://www.wrike.com/open.htm?id=1569330235
 * -  hao
 * qbase task
 */
public class AddKeywordTag_kwUrl_13297 {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;

    public AddKeywordTag_kwUrl_13297() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
    }

    private static String localFileFolder = "files/13297Files/addKW";
    private static String cityFile = "files/13297Files/location_13297.txt";
    private static int domainId = 13297;
    static boolean isTest = false;
    private static Map<String, Integer> locationMap = new HashMap<>();
    private List<String> missTagList = new ArrayList<>();

    public static void main(String[] args) {
        if (null != args && args.length > 0) {
            if (args[0].equals("true")) {
                isTest = true;
            }
        }
        AddKeywordTag_kwUrl_13297 in = new AddKeywordTag_kwUrl_13297();
        in.process();
    }

    private void process() {

        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    processFile(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        }


    }

    private void processFile(File file) throws Exception {

        readLinesFromFile(cityFile);

        System.out.println("Found file " + file.getName() + ", start to process !");
        List<ResourceBatchDetailEntity> lineList = parseExcel(file.getAbsolutePath());
        System.out.println("###lineList : " + lineList.size());
        if (lineList.size() > 0) {
            if (!isTest) {
                System.out.println("===###Qbasetask : add keyword tag geo ===========================");
                addKWTagGeoQBaseTast(lineList);
//                System.out.println("===###Qbasetask : add keyword url ===========================");
//                addKWUrlQBaseTast(lineList);
            }
        }
    }

    private void addKWTagGeoQBaseTast(List<ResourceBatchDetailEntity> keywordIdList) {

        int detailCnt = 2000;
        String se = "1-1-m";

        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            String kw = entity.getResourceMain();
            kw = FormatUtils.decodeKeyword(kw);
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceMain(kw);
            rbd.setResourceSubordinate(entity.getResourceSubordinate());
            rbd.setResourceAdditional(entity.getResourceAdditional());
            rbd.setResourceSearchengines(se);
            rbd.setResourceMd5(Md5Util.Md5(kw + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private void addKWUrlQBaseTast(List<ResourceBatchDetailEntity> keywordIdList) {

        String se = "1-1-m";
        int detailCnt = 2000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TARGET_URL;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceMain(entity.getResourceMain());
            rbd.setResourceSubordinate(entity.getUrl());
            rbd.setResourceSearchengines(se);
            rbd.setResourceMd5(Md5Util.Md5(entity.getResourceMain() + entity.getUrl() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private List<ResourceBatchDetailEntity> parseExcel(String file) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser(',');
        List<ResourceBatchDetailEntity> dateList = new ArrayList<>();

        int lineCnt = 0;
        for (String line : lines) {
            lineCnt++;
            String[] cols = parser.parseLine(line);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[1], "URL")) {
                    System.out.println(" line :" + lineCnt + " head ");
                } else {

                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    KeywordEntity kwEntity = keywordEntityDAO.checkNationalAndGeoKw(kwStr, domainId);
                    if (null == kwEntity) {
                        System.out.println("===###ingoreKw : " + kwStr);
                        missTagList.add(kwStr);
                        continue;
                    }


                    String tagName = cols[4];
                    String url = cols[1];
                    String location = cols[2];
                    String state = cols[3];
                    Integer cityId = null;
                    String cityName = location + ", " + state;
                    if (null != locationMap.get(cityName)) {
                        cityId = locationMap.get(cityName);
                        continue;
                    } else {
                        if (location.equals("DeBary")){
                            cityId = 305860;
                        }else if (location.equals("DeSoto")){ //   Desoto, TX	306612
                            cityId = 306612;
                        }else if (location.contains("Delhi")){
                            cityId = 323256;
                        }else if (location.equals("McAllen")){ //Mcallen, TX	301968
                            cityId = 301968;
                        }else if (location.equals("McKinney")){ //Mckinney, TX	307291
                            cityId = 307291;
                        }
                        System.out.println(" ===###MiassCity : " + cityName + " ,kwStr :  " + kwStr);
                    }




                    ResourceBatchDetailEntity en = new ResourceBatchDetailEntity();
                    System.out.println(" tagName :" + tagName + " kwStr : " + kwStr + ", city : " + cityId + " , url : "+ url);
                    en.setResourceMain(kwStr);
                    en.setResourceAdditional(String.valueOf(cityId));
                    en.setResourceSubordinate(tagName);
                    en.setUrl(url);
                    dateList.add(en);
//                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }

    public static void readLinesFromFile(String filePath) {
        //(1) Logan, UT 84321, USA
        //(2) Lehi, UT 84043, USA
        //(3) Bismarck, ND 58504, USA

        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] parts = line.split("\t");
                locationMap.put(parts[0], Integer.parseInt(parts[1]));
            }
        } catch (FileNotFoundException e) {
            System.out.println("文件未找到: " + filePath);
            e.printStackTrace();
        } catch (IOException e) {
            System.out.println("读取文件时发生错误: " + filePath);
            e.printStackTrace();
        }
        System.out.println("===###CityMap : " + locationMap.size());
    }
}
