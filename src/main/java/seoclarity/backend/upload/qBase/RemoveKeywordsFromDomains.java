package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Quote LLC Suspended Domains | Delete Keywords in Bulk
 * https://www.wrike.com/open.htm?id=1063663369
 * -  hao
 * qbase task
 * 20230223
 *
 *
 *
 * //查询 rankcheck
 * select ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device,count(*)
 *
 * from rc_keyword_se_rel ksr
 * left join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId
 * where  kdr.ownDomainId = 11251
 * group by ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device
 * order by ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device
 *
 *查询 keyword-tag关联关系
 * select ctk.id,ctk.ownDomainId,ctk.keywordType,ctk.keywordId,ctk.keywordRankcheckId,ctk.locationId,ctk.groupTagId,tag.tag_name,tk.keyword_name
 * from cdb_tracked_keyword ctk
 * left join t_keyword  tk on ctk.ownDomainId = tk.own_domain_id  and ctk.keywordId = tk.id
 * left join t_group_tag tag on ctk.groupTagId= tag.id
 * where ctk.ownDomainId  = 4  and tag.tag_name =  'TestDeleteKwFromTag_tag_20220412_ewain_oid4_a2'
 * order by groupTagId desc , locationId desc , keywordId
 *
 *
 * 查询ranking计费关键字数量
 * select keywordType, searchEngineId,languageId,device, count(*)
 *
 * from cdb_keyword_search_engine_rel rel
 * left join t_keyword  tk on rel.ownDomainId = tk.own_domain_id  and rel.keywordId = tk.id
 * where rel.ownDomainId  = 8414 and tk.keyword_name like 'EwainTestDelKwFromGeoByDevice_8414_20220429c%'
 * group by keywordType, searchEngineId,languageId,device
 * order by keywordType, searchEngineId,languageId,device
 *
 *
 *
 *
 */
public class RemoveKeywordsFromDomains {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;

    public RemoveKeywordsFromDomains() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }
    private static Integer[] domains10886 = new Integer[]{
            10886
    };
    private static Integer[] domains = new Integer[]{
            10906,
            10931,
            10932,
            10933,
            10942,
            10943,
            10944,
            10945,
            10946,
            10947,
            10948,
            10949,
            10950,
            10951,
            10952,
            10953,
            10954,
            10955,
            10956,
            10957,
            10958,
            10959,
            10960,
            10961,
            10962,
            10963,
            10964,
            10965,
            10966,
            10967,
            10993
    };

    public static void main(String[] args) {
        RemoveKeywordsFromDomains in = new RemoveKeywordsFromDomains();
        in.process();
    }

    private void process() {
        System.out.println("****************** gogogo *****************");

        for (Integer domainId : domains) {
            try {
                System.out.println("####processDomain : " + domainId);
                processData(domainId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void processData(Integer domainId) throws Exception {

        List<KeywordEntity> lineList = keywordEntityDAO.getKeywordByDomainId(domainId);
        System.out.println("###KwCntOfdomain :" + domainId + " kwcnt : " + lineList.size());
        if (lineList.size()>0) {
            getQBaseTast(lineList, domainId);
        }
    }


    private void getQBaseTast(List<KeywordEntity> keywordIdList, Integer domainId) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_KEYWORD;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (KeywordEntity kw : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(kw.getId());
            rbd.setResourceMd5(Md5Util.Md5(kw.getId() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

}
