package seoclarity.backend.upload.qBase;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.google.gson.Gson;

import seoclarity.backend.clarity360.exception.TaskException;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.RcKeywordSeRelEntityDAO;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.onetime.conductor.entity.ConductorKeywordInfo;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * Amazon.com 11892 | Change Rank-Checking Search Engine
 * https://www.wrike.com/open.htm?id=1185583240
 * -  hao
 * qbase task keyword
 * <p>
 */
public class AddKeywordTagConductor {

    private static int oid = 11892;
    private static int engineId = 1;
    private static int languageId = 1;
    private static String device = "d";
    private static int cityId = 0;

    private RcKeywordSeRelEntityDAO rcKeywordSeRelEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;

    public AddKeywordTagConductor() {
        rcKeywordSeRelEntityDAO = SpringBeanFactory.getBean("rcKeywordSeRelEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    public static void main(String[] args) {
        AddKeywordTagConductor in = new AddKeywordTagConductor();
        for (String account : desktopAccountInfos) {
                String aId = account.split("_")[0];
                in.process(aId);
        }
    }

    public static Map<Integer, Integer> locaitonMap = new HashMap<Integer, Integer>();
    public static Map<Integer, Integer> accIdDomainIdMap = new HashMap<Integer, Integer>();
    public static Map<Integer, String> domainNameMap = new HashMap<Integer, String>();

    private static final String LOCAL = "/home/<USER>/conductor";
    private static String KEYWORD_FILE_NAME_TEMPLATE = "KBB_KEYWORD_%s.txt";
    
        private static List<String> desktopAccountInfos = new ArrayList<String>();
    static {
//        desktopAccountInfos.add("16088_79964");
//        desktopAccountInfos.add("19564_96403");
//        desktopAccountInfos.add("20282_100859");
        desktopAccountInfos.add("20840_105574");
    
        locaitonMap.put(3, 0);
        locaitonMap.put(399, 308962);
        locaitonMap.put(795, 310599);
    
//      accIdDomainIdMap.put(16088, 13649);
//      accIdDomainIdMap.put(19564, 13651);
//      accIdDomainIdMap.put(20282, 13653);
//      accIdDomainIdMap.put(20840, 13648);
    

//        accIdDomainIdMap.put(16088, 13475);
//        accIdDomainIdMap.put(19564, 13650);
//        accIdDomainIdMap.put(20282, 13652);
        accIdDomainIdMap.put(20840, 13648);
    
//        domainNameMap.put(13649, "argos.co.uk");
//        domainNameMap.put(13651, "tuclothing.sainsburys.co.uk");
//        domainNameMap.put(13653, "habitat.co.uk");
        domainNameMap.put(13648, "sainsburys.co.uk");
    
    }
    private void process(String accId) {
    
        List<String[]> desktopKeywordList = new ArrayList<String[]>();
        List<String[]> mobileKeywordList = new ArrayList<String[]>();
        List<String[]> desktopGeoKeywordList = new ArrayList<String[]>();
        List<String[]> mobileGeoKeywordList = new ArrayList<String[]>();
       
        String filePath = LOCAL + "/" + String.format(KEYWORD_FILE_NAME_TEMPLATE, accId);
    
        File keywordFile = new File(filePath);
        if (keywordFile == null || !keywordFile.isFile()) {
                        throw new TaskException("File not found!" + filePath);
                }
    
        try {
            String content = "";
            StringBuffer json = new StringBuffer();
            BufferedReader bf = new BufferedReader(new FileReader(keywordFile));
            while (content != null) {
                content = bf.readLine();

                if (content == null) {
                        break;
                }

                json.append(content);

            }
            bf.close();

            ConductorKeywordInfo[] keywordArray = new Gson().fromJson(json.toString(), ConductorKeywordInfo[].class);

            Integer locationId = 0;
            for(ConductorKeywordInfo conductorKeywordInfo : keywordArray) {
                if (conductorKeywordInfo.getIsActive()) {

                    locationId = locaitonMap.get(conductorKeywordInfo.getLocationId());

                    if (locationId == 0) {
                        if (StringUtils.equalsIgnoreCase(conductorKeywordInfo.getDeviceId(), "1")) {

                                desktopKeywordList.add(new String[] {conductorKeywordInfo.getQueryPhrase()});
                        } else if (StringUtils.equalsIgnoreCase(conductorKeywordInfo.getDeviceId(), "2")) {

                                mobileKeywordList.add(new String[] {conductorKeywordInfo.getQueryPhrase()});
                        }
                    } else {
                        if (StringUtils.equalsIgnoreCase(conductorKeywordInfo.getDeviceId(), "1")) {

                                desktopGeoKeywordList.add(new String[] {conductorKeywordInfo.getQueryPhrase(), locationId + ""});
                        } else if (StringUtils.equalsIgnoreCase(conductorKeywordInfo.getDeviceId(), "2")) {

                                mobileGeoKeywordList.add(new String[] {conductorKeywordInfo.getQueryPhrase(), locationId + ""});
                        }
                    }
                }
            }

            System.out.println("desktopKeywordList:" + desktopKeywordList.size());
            System.out.println("mobileKeywordList:" + mobileKeywordList.size());
            System.out.println("desktopGeoKeywordList:" + desktopGeoKeywordList.size());
            System.out.println("mobileGeoKeywordList:" + mobileGeoKeywordList.size());

        } catch (Exception e) {
                e.printStackTrace();
        }

        if (CollectionUtils.isNotEmpty(desktopKeywordList)) {
                getQBaseTast(desktopKeywordList, accIdDomainIdMap.get(NumberUtils.toInt(accId)), "6-8-d", ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD);
                }
    
        if (CollectionUtils.isNotEmpty(mobileKeywordList)) {
                getQBaseTast(mobileKeywordList, accIdDomainIdMap.get(NumberUtils.toInt(accId)), "6-8-m", ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD);
        
        }
    
        if (CollectionUtils.isNotEmpty(desktopGeoKeywordList)) {
                getQBaseTast(desktopGeoKeywordList, accIdDomainIdMap.get(NumberUtils.toInt(accId)), "6-8-d", ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO);
    
        }
    
        if (CollectionUtils.isNotEmpty(mobileGeoKeywordList)) {
                getQBaseTast(mobileGeoKeywordList, accIdDomainIdMap.get(NumberUtils.toInt(accId)), "6-8-m", ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO);
    
        }
    
    }
    
    /**
     * we need set engine value to info and detail!
     * info:
     *      engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
     * detail:
     *      resourceMain: keywordName (raw keyword name,  please don't encode!!)
     *      resourceSubordinate: tagName (optional)
     *      resourceAdditional: cityId
     *      resource_searchengines: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
    public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO = 10142;
     */

    private void getQBaseTast(List<String[]> keywordIdList, int oid, String se, Integer operationType) {
    
        if (oid == 0 || CollectionUtils.isEmpty(keywordIdList)) {
                        return;
                }
    
        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        Date rbiDate = new Date();
//        String se = "6-8-d,217-1-m";

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (String keyword[] : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(keyword[0]);
            rbd.setResourceMd5(Md5Util.Md5(String.valueOf(oid) + keyword + se + operationType + (keyword.length >=2 ? keyword[1] : "") + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            rbd.setResourceSearchengines(se);
            if (operationType.intValue() == ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO) {
                rbd.setResourceAdditional(keyword[1]);
                        }
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());

    }

}