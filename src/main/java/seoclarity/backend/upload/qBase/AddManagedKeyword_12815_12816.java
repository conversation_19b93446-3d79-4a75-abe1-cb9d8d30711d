package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.RcKeywordSeRelEntityDAO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.util.*;

/**
 * Amazon.com 11892 | Change Rank-Checking Search Engine
 * https://www.wrike.com/open.htm?id=1185583240
 * -  hao
 * qbase task keyword
 * <p>
 */
public class AddManagedKeyword_12815_12816 {


    private RcKeywordSeRelEntityDAO rcKeywordSeRelEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;

    public AddManagedKeyword_12815_12816() {
        rcKeywordSeRelEntityDAO = SpringBeanFactory.getBean("rcKeywordSeRelEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    public static void main(String[] args) {
        AddManagedKeyword_12815_12816 in = new AddManagedKeyword_12815_12816();
        in.process1();
    }

    private void process1() {

        String filePath = "files/12815Files";

        try {
            System.out.println("=========================Process desktop===================================");
            for (File file : new File(filePath).listFiles()) {

                if (file.getName().contains(".csv")) {
                    System.out.println("====================== gogogo ======================");
                    System.out.println("file name : " + file.getName());
                    int oid = 12815;
                    int engine = 6;
                    int language = 8;
                    if (file.getAbsolutePath().contains("Icelolly")) {
                        oid = 12815;
                    } else {
                        oid = 12816;
                    }

                    Set<String> keywordSet_m =   parseExcel(file.getAbsolutePath(), oid,"mobile");

                    try {
                        getQBaseTast(keywordSet_m, oid,"6-8-m");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    Set<String> keywordSet_d =   parseExcel(file.getAbsolutePath(), oid,"desktop");

                    try {
                        getQBaseTast(keywordSet_d, oid,"6-8-d");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    System.out.println("===###keywordSet_m : " + keywordSet_m.size() + " , keywordSet_d : " + keywordSet_d.size());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private Set<String> parseExcel(String file, int oid,String device) throws Exception {
        Set<String> keywordSet = new HashSet<String>();
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser(',');
        int lineCnt = 0;
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            lineCnt++;
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
//                CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(fileV2)), csvFullFormat);

                String[] cols = parser.parseLine(line);

                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Location") && StringUtils.equalsIgnoreCase(cols[2], "Device")) {
                    System.out.println(" 读取到第一行，啥也不干");
                    //Location,Search Engine,Device,Date,Keyword,Search Volume,Web Rank,True Rank,Ranking URL
                    //United Kingdom,Google,Desktop,01/01/2024,0 deposit holidays,6600,100,100,""
                } else if (cols[2].equalsIgnoreCase(device)) {

                    String kwStr = cols[4];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    keywordSet.add(kwStr);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return keywordSet;
    }

    private void getQBaseTast(Set<String> keywordIdList, int oid,String se) {
        int detailCnt = 2000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_OPTIONAL_TAG;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (String keyword : keywordIdList) {

            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(keyword);
            rbd.setResourceMd5(Md5Util.Md5(keyword + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            rbd.setResourceSearchengines(se);
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

    }


    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
