package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Lamark Media | Bulk remove Keywords and Pages
 * https://www.wrike.com/open.htm?id=1337570687
 * -  hao
 * qbase task
 * 20230223
 * <p>
 * <p>
 * 1.delete kw: OPERATION_TYPE_BATCH_DELETE_KEYWORD = 10112;
 * get kwId: select id from t_keyword where type = 1 and rank_check in (1,9) and own_domain_id = xxx;
 * 2.delete url: OPERATION_TYPE_BATCH_DELETE_TARGET_URL = 12011;
 * get urlId: select id from t_keyword where type = 1 and status = 1 and own_domain_id = xxx;
 * 3.delete tag: OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID = 13011;
 * <p>
 * <p>
 * <p>
 * //查询 rankcheck
 * select ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device,count(*)
 * <p>
 * from rc_keyword_se_rel ksr
 * left join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId
 * where  kdr.ownDomainId = 11251
 * group by ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device
 * order by ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device
 * <p>
 * 查询 keyword-tag关联关系
 * select ctk.id,ctk.ownDomainId,ctk.keywordType,ctk.keywordId,ctk.keywordRankcheckId,ctk.locationId,ctk.groupTagId,tag.tag_name,tk.keyword_name
 * from cdb_tracked_keyword ctk
 * left join t_keyword  tk on ctk.ownDomainId = tk.own_domain_id  and ctk.keywordId = tk.id
 * left join t_group_tag tag on ctk.groupTagId= tag.id
 * where ctk.ownDomainId  = 4  and tag.tag_name =  'TestDeleteKwFromTag_tag_20220412_ewain_oid4_a2'
 * order by groupTagId desc , locationId desc , keywordId
 * <p>
 * <p>
 * 查询ranking计费关键字数量
 * select keywordType, searchEngineId,languageId,device, count(*)
 * <p>
 * from cdb_keyword_search_engine_rel rel
 * left join t_keyword  tk on rel.ownDomainId = tk.own_domain_id  and rel.keywordId = tk.id
 * where rel.ownDomainId  = 8414 and tk.keyword_name like 'EwainTestDelKwFromGeoByDevice_8414_20220429c%'
 * group by keywordType, searchEngineId,languageId,device
 * order by keywordType, searchEngineId,languageId,device
 */
public class RemoveKeywordsTagAndUrlTag {
    private static int detailCnt = 2000;

    private KeywordEntityDAO keywordEntityDAO;
    private TargetUrlEntityDAO targetUrlEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;

    public RemoveKeywordsTagAndUrlTag() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    private static Integer[] domains11052 = new Integer[]{
            11052
    };
    private static Integer[] domains02 = new Integer[]{
            11051,
            11053,
            11054,
            11055,
            11059,
            11060,
            11062,
            11063,
            11064,
            11065,
            11066,
            11067,
            11077
    };
    private static Integer[] domains03 = new Integer[]{
            11081,
            11086,
            11087,
            11088,
            11089,
            11090,
            11091,
            11092,
            11093,
            11096
    };
    private static Integer[] domains04 = new Integer[]{
            11101,
            11102,
            11103,
            11136,
            11137,
            11191,
            11602,
            11626,
            11657,
            11765,
            11785,
            11851,
            11852
    };
    private static Integer[] domains05 = new Integer[]{
            12046,
            12047,
            12048,
            12049,
            12050,
            12051,
            12052,
            12053,
            12054,
            12055,
            12056,
            12057,
            12058,
            12067
    };
    private static Integer[] domains06 = new Integer[]{
            12078,
            12220,
            12668,
            12734,
            12780,
            12783,
            12789,
            12867
    };

    public static void main(String[] args) {
        RemoveKeywordsTagAndUrlTag in = new RemoveKeywordsTagAndUrlTag();
        String num = args[0];
        in.process(num);
    }

    private void process(String num) {
        System.out.println("****************** gogogo *****************");
        Integer[] domains = domains11052;
        if (num.equals("02")) {
            domains = domains02;
        } else if (num.equals("03")) {
            domains = domains03;
        } else if (num.equals("04")) {
            domains = domains04;
        } else if (num.equals("05")) {
            domains = domains05;
        } else if (num.equals("06")) {
            domains = domains06;
        }

        for (Integer domainId : domains) {
            try {
                System.out.println("####processDomain : " + domainId);
                processData(domainId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void processData(Integer domainId) throws Exception {
//1.delete kw: OPERATION_TYPE_BATCH_DELETE_KEYWORD = 10112;
//get kwId: select id from t_keyword where type = 1 and rank_check in (1,9) and own_domain_id = xxx;
//2.delete url: OPERATION_TYPE_BATCH_DELETE_TARGET_URL = 12011;
//get urlId: select id from t_target_url where type = 1 and status = 1 and own_domain_id = xxx;
//3.delete tag: OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID = 13011;

        List<KeywordEntity> lineList = keywordEntityDAO.getKeywordByDomainId(domainId);
        System.out.println("###Delete keyword  :" + domainId + " kwcnt : " + lineList.size());
        if (lineList.size() > 0) {
            deleteKeywordTask(lineList, domainId);
            System.out.println("===insert detail success, size:" + lineList.size());
        }


        List<TargetUrlEntity> targetUrlsList = targetUrlEntityDAO.getUrlsByDomain(domainId);
        System.out.println("###Delete Url  :" + domainId + " urlcnt : " + targetUrlsList.size());
        if (targetUrlsList.size() > 0) {
            deleteUrlTask(targetUrlsList, domainId);
            System.out.println("===insert detail success, size:" + targetUrlsList.size());
        }

        List<GroupTagEntity> allTagsList = groupTagEntityDAO.getAllTagEntityByDomain(domainId);
        System.out.println("###Delete Tags  :" + domainId + " tagcnt : " + allTagsList.size());
        if (allTagsList.size() > 0) {
            deleteTagsTask(allTagsList, domainId);
            System.out.println("===insert detail success, size:" + allTagsList.size());
        }


    }


    private void deleteKeywordTask(List<KeywordEntity> keywordIdList, Integer domainId) {

        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_KEYWORD;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert deleteKeywordTask success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (KeywordEntity kw : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(kw.getId());
            rbd.setResourceMd5(Md5Util.Md5(kw.getId() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);

    }


    private void deleteUrlTask(List<TargetUrlEntity> removeUrlsList, Integer domainId) {

        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_TARGET_URL;

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(new Date());
        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert deleteUrlTask  success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (TargetUrlEntity urlEntity : removeUrlsList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(urlEntity.getId());
            rbd.setResourceMain(urlEntity.getUrl());
            rbd.setResourceMd5(Md5Util.Md5(domainId + urlEntity.getUrl() + new Date().getTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }

        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);


    }

    private void deleteTagsTask(List<GroupTagEntity> keywordIdList, Integer domainId) {

        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);
        //t_group_tag.dynamic_flg =0?1  ==1?2
        resourceBatchInfoEntity.setCustomFlag(2);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (GroupTagEntity tag : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId((long) tag.getId());
            rbd.setResourceCategory(3);
            rbd.setResourceMd5(Md5Util.Md5(tag.getId() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));

            resourceBatchDetailEntityList.add(rbd);
            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);

    }

}
