package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.RcKeywordSeRelEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Delete all Baidu managed keywords of test OID 7741
 * https://www.wrike.com/open.htm?id=1367448783
 * -  hao
 * qbase task  national
 */
public class RemoveKeywordsFrom7741 {

    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private RcKeywordSeRelEntityDAO rcKeywordSeRelEntityDAO;
    private KeywordEntityDAO keywordEntityDAO;

    public RemoveKeywordsFrom7741() {
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        rcKeywordSeRelEntityDAO = SpringBeanFactory.getBean("rcKeywordSeRelEntityDAO");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
    }

    private static int domainId = 7741;

    public static void main(String[] args) {
        RemoveKeywordsFrom7741 in = new RemoveKeywordsFrom7741();
        in.process();
    }

    private void process() {

        try {
            removeKeywordTask();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void removeKeywordTask() {
        List<RcKeywordSeRelEntity> keywords = rcKeywordSeRelEntityDAO.getKeywords(150, 6, "d", 0, domainId);
        System.out.println("===###keywordList : " + keywords.size());

        List<String> keywordNames = keywords.stream()
                .map(RcKeywordSeRelEntity::getKeywordText)
                .collect(Collectors.toList());

        System.out.println("===###keywordNames : " + keywordNames.size());

        List<KeywordEntity> keywordList = keywordEntityDAO.getByKeywordNameListTest(domainId, keywordNames);

//        List<KeywordEntity> allResults = new ArrayList<>();
//        List<String> query30 = new ArrayList<>();
//        for (String s : keywordNames) {
//            query30.add(s);
//            if (query30.size() >= 30) {
//                System.out.println("===###query30 : 每30个关键字查一次 ================ " + query30.size());
//                List<KeywordEntity> keywordList = keywordEntityDAO.getByKeywordNameListTest(domainId, query30);
//                allResults.addAll(keywordList);
//                query30 = new ArrayList<>();
//            }
//
//        }

//        System.out.println("===###allResults : " + allResults.size());

        if (keywordList.size() > 0) {
            getQBaseDelKeyword(keywordList);
        }

    }


    private void getQBaseDelKeyword(List<KeywordEntity> keywordIdList) {

        int detailCnt = 2000;
        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_KEYWORD;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        int i = 0;
        for (KeywordEntity kw : keywordIdList) {
            i++;
            System.out.println("===### kid  :" + kw.getId());
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(kw.getId());
            rbd.setResourceCategory(1);
            rbd.setResourceMd5(Md5Util.Md5(kw.getId() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
