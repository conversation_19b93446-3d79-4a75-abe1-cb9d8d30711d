package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.rankcheck.GeoMasterEntity;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Indeed | Tags + Pages Removal
 * https://www.wrike.com/open.htm?id=1396453057
 * -  hao
 * qbase task
 * 20230223
 * <p>
 * <p>
 * 2.delete url: OPERATION_TYPE_BATCH_DELETE_TARGET_URL = 12011;
 * get urlId: select id from t_keyword where type = 1 and status = 1 and own_domain_id = xxx;
 * 3.delete tag: OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID = 13011;
 * <p>
 * <p>
 * <p>
 * //查询 rankcheck
 * select ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device,count(*)
 * <p>
 * from rc_keyword_se_rel ksr
 * left join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId
 * where  kdr.ownDomainId = 11251
 * group by ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device
 * order by ksr.keywordType,ksr.searchEngineId,ksr.languageId,ksr.device
 * <p>
 *
 * <p>
 * 查询ranking计费关键字数量
 * select keywordType, searchEngineId,languageId,device, count(*)
 * <p>
 * from cdb_keyword_search_engine_rel rel
 * left join t_keyword  tk on rel.ownDomainId = tk.own_domain_id  and rel.keywordId = tk.id
 * where rel.ownDomainId  = 8414 and tk.keyword_name like 'EwainTestDelKwFromGeoByDevice_8414_20220429c%'
 * group by keywordType, searchEngineId,languageId,device
 * order by keywordType, searchEngineId,languageId,device
 */
public class RemoveUrlTag_8711_8762 {
    private static int detailCnt = 2000;
    private static List<Long> tagIds = new ArrayList<>();
    private static Integer oid = 8711;
    private static boolean isTest = false;
    private static String missTags = "";

    private static Integer sheetIndex = 0;
    private GroupTagRelationEntityDAO groupTagRelationEntityDAO;
    private TargetUrlEntityDAO targetUrlEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;

    public RemoveUrlTag_8711_8762() {
        groupTagRelationEntityDAO = SpringBeanFactory.getBean("groupTagRelationEntityDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    private static String localFileFolder = "files/8762Files/qbaseTask";

    public static void main(String[] args) {
        RemoveUrlTag_8711_8762 in = new RemoveUrlTag_8711_8762();
        if (null != args && args.length > 0) {
            if (args[0].equals("true")) {
                isTest = true;
            }
            oid = Integer.parseInt(args[1]);
            sheetIndex = Integer.parseInt(args[2]);
        }
        in.process();
    }

    private void process() {
        System.out.println("****************** gogogo *****************");
        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    processFile(file, oid, sheetIndex);

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }


    }

    private void processFile(File file, Integer oid, Integer sheetId) throws Exception {
        System.out.println("Found file " + file.getName() + ", start to process !");
        List<Long> lineList = parseExcel(file.getAbsolutePath(), oid, sheetId);
        System.out.println("===###lineList : " + lineList.size());
        System.out.println("===###tags : " + tagIds.size());
        System.out.println("===###missTags " + missTags);
        if (lineList.size() > 0 && !isTest) {
            try {
                deleteUrlTask(lineList, oid);
            }catch (Exception e){
                e.printStackTrace();
            }

        }
        if (tagIds.size() > 0 && !isTest) {
            try {
                deleteTagsTask(oid);
            }catch (Exception e){
                e.printStackTrace();
            }

        }
    }


    private List<Long> parseExcel(String file, Integer oid, Integer sheetId) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(sheetId);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        List<Long> dataList = new ArrayList<>();
        int cnt = 0;
        for (int n = start; n <= end; n++) {

            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            String[] cols = getStringArray(row);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Tag name")) {
                    System.out.println(" line :" + cnt + " head ");
                } else {
                    ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                    String tagName = cols[0];
                    if (StringUtils.isBlank(tagName)) {
                        System.out.println("=Skip empty tagName:" + tagName);
                        continue;
                    }
                    List<GroupTagRelationEntity> urlListOfTag = groupTagRelationEntityDAO.getGroupTagRelByTagName(tagName, GroupTagRelationEntity.RESOURCE_TYPE_TARGETURL, oid);
                    if (urlListOfTag.size() > 0) {
                        tagIds.add(Long.valueOf(urlListOfTag.get(0).getGroupTagId()));
                    } else {
                        missTags = missTags + " , " + tagName + " , ";
                    }

                    List<Long> resourceIds = urlListOfTag.stream()
                            .map(GroupTagRelationEntity::getResourceId)
                            .collect(Collectors.toList());
                    dataList.addAll(resourceIds);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dataList;
    }


    private void deleteUrlTask(List<Long> removeUrlsList, Integer domainId) {

        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_TARGET_URL;

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(new Date());
        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert deleteUrlTask  success, infoId: " + id + " size : " + removeUrlsList.size());

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (Long urlId : removeUrlsList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(urlId);
            rbd.setResourceMd5(Md5Util.Md5(urlId + domainId + new Date().getTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }

        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);


    }

    private void deleteTagsTask(Integer domainId) {

        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);
        //t_group_tag.dynamic_flg =0?1  ==1?2
        resourceBatchInfoEntity.setCustomFlag(2);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id + " size : " + tagIds.size());

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (Long tagId : tagIds) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(tagId);
            rbd.setResourceCategory(3);
            rbd.setResourceMd5(Md5Util.Md5(tagId + domainId + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));

            resourceBatchDetailEntityList.add(rbd);
            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);

    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

}
