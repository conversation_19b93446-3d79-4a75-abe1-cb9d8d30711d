package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.GeoMasterEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.rankcheck.GeoMasterEntity;
import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.util.*;

/**
 * rover.com 7605 | Keywords to geo-locations
 * https://www.wrike.com/open.htm?id=1270297316
 * -  hao
 * qbase task geokeywordtag
 * <p>
 * domain_geo_mapping
 * keyword_geo_mapping
 * cdb_tracked_keyword
 * cdb_keyword_search_engine_rel
 * <p>
 * SELECT
 * keywordType kwType,
 * concat(
 * searchEngineId,
 * '_',
 * languageId,
 * '_',
 * device
 * ) SE,
 * frequency freq,
 * count(*),
 * count(DISTINCT rel.keywordId) kwCnt
 * FROM
 * rc_keyword_se_rel rel
 * JOIN rc_keyword_domain_rel domainRel ON rel.id = domainRel.keywordSeRelId
 * WHERE
 * ownDomainId = 7605
 * GROUP BY
 * keywordType,
 * SE,
 * frequency;
 */
public class AddGeoKeywordTag_Rover {

    private static Map<String, Integer> location13543 = new HashMap<>();
    private static Map<String, Integer> location13544 = new HashMap<>();
    private static Map<String, Integer> locationMap = new HashMap<>();

    List<ResourceBatchDetailEntity> notGeoList = new ArrayList<>();
    List<ResourceBatchDetailEntity> onlyKwList = new ArrayList<>();
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;



    static {

        location13543.put("kolding", 323201);
        location13543.put("aarhus", 323197);
        location13543.put("københavn", 323196);
        location13543.put("randers", 323202);
        location13543.put("odense", 323198);
        location13543.put("aalborg", 323199);
        location13543.put("horsens", 323203);
        location13543.put("esbjerg", 323200);

        location13544.put("galway", 323207);
        location13544.put("drogheda", 323205);
        location13544.put("dublin", 323206);
        location13544.put("limerick", 323209);
        location13544.put("cork", 323204);
        location13544.put("kilkenny", 323208);
        location13544.put("waterfod", 323210);


    }


    public AddGeoKeywordTag_Rover() {
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    private static String localFileFolder = "files/roverFiles/qbaseTask";
    private static Integer oid = 13543;
    private static boolean isTest = false;

    public static void main(String[] args) {
        if (null != args && args.length > 0) {
            oid = Integer.parseInt(args[0]);
            if (args[1].equals("true")) {
                isTest = true;
            }
        }
        AddGeoKeywordTag_Rover in = new AddGeoKeywordTag_Rover();
        in.process();
    }

    private void process() {

        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    if (file.getName().contains(String.valueOf(oid))) {
                        if (oid == 13543) {
                            locationMap.putAll(location13543);
                        } else if (oid == 13544) {
                            locationMap.putAll(location13544);
                        }
                        processFile(file);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void processFile(File file) throws Exception {
        System.out.println("Found file " + file.getName() + ", start to process !");
        List<ResourceBatchDetailEntity> lineList = parseExcel(file.getAbsolutePath());
        System.out.println("###lineList : " + lineList.size());

        if (lineList.size() > 0 && !isTest) {
            System.out.println("===########geo qbase run ====================");
            getQBaseTast(lineList, oid);

            if (notGeoList.size() > 0) {
                System.out.println("===########keywordTag qbase run ====================");
                keywordTagQbase(notGeoList, oid);
            }

            if (onlyKwList.size() > 0) {
                System.out.println("===########OnlyKw qbase run ====================");
                addKeyword(onlyKwList, oid);
            }
        } else {
            System.out.println(" ============== test ==================");
        }
    }

    private void getQBaseTast(List<ResourceBatchDetailEntity> keywordIdList, int oid) {

        int detailCnt = 2000;
        String se = "";
        if (oid == 13543) {
            se = "9-10-m,9-10-d";
        } else if (oid == 13544) {
            se = "20-21-d,20-21-m";
        }
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            String kw = entity.getResourceMain();
            kw = FormatUtils.decodeKeyword(kw);
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(kw);
            rbd.setResourceSubordinate(entity.getResourceSubordinate());
            rbd.setResourceAdditional(entity.getResourceAdditional());
            rbd.setResourceSearchengines(se);
            rbd.setResourceMd5(Md5Util.Md5(kw + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);

        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }


    private void keywordTagQbase(List<ResourceBatchDetailEntity> keywordIdList, int oid) {

        int detailCnt = 2000;
        String se = "";
        if (oid == 13543) {
            se = "9-10-m,9-10-d";
        } else if (oid == 13544) {
            se = "20-21-d,20-21-m";
        }
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG;
        Date rbiDate = new Date();
        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            String kw = entity.getResourceMain();
            kw = FormatUtils.decodeKeyword(kw);
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(kw); // 不能encode
            rbd.setResourceSubordinate(entity.getResourceSubordinate());
            rbd.setResourceSearchengines(se);
            // setResourceMd5 唯一的
            rbd.setResourceMd5(Md5Util.Md5(kw + new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private void addKeyword(List<ResourceBatchDetailEntity> keywordIdList, int oid) {
        int detailCnt = 2000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD;
        Date rbiDate = new Date();
        String se = "";
        if (oid == 13543) {
            se = "9-10-m,9-10-d";
        } else if (oid == 13544) {
            se = "20-21-d,20-21-m";
        }

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            String kw = entity.getResourceMain();
            kw = FormatUtils.decodeKeyword(kw);
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(kw);
            rbd.setResourceMd5(Md5Util.Md5(kw + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            rbd.setResourceSearchengines(se);
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());

    }

    private List<ResourceBatchDetailEntity> parseExcel(String file) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser(',');
        List<ResourceBatchDetailEntity> dateList = new ArrayList<>();
        int cnt = 0;
        for (String line : lines) {

            cnt++;
            String[] cols = parser.parseLine(line);

            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword")) {
                    System.out.println(" line :" + cnt + " head ");
                } else {
                    ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                    String kwStr = cols[0];
                    if (kwStr.equals("Keyword")){
                        continue;
                    }
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    if (StringUtils.isBlank(cols[2])) {
                        resourceBatchDetailEntity.setResourceMain(kwStr);
                        resourceBatchDetailEntity.setOwnDomainId(oid);
                        onlyKwList.add(resourceBatchDetailEntity);
                        System.out.println("===###OnlyKeyword : " + kwStr + " ,oid :  " + oid);
                    } else if (cols[1].contains("national") && StringUtils.isNotBlank(cols[2])) {
                        String keywordTag = cols[2];
                        resourceBatchDetailEntity.setResourceMain(kwStr);
                        resourceBatchDetailEntity.setOwnDomainId(oid);
                        resourceBatchDetailEntity.setResourceSubordinate(keywordTag);
                        notGeoList.add(resourceBatchDetailEntity);
                        System.out.println("===###NoLocation : " + kwStr + "tag : " + keywordTag + " ,oid :  " + oid);
                    } else {
                        String keywordTag = cols[2];
                        Integer cityId = locationMap.get(cols[1]);
                        resourceBatchDetailEntity.setResourceMain(kwStr);
                        resourceBatchDetailEntity.setOwnDomainId(oid);
                        resourceBatchDetailEntity.setResourceSubordinate(keywordTag);
                        resourceBatchDetailEntity.setResourceAdditional(String.valueOf(cityId));
                        dateList.add(resourceBatchDetailEntity);
                        System.out.println("===###Geo : " + kwStr + ",cityId : " + cityId +",location " +cols[1]+  ",tag : " + keywordTag + " ,oid :  " + oid);
                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
