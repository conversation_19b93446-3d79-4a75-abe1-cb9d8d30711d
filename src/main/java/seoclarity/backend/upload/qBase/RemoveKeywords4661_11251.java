package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Too many Baidu daily keywords for demo/test domains
 * https://www.wrike.com/open.htm?id=1367433250
 * -  hao
 * qbase task  national
 */
public class RemoveKeywords4661_11251 {

    private static String removeKWFile = "files/4661Files/removeKW.txt";
    private static List<String> kw4661List = new ArrayList<>();
    private static List<String> kw11251List = new ArrayList<>();
    private static List<String> kwErrList = new ArrayList<>();
    private static List<String> reservedKWList = new ArrayList<>();
    private static int domainId_4661 = 4661;
    private static int domainId_11251 = 11251;
    private static int count492 = 0;


    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private KeywordEntityDAO keywordEntityDAO;

    public RemoveKeywords4661_11251() {
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
    }


    public static void main(String[] args) {
        RemoveKeywords4661_11251 in = new RemoveKeywords4661_11251();
        in.process();
    }

    private void process() {

        try {
            removeKeywordTask();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void removeKeywordTask() {


        readLinesFromFile(removeKWFile);
        System.out.println("===###kw4661List : " + kw4661List.size());
        System.out.println("===###kw11251List : " + kw11251List.size());
        System.out.println("===###kwErrList : " + kwErrList.size());
        System.out.println("===###reservedKWList : " + reservedKWList.size());


        List<KeywordEntity> kwId4661 = get4661KWId(domainId_4661, kw4661List);
        List<KeywordEntity> kwId11251 = get4661KWId(domainId_11251, kw11251List);


        if (kwId4661.size() > 0) {
            getQBaseDelKeyword(kwId4661, domainId_4661);
        }

        if (kwId11251.size() > 0) {
            getQBaseDelKeyword(kwId11251, domainId_11251);
        }
    }

    private List<KeywordEntity> get4661KWId(int domainId, List<String> kw4661List) {
        List<KeywordEntity> keywordEntityList = new ArrayList<>();
        List<String> query100 = new ArrayList<>();
        for (String s : kw4661List) {
            query100.add(s);
            if (query100.size() >= 100) {
                System.out.println("===###query100 : 每100个关键字查一次 ================ " + query100.size());
                List<KeywordEntity> keywordList = keywordEntityDAO.getByRawKeywordNameList(domainId, query100);
                keywordEntityList.addAll(keywordList);
                query100 = new ArrayList<>();
            }

        }
        return keywordEntityList;
    }


    private void getQBaseDelKeyword(List<KeywordEntity> keywordIdList, Integer domainId) {

        int detailCnt = 2000;
        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_KEYWORD;
        Date rbiDate = new Date();
        String engine = "150-6-d,150-6-m";

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setEngineLanguageDevice(engine);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (KeywordEntity kw : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(kw.getId());
            rbd.setResourceSearchengines(engine);
            rbd.setResourceCategory(1);
            rbd.setResourceMd5(Md5Util.Md5(kw.getId() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }


    public static void readLinesFromFile(String filePath) {

        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null && StringUtils.isNotBlank(line)) {
                System.out.println(line);
                String[] parts = line.split("\t");
                String kw = parts[1];
                String domain = parts[2];
                if (kw.contains("'")){
                    kw = kw.replace("'","\\'");
                }

                get4661_11251KW(kw, domain);

            }
        } catch (FileNotFoundException e) {
            System.out.println("文件未找到: " + filePath);
            e.printStackTrace();
        } catch (IOException e) {
            System.out.println("读取文件时发生错误: " + filePath);
            e.printStackTrace();
        }
    }

    private static void get4661_11251KW(String kw, String domain) {
        if (domain.contains(",")) {
            count492++;
            if (count492 > 492) {
                kw4661List.add(kw);
                kw11251List.add(kw);
            } else {
                reservedKWList.add(kw);
            }
        } else if (domain.equals("4661")) {
            kw4661List.add(kw);
        } else if (domain.equals("11251")) {
            kw11251List.add(kw);
        } else {
            kwErrList.add(kw);
        }
    }


}
