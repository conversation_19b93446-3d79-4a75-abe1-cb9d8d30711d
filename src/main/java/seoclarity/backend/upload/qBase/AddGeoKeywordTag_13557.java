package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.rankcheck.GeoMasterEntity;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.util.*;

/**
 * www.ticketmaster.com - 13557 | KW + Tag Upload
 * https://www.wrike.com/open.htm?id=1556404652
 * -  hao
 * qbase task geokeywordtag
 * <p>
 * domain_geo_mapping
 * keyword_geo_mapping
 * cdb_tracked_keyword
 * cdb_keyword_search_engine_rel
 * <p>
 * SELECT
 * keywordType kwType,
 * concat(
 * searchEngineId,
 * '_',
 * languageId,
 * '_',
 * device
 * ) SE,
 * frequency freq,
 * count(*),
 * count(DISTINCT rel.keywordId) kwCnt
 * FROM
 * rc_keyword_se_rel rel
 * JOIN rc_keyword_domain_rel domainRel ON rel.id = domainRel.keywordSeRelId
 * WHERE
 * ownDomainId = 7605
 * GROUP BY
 * keywordType,
 * SE,
 * frequency;
 */
public class AddGeoKeywordTag_13557 {

    private static Map<String, Integer> cityMap = new HashMap<>();

    List<ResourceBatchDetailEntity> geoUSList_m = new ArrayList<>();
    List<ResourceBatchDetailEntity> geoUSList_d = new ArrayList<>();

    List<ResourceBatchDetailEntity> nationalUSList_1_1_d = new ArrayList<>();

    List<ResourceBatchDetailEntity> nationalMexicoList_11_12_m = new ArrayList<>();

    List<ResourceBatchDetailEntity> nationalMexicoList_11_12_d = new ArrayList<>();
    private static String errorCol = "";

    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;


    static {

        cityMap.put("san diego, ca", 302079);
        cityMap.put("san francisco, ca", 303112);
        cityMap.put("seattle, wa", 302997);
        cityMap.put("dallas, tx", 300141);
        cityMap.put("atlanta, ga", 300025);
        cityMap.put("houston, tx", 303274);
        cityMap.put("phoenix, az", 302617);
        cityMap.put("new york, ny", 300893);
        cityMap.put("washington, dc", 300378);
        cityMap.put("los angeles, ca", 302442);
        cityMap.put("las vegas, nv", 300459);
        cityMap.put("boston, ma", 301459);
        cityMap.put("chicago, il", 301666);


    }


    public AddGeoKeywordTag_13557() {
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    private static String localFileFolder = "files/13557Files/qbaseTask";
    private static Integer oid = 13557;
    private static boolean isTest = false;

    public static void main(String[] args) {
        if (null != args && args.length > 0) {
            oid = Integer.parseInt(args[0]);
            if (args[1].equals("true")) {
                isTest = true;
            }
        }
        AddGeoKeywordTag_13557 in = new AddGeoKeywordTag_13557();
        in.process();
    }

    private void process() {

        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    processFile(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void processFile(File file) throws Exception {
        System.out.println("Found file " + file.getName() + ", start to process !");
        parseExcel(file.getAbsolutePath());

        if (!isTest) {
            System.out.println("===########geo qbase run ====================");

            if (geoUSList_d.size() > 0) {
                System.out.println("===########geoUSList_d qbase run ====================" + geoUSList_d.size() );
                keywordTagGeo(geoUSList_d, "1-1-d");
            }

            if (geoUSList_m.size() > 0) {
                System.out.println("===########geoUSList_m qbase run ====================" + geoUSList_m.size());
                keywordTagGeo(geoUSList_m, "1-1-m");
            }

            if (nationalUSList_1_1_d.size()>0){
                System.out.println("===########nationalUSList_1_1_d qbase run ====================" + nationalUSList_1_1_d.size());
                addKeywordTag_national(nationalUSList_1_1_d, "1-1-d");
            }

            if (nationalMexicoList_11_12_m.size()>0){
                System.out.println("===########nationalMexicoList_11_12_m qbase run ====================" + nationalMexicoList_11_12_m.size());
                addKeywordTag_national(nationalMexicoList_11_12_m, "11-12-m");
            }

            if (nationalMexicoList_11_12_d.size()>0){
                System.out.println("===########nationalMexicoList_11_12_d qbase run ====================" + nationalMexicoList_11_12_d.size());
                addKeywordTag_national(nationalMexicoList_11_12_d, "11-12-d");
            }

        } else {
            System.out.println(" ============== test ==================");
        }
    }


    private void keywordTagGeo(List<ResourceBatchDetailEntity> keywordIdList, String se) {

        int detailCnt = 2000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG;
        Date rbiDate = new Date();
        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            String kw = entity.getResourceMain();
            kw = FormatUtils.decodeKeyword(kw);
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(kw); // 不能encode
            rbd.setResourceSubordinate(entity.getResourceSubordinate());
            rbd.setResourceAdditional(entity.getResourceAdditional());
            rbd.setResourceSearchengines(se);
            // setResourceMd5 唯一的
            rbd.setResourceMd5(Md5Util.Md5(kw + new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private void addKeywordTag_national(List<ResourceBatchDetailEntity> keywordIdList, String se) {

        int detailCnt = 2000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG;
        Date rbiDate = new Date();
        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
            for (ResourceBatchDetailEntity entity : keywordIdList) {
                ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
                String kw = "";
                try {
                    kw =  entity.getResourceMain();
                    kw = FormatUtils.decodeKeyword(kw);
                }catch (Exception e){
                    System.out.println(entity.getResourceMain());
                    kw = entity.getResourceMain().replaceAll("%(?!\\p{XDigit}{2})", "%25");
                    kw = FormatUtils.decodeKeyword(kw);
                }

                rbd.setInfoId(id);
                rbd.setActionType(actionType);
                rbd.setOwnDomainId(oid);
                rbd.setResourceMain(kw); // 不能encode
//            rbd.setResourceId(kw.getTagId());
                rbd.setResourceSubordinate(entity.getResourceSubordinate());
                rbd.setResourceSearchengines(se);
                // setResourceMd5 唯一的
                rbd.setResourceMd5(Md5Util.Md5(kw + entity.getResourceSubordinate() + new Date().getTime()));
                rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
                resourceBatchDetailEntityList.add(rbd);

                if (resourceBatchDetailEntityList.size() >= detailCnt) {
                    resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                    System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                    resourceBatchDetailEntityList = new ArrayList<>();
                }
            }
            if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
            }

        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }


    private void parseExcel(String file) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        List<ResourceBatchDetailEntity> dateList = new ArrayList<>();

        int colSize = 16;
        int cnt = 0;
        for (int n = start; n <= end; n++) {
            cnt++;
            if (cnt < 3 ){
                continue;
            }
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            String[] cols = getStringArray(row);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[1], "Location")) {
                    System.out.println(" line :" + cnt + " head ");
                } else {
                    String kwStr = cols[2];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    List<String> tagList = new ArrayList<>();
                    for (int i = 3; i < colSize; i++){
                        if (StringUtils.isNotBlank(cols[i])) {
                            tagList.add(cols[i]);
                        }
                    }

                    String cityname = cols[1];
                    if (cityname.contains("Mexico") && cols[0].equals("Smartphone")){
                        // national
                        for (String keywordTag : tagList) {
                            ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                            resourceBatchDetailEntity.setResourceMain(kwStr);
                            resourceBatchDetailEntity.setOwnDomainId(oid);
                            resourceBatchDetailEntity.setResourceSubordinate(keywordTag);
                            resourceBatchDetailEntity.setResourceSearchengines("11-12-m");
                            nationalMexicoList_11_12_m.add(resourceBatchDetailEntity);
                        }
                        continue;
                    }

                    if (cityname.contains("Mexico") && cols[0].equals("Desktop")){
                        // national
                        for (String keywordTag : tagList) {
                            ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                            resourceBatchDetailEntity.setResourceMain(kwStr);
                            resourceBatchDetailEntity.setOwnDomainId(oid);
                            resourceBatchDetailEntity.setResourceSubordinate(keywordTag);
                            resourceBatchDetailEntity.setResourceSearchengines("11-12-d");
                            nationalMexicoList_11_12_d.add(resourceBatchDetailEntity);
                        }
                        continue;

                    }


                    if (cityname.contains("united states")){
                        // national
                        for (String keywordTag : tagList) {
                        ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                        resourceBatchDetailEntity.setResourceMain(kwStr);
                        resourceBatchDetailEntity.setResourceSubordinate(keywordTag);
                        resourceBatchDetailEntity.setOwnDomainId(oid);
                        resourceBatchDetailEntity.setResourceSearchengines("1-1-d");
                        nationalUSList_1_1_d.add(resourceBatchDetailEntity);
                       }
                        continue;
                    }

                    Integer cityId = cityMap.get(cityname);
                    String device = cols[0];
//                    if (StringUtils.equalsIgnoreCase(device, "Desktop")){
//                        device = "d";
//                    }

                    if (device.contains("Desktop") &&!cityname.contains("Mexico")){
                        // national
                        for (String keywordTag : tagList) {
                            ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                            resourceBatchDetailEntity.setResourceMain(kwStr);
                            resourceBatchDetailEntity.setResourceSubordinate(keywordTag);
                            resourceBatchDetailEntity.setOwnDomainId(oid);
                            resourceBatchDetailEntity.setResourceSearchengines("1-1-d");
                            resourceBatchDetailEntity.setResourceAdditional(String.valueOf(cityId));
                            geoUSList_d.add(resourceBatchDetailEntity);
                        }
                        continue;
                    }

                    if (device.contains("Smartphone") &&!cityname.contains("Mexico")){
                        // national
                        for (String keywordTag : tagList) {
                            ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                            resourceBatchDetailEntity.setResourceMain(kwStr);
                            resourceBatchDetailEntity.setResourceSubordinate(keywordTag);
                            resourceBatchDetailEntity.setOwnDomainId(oid);
                            resourceBatchDetailEntity.setResourceSearchengines("1-1-m");
                            resourceBatchDetailEntity.setResourceAdditional(String.valueOf(cityId));
                            geoUSList_m.add(resourceBatchDetailEntity);
                        }
                        continue;
                    }

                }
            } catch (Exception e) {
                System.out.println("===###col : " + cnt + " , " + cols[0] + " , " + cols[1] + " , " + cols[2] + " , " +cols[3]);
                e.printStackTrace();
            }
        }

    }


    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
