package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * enterprise.com (9742) | Copy over all keywords
 * https://www.wrike.com/open.htm?id=1280290043
 * -  hao
 * qbase task  national
 * 1 copy over keywords.
 * 2 copy over tags
 * 3 copy over keywords + tags relationship.
 */
public class CopyKeywordTagRelFrom9742To10784 {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private GroupTagRelationEntityDAO groupTagRelationEntityDAO;

    public CopyKeywordTagRelFrom9742To10784() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        groupTagRelationEntityDAO = SpringBeanFactory.getBean("groupTagRelationEntityDAO");
    }

    private static int from_domainId = 9742;
    private static int to_domainId = 10784;
    private static int testNumber = 5;
    private static int tagType = 2;
    private static int tagResourceType = 2;

    public static void main(String[] args) {
        CopyKeywordTagRelFrom9742To10784 in = new CopyKeywordTagRelFrom9742To10784();
//        in.process();
        System.out.println("    // 只添加几个tag 和keyword tag 关系 ");
        in.process2();
    }

    private void process2() {
        List<GroupTagRelationEntity>  relationEntityList =  groupTagRelationEntityDAO.getKeywordTagRelationByDomainId(from_domainId,tagResourceType);
        if (relationEntityList.size()>0){
            getQBaseAddKeyTagsRel(relationEntityList);
        }
    }

    private void process() {

        try {
            addKeywordTask();
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            addTagTask();
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            addKeywordTagRelTask();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void addKeywordTagRelTask() {
        List<GroupTagRelationEntity>  relationEntityList =  groupTagRelationEntityDAO.getKeywordTagRelationByDomainId(from_domainId,tagResourceType);
        if (relationEntityList.size()>0){
            getQBaseAddKeyTagsRel(relationEntityList);
        }
    }

    private void addTagTask() {
        List<GroupTagEntity> groupTagEntityList =  groupTagEntityDAO.getTagEntityByType(from_domainId,tagType);
        if (groupTagEntityList.size()>0){
            getQBaseAddTags(groupTagEntityList);
        }
    }

    private void addKeywordTask() {
        List<KeywordEntity> keywordList = keywordEntityDAO.getKeywordByDomainId(from_domainId);
        System.out.println("===###keywordList : " + keywordList.size());
        if (keywordList.size()>0){
            getQBaseAddKeyword(keywordList);
        }

    }


    private void getQBaseAddKeyword(List<KeywordEntity> keywordIdList) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(to_domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        int i = 0;
        for (KeywordEntity kw : keywordIdList) {
            i++ ;

            String keyword = kw.getKeywordName();
            keyword = FormatUtils.decodeKeyword(keyword);

            System.out.println("===###keyword : " + kw.getKeywordName() + ", decodeKeyword :" +keyword);
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(to_domainId);
            rbd.setResourceMain(keyword);
            rbd.setResourceMd5(Md5Util.Md5(keyword + new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private void getQBaseAddTags(List<GroupTagEntity> groupTagEntityList) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_TAG;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(to_domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        int i = 0;
        for (GroupTagEntity tagEntity : groupTagEntityList) {
            i++ ;

            String tagName = tagEntity.getTagName();

            System.out.println("===###tagName : " +tagName );
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(to_domainId);
            rbd.setResourceMain(tagName);
            rbd.setResourceSubordinate("2");
            rbd.setResourceCategory(1);
            rbd.setResourceMd5(Md5Util.Md5(tagName + new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private void getQBaseAddKeyTagsRel(List<GroupTagRelationEntity> groupTagEntityList) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG;
        Date rbiDate = new Date();
        String se = "1-1-d,1-1-m";
//1-1-d,1-1-m
        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(to_domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        int i = 0;
        for (GroupTagRelationEntity relEntity : groupTagEntityList) {
            i++ ;

            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(to_domainId);
            rbd.setResourceMain(relEntity.getKeywordName());
            rbd.setResourceSubordinate(relEntity.getTagName());
            rbd.setResourceSearchengines(se);
            rbd.setResourceMd5(Md5Util.Md5(relEntity.getKeywordName() +relEntity.getTagName()+ new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }

        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
