package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.GeoMasterEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.rankcheck.GeoMasterEntity;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.*;

/**
 * dominos.com - 12584 | Bulk add Geo location
 * https://www.wrike.com/open.htm?id=1395662057
 * -  hao
 * qbase task geokeywordtag
 * <p>
 * domain_geo_mapping
 * keyword_geo_mapping
 * cdb_tracked_keyword
 * cdb_keyword_search_engine_rel
 *
 *SELECT
 * 	keywordType kwType,
 * 	concat(
 * 		searchEngineId,
 * 		'_',
 * 		languageId,
 * 		'_',
 * 		device
 * 	) SE,
 * 	frequency freq,
 * 	count(*),
 * 	count(DISTINCT rel.keywordId) kwCnt
 * FROM
 * 	rc_keyword_se_rel rel
 * JOIN rc_keyword_domain_rel domainRel ON rel.id = domainRel.keywordSeRelId
 * WHERE
 * 	ownDomainId = 7605
 * GROUP BY
 * 	keywordType,
 * 	SE,
 * 	frequency;
 *
 */
public class AddGeoKeywordTag_12584 {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private GeoMasterEntityDAO geoMasterEntityDAO;


    public AddGeoKeywordTag_12584() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        geoMasterEntityDAO = SpringBeanFactory.getBean("geoMasterEntityDAO");
    }

    private static String localFileFolder = "files/12584Files";
    private static String cityFile = "files/12584Files/geo_mapping_20240620.txt";
    private Integer oid = 12584;

    private static String[] jumpList = new String[]{"Beaufort, NC", "East Hampstead, NH", "Fairview, PA", "Hamburg, NY", "Stafford Springs, CT", "UNCASVILLE, CT", "Union, NJ"};
    private static Map<String,Integer> cityIdRel = new HashMap<>();
    private static List<String> nonCityList = new ArrayList<>();
    private static boolean isTest = false;
    private static boolean runRest = false;
    private static Map<String,String> cityMap = new HashMap<>();
    static {
        cityIdRel.put("Beaufort, NC",323101);
        cityIdRel.put("East Hampstead, NH",313195);
        cityIdRel.put("Fairview, PA",315372);
        cityIdRel.put("Hamburg, NY",323102);
        cityIdRel.put("Stafford Springs, CT",317653);
        cityIdRel.put("UNCASVILLE, CT",323103);
        cityIdRel.put("Union, NJ",306265);
    }


    public static void main(String[] args) {
        if (null != args && args.length > 0) {
            if (args[0].equals("true")) {
                isTest = true;
            }
            if (args[1].equals("true")){
                runRest = true;
            }
        }
        AddGeoKeywordTag_12584 in = new AddGeoKeywordTag_12584();
        in.process();
    }

    private void process() {
        getLocationFromFile(cityFile);

        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                if (file.getName().contains(".csv")) {
                    try {
                        System.out.println(" ****  file name  : " + file.getName());
                        processFile(file);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }else {

                }
            }
        }
    }

    private void processFile(File file) throws Exception {
            System.out.println("Found file " + file.getName() + ", start to process !");
            List<ResourceBatchDetailEntity> lineList = parseExcel(file.getAbsolutePath());
            System.out.println("###lineList : " + lineList.size());
            if (nonCityList.size()>0){
                System.out.println("===###输出不存在的city ========================");
                for (String str : nonCityList) {
                    System.out.println("####");
                    System.out.println(str);
                }
            }else {
                System.out.println("===###所有city 都存在 =========================");
            }
            if (lineList.size() > 0 && !isTest) {
                System.out.println("===###qbase run ====================");
                getQBaseTast(lineList, oid);
            } else {
                System.out.println(" ============== test ==================");
            }
    }

    private void getQBaseTast(List<ResourceBatchDetailEntity> keywordIdList, int oid) {

        int detailCnt = 2000;
        String se = "1-1-d,1-1-m";
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(entity.getResourceMain());
            rbd.setResourceSubordinate(entity.getResourceSubordinate());
            rbd.setResourceAdditional(entity.getResourceAdditional());
            rbd.setResourceSearchengines(se);
            rbd.setResourceMd5(Md5Util.Md5(entity.getResourceMain() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);

        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }


    private List<ResourceBatchDetailEntity> parseExcel(String file) throws Exception {
        List<String> jumpList0 = Arrays.asList(jumpList);

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser(',');
        List<ResourceBatchDetailEntity> dateList = new ArrayList<>();
        int cnt = 0;
        for (String line : lines) {

            cnt++;
            String[] cols = parser.parseLine(line);

            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[1], "Location")) {
                    System.out.println(" line :" + cnt + " head ");
                } else {
                    ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String cityname = cols[3];
                    Integer cityId = null;
                    if (runRest){
                        if (jumpList0.contains(cityname)){
                            cityId = cityIdRel.get(cityname);
                            String keywordTag1 = cols[1];
                            resourceBatchDetailEntity.setResourceMain(kwStr);
                            resourceBatchDetailEntity.setResourceSubordinate(keywordTag1);
                            resourceBatchDetailEntity.setResourceAdditional(String.valueOf(cityId));
                            resourceBatchDetailEntity.setOwnDomainId(oid);
                            dateList.add(resourceBatchDetailEntity);

                            String keywordTag2 = cols[2];
                            resourceBatchDetailEntity.setResourceMain(kwStr);
                            resourceBatchDetailEntity.setResourceSubordinate(keywordTag2);
                            resourceBatchDetailEntity.setResourceAdditional(String.valueOf(cityId));
                            resourceBatchDetailEntity.setOwnDomainId(oid);
                            dateList.add(resourceBatchDetailEntity);

                            System.out.println(" kw : " + kwStr + ", keywordTag1 : " + keywordTag1 +" , keywordTag2 :"+ keywordTag2 + ", cityId  :" + cityId + ", cityname : " + cityname);

                        }

                    }else {

                        if (jumpList0.contains(cityname)) {
                            System.out.println("===###jumpCity : " + cityname);
                            continue;
                        }

                        cityId = Integer.parseInt(cityMap.get(cityname));
                        if (null == cityId) {
                            nonCityList.add(cityname);
                            continue;
                        }

                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }

    public static void getLocationFromFile(String filePath) {

        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null && StringUtils.isNotBlank(line)) {
                System.out.println(line);
                String[] parts = line.split("\t");
                String cityName = parts[0];
                String cityId = parts[1];
                cityMap.put(cityName,cityId);

            }
        } catch (FileNotFoundException e) {
            System.out.println("文件未找到: " + filePath);
            e.printStackTrace();
        } catch (IOException e) {
            System.out.println("读取文件时发生错误: " + filePath);
            e.printStackTrace();
        }
    }
}
