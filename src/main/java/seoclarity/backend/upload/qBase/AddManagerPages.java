package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-09-05
 * @path Hyatt | add managed Pages+ Tags in Bulk
 * https://www.wrike.com/open.htm?id=958541014
 */
public class AddManagerPages {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");

    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;

    private String errorList = new String();
    private static Boolean isMobile = true;
    private StringBuilder errCols = new StringBuilder();
    private int testcnt = 10;

    public AddManagerPages() {
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
    }

    public static void main(String[] args) {


        AddManagerPages ins = new AddManagerPages();
        String filePath = "files/qbase/";
//        String filePath = "i:\\zzqbase\\";

        try {
            System.out.println("=========================Process ===================================");
            for (File file : new File(filePath).listFiles()) {
                if (file.getName().contains("xlsx")) {
                    System.out.println("====================== gogogo ======================");
                    System.out.println("file name : " + file.getName());
                    ins.process(file.getAbsolutePath());

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();

        int cnt = 0;
        for (int n = start; n <= end; n++) {

            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            try {
                String[] cols = getStringArray(row);
                String oid = cols[1];


                if (StringUtils.isNotBlank(oid) && oid.equals("7744.0")) {
                    String tag_home = cols[3];
                    String tag_room = cols[4];
                    String tag_areaA = cols[5];
                    String tag_dining = cols[6];
                    String tag_se = cols[7];
                    String tag_weddings = cols[8];
//                    String tag_offers = cols[9];

                    System.out.println(" 第 ： " + cnt + " 行 ： 【"
                            + " oid : " + oid
                            + " tag_home :" + tag_home
                            + " tag_room : " + tag_room
                            + " tag_areaA :" + tag_areaA
                            + " tag_dining : " + tag_dining
                            + " tag_se :" + tag_se
                            + " tag_weddings : " + tag_weddings
//                            + " tag_offers :" + tag_offers
                            + " 】"
                    );

                    Map<String, String> urlTagMap = new HashMap<>();
                    if (StringUtils.isNotBlank(tag_home)) {
                        urlTagMap.put(tag_home,"Home");
                    }
                    if (StringUtils.isNotBlank(tag_room)) {
                        urlTagMap.put(tag_room,"Rooms");
                    }
                    if (StringUtils.isNotBlank(tag_areaA)) {
                        urlTagMap.put(tag_areaA,"Area Attractions");
                    }
                    if (StringUtils.isNotBlank(tag_dining)) {
                        urlTagMap.put(tag_dining,"Dining");
                    }
                    if (StringUtils.isNotBlank(tag_se)) {
                        urlTagMap.put(tag_se,"Special Events");
                    }
                    if (StringUtils.isNotBlank(tag_weddings)) {
                        urlTagMap.put(tag_weddings,"Weddings");
                    }
//                    if (StringUtils.isNotBlank(tag_offers)) {
//                        urlTagMap.put(tag_offers,"Offers");
//                    }

                    Double oidD = Double.valueOf(oid);
                    Integer oidI = (int) Math.ceil(oidD);
                    addUrlTag(oidI, urlTagMap);

                } else {
//                    System.out.println(" =================err : ==================");
//                    StringBuilder sb = new StringBuilder();
//                    sb.append("  == : ");
//                    for (String str : cols){
//                        sb.append(str).append(" , ");
//                    }
//                    System.out.println(cnt + sb.toString());

                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }


    private void addUrlTag(int oId, Map<String, String> urlTagMap) {
        System.out.println("********************* start addUrlTag");
        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(oId);
        if (ownDomainEntity == null) {
            System.out.println("********* domain is not active:" + oId);
            return;
        }
        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(oId);
        if (ownDomainSettingEntity == null) {
            System.out.println("********* ownDomainSettingEntity null：" + oId);
            return;
        }

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oId);
        resourceBatchInfoEntity.setOperationType(operationType);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (String url : urlTagMap.keySet()) {
            String tagName = urlTagMap.get(url);
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oId);
            rbd.setResourceMain(url);
            rbd.setResourceSubordinate(tagName);
            rbd.setResourceMd5(Md5Util.Md5(url + new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));

            resourceBatchDetailEntityList.add(rbd);
            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }

        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        System.out.println("process successfully!");
    }

}
