package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.util.*;

/**
 * Lamark| www.slumberland.com -11073| Bulk Add Geo Keywords
 * https://www.wrike.com/open.htm?id=1199494940
 * -  hao
 * qbase task geokeyword
 * <p>
 * domain_geo_mapping
 * keyword_geo_mapping
 * cdb_tracked_keyword
 * cdb_keyword_search_engine_rel
 *
 *SELECT
 * 	keywordType kwType,
 * 	concat(
 * 		searchEngineId,
 * 		'_',
 * 		languageId,
 * 		'_',
 * 		device
 * 	) SE,
 * 	frequency freq,
 * 	count(*),
 * 	count(DISTINCT rel.keywordId) kwCnt
 * FROM
 * 	rc_keyword_se_rel rel
 * JOIN rc_keyword_domain_rel domainRel ON rel.id = domainRel.keywordSeRelId
 * WHERE
 * 	ownDomainId = 11073
 * GROUP BY
 * 	keywordType,
 * 	SE,
 * 	frequency;
 *
 */
public class AddGeoKeyword_11073_stander {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;

    public AddGeoKeyword_11073_stander() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
    }

    private static String localFileFolder = "files/11073Files/keyword/";
    private static String cityRelatiuon = "files/11073Files/city/geo_mapping_11073_20230912.txt";
    private Integer oid = 11073;
    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t');
    private static Map<String, String> cityList = new HashMap<>();
    private static boolean isTest = false;

    public static void main(String[] args) {
        if (null != args && args.length > 0) {
            if (args[0].equals("true")) {
                isTest = true;
            }
        }
        AddGeoKeyword_11073_stander in = new AddGeoKeyword_11073_stander();
        in.process();
    }

    private void process() {

        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    processFile(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void processFile(File file) throws Exception {

        if (StringUtils.startsWith(file.getName(), "Lamark") && file.isFile()) {
            System.out.println("Found file " + file.getName() + ", start to process !");
            getCityMapping();
            List<ResourceBatchDetailEntity> lineList = parseExcel(file.getAbsolutePath());
            System.out.println("###lineList : " + lineList.size());
            if (lineList.size() > 0 && !isTest) {
                System.out.println("===###qbase run ====================");
                getQBaseTast(lineList, oid);
            } else {
                System.out.println(" ============== test ==================");
            }
        }
    }

    private void getCityMapping() throws Exception {
        File file = new File(cityRelatiuon);
        FileReader fr = new FileReader(file);
        List<CSVRecord> csvRecords = null;
        CSVParser csvParser = new CSVParser(fr, csvFormat);
        csvRecords = csvParser.getRecords();
        for (int i = 0; i < csvRecords.size(); i++) {
            CSVRecord csvRecord = csvRecords.get(i);
            String cityName = csvRecord.get(0);
            String cityId = csvRecord.get(1);
            System.out.println("====cityName :" + cityName + " ,cityId :  " + cityId);
            cityList.put(cityName, cityId);
        }

    }

    private void getQBaseTast(List<ResourceBatchDetailEntity> keywordIdList, int oid) {

        int detailCnt = 10000;
        String se = "1_1_d,1-1-m";
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(entity.getResourceMain());
            rbd.setResourceAdditional(entity.getResourceAdditional());
            rbd.setResourceMd5(Md5Util.Md5(entity.getResourceMain() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }


    private List<ResourceBatchDetailEntity> parseExcel(String file) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        List<ResourceBatchDetailEntity> dateList = new ArrayList<>();
        int cnt = 0;
        for (int n = start; n <= end; n++) {

            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            String[] cols = getStringArray(row);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "OID") && StringUtils.equalsIgnoreCase(cols[1], "Keyword")) {
                    System.out.println(" line :" + cnt + " head ");
                } else if (cols[4].equals("Mobile")) {
                    ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                    String kwStr = cols[1];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String location = cols[2];
                    String cityId = cityList.get(location);
                    System.out.println(" kw : " + kwStr + ", domain : " + oid + ", location  :" + location + ", city : " + cityId);
                    resourceBatchDetailEntity.setResourceMain(kwStr);
                    resourceBatchDetailEntity.setResourceAdditional(cityId);
                    resourceBatchDetailEntity.setOwnDomainId(oid);
                    dateList.add(resourceBatchDetailEntity);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
