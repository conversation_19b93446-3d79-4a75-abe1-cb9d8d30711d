package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Apple | Bulk keyword upload
 * https://www.wrike.com/open.htm?id=1562658656
 * -  hao
 * qbase task
 */
public class AddKeywordTagRelation_AppleDomain {

    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;

    public AddKeywordTagRelation_AppleDomain() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    private static String localFileFolder = "files/appleFiles/addKW";
    private static Integer domainId = 5764;
    private static Map<String, Integer> domainMap = new HashMap<>();
    private static boolean istest = false;

    static {
        domainMap.put("ae", 5740);
        domainMap.put("at", 5741);
        domainMap.put("benl", 5743);
        domainMap.put("chde", 5747);
        domainMap.put("cz", 5749);
        domainMap.put("dk", 5751);
        domainMap.put("fi", 5753);
        domainMap.put("hu", 5756);
        domainMap.put("ie", 5757);
        domainMap.put("no", 5765);
        domainMap.put("pl", 5768);
        domainMap.put("pt", 5769);
        domainMap.put("ru", 5770);
        domainMap.put("se", 5771);
        domainMap.put("tr", 5774);
        domainMap.put("befr", 5784);
        domainMap.put("chfr", 5785);
        domainMap.put("lu", 5843);

        domainMap.put("au", 5742);
        domainMap.put("br", 5744);
        domainMap.put("ca", 5745);
        domainMap.put("de", 5750);
        domainMap.put("nz", 5766);
        domainMap.put("ph", 5767);
        domainMap.put("sg", 5772);
        domainMap.put("th", 5773);
        domainMap.put("tw", 5775);
        domainMap.put("uk", 5776);
        domainMap.put("vn", 11977);
        domainMap.put("cl", 12552);
        domainMap.put("in", 5758);
        domainMap.put("it", 5759);
        domainMap.put("jp", 5760);
        domainMap.put("kr", 5761);
        domainMap.put("mx", 5762);
        domainMap.put("my", 5763);
        domainMap.put("nl", 5764);
        domainMap.put("fr", 5754);
        domainMap.put("hk", 5755);
    }

    public static void main(String[] args) {
        AddKeywordTagRelation_AppleDomain in = new AddKeywordTagRelation_AppleDomain();
        if (args[0].equals("true")) {
            istest = true;
        }
        in.process();
    }

    private void process() {

        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    processFile(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        }


    }

    private void processFile(File file) throws Exception {

        List<ResourceBatchDetailEntity> lineList = parseExcel(file.getAbsolutePath());
        System.out.println("###lineList : " + lineList.size());

        // 根据 domain 进行分组
        Map<Integer, List<ResourceBatchDetailEntity>> groupedByDomain = lineList.stream()
                .collect(Collectors.groupingBy(ResourceBatchDetailEntity::getOwnDomainId));

        if (lineList.size() > 0) {
            for (Integer key : groupedByDomain.keySet()) {
                OwnDomainEntity domainENtity = ownDomainEntityDAO.getById(key);
                int engine = ScKeywordRankManager.getSearchEngineId(domainENtity);
                int language = ScKeywordRankManager.getSearchLanguageId(domainENtity);
                String se = engine + "-" + language + "-d" + "," + engine + "-" + language + "-m";
                List<ResourceBatchDetailEntity> entry = groupedByDomain.get(key);
                System.out.println("===###qbaseParam  domain : " + key + " , se : " + se);
                keywordTagQbase(entry, key, se);
            }

        }
    }


    private void keywordTagQbase(List<ResourceBatchDetailEntity> keywordIdList, int oid, String se) {

        if (oid == 5770) {
//            | 5770 | google.ru   | 39_0_d  |
//|             5770 | google.ru   | 39_39_m |
            se = "39-0-d,39-39-m," + se;
        }

        System.out.println("========================  oid  : " + oid + " , se : " + se);

        if (istest) {
            return;
        }

        int detailCnt = 2000;

        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG;
        Date rbiDate = new Date();
        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            String kw = entity.getResourceMain();
            kw = FormatUtils.decodeKeyword(kw);
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(kw); // 不能encode
            rbd.setResourceSubordinate(entity.getResourceSubordinate());
            rbd.setResourceSearchengines(se);
            // setResourceMd5 唯一的
            rbd.setResourceMd5(Md5Util.Md5(kw + new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private List<ResourceBatchDetailEntity> parseExcel(String file) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser(',');
        List<ResourceBatchDetailEntity> dateList = new ArrayList<>();

        int lineCnt = 0;
        for (String line : lines) {
            lineCnt++;
            String[] cols = parser.parseLine(line);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Domain") && StringUtils.equalsIgnoreCase(cols[1], "Keyword")) {
                    System.out.println(" line :" + lineCnt + " head ");
                } else {

                    String kwStr = cols[1];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String tagNames = cols[2];
                    String[] tagNameStr = tagNames.split(",");
                    String domainName = cols[0];

                    if (domainName.equals("www.apple.com/nl/")) {
                        domainId = 5764;
                    } else {
                        String[] domainStr = domainName.split("/");
                        String engine = domainStr[1];
                        domainId = domainMap.get(engine);
                        if (domainId == null) {
                            System.out.println("===###MissDomain : " + domainName);
                        }
                    }


                    for (String tagName : tagNameStr) {
                        ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                        System.out.println(domainId+" ,  tagName :" + tagName + " kwStr : " + kwStr);
                        resourceBatchDetailEntity.setResourceSubordinate(tagName);
                        resourceBatchDetailEntity.setResourceMain(kwStr);
                        resourceBatchDetailEntity.setOwnDomainId(domainId);
                        dateList.add(resourceBatchDetailEntity);
                    }
//                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
