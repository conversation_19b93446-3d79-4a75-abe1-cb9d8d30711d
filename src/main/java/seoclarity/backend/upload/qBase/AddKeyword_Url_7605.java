package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Rover US | OID 7605| Geo Locations keywords
 * https://www.wrike.com/open.htm?id=1257302352
 * -  hao
 * qbase task geokeyword + url
 */
public class AddKeyword_Url_7605 {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;

    public AddKeyword_Url_7605() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    }

    private static String localFileFolder = "files/7605Files/";
    private Integer oid = 7605;
    private static boolean isTest = false;
    private static List<String> IngorekeywordList = new ArrayList<>();

    public static void main(String[] args) {
        if (null != args && args.length > 0) {
            if (args[0].equals("true")) {
                isTest = true;
            }
        }
        AddKeyword_Url_7605 in = new AddKeyword_Url_7605();
        in.process();
    }

    private void process() {

        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    processFile(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void processFile(File file) throws Exception {

        if (StringUtils.startsWith(file.getName(), "Dog") && file.isFile()) {
            System.out.println("Found file " + file.getName() + ", start to process !");
            List<ResourceBatchDetailEntity> lineList = parseExcel(file.getAbsolutePath());
            System.out.println("=====###lineList : " + lineList.size());

            for (String kw : IngorekeywordList) {
                System.out.println(kw);
            }

            if (lineList.size() > 0 && isTest) {
                System.out.println("===###qbase run ====================");
                getQBaseTast(lineList, oid);
            } else {
                System.out.println(" ============== test ==================");
            }
        }
    }

    private void getQBaseTast(List<ResourceBatchDetailEntity> keywordIdList, int oid) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TARGET_URL;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (ResourceBatchDetailEntity entity : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceId(entity.getResourceId());
            rbd.setResourceSubordinate(entity.getResourceSubordinate());
            rbd.setResourceMd5(Md5Util.Md5(entity.getResourceId() + entity.getResourceSubordinate() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }


    private List<ResourceBatchDetailEntity> parseExcel(String file) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        List<ResourceBatchDetailEntity> dateList = new ArrayList<>();
        int cnt = 0;
        for (int n = start; n <= end; n++) {

            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            String[] cols = getStringArray(row);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[1], "Url")) {
                    System.out.println(" line :" + cnt + " head ");
                } else {
                    ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    KeywordEntity kwEntity = keywordEntityDAO.checkNationalAndGeoKw(kwStr, oid);
                    if (null == kwEntity) {
                        System.out.println("===###ingoreKw : " + kwStr);
                        IngorekeywordList.add(kwStr);
                        continue;
                    }

                    String url = cols[1];

                    resourceBatchDetailEntity.setResourceId(kwEntity.getId());
                    resourceBatchDetailEntity.setResourceSubordinate(url);
                    resourceBatchDetailEntity.setOwnDomainId(oid);
                    dateList.add(resourceBatchDetailEntity);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
