package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * enterprise.com (9742) | Copy over all keywords
 * https://www.wrike.com/open.htm?id=1280290043
 * -  hao
 * qbase task  national
 * 1 copy over keywords.
 * 2 copy over tags
 * 3 copy over keywords + tags relationship.
 */
public class RemoveKeywordTagRelFrom10784 {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private GroupTagRelationEntityDAO groupTagRelationEntityDAO;

    public RemoveKeywordTagRelFrom10784() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        groupTagRelationEntityDAO = SpringBeanFactory.getBean("groupTagRelationEntityDAO");
    }

    private static int domainId = 10784;
    private static int testNumber = 5;
    private static int tagType = 2;
    private static int tagResourceType = 2;
    private static boolean test = false;

    public static void main(String[] args) {
        RemoveKeywordTagRelFrom10784 in = new RemoveKeywordTagRelFrom10784();
        in.process();
    }

    private void process() {

        try {
            removeKeywordTask();
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            removeTagTask();
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            removeKeywordTagRelTask();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void removeKeywordTagRelTask() {
        List<GroupTagRelationEntity>  relationEntityList =  groupTagRelationEntityDAO.getKeywordTagRelationByDomainId(domainId,tagResourceType);
        if (relationEntityList.size()>0){
            getQBaseDelKeyTagsRel(relationEntityList);
        }
    }

    private void removeTagTask() {
        List<GroupTagEntity> groupTagEntityList =  groupTagEntityDAO.getTagEntityByType(domainId,tagType);
        if (groupTagEntityList.size()>0){
            getQBaseDelTags(groupTagEntityList);
        }
    }

    private void removeKeywordTask(){
        List<KeywordEntity> keywordList = keywordEntityDAO.getKeywordByDomainId(domainId);
        System.out.println("===###keywordList : " + keywordList.size());
        if (keywordList.size()>0){
            getQBaseDelKeyword(keywordList);
        }

    }


    private void getQBaseDelKeyword(List<KeywordEntity> keywordIdList) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_KEYWORD;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        int i = 0;
        for (KeywordEntity kw : keywordIdList) {
            i++ ;
            if (test){
                if (i>testNumber){
                    break;
                }
            }
            System.out.println("===### kid  :" +kw.getId());
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(kw.getId());
            rbd.setResourceMd5(Md5Util.Md5(kw.getId() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private void getQBaseDelTags(List<GroupTagEntity> groupTagEntityList) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);
        resourceBatchInfoEntity.setCustomFlag(2);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        int i = 0;
        for (GroupTagEntity tagEntity : groupTagEntityList) {
            i++ ;
            if (test){
                if (i>testNumber){
                    break;
                }
            }
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(Long.valueOf(tagEntity.getId()));
            rbd.setResourceCategory(1);
            rbd.setResourceMd5(Md5Util.Md5(tagEntity.getId()+ System.nanoTime() + "" ));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private void getQBaseDelKeyTagsRel(List<GroupTagRelationEntity> groupTagEntityList) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_TAG_ID;
        Date rbiDate = new Date();
        String se = "1-1-d,1-1-m";
//1-1-d,1-1-m
        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        int i = 0;
        for (GroupTagRelationEntity relEntity : groupTagEntityList) {
            i++ ;
            if (test){
                if (i>testNumber){
                    break;
                }
            }
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(Long.valueOf(relEntity.getGroupTagId()));
            rbd.setResourceCategory(1);
            rbd.setResourceSearchengines(se);
            rbd.setResourceMd5(Md5Util.Md5(relEntity.getKeywordName() +relEntity.getTagName()+ new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }

        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
