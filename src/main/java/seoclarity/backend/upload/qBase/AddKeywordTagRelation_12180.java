package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * homedepot.com 12180 | Integrate historic ranking data
 * https://www.wrike.com/open.htm?id=1174244632
 * -  hao
 * qbase task
 */
public class AddKeywordTagRelation_12180 {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;

    public AddKeywordTagRelation_12180() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
    }

    private static String localFileFolder = "files/12180Files/addKW";
    private static int domainId = 12180;
    private List<String> missTagList = new ArrayList<>();

    public static void main(String[] args) {
        AddKeywordTagRelation_12180 in = new AddKeywordTagRelation_12180();
        in.process();
    }

    private void process() {

        File doneFolder = new File(localFileFolder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    processFile(file);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        }


    }

    private void processFile(File file) throws Exception {

        if (StringUtils.startsWith(file.getName(), "keyword") && file.isFile()) {
            System.out.println("Found file " + file.getName() + ", start to process !");
            List<KeywordEntity> lineList = parseExcel(file.getAbsolutePath());
            System.out.println("###lineList : " + lineList.size());
            if (lineList.size() > 0) {
                getQBaseTast(lineList);
            }
        }
    }

    private void getQBaseTast(List<KeywordEntity> keywordIdList) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG;
        Date rbiDate = new Date();
        String se = "1-1-d,1-1-m";
        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (KeywordEntity kw : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceMain(kw.getKeywordName()); // 不能decode
//            rbd.setResourceId(kw.getTagId());
            rbd.setResourceSubordinate(kw.getTagName());
            rbd.setResourceSearchengines(se);
            // setResourceMd5 唯一的
            rbd.setResourceMd5(Md5Util.Md5(kw.getKeywordName() +kw.getTagName()+ new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }


    private List<KeywordEntity> parseExcel(String file) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser(',');
        List<KeywordEntity> dateList = new ArrayList<>();

        int lineCnt = 0;
        for (String line : lines) {
            lineCnt++;
            String[] cols = parser.parseLine(line);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[1].trim(), "Keyword")) {
                    System.out.println(" line :" + lineCnt + " head ");
                } else {


                    String kwStr = cols[1];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    int colNum = 0;
                    for (String tagnameStr : cols){
                        colNum++;
                        if (colNum>5){
                            String tagName = tagnameStr.trim();
                            KeywordEntity en = new KeywordEntity();
                            System.out.println(" tagName :" + tagName + " kwStr : " + kwStr);
                            en.setTagName(tagName);
                            en.setKeywordName(kwStr);
                            dateList.add(en);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }
}
