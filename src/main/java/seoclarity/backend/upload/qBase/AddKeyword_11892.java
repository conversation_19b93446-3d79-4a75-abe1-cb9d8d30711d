package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.RcKeywordSeRelEntityDAO;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Amazon.com 11892 | Change Rank-Checking Search Engine
 * https://www.wrike.com/open.htm?id=1185583240
 * -  hao
 * qbase task keyword
 * <p>
 */
public class AddKeyword_11892 {

    private static int oid = 11892;
    private static int engineId = 1;
    private static int languageId = 1;
    private static String device = "d";
    private static int cityId = 0;

    private RcKeywordSeRelEntityDAO rcKeywordSeRelEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;

    public AddKeyword_11892() {
        rcKeywordSeRelEntityDAO = SpringBeanFactory.getBean("rcKeywordSeRelEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    public static void main(String[] args) {
        AddKeyword_11892 in = new AddKeyword_11892();
        in.process();
    }

    private void process() {
        List<RcKeywordSeRelEntity> lineList = rcKeywordSeRelEntityDAO.getKeywords(engineId, languageId, device,cityId, 11892);
        System.out.println("###lineList : " + lineList.size());
        if (lineList.size() <= 0) {
            System.out.println(" ===###getKeywordCityList no data! oid:" + oid);
            return;
        }
        try {
            getQBaseTast(lineList, oid);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private void getQBaseTast(List<RcKeywordSeRelEntity> keywordIdList, int oid) {
        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD;
        Date rbiDate = new Date();
        String se = "217-1-d,217-1-m";

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (RcKeywordSeRelEntity entity : keywordIdList) {
            String kw = entity.getKeywordText();
            kw = FormatUtils.decodeKeyword(kw);
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(kw);
            rbd.setResourceMd5(Md5Util.Md5(kw + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            rbd.setResourceSearchengines(se);
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
            if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
            }

            resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());

    }


    private List<ResourceBatchDetailEntity> parseExcel(String file) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        List<ResourceBatchDetailEntity> dateList = new ArrayList<>();
        int cnt = 0;
        for (int n = start; n <= end; n++) {

            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            String[] cols = getStringArray(row);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Domain") && StringUtils.equalsIgnoreCase(cols[1], "OID")) {
                    System.out.println(" line :" + cnt + " head ");
                } else {
                    ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
                    String kwStr = cols[2];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String domainId = cols[1];
                    if (domainId.contains(".")) {
                        domainId = domainId.substring(0, domainId.indexOf("."));
                    }
                    String location = cols[3];
                    String city = "";
                    switch (location) {
                        case "Arvin, CA":
                            city = "315595";
                            break;
                        case "Bakersfield, CA":
                            city = "302721";
                            break;
                        case "Lamont, CA":
                            city = "318840";
                            break;
                        case "Chicago, IL":
                            city = "301666";
                            break;
                        case "Minneapolis, MN":
                            city = "302419";
                            break;
                        case "Kansas City, MO":
                            city = "303229";
                            break;
                        case "Evansville, IN":
                            city = "302527";
                            break;
                        case "Indianapolis, IN":
                            city = "302019";
                            break;
                        default:
                    }
                    System.out.println(" kw : " + kwStr + ", domain : " + domainId + ", location  :" + location + ", city : " + city);
                    resourceBatchDetailEntity.setResourceMain(kwStr);
                    resourceBatchDetailEntity.setResourceAdditional(city);
                    resourceBatchDetailEntity.setOwnDomainId(Integer.parseInt(domainId));
                    dateList.add(resourceBatchDetailEntity);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public void writeToTxt(BufferedWriter ot, String key) {
        try {
            ot.write(key);
            ot.write("\r\n");
        } catch (Exception e) {

        }
    }
}
