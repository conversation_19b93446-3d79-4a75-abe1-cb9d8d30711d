package seoclarity.backend.upload.qBase;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.CdbTrackedKeywordEntityDAO;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.*;

/**
 * www.matalan.co.uk - 11235 | Bulk Keyword Tag removal
 * https://www.wrike.com/open.htm?id=1087405200
 * 根据tag 删除所有 tag下的 keyword
 * 删除所有tag
 * -  hao
 * qbase task
 * <p>
 * (1) First delete keywords in tag:
 * OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_TAG_ID = 10129;
 * (2) then delete keyword tag(should be empty after step 1):
 * OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID = 13011;
 */
public class RemoveKeywordTag_8711 {

    private static Map<String, String> map = new HashMap<>();
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private CdbTrackedKeywordEntityDAO cdbTrackedKeywordEntityDAO;

    public RemoveKeywordTag_8711() {
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
    }

    static {
        map.put("acme_news", "437");
        map.put("Education", "1655");
        map.put("Education_Certifications", "330");
        map.put("Education_Landing_Page", "2");
        map.put("Education_Schools", "1002");
        map.put("Education_Schools_Overview", "829");
        map.put("Education_Schools_Programs", "175");
        map.put("Education_Schools_Reviews", "295");
        map.put("Education_SERP", "295");
        map.put("Education_Unified_SERP", "60");
        map.put("about {0}", "442");
        map.put("acme_cmp_jobtitle_jobs", "352");
        map.put("acme_cmp_jobtitle_temp", "374");
        map.put("acme_cmp_location_temp", "7");
        map.put("acme_enterprise_jobs_temp", "0");
        map.put("acme_glassdoor_term_hourly_pay", "305");
        map.put("acme_glassdoor_term_make", "0");
        map.put("acme_glassdoor_term_monthly_pay", "6");
        map.put("acme_glassdoor_term_salaries", "1686");
        map.put("acme_industry_cmp", "0");
        map.put("acme_opp_analysis_2021_temp", "23227");
        map.put("acme_raw_titles_analysis_temp", "3031");
        map.put("acme_salaries_glassdoor", "1493");
        map.put("acme_salarydotcom", "490");
        map.put("acme_salarydotcom_term_salary_pay_make", "0");
        map.put("acme_smb_enterprise_jobs_temp", "0");
        map.put("acme_smb_jobs_temp", "0");
        map.put("background_check", "34");
        map.put("benefits", "345");
        map.put("benefits for working at {keyword}", "4");
        map.put("benefits for working for {keyword}", "2");
        map.put("careers at {cmp}", "0");
        map.put("careers at {keyword}", "77");
        map.put("career_explorer_salary", "2233");
        map.put("CompanySnapshot", "73093");
        map.put("delete (0)", "0");
        map.put("does {keyword1} or {keyword2} pay more", "1");
        map.put("does {keyword} drug test", "155");
        map.put("does {keyword} drug test for weed", "4");
        map.put("does {keyword} give raises", "2");
        map.put("does {keyword} hire felons", "32");
        map.put("does {keyword} hire part time", "1");
        map.put("does {keyword} pay weekly", "72");
        map.put("does {keyword} pay weekly or bi-weekly", "1");
        map.put("dress_code", "236");
        map.put("drug_test", "89");
        map.put("Education Landing page", "0");
        map.put("Education_Opportunity_Sizing_10k_schools_part2_temp", "0");
        map.put("Education_Opportunity_Sizing_10k_Schools_temp", "2678");
        map.put("Education_Opportunity_Sizing_5k_Schools_temp", "0");
        map.put("Education_Opportunity_Sizing_all_certifications_temp", "1889");
        map.put("Education_Opportunity_Sizing_live_certifications_temp (0)", "0");
        map.put("employee benefits {keyword}", "1");
        map.put("hiring at {cmp}", "0");
        map.put("hiring at {keyword}", "1");
        map.put("hiring_process", "35");
        map.put("how much does {keyword} pay", "326");
        map.put("how often does {keyword} give raises", "2");
        map.put("how often does {keyword} pay", "9");
        map.put("inc. careers", "1");
        map.put("interviews", "133");
        map.put("jobs at {cmp}", "0");
        map.put("jobs at {keyword}", "113");
        map.put("ltd reviews", "1");
        map.put("promotions", "2");
        map.put("replacements", "1");
        map.put("salary", "44");
        map.put("what to wear to a {keyword} interview", "6");
        map.put("who pays more {keyword1} or {keyword2}", "2");
        map.put("why {0}", "278");
        map.put("work at {0}", "323");
        map.put("work for {0}", "341");
        map.put("working at {keyword}", "393");
        map.put("working at {keyword} benefits", "5");
        map.put("working_hours", "33");
        map.put("work_from_home", "103");
        map.put("{0} employees", "462");
        map.put("{0} funding", "214");
        map.put("{0} health benefits", "242");
        map.put("{0} health insurance", "394");
        map.put("{0} highest salary", "24");
        map.put("{0} hiring", "447");
        map.put("{0} insurance", "432");
        map.put("{0} internship", "425");
        map.put("{0} jobs", "490");
        map.put("{0} pay", "444");
        map.put("{0} perks", "264");
        map.put("{0} promotions", "297");
        map.put("{0} revenue", "432");
        map.put("{0} wage", "152");
        map.put("{0} work culture", "127");
        map.put("{cmp}", "489");
        map.put("{cmp} careers", "0");
        map.put("{cmp} jobs", "0");
        map.put("{keyword1} or {keyword2}", "41");
        map.put("{keyword1} or {keyword2} which is better", "6");
        map.put("{keyword1} vs {keyword2}", "133");
        map.put("{keyword1} vs {keyword2} pay", "1");
        map.put("{keyword}", "1542");
        map.put("{keyword} average salary", "40");
        map.put("{keyword} background check", "46");
        map.put("{keyword} benefits", "537");
        map.put("{keyword} benefits enrollment", "2");
        map.put("{keyword} benefits package", "6");
        map.put("{keyword} careers", "4420");
        map.put("{keyword} company values", "17");
        map.put("{keyword} core values", "66");
        map.put("{keyword} dress code", "135");
        map.put("{keyword} drug test policy", "3");
        map.put("{keyword} employee benefits", "228");
        map.put("{keyword} employee benefits package", "1");
        map.put("{keyword} employee discount", "165");
        map.put("{keyword} employee reviews", "68");
        map.put("{keyword} employee training", "1");
        map.put("{keyword} employment", "1171");
        map.put("{keyword} hiring process", "108");
        map.put("{keyword} interview", "108");
        map.put("{keyword} interview preparation", "4");
        map.put("{keyword} interview process", "33");
        map.put("{keyword} interview questions", "282");
        map.put("{keyword} interview tips", "2");
        map.put("{keyword} job benefits", "5");
        map.put("{keyword} job interview", "6");
        map.put("{keyword} jobs", "519");
        map.put("{keyword} jobs remote", "63");
        map.put("{keyword} maternity leave", "26");
        map.put("{keyword} mission statement", "333");
        map.put("{keyword} part time benefits", "8");
        map.put("{keyword} part time hours", "10");
        map.put("{keyword} paternity leave", "10");
        map.put("{keyword} pay schedule", "15");
        map.put("{keyword} phone interview", "8");
        map.put("{keyword} phone interview questions", "8");
        map.put("{keyword} pto accrual rate", "1");
        map.put("{keyword} pto policy", "2");
        map.put("{keyword} remote jobs", "116");
        map.put("{keyword} remote work policy", "7");
        map.put("{keyword} return to office", "12");
        map.put("{keyword} reviews", "3546");
        map.put("{keyword} sabbatical", "1");
        map.put("{keyword} salaries", "154");
        map.put("{keyword} salary", "1913");
        map.put("{keyword} shift hours", "10");
        map.put("{keyword} sick day policy", "2");
        map.put("{keyword} sick days", "4");
        map.put("{keyword} unemployment benefits", "10");
        map.put("{keyword} uniform", "254");
        map.put("{keyword} vacation policy", "10");
        map.put("{keyword} vacation time", "5");
        map.put("{keyword} wfh", "0");
        map.put("{keyword} work from home", "204");
        map.put("{keyword} work from home 2021", "1");
        map.put("{keyword} work from home jobs", "86");
        map.put("{keyword} work from home policy", "5");
        map.put("{keyword} work life balance", "11");
        map.put("{keyword} workers benefits", "4");
        map.put("{keyword} working hours", "48");
    }

    private static String localFileFolder = "files/8711Files/";
    private static int domainId = 8711;
    private static String SPIT = "/t";
    private List<String> errKeywordList = new ArrayList<>();

    public static void main(String[] args) {
        RemoveKeywordTag_8711 in = new RemoveKeywordTag_8711();
        // 删除tag 下所有的 keyword
        try {
            in.process();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    private void removeTags(List<GroupTagEntity> tags) {
        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);
        //t_group_tag.dynamic_flg =0?1  ==1?2
        resourceBatchInfoEntity.setCustomFlag(1);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (GroupTagEntity tag : tags) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(tag.getTagid());
            rbd.setResourceCategory(1);
            rbd.setResourceMd5(Md5Util.Md5(tag.getTagid() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));

            resourceBatchDetailEntityList.add(rbd);
            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

    private void process() throws Exception {
        List<GroupTagEntity> tags = parseGroupTagKwList();
        System.out.println(tags.size());
//        removeKw(tags);

        removeTags(tags);

        // 检测删除情况
        queryCheckRemoveKw(tags);


    }

    private void queryCheckRemoveKw(List<GroupTagEntity> tags) {
        getKeywordCountByTags(tags, domainId);

    }

    private void getKeywordCountByTags(List<GroupTagEntity> tags, int domainId) {
        //select groupTagId ,count(*)  from cdb_tracked_keyword where ownDomainId = 8711 and groupTagId in( 7474581  ,0) group by groupTagId
        StringBuffer sql = new StringBuffer();
        sql.append(" select ");
        sql.append(" k.groupTagId,  t.tag_name  , count(*)   ");
        sql.append(" from cdb_tracked_keyword k ");
        sql.append("left join t_group_tag t  on k.groupTagId = t.id");
        sql.append(" where ownDomainId = ?");
        sql.append(" and groupTagId in (0 ");
        for (GroupTagEntity en : tags) {
            sql.append(",");
            sql.append(en.getTagid());
        }
        sql.append(") ");
        sql.append(" group by groupTagId");

        System.out.println("=================getKeywordCountByTags =====================");
        System.out.println(sql.toString());

    }


    private void removeKw(List<GroupTagEntity> tags) {

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_TAG_ID;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (GroupTagEntity group : tags) {
            if (group.getKeywordCount() <= 0 ){
                System.out.println("===####tag 没有keyword 所以不删除 " + group.getTagName());
                continue;
            }

            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(group.getTagid());
            rbd.setResourceSearchengines(null);
            rbd.setResourceCategory(1);
            rbd.setResourceMd5(Md5Util.Md5(group.getTagid() + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));

            resourceBatchDetailEntityList.add(rbd);
            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }

        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }


    private List<GroupTagEntity> parseGroupTagKwList() throws Exception {
        List<GroupTagEntity> resultList = new ArrayList<>();
        for (String str : map.keySet()) {
            String tagName = str;
            String kwCount = map.get(str);

            if (StringUtils.isBlank(tagName)) {
                System.out.println("=Skip empty tagName:" + tagName);
                continue;
            }
//                    if (tagName.equals("baby accessories")){
//                        System.out.println("=跳过测试数据:" + tagName);
//                        continue;
//                    }

            String tagId = "";
            if (tagName.contains("'")) {
                tagName = StringUtils.replace(tagName, "'", "\\'");
                System.out.println(tagName);
            }

            try {
                tagId = groupTagEntityDAO.getTagIdByTagName(tagName, GroupTagEntity.TAG_TYPE_KEYWORD, domainId);
            }catch (Exception e){
                System.out.println("===####tag : " + tagName + " 没有查到， 可能删除了 ， 跳过");
                continue;
            }


            System.out.println("===### tagName :: " + tagName + " , tagId : " + tagId + " , kwCount : " + kwCount );
            GroupTagEntity en = new GroupTagEntity();
            en.setTagName(tagName);
            en.setTagid(Long.valueOf(tagId));
            en.setKeywordCount(Integer.parseInt(kwCount));
            resultList.add(en);
        }


        return resultList;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }
}
