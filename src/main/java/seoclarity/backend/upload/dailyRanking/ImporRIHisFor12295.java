package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-01-15
 * @path
 * mSix & Partners | Historical Rank Integration
 * https://www.wrike.com/open.htm?id=1281784119
 */
public class ImporRIHisFor12295 extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private static final String https = "https://";

    private OwnDomainEntityDAO ownDomainEntityDAO;

    private static int domainId = 12685;
    private static String table_device = "m";
    private static String testFile = "";

    private static Map<String,Integer> domain = new HashedMap();
    private static Map<String,String> domainSE = new HashedMap();

    public ImporRIHisFor12295() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    }
    static {
        //12682 GB-en 	q	6_8
        //12690 DE DE-de	14_15
        //12690  GB-en		6_8
        //12690 FR FR-fr		4_7
        //12690 IT IT-it		8_9
        //12701  DE-de 		14_15
        //12701 B2 GB-en 	6_8
        //12295 GB-en		6_8

        domain.put("12690EN",12690);
        domain.put("12690DE",12690);
        domain.put("12690FR",12690);
        domain.put("12690IT",12690);
        domain.put("12682",12682);
        domain.put("12701B1",12701);
        domain.put("12701B2",12701);
        domain.put("12295",12295);


        domainSE.put("12690EN","6_8");
        domainSE.put("12690DE","14_15");
        domainSE.put("12690FR","4_7");
        domainSE.put("12690IT","8_9");
        domainSE.put("12682","6_8");
        domainSE.put("12701B1","14_15");
        domainSE.put("12701B2","6_8");
        domainSE.put("12295","6_8");
    }


    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test12690_local_";
        prefix_ins_table = "test12690_local_";

        checkHistoryTables = true;
        ImporRIHisFor12295 ins = new ImporRIHisFor12295();
        String filePath = "files/12295Files";
        if (args !=null && args.length>0){
            testFile = args[0];
        }

        try {
            System.out.println("=========================Process desktop===================================");
            for (File file : new File(filePath).listFiles()) {

//                if (file.getName().endsWith(".csv")) {
                if (file.getName().contains(".csv")) {
                    System.out.println("====================== gogogo ======================");
                    System.out.println("file name : " + file.getName());
                    if (StringUtils.isNotBlank(testFile)){
                        if (!file.getName().contains(testFile)){
                            continue;
                        }
                    }
                    ins.process(file.getAbsolutePath(), file.getName());
//                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file, String fileName) throws Exception {

        String key = fileName.substring(0,fileName.indexOf("."));
//        domainId = domain.get(key);
        String engineLanguageStr = domainSE.get(key);
        String[] se = engineLanguageStr.split("_");
        searchEngine = Integer.parseInt(se[0]);
         language = Integer.parseInt(se[1]);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, domainId);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + searchEngine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + domainId + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, true);
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, List<KeywordRankVO>> parseExcel(String file, int oid) throws Exception {
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");

        CSVParser parser = new CSVParser('\t');
        int lineCnt = 0;
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            lineCnt++;
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
                String[] cols = parser.parseLine(line);
                if (StringUtils.equalsIgnoreCase(cols[0], "Date") && StringUtils.equalsIgnoreCase(cols[1], "Site")) {
                    System.out.println(" 读取到第一行，啥也不干");
                } else {

                    String kwStr = cols[2];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[8];
                    if (StringUtils.isNotBlank(url) && !url.startsWith("https://")) {
                        url = https + url;
                    }
                    String device = cols[5];
                    if (device.equalsIgnoreCase("desktop")){
                        continue;
                    }

                    String rank = cols[6];
                    String webRank = cols[7];
//                    String searchEngine = cols[3];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String sv = cols[10];
                    if (StringUtils.isBlank(sv) || sv.contains("n") ) {
                        sv = "0";
                    }
                    String date = cols[0];
                    String cpc = cols[12];
                    if (StringUtils.isBlank(cpc) || cpc.contains("n")){
                        cpc = "0";
                    }

                    List<String> columns = new ArrayList<String>();
                    columns.add("0");
                    columns.add("0");
                    columns.add(device);
                    columns.add(date);
                    columns.add(kwStr);
                    columns.add(sv);
                    columns.add(rank);
                    columns.add(webRank);
                    columns.add(url);
                    columns.add(cpc);

                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }

    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Integer trueRank = StringUtils.isBlank(column.get(6)) ? 999 : Integer.parseInt(StringUtils.trim(column.get(6)));
            Integer webRank = StringUtils.isBlank(column.get(7)) ? 999 : Integer.parseInt(StringUtils.trim(column.get(7)));

            String url = column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc(column.get(9));
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, domain : "+processDomainId +" kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
//            else {
//                System.out.println("== just in info table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
//                errdateMap.put("keyword", column.get(4));
//                errdateMap.put("sv", column.get(5));
//                errdateMap.put("rank", column.get(6));
//                errdateMap.put("baseRank", column.get(7));
//                errdateMap.put("url", column.get(8));
//                errdateMap.put("date", column.get(3));
//                errDateList.add(errdateMap);
//            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
            return "";
        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
        } else if (url.length() <= ownDomainName.length()) {
            throw new Exception("Wrong url, url:" + url);
        }
        return url;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

}
