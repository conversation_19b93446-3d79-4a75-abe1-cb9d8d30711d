package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-06-14
 * @path https://www.wrike.com/open.htm?id=1384588455
 * 多个sheet 页 循环head 循环数据
 * <p>
 */
public class ImporRIHisForPens extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
    private SimpleDateFormat ddMMMyyyy = new SimpleDateFormat("dd-MMM-yyyy");

    private static String device = "d";
    private static int oid = 12753;
    private static boolean testFlg = true;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;

    public ImporRIHisForPens() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
    }


    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test12754_local_";
        prefix_ins_table = "test12754_local_";

        String filePath = "files/12759Files/240710Files";
        filePath = "files/12759Files/240710";
        ImporRIHisForPens ins = new ImporRIHisForPens();

        if (null != args && args.length > 0) {
            if (args[0].equals("f")) {
                testFlg = false;
                device = args[1];
            }

        }
        try {
            for (File file : new File(filePath).listFiles()) {
                if (file.getName().endsWith(".xlsx") && file.getName().contains("WEEKLY")) {
                    System.out.println(" ********************* run ************************");
                    System.out.println(file.getName() + ", device " + device);
                    ins.process(file.getAbsolutePath());

                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) throws Exception {
        parseExcel(file);
    }


    private void parseExcel(String file) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        for (int i = 0; i < wb.getNumberOfSheets(); i++) {
            List<String> columnList = new ArrayList<>();
            XSSFSheet sheet = wb.getSheetAt(i);

            String sheetName = wb.getSheetName(i);
            String[] domains = sheetName.split(" ");
            oid = Integer.parseInt(domains[domains.length - 1]);

            OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
            searchEngine = ScKeywordRankManager.getSearchEngineId(domainEntity);
            language = ScKeywordRankManager.getSearchLanguageId(domainEntity);

            int start = sheet.getFirstRowNum();
            int end = sheet.getLastRowNum();
            Map<String, List<KeywordRankVO>> dateMap = new HashMap<>();
            System.out.println("===file:" + file + ", lines:" + sheet.getPhysicalNumberOfRows());
            int cnt = 0;
            for (int n = start; n <= end; n++) {
                cnt++;
                XSSFRow row = sheet.getRow(n);
                if (row == null || row.getPhysicalNumberOfCells() == 0) {
                    continue;
                }

                try {
                    String[] cols = getStringArray(row);
                    // header
                    if (StringUtils.equalsIgnoreCase(cols[0], "Keyword")) {
                        System.out.println(" 读取到第一行，啥也不干");
                        columnList = Arrays.asList(cols);
                    } else {

                        List<List<String>> columnsList = new ArrayList<>();
                        String kwStr = cols[0];
                        kwStr = StringUtils.trim(kwStr).toLowerCase();

                        if (StringUtils.isBlank(kwStr)) {
                            System.out.println("=Skip empty keyword:" + kwStr);
                            continue;
                        }

                        String sv = "0";
                        KeywordAdwordsEntity entity = keywordAdwordsEntityDAO.getNationalKwSV(URLEncoder.encode(kwStr, "UTF-8"), language);
                        if (null != entity) {
                            sv = String.valueOf(entity.getAvgMonthlySearchVolume());
                            if (null == sv || StringUtils.isBlank(sv)) {
                                sv = "0";
                            }
                        }

                        if (null != entity && entity.getAvgMonthlySearchVolume() == null) {
                            System.out.println(" ===###kwStr : " + kwStr + " ,cnt : " + cnt + " , sv " + sv);
                            sv = "0";
                        } else {

                        }

                        for (int j = 1; j < cols.length - 1; j += 2) {

                            String date = columnList.get(j);
                            date = date.substring(date.indexOf("_") + 1, date.length()); // yyyymmdd
                            String rank = cols[j];
                            String url = cols[j + 1];
                            List<String> datesOfWeekStr = getWeekDatesAsString(date);
                            for (String today : datesOfWeekStr) {
                                mergeData(url, rank, today, kwStr, sv, columnsList);
                            }
                        }


                        for (List<String> columns : columnsList) {
                            KeywordRankVO[] vos = parsertoEntity(columns, oid);

                            if (vos != null && vos.length > 0) {
                                for (KeywordRankVO vo : vos) {
                                    String date = vo.getQueryDate();
                                    if (dateMap.containsKey(date)) {
                                        dateMap.get(date).add(vo);
                                    } else {
                                        List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                        voList.add(vo);
                                        dateMap.put(date, voList);
                                    }
                                }
                            }
                        }
                    }


                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
            savedomainsData(dateMap, oid);

            try {
                Thread.sleep(5000);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            System.out.println(" ===###finished oid : " + oid + " ,sleep 5 s  ==============================");

        }
    }

    private void savedomainsData(Map<String, List<KeywordRankVO>> dateMap, int oid) {
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file: , dateList:" + dateList.size());
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, device.equals("m"));
                    if (!testFlg) {
                        loadData(processList);
                    } else {
                        System.out.println("======测试结束=======");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static List<String> getWeekDatesAsString(String inputDateString) {

        DateTimeFormatter formatter_yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 将输入的日期字符串转换为LocalDate对象
        LocalDate inputDate = LocalDate.parse(inputDateString, formatter_yyyyMMdd);

        List<String> weekDatesStr = new ArrayList<>();

        // 调整到周日开始（如果需要从周一开始，可省略这步）
        inputDate = inputDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 遍历一周内的每一天，从周日到下周六，并转换为字符串
        for (int i = 0; i < 7; i++) {
            LocalDate date = inputDate.plusDays(i);
            weekDatesStr.add(date.format(formatter));
        }

        return weekDatesStr;
    }


    private String getRank(String col) {
        return (col.contains("N") || StringUtils.isBlank(col) || col.equals("0") || col.contains("r") || col.contains("-")) ? "999" : col;
    }

    private void mergeData(String url, String rank, String date, String kwStr, String sv, List<List<String>> columnsList) throws ParseException {
        String location = "US-en";
        System.out.println("kwStr: " + kwStr + ", date: " + date + ", url: " + url + ", rank: " + rank + " , sv : " + sv);

        if (StringUtils.isBlank(url) || !url.startsWith("https")) {
            url = "";
        }

        if (null == sv || StringUtils.isBlank(sv)) {
            sv = "0";
        }

        if (StringUtils.isBlank(rank) || rank.contains("-") || rank.equals("") || rank.equals("0") || rank.contains("r")) {
            rank = "999";
        }

        List<String> col0 = new ArrayList<>();
        col0.add(location);
        col0.add(String.valueOf(searchEngine));
        col0.add(device);
        col0.add(date);
        col0.add(kwStr);
        col0.add(sv);
        col0.add(rank);
        col0.add(rank);
        col0.add(url);
        columnsList.add(col0);

    }

    @NotNull
    private ArrayList<String> getColumns1() {
        return new ArrayList<String>();
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }


    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }


    @Override
    public void loadData(List<KeywordRankVO> dataList) throws Exception {
        System.out.println("willLoad:" + dataList.size());
        try {
            threadPool.init();
            CommonUtils.initThreads(MAX_THREAD);

            processInsert(workDate, searchEngine, language, mobileRanking, dataList);

            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            // 判断 kw 是不是 数字 ：
            try {
                Double kwDou = Double.valueOf(kw);
                Integer kwint = (int) Math.ceil(kwDou);
                kw = String.valueOf(kwint);
                System.out.println(" kw : " + column.get(4) + " 是 数字类型。。。");
            } catch (Exception e) {
                // 啥也不干
            }
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Double trueRankd = Double.valueOf(column.get(6));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(7));
            Integer webRank = (int) Math.ceil(webRankd);

            String url = (column.get(8).contains("N/A") || column.get(8).equals("0")) ? "" : column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc("0");
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
//        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
//            return "";
//        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
//            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
//        } else if (url.length() <= ownDomainName.length()) {
//            throw new Exception("Wrong url, url:" + url);
//        }
        return url;
    }


}
