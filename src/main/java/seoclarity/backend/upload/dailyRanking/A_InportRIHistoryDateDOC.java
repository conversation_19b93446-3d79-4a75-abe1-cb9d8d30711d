package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Hao
 * DOC of history data import
 */
public class A_InportRIHistoryDateDOC extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("dd-MMM-yyyy");
    private static final String https = "https://";



    /**
     * todo
     *    check domain main SE
      */
    //select tod.id,keyword_rank_frequency freq, concat(enable_city_rank, '_', enable_geo_mobile, '_', geo_frequence) geo,
    //separate_geo sepGeo,enable_different_ses diffSE, concat(engineId,'_',languageId, '_', if(ifNull(tod.mobile_domain_flg,0)=1, 'm', 'd')) SEId,
    //concat(search_engine,'(',search_engine_country, '_', language, ')') SE
    // from t_own_domain_setting tods join t_own_domain tod on tods.own_domain_id=tod.id
    // join engine_country_language_mapping mapp on tod.search_engine_country=mapp.countryQueryName
    // and tod.search_engine=mapp.engineQueryName and tod.language=mapp.languageQueryName
    //and tod.rank_from=mapp.rankFrom
    //where tod.status=1 and tod.id in(12756,12757,12758,12759);

    /**
     *  todo
     *   check if domain has different SE (if exist then ask customer to decide if need load different SE )
     */
//    select id,own_domain_id OID,search_engine,concat(search_engine_id,'_',search_engine_languageid, '_', device) SE
//    from domain_search_engine_rel where own_domain_id in(12756,12757,12758,12759);


    /**
     *  todo
     *    The history table changes its name in a given month
      */

    // ClDailyRankingEntityDao :  public static final int DEVIDING_DAY = 202210;


    /**
     * todo
     *  xlsx file
     */
    private Map<String, List<KeywordRankVO>> parseExcelXlsx(String file, int oid) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        System.out.println(" start : " + start + " end : " + end);
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();

        for (int n = start; n <= end; n++) {

            try {
                XSSFRow row = sheet.getRow(n);
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[4], "Keyword") && StringUtils.equalsIgnoreCase(cols[3], "Date")) {
                    System.out.println(" line :"  + " head ");
                } else if (cols[2].equals("Mobile")){

                    String kwStr = cols[4];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[7];
                    if (!url.startsWith("https://")) {
                        url = "";
                    }
//                    url = getUrl(url, domain);
                    String rank = cols[6].contains("o") ? "999" : cols[6].substring(0,cols[6].indexOf("."));
                    String sv = cols[5].substring(0,cols[5].indexOf("."));
                    String cpc = "0";
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String date = cols[3];
                    Calendar calendar1 = Calendar.getInstance();
                    calendar1.setTime(yyyyMMdd.parse(date));
                    date = yyyy_MM_dd.format(calendar1.getTime());
                    System.out.println(date);
                    List<String> columns = new ArrayList<String>();
                    columns.add(cols[1]);
                    columns.add(kwStr);
                    columns.add(date);
                    columns.add(rank);
                    columns.add(url);
                    columns.add(sv);
                    columns.add(cpc);
                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return dateMap;
    }

    /**
     *   todo
     * csv file
     */
    private Map<String, List<KeywordRankVO>> parseExcelCsv(String file, int oid) throws Exception {
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");

        CSVParser parser = new CSVParser('\t');
        int lineCnt = 0;
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            lineCnt++;
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
                String[] cols = parser.parseLine(line);
                if (StringUtils.equalsIgnoreCase(cols[0], "Date") && StringUtils.equalsIgnoreCase(cols[1], "Site")) {
                    System.out.println(" 读取到第一行，啥也不干");
                } else {

                    String kwStr = cols[2];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[8];
                    if (StringUtils.isNotBlank(url) && !url.startsWith("https://")) {
                        url = https + url;
                    }
                    String device = cols[5];
                    if (device.equalsIgnoreCase("desktop")){
                        continue;
                    }

                    String rank = cols[6];
                    String webRank = cols[7];
//                    String searchEngine = cols[3];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String sv = cols[10];
                    if (StringUtils.isBlank(sv) || sv.contains("n") ) {
                        sv = "0";
                    }
                    String date = cols[0];
                    String cpc = cols[12];
                    if (StringUtils.isBlank(cpc) || cpc.contains("n")){
                        cpc = "0";
                    }

                    List<String> columns = new ArrayList<String>();
                    columns.add("0");
                    columns.add("0");
                    columns.add(device);
                    columns.add(date);
                    columns.add(kwStr);
                    columns.add(sv);
                    columns.add(rank);
                    columns.add(webRank);
                    columns.add(url);
                    columns.add(cpc);

                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }

    /**
     * todo
     * some fields need be care
     */

    // url :  must contents (https://)
    // sv :  must be number not null   	Integer searchVolumn = getInteger(sv);
    // use sv from DB  :     KeywordAdwordsEntity entity = keywordAdwordsEntityDAO.getNationalKwSV(URLEncoder.encode(kwStr, "UTF-8"), language);


    /**
     * todo
     * DB
     */

    //cdb-ri-201 :
    //clickhouse-client -h 69.175.4.110 --password clarity99! --database seo_daily_ranking -m -n
    //cdb-ri-nj-001
    //clickhouse-client -h 63.251.114.6 --password clarity99! -m -n --database seo_daily_ranking
















































    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(1)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

//            Integer trueRank = Integer.parseInt(StringUtils.trim(column.get(3)));
//            Integer webRank = Integer.parseInt(StringUtils.trim(column.get(3)));
            Double trueRankd = Double.valueOf(column.get(3));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(3));
            Integer webRank = (int) Math.ceil(webRankd);

            String url = column.get(4);
            String rankingDate = (column.get(2));
            if (StringUtils.isBlank(url)) {
                trueRank = 999;
                webRank = 999;
            }

            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(column.get(5));
            vo1.setCpc(column.get(6));
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                if (rankingVo != null) {
                    list.add(rankingVo);
                } else {
                    System.out.println("=Skip wrong ranking url, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                }
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }


    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        if (StringUtils.isBlank(url) || trueRank == 101) {
            return null;
        }
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);

        return vo;
    }


    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }


    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {

    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking, List<KeywordRankVO> dataList) {

    }
}
