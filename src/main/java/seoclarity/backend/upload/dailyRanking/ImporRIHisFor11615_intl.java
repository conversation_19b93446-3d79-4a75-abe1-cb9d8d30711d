package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-03-15
 * @path Worldremit.com - 11615 | Settings | Increase GSC Profile Limit
 * https://www.wrike.com/open.htm?id=1073600889
 */
public class ImporRIHisFor11615_intl extends ImportHistoryRankingForDailyRanking {

    private static int domainId = 11615;
    private static int country ;
    public static final Map<Integer, String> COUNTRY_MAP = new HashMap();
    public static final Map<Integer, String> ENGINE_LANGUAGE = new HashMap();
    private SimpleDateFormat ddMMMyyyy = new SimpleDateFormat("dd-MMM-yyyy");
    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");

    static {
        COUNTRY_MAP.put(75728, "MX-es");//11-12
        COUNTRY_MAP.put(75405, "NZ-en");//38-38
        COUNTRY_MAP.put(75403, "AU-en");//2-5
        COUNTRY_MAP.put(75402, "CA-en");//3-3
        COUNTRY_MAP.put(33275, "US-en");//1-1
        COUNTRY_MAP.put(33271, "UK-en");//6-8
        COUNTRY_MAP.put(75674, "ES-es");//16-17
        COUNTRY_MAP.put(75395, "DE-de");//14-15
        COUNTRY_MAP.put(75729, "CO-es");//27-28
        COUNTRY_MAP.put(75505, "FR-fr");//4-7

        ENGINE_LANGUAGE.put(75728, "11-12");//11-12
        ENGINE_LANGUAGE.put(75405, "38-38");//38-38
        ENGINE_LANGUAGE.put(75403, "2-5");//2-5
        ENGINE_LANGUAGE.put(75402, "3-3");//3-3
        ENGINE_LANGUAGE.put(33275, "1-1");//1-1
        ENGINE_LANGUAGE.put(33271, "6-8");//6-8
        ENGINE_LANGUAGE.put(75674, "16-17");//16-17
        ENGINE_LANGUAGE.put(75395, "14-15");//14-15
        ENGINE_LANGUAGE.put(75729, "27-28");//27-28
        ENGINE_LANGUAGE.put(75505, "4-7");//4-7
    }

    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test11615_local_";
        prefix_ins_table = "test11615_local_";
        ImporRIHisFor11615_intl ins = new ImporRIHisFor11615_intl();
        String filePath = "files/11615Files/";
        String folder = "";

        if (args != null & args.length > 0) {
            folder = args[0];
            filePath = filePath + folder + "/";
        } else {

        }

        try {
            System.out.println("=========================Process desktop===================================");
            for (File file : new File(filePath).listFiles()) {

                if (file.getName().endsWith(".csv")) {
                    System.out.println("====================== gogogo ======================");
                    String[] name = file.getName().split("_");
                    country = Integer.parseInt(name[1]);
                    System.out.println("file name : " + file.getName() + " country = " + country);
                    ins.process(file.getAbsolutePath());
//                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) {

//        OwnDomainEntity domainENtity = ownDomainEntityDAO.getById(domainId);
        String engine_languge = ENGINE_LANGUAGE.get(country);
        System.out.println(" engine_languge : " + engine_languge);
        searchEngine = Integer.parseInt(engine_languge.split("-")[0]);
        language = Integer.parseInt(engine_languge.split("-")[1]);
        System.out.println(" engine :" + searchEngine + "language : " + language);
        System.out.println("************************** desktop *****************************");
        dataMapForDesktop(file);
        System.out.println("************************** mobile *****************************");
        dataMapForMobile(file);
    }

    private void dataMapForMobile(String file) {
        Map<String, List<KeywordRankVO>> dateMap = null;
        try {
            dateMap = parseExcelm(file, domainId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + searchEngine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + domainId + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, true);
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void dataMapForDesktop(String file) {
        Map<String, List<KeywordRankVO>> dateMap = null;
        try {
            dateMap = parseExceld(file, domainId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + searchEngine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + domainId + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, false);
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, List<KeywordRankVO>> parseExceld(String file, int oid) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser('\t');
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();

        for (String line : lines) {
            String[] cols = parser.parseLine(line);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[1], "Keyword") && StringUtils.equalsIgnoreCase(cols[0], "rankingDate")) {
                    System.out.println(" line : head ");
                } else {

                    String kwStr = cols[1];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[4];
//                    url = getUrl(url, domain);
                    String rank = cols[6];
                    String searchEngine = COUNTRY_MAP.get(country);
                    String sv = cols[2];
                    String cpc = cols[3];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String date = cols[0];
//                    Calendar calendar1 = Calendar.getInstance();
//                    calendar1.setTime(ddMMMyyyy.parse(date));
//                    date = yyyy_MM_dd.format(calendar1.getTime());
                    System.out.println(date);
//                    System.out.println(" date : " + date);
                    List<String> columns = new ArrayList<String>();
                    columns.add(searchEngine);
                    columns.add(kwStr);
                    columns.add(date);
                    columns.add(rank);
                    columns.add(url);
                    columns.add(sv);
                    columns.add(cpc);
                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }



    private Map<String, List<KeywordRankVO>> parseExcelm(String file, int oid) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser('\t');
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();

        for (String line : lines) {
            String[] cols = parser.parseLine(line);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[1], "Keyword") && StringUtils.equalsIgnoreCase(cols[0], "rankingDate")) {
                    System.out.println(" line :"  + " head ");
                } else {

                    String kwStr = cols[1];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[5];
//                    url = getUrl(url, domain);
                    String rank = cols[7];
                    String searchEngine = COUNTRY_MAP.get(country);
                    String sv = cols[2];
                    String cpc= cols[3];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String date = cols[0];
                    System.out.println(" date : " + date);
//                    Date date1 = new Date(date);
//                    date = yyyy_MM_dd.format(date1);
//                    System.out.println(" date : " + date);
                    List<String> columns = new ArrayList<String>();
                    columns.add(searchEngine);
                    columns.add(kwStr);
                    columns.add(date);
                    columns.add(rank);
                    columns.add(url);
                    columns.add(sv);
                    columns.add(cpc);
                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }


    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(1)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

//            Integer trueRank = Integer.parseInt(StringUtils.trim(column.get(3)));
//            Integer webRank = Integer.parseInt(StringUtils.trim(column.get(3)));
            Double trueRankd = Double.valueOf(column.get(3));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(3));
            Integer webRank = (int) Math.ceil(webRankd);

            String url = column.get(4);
            String rankingDate = (column.get(2));
            if (StringUtils.isBlank(url)) {
                trueRank = 101;
                webRank = 101;
            }

//            String sv = "0";
//            if (kwSet.contains(kw)) {
//                sv = kwMap.get(kw);
//            } else {
//                KeywordAdwordsEntity entity = keywordAdwordsEntityDAO.getNationalKwSV(URLEncoder.encode(kw, "UTF-8"), language);
//                if (null != entity) {
//                    kwSet.add(kw);
//                    sv = String.valueOf(entity.getAvgMonthlySearchVolume());
//                    kwMap.put(kw, sv);
//                } else {
//                    kwSet.add(kw);
//                    kwMap.put(kw, "0");
//                }
//            }
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(column.get(5));
            vo1.setCpc(column.get(6));
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 101 && StringUtils.isNotBlank(url)) {
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                if (rankingVo != null) {
                    list.add(rankingVo);
                } else {
                    System.out.println("=Skip wrong ranking url, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                }
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        if (StringUtils.isBlank(url) || trueRank == 101) {
            return null;
        }
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);

        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
            return "";
        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
        } else if (url.length() <= ownDomainName.length()) {
            throw new Exception("Wrong url, url:" + url);
        }
        return url;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

}
