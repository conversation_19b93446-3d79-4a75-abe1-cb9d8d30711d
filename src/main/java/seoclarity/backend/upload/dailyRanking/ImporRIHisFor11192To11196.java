package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-07-19
 * @path seoclarity.backend.upload.dailyRanking.ImporRIHisFor11192To11196
 * https://www.wrike.com/open.htm?id=931934722
 */
public class ImporRIHisFor11192To11196 extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat MMddyyyy = new SimpleDateFormat("MM/dd/yyyy");
    private SimpleDateFormat MM_dd_yyyy = new SimpleDateFormat("MM-dd-yyyy");
    private static final String https = "https://";

    private Set<String> kwSet = new HashSet<>();
    private Map<String, String> kwMap = new HashMap<>();
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;

    String[] dwStr = new String[]{
            "2022-04-29",
            "2022-04-30",
            "2022-05-01",
            "2022-05-02",
            "2022-05-03",
            "2022-05-04",
            "2022-05-05",
            "2022-05-06",
            "2022-05-07",
            "2022-05-08",
            "2022-05-09",
            "2022-05-10",
            "2022-05-11",
            "2022-05-12",
            "2022-05-13"
    };

    private static String device;

    public ImporRIHisFor11192To11196() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
    }

//    CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader(
//            "Keyword",
//            "Search Volume",
//            "Rank",
//            "Base Rank",
//            "URL",
//            "Date"
//    )
//            .withDelimiter(',');


    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test11192_local_";
        prefix_ins_table = "test11192_local_";

        checkHistoryTables = true;
        ImporRIHisFor11192To11196 ins = new ImporRIHisFor11192To11196();
        String filePath = "files/11192To11196Files";

        try {
            System.out.println("=========================Process desktop===================================");
            for (File file : new File(filePath).listFiles()) {

//                if (file.getName().endsWith(".csv")) {
//
//                if (file.getName().contains("DWR")) {
//                    System.out.println("====================== gogogo ======================");
//                    System.out.println("****************** oid 11192  *****************************8");
//                    System.out.println("file name : " + file.getName());
//                    if (file.getName().contains("Desktop")) {
//                        device = "d";
//                    } else {
//                        device = "m";
//                    }
//                    System.out.println(" device : " + device);
//                    ins.process(file.getAbsolutePath(), file.getName());
////                    }
//                }


//                if (file.getName().contains("HM Store")) {
//                    System.out.println("====================== gogogo ======================");
//                    System.out.println("****************** oid 11193  *****************************8");
//                    System.out.println("file name : " + file.getName());
//                    if (file.getName().contains("Desktop")) {
//                        device = "d";
//                    } else {
//                        device = "m";
//                    }
//                    System.out.println(" device : " + device);
//                    if (device.equals("d")){
//                    ins.process11193(file.getAbsolutePath(), file.getName());
//                    }
//                }
////
//
//                if (file.getName().contains("HAY")) {
//                    System.out.println("====================== gogogo ======================");
//                    System.out.println("****************** oid 11194  *****************************8");
//                    System.out.println("file name : " + file.getName());
//                    if (file.getName().contains("Desktop")) {
//                        device = "d";
//                    } else {
//                        device = "m";
//                    }
//                    System.out.println(" device : " + device);
//                    if (device.equals("m")) {
//                        ins.process11194(file.getAbsolutePath(), file.getName());
//                    }
////                    }
//                }
//
//
//                if (file.getName().contains("Knoll")) {
//                    System.out.println("====================== gogogo ======================");
//                    System.out.println("****************** oid 11195  *****************************8");
//                    System.out.println("file name : " + file.getName());
//                    if (file.getName().contains("Desktop")) {
//                        device = "d";
//                    } else {
//                        device = "m";
//                    }
//                    System.out.println(" device : " + device);
//                    ins.process11195(file.getAbsolutePath(), file.getName());
////                    }
//                }
//
//
                if (file.getName().contains("Fully")) {
                    System.out.println("====================== gogogo ======================");
                    System.out.println("****************** oid 11196  *****************************8");
                    System.out.println("file name : " + file.getName());
                    if (file.getName().contains("Desktop")) {
                        device = "d";
                    } else {
                        device = "m";
                    }
                    System.out.println(" device : " + device);
                    ins.process11196(file.getAbsolutePath(), file.getName());
//                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file, String fileName) throws Exception {
        int oid = 11192;
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        int engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        int language = ScKeywordRankManager.getSearchLanguageId(domainEntity);

//        int engine = 1;
//        int language = 1;
        System.out.println(" engine : " + engine + " language : " + language);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, oid, fileName);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + oid + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, engine, language, device.equals("m"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, List<KeywordRankVO>> parseExcel(String file, int oid, String fileName) throws Exception {
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
//        File fileV2 = new File("err" + fileName);
//        System.out.println("**********fileV2 :  err" + file);

        CSVParser parser = new CSVParser(',');
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
                String[] cols = parser.parseLine(line);
                if (cols[0].equalsIgnoreCase("Date") || cols[1].equalsIgnoreCase("Location")) {
                    System.out.println(" 第一行 啥也不干 ");
                } else {
                    System.out.println(" line : " + line);
                    String kwStr = cols[5];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[8];
//                    if (StringUtils.isNotBlank(url) && !url.startsWith("https://")) {
//                        url = https + url;
//                    }

                    String rank = cols[7];
                    String webRank = cols[7];
                    String searchEngine = cols[2];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String sv = cols[6];
                    if (sv.contains("n") || StringUtils.isBlank(sv)) {
                        sv = "0";
                    }
                    String location = cols[1];

                    String date = cols[0];
                    String newDate = "";
                    System.out.println(" date : " + date);
                    if (date.contains("-")) {
                        String[] d = date.split("-");
                        String month = d[0];
                        String day = d[1];
                        String year = d[2].equals("22") ? "2022" : "2021";
                        newDate = year + "-" + month + "-" + day;
                    } else if (date.contains("/")) {
                        String[] d = date.split("/");
                        String month = d[0];
                        String m = "";
                        if (month.length() > 2) {
                            m = month.substring(2, month.length());
                        } else {
                            m = month;
                        }
                        String day = d[1];
                        String year = d[2].equals("22") ? "2022" : "2021";
                        newDate = year + "-" + m + "-" + day;
                    } else if (date.contains("\\")) {
                        String[] d = date.split("/");
                        String month = d[0];
                        String m = month.substring(2, month.length());
                        String day = d[1];
                        String year = d[2].equals("22") ? "2022" : "2021";
                        newDate = year + "-" + m + "-" + day;
                    }
                    Calendar calendar3 = Calendar.getInstance();
                    calendar3.setTime(yyyy_MM_dd.parse(newDate));
                    newDate = yyyy_MM_dd.format(calendar3.getTime());

                    boolean fll = false;
                    for (String str : dwStr) {
                        if (str.equals(newDate)) {
                            fll = true;
                            break;
                        }
                    }
                    if (fll) {
                        // 日期不变
                        date = newDate;
                    } else {
                        // 日期 减3 天
                        Calendar calendar1 = Calendar.getInstance();
                        calendar1.setTime(yyyy_MM_dd.parse(newDate));
                        calendar1.add(Calendar.DATE, -3);
                        date = yyyy_MM_dd.format(calendar1.getTime());
                    }

                    System.out.println("date  ： " + date + " newDate : " + newDate + "url : " + url);
                    List<String> columns = new ArrayList<String>();
                    columns.add(location);
                    columns.add(searchEngine);
                    columns.add(device);
                    columns.add(date);
                    columns.add(kwStr);
                    columns.add(sv);
                    columns.add(rank);
                    columns.add(webRank);
                    columns.add(url);

                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }

    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Integer trueRank = StringUtils.isBlank(column.get(6)) ? 999 : Integer.parseInt(StringUtils.trim(column.get(6)));
            Integer webRank = StringUtils.isBlank(column.get(7)) ? 999 : Integer.parseInt(StringUtils.trim(column.get(7)));

            String url = column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc("0");
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
            return "";
        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
        } else if (url.length() <= ownDomainName.length()) {
            throw new Exception("Wrong url, url:" + url);
        }
        return url;
    }

    private void process11193(String file, String fileName) throws Exception {
        int oid = 11193;
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        int engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        int language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
        System.out.println(" engine : " + engine + " language : " + language);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, oid, fileName);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + oid + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, engine, language, device.equals("m"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel11193(String file, int oid, String fileName) throws Exception {
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
//        File fileV2 = new File("err" + fileName);
//        System.out.println("**********fileV2 :  err" + file);

        CSVParser parser = new CSVParser(',');
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
                String[] cols = parser.parseLine(line);
                if (cols[2].equalsIgnoreCase("Search Engine") && cols[1].equalsIgnoreCase("Location")) {
                    System.out.println(" 第一行 啥也不干 ");
                } else {
                    System.out.println(" line : " + line);
                    String kwStr = cols[4];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[7];
//                    if (StringUtils.isNotBlank(url) && !url.startsWith("https://")) {
//                        url = https + url;
//                    }

                    String rank = cols[6];
                    String webRank = cols[6];
                    String searchEngine = cols[2];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String sv = cols[5];
                    if (sv.contains("n") || StringUtils.isBlank(sv)) {
                        sv = "0";
                    }
                    String location = cols[1];

                    String date = cols[0];
                    System.out.println(" date : " + date);
                    String[] d = date.split("-");
                    String month = d[0];
                    String day = d[1];
                    String year = d[2].equals("22") ? "2022" : "2021";

                    String newDate = year + "-" + month + "-" + day;
                    System.out.println("date  ： " + date + " newDate : " + newDate + "url : " + url);
                    List<String> columns = new ArrayList<String>();
                    columns.add(location);
                    columns.add(searchEngine);
                    columns.add(device);
                    columns.add(newDate);
                    columns.add(kwStr);
                    columns.add(sv);
                    columns.add(rank);
                    columns.add(webRank);
                    columns.add(url);

                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(newDate)) {
                                dateMap.get(newDate).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(newDate, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }


    private void process11194(String file, String fileName) throws Exception {
        int oid = 11194;
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        int engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        int language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
        System.out.println(" engine : " + engine + " language : " + language);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel11194(file, oid, fileName);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + oid + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, engine, language, device.equals("m"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, List<KeywordRankVO>> parseExcel11194(String file, int oid, String fileName) throws Exception {
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
//        File fileV2 = new File("err" + fileName);
//        System.out.println("**********fileV2 :  err" + file);

        CSVParser parser = new CSVParser(',');
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
                String[] cols = parser.parseLine(line);
                if (cols[2].equalsIgnoreCase("Search Engine") && cols[1].equalsIgnoreCase("Location")) {
                    System.out.println(" 第一行 啥也不干 ");
                } else {
                    List<String> columns = new ArrayList<String>();

                    if (device.equals("m")) {
                        System.out.println(" line : " + line);
                        String kwStr = cols[5];
                        kwStr = StringUtils.trim(kwStr).toLowerCase();
                        String url = cols[8];
//                    if (StringUtils.isNotBlank(url) && !url.startsWith("https://")) {
//                        url = https + url;
//                    }

                        String rank = cols[7];
                        String webRank = cols[7];
                        String searchEngine = cols[2];
                        if (StringUtils.isBlank(kwStr)) {
                            System.out.println("=Skip empty keyword:" + kwStr);
                            continue;
                        }
                        String sv = cols[6];
                        if (sv.contains("n") || StringUtils.isBlank(sv)) {
                            sv = "0";
                        }
                        String location = cols[1];

                        String date = cols[0];
                        System.out.println(" date : " + date);
                        String newDate = "";
                        if (date.contains("-")){
                            String[] d = date.split("-");
                            String month = d[0];
                            String day = d[1];
                            String year = d[2].equals("22") ? "2022" : "2021";
                            newDate = year + "-" + month + "-" + day;
                            Calendar calendar1 = Calendar.getInstance();
                            calendar1.setTime(yyyy_MM_dd.parse(newDate));
                            date = yyyy_MM_dd.format(calendar1.getTime());
                        }else {
                            Calendar calendar1 = Calendar.getInstance();
                            calendar1.setTime(MMddyyyy.parse(date));
                            date = yyyy_MM_dd.format(calendar1.getTime());
                        }
                        boolean fll = false;
                        for (String str : dwStr){
                            if (str.equals(date)){
                                fll = true;
                                break;
                            }
                        }
                        if (!fll){
                            Calendar calendar2 = Calendar.getInstance();
                            try {
                                calendar2.setTime(yyyy_MM_dd.parse(date));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            calendar2.add(Calendar.DATE, -3);
                            date = yyyy_MM_dd.format(calendar2.getTime());
                        }
                        System.out.println("date  ： " + date + "url : " + url);

                        columns.add(location);
                        columns.add(searchEngine);
                        columns.add(device);
                        columns.add(date);
                        columns.add(kwStr);
                        columns.add(sv);
                        columns.add(rank);
                        columns.add(webRank);
                        columns.add(url);

                        System.out.println(" location : " + location
                        + "searchEngine" + searchEngine
                        +"device" + device);
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);
                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    } else {

                        System.out.println(" line : " + line);
                        String kwStr = cols[4];
                        kwStr = StringUtils.trim(kwStr).toLowerCase();
                        String url = cols[7];
//                    if (StringUtils.isNotBlank(url) && !url.startsWith("https://")) {
//                        url = https + url;
//                    }

                        String rank = cols[6];
                        String webRank = cols[6];
                        String searchEngine = cols[2];
                        if (StringUtils.isBlank(kwStr)) {
                            System.out.println("=Skip empty keyword:" + kwStr);
                            continue;
                        }
                        String sv = cols[5];
                        if (sv.contains("n") || StringUtils.isBlank(sv)) {
                            sv = "0";
                        }
                        String location = cols[1];

                        String date = cols[0];
                        System.out.println(" date : " + date);
                        String newDate = "";
                        if (date.contains("-")) {
                            String[] d = date.split("-");
                            String month = d[0];
                            String day = d[1];
                            String year = d[2].equals("22") ? "2022" : "2021";
                            newDate = year + "-" + month + "-" + day;
                            Calendar calendar1 = Calendar.getInstance();
                            calendar1.setTime(yyyy_MM_dd.parse(newDate));
                            date = yyyy_MM_dd.format(calendar1.getTime());
                        } else {
                            Calendar calendar1 = Calendar.getInstance();
                            calendar1.setTime(MMddyyyy.parse(date));
                            date = yyyy_MM_dd.format(calendar1.getTime());
                        }
                        boolean fll = false;
                        for (String str : dwStr) {
                            if (str.equals(date)) {
                                fll = true;
                                break;
                            }
                        }
                        if (!fll) {
                            Calendar calendar2 = Calendar.getInstance();
                            try {
                                calendar2.setTime(yyyy_MM_dd.parse(date));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            calendar2.add(Calendar.DATE, -3);
                            date = yyyy_MM_dd.format(calendar2.getTime());
                        }
                        System.out.println("date  ： " + date + "url : " + url);
                        columns.add(location);
                        columns.add(searchEngine);
                        columns.add(device);
                        columns.add(date);
                        columns.add(kwStr);
                        columns.add(sv);
                        columns.add(rank);
                        columns.add(webRank);
                        columns.add(url);
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);
                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }


    private void process11196(String file, String fileName) throws Exception {
        int oid = 11196;
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        int engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        int language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
        System.out.println(" engine : " + engine + " language : " + language);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel11196(file, oid, fileName);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + oid + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, engine, language, device.equals("m"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel11196(String file, int oid, String fileName) throws Exception {
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
//        File fileV2 = new File("err" + fileName);
//        System.out.println("**********fileV2 :  err" + file);

        CSVParser parser = new CSVParser(',');
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
                String[] cols = parser.parseLine(line);
                if (cols[2].equalsIgnoreCase("Search Engine") && cols[1].equalsIgnoreCase("Location")) {
                    System.out.println(" 第一行 啥也不干 ");
//                } else if (cols[0].equals("9/30/2021")||cols[0].contains("9/30/2021")){
//                    System.out.println(" 单跑 9月 30 日的数据 ");
                } else {
                    List<String> columns = new ArrayList<String>();

                    System.out.println(" line : " + line);
                    String kwStr = cols[5];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[8];
//                    if (StringUtils.isNotBlank(url) && !url.startsWith("https://")) {
//                        url = https + url;
//                    }

                    String rank = cols[7];
                    String webRank = cols[7];
                    String searchEngine = cols[2];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String sv = cols[6];
                    if (sv.contains("n") || StringUtils.isBlank(sv)) {
                        sv = "0";
                    }
                    String location = cols[1];

                    String date = cols[0];
                    System.out.println(" date : " + date);
                    Calendar calendar1 = Calendar.getInstance();
                    calendar1.setTime(MMddyyyy.parse(date));
//                    calendar1.add(Calendar.DATE, -3);
                    date = yyyy_MM_dd.format(calendar1.getTime());

                    String weekDay = getLastSunday(date);

                    System.out.println("date  ： " + weekDay + "url : " + url);

                    columns.add(location);
                    columns.add(searchEngine);
                    columns.add(device);
                    columns.add(weekDay);
                    columns.add(kwStr);
                    columns.add(sv);
                    columns.add(rank);
                    columns.add(webRank);
                    columns.add(url);
                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(weekDay)) {
                                dateMap.get(weekDay).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(weekDay, voList);
                            }
                        }
                    }


                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }


    private void process11195(String file, String fileName) throws Exception {
        int oid = 11195;
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        int engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        int language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
        System.out.println(" engine : " + engine + " language : " + language);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel11195(file, oid, fileName);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + oid + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, engine, language, device.equals("m"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel11195(String file, int oid, String fileName) throws Exception {
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
//        File fileV2 = new File("err" + fileName);
//        System.out.println("**********fileV2 :  err" + file);

        CSVParser parser = new CSVParser(',');
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
                String[] cols = parser.parseLine(line);
                if (cols[2].equalsIgnoreCase("Search Engine") && cols[1].equalsIgnoreCase("Location")) {
                    System.out.println(" 第一行 啥也不干 ");
                } else {
                    List<String> columns = new ArrayList<String>();

                    System.out.println(" line : " + line);
                    String kwStr = cols[4];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[7];
//                    if (StringUtils.isNotBlank(url) && !url.startsWith("https://")) {
//                        url = https + url;
//                    }

                    String rank = cols[6];
                    if (StringUtils.isBlank(rank)) {
                        rank = "999";
                    }
                    String rk = rank;
                    if (rank.contains(".")) {
                        rk = rank.substring(0, rank.indexOf("."));
                    }

                    String searchEngine = cols[2];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String sv = cols[5];
                    if (sv.contains("n") || StringUtils.isBlank(sv)) {
                        sv = "0";
                    }
                    String location = cols[1];

                    String date = cols[0];
                    System.out.println(" date : " + date);
                    String[] dt = date.split("-");
                    String year = dt[2];
                    if (year.equals("21")) {
                        date = dt[0] + "-" + dt[1] + "-" + "2021";
                    } else if (year.equals("22")) {
                        date = dt[0] + "-" + dt[1] + "-" + "2022";
                    }
                    System.out.println(" date : " + date);
                    Calendar calendar1 = Calendar.getInstance();
                    try {
                        calendar1.setTime(MM_dd_yyyy.parse(date));
                        calendar1.add(Calendar.DATE, -3);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    date = yyyy_MM_dd.format(calendar1.getTime());

                    System.out.println("date  ： " + date + "url : " + url);

                    columns.add(location);
                    columns.add(searchEngine);
                    columns.add(device);
                    columns.add(date);
                    columns.add(kwStr);
                    columns.add(sv);
                    columns.add(rk);
                    columns.add(rk);
                    columns.add(url);
                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }


                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }
    // getLastSunday
    public String getLastSunday(String date){
        Calendar cal = Calendar.getInstance();
        String weekDay = "";
        try {
            cal.setTime(yyyy_MM_dd.parse(date));
            int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK) -1;
            if (dayOfWeek == 0){
                weekDay = date;
            }else {
                cal.add(Calendar.DATE,-(dayOfWeek));
                weekDay = yyyy_MM_dd.format(cal.getTime());

            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return weekDay;
    }

}
