package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;

import java.io.File;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-05-23
 * Orsted | Historical Ranking Import
 * @path https://www.wrike.com/open.htm?id=1118160116
 */
public class ImporRIHisForOrsted extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
    private SimpleDateFormat ddMMMyyyy = new SimpleDateFormat("dd-MMM-yyyy");

    private static String device = "d";
    private static int oid = 11914;
    static boolean qb_flg = true;

    private static boolean testFlg = false;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private KeywordEntityDAO keywordEntityDAO;

    public ImporRIHisForOrsted() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
    }


    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test11914_local_";
        prefix_ins_table = "test11914_local_";
        String folder = "";
        if (args != null & args.length > 0) {
            folder = args[0] + "/";
            if (StringUtils.isNotBlank(args[1]) && args[1].equals("f")){
                qb_flg = false;
            }
        }

        String filePath = "files/11914Files/"+folder;
        System.out.println(filePath);
        ImporRIHisForOrsted ins = new ImporRIHisForOrsted();

        try {
            for (File file : new File(filePath).listFiles()) {
                System.out.println(" ********************* run ************************");
                String domainId = file.getName().substring(0, file.getName().indexOf("."));
                oid = Integer.parseInt(domainId);
//                if (oid == 11896 || oid == 11914) {
//                    device = "d";
//                } else {
//                    device = "m";
//                }
                System.out.println(" file : " +file.getName() + " oid  :  " + oid + " device : " + device);
                if (file.getName().endsWith(".xls")) {
                    ins.process(file.getAbsolutePath());
                } else if (file.getName().endsWith(".csv")) {
                    ins.processCsv(file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void processCsv(String file) throws Exception {

        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        searchEngine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
        System.out.println(" engine : " +searchEngine + " language : " + language);

        Map<String, List<KeywordRankVO>> dateMap = parseExcelCsv(file, searchEngine, oid);

        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList.size());
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, device.equals("m"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, List<KeywordRankVO>> parseExcelCsv(String file, int engine, int oid) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        CSVParser parser = new CSVParser(',');
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        List<KeywordEntity> qbaseList = new ArrayList<>();
        List<String> columnList = new ArrayList<>();
        int co = 0;
        for (String line : lines) {
            co++;
            if (co < 7) {
                continue;
            }
            String[] cols = parser.parseLine(line);
            try {
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[1], "Tags")) {
                    System.out.println(" 读取到第一行 ");
                    columnList = Arrays.asList(cols);
                } else {

                    List<List<String>> columnsList = new ArrayList<>();
                    String kw = cols[0];
                    String tagName = cols[1];
                    String kwStr = StringUtils.trim(kw).toLowerCase();

                    // managed keyword 添加qbase 任务 提交 keywordtagrel
                    //  t_keyword type=1 rank_check = 1
                    KeywordEntity keywordEntity = keywordEntityDAO.checkManagedKw(kwStr, oid);
                    if (null != keywordEntity && keywordEntity.getId() > 0) {
                        System.out.println(" managed 关键字 ： " + kw + " , " + tagName + " 添加到qbase 任务 ");
                        KeywordEntity kwen = new KeywordEntity();
                        kwen.setKeywordName(kw);
                        kwen.setTagName(tagName);
                        qbaseList.add(kwen);
                    }

                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String sv = "";
                    if (StringUtils.isNotBlank(cols[cols.length - 2]) && !cols[cols.length - 2].contains("n")) {
                        sv = cols[cols.length - 2];
                        Double svD = Double.parseDouble(sv);
                        Long svl = svD.longValue();
                        sv = svl.toString();
                    } else {
                        sv = "0";
                    }
                    String cpc = "";
                    if (StringUtils.isNotBlank(cols[cols.length - 1]) && !cols[cols.length - 1].contains("n")) {
                        cpc = cols[cols.length - 1];
                        Double cpcD = Double.parseDouble(cpc);
                        Long cpcl = cpcD.longValue();
                        cpc = cpcl.toString();
                    } else {
                        cpc = "0";
                    }

                    boolean dailyFlg = false;
                    for (int i = 2; i < cols.length - 3; i += 3) {
                        //*.orsted.de/*_20230313 之前 都是 weekly
                        String date = columnList.get(i);
                        date = date.substring(date.indexOf("_") + 1, date.length()); // yyyymmdd
                        String rank = cols[i];
                        String url = cols[i + 2];

                        if (date.contains("northseaenergyisland")) {
                            System.out.println("northseaenergyisland 中断 "+file + " col : " + date);
                            break;
                        }
                        if (date.contains("equinor")) {
                            System.out.println("equinor 中断 "+file + " col : " + date);
                            break;
                        }
                        if (date.contains("difference")) {
                            System.out.println("difference 中断 "+file + " col : " + date);
                            break;
                        }

                        //*.orsted.de/*_20230312 20230313 之后 都是 daily
                        if (date.contains("20230312") || date.contains("20230313")) {
                            dailyFlg = true;
                        }

                        Calendar calendar1 = Calendar.getInstance();
                        calendar1.setTime(yyyyMMdd.parse(date));
                        Date day = calendar1.getTime();
                        if (dailyFlg) {
                            // daily
                        } else {
                            // weekly
                            day = DateUtils.getLastSunday(day);
                        }
                        date = yyyy_MM_dd.format(day);

                        if (i % 72 == 0 || i < 5) {
                            System.out.println("url : " + url + " , rank :" + rank + " ,date :" + date + " , kwStr :" + kwStr + ",sv:  " + sv + " , cpc " + cpc);
                        }
                        mergeData(url, rank, date, kwStr, sv, cpc, columnsList);
                    }


                    for (List<String> columns : columnsList) {
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);

                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                String date = vo.getQueryDate();
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        // 提交 qbase
        System.out.println(" qbase list : " + qbaseList.size());
        getQBaseTast(qbaseList);
        return dateMap;
    }

    private void process(String file) throws Exception {

        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        searchEngine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        language = ScKeywordRankManager.getSearchLanguageId(domainEntity);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, searchEngine, oid);

        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList.size());
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, device.equals("m"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel(String file, int engine, int oid) throws Exception {


        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<>();
        List<KeywordEntity> qbaseList = new ArrayList<>();
        System.out.println("===file:" + file + ", lines:" + sheet.getPhysicalNumberOfRows());
        List<String> columnList = new ArrayList<>();
        int cnt = 0;
        for (int n = start; n <= end; n++) {
            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            if (n < 6) {
                continue;
            }

            try {
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[1], "Tags")) {
                    System.out.println(" 读取到第一行 ");
                    columnList = Arrays.asList(cols);
                } else {

                    List<List<String>> columnsList = new ArrayList<>();
                    String kw = cols[0];
                    String tagName = cols[1];
                    String kwStr = StringUtils.trim(kw).toLowerCase();

                    // managed keyword 添加qbase 任务 提交 keywordtagrel
                    //  t_keyword type=1 rank_check = 1
                    KeywordEntity keywordEntity = keywordEntityDAO.checkManagedKw(kwStr, oid);
                    if (null != keywordEntity && keywordEntity.getId() > 0) {
                        System.out.println(" managed 关键字 ： " + kw + " , " + tagName + " 添加到qbase 任务 ");
                        KeywordEntity kwen = new KeywordEntity();
                        kwen.setKeywordName(kw);
                        kwen.setTagName(tagName);
                        qbaseList.add(kwen);
                    }

                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String sv = "";
                    if (StringUtils.isNotBlank(cols[cols.length - 2]) && !cols[cols.length - 2].contains("n")) {
                        sv = cols[cols.length - 2];
                        Double svD = Double.parseDouble(sv);
                        Long svl = svD.longValue();
                        sv = svl.toString();
                    } else {
                        sv = "0";
                    }
                    String cpc = "";
                    if (StringUtils.isNotBlank(cols[cols.length - 1]) && !cols[cols.length - 1].contains("n")) {
                        cpc = cols[cols.length - 1];
                        Double cpcD = Double.parseDouble(cpc);
                        Long cpcl = cpcD.longValue();
                        cpc = cpcl.toString();
                    } else {
                        cpc = "0";
                    }

                    boolean dailyFlg = false;
                    for (int i = 2; i < cols.length - 3; i += 3) {
                        //*.orsted.de/*_20230313 之前 都是 weekly
                        String date = columnList.get(i);
                        date = date.substring(date.indexOf("_") + 1, date.length()); // yyyymmdd
                        String rank = cols[i];
                        String url = cols[i + 2];

                        if (date.contains("northseaenergyisland")) {
                            System.out.println("northseaenergyisland 中断 "+file + " col : " + date);
                            break;
                        }
                        if (date.contains("equinor")) {
                            System.out.println("equinor 中断 "+file + " col : " + date);
                            break;
                        }
                        if (date.contains("difference")) {
                            System.out.println("difference 中断 "+file + " col : " + date);
                            break;
                        }

                        //*.orsted.de/*_20230312 之后 都是 daily
                        if (date.contains("20230312") || date.contains("20230313")) {
                            dailyFlg = true;
                        }
                        Calendar calendar1 = Calendar.getInstance();
                        calendar1.setTime(yyyyMMdd.parse(date));
                        Date day = calendar1.getTime();
                        if (dailyFlg) {
                            // daily
                        } else {
                            // weekly
                            day = DateUtils.getLastSunday(day);
                        }
                        date = yyyy_MM_dd.format(day);

                        if (i % 72 == 0 || i < 5) {
                            System.out.println("url : " + url + " , rank :" + rank + " ,date :" + date + " , kwStr :" + kwStr + ",sv:  " + sv + " , cpc " + cpc);
                        }
                        mergeData(url, rank, date, kwStr, sv, cpc, columnsList);
                    }


                    for (List<String> columns : columnsList) {
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);

                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                String date = vo.getQueryDate();
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        // 提交 qbase
        System.out.println(" qbase list : " + qbaseList.size());
        getQBaseTast(qbaseList);
        return dateMap;

    }

    private String getRank(String col) {
        return (col.contains("N") || StringUtils.isBlank(col) || col.equals("0") || col.contains("r")) ? "999" : col;
    }

    private void mergeData(String url, String rank, String date, String kwStr, String sv, String cpc, List<List<String>> columnsList) throws ParseException {
        String location = "0";

        if (StringUtils.isBlank(url) || !url.startsWith("https")) {
            url = "";
        }
        if (StringUtils.isBlank(rank) || rank.contains("-") || rank.equals("") || rank.equals("0")|| rank.contains("r")){
            rank = "999";
        }

        List<String> col0 = new ArrayList<>();
        col0.add(location);
        col0.add(String.valueOf(searchEngine));
        col0.add(device);
        col0.add(date);
        col0.add(kwStr);
        col0.add(sv);
        col0.add(rank);
        col0.add(rank);
        col0.add(url);
        col0.add(cpc);
        columnsList.add(col0);

    }

    @NotNull
    private ArrayList<String> getColumns1() {
        return new ArrayList<String>();
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }


    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }


    @Override
    public void loadData(List<KeywordRankVO> dataList) throws Exception {
        System.out.println("willLoad:" + dataList.size());
        try {
            threadPool.init();
            CommonUtils.initThreads(MAX_THREAD);

            processInsert(workDate, searchEngine, language, mobileRanking, dataList);

            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            // 判断 kw 是不是 数字 ：
            try {
                Double kwDou = Double.valueOf(kw);
                Integer kwint = (int) Math.ceil(kwDou);
                kw = String.valueOf(kwint);
                System.out.println(" kw : " + column.get(4) + " 是 数字类型。。。");
            } catch (Exception e) {
                // 啥也不干
            }
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Double trueRankd = Double.valueOf(column.get(6));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(7));
            Integer webRank = (int) Math.ceil(webRankd);


            String url = (column.get(8).contains("N/A") || column.get(8).equals("0")) ? "" : column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc(column.get(9));
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }


    private void getQBaseTast(List<KeywordEntity> keywordIdList) {
        if (!qb_flg){
            System.out.println(" 跳过qbase ： " + oid);
            return;
        }

        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (KeywordEntity kw : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(kw.getKeywordName()); // 不能decode
//            rbd.setResourceId(kw.getTagId());
            rbd.setResourceSubordinate(kw.getTagName());
            // setResourceMd5 唯一的
            rbd.setResourceMd5(Md5Util.Md5(kw.getKeywordName() + kw.getTagName() + new Date().getTime()));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }


}
