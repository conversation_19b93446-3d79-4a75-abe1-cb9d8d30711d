package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-07-15
 * @path https://www.wrike.com/open.htm?id=928534102
 */
public class ImporRIHisFor10995 extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat MM_dd_yyyy = new SimpleDateFormat("MM/dd/yyyy");
    private static Map<String, List<String>> domainMap = new HashMap<>();

    private static String device = "m";
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;

    public ImporRIHisFor10995() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
    }


    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test10995_local_";
        prefix_ins_table = "test10995_local_";

        String filePath = "10995File";
        ImporRIHisFor10995 ins = new ImporRIHisFor10995();
        try {
            for (File file : new File(filePath).listFiles()) {
                if (file.getName().endsWith(".xlsx")) {
                    System.out.println(" ********************* run ************************8");
                    ins.process(file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) throws Exception {
        int oid = 10995;
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        int engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        int language = ScKeywordRankManager.getSearchLanguageId(domainEntity);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, engine, oid);

        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList.size());
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, engine, language, device.equals("m"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel(String file, int engine, int oid) throws Exception {


        String location = "US-en";
        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<>();
        System.out.println("===file:" + file + ", lines:" + sheet.getPhysicalNumberOfRows());
        int cnt = 0;
        for (int n = start; n <= end; n++) {
            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }

            try {
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[1], "Rank 043021")) {
                    System.out.println(" 读取到第一行，啥也不干");
                } else {
                    if (cnt % 100 == 0 || cnt == 1) {
                        for (String str : cols) {
                            System.out.println(" 每隔100 行 打印一下 看看是啥 ： ");
                            System.out.println(str);
                        }
                    }
                    List<List<String>> columnsList = new ArrayList<>();
                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String domain = "www.sterlingfurniture.co.uk";
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String date043021 = "2021-04-30";
                    String date043021Rank = (cols[1].contains("N/A") || StringUtils.isBlank(cols[1]) || cols[1].equals("0")) ? "999" : cols[1];
                    String date043021Url = cols[14];
                    List<String> col0 = new ArrayList<>();
                    col0.add(location);
                    col0.add(String.valueOf(engine));
                    col0.add(device);
                    col0.add(date043021);
                    col0.add(kwStr);
                    col0.add("0");
                    col0.add(date043021Rank);
                    col0.add(date043021Rank);
                    col0.add(date043021Url);
                    columnsList.add(col0);
                    String date053121 = "2021-05-31";
                    String date053121Rank = (cols[2].contains("N/A") || StringUtils.isBlank(cols[2]) || cols[2].equals("0")) ? "999" : cols[2];
                    String date053121Url = cols[15];
                    List<String> col1 = new ArrayList<>();
                    col1.add(location);
                    col1.add(String.valueOf(engine));
                    col1.add(device);
                    col1.add(date053121);
                    col1.add(kwStr);
                    col1.add("0");
                    col1.add(date053121Rank);
                    col1.add(date053121Rank);
                    col1.add(date053121Url);
                    columnsList.add(col1);

                    String date063021 = "2021-06-30";
                    String date063021Rank = (cols[3].contains("N/A") || StringUtils.isBlank(cols[3]) || cols[3].equals("0")) ? "999" : cols[3];
                    String date063021Url = cols[16];
                    List<String> col2 = new ArrayList<>();
                    col2.add(location);
                    col2.add(String.valueOf(engine));
                    col2.add(device);
                    col2.add(date063021);
                    col2.add(kwStr);
                    col2.add("0");
                    col2.add(date063021Rank);
                    col2.add(date063021Rank);
                    col2.add(date063021Url);
                    columnsList.add(col2);

                    String date073121 = "2021-07-31";
                    String date073121Rank = (cols[4].contains("N/A") || StringUtils.isBlank(cols[4]) || cols[4].equals("0")) ? "999" : cols[4];
                    String date073121Url = cols[17];
                    List<String> col3 = new ArrayList<>();
                    col3.add(location);
                    col3.add(String.valueOf(engine));
                    col3.add(device);
                    col3.add(date073121);
                    col3.add(kwStr);
                    col3.add("0");
                    col3.add(date073121Rank);
                    col3.add(date073121Rank);
                    col3.add(date073121Url);
                    columnsList.add(col3);

                    String date083121 = "2021-08-31";
                    String date083121Rank = (cols[5].contains("N/A") || StringUtils.isBlank(cols[5]) || cols[5].equals("0")) ? "999" : cols[5];
                    String date083121Url = cols[16];
                    List<String> col4 = new ArrayList<>();
                    col4.add(location);
                    col4.add(String.valueOf(engine));
                    col4.add(device);
                    col4.add(date083121);
                    col4.add(kwStr);
                    col4.add("0");
                    col4.add(date083121Rank);
                    col4.add(date083121Rank);
                    col4.add(date083121Url);
                    columnsList.add(col4);

                    String date093021 = "2021-09-30";
                    String date093021Rank = (cols[6].contains("N/A") || StringUtils.isBlank(cols[6]) || cols[6].equals("0")) ? "999" : cols[6];
                    String date093021Url = cols[17];
                    List<String> col5 = new ArrayList<>();
                    col5.add(location);
                    col5.add(String.valueOf(engine));
                    col5.add(device);
                    col5.add(date093021);
                    col5.add(kwStr);
                    col5.add("0");
                    col5.add(date093021Rank);
                    col5.add(date093021Rank);
                    col5.add(date093021Url);
                    columnsList.add(col5);

                    String date103121 = "2021-10-31";
                    String date103121Rank = (cols[7].contains("N/A") || StringUtils.isBlank(cols[7]) || cols[7].equals("0")) ? "999" : cols[7];
                    String date103121Url = cols[18];
                    List<String> col6 = new ArrayList<>();
                    col6.add(location);
                    col6.add(String.valueOf(engine));
                    col6.add(device);
                    col6.add(date103121);
                    col6.add(kwStr);
                    col6.add("0");
                    col6.add(date103121Rank);
                    col6.add(date103121Rank);
                    col6.add(date103121Url);
                    columnsList.add(col6);

                    String date113021 = "2021-11-30";
                    String date113021Rank = (cols[8].contains("N/A") || StringUtils.isBlank(cols[8]) || cols[8].equals("0")) ? "999" : cols[8];
                    String date113021Url = cols[19];
                    List<String> col7 = new ArrayList<>();
                    col7.add(location);
                    col7.add(String.valueOf(engine));
                    col7.add(device);
                    col7.add(date113021);
                    col7.add(kwStr);
                    col7.add("0");
                    col7.add(date113021Rank);
                    col7.add(date113021Rank);
                    col7.add(date113021Url);
                    columnsList.add(col7);

                    String date123121 = "2021-12-31";
                    String date123121Rank = (cols[9].contains("N/A") || StringUtils.isBlank(cols[9]) || cols[9].equals("0")) ? "999" : cols[9];
                    String date123121Url = cols[20];
                    List<String> col8 = new ArrayList<>();
                    col8.add(location);
                    col8.add(String.valueOf(engine));
                    col8.add(device);
                    col8.add(date123121);
                    col8.add(kwStr);
                    col8.add("0");
                    col8.add(date123121Rank);
                    col8.add(date123121Rank);
                    col8.add(date123121Url);
                    columnsList.add(col8);

                    String date013122 = "2022-01-31";
                    String date013122Rank = (cols[10].contains("N/A") || StringUtils.isBlank(cols[10]) || cols[10].equals("0")) ? "999" : cols[10];
                    String date013122Url = cols[21];
                    List<String> col9 = new ArrayList<>();
                    col9.add(location);
                    col9.add(String.valueOf(engine));
                    col9.add(device);
                    col9.add(date013122);
                    col9.add(kwStr);
                    col9.add("0");
                    col9.add(date013122Rank);
                    col9.add(date013122Rank);
                    col9.add(date013122Url);
                    columnsList.add(col9);

                    String date022822 = "2022-02-28";
                    String date022822Rank = (cols[11].contains("N/A") || StringUtils.isBlank(cols[11]) || cols[11].equals("0")) ? "999" : cols[11];
                    String date022822Url = cols[22];
                    List<String> col10 = new ArrayList<>();
                    col10.add(location);
                    col10.add(String.valueOf(engine));
                    col10.add(device);
                    col10.add(date022822);
                    col10.add(kwStr);
                    col10.add("0");
                    col10.add(date022822Rank);
                    col10.add(date022822Rank);
                    col10.add(date022822Url);
                    columnsList.add(col10);

                    String date033022 = "2022-03-30";
                    String date033022Rank = (cols[12].contains("N/A") || StringUtils.isBlank(cols[12]) || cols[12].equals("0")) ? "999" : cols[12];
                    String date033022Url = cols[23];
                    List<String> col11 = new ArrayList<>();
                    col11.add(location);
                    col11.add(String.valueOf(engine));
                    col11.add(device);
                    col11.add(date033022);
                    col11.add(kwStr);
                    col11.add("0");
                    col11.add(date033022Rank);
                    col11.add(date033022Rank);
                    col11.add(date033022Url);
                    columnsList.add(col11);

                    String date043022 = "2022-04-30";
                    String date043022Rank = (cols[13].contains("N/A") || StringUtils.isBlank(cols[13]) || cols[13].equals("0")) ? "999" : cols[13];
                    String date043022Url = cols[24];
                    List<String> col12 = new ArrayList<>();
                    col12.add(location);
                    col12.add(String.valueOf(engine));
                    col12.add(device);
                    col12.add(date043022);
                    col12.add(kwStr);
                    col12.add("0");
                    col12.add(date043022Rank);
                    col12.add(date043022Rank);
                    col12.add(date043022Url);
                    columnsList.add(col12);

                    for (List<String> columns : columnsList) {
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);

                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                String date = vo.getQueryDate();
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return dateMap;

    }

    @NotNull
    private ArrayList<String> getColumns1() {
        return new ArrayList<String>();
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }


    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }


    @Override
    public void loadData(List<KeywordRankVO> dataList) throws Exception {
        System.out.println("willLoad:" + dataList.size());
        try {
            threadPool.init();
            CommonUtils.initThreads(MAX_THREAD);

            processInsert(workDate, searchEngine, language, mobileRanking, dataList);

            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            // 判断 kw 是不是 数字 ：
            try {
                Double kwDou = Double.valueOf(kw);
                Integer kwint = (int) Math.ceil(kwDou);
                kw = String.valueOf(kwint);
                System.out.println(" kw : " + column.get(4) + " 是 数字类型。。。");
            } catch (Exception e) {
                // 啥也不干
            }
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Double trueRankd = Double.valueOf(column.get(6));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(7));
            Integer webRank = (int) Math.ceil(webRankd);


            String url = (column.get(8).contains("N/A") || column.get(8).equals("0")) ? "" : column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc("0");
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
//        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
//            return "";
//        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
//            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
//        } else if (url.length() <= ownDomainName.length()) {
//            throw new Exception("Wrong url, url:" + url);
//        }
        return url;
    }


}
