package seoclarity.backend.upload.dailyRanking;

import seoclarity.backend.dao.actonia.AioKeywordWithoutSubrankDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 *--Hao
 * https://www.wrike.com/open.htm?id=1478839255
 * s20 : /home/<USER>/source/extract_scripts/clarity-backend-scripts/AioSummary.sh>>
 * nohup mvn  exec:java -Dexec.mainClass="seoclarity.backend.upload.dailyRanking.AioSummary"
 * -Dexec.args="2024-08-01 2024-08-23 t" -Dexec.cleanupDaemonThreads=false >> a.log&
 */
public class AioSummary {
    private static String sdate = "2024-08-01";
    private static String edate = "2024-08-01";
    private static Integer engineId = 1;
    private static Integer languageId = 1;
    private static boolean isMobile = false;
    private static boolean isTest = false;

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private AioKeywordWithoutSubrankDAO aioKeywordWithoutSubrankDAO;

    public AioSummary() {
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        aioKeywordWithoutSubrankDAO = SpringBeanFactory.getBean("aioKeywordWithoutSubrankDAO");
    }

    public static void main(String[] args) {
        if (null != args && args.length > 0) {
            sdate = args[0];
            edate = args[1];
            isTest = args[2].equals("t") ? true : false;
        }
        System.out.println("sdate : " + sdate + " edate : " + edate + " isMobile : " + isMobile + " isTest : " + isTest);

        AioSummary in = new AioSummary();
        in.process();
    }

    private void process() {
        if (sdate.equals(edate)) {
            // 没有传入日期 获取当前日期
            Calendar calendar = Calendar.getInstance();
            Date date = calendar.getTime();
            sdate = edate = dateFormat.format(date);
        } else {
            //传入日期 获取日期区间
        }

        List<CLRankingDetailEntity> list = clDailyRankingEntityDao.getAioSummary(sdate, edate, engineId, languageId, false);
        System.out.println("===###listSize : " + list.size());
        if (list != null) {
            if (!isTest) {
                aioKeywordWithoutSubrankDAO.insertBitch(list, engineId, languageId, false);
            }
        }

        List<CLRankingDetailEntity> list2 = clDailyRankingEntityDao.getAioSummary(sdate, edate, engineId, languageId, true);
        System.out.println("===###listSize : " + list.size());
        if (list2 != null) {
            if (!isTest) {
                aioKeywordWithoutSubrankDAO.insertBitch(list2, engineId, languageId, true);
            }
        }


    }
}
