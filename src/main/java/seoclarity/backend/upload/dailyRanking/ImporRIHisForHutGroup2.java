package seoclarity.backend.upload.dailyRanking;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @date 2021-03-08
 * @path seoclarity.backend.upload.dailyRanking.ImporRIHisForHutGroup2
 * https://www.wrike.com/open.htm?id=651742590
 */
public class ImporRIHisForHutGroup2 extends ImportHistoryRankingForDailyRanking {
	private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
	private static Map<Integer, String> domainMap = new HashMap<Integer, String>();
	static {
		domainMap.put(10186 ,  "www.burtsbees.co.uk");
		domainMap.put(10204 ,  "www.omorovicza.co.uk");
		domainMap.put(10205 ,  "www.omorovicza.com");
		domainMap.put(10206 ,  "www.optifast.co.uk"); 
		domainMap.put(9750  , "uk.sttropeztan.com"); 
		domainMap.put(9751  , "us.sttropeztan.com"); 
	}
	private OwnDomainEntityDAO ownDomainEntityDAO;
	
	public ImporRIHisForHutGroup2() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}

	public static void main(String[] args) {
		isUseSendToQDate = true;
		isUseMonitorTable = true;
		isUseRIExistsCheck = true;
		isQueryForSv = true;
		specified_monitor_table_refix = "test_his_daily_ranking_imp_monitor_deckers_";
		ImporRIHisForHutGroup2 ins = new ImporRIHisForHutGroup2();
		try {
			System.out.println("=========================Process desktop===================================");
			for (File file : new File(args[0]).listFiles()) {
				// uk.sttropeztan.com - 9750.csv
				if (file.getName().endsWith(".csv")) {
					ins.process(file.getAbsolutePath());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void process(String file) throws Exception{
		int oid = Integer.valueOf(StringUtils.trim(StringUtils.split(StringUtils.substringBeforeLast(file, "."), "-")[1]));
		OwnDomainEntity domainENtity = ownDomainEntityDAO.getById(oid);
		int engine = ScKeywordRankManager.getSearchEngineId(domainENtity);
		int language = ScKeywordRankManager.getSearchLanguageId(domainENtity);
		
		Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, oid);
		if (dateMap.size() > 0) {
			List<String> dateList = new ArrayList<String>(dateMap.keySet());
			Collections.sort(dateList);
			System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
			try {
				Thread.sleep(20000);
			} catch (Exception e) {
			}
			for (String date: dateList) {
				try {
					List<KeywordRankVO> processList = dateMap.get(date);
					System.out.println("=====================OID:" + oid + date + "===============================");
					Date workDate = yyyy_MM_dd.parse(date);
					setParameters(workDate, engine, language, false);
					loadData(processList);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	private Map<String, List<KeywordRankVO>> parseExcel(String file, int oid) throws Exception{
		List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
		Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
		
		System.out.println("===file:" + file + ", lines:" + lines.size());
		
		CSVParser parser = new CSVParser(',');
		int totalColumns = 0;
		List<String> header = null;
		int lineCnt = 0;
		for (String line : lines) {
			lineCnt++;
			if (StringUtils.isBlank(line)) {
				continue;
			}
			try {
				String[] cols = parser.parseLine(line);
				// header
				if (StringUtils.equalsIgnoreCase(cols[0], "Domain") && StringUtils.equalsIgnoreCase(cols[1], "Keyword")) {
					totalColumns = cols.length;
					header = new ArrayList<String>(Arrays.asList(cols));
					System.out.println("===file:" + file + ", date list:" + header.subList(6, header.size() - 1));
				} else if (cols.length != totalColumns) {
						System.out.println("===cols wrong. oid:" +oid + ", File:" + file + ", lineCnt:" + lineCnt + ", colLength:" + cols.length + ", totalColumns:" + totalColumns + ", line:" + line);
						continue;
				} else {
					String domain = cols[0];
					String kwStr = cols[1];
					kwStr = StringUtils.trim(kwStr).toLowerCase();
					String url = cols[5];
					url = getUrl(url, domain);
					
					if (StringUtils.isBlank(kwStr)) {
						System.out.println("=Skip empty keyword:" + kwStr);
						continue;
					}
					
					for (int i = 6; i < cols.length; i++) {
						String date = header.get(i);
						int rank = 101;
						try {
							rank =  (StringUtils.isNotBlank(cols[i]) && !StringUtils.equals(StringUtils.trim(cols[i]), "-")) ? Integer.valueOf(StringUtils.trim(cols[i])) : 101;
						} catch (Exception e) {
							System.out.println("===Parse rank failed.File:" + file + ", lineIdx:" + lineCnt + ", colsIdx:" + i + ", rank:" + cols[i] + ", line:" + line);
						}
						
						List<String> columns = new ArrayList<String>();
						columns.add(kwStr);
						columns.add("0");
						columns.add(String.valueOf(rank));
						columns.add(String.valueOf(rank));
						columns.add(url);
						columns.add(date);
						
						KeywordRankVO[] vos = parsertoEntity(columns, oid);
						if (vos != null && vos.length > 0) {
							for (KeywordRankVO vo : vos) {
								if (dateMap.containsKey(date)) {
									dateMap.get(date).add(vo);
								} else {
									List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
									voList.add(vo);
									dateMap.put(date, voList);
								}
							}
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
		return dateMap;
	}

	@Override
	void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
		this.workDate = workdate;
		this.searchEngine = searchEngine;
		this.language = language;
		this.mobileRanking = mobileRanking;
	}

	@Override
	void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
			List<KeywordRankVO> dataList) {
		// check for duplicated keywords
		Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
		dataList.stream().forEach((vo) -> {
			String key = vo.getId().toString();
			if (checkMap.get(key) == null) {
				checkMap.put(key, vo);
			} else {
				KeywordRankVO vo0 = checkMap.get(key);
				KeywordRankVO mergedVo = mergeRanking(vo0, vo);
				checkMap.put(key, mergedVo);
			}
		});
		dataList.clear();
		dataList.addAll(checkMap.values());
	}
	
	private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
		List<String> domainList = new ArrayList<String>();
		try {
			String kw = StringUtils.trim(column.get(0)).toLowerCase();
			kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();
			String sv = StringUtils.trim((column.get(1)));
			Integer searchVolumn = getInteger(sv);
			searchVolumn = searchVolumn < 0 ? 0 : searchVolumn;
			Integer trueRank = Integer.valueOf(StringUtils.trim(column.get(2)));
			Integer webRank = Integer.valueOf(StringUtils.trim(column.get(3)));
			String url = column.get(4);
			String rankingDate = (column.get(5));
			// TODO
			if (StringUtils.isBlank(url)) {
				trueRank = 101;
				webRank = 101;
			}
			
			domainList.add(String.valueOf(processDomainId));

			List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

			KeywordRankVO vo1 = new KeywordRankVO();
			vo1.setKeyword(kw);
			vo1.setQueryDate(rankingDate);
			vo1.setSendToQDate(
					FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
			vo1.setDomainList(domainList);
			vo1.setSearchVol(searchVolumn.toString());
			vo1.setCpc("0");
			List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
			if (trueRank < 101 && StringUtils.isNotBlank(url)) {
				KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
				if (rankingVo != null) {
					list.add(rankingVo);
				} else {
					System.out.println("=Skip wrong ranking url, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
				}
			}
			vo1.setKeywordRankEntityVOs(list);
			voList.add(vo1);

			return voList.toArray(new KeywordRankVO[voList.size()]);
		} catch (Exception e) {
			System.out.println("Parse entity wrong. line:" + column);
			e.printStackTrace();
		}
		return null;
	}
	
	private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
		if (StringUtils.isBlank(url) || trueRank == 101) {
			return null;
		}
		KeywordRankEntityVO vo = new KeywordRankEntityVO();
		vo.setLandingPage(url);
		vo.setRank(trueRank);
		vo.setSpecifiedWebRank(webRank);
		vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
		
		return vo;
	}
	
	protected static String getUrl(String str, String ownDomainName) throws Exception{
		String url = StringUtils.trim(str);
		if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
			return "";
		} else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
			throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
		} else if (url.length() <= ownDomainName.length()) {
			throw new Exception("Wrong url, url:" + url);
		}
		return url;
	}
	
}
