package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-02-15
 * @path UniGroup | Historical ranking import 11402,11403 | Historical Ranking Import
 * https://www.wrike.com/open.htm?id=1058639250
 */
public class ImporRIHisFor11402_11403 extends ImportHistoryRankingForDailyRanking {

    static List<Map<String, String>> columnsList = new ArrayList<>();
    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
    private static int engine;
    private static int language;

    private Set<String> kwSet = new HashSet<>();
    private Map<String, String> kwMap = new HashMap<>();
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private String errorList = new String();
    private static Boolean isMobile = true;
    static Set<List<String>> dupllicateSet = new HashSet<>();
    private static int domainId = 11402;
    private static String device = "d";

    public ImporRIHisFor11402_11403() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    }

    @Override
    public void loadData(List<KeywordRankVO> dataList) throws Exception {
        System.out.println("willLoad:" + dataList.size());
        try {
            threadPool.init();
            CommonUtils.initThreads(MAX_THREAD);
            System.out.println(workDate);
            System.out.println(searchEngine);
            System.out.println(language);
            System.out.println(mobileRanking);
            processInsert(workDate, searchEngine, language, mobileRanking, dataList);

            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test11402_local_";
        prefix_ins_table = "test11402_local_";

        checkHistoryTables = false;
        ImporRIHisFor11402_11403 ins = new ImporRIHisFor11402_11403();
        String filePathd = "files/11402Files/d";
        String filePathm = "files/11402Files/m";
//        String filePath = "I:\\11402\\Mayflower Desktop";
//        ins.processd(filePathd,"d");
        ins.processd(filePathm,"m");

        System.out.println("error list : " + ins.errorList);
    }

    private void processd(String filePathd,String devic) {
        try {
         device = devic;
            System.out.println("=========================Process ===================================");
            for (File file : new File(filePathd).listFiles()) {
                // todo  跑之前 先去看下 ClDailyRankingEntityDao   69 行
                if (file.getName().contains(".csv")) {
                    System.out.println("====================== gogogo ======================");
                    System.out.println("file name : " + file.getName());
                    process(file);

                }
            }

            margeRankData();

            Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
            //数据 去重
            for (List<String> columns : dupllicateSet) {
                for (String str : columns) {
                    System.out.println(str);
                }
                KeywordRankVO[] vos = parsertoEntity(columns, domainId);
                String date = columns.get(3);
                if (vos != null && vos.length > 0) {
                    for (KeywordRankVO vo : vos) {
                        if (dateMap.containsKey(date)) {
                            dateMap.get(date).add(vo);
                        } else {
                            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                            voList.add(vo);
                            dateMap.put(date, voList);
                        }
                    }
                }
            }

            if (dateMap.size() > 0) {
                List<String> dateList = new ArrayList<String>(dateMap.keySet());
                Collections.sort(dateList);
                System.out.println("=== file:" + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
                for (String date : dateList) {
                    try {
                        List<KeywordRankVO> processList = dateMap.get(date);
                        Date workDate = yyyy_MM_dd.parse(date);
                        setParameters(workDate, engine, language, device.startsWith("m"));
                        loadData(processList);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void margeRankData() {
        Map<String, Map<String, String>> keyMap = new HashMap<>();
        for (Map<String, String> column : columnsList) {
            String kw = column.get("kwStr");
            String dateStr = column.get("date");
            String url = column.get("url");
            String rank = column.get("rank");
            String webRank = column.get("webRank");
            String key = kw + "_" + dateStr + "_" + url;


            if (null != keyMap && keyMap.containsKey(key)) {
                Map<String, String> value = keyMap.get(key);
                System.out.println("alexander and james [same key ]");
                if (!rank.equals("0") && !rank.contains("Not")) {
                    value.put("rank", rank);
                    value.put("webRank", webRank);
                }


            } else {
                System.out.println("alexander and james [different  key ]");
                keyMap.put(key, column);
            }

        }
        for (String key1 : keyMap.keySet()) {
            Map<String, String> value = keyMap.get(key1);
            List<String> columns = new ArrayList<String>();
            System.out.println(" 最终数据  ： *****************************************");
            System.out.println(value);
            columns.add(value.get("location"));
            columns.add(value.get("searchEngine"));
            columns.add(value.get("device"));
            columns.add(value.get("date"));
            columns.add(value.get("kwStr"));
            columns.add(value.get("sv"));
            columns.add(value.get("rank"));
            columns.add(value.get("webRank"));
            columns.add(value.get("url"));

            dupllicateSet.add(columns);
        }
    }

    private void process(File file) throws Exception {
        parseExcel(file);
    }

    private void parseExcel(File file) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file.getAbsolutePath()), "UTF-8");
        CSVParser parser = new CSVParser(',');

        OwnDomainEntity domainENtity = ownDomainEntityDAO.getById(domainId);
        engine = ScKeywordRankManager.getSearchEngineId(domainENtity);
        language = ScKeywordRankManager.getSearchLanguageId(domainENtity);
        System.out.println(" engine :" + engine + "language : " + language);

        int n = 0;
        for (String line : lines) {
            n++;
            try {
                String[] cols = parser.parseLine(line);
                System.out.println(cols[0]);
                if (n < 11) {
                    continue;
                }
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[2], "Monthly Search Volume")) {
                    System.out.println(" 第一行");
                } else {

                    String domain = "mayflower.com";
                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[3].startsWith("https") ? cols[3] : "";
//                    url = getUrl(url, domain);
                    String rank = cols[2].contains("o")||cols[2].contains("O")?"999":cols[2];
                    String searchEngine = "Google US";
                    String sv = cols[1];
                    if (sv.contains("n") || StringUtils.isBlank(sv) || sv.contains("N")) {
                        sv = "0";
                    }else if (sv.contains(",")){
                        sv = sv.replace(",","");
                    }
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String date = file.getName().substring(0,file.getName().length()-4) + "-01";
                    System.out.println(" date : " + date);
                    Map<String, String> columeMap = new HashMap<>();
                    columeMap.put("location", "");
                    columeMap.put("searchEngine", searchEngine);
                    columeMap.put("device", device);
                    columeMap.put("date", date);
                    columeMap.put("kwStr", kwStr);
                    columeMap.put("sv", sv);
                    columeMap.put("rank", rank);
                    columeMap.put("webRank", rank);
                    columeMap.put("url", url);
                    columnsList.add(columeMap);

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

//        return dateMap;
    }

    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Double trueRankD = StringUtils.isBlank(column.get(6)) || column.get(6).contains("o") ? 999 : Double.parseDouble(StringUtils.trim(column.get(6)));
            Double webRankD = StringUtils.isBlank(column.get(7)) || column.get(7).contains("o") ? 999 : Double.parseDouble(StringUtils.trim(column.get(7)));
            Integer trueRank = Integer.parseInt(String.valueOf(trueRankD.intValue()));
            Integer webRank = Integer.parseInt(String.valueOf(webRankD.intValue()));

            // 当 true rank 或者webrank 有一个为0  truerank = webRank
            if (trueRank > 0 || webRank > 0) {
                if (trueRank == 0) {
                    trueRank = webRank;
                } else if (webRank == 0) {
                    webRank = trueRank;
                }
            }
            String url = column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5).contains("o") ? "0" : column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc("0");
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
            return "";
        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
        } else if (url.length() <= ownDomainName.length()) {
            throw new Exception("Wrong url, url:" + url);
        }
        return url;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

}
