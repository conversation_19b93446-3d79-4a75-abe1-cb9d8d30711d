package seoclarity.backend.upload.dailyRanking;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.summary.onetime.BEApi;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * data from BE api
 * https://www.wrike.com/open.htm?id=1279360106
 */
@CommonsLog
public class ImporRIHisFor12709 extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat ddMMMyyyy = new SimpleDateFormat("dd-MMM-yyyy");
    private static int engine;
    private static int language;
    private static String URL_TYPE = "https://";
    private static String device = "d";
    private static int sheet_page = 0;

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;
    private String errorList = new String();
    static Set<List<String>> dupllicateSet = new HashSet<>();
    private static List<String> managedKwList = new ArrayList<>();
    public ImporRIHisFor12709() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
    }

    @Override
    public void loadData(List<KeywordRankVO> dataList) throws Exception {
        System.out.println("willLoad:" + dataList.size());
        try {
            threadPool.init();
            CommonUtils.initThreads(MAX_THREAD);
            System.out.println(workDate);
            System.out.println(searchEngine);
            System.out.println(language);
            System.out.println(false);
            processInsert(workDate, searchEngine, language, mobileRanking, dataList);

            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public static void main(String[] args) {

        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test12709_local_";
        prefix_ins_table = "test12709_local_";

        checkHistoryTables = false;
        ImporRIHisFor12709 ins = new ImporRIHisFor12709();
        String filePath = "/home/<USER>/source/jason/extract/BEApi/12709/";

        try {
            System.out.println("=========================Process ===================================");

            OwnDomainEntity domainEntity = ins.ownDomainEntityDAO.getById(12709);
            engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
            language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
            System.out.println(" engine :" + engine + "language : " + language);
            managedKwList = getManagedKeywordList(12709);

            for (File weeklyFolder : new File(filePath).listFiles()) {
                log.info("===processFolder:" + weeklyFolder.getName());
//                if(!weeklyFolder.getName().equals(args[0])){
//                    continue;
//                }
                for(File file : weeklyFolder.listFiles()){
                    log.info("===processFile:" + file.getName());
                    ins.process(file,  12709, "mobile");
                }
            }

//            filePath = "/home/<USER>/source/jason/extract/BEApi/12710/";
//            OwnDomainEntity domainEntity = ins.ownDomainEntityDAO.getById(12710);
//            engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
//            language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
//            System.out.println(" engine :" + engine + "language : " + language);
//            managedKwList = getManagedKeywordList(12710);
//
//            for (File weeklyFolder : new File(filePath).listFiles()) {
////                if(!weeklyFolder.getName().equals(args[0])){
////                    continue;
////                }
//                log.info("===processFolder:" + weeklyFolder.getName());
//                for(File file : weeklyFolder.listFiles()){
//                    log.info("===processFile:" + file.getName());
//                    ins.process(file,  12710, "desktop");
//                }
//            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("error list : " + ins.errorList);
    }

    private static List<String> getManagedKeywordList(int domainId){
        List<String> kwList = new ArrayList<>();
        try {
            File file = new File("");
            if(domainId == 12709){
                file = new File("/home/<USER>/source/jason/extract/BEApi/12709/12709_trackedkeywords.txt");
            }else if(domainId == 12710){
                file = new File("/home/<USER>/source/jason/extract/BEApi/12710/12710_trackedkeywords.txt");
            }
            kwList = FileUtils.readLines(file, "utf-8");
            log.info("==getManagedKeywordList:" + kwList.size());
        }catch (Exception e){
            e.printStackTrace();
        }
        return kwList;
    }

    private void process(File file, int domainId, String device) throws Exception {
        Map<String, List<KeywordRankVO>> dateMap = parseFile(file, domainId, device);

        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList.size() + ", engine:" + engine + ", language:" + language + " device : " + device);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================Date :" +  date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, engine, language, device.equals("mobile"));
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, List<KeywordRankVO>> parseFile(File file, int domainId, String device) throws Exception {
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        List<String> lineList = FileUtils.readLines(file, "utf-8");
        Gson gson = new Gson();
        int index = 0;
        for (String line : lineList) {
            index ++;
            if (StringUtils.isBlank(line)) {
                log.info("skip empty line.");
                continue;
            }

            try {
                BEApi.ResponseValue responseValue = new Gson().fromJson(line, BEApi.ResponseValue.class);

                int cityId = 0;
                String location = responseValue.getSearch_engine();
                if(domainId == 12709 && location.equalsIgnoreCase("Google Pennsylvania, United States (D)")){
                    cityId = 301746;
                }else if (domainId == 12709 && !location.equalsIgnoreCase("Google Pennsylvania, United States (D)")){
                    log.info("======not find cityId:" + location + ",lineNo:" + index);
                    continue;
                }

                if (StringUtils.isBlank(responseValue.getKeyword())) {
                    System.out.println("=Skip empty keywordLine:" + index + ",line:" + line);
                    continue;
                }

                String keywordName = responseValue.getKeyword().trim().toLowerCase();

                if(!managedKwList.contains(keywordName)){
                    log.info("====not managedKw,Skip:" + keywordName + ",line:" + line);
                    continue;
                }

                String url = responseValue.getPage_url();
                String trueRank = responseValue.getBlended_rank() == null ? null : responseValue.getBlended_rank().toString();
                String webRank = responseValue.getRank() == null ? null : responseValue.getRank().toString();
                Integer time = responseValue.getTime();

                String sv = "0";
                String cpc = "0";
                KeywordAdwordsEntity entity =
                        keywordAdwordsEntityDAO.getGeoKwSv(URLEncoder.encode(keywordName, "UTF-8"), 99, language, String.valueOf(cityId));
                if(entity != null){
                    sv = String.valueOf(entity.getAvgMonthlySearchVolume());
                    cpc = String.valueOf(entity.getCostPerClick());
                }

                int year = Integer.parseInt(time.toString().substring(0,4));
                int week = Integer.parseInt(time.toString().substring(4,6));
//                log.info("==year:" + year + ",week:" + week);
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.WEEK_OF_YEAR, week + 1); // 设置周数
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY); // 设置要获取的日期是周一
                String date = FormatUtils.formatDate(calendar.getTime(), FormatUtils.DATE_PATTERN_2);
//                System.out.println(date);

//                    System.out.println(" date : " + date);
                List<String> columns = new ArrayList<String>();
                columns.add(keywordName);
                columns.add(url);
                columns.add(date);
                columns.add(trueRank);
                columns.add(webRank);
                columns.add(sv);
                columns.add(cpc);
                if(domainId == 12709){
                    columns.add(String.valueOf(cityId));
                }

                KeywordRankVO[] vos = parsertoEntity(columns, domainId);
                if (vos != null && vos.length > 0) {
                    for (KeywordRankVO vo : vos) {
                        if (dateMap.containsKey(date)) {
                            dateMap.get(date).add(vo);
                        } else {
                            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                            voList.add(vo);
                            dateMap.put(date, voList);
                        }
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }

    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(0)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();
            String url = getUrl(StringUtils.trim(column.get(1)));
            String rankingDate = (column.get(2));

            Integer trueRank = checkRank(column.get(3));
            Integer webRank = checkRank(column.get(4));

            if(trueRank < 101 && webRank >= 101){
                webRank = trueRank;
            }else if(trueRank >= 101 && webRank < 101){
                trueRank = webRank;
            }

            String sv = StringUtils.isBlank(column.get(5)) ? "0" : column.get(5);
            String cpc = StringUtils.isBlank(column.get(6)) ? "0" : column.get(6);

            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc(cpc);

            if(processDomainId == 12709){
                vo1.setCityId(Integer.parseInt(column.get(7)));
            }

            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 101 && StringUtils.isNotBlank(url)) {
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                if (rankingVo != null) {
                    list.add(rankingVo);
                } else {
                    System.out.println("=Skip wrong ranking url, kw:" + kw + ", trueRank:" + trueRank + ", line:[" + JSON.toJSONString(column) + "]");
                }
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private static Integer checkRank(String str) {
        if (StringUtils.isBlank(str) || StringUtils.containsIgnoreCase(str, "Not Ranked")) {
            str = "101";
        }
        String blendedRank = StringUtils.trim(str);
        Integer trueRank = Integer.valueOf(blendedRank);
        trueRank = (trueRank <= 0 || trueRank > 101) ? 101 : trueRank;

        return trueRank;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    private static String getUrl(String str) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url)) {
            return "";
        }

        return url;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

}
