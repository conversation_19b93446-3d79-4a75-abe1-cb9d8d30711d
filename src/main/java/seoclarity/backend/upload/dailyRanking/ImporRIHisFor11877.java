package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-06-28
 * @path DSW 11877 | Historical Ranking Data Upload
 * https://www.wrike.com/open.htm?id=1149447805
 */
public class ImporRIHisFor11877 extends ImportHistoryRankingForDailyRanking {

    private static int domainId = 11877;
    private static int country ;
    public static final Map<Integer, String> COUNTRY_MAP = new HashMap();
    public static final Map<Integer, String> ENGINE_LANGUAGE = new HashMap();
//    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyy/MM/dd");
    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("dd-MMM-yyyy");

    private OwnDomainEntityDAO ownDomainEntityDAO;
   public ImporRIHisFor11877(){
       ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
   }

    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test11877_local_";
        prefix_ins_table = "test11877_local_";
        ImporRIHisFor11877 ins = new ImporRIHisFor11877();
        String filePath = "files/11877Files/";
        String folder = "";

        if (args != null & args.length > 0) {
            folder = args[0];
            filePath = filePath + folder + "/";
        } else {

        }

        try {
            System.out.println("=========================Process desktop===================================");
            for (File file : new File(filePath).listFiles()) {

                if (file.getName().endsWith(".xlsx")) {
                    System.out.println("====================== gogogo ======================");
                    System.out.println("file name : " + file.getName());
                    ins.process(file.getAbsolutePath());
//                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) {

        OwnDomainEntity domainENtity = ownDomainEntityDAO.getById(domainId);
        searchEngine = ScKeywordRankManager.getSearchEngineId(domainENtity);
        language = ScKeywordRankManager.getSearchLanguageId(domainENtity);
        System.out.println(" engine :" + searchEngine + "language : " + language);
        System.out.println("************************** desktop *****************************");
        dataMapForDesktop(file);
//        System.out.println("************************** mobile *****************************");
//        dataMapForMobile(file);
    }

    private void dataMapForMobile(String file) {
        Map<String, List<KeywordRankVO>> dateMap = null;
        try {
            dateMap = parseExcelm(file, domainId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + searchEngine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + domainId + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, true);
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void dataMapForDesktop(String file) {
        Map<String, List<KeywordRankVO>> dateMap = null;
        try {
            dateMap = parseExceld(file, domainId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + searchEngine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + domainId + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, false);
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, List<KeywordRankVO>> parseExceld(String file, int oid) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        System.out.println(" start : " + start + " end : " + end);
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();


        int lineCnt = 0;
        for (int n = start; n <= end; n++) {
            lineCnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }

            try {
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[4], "Keyword") && StringUtils.equalsIgnoreCase(cols[3], "Date")) {
                    System.out.println(" line :" + lineCnt + " head ");
                } else if (cols[2].equalsIgnoreCase("Desktop")){

                    String kwStr = cols[4];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[7];
                    if (!url.startsWith("https://")) {
                        url = "";
                    }
//                    url = getUrl(url, domain);
                    String rank = cols[6].contains("o") ? "999" : cols[6].substring(0,cols[6].indexOf("."));
                    String sv = cols[5].substring(0,cols[5].indexOf("."));
                    String cpc = "0";
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String date = cols[3];
                    Calendar calendar1 = Calendar.getInstance();
                    calendar1.setTime(yyyyMMdd.parse(date));
                    date = yyyy_MM_dd.format(calendar1.getTime());
                    System.out.println(date);
//                    System.out.println(" date : " + date);
                    List<String> columns = new ArrayList<String>();
                    columns.add(cols[1]);
                    columns.add(kwStr);
                    columns.add(date);
                    columns.add(rank);
                    columns.add(url);
                    columns.add(sv);
                    columns.add(cpc);
                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }


    private Map<String, List<KeywordRankVO>> parseExcelm(String file, int oid) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        System.out.println(" start : " + start + " end : " + end);
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();

        for (int n = start; n <= end; n++) {

            try {
                XSSFRow row = sheet.getRow(n);
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[4], "Keyword") && StringUtils.equalsIgnoreCase(cols[3], "Date")) {
                    System.out.println(" line :"  + " head ");
                } else if (cols[2].equals("Mobile")){

                    String kwStr = cols[4];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[7];
                    if (!url.startsWith("https://")) {
                        url = "";
                    }
//                    url = getUrl(url, domain);
                    String rank = cols[6].contains("o") ? "999" : cols[6].substring(0,cols[6].indexOf("."));
                    String sv = cols[5].substring(0,cols[5].indexOf("."));
                    String cpc = "0";
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String date = cols[3];
                    Calendar calendar1 = Calendar.getInstance();
                    calendar1.setTime(yyyyMMdd.parse(date));
                    date = yyyy_MM_dd.format(calendar1.getTime());
                    System.out.println(date);
                    List<String> columns = new ArrayList<String>();
                    columns.add(cols[1]);
                    columns.add(kwStr);
                    columns.add(date);
                    columns.add(rank);
                    columns.add(url);
                    columns.add(sv);
                    columns.add(cpc);
                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return dateMap;
    }



    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(1)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

//            Integer trueRank = Integer.parseInt(StringUtils.trim(column.get(3)));
//            Integer webRank = Integer.parseInt(StringUtils.trim(column.get(3)));
            Double trueRankd = Double.valueOf(column.get(3));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(3));
            Integer webRank = (int) Math.ceil(webRankd);

            String url = column.get(4);
            String rankingDate = (column.get(2));
            if (StringUtils.isBlank(url)) {
                trueRank = 999;
                webRank = 999;
            }

            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(column.get(5));
            vo1.setCpc(column.get(6));
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                if (rankingVo != null) {
                    list.add(rankingVo);
                } else {
                    System.out.println("=Skip wrong ranking url, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                }
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        if (StringUtils.isBlank(url) || trueRank == 101) {
            return null;
        }
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);

        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
            return "";
        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
        } else if (url.length() <= ownDomainName.length()) {
            throw new Exception("Wrong url, url:" + url);
        }
        return url;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

}
