package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-07-10
 * @path pens.com | 2023 Historical ranking data import
 * https://www.wrike.com/open.htm?id=1384588455
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.dailyRanking.ImporRIHisForPensV3" -Dexec.args="f m 1275" -Dexec.cleanupDaemonThreads=false >> ImporRIHisForPensV3.log&* tail -f 12815d.log
 */
public class ImporRIHisForPensV3 extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private static final String https = "https://";
    private static String device = "d";
    private static int oid = 12753;
    private static boolean testFlg = true;
    private static String folder = "1275";
    private Set<String> kwSet = new HashSet<>();
    private Map<String, String> kwMap = new HashMap<>();
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;

    public ImporRIHisForPensV3() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
    }


    public static void main(String[] args) {

        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test12753_local_";
        prefix_ins_table = "test12753_local_";

        String filePath = "files/12759Files/240314Files";
        filePath = "files/12759Files/240710";
        ImporRIHisForPensV3 ins = new ImporRIHisForPensV3();

        if (null != args && args.length > 0) {
            if (args[0].equals("f")) {
                testFlg = false;
                device = args[1];
                folder = args[2];
            }

        }

        try {
            for (File file : new File(filePath + "/" + folder).listFiles()) {


                if (file.getName().contains(".csv")) {
                    System.out.println("====================== gogogo ======================");
                    System.out.println("file name : " + file.getName());
                    ins.process(file.getAbsolutePath(), file.getName(), device);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file, String fileName, String device) throws Exception {

        oid = Integer.parseInt(fileName.substring(0, fileName.indexOf(".")));
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        searchEngine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        language = ScKeywordRankManager.getSearchLanguageId(domainEntity);

        System.out.println("===###RunFile: " + fileName + " , device " + device + " ,oid :  " + oid);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, oid, device);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + searchEngine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + oid + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    if (!testFlg) {
                        setParameters(workDate, searchEngine, language, device.equals("mobile"));
                        loadData(processList);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel(String file, int oid, String device) throws Exception {
        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
        List<String> columnList = new ArrayList<>();
        CSVParser parser = new CSVParser(',');
        int lineCnt = 0;
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        for (String line : lines) {
            lineCnt++;
            if (StringUtils.isBlank(line)) {
                continue;
            }
            if (lineCnt < 7) {
                continue;
            }

            try {

                String[] cols = parser.parseLine(line);

                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword") && StringUtils.equalsIgnoreCase(cols[1], "Tags")) {
                    System.out.println(" 读取到第一行，啥也不干");
                    columnList = Arrays.asList(cols);
                } else {
                    List<List<String>> columnsList = new ArrayList<>();

                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();

                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }
                    String sv = "0";
                    String cpc = "0";
                    for (int k = 0; k < columnList.size(); k++) {
                        if (columnList.get(k).contains("Search Volume")) {
                            sv = cols[k];
                            if (sv.contains("n") || StringUtils.isBlank(sv) || sv.contains("-")) {
                                sv = "0";
                            }
                            continue;
                        }
                        if (columnList.get(k).contains("CPC")) {
                            cpc = cols[k];
                            if (cpc.contains("n") || StringUtils.isBlank(cpc) || cpc.contains("-")) {
                                cpc = "0";
                            }
                            continue;
                        }
                    }


                    for (int j = 2; j < cols.length - 1; j += 3) {

                        String date = columnList.get(j);
                        if (date.contains("difference")) {
                            // 结束 .
                            break;
                        }

                        date = date.substring(date.indexOf("_") + 1, date.length()); // yyyymmdd
                        String rank = cols[j];
                        String url = cols[j + 2];
                        List<String> datesOfWeekStr = getWeekDatesAsString(date);
                        for (String today : datesOfWeekStr) {
                            mergeData(url, rank, today, kwStr, sv, cpc, columnsList);
                        }
                    }

                    for (List<String> columns : columnsList) {
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);

                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                String date = vo.getQueryDate();
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }

    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Integer trueRank = StringUtils.isBlank(column.get(6)) ? 999 : Integer.parseInt(StringUtils.trim(column.get(6)));
            Integer webRank = StringUtils.isBlank(column.get(7)) ? 999 : Integer.parseInt(StringUtils.trim(column.get(7)));

            String url = column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc(column.get(9));
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
            return "";
        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
        } else if (url.length() <= ownDomainName.length()) {
            throw new Exception("Wrong url, url:" + url);
        }
        return url;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

    public static List<String> getWeekDatesAsString(String inputDateString) {

        DateTimeFormatter formatter_yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 将输入的日期字符串转换为LocalDate对象
        LocalDate inputDate = LocalDate.parse(inputDateString, formatter_yyyyMMdd);

        List<String> weekDatesStr = new ArrayList<>();

        // 调整到周日开始（如果需要从周一开始，可省略这步）
        inputDate = inputDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 遍历一周内的每一天，从周日到下周六，并转换为字符串
        for (int i = 0; i < 7; i++) {
            LocalDate date = inputDate.plusDays(i);
            weekDatesStr.add(date.format(formatter));
        }

        return weekDatesStr;
    }

    private void mergeData(String url, String rank, String date, String kwStr, String sv, String cpc, List<List<String>> columnsList) throws ParseException {
        String location = "US-en";
        System.out.println("kwStr: " + kwStr + ", date: " + date + ", url: " + url + ", rank: " + rank + " , sv : " + sv);

        if (StringUtils.isBlank(url) || !url.startsWith("https")) {
            url = "";
        }

        if (null == sv || StringUtils.isBlank(sv)) {
            sv = "0";
        }

        if (StringUtils.isBlank(rank) || rank.contains("-") || rank.equals("") || rank.equals("0") || rank.contains("r")) {
            rank = "999";
        }

        List<String> col0 = new ArrayList<>();
        col0.add(location);
        col0.add(String.valueOf(searchEngine));
        col0.add(device);
        col0.add(date);
        col0.add(kwStr);
        col0.add(sv);
        col0.add(rank);
        col0.add(rank);
        col0.add(url);
        col0.add(cpc);
        columnsList.add(col0);

    }

}
