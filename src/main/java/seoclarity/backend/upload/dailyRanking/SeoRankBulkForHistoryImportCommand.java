package seoclarity.backend.upload.dailyRanking;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.google.common.net.InternetDomainName;

import ru.yandex.clickhouse.ClickHouseArray;
import ru.yandex.clickhouse.domain.ClickHouseDataType;
import seoclarity.backend.dao.actonia.RankingDailyMonitorEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.utils.ClarityDBConnection;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public class SeoRankBulkForHistoryImportCommand extends BaseThreadCommand {
	private final String INSERT_KWD_INFO_SQL = "insert into seo_daily_ranking.{0} (keyword_name ,own_domain_id ,keyword_rankcheck_id,engine_id, language_id ,location_id,questions ,answerbox_url ,ll_flg, pla_flg ,ppc_flg ,top_ppc_cnt ,ranking_date , avg_search_volume , cpc ,knog_flg ,count_of_search ,sign, attrstr.key,  attrstr.value, attrint.key, attrint.value) values (?, ?, ?, ?, ?,? ,? , ? ,? ,? ,? ,? ,? ,? ,? ,?, ?, ?, ?, ?, ?, ?)";
	private final String INSERT_KWD_DETAIL_SQL = "insert into seo_daily_ranking.{0} (keyword_name ,own_domain_id ,keyword_rankcheck_id,engine_id, language_id ,location_id,domain_reverse ,root_domain_reverse  ,uri ,url, protocol ,true_rank ,web_rank ,type ,avg_search_volume ,cpc ,hrd ,hrrd ,ranking_date ,rating ,wtd_vol_tr ,est_traffic_tr ,wtd_vol_wr ,est_traffic_wr, folder_level1, folder_level2, folder_level3,sign , attrs.key,attrs.value, label ,meta , lable_len ,meta_len,frequently_occurring_domain,frequently_occurring_domain_count ) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

	private String ipAddress;

	private KeywordRankVO[] rankData;

	private int searchEngine;

	private int language;

	private boolean mobileRanking;

	private boolean hotCluster = false;

	private boolean isUseMonitorTable;

	// default value
	private String prefix_ins_table = "local_test_his_";
//	private static final String RI_OLD_HOT_DB_OUTTER_IP = "***********:8123";
//	private static final String RI_HOT_DB_OUTTER_IP = "***********:8123";

	private static final String RI_OLD_HOT_DB_OUTTER_IP = "***************:8123";  //cdb-ri-nj-101 ***********  ===>cdb-ri-nj-114 ***************
	private static final String RI_HOT_DB_OUTTER_IP = "************:8123"; //cdb-ri-301  ***********   	 ===>cdb-ri-307   ************

	private RankingDailyMonitorEntityDAO rankingDailyMonitorEntityDAO;
	private ClDailyRankingEntityDao clDailyRankingEntityDao;

	private Connection connection;
	private Connection connectionBackup;

	private boolean checkKeywordExistsInMonitorWithSendToQDate = false;
	private static final boolean IS_UPLOAD_BACKIP_DB =true;
	// https://www.wrike.com/open.htm?id=370685433
	private static final List<String> NEED_TITLE_DOMAIN_LIST = Arrays.asList(new String[] {
	});

	List<String> attrsGroup = new ArrayList<String>();

	public static final String MONITOR_TABLE_PREFIX = "test_his_daily_ranking_imp_monitor_";
	private static String specified_monitor_table_refix = null;
	public static final String MONITOR_TABLE_HOT = "hot_";
	public static final String MONITOR_TABLE_COLD = "col_";
	public static final int TYPE_REGULAR = 1;
	public static final int TYPE_MOBILE = 2;

	private static final int SEARCH_ENGINE_BING = 255;
	private static final int SEARCH_ENGINE_YAHOO = 100;
	private static final int SEARCH_ENGINE_BAIDU = 150;
	private static final int SEARCH_ENGINE_YANDEX = 120;
	private static final int SEARCH_ENGINE_NAVER = 160;
	private static final int SEARCH_ENGINE_SO = 170;
	private static Integer[] not_google_engine = new Integer[] {
			SEARCH_ENGINE_BING,
			SEARCH_ENGINE_YAHOO,
			SEARCH_ENGINE_BAIDU,
			SEARCH_ENGINE_YANDEX,
			SEARCH_ENGINE_NAVER,
			SEARCH_ENGINE_SO
	};

	public void setMonitorTablePrefix(String monitorTablePrefix) {
		specified_monitor_table_refix = monitorTablePrefix;
	}

	public SeoRankBulkForHistoryImportCommand(String ipAddress, KeywordRankVO[] rankData, int searchEngine, int language,
											  boolean mobileRanking, boolean isUseMonitorTable, String prefix_ins_table) {
		super();

		this.ipAddress = ipAddress;
		this.rankData = rankData;
		this.searchEngine = searchEngine;
		this.language = language;
		this.mobileRanking = mobileRanking;
		this.isUseMonitorTable = isUseMonitorTable;
		if (StringUtils.isNotBlank(prefix_ins_table)) {
			this.prefix_ins_table = prefix_ins_table;
		}
		rankingDailyMonitorEntityDAO = SpringBeanFactory.getBean("rankingDailyMonitorEntityDAO");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");

		attrsGroup.add("answerbox_flg");
		attrsGroup.add("ll_flg");
		attrsGroup.add("pla_flg");
		attrsGroup.add("ppc_flg");
		attrsGroup.add("peoplealsoask_flg");
		attrsGroup.add("app_flg");
		attrsGroup.add("apm_flg");
		attrsGroup.add("img_flg");
		attrsGroup.add("news_flg");
		attrsGroup.add("video_flg");
		attrsGroup.add("is_apm");
		attrsGroup.add("price_flg");
		attrsGroup.add("stock_flg");
		attrsGroup.add("is_price");
		attrsGroup.add("is_stock");
		attrsGroup.add("ratingnumber");
		attrsGroup.add("job_link");
		attrsGroup.add("apple_category");
		attrsGroup.add("knog_flg");
		attrsGroup.add("flight_search_flg");
		attrsGroup.add("social_in_kg");
		attrsGroup.add("qa_flg");
		attrsGroup.add("is_qa");
	}

	private String getMonitorTableName() {
		String date = DateFormatUtils.format(CacheModleFactory.getInstance().getRankingDate(), "yyyyMMdd");
		String rankingTableName = specified_monitor_table_refix + (hotCluster ? MONITOR_TABLE_HOT : MONITOR_TABLE_COLD)
				+ date;
		return rankingTableName;
	}

	// by Meo
	// use the keyword sendToQDate as the current keyword ranking date
	private boolean isKeywordExistsInMonitorBySendToQDate(KeywordRankVO keywordRankVO, int ownDomainId) {
		int type = mobileRanking ? TYPE_MOBILE : TYPE_REGULAR;
		String rankingTableName = null;
		try {
			if (keywordRankVO == null || keywordRankVO.getSendToQDate() == null || keywordRankVO.getSendToQDate() <= 0) {
				System.out.println("Wrong!!! Wrong sendToQDate. kName: " + keywordRankVO.getKeyword() + " , engine: "
						+ searchEngine + " , language: " + language  + ", mobileRanking:" + mobileRanking + ", type:" + type
						+ ", ownDomainId:" + ownDomainId + ", sendToDate:" + keywordRankVO.getSendToQDate());
				return true;
			}
			Integer sendToDate = keywordRankVO.getSendToQDate();
			String sendToQDate2 =  DateFormatUtils.format(keywordRankVO.getSendToQDateAsDate(), "yyyyMMdd");

			if (!StringUtils.equals(sendToQDate2, String.valueOf(sendToDate))) {
				System.out.println("Wrong!!! Wrong sendToQDate. kName: " + keywordRankVO.getKeyword() + " , engine: "
						+ searchEngine + " , language: " + language  + ", mobileRanking:" + mobileRanking + ", type:" + type
						+ ", ownDomainId:" + ownDomainId + ", sendToDate:" + sendToDate);
				return  true;
			}

			rankingTableName = specified_monitor_table_refix + (hotCluster ? MONITOR_TABLE_HOT : MONITOR_TABLE_COLD)
					+ sendToDate.toString();
		} catch (Exception e) {
			System.out.println("Wrong!!! Check Kw Exists Failed. kName: " + keywordRankVO.getKeyword() + " , engine: "
					+ searchEngine + " , language: " + language  + ", mobileRanking:" + mobileRanking + ", type:" + type
					+ ", ownDomainId:" + ownDomainId + ", sendToDate:" + keywordRankVO.getSendToQDate());
			e.printStackTrace();
			return true;
		}
		return isKeywordExistsInMonitor(keywordRankVO, rankingTableName, ownDomainId);
	}

	// by Meo
	private boolean isKeywordExistsInMonitor(KeywordRankVO keywordRankVO, String tableName, int ownDomainId) {
		try {
			Integer kid = keywordRankVO.getId();
			if (kid == null){
				return false;
			}
			int type = mobileRanking ? TYPE_MOBILE : TYPE_REGULAR;
			// check exists
			int cityId = keywordRankVO.getCityId() == null ? 0 : keywordRankVO.getCityId();
			int frequency = 1;
			boolean isExists = rankingDailyMonitorEntityDAO.keywordExists(tableName, kid, searchEngine, language, cityId, frequency, type, ownDomainId);
			if (isExists) {
				System.out.println("== existsing alert, kid:" + keywordRankVO.getId() + ", kName: " + keywordRankVO.getKeyword() + " , engine: "
						+ searchEngine + " , language: " + language + ", mobileRanking:" + mobileRanking + ", type:" + type + " ,ownDomainId: " + ownDomainId
						+ (checkKeywordExistsInMonitorWithSendToQDate ? (", sendToQueueDate:" + keywordRankVO.getSendToQDate()) : ""));
			} else {
				try {
					rankingDailyMonitorEntityDAO.insert(tableName, kid, searchEngine, language, cityId, frequency, type, ownDomainId);
				} catch (Exception e) {
					System.out.println(tableName+"== EXCEPTION kName: " + keywordRankVO.getKeyword() + " , engine: "
							+ searchEngine + " , language: " + language  + ", mobileRanking:" + mobileRanking + ", type:" + type+ ", ownDomainId:" + ownDomainId);
					e.printStackTrace();
					return true;
				}
			}
			return isExists;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	private void checkKeywordExistsInMonitor(List<KeywordRankVO> voList , String tableName, int ownDomainId) {
		int cityId = 0;
		int frequency = 1;
		int type = mobileRanking ? TYPE_MOBILE : TYPE_REGULAR;
		int total = voList.size();
		Map<Integer, KeywordRankVO> map = new LinkedHashMap<Integer, KeywordRankVO>();
		voList.stream().forEach((x) -> map.put(x.getId(), x));
		List<Integer> existsKidList = rankingDailyMonitorEntityDAO.getExistsByKid(tableName, map.keySet(), searchEngine, language, cityId, type, ownDomainId);
		if (existsKidList != null && existsKidList.size() > 0) {
			voList.clear();
			voList.addAll(map.values());
			System.out.println("== existsing alert, ownDomainId:" + ownDomainId + ", searchEngine:" + searchEngine + ", language:" + language
					+ ", cityId:" + cityId + ", frequency:" + frequency + ", tableName:" + tableName + ", total:" + total + ", exists:" + existsKidList.size() + ", process:" + voList.size() + ", existsKidList:" + existsKidList);
			map.keySet().stream().filter((kid) -> (existsKidList.contains(kid.intValue()))).forEach((kid) -> {
				map.remove(kid.intValue());
			});
		}
		if (map.size() > 0) {
			rankingDailyMonitorEntityDAO.saveBatch(tableName, map.keySet(), searchEngine, language, cityId, frequency, type, ownDomainId);
		}
	}

	private void checkKeywordExistsInRI(List<KeywordRankVO> voList , int ownDomainId) {
		int cityId = 0;
		int frequency = 1;
		int total = voList.size();
		String dateStr = DateFormatUtils.format(CacheModleFactory.getInstance().getRankingDate(), "yyyy-MM-dd");
		String infoTable = getKeyowrdInfoHistoricalTableName(searchEngine, language);

		Map<Integer, KeywordRankVO> map = new LinkedHashMap<Integer, KeywordRankVO>();
		voList.stream().forEach((x) -> map.put(x.getId(), x));
		List<Map<String, Object>> existsList = clDailyRankingEntityDao.checkExistsFromForKidList(infoTable, ownDomainId, searchEngine, language, cityId, dateStr, map.keySet());
		List<Integer> existsKidList = new ArrayList<>();
		if (existsList != null && existsList.size() > 0) {
			existsList.stream().forEach((obj) -> existsKidList.add(Integer.valueOf(obj.get("keyword_rankcheck_id").toString())));
		}
		if (existsKidList != null && existsKidList.size() > 0) {
			existsKidList.stream().forEach((kid) -> map.remove(kid.intValue()));
			voList.clear();
			voList.addAll(map.values());
			System.out.println("== existsing alert, ownDomainId:" + ownDomainId + ", searchEngine:" + searchEngine + ", language:" + language
					+ ", cityId:" + cityId + ", frequency:" + frequency + ", dateStr:" + dateStr + ", total:" + total + ", exists:" + existsKidList.size() + ", process:" + voList.size() + ", existsKidList:" + existsKidList);
		}
	}

	@Override
	protected void execute() throws Exception {
		long a = System.currentTimeMillis();
		if (specified_monitor_table_refix == null) {
			specified_monitor_table_refix = MONITOR_TABLE_PREFIX;
		}
		insertData();
		long b = System.currentTimeMillis();
		System.out.println("End command IP: " + ipAddress + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
		CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
	}

	private void insertData() throws Exception {
		// ==================================================
		connection = ClarityDBConnection.getDBConnection(RI_HOT_DB_OUTTER_IP);
		if (hotCluster) {
			connection = null;
		}
		PreparedStatement keywordLevelStatement = null;
		PreparedStatement keywordDetailStatement = null;
		// ==================================================
		if (IS_UPLOAD_BACKIP_DB) {
			try {
				connectionBackup = ClarityDBConnection.getDBConnection(RI_OLD_HOT_DB_OUTTER_IP);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		PreparedStatement keywordLevelStatementBack = null;
		PreparedStatement keywordDetailStatementBack = null;

		try {
			keywordLevelStatement = connection.prepareStatement(
					StringUtils.replace(INSERT_KWD_INFO_SQL, "{0}", getKeyowrdInfoHistoricalTableName(searchEngine, language)));
			keywordDetailStatement = connection.prepareStatement(StringUtils.replace(INSERT_KWD_DETAIL_SQL, "{0}",
					getKeyowrdDetailHistoricalTableName(searchEngine, language)));

			// ===================================
			if (IS_UPLOAD_BACKIP_DB && connectionBackup != null) {
				keywordLevelStatementBack = connectionBackup.prepareStatement(
						StringUtils.replace(INSERT_KWD_INFO_SQL, "{0}", getKeyowrdInfoHistoricalTableName(searchEngine, language)));
				keywordDetailStatementBack = connectionBackup.prepareStatement(StringUtils.replace(INSERT_KWD_DETAIL_SQL, "{0}",
						getKeyowrdDetailHistoricalTableName(searchEngine, language)));
			}

			String monitorTableName = getMonitorTableName();

			List<KeywordRankVO> rankDataList = new ArrayList<>();
			rankDataList.addAll(Arrays.asList(rankData));

			if (isUseMonitorTable && !checkKeywordExistsInMonitorWithSendToQDate) {
//				checkKeywordExistsInMonitor(rankDataList, monitorTableName, Integer.valueOf(rankDataList.get(0).getDomainList().get(0)));
				checkKeywordExistsInRI(rankDataList, Integer.valueOf(rankDataList.get(0).getDomainList().get(0)));
			}

			for (KeywordRankVO keywordRankVO : rankDataList) {
				parerLineForChild(keywordRankVO, 	keywordLevelStatement, keywordDetailStatement,
						monitorTableName, keywordLevelStatementBack, keywordDetailStatementBack);
			}

			keywordLevelStatement.executeBatch();
			keywordDetailStatement.executeBatch();
			// =================================
			if (IS_UPLOAD_BACKIP_DB && connectionBackup != null) {
				try {
					keywordLevelStatementBack.executeBatch();
					keywordDetailStatementBack.executeBatch();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		} catch (SQLException e1) {
			e1.printStackTrace();
		} finally {
			if (keywordLevelStatement != null) {
				keywordLevelStatement.close();
			}
			if (keywordDetailStatement != null) {
				keywordDetailStatement.close();
			}
			// =====================
			if (keywordLevelStatementBack != null) {
				keywordLevelStatementBack.close();
			}
			if (keywordDetailStatementBack != null) {
				keywordDetailStatementBack.close();
			}
			if (connection != null) {
				ClarityDBConnection.closeConnection(connection);
			}
			if (connectionBackup != null) {
				ClarityDBConnection.closeConnection(connectionBackup);
			}
		}
	}

	private void parerLineForChild(KeywordRankVO keywordRankVO,
								   PreparedStatement keywordLevelStatement, PreparedStatement keywordDetailStatement, String monitorTableName,
								   PreparedStatement keywordLevelStatementBack, PreparedStatement keywordDetailStatementBack) {
		int kId = 0;
		if (keywordRankVO.getId() == null) {
			// do nothing
		} else {
			kId = keywordRankVO.getId();
		}

		if (kId == 0) {
			System.out.println("@@@kId can't be null, engine: " + searchEngine + " ,language: " + language);
			return;
		}
		if (keywordRankVO.getDomainList() == null || keywordRankVO.getDomainList().size() == 0) {
			System.out.println("@@@ownDomainId can't be null, engine: " + searchEngine + " ,language: " + language);
			return;
		}


		boolean hasPeopleAlsoAsk = false;
		boolean hasAnswerBox = false;
		boolean hasLL = false;
		boolean hasPla = false;
		boolean hasPpc = false;
		boolean hasApp = false;
		boolean hasAmp = false;
		boolean hasImg = false;
		boolean hasNews = false;
		boolean hasVideo = false;
		boolean hasPrice = false;
		boolean hasStock = false;
		boolean hasQaList = false;

		String googleRecommendStr = StringUtils.isBlank(keywordRankVO.getGoogleRecommend()) ? "-" : keywordRankVO.getGoogleRecommend();

		if(StringUtils.isNotBlank(keywordRankVO.getQuestions())) {
			hasPeopleAlsoAsk = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getAnswerBox())) {
			hasAnswerBox = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getLlFlg()) && StringUtils.equalsIgnoreCase(keywordRankVO.getLlFlg(), "y")) {
			hasLL = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getPlaFlg()) && StringUtils.equalsIgnoreCase(keywordRankVO.getPlaFlg(), "y")) {
			hasPla = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getPpcFlg()) && StringUtils.equalsIgnoreCase(keywordRankVO.getPpcFlg(), "y")) {
			hasPpc = true;
		}
		if(StringUtils.isNotBlank(keywordRankVO.getAppFlg()) && StringUtils.equalsIgnoreCase(keywordRankVO.getAppFlg(), "y")) {
			hasApp = true;
		}

		if (keywordRankVO.getDomainList() != null && keywordRankVO.getDomainList().size() > 0) {
			for (String domainId : keywordRankVO.getDomainList()) {
				if (isUseMonitorTable) {
					if (!checkKeywordExistsInMonitorWithSendToQDate) {
//						if (isKeywordExistsInMonitor(keywordRankVO, monitorTableName, NumberUtils.toInt(domainId))) {
//							continue;
//						}
					} else {
						if (isKeywordExistsInMonitorBySendToQDate(keywordRankVO, NumberUtils.toInt(domainId))) {
							continue;
						}
					}
				}

				// ==============================================================================
				processParse(keywordRankVO, keywordLevelStatement, keywordDetailStatement, domainId, kId,
						googleRecommendStr, hasPeopleAlsoAsk, hasAnswerBox, hasLL, hasPla, hasPpc, hasApp, hasAmp, hasImg, hasNews, hasVideo, hasPrice, hasStock, hasQaList);
				if (IS_UPLOAD_BACKIP_DB) {
					processParse(keywordRankVO, keywordLevelStatementBack, keywordDetailStatementBack, domainId, kId,
							googleRecommendStr, hasPeopleAlsoAsk, hasAnswerBox, hasLL, hasPla, hasPpc, hasApp, hasAmp, hasImg, hasNews, hasVideo, hasPrice, hasStock, hasQaList);
				}
				// ==============================================================================
			}
		}
	}

	private void processParse(KeywordRankVO keywordRankVO, PreparedStatement keywordLevelStatement,
							  PreparedStatement keywordDetailStatement, String domainId, int kId,
							  String googleRecommendStr, boolean hasPeopleAlsoAsk,
							  boolean hasAnswerBox, boolean hasLL, boolean hasPla, boolean hasPpc, boolean hasApp, boolean hasAmp, boolean hasImg,
							  boolean hasNews, boolean hasVideo, boolean hasPrice, boolean hasStock, boolean hasQaList) {
		try {
			// set the universal type flag first
			for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
				if(keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_IMGAGE) {
					hasImg = true;
				}
				if(keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_NEWS) {
					hasNews = true;
				}
				if(keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_VIDEO) {
					hasVideo = true;
				}
				if(StringUtils.isNotBlank(keywordRankEntityVO.getAmpFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getAmpFlg(), "y")) {
					hasAmp = true;
				}
				if (StringUtils.isNotBlank(keywordRankEntityVO.getStockFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getStockFlg(),"y")) {
					hasStock = true;
				}
				if (StringUtils.isNotBlank(keywordRankEntityVO.getPriceFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getPriceFlg(),"y")) {
					hasPrice = true;
				}
				if (StringUtils.isNotBlank(keywordRankEntityVO.getQuestionlist()) && !StringUtils.equalsIgnoreCase(keywordRankEntityVO.getQuestionlist(), "!_!")) {
					hasQaList = true;
				}
			}

			int index = 1;
			keywordLevelStatement.setString(index++, StringUtils.lowerCase(keywordRankVO.getKeywordForHtml()));
			keywordLevelStatement.setInt(index++, NumberUtils.toInt(domainId));
			keywordLevelStatement.setInt(index++, kId);
			keywordLevelStatement.setInt(index++, searchEngine);
			keywordLevelStatement.setInt(index++, language);
			keywordLevelStatement.setInt(index++, keywordRankVO.getCityId());
			keywordLevelStatement.setString(index++, keywordRankVO.getQuestions());
			keywordLevelStatement.setString(index++, keywordRankVO.getAnswerBox());
			keywordLevelStatement.setInt(index++, convertValue(keywordRankVO.getLlFlg()));
			keywordLevelStatement.setInt(index++, convertValue(keywordRankVO.getPlaFlg()));
			keywordLevelStatement.setInt(index++, convertValue(keywordRankVO.getPpcFlg()));
			keywordLevelStatement.setInt(index++, keywordRankVO.getTopPPCCnt());
			if (!checkKeywordExistsInMonitorWithSendToQDate) {
				keywordLevelStatement.setDate(index++,
						new Date(CacheModleFactory.getInstance().getRankingDate().getTime()));
			} else {
				keywordLevelStatement.setDate(index++, new Date(keywordRankVO.getSendToQDateAsDate().getTime()));
			}
			keywordLevelStatement.setLong(index++, Long.parseLong(keywordRankVO.getSearchVol()));
			keywordLevelStatement.setFloat(index++, Float.parseFloat(keywordRankVO.getCpc()));
			keywordLevelStatement.setInt(index++, convertValue(keywordRankVO.getKnogTag()));
			keywordLevelStatement.setInt(index++, 0);
			keywordLevelStatement.setInt(index++, 1);

			// google recommend
			List<String> attrstrKeys = new ArrayList<String>();
			List<String> attrstrValues = new ArrayList<String>();

			List<Integer> ignoreGoogleRecommenEnginedList = Arrays.asList(not_google_engine);
			if (!ignoreGoogleRecommenEnginedList.contains(searchEngine) && !mobileRanking) {
				attrstrKeys.add("google_recommend");
				attrstrValues.add(googleRecommendStr);
			}

			// https://www.wrike.com/open.htm?id=367737008
			attrstrKeys.add("additionalQuestions");
			attrstrValues.add(StringUtils.isBlank(keywordRankVO.getAdditionalQuestions()) ? "-" : keywordRankVO.getAdditionalQuestions());

			// https://www.wrike.com/open.htm?id=367737008
			// app list
			attrstrKeys.add("appList");
			String appList = (keywordRankVO.getAppList() == null || keywordRankVO.getAppList().size() == 0) ? "-" : StringUtils.join(keywordRankVO.getAppList(), "@_@");
			attrstrValues.add(appList);

			attrstrKeys.add("job_link");
			if(StringUtils.isNotBlank(keywordRankVO.getJobLink())) {
				String[] jobSources = keywordRankVO.getJobLink().split("@_@");
				if(jobSources.length == 2 && StringUtils.isNotBlank(jobSources[1])) {
					attrstrValues.add(jobSources[1]);
				} else {
					attrstrValues.add("-");
				}
			} else {
				attrstrValues.add("-");
			}
//begin xxxxyyyy
			keywordLevelStatement.setArray(index++, new ClickHouseArray(ClickHouseDataType.String, attrstrKeys.toArray(new String[attrstrKeys.size()])));
			keywordLevelStatement.setArray(index++, new ClickHouseArray(ClickHouseDataType.String, attrstrValues.toArray(new String[attrstrValues.size()])));
//end xxxxyyyy
			List<String> attrintKeys = new ArrayList<String>();
			List<Integer> attrintValues = new ArrayList<Integer>();

			// https://www.wrike.com/open.htm?id=297297872
			attrintKeys.add("fps_count");
			// now use the real ranking count, mobile keywordRankVO.getFirstPageSize() sometimes not correct
			attrintValues.add(keywordRankVO.getKeywordRankEntityVOs() == null ? 0 : keywordRankVO.getKeywordRankEntityVOs().size());

			// https://www.wrike.com/open.htm?id=367737008
			// add universal type
			attrintKeys.add("peoplealsoask_flg");
			attrintValues.add(hasPeopleAlsoAsk ? 1 : 0);

			attrintKeys.add("app_flg");
			attrintValues.add(hasApp ? 1 : 0);

			attrintKeys.add("apm_flg");
			attrintValues.add(hasAmp ? 1 : 0);

			attrintKeys.add("img_flg");
			attrintValues.add(hasImg ? 1 : 0);

			attrintKeys.add("news_flg");
			attrintValues.add(hasNews ? 1 : 0);

			attrintKeys.add("video_flg");
			attrintValues.add(hasVideo ? 1 : 0);

			attrintKeys.add("price_flg");
			attrintValues.add(hasPrice ? 1 : 0);

			attrintKeys.add("stock_flg");
			attrintValues.add(hasStock ? 1 : 0);

			attrintKeys.add("flight_search_flg");
			attrintValues.add(convertValue(keywordRankVO.getFlightSearchFlg()));

			attrintKeys.add("social_in_kg");
			attrintValues.add(convertValue(keywordRankVO.getSocialInKg()));

			attrintKeys.add("job_link");
			try {
				if(StringUtils.isNotBlank(keywordRankVO.getJobLink())) {
					String[] jobSources = keywordRankVO.getJobLink().split("@_@");
					if(jobSources.length == 2 && StringUtils.isNotBlank(jobSources[1])) {
						attrintValues.add(1);
					} else {
						attrintValues.add(0);
					}
				} else {
					attrintValues.add(0);
				}
			} catch (Exception e) {
				e.printStackTrace();
				attrintValues.add(0);
			}

			// https://www.wrike.com/open.htm?id=399697352
			attrintKeys.add("qa_flg");
			attrintValues.add(hasQaList? 1 : 0);
//begin xxxxyyyy
			keywordLevelStatement.setArray(index++, new ClickHouseArray(ClickHouseDataType.String, attrintKeys.toArray(new String[attrintKeys.size()])));
			keywordLevelStatement.setArray(index++, new ClickHouseArray(ClickHouseDataType.Int32, attrintValues.toArray(new Integer[attrintValues.size()])));
//end xxxxyyyy
			keywordLevelStatement.addBatch();

			// ===============================================
			Set<String> uniqueDomainSet = new HashSet<String>();
			Set<String> uniqueDomainIncludeSDomainSet = new HashSet<String>();
//			int webRankingCount = 0;
			int firstLocalListingTrueRank = 0;

			String maxFrequencyDomain = null;
			int maxFrequencyCnt = 0;

			for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
				if (keywordRankEntityVO.getRank() > 101) {
					continue;
				}
				index = 1;

				String url = keywordRankEntityVO.getLandingPage();
				if (StringUtils.equalsIgnoreCase(url, "#") || StringUtils.equalsIgnoreCase(url, ";")) {
					url = "http://instantanswers.google.com/";
				}
				//
				String[] domainUrlList = CommonUtils.splitString(url);
				if (domainUrlList == null) {
					continue;
				}

				// domain
				String domainName = domainUrlList[0];
				// url
				String uriPattern = domainUrlList[1];
				// protocol
				String protocol = domainUrlList[2];

				// add start
				boolean urlhaschanged = false;
				String convertedUrl = null;
				String[] reprocessUri = CommonUtils.getDomainReverseAndUriFromUrl(uriPattern, domainName);
				if (reprocessUri != null && reprocessUri.length >= 2) {
					convertedUrl = CommonUtils.getConvertUrl(uriPattern, domainName);
					domainName = reprocessUri[0];
					uriPattern = reprocessUri[1];
					urlhaschanged = true;
					if (StringUtils.containsIgnoreCase(domainName, "com.googleusercontent.webcache")) {
						String[] reprocessUri2 = CommonUtils.getDomainReverseAndUriFromUrl(uriPattern);
						if (reprocessUri2 != null && reprocessUri2.length >= 2) {
							domainName = reprocessUri2[0];
							// if it contain webcache. we have to decode
							// twice
							uriPattern = URLDecoder.decode(URLDecoder.decode(reprocessUri2[1], "utf-8"),
									"utf-8");
						}
					}
				}
				// add end

				// to solve google map issue.
				if (StringUtils.containsIgnoreCase(domainName, ".google.www")
						&& keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_WEB_RESOURCE
						&& StringUtils.startsWithIgnoreCase(uriPattern, "/maps/")) {
					keywordRankEntityVO.setType(KeywordRankEntityVO.TYPE_ADDRESS);
				}

				String newDomainName = RankTypeManager.convertToNewDomainName(domainName, keywordRankEntityVO.getType(), uriPattern);

				String rootDomain = getRootDomain(newDomainName);

				if (keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_LOCALLISTING) {
					firstLocalListingTrueRank = (firstLocalListingTrueRank == 0 ? keywordRankEntityVO.getRank() : firstLocalListingTrueRank);
				}

				keywordDetailStatement.setString(index++, StringUtils.lowerCase(keywordRankVO.getKeywordForHtml()));
				keywordDetailStatement.setInt(index++, NumberUtils.toInt(domainId));
				keywordDetailStatement.setInt(index++, kId);
				keywordDetailStatement.setInt(index++, searchEngine);
				keywordDetailStatement.setInt(index++, language);
				keywordDetailStatement.setInt(index++, keywordRankVO.getCityId());
				keywordDetailStatement.setString(index++, newDomainName);
				keywordDetailStatement.setString(index++, rootDomain);
				keywordDetailStatement.setString(index++, uriPattern);
				if(urlhaschanged && convertedUrl !=null) {
					keywordDetailStatement.setString(index++, convertedUrl);
				} else {
					keywordDetailStatement.setString(index++, url);
				}
				keywordDetailStatement.setInt(index++, NumberUtils.toInt(protocol));
				keywordDetailStatement.setInt(index++, keywordRankEntityVO.getRank());

				int webRank = (keywordRankEntityVO.getSpecifiedWebRank() != null) ? keywordRankEntityVO.getSpecifiedWebRank() : keywordRankEntityVO.getRank();
				keywordDetailStatement.setInt(index++, webRank);
				keywordDetailStatement.setInt(index++, keywordRankEntityVO.getType());
				keywordDetailStatement.setLong(index++, Long.parseLong(keywordRankVO.getSearchVol()));
				keywordDetailStatement.setFloat(index++, Float.valueOf(keywordRankVO.getCpc()));
				// hrd
				if (uniqueDomainSet.contains(newDomainName)) {
					keywordDetailStatement.setInt(index++, 0);
				} else {
					uniqueDomainSet.add(newDomainName);
					keywordDetailStatement.setInt(index++, 1);
				}

				// hrrd (root domain)
				if (uniqueDomainIncludeSDomainSet.contains(rootDomain)) {
					keywordDetailStatement.setInt(index++, 0);
				} else {
					uniqueDomainIncludeSDomainSet.add(rootDomain);
					keywordDetailStatement.setInt(index++, 1);
				}
				if (!checkKeywordExistsInMonitorWithSendToQDate) {
					keywordDetailStatement.setDate(index++,
							new Date(CacheModleFactory.getInstance().getRankingDate().getTime()));
				} else {
					keywordDetailStatement.setDate(index++,
							new Date(keywordRankVO.getSendToQDateAsDate().getTime()));
				}

				keywordDetailStatement.setInt(index++, convertValue(keywordRankEntityVO.getRating()));

				keywordDetailStatement.setLong(index++,
						keywordRankEntityVO.getRank() * NumberUtils.toLong(keywordRankVO.getSearchVol()));
				keywordDetailStatement.setLong(index++,
						getTraffic(NumberUtils.toLong(keywordRankVO.getSearchVol()),
								keywordRankEntityVO.getRank()).longValue());

				keywordDetailStatement.setLong(index++,
						webRank * NumberUtils.toLong(keywordRankVO.getSearchVol()));
				keywordDetailStatement.setLong(index++,
						getTraffic(NumberUtils.toLong(keywordRankVO.getSearchVol()), webRank).longValue());

				HashSet<String> topFolders = getetDirectory(uriPattern);
				if (topFolders != null && topFolders.size() > 0) {
					if (topFolders.size() == 3) {
						for (String folder : topFolders) {
							keywordDetailStatement.setString(index++, folder);
						}
					} else if (topFolders.size() == 2) {
						for (String folder : topFolders) {
							keywordDetailStatement.setString(index++, folder);
						}
						keywordDetailStatement.setString(index++, "");
					} else if (topFolders.size() == 1) {
						for (String folder : topFolders) {
							keywordDetailStatement.setString(index++, folder);
						}
						keywordDetailStatement.setString(index++, "");
						keywordDetailStatement.setString(index++, "");
					}
				} else {
					keywordDetailStatement.setString(index++, "");
					keywordDetailStatement.setString(index++, "");
					keywordDetailStatement.setString(index++, "");
				}

				keywordDetailStatement.setInt(index++, 1);
//begin xxxxyyyy
				keywordDetailStatement.setArray(index++,
						new ClickHouseArray(ClickHouseDataType.String, attrsGroup.toArray(new String[attrsGroup.size()])));
//end xxxxyyyy
				List<String> attrsValue = new ArrayList<String>();
				attrsValue.add(hasAnswerBox ? "1" : "0");
				attrsValue.add(hasLL ? "1" : "0");
				attrsValue.add(hasPla ? "1" : "0");
				attrsValue.add(hasPpc ? "1" : "0");
				attrsValue.add(hasPeopleAlsoAsk ? "1" : "0");
				attrsValue.add(hasApp ? "1" : "0");
				attrsValue.add(hasAmp ? "1" : "0");
				attrsValue.add(hasImg ? "1" : "0");
				attrsValue.add(hasNews ? "1" : "0");
				attrsValue.add(hasVideo ? "1" : "0");
				if(StringUtils.isNotBlank(keywordRankEntityVO.getAmpFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getAmpFlg(), "y")) {
					attrsValue.add("1");
				} else {
					attrsValue.add("0");
				}
				attrsValue.add(hasPrice ? "1" : "0");
				attrsValue.add(hasStock ? "1" : "0");

				if (StringUtils.isNotBlank(keywordRankEntityVO.getPriceFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getPriceFlg(),"y")) {
					attrsValue.add("1");
				} else {
					attrsValue.add("0");
				}
				if (StringUtils.isNotBlank(keywordRankEntityVO.getStockFlg()) && StringUtils.equalsIgnoreCase(keywordRankEntityVO.getStockFlg(),"y")) {
					attrsValue.add("1");
				} else {
					attrsValue.add("0");
				}
				attrsValue.add(keywordRankEntityVO.getRatingNumber());

				// job url
				if(StringUtils.isNotBlank(keywordRankVO.getJobLink())) {
					String[] jobSources = keywordRankVO.getJobLink().split("@_@");
					if(jobSources.length == 2 && StringUtils.isNotBlank(jobSources[1])) {
						attrsValue.add(jobSources[1]);
					} else {
						attrsValue.add("-");
					}
				} else {
					attrsValue.add("-");
				}

				// apple category
				if (StringUtils.isNotBlank(keywordRankVO.getCategory())) {
					if (StringUtils.equalsIgnoreCase(keywordRankVO.getCategory(), "explore")) {
						attrsValue.add("1");
					} else if (StringUtils.equalsIgnoreCase(keywordRankVO.getCategory(), "accessories")) {
						attrsValue.add("2");
					} else if (StringUtils.equalsIgnoreCase(keywordRankVO.getCategory(), "support")) {
						attrsValue.add("3");
					} else if (StringUtils.equalsIgnoreCase(keywordRankVO.getCategory(), "apples store")) {
						attrsValue.add("4");
					} else {
						attrsValue.add("0");
					}
				} else {
					attrsValue.add("0");
				}

				// knong_flag
				// https://www.wrike.com/open.htm?id=296735726
				attrsValue.add(convertValue(keywordRankVO.getKnogTag()).toString());

				// flight_search_flg
				// https://www.wrike.com/open.htm?id=299449773
				attrsValue.add(convertValue(keywordRankVO.getFlightSearchFlg()).toString());

				// social_in_kg
				// https://www.wrike.com/open.htm?id=306376871
				attrsValue.add(convertValue(keywordRankVO.getSocialInKg()).toString());

				// qa list flag
				// https://www.wrike.com/open.htm?id=399697352
				attrsValue.add(hasQaList ? "1" : "0");
				boolean isQa = StringUtils.isNotBlank(keywordRankEntityVO.getQuestionlist()) && !StringUtils.equals(keywordRankEntityVO.getQuestionlist(), "!_!");
				attrsValue.add(isQa ? "1" : "0");
//begin xxxxyyyy
				keywordDetailStatement.setArray(index++,
						new ClickHouseArray(ClickHouseDataType.String, attrsValue.toArray(new String[attrsValue.size()])));
//end xxxxyyyy
				// title/meta
				// https://www.wrike.com/open.htm?id=221768860
				// https://www.wrike.com/open.htm?id=370685433
//				if( keywordRankEntityVO.getRank() <= 20) {
				if((CommonUtils.getDayOfWeek(CacheModleFactory.getInstance().getRankingDate()) == 7
						|| (checkKeywordExistsInMonitorWithSendToQDate && CommonUtils.getDayOfWeek(keywordRankVO.getSendToQDateAsDate()) == 7))
						|| (NEED_TITLE_DOMAIN_LIST.contains(domainId))) {
					keywordDetailStatement.setString(index++, keywordRankEntityVO.getLabel());
					keywordDetailStatement.setString(index++, keywordRankEntityVO.getMetaDesc());
					keywordDetailStatement.setInt(index++, keywordRankEntityVO.getLabelLen());
					keywordDetailStatement.setInt(index++, keywordRankEntityVO.getMetaDescLen());
				} else {
					keywordDetailStatement.setString(index++, "");
					keywordDetailStatement.setString(index++, "");
					keywordDetailStatement.setInt(index++, 0);
					keywordDetailStatement.setInt(index++, 0);
				}

				// top10 top frequency domain
				keywordDetailStatement.setString(index++, maxFrequencyDomain);
				keywordDetailStatement.setInt(index++, maxFrequencyCnt);

				keywordDetailStatement.addBatch();

				// ignore sub rank
//				if (keywordRankEntityVO.getSubRankVOs() != null
//						&& keywordRankEntityVO.getSubRankVOs().size() > 0) {
//
//					int subRank = 1;
//					for (KeywordSubRankEntityVO subVO : keywordRankEntityVO.getSubRankVOs()) {
//
//					}
//				} else {
//					// if the keyword rank has no sub rank, check the question list
//					// add to sub rank
//	                if (isQa) {
//	                }
//				}
			}

			// locallisting start
			if (keywordRankVO.getLocalListing() != null && keywordRankVO.getLocalListing().size() > 0) {
			}
			// locallisting end
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private BigDecimal getTraffic(long searchVolume, int rank) {
		double traffic = 0;
		if (rank == 1) {
			traffic = searchVolume * 0.3;
		} else if (rank == 2) {
			traffic = searchVolume * 0.24;
		} else if (rank == 3) {
			traffic = searchVolume * 0.15;
		} else if (rank == 4) {
			traffic = searchVolume * 0.1;
		} else if (rank == 5) {
			traffic = searchVolume * 0.08;
		} else if (rank == 6) {
			traffic = searchVolume * 0.05;
		} else if (rank == 7) {
			traffic = searchVolume * 0.04;
		} else if (rank == 8) {
			traffic = searchVolume * 0.03;
		} else if (rank == 9) {
			traffic = searchVolume * 0.01;
		} else if (rank == 10) {
			traffic = searchVolume * 0;
		}

		BigDecimal bDec = new BigDecimal(traffic);
		return bDec.setScale(0, BigDecimal.ROUND_HALF_UP);
	}

	public static boolean isUniversalUrl(String url, int urlType) {
		if (RankTypeManager.isWebRank(url, urlType)) {
			return false;
		}
		return true;
	}

	// https://www.wrike.com/open.htm?id=389170665
	private String getRootDomain(String fullDomain) {
		String domainName = null;
		try {
			domainName = StringUtils.reverseDelimited(fullDomain, '.');
			return StringUtils.reverseDelimited(InternetDomainName.from(domainName).topPrivateDomain().toString(), '.');
		} catch (Exception e) {
			try {
				if (StringUtils.startsWithIgnoreCase(domainName, "www.")) {
					return StringUtils.reverseDelimited(StringUtils.removeStartIgnoreCase(domainName, "www."), '.');
				} else {
					return StringUtils.reverseDelimited(domainName, '.');
				}
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
		return null;
	}

	private String getUrlForSubRankViaDecode(String url) {

		if (StringUtils.containsIgnoreCase(url, "https")) {

			url = extractSubUrl(url, "https");

			url = decodeHttpUrl(url);
			url = decodeHttpUrl(url);
			url = decodeHttpUrl(url);
		} else if (StringUtils.containsIgnoreCase(url, "http")) {

			url = extractSubUrl(url, "http");

			url = decodeHttpUrl(url);
			url = decodeHttpUrl(url);
			url = decodeHttpUrl(url);
		} else {
			url = "/";
		}

		return url;
	}

	private String extractSubUrl(String url, String protocol) {

		int i = StringUtils.indexOf(url, protocol);
		url = StringUtils.substring(url, i);

		int j = StringUtils.indexOf(url, "&");
		url = StringUtils.substring(url, 0, j);

		return url;
	}

	private String decodeHttpUrl(String urlStr) {
		try {
			return decode(urlStr, Charset.forName("UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return urlStr;
	}

	private String decode(String source, Charset charset) {
		int length = source.length();
		ByteArrayOutputStream bos = new ByteArrayOutputStream(length);
		boolean changed = false;
		for (int i = 0; i < length; i++) {
			int ch = source.charAt(i);
			if (ch == '%') {
				if ((i + 2) < length) {
					char hex1 = source.charAt(i + 1);
					char hex2 = source.charAt(i + 2);
					int u = Character.digit(hex1, 16);
					int l = Character.digit(hex2, 16);
					if (u == -1 || l == -1) {
						throw new IllegalArgumentException("Invalid encoded sequence \"" + source.substring(i) + "\"");
					}
					bos.write((char) ((u << 4) + l));
					i += 2;
					changed = true;
				} else {
					throw new IllegalArgumentException("Invalid encoded sequence \"" + source.substring(i) + "\"");
				}
			} else {
				bos.write(ch);
			}
		}
		return (changed ? new String(bos.toByteArray(), charset) : source);
	}

	@Override
	protected void undo() throws Exception {
	}

	private Integer convertValue(String value) {
		if (StringUtils.equalsIgnoreCase(value, "y")) {
			return 1;
		}
		return 0;
	}

	private String getKeyowrdInfoHistoricalTableName(int engineId, int languageId) {
		if (mobileRanking) {
			if (searchEngine == 1 && language == 1) {
				return prefix_ins_table + "m_ranking_info_historical_us";
			} else {
				return prefix_ins_table + "m_ranking_info_historical_intl";
			}
		} else {
			if (searchEngine == 1 && language == 1) {
				return prefix_ins_table + "d_ranking_info_historical_us";
			} else {
				return prefix_ins_table + "d_ranking_info_historical_intl";
			}
		}
	}

	private String getKeyowrdDetailHistoricalTableName(int engineId, int languageId) {
		if (mobileRanking) {
			if (searchEngine == 1 && language == 1) {
				return prefix_ins_table + "m_ranking_detail_historical_us";
			} else {
				return prefix_ins_table + "m_ranking_detail_historical_intl";
			}
		} else {
			if (searchEngine == 1 && language == 1) {
				return prefix_ins_table + "d_ranking_detail_historical_us";
			} else {
				return prefix_ins_table + "d_ranking_detail_historical_intl";
			}
		}
	}

//	private String getKeyowrdSubRankTableName(int engineId, int languageId) {
//		if (mobileRanking) {
//			if (searchEngine == 1 && language == 1) {
//				return prefix_ins_table + "m_ranking_subrank_us";
//			} else {
//				return prefix_ins_table + "m_ranking_subrank_intl";
//			}
//		} else {
//			if (searchEngine == 1 && language == 1) {
//				return prefix_ins_table + "d_ranking_subrank_us";
//			} else {
//				return prefix_ins_table + "d_ranking_subrank_intl";
//			}
//		}
//	}

	public HashSet<String> getetDirectory(String url) throws Exception {
		url = StringUtils.substringBeforeLast(url, "/");

		String[] uriArraries = url.replaceFirst("/", "").split("/");

		String firstDepth = "";
		String secondDepth = "";
		String thirdDepth = "";
		LinkedHashSet<String> top3Directories = new LinkedHashSet<String>();

		if (uriArraries != null) {
			for (int i = 0; i < uriArraries.length; i++) {
				String directory = uriArraries[i];
				if (i == 0) {
					firstDepth = directory;
				} else if (i == 1) {
					secondDepth = firstDepth + "/" + directory;
				} else if (i == 2) {
					thirdDepth = secondDepth + "/" + directory;
				}
			}
		}

		if (StringUtils.isNotBlank(firstDepth)) {
			top3Directories.add(firstDepth);
		}
		if (StringUtils.isNotBlank(secondDepth)) {
			top3Directories.add(secondDepth);
		}
		if (StringUtils.isNotBlank(thirdDepth)) {
			top3Directories.add(thirdDepth);
		}

		return top3Directories;
	}

	public void setCheckKeywordExistsInMonitorWithSendToQDate(boolean checkKeywordExistsInMonitorWithSendToQDate) {
		this.checkKeywordExistsInMonitorWithSendToQDate = checkKeywordExistsInMonitorWithSendToQDate;
	}

//	private String getMaxDomain(Map<String, Integer> domainFrequencyMap) {
//		int maxCnt = 0;
//		String maxDomain = null;
//		for (String rootDomain : domainFrequencyMap.keySet()) {
//			int cnt = domainFrequencyMap.get(rootDomain);
//			if (cnt > 1 && cnt > maxCnt) {
//				maxCnt = cnt;
//				maxDomain = rootDomain;
//			}
//		}
//
//		return maxDomain;
//	}

}
