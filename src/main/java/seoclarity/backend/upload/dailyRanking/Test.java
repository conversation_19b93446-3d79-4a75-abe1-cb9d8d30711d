package seoclarity.backend.upload.dailyRanking;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.amazonaws.services.sqs.AmazonSQS;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.upload.dailyRanking
 * @author: cil
 * @date: 2021-07-29 15:51
 *
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.dailyRanking.Test"
 *
 **/
public class Test {

    public static void main(String[] args) {

        AmazonSQS amazonSQS = SQSUtils.getAmazonSQS();

        File[] files = FileUtil.ls("/disk1/scribeKeyword/commoncrawl_keywordRank_150_6");
        for (File file : files) {
            if(StrUtil.containsIgnoreCase(file.getName(), "2024-04-07") == false) {
                continue;
            }
            List<String> stringList = FileUtil.readLines(file, StandardCharsets.UTF_8);
            for (String s : stringList) {
                JSONObject message = new JSONObject();
                JSONObject object = JSON.parseObject(s);
                message.put("keywordText", object.getString("keyword"));
                message.put("searchEngine", 150);
                message.put("searchLanguage", 6);
                message.put("domainList", object.getJSONArray("domainList"));
                message.put("id", object.getInteger("id"));
                message.put("cityId", 0);
                message.put("clarityDBKeywordHash", object.getString("clarityDBKeywordHash"));
                message.put("sendToQDate", 20240408);


                SQSUtils.sendMessageToQueue(amazonSQS, "LEO_GEO_TEST", message.toJSONString());
            }
        }

    }

}
