package seoclarity.backend.upload.dailyRanking;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

public class CheckErrorRegEx {
    private CommonParamDAO commonParamDAO;
    static final int maxCnt = 120000;
    static final int minCnt = 0;
    static Gson gson = new Gson();

    private CheckErrorRegEx() {
        commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
    }

    public static void main(String[] args) throws IOException {
        System.out.println(" gogogo ");
        CheckErrorRegEx checkErrorRegEx = new CheckErrorRegEx();
        Set<Map<String, String>> response = new HashSet<>();
        Map<String, Object> res = new HashMap<>();
        List<CommonParamEntity> regExList = checkErrorRegEx.commonParamDAO.getRegExList();
        System.out.println(" regExList.size : " + regExList.size());

        for (CommonParamEntity vo : regExList) {
            JSONObject paramJson = JSONObject.parseObject(vo.getParamJson());
            System.out.println("id : " + vo.getId() + " paramJson :" + paramJson);
            String err = checkErrorRegExParams(paramJson);
            System.out.println(" err :" + err);
            if (StringUtils.isNotBlank(err)) {
                String erro = err.substring(0, err.length() - 2);
                Map<String, String> data = new HashMap<>();
                data.put("id", vo.getId().toString());
                data.put("name", vo.getTitle());
                data.put("ownDomainId", vo.getOwnDomainId().toString());
                data.put("errRegEx", erro);
                System.out.println(" add regEx : " + gson.toJson(data));
                response.add(data);
            }
        }

        res.put("data", response);
        File f = new File("Regex.log");
        FileUtils.write(f, gson.toJson(res), true);
    }

    private static String checkErrorRegExParams(JSONObject paramJson) throws IOException {
        StringBuffer err = new StringBuffer();
        JSONArray jsonArray = JSONArray.parseArray(String.valueOf(paramJson.get("items")));
        if (ArrayUtils.isNotEmpty(new JSONArray[]{jsonArray}) && jsonArray.size() > 0) {
            for (Object obj : jsonArray) {
                JSONObject json = (JSONObject) JSONObject.toJSON(obj);
                err.append(checkErrorRegExParams(json));
            }
        } else {
            String regEx = paramJson.get("value").toString();
            String action = paramJson.get("action").toString();
            System.out.println(" regEx : " + regEx + " action : " + action);

            try {
                if (StringUtils.isNotBlank(action) && action.equals("pt") || action.equals("npt")) {
                    Pattern pattern = Pattern.compile(regEx);
                }
            } catch (Exception e) {
                err.append(regEx).append(" , ");
                System.out.println(" data : " + regEx);
            }

        }
        return err.toString();
    }

}
