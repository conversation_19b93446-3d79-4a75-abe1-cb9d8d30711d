package seoclarity.backend.upload.dailyRanking;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @date 2021-05-05
 * @path seoclarity.backend.upload.dailyRanking.ImporRIHisFor7684
 * https://www.wrike.com/open.htm?id=651742590
 */
public class ImporRIHisFor7684 extends ImportHistoryRankingForDailyRanking {
    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat MM_dd_yyyy = new SimpleDateFormat("MM/dd/yyyy");
    private static Map<String, List<String>> domainMap = new HashMap<>();
    private static Map<String, String> resultType = new HashMap<>();
    private static String UKE = "United Kingdom / English";
    private static String SS = "Sweden / Swedish";
    private static String GG = "Germany / German";
    private static String CF = "Canada / French";
    private static String CE = "Canada / English";
    private static String AE = "Australia / English";
    private static String UE = "US / English";

    static {
        List<String> domainList1 = new ArrayList<>();
        domainList1.add("10384");
        domainList1.add("www.ancestry.ca");
        List<String> domainList2 = new ArrayList<>();
        domainList2.add("10385");
        domainList2.add("www.ancestry.co.uk");
        List<String> domainList3 = new ArrayList<>();
        domainList3.add("10386");
        domainList3.add("www.ancestry.mx");
        List<String> domainList4 = new ArrayList<>();
        domainList4.add("10387");
        domainList4.add("www.ancestry.de");
        List<String> domainList5 = new ArrayList<>();
        domainList5.add("10388");
        domainList5.add("www.ancestry.it");
        List<String> domainList6 = new ArrayList<>();
        domainList6.add("10389");
        domainList6.add("www.ancestry.se");
        List<String> domainList7 = new ArrayList<>();
        domainList7.add("10390");
        domainList7.add("www.ancestry.fr");
        List<String> domainList8 = new ArrayList<>();
        domainList8.add("10391");
        domainList8.add("www.ancestry.com.au");
        List<String> domainList9 = new ArrayList<>();
        domainList9.add("10392");
        domainList9.add("www.newspapers.com");
        List<String> domainList10 = new ArrayList<>();
        domainList10.add("10395");
        domainList10.add("www.weremember.com");
        List<String> domainList11 = new ArrayList<>();
        domainList11.add("10398");
        domainList11.add("www.progenealogists.com");

        domainMap.put("ancestry.ca", domainList1);
        domainMap.put("ancestry.co.uk", domainList2);
        domainMap.put("ancestry.de", domainList4);
        domainMap.put("ancestry.se", domainList6);
        domainMap.put("ancestry.com.au", domainList8);
        domainMap.put("newspapers.com", domainList9);
        domainMap.put("weremember.com", domainList10);
        domainMap.put("progenealogists.com", domainList11);

        resultType.put("Standard Link", "1");
        resultType.put("Answer Box", "9");

    }

    private OwnDomainEntityDAO ownDomainEntityDAO;

    public ImporRIHisFor7684() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    }

    public static void main(String[] args) {
        String filePath = args[0];
        isUseSendToQDate = true;
        isUseMonitorTable = true;
        isUseRIExistsCheck = false;
        isQueryForSv = false;
        specified_monitor_table_refix = "test_his_daily_ranking_imp_monitor_7684_";
        ImporRIHisFor7684 ins = new ImporRIHisFor7684();
        try {
            System.out.println("=========================Process desktop===================================");
            for (File file : new File(filePath).listFiles()) {
                // uk.sttropeztan.com - 9750.csv
                if (file.getName().endsWith(".xlsx")) {
                    ins.process(file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) throws Exception {
        int oid = 7684;

        int engine = 1;
        int language = 1;

        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
//            try {
//                Thread.sleep(20000);
//            } catch (Exception e) {
//            }


            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);


                    List<KeywordRankVO> keywordRankVOStream25 = processList.parallelStream().filter(vo -> ("2".equals(vo.getEngine()))).filter(vo -> ("5".equals(vo.getLanguage()))).collect(Collectors.toList());
                    List<KeywordRankVO> keywordRankVOStream33 = processList.parallelStream().filter(vo -> ("3".equals(vo.getEngine()))).filter(vo -> ("3".equals(vo.getLanguage()))).collect(Collectors.toList());
                    List<KeywordRankVO> keywordRankVOStream34 = processList.parallelStream().filter(vo -> ("3".equals(vo.getEngine()))).filter(vo -> ("4".equals(vo.getLanguage()))).collect(Collectors.toList());
                    List<KeywordRankVO> keywordRankVOStream1415 = processList.parallelStream().filter(vo -> ("14".equals(vo.getEngine()))).filter(vo -> ("15".equals(vo.getLanguage()))).collect(Collectors.toList());
                    List<KeywordRankVO> keywordRankVOStream1314 = processList.parallelStream().filter(vo -> ("13".equals(vo.getEngine()))).filter(vo -> ("14".equals(vo.getLanguage()))).collect(Collectors.toList());
                    List<KeywordRankVO> keywordRankVOStream58 = processList.parallelStream().filter(vo -> ("5".equals(vo.getEngine()))).filter(vo -> ("8".equals(vo.getLanguage()))).collect(Collectors.toList());
                    List<KeywordRankVO> keywordRankVOStream11 = processList.parallelStream().filter(vo -> ("1".equals(vo.getEngine()))).filter(vo -> ("1".equals(vo.getLanguage()))).collect(Collectors.toList());

                    List<List> list = new ArrayList<>();
                    list.add(keywordRankVOStream25);
                    list.add(keywordRankVOStream33);
                    list.add(keywordRankVOStream34);
                    list.add(keywordRankVOStream1415);
                    list.add(keywordRankVOStream1314);
                    list.add(keywordRankVOStream58);
                    list.add(keywordRankVOStream11);

                    for (List<KeywordRankVO> processLists : list) {
                        if (processLists != null && processLists.size() > 0) {
                            System.out.println("=====================OID:" + oid + date + "===============================");
                            Date workDate = yyyy_MM_dd.parse(date);
                            setParameters(workDate, Integer.valueOf(processLists.get(0).getEngine()), Integer.valueOf(processLists.get(0).getLanguage()), false);
                            loadData(processLists);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel(String file) throws Exception {

        int oid;

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();

        String[] headerCol = getStringArray(sheet.getRow(sheet.getFirstRowNum() + 8));
        List<String> header = new ArrayList<String>(Arrays.asList(headerCol));


//        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");


        Map<String, List<KeywordRankVO>> dateMap = new HashMap<>();

        System.out.println("===file:" + file + ", lines:" + sheet.getPhysicalNumberOfRows());

        int totalColumns = 0;

        int lineCnt = 0;
        for (int n = start; n <= end; n++) {
            lineCnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }


//            int endCell = row.getLastCellNum();

            try {
                String[] cols = getStringArray(row);


                System.out.println(cols[15]);
                if ("ancestry.com".equals(domainMap.get(cols[15]))) {
                    continue;
                }

                if (domainMap.get(cols[15]) == null) {
                    continue;
                }
                oid = Integer.valueOf(domainMap.get(cols[15]).get(0));


                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Prime") && StringUtils.equalsIgnoreCase(cols[1], "Keyword")) {
//                    totalColumns = cols.length;
//                    List<String> header = new ArrayList<String>(Arrays.asList(cols));
//                    System.out.println("===file:" + file + ", date list:" + header.subList(6, header.size() - 1));
                } /*else if (cols.length != totalColumns) {
                    System.out.println("===cols wrong. oid:" + oid + ", File:" + file + ", lineCnt:" + lineCnt + ", colLength:" + cols.length + ", totalColumns:" + totalColumns + ", line:" + sheet.getPhysicalNumberOfRows());
                    continue;
                } */ else {
                    String domain = domainMap.get(cols[15]).get(1);
                    String kwStr = cols[1];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
//                    String url = cols[5];
                    String url = cols[8];
                    url = getUrl(url, domain);

                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

//                    for (int i = 8; i < cols.length; i++) {
                    String date1 = header.get(3).replaceAll("Rank", "").trim();
                    String date2 = header.get(4).replaceAll("Rank", "").trim();

                    //add one day for date1 and date2
                    date1 = yyyy_MM_dd.format(new Date(MM_dd_yyyy.parse(date1).getTime() + 24 * 60 * 60 * 1000));
                    date2 = yyyy_MM_dd.format(new Date(MM_dd_yyyy.parse(date2).getTime() + 24 * 60 * 60 * 1000));
                    int rank1, rank2;

                    try {
                        rank1 = Integer.valueOf(cols[3].substring(0, cols[3].indexOf(".")));
                    } catch (Exception e) {
                        rank1 = 101;
                    }
                    try {
                        rank2 = Integer.valueOf(cols[4].substring(0, cols[4].indexOf(".")));
                    } catch (Exception e) {
                        rank2 = 101;
                    }


                    List<String> columns1 = getColumns1();
                    columns1.add(kwStr);
                    columns1.add(cols[2]);
                    if (cols[8] == null || "".equals(cols[8])) {
                        columns1.add("101");
                        columns1.add("101");
                    } else if (cols[3] == null || "Did not rank".equals(cols[3])) {
                        columns1.add("101");
                        columns1.add("101");
                    } else if (cols[6] == null || "N/A".equals(cols[6])) {
                        columns1.add("101");
                        columns1.add("101");
                    } else {
                        columns1.add(String.valueOf(rank1));
                        columns1.add(String.valueOf(rank1));
                    }
                    columns1.add(url);
                    columns1.add(date1);
                    columns1.add(cols[6]);
                    columns1.add(cols[2]);
                    columns1.add(cols[9]);
                    columns1.add(cols[16]);
                    KeywordRankVO[] vos1 = parsertoEntity(columns1, oid);
                    if (vos1 != null && vos1.length > 0) {
                        for (KeywordRankVO vo : vos1) {
                            if (dateMap.containsKey(date1)) {
                                dateMap.get(date1).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date1, voList);
                            }
                        }
                    }


                    List<String> columns2 = new ArrayList<String>();
                    columns2.add(kwStr);
                    columns2.add(cols[2]);

                    if (cols[8] == null || "".equals(cols[8])) {
                        columns2.add("101");
                        columns2.add("101");
                    } else if (cols[4] == null || "Did not rank".equals(cols[4])) {
                        columns2.add("101");
                        columns2.add("101");
                    } else if (cols[7] == null || "N/A".equals(cols[7])) {
                        columns2.add("101");
                        columns2.add("101");
                    } else {
                        columns2.add(String.valueOf(rank2));
                        columns2.add(String.valueOf(rank2));
                    }

                    columns2.add(url);
                    columns2.add(date2);
                    columns2.add(cols[7]);
                    columns2.add(cols[2]);
                    columns2.add(cols[9]);
                    columns2.add(cols[16]);


                    KeywordRankVO[] vos2 = parsertoEntity(columns2, oid);

                    if (vos2 != null && vos2.length > 0) {
                        for (KeywordRankVO vo : vos2) {
                            if (dateMap.containsKey(date2)) {
                                dateMap.get(date2).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date2, voList);
                            }
                        }
                    }

//                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return dateMap;

    }

    @NotNull
    private ArrayList<String> getColumns1() {
        return new ArrayList<String>();
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i).toString();
        }
        return lines;
    }

    private Map<String, List<KeywordRankVO>> parseExcel(String file, int oid) throws Exception {


        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");


        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();

        System.out.println("===file:" + file + ", lines:" + lines.size());

        CSVParser parser = new CSVParser(',');
        int totalColumns = 0;
        List<String> header = null;
        int lineCnt = 0;
        for (String line : lines) {
            lineCnt++;
            if (StringUtils.isBlank(line)) {
                continue;
            }
            try {
                String[] cols = parser.parseLine(line);
                System.out.println(cols[15]);
                oid = Integer.valueOf(domainMap.get(cols[15]).get(0));


                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Domain") && StringUtils.equalsIgnoreCase(cols[1], "Keyword")) {
                    totalColumns = cols.length;
                    header = new ArrayList<String>(Arrays.asList(cols));
                    System.out.println("===file:" + file + ", date list:" + header.subList(6, header.size() - 1));
                } else if (cols.length != totalColumns) {
                    System.out.println("===cols wrong. oid:" + oid + ", File:" + file + ", lineCnt:" + lineCnt + ", colLength:" + cols.length + ", totalColumns:" + totalColumns + ", line:" + line);
                    continue;
                } else {
                    String domain = "www.ancestry.com";
                    String kwStr = cols[1];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
//                    String url = cols[5];
                    String url = cols[8];
                    url = getUrl(url, domain);

                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    for (int i = 8; i < cols.length; i++) {
                        String date = header.get(i);
                        int rank = 101;
                        try {
                            rank = (StringUtils.isNotBlank(cols[i])
                                    && !StringUtils.equals(StringUtils.trim(cols[i]), "-"))
                                    ? Integer.valueOf(StringUtils.trim(cols[i])) : 101;
                        } catch (Exception e) {
                            System.out.println("===Parse rank failed.File:" + file + ", lineIdx:" + lineCnt + ", colsIdx:" + i + ", rank:" + cols[i] + ", line:" + line);
                        }

                        List<String> columns = new ArrayList<String>();
                        columns.add(kwStr);
                        columns.add("0");
                        columns.add(String.valueOf(rank));
                        columns.add(String.valueOf(rank));
                        columns.add(url);
                        columns.add(date);

                        KeywordRankVO[] vos = parsertoEntity(columns, oid);
                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }

    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }


    @Override
    public void
    loadData(List<KeywordRankVO> dataList) throws Exception {
        try {
            threadPool.init();
            CommonUtils.initThreads(MAX_THREAD);

            processInsert(workDate, searchEngine, language, mobileRanking, dataList);

            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        List<String> domainList = new ArrayList<String>();
        try {
            String kw = StringUtils.trim(column.get(0)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();
            String sv = StringUtils.trim((column.get(1)));
            Integer searchVolumn = getInteger(sv);
            searchVolumn = searchVolumn < 0 ? 0 : searchVolumn;
            Integer trueRank = Integer.valueOf(StringUtils.trim(column.get(2)));
            Integer webRank = Integer.valueOf(StringUtils.trim(column.get(3)));
            String url = column.get(4);
            String rankingDate = (column.get(5));
            String type = (resultType.get(column.get(6)));
            String monthlySearchVolume = (column.get(7));

            String engine = "1";
            String language = "1";
            if (column.get(9).contains(UE)) {
                engine = "1";
                language = "1";
            } else if (column.get(9).contains(AE)) {
                engine = "2";
                language = "5";
            } else if (column.get(9).contains(CE)) {
                engine = "3";
                language = "3";
            } else if (column.get(9).contains(CF)) {
                engine = "3";
                language = "4";
            } else if (column.get(9).contains(GG)) {
                engine = "14";
                language = "15";
            } else if (column.get(9).contains(SS)) {
                engine = "13";
                language = "14";
            } else if (column.get(9).contains(UKE)) {
                engine = "5";
                language = "8";
            }

            String title = column.get(8);
            // TODO
            if (StringUtils.isBlank(url)) {
                trueRank = 101;
                webRank = 101;
            }
            System.out.println("kw = " + kw + "   engine = " + engine + "    language = " + language);
            domainList.add(String.valueOf(processDomainId));

            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(searchVolumn.toString());
            vo1.setCpc(monthlySearchVolume);
            vo1.setEngine(engine);
            vo1.setLanguage(language);
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 101 && StringUtils.isNotBlank(url)) {
                System.out.println(" type  =  : " + type);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank, type, title);

                if (rankingVo != null) {
                    list.add(rankingVo);
                } else {
                    System.out.println("=Skip wrong ranking url, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                }
            }
            vo1.setKeywordRankEntityVOs(list);

            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank, String type, String label) {
        if (StringUtils.isBlank(url) || trueRank == 101) {
            return null;
        }
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(Integer.valueOf(type));
        vo.setLabel(label);

        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
//        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
//            return "";
//        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
//            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
//        } else if (url.length() <= ownDomainName.length()) {
//            throw new Exception("Wrong url, url:" + url);
//        }
        return url;
    }

}
