package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import scala.Int;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-03-08
 * @path seoclarity.backend.upload.dailyRanking.ImporRIHisFor10799
 * https://www.wrike.com/open.htm?id=855280457
 */
public class ImporRIHisFor10799 extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");

    private Set<String> kwSet = new HashSet<>();
    private Map<String, String> kwMap = new HashMap<>();


    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;

    public ImporRIHisFor10799() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
    }

    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test10799_local_";
        prefix_ins_table = "test10799_local_";
        ImporRIHisFor10799 ins = new ImporRIHisFor10799();
        String filePath = "rankFile";

        try {
            System.out.println("=========================Process desktop===================================");
            for (File file : new File(filePath).listFiles()) {

                if (file.getName().startsWith("Tesco")) {
//                    if (file.getName().contains("February")) {
                        System.out.println("====================== gogogo ======================");
                        System.out.println("file name : " + file.getName());
                        ins.process(file.getAbsolutePath());
//                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) throws Exception {
        int oid = 10799;
        OwnDomainEntity domainENtity = ownDomainEntityDAO.getById(oid);
        int engine = ScKeywordRankManager.getSearchEngineId(domainENtity);
        language = ScKeywordRankManager.getSearchLanguageId(domainENtity);
        System.out.println(" engine :" + engine + "language : " + language );
        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, oid);
        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList + ", engine:" + engine + ", language:" + language);
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    System.out.println("=====================OID:" + oid + date + "===============================");
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, engine, language, true);
                    loadData(processList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, List<KeywordRankVO>> parseExcel(String file, int oid) throws Exception {

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        System.out.println(" start : " + start + " end : " + end);
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();


        int lineCnt = 0;
        for (int n = start; n <= end; n++) {
            lineCnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }

            try {
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[1], "Keyword") && StringUtils.equalsIgnoreCase(cols[4], "Url")) {
                    System.out.println(" line :" + lineCnt + " head ");
                } else if (cols[0].equalsIgnoreCase("Google UK (Mobile)")) {

                    String domain = "www.tesco.com";
                    String kwStr = cols[1];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String url = cols[4];
//                    url = getUrl(url, domain);
                    String rank = cols[3];
                    String searchEngine = cols[0];
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String date = cols[2];
                    Date date1 = new Date(date);
                    date = yyyy_MM_dd.format(date1);
//                    System.out.println(" date : " + date);
                    List<String> columns = new ArrayList<String>();
                    columns.add(searchEngine);
                    columns.add(kwStr);
                    columns.add(date);
                    columns.add(rank);
                    columns.add(url);

                    KeywordRankVO[] vos = parsertoEntity(columns, oid);
                    if (vos != null && vos.length > 0) {
                        for (KeywordRankVO vo : vos) {
                            if (dateMap.containsKey(date)) {
                                dateMap.get(date).add(vo);
                            } else {
                                List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                voList.add(vo);
                                dateMap.put(date, voList);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateMap;
    }

    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(1)).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

//            Integer trueRank = Integer.parseInt(StringUtils.trim(column.get(3)));
//            Integer webRank = Integer.parseInt(StringUtils.trim(column.get(3)));
            Double trueRankd = Double.valueOf(column.get(3));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(3));
            Integer webRank = (int) Math.ceil(webRankd);

            String url = column.get(4);
            String rankingDate = (column.get(2));
            if (StringUtils.isBlank(url)) {
                trueRank = 101;
                webRank = 101;
            }

            String sv = "0";
            if (kwSet.contains(kw)) {
                sv = kwMap.get(kw);
            } else {
                KeywordAdwordsEntity entity = keywordAdwordsEntityDAO.getNationalKwSV(URLEncoder.encode(kw, "UTF-8"),language);
                if (null != entity) {
                    kwSet.add(kw);
                    sv = String.valueOf(entity.getAvgMonthlySearchVolume());
                    kwMap.put(kw, sv);
                } else {
                    kwSet.add(kw);
                    kwMap.put(kw, "0");
                }
            }
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc("0");
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 101 && StringUtils.isNotBlank(url)) {
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                if (rankingVo != null) {
                    list.add(rankingVo);
                } else {
                    System.out.println("=Skip wrong ranking url, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                }
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        if (StringUtils.isBlank(url) || trueRank == 101) {
            return null;
        }
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);

        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
            return "";
        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
        } else if (url.length() <= ownDomainName.length()) {
            throw new Exception("Wrong url, url:" + url);
        }
        return url;
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }

}
