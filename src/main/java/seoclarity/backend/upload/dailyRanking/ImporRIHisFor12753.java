package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-03-06
 * @path https://www.wrike.com/open.htm?id=1315462836
 * <p>
 * https://www.wrike.com/open.htm?id=1354349590
 */
public class ImporRIHisFor12753 extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
    private SimpleDateFormat ddMMMyyyy = new SimpleDateFormat("dd-MMM-yyyy");

    private static String device = "d";
    private static int oid = 12755;
    private static int runFile = 12755;

    private static boolean testFlg = true;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;

    public ImporRIHisFor12753() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
    }


    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test12755_local_";
        prefix_ins_table = "test12755_local_";

        String filePath = "files/12759Files/240314Files";
        filePath = "files/12759Files/240424"; //https://www.wrike.com/open.htm?id=1354349590
        ImporRIHisFor12753 ins = new ImporRIHisFor12753();

        if (null != args && args.length > 0) {
            if (args[0].equals("f")) {
                testFlg = false;
            }
            runFile = Integer.parseInt(args[1]);
        }
        try {
            for (File file : new File(filePath).listFiles()) {
                if (file.getName().endsWith(".xlsx")) {
                    System.out.println(" ********************* run ************************8");
                    String fName = file.getName();
                    if (fName.contains("12756")) {
                        oid = 12756;
                    } else if (fName.contains("12757")) {
                        oid = 12757;
                    } else if (fName.contains("12758")) {
                        oid = 12758;
                    } else if (fName.contains("12759")) {
                        oid = 12759;
                    }

                    else if (fName.contains("12755")) {
                        oid = 12755;
                    } else if (fName.contains("12762")) {
                        oid = 12762;
                    } else if (fName.contains("12769")) {
                        oid = 12769;
                    } else if (fName.contains("12775")) {
                        oid = 12775;
                    }
                    if (runFile == oid) {
                        device = "d";
                        System.out.println(file.getName() + ", device " + device);
                        ins.process(file.getAbsolutePath());

                        device = "m";
                        System.out.println(file.getName() + ", device " + device);
                        ins.process(file.getAbsolutePath());
                    }
                }

                if (file.getName().endsWith(".csv")) {
                    System.out.println(" ********************* run ************************8");
                    String fName = file.getName();
                    if (fName.contains("12755")) {
                        oid = 12755;
                    } else if (fName.contains("12762")) {
                        oid = 12762;
                    } else if (fName.contains("12769")) {
                        oid = 12769;
                    } else if (fName.contains("12775")) {
                        oid = 12775;
                    }
                    if (runFile == oid) {
                        device = "d";
                        System.out.println(file.getName() + ", device " + device);
                        ins.processCsv(file.getAbsolutePath());

                        device = "m";
                        System.out.println(file.getName() + ", device " + device);
                        ins.processCsv(file.getAbsolutePath());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) throws Exception {
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        searchEngine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
        System.out.println("searchEngine:" + searchEngine + ", language:" + language);
        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, searchEngine, oid);

        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList.size());
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, device.equals("m"));
                    if (!testFlg) {
                        loadData(processList);
                    } else {
                        System.out.println("======测试结束=======");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel(String file, int engine, int oid) throws Exception {


        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<>();
        System.out.println("===file:" + file + ", lines:" + sheet.getPhysicalNumberOfRows());
        int cnt = 0;
        for (int n = start; n <= end; n++) {
            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }

            try {
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword")) {
                    System.out.println(" 读取到第一行，啥也不干");
                } else {

                    List<List<String>> columnsList = new ArrayList<>();
                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();

                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String sv = "0";
                    KeywordAdwordsEntity entity = keywordAdwordsEntityDAO.getNationalKwSV(URLEncoder.encode(kwStr, "UTF-8"), language);
                    if (null != entity) {
                        sv = String.valueOf(entity.getAvgMonthlySearchVolume());
                        if (null == sv || StringUtils.isBlank(sv)) {
                            sv = "0";
                        }
                    }

                    if (null != entity && entity.getAvgMonthlySearchVolume() == null) {
                        System.out.println(" ===###kwStr : " + kwStr + " ,cnt : " + cnt + " , sv " + sv);
                        sv = "0";
                    } else {

                    }


                    String date2301 = "2023-01-01";
                    List<String> dateList2301 = getMonthDays(date2301);
                    String rank2301 = getRank(cols[1]);
                    String url2301 = cols[2];
                    for (String date : dateList2301) {
                        mergeData(url2301, rank2301, date, kwStr, sv, columnsList);
                    }

                    String date2302 = "2023-02-01";
                    List<String> dateList2302 = getMonthDays(date2302);
                    String rank2302 = getRank(cols[3]);
                    String url2302 = cols[4];
                    for (String date : dateList2302) {
                        mergeData(url2302, rank2302, date, kwStr, sv, columnsList);
                    }

                    String date2303 = "2023-03-01";
                    List<String> dateList2303 = getMonthDays(date2303);
                    String rank2303 = getRank(cols[5]);
                    String url2303 = cols[6];
                    for (String date : dateList2303) {
                        mergeData(url2303, rank2303, date, kwStr, sv, columnsList);
                    }

                    String date2304 = "2023-04-01";
                    List<String> dateList2304 = getMonthDays(date2304);
                    String rank2304 = getRank(cols[7]);
                    String url2304 = cols[8];
                    for (String date : dateList2304) {
                        mergeData(url2304, rank2304, date, kwStr, sv, columnsList);
                    }

                    String date2305 = "2023-05-01";
                    List<String> dateList2305 = getMonthDays(date2305);
                    String rank2305 = getRank(cols[9]);
                    String url2305 = cols[10];
                    for (String date : dateList2305) {
                        mergeData(url2305, rank2305, date, kwStr, sv, columnsList);
                    }

                    String date2306 = "2023-06-01";
                    List<String> dateList2306 = getMonthDays(date2306);
                    String rank2306 = getRank(cols[11]);
                    String url2306 = cols[12];
                    for (String date : dateList2306) {
                        mergeData(url2306, rank2306, date, kwStr, sv, columnsList);
                    }

                    String date2307 = "2023-07-01";
                    List<String> dateList2307 = getMonthDays(date2307);
                    String rank2307 = getRank(cols[13]);
                    String url2307 = cols[14];
                    for (String date : dateList2307) {
                        mergeData(url2307, rank2307, date, kwStr, sv, columnsList);
                    }

                    String date2308 = "2023-08-01";
                    List<String> dateList2308 = getMonthDays(date2308);
                    String rank2308 = getRank(cols[15]);
                    String url2308 = cols[16];
                    for (String date : dateList2308) {
                        mergeData(url2308, rank2308, date, kwStr, sv, columnsList);
                    }


                    String date2309 = "2023-09-01";
                    List<String> dateList2309 = getMonthDays(date2309);
                    String rank2309 = getRank(cols[17]);
                    String url2309 = cols[18];
                    for (String date : dateList2309) {
                        mergeData(url2309, rank2309, date, kwStr, sv, columnsList);
                    }


                    String date2310 = "2023-10-01";
                    List<String> dateList2310 = getMonthDays(date2310);
                    String rank2310 = getRank(cols[19]);
                    String url2310 = cols[20];
                    for (String date : dateList2310) {
                        mergeData(url2310, rank2310, date, kwStr, sv, columnsList);
                    }


                    String date2311 = "2023-11-01";
                    List<String> dateList2311 = getMonthDays(date2311);
                    String rank2311 = getRank(cols[21]);
                    String url2311 = cols[22];
                    for (String date : dateList2311) {
                        mergeData(url2311, rank2311, date, kwStr, sv, columnsList);
                    }


                    String date2312 = "2023-12-01";
                    List<String> dateList2312 = getMonthDays(date2312);
                    String rank2312 = getRank(cols[23]);
                    String url2312 = cols[24];
                    for (String date : dateList2312) {
                        mergeData(url2312, rank2312, date, kwStr, sv, columnsList);
                    }


                    for (List<String> columns : columnsList) {
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);

                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                String date = vo.getQueryDate();
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return dateMap;

    }


    private void processCsv(String file) throws Exception {
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        searchEngine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
        System.out.println("searchEngine:" + searchEngine + ", language:" + language);
        Map<String, List<KeywordRankVO>> dateMap = parseExcelCsv(file, searchEngine, oid);

        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList.size());
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, device.equals("m"));
                    if (!testFlg) {
                        loadData(processList);
                    } else {
                        System.out.println("======测试结束=======");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcelCsv(String file, int engine, int oid) throws Exception {

        List<String> lines = FileUtils.readLines(new File(file), "UTF-8");

        CSVParser parser = new CSVParser(',');
        int lineCnt = 0;
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<String, List<KeywordRankVO>>();
        int cnt = 0;
        for (String line : lines) {
            cnt++;

            if (StringUtils.isBlank(line)) {
                continue;
            }

            try {
                String[] cols = parser.parseLine(line);
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword")) {
                    System.out.println(" 读取到第一行，啥也不干");
                } else {

                    List<List<String>> columnsList = new ArrayList<>();
                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();

                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String sv = "0";
                    KeywordAdwordsEntity entity = keywordAdwordsEntityDAO.getNationalKwSV(URLEncoder.encode(kwStr, "UTF-8"), language);
                    if (null != entity) {
                        sv = String.valueOf(entity.getAvgMonthlySearchVolume());
                        if (null == sv || StringUtils.isBlank(sv)) {
                            sv = "0";
                        }
                    }

                    if (null != entity && entity.getAvgMonthlySearchVolume() == null) {
                        System.out.println(" ===###kwStr : " + kwStr + " ,cnt : " + cnt + " , sv " + sv);
                        sv = "0";
                    } else {

                    }


                    String date2301 = "2023-01-01";
                    List<String> dateList2301 = getMonthDays(date2301);
                    String rank2301 = getRank(cols[1]);
                    String url2301 = cols[2];
                    for (String date : dateList2301) {
                        mergeData(url2301, rank2301, date, kwStr, sv, columnsList);
                    }

                    String date2302 = "2023-02-01";
                    List<String> dateList2302 = getMonthDays(date2302);
                    String rank2302 = getRank(cols[3]);
                    String url2302 = cols[4];
                    for (String date : dateList2302) {
                        mergeData(url2302, rank2302, date, kwStr, sv, columnsList);
                    }

                    String date2303 = "2023-03-01";
                    List<String> dateList2303 = getMonthDays(date2303);
                    String rank2303 = getRank(cols[5]);
                    String url2303 = cols[6];
                    for (String date : dateList2303) {
                        mergeData(url2303, rank2303, date, kwStr, sv, columnsList);
                    }

                    String date2304 = "2023-04-01";
                    List<String> dateList2304 = getMonthDays(date2304);
                    String rank2304 = getRank(cols[7]);
                    String url2304 = cols[8];
                    for (String date : dateList2304) {
                        mergeData(url2304, rank2304, date, kwStr, sv, columnsList);
                    }

                    String date2305 = "2023-05-01";
                    List<String> dateList2305 = getMonthDays(date2305);
                    String rank2305 = getRank(cols[9]);
                    String url2305 = cols[10];
                    for (String date : dateList2305) {
                        mergeData(url2305, rank2305, date, kwStr, sv, columnsList);
                    }

                    String date2306 = "2023-06-01";
                    List<String> dateList2306 = getMonthDays(date2306);
                    String rank2306 = getRank(cols[11]);
                    String url2306 = cols[12];
                    for (String date : dateList2306) {
                        mergeData(url2306, rank2306, date, kwStr, sv, columnsList);
                    }

                    String date2307 = "2023-07-01";
                    List<String> dateList2307 = getMonthDays(date2307);
                    String rank2307 = getRank(cols[13]);
                    String url2307 = cols[14];
                    for (String date : dateList2307) {
                        mergeData(url2307, rank2307, date, kwStr, sv, columnsList);
                    }

                    String date2308 = "2023-08-01";
                    List<String> dateList2308 = getMonthDays(date2308);
                    String rank2308 = getRank(cols[15]);
                    String url2308 = cols[16];
                    for (String date : dateList2308) {
                        mergeData(url2308, rank2308, date, kwStr, sv, columnsList);
                    }


                    String date2309 = "2023-09-01";
                    List<String> dateList2309 = getMonthDays(date2309);
                    String rank2309 = getRank(cols[17]);
                    String url2309 = cols[18];
                    for (String date : dateList2309) {
                        mergeData(url2309, rank2309, date, kwStr, sv, columnsList);
                    }


                    String date2310 = "2023-10-01";
                    List<String> dateList2310 = getMonthDays(date2310);
                    String rank2310 = getRank(cols[19]);
                    String url2310 = cols[20];
                    for (String date : dateList2310) {
                        mergeData(url2310, rank2310, date, kwStr, sv, columnsList);
                    }


                    String date2311 = "2023-11-01";
                    List<String> dateList2311 = getMonthDays(date2311);
                    String rank2311 = getRank(cols[21]);
                    String url2311 = cols[22];
                    for (String date : dateList2311) {
                        mergeData(url2311, rank2311, date, kwStr, sv, columnsList);
                    }


                    String date2312 = "2023-12-01";
                    List<String> dateList2312 = getMonthDays(date2312);
                    String rank2312 = getRank(cols[23]);
                    String url2312 = cols[24];
                    for (String date : dateList2312) {
                        mergeData(url2312, rank2312, date, kwStr, sv, columnsList);
                    }


                    for (List<String> columns : columnsList) {
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);

                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                String date = vo.getQueryDate();
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return dateMap;

    }

    public static List<String> getMonthDays(String inputDateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(inputDateString, formatter);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());

        List<String> dateList = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            dateList.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }

        return dateList;
    }


    private String getRank(String col) {
        return (col.contains("N") || StringUtils.isBlank(col) || col.equals("0") || col.contains("r") || col.contains("-")) ? "999" : col;
    }

    private void mergeData(String url, String rank, String date, String kwStr, String sv, List<List<String>> columnsList) throws ParseException {
        String location = "US-en";
        System.out.println("kwStr: " + kwStr + ", date: " + date + ", url: " + url + ", rank: " + rank + " , sv : " + sv);

        if (StringUtils.isBlank(url) || !url.startsWith("https")) {
            url = "";
        }

        if (null == sv || StringUtils.isBlank(sv)) {
            sv = "0";
        }
        List<String> col0 = new ArrayList<>();
        col0.add(location);
        col0.add(String.valueOf(searchEngine));
        col0.add(device);
        col0.add(date);
        col0.add(kwStr);
        col0.add(sv);
        col0.add(rank);
        col0.add(rank);
        col0.add(url);
        columnsList.add(col0);

    }

    @NotNull
    private ArrayList<String> getColumns1() {
        return new ArrayList<String>();
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }


    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }


    @Override
    public void loadData(List<KeywordRankVO> dataList) throws Exception {
        System.out.println("willLoad:" + dataList.size());
        try {
            threadPool.init();
            CommonUtils.initThreads(MAX_THREAD);

            processInsert(workDate, searchEngine, language, mobileRanking, dataList);

            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            // 判断 kw 是不是 数字 ：
            try {
                Double kwDou = Double.valueOf(kw);
                Integer kwint = (int) Math.ceil(kwDou);
                kw = String.valueOf(kwint);
                System.out.println(" kw : " + column.get(4) + " 是 数字类型。。。");
            } catch (Exception e) {
                // 啥也不干
            }
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Double trueRankd = Double.valueOf(column.get(6));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(7));
            Integer webRank = (int) Math.ceil(webRankd);

            String url = (column.get(8).contains("N/A") || column.get(8).equals("0")) ? "" : column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc("0");
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
//        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
//            return "";
//        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
//            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
//        } else if (url.length() <= ownDomainName.length()) {
//            throw new Exception("Wrong url, url:" + url);
//        }
        return url;
    }


}
