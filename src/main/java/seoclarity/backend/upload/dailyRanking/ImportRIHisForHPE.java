package seoclarity.backend.upload.dailyRanking;

import com.alibaba.fastjson.JSON;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.RFC4180Parser;
import com.opencsv.RFC4180ParserBuilder;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=437516099
 */
@CommonsLog
public class ImportRIHisForHPE extends ImportHistoryRankingForDailyRanking {

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',').withFirstRecordAsHeader().withSkipHeaderRecord().withQuote('\\');
    private static final String DEVICE_DESKTOP = "Desktop";

    private static final List<Integer> processDomainList = Arrays.asList(
            8887, 8904, 8888, 8889, 8890, 8891, 8892, 8917, 8906, 8907, 8908,
            8886, 8883, 8893, 8881, 8882, 8909, 8910, 8894, 8884, 8911, 8895,
            8896, 8912, 8913, 8914, 8915, 8916, 8897, 8918, 8899, 8903, 8900,
            8901, 8902, 8919, 8885, 8898, 8926, 8924, 8925, 8927
    );

    public static final Map<Integer, String> DOMAIN_ENGINE_MAP = new HashMap();

    static {
        DOMAIN_ENGINE_MAP.put(8887, "www.hpe.com/au/en/");
        DOMAIN_ENGINE_MAP.put(8904, "www.hpe.com/at/de/");
        DOMAIN_ENGINE_MAP.put(8888, "www.hpe.com/be/en/");
        DOMAIN_ENGINE_MAP.put(8889, "www.hpe.com/be/fr/");
        DOMAIN_ENGINE_MAP.put(8890, "www.hpe.com/br/pt/");
        DOMAIN_ENGINE_MAP.put(8891, "www.hpe.com/ca/en/");
        DOMAIN_ENGINE_MAP.put(8892, "www.hpe.com/ca/fr/");
        DOMAIN_ENGINE_MAP.put(8917, "www.hpe.com/cn/zh/");
        DOMAIN_ENGINE_MAP.put(8906, "www.hpe.com/cz/en/");
        DOMAIN_ENGINE_MAP.put(8907, "www.hpe.com/dk/en/");
        DOMAIN_ENGINE_MAP.put(8908, "www.hpe.com/fi/en/");
        DOMAIN_ENGINE_MAP.put(8886, "www.hpe.com/fr/fr/");
        DOMAIN_ENGINE_MAP.put(8883, "www.hpe.com/de/de/");
        DOMAIN_ENGINE_MAP.put(8893, "www.hpe.com/hk/en/");
        DOMAIN_ENGINE_MAP.put(8881, "www.hpe.com/us/en/");
        DOMAIN_ENGINE_MAP.put(8882, "www.hpe.com/in/en/");
        DOMAIN_ENGINE_MAP.put(8909, "www.hpe.com/ie/en/");
        DOMAIN_ENGINE_MAP.put(8910, "www.hpe.com/il/en/");
        DOMAIN_ENGINE_MAP.put(8894, "www.hpe.com/it/it/");
        DOMAIN_ENGINE_MAP.put(8884, "www.hpe.com/jp/ja/");
        DOMAIN_ENGINE_MAP.put(8911, "www.hpe.com/my/en/");
        DOMAIN_ENGINE_MAP.put(8895, "www.hpe.com/mx/es/");
        DOMAIN_ENGINE_MAP.put(8896, "www.hpe.com/nl/en/");
        DOMAIN_ENGINE_MAP.put(8912, "www.hpe.com/nz/en/");
        DOMAIN_ENGINE_MAP.put(8913, "www.hpe.com/no/en/");
        DOMAIN_ENGINE_MAP.put(8914, "www.hpe.com/pl/en/");
        DOMAIN_ENGINE_MAP.put(8915, "www.hpe.com/pt/en/");
        DOMAIN_ENGINE_MAP.put(8916, "www.hpe.com/ru/ru/");
        DOMAIN_ENGINE_MAP.put(8897, "www.hpe.com/sg/en/");
        DOMAIN_ENGINE_MAP.put(8918, "www.hpe.com/za/en/");
        DOMAIN_ENGINE_MAP.put(8899, "www.hpe.com/es/es/");
        DOMAIN_ENGINE_MAP.put(8903, "www.hpe.com/se/en/");
        DOMAIN_ENGINE_MAP.put(8900, "www.hpe.com/ch/de/");
        DOMAIN_ENGINE_MAP.put(8901, "www.hpe.com/ch/fr/");
        DOMAIN_ENGINE_MAP.put(8902, "www.hpe.com/tw/en/");
        DOMAIN_ENGINE_MAP.put(8919, "www.hpe.com/tr/en/");
        DOMAIN_ENGINE_MAP.put(8885, "www.hpe.com/uk/en/");
        DOMAIN_ENGINE_MAP.put(8898, "www.hpe.com/kr/ko/");

        DOMAIN_ENGINE_MAP.put(8926, "h50146.www5.hpe.com");
        DOMAIN_ENGINE_MAP.put(8924, "h17007.www1.hpe.com");
        DOMAIN_ENGINE_MAP.put(8925, "h20195.www2.hpe.com");
        DOMAIN_ENGINE_MAP.put(8927, "education.hpe.com");
        DOMAIN_ENGINE_MAP.put(8922, "buy.hpe.com/de/de/");
        DOMAIN_ENGINE_MAP.put(8921, "buy.hpe.com/in/en/");
        DOMAIN_ENGINE_MAP.put(8920, "buy.hpe.com/us/en/");


    }

    public static final Map<String, Integer> COUNTRY_DOMAIN_MAP = new HashMap();

    static {
        COUNTRY_DOMAIN_MAP.put("Austria", 8904);
        COUNTRY_DOMAIN_MAP.put("Australia", 8887);
        COUNTRY_DOMAIN_MAP.put("Belgium", 8888);
        COUNTRY_DOMAIN_MAP.put("Belgium - FR", 8889);
        COUNTRY_DOMAIN_MAP.put("Brazil", 8890);
        COUNTRY_DOMAIN_MAP.put("Canada", 8891);
        COUNTRY_DOMAIN_MAP.put("Canada - FR", 8892);
        COUNTRY_DOMAIN_MAP.put("Switzerland - DE", 8900);
        COUNTRY_DOMAIN_MAP.put("Switzerland - FR", 8901);
        COUNTRY_DOMAIN_MAP.put("Hong Kong", 8893);
        COUNTRY_DOMAIN_MAP.put("China", 8917);
        COUNTRY_DOMAIN_MAP.put("Czech Republic", 8906);
        COUNTRY_DOMAIN_MAP.put("Germany", 8883);
        COUNTRY_DOMAIN_MAP.put("Denmark", 8907);
        COUNTRY_DOMAIN_MAP.put("Spain", 8899);
        COUNTRY_DOMAIN_MAP.put("Finland", 8908);
        COUNTRY_DOMAIN_MAP.put("France", 8886);
        COUNTRY_DOMAIN_MAP.put("Ireland", 8909);
        COUNTRY_DOMAIN_MAP.put("Israel", 8910);
        COUNTRY_DOMAIN_MAP.put("India", 8882);
        COUNTRY_DOMAIN_MAP.put("Italy", 8894);
        COUNTRY_DOMAIN_MAP.put("Japan", 8884);
        COUNTRY_DOMAIN_MAP.put("South Korea", 8898);
        COUNTRY_DOMAIN_MAP.put("Mexico", 8895);
        COUNTRY_DOMAIN_MAP.put("Malaysia", 8911);
        COUNTRY_DOMAIN_MAP.put("Netherland", 8896);
        COUNTRY_DOMAIN_MAP.put("Netherlands - NL", 8896);
        COUNTRY_DOMAIN_MAP.put("Norway", 8913);
        COUNTRY_DOMAIN_MAP.put("New Zealand", 8912);
        COUNTRY_DOMAIN_MAP.put("Poland", 8914);
        COUNTRY_DOMAIN_MAP.put("Portugal", 8915);
        COUNTRY_DOMAIN_MAP.put("Russia", 8916);
        COUNTRY_DOMAIN_MAP.put("Sweden", 8903);
        COUNTRY_DOMAIN_MAP.put("Sweden - SE", 8903);
        COUNTRY_DOMAIN_MAP.put("Singapore", 8897);
        COUNTRY_DOMAIN_MAP.put("Turkey", 8919);
        COUNTRY_DOMAIN_MAP.put("Turkey - TR", 8919);
        COUNTRY_DOMAIN_MAP.put("Taiwan", 8902);
        COUNTRY_DOMAIN_MAP.put("Taiwan - ZH", 8902);
        COUNTRY_DOMAIN_MAP.put("UK", 8885);
        COUNTRY_DOMAIN_MAP.put("HPE-US", 8881);
        COUNTRY_DOMAIN_MAP.put("South Africa", 8918);
    }

    private OwnDomainEntityDAO ownDomainEntityDAO;

    public ImportRIHisForHPE(){
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    }

    private static String folder;
    private static int emptyDomainCount;

    private static Set<String> countrySet = new HashSet<>();
    private static Map<Integer, List<KeywordRankVO>> domainKeywordRankMap = new HashMap<>();

    public static void main(String[] args) {

//        folder = "D:\\upload\\kp\\HPE\\";
//        folder = "D:\\upload\\kp\\HPE\\upload\\";
//        boolean isMobile = false;

        int engine = 1;
        int language = 1;
        boolean isMobile = Boolean.valueOf(args[1]);
        folder = args[0];


        ImportRIHisForHPE ins = new ImportRIHisForHPE();

        isUseSendToQDate = true;
        isUseMonitorTable = false;
        prefix_ins_table = "local_his20191225_";

        File[] files = new File(folder).listFiles();
        List<String> fileList = new ArrayList<String>();

        for (File f : files) {
            if (!f.getName().endsWith(".csv") || f.isDirectory()) {
                continue;
            }
            fileList.add(f.getAbsolutePath());
        }

        String[] fileArray = fileList.toArray(new String[fileList.size()]);
        Arrays.sort(fileArray);
        for (String fileName : fileArray) {
            try {
                ins.process(fileName, engine, language, isMobile);
            } catch (Exception e) {
                System.out.println("=========Process file failed, file:" + fileName);
                e.printStackTrace();
            }
        }

        try {
            ins.loadData(isMobile);
        }catch (Exception e){
            log.error(" load data error ");
            e.printStackTrace();
        }


    }

    private void loadData(boolean isMobile) throws Exception{

        Date workDate = FormatUtils.toDate("2019-01-06", FormatUtils.DATE_PATTERN_2);

        for(Integer domainId : domainKeywordRankMap.keySet()){
            if(domainId.equals(8916) || domainId.equals(8917)){
                log.info("===skip 8916, 8917");
                continue;
            }
            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityById(domainId);
            if (ownDomainEntity == null) {
                System.out.println("=== domain not exist , exit !!");
                return;
            }

            int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
            int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);

            log.info("======setParameters: domainId:" + domainId + ",engineId:" + engineId + ",languageId:" + languageId + ",isMobile:" + isMobile);
            setParameters(workDate, engineId, languageId, isMobile);
            log.info("======domainKeywordRankMap size: " + domainKeywordRankMap.get(domainId).size());
            loadData(domainKeywordRankMap.get(domainId));
        }
    }

    private void process(String file, int engine, int language, boolean isMobile) throws Exception {

//        List<KeywordRankVO> dataList = new ArrayList<KeywordRankVO>();
//        List<KeywordRankVO> dataList8916 = new ArrayList<KeywordRankVO>();
//        List<KeywordRankVO> dataList8917 = new ArrayList<KeywordRankVO>();
//        List<CSVRecord> csvRecords = null;

        RFC4180Parser rfc4180Parser = new RFC4180ParserBuilder().build();
        try (CSVReader csvReader = new CSVReaderBuilder(
                new BufferedReader(new InputStreamReader(new FileInputStream(new File(file)), "utf-8"))).withCSVParser(rfc4180Parser).build()) {

//            log.info("===" + JSON.toJSONString(csvReader.readAll()));

            List<String[]> lines = csvReader.readAll();

            int index = 0;
            for (String[] line : lines) {
                index++;
                if (line == null || line.length <= 0) {
                    System.out.println("empty line, i:" + index + ", line:[" + JSON.toJSONString(line) + "]");
                    continue;
                }
                //skip header
                if (StringUtils.containsIgnoreCase(line[0], "Location")) {
                    continue;
                }

                String device = line[2];
//                device = removeDoubleQuote(device);
                if (!StringUtils.equalsIgnoreCase(device, "desktop")) {
                    System.out.println("device:" + device);
                    System.out.println("Skip mobile , idx:" + index + ", line:[" + JSON.toJSONString(line) + "]");
                    continue;
                }

                KeywordRankVO[] voList = parsertoEntity(line);

//                if (voList != null) {
//                    for (KeywordRankVO vo : voList) {
//
//                        if (vo.getDomainList().contains("8916")) {
//                            dataList8916.add(vo);
//                        } else if (vo.getDomainList().contains("8917")) {
//                            dataList8917.add(vo);
//                        } else {
//                            dataList.add(vo);
//                        }
//                    }
//                }

            }

        } catch (Exception e) {
            e.printStackTrace();
        }

//        log.info("====countrySet size: " + countrySet.size());
//        log.info("====countrySet: " + JSON.toJSONString(countrySet));

//        log.info("=total size: " + (dataList.size() + dataList8917.size() + dataList8916.size()));
//        log.info("==emptyDomainCount : " + emptyDomainCount);
//        log.info("===dataList size: " + dataList.size());
//        log.info("====dataList8917 size: " + dataList8917.size());
////        log.info("====dataList8917: " + JSON.toJSONString(dataList8917));
//        log.info("=====dataList8916 size: " + dataList8916.size());
//        log.info("======file: " + file.replace(folder, ""));

        Date workDate = FormatUtils.toDate(file.replace(folder, "").substring(0, 10), FormatUtils.DATE_PATTERN_2);
        System.out.println("=====================" + workDate + "===============================");


//        setParameters(workDate, engine, language, isMobile);
//        loadData(dataList);
//
//        setParameters(workDate, 39 , 39, isMobile);
//        loadData(dataList8916);
//
//        setParameters(workDate, 30 , 6, isMobile);
//        loadData(dataList8917);
    }

    private String removeDoubleQuote(String str) {
        if (str.startsWith("\"") && str.endsWith("\"")) {
            str = str.substring(1);
            str = str.substring(0, str.length() - 1);
//            System.out.println("===remove start and end " + str);
        }
        return str;
    }

    private KeywordRankVO[] parsertoEntity(String[] columns) {
        List<String> domainList = new ArrayList<String>();

        try {
            // garbage in garbage out
            // ignore this character
//			if (StringUtils.endsWithIgnoreCase(kw, "%c2%a0")) {
//				kw = StringUtils.removeEndIgnoreCase(kw, "%c2%a0");
//			}
//			if (StringUtils.containsIgnoreCase(kw, "%c2%a0")) {
//				kw = StringUtils.replace(kw, "%c2%a0", "+");
//				kw = URLDecoder.decode(kw.trim(), "UTF-8").trim().toLowerCase();
//				kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();
//			}

            String country = (columns[0]);
            countrySet.add(country);

            String rankingDate = (columns[3]);

            String kw = StringUtils.trim((columns[4])).toLowerCase();
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            String sv = StringUtils.trim((columns[5]));
            Integer searchVolumn = Integer.valueOf(sv);
            searchVolumn = searchVolumn < 0 ? 0 : searchVolumn;

            Integer trueRank = checkRank(StringUtils.trim((columns[6])));

            String url = getUrl(StringUtils.trim((columns[7])));
            Integer domainId = null;
            if(StringUtils.containsIgnoreCase(url , "www.hpe.com")){
                domainId = getDomainByCountry(country);
            }else {
                domainId = getDomainByUrl((columns[7]));
                if (domainId == null) {
                    domainId = getDomainByCountry(country);
                }
            }

            if(domainId == null){
                emptyDomainCount++;
                log.info("===no domain rankingDate:" + rankingDate + ",kw:" + kw + ",searchVolumn: " + searchVolumn + ",trueRank: " + trueRank + ",domainId: " + domainId
                        + ",url : " + url);
            }

            domainList.add(String.valueOf(domainId));

            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

//            if ((trueRank == 101 && StringUtils.isNotBlank(url))) {
//                System.out.println("Wong rank for url, line:[" + JSON.toJSONString(csvRecord.toMap()) + "]");
//            } else {
            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(searchVolumn.toString());
            vo1.setCpc("0");
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 101 && StringUtils.isNotBlank(url)) {
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank);
                if (rankingVo != null) {
                    list.add(rankingVo);
                } else {
                    System.out.println("=Skip wrong ranking url, kw:" + kw + ", trueRank:" + trueRank + ", line:[" + JSON.toJSONString(columns) + "]");
                }
            } else if (StringUtils.isNotBlank(url)) {
//                System.out.println("=Skip 101 ranking with url, kw:" + kw + ", trueRank:" + trueRank + ", line:[" + JSON.toJSONString(columns) + "]");
            } else {
//					System.out.println("=Skip 101 ranking, idx:" + idx + ", kw:" + kw + ", trueRank1:" + trueRank1 + ", webRank1:" + webRank1 + ", line:[" + StringUtils.join(columns, ",") + "]");
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);
//            }

            if(domainKeywordRankMap.get(domainId) != null){
                List<KeywordRankVO> keywordRankVOList = domainKeywordRankMap.get(domainId);
                keywordRankVOList.addAll(voList);
                domainKeywordRankMap.put(domainId, keywordRankVOList);
            }else {
                domainKeywordRankMap.put(domainId, voList);
            }

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:[" + JSON.toJSONString(columns) + "]");
            e.printStackTrace();
        }
        return null;
    }


    private static Integer checkRank(String str) {
        String blendedRank = StringUtils.trim(str);
        if (StringUtils.isBlank(blendedRank) || StringUtils.containsIgnoreCase(blendedRank, "NR")) {
            blendedRank = "101";
        }
        Integer trueRank = Integer.valueOf(blendedRank);
        trueRank = (trueRank <= 0 || trueRank > 101) ? 101 : trueRank;

        return trueRank;
    }

    private static Integer getDomainByUrl(String str) {
        String url = StringUtils.trim(str);
        if (StringUtils.isNotBlank(url)) {
            for (Integer oid : DOMAIN_ENGINE_MAP.keySet()) {
                if (url.contains(DOMAIN_ENGINE_MAP.get(oid))) {
                    return oid;
                }
            }

            return null;
        } else {
            return null;
        }
    }

    private static Integer getDomainByCountry(String country) {
        if (StringUtils.isNotBlank(country)) {
            return COUNTRY_DOMAIN_MAP.get(country);
        } else {
            return null;
        }
    }

    private static String getUrl(String str) throws Exception {
        String url = StringUtils.trim(str);
        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
            return "";
        }

        return url;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank) {
        if (StringUtils.isBlank(url) || trueRank == 101) {
            return null;
        }
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
//        vo.setSpecifiedWebRank(trueRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);

        return vo;
    }


    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking, List<KeywordRankVO> dataList) {

    }


}
