package seoclarity.backend.upload.dailyRanking;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.DateUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-04-12
 * @path https://www.wrike.com/open.htm?id=1093733373
 */
public class ImporRIHisFor11720 extends ImportHistoryRankingForDailyRanking {

    private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
    private SimpleDateFormat ddMMMyyyy = new SimpleDateFormat("dd-MMM-yyyy");

    private static String device = "m";
    private static int oid = 11720;

    private static boolean testFlg = false;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;

    public ImporRIHisFor11720() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
    }


    public static void main(String[] args) {
        isUseSendToQDate = true;
        isUseMonitorTable = false;
        isUseRIExistsCheck = true;
        isQueryForSv = false;
        specified_monitor_table_refix = "test11720_local_";
        prefix_ins_table = "test11720_local_";

        String filePath = "files/11720Files/";
        ImporRIHisFor11720 ins = new ImporRIHisFor11720();

        if (null != args && args.length>0){
            if (args[0].equals("t")){
                testFlg = true;
            }
        }
        try {
            for (File file : new File(filePath).listFiles()) {
                if (file.getName().endsWith(".xlsx")) {
                    System.out.println(" ********************* run ************************8");
                    ins.process(file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void process(String file) throws Exception {
        OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(oid);
        searchEngine = ScKeywordRankManager.getSearchEngineId(domainEntity);
        language = ScKeywordRankManager.getSearchLanguageId(domainEntity);

        Map<String, List<KeywordRankVO>> dateMap = parseExcel(file, searchEngine, oid);

        if (dateMap.size() > 0) {
            List<String> dateList = new ArrayList<String>(dateMap.keySet());
            Collections.sort(dateList);
            System.out.println("=== file:" + file + ", dateList:" + dateList.size());
            try {
                Thread.sleep(20000);
            } catch (Exception e) {
            }
            for (String date : dateList) {
                try {
                    List<KeywordRankVO> processList = dateMap.get(date);
                    Date workDate = yyyy_MM_dd.parse(date);
                    setParameters(workDate, searchEngine, language, device.equals("m"));
                    if (!testFlg) {
                        loadData(processList);
                    }else {
                        System.out.println("======测试结束=======");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }


    private Map<String, List<KeywordRankVO>> parseExcel(String file, int engine, int oid) throws Exception {


        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<>();
        System.out.println("===file:" + file + ", lines:" + sheet.getPhysicalNumberOfRows());
        int cnt = 0;
        for (int n = start; n <= end; n++) {
            cnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }

            try {
                String[] cols = getStringArray(row);
                // header
                if (StringUtils.equalsIgnoreCase(cols[0], "Keyword")) {
                    System.out.println(" 读取到第一行，啥也不干");
                } else {
                    if (cnt % 100 == 0 || cnt == 1) {
                        for (String str : cols) {
                            System.out.println(" 每隔100 行 打印一下 看看是啥 ： ");
                            System.out.println(str);
                        }
                    }
                    List<List<String>> columnsList = new ArrayList<>();
                    String kwStr = cols[0];
                    kwStr = StringUtils.trim(kwStr).toLowerCase();
                    String domain = "llflooring.com";
                    if (StringUtils.isBlank(kwStr)) {
                        System.out.println("=Skip empty keyword:" + kwStr);
                        continue;
                    }

                    String sv = "0";
                    KeywordAdwordsEntity entity = keywordAdwordsEntityDAO.getNationalKwSV(URLEncoder.encode(kwStr, "UTF-8"), language);
                    if (null != entity) {
                        sv = String.valueOf(entity.getAvgMonthlySearchVolume());
                    }

                    String date220409 = "2022-04-09";
                    String rank220409 = getRank(cols[1]);
                    String url220409 = cols[2];
                    mergeData(url220409, rank220409, date220409, kwStr,sv, columnsList);

                    String date220416 = "2022-04-16";
                    String rank220416 =  getRank(cols[3]);
                    String url220416 = cols[4];
                    mergeData(url220416, rank220416, date220416, kwStr,sv, columnsList);

                    String date220423 = "2022-04-23";
                    String rank220423 =  getRank(cols[5]);
                    String url220423 = cols[6];
                    mergeData(url220423, rank220423, date220423, kwStr,sv, columnsList);

                    String date220430 = "2022-04-30";
                    String rank220430 =  getRank(cols[7]);
                    String url220430 = cols[8];
                    mergeData(url220430, rank220430, date220430, kwStr,sv, columnsList);

                    String date220507 = "2022-05-07";
                    String rank220507 =  getRank(cols[9]);
                    String url220507 = cols[10];
                    mergeData(url220507, rank220507, date220507, kwStr,sv, columnsList);

                    String date220514 = "2022-05-14";
                    String rank220514 =  getRank(cols[11]);
                    String url220514 = cols[12];
                    mergeData(url220514, rank220514, date220514, kwStr,sv, columnsList);

                    String date220521 = "2022-05-21";
                    String rank220521 =  getRank(cols[13]);
                    String url220521 = cols[14];
                    mergeData(url220521, rank220521, date220521, kwStr,sv, columnsList);

                    String date220528 = "2022-05-28";
                    String rank220528 =  getRank(cols[15]);
                    String url220528 = cols[16];
                    mergeData(url220528, rank220528, date220528, kwStr,sv, columnsList);

                    String date220604 = "2022-06-04";
                    String rank220604 =  getRank(cols[17]);
                    String url220604 = cols[18];
                    mergeData(url220604, rank220604, date220604, kwStr,sv, columnsList);

                    String date220611 = "2022-06-11";
                    String rank220611 =  getRank(cols[19]);
                    String url220611 = cols[20];
                    mergeData(url220611, rank220611, date220611, kwStr,sv, columnsList);

                    String date220618 = "2022-06-18";
                    String rank220618 =  getRank(cols[21]);
                    String url220618 = cols[22];
                    mergeData(url220618, rank220618, date220618, kwStr,sv, columnsList);

                    String date220625 = "2022-06-25";
                    String rank220625 =  getRank(cols[23]);
                    String url220625 = cols[24];
                    mergeData(url220625, rank220625, date220625, kwStr,sv, columnsList);

                    String date220702 = "2022-07-02";
                    String rank220702 =  getRank(cols[25]);
                    String url220702 = cols[26];
                    mergeData(url220702, rank220702, date220702, kwStr,sv, columnsList);

                    String date220709 = "2022-07-09";
                    String rank220709 =  getRank(cols[27]);
                    String url220709 = cols[28];
                    mergeData(url220709, rank220709, date220709, kwStr,sv, columnsList);

                    String date220716 = "2022-07-16";
                    String rank220716 =  getRank(cols[29]);
                    String url220716 = cols[30];
                    mergeData(url220716, rank220716, date220716, kwStr,sv, columnsList);

                    String date220723 = "2022-07-23";
                    String rank220723 =  getRank(cols[31]);
                    String url220723 = cols[32];
                    mergeData(url220723, rank220723, date220723, kwStr,sv, columnsList);

                    String date220730 = "2022-07-30";
                    String rank220730 =  getRank(cols[33]);
                    String url220730 = cols[34];
                    mergeData(url220730, rank220730, date220730, kwStr,sv, columnsList);

                    String date220806 = "2022-08-06";
                    String rank220806 =  getRank(cols[35]);
                    String url220806 = cols[36];
                    mergeData(url220806, rank220806, date220806, kwStr,sv, columnsList);

                    String date220813 = "2022-08-13";
                    String rank220813 =  getRank(cols[37]);
                    String url220813 = cols[38];
                    mergeData(url220813, rank220813, date220813, kwStr,sv, columnsList);

                    String date220820 = "2022-08-20";
                    String rank220820 =  getRank(cols[39]);
                    String url220820 = cols[40];
                    mergeData(url220820, rank220820, date220820, kwStr,sv, columnsList);

                    String date220827 = "2022-08-27";
                    String rank220827 =  getRank(cols[41]);
                    String url220827 = cols[42];
                    mergeData(url220827, rank220827, date220827, kwStr,sv, columnsList);

                    String date220903 = "2022-09-03";
                    String rank220903 =  getRank(cols[43]);
                    String url220903 = cols[44];
                    mergeData(url220903, rank220903, date220903, kwStr,sv, columnsList);

                    String date220910 = "2022-09-10";
                    String rank220910 =  getRank(cols[45]);
                    String url220910 = cols[46];
                    mergeData(url220910, rank220910, date220910, kwStr,sv, columnsList);

                    String date220917 = "2022-09-17";
                    String rank220917 =  getRank(cols[47]);
                    String url220917 = cols[48];
                    mergeData(url220917, rank220917, date220917, kwStr,sv, columnsList);

                    String date220924 = "2022-09-24";
                    String rank220924 =  getRank(cols[49]);
                    String url220924 = cols[50];
                    mergeData(url220924, rank220924, date220924, kwStr,sv, columnsList);

                    String date221001 = "2022-10-01";
                    String rank221001 =  getRank(cols[51]);
                    String url221001 = cols[52];
                    mergeData(url221001, rank221001, date221001, kwStr,sv, columnsList);

                    String date221008 = "2022-10-08";
                    String rank221008 =  getRank(cols[53]);
                    String url221008 = cols[54];
                    mergeData(url221008, rank221008, date221008, kwStr,sv, columnsList);

                    String date221015 = "2022-10-15";
                    String rank221015 =  getRank(cols[55]);
                    String url221015 = cols[56];
                    mergeData(url221015, rank221015, date221015, kwStr,sv, columnsList);

                    String date221022 = "2022-10-22";
                    String rank221022 =  getRank(cols[57]);
                    String url221022 = cols[58];
                    mergeData(url221022, rank221022, date221022, kwStr,sv, columnsList);

                    String date221029 = "2022-10-29";
                    String rank221029 =  getRank(cols[59]);
                    String url221029 = cols[60];
                    mergeData(url221029, rank221029, date221029, kwStr,sv, columnsList);

                    String date221105 = "2022-11-05";
                    String rank221105 =  getRank(cols[61]);
                    String url221105 = cols[62];
                    mergeData(url221105, rank221105, date221105, kwStr,sv, columnsList);

                    String date221112 = "2022-11-12";
                    String rank221112 =  getRank(cols[63]);
                    String url221112 = cols[64];
                    mergeData(url221112, rank221112, date221112, kwStr,sv, columnsList);

                    String date221119 = "2022-11-19";
                    String rank221119 =  getRank(cols[65]);
                    String url221119 = cols[66];
                    mergeData(url221119, rank221119, date221119, kwStr,sv, columnsList);

                    String date221126 = "2022-11-26";
                    String rank221126 =  getRank(cols[67]);
                    String url221126 = cols[68];
                    mergeData(url221126, rank221126, date221126, kwStr,sv, columnsList);

                    String date221203 = "2022-12-03";
                    String rank221203 =  getRank(cols[69]);
                    String url221203 = cols[70];
                    mergeData(url221203, rank221203, date221203, kwStr,sv, columnsList);

                    String date221210 = "2022-12-10";
                    String rank221210 =  getRank(cols[71]);
                    String url221210 = cols[72];
                    mergeData(url221210, rank221210, date221210, kwStr,sv, columnsList);

                    String date221217 = "2022-12-17";
                    String rank221217 =  getRank(cols[73]);
                    String url221217 = cols[74];
                    mergeData(url221217, rank221217, date221217, kwStr,sv, columnsList);

                    String date221224 = "2022-12-24";
                    String rank221224 =  getRank(cols[75]);
                    String url221224 = cols[76];
                    mergeData(url221224, rank221224, date221224, kwStr,sv, columnsList);

                    String date221231 = "2022-12-31";
                    String rank221231 =  getRank(cols[77]);
                    String url221231 = cols[78];
                    mergeData(url221231, rank221231, date221231, kwStr,sv, columnsList);

                    String date230107 = "2023-01-07";
                    String rank230107 =  getRank(cols[79]);
                    String url230107 = cols[80];
                    mergeData(url230107, rank230107, date230107, kwStr,sv, columnsList);

                    String date230114 = "2023-01-14";
                    String rank230114 =  getRank(cols[81]);
                    String url230114 = cols[82];
                    mergeData(url230114, rank230114, date230114, kwStr,sv, columnsList);

                    String date230121 = "2023-01-21";
                    String rank230121 =  getRank(cols[83]);
                    String url230121 = cols[84];
                    mergeData(url230121, rank230121, date230121, kwStr,sv, columnsList);

                    String date230128 = "2023-01-28";
                    String rank230128 =  getRank(cols[85]);
                    String url230128 = cols[86];
                    mergeData(url230128, rank230128, date230128, kwStr,sv, columnsList);

                    String date230204 = "2023-02-04";
                    String rank230204 =  getRank(cols[87]);
                    String url230204 = cols[88];
                    mergeData(url230204, rank230204, date230204, kwStr,sv, columnsList);

                    String date230211 = "2023-02-11";
                    String rank230211 =  getRank(cols[89]);
                    String url230211 = cols[90];
                    mergeData(url230211, rank230211, date230211, kwStr,sv, columnsList);

                    String date230218 = "2023-02-18";
                    String rank230218 =  getRank(cols[91]);
                    String url230218 = cols[92];
                    mergeData(url230218, rank230218, date230218, kwStr,sv, columnsList);

                    String date230225 = "2023-02-25";
                    String rank230225 =  getRank(cols[93]);
                    String url230225 = cols[94];
                    mergeData(url230225, rank230225, date230225, kwStr,sv, columnsList);

                    String date230304 = "2023-03-04";
                    String rank230304 =  getRank(cols[95]);
                    String url230304 = cols[96];
                    mergeData(url230304, rank230304, date230304, kwStr,sv, columnsList);

                    String date230311 = "2023-03-11";
                    String rank230311 =  getRank(cols[97]);
                    String url230311 = cols[98];
                    mergeData(url230311, rank230311, date230311, kwStr,sv, columnsList);

                    String date230318 = "2023-03-18";
                    String rank230318 =  getRank(cols[99]);
                    String url230318 = cols[100];
                    mergeData(url230318, rank230318, date230318, kwStr,sv, columnsList);

                    String date230325 = "2023-03-25";
                    String rank230325 =  getRank(cols[101]);
                    String url230325 = cols[102];
                    mergeData(url230325, rank230325, date230325, kwStr,sv, columnsList);



                    for (List<String> columns : columnsList) {
                        KeywordRankVO[] vos = parsertoEntity(columns, oid);

                        if (vos != null && vos.length > 0) {
                            for (KeywordRankVO vo : vos) {
                                String date = vo.getQueryDate();
                                if (dateMap.containsKey(date)) {
                                    dateMap.get(date).add(vo);
                                } else {
                                    List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();
                                    voList.add(vo);
                                    dateMap.put(date, voList);
                                }
                            }
                        }
                    }


                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return dateMap;

    }

    private String getRank(String col) {
       return  (col.contains("N") || StringUtils.isBlank(col) || col.equals("0") || col.contains("r")) ? "999" : col;
    }

    private void mergeData(String url, String rank, String date, String kwStr,String sv, List<List<String>> columnsList) throws ParseException {
        String location = "US-en";

        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(yyyy_MM_dd.parse(date));
        Date day =calendar1.getTime();
        day =   DateUtils.getLastSunday(day);
        date = yyyy_MM_dd.format(day);

        if (StringUtils.isBlank(url) || !url.startsWith("https")){
            url = "";
        }

        List<String> col0 = new ArrayList<>();
        col0.add(location);
        col0.add(String.valueOf(searchEngine));
        col0.add(device);
        col0.add(date);
        col0.add(kwStr);
        col0.add(sv);
        col0.add(rank);
        col0.add(rank);
        col0.add(url);
        columnsList.add(col0);

    }

    @NotNull
    private ArrayList<String> getColumns1() {
        return new ArrayList<String>();
    }

    private String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null ? "" : row.getCell(i).toString();
        }
        return lines;
    }


    @Override
    void setParameters(Date workdate, int searchEngine, int language, boolean mobileRanking) {
        this.workDate = workdate;
        this.searchEngine = searchEngine;
        this.language = language;
        this.mobileRanking = mobileRanking;
    }


    @Override
    public void loadData(List<KeywordRankVO> dataList) throws Exception {
        System.out.println("willLoad:" + dataList.size());
        try {
            threadPool.init();
            CommonUtils.initThreads(MAX_THREAD);

            processInsert(workDate, searchEngine, language, mobileRanking, dataList);

            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    void getInsertVoList(Date workdate, int searchEngine, int language, boolean mobileRanking,
                         List<KeywordRankVO> dataList) {
        // check for duplicated keywords
        Map<String, KeywordRankVO> checkMap = new HashMap<String, KeywordRankVO>();
        dataList.stream().forEach((vo) -> {
            String key = vo.getId().toString();
            if (checkMap.get(key) == null) {
                checkMap.put(key, vo);
            } else {
                KeywordRankVO vo0 = checkMap.get(key);
                KeywordRankVO mergedVo = mergeRanking(vo0, vo);
                checkMap.put(key, mergedVo);
            }
        });
        dataList.clear();
        dataList.addAll(checkMap.values());
    }

    private KeywordRankVO[] parsertoEntity(List<String> column, int processDomainId) {
        try {
            List<String> domainList = new ArrayList<String>();
            String kw = StringUtils.trim(column.get(4)).toLowerCase();
            // 判断 kw 是不是 数字 ：
            try {
                Double kwDou = Double.valueOf(kw);
                Integer kwint = (int) Math.ceil(kwDou);
                kw = String.valueOf(kwint);
                System.out.println(" kw : " + column.get(4) + " 是 数字类型。。。");
            } catch (Exception e) {
                // 啥也不干
            }
            kw = URLEncoder.encode(kw, "UTF-8").toLowerCase();

            Double trueRankd = Double.valueOf(column.get(6));
            Integer trueRank = (int) Math.ceil(trueRankd);
            Double webRankd = Double.valueOf(column.get(7));
            Integer webRank = (int) Math.ceil(webRankd);


            String url = (column.get(8).contains("N/A") || column.get(8).equals("0")) ? "" : column.get(8);
            String rankingDate = (column.get(3));
            // rank webrank url not null or else they just upload to  info_rable
            if (StringUtils.isBlank(url) || StringUtils.isBlank(column.get(6)) || StringUtils.isBlank(column.get(7))) {
                trueRank = 999;
                webRank = 999;
            }
            String sv = column.get(5);
            domainList.add(String.valueOf(processDomainId));
            List<KeywordRankVO> voList = new ArrayList<KeywordRankVO>();

            KeywordRankVO vo1 = new KeywordRankVO();
            vo1.setKeyword(kw);
            vo1.setQueryDate(rankingDate);
            vo1.setSendToQDate(
                    FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankingDate, FormatUtils.DATE_PATTERN_2)));
            vo1.setDomainList(domainList);
            vo1.setSearchVol(sv);
            vo1.setCpc("0");
            List<KeywordRankEntityVO> list = new ArrayList<KeywordRankEntityVO>();
            if (trueRank < 999 && StringUtils.isNotBlank(url) && webRank < 999) {
                System.out.println("== in info and detail table, kw:" + kw + ", trueRank:" + trueRank + ", line:" + column);
                KeywordRankEntityVO rankingVo = parseForRankingList(url, trueRank, webRank);
                list.add(rankingVo);
            }
            vo1.setKeywordRankEntityVOs(list);
            voList.add(vo1);

            return voList.toArray(new KeywordRankVO[voList.size()]);
        } catch (Exception e) {
            System.out.println("Parse entity wrong. line:" + column);
            e.printStackTrace();
        }
        return null;
    }

    private KeywordRankEntityVO parseForRankingList(String url, int trueRank, int webRank) {
        KeywordRankEntityVO vo = new KeywordRankEntityVO();
        vo.setLandingPage(url);
        vo.setRank(trueRank);
        vo.setSpecifiedWebRank(webRank);
        vo.setType(KeywordRankEntityVO.TYPE_WEB_RESOURCE);
        return vo;
    }

    protected static String getUrl(String str, String ownDomainName) throws Exception {
        String url = StringUtils.trim(str);
//        if (StringUtils.isBlank(url) || StringUtils.startsWithIgnoreCase(url, "Not")) {
//            return "";
//        } else if (!StringUtils.containsIgnoreCase(url, ownDomainName)) {
//            throw new Exception("Url not belong to onw domain, own domain:" + ownDomainName + ", url:" + url);
//        } else if (url.length() <= ownDomainName.length()) {
//            throw new Exception("Wrong url, url:" + url);
//        }
        return url;
    }


}
