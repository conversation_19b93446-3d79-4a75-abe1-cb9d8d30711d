package seoclarity.backend.upload;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.amazonaws.regions.Regions;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordAdwordsEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordAdwordsEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.service.KeywordNameRelService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

// https://www.wrike.com/open.htm?id=*********
// https://www.wrike.com/open.htm?id=*********
public class ExtractRedVenturesKeywords {
	private static final SimpleDateFormat SDF_YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
	private static final SimpleDateFormat SDF_YYYY_MM_DD = new SimpleDateFormat("yyyy-MM-dd");
	
	private static final String COMPANY_NAME = "Red Ventures LLC";
	
	private static final String OUTPUT_FOLDER = "/home/<USER>/"; // TODO
	private static final String RV_KEYWORD_OUTPUT_FILENAME_PREFIX = "RedVenture_UniqueKeywords_"; // TODO
	private static final String RV_KEYWORD_TAG_OUTPUT_FILENAME_PREFIX = "RedVenture_KeywordTags_"; // TODO
	private static final String RV_DOMAIN_INFO_OUTPUT_FILENAME_PREFIX = "RedVentures_ManagedDomains_"; // TODO
	private static final String RV_OUTPUT_FILENAME_SUFFFIX = ".txt"; // TODO
	
	private static final String RV_S3_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
	private static final String RV_S3_SECRET_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
	private static final String RV_S3_ROLE_ARN ="arn:aws:iam::054715827583:role/opticon_seoclarity_export_s3_role"; // TODO
	public static final String RV_S3_BUCKET_NAME = "opticon-seoclarity-export-bucket-production-us-east-1"; // TODO
	private static final String RV_S3_BUCKET_NAME_ADDTIONAL = "opticon-seoclarity-health-data-production-us-east-1"; // TODO
	private static final String RV_S3_BUCKET_KEY_PREFIX = "keyword_tags"; // TODO
	private static final String RV_S3_BUCKET_KEY_PREFIX_FOR_DOMAIN_EXTRACT = "domain_info";
	public static final String KEY_DELIMITER = "/";
	private static final int S3_SESSION_DURATION_SECONDS = 3600;
	private static final int S3_RETRY_COUNT = 10;
	
	private static final int RV_FTP_DOMAIN_ID = 8879;
	private static final String RV_FTP_FOLDER = "/home/<USER>/8879";
	private static final String FTP_FOLDER_FOR_DOMAIN_EXTRACT = "/home/<USER>/8879/domain_info/";
	private static final int FTP_RETRY_COUNT = 30;
	
	private static final int CITY_ID = 0;
	private static final String ENGINE_SPLIT = "!_!";
	private static final String OUTPUT_SPLIT = "\t";

	private  static LogglyVO logglyVO = new LogglyVO();
	private static int totalCnt = 0;

	// https://www.wrike.com/open.htm?id=*********
	private static final int INDEED_FTP_DOMAIN_ID = 8711;
	private static final String INDEED_FTP_FOLDER = "/home/<USER>/8711";
	private static final String INDEED_KEYWORD_TAG_OUTPUT_FILENAME_SUFFFIX = "_Keyword+tags";
	private static final String INDEED_OUTPUT_FILENAME_SUFFFIX = ".txt";
	private static final int[] INDEED_OID_ARR = new int[] {
			9200,9201,9202,8754,9204,9203,9240,8755,8756,8757,9230,9205,9194,9238,9206,9233,9219,8758,9207,9232,9224,9028,8760,8759,9222,9223,9197,9208,9227,8762,9193,8763,9209,9239,9235,9225,8764,9210,9234,8765,9211,9212,9242,9236,9213,9214,9220,9196,9221,9229,9231,9199,9226,9215,9195,9216,9192,9217,9228,8761,8711,9106,9241,9237,9198,9218
	};
	
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private KeywordEntityDAO keywordEntityDAO;
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private SeoClarityKeywordAdwordsEntityDAO seoClarityKeywordAdwordsEntityDAO;
	private ScKeywordRankManager scKeywordRankManager;
	private KeywordNameRelService keywordNameRelService = new KeywordNameRelService();
	
	public ExtractRedVenturesKeywords () {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		seoClarityKeywordAdwordsEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordAdwordsEntityDAO");
		scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
	}
	
	public static void main(String[] args) throws Exception {
		ExtractRedVenturesKeywords ins = new ExtractRedVenturesKeywords();
		String type = null;
		if (args != null && args.length > 0) {
			type = args[0];
		}
		if ("domainInfo".equalsIgnoreCase(type)) {
			ins.exportDomainInfo();
		}else {
			ins.exportDomainInfo();
			ins.exportRedVentureKWTags();
			ins.exportIndeedKWTags(); // https://www.wrike.com/open.htm?id=*********
		}
		// ins.exportUniqueKWs();
	}
	
	private void exportDomainInfo() {
		System.out.println("===========Start to export domain info===========");
		Date now = new Date();
		String currentDateYyyymmdd = SDF_YYYYMMDD.format(now);
		List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(COMPANY_NAME);
		System.out.println(" ======domainList:" + domainList.size() + ", currentDateYyyymmdd:" + currentDateYyyymmdd);
		List<Integer> idList = new ArrayList<Integer>();
		for (OwnDomainEntity domain : domainList) {
			idList.add(domain.getId());
		}
		
		List<Map<String, Object>> resultList = ownDomainEntityDAO.getDomainCountryInfo(idList);
		
		String outputFileName = OUTPUT_FOLDER + RV_DOMAIN_INFO_OUTPUT_FILENAME_PREFIX + currentDateYyyymmdd + ".xlsx";
		String mailDescription = "ExtractRedVenturesDomainInfo";
		
		try {
			OutputStream out = new FileOutputStream(outputFileName);
			XSSFWorkbook wb = new XSSFWorkbook();
			List<String[]> rows = new ArrayList<String[]>();
			
			rows.add(new String[] {
					"Domain ID", "Domain Name", "Country provisioned"
			});
			for (Map<String, Object> map : resultList) {
				String id = map.get("id").toString();
				String domain = map.get("domain").toString();
				String countryDisplayName = map.get("countryDisplayName").toString();
				rows.add(new String[] {
						id, domain, countryDisplayName
				});
			}
			
			XSSFSheet sheet = wb.createSheet();
			createXSSFRow(sheet, rows);
			wb.write(out);
			out.close();
			
			System.out.println("=Export file:" + outputFileName + " s3Bucket:" + RV_S3_BUCKET_KEY_PREFIX_FOR_DOMAIN_EXTRACT + 
				" ftpDomainId:" + RV_FTP_DOMAIN_ID + " ftpFolder:" + FTP_FOLDER_FOR_DOMAIN_EXTRACT);
			saveToClient(currentDateYyyymmdd, outputFileName, RV_S3_BUCKET_KEY_PREFIX_FOR_DOMAIN_EXTRACT, RV_FTP_DOMAIN_ID, FTP_FOLDER_FOR_DOMAIN_EXTRACT, mailDescription);
		} catch (Exception e) {
			e.printStackTrace();
			ZeptoMailSenderComponent.sendEmailReport(new Date(), mailDescription + " Error", mailDescription + " error occurred", e.getMessage());
		}
	}
	
	private void saveToClient(String currentDateYyyymmdd, String outputFileName, String s3BucketName, int ftpDomainId, String ftpFolder, String mailDescription) throws Exception{
		GZipUtil.zip(outputFileName, outputFileName + GZipUtil.GZFile_POSTFIX);
		String fullPathOutputZipFileName = outputFileName + GZipUtil.GZFile_POSTFIX;
		
		boolean savedToS3 = false;
		if (StringUtils.isNotEmpty(s3BucketName)) {
			savedToS3 = saveFileToS3(currentDateYyyymmdd, fullPathOutputZipFileName, s3BucketName);
		} else {
			System.out.println(" =NotSendToS3:" + outputFileName);
			savedToS3 = true;
		}
		
		boolean savedToFTP = FTPUtils.saveFileToFTPWithRetryCount(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, ftpDomainId, 
			fullPathOutputZipFileName, ftpFolder, FTP_RETRY_COUNT);
		if (savedToS3 && savedToFTP) {
			if (StringUtils.isNotEmpty(s3BucketName)) {
				ZeptoMailSenderComponent.sendEmailReport(new Date(), mailDescription + " Success", mailDescription + " sent file to S3 and FTP", null);
			}
			try {
				FileUtils.deleteQuietly(new File(outputFileName));
				FileUtils.deleteQuietly(new File(fullPathOutputZipFileName));
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			ZeptoMailSenderComponent.sendEmailReport(new Date(), mailDescription + " sent file to S3/FTP Error",
				mailDescription + " send to s3:" + savedToS3 + ", send to FTP:" + savedToFTP, null);
		}
	}
	
	private void exportIndeedKWTags() throws Exception {
		String mailDescription = "ExtractIndeedKeywordTags";
		try {
			System.out.println(" ======IndeedDomainList:" + INDEED_OID_ARR.length);
			Date now = new Date();
			String currentDateYyyymmdd = SDF_YYYYMMDD.format(now);
			String processDate = SDF_YYYY_MM_DD.format(now);
			for (int ownDomainId : INDEED_OID_ARR) {
				OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityById(ownDomainId);
				String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
				String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

				logglyVO.setoId(String.valueOf(ownDomainId));
				logglyVO.setName("ExtractRedVenturesKeywords");
				logglyVO.setDevice(ownDomainEntity.isMobileDomain()?"m":"d");

				logglyVO.setpDate(pDate);
				List<String> groupList = new ArrayList<>();
				groupList.add(LogglyVO.GROUP_KEYWORD_AND_TAG_EXTRACT);
				logglyVO.setGroups(groupList);

				String outputFileName = OUTPUT_FOLDER + ownDomainEntity.getDomain() + INDEED_KEYWORD_TAG_OUTPUT_FILENAME_SUFFFIX + currentDateYyyymmdd + 
						INDEED_OUTPUT_FILENAME_SUFFFIX;
				List<String> headerList = new ArrayList<String>();
				StringBuffer headerLine = new StringBuffer();
				headerLine.append("Keyword").append(OUTPUT_SPLIT);
				headerLine.append("Tag").append(OUTPUT_SPLIT);
				headerLine.append("Date");
				headerList.add(headerLine.toString());
				FileUtils.writeLines(new File(outputFileName), "UTF-8", headerList, true);
				
				List<KeywordEntity> keywordList = keywordEntityDAO.geKeywordTagList(ownDomainId);
				System.out.println(" ======ProcesssingIndeedOID:" + ownDomainId + " domain:" + ownDomainEntity.getDomain() + " processDate:" + processDate + 
					" cnt:" + (keywordList != null ? keywordList.size() : 0) + " localFile:" + outputFileName + " s3Bucket:" + RV_S3_BUCKET_KEY_PREFIX + 
					" ftpDomainId:" + INDEED_FTP_DOMAIN_ID + " ftpFolder:" + INDEED_FTP_FOLDER);
				totalCnt = keywordList.size();
				if (keywordList != null && keywordList.size() > 0) {
					List<String> outputList = new ArrayList<String>();
					for (KeywordEntity keywordEntity : keywordList) {
						String keywordName = StringEscapeUtils.unescapeHtml(URLDecoder.decode(keywordEntity.getKeywordName(), "UTF-8"));
						StringBuffer line = new StringBuffer();
						line.append(keywordName).append(OUTPUT_SPLIT);
						line.append(keywordEntity.getTagName()).append(OUTPUT_SPLIT);
						line.append(processDate);
						outputList.add(line.toString());
					}
					FileUtils.writeLines(new File(outputFileName), "UTF-8", outputList, true);
				}
				saveToClient(currentDateYyyymmdd, outputFileName, null, INDEED_FTP_DOMAIN_ID, INDEED_FTP_FOLDER, mailDescription);
				logglyVO.setStatus(LogglyVO.STATUS_OK);
				logglyVO.setsTime(stTime);
				logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
				logglyVO.setRows(String.valueOf(totalCnt));
				String body = new Gson().toJson(logglyVO);
				LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
			}
		} catch (Exception exp) {
			exp.printStackTrace();

			logglyVO.setStatus(LogglyVO.STATUS_NG);
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
			ZeptoMailSenderComponent.sendEmailReport(new Date(), mailDescription + " Error", mailDescription + " error occurred", exp.getMessage());
		}
	}
	
	private void exportRedVentureKWTags() throws Exception {
		String mailDescription = "ExtractRedVenturesKeywordTags";
		try {
			List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(COMPANY_NAME);
			System.out.println(" ======RVdomainList:" + domainList.size());
			
			Date now = new Date();
			String currentDateYyyymmdd = SDF_YYYYMMDD.format(now);
			String outputFileName = OUTPUT_FOLDER + RV_KEYWORD_TAG_OUTPUT_FILENAME_PREFIX + currentDateYyyymmdd + RV_OUTPUT_FILENAME_SUFFFIX;
			int totalCnt = 0;
			
			String processDate = SDF_YYYY_MM_DD.format(now);
			writeRedVentureHeaderToFile(outputFileName);
			
			for (OwnDomainEntity ownDomainEntity : domainList) {
				int ownDomainId = ownDomainEntity.getId();

				String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
				String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

				logglyVO.setoId(String.valueOf(ownDomainId));
				logglyVO.setName("ExtractRedVenturesKeywordTags");
				logglyVO.setDevice(ownDomainEntity.isMobileDomain()?"m":"d");

				logglyVO.setpDate(pDate);
				List<String> groupList = new ArrayList<>();
				groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
				logglyVO.setGroups(groupList);

				List<KeywordEntity> keywordList = keywordEntityDAO.geKeywordTagListV2(ownDomainId);
				totalCnt = keywordList.size();
				System.out.println(" ==ExportOID:" + ownDomainId + " cnt:" + (keywordList != null ? keywordList.size() : 0));
				if (keywordList != null && keywordList.size() > 0) {
					List<String> outputList = new ArrayList<String>();
					for (KeywordEntity keywordEntity : keywordList) {
						String keywordName = keywordEntity.getKeywordName();
						keywordName = StringEscapeUtils.unescapeHtml(URLDecoder.decode(keywordName, "UTF-8"));
						
						StringBuffer line = new StringBuffer();
						line.append(processDate).append(OUTPUT_SPLIT);
						line.append(ownDomainId).append(OUTPUT_SPLIT);
						line.append(keywordName).append(OUTPUT_SPLIT);
						line.append(keywordEntity.getTagName()).append(OUTPUT_SPLIT);
						line.append(keywordEntity.getRankcheckId()).append(OUTPUT_SPLIT);
						line.append(keywordEntity.getCreateDate() != null ? SDF_YYYY_MM_DD.format(keywordEntity.getCreateDate()) : keywordEntity.getCreateDate());
						outputList.add(line.toString());
						totalCnt++;
					}
					FileUtils.writeLines(new File(outputFileName), "UTF-8", outputList, true);
				}
				logglyVO.setStatus(LogglyVO.STATUS_OK);
				logglyVO.setsTime(stTime);
				logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
				logglyVO.setRows(String.valueOf(totalCnt));
				String body = new Gson().toJson(logglyVO);
				LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

			}
			System.out.println(" ======TotalExportLines:" + totalCnt + " localFile:" + outputFileName + " processDate:" + processDate + 
				" s3Bucket:" + RV_S3_BUCKET_KEY_PREFIX + " ftpDomainId:" + RV_FTP_DOMAIN_ID + " ftpFolder:" + RV_FTP_FOLDER);
			saveToClient(currentDateYyyymmdd, outputFileName, RV_S3_BUCKET_KEY_PREFIX, RV_FTP_DOMAIN_ID, RV_FTP_FOLDER, mailDescription);
		} catch (Exception exp) {
			exp.printStackTrace();
			logglyVO.setStatus(LogglyVO.STATUS_NG);
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
			ZeptoMailSenderComponent.sendEmailReport(new Date(), mailDescription + " Error", mailDescription + " error occurred", exp.getMessage());
		}
	}
	
	private void writeRedVentureHeaderToFile(String outputFileName) throws Exception {
		List<String> headerList = new ArrayList<String>();
		StringBuffer line = new StringBuffer();
		line.append("ProcessDate").append(OUTPUT_SPLIT);
		line.append("DomainId").append(OUTPUT_SPLIT);
		line.append("KeywordName").append(OUTPUT_SPLIT);
		line.append("TagName").append(OUTPUT_SPLIT);
		line.append("RankcheckId").append(OUTPUT_SPLIT);
		line.append("CreateDate");
		headerList.add(line.toString());
		FileUtils.writeLines(new File(outputFileName), "UTF-8", headerList, true);
	}
	
	private static boolean saveFileToS3(String currentDateYyyymmdd, String fullPathFileName, String bucketKey) {
		return saveFileToS3ByKey(currentDateYyyymmdd, fullPathFileName, bucketKey);
	}
	
	public static boolean saveFileToS3ByKey(String currentDateYyyymmdd, String fullPathFileName, String s3KeyPrefix) {
		// <s3 bucket> /keyword_tags/ 2020 / 01 / 03 / <actual file name>
		String year = currentDateYyyymmdd.substring(0, 4);
		String month = currentDateYyyymmdd.substring(4, 6);
		String day = currentDateYyyymmdd.substring(6);
		
		String fileName = fullPathFileName.substring(fullPathFileName.lastIndexOf(File.separator) + 1);
		String s3Key = s3KeyPrefix + KEY_DELIMITER + year + KEY_DELIMITER + month + KEY_DELIMITER + day + KEY_DELIMITER + fileName;
		System.out.println(" ==saveFileToS3ByKey:" + s3Key + " file:" + fullPathFileName);
		
		return AmazonS3UploadTool.sendFileToS3WithRoleArn(RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY, Regions.US_EAST_1, RV_S3_ROLE_ARN, RV_S3_BUCKET_NAME, s3Key, 
			fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
	}
	
	public static boolean saveFileToS3ByKeyByTransferManager(String currentDateYyyymmdd, String fullPathFileName, String s3KeyPrefix, boolean isAddtionalBucket) {
		// <s3 bucket> /keyword_tags/ 2020 / 01 / 03 / <actual file name>
		String year = currentDateYyyymmdd.substring(0, 4);
		String month = currentDateYyyymmdd.substring(4, 6);
		String day = currentDateYyyymmdd.substring(6);
		
		String fileName = fullPathFileName.substring(fullPathFileName.lastIndexOf(File.separator) + 1);
		String s3Key = s3KeyPrefix + KEY_DELIMITER + year + KEY_DELIMITER + month + KEY_DELIMITER + day + KEY_DELIMITER + fileName;
		String bucketName = !isAddtionalBucket ? RV_S3_BUCKET_NAME : RV_S3_BUCKET_NAME_ADDTIONAL;
		
		if (isAddtionalBucket) {
			System.out.println(" ==saveFileToS3ByKeyByTransferManager:" + s3Key + " file:" + fullPathFileName);
			return AmazonS3UploadTool.sendFileToS3ByTransferManager(RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY, Regions.US_EAST_1, bucketName, s3Key, 
					fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
		}
		
		System.out.println(" ==sendFileToS3WithRoleArnByTransferManager:" + RV_S3_ROLE_ARN + " file:" + fullPathFileName);
		return AmazonS3UploadTool.sendFileToS3WithRoleArnByTransferManager(RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY, Regions.US_EAST_1, RV_S3_ROLE_ARN, bucketName, s3Key, 
			fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
	}
	
	public static boolean saveFileToS3ByKeyByTransferManager(String currentDateYyyymmdd, String fullPathFileName, String s3KeyPrefix, String s3BucketName) {
		// <s3 bucket> /keyword_tags/ 2020 / 01 / 03 / <actual file name>
		String year = currentDateYyyymmdd.substring(0, 4);
		String month = currentDateYyyymmdd.substring(4, 6);
		String day = currentDateYyyymmdd.substring(6);
		
		String fileName = fullPathFileName.substring(fullPathFileName.lastIndexOf(File.separator) + 1);
		String s3Key = s3KeyPrefix + KEY_DELIMITER + year + KEY_DELIMITER + month + KEY_DELIMITER + day + KEY_DELIMITER + fileName;
		
		System.out.println(" ==sendFileToS3WithRoleArnByTransferManager:" + RV_S3_ROLE_ARN + " file:" + fullPathFileName);
		return AmazonS3UploadTool.sendFileToS3WithRoleArnByTransferManager(RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY, Regions.US_EAST_1, RV_S3_ROLE_ARN, s3BucketName, s3Key, 
			fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
	}
	
	public static boolean saveFileToS3ByKeyByTransferManagerForTest(String currentDateYyyymmdd, String fullPathFileName, String s3KeyPrefix) {
		// <s3 bucket> /keyword_tags/ 2020 / 01 / 03 / <actual file name>
		String year = currentDateYyyymmdd.substring(0, 4);
		String month = currentDateYyyymmdd.substring(4, 6);
		String day = currentDateYyyymmdd.substring(6);
		
		String fileName = fullPathFileName.substring(fullPathFileName.lastIndexOf(File.separator) + 1);
		String s3Key = "top_keywords_test" + KEY_DELIMITER + year + KEY_DELIMITER + month + KEY_DELIMITER + day + KEY_DELIMITER + fileName;
		System.out.println(" ==Save file to s3 Key:" + s3Key + " file:" + fullPathFileName);
		String bucketName = RV_S3_BUCKET_NAME;
		
		return AmazonS3UploadTool.sendFileToS3WithRoleArnByTransferManager(RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY, Regions.US_EAST_1, RV_S3_ROLE_ARN, bucketName, s3Key, 
			fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
	}
	
	public static boolean saveFileToS3ByKeyByTransferManager(String fileName, String fullPathFileName, String bucketName, 
			String accessKey, String secretKey) {
		// <s3 bucket> /keyword_tags/ 2020 / 01 / 03 / <actual file name>
		System.out.println(" ==sendFileToS3ByTransferManager:" + fullPathFileName);
		return AmazonS3UploadTool.sendFileToS3ByTransferManager(accessKey, secretKey, Regions.EU_WEST_1, bucketName, fileName, 
			fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
	}
	
	public static boolean saveFileToS3ForUntagByTransferManager(String currentDateYyyyWw, String fullPathFileName, String s3KeyPrefix) {
		//s3://opticon-seoclarity-export-bucket-production-us-east-1/keyword_untagged/YYYY/WW
		String fileName = fullPathFileName.substring(fullPathFileName.lastIndexOf(File.separator) + 1);
		String s3Key = s3KeyPrefix + KEY_DELIMITER + currentDateYyyyWw + KEY_DELIMITER + fileName;
		System.out.println(" ==saveFileToS3ForUntagByTransferManager:" + s3Key + " file:" + fullPathFileName);
		return AmazonS3UploadTool.sendFileToS3WithRoleArnByTransferManager(RV_S3_ACCESS_KEY, RV_S3_SECRET_KEY, Regions.US_EAST_1, RV_S3_ROLE_ARN, RV_S3_BUCKET_NAME, s3Key, 
			fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
	}
	
	private void createXSSFRow(XSSFSheet sheet, List<String[]> rows) {
		for (int rowIdx = 0; rowIdx < rows.size(); rowIdx++) {
			XSSFRow row = sheet.createRow(rowIdx);
			int cellIdx = 0;
			XSSFCell cell = row.createCell(cellIdx);
			cell.setCellValue(rows.get(rowIdx)[cellIdx]);
			cellIdx++;
			
			XSSFCell cell2 = row.createCell(cellIdx);
			cell2.setCellValue(rows.get(rowIdx)[cellIdx]);
			cellIdx++;
			
			XSSFCell cell3 = row.createCell(cellIdx);
			cell3.setCellValue(rows.get(rowIdx)[cellIdx]);
			cellIdx++;
		}
	}
	
	private void exportUniqueKWs() throws Exception {
		List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(COMPANY_NAME);
		Map<String, List<Integer>> engineDomainMap = new HashMap<String, List<Integer>>();
		for (OwnDomainEntity ownDomainEntity : domainList) {
			String engine = ownDomainEntity.getSearchEngine();
			String language = ownDomainEntity.getLanguage();
			int oid = ownDomainEntity.getId();
			int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
			int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
			System.out.println(" OID:" + oid + " engine:" + engine + " language:" + language + " engineId:" + engineId + " languageId:" + languageId);
			
			String mapKey = engineId + ENGINE_SPLIT + languageId;
			List<Integer> oidList = engineDomainMap.get(mapKey);
			if (oidList == null) {
				oidList = new ArrayList<Integer>();
				engineDomainMap.put(mapKey, oidList);
			}
			oidList.add(oid);
		}
		
		System.out.println(" ======domainCnt:" + domainList.size() + " engineDomainMap:" + engineDomainMap.size());
		String currentDate = SDF_YYYYMMDD.format(new Date());
		
		for(String key : engineDomainMap.keySet()) {
			List<Integer> oidList = engineDomainMap.get(key);
			// TODO
			StringBuffer sb = new StringBuffer();
			for (Integer oid : oidList) {
				sb.append(oid).append(",");
			}
			System.out.println(" ===key:" + key + " oidList:" + sb.toString());
			
			String[] arr = key.split(ENGINE_SPLIT);
			int engineId = Integer.parseInt(arr[0]);
			int languageId = Integer.parseInt(arr[1]);
			String outputFileName = OUTPUT_FOLDER + RV_KEYWORD_OUTPUT_FILENAME_PREFIX + engineId + "_" + languageId + "_" + currentDate;
			exportKWs(engineId, languageId, oidList, currentDate, outputFileName);	
		}
	}
	
	private void exportKWs(int engineId, int languageId, List<Integer> domainIdList, String currentDate, String outputFileName) throws Exception {
		System.out.println("  ===exportKWs engine:" + engineId + " language:" + languageId + " oidCnt:" + domainIdList.size());
		
		// TODO page query
		List<KeywordEntity> keywordList = keywordEntityDAO.geKeywordByDomainList(domainIdList);
		
		if (keywordList != null && keywordList.size() > 0) {
			System.out.println("  ==engine:" + engineId + " language:" + languageId + " KWCnt:" + keywordList.size());
			Set<String> uniqueKWSet = new HashSet<String>();
			for (KeywordEntity keywordEntity : keywordList) {
				KeywordEntity newEntity = getKeywordByKeywordName(keywordEntity.getKeywordName(), keywordEntity.getOwnDomainId());
				String kwName = newEntity.getKeywordName();
				System.out.println("  OID:" + keywordEntity.getOwnDomainId() + " KW:" + keywordEntity.getKeywordName() + "-->" + kwName); // TODO
				uniqueKWSet.add(kwName);
			}
			
			List<String> outputList = new ArrayList<String>();
			Iterator<String> ite = uniqueKWSet.iterator();
			while (ite.hasNext()) {
				String kwName = ite.next();
				SeoClarityKeywordEntity seoClarityKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(kwName);
				if (seoClarityKeywordEntity != null) {
					int rankcheckId = seoClarityKeywordEntity.getId();
					int avgSv = 0;
					double cpc = 0.0d;
					SeoClarityKeywordAdwordsEntity adwordsEntity = seoClarityKeywordAdwordsEntityDAO.getExistedEntity(rankcheckId, engineId, languageId, CITY_ID);
					if (adwordsEntity != null) {
						avgSv = adwordsEntity.getAvgMonthlySearchVolume() != null ? adwordsEntity.getAvgMonthlySearchVolume().intValue() : 0;
						cpc = adwordsEntity.getCostPerClick() != null ? adwordsEntity.getCostPerClick().doubleValue() : 0.0d;
					}
					StringBuffer line = new StringBuffer();
					line.append(kwName).append(OUTPUT_SPLIT);
					line.append(avgSv).append(OUTPUT_SPLIT);
					line.append(cpc).append(OUTPUT_SPLIT);
					line.append(rankcheckId);
					outputList.add(line.toString());
				} else {
					System.out.println(" Abnormal NoRankcheckKW:" + kwName);
				}
			}
			
			System.out.println("  ==engine:" + engineId + " lang:" + languageId + " uniqueKW:" + uniqueKWSet.size() + " outputList:" + outputList.size() + 
				" outputFileName:" + outputFileName);
			FileUtils.writeLines(new File(outputFileName), "UTF-8", outputList, true);
			
		} else {
			System.out.println(" Abnormal NoKWFor engine:" + engineId + " language:" + languageId + " oidList:" + domainIdList.size());	
		}
	}
	
    private KeywordEntity getKeywordByKeywordName(String keywordName, int ownDomainId) {
        KeywordEntity keywordEntity = keywordEntityDAO.findFirstByKeywordNameAndRanked(keywordName, ownDomainId);
        if (keywordEntity == null) {
            keywordEntity = keywordEntityDAO.findFirstByKeywordName(keywordName, ownDomainId);
        }
        if (keywordEntity == null) {
            keywordEntity = keywordNameRelService.findFirstByKeywordName(keywordName, ownDomainId);
        }
        return keywordEntity;
    }
}