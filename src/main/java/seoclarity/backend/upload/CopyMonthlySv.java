package seoclarity.backend.upload;

import com.alibaba.fastjson.JSON;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.util.CollectionUtils;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordAdwordsEntityDAO;
import seoclarity.backend.dao.rankcheck.retrievesv.KeywordAdwordsExpandedDAO;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordAdwordsEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsExpandedEntity;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 只更新12个月的sv，不更新avgsv
 */
@CommonsLog
public class CopyMonthlySv {

    private static int threadCount = 10;
    private ExecutorService newThreadPool =
            new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());
    private static final String SPLIT_FIELD = "\t";
    private static final int ENGINE_ID = 99;
    private static int PAGE_KEYWORD_COUNT = 200;
    private Integer startMonth = 202401; // TODO
    private Integer endMonth = 202412; // TODO
    private static int UPLOAD_INFO_ID = 25070123; // TODO
    private static String START_HOUR = "00:00";
    private static String STOP_HOUR = "3:00";

    private static Boolean isInsertExpandAll = null; //expand重新建表时打开
    private static Integer copyStartMonth = null;
    private KeywordAdwordsExpandedDAO keywordAdwordsExpandedDAO;
    private SeoClarityKeywordAdwordsEntityDAO seoClarityKeywordAdwordsEntityDAO;

    public CopyMonthlySv() {
        keywordAdwordsExpandedDAO = SpringBeanFactory.getBean("keywordAdwordsExpandedDAO");
        seoClarityKeywordAdwordsEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordAdwordsEntityDAO");
    }


    private void prepareUpdateByFile(String fullPathLocalFileName) throws Exception{
        if(isInsertExpandAll == null){
            log.error("===========ERRisInsertExpandAll null!!!");
            return;
        }
        if(copyStartMonth == null){
            log.error("===========ERRcopyStartMonth null!!!");
            return;
        }
        File folder = new File(fullPathLocalFileName);
        if (folder.isDirectory()) {
            for (File file1 : folder.listFiles()) {
                processFile(file1.getAbsolutePath());
            }
        } else {
            processFile(fullPathLocalFileName);
        }
        waitingForExit();
    }

    private void processFile(String filePath) throws Exception {
        log.info("===========processFile:" + filePath);
        Thread.sleep(1000 * 1);
        File inFile = new File(filePath);
        Map<Integer, List<Integer>> languageKwMap = new HashMap();
        try {

            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(inFile));
            BufferedReader bf = new BufferedReader(inputReader);

            String line;
            int linNo = 0;
            while ((line = bf.readLine()) != null) {
                linNo++;

                String[] arr = line.split(SPLIT_FIELD);
                int languageId = Integer.parseInt(arr[0]);
                int keywordRankCheckId = Integer.parseInt(arr[1]);

                if (languageKwMap.get(languageId) == null) {
                    List<Integer> rankCheckIdList = new ArrayList<>();
                    rankCheckIdList.add(keywordRankCheckId);
                    languageKwMap.put(languageId, rankCheckIdList);
                } else {
                    List<Integer> rankCheckIdList = languageKwMap.get(languageId);
                    rankCheckIdList.add(keywordRankCheckId);
                    languageKwMap.put(languageId, rankCheckIdList);
                }


            }

            bf.close();
            inputReader.close();

            log.info("-----------------languageKwMap size:" + languageKwMap.size());
//            Date copyStartMonthDate = DateUtils.addYears(FormatUtils.toDate(String.valueOf(startMonth), "yyyyMM"), -1);
//            copyStartMonth = Integer.parseInt(FormatUtils.formatDate(copyStartMonthDate, "yyyyMM"));

            for (Integer languageId : languageKwMap.keySet()) {
                log.info("======processing language:" + languageId + ",size:" + languageKwMap.size());

                List<Integer> rankCheckIdList = languageKwMap.get(languageId);
//                List<List<Integer>> pageList = seoclarity.backend.utils.CollectionSplitUtils.splitCollectionBySize(rankCheckIdList, PAGE_KEYWORD_COUNT);
//                List<List<Integer>> pageList = CollectionUtil.split(rankCheckIdList, PAGE_KEYWORD_COUNT);;
//                int pageIndex = 0;

                List<Integer> pageList = new ArrayList<>();
                int index = 0;
                long startTime = System.currentTimeMillis();
                for (Integer rcId : rankCheckIdList) {
                    index++;
                    pageList.add(rcId);
                    if (pageList.size() >= PAGE_KEYWORD_COUNT) {
                        if (isInStopTime()) {
                            int randNumber = 300 + (new Random().nextInt(500 - 300) + 1);
                            System.out.println("Ts1: " + (randNumber) + "ms");
                            Thread.sleep(randNumber);
                        }
                        log.info("======index:" + index + ",pageListSize:" + pageList.size());
                        batchCopy(languageId, pageList);
                        pageList = new ArrayList<>();
                        log.info("==耗时1：" + (System.currentTimeMillis() - startTime) + " ms");
                        startTime = System.currentTimeMillis();
                    }
                }

                if (!CollectionUtils.isEmpty(pageList)) {
                    if (isInStopTime()) {
                        int randNumber = 300 + (new Random().nextInt(500 - 300) + 1);
                        System.out.println("Ts1: " + (randNumber) + "ms");
                        Thread.sleep(randNumber);
                    }
                    log.info("======index2:" + index + ",pageListSize:" + pageList.size());
                    batchCopy(languageId, pageList);
                    log.info("==耗时2：" + (System.currentTimeMillis() - startTime) + " ms");
                }


            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static boolean isInStopTime() {

        SimpleDateFormat df = new SimpleDateFormat("HH:mm");
        Date now = null;
        Date startTime = null;
        Date endTime = null;
        try {
            now = df.parse(df.format(new Date()));
//            System.out.println("***now:" + now);
            startTime = df.parse(START_HOUR);
            endTime = df.parse(STOP_HOUR);
            System.out.println(endTime);

            Calendar date = Calendar.getInstance();
            date.setTime(now);

            Calendar start = Calendar.getInstance();
            start.setTime(startTime);

            Calendar end = Calendar.getInstance();
            end.setTime(endTime);

            if (date.after(start) && date.before(end)) {
                return true;
            } else {
                return false;
            }

        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }

    }

    private void batchCopy(int languageId, List<Integer> pageList) throws Exception {
        Map<Integer, List<KeywordAdwordsExpandedEntity>> keywordExpandMap = new HashMap<>();

        List<KeywordAdwordsExpandedEntity> expandList = keywordAdwordsExpandedDAO.getExistExpandSvForCopy(ENGINE_ID, languageId, false, pageList, copyStartMonth);
        if (!CollectionUtils.isEmpty(expandList)) {
            for (KeywordAdwordsExpandedEntity expanded : expandList) {
                Integer kwId = expanded.getKeywordId();
                if (keywordExpandMap.get(kwId) == null) {
                    List<KeywordAdwordsExpandedEntity> list = new ArrayList<>();
                    list.add(expanded);
                    keywordExpandMap.put(kwId, list);
                } else {
                    List<KeywordAdwordsExpandedEntity> list = keywordExpandMap.get(kwId);
                    list.add(expanded);
                    keywordExpandMap.put(kwId, list);
                }
            }
        }
//        log.info("=====keywordExpandMap:" + keywordExpandMap);
        List<SeoClarityKeywordAdwordsEntity> updateList = new ArrayList<>();
        List<KeywordAdwordsExpandedEntity> insertExpandList = new ArrayList<>();
        for (Integer rcKwId : pageList) {
            List<KeywordAdwordsExpandedEntity> expandedEntityList = keywordExpandMap.get(rcKwId);
            if (CollectionUtils.isEmpty(expandedEntityList)) {
                log.info("====skip noExpand:" + rcKwId);
                continue;
            }

            SeoClarityKeywordAdwordsEntity keywordAdwordsEntity = new SeoClarityKeywordAdwordsEntity();
            keywordAdwordsEntity.setSearchEngineId(ENGINE_ID);
            keywordAdwordsEntity.setRealEngineId(ENGINE_ID);
            keywordAdwordsEntity.setLanguageId(languageId);
            keywordAdwordsEntity.setKeywordId(rcKwId);
            keywordAdwordsEntity.setCityId(0);
            keywordAdwordsEntity.setStartMonth(startMonth);
            keywordAdwordsEntity.setEndMonth(endMonth);
            keywordAdwordsEntity.setUploadInfoId(UPLOAD_INFO_ID);

            boolean canBeUpdate = false;
            int index = 0;
//            log.info("===expandedEntityList size:" + expandedEntityList.size());
            for (KeywordAdwordsExpandedEntity expandedEntity : expandedEntityList) {
                int svMonth = expandedEntity.getMonth().intValue();
                if(svMonth >= 202301){//todo copy 月份  eg：从21年1月-12月 考到 22年1月-12月，如果22年有一部分数据，那些月份不动
                    switch (svMonth) {
                        case 202301:
                            setMonthSv(keywordAdwordsEntity, 1, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202302:
                            setMonthSv(keywordAdwordsEntity, 2, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202303:
                            setMonthSv(keywordAdwordsEntity, 3, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202304:
                            setMonthSv(keywordAdwordsEntity, 4, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202305:
                            setMonthSv(keywordAdwordsEntity, 5, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202306:
                            setMonthSv(keywordAdwordsEntity, 6, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202307:
                            setMonthSv(keywordAdwordsEntity, 7, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202308:
                            setMonthSv(keywordAdwordsEntity, 8, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202309:
                            setMonthSv(keywordAdwordsEntity, 9, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202310:
                            setMonthSv(keywordAdwordsEntity, 10, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202311:
                            setMonthSv(keywordAdwordsEntity, 11, expandedEntity.getMonthlySearchVolume());
                            break;
                        case 202312:
                            setMonthSv(keywordAdwordsEntity, 12, expandedEntity.getMonthlySearchVolume());
                            break;
                        default:
                            break;
                    }
                }

            }
            if(!checkAllMonthSv(keywordAdwordsEntity)){
                log.error("===SvNotFull:" + index + ",kw:" + rcKwId + "," + JSON.toJSONString(keywordAdwordsEntity));
                continue;
            }
//            if (index != 12) {
//                log.error("===SvNotFull:" + index + ",kw:" + rcKwId + "," + JSON.toJSONString(keywordAdwordsEntity));
//                continue;
//            }
            updateList.add(keywordAdwordsEntity);
            insertExpandList.addAll(setCopyExpand(expandedEntityList));

        }
        log.info("====UpSize:" + updateList.size());
        seoClarityKeywordAdwordsEntityDAO.batchUpdateForCopy(updateList);

        log.info("====insertExpandList:" + insertExpandList.size());
        List<List<KeywordAdwordsExpandedEntity>> splitList =
                CollectionSplitUtils.splitCollectionBySize(insertExpandList, 480);
        int retryCnt = 0;
        while (true) {
            if (retryCnt > 5) {
                log.error("***OVERMAXRETRYCNT:" + retryCnt + ",insertExpandList:" + insertExpandList.size());
                break;
            }
            try {//todo multi threads
                List<Future<Boolean>> futureTasks = new ArrayList<>();
                for (List<KeywordAdwordsExpandedEntity> list : splitList) {
                    UpdateAdwordsCommand updateAdwordsCommand = new UpdateAdwordsCommand(list, languageId, isInsertExpandAll);
                    Future f = newThreadPool.submit(updateAdwordsCommand);
                    futureTasks.add(f);
                }

                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(futureTasks)) {
                    for (Future future : futureTasks) {
                        Boolean insertResult = (Boolean) future.get();
                        log.info("===insertResult:" + insertResult);
                    }
                }
                break;
            } catch (Exception e) {
                e.printStackTrace();
                retryCnt++;
                Thread.sleep(1000 * 10);
                log.info("====insertExpandError,retryCnt:" + retryCnt);
            }
        }
    }

    private void setMonthSv(SeoClarityKeywordAdwordsEntity keywordAdwordsEntity, int month, int sv) throws Exception {

        switch (month) {
            case 1:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume1() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume1(sv);
                }
                break;
            case 2:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume2() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume2(sv);
                }
                break;
            case 3:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume3() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume3(sv);
                }
                break;
            case 4:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume4() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume4(sv);
                }
                break;
            case 5:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume5() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume5(sv);
                }
                break;
            case 6:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume6() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume6(sv);
                }
                break;
            case 7:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume7() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume7(sv);
                }
                break;
            case 8:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume8() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume8(sv);
                }
                break;
            case 9:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume9() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume9(sv);
                }
                break;
            case 10:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume10() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume10(sv);
                }
                break;
            case 11:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume11() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume11(sv);
                }
                break;
            case 12:
                if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume12() == null){
                    keywordAdwordsEntity.setMonthlySearchVolume12(sv);
                }
                break;
            default:
                throw new Exception("badData");
        }

    }

    private List<KeywordAdwordsExpandedEntity> setCopyExpand(List<KeywordAdwordsExpandedEntity> existExpandList) throws Exception{
        List<KeywordAdwordsExpandedEntity> insertExpandList = new ArrayList<>();
        for(KeywordAdwordsExpandedEntity keywordAdwordsExpandedEntity : existExpandList){

            int month = keywordAdwordsExpandedEntity.getMonth().intValue();//eg:202403
            if(month< copyStartMonth){
                log.info("====skipMonth:" + month);
                continue;
            }
//            log.info("====AM:" + month);
            insertExpandList.add(keywordAdwordsExpandedEntity);

        }

        Map<Integer, KeywordAdwordsExpandedEntity> existMonthExpandMap = existExpandList.stream().collect(Collectors.toMap(var1 -> var1.getMonth(), var2 -> var2));

        DateTimeFormatter yearMonthFormatter = DateTimeFormatter.ofPattern("yyyyMM");
        YearMonth startYearMonth;
        YearMonth endYearMonth;

        if(isInsertExpandAll){
            startYearMonth = YearMonth.parse(String.valueOf(copyStartMonth), yearMonthFormatter);
            endYearMonth = YearMonth.parse(String.valueOf(endMonth), yearMonthFormatter);

        }else {
            startYearMonth = YearMonth.parse(String.valueOf(startMonth), yearMonthFormatter);
            endYearMonth = YearMonth.parse(String.valueOf(endMonth), yearMonthFormatter);
        }
        log.info("*************startYearMonth:" + startYearMonth + ",endYearMonth:" + endYearMonth);
        // 循环遍历每个月
        YearMonth currentYearMonth = startYearMonth;
        while (!currentYearMonth.isAfter(endYearMonth)) {
            // 获取该月的第一天作为 LocalDate
            LocalDate firstDayOfMonth = currentYearMonth.atDay(1);

//            log.info("===========setFirstDayOfMonth:" + firstDayOfMonth);
            Integer processYearMonth = Integer.parseInt(firstDayOfMonth.format(yearMonthFormatter));

            if(existMonthExpandMap.get(processYearMonth) == null){

                LocalDate minYearMonthDate = firstDayOfMonth.minusYears(1);
                Integer minYearMonth = Integer.parseInt(minYearMonthDate.format(yearMonthFormatter));
//                log.info("===========setMinYearMonth:" + minYearMonth);

                KeywordAdwordsExpandedEntity keywordAdwordsExpandedEntity = existMonthExpandMap.get(minYearMonth);
                KeywordAdwordsExpandedEntity copyExpandEntity = (KeywordAdwordsExpandedEntity)BeanUtils.cloneBean(keywordAdwordsExpandedEntity);
                copyExpandEntity.setMonth(processYearMonth);
                insertExpandList.add(copyExpandEntity);
                log.info("=====copy " + minYearMonth);
            }

            currentYearMonth = currentYearMonth.plusMonths(1);
        }

        log.info("=====insertExpandListSize:" + insertExpandList.size());
//        log.info("=====insertExpandListSize:" + new Gson().toJson(insertExpandList));

        return insertExpandList;
    }

    protected void waitingForExit() {
        waitingThread(newThreadPool);
        newThreadPool.shutdown();
    }

    protected void waitingThread(ExecutorService executorThreadPool) {
        while (((ThreadPoolExecutor) executorThreadPool).getActiveCount() > 0) {
            try {
                log.info("Still have alive thread, waiting...");
                Thread.sleep(60 * 1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private boolean checkAllMonthSv(SeoClarityKeywordAdwordsEntity keywordAdwordsEntity){
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume1() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume2() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume3() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume4() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume5() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume6() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume7() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume8() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume9() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume10() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume11() == null){
            return false;
        }
        if(keywordAdwordsEntity.getCanBeNullMonthlySearchVolume12() == null){
            return false;
        }
        return true;
    }

    public static void main(String[] args) throws Exception {
        UPLOAD_INFO_ID = Integer.parseInt(FormatUtils.formatDate(new Date(), "yyMMddHH"));
        isInsertExpandAll = Boolean.parseBoolean(args[1]);
        copyStartMonth = Integer.parseInt(args[2]);
        CopyMonthlySv copyMonthlySv = new CopyMonthlySv();
        copyMonthlySv.prepareUpdateByFile(args[0]);
    }

}
