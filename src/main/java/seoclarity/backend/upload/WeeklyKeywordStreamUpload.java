package seoclarity.backend.upload;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.GscClickstreamUploadLogDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.KeywordStreamDAO;
import seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickstreamUploadLogEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.io.File;
import java.util.*;

import static seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickstreamUploadLogEntity.*;

@CommonsLog
public class WeeklyKeywordStreamUpload {

    public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

    private static final String FILE_FOLDER = "/home/<USER>/";
    private static final int LOAD_COUNT = 50000;
    private static final int THREAD_COUNT = 5;
    private static final int RETRY_TIMES = 3;

    private static int version = 1;
    private static int sign = 1;
    private static int frequency = 1;//daily
    private Set<String> notFindCountrySet = new HashSet<>();
    private long streamCount = 0;

    private GscClickSteamDAO gscClickSteamDAO;
    private KeywordStreamDAO keywordStreamDAO;
    private GscClickstreamUploadLogDAO gscClickstreamUploadLogDAO;

    public WeeklyKeywordStreamUpload() {
        gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
        keywordStreamDAO = SpringBeanFactory.getBean("keywordStreamDAO");
        gscClickstreamUploadLogDAO = SpringBeanFactory.getBean("gscClickstreamUploadLogDAO");
    }

    public static void main(String args[]) throws Exception {

        CommonUtils.initThreads(THREAD_COUNT);
        if(args == null || args.length < 2){
            log.info("===param error,exit!");
            return;
        }

        WeeklyKeywordStreamUpload weeklyKeywordStreamUpload = new WeeklyKeywordStreamUpload();
        weeklyKeywordStreamUpload.uploadKeywordStream(Integer.parseInt(args[0]), Integer.parseInt(args[1]));
    }

    private void uploadKeywordStream(int startNo, int endNo) {

        Long uploadLogId = null;
        Date startTime = new Date();
        try {

            GscClickstreamUploadLogEntity gscClickStreamUploadLog = gscClickstreamUploadLogDAO.getNeedUploadWeeklyLog(frequency, WEEKLY_SUMMARY_SUCCESS);
            if (gscClickStreamUploadLog == null) {
                log.info("===no weekly summary need to run, exit:" + new Date());
                return;
            }

            int month = gscClickStreamUploadLog.getMonth();
            int weekIndexInMonth = gscClickStreamUploadLog.getWeekIndexInMonth();
            Integer weekStartDate = gscClickStreamUploadLog.getWeekStartDate();
            Integer weekEndDate = gscClickStreamUploadLog.getWeekEndDate();
            String weekStartDateStr = FormatUtils.formatDate(FormatUtils.toDate(weekStartDate.toString(), FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_PATTERN_2);
            String weekEndDateStr = FormatUtils.formatDate(FormatUtils.toDate(weekEndDate.toString(), FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_PATTERN_2);

            log.info("===============start processing ========month:" + month + ",weekIndexInMonth:" + weekIndexInMonth
                    + ",weekStartDate:" + weekStartDate + ",weekEndDate:" + weekEndDate + "startNo:" + startNo + ",endNo:" + endNo);

            threadPool.init();
            File file = new File("/home/<USER>/gscClickStream/notFindCountry" + FormatUtils.formatDate(new Date(), "yyyyMMddHHmmss") + ".txt");
            for (int i = startNo; i <= endNo; i++) {
                log.info("===startHashNo:" + i);

                List<KeywordStreamEntity> keywordStreamList = keywordStreamDAO.getWeeklyKeywordsByHash(month, weekIndexInMonth, weekStartDateStr, weekEndDateStr, i);
                log.info("===keywordStreamList size:" + keywordStreamList.size());

                processUploadKeywordStream(keywordStreamList);

                log.info("===notFindCountrySet size:" + notFindCountrySet.size());
                log.info("===notFindCountrySet:" + new Gson().toJson(notFindCountrySet));

            }
            uploadLogId = gscClickStreamUploadLog.getId();

            Date endTime = new Date();
            gscClickstreamUploadLogDAO.updateWeeklyStreamById(uploadLogId, WEEKLY_STREAM_SUCCESS, startTime, endTime, streamCount, null);


            FileUtils.writeLines(file, "UTF-8", notFindCountrySet, true);
            notFindCountrySet.clear();


        }catch (Exception e){
            e.printStackTrace();
            gscClickstreamUploadLogDAO.updateWeeklyStreamById(uploadLogId, GscClickstreamUploadLogEntity.UPLOAD_DIS_GSC_CLICK_STREAM_STATUS_FAILURE, startTime, new Date(), streamCount, "load error1");
        }

        do {
            try {
                Thread.sleep(100);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } while (threadPool.getThreadPool().getActiveCount() > 0);

        threadPool.destroy();
    }


    private void processUploadKeywordStream(List<KeywordStreamEntity> keywordStreamList) {
        String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
        while (ipAddress == null) {
            try {
                Thread.sleep(100);
                ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
            } catch (Exception e) {
            }
        }
        WeeklyKeywordStreamUpload.KeywordStreamCommand cmd = new WeeklyKeywordStreamUpload.KeywordStreamCommand(ipAddress, keywordStreamList);
        cmd.setStatus(true);
        try {
            threadPool.execute(cmd);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    class KeywordStreamCommand extends BaseThreadCommand {
        private String ipAddress;
        private List<KeywordStreamEntity> keywordStreamList = new ArrayList<>();
        private KeywordStreamDAO keywordStreamDAO;

        public KeywordStreamCommand(String ipAddress, List<KeywordStreamEntity> keywordStreamList) {
            this.ipAddress = ipAddress;
            this.keywordStreamList = keywordStreamList;
            keywordStreamDAO = SpringBeanFactory.getBean("keywordStreamDAO");
        }

        @Override
        protected void execute() throws Exception {
            long a = System.currentTimeMillis();

            log.info("====execute keywordStreamList:" + keywordStreamList.size());
            if (CollectionUtils.isNotEmpty(keywordStreamList)) {
                List<KeywordStreamEntity> insetList = new ArrayList<>();

                for (KeywordStreamEntity keywordStreamEntity : keywordStreamList) {

                    String country = keywordStreamEntity.getCountryCd();
                    if (!ClickStreamUtils.COUNTRY_MAP.containsKey(country)) {
//                        log.error("==notFindCountry:" + country);
                        notFindCountrySet.add(country);
                        continue;
                    }

                    String kw = keywordStreamEntity.getKeywordName();
                    String[] res = ClickStreamUtils.format(kw);
                    if (res == null) {
//                        log.error("===formatKwnNULL:" + kw);
                        continue;
                    }

                    String[] params = ClickStreamUtils.COUNTRY_MAP.get(country);
                    String code = params[0];
                    String languageName = params[1];
                    int engineId = Integer.valueOf(params[2]);
                    int languageId = Integer.valueOf(params[3]);

                    kw = res[0];
                    String decodedKw = res[1];

                    String kwName = ClickStreamUtils.formatForAnalyzer(decodedKw);

                    KeywordStreamEntity insetEntity = new KeywordStreamEntity();
                    insetEntity.setMonth(keywordStreamEntity.getMonth());
                    insetEntity.setWeekIndex(keywordStreamEntity.getWeekIndex());
                    insetEntity.setWeekStartDate(keywordStreamEntity.getWeekStartDate());
                    insetEntity.setWeekEndDate(keywordStreamEntity.getWeekEndDate());
                    insetEntity.setCountryCd(keywordStreamEntity.getCountryCd());
                    insetEntity.setEngineId(engineId);
                    insetEntity.setLanguageId(languageId);
                    insetEntity.setKeywordName(kw);

                    ////分词
                    List<String> word = new ArrayList<String>();
                    List<String> stream = new ArrayList<String>();

                    List<String> keywordVariationOneword = new ArrayList<String>();
                    List<String> keywordVariationNgram = new ArrayList<String>();

                    if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
                        word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, "ar"));
                    } else {
                        word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, code));
                    }
                    stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(kwName, languageName));
                    keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(kwName));
                    keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(kwName, false));

                    insetEntity.setWord(word);
                    insetEntity.setStream(stream);

                    if (stream.size() == 0) {
                        stream.add(kwName);
                    }

                    insetEntity.setKeywordVariationOneword(keywordVariationOneword);
                    insetEntity.setKeywordVariationNgram(MonthlyKeywordTokenizerUploadV2.setPlaceHolder(keywordVariationNgram));

                    insetList.add(insetEntity);

                    if (insetList.size() >= 50000) {
                        log.info("====insert insetList:" + insetList.size());
                        int retry = 0;
                        while (true){
                            try {
                                keywordStreamDAO.insertWeeklyKeywordStreamForBatch(insetList);
                                streamCount += insetList.size();
                                break;
                            }catch (Exception e){
                                e.printStackTrace();
                                if(retry >= RETRY_TIMES){
                                    log.info("===OVERREARY1:" + retry);
                                    throw e;
                                }
                                try {
                                    Thread.sleep(1000 * 60);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                }
                                retry ++;
                            }
                        }
                        insetList = new ArrayList<>();
                    }
                }

                if (CollectionUtils.isNotEmpty(insetList)) {
                    log.info("====insert2 insetList:" + insetList.size());
                    int retry = 0;
                    while (true){
                        try {
                            keywordStreamDAO.insertWeeklyKeywordStreamForBatch(insetList);
                            streamCount += insetList.size();
                            break;
                        }catch (Exception e){
                            e.printStackTrace();
                            if(retry >= RETRY_TIMES){
                                log.info("===OVERREARY2:" + retry);
                                throw e;
                            }
                            try {
                                Thread.sleep(1000 * 60);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            retry ++;
                        }
                    }
                }

                long b = System.currentTimeMillis();
                System.out.println("ipAddress" + ipAddress + ",End command Cost time: " + (b - a) * 1.0 / 1000 + "s");
                CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
            }

        }

        @Override
        protected void undo() throws Exception {

        }

    }

}
