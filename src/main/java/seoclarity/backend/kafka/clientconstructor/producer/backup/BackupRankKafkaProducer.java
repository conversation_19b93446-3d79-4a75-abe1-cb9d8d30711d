package seoclarity.backend.kafka.clientconstructor.producer.backup;

import lombok.Getter;
import org.apache.kafka.clients.producer.Producer;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import seoclarity.backend.kafka.clientconstructor.producer.KafkaCommonSender;

public class BackupRankKafkaProducer {

    private final Logger log = LogManager.getLogger(BackupRankKafkaProducer.class.getName());

    @Getter
    private Producer<String, byte[]> byteProducer;
    @Getter
    private Producer<String, String> stringProducer;

    private static BackupRankKafkaProducer rankKafkaProducer;

    public static BackupRankKafkaProducer getInstance(){
        if (rankKafkaProducer == null) {
            rankKafkaProducer = new BackupRankKafkaProducer();
        }
        return rankKafkaProducer;
    }

    public BackupRankKafkaProducer() {
        if (byteProducer == null) {
            byteProducer = BackupRankProducerConstructor.createProducer();
        }
        if (stringProducer == null) {
            stringProducer = BackupRankProducerConstructor.createStringValueProducer();
        }
    }

    public void send(String topic, long timestamp, String key, byte[] jsonData){
        KafkaCommonSender.getInstance().send(topic, timestamp, key, jsonData, byteProducer);
    }

    public void send(String topic, long timestamp, String key, String jsonData){
        KafkaCommonSender.getInstance().send(topic, timestamp, key, jsonData, stringProducer);
    }

    public void close() {
        try {
            if (byteProducer != null) {
                log.info("close byte producer.");
                byteProducer.close();
            }
            if (stringProducer != null) {
                log.info("close string producer.");
                stringProducer.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

