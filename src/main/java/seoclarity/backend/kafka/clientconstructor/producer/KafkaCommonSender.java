package seoclarity.backend.kafka.clientconstructor.producer;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import seoclarity.backend.kafka.clientconstructor.producer.backup.BackupRankKafkaProducer;

import java.util.Date;

public class KafkaCommonSender {

    public static KafkaCommonSender commonSender;

    public static KafkaCommonSender getInstance(){
        if (commonSender == null) {
            commonSender = new KafkaCommonSender();
        }
        return commonSender;
    }

    private final Logger log = LogManager.getLogger(BackupRankKafkaProducer.class.getName());

    public void send(String topic, long timestamp, String key, byte[] jsonData, Producer<String, byte[]> producer){
        ProducerRecord<String, byte[]> record = new ProducerRecord<>(topic, null, timestamp, key, jsonData);
        producer.send(record, (metadata, exception) -> {
            if (exception != null) {
                exception.printStackTrace();
            } else {
                log.info("send key id: " + key + ", currtime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            }
        });
    }

    public void send(String topic, long timestamp, String key, String jsonData, Producer<String, String> producerStr){
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, null, timestamp, key, jsonData);
        producerStr.send(record, (metadata, exception) -> {
            if (exception != null) {
                exception.printStackTrace();
            } else {
                log.info("send key id: " + key + ", currtime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            }
        });
    }
}
