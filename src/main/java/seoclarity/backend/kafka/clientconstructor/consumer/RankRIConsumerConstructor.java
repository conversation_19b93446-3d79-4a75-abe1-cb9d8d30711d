package seoclarity.backend.kafka.clientconstructor.consumer;

import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import seoclarity.backend.kafka.properties.RankRIKafkaProperties;

import java.util.Collection;
import java.util.Properties;

public class RankRIConsumerConstructor {
    private final static Logger log = LogManager.getLogger(RankRIConsumerConstructor.class.getName());

    private static RankRIConsumerConstructor rankRIConsumerConstructorIns;

    public static RankRIConsumerConstructor getInstance(){
        if (rankRIConsumerConstructorIns == null) {
            rankRIConsumerConstructorIns = new RankRIConsumerConstructor();
        }
        return rankRIConsumerConstructorIns;
    }

    /**
     * use default ri group
     * @return
     */
    public KafkaConsumer<String, String> buildKafkaConsumer(){
        final Properties props = RankRIKafkaProperties.getInstance().buildConsumerProperties();
        KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(props);
        return kafkaConsumer;
    }

    /**
     * use default ri group and provided topics
     * @return
     */
    public Consumer<String, byte[]> buildKafkaConsumerWithTopics(Collection<String> topicsName) {
        if (topicsName.isEmpty()) {
            log.error("please provide topic...");
            return null;
        }
        final Properties props = RankRIKafkaProperties.getInstance().buildConsumerProperties();
        KafkaConsumer<String, byte[]> kafkaConsumer = new KafkaConsumer<>(props);
        kafkaConsumer.subscribe(topicsName);
        log.info("build kafka consumer with topic: " + topicsName);
        return kafkaConsumer;
    }

    /**
     * use provided ri group and topics
     * @return
     */
    public Consumer<String, String> buildKafkaConsumerWithTopicsAndGroup(Collection<String> topicsNames, String groupName) {
        if (topicsNames.isEmpty()) {
            log.error("please provide  topic...");
            return null;
        }
        final Properties props = RankRIKafkaProperties.getInstance().buildConsumerProperties(groupName);
        KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(props);
        kafkaConsumer.subscribe(topicsNames);
        log.info("build kafka consumer with topic: " + topicsNames);
        return kafkaConsumer;
    }

    /**
     * use provided ri group and topics
     * @return
     */
    public Consumer<String, String> buildKafkaConsumerWithoutTopicsAndGroup() {
        final Properties props = RankRIKafkaProperties.getInstance().buildConsumerPropertiesWithoutGroup();
        return new KafkaConsumer<>(props);
    }

}
