package seoclarity.backend.kafka.properties;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.Properties;

public class RankAdminKafkaProperties {
    private final static Properties prop = PlatformPropertiesLoader.getInstance().getProperties();

    private static RankAdminKafkaProperties adminPropIns;

    public static RankAdminKafkaProperties getInstance() {
        if (adminPropIns == null) {
            adminPropIns = new RankAdminKafkaProperties();
        }
        return adminPropIns;
    }

    public Properties buildAdminClientBaseProperties() {
        Properties kafkaProps = new Properties();
        kafkaProps = new Properties();
        kafkaProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, prop.getProperty("rank.admin.kafka.bootstrap.internal.servers"));
        kafkaProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        kafkaProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        kafkaProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, prop.getProperty("rank.admin.session.timeout.ms"));
        kafkaProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, prop.getProperty("rank.admin.heartbeat.interval.ms"));
        //SASL_PLAINTEXT
        kafkaProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        kafkaProps.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.plain.PlainLoginModule required username=se0Clarity99 password=LmNoPq123rStUv456WxYzAbC789dEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpOSOFJI0k;");
        kafkaProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        return kafkaProps;
    }

    public Properties buildAdminClientPropertiesWithGroup(String groupName) {
        Properties properties = buildAdminClientBaseProperties();
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, groupName);
        return properties;
    }

}
