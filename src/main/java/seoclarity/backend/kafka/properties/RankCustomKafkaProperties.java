package seoclarity.backend.kafka.properties;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.Properties;

public class RankCustomKafkaProperties {
    private final static Properties prop = PlatformPropertiesLoader.getInstance().getProperties();

    private static RankCustomKafkaProperties customPropIns;

    public static RankCustomKafkaProperties getInstance() {
        if (customPropIns == null) {
            customPropIns = new RankCustomKafkaProperties();
        }
        return customPropIns;
    }

    public Properties buildConsumerBaseProperties() {
        Properties kafkaProps = new Properties();
        kafkaProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, prop.getProperty("custom.consumer.kafka.bootstrap.servers"));
        kafkaProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        kafkaProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        kafkaProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, prop.getProperty("custom.consumer.max.poll.records"));
        kafkaProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, prop.getProperty("custom.consumer.fetch.min.bytes"));
        kafkaProps.put(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, prop.getProperty("custom.consumer.fetch.max.bytes"));
        kafkaProps.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, prop.getProperty("custom.consumer.max.partition.fetch.bytes"));
        kafkaProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, prop.getProperty("custom.consumer.fetch.max.wait.ms"));
        kafkaProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, prop.getProperty("custom.consumer.auto.commit"));
        kafkaProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, prop.getProperty("custom.consumer.auto.offset.reset", "earliest"));

        //SASL_PLAINTEXT
        kafkaProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        kafkaProps.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.plain.PlainLoginModule required username=se0Clarity99 password=LmNoPq123rStUv456WxYzAbC789dEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpOSOFJI0k;");
        kafkaProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        return kafkaProps;
    }


    public Properties buildConsumerProperties(String groupName) {
        final Properties properties = buildConsumerBaseProperties();
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, groupName);
        return properties;
    }

    public Properties buildConsumerProperties() {
        final Properties properties = buildConsumerBaseProperties();
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, prop.getProperty("custom.consumer.kafka.group.id"));
        return properties;
    }

    public Properties buildConsumerPropertiesWithoutGroup() {
        return buildConsumerBaseProperties();
    }
}
