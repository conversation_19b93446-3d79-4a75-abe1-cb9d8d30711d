package seoclarity.backend.opensearch;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.github.benmanes.caffeine.cache.RemovalListener;
import com.google.common.io.Resources;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.opensearch.action.bulk.BulkProcessor;
import org.opensearch.action.bulk.BulkRequest;
import org.opensearch.action.bulk.BulkResponse;
import org.opensearch.action.delete.DeleteRequest;
import org.opensearch.action.get.GetRequest;
import org.opensearch.action.index.IndexRequest;
import org.opensearch.action.update.UpdateRequest;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.indices.CreateIndexRequest;
import org.opensearch.client.indices.CreateIndexResponse;
import org.opensearch.client.indices.GetIndexRequest;
import org.opensearch.common.xcontent.XContentType;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class OpenSearchUtil implements RemovalListener<String, List<Object>>, BulkProcessor.Listener{
	
	public static final String OPEN_SEARCH_HOST = "***************"; // TODO
//	public static final String OPEN_SEARCH_HOST = "**************"; // TODO
	public static final int OPEN_SEARCH_PORT = 9200;
	public static final String OPEN_SEARCH_URL = "https://" + OPEN_SEARCH_HOST + ":" + OPEN_SEARCH_PORT;
	public static final String OPEN_SEARCH_UNAME = "admin";
	public static final String OPEN_SEARCH_PASSWORD = "VtwS9yG8HATRCpf4";
	public static final String INDEX_NAME = "my_index1";
	
	private int writeRequestCount;
	private int writeCount;
	private int writeFailedCount;

	private RestHighLevelClient client;
	private OpenSearchConnection connection;
	private final ReentrantLock waitAckLock = new ReentrantLock(true);
	public Cache<String, List<Object>> waitAck;

	BulkProcessor.Listener listener;
	public OpenSearchUtil(BulkProcessor.Listener listener) {
		waitAck = Caffeine.newBuilder()
//                        .expireAfterWrite(60, TimeUnit.SECONDS)
				.expireAfterAccess(60, TimeUnit.SECONDS)
				.removalListener(this)
				.build();
		this.listener = listener;
		getConnection();
	}

	@SneakyThrows
	public static void main(String[] args) {
//		OpenSearchUtil openSearchUtil = new OpenSearchUtil();
//		Object o = openSearchUtil.waitAck.get("123", k -> new LinkedList<>());

		// 获取opensearch连接
//		openSearchUtil.getConnection();
		// 创建index
//		openSearchUtil.checkOrCreateIndex(INDEX_NAME, "test.mapping");
//		Person person = new Person("姓名", 123);
//		openSearchUtil.write(INDEX_NAME, person);
//		openSearchUtil.update(INDEX_NAME, person, "123");
//		openSearchUtil.delete(INDEX_NAME, "123");
//		openSearchUtil.get(INDEX_NAME, "123");
//		openSearchUtil.close();

		// 批量数据写入
//		openSearchUtil.batchWrite(new Person("name1", 20));
//		openSearchUtil.close();
	}

	@SneakyThrows
	private void get(String indexName, String documentId) {
		GetRequest request = new GetRequest(indexName, documentId);
		boolean exists = client.exists(request, RequestOptions.DEFAULT);
		if (exists) {
			String source = client.get(request, RequestOptions.DEFAULT).getSourceAsString();
			System.out.println("Document retrieved: " + source);
		} else {
			System.out.println("Document with ID: " + documentId + " does not exist.");
		}
	}

	@SneakyThrows
	private void delete(String indexName, String documentId) {
		DeleteRequest deleteRequest = new DeleteRequest(indexName, documentId);
		client.delete(deleteRequest, RequestOptions.DEFAULT);
	}

	@SneakyThrows
	private void update(String indexName, Object person, String id) {
		ObjectMapper objectMapper = new ObjectMapper();
		String s = objectMapper.writeValueAsString(person);
		UpdateRequest updateRequest = new UpdateRequest(indexName, id)
				.doc(s, XContentType.JSON);
		client.update(updateRequest, RequestOptions.DEFAULT);
	}

	public void closeConnection() {
		if (connection != null) {
			try {
				client.close();
				connection.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	@SneakyThrows
	private void write(String indexName, Object object) {
		IndexRequest request = new IndexRequest(indexName);
		ObjectMapper objectMapper = new ObjectMapper();

		String s = objectMapper.writeValueAsString(object);
		request.source(s, XContentType.JSON);
		/**
		 * true: id冲突会返回DocumentAlreadyExistsException异常
		 * false: id冲突会覆盖
		 */
		request.create(true);
		client.index(request, RequestOptions.DEFAULT);
	}

	/**
	 * @param object 写入对象
	 * @param id id
	 */
	@SneakyThrows
	public void batchWrite(String indexName, Object object, String id) {
		IndexRequest request = new IndexRequest(indexName);
		ObjectMapper objectMapper = new ObjectMapper();

		String jsonStr = objectMapper.writeValueAsString(object);
		// id = org.apache.commons.codec.digest.DigestUtils.sha256Hex(url);
		request.id(id);
		request.source(jsonStr, XContentType.JSON);
		request.create(false);

		waitAckLock.lock();
		try {
			final List<Object> tt = waitAck.get(id, k -> new LinkedList<>());
			tt.add(jsonStr);
		} finally {
			waitAckLock.unlock();
		}
		connection.addToProcessor(request);
	}

	public synchronized void getConnection() {
		if (ObjectUtil.isNull(connection)) {
			log.info("connection = null, get connection...");

			Map<String, Object> conf = new HashMap<>();
			conf.put("opensearch.addresses", OPEN_SEARCH_URL);
			conf.put("opensearch.user", OPEN_SEARCH_UNAME);
			conf.put("opensearch.password", OPEN_SEARCH_PASSWORD);
			// 禁用tls
			conf.put("opensearch.disable.tls.validation", true);
			// 请求刷新间隔
			conf.put("opensearch.flushInterval", "5s");
			// 多少个数据合并为一次请求
			conf.put("opensearch.bulkActions", 1000);
			// 最多能有多少个并行的请求同时发出
			conf.put("opensearch.concurrentRequests", 1);

			// 如果外部不提供批处理回调函数即使用默认回调函数处理
			connection = OpenSearchConnection.getConnection(conf, ObjUtil.isNull(listener) ? this : listener);

			if (!ObjUtil.isNull(connection)) {
				client = connection.getClient();
				log.info("get connection success");
			} else {
				log.info("get connection fail");
			}
		}
	}

	@SneakyThrows
	public synchronized void connectAndCreateIndexIfNotExist(String indexName, String mappingName) {
		getConnection();
		final boolean indexExists = client.indices().exists(new GetIndexRequest(indexName), RequestOptions.DEFAULT);
		log.info("Index '{}' exists? {}", indexName, indexExists);
		if (!indexExists) {
			boolean created = createIndex(client, indexName, mappingName);
			log.info("Index '{}' created? {} using {}", indexName, created, mappingName);
			if (created) {
				log.info("Index has created.");
			}
		}
	}

	public boolean createIndex(RestHighLevelClient client, String indexName, String resourceName) {
		try {
			final CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);

			final URL mapping = Thread.currentThread().getContextClassLoader().getResource(resourceName);

			final String jsonIndexConfiguration = Resources.toString(mapping, Charsets.UTF_8);

			createIndexRequest.source(jsonIndexConfiguration, XContentType.JSON);

			final CreateIndexResponse createIndexResponse =
					client.indices().create(createIndexRequest, RequestOptions.DEFAULT);
			return createIndexResponse.isAcknowledged();
		} catch (IOException e) {
			return false;
		}
	}

	@Override
	public void onRemoval(@Nullable String key, @Nullable List<Object> value, RemovalCause removalCause) {
		if (!removalCause.wasEvicted()) return;
//		log.error("Purged from waitAck {} with {} values", key, value.size());
		for (Object t : value) {
			if(t != null) {
//				_collector.fail(t);
			}
		}
	}

	@Override
	public void beforeBulk(long executionId, BulkRequest request) {
		int requestCnt = request.numberOfActions();
		writeRequestCount += requestCnt;
		log.info("  beforeBulk:{}", requestCnt);
	}

	@Override
	public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
		int writeCnt = request.numberOfActions();
		long failCnt = Arrays.stream(response.getItems()).filter(e -> e.getFailure() != null).count();
		writeCount += writeCnt;
		writeFailedCount += (int)failCnt;
		log.info("afterBulk:{},fail:{}", writeCnt, failCnt);
	}

	@Override
	public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
		log.warn(failure.toString());
	}
	
	public int getWriteRequestCount() {
		return writeRequestCount;
	}

	public int getWriteCount() {
		return writeCount;
	}

	public int getWriteFailedCount() {
		return writeFailedCount;
	}
}
