package seoclarity.backend.opensearch;

/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to you under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.ssl.SSLContextBuilder;
import org.opensearch.action.DocWriteRequest;
import org.opensearch.action.bulk.BulkProcessor;
import org.opensearch.action.bulk.BulkRequest;
import org.opensearch.action.bulk.BulkResponse;
import org.opensearch.client.*;
import org.opensearch.client.sniff.Sniffer;
import org.opensearch.common.unit.TimeValue;

import java.io.IOException;
import java.net.URI;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Utility class to instantiate an OpenSearch client and bulkprocessor based on the configuration.
 */
@Slf4j
public final class OpenSearchConnection {
	public static final String PARAMPREFIX = "opensearch.";

	private final RestHighLevelClient client;
	private final BulkProcessor processor;
	private final Sniffer sniffer;

	private OpenSearchConnection(RestHighLevelClient client, BulkProcessor bulkProcessor, Sniffer sniffer) {
		processor = bulkProcessor;
		this.client = client;
		this.sniffer = sniffer;
	}

	public RestHighLevelClient getClient() {
		return client;
	}

	public void addToProcessor(final DocWriteRequest<?> request) {
		processor.add(request);
	}

	public static RestHighLevelClient getClient(Map<String, Object> stormConf) {
		final List<HttpHost> hosts = new ArrayList<>();

		final List<String> confighosts = loadListFromConf(PARAMPREFIX + "addresses", stormConf);

		for (String host : confighosts) {
			// no port specified? use default one
			int port = 9200;
			String scheme = "http";
			// no scheme specified? use http
			if (!host.startsWith(scheme)) {
				host = "http://" + host;
			}
			URI uri = URI.create(host);
			if (uri.getHost() == null) {
				throw new RuntimeException("host undefined " + host);
			}
			if (uri.getPort() != -1) {
				port = uri.getPort();
			}
			if (uri.getScheme() != null) {
				scheme = uri.getScheme();
			}
			hosts.add(new HttpHost(uri.getHost(), port, scheme));
		}

		final RestClientBuilder builder = RestClient.builder(hosts.toArray(new HttpHost[0]));

		// authentication via user / password
		final String user = MapUtil.getStr(stormConf, PARAMPREFIX + "user");
		final String password = MapUtil.getStr(stormConf, PARAMPREFIX + "password");
		final String proxyhost = MapUtil.getStr(stormConf, PARAMPREFIX + "proxy.host");
		final int proxyport = MapUtil.getInt(stormConf, PARAMPREFIX + "proxy.port", -1);
		final String proxyscheme = MapUtil.getStr(stormConf, PARAMPREFIX + "proxy.scheme", "http");
		final boolean disableTlsValidation = MapUtil.getBool(stormConf, PARAMPREFIX + "disable.tls.validation", false);
		final boolean needsUser = StrUtil.isNotBlank(user) && StrUtil.isNotBlank(password);
		final boolean needsProxy = StrUtil.isNotBlank(proxyhost) && proxyport != -1;

		if (needsUser || needsProxy || disableTlsValidation) {
			builder.setHttpClientConfigCallback(
					httpClientBuilder -> {
						if (needsUser) {
							final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
							credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(user, password));
							httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
						}
						if (needsProxy) {
							httpClientBuilder.setProxy(new HttpHost(proxyhost, proxyport, proxyscheme));
						}

						if (disableTlsValidation) {
							try {
								final SSLContextBuilder sslContext = new SSLContextBuilder();
								sslContext.loadTrustMaterial(null, new TrustAllStrategy());
								httpClientBuilder.setSSLContext(sslContext.build());
								httpClientBuilder.setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE);
							} catch (Exception e) {
								throw new RuntimeException("Failed to disable TLS validation", e);
							}
						}
						return httpClientBuilder;
					});
		}

		final int connectTimeout = MapUtil.getInt(stormConf, PARAMPREFIX + "connect.timeout", RestClientBuilder.DEFAULT_CONNECT_TIMEOUT_MILLIS * 30);
		final int socketTimeout = MapUtil.getInt(stormConf, PARAMPREFIX + "socket.timeout", RestClientBuilder.DEFAULT_SOCKET_TIMEOUT_MILLIS);
		// timeout until connection is established
		builder.setRequestConfigCallback(
				requestConfigBuilder ->
						requestConfigBuilder
								.setConnectTimeout(connectTimeout)
								.setSocketTimeout(socketTimeout) // Timeout when waiting
				// for data
		);

		// check if this has gone somewhere else in ES 7
		// int maxRetryTimeout = ConfUtils.getInt(stormConf, Constants.PARAMPREFIX +
		// boltType +
		// ".max.retry.timeout",
		// DEFAULT_MAX_RETRY_TIMEOUT_MILLIS);
		// builder.setMaxRetryTimeoutMillis(maxRetryTimeout);

		// configure headers etc...
		// Map<String, String> configSettings = (Map) stormConf
		// .get(Constants.PARAMPREFIX + boltType + ".settings");
		// if (configSettings != null) {
		// configSettings.forEach((k, v) -> settings.put(k, v));
		// }

		// use node selector only to log nodes listed in the config
		// and/or discovered through sniffing
		builder.setNodeSelector(
				nodes -> {
					for (Node node : nodes) {
						log.debug(
								"Connected to OpenSearch node {} [{}]",
								node.getName(),
								node.getHost());
					}
				});

		final boolean compression = MapUtil.getBool(stormConf, PARAMPREFIX + "compression", true);

		builder.setCompressionEnabled(compression);

		return new RestHighLevelClient(builder);
	}


	/**
	 * Creates a connection with a default listener. The values for bolt type are
	 * [indexer,status,metrics]
	 */
	public static OpenSearchConnection getConnection(Map<String, Object> stormConf) {
		BulkProcessor.Listener listener =
				new BulkProcessor.Listener() {
					@Override
					public void beforeBulk(long executionId, BulkRequest request) {
					}

					@Override
					public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
					}

					@Override
					public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
					}
				};
		return getConnection(stormConf, listener);
	}

	public static OpenSearchConnection getConnection(Map<String, Object> stormConf, BulkProcessor.Listener listener) {

		final RestHighLevelClient client = getClient(stormConf);

		final String flushIntervalString = MapUtil.getStr(stormConf, PARAMPREFIX + "flushInterval", "10s");

		final TimeValue flushInterval = TimeValue.parseTimeValue(flushIntervalString, TimeValue.timeValueSeconds(5), "flushInterval");

		final int bulkActions = MapUtil.getInt(stormConf, PARAMPREFIX + "bulkActions", 1000);

		final int concurrentRequests = MapUtil.getInt(stormConf, PARAMPREFIX + "concurrentRequests", 1);

		final BulkProcessor bulkProcessor =
				BulkProcessor.builder(
								(request, bulkListener) ->
										client.bulkAsync(
												request, RequestOptions.DEFAULT, bulkListener),
								listener)
						.setFlushInterval(flushInterval)
						.setBulkActions(bulkActions)
						.setConcurrentRequests(concurrentRequests)
						.build();

		boolean sniff = MapUtil.getBool(stormConf, PARAMPREFIX + "dottedType" + "sniff", false);
		Sniffer sniffer = null;
		if (sniff) {
			sniffer = Sniffer.builder(client.getLowLevelClient()).build();
		}

		return new OpenSearchConnection(client, bulkProcessor, sniffer);
	}

	private boolean isClosed = false;

	public void close() {
		if (isClosed) {
			log.warn("Tried to close an already closed connection!");
			return;
		}

		// Maybe some kind of identifier?
		log.info("Start closing the OpenSearch connection");

		// First, close the BulkProcessor ensuring pending actions are flushed
		try {
			boolean success = processor.awaitClose(60, TimeUnit.SECONDS);
			if (!success) {
				throw new RuntimeException(
						"Failed to flush pending actions when closing BulkProcessor");
			}
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}

		if (sniffer != null) {
			sniffer.close();
		}

		// Now close the actual client
		try {
			client.close();
		} catch (IOException e) {
			// ignore silently
			log.info("Client threw IO exception.");
		}

		isClosed = true;
	}

	public static List<String> loadListFromConf(String paramKey, Map stormConf) {
		Object obj = stormConf.get(paramKey);
		List<String> list = new LinkedList<>();

		if (obj == null) return list;

		if (obj instanceof Collection) {
			list.addAll((Collection<String>) obj);
		} else { // single value?
			list.add(obj.toString());
		}
		return list;
	}
}

