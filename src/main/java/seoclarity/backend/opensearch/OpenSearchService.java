package seoclarity.backend.opensearch;

import lombok.extern.log4j.Log4j2;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.FieldValue;
import org.opensearch.client.opensearch._types.Result;
import org.opensearch.client.opensearch.core.*;
import org.opensearch.client.opensearch.core.bulk.BulkOperation;
import org.opensearch.client.opensearch.core.bulk.DeleteOperation;
import org.opensearch.client.opensearch.core.bulk.IndexOperation;
import org.opensearch.client.opensearch.core.search.Hit;

import javax.annotation.Nullable;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Log4j2
public class OpenSearchService<T> {
    private final OpenSearchClient openSearchClient;

    public OpenSearchService(OpenSearchClient openSearchClient) {
        this.openSearchClient = openSearchClient;
    }

    public void indexDocument(String index, String id, T document) {
        IndexRequest<T> indexRequest = new IndexRequest.Builder<T>()
                .index(index)
                .id(id)
                .document(document)
                .build();
        // Execute the index request
        try {
            final IndexResponse indexResponse = openSearchClient.index(indexRequest);
            if (indexResponse.result() != Result.Created) {
                log.error("Failed to index document with ID: {} in index: {}", id, index);
            }
        } catch (IOException e) {
            log.error("Failed to index document with ID: {} in index: {}", id, index, e);
            throw new RuntimeException(e);
        }
    }

    public void bulkIndexDocuments(String index, List<T> documents, @Nullable Function<T, String> idGenerator) {
        final BulkRequest.Builder builder = new BulkRequest.Builder();
        documents.forEach(document -> {
            IndexOperation.Builder<Object> indexOp = new IndexOperation.Builder<>()
                    .index(index)
                    .document(document);
            if (idGenerator != null) {
                indexOp.id(idGenerator.apply(document));
            }
            builder.operations(op -> op.index(indexOp.build()));
        });
        try {
            var bulkResponse = openSearchClient.bulk(builder.build());
            if (bulkResponse.errors()) {
                log.error("Failed to index some documents in bulk operation for index: {}", index);
            }
        } catch (IOException e) {
            log.error("Failed to execute bulk index operation for index: {}", index, e);
            throw new RuntimeException(e);
        }
    }

    // search List<T> by fields values
    public List<T> search(String index, String field, String value, Class<T> clazz) {
        SearchRequest searchRequest = new SearchRequest.Builder()
             .index(index)
             .query(q -> q.match(m -> m.field(field).query(FieldValue.of(value))))
             .build();
        try {
            final SearchResponse<T> searchResponse = openSearchClient.search(searchRequest, clazz);
            return searchResponse.hits().hits().stream().map(Hit::source).toList();
        } catch (IOException e) {
            log.error("Failed to search documents in index: {}", index, e);
            throw new RuntimeException(e);
        }
    }

    public List<T> search(String index, String query, Class<T> clazz) {
        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(index)
                .query(q -> q.term(t -> t.field("url").value(value -> value.stringValue(query))))
                .build();
        try {
            final SearchResponse<T> search = this.openSearchClient.search(searchRequest, clazz);
            return search.hits().hits().stream().map(Hit::source).toList();
        } catch (IOException e) {
            log.error("Failed to search documents in index: {}", index, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * @param index   index name
     * @param ids   the list of document ids to search for
     * @param clazz the class of the document type
     * @return the list of documents found
     */
    public List<T> searchDocumentByIds(String index, List<String> ids, Class<T> clazz) {
        SearchRequest searchRequest = new SearchRequest.Builder()
              .index(index)
              .query(q -> q.ids(idsQuery -> idsQuery.values(ids)))
              .build();
        try {
            final SearchResponse<T> searchResponse = openSearchClient.search(searchRequest, clazz);
            return searchResponse.hits().hits().stream().map(Hit::source).toList();
        } catch (IOException e) {
            log.error("Failed to search documents by IDs in index: {}", index, e);
        }
        return Collections.emptyList();
    }

    // batch update documents by ids
    public void bulkUpdateDocumentByIds(String index, List<T> documents, Function<T, String> idGenerator) {
        final List<BulkOperation> bulkOperations = documents.stream().map(doc -> new BulkOperation.Builder()
                        .index(IndexOperation.of(io -> io.index(index).id(idGenerator.apply(doc)).document(doc)))
                        .build())
                .toList();
        final BulkRequest bulkRequest = new BulkRequest.Builder().index(index).operations(bulkOperations).build();
        try {
            final BulkResponse bulkResponse = this.openSearchClient.bulk(bulkRequest);
            if (bulkResponse.errors()) {
                log.error("Failed to update some documents in bulk operation for index: {}", index);
            }
        } catch (IOException e) {
            log.error("Failed to execute bulk index operation for index: {}", index, e);
            throw new RuntimeException(e);
        }
    }

    // bulk delete documents by ids
    public void bulkDeleteDocumentByIds(String index, List<String> ids) {
        final List<BulkOperation> bulkOperations = ids.stream().map(id -> new BulkOperation.Builder()
                       .delete(DeleteOperation.of(io -> io.index(index).id(id)))
                       .build())
               .toList();
        final BulkRequest bulkRequest = new BulkRequest.Builder().operations(bulkOperations).build();
        try {
            final BulkResponse bulkResponse = this.openSearchClient.bulk(bulkRequest);
            if (bulkResponse.errors()) {
                log.error("Failed to delete some documents in bulk operation for index: {}", index);
            }
        } catch (IOException e) {
            log.error("Failed to execute bulk index operation for index: {}", index, e);
            throw new RuntimeException(e);
        }
    }
}
