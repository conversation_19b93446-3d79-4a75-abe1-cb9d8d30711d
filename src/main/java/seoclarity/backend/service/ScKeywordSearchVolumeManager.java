package seoclarity.backend.service;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.MonthlySenderInfoEntityDAO;
import seoclarity.backend.dao.actonia.RgSenderInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordCityAdwordsDataEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordAdwordsEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.entity.WordTokenizerEntity;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.SolrInfoEntity;
import seoclarity.backend.entity.rankcheck.RGSenderInfoEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordAdwordsEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.onetime.MonthlyKeywordCleanUpCommand;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

/**
 * 
 * <AUTHOR>
 * @date 2020-09-25
 * @path seoclarity.backend.service.ScKeywordSearchVolumeManager
 *
 */
@Component
public class ScKeywordSearchVolumeManager {
	
	public static final int QUERY_AVG_TYPE = 1;
	
	public static final int QUERY_BEST_TYPE = 2;
	
	public static final int QUERY_ALL_TYPE = 3;
	
	private static JsonMapper mapper = new JsonMapper();

    @Resource
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	
    @Resource
    private KeywordCityAdwordsDataEntityDAO scKeywordCityAdwordsDataEntityDAO;
    
    @Resource
    private KeywordNameRelService keywordNameRelService;
    
    @Resource
    private SeoClarityKeywordAdwordsEntityDAO seoClarityKeywordAdwordsEntityDAO;

	@Resource
	private MonthlySenderInfoEntityDAO monthlySenderInfoEntityDAO;

	@Resource
	private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;

	@Resource
	private RgSenderInfoEntityDAO rgSenderInfoEntityDAO;

	public static Map<String, String> engineLanguageCountryCodeMap = new HashMap<>();
	public static Map<String, String> engineLanguageFullLanguageMap = new HashMap<>();

	public static Map<String, String> languageNameMap = new LinkedHashMap<String, String>();
	public static Map<String, String> countryAlphaMapping = new LinkedHashMap<String, String>();
	public Map<String,String> languageMap = new HashMap<>();
//    public ScKeywordSearchVolumeManager() {
//    	seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
//    	scKeywordCityAdwordsDataEntityDAO = SpringBeanFactory.getBean("keywordCityAdwordsDataEntityDAO");
//    	scKeywordAdwordsDataEntityDAO = SpringBeanFactory.getBean("scKeywordAdwordsDataEntityDAO");
//    	keywordNameRelService = new KeywordNameRelService();
//    	keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
//    	seoClarityKeywordAdwordsEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordAdwordsEntityDAO");
//    }
//	

	static {
		languageNameMap.put("ar", "Arabic");
		languageNameMap.put("da", "Danish");
		languageNameMap.put("de", "German");
		languageNameMap.put("en", "English");
		languageNameMap.put("es", "Spanish");
		languageNameMap.put("fi", "Finnish");
		languageNameMap.put("fr", "French");
		languageNameMap.put("it", "Italian");
		languageNameMap.put("ja", "Japanese");
		languageNameMap.put("lb", "German");
		languageNameMap.put("mk", "English");
		languageNameMap.put("nl", "Dutch");
		languageNameMap.put("pt", "Portuguese");
		languageNameMap.put("ru", "Russian");
		languageNameMap.put("sk", "Polish");
		languageNameMap.put("sv", "Swedish");
		languageNameMap.put("zh", "Chinese");
		languageNameMap.put("zh-tw", "Chinese");

		countryAlphaMapping.put("AFG", "AF");
		countryAlphaMapping.put("ALA", "AX");
		countryAlphaMapping.put("ALB", "AL");
		countryAlphaMapping.put("DZA", "DZ");
		countryAlphaMapping.put("ASM", "AS");
		countryAlphaMapping.put("AND", "AD");
		countryAlphaMapping.put("AGO", "AO");
		countryAlphaMapping.put("AIA", "AI");
		countryAlphaMapping.put("ATA", "AQ");
		countryAlphaMapping.put("ATG", "AG");
		countryAlphaMapping.put("ARG", "AR");
		countryAlphaMapping.put("ARM", "AM");
		countryAlphaMapping.put("ABW", "AW");
		countryAlphaMapping.put("AUS", "AU");
		countryAlphaMapping.put("AUT", "AT");
		countryAlphaMapping.put("AZE", "AZ");
		countryAlphaMapping.put("BHS", "BS");
		countryAlphaMapping.put("BHR", "BH");
		countryAlphaMapping.put("BGD", "BD");
		countryAlphaMapping.put("BRB", "BB");
		countryAlphaMapping.put("BLR", "BY");
		countryAlphaMapping.put("BEL", "BE");
		countryAlphaMapping.put("BLZ", "BZ");
		countryAlphaMapping.put("BEN", "BJ");
		countryAlphaMapping.put("BMU", "BM");
		countryAlphaMapping.put("BTN", "BT");
		countryAlphaMapping.put("BOL", "BO");
		countryAlphaMapping.put("BES", "BQ");
		countryAlphaMapping.put("BIH", "BA");
		countryAlphaMapping.put("BWA", "BW");
		countryAlphaMapping.put("BVT", "BV");
		countryAlphaMapping.put("BRA", "BR");
		countryAlphaMapping.put("IOT", "IO");
		countryAlphaMapping.put("BRN", "BN");
		countryAlphaMapping.put("BGR", "BG");
		countryAlphaMapping.put("BFA", "BF");
		countryAlphaMapping.put("BDI", "BI");
		countryAlphaMapping.put("CPV", "CV");
		countryAlphaMapping.put("KHM", "KH");
		countryAlphaMapping.put("CMR", "CM");
		countryAlphaMapping.put("CAN", "CA");
		countryAlphaMapping.put("CYM", "KY");
		countryAlphaMapping.put("CAF", "CF");
		countryAlphaMapping.put("TCD", "TD");
		countryAlphaMapping.put("CHL", "CL");
		countryAlphaMapping.put("CHN", "CN");
		countryAlphaMapping.put("CXR", "CX");
		countryAlphaMapping.put("CCK", "CC");
		countryAlphaMapping.put("COL", "CO");
		countryAlphaMapping.put("COM", "KM");
		countryAlphaMapping.put("COG", "CG");
		countryAlphaMapping.put("COD", "CD");
		countryAlphaMapping.put("COK", "CK");
		countryAlphaMapping.put("CRI", "CR");
		countryAlphaMapping.put("CIV", "CI");
		countryAlphaMapping.put("HRV", "HR");
		countryAlphaMapping.put("CUB", "CU");
		countryAlphaMapping.put("CUW", "CW");
		countryAlphaMapping.put("CYP", "CY");
		countryAlphaMapping.put("CZE", "CZ");
		countryAlphaMapping.put("DNK", "DK");
		countryAlphaMapping.put("DJI", "DJ");
		countryAlphaMapping.put("DMA", "DM");
		countryAlphaMapping.put("DOM", "DO");
		countryAlphaMapping.put("ECU", "EC");
		countryAlphaMapping.put("EGY", "EG");
		countryAlphaMapping.put("SLV", "SV");
		countryAlphaMapping.put("GNQ", "GQ");
		countryAlphaMapping.put("ERI", "ER");
		countryAlphaMapping.put("EST", "EE");
		countryAlphaMapping.put("SWZ", "SZ");
		countryAlphaMapping.put("ETH", "ET");
		countryAlphaMapping.put("FLK", "FK");
		countryAlphaMapping.put("FRO", "FO");
		countryAlphaMapping.put("FJI", "FJ");
		countryAlphaMapping.put("FIN", "FI");
		countryAlphaMapping.put("FRA", "FR");
		countryAlphaMapping.put("GUF", "GF");
		countryAlphaMapping.put("PYF", "PF");
		countryAlphaMapping.put("ATF", "TF");
		countryAlphaMapping.put("GAB", "GA");
		countryAlphaMapping.put("GMB", "GM");
		countryAlphaMapping.put("GEO", "GE");
		countryAlphaMapping.put("DEU", "DE");
		countryAlphaMapping.put("GHA", "GH");
		countryAlphaMapping.put("GIB", "GI");
		countryAlphaMapping.put("GRC", "GR");
		countryAlphaMapping.put("GRL", "GL");
		countryAlphaMapping.put("GRD", "GD");
		countryAlphaMapping.put("GLP", "GP");
		countryAlphaMapping.put("GUM", "GU");
		countryAlphaMapping.put("GTM", "GT");
		countryAlphaMapping.put("GGY", "GG");
		countryAlphaMapping.put("GIN", "GN");
		countryAlphaMapping.put("GNB", "GW");
		countryAlphaMapping.put("GUY", "GY");
		countryAlphaMapping.put("HTI", "HT");
		countryAlphaMapping.put("HMD", "HM");
		countryAlphaMapping.put("VAT", "VA");
		countryAlphaMapping.put("HND", "HN");
		countryAlphaMapping.put("HKG", "HK");
		countryAlphaMapping.put("HUN", "HU");
		countryAlphaMapping.put("ISL", "IS");
		countryAlphaMapping.put("IND", "IN");
		countryAlphaMapping.put("IDN", "ID");
		countryAlphaMapping.put("IRN", "IR");
		countryAlphaMapping.put("IRQ", "IQ");
		countryAlphaMapping.put("IRL", "IE");
		countryAlphaMapping.put("IMN", "IM");
		countryAlphaMapping.put("ISR", "IL");
		countryAlphaMapping.put("ITA", "IT");
		countryAlphaMapping.put("JAM", "JM");
		countryAlphaMapping.put("JPN", "JP");
		countryAlphaMapping.put("JEY", "JE");
		countryAlphaMapping.put("JOR", "JO");
		countryAlphaMapping.put("KAZ", "KZ");
		countryAlphaMapping.put("KEN", "KE");
		countryAlphaMapping.put("KIR", "KI");
		countryAlphaMapping.put("PRK", "KP");
		countryAlphaMapping.put("KOR", "KR");
		countryAlphaMapping.put("KWT", "KW");
		countryAlphaMapping.put("KGZ", "KG");
		countryAlphaMapping.put("LAO", "LA");
		countryAlphaMapping.put("LVA", "LV");
		countryAlphaMapping.put("LBN", "LB");
		countryAlphaMapping.put("LSO", "LS");
		countryAlphaMapping.put("LBR", "LR");
		countryAlphaMapping.put("LBY", "LY");
		countryAlphaMapping.put("LIE", "LI");
		countryAlphaMapping.put("LTU", "LT");
		countryAlphaMapping.put("LUX", "LU");
		countryAlphaMapping.put("MAC", "MO");
		countryAlphaMapping.put("MDG", "MG");
		countryAlphaMapping.put("MWI", "MW");
		countryAlphaMapping.put("MYS", "MY");
		countryAlphaMapping.put("MDV", "MV");
		countryAlphaMapping.put("MLI", "ML");
		countryAlphaMapping.put("MLT", "MT");
		countryAlphaMapping.put("MHL", "MH");
		countryAlphaMapping.put("MTQ", "MQ");
		countryAlphaMapping.put("MRT", "MR");
		countryAlphaMapping.put("MUS", "MU");
		countryAlphaMapping.put("MYT", "YT");
		countryAlphaMapping.put("MEX", "MX");
		countryAlphaMapping.put("FSM", "FM");
		countryAlphaMapping.put("MDA", "MD");
		countryAlphaMapping.put("MCO", "MC");
		countryAlphaMapping.put("MNG", "MN");
		countryAlphaMapping.put("MNE", "ME");
		countryAlphaMapping.put("MSR", "MS");
		countryAlphaMapping.put("MAR", "MA");
		countryAlphaMapping.put("MOZ", "MZ");
		countryAlphaMapping.put("MMR", "MM");
		countryAlphaMapping.put("NAM", "NA");
		countryAlphaMapping.put("NRU", "NR");
		countryAlphaMapping.put("NPL", "NP");
		countryAlphaMapping.put("NLD", "NL");
		countryAlphaMapping.put("NCL", "NC");
		countryAlphaMapping.put("NZL", "NZ");
		countryAlphaMapping.put("NIC", "NI");
		countryAlphaMapping.put("NER", "NE");
		countryAlphaMapping.put("NGA", "NG");
		countryAlphaMapping.put("NIU", "NU");
		countryAlphaMapping.put("NFK", "NF");
		countryAlphaMapping.put("MKD", "MK");
		countryAlphaMapping.put("MNP", "MP");
		countryAlphaMapping.put("NOR", "NO");
		countryAlphaMapping.put("OMN", "OM");
		countryAlphaMapping.put("PAK", "PK");
		countryAlphaMapping.put("PLW", "PW");
		countryAlphaMapping.put("PSE", "PS");
		countryAlphaMapping.put("PAN", "PA");
		countryAlphaMapping.put("PNG", "PG");
		countryAlphaMapping.put("PRY", "PY");
		countryAlphaMapping.put("PER", "PE");
		countryAlphaMapping.put("PHL", "PH");
		countryAlphaMapping.put("PCN", "PN");
		countryAlphaMapping.put("POL", "PL");
		countryAlphaMapping.put("PRT", "PT");
		countryAlphaMapping.put("PRI", "PR");
		countryAlphaMapping.put("QAT", "QA");
		countryAlphaMapping.put("REU", "RE");
		countryAlphaMapping.put("ROU", "RO");
		countryAlphaMapping.put("RUS", "RU");
		countryAlphaMapping.put("RWA", "RW");
		countryAlphaMapping.put("BLM", "BL");
		countryAlphaMapping.put("SHN", "SH");
		countryAlphaMapping.put("KNA", "KN");
		countryAlphaMapping.put("LCA", "LC");
		countryAlphaMapping.put("MAF", "MF");
		countryAlphaMapping.put("SPM", "PM");
		countryAlphaMapping.put("VCT", "VC");
		countryAlphaMapping.put("WSM", "WS");
		countryAlphaMapping.put("SMR", "SM");
		countryAlphaMapping.put("STP", "ST");
		countryAlphaMapping.put("SAU", "SA");
		countryAlphaMapping.put("SEN", "SN");
		countryAlphaMapping.put("SRB", "RS");
		countryAlphaMapping.put("SYC", "SC");
		countryAlphaMapping.put("SLE", "SL");
		countryAlphaMapping.put("SGP", "SG");
		countryAlphaMapping.put("SXM", "SX");
		countryAlphaMapping.put("SVK", "SK");
		countryAlphaMapping.put("SVN", "SI");
		countryAlphaMapping.put("SLB", "SB");
		countryAlphaMapping.put("SOM", "SO");
		countryAlphaMapping.put("ZAF", "ZA");
		countryAlphaMapping.put("SGS", "GS");
		countryAlphaMapping.put("SSD", "SS");
		countryAlphaMapping.put("ESP", "ES");
		countryAlphaMapping.put("LKA", "LK");
		countryAlphaMapping.put("SDN", "SD");
		countryAlphaMapping.put("SUR", "SR");
		countryAlphaMapping.put("SJM", "SJ");
		countryAlphaMapping.put("SWE", "SE");
		countryAlphaMapping.put("CHE", "CH");
		countryAlphaMapping.put("SYR", "SY");
		countryAlphaMapping.put("TWN", "TW");
		countryAlphaMapping.put("TJK", "TJ");
		countryAlphaMapping.put("TZA", "TZ");
		countryAlphaMapping.put("THA", "TH");
		countryAlphaMapping.put("TLS", "TL");
		countryAlphaMapping.put("TGO", "TG");
		countryAlphaMapping.put("TKL", "TK");
		countryAlphaMapping.put("TON", "TO");
		countryAlphaMapping.put("TTO", "TT");
		countryAlphaMapping.put("TUN", "TN");
		countryAlphaMapping.put("TUR", "TR");
		countryAlphaMapping.put("TKM", "TM");
		countryAlphaMapping.put("TCA", "TC");
		countryAlphaMapping.put("TUV", "TV");
		countryAlphaMapping.put("UGA", "UG");
		countryAlphaMapping.put("UKR", "UA");
		countryAlphaMapping.put("ARE", "AE");
		countryAlphaMapping.put("GBR", "GB");
		countryAlphaMapping.put("USA", "US");
		countryAlphaMapping.put("UMI", "UM");
		countryAlphaMapping.put("URY", "UY");
		countryAlphaMapping.put("UZB", "UZ");
		countryAlphaMapping.put("VUT", "VU");
		countryAlphaMapping.put("VEN", "VE");
		countryAlphaMapping.put("VNM", "VN");
		countryAlphaMapping.put("VGB", "VG");
		countryAlphaMapping.put("VIR", "VI");
		countryAlphaMapping.put("WLF", "WF");
		countryAlphaMapping.put("ESH", "EH");
		countryAlphaMapping.put("YEM", "YE");
		countryAlphaMapping.put("ZMB", "ZM");
		countryAlphaMapping.put("ZWE", "ZW");

	}
    public SeoClarityKeywordEntity getCleanupKeyword(KeywordEntity keywordEntity, boolean isCleanUp) {
    	String keywordName = keywordNameRelService.getKeywordName(keywordEntity);
    	
    	System.out.println(keywordName);
    	
    	String encodeAlterText = keywordName;
    	
    	if (isCleanUp) {
			try {
				String originalText = URLDecoder.decode(keywordName, "utf-8");
				String alterText = originalText;
				int tmp = 0;
				do {
					Map<String, String> processInfo = MonthlyKeywordCleanUpCommand.cleanupKeyowrds(alterText);
					if (StringUtils.equalsIgnoreCase(processInfo.get("needReprocess"), "false")) {
						break;
					}
					alterText = processInfo.get("canonicalkeyword");
					tmp++;
					if (tmp > 100)
						System.out.println("----------------------ERROR Orig Text:" + originalText
								+ "----------------------------");
				} while (true);
	
				encodeAlterText = URLEncoder.encode(alterText, "utf-8");
			} catch (Exception e) {
				e.printStackTrace();
			}
    	}
		if(encodeAlterText == null) {
			System.out.println("keyword_entity can not find keyword, orginal : " + keywordName);
			return null;
		}

		SeoClarityKeywordEntity scKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(encodeAlterText);
		if (scKeywordEntity == null) {
			System.out.println("keyword_entity can not find keyword :, encodeAlterText " + encodeAlterText);
			return null;
		}
		System.out.println(scKeywordEntity.getId());
		return scKeywordEntity;
    }
    
    public Map<String, Object> getAvgSearchVolumeAndCPCForCleanedKeyword(int searchEngineId, int languageId, KeywordEntity keywordEntity,int cityID, boolean isCleanUp) {
    	String keywordName = keywordNameRelService.getKeywordName(keywordEntity);
    	System.out.println(" ==QueryCityAdwords KW:" + keywordName);
    	String encodeAlterText = keywordName;
    	if (isCleanUp) {
			try {
				String originalText = URLDecoder.decode(keywordName, "utf-8");
				String alterText = originalText;
				int tmp = 0;
				do {
					Map<String, String> processInfo = MonthlyKeywordCleanUpCommand.cleanupKeyowrds(alterText);
					if (StringUtils.equalsIgnoreCase(processInfo.get("needReprocess"), "false")) {
						break;
					}
					alterText = processInfo.get("canonicalkeyword");
					tmp++;
					if (tmp > 100)
						System.out.println("-------ERROR Orig Text:" + originalText + "------");
				} while (true);
	
				encodeAlterText = URLEncoder.encode(alterText, "utf-8");
			} catch (Exception e) {
				e.printStackTrace();
			}
    	}
		if(encodeAlterText == null) {
			System.out.println("keyword_entity can not find keyword, orginal : " + keywordName);
			return null;
		}

		SeoClarityKeywordEntity scKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(encodeAlterText);
		if (scKeywordEntity == null) {
			System.out.println("keyword_entity can not find keyword :, encodeAlterText " + encodeAlterText);
			return null;
		}
		System.out.println(scKeywordEntity.getId());
		List<Map<String, Object>> res = scKeywordCityAdwordsDataEntityDAO.getAvgSearchVolumeAndCPC(scKeywordEntity.getId(), languageId, searchEngineId, cityID);
		if (res != null  && res.size() > 0) {
			return res.get(0);
		} else {
			return null;
		}
    }
    
    public static String getSolrUrlRandomStatic(SolrInfoEntity solrInfo) {
    	if (solrInfo == null || StringUtils.isBlank(solrInfo.getSolrUrl())) {
    		System.out.println("-------------solr_url is empty");
    		return null;
    	}
    	
    	//If the list of Ips is null, then use the IP address provided.
    	if (StringUtils.isBlank(solrInfo.getIpList())) {
    		return solrInfo.getSolrUrl();
    	}
    	
    	String[] ipList = mapper.fromJson(solrInfo.getIpList(), String[].class);
    	if (ipList == null || ipList.length == 0) {
    		return solrInfo.getSolrUrl();
    	}
    	
    	//get the core name from the solr_url column and then randomly pick one ip 
    	//from the list of ips and query it. 
    	int arrayIndex = RandomUtils.nextInt(ipList.length);
    	String randomIp = ipList[arrayIndex];
    	System.out.println("-------random for ip [" + arrayIndex + "]");
    	
    	// http://*************:8983/solr/monthlyranking_IT_201505
    	String solrUrl = solrInfo.getSolrUrl();
    	String protocol = StringUtils.substringBefore(solrUrl, "://");
    	solrUrl = StringUtils.substringAfter(solrUrl, "://");
    	
    	StringBuilder randomSolrUrl = new StringBuilder();
    	randomSolrUrl.append(protocol).append("://");
    	randomSolrUrl.append(randomIp).append(":8983/");
    	randomSolrUrl.append(StringUtils.substringAfter(solrUrl, "/"));
    	
    	return randomSolrUrl.toString();
    }
    
    
	//https://www.wrike.com/open.htm?id=271687280
	//by Sunny
	public static String cleanupMonthlyKeyword(String originalKeyword) {
		
    	String encodeAlterText = null;
		try {
			String originalText = URLDecoder.decode(originalKeyword, "utf-8");
			String alterText = originalText;
			int tmp = 0;
			do {
				Map<String, String> processInfo = MonthlyKeywordCleanUpCommand.cleanupKeyowrds(alterText);
				if (StringUtils.equalsIgnoreCase(processInfo.get("needReprocess"), "false")) {
					break;
				}
				alterText = processInfo.get("canonicalkeyword");
				tmp++;
				if (tmp > 100)
					System.out.println("----------------------ERROR Orig Text:" + originalText
							+ "----------------------------");
			} while (true);

			encodeAlterText = URLEncoder.encode(alterText, "utf-8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		if(StringUtils.isBlank(encodeAlterText)) {
			System.out.println(" EmptyCleanedKW,orginal:" + originalKeyword);
			return null;
		}
		
		return encodeAlterText;
		
	}
	
	public int getSearchEngineIdForAdwords(OwnDomainEntity ownDomain) {
        return 99; // default google
    }
	
	public SeoClarityKeywordAdwordsEntity getSearchVolumeEntity(KeywordEntity keywordEntity, int engineId, int languageId, int cityId, int type) {
			String keywordName = keywordNameRelService.getKeywordName(keywordEntity);
	//		System.out.println("===cleanup:" + keywordEntity.getKeywordName() + " : " + keywordEntity.getId() +" , === " + keywordName);
			if (StringUtils.isBlank(keywordName)) {
				System.out.println(" EmptyKWName:" + keywordEntity.getId());
				return null;
			}
			return getSearchVolumeEntity(keywordName, engineId, languageId, cityId, type);
	}
	
	public SeoClarityKeywordAdwordsEntity getSearchVolumeEntityRetry(KeywordEntity keywordEntity, int engineId, int languageId, int cityId, int type) {
		int retryCnt = 2;
		int cnt = 0;
		try {
			return getSearchVolumeEntity(keywordEntity, engineId, languageId, cityId, type);
		} catch (Exception e) {
			while (cnt < retryCnt) {
				cnt ++;
				try {
					Thread.sleep(100);
					return getSearchVolumeEntity(keywordEntity, engineId, languageId, cityId, type);
				} catch (Exception e1) {
					if (cnt >= retryCnt) {
						System.out.println("=Get sv entity failed, re-try cnt:" + cnt);
						e1.printStackTrace();
					}
				}
			}
		}
		return null;
	}
	
	public SeoClarityKeywordAdwordsEntity getSearchVolumeEntity(String keywordName, int engineId, int languageId, int cityId, int type) {

		String cleanupedKeywordName = cleanupMonthlyKeyword(keywordName);

//		System.out.println("===cleanup:before = "+ keywordName + ", after = " + cleanupedKeywordName);
		
		SeoClarityKeywordAdwordsEntity resultEntity = null;
		
		if (StringUtils.isNotBlank(cleanupedKeywordName)) {
			
			SeoClarityKeywordEntity seoClarityKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(cleanupedKeywordName);
			
			if (seoClarityKeywordEntity == null) {
	            System.out.println(" NotFoundCleanedKW:" + cleanupedKeywordName);
			} else {
				if (type == QUERY_BEST_TYPE) {
					resultEntity = seoClarityKeywordAdwordsEntityDAO.getBestSearchVolumeEntity(seoClarityKeywordEntity.getId(), engineId, languageId, cityId);
				} else if (type == QUERY_BEST_TYPE) {
					resultEntity = seoClarityKeywordAdwordsEntityDAO.getAvgCpcEntity(seoClarityKeywordEntity.getId(), engineId, languageId, cityId);
				} else {
					resultEntity = seoClarityKeywordAdwordsEntityDAO.getAllSearchVolumeEntity(seoClarityKeywordEntity.getId(), engineId, languageId, cityId);
				}
			}
			
		}
		
		if (resultEntity == null && !StringUtils.equals(keywordName, cleanupedKeywordName) ) {
			SeoClarityKeywordEntity seoClarityKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(keywordName);
			
			if (seoClarityKeywordEntity == null) {
	            System.out.println(" NotFoundOriginalKW" + keywordName);
			} else {
				if (type == QUERY_BEST_TYPE) {
					resultEntity = seoClarityKeywordAdwordsEntityDAO.getBestSearchVolumeEntity(seoClarityKeywordEntity.getId(), engineId, languageId, cityId);
				} else if (type == QUERY_BEST_TYPE) {
					resultEntity = seoClarityKeywordAdwordsEntityDAO.getAvgCpcEntity(seoClarityKeywordEntity.getId(), engineId, languageId, cityId);
				} else {
					resultEntity = seoClarityKeywordAdwordsEntityDAO.getAllSearchVolumeEntity(seoClarityKeywordEntity.getId(), engineId, languageId, cityId);
				}
			}
		}
		
		return resultEntity;
		
	}

	public void getSearchVolumeEntity(List<KeywordProperty> keywords, int languageId, int city) {
		List<KeywordEntity> kwList = new ArrayList<KeywordEntity>();
		int size = 300;
		Map<Integer, SeoClarityKeywordAdwordsEntity> map = new HashMap<Integer, SeoClarityKeywordAdwordsEntity>();

		for (KeywordProperty kp : keywords) {
			KeywordEntity kwEntity = new KeywordEntity();
			kwEntity.setKeywordName(kp.getKeywordText());
			kwEntity.setId(kp.getId() == null ? null : kp.getId().longValue());
			kwList.add(kwEntity);
			if (kwList.size() >= size) {
				try {
					map.putAll(getSearchVolumeEntity(kwList.toArray(new KeywordEntity[kwList.size()]), getSearchEngineIdForAdwords(null), languageId, city, ScKeywordSearchVolumeManager.QUERY_AVG_TYPE));
				} catch (Exception e) {
					e.printStackTrace();
				}
				kwList.clear();
			}
		}

		if (kwList.size() > 0) {
			try {
				map.putAll(getSearchVolumeEntity(kwList.toArray(new KeywordEntity[kwList.size()]), getSearchEngineIdForAdwords(null), languageId, city, ScKeywordSearchVolumeManager.QUERY_AVG_TYPE));
			} catch (Exception e) {
				e.printStackTrace();
			}
			kwList.clear();
		}

		for (KeywordProperty kp : keywords) {
			try {
				SeoClarityKeywordAdwordsEntity svEntity = map.get(kp.getId().intValue());
				if (svEntity == null) {
					kp.setSearchVol(0);
					kp.setCpc(0f);
					kp.setNotRealSearchVolume(true);
				} else {
					kp.setSearchVol(svEntity.getAvgMonthlySearchVolume() == null ? 0 : svEntity.getAvgMonthlySearchVolume());
					kp.setCpc(svEntity.getCostPerClick() == null ? 0f : Float.valueOf(svEntity.getCostPerClick().toString()));
					kp.setNotRealSearchVolume(false);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public Map<Integer, SeoClarityKeywordAdwordsEntity> getSearchVolumeEntity(KeywordEntity[] keywords, int engineId, int languageId, int cityId, int type) {
		Map<Integer, String> sameKeywordEntities = new HashMap<>();
		Map<Integer, String> diffKeywordEntitiesAfter = new HashMap<>();
		Map<Integer, String> diffKeywordEntitiesBefore = new HashMap<>();
		for(KeywordEntity keywordEntity : keywords){
			String keywordName = keywordNameRelService.getKeywordName(keywordEntity);
			String cleanupedKeywordName = cleanupMonthlyKeyword(keywordName);
			if(keywordName.equals(cleanupedKeywordName)){
				sameKeywordEntities.put(keywordEntity.getId().intValue(), cleanupedKeywordName);
			}else {
				diffKeywordEntitiesBefore.put(keywordEntity.getId().intValue(), keywordName);
				diffKeywordEntitiesAfter.put(keywordEntity.getId().intValue(), cleanupedKeywordName);
//				System.out.println("===cleanup:"+ keywordEntity.getKeywordName() + " = "+ keywordEntity.getId() +" , === before = "+keywordName + ", after = "+cleanupedKeywordName);
			}
		}

		Map<Integer, SeoClarityKeywordAdwordsEntity> resultMapSame = common(sameKeywordEntities,engineId, languageId, cityId, type);
		System.out.println("same.map.size:"+resultMapSame.size());

//		Set<Integer> setKeys = sameKeywordEntities.keySet();
//		Set<Integer> setKeysDiffAfter = diffKeywordEntitiesAfter.keySet();
//
//		Map<Integer, String> diffMapAfter = new HashMap<>();
//		Map<Integer, String> newSetKeysDiffBefore = new HashMap<>();
//		for(Integer iDiff : setKeysDiffAfter){
//			boolean b = true;
//			for(Integer integer : setKeys){
//				if(sameKeywordEntities.get(integer).equals(diffKeywordEntitiesAfter.get(iDiff))){
//					resultMapSame.put(iDiff, resultMapSame.get(iDiff));
//					b = false;
//					break;
//				}
//			}
//			if(b){
//				diffMapAfter.put(iDiff,diffKeywordEntitiesAfter.get(iDiff));
//				newSetKeysDiffBefore.put(iDiff,diffKeywordEntitiesBefore.get(iDiff));
//			}
//		}

		Map<Integer, SeoClarityKeywordAdwordsEntity> resultMapDiffAfter = common(diffKeywordEntitiesAfter,engineId, languageId, cityId, type);
		System.out.println("diffAfter.map.size:"+resultMapDiffAfter.size());

//		Set<Integer> set = resultMapDiffAfter.keySet();
//		List<Integer> list = new ArrayList<>(set);
//		Set<Integer> setKeysDiffBefore = newSetKeysDiffBefore.keySet();
//		Map<Integer, String> diffMapBefore = new HashMap<>();
//		for(Integer integer : setKeysDiffBefore){
//			if(!list.contains(integer)){
//				diffMapBefore.put(integer, newSetKeysDiffBefore.get(integer));
//			}
//		}
		Map<Integer, SeoClarityKeywordAdwordsEntity> resultMapDiffBefore = common(diffKeywordEntitiesBefore,engineId, languageId, cityId, type);
		System.out.println("diffBefore.map.size:"+resultMapDiffBefore.size());

		Map<Integer, SeoClarityKeywordAdwordsEntity> map = new HashMap<>();
		map.putAll(resultMapDiffBefore);
		map.putAll(resultMapSame);
		map.putAll(resultMapDiffAfter);
		return map;
	}
	
	public Map<Integer, SeoClarityKeywordAdwordsEntity> common(Map<Integer, String>cleanupMap, int engineId, int languageId, int cityId, int type){
		Set<Integer> keywordIdSet = cleanupMap.keySet();
		Map<Integer, Integer> keywordIdSeoKeywordIdMap = new HashMap<>();
		Map<Integer, SeoClarityKeywordAdwordsEntity> resultMap = new HashMap<>();
		Collection<String> c = cleanupMap.values();
		List<SeoClarityKeywordEntity> seoClarityKeywordEntities = seoClarityKeywordEntityDAO.getByKeywords(new ArrayList(c));
		if(seoClarityKeywordEntities != null){
			for(Integer keywordId : keywordIdSet){
				for(SeoClarityKeywordEntity seoClarityKeywordEntity :seoClarityKeywordEntities){
					if(seoClarityKeywordEntity != null && seoClarityKeywordEntity.getKeywordText() !=null && cleanupMap.get(keywordId) != null && cleanupMap.get(keywordId).equalsIgnoreCase(seoClarityKeywordEntity.getKeywordText())){
						keywordIdSeoKeywordIdMap.put(keywordId, seoClarityKeywordEntity.getId());
						break;
					}
				}
			}
		}

		Collection<Integer> setValues = keywordIdSeoKeywordIdMap.values();
		List<Integer> integers = new ArrayList(setValues);
		List<SeoClarityKeywordAdwordsEntity> list = new ArrayList<>();
		if(integers.size()>0){
			if (type == QUERY_BEST_TYPE) {
				System.out.println("integers.size:" + integers.size());
				list = seoClarityKeywordAdwordsEntityDAO.getBestSearchVolumeEntity(integers, engineId, languageId, cityId);
			} else if (type == QUERY_BEST_TYPE) {
				System.out.println("integers.size:" + integers.size());
				list = seoClarityKeywordAdwordsEntityDAO.getAvgCpcEntity(integers, engineId, languageId, cityId);
			} else {
				//System.out.println("integers.size:" + integers.size());
				list = seoClarityKeywordAdwordsEntityDAO.getAllSearchVolumeEntity(integers, engineId, languageId, cityId);
			}
		}
		//System.out.println("batch.query:"+list.size());
		Set<Integer> filterKeywordIdSet = keywordIdSeoKeywordIdMap.keySet();
		for(Integer keywordId : filterKeywordIdSet){
			for(SeoClarityKeywordAdwordsEntity seoClarityKeywordAdwordsEntity : list){
				if(keywordIdSeoKeywordIdMap.get(keywordId).equals(seoClarityKeywordAdwordsEntity.getKeywordId())){
					resultMap.put(keywordId, seoClarityKeywordAdwordsEntity);
				}
			}
		}
		return resultMap;
	}

	public Map<String, SeoClarityKeywordAdwordsEntity> getSearchVolumeEntityMap(List<KeywordEntity> keywordEntityList, int engineId, int languageId, int cityId, int type) {

    	Map<String, SeoClarityKeywordAdwordsEntity> resultMap = new HashMap<>();

    	List<KeywordEntity> processingList = new ArrayList<>();

    	for(KeywordEntity keywordEntity : keywordEntityList){

			processingList.add(keywordEntity);
			if(processingList.size() >= 200){
				Map<String, String> keywordNameMap = keywordNameRelService.getKeywordNameMap(processingList);
				System.out.println("===keywordNameMap size : " + keywordNameMap.size());
				if(!CollectionUtils.isEmpty(keywordNameMap)){
					resultMap.putAll(getSearchVolumeEntityByKeywords(keywordNameMap, engineId, languageId, cityId, type));
				}
				processingList.clear();
			}

		}

		if(!CollectionUtils.isEmpty(processingList)){
			Map<String, String> keywordNameMap = keywordNameRelService.getKeywordNameMap(processingList);
			System.out.println("===keywordNameMap1 size : " + keywordNameMap.size());
			if(!CollectionUtils.isEmpty(keywordNameMap)){
				resultMap.putAll(getSearchVolumeEntityByKeywords(keywordNameMap, engineId, languageId, cityId, type));
			}
		}

		System.out.println("===getSearchVolumeEntityMap size : " + resultMap.size());
		return resultMap;
	}

	public Map<String, SeoClarityKeywordAdwordsEntity> getSearchVolumeEntityByKeywords(Map<String, String> keywordNameMap, int engineId, int languageId, int cityId, int type) {

    	Map<String, SeoClarityKeywordAdwordsEntity> resultMap = new HashMap<>();
		List<SeoClarityKeywordAdwordsEntity> resultEntity = new ArrayList<>();
		List<String> cleanupedKeywordNameList = new ArrayList<>();
		List<Integer> rankCheckKeywordIdList = new ArrayList<>();

		Map<String,String> cleanupedKeywordNameMap = new HashMap<>();

    	for(String originalKeywordName: keywordNameMap.keySet()){

			String cleanupedKeywordName = cleanupMonthlyKeyword(keywordNameMap.get(originalKeywordName));
			if(originalKeywordName.equalsIgnoreCase(cleanupedKeywordName)){
				cleanupedKeywordName = originalKeywordName;
			}
			if(StringUtils.isNotBlank(cleanupedKeywordName)){
				cleanupedKeywordNameList.add(cleanupedKeywordName);
				cleanupedKeywordNameMap.put(cleanupedKeywordName, originalKeywordName);
			}

		}

		resultEntity = seoClarityKeywordAdwordsEntityDAO.getExistedAdwordListByKeywordName(cleanupedKeywordNameList, engineId, languageId, cityId);
    	if(!CollectionUtils.isEmpty(resultEntity)){
//    		System.out.println("===resultEntity size: " + resultEntity.size());
    		//remove exist keyword
			for(SeoClarityKeywordAdwordsEntity seoClarityKeywordAdwordsEntity : resultEntity){
				Iterator<Map.Entry<String, String>> it = cleanupedKeywordNameMap.entrySet().iterator();
				while(it.hasNext()){
					Map.Entry<String, String> entry = it.next();
					if(seoClarityKeywordAdwordsEntity.getKeywordName().equalsIgnoreCase(entry.getKey())){
						resultMap.put(entry.getValue(), seoClarityKeywordAdwordsEntity);
						it.remove();
					}
				}
			}
		}

		if(!CollectionUtils.isEmpty(cleanupedKeywordNameMap)){

//			System.out.println("===cleanupedKeywordNameMap " + JSON.toJSONString(cleanupedKeywordNameMap));

			rankCheckKeywordIdList.clear();

			List<String> originKeywordList = new ArrayList<>();

			for(String cleanupKeywordName : cleanupedKeywordNameMap.keySet()){
				originKeywordList.add(cleanupedKeywordNameMap.get(cleanupKeywordName));
			}

			resultEntity.clear();
			resultEntity = seoClarityKeywordAdwordsEntityDAO.getExistedAdwordListByKeywordName(originKeywordList, engineId, languageId, cityId);
			for(SeoClarityKeywordAdwordsEntity seoClarityKeywordAdwordsEntity: resultEntity){
				resultMap.put(seoClarityKeywordAdwordsEntity.getKeywordName().toLowerCase(), seoClarityKeywordAdwordsEntity);
			}

		}

		return resultMap;
	}
	
	/**
	 * jason : marge old version and don't search solr
	 * @param keywordList
	 * @param languageId
	 * @param cityId
	 * @throws Exception
	 */
	public void getSearchVolumeAndCpcValueInAdwords(List<KeywordProperty> keywordList , int languageId, int cityId) throws Exception {
		if(keywordList == null || keywordList.isEmpty()) {
			return;
		}
		if(cityId > 0) {
			int adwordsEngineId = getSearchEngineIdForAdwords(null);
			for(KeywordProperty keyword : keywordList) {
				try {
					KeywordEntity keywordEntity = new KeywordEntity();
					keywordEntity.setId(Long.parseLong(keyword.getKeywordId().toString()));
					keywordEntity.setKeywordName(keyword.getKeywordText());
					Map<String, Object> map = getAvgSearchVolumeAndCPCForCleanedKeyword(adwordsEngineId, languageId, keywordEntity, cityId, true);
					if(map == null) {
						map = getAvgSearchVolumeAndCPCForCleanedKeyword(adwordsEngineId, languageId, keywordEntity, cityId, false);
						System.out.println(" getAvgSearchVolumeAndCPC by non-cleanup keyword!!");
					}
					if(map != null) {
						if(map.get("avg_monthly_search_volume") != null) {
							keyword.setSearchVol(NumberUtils.toLong(map.get("avg_monthly_search_volume").toString()));
						}
						if(map.get("cost_per_click") != null) {
							keyword.setCpc(NumberUtils.toFloat(map.get("cost_per_click").toString()));
						}
						keyword.setNotRealSearchVolume(false);
					} else {
						keyword.setNotRealSearchVolume(true);
					}
					if (keyword.getCpc() < 0) {
						System.out.println("Warring! cpc is less than 0, kid:" + keyword.getKeywordId() + ", cpc:" + keyword.getCpc() + " -> 0");
						keyword.setCpc(0);
					}
				}catch (Exception e) {
					e.printStackTrace();
				}
			}
		} else {
			for (KeywordProperty keyword : keywordList) {
				try {
					KeywordEntity keywordEntity = new KeywordEntity();
					keywordEntity.setId(Long.parseLong(keyword.getKeywordId() == null ? "0" : keyword.getKeywordId().toString()));
					keywordEntity.setKeywordName(keyword.getKeywordText());
					int adwordsEngineId = getSearchEngineIdForAdwords(null);
//					SeoClarityKeywordAdwordsEntity svEntity = getSearchVolumeEntity(keywordEntity, adwordsEngineId, languageId, 0, QUERY_AVG_TYPE);
					SeoClarityKeywordAdwordsEntity svEntity = getSearchVolumeEntityRetry(keywordEntity, adwordsEngineId, languageId, 0, QUERY_AVG_TYPE);
					if (svEntity != null) {
						if (svEntity.getAvgMonthlySearchVolume() != null) {
							keyword.setSearchVol(svEntity.getAvgMonthlySearchVolume());
						}
						if (svEntity.getCostPerClick() != null) {
							keyword.setCpc(svEntity.getCostPerClick().floatValue());
						}
						keyword.setNotRealSearchVolume(false);
					} else {
						keyword.setNotRealSearchVolume(true);
					}
					if (keyword.getCpc() < 0) {
						System.out.println("Warring! cpc is less than 0, kid:" + keyword.getKeywordId() + ", cpc:" + keyword.getCpc() + " -> 0");
						keyword.setCpc(0);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	public WordTokenizerEntity getWordTokenizerEntity(int engineId, int languageId, String keywordName) throws Exception {
		if (!engineLanguageCountryCodeMap.containsKey(engineId+"-"+languageId)) {
			EngineCountryLanguageMappingEntity engineLanguageMappingEntity = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageIdAndRankFrom(engineId, languageId, EngineCountryLanguageMappingEntity.RANK_FROM_ALL);
			String code = engineLanguageMappingEntity.getCountryQueryName().toLowerCase();
			engineLanguageCountryCodeMap.put(engineId+"-"+languageId, code);
			if (!engineLanguageFullLanguageMap.containsKey(engineId+"-"+languageId)) {
				String languageName = engineLanguageMappingEntity.getLanguageQueryName().toLowerCase();
				String fullLanguageName = languageNameMap.get(languageName);
				engineLanguageFullLanguageMap.put(engineId+"-"+languageId, fullLanguageName);
			}
		}
		String countryCode = engineLanguageCountryCodeMap.get(engineId + "-" + languageId);
		if (countryCode == null) throw new Exception("The corresponding countryCode is not found!");
		String languageName = engineLanguageFullLanguageMap.get(engineId + "-" + languageId);
		if (languageName == null) throw new Exception("The corresponding languageName is not found!");
		return getWordTokenizerEntity(keywordName, countryCode, languageName);
	}

	private WordTokenizerEntity getWordTokenizerEntity(String keywordName, String countryCode, String languageName){
		WordTokenizerEntity wordTokenizerEntity = new WordTokenizerEntity();
		wordTokenizerEntity.setWord(SnowBallAndNgramForForeignLanguages.wordTokenizer(keywordName, countryCode).toArray(new String[0]));
		wordTokenizerEntity.setStream(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(keywordName, languageName).toArray(new String[0]));
		wordTokenizerEntity.setKeywordVariationNgram(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(keywordName, false).toArray(new String[0]));
		wordTokenizerEntity.setKeywordVariationOneWord(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(keywordName, true).toArray(new String[0]));
		return wordTokenizerEntity;
	}


	public String[] getWordSteamByCountryAlpha(String keywordName, String countryCode){
		String languageName = "";
		String twoDigitCountryCode = countryAlphaMapping.get(countryCode.toUpperCase());
		if(StringUtils.isBlank(twoDigitCountryCode)){
//			System.out.println("==not find twoDigitCountryCode:" + countryCode + ",using English.");
			languageName = "English";
		}else {
			twoDigitCountryCode = twoDigitCountryCode.toLowerCase();
			String monthlySenderLanguageName = "";

			if(languageMap.get(twoDigitCountryCode) == null){

				List<RGSenderInfoEntity> monthlySenderInfoList = rgSenderInfoEntityDAO.getByCountryName(twoDigitCountryCode);
				if(CollectionUtils.isEmpty(monthlySenderInfoList)){
//					System.out.println("==not find monthlySenderInfoList:" + twoDigitCountryCode + ",using English.");
					monthlySenderLanguageName = "en";
				}else {
					if(monthlySenderInfoList.size() == 1){
						monthlySenderLanguageName = monthlySenderInfoList.get(0).getLanguageName();
					}else {
						switch (twoDigitCountryCode){
							case "be" :
								monthlySenderLanguageName = "fr";
								break;
							case "bh" :
								monthlySenderLanguageName = "en";
								break;
							case "ca" :
								monthlySenderLanguageName = "en";
								break;
							case "ch" :
								monthlySenderLanguageName = "fr";
								break;
							case "cn" :
								monthlySenderLanguageName = "zh";
								break;
							case "kw" :
								monthlySenderLanguageName = "en";
								break;
							case "my" :
								monthlySenderLanguageName = "en";
								break;
							case "ph" :
								monthlySenderLanguageName = "en";
								break;
							case "th" :
								monthlySenderLanguageName = "nl";
								break;
							default:
								monthlySenderLanguageName = "en";
								break;
						}
					}

				}
				languageMap.put(twoDigitCountryCode,monthlySenderLanguageName);
			}else {
				monthlySenderLanguageName = languageMap.get(twoDigitCountryCode);
			}

			languageName = languageNameMap.get(monthlySenderLanguageName);
			if(StringUtils.isBlank(languageName)){
//				System.out.println("==not find languageName with monthlySenderLanguageName:" + monthlySenderLanguageName + ",using English.");
				languageName = "English";
			}

		}

//		System.out.println("===languageName:" + languageName);
		String[] streamWord = new String[]{};
		try {
			streamWord = SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(keywordName, languageName).toArray(new String[0]);
		}catch (Exception e){
//			e.printStackTrace();
			System.out.println("===languageName:" + languageName + ",keywordName:" + keywordName);
			throw e;
		}
		return streamWord;
	}

}
