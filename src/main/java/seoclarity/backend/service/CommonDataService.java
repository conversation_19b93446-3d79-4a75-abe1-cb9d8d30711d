package seoclarity.backend.service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.ScriptDeployInfoEntityDAO;
import seoclarity.backend.dao.actonia.ScriptRunInstanceEntityDAO;
import seoclarity.backend.dao.actonia.SolrInfoEntityDAO;
import seoclarity.backend.dao.actonia.bot.BotDirectoryDao;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.bot.BotDirectoryEntity;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 *
 */
@Component
public class CommonDataService {

	private static final String NO_BREAK_SPACE = "%C2%A0";
	public static final int MAX_LONG_KEYWORD_NAME_LEN = 600;

	public static final int MYSQL_DB_INSERT = 10 * 1000;
	public static final int MYSQL_DB_QUERY_WITH_STRING = 300;
	public static final int MYSQL_DB_QUERY_WITH_HASH = 1000;
	public static final int MYSQL_DB_OPERATE_WITH_NUMBER = 1000;
	public static final int MONITOR_BATCH_FOR_LOG = 10000;
	public static final int CLARITY_DB_HASH_QUERY = 200;
	public static final int CLARITY_DB_RE_TRY_COUNT = 3;

	public static final String RV_COMPANY_NAME = "Red Ventures LLC";
	public static final String RV_SECONDARY_OMPANY_NAME = "RVO Health, LLC"; // https://www.wrike.com/open.htm?id=**********
	public static final String RV_THIRD_COMPANY_NAME = "ZPG limited"; // https://www.wrike.com/open.htm?id=**********

//	@Resource
//	private OwnDomainEntityDAO ownDomainEntityDAO;
//
//	@Resource
//	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;

	@Resource
	private SolrInfoEntityDAO solrInfoEntityDAO;

	@Resource
	private ScriptDeployInfoEntityDAO scriptDeployInfoEntityDAO;

	@Resource
	private ScriptRunInstanceEntityDAO scriptRunInstanceEntityDAO;



	private static BotDirectoryDao botDirectoryDao;
	private static OwnDomainEntityDAO ownDomainEntityDAO;
	private static OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	static {
		botDirectoryDao = SpringBeanFactory.getBean("botDirectoryDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
	}

	public static Map<String, Integer> userAgentMap = Collections.synchronizedMap(new HashMap<String, Integer>());

	private static Map<Integer, OwnDomainEntity> ownDomainEntityMap;
	static {
		if (ownDomainEntityMap == null || ownDomainEntityMap.size() == 0) {
			ownDomainEntityMap = ownDomainEntityDAO.getOwnDomainEntityMap();
		}
	}

	private static Map<Integer, OwnDomainSettingEntity> ownDomainSettingEntityMap;
	static {
		if (ownDomainSettingEntityMap == null || ownDomainSettingEntityMap.size() == 0) {
			ownDomainSettingEntityMap = ownDomainSettingEntityDAO.getOwnDomainSettingEntityMap();
		}
	}

	// https://www.wrike.com/open.htm?id=27534938
//	public static final int ADWORDS_CHECK_MONTHS = 2;
//	public static final int SOLR_CORE_CHECK_MONTHS = 6;
//	private static final Map<String, String> countryCoreMap = new HashMap<String,String>();
//	static {
//		countryCoreMap.put("AE", "AE");
//		countryCoreMap.put("AR", "AR");
//		countryCoreMap.put("AT", "AT");
//		countryCoreMap.put("AU", "AU");
//		countryCoreMap.put("BE", "BE");
//		countryCoreMap.put("BG", "BG");
//		countryCoreMap.put("BR", "BR");
//		countryCoreMap.put("CA", "CA");
//		countryCoreMap.put("CH", "CH");
//		countryCoreMap.put("CL", "CL");
//		countryCoreMap.put("CN", "CN");
//		countryCoreMap.put("CO", "CO");
//		countryCoreMap.put("CZ", "CZ");
//		countryCoreMap.put("DE", "DE");
//		countryCoreMap.put("DK", "DK");
//		countryCoreMap.put("ES", "ES");
//		countryCoreMap.put("FI", "FI");
//		countryCoreMap.put("FJ", "FJ");
//		countryCoreMap.put("FR", "FR");
//		countryCoreMap.put("GR", "GR");
//		countryCoreMap.put("HU", "HU");
//		countryCoreMap.put("ID", "ID");
//		countryCoreMap.put("IE", "IE");
//		countryCoreMap.put("IL", "IL");
//		countryCoreMap.put("IN", "IN");
//		countryCoreMap.put("IT", "IT");
//		countryCoreMap.put("JP", "JP");
//		countryCoreMap.put("KR", "KR");
//		countryCoreMap.put("MX", "MX");
//		countryCoreMap.put("MY", "MY");
//		countryCoreMap.put("NG", "NG");
//		countryCoreMap.put("NL", "NL");
//		countryCoreMap.put("NO", "NO");
//		countryCoreMap.put("NZ", "NZ");
//		countryCoreMap.put("PE", "PE");
//		countryCoreMap.put("PL", "PL");
//		countryCoreMap.put("PR", "PR");
//		countryCoreMap.put("PT", "PT");
//		countryCoreMap.put("RO", "RO");
//		countryCoreMap.put("RU", "RU");
//		countryCoreMap.put("SA", "SA");
//		countryCoreMap.put("SE", "SE");
//		countryCoreMap.put("SG", "SG");
//		countryCoreMap.put("TH", "TH");
//		countryCoreMap.put("TR", "TR");
//		countryCoreMap.put("TW", "TW");
//		countryCoreMap.put("UK", "UK");
//		countryCoreMap.put("US", "US");
//		countryCoreMap.put("ZA", "ZA");
//	}
//	public static Map<String, HttpSolrServer> searchVolumeServerMap = new HashMap<String, HttpSolrServer>();
//	private static boolean initializedSolrInstances = false;
//
//
//	private Map<Integer, Integer> domainTierMap;


//	public Integer getTier(int ownDomainId) {
//		if (domainTierMap == null || domainTierMap.size() == 0) {
//			ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
//			domainTierMap = ownDomainSettingEntityDAO.getDomainTierMap();
//		}
//
//		return domainTierMap.get(ownDomainId);
//	}
//
//
//	public static QueryResponse querySolrForKeyword(HttpSolrServer solrServer, String decodedKeyword) {
//		// first use the original decoded keyword text
//		String queryString = "keyword_string:\"" + decodedKeyword + "\"";
//		QueryResponse solrQueryResponse = querySolr(solrServer, queryString);
//		if (solrQueryResponse == null) {
//			// if data for original decoded keyword text does not exist in Solr, remove "\"" of keyword and use it to query
//			queryString = "keyword_lower_s:\"" + decodedKeyword + "\"";
//			solrQueryResponse = querySolr(solrServer, queryString);
//		}
//
//		return solrQueryResponse;
//	}

//	private static QueryResponse querySolr(HttpSolrServer solrServer, String queryString) {
//		SolrQuery query = new SolrQuery();
//		query.setQuery(queryString);
//		query.setRows(200);
//
//		QueryResponse solrQueryResponse = null;
//		try{
//			solrQueryResponse = solrServer.query(query);
//		} catch(Exception e) {
//			e.printStackTrace();
//			return null;
//		}
//
//		if (solrQueryResponse != null && solrQueryResponse.getResults() != null &&
//				solrQueryResponse.getResults().getNumFound() > 0) {
//			return solrQueryResponse;
//		}
//
//		return null;
//	}
//
//	public static String getCountryName(String country, String searchEngine) {
//		if (StringUtils.isBlank(country)) {
//			return "United States";
//		}
//		if (StringUtils.equalsIgnoreCase(country, "UK")) {
//			return "United Kingdom";
//		} else if (StringUtils.equalsIgnoreCase(country, "US")) {
//			return "United States";
//		} else if (StringUtils.equalsIgnoreCase(country, "CA")) {
//			return "Canada";
//		} else if (StringUtils.equalsIgnoreCase(country, "AU")) {
//			return "Australia";
//		} else if (StringUtils.equalsIgnoreCase(country, "CN") && !StringUtils.containsIgnoreCase(searchEngine, "google.com.hk")) {
//			return "China";
//		} else if (StringUtils.equalsIgnoreCase(country, "PT")) {
//			return "Portugal";
//		} else if (StringUtils.equalsIgnoreCase(country, "FR")) {
//			return "France";
//		} else if (StringUtils.equalsIgnoreCase(country, "DE")) {
//			return "Germany";
//		} else if (StringUtils.equalsIgnoreCase(country, "BR")) {
//			return "Brazil";
//		} else if (StringUtils.equalsIgnoreCase(country, "ES")) {
//			return "Spain";
//		} else if (StringUtils.equalsIgnoreCase(country, "NL")) {
//			return "Netherlands";
//		} else if (StringUtils.equalsIgnoreCase(country, "IT")) {
//			return "Italy";
//		} else if (StringUtils.equalsIgnoreCase(country, "DK")) {
//			return "Denmark";
//		} else if (StringUtils.equalsIgnoreCase(country, "FI")) {
//			return "Finland";
//		} else if (StringUtils.equalsIgnoreCase(country, "MX")) {
//			return "Mexico";
//		} else if (StringUtils.equalsIgnoreCase(country, "NO")) {
//			return "Norway";
//		} else if (StringUtils.equalsIgnoreCase(country, "SE")) {
//			return "Sweden";
//		} else if (StringUtils.equalsIgnoreCase(country, "JP")) {
//			return "Japan";
//		}
//
//		else if (StringUtils.equalsIgnoreCase(country, "IN")) {
//			return "India";
//		} else if (StringUtils.equalsIgnoreCase(country, "IE")) {
//			return "Ireland";
//		} else if (StringUtils.equalsIgnoreCase(country, "KR")) {
//			return "Korea";
//		} else if (StringUtils.equalsIgnoreCase(country, "CH")) {
//			return "Switzerland";
//		} else if (StringUtils.equalsIgnoreCase(country, "BE")) {
//			return "Belgium";
//		}
//
//		else if (StringUtils.equalsIgnoreCase(country, "AR")) {
//			return "Argentina";
//		} else if (StringUtils.equalsIgnoreCase(country, "CL")) {
//			return "Chile";
//		} else if (StringUtils.equalsIgnoreCase(country, "CO")) {
//			return "Colombia";
//		} else if (StringUtils.equalsIgnoreCase(country, "PR")) {
//			return "Puerto Rico";
//		} else if(StringUtils.equalsIgnoreCase(country, "AT")) {
//			return "Austria";
//		} else if (StringUtils.equalsIgnoreCase(country, "CN") && StringUtils.containsIgnoreCase(searchEngine, "google.com.hk")) {
//			return "Hong Kong";
//		}
//
//		else if (StringUtils.equalsIgnoreCase(country, "ID")) {
//			return "Indonesia";
//		} else if (StringUtils.equalsIgnoreCase(country, "MY")) {
//			return "Malaysia";
//		} else if (StringUtils.equalsIgnoreCase(country, "PH")) {
//			return "Philippines";
//		} else if (StringUtils.equalsIgnoreCase(country, "TW")) {
//			return "Taiwan";
//		} else if(StringUtils.equalsIgnoreCase(country, "TH")) {
//			return "Thailand";
//		} else if(StringUtils.equalsIgnoreCase(country, "VN")) {
//			return "Vietnam";
//		} else if(StringUtils.equalsIgnoreCase(country, "SG")) {
//			return "Singapore";
//		} else if(StringUtils.equalsIgnoreCase(country, "NZ")) {
//			return "New Zealand";
//		} else if(StringUtils.equalsIgnoreCase(country, "RU")) {
//			return "Russia";
//		}
//
//		//https://www.wrike.com/open.htm?id=9415299
//		else if (StringUtils.equalsIgnoreCase(country, "CZ")) {
//			return "Czech Republic";
//		} else if (StringUtils.equalsIgnoreCase(country, "HU")) {
//			//https://www.wrike.com/open.htm?id=30801648
//			//by sunny
//			return "Hungary";
//		} else if (StringUtils.equalsIgnoreCase(country, "PL")) {
//			return "Poland";
//		}
//
//		//https://www.wrike.com/open.htm?id=15635690
//		else if (StringUtils.equalsIgnoreCase(country, "SA")) {
//			return "Saudi Arabia";
//		} else if (StringUtils.equalsIgnoreCase(country, "PE")) {
//			return "Peru";
//		}
//
//		//https://www.wrike.com/open.htm?id=15737949
//		else if (StringUtils.equalsIgnoreCase(country, "AE")) {
//			return "United Arab Emirates";
//		}
//
//		//https://www.wrike.com/open.htm?id=21126507
//		//by cee
//		else if (StringUtils.equalsIgnoreCase(country, "ZA")) {
//			return "South Africa";
//		}
//
//		else if (StringUtils.equalsIgnoreCase(country, "TR")) {
//			return "Turkey";
//		}
//
//
//
//
//		//https://www.wrike.com/open.htm?id=35274756
//		//by sunny
//		else if (StringUtils.equalsIgnoreCase(country, "KE")) {
//			return "Kenya";
//		}
//
//		//https://www.wrike.com/open.htm?id=36219549
//		//https://www.wrike.com/open.htm?id=40979671
//		// https://www.wrike.com/open.htm?id=36219549
//		// by sunny
//		else if (StringUtils.equalsIgnoreCase(country, "SK")) {
//			return "Slovakia";
//		}
//		else if (StringUtils.equalsIgnoreCase(country, "IL")) {
//			return "Israel";
//		}
//		else if (StringUtils.equalsIgnoreCase(country, "EC")) {
//			return "Ecuador";
//		}
//		else if (StringUtils.equalsIgnoreCase(country, "VE")) {
//			return "Venezuela";
//		}
//		else if (StringUtils.equalsIgnoreCase(country, "CR")) {
//			return "Costa Rica";
//		}
//
//
//		else {
//			return "United States";
//		}
//	}
//
//	public static String getSearchVolumn(String decodedKeywordName, HttpSolrServer solrServer) {
//		if (solrServer == null || StringUtils.isEmpty(decodedKeywordName)) {
//			return null;
//		}
//
//		solrServer.setMaxRetries(1);
//		SolrQuery query = new SolrQuery();
//		query.setQuery("keyword_string:\"" + decodedKeywordName + "\"");
//
//		try {
//			QueryResponse solrQueryResponse = solrServer.query(query);
//			Iterator<SolrDocument> iter = solrQueryResponse.getResults().iterator();
//			if (iter.hasNext()) {
//				SolrDocument resultDoc = iter.next();
//				Integer avgSearchVolume = (Integer) resultDoc.getFieldValue("local_monthly_search");
//				if (avgSearchVolume == null) {
//					System.out.println("No avgSV for KW:" + decodedKeywordName);
//					return null;
//				} else {
//					System.out.println("KW:" + decodedKeywordName + " SV:" + avgSearchVolume);
//					return String.valueOf(avgSearchVolume.intValue());
//				}
//			} else {
//				System.out.println("NoKW:" + decodedKeywordName);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//		return null;
//	}
//
//
//	public HttpSolrServer getHttpSolrServerInfo(String countryName) throws Exception {
//		if (StringUtils.isEmpty(countryName)) {
//			return null;
//		}
//
//		if (initializedSolrInstances == false) {
//			initSolrInstances();
//			initializedSolrInstances = true;
//		}
//
//		String coreName = countryCoreMap.get(countryName);
//		if (!StringUtils.isEmpty(coreName)) {
//			return searchVolumeServerMap.get(countryName);
//		}
//
//		return null;
//	}


//	public void initSolrInstances() throws Exception {
//		System.out.println("###############################initializeSolrInstances");
//		Set<String> countrySet = countryCoreMap.keySet();
//		Iterator<String> ite = countrySet.iterator();
//		while (ite.hasNext()) {
//			initSolrInstance(ite.next());
//		}
//
//		Collection<HttpSolrServer> coll = searchVolumeServerMap.values();
//		System.out.println("==============HttpSolrServers:");
//		for (HttpSolrServer server : coll) {
//			System.out.println("   " + server.getBaseURL());
//		}
//		initializedSolrInstances = true;
//	}

//	//Neelam- I need this method please do not deprecate it
//	public void initSolrInstance(String countryName) throws Exception {
//		SolrInfoEntity solrInfoEntity = null;
//		String solrConnectionUrl = null;
//		HttpSolrServer searchVolumeServer = null;
//		String coreName = countryCoreMap.get(countryName);
//		if (!StringUtils.isEmpty(coreName)) {
//			solrInfoEntity = solrInfoEntityDAO.getCountryLatest(SolrInfoEntity.SEARCH_VOLUME_SOLR, countryName.toUpperCase());
//			if (solrInfoEntity != null) {
//				solrConnectionUrl = solrInfoEntity.getSolrUrl();
//				if (StringUtils.isNotBlank(solrConnectionUrl)) {
//					System.out.println("======coreName:" + coreName + " SolrURL:" + solrConnectionUrl);
//					searchVolumeServer = checkSolrInstance(solrConnectionUrl);
//					if (searchVolumeServer != null) {
//						searchVolumeServerMap.put(coreName, searchVolumeServer);
//					}
//				}
//			}
//		}
//	}

	/***************************************************** Search Volume solr core TODO ****************************************************************/
	// https://www.wrike.com/open.htm?id=53987296  search volume solr url pick from solr_info table
//	private static Map<String, SolrInfoEntity> svSolrMap = null;
	// search volume solr core, due to too many reference linked at this method, we'd better don't change method name
//	/**
//	 * Get httpSolrServer for search volume
//	 * @param countryName
//	 */
//	public static HttpSolrServer getHttpSolrServer(String countryName) throws Exception {
//
//		HttpSolrServer solrServer = null;
//
//		if(StringUtils.isEmpty(countryName)) {
//			return null;
//		}
//
//		if(svSolrMap == null) {
//			initSVSolrInstanceV2();
//		}
//
//		System.out.println("----- Search Volume countryCode: " + countryName.toLowerCase());
//		SolrInfoEntity solrInfoEntity = svSolrMap.get(countryName.toLowerCase());
//
//		int tryCount = 5;
//		//If the query does not have a result the first time from an ip,
//		//query the second time with a different IP. and return result.
//		for (int i = 0; i < tryCount; i++) {
//			String solrUrl = null;
//			if(solrInfoEntity == null){
//				solrUrl = getSolrUrlRandomStatic(svSolrMap.get("us"));
//			} else {
//				solrUrl = getSolrUrlRandomStatic(solrInfoEntity);
//			}
////	    	if (solrUrl.contains("**************:8983")) {
////	    		System.out.println("-----Skip bad Search Volume SORL URL temporarily: " + solrUrl);
////	    		i--;
////	    		continue;
////	    	}
//			System.out.println("-----Search Volume SORL URL: " + solrUrl);
//
//			try {
//				solrServer = checkSolrInstance(solrUrl);
////			    	if(solrServer != null && ping(solrServer)) {
//				if(solrServer != null) {
////			    		solrServer.setMaxRetries(5);
////					solrServer.setSoTimeout(2 * 60 * 1000);
////					solrServer.setConnectionTimeout(30 * 1000);
//					return solrServer;
//				}
//			} catch (Exception e) {
//				e.printStackTrace();
//			} catch (Error e) {
//				e.printStackTrace();
//			}
//		}
//		return solrServer;
//	}
//
//	protected static boolean ping(HttpSolrServer solrServer) {
//		try {
//			SolrPingResponse response = solrServer.ping();
//			int respCode = response.getStatus();
//			int qTime = response.getQTime();
//			System.out.println(" respCode: " + respCode + "; QTime: " + qTime);
//			System.out.println(solrServer.getBaseURL() + " ping is OK.");
//			return true;
//		} catch (Exception e) {
//			System.out.println(solrServer.getBaseURL() + " ping failed.");
//			return false;
//		}
//	}

//	private static void initSVSolrInstanceV2() {
//		SolrInfoEntityDAO solrInfoEntityDao = SpringBeanFactory.getBean("solrInfoEntityDAO");
//		svSolrMap = solrInfoEntityDao.getAllAvailableSolrServerViaTypeReturnAsMapV2(SolrInfoEntity.SEARCH_VOLUME_SOLR);
//	}
	/***************************************************** Search Volume solr core ****************************************************************/

//	public static HttpSolrServer checkSolrInstance(String solrServerUrl) throws Exception {
//		HttpSolrServer searchVolumeServer = null;
//		SolrPingResponse pingResponse = null;
//		int retry = 1;
//		do {
//			try {
//				searchVolumeServer = new HttpSolrServer(solrServerUrl);
//				searchVolumeServer.setMaxRetries(1);
////				searchVolumeServer.setSoTimeout(5 * 60 * 1000);
////				searchVolumeServer.setConnectionTimeout(5 * 60 * 1000);
//				searchVolumeServer.setSoTimeout(30 * 1000);
//				searchVolumeServer.setConnectionTimeout(30 * 1000);
//
//				pingResponse = searchVolumeServer.ping();
//				if (pingResponse.getStatus() == 0) {
//					System.out.println("######Server:" + solrServerUrl + " " + searchVolumeServer.getBaseURL());
//					return searchVolumeServer;
//				}
//			} catch (Exception e) {
//				System.out.println(e.getMessage());
//			}
//			searchVolumeServer = null;
//			retry++;
//		} while (retry <= 2);
//
//		if (searchVolumeServer == null) {
//			System.out.println("######Server:" + solrServerUrl +"  is not ready");
//		}
//		return null;
//	}

	/**
	 * MD5 encoder
	 * by floyd
	 */
	static MessageDigest md = null;

	static {
		try {
			md = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException ne) {
			ne.printStackTrace();
			System.out.println("NoSuchAlgorithmException: md5" + ne);
		}
	}

//	/**
//	 * Get encode String via md5 for File
//	 * by floyd
//	 */
//	public static String md5(File f) {
//		FileInputStream fis = null;
//		try {
//			fis = new FileInputStream(f);
//			//100KB each time
//			byte[] buffer = new byte[102400];
//			int length;
//			while ((length = fis.read(buffer)) != -1) {
//				md.update(buffer, 0, length);
//			}
//
//			return new String(Hex.encodeHex(md.digest()));
//		} catch (FileNotFoundException e) {
//			System.out.println("md5 file " + f.getAbsolutePath() + " failed:" + e.getMessage());
//			return null;
//		} catch (IOException e) {
//			System.out.println("md5 file " + f.getAbsolutePath() + " failed:" + e.getMessage());
//			return null;
//		} finally {
//			try {
//				if (fis != null) fis.close();
//			} catch (IOException e) {
//				e.printStackTrace();
//			}
//		}
//	}
//
//	public static String getServerInfo() {
//		try {
//			InetAddress netAddress = getInetAddress();
//			String hostIp = getHostIp(netAddress);
//			if (StringUtils.isEmpty(hostIp)) {
//				hostIp = getHostName(netAddress);
//				System.out.println("HostName:" + hostIp);
//			}
//			String currentPath = new File("").getCanonicalPath();
//			String serverInfo = (hostIp == null ? "" : hostIp) + ":" + currentPath;
//			System.out.println("ServerInfo:" + serverInfo);
//			return serverInfo;
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		System.out.println("Failed to get serverInfo.");
//		return null;
//	}

//	private static InetAddress getInetAddress(){
//		try{
//			return InetAddress.getLocalHost();
//		}catch(UnknownHostException e){
//			System.out.println("unknown host!");
//		}
//		return null;
//	}
//
//	private static String getHostIp(InetAddress netAddress){
//		if(null == netAddress){
//			return null;
//		}
//		String ip = netAddress.getHostAddress();
//		return ip;
//	}
//
//	private static String getHostName(InetAddress netAddress){
//		if(null == netAddress){
//			return null;
//		}
//		String name = netAddress.getHostName();
//		return name;
//	}
//
//	// https://www.wrike.com/open.htm?id=40580950   --by floyd
//	public boolean isManualFiscalMonthForYoY(int owndomainId){
//		Integer result = ownDomainSettingEntityDAO.checkFiscalMonthByDomainId(owndomainId);
//		if(result == 1) {
//			return true;
//		}
//		return false;
//	}


	/********************* https://www.wrike.com/open.htm?id=43479796 query Solr URL from MYSQL ***********************************/

//	@Deprecated
//	private static Map<String, SolrInfoEntity> solrUrlMapV2 = null;

//	@Deprecated
//	public HttpSolrServer getSolrServer(String countryCode) {
//		// get solr url from DB via solrInfoEntityDAO
//		if(solrUrlMapV2 == null) {
//			solrUrlMapV2 = solrInfoEntityDAO.getAllAvailableSolrServerViaTypeReturnAsMapV2(SolrInfoEntity.MONTHLY_RANKING_SOLR);
//		}
//		System.out.println("---countryCode: " + countryCode.toLowerCase());
//		SolrInfoEntity solrInfoEntity = solrUrlMapV2.get(countryCode.toLowerCase());
//
//		int tryCount = 3;
//
//		HttpSolrServer solrServer = null;
//		//If the query does not have a result the first time from an ip,
//		//query the second time with a different IP. and return result.
//		for (int i = 0; i < tryCount; i++) {
//			String solrUrl = null;
//			if(solrInfoEntity == null){
//				solrUrl = getSolrUrlRandom(solrUrlMapV2.get("us"));
//			} else {
//				solrUrl = getSolrUrlRandom(solrInfoEntity);
//			}
//			System.out.println("---SORL URL: " + solrUrl);
//			solrServer = new HttpSolrServer(solrUrl);
//
//			solrServer.setMaxRetries(5);
//			solrServer.setSoTimeout(60 * 15 * 1000);
//			solrServer.setConnectionTimeout(60 * 15 * 1000);
//			if(solrServer != null) {
//				return solrServer;
//			}
//		}
//
//		return solrServer;
//	}

	/**************************************************** Monthlyranking desktop and mobile TODO *******************************************************************/
	// https://www.wrike.com/open.htm?id=53485380
	// monthlyranking solr map for desktop
	private static Map<String, SolrInfoEntity> mrkSolrUrlMap = null;

	// monthlyranking solr map for Mobile
	private static Map<String, SolrInfoEntity> mrkMobileSolrUrlMap = null;

//	// https://www.wrike.com/open.htm?id=53485380	for both mobile and desktop		-- by floyd
//	public HttpSolrServer getSolrServerForMonthlyRanking(String countryCode, String solrType) {
//
//		// initialize solr maps
//		if(mrkSolrUrlMap == null) {
//			mrkSolrUrlMap = solrInfoEntityDAO.getAllAvailableSolrServerViaTypeReturnAsMapV2(SolrInfoEntity.MONTHLY_RANKING_SOLR);
//		}
//		if(mrkMobileSolrUrlMap == null) {
//			mrkMobileSolrUrlMap = solrInfoEntityDAO.getAllAvailableSolrServerViaTypeReturnAsMapV2(SolrInfoEntity.MONTHLY_RANKING_MOBILE_SOLR);
//		}
//
//		// for desktop
//		if(StringUtils.equals(solrType, SolrInfoEntity.MONTHLY_RANKING_SOLR.toString())) {
//			return getSolrServerFromMap(countryCode, mrkSolrUrlMap);
//		}
//		// for mobile
//		if(StringUtils.equals(solrType, SolrInfoEntity.MONTHLY_RANKING_MOBILE_SOLR.toString())) {
//			return getSolrServerFromMap(countryCode, mrkMobileSolrUrlMap);
//		}
//
//		return getSolrServerFromMap(countryCode, mrkSolrUrlMap);
//	}

//	// generate SolrServer whose url formed from solr map,  with retry mechanism
//	private HttpSolrServer getSolrServerFromMap(String countryCode, Map<String, SolrInfoEntity> map) {
//		System.out.println("---countryCode: " + countryCode.toLowerCase());
//		SolrInfoEntity solrInfoEntity = map.get(countryCode.toLowerCase());
//
//		int tryCount = 3;
//
//		HttpSolrServer solrServer = null;
//		//If the query does not have a result the first time from an ip,
//		//query the second time with a different IP. and return result.
//		for (int i = 0; i < tryCount; i++) {
//			String solrUrl = null;
//			if(solrInfoEntity == null){
//				// https://www.wrike.com/open.htm?id=53327392
//				// if the data[Solr entity] is empty return null for Competitor Keywords DownloadAll -- floyd
////	    		solrUrl = getSolrUrlRandom(map.get("us"));
//				return null;
//			} else {
//				solrUrl = getSolrUrlRandom(solrInfoEntity);
//			}
//			System.out.println("---SORL URL: " + solrUrl);
//
//			try {
//				solrServer = new HttpSolrServer(solrUrl);
//				solrServer.setMaxRetries(5);
//				solrServer.setSoTimeout(60 * 15 * 1000);
//				solrServer.setConnectionTimeout(60 * 15 * 1000);
//				if(solrServer != null) {
//					return solrServer;
//				}
//			} catch (Exception e) {
//				e.printStackTrace();
//			} catch (Error e) {
//				e.printStackTrace();
//			}
//		}
//		return solrServer;
//	}

//	public String getSolrUrlRandom(SolrInfoEntity solrInfo) {
//		return getSolrUrlRandomStatic(solrInfo);
//	}
/**************************************************** Monthlyranking desktop and mobile *******************************************************************/

	/***************************************************** Content Idea solr core TODO ****************************************************************/
//	private Map<String, SolrInfoEntity> contentIdeaSolrMap = null;

//	public HttpSolrServer getSolrServerForContentIdea() {
//		// initialize solr maps
//		if(contentIdeaSolrMap == null) {
//			contentIdeaSolrMap = solrInfoEntityDAO.getAllAvailableSolrServerViaTypeReturnAsMapV2(SolrInfoEntity.CONTENT_IDEA_SOLR);
//		}
//		// content idea don't support country code for now and we think it will work with country code in a long run in future
//		// so we hard code 'us' as default country code so far
//		return getSolrServerFromMap("us", contentIdeaSolrMap);
//	}

	/***************************************************** Content Idea solr core ****************************************************************/

//	static JsonMapper mapper = new JsonMapper();

//	//https://www.wrike.com/open.htm?id=52384759
//	//by floyd
//	public static String getSolrUrlRandomStatic(SolrInfoEntity solrInfo) {
//		if (solrInfo == null || StringUtils.isBlank(solrInfo.getSolrUrl())) {
//			System.out.println("-------------solr_url is empty");
//			return null;
//		}
//
//		//If the list of Ips is null, then use the IP address provided.
//		if (StringUtils.isBlank(solrInfo.getIpList())) {
//			return solrInfo.getSolrUrl();
//		}
//
//		String[] ipList = mapper.fromJson(solrInfo.getIpList(), String[].class);
//		if (ipList == null || ipList.length == 0) {
//			return solrInfo.getSolrUrl();
//		}
//
//		//get the core name from the solr_url column and then randomly pick one ip
//		//from the list of ips and query it.
//		int arrayIndex = RandomUtils.nextInt(ipList.length);
//		String randomIp = ipList[arrayIndex];
//		System.out.println("-------random for ip [" + arrayIndex + "]");
//
//		// http://*************:8983/solr/monthlyranking_IT_201505
//		String solrUrl = solrInfo.getSolrUrl();
//		String protocol = StringUtils.substringBefore(solrUrl, "://");
//		solrUrl = StringUtils.substringAfter(solrUrl, "://");
//
//		StringBuilder randomSolrUrl = new StringBuilder();
//		randomSolrUrl.append(protocol).append("://");
//		randomSolrUrl.append(randomIp).append(":8983/");
//		randomSolrUrl.append(StringUtils.substringAfter(solrUrl, "/"));
//
//		return randomSolrUrl.toString();
//	}

/********************* https://www.wrike.com/open.htm?id=60805908 ***********************************/
/***************************************************** Polite Crawler solr core start ****************************************************************/

	// https://www.wrike.com/open.htm?id=225833037
    public ScriptDeployInfoEntity getScriptDeployInfo(String projectName, String fullQulifiedClass) {
    	return scriptDeployInfoEntityDAO.checkExist(projectName, fullQulifiedClass);
    }
    

//	public int saveScriptRunInfo(int deployInfoId, int processDate, int status, String serverIp, String serverPath) throws Exception {
//		ScriptRunInstanceEntity entity = new ScriptRunInstanceEntity();
//		entity.setDeployInfoId(deployInfoId);
//		entity.setProcessDate(processDate);
//		entity.setStatus(status);
//		entity.setServerIp(serverIp);
//		entity.setServerPath(serverPath);
//
//		int instanceId = scriptRunInstanceEntityDAO.insert(entity);
//		System.out.println("==InsertScriptRunInstance:" + instanceId);
//
//		return instanceId;
//	}

    public ScriptRunInstanceEntity saveScriptRunInfo(int deployInfoId, int processDate, int status, int ownDomainId, String serverIp, String serverPath) throws Exception {
        ScriptRunInstanceEntity entity = new ScriptRunInstanceEntity();
        entity.setDeployInfoId(deployInfoId);
        entity.setOwnDomainId(ownDomainId);
        entity.setProcessDate(processDate);
        entity.setStatus(status);
        entity.setServerIp(serverIp);
        entity.setServerPath(serverPath);

        int instanceId = scriptRunInstanceEntityDAO.insertFullProperties(entity);
        System.out.println("==InsertScriptRunInstance:" + instanceId);

        entity.setId(instanceId);

        return entity;
    }

//	public ScriptRunInstanceEntity saveScriptRunInfo(int deployInfoId, int processDate, int status, int ownDomainId, String serverIp, String serverPath, String device) throws Exception {
//		ScriptRunInstanceEntity entity = new ScriptRunInstanceEntity();
//		entity.setDeployInfoId(deployInfoId);
//		entity.setOwnDomainId(ownDomainId);
//		entity.setProcessDate(processDate);
//		entity.setStatus(status);
//		entity.setServerIp(serverIp);
//		entity.setServerPath(serverPath);
//		entity.setDevice(device);
//
//		int instanceId = scriptRunInstanceEntityDAO.insertFullProperties(entity);
//		System.out.println("==InsertScriptRunInstance:" + instanceId);
//
//		entity.setId(instanceId);
//
//		return entity;
//	}

	public void updateScriptRunInfo(ScriptRunInstanceEntity entity) {
		if (entity == null) {
			System.out.println("==UpdateScriptRunInstance Error:entity is null");
		} else {
			System.out.println("==UpdateScriptRunInstance:" + entity.getId());
			scriptRunInstanceEntityDAO.updateRunInfo(entity);
		}
	}


/***************************************************** Polite Crawler solr core end ****************************************************************/



	/********************* https://www.wrike.com/open.htm?id=41887237 ***********************************/

//	public static final String CALL_LATEST_DOC = "latestDoc";
//	public static final String CALL_SOLR_QUERYTXT = "";
//	public static final String CALL_DOC_IN_DATERANGE = "docInDateRange";

//	private String getData(DBObject obj, String item) {
//		if(obj == null) {
//			return null;
//		}
//		if(obj.get(item) == null) {
//			return null;
//		}
//		return obj.get(item).toString();
//	}

//	private String[] getDataArray(DBObject obj, String item) {
//
////    	["Monthly, topical flea and tick preventative for dogs","Frontline Plus for Dogs 0-22 lbs, Orange, 6 Pack Customer Reviews"]
//		String jsonStr = getData(obj, item);
//
//		if(jsonStr == null) {
//			return null;
//		}
//
//		BasicBSONList jsonArr = (BasicBSONList) JSON.parse(jsonStr);
//
////    	System.out.println("--- Json SIZE:" + jsonArr.size() + ", item: " + item);
//		String[] result = new String[jsonArr.size()];
//		for (int i=0; i < jsonArr.size(); i++ ) {
////			System.out.println("res:" + jsonArr.get(i).toString());
//			result[i] = jsonArr.get(i).toString();
//		}
//
//		return result;
//	}
//
//	private String parameterByJson(int domainId, Map<String, String> param) {
//		OwnDomainEntity ownDomain = ownDomainEntityDAO.getById(domainId);
//		String languageCode = ownDomain.getLanguage();
//
//		StringBuilder sb = new StringBuilder();
//		sb.append("[]");
//
//		DBObject obj  = new BasicDBObject();
//		obj.put("languageCode", languageCode);
//		obj.put("start", 0);
//		obj.put("returnFieldList", sb.toString());
//
//		for (String key : param.keySet()) {
//			obj.put(key, param.get(key));
//		}
//
//		String resultJson = JSON.serialize(obj);
//		resultJson = resultJson.replaceAll(" ", "");
//		resultJson = resultJson.replace("\"[", "[");
//		resultJson = resultJson.replace("]\"", "]");
//		return resultJson;
//	}
    
    /*
    private String getTextFromWSInBalance(String uri) {
    	if(rand == null) {
    		rand = new Random();
    	}
    	int seed = rand.nextInt(3), retry = 0;
    	String testUri = "/seoClarity/";
    	String wsAddr = null;
    	
    	while(retry < 3) {
    		System.out.println("-----get seed for balance: " + seed);
    		wsAddr = hostBalanceMap.get(seed);
    		System.out.println("-----current ws addr: " + wsAddr + testUri);
    		
    		HttpURLConnection conn = null;
    		int responseCode = 0;
    		try {
    			
				URL url = new URL(wsAddr + testUri);
				conn = (HttpURLConnection) url.openConnection();
				conn.setRequestMethod("GET");
				conn.setConnectTimeout(21000);
				conn.setReadTimeout(21000);
				conn.setInstanceFollowRedirects(false);
				conn.setRequestProperty("contentType", "UTF-8");
				conn.setRequestProperty("acceptCharset", "UTF-8");
				conn.setRequestProperty("Accept-Charset", "UTF-8");
				
				responseCode = conn.getResponseCode();
				conn.disconnect();
				
				// retry condition
				if(responseCode != 200) {
					seed = rand.nextInt(3);
					retry ++;
					System.out.println("@@@responseCode not 200:  retry:" + retry);
					continue;
				}
				System.out.println("queryUrl:" + wsAddr + uri);
				return getTextFromWSViaHttp(wsAddr + uri);
			} catch (SocketTimeoutException re) {
				// retry
				seed = rand.nextInt(3);
				retry ++;
				System.out.println("@@@READ TIME OUT:  retry:" + retry);
				continue;
			} catch (Exception e) {
				e.printStackTrace();
			}
    	}
    	System.out.println("--- Retry 3 times to Connect web service, ignore...  , query_uri:" + uri);
    	return null;
    }
    
    private String getTextFromWSViaHttp(String webServiceUrl) {
		
		HttpURLConnection conn = null;
		InputStream inputStream = null;
		try {
			URL url = new URL(webServiceUrl);
			
			conn = (HttpURLConnection) url.openConnection();
			
			conn.setRequestMethod("GET");
			conn.setConnectTimeout(21000);
			conn.setReadTimeout(21000);
			conn.setInstanceFollowRedirects(false);
			conn.setRequestProperty("contentType", "UTF-8");
			conn.setRequestProperty("acceptCharset", "UTF-8");
			conn.setRequestProperty("Accept-Charset", "UTF-8");
			// for temp this line will cause the Exception, so annotated
//			conn.setRequestProperty("Connection", "close");
			
			inputStream = conn.getInputStream();
			
			String jsonStr = IOUtils.toString(inputStream, "UTF-8");
			System.out.println(" result: " + jsonStr);
			return jsonStr;
		} catch (MalformedURLException e) {
			System.out.println("Init URL error. " + webServiceUrl);
			e.printStackTrace();
		} catch (IOException e) {
			System.out.println("Open URL error. " + webServiceUrl);
			e.printStackTrace();
			int responseCode = 0;
			try {
				responseCode = conn.getResponseCode();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			if (responseCode == 404) {
				System.out.println("Response Code : " + responseCode + "; (No Data)");
				return null;
			}
		} finally {
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (conn != null) {
				conn.disconnect();
				conn = null;
			}
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
		return null;
	}
     */



//	public static String formatKeywordForInsert(String keywordStr) {
//		if (StringUtils.isBlank(keywordStr)) {
//			return null;
//		}
//
//		keywordStr = StringUtils.chomp(keywordStr);
//		keywordStr = StringUtils.chomp(keywordStr);
//
//		// remove invisible characters
//		keywordStr = keywordStr.replaceAll("\\s", " ");
//
//		// remove multi-space characters to one space
//		keywordStr = keywordStr.replaceAll(" +", " ");
//
//		// remove START and END space
//		keywordStr = StringUtils.stripToEmpty(keywordStr);
//
//		return keywordStr;
//	}
//
	public static String formatEncodeKeywordForInsert(String encodeKeyword) {
		if (StringUtils.isBlank(encodeKeyword)) {
			return null;
		}
		
		// remove START and END space
		encodeKeyword = StringUtils.stripToEmpty(encodeKeyword);
		
		// remove Full-width space
		encodeKeyword = removeNoBreakSpace(encodeKeyword);
		encodeKeyword = StringUtils.replace(encodeKeyword, "%E3%80%80", "+");
		
		// remove multi-space (+) to one space
		// encodeKeyword = encodeKeyword.replaceAll("\\++", "+");
		encodeKeyword = encodeKeyword.replaceAll("[+]+", "+");
		
		if (StringUtils.endsWith(encodeKeyword, "+")) {
			encodeKeyword = StringUtils.removeEnd(encodeKeyword, "+");
		}
		if (StringUtils.startsWith(encodeKeyword, "+")) {
			encodeKeyword = StringUtils.removeStart(encodeKeyword, "+");
		}
		
		// https://www.wrike.com/open.htm?id=********
		// by Meo
		if (encodeKeyword.length() >= 254) {
//			String message = "The following keywords could not be added to your account because they exceeded the length limitation. [";
			String message = "The following keywords are longer than 255 max length.[";
			message = message + encodeKeyword + "]";
			System.out.println(message);
//			return null;
		}
		if (encodeKeyword.length() >= MAX_LONG_KEYWORD_NAME_LEN) {
			String message = "The following keywords could not be added to your account because they exceeded the length limitation. [";
			message = message + encodeKeyword + "]";
			message = message + ", length:" + encodeKeyword.length();
			System.out.println(message);
			return null;
		}

		return encodeKeyword;
	}
//
//	public static String formatEncodeKeywordForRegex(String encodeKeyword) {
//		if (StringUtils.isBlank(encodeKeyword)) {
//			return null;
//		}
//
//		// remove START and END space
//		encodeKeyword = StringUtils.stripToEmpty(encodeKeyword);
//
//		// remove Full-width space
//		if (StringUtils.contains(encodeKeyword, NO_BREAK_SPACE)) {
//			encodeKeyword = StringUtils.replace(encodeKeyword, NO_BREAK_SPACE, "\\+");
//		}
//		encodeKeyword = StringUtils.replace(encodeKeyword, "%E3%80%80", "\\+");
//
//		// remove multi-space (+) to one space
//		encodeKeyword = encodeKeyword.replaceAll("(\\\\\\+)+", "\\\\+");
//
//		if (StringUtils.endsWith(encodeKeyword, "\\+")) {
//			encodeKeyword = StringUtils.removeEnd(encodeKeyword, "\\+");
//		}
//		if (StringUtils.startsWith(encodeKeyword, "\\+")) {
//			encodeKeyword = StringUtils.removeStart(encodeKeyword, "\\+");
//		}
//
//		if (encodeKeyword.length() >= 254) {
//			String message = "The following keywords could not be added to your account because they exceeded the length limitation. [";
//			message = message + encodeKeyword + "]";
//			System.out.println(message);
//			return null;
//		}
//
//		return encodeKeyword;
//	}

	private static String removeNoBreakSpace(String encodeKeyword) {
		//remove %C2%A0 (No-Break Space)
		//http://www.utf8-chartable.de/
		if (StringUtils.contains(encodeKeyword, NO_BREAK_SPACE)) {
			return StringUtils.replace(encodeKeyword, NO_BREAK_SPACE, "+");
		}
		return encodeKeyword;
	}

	public ScriptRunInstanceEntity saveScriptRunInfo(int deployInfoId, int processDate, int status, int ownDomainId, String country, String serverIp, String serverPath, String device, int tagId, int frequency) throws Exception {
		ScriptRunInstanceEntity entity = new ScriptRunInstanceEntity();
		entity.setDeployInfoId(deployInfoId);
		entity.setOwnDomainId(ownDomainId);
		entity.setProcessDate(processDate);
		entity.setStatus(status);
		entity.setServerIp(serverIp);
		entity.setCountry(country);
		entity.setServerPath(serverPath);
		entity.setDevice(device);
		entity.setTagId(tagId);
		entity.setFrequency(frequency);

		ScriptRunInstanceEntity checkExistEntity = scriptRunInstanceEntityDAO.checkExist(entity);

		if (checkExistEntity != null && checkExistEntity.getId() > 0) {
			checkExistEntity.setFatalError("");
			return checkExistEntity;
		} else {

			int instanceId = scriptRunInstanceEntityDAO.insertFullProperties(entity);
			System.out.println("==InsertScriptRunInstance:" + instanceId);

			entity.setId(instanceId);

			return entity;
		}

	}

	public ScriptRunInstanceEntity saveScriptRunInfo(ScriptRunInstanceEntity entity, boolean needTargetDomain) throws Exception {

		ScriptRunInstanceEntity checkExistEntity = scriptRunInstanceEntityDAO.checkExist(entity, needTargetDomain);

		if (checkExistEntity != null && checkExistEntity.getId() > 0) {
			checkExistEntity.setFatalError("");
			return checkExistEntity;
		} else {

			int instanceId = scriptRunInstanceEntityDAO.insertFullProperties(entity);
			System.out.println("==InsertScriptRunInstance:" + instanceId);

			entity.setId(instanceId);

			return entity;
		}

	}

	public ScriptRunInstanceEntity createScriptRunInstanceEntity(int deployInfoId, int processDate, int status, int ownDomainId, String country, String serverIp, String serverPath, String device, int tagId, int frequency, String targetDomain) {

		ScriptRunInstanceEntity entity = new ScriptRunInstanceEntity();
		entity.setDeployInfoId(deployInfoId);
		entity.setOwnDomainId(ownDomainId);
		entity.setProcessDate(processDate);
		entity.setStatus(status);
		entity.setServerIp(serverIp);
		entity.setCountry(country);
		entity.setServerPath(serverPath);
		entity.setDevice(device);
		entity.setTagId(tagId);
		entity.setFrequency(frequency);
		entity.setTargetDomain(targetDomain);

		return entity;

	}
	public static String encodeQueueBaseKeyword(String paramKeywordName) throws UnsupportedEncodingException {
		if (paramKeywordName == null) {
			return null;
		}
		String keywordName = formatKeywordForInsert(StringUtils.lowerCase(paramKeywordName));
		if (!StringUtils.isEmpty(keywordName)) {
			keywordName = StringUtils.lowerCase(keywordName);
			keywordName = URLEncoder.encode(keywordName, "UTF-8");
		}
		keywordName = formatEncodeKeywordForInsert(keywordName);
		if (keywordName == null) {
			return null;
		}
		return keywordName.toLowerCase();
	}

	public static String formatKeywordForInsert(String keywordStr) {
		if (StringUtils.isBlank(keywordStr)) {
			return null;
		}

		keywordStr = StringUtils.chomp(keywordStr);
		keywordStr = StringUtils.chomp(keywordStr);

		// remove invisible characters
		keywordStr = keywordStr.replaceAll("\\s", " ");

		// remove multi-space characters to one space
		keywordStr = keywordStr.replaceAll(" +", " ");

		// remove START and END space
		keywordStr = StringUtils.stripToEmpty(keywordStr);

		return keywordStr;
	}

	public static boolean containsEmoji(String source) {
		int len = source.length();
		boolean isEmoji = false;
		for (int i = 0; i < len; i++) {
			char hs = source.charAt(i);
			if (0xd800 <= hs && hs <= 0xdbff) {
				if (source.length() > 1) {
					char ls = source.charAt(i + 1);
					int uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
					if (0x1d000 <= uc && uc <= 0x1f77f) {
						return true;
					}
				}
			} else {
				// non surrogate
				if (0x2100 <= hs && hs <= 0x27ff && hs != 0x263b) {
					return true;
				} else if (0x2B05 <= hs && hs <= 0x2b07) {
					return true;
				} else if (0x2934 <= hs && hs <= 0x2935) {
					return true;
				} else if (0x3297 <= hs && hs <= 0x3299) {
					return true;
				} else if (hs == 0xa9 || hs == 0xae || hs == 0x303d
						|| hs == 0x3030 || hs == 0x2b55 || hs == 0x2b1c
						|| hs == 0x2b1b || hs == 0x2b50 || hs == 0x231a) {
					return true;
				}
				if (!isEmoji && source.length() > 1 && i < source.length() - 1) {
					char ls = source.charAt(i + 1);
					if (ls == 0x20e3) {
						return true;
					}
				}
			}
		}
		return isEmoji;
	}

	public static String filterEmoji(String source) {
		if (StringUtils.isBlank(source)) {
			return source;
		}
		StringBuilder buf = null;
		int len = source.length();
		for (int i = 0; i < len; i++) {
			char codePoint = source.charAt(i);
			if (isEmojiCharacter(codePoint)) {
				if (buf == null) {
					buf = new StringBuilder(source.length());
				}
				buf.append(codePoint);
			}
		}
		if (buf == null) {
			return source;
		} else {
			if (buf.length() == len) {
				buf = null;
				return source;
			} else {
				return buf.toString();
			}
		}
	}

	private static boolean isEmojiCharacter(char codePoint) {
		return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA)
				|| (codePoint == 0xD)
				|| ((codePoint >= 0x20) && (codePoint <= 0xD7FF))
				|| ((codePoint >= 0xE000) && (codePoint <= 0xFFFD))
				|| ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
	}

	public static Map<String, Integer> getDirectoryEntities (){
		if (userAgentMap == null || userAgentMap.size() == 0) {
			List<BotDirectoryEntity> directoryEntities = botDirectoryDao.getAllAgent();
			for (BotDirectoryEntity directoryEntity : directoryEntities) {
				String key = directoryEntity.getOwnDomainId() + "!_!" + directoryEntity.getUserAgent();
				userAgentMap.put(key, directoryEntity.getId());
			}
		}
		return userAgentMap;
	}

	public static OwnDomainEntity getOwnDomainEntity(int oid) {
		if (ownDomainEntityMap == null || ownDomainEntityMap.size() == 0) {
			ownDomainEntityMap = ownDomainEntityDAO.getOwnDomainEntityMap();
		}
		return ownDomainEntityMap.get(oid);
	}
	public static OwnDomainSettingEntity getOwnDomainSettingEntity(int oid) {
		if (ownDomainSettingEntityMap == null || ownDomainSettingEntityMap.size() == 0) {
			ownDomainSettingEntityMap = ownDomainSettingEntityDAO.getOwnDomainSettingEntityMap();
		}
		return ownDomainSettingEntityMap.get(oid);
	}

	public List<OwnDomainEntity> getRVTargetDomainList() {
		List<OwnDomainEntity> resultList = new ArrayList<OwnDomainEntity>();
		List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(RV_COMPANY_NAME);
		if (domainList != null) {
			resultList.addAll(domainList);
		}
		List<OwnDomainEntity> secondaryDomainList = ownDomainEntityDAO.getDomainListBasedCompanyName(RV_SECONDARY_OMPANY_NAME);
		if (secondaryDomainList != null) {
			resultList.addAll(secondaryDomainList);
		}
		List<OwnDomainEntity> thirdDomainList = ownDomainEntityDAO.getDomainListBasedCompanyName(RV_THIRD_COMPANY_NAME);
		if (thirdDomainList != null) {
			resultList.addAll(thirdDomainList);
		}
		System.out.println("====RVDomainCnt:" + resultList.size() + " " + RV_COMPANY_NAME + ":" + (domainList != null ? domainList.size() : 0) + 
			" " + RV_SECONDARY_OMPANY_NAME + ":" + (secondaryDomainList != null ? secondaryDomainList.size() : 0) + 
			" " + RV_THIRD_COMPANY_NAME + ":" + (thirdDomainList != null ? thirdDomainList.size() : 0));
		return resultList;
	}

	public List<Integer> getRVTargetDomainIdList() {
		List<Integer> resultList = new ArrayList<Integer>();
		List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(RV_COMPANY_NAME);
		if (domainList != null) {
			List<Integer> rvIdList = domainList.stream().map(v1 -> v1.getId()).collect(Collectors.toList());
			resultList.addAll(rvIdList);
		}
		List<OwnDomainEntity> secondaryDomainList = ownDomainEntityDAO.getDomainListBasedCompanyName(RV_SECONDARY_OMPANY_NAME);
		if (secondaryDomainList != null) {
			List<Integer> sedRvIdList = secondaryDomainList.stream().map(v1 -> v1.getId()).collect(Collectors.toList());
			resultList.addAll(sedRvIdList);
		}
		List<OwnDomainEntity> thirdDomainList = ownDomainEntityDAO.getDomainListBasedCompanyName(RV_THIRD_COMPANY_NAME);
		if (thirdDomainList != null) {
			List<Integer> sedRvIdList = thirdDomainList.stream().map(v1 -> v1.getId()).collect(Collectors.toList());
			resultList.addAll(sedRvIdList);
		}
		System.out.println("====RVDomainIdCnt:" + resultList.size() + " " + RV_COMPANY_NAME + ":" + (domainList != null ? domainList.size() : 0) + 
			" " + RV_SECONDARY_OMPANY_NAME + ":" + (secondaryDomainList != null ? secondaryDomainList.size() : 0) + 
			" " + RV_THIRD_COMPANY_NAME + ":" + (thirdDomainList != null ? thirdDomainList.size() : 0));
		return resultList;
	}

}