package seoclarity.backend.clarity360.vo;

import java.util.List;
import java.util.Map;

public class GscVO extends Clarity360BaseVO {
	
	private Integer relId;
	
	private Integer[] acrossDomainIds;
	private Integer[] relIds;
	private List<Integer> relIdList;
	
	private Boolean bigQuery;
	
	public Boolean getBigQuery() {
		return bigQuery;
	}

	public void setBigQuery(Boolean bigQuery) {
		this.bigQuery = bigQuery;
	}

	public Integer getRelId() {
		return relId;
	}

	public void setRelId(Integer relId) {
		this.relId = relId;
	}

	public Integer[] getAcrossDomainIds() {
		return acrossDomainIds;
	}

	public void setAcrossDomainIds(Integer[] acrossDomainIds) {
		this.acrossDomainIds = acrossDomainIds;
	}

	public Integer[] getRelIds() {
		return relIds;
	}

	public void setRelIds(Integer[] relIds) {
		this.relIds = relIds;
	}

	public List<Integer> getRelIdList() {
		return relIdList;
	}

	public void setRelIdList(List<Integer> relIdList) {
		this.relIdList = relIdList;
	}

}
