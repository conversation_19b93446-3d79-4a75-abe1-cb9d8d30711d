package seoclarity.backend.clarity360.vo;

public class RgVO extends Clarity360BaseVO {

	private Integer index;
	private Integer engineId;
	private Integer languageId;
	private String device;
	private String domainName;
	private Integer ownDomainId;
	private Boolean trueRank;
	private String type;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public Integer getEngineId() {
		return engineId;
	}

	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Boolean getTrueRank() {
		return trueRank;
	}

	public void setTrueRank(Boolean trueRank) {
		this.trueRank = trueRank;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
