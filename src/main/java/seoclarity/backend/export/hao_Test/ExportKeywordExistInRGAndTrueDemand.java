package seoclarity.backend.export.hao_Test;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.RGKeywordStreamDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickSteamEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1097358460
 * --Hao
 * --20230418
 */
public class ExportKeywordExistInRGAndTrueDemand {

    private static String folder = "files/kwFiles/";
    private static final String SPLIT = "\t";
    //    private static String folder = "i:/11724/22";
    private static int engineId = 1;
    private static int languageId = 1;


    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
    private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private RGKeywordStreamDAO rgKeywordStreamDAO;
    private GscClickSteamDAO gscClickSteamDAO;

    public ExportKeywordExistInRGAndTrueDemand() {
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
        rgKeywordStreamDAO = SpringBeanFactory.getBean("RGKeywordStreamDAO");
        gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
        seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
    }

    public static void main(String[] args) {
        System.out.println(" ======================= gogogo ========================");
        if (null != args && args.length > 0) {
            folder = folder + args[0];
        }
        ExportKeywordExistInRGAndTrueDemand in = new ExportKeywordExistInRGAndTrueDemand();
        try {
            in.process();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    private void process() throws IOException {
        for (File file : new File(folder).listFiles()) {
            if (file.getName().contains(".txt")) {
                List<String> lines;
                try {
                    lines = IOUtils.readLines(new FileInputStream(file));
                } catch (IOException e) {
                    e.printStackTrace();
                    return;
                }
//            queryByRankcheck(lines,file.getName());
                queryByRankcheckV2(lines, file.getName());
            }
        }
    }

    private void queryByRankcheck(List<String> lines, String fileName) throws IOException {
        File outFile = new File(folder + "process" + fileName);
        //
        List<List<String>> kwList2 = CollectionSplitUtils.splitCollectionBySizeWithStream(lines, 100);
        for (List<String> kwList3 : kwList2) {

            System.out.println(" kwList3 size ");
            System.out.println(kwList3.size());
            List<String> kyNameList = kwList3.stream().map(kw -> {
                try {
                    return URLEncoder.encode(kw.toLowerCase(), "utf-8").toLowerCase();
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                    return null;
                }
            }).collect(Collectors.toList());
            //log.info("--------------[" + timeFormat.format(new Date()) + "] get seoClarityKeywordEntityList --------------");
            List<SeoClarityKeywordEntity> seoClarityKeywordEntityList = seoClarityKeywordEntityDAO.getKeywordEntityListByNames(kyNameList);
            if (seoClarityKeywordEntityList.size() == 0) {
                continue;
            }
            System.out.println(" seoClarityKeywordEntityList size : ");
            System.out.println(seoClarityKeywordEntityList.size());

            List<Integer> rankCheckIdList = seoClarityKeywordEntityList.stream().map(item -> item.getId()).collect(Collectors.toList());

            List<Integer> dirtyKeywordIdList = seoClarityKeywordEntityDAO.checkExistsInDirtyKeywords(engineId, languageId, rankCheckIdList);
            System.out.println(" dirtyKeywordIdList size : ");
            System.out.println(dirtyKeywordIdList.size());

            List<Integer> dirtyKeywordIdList2 = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.checkExistsInDirtyKeywords(engineId, languageId, rankCheckIdList);

            System.out.println(" dirtyKeywordIdList2 size : ");
            System.out.println(dirtyKeywordIdList2.size());

            Set<Integer> kwIdSet = new HashSet<>();
            kwIdSet.addAll(dirtyKeywordIdList);
            kwIdSet.addAll(dirtyKeywordIdList2);

            System.out.println(" kwIdSet size : ");
            System.out.println(kwIdSet.size());

//            seoClarityKeywordEntityList.stream().filter(entity->kwIdSet.contains(entity.getId())).forEach(entity->entity.setRgExistence("RG exist"));
            List<String> outLines = new ArrayList<>();
            for (SeoClarityKeywordEntity en : seoClarityKeywordEntityList) {
                int id = en.getId();
                String kw = en.getKeywordText();
                String rgExist = "";
                if (kwIdSet.contains(id)) {
                    rgExist = "RG Exist";
                }
                System.out.println(" kw : " + kw + " rgExist : " + rgExist);

                outLines.add(kw + "\t" + rgExist);
            }
            FileUtils.writeLines(outFile, outLines, true);

        }
    }

    private void queryByRankcheckV2(List<String> lines, String fileName) throws IOException {
        String fi = fileName.substring(0,fileName.indexOf("."));
        File outFile = new File(folder  + fi + "_result.txt");

        List<String> headers = new ArrayList<String>();
        StringBuffer header = new StringBuffer();


        header.append("KeywordName").append(SPLIT);
        header.append("TrueDemand").append(SPLIT);
        header.append("RGExist");
        headers.add(header.toString());
        FileUtils.writeLines(outFile, headers, true);
        //
        List<List<String>> kwList2 = CollectionSplitUtils.splitCollectionBySizeWithStream(lines, 100);
        String tableName = "local_rg_keyword_expansion_check_" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");

        //(3) create tmp keyword table on lweb-cdb-cstream-01:
        String table = rgKeywordStreamDAO.createTableByDatetime(tableName);
//        System.out.println("===###tablename : " + table);
        for (List<String> kwList3 : kwList2) {
            List<SeoClarityKeywordEntity> endData = new ArrayList<>();
            List<String> kyNameList = kwList3.stream().map(kw -> {
                try {
                    return URLEncoder.encode(kw.toLowerCase(), "utf-8").toLowerCase();
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                    return null;
                }
            }).collect(Collectors.toList());

            List<SeoClarityKeywordEntity> seoClarityKeywordEntityList = seoClarityKeywordEntityDAO.getKeywordEntityListByNames(kyNameList);


            if (seoClarityKeywordEntityList.size() <= 0) {
                for (String s : kwList3) {
                    SeoClarityKeywordEntity ent = new SeoClarityKeywordEntity();
                    ent.setId(0);
                    ent.setKeywordText(s);
                    endData.add(ent);
                }

                System.out.println(" 100个关键字都找不到 : " + kyNameList);

                // (4) load keywords of step 1 into default.local_rg_keyword_expansion_check_yyyyMMddHHMMSS
                rgKeywordStreamDAO.insertKeyword(endData, table);
                continue;
            }
            List<Integer> rankCheckIdList = seoClarityKeywordEntityList.stream().map(item -> item.getId()).collect(Collectors.toList());

            List<Integer> dirtyKeywordIdList = seoClarityKeywordEntityDAO.checkExistsInDirtyKeywords(engineId, languageId, rankCheckIdList);

            List<Integer> rGKeywordIdList = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.checkExistsInDirtyKeywords(engineId, languageId, rankCheckIdList);

            Set<Integer> kwIdSet = new HashSet<>();
            kwIdSet.addAll(dirtyKeywordIdList);
            kwIdSet.addAll(rGKeywordIdList);

            for (SeoClarityKeywordEntity en : seoClarityKeywordEntityList) {
                if (!kwIdSet.contains(en.getId())) {
                    en.setId(0);
                }else {

                }
            }


            for (String s : kwList3) {
                SeoClarityKeywordEntity ent = new SeoClarityKeywordEntity();
                for (SeoClarityKeywordEntity en1 : seoClarityKeywordEntityList) {
                    String decodes =URLEncoder.encode(s.toLowerCase(), "utf-8").toLowerCase();
                    if (en1.getKeywordText().equals(decodes)) {
                        ent.setId(en1.getId());
                        ent.setKeywordText(s);
                        endData.add(ent);
                        break;
                    } else {

                    }

                }
                if (null == ent || StringUtils.isBlank(ent.getKeywordText())) {
                    ent.setId(0);
                    ent.setKeywordText(s);
                    endData.add(ent);
                }
            }

            System.out.println(" enddata size : " + endData.size());

            // (4) load keywords of step 1 into default.local_rg_keyword_expansion_check_yyyyMMddHHMMSS
            rgKeywordStreamDAO.insertKeyword(endData, table);

        }
        // export keyword info from default.local_rg_keyword_expansion_check_yyyyMMddHHMMSS:
        List<GscClickSteamEntity> loadData = gscClickSteamDAO.queryLoadData(table);
        System.out.println(" ====###loadDatasize :" + loadData.size() + " table : " + table + " file : " + fileName + " outfile :" + outFile.getName());
        //write file
        List<String> outLines = new ArrayList<>();
        if (loadData.size() > 0) {
            for (GscClickSteamEntity en : loadData) {
                String kwName = en.getKeyword();
                String inRG = String.valueOf(en.getInRG());
                String trueDemand = en.getTrueDemand();
                outLines.add(kwName + SPLIT + trueDemand + SPLIT + inRG);
            }
        }
        FileUtils.writeLines(outFile, outLines, true);
    }

}
