package seoclarity.backend.export.hao_Test;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.UserDAO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.UserEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@CommonsLog
public class HutGroupUserExport {

    private static String COMPANY = "hut";
    private static String COMPANY2 = "group";
    private static final String SPLIT = "\t";

    private UserDAO userDAO;

    public HutGroupUserExport() {
        userDAO = SpringBeanFactory.getBean("userDAO");
    }

    static final Integer[] domainIds = new Integer[]{
            11270,
            11271,
            11277,
            11278,
            11308,
            11313,
            11314,
            11334,
            11407,
            11499,
            11500,
            11505,
            11507,
            11515,
            11532,
            11603,
            11604,
            11639,
            11643,
            11722,
            11723,
            11725,
            9744,
            9753,
            9755,
            9970,
            9971,
            10092,
            10097,
            10182,
            10183,
            10184,
            10189,
            10197,
            10202,
            10203,
            10204,
            10205,
            10206,
            10208,
            10274,
            10275,
            10276,
            10402,
            10483,
            10484,
            10485,
            10508,
            10513,
            10515,
            10516,
            10517,
            10521,
            10527,
            10528,
            10529,
            10530,
            10531,
            10532,
            10533,
            10555,
            10578,
            10579,
            10580,
            10583,
            10608,
            10610,
            10611,
            10652,
            10653,
            10654,
            10656,
            10753,
            10776,
            10793,
            10801,
            10921,
            10922,
            10972,
            10991,
            10992,
            11001,
            11002,
            11031,
            11119,
            11215,
            11216,
            11229,
            11343
    };

    public static void main(String[] args) {
        System.out.println(" **************************** gogogo *******************************");
        HutGroupUserExport in = new HutGroupUserExport();
        in.process();
    }

    private void process() {

        List<String> extractLines = new ArrayList<String>();
        File fi = new File("files/hutGroup.txt");

        try {
            addHeadersForExactFile(fi);
        } catch (IOException e) {
            e.printStackTrace();
        }

        List<UserEntity> userList = userDAO.getUsersByCompany(COMPANY, COMPANY2, domainIds);
        System.out.println("  userList size :" + userList.size());
        if (userList.size() > 0) {

            List<Integer> idList = new ArrayList<>();
            for (UserEntity user : userList) {
                idList.add(user.getId());
            }
            List<UserEntity> userDetails = userDAO.getUserDetails(idList);
            if (userDetails.size() > 0) {
                System.out.println(" userDetails : " + userDetails.size());
                for (UserEntity entity : userDetails) {
                    extractLines.add(appendData(entity));
                }
                try {
                    System.out.println(" extractLines size :   " + extractLines.size());
                    FileUtils.writeLines(fi, extractLines, true);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private String appendData(UserEntity entity) {
        StringBuffer line = new StringBuffer();

        line.append(entity.getEmail()).append(SPLIT);
        line.append(entity.getName()).append(SPLIT);
        if (entity.getState() == 1) {
            line.append("active(1)").append(SPLIT);
        } else if (entity.getState() == 2) {
            line.append("removed(2)").append(SPLIT);
        } else {
            line.append(entity.getState()).append(SPLIT);
        }
        line.append(entity.getCompany()).append(SPLIT);
        line.append(entity.getLastLogin()).append(SPLIT);
        line.append(entity.getDomainCount());

        System.out.println(" line : " + line.toString());
        return line.toString();
    }


    private void addHeadersForExactFile(File outFile) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Email").append(SPLIT);
        header.append("User Name").append(SPLIT);
        header.append("State").append(SPLIT);
        header.append("Company").append(SPLIT);
        header.append("Last Login").append(SPLIT);
        header.append("Domain Count");
        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }
}
