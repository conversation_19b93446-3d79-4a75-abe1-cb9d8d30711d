package seoclarity.backend.export;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.AgencyInfoEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.service.AgencyInfoManager;
import seoclarity.backend.utils.Constants;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;

import java.util.*;

@CommonsLog
public class ExpediaDailyEmailAlertForKeywords {

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    private AgencyInfoManager agencyInfoManager;

    private boolean shouldSendMail = false;

    public ExpediaDailyEmailAlertForKeywords() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        agencyInfoManager = SpringBeanFactory.getBean("agencyInfoManager");
    }

    public static final List<Integer> EXPEDIA_DOMAIN_LIST = Arrays.asList(
            12830, 12835, 12836, 12839, 13347, 522, 550, 551, 552, 553, 555, 556, 561, 562, 4727, 4728, 4729, 4730, 4731, 4732, 4733, 4734,
            4735, 4736, 4737, 4738, 4739, 4740, 4741, 4742, 4743, 4744, 4745, 4746, 4747, 4748, 4749, 4750, 4751, 4752, 4753, 4754, 4755,
            4756, 4758, 4761, 4762, 4763, 4764, 4765, 4769, 5069, 5070, 5071, 5115, 5149, 5157, 5160, 5609, 5654, 5655, 5656, 5657, 5660,
            5661, 5662, 5663, 5664, 5665, 5666, 5667, 5847, 5848, 5988, 5989, 6063, 6373, 6632, 6654, 6979, 6980, 7076, 7175, 7177, 7178,
            7179, 7181, 7182, 7410, 7412, 7586, 7587, 7588, 7589, 7590, 8439, 10658, 10659, 10660, 10661, 10662, 10663, 10664, 10665,
            10666, 10667, 10668, 10669, 10670, 10671, 10672, 10673, 10674, 10675, 10978, 10979, 10980, 12286, 12287, 12288, 12784,
            12822, 12823, 12824, 12825, 12826, 12827, 12828, 12829, 12831, 12832, 7186, 7187, 7188, 7189, 8644, 8645, 8646, 8647, 8648,
            8649, 8650, 8651, 8743, 8744, 9005, 9271, 9275, 9276, 9277, 9278, 9279, 9280, 9281, 9282, 9283, 9284, 9285, 9286, 9287, 9288,
            9289, 9291, 9290, 9292, 9293, 9294, 9295, 9296, 9297, 9298, 9299, 9300, 9301, 9302, 9303, 9304, 9305, 9306, 9307, 9308, 9309,
            9311, 9312, 9313, 9331, 9332, 9333, 9334, 9335, 9522, 9523, 9524, 9525, 9526, 9527, 9528, 9529, 9532, 9534, 9535, 9536, 9537,
            9538, 9540, 9541, 9543
    );

    private void process(Date processingDate) {
        if(processingDate == null){
            processingDate = DateUtils.addDays(new Date(), -1);
        }
        String message = "";
        for (Integer domainId : EXPEDIA_DOMAIN_LIST) {
            message += getKwCntFromQueuebaseByDomain(domainId, processingDate);
        }

        log.info("==================shouldSendMail:" + shouldSendMail);
        if(shouldSendMail){
            sendMailReport(FormatUtils.formatDate(processingDate, FormatUtils.DATE_PATTERN_2), message);
        }

    }

    private String getKwCntFromQueuebaseByDomain(int domainId, Date processingDate) {

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + domainId);
            return "";
        }
        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);
        Integer pauseRankingFlg = ownDomainSettingEntity.getPauseRankingFlg();
        Integer pauseRankingStatus = ownDomainSettingEntity.getPauseRankingStatus();

        String domainName = ownDomainEntity.getDomain() + " - " + domainId;
        if (pauseRankingFlg != null && pauseRankingFlg == 1 && pauseRankingStatus != null && pauseRankingStatus == 2) {
            log.info("pauseRankin domainId:" + domainId + ",pauseRankingFlg:" + pauseRankingFlg + ",pauseRankingStatus:" + pauseRankingStatus);
            domainName = domainName + "(Paused)";
        }
        String message = " <tr><td>" + domainName + "</td>";

        Date endDate = DateUtils.addDays(processingDate, 1);
        String processingDateStr = FormatUtils.formatDate(processingDate, FormatUtils.DATE_PATTERN_2);
        String processingEndDateStr = FormatUtils.formatDate(endDate, FormatUtils.DATE_PATTERN_2);
        log.info("=======getKwCntFromQueuebaseByDomain:" + domainId + ",processingDate:" + processingDate + ",endDate:" + endDate);

        int addKwCnt = 0;
        int deleteKwCnt = 0;
        List<Map<Integer, Object>> list = resourceBatchInfoEntityDAO.getExpediaDailyReport(domainId, processingDateStr, processingEndDateStr);

        if (CollectionUtils.isNotEmpty(list)) {

            for (Map<Integer, Object> map : list) {
                Object actionType = map.get("actionType");
                String kwCnt = map.get("kwCnt").toString();
                if(!shouldSendMail && !kwCnt.equals("0")){
                    log.info("==================SendMailDomainId:" + domainId);
                    shouldSendMail = true;
                }

                if(actionType != null && actionType.toString().equals("0")){
                    deleteKwCnt = Integer.parseInt(kwCnt);
                }else if(actionType != null && actionType.toString().equals("1")){
                    addKwCnt = Integer.parseInt(kwCnt);
                }
                log.info("====actionType:" + actionType + ",kwCnt:" + kwCnt);
            }
        } else {
            //todo no queuebase task
//            message += " <td>0</td>" ;
//            message += " <td>0</td>" ;
        }
        message += " <td>" + addKwCnt + "</td>" ;
        message += " <td>" + deleteKwCnt + "</td>" ;
        message += " </tr>" ;

        return message;
    }

    private void sendMailReport(String processingDate, String message) {

        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(4);

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Expedia");
        reportMap.put("successMessage", message);
//        String emailTo = "<EMAIL>";
        String emailTo[] = new String[]{
                "<EMAIL>", "<EMAIL>"
        };
        String subject = "Expedia daily email alert for keywords " + processingDate;
        String[] ccTo = new String[]{Constants.DEV_TEAM_EMAIL};
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject,
                "mail_expedia_keyword_report.txt", "mail_expedia_keyword_report.html",
                reportMap, agencyInfo, ZeptoMailSenderComponent.FUNCTION_TYPE_ALERTS, null, null);
    }



    public static void main(String[] args) throws Exception {
        ExpediaDailyEmailAlertForKeywords expediaDailyEmailAlertForKeywords = new ExpediaDailyEmailAlertForKeywords();
        if(args != null && args.length >= 1){
            expediaDailyEmailAlertForKeywords.process(FormatUtils.toDate(args[0], FormatUtils.DATE_PATTERN_2));
        }else {
            expediaDailyEmailAlertForKeywords.process(null);
        }

    }


}
