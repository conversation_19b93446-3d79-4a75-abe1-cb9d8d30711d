package seoclarity.backend.export;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.jets3t.service.impl.rest.httpclient.GoogleStorageService;
import org.jets3t.service.model.GSObject;
import org.jets3t.service.model.StorageBucket;
import org.jets3t.service.security.GSCredentials;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.bigquery.Bigquery;
import com.google.api.services.bigquery.model.Job;
import com.google.api.services.bigquery.model.JobConfiguration;
import com.google.api.services.bigquery.model.JobConfigurationLoad;
import com.google.api.services.bigquery.model.JobReference;
import com.google.api.services.bigquery.model.TableReference;
import com.google.api.services.bigquery.model.TableSchema;
import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.RankIndexParamEntityDAO;
import seoclarity.backend.dao.actonia.bigquery.BigqueryAuthenticationInfoDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.rankcheck.SeoClarityLanguageEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClaritySearchEngineEntityDAO;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.entity.actonia.CommonParamEntity.ParamFuncName;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.RankIndexParamEntity;
import seoclarity.backend.entity.actonia.bigquery.BigqueryAuthenticationInfoEntity;
import seoclarity.backend.export.vo.ClarityDBTypesFilterVO;
import seoclarity.backend.export.vo.TextTypeFilterVO;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.JsonMapper;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.BigQueryUtils;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

/**
 * <AUTHOR>
 * @create 
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.export.ExtractWtdAvgRankAndSom" -Dexec.args="8443 2 2 0 2020-10-20"
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.export.ExtractWtdAvgRankAndSom" -Dexec.args="8443 2 2 0"
 * 
 * 			ownDomainId = NumberUtils.toInt(args[0]);
			device = NumberUtils.toInt(args[1]); //0:desktop  1:mobile 2:all
			geoType = NumberUtils.toInt(args[2]);//0:national 1:geo    2:all
			tagIds = args[3];
			qDate = args[4];
 **/
public class ExtractWtdAvgRankAndSom {
	
	private static Bigquery bigquery;
    private static final List<String> SCOPE = Arrays.asList("https://www.googleapis.com/auth/bigquery");
    private static final HttpTransport TRANSPORT = new NetHttpTransport();
    private static final JsonFactory JSON_FACTORY = new JacksonFactory();

	private  static LogglyVO logglyVO = new LogglyVO();
	private static int totalCnt = 0;


    private ClDailyRankingEntityDao clDailyRankingEntityDao;
	
	private RankIndexParamEntityDAO rankIndexParamEntityDAO;
	
	private OwnDomainEntityDAO ownDomainEntityDAO;
	
	private ScKeywordRankManager scKeywordRankManager;
	
	private SeoClaritySearchEngineEntityDAO seoClaritySearchEngineEntityDAO;

	private SeoClarityLanguageEntityDAO seoClarityLanguageEntityDAO;

	private BigqueryAuthenticationInfoDAO bigqueryAuthenticationInfoDAO;
	
	private CommonParamDAO commonParamDAO;
	private ExtractService extractService;
	
	protected final JsonMapper mapper = new JsonMapper();
	
	public ExtractWtdAvgRankAndSom() {
		// TODO Auto-generated constructor stub
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		rankIndexParamEntityDAO = SpringBeanFactory.getBean("rankIndexParamEntityDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
		seoClarityLanguageEntityDAO = SpringBeanFactory.getBean("seoClarityLanguageEntityDAO");
		bigqueryAuthenticationInfoDAO = SpringBeanFactory.getBean("bigqueryAuthenticationInfoDAO");
		seoClaritySearchEngineEntityDAO = SpringBeanFactory.getBean("seoClaritySearchEngineEntityDAO");
		commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
		extractService = SpringBeanFactory.getBean("extractService");
	}
	
	public static int GEO_TYPE_NATIONAL = 0;
	public static int GEO_TYPE_GEO = 1;
	public static int GEO_TYPE_ALL = 2;
	
	public static int DEVICE_DESKTOP = 0;
	public static int DEVICE_MOBILE = 1;
	public static int DEVICE_ALL = 2;
	
	private static Integer ownDomainId;
	private static Integer device; 
	private static Integer geoType;
	private static String tagIds;
	private static String qDate;

	public static void main(String[] args) {

		String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

		if (args != null && args.length >= 5) {
			
			ownDomainId = NumberUtils.toInt(args[0]);
			device = NumberUtils.toInt(args[1]); //0:desktop  1:mobile 2:all
			geoType = NumberUtils.toInt(args[2]);//0:national 1:geo    2:all
			tagIds = args[3];
			qDate = args[4];
			
			Pattern pattern = Pattern.compile("(\\d{4})-(\\d+)-(\\d+).*");
			Matcher matcher = pattern.matcher(qDate);
            if (!matcher.matches()) {
            	System.out.println("@@@ date format is not correct!!! exit");
            	System.exit(-1);
            }
			
			if (ownDomainId <= 0) {
				System.out.println("@@@ ownDomainId is empty");
				System.exit(-1);
			}

			System.out.println(" @@@ ownDomainId : " + ownDomainId + ", device : " + device + ", geoType : " + geoType + ", tagId : " + tagIds);
			ExtractWtdAvgRankAndSom extractWtdAvgRankAndSom = new ExtractWtdAvgRankAndSom();
			extractWtdAvgRankAndSom.process();
			logglyVO.setpDate(qDate);
			logglyVO.setStatus(LogglyVO.STATUS_OK);
			logglyVO.setsTime(stTime);
			logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
			logglyVO.setRows(String.valueOf(totalCnt));
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
		} else if (args != null && args.length == 4) {
			
			ownDomainId = NumberUtils.toInt(args[0]);
			device = NumberUtils.toInt(args[1]); //0:desktop  1:mobile 2:all
			geoType = NumberUtils.toInt(args[2]);//0:national 1:geo    2:all
			tagIds = args[3];
			qDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -1), "yyyy-MM-dd");
			
			Pattern pattern = Pattern.compile("(\\d{4})-(\\d+)-(\\d+).*");
			Matcher matcher = pattern.matcher(qDate);
            if (!matcher.matches()) {
            	System.out.println("@@@ date format is not correct!!! exit");
            	System.exit(-1);
            }
			
			if (ownDomainId <= 0) {
				System.out.println("@@@ ownDomainId is empty");
				System.exit(-1);
			}
			
			System.out.println(" @@@ ownDomainId : " + ownDomainId + ", device : " + device + ", geoType : " + geoType + ", tagId : " + tagIds);
			ExtractWtdAvgRankAndSom extractWtdAvgRankAndSom = new ExtractWtdAvgRankAndSom();
			extractWtdAvgRankAndSom.process();

			logglyVO.setpDate(qDate);
			logglyVO.setStatus(LogglyVO.STATUS_OK);
			logglyVO.setsTime(stTime);
			logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
			logglyVO.setRows(String.valueOf(totalCnt));
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
		} else {
			System.out.println(" @@@ param is not correct");
			System.exit(-1);
		}
	}
	
	private static List<String> COMPETITOR_LIST = new ArrayList<>();
	static {
		
		COMPETITOR_LIST.add("jet2holidays.com");
		COMPETITOR_LIST.add("lastminute.com");
		COMPETITOR_LIST.add("onthebeach.co.uk");
		COMPETITOR_LIST.add("tui.co.uk");
		COMPETITOR_LIST.add("firstchoice.co.uk");
		COMPETITOR_LIST.add("thomascook.com");
	}
	
	
	private static String[] domainLevelHeaderColumn = new String[] {
		"Domain", "Ranking Date", "Engine", "Language", "Device", "Wtd. Avg Rank", "Share Of Market" 
	};
	
	private static String[] contentTypeLevelHeaderColumn = new String[] {
		"Content Type", "Domain", "Ranking Date", "Engine", "Language", "Device", "Share Of Market",
	};
	
	private static String tmpFolder = "/home/<USER>/";
	private static String prefixDomainLevel = "Extract_Domain_Level_SOM_Wtdavgrank_";
	private static String prefixContentTypeLevel = "Extract_Content_Type_Level_SOM_Wtdavgrank_";
	
	private static String SPLIT = "\t";
	
    private static String bucketName;
    private static String accessKey;
    private static String secretKey;

    private static String AccountId;
    private static String projectId;
    private static String dataSetId;
    private static String tableId;
    private static String privateKeyPath;
    
    private static String tableName = "love_holiday_daily_extract";

	private void process() {
		
		BigqueryAuthenticationInfoEntity bigqueryAuthenticationInfoEntity = bigqueryAuthenticationInfoDAO.getByDomainId(8443);
        if (bigqueryAuthenticationInfoEntity == null) {
            System.out.println("===bigquery AuthenticationInfo not exist " + 4);
            return;
        }

        bucketName = bigqueryAuthenticationInfoEntity.getBucketName();
        accessKey = bigqueryAuthenticationInfoEntity.getAccessKey();
        secretKey = bigqueryAuthenticationInfoEntity.getSecretKey();
        AccountId = bigqueryAuthenticationInfoEntity.getAccountId();
        projectId = bigqueryAuthenticationInfoEntity.getProjectId();
        dataSetId = bigqueryAuthenticationInfoEntity.getDataSetId();
        tableId = bigqueryAuthenticationInfoEntity.getTableId();
        privateKeyPath = bigqueryAuthenticationInfoEntity.getPrivateKeyPath();
        
        if (StringUtils.isBlank(bucketName)) {
            System.out.println("!!!!! bucketName null,exit:" + bucketName);
            return;
        }
        if (StringUtils.isBlank(accessKey)) {
            System.out.println("!!!!! accessKey null,exit:" + accessKey);
            return;
        }
        if (StringUtils.isBlank(secretKey)) {
            System.out.println("!!!!! secretKey null,exit:" + secretKey);
            return;
        }
        if (StringUtils.isBlank(AccountId)) {
            System.out.println("!!!!! AccountId null,exit:" + AccountId);
            return;
        }
        if (StringUtils.isBlank(projectId)) {
            System.out.println("!!!!! projectId null,exit:" + projectId);
            return;
        }
        if (StringUtils.isBlank(dataSetId)) {
            System.out.println("!!!!! dataSetId null,exit:" + dataSetId);
            return;
        }
        if (StringUtils.isBlank(tableId)) {
            System.out.println("!!!!! tableId null,exit:" + tableId);
            return;
        }
        if (StringUtils.isBlank(privateKeyPath)) {
            System.out.println("!!!!! privateKeyPath null,exit:" + privateKeyPath);
            return;
        }
        
        //create table
        BigQueryUtils bigQueryUtils = new BigQueryUtils(projectId, dataSetId, tableId, AccountId, privateKeyPath);
        try {
            bigQueryUtils.createTableByRestApiBySchema(BigQueryUtils.getJsonRankTableSchemaLoveHolidayBigQuery(), 
            		tableName);
        } catch (GoogleJsonResponseException e) {
            if (e.getDetails().getCode() == 409) {
                System.out.println("===table Already Exists:" + e.getMessage());
            } else {
                System.out.println("===create table error:" + e.getMessage());
                System.exit(1);
            }

			logglyVO.setStatus(LogglyVO.STATUS_NG);
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        } catch (Exception e) {
            e.printStackTrace();
            System.exit(1);
        }
        
		
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
		
		//get own domain setting and replace with the competitor domain name
		OwnDomainEntity competitorDomain = ownDomainEntityDAO.getById(ownDomainId);
		if (ownDomainEntity == null) {
			System.out.println("--- domain is not exist, ownDomainId:" + ownDomainId);
			try {
				extractService.sendMailReport("ERROR:Export for inactive OID:" + ownDomainId, "Please disable export for inactive OID:" + ownDomainId + "(" + getClass().getName() + ")");
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			return;
		}

		int frequency = 1;//daily
		if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
			qDate = FormatUtils.getLastSundayForWeeklyOfString(qDate);
			System.out.println("====weekly domain processDate:" + qDate);
			frequency = 7;//weekly
		}
		logglyVO.setoId(String.valueOf(ownDomainId));
		logglyVO.setName("ExtractWtdAvgRankAndSom");
		logglyVO.setDevice(device==1?"m":"d");
		List<String> groupList = new ArrayList<>();
		groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
		logglyVO.setGroups(groupList);
		
		//--------------------- get content type list----------------------------
		List<CommonParamEntity> contentTypeList = commonParamDAO.getListByFuncNameAndOwndomainId(ownDomainId, ParamFuncName.UrlTextType.name());
		
		
		//--------------------- get ctr ----------------------
		
		List<RankIndexParamEntity> indexList = rankIndexParamEntityDAO.getRankIndexParams(ownDomainId, 0);
		
		//if ctr is not setting, then use default ctr
		if (indexList == null || indexList.size() ==0) {
			indexList = rankIndexParamEntityDAO.getRankIndexParams(0, 0);
		}
		Float[] ctrArray = new Float[indexList.size()];
		for(int i = 0; i < indexList.size(); i++) {
			ctrArray[i] = indexList.get(i).getParamValue();
		}
		
		//---------------------- get engine list ---------------------------
		
		List<DomainSearchEngineRelEntity> allEngineList = new ArrayList<DomainSearchEngineRelEntity>();
		DomainSearchEngineRelEntity domainSearchEngineRelEntity = new DomainSearchEngineRelEntity();
		
		domainSearchEngineRelEntity.setRankcheckSearchEngineId(ScKeywordRankManager.getSearchEngineId(ownDomainEntity));
		domainSearchEngineRelEntity.setRankcheckSearchLanguageid(ScKeywordRankManager.getSearchLanguageId(ownDomainEntity));
		
		allEngineList.add(domainSearchEngineRelEntity);
		
		List<DomainSearchEngineRelEntity> engineList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
		
		allEngineList.addAll(engineList);
		
		List<String> uniqueKeyList = new ArrayList<>();
		
		for(DomainSearchEngineRelEntity engineRelEntity : engineList) {
			String key = engineRelEntity.getRankcheckSearchEngineId() + "_" + engineRelEntity.getRankcheckSearchLanguageid();
			if (!uniqueKeyList.contains(key)) {
				uniqueKeyList.add(key);
			}
		}
		
		List<Integer> deviceList = new ArrayList<>();
		if (device == DEVICE_ALL) {
			deviceList.add(DEVICE_DESKTOP);
			deviceList.add(DEVICE_MOBILE);
		} else {
			deviceList.add(device);
		}
		
		//---------------------- create file ---------------------------
		try {
			File domainLevelTmpFileFolder = new File(tmpFolder + ownDomainId + "/domainLevel/");
			File contentTypeLevelTmpFileFolder = new File(tmpFolder + ownDomainId + "/contentTypeLevel/");
			
			if (domainLevelTmpFileFolder == null || !domainLevelTmpFileFolder.exists()) {
				domainLevelTmpFileFolder.mkdirs();
			}
			
			if (contentTypeLevelTmpFileFolder == null || !contentTypeLevelTmpFileFolder.exists()) {
				contentTypeLevelTmpFileFolder.mkdirs();
			}
			
			File domainLevelFile = new File(domainLevelTmpFileFolder.getAbsolutePath() 
					+ "/" + prefixDomainLevel + ownDomainId + "_" + qDate + ".csv");
//			File contentTypeLevelFile = new File(contentTypeLevelTmpFileFolder.getAbsolutePath() 
//					+ "/" + prefixContentTypeLevel + ownDomainId + "_" + qDate + ".csv");
			
			if (domainLevelFile != null && domainLevelFile.exists() ) {
				domainLevelFile.delete();
				domainLevelFile = new File(domainLevelTmpFileFolder.getAbsolutePath() + "/" + prefixDomainLevel + ownDomainId + "_" + qDate + ".csv");
			}
			
//			if (contentTypeLevelFile != null && contentTypeLevelFile.exists() ) {
//				contentTypeLevelFile.delete();
//				contentTypeLevelFile = new File(contentTypeLevelTmpFileFolder.getAbsolutePath() + "/" + prefixContentTypeLevel + ownDomainId + "_" + qDate + ".csv");
//			}
			
//			System.out.println("contentTypeLevelFile path :" + contentTypeLevelFile.getAbsolutePath());
			System.out.println("domainLevelFile path :" + domainLevelFile.getAbsolutePath());
			
			System.out.println(" ----- write header -----");
//			writeHeader(StringUtils.join(domainLevelHeaderColumn, SPLIT) + "\n", domainLevelFile);
//			writeHeader(StringUtils.join(contentTypeLevelHeaderColumn, SPLIT) + "\n", contentTypeLevelFile);
			
			totalCnt = uniqueKeyList.size();
			for(String engineLanguageKey : uniqueKeyList) {
				Integer engineId = NumberUtils.toInt(StringUtils.split(engineLanguageKey, "_")[0]);
				Integer languageId = NumberUtils.toInt(StringUtils.split(engineLanguageKey, "_")[1]);
				
				if (engineId == 0 || languageId == 0) {
					System.out.println("=====Engine language is not correct !!! engineLanguageKey:" + engineLanguageKey);
					System.out.println("=====Engine language is not correct !!! engineId:" + engineId);
					System.out.println("=====Engine language is not correct !!! languageId:" + languageId);
					continue;
				}
				
				System.out.println("===== processing engine:");
			
				String engineName = seoClaritySearchEngineEntityDAO.getEngineNameById(engineId);
				String languageName = seoClarityLanguageEntityDAO.getLanguageById(languageId);
				
				// 5/8 and 6/8 are having different engine name
				if (StringUtils.equalsIgnoreCase(engineName, "google.co.uk-ALL")) {
					engineName = "google.co.uk";
				}
				
				try {
					if (StringUtils.contains(languageName, "_")) {
						languageName = StringUtils.lowerCase(StringUtils.split(languageName, "_")[1]);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				
				if (!ScKeywordRankManager.isGoogleEngine(engineId)) {
					System.out.println("@@@ skip for other engines, engineRelEntity.getSearchEngineId():" + engineId);
					continue;
				}
				
				for(Integer deviceType : deviceList) {
					
					processByDomain(contentTypeList, ownDomainEntity, deviceType, 
							engineId, languageId, ctrArray, engineName, languageName, 
							domainLevelFile);
					
					//all competitor is 
					for(String competitor : COMPETITOR_LIST) {
						competitorDomain.setName(competitor);
						competitorDomain.setDomain(competitor);
						competitorDomain.setExactMatch(OwnDomainEntity.MATCH_BROAD + "");
						
						processByDomain(contentTypeList, competitorDomain, deviceType, 
								engineId, languageId, ctrArray, engineName, languageName, 
								domainLevelFile);
					}
					
					System.out.println("====== copy file to ftp server");
					
					//send to google cloud storage
		            uploadFileToGS(domainLevelFile.getAbsolutePath());
		            
		            //push to google big query
		            uploadToBigQuery(domainLevelFile.getName());
					
//					FTPUtils.saveFileToFTP(ownDomainId, (contentTypeLevelFile.getAbsolutePath()), "/home/<USER>/8443/");
					FTPUtils.saveFileToFTP(ownDomainId, (domainLevelFile.getAbsolutePath()), "/home/<USER>/8443/");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();

			logglyVO.setStatus(LogglyVO.STATUS_NG);
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
		}
	}
	
    public static void uploadFileToGS(String uploadFileName) throws Exception {
        GSCredentials gsCredentials = new GSCredentials(
                accessKey,
                secretKey);
        System.out.println(accessKey);
        GoogleStorageService gsService = new GoogleStorageService(gsCredentials);

        // TODO
//        String uploadFileName = "e://abc.txt";

        try {
            long a = System.currentTimeMillis();

            File fileData = new File(uploadFileName);
            GSObject fileObject = new GSObject(fileData);

            StorageBucket sb = gsService.getBucket(bucketName);
            if (sb == null) {
                System.out.println(bucketName);

                gsService.createBucket(bucketName);
            }
            gsService.putObject(bucketName, fileObject);

            long b = System.currentTimeMillis();
            System.out.println("Upload to GoogleCloudStorage: " + (b - a) * 1.0 / 1000 + " s ");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    
    private void uploadToBigQuery(String fileName) throws Exception {

        GoogleCredential credential = new GoogleCredential.Builder().setTransport(TRANSPORT)
                .setJsonFactory(JSON_FACTORY)
                .setServiceAccountId(AccountId)
                .setServiceAccountScopes(SCOPE)
                .setServiceAccountPrivateKeyFromP12File(new File(privateKeyPath)).build();

        bigquery = new Bigquery.Builder(TRANSPORT, JSON_FACTORY, credential)
                .setApplicationName("BigQuery-Service-Accounts/0.1").setHttpRequestInitializer(credential).build();

        uploadRankingToBigQuery(fileName, BigQueryUtils.getJsonRankTableSchemaLoveHolidayBigQuery(), 
        		tableName);

    }
    
    public TableReference getDestTableByTableName(String tableId) {
        TableReference destTable = new TableReference();
        destTable.setProjectId(projectId);
        destTable.setDatasetId(dataSetId);
        destTable.setTableId(tableId);

        return destTable;
    }
    
    public JobConfigurationLoad getConfigLoad(TableReference destTable, TableSchema schema, String fileName) {
        JobConfigurationLoad configLoad = new JobConfigurationLoad();
        configLoad.setSchema(schema);
        configLoad.setDestinationTable(destTable);
        configLoad.setSourceFormat("NEWLINE_DELIMITED_JSON");
        // this is for CSV template. no need to merge for now
//		configLoad.setSourceFormat("CSV");


        List<String> uris = new ArrayList<String>();
        // TODO
        String exportFileName = "gs://" + bucketName + "/" + fileName;

        uris.add(exportFileName);
        configLoad.setSourceUris(uris);
        return configLoad;
    }

    
    private void uploadRankingToBigQuery(String fileName, TableSchema schema, String tableId) throws Exception {
        long a = System.currentTimeMillis();
        // this is for CSV template. no need to merge for now
//		TableSchema schema = getCSVRankTableSchema();

        TableReference destTable = getDestTableByTableName(tableId);
        JobConfigurationLoad configLoad = getConfigLoad(destTable, schema, fileName);

        Job job = new Job();
//        JobReference jobReference = new JobReference().setLocation("europe-west2");
//        job.setJobReference(jobReference);
        JobConfiguration config = new JobConfiguration();
        config.setLoad(configLoad);
        job.setConfiguration(config);

        System.out.println("data uploading");
        Bigquery.Jobs.Insert insert = bigquery.jobs().insert(projectId, job);
        insert.setProjectId(projectId);
        JobReference jobRef = insert.execute().getJobReference();
//        jobRef.setLocation("europe-west2");
        System.out.println("data loaded , jobId:" + jobRef.getJobId() + ",projectId:" + jobRef.getProjectId() + "location:" + jobRef.getLocation());

        if (!projectId.equals("lh-seo-production")) {//8443
            Job returnJob = checkQueryResults(projectId, jobRef);
            System.out.println("data verified");
        }

        long b = System.currentTimeMillis();
        System.out.println("Uploading Data to BigQuery Used : " + (b - a) * 1.0 / (1000 * 60) + " mins.");
    }
    
    private static Job checkQueryResults(String projectId, JobReference jobId)
            throws IOException, InterruptedException {
        long startTime = System.currentTimeMillis();
        long elapsedTime;

        while (true) {
            try {
                Job pollJob = bigquery.jobs().get(projectId, jobId.getJobId()).execute();
                elapsedTime = System.currentTimeMillis() - startTime;
                System.out.format("Job status (%dms) %s: %s\n", elapsedTime, jobId.getJobId(),
                        pollJob.getStatus().getState());
                if (pollJob.getStatus().getState().equals("DONE")) {
                    if (pollJob.getStatus().getErrors() != null) {
                        System.out.println(pollJob.getStatus().getErrors().toString());
                        System.out.println(pollJob.getStatus().getErrorResult().toPrettyString());
                        System.out.println("STOP Processing");
                        throw new InterruptedException();
                    }
                    return pollJob;
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }
            Thread.sleep(1000);
        }
    }

	
	private void processByDomain(List<CommonParamEntity> contentTypeList, 
			OwnDomainEntity ownDomainEntity, Integer deviceType,
			Integer engineId, Integer languageId, Float[] ctrArray,
			String engineName, String languageName, File extractFile) {
		
		List<String> contentList = new ArrayList<>();
		String contentType = "";
		String som = "";
		String wtdAvgRank = "";
		
		//"Keyword", "Domain", "Ranking Date", "Engine", "Language", "Device", "Share Of Market",
		//content type level
		for(CommonParamEntity entity : contentTypeList) {
			
			ClarityDBTypesFilterVO filterVO = convertToClarityDBTypesFilterVO(entity);
			
			System.out.println(new Gson().toJson(filterVO));
			
			List<Map<String, Object>> contentTypeLevelResultList = clDailyRankingEntityDao.getContentTypeLevelWtdavgRankAndSom(
					ownDomainEntity, deviceType, engineId, languageId, 
					qDate, ctrArray, tagIds, geoType, filterVO);
			//keyword_name, keyword_rankcheck_id,som
			
			contentList = new ArrayList<>();
			
			if (contentTypeLevelResultList!= null && contentTypeLevelResultList.size() > 0) {
				for (Map<String, Object> map : contentTypeLevelResultList) {
					
					JsonExtractEntity jsonExtractEntity = new JsonExtractEntity();
					/**
					 *  fields.add(domainId);
				        fields.add(domainName);
				        fields.add(contentType);
				        fields.add(date);
				        fields.add(engine);
				        fields.add(language);
				        fields.add(device);
				        fields.add(wtdAvgRank);
				        fields.add(som);
					 */
					contentType = map.get("contentType") != null ? map.get("contentType").toString() : "";
					som = map.get("som") != null ? map.get("som").toString() : "";
					
					jsonExtractEntity.setDomain_id(ownDomainEntity.getId());
					jsonExtractEntity.setDomain_name(ownDomainEntity.getDomain());
					jsonExtractEntity.setContent_type(contentType);
					jsonExtractEntity.setRanking_date(qDate);
					jsonExtractEntity.setEngine(engineName);
					jsonExtractEntity.setLanguage(languageName);
					jsonExtractEntity.setDevice(getDeviceName(deviceType));
					jsonExtractEntity.setWtd_avg_rank("");
					jsonExtractEntity.setSom(som);
					
//					contentList.add(contentType + SPLIT + ownDomainEntity.getDomain() + 
//							SPLIT + qDate + SPLIT + engineName + SPLIT + languageName + 
//							SPLIT + getDeviceName(deviceType) + SPLIT + som + "\n");
					
					contentList.add(new Gson().toJson(jsonExtractEntity));
				}
				System.out.println("content type level Total input line: " + contentList.size());
				
				try {
					FileUtils.writeLines(extractFile, contentList, true);
				} catch (Exception e) {
					e.printStackTrace();
				}
				
//				writeResultIntoFile(contentList, extractFile);
			}
			contentList = new ArrayList<>();
			
		}
		
		//"Domain", "Ranking Date", "Engine", "Language", "Device", "Wtd. Avg Rank", "Share Of Market" 
		//domain level
		List<Map<String, Object>> domainLevelResultList = clDailyRankingEntityDao.getDomainLevelWtdavgRankAndSom(
				ownDomainEntity, deviceType, engineId, 
				languageId, qDate, ctrArray, tagIds, geoType);
		
		if (domainLevelResultList!= null && domainLevelResultList.size() > 0) {
			for (Map<String, Object> map : domainLevelResultList) {
				
				JsonExtractEntity jsonExtractEntity = new JsonExtractEntity();
				
				wtdAvgRank = map.get("wtdAvgRank") != null ? map.get("wtdAvgRank").toString() : "";
				som = map.get("som") != null ? map.get("som").toString() : "";
				
				jsonExtractEntity.setDomain_id(ownDomainEntity.getId());
				jsonExtractEntity.setDomain_name(ownDomainEntity.getDomain());
				jsonExtractEntity.setContent_type("");
				jsonExtractEntity.setRanking_date(qDate);
				jsonExtractEntity.setEngine(engineName);
				jsonExtractEntity.setLanguage(languageName);
				jsonExtractEntity.setDevice(getDeviceName(deviceType));
				jsonExtractEntity.setWtd_avg_rank(wtdAvgRank);
				jsonExtractEntity.setSom(som);
				
				contentList.add(new Gson().toJson(jsonExtractEntity));
//				contentList.add(ownDomainEntity.getDomain() +  SPLIT + qDate + 
//						SPLIT + engineName + 
//						SPLIT + languageName + 
//						SPLIT + getDeviceName(deviceType) + SPLIT + wtdAvgRank + 
//						SPLIT + som + "\n");
			}
			
			System.out.println("domain level Total input line: " + contentList.size());
			try {
				FileUtils.writeLines(extractFile, contentList, true);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	public ClarityDBTypesFilterVO convertToClarityDBTypesFilterVO(CommonParamEntity paramEntity) {
		if (paramEntity == null) {
			return null;
		}

		ClarityDBTypesFilterVO vo = new ClarityDBTypesFilterVO();
		vo.setId(paramEntity.getId());
		vo.setName(paramEntity.getTitle());
		vo.setFilterType(paramEntity.getFuncName());

		String jsonData = paramEntity.getParamJson();
		//Cee 
	    //a) the old filters are [{action:, value: }, {action:, value: }]
	    //b) the new filter are complex AND / OR query, and it is Recursive Model, only one Object
//		TextTypeFilterVO[] patternFilters = mapper.fromJson(jsonData, TextTypeFilterVO[].class);
		TextTypeFilterVO patternFilter = new Gson().fromJson(jsonData, TextTypeFilterVO.class);
		vo.setFilter(patternFilter);

		return vo;
	}
	
	
	private String getDeviceName(Integer device) {
		if (device == null) {
			return null;
		} else if (device.intValue() == DEVICE_DESKTOP) {
			return "desktop";
		} else if (device.intValue() == DEVICE_MOBILE) {
			return "mobile";
		}
		return null;
	}
	
	private void writeResultIntoFile(List<String> resultList, File outputFile) {
		
		try {
			FileWriter fw = new FileWriter(outputFile, true);
			
			try {
				for (String content : resultList) {
					fw.write(content);
				}
				
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				fw.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	private void writeHeader(String headerLine, File outputFile) {
		List<String> resultList = new ArrayList<>();
		resultList.add(headerLine);
		writeResultIntoFile(resultList, outputFile);
	}

	
	 private class JsonExtractEntity {
		 
		 /**
		  *         
	       	domainId.setName("domain_id");
	        domainName.setName("domain_name");
	        date.setName("ranking_date");
	        engine.setName("engine");
	        contentType.setName("content_type");
	        wtdAvgRank.setName("wtd_avg_rank")
	        language.setName("language");
	        device.setName("device");
	        som.setName("som");
		  */

	        private Integer domain_id;
	        private String domain_name;
	        private String ranking_date;
	        private String content_type;
	        private String engine;
	        private String language;
	        private String device;
	        private String wtd_avg_rank;
	        private String som;
	        
			public Integer getDomain_id() {
				return domain_id;
			}
			public void setDomain_id(Integer domain_id) {
				this.domain_id = domain_id;
			}
			public String getDomain_name() {
				return domain_name;
			}
			public void setDomain_name(String domain_name) {
				this.domain_name = domain_name;
			}
			public String getRanking_date() {
				return ranking_date;
			}
			public void setRanking_date(String ranking_date) {
				this.ranking_date = ranking_date;
			}
			public String getEngine() {
				return engine;
			}
			public void setEngine(String engine) {
				this.engine = engine;
			}
			public String getContent_type() {
				return content_type;
			}
			public void setContent_type(String content_type) {
				this.content_type = content_type;
			}
			public String getWtd_avg_rank() {
				return wtd_avg_rank;
			}
			public void setWtd_avg_rank(String wtd_avg_rank) {
				this.wtd_avg_rank = wtd_avg_rank;
			}
			public String getLanguage() {
				return language;
			}
			public void setLanguage(String language) {
				this.language = language;
			}
			public String getDevice() {
				return device;
			}
			public void setDevice(String device) {
				this.device = device;
			}
			public String getSom() {
				return som;
			}
			public void setSom(String som) {
				this.som = som;
			}
	        
	        
    }
}
