package seoclarity.backend.export.alert;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import seoclarity.backend.utils.ClarityDBAPIUtils;

/**
 * <AUTHOR>
 * @date 2020-09-26
 * @path seoclarity.backend.export.alert.TestDownload
 * 
 */
public class TestDownload {
	
	public static void main(String[] args) throws Exception{
		Map<String, String> paramMap= new HashMap<String, String>();
		paramMap.put("engine", "google");
		paramMap.put("market", "en-us");
		paramMap.put("sdate", "20200920");
		paramMap.put("edate", "20200920");
		paramMap.put("oid", "7135");
		paramMap.put("device", args[1]);
		
		int total = 48095;
		int pageSize = 20;
		int offset = 0;
		
		
		String url = "http://10.48.67.140:8183/seoClarity/keywordV2";
		
		String h = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>";
		String x = "<keywords/>";
		String s = "<keywords>";
		String e = "</keywords>";
		
		System.out.println("===file:" + args[0] + ", device:" + args[1]);
		
		
		FileUtils.write(new File(args[0]), h, "UTF-8", true);
		FileUtils.write(new File(args[0]), s, "UTF-8", true);
		
		int idx = total / pageSize + 1;
		for (int i = 0; i <= idx; i++) {
			paramMap.put("limit", String.valueOf(pageSize));
			paramMap.put("offset", String.valueOf(offset));
			
			try {
				System.out.println("=================");
				System.out.println("===paramMap:" + paramMap);
				String str = ClarityDBAPIUtils.simpleGet(url, paramMap);
				str = StringUtils.removeStartIgnoreCase(str, h).trim();
				str = StringUtils.removeStartIgnoreCase(str, x).trim();
				str = StringUtils.removeStartIgnoreCase(str, s).trim();
				str = StringUtils.replace(str, e, "");
				FileUtils.write(new File(args[0]), str, "UTF-8", true);
			} catch (Exception e1) {
				e1.printStackTrace();
			}
			offset = (i + 1) * pageSize;
		}
		
		FileUtils.write(new File(args[0]), e, "UTF-8", true);
		
	}

}
