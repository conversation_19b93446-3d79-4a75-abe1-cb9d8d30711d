package seoclarity.backend.export;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;

import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.bean.FTPServerInfoBean;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

public class ExtractTruliaDataDailyFromClarityDB {

    private GroupTagEntityDAO groupTagEntityDAO;
    private KeywordEntityDAO keywordEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private List<String> competitorList = new ArrayList<String>();
    private ExtractService extractService;

    private static final int QUERY_TRY_COUNT = 10;
    private static final int MOBILE_ENGINE = 999;
    private static final int SSH_TRY_COUNT = 20;
    private static String device = "desktop";
    private int searchEngineId;
    private int languageId;

    public ExtractTruliaDataDailyFromClarityDB() {
        super();

        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        extractService = SpringBeanFactory.getBean("extractService");

        //need add owndomain in first place
        if (domainId == 3431) {
            competitorList.add("www.trulia.com");
            competitorList.add("www.zillow.com");
            competitorList.add("www.realtor.com");
            competitorList.add("www.redfin.com");
            competitorList.add("www.homes.com");
            competitorList.add("www.estately.com");
        } else if (domainId == 476) {
            competitorList.add("www.zillow.com");
            competitorList.add("www.hotpads.com");
            competitorList.add("www.streeteasy.com");
            competitorList.add("www.nakedapartments.com");
            competitorList.add("www.trulia.com");
            competitorList.add("www.realestate.com");
            competitorList.add("www.homes.com");
            competitorList.add("www.realtor.com");
            //Leo - https://www.wrike.com/open.htm?id=154420467
            competitorList.add("www.redfin.com");
        } else {
            if(domainId != 7407){
                System.out.println("didn't find domain's competitor, EXIT!");
                System.exit(1);
            }
        }

    }

    private static String FTP_PATH = "";
    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private static int domainId = 0;
    private static String loc = "";
    private FileWriter writer;
    private final String SPLIT = ",";
    private static Date logDate;
    private static Date processDate = new Date();

    public void process() throws Exception {

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(domainId);
        if (ownDomainEntity == null) {
            System.out.println("=== domain not exist , exit !!");
            try {
                extractService.sendMailReport("ERROR:Export for inactive OID:" + domainId, "Please disable export for inactive OID:" + domainId + "(" + getClass().getName() + ")");
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            return;
        }

        int frequency = 1;//daily
        if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
            processDate = FormatUtils.getLastSundayForWeeklyDomainExtract(processDate);
            System.out.println("====weekly domain processDate:" + processDate);
            frequency = 7;//weekly
        }

        languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        String pDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);
        String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

        logglyVO.setoId(String.valueOf(domainId));
        logglyVO.setName("ExtractTruliaDataDailyFromClarityDB");
        logglyVO.setDevice(device.startsWith("m")?"m":"d");

        logglyVO.setpDate(pDate);
        List<String> groupList = new ArrayList<>();
        groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
        logglyVO.setGroups(groupList);

        System.out.println(" get right domain ready for extract ");

        if(domainId != 7407){
            //extract GroupTag
            System.out.println(" begine extract GroupTag ");
            extractByType("GroupTag", ownDomainEntity);

            //extract CompetitorRanking
            System.out.println(" begine extract CompetitorRanking ");
            extractByType("CompetitorRanking", ownDomainEntity);

            //extract keyword
            System.out.println(" begine extract keyword ");
            extractByType("keyword", ownDomainEntity);
        }

        //extract keywordRank
        System.out.println(" begine extract keywordRank ");
        extractByType("KeywordRanking", ownDomainEntity);


        logglyVO.setStatus(LogglyVO.STATUS_OK);
        logglyVO.setsTime(stTime);
        logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
        logglyVO.setRows(String.valueOf(totalCnt));
        String body = new Gson().toJson(logglyVO);
        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

        System.out.println("end exit");
    }

    private void extractByType(String type, OwnDomainEntity ownDomainEntity) throws Exception {

        List<Object[]> resultObj = new ArrayList<Object[]>();
        String filePath = "";
        File targetFile;

        //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/500505448
        FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
        String host = ftpServerInfo.getPrivateHost();
        String ftpUsername = ftpServerInfo.getServerUserName();
        String ftpPassword = ftpServerInfo.getServerPassword();

        if (StringUtils.equalsIgnoreCase(type, "groupTag")) {
            try {
                List<GroupTagEntity> tagList = groupTagEntityDAO.getAllTagEntityByDomain(ownDomainEntity.getId());
                filePath = loc + "/" + getFileName("Tags", device);
                targetFile = new File(filePath);
                if (targetFile.exists()) {
                    FileUtils.deleteFile(targetFile);
                }

                System.out.println(" tag file path is " + filePath);

                writer = new FileWriter(filePath, true);
                writerTitle(getTagsTitle());

                if (CollectionUtils.isNotEmpty(tagList)) {

                    System.out.println(" tag list size : " + tagList.size());

                    for (GroupTagEntity groupTag : tagList) {
                        resultObj.add(getObjectFromTags(groupTag));
                    }
                }
                totalCnt = resultObj.size();
                writeCsvFile(resultObj);
                resultObj.clear();

                System.out.println(" tag extact end start copyto FTP ");
                copyBySSH(host, ftpUsername, ftpPassword, filePath, FTP_PATH, 0);
                // FileUtils.deleteFile(new File(filePath));
            } catch (Exception e) {
//                emailSenderComponent.sendEmailReport(new Date(), "extract trulia/zillow data daily from clarityDB", "extract " + domainId + " groupTag data failed, please check!", null, "<EMAIL>", null);
                e.printStackTrace();
            }

        } else if (StringUtils.equalsIgnoreCase(type, "CompetitorRanking")) {
            try {
                List<Map<String, Object>> keywordList = keywordEntityDAO.getAllActiveKeywordsAndGroupTag(ownDomainEntity.getId());
                filePath = loc + "/" + getFileName("Competitor_Ranking" , device);
                targetFile = new File(filePath);
                if (targetFile.exists()) {
                    FileUtils.deleteFile(targetFile);
                }

                boolean isMobile = false;
                if(StringUtils.isNotBlank(device) && StringUtils.equalsIgnoreCase(device, "mobile")){
                    isMobile = true;
}

                System.out.println(" keyword file path is " + filePath);

                CommonCsvExportV2 csvExportV2 = new CommonCsvExportV2(filePath);
                csvExportV2.writeCsvLineFile(getKeywordsTitle());

                //ewain
                List<String> keyNameList = new ArrayList<>();
                keywordList.forEach(var->{
                    if (var.get("keyword_name") != null) {
                        keyNameList.add(FormatUtils.decodeKeyword(var.get("keyword_name").toString()));
                    }
                });
                List<List<String>> keywordNameLists = CollectionSplitUtils.splitCollectionBySize(keyNameList, 200);
                System.out.println("====> keywordList size: " + keywordList.size() + ", keyNameList size: " + keyNameList.size() + ", keywordNameLists size: " + keywordNameLists.size());
                List<CLRankingDetailEntity> resultLists = new ArrayList<>();
                for (List<String> keywordNameList : keywordNameLists) {

                    int retryCount = 1;
                    List<CLRankingDetailEntity> detailList = new ArrayList<>();
                    while (true) {
                        try {
                            detailList = clDailyRankingEntityDao.getKeywordDetail(
                                    domainId, searchEngineId, languageId,
                                    0, FormatUtils.formatDate(logDate, "yyyy-MM-dd"),
                                    isMobile, replaceEscape(keywordNameList));
                            break;
                        } catch (Exception e) {

                            if (retryCount >= QUERY_TRY_COUNT) {
                                System.out.println("====error extract oid : " + domainId);
                                System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
                                String[] ccTo = new String[]{"<EMAIL>", "<EMAIL>"};
//                                emailSenderComponent.sendEmailReport(new Date(), "extract trulia/zillow data daily from clarityDB", "extract " + domainId + " CompetitorRanking data failed, please check!", null, "<EMAIL>", ccTo);
                                return;
                            }

                            e.printStackTrace();
                            System.out.println("====extract error oid:" + domainId + ", sleep " + (1000 * 60 * retryCount));
                            try {
                                Thread.sleep(1000 * 60 * retryCount);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            retryCount++;
                        }
                    }

                    resultLists.addAll(detailList);
                }
                System.out.println("====> resultLists size: " + resultLists.size());
                Map<String, List<CLRankingDetailEntity>> cLRankingDetailKwNameMap = CollectionSplitUtils.listToMapGroupByKeyMethod(resultLists, "getKeywordName", CLRankingDetailEntity.class);

                System.out.println("======> cLRankingDetailKwNameMap size: " + cLRankingDetailKwNameMap.keySet().size());

                if (CollectionUtils.isNotEmpty(keywordList)) {

                    for (Map<String, Object> map : keywordList) {

                        Object[] obj = new Object[getKeywordsTitle().length];

                        KeywordEntity keyword = new KeywordEntity();
                        String keywordEncodeName = map.get("keyword_name") == null ? "" : map.get("keyword_name").toString();
                        if (StringUtils.isEmpty(keywordEncodeName)) {
                            continue;
                        }
                        String tagName = map.get("tagName") == null ? "" : map.get("tagName").toString();
                        long keywordId = map.get("id") == null ? 0 : NumberUtils.toLong(map.get("id").toString());

                        keyword.setKeywordName(keywordEncodeName);
                        keyword.setId(keywordId);

                        int i = 0;
                        obj[i++] = tagName;
                        obj [i++] = keywordId;
                        String decodeKwName = URLDecoder.decode(keywordEncodeName, "utf-8");
                        obj[i++] = decodeKwName;

                        //ewain
                        List<CLRankingDetailEntity> resultList = new ArrayList<>();
                        if (cLRankingDetailKwNameMap.containsKey(decodeKwName)) {
                            resultList = cLRankingDetailKwNameMap.get(decodeKwName);
                        }

                        for (String domain : competitorList) {
                            boolean process = false;
                            for (CLRankingDetailEntity entity : resultList) {
                                boolean isSameDomain = FormatUtils.isSameDomain(domain, entity.getDomainName());
                                if (isSameDomain) {
                                    if (!ownDomainEntity.getDomain().equalsIgnoreCase(domain)) {
                                        obj[i++] = domain;
                                    }
                                    process = true;
                                    obj[i++] = entity.getRank();
                                    break;
                                }

                            }
                            if (!process) {
                                if (!ownDomainEntity.getDomain().equalsIgnoreCase(domain)) {
                                    obj[i++] = domain;
                                }
                                obj[i++] = "100+";
                            }
                        }
                        csvExportV2.writeCsvLineFile(obj);
                    }
                }
                totalCnt =keywordList.size();
                System.out.println(" Competitor Ranking extact end  start copyto FTP ");
                copyBySSH(host, ftpUsername, ftpPassword, filePath, FTP_PATH, 0);
                // FileUtils.deleteFile(new File(filePath));
            } catch (Exception e) {
//                emailSenderComponent.sendEmailReport(new Date(), "extract trulia/zillow data daily from clarityDB", "extract " + domainId + " CompetitorRanking data failed, please check!", null, "<EMAIL>", null);
                e.printStackTrace();
            }
        } else if (StringUtils.equalsIgnoreCase(type, "keyword")) {
            try {
                List<Map<String, Object>> keywordList = keywordEntityDAO.getAllActiveKeywordsAndGroupTag(ownDomainEntity.getId());
                List<KeywordEntity> keywordEntityList = keywordEntityDAO.getKeywordByDomainId(ownDomainEntity.getId());
                filePath = loc + "/" + getFileName("Keywords" , device);
                targetFile = new File(filePath);
                if (targetFile.exists()) {
                    FileUtils.deleteFile(targetFile);
                }

                System.out.println(" keyword file path is " + filePath);

                writer = new FileWriter(filePath, true);
                writerTitle(getSimpleKeywordTitle());
                if (CollectionUtils.isNotEmpty(keywordList)) {

                    System.out.println(" keyword list size : " + keywordList.size());

                    for (KeywordEntity keywordEntity : keywordEntityList) {
                        resultObj.add(getObjectFromKeywords(keywordEntity));
                    }
                }
                totalCnt = keywordEntityList.size();
                writeCsvFile(resultObj);
                resultObj.clear();
                System.out.println(" keyword extact end  start copyto FTP ");
                copyBySSH(host, ftpUsername, ftpPassword, filePath, FTP_PATH, 0);
                // FileUtils.deleteFile(new File(filePath));
            } catch (Exception e) {
//                emailSenderComponent.sendEmailReport(new Date(), "extract trulia/zillow data daily from clarityDB", "extract " + domainId + " keyword data failed, please check!", null, "<EMAIL>", null);
                e.printStackTrace();
            }
        } else if (StringUtils.equalsIgnoreCase(type, "KeywordRanking")) {
            try {
                boolean isMobile = false;
                if(StringUtils.isNotBlank(device) && StringUtils.equalsIgnoreCase(device, "mobile")){
                    isMobile = true;
                }

                boolean isBroadMatch = ownDomainEntity.isBroadMatch();
                String domainName = ownDomainEntity.getDomain();
                String domainReverse = StringUtils.reverseDelimited(domainName, '.');
                String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

                int retryCount = 1;
                List<CLRankingDetailEntity> resultList = new ArrayList<>();
                while (true) {
                    try {
                        resultList = clDailyRankingEntityDao.getKeywordInfo(domainId, searchEngineId, languageId,
                                0, FormatUtils.formatDate(logDate, "yyyy-MM-dd"), isMobile,
                                rootDomainReverse, domainReverse, isBroadMatch);
                        break;
                    } catch (Exception e) {

                        if (retryCount >= QUERY_TRY_COUNT) {
                            System.out.println("====error extract oid : " + domainId);
                            System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
                            String[] ccTo = new String[]{"<EMAIL>", "<EMAIL>"};
//                            emailSenderComponent.sendEmailReport(new Date(), "extract trulia/zillow data daily from clarityDB", "extract " + domainId + " CompetitorRanking data failed, please check!", null, "<EMAIL>", ccTo);
                            return;
                        }

                        e.printStackTrace();
                        System.out.println("====extract error oid:" + domainId + ", sleep " + (1000 * 60 * retryCount));
                        try {
                            Thread.sleep(1000 * 60 * retryCount);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        retryCount++;
                    }
                }

                filePath = loc + "/" + getFileName("KeywordRanking" , device);
                targetFile = new File(filePath);
                if (targetFile.exists()) {
                    FileUtils.deleteFile(targetFile);
                }

                System.out.println(" KeywordRanking file path is " + filePath);
                CommonCsvExportV2 csvExportV2 = new CommonCsvExportV2(filePath);
                csvExportV2.writeCsvLineFile(getKeywordRankingTitle());

                Integer mapRank;
                Integer newsRank;
                Integer imageRank;
                Integer videoRank;
                Integer authorRank;
                Integer starRank;

                totalCnt = resultList.size();

                System.out.println("========== totalCnt: " + totalCnt);
                if (CollectionUtils.isNotEmpty(resultList)) {
                    for (CLRankingDetailEntity entity : resultList) {

                        mapRank = 101;
                        imageRank = 101;
                        newsRank = 101;
                        videoRank = 101;
                        authorRank = 101;
                        starRank = 101;

                        KeywordEntity keywordEntity = keywordEntityDAO.getByKeywordName(FormatUtils.encodeKeyword(entity.getKeywordName()), domainId);

                        if (keywordEntity == null) {
                            System.out.println("!!!!! keyword not found !!!!" + FormatUtils.encodeKeyword(entity.getKeywordName()));
                            continue;
                        }

                        try {
                            if (StringUtils.isNotBlank(entity.getTypeArray())
                                    && StringUtils.isNotBlank(entity.getRankArray())
                                    && entity.getTypeArray().length() == entity.getRankArray().length()) {

                                String[] typeArray = entity.getTypeArray().split(",");
                                String[] rankArray = entity.getRankArray().split(",");
                                for(int i = 0; i < typeArray.length; i ++) {

                                    int rankType = Integer.parseInt(typeArray[i]);
                                    int rankInt = Integer.parseInt(rankArray[i]);

//                                    System.out.println(" ========== rankType : " + rankType + ", rankInt: " + rankInt);

                                    if (rankType == KeywordRankEntityVO.TYPE_ADDRESS) {
                                        mapRank = rankInt;
                                    } else if (rankType == KeywordRankEntityVO.TYPE_IMGAGE) {
                                        imageRank = rankInt;
                                    } else if (rankType == KeywordRankEntityVO.TYPE_NEWS) {
                                        newsRank = rankInt;
                                    } else if (rankType == KeywordRankEntityVO.TYPE_VIDEO) {
                                        videoRank = rankInt;
                                    } else if (rankType == -1) {
                                        starRank = rankInt;
                                    }
                                }
                            } else {
    //    						System.out.println("!!! type array is not correct!!!!" + new Gson().toJson(entity));
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        resultObj.add(new Object[]{
                                FormatUtils.formatDateToYyyyMmDd(logDate) + "_" + keywordEntity.getId(),
                                FormatUtils.formatDateToYyyyMmDd(logDate),
                                entity.getKeywordName(),
                                entity.getRank(),
                                entity.getWebRank(),
                                entity.getUrl() == null ? "" : entity.getUrl(),
                                entity.getFps(),
                                mapRank,
                                newsRank,
                                imageRank,
                                videoRank,
                                authorRank,
                                starRank,
                                entity.getAvgSearchVolume()
                        });
                    }
                }
                csvExportV2.writeCsvFile(resultObj);
                resultObj.clear();
                System.out.println(" KeywordRanking extact end  start copyto FTP ");
                copyBySSH(host, ftpUsername, ftpPassword, filePath, FTP_PATH, 0);
                // FileUtils.deleteFile(new File(filePath));
            } catch (Exception e) {
//                emailSenderComponent.sendEmailReport(new Date(), "extract trulia/zillow data daily from clarityDB", "extract " + domainId + " KeywordRanking data failed, please check!", null, "<EMAIL>", null);
                e.printStackTrace();
            }
        }
    }

    private List<String> replaceEscape(List<String> keywordNameList) {
        List<String> result = new ArrayList<>();
        for (String keywordName : keywordNameList) {
            if (StringUtils.contains(keywordName, "'")) {
                String replaceKw = keywordName.replace("\\", "\\\\").replace("'", "\\'");
                result.add(replaceKw);
                System.out.println("kw replace=>kw: " + keywordName + ", rp kw: " + replaceKw);
            }else {
                result.add(keywordName);
            }
        }
        return result;
    }

    /*private List<String> replaceEscape(List<String> keywordNameList) {
        List<String> result = new ArrayList<>();
        for (String keywordName : keywordNameList) {
            if (StringUtils.contains(keywordName, "'")) {
                //result.add(keywordName.replace("'", "\\'"));
                result.add(replaceSingleQuote(keywordName));
            }else {
                result.add(keywordName);
            }
        }
        return result;
    }

    private static String replaceSingleQuote(String decodeKeywordName) {
        if (StringUtils.contains(decodeKeywordName, "'")) {
            try {
                String tmpKW = decodeKeywordName;
                StringBuffer sb = new StringBuffer();
                int pos = 0;
                int startPos = 0;
                while (true) {
                    if (pos > 0) {
                        pos = tmpKW.indexOf("'", pos + 1);
                    } else {
                        pos = tmpKW.indexOf("'");
                    }
                    if (pos >= 0) {
                        if (pos == 0) {
                            sb.append("\\'");
                            pos = pos + 1;
                            startPos = pos;
                        } else if ("\\".equals(tmpKW.substring(pos - 1, pos))) {
                            sb.append(tmpKW, startPos, pos);
                            startPos = pos;
                        } else {
                            sb.append(tmpKW, startPos, pos).append("\\'");
                            startPos = pos + 1;
                        }
                    }
                    if (pos < 0 || pos >= tmpKW.length() - 1) {
                        sb.append(tmpKW, startPos, tmpKW.length());
                        break;
                    }
                }
                System.out.println("replaceSingleQuote:" + decodeKeywordName + "->" + sb.toString());
                return sb.toString();
            } catch (Exception exp) {
                exp.printStackTrace();
                return StringUtils.replace(decodeKeywordName, "'", "\\'");
            }
        }
        return decodeKeywordName;
    }*/

    private void writeCsvFile(List<Object[]> contentList) {
        if (contentList != null) {
            for (Object[] contentArray : contentList) {
                StringBuffer contentStrBuff = new StringBuffer();
                for (Object obj : contentArray) {
                    if (obj != null) {
                        //Leo - https://www.wrike.com/open.htm?id=96882635
                        contentStrBuff.append(csvHandlerStr(String.valueOf(obj))).append(SPLIT);
                    } else {
                        contentStrBuff.append("").append(SPLIT);
                    }
                }
                writeCsvLine(contentStrBuff.toString().substring(0, contentStrBuff.toString().length() - 1));
            }
        }
    }

    //Leo - https://www.wrike.com/open.htm?id=96882635
    private String csvHandlerStr(String str) {
        String tempDescription = str;
        if (StringUtils.containsIgnoreCase(str, ",")) {
            tempDescription = StringUtils.replace(str, "\"", "\"\"");
        }
        tempDescription = "\"" + tempDescription + "\"";
        return tempDescription;
    }

    private Object[] getObjectFromKeywords(KeywordEntity keywordEntity) throws UnsupportedEncodingException {
        Object[] obj = new Object[getSimpleKeywordTitle().size()];

        int i = 0;

        obj[i++] = keywordEntity.getId();
        obj[i++] = URLDecoder.decode(keywordEntity.getKeywordName(), "utf-8");

        return obj;
    }

    private Object[] getObjectFromTags(GroupTagEntity groupTag) {
        Object[] obj = new Object[getTagsTitle().size()];

        int i = 0;

        obj[i++] = groupTag.getId();
        obj[i++] = groupTag.getTagName();

        return obj;
    }

    private void writeCsvLine(String line) {
        if (null == line) {
            return;
        }
        try {
            writer.write(line + "\n");
            writer.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void writerTitle(List<String> titleList) {
        StringBuffer titleStrBuff = new StringBuffer();
        if (titleList != null) {
            for (String title : titleList) {
                titleStrBuff.append(title).append(SPLIT);
            }
            writeCsvLine(titleStrBuff.toString().substring(0, titleStrBuff.toString().length() - 1));
        }
    }

    private Object[] getKeywordRankingTitle() {
        return new Object[]{
                "date_key",
                "trans_date",
                "keyword",
                "highest_true_rank",
                "highestWebRank",
                "highestRankUrl",
                "fps",
                "highestLocalRank",
                "highestNewsRank",
                "highestImageRank",
                "highestVideoRank",
                "highestAuthorRank",
                "highestStarRank",
                "monthlySearchVolume"
        };
    }

    private String getFileName(String type, String device) {
        int dateName = FormatUtils.formatDateToYyyyMmDd(processDate);
        if (domainId == 3431) {
            return "Trulia" + type + dateName + ".csv";
        } else if (domainId == 476) {
            return "Zillow" + type + dateName + ".csv";
        } else if(domainId == 7407){
            return domainId + "_" + "www.zillow.com_Test_Profile_" + dateName + "_" + type + "_" + device + ".txt";
        }
        return "Trulia" + type + dateName + ".csv";
    }

    private List<String> getTagsTitle() {

        List<String> titles = new ArrayList<String>();
        titles.add("tag_id");
        titles.add("tag_name");

        return titles;
    }

    private List<String> getSimpleKeywordTitle() {

        List<String> titles = new ArrayList<String>();
        titles.add("keyword_id");
        titles.add("keyword_name");

        return titles;
    }

    private Object[] getKeywordsTitle() {

        List<Object> objs = new ArrayList<>();
        objs.add("Keyword Tag");
        objs.add("Keyword Id");
        objs.add("Keyword");
        objs.add("Rank");
        //remove self domain
        for (int i = 0; i < competitorList.size() - 1; i++) {
            objs.add("Competitor " + (i + 1));
            objs.add("Competitor " + (i + 1) + " Rank");
        }

        return objs.toArray();

    }

    /**
     * @param host
     * @param pw
     * @param from
     * @param saveTo
     * @param type
     * @throws Exception
     */
    private void copyBySSH(String host, String ftpUsername, String pw, String from, String saveTo, int type) throws Exception {

        if (type == 0) {
            System.out.println("copy from from local to remote , file name:" + from);
        } else {
            System.out.println("copy from from remote to local , file name:" + from);
        }

        boolean copySucceed = false;

        for (int i = 0; i < SSH_TRY_COUNT; i++) {
            try {

                Connection connection = new Connection(host);
                connection.connect();
                if (connection.authenticateWithPassword(ftpUsername, pw)) {
                    System.out.println("login");

                    SCPClient scpClient = connection.createSCPClient();
                    if (type == 0) {
                        scpClient.put(from, saveTo);
                    } else {
                        scpClient.get(from, saveTo);
                    }
                    connection.close();
                    copySucceed = true;
                    break;
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

            System.out.println("Failed to login to target host...");

            try {
                Thread.sleep(300000);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (copySucceed == true) {
            System.out.println("Copy " + from + " to " + host + " successfully.");
        } else {
            throw new RuntimeException("Couldnot login " + host + " for " + SSH_TRY_COUNT
                    + " tries. Please copy file yourself.");
        }

        System.out.println("copy ssh success ");

    }

    public static void main(String[] args) throws ParseException {

        if (args != null && args.length > 0) {
            domainId = NumberUtils.toInt(args[0]);
        } else {
            domainId = 3431;
        }

        if (args != null && args.length > 1) {
            loc = args[1];
        } else {
            loc = "/home/<USER>/source/ExtractTruliaDataDaily/file-" + domainId;
        }

        if (args != null && args.length > 2) {
            FTP_PATH = args[2];
        } else {
            FTP_PATH = "/home/<USER>/" + domainId;
        }

        //https://www.wrike.com/open.htm?id=363402497
        if (args != null && args.length > 4) {
            device = args[4];
        }

        if (args != null && args.length > 3 && StringUtils.isNotBlank(args[3])) {
            String[] dateStrs = StringUtils.split(args[3], ",");
            String[] pattern = new String[]{"yyyy-MM-dd", "yyyy/MM/dd", "yyyyMMdd"};
            if (dateStrs.length == 2) {
                Date startDate = DateUtils.parseDate(dateStrs[0], pattern);
                Date endDate = DateUtils.parseDate(dateStrs[1], pattern);
                while (startDate.compareTo(endDate) <= 0) {
                    processDate = startDate;
                    logDate = DateUtils.addDays(processDate, -2);

                    System.out.println("log date is :" + logDate + ", process date is : " + processDate);
                    ExtractTruliaDataDailyFromClarityDB extractTruliaDataDaily = new ExtractTruliaDataDailyFromClarityDB();
                    try {
                        extractTruliaDataDaily.process();
                    } catch (Exception e) {
                        e.printStackTrace();

                        logglyVO.setStatus(LogglyVO.STATUS_NG);
                        String body = new Gson().toJson(logglyVO);
                        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                    }
                    startDate = DateUtils.addDays(startDate, 1);
                }
            } else {
                System.out.println("params error for date");
            }
        } else {
            processDate = new Date();
            logDate = DateUtils.addDays(processDate, -2);

            System.out.println("log date is :" + logDate + ", process date is : " + processDate);
            ExtractTruliaDataDailyFromClarityDB extractTruliaDataDaily = new ExtractTruliaDataDailyFromClarityDB();
            try {
                extractTruliaDataDaily.process();
            } catch (Exception e) {
                e.printStackTrace();

                logglyVO.setStatus(LogglyVO.STATUS_NG);
                String body = new Gson().toJson(logglyVO);
                LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
            }
        }

    }

}
