package seoclarity.backend.export;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.service.KeywordNameRelService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * 
 * <AUTHOR>
 * @date 2019-07-10
 * @path seoclarity.backend.export.ExtractRGKeywordTop10UrlForDomainDailyRanking
 *	use daily ranking url to query monthly estd traffic
 */
public class ExtractRGKeywordTop10UrlForDomainDailyRanking {
	private static int currentMonth = 0;
	private static String querryDate = "";
	private static final int TOPXURL = 10;
	private static String outFile;
	private int engine = 0;
	private int language = 0;
	private static int oid = 0;
	private static boolean isMobile = false;
	
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private KeywordEntityDAO keywordEntityDAO;
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private KeywordNameRelService keywordNameRelService;
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private MonthlyRankingDao monthlyRankingDao;
	
	public ExtractRGKeywordTop10UrlForDomainDailyRanking() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		keywordNameRelService = SpringBeanFactory.getBean("keywordNameRelService");
		monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
	}
	
	public static void main(String[] args) {
		oid = Integer.valueOf(args[0]);
		outFile = args[1];
		
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MONTH, -1);
		currentMonth = Integer.valueOf(new SimpleDateFormat("yyyyMM").format(cal.getTime()));
		cal.add(Calendar.MONTH, 1);
		cal.add(Calendar.DAY_OF_MONTH, -1);
		querryDate = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
		
		ExtractRGKeywordTop10UrlForDomainDailyRanking ins = new ExtractRGKeywordTop10UrlForDomainDailyRanking();
		try {
			FileUtils.write(new File(outFile), "Keyword,True Rank,URL,Total Keyword,Estd Traffic,Traffic Potentional" + "\n", "UTF-8", true);
			ins.getKeywords(oid);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private List<SeoClarityKeywordEntity> getRankCheckList(List<KeywordEntity> kwList) {
		List<String> kwSet = new ArrayList<String>();
		for (KeywordEntity kw : kwList) {
			if (kw == null || kw.getId() == null) {
				System.out.println("Skip empty kw, kw:" + (kw != null ? kw.getKeywordName() : "") + ", kid:" + kw.getId());
				continue;
			}
			kwSet.add(keywordNameRelService.getKeywordName(kw));
		}
		List<SeoClarityKeywordEntity> list = seoClarityKeywordEntityDAO.getKeywordEntityByNames(kwSet);
		if (list.size() != kwList.size()) {
			Set<String> existsList = new HashSet<String>();
			for (SeoClarityKeywordEntity kw : list) {
				existsList.add(kw.getKeywordText().toLowerCase());
			}
			for (String str : kwSet) {
				if (!existsList.contains(str.toLowerCase())) {
					System.out.println("=Not found kw:" + str + " OID:" + oid);
				}
			}
			
		}
		return list;
	}
	
	private void getKeywords(int oid) throws Exception{
		OwnDomainEntity domain = ownDomainEntityDAO.getById(oid);
		engine = ScKeywordRankManager.getSearchEngineId(domain);
		language = ScKeywordRankManager.getSearchLanguageId(domain);
		
		System.out.println("keyword oid :" + oid + ", out file:" + outFile + ", engine:" + engine + ", language:" + language 
				+ ", currentMonth:" + currentMonth + ", querryDate:" + querryDate + ", isMobile:" + isMobile);

		int pageSize = 200;
		long maxId = 0l;
		
		List<SeoClarityKeywordEntity> allKwList = new ArrayList<SeoClarityKeywordEntity>();
		int total = 0;
		while(true) {
			List<KeywordEntity> kwList = keywordEntityDAO.getKeywordByDomainIdByPage(oid, maxId, pageSize);
			if (kwList != null && kwList.size() > 0) {
				total += kwList.size();
				maxId = kwList.get(kwList.size() - 1).getId();
				List<SeoClarityKeywordEntity> list = getRankCheckList(kwList);
				allKwList.addAll(list);
				System.out.println("OID:" + oid + ", kwList:" + kwList.size() + ", rank checkList:" + list.size());
			} else {
				break;
			}
		}
		System.out.println("==============================================");
		System.out.println("OID:" + oid + ", total:" + total + ", all rank check List:" + allKwList.size());
		
		// check kw exsts
		int processCnt = 0;
		Map<String, String> kwMap = new HashMap<String, String>();
		for (SeoClarityKeywordEntity kw : allKwList) {
			processCnt++;
			
			kwMap.put(String.valueOf(kw.getId()), kw.getKeywordText());
			if (kwMap.size() >= 10) {
				System.out.println("=Current process:" + kwMap.size() + ", total:" + processCnt);
				processKw(kwMap);
				kwMap.clear();
			}
		}
		
		if (kwMap.size() > 0) {
			System.out.println("=Current process:" + kwMap.size() + ", total:" + processCnt);
			processKw(kwMap);
			kwMap.clear();
		}
	}
	
	private void processKw(Map<String, String> kwMap) {
		Map<String, List<String>> kwUrlMap = getTop10UrlInKP(engine, language, kwMap);
		for (String kid : kwUrlMap.keySet()) {
			List<String> urls = kwUrlMap.get(kid);
			if (urls.size() == 0) {
				System.out.println("=Skip not ranking keyword, rankcheckKid:" + kid + ", kw:" + kwMap.get(kid));
			} else {
				getRankingDataForUrl(urls, engine, language, kwMap.get(kid));
			}
		}
	}
	
	private Map<String, List<String>> getTop10UrlInKP(int engine, int language, Map<String, String> kwMap) {
		StringBuffer sql = new StringBuffer();
		
//		String table = (isMobile ? "m_" : "d_") + "ranking_detail" + (engine == 1 ? "_us" : "_intl");
		String table = "merge(seo_daily_ranking, '^" + (isMobile ? "m_" : "d_") + "ranking_detail" + (engine == 1 ? "_us" : "_intl") + "')";
		
		sql.append(" SELECT ");
		sql.append("     keyword_rankcheck_id, ");
		sql.append("     true_rank, ");
		sql.append("     url ");
		sql.append(" FROM " + table + " ");
		sql.append(" WHERE (keyword_rankcheck_id in (" + StringUtils.join(kwMap.keySet(), ',') + ") ) ");
		sql.append(" AND (engine_id = " + engine + ") AND (language_id = " + language + ")  and ranking_date = '" + querryDate + "' "
				+ "and location_id = 0  and own_domain_id = " + oid + " and true_rank <= " + TOPXURL + " "
				+ "order by keyword_rankcheck_id, true_rank ");
		
		List<Map<String, Object>> resultList = clDailyRankingEntityDao.executeSql(sql.toString(), null);
		
		
		Map<String, List<String>> kwUrlMap = new HashMap<String, List<String>>();
		if (kwUrlMap != null) {
			try {
				for (Map<String, Object> map : resultList) {
					String kid = map.get("keyword_rankcheck_id").toString();
					String url = map.get("url").toString();
					if (kwUrlMap.containsKey(kid)) {
						kwUrlMap.get(kid).add(url);
					} else {
						List<String> urls = new ArrayList<String>();
						urls.add(url);
						kwUrlMap.put(kid, urls);
					}
				}
				for (String key : kwMap.keySet()) {
					if (!kwUrlMap.containsKey(key)) {
						System.out.println("=Skip not ranking url Kid:" + key + ", kw:" + kwMap.get(key) + ", engine:" + engine + ", language:" + language);
					}
				}
			} catch (Exception e) {
				System.out.println("Parse json failed. resultList:" + resultList);
				System.out.println("Sql:" + sql.toString());
				e.printStackTrace();
			}
		}
		
		return kwUrlMap;
	}
	
	private void getRankingDataForUrl(List<String> urlList, int engine, int language, String kw) {
		String table = (isMobile ? "m_" : "d_") + "ranking_detail_" + currentMonth + (engine == 1 ? "_us" : "_intl");
		
		StringBuffer sql = new StringBuffer();
		for (int i = 0; i < urlList.size(); i++) {
			String url = urlList.get(i);
			
			if (i > 0) {
				sql.append(" UNION ALL ");
			}
			
			sql.append(" SELECT ");
			sql.append("      " + i + " as idx, ");
			sql.append("     count() AS cnt, ");
			sql.append("     sum(round(( ");
			sql.append(" 		caseWithExpression(true_rank, 1, 0.193, 2, 0.114, 3, 0.077, 4, 0.052, 5, 0.041, 6, 0.033, 7, 0.026, 8, 0.021, 9, 0.02, 10, 0.022, 0) ");
			sql.append(" 		* dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', (tuple(concat(toString(keyword_rankcheck_id), '_', toString(" + language + ")))))) + 0.01)) ");
			sql.append(" 	AS sum_est_traffic_tr, ");
			sql.append("     sum(dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', (tuple(concat(toString(keyword_rankcheck_id), '_', toString(" + language + ")))))) AS total_searchvol, ");
			sql.append("     round((total_searchvol * 0.193) + 0.001) AS traffic_potential, ");
			sql.append("     round((sum_est_traffic_tr / (total_searchvol * 0.193)) * 100, 2) AS search_visibility ");
			sql.append(" FROM " + table + " ");
			sql.append(" WHERE ");
			
			String qDomain = ClarityDBUtils.getDomain(url);
			String rootDomain = ClarityDBUtils.getRootDomain(qDomain);
			if(qDomain.equalsIgnoreCase(rootDomain)){
				String reverseDomain = StringUtils.reverseDelimited(qDomain, '.');
				sql.append(" ( (root_domain_reverse = '"+reverseDomain+"') AND hrrd=1 AND (urlhash = URLHash('"+url+"')) ) ");
			}else{
				String reverseDomain = StringUtils.reverseDelimited(qDomain, '.');
				String reverserootDomain = StringUtils.reverseDelimited(rootDomain, '.');
				sql.append(" ( (root_domain_reverse = '"+reverserootDomain+"') AND (domain_reverse = '"+reverseDomain+"') AND (urlhash = URLHash('"+url+"')) ) ");
			}
			
			sql.append(" AND (language_id = " + language + ") AND (engine_id = " + engine + ") ");
			sql.append(" AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(" + engine + "), toUInt64(" + language + "), toUInt64(keyword_rankcheck_id))) = 0) ");
		}
		
		List<Map<String, Object>> resultList = monthlyRankingDao.executeSql(sql.toString(), null);
		Map<String, String[]> valsMap = new HashMap<String, String[]>();
		for (Map<String, Object> map : resultList) {
			int idx = Integer.valueOf(map.get("idx").toString());
			String cnt = map.get("cnt").toString();
			String sum_est_traffic_tr = map.get("sum_est_traffic_tr").toString();
			String total_searchvol = map.get("total_searchvol").toString();
			String traffic_potential = map.get("traffic_potential").toString();
			String search_visibility = map.get("search_visibility").toString();
			valsMap.put(String.valueOf(idx), new String[] {
				String.valueOf(idx), cnt, 	sum_est_traffic_tr, total_searchvol, traffic_potential, search_visibility
			});
		}
		List<String> rowList = new ArrayList<String>();
		for (int i = 0; i < urlList.size(); i++) {
			String url = urlList.get(i);
			
			List<String> line = new ArrayList<String>();
			line.add(("\"" + (StringUtils.contains(kw, '"') ? StringUtils.replace(kw, "\"", "\\\"") : kw)) + "\"");
			line.add(String.valueOf(i + 1));
			line.add("\"" + url + "\"");
			
			String[] cols = valsMap.get(String.valueOf(i));
			
			if (cols != null && cols.length == 6) {
				String cnt = cols[1];
				String sum_est_traffic_tr = cols[2];
				String traffic_potential = cols[4];
				String search_visibility = cols[5];
				
				line.add(cnt);
				line.add(sum_est_traffic_tr);
				line.add(traffic_potential);
			} else {
				line.add("\"-\"");
				line.add("\"-\"");
				line.add("\"-\"");
			}
			rowList.add(StringUtils.join(line, ','));
		}
		
		try {
			FileUtils.writeLines(new File(outFile), "UTF-8", rowList, true);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

}
