package seoclarity.backend.export.marven;

import seoclarity.backend.asyncapi.RankUtils;

import java.text.SimpleDateFormat;
import java.util.List;

public class KeywordClusterV3 {
    public static void main(String args[]) {
        if (args.length < 1) {
            System.out.println("param is error");
            return;
        }
        int being =0;
        int end =0;
        String inputfilepath = args[0];
        if(args.length ==3) {


        }
        try{
            List<String> list =  RankUtils.loadFileListV1(inputfilepath);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");

            //System.out.println("=====current time:"+formatter.format(new Date())+"=====");
            //checkExist(fulloutfilename+"/"+formatter.format(new Date())+"/",list);
        }catch (Exception ex){
            ex.printStackTrace();
            System.out.println("err");
        }
    }
}
