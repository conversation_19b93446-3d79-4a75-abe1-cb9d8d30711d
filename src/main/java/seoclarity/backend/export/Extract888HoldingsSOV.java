package seoclarity.backend.export;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.RankIndexParamVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * s20 check aws s3 ls s3://com.prod.euw1.cross.real.seo-clarity.bucket/ --profile 888HoldingsSOV
 */
@CommonsLog
public class Extract888HoldingsSOV {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static final String KEY_SPLIT = "#_#";
    private static String LOC = "/home/<USER>/2487";
    private static final int QUERY_TRY_COUNT = 10;
    private static final String TAG_NAME = "priority";

    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private static LocalDate processMonth;

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private GroupTagEntityDAO groupTagEntityDAO;
    private RankIndexParamEntityDAO rankIndexParamEntityDAO;
    private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
    private GscBaseDao gscBaseDao;
    private GwmDomainRelDAO gwmDomainRelDAO;
    private ExtractService extractService;

    public Extract888HoldingsSOV(){
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        rankIndexParamEntityDAO = SpringBeanFactory.getBean("rankIndexParamEntityDAO");
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
        gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
        gwmDomainRelDAO = SpringBeanFactory.getBean("gwmDomainRelDAO");
        extractService = SpringBeanFactory.getBean("extractService");
    }

    public static final Map<Integer, String> DOMAIN_INFO_MAP = new HashMap();

    static {
        DOMAIN_INFO_MAP.put(2119,"888casino!_!Casino!_!www.888casino.com");
        DOMAIN_INFO_MAP.put(2137,"888poker!_!Poker!_!www.888poker.com");
        DOMAIN_INFO_MAP.put(2481,"888poker!_!Poker!_!br.888poker.com");
//        DOMAIN_INFO_MAP.put(2486,"888poker!_!Poker!_!fr.888poker.com");
        DOMAIN_INFO_MAP.put(2487,"888poker.ca-on!_!Poker!_!www.888poker.com");
        DOMAIN_INFO_MAP.put(2494,"slots.germany!_!Casino!_!de.888casino.com");
        DOMAIN_INFO_MAP.put(2495,"888poker.germany!_!Poker!_!www.888poker.de");
        DOMAIN_INFO_MAP.put(2504,"888casino.es!_!Casino!_!www.888casino.es");
        DOMAIN_INFO_MAP.put(2505,"888poker.es!_!Poker!_!www.888poker.es");
        DOMAIN_INFO_MAP.put(2506,"888sport.es!_!Sport!_!www.888sport.es");
        DOMAIN_INFO_MAP.put(2528,"888casino.se!_!Casino!_!www.888casino.se");
        DOMAIN_INFO_MAP.put(2529,"888poker.se!_!Poker!_!www.888poker.se");
        DOMAIN_INFO_MAP.put(2530,"888sport.se!_!Sport!_!www.888sport.se");
//        DOMAIN_INFO_MAP.put(2534,"777!_!Casino!_!www.777.com");
        DOMAIN_INFO_MAP.put(2541,"888sport!_!Sport!_!www.888sport.com");
        DOMAIN_INFO_MAP.put(2554,"888casino!_!Casino!_!www.888casino.com");
        DOMAIN_INFO_MAP.put(3763,"888poker.dk!_!Poker!_!www.888poker.dk");
        DOMAIN_INFO_MAP.put(3764,"888sport.dk!_!Sport!_!www.888sport.dk");
        DOMAIN_INFO_MAP.put(3765,"888casino.dk!_!Casino!_!www.888casino.dk");
        DOMAIN_INFO_MAP.put(4327,"888sport.it!_!Sport!_!www.888sport.it");
        DOMAIN_INFO_MAP.put(5186,"888sport.ro!_!Sport!_!www.888sport.ro");
        DOMAIN_INFO_MAP.put(5187,"888casino.ro!_!Casino!_!www.888casino.ro");
        DOMAIN_INFO_MAP.put(5188,"888poker.ro!_!Poker!_!www.888poker.ro");
        DOMAIN_INFO_MAP.put(5313,"888sport.germany!_!Sport!_!www.888sport.de");
        DOMAIN_INFO_MAP.put(6442,"888poker.it!_!Poker!_!www.888poker.it");
        DOMAIN_INFO_MAP.put(6446,"888casino.pt!_!Casino!_!casino.888.pt");
        DOMAIN_INFO_MAP.put(7004,"888poker!_!Poker!_!www.888poker.com");
        DOMAIN_INFO_MAP.put(2510,"888casino.it!_!Casino!_!www.888casino.it");
    }

    private void processExtract(boolean isMobile) {

        LocalDate startDay = processMonth.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endDay = processMonth.with(TemporalAdjusters.lastDayOfMonth());

        String fileName = getFileName(isMobile);

        File localFolder = new File(LOC);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }

        String localFilePath = LOC + File.separator;
        String remoteFilePath = "/" + processMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")) + "/" + fileName;
//        String remoteFilePath = localFilePath;
        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        try {
            addHeadersForExactFile(localFile);

            while (startDay.compareTo(endDay) <= 0) {

                ZoneId zone = ZoneId.systemDefault();
                Instant instant = startDay.atStartOfDay().atZone(zone).toInstant();
                Date processDate = Date.from(instant);
                log.info("==== startDay:" + startDay + ",processDate:" + processDate);

                for(Integer domainId: DOMAIN_INFO_MAP.keySet()){

                    String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
                    String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

                    logglyVO.setoId(String.valueOf(domainId));
                    logglyVO.setName("Extract888HoldingsSOV");
                    logglyVO.setDevice(isMobile?"m":"d");

                    logglyVO.setpDate(pDate);
                    List<String> groupList = new ArrayList<>();
                    groupList.add(LogglyVO.GROUP_CUSTOMIZED_EXTRACT);
                    logglyVO.setGroups(groupList);

                    OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
                    if (ownDomainEntity == null) {
                        log.error(" domain not exist : " + domainId);
                        try {
                            extractService.sendMailReport("ERROR:Export for inactive OID:" + domainId, "Please disable export for inactive OID:" + domainId + "(" + getClass().getName() + ")");
                        } catch (Exception exp) {
                            exp.printStackTrace();
                        }
                        continue;
                    }
                    log.info("====== processing domain:" + domainId);
                    processFile(localFile, ownDomainEntity, isMobile, processDate);

                    logglyVO.setStatus(LogglyVO.STATUS_OK);
                    logglyVO.setsTime(stTime);
                    logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
                    logglyVO.setRows(String.valueOf(totalCnt));
                    String body = new Gson().toJson(logglyVO);
                    LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                }

                startDay = startDay.plusDays(1);
            }


            int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3;
//            int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
            copyFileToRemoteServer(serverType, 2487, localFileName, remoteFilePath);


        } catch (Exception e) {
            e.printStackTrace();

            String subject = getEmailSubject( false, processMonth);
            String message = subject;

            return;
        }


    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, boolean isMobile, Date processDate) throws Exception {

        int ownDomainId = ownDomainEntity.getId();

        List<String> dataList = new ArrayList<>();

        int retryCount = 1;
        while (true) {
            try {
                dataList = getDataFromDB(ownDomainEntity, isMobile, processDate);

                totalCnt = dataList.size();
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + ownDomainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + ownDomainId);
                    String subject = getEmailSubject(false, processMonth);
                    String message = subject;
//                    sendMailReport(subject, message);
                    logglyVO.setStatus(LogglyVO.STATUS_NG);
                    String body = new Gson().toJson(logglyVO);
                    LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                    return;
                }
                e.printStackTrace();
                System.out.println("====extract error oid:" + ownDomainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        FileUtils.writeLines(localFile, dataList, true);


    }

    private String getFileName(boolean isMobile) {

        String device = isMobile ? "mobile" : "desktop";
        String fileName = "ShareofVoice_" + processMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")) + "_" + device + ".csv";
        return fileName;
    }

    private void addHeadersForExactFile(File outFile) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Domain").append(SPLIT);
        header.append("Subbrands").append(SPLIT);
        header.append("Product").append(SPLIT);
        header.append("Country").append(SPLIT);
        header.append("Date").append(SPLIT);
        header.append("Keyword tag").append(SPLIT);
        header.append("Share of Voice").append(SPLIT);
//        header.append("All Clicks").append(SPLIT);
        header.append("Branded Clicks").append(SPLIT);
        header.append("Nonbranded Clicks");

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity, boolean isMobile, Date processDate) throws Exception{

        List<String> extractLines = new ArrayList<String>();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        String domainReverse = null;
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);;

        List<RankIndexParamEntity> paramList = rankIndexParamEntityDAO.getRankIndexParams(ownDomainId, 0);
        RankIndexParamVO rankIndexParamVO = new RankIndexParamVO(paramList);
        List<String> ctrList = rankIndexParamVO.getParamList();

        GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(ownDomainId, TAG_NAME, GroupTagEntity.TAG_TYPE_KEYWORD);
        if(groupTagEntity == null){
            log.error("=====domain:" + ownDomainId + " not find tag!");
            return extractLines;
        }

        List<CLRankingDetailEntity> dataList = new ArrayList<>();
        CLRankingDetailEntity data = clDailyRankingEntityDao.export888ShareOfVoice(ownDomainId, engineId, languageId, rankingDate, isMobile, groupTagEntity.getId(), rootDomainReverse, ctrList);

        String desktop = isMobile? "mobile" : "desktop";
        List<Integer> profileIdList = gwmDomainRelDAO.getProfilesByDomainId(ownDomainId);
        List<GscEntity> gscEntityList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(profileIdList)){
            gscEntityList = gscBaseDao.getExtract888ClicksData(GscEntity.TYPE_ALL, ownDomainId, rankingDate, profileIdList);
            log.info("====gscEntityList size:" + gscEntityList.size());
        }else {
            log.info("----profileIdList empty!");
        }

        String country = "";
        EngineCountryLanguageMappingEntity mapping = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageId(engineId, languageId);
        if(mapping == null){
            log.error("=====not find mapping engineId:" + engineId + ",languageId:" + languageId);
        }else {
            country = mapping.getCountryDisplayName();
        }

        if(data != null){
            extractLines.add(appendData(data, ownDomainId, country, gscEntityList));
        }


        return extractLines;
    }

    public static String appendData(CLRankingDetailEntity clRankingDetailEntity, int domainId, String country, List<GscEntity> gscEntityList) {
        StringBuffer line = new StringBuffer();

        String domain = DOMAIN_INFO_MAP.get(domainId).split("!_!")[2];
        String subbrands = DOMAIN_INFO_MAP.get(domainId).split("!_!")[0];
        String product = DOMAIN_INFO_MAP.get(domainId).split("!_!")[1];

        line.append(domain).append(SPLIT);
        line.append(subbrands).append(SPLIT);
        line.append(product).append(SPLIT);
        line.append(country).append(SPLIT);
        line.append(FormatUtils.formatDate(FormatUtils.toDate(clRankingDetailEntity.getRankingDate(), FormatUtils.DATE_PATTERN_2), "dd/MM/yyyy")).append(SPLIT);
        line.append(TAG_NAME).append(SPLIT);
        line.append(clRankingDetailEntity.getShareOfVoice()).append(SPLIT);
//        Double allClicks = 0d;
        Double brandedClicks = 0d;
        Double nonbrandedClicks = 0d;
        if(CollectionUtils.isNotEmpty(gscEntityList)){
            for(GscEntity gscEntity : gscEntityList){
//                if(gscEntity.getTypeName().intValue() == 1){
//                    allClicks = gscEntity.getClicks();
//                }
                if(gscEntity.getTypeName().intValue() == 2){
                    brandedClicks = gscEntity.getClicks();
                }
                if(gscEntity.getTypeName().intValue() == 3){
                    nonbrandedClicks = gscEntity.getClicks();
                }
            }
        }
//        line.append(allClicks).append(SPLIT);
        line.append(brandedClicks).append(SPLIT);
        line.append(nonbrandedClicks);

        return line.toString();
    }

    private void copyFileToRemoteServer(int serverType, int domainId, String localFilePath, String remoteFilePath) throws Exception{//todo

        int getServerInfoDomainId = domainId;
        boolean success = serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, getServerInfoDomainId, localFilePath, remoteFilePath, null);
        if (success) {
            String subject = getEmailSubject(success, processMonth);
            String message = subject;
//            sendMailReport(subject, message);
        }

    }

    private String getEmailSubject(boolean success, LocalDate processDate) {
        String subject = "";
        String status = success ? "Success" : "Failed";
        subject = status + " Export 888 SOV " + processDate;
        return subject;
    }

    public static void main(String[] args) throws Exception{


        Extract888HoldingsSOV extract888HoldingsSOV = new Extract888HoldingsSOV();

        if(args == null || args.length < 1){
            log.error("====param error exit");
            return;
        }
        boolean isMobile = Boolean.parseBoolean(args[0]);


        if (args.length >= 2) {
            processMonth = LocalDate.parse(args[1]);//must be yyyy-MM-dd
            extract888HoldingsSOV.processExtract(isMobile);
        } else {
            LocalDate localDate = LocalDate.now();
            processMonth = localDate.minusMonths(1);
            extract888HoldingsSOV.processExtract(isMobile);
        }

    }

}
