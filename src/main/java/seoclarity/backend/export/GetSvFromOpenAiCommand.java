package seoclarity.backend.export;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.openai.OpenAiLoggingDAO;
import seoclarity.backend.entity.actonia.openai.*;
import seoclarity.backend.entity.rankcheck.RcKeywordDomainRelEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.ClarityDBAPIUtils;
import seoclarity.backend.utils.Constants;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

@CommonsLog
public class GetSvFromOpenAiCommand implements Callable<Map<String,Set<String>>> {

    private static final String SPLIT = "\t";
    private static String API_KEY;
    private static String REQUEST_OPEN_AI_URL;
    private static String REQUEST_OPEN_AI_EMBEDDING_URL;
    private static String REQUEST_API_URL;
    private static final String PROVIDER = "openai";
    private static final String ENDPOINT = "chat/completions";

    private static final int RETRY_TIMES = 5;
    private static final String MESSAGE_ROLE_USER = "user";

    private static final String FINISH_REASON_STOP = "stop";
    private Gson gson = new GsonBuilder().disableHtmlEscaping().create();

    //default
    private double temperature = 0.6;
    private double topP = 1.0;
    private double frequencyPenalty = 0.1;
    private double presencePenalty = 0.1;

    private int resultType  = 1;


    private List<RcKeywordDomainRelEntity> requestList;
    private Map<String, String> messageMap;
    private String model;
    private OpenAPromptEntity openAPromptEntity;

    public GetSvFromOpenAiCommand(String openAiKey, String requestOpenAiUrl,String requestOpenAiEmbeddingUrl, String requestApiUrl,
                                  List<RcKeywordDomainRelEntity> requestList,
                                  Map<String, String> messageMap,
                                  String model, OpenAPromptEntity openAPromptEntity, int resultType){

        this.API_KEY = openAiKey;
        this.REQUEST_OPEN_AI_URL = requestOpenAiUrl;
        this.REQUEST_API_URL = requestApiUrl;
        this.REQUEST_OPEN_AI_EMBEDDING_URL = requestOpenAiEmbeddingUrl;
        this.requestList = requestList;
        this.messageMap = messageMap;
        this.model = model;
        this.openAPromptEntity = openAPromptEntity;
        this.resultType = resultType;
    }


    @Override
    public Map<String, Set<String>> call() throws Exception {
        Map<String, Set<String>> totalResultMap = new HashMap<>();
        long startTime = System.currentTimeMillis();
        log.info("Thread Start :" + Thread.currentThread().getName() + ",requestListSize:" + requestList.size());
        Thread.sleep(1000);

        List<String> errorKwList = new ArrayList<>();
        List<String> lineList = new ArrayList<>();

        if(resultType == 1){
            totalResultMap = getCountrySplitMap();
        }else if (resultType == 2){
            totalResultMap = getKeywordSplitMap();
        }

        log.info("===========================totalResultMap:" + totalResultMap.size());
        log.info("Thread End :" + Thread.currentThread().getName() + " total time :" + (System.currentTimeMillis() - startTime) / 1000);

        return totalResultMap;
    }

    private Map<String, Set<String>> getCountrySplitMap(){
        Map<String, Set<String>> totalResultMap = new HashMap<>();
        int index =0;
        Map<String, Set<String>> resultMap;
        for(RcKeywordDomainRelEntity keywordDomainRel : requestList){
            index ++;

            String countryCd = keywordDomainRel.getCountryCd();
            String keywordName = keywordDomainRel.getKeywordName();
            resultMap = new HashMap<>();
            try {
                index ++;
//                if(index >=2){//todo test
//                    break;
//                }

                //step1: get keyword lists from openAI
                RequestContent requestContent = setBodyMap(model, messageMap, keywordName, openAPromptEntity);
                String bodyStr = gson.toJson(requestContent);
                String response = getResponse(bodyStr);
                if (response == null) {
                    log.error(" ====== response null");
                }

                //format  response
                List<String> responseKwList = formatResponseV2(response);
                log.info("=====responseKwList:" + gson.toJson(responseKwList));
                if(CollectionUtils.isEmpty(responseKwList)){
                    log.error("==========NOTFINDresponseKwList:" + keywordName + ",countryCd:" + countryCd);
                }

                Set<String> countryKwList = new HashSet<>();
                countryKwList.addAll(responseKwList);

                Set<String> responseKwSet = new HashSet<>();
                responseKwSet.addAll(responseKwList);
                resultMap.put(countryCd + "!_!" + keywordName, responseKwSet);

                if(totalResultMap.get(countryCd) == null){
                    totalResultMap.put(countryCd, countryKwList);
                }else {
                    Set<String> existKwList = totalResultMap.get(countryCd);
                    existKwList.addAll(countryKwList);
                    totalResultMap.put(countryCd, existKwList);
                }

                log.info("=====commandMap:" + gson.toJson(resultMap));
                totalResultMap.putAll(resultMap);
                log.info("=====totalResultMap:" + gson.toJson(totalResultMap));

            }catch (Exception e){
                e.printStackTrace();
                log.info("=====error kw:" + keywordName + ",countryCd:" + countryCd);
            }
        }
        return totalResultMap;
    }

    private Map<String, Set<String>> getKeywordSplitMap(){
        Map<String, Set<String>> totalResultMap = new HashMap<>();
        int index =0;
        for(RcKeywordDomainRelEntity keywordDomainRel : requestList){
            index ++;

            String keywordName = keywordDomainRel.getKeywordName();
            try {
                index ++;
//                if(index >=2){//todo test
//                    break;
//                }

                //step1: get keyword lists from openAI
                RequestContent requestContent = setBodyMap(model, messageMap, keywordName, openAPromptEntity);
                String bodyStr = gson.toJson(requestContent);
                String response = getResponse(bodyStr);
                if (response == null) {
                    log.error(" ====== response null");
                }

                //format  response
                List<String> responseKwList = formatResponseV2(response);
                log.info("=====responseKwList:" + gson.toJson(responseKwList));
                if(CollectionUtils.isEmpty(responseKwList)){
                    log.error("==========NOTFINDresponseKwList:" + keywordName);
                }

                Set<String> countryKwList = new HashSet<>();
                countryKwList.addAll(responseKwList);

                totalResultMap.put(keywordName, countryKwList);

                log.info("=====totalResultMap:" + gson.toJson(totalResultMap));

            }catch (Exception e){
                e.printStackTrace();
                log.info("=====error kw:" + keywordName);
            }
        }
        return totalResultMap;
    }

    private RequestContent setBodyMap(String model, Map<String, String> messageMap, String kwContent, OpenAPromptEntity openAPrompt) {

        RequestContent requestContent = new RequestContent();
        requestContent.setModel(model);
        requestContent.setTemperature(openAPrompt.getTemperature() == null ? temperature : openAPrompt.getTemperature());
        requestContent.setTop_p(openAPrompt.getTopP() == null ? topP : openAPrompt.getTopP());
        requestContent.setFrequency_penalty(openAPrompt.getFrequencyPenalty() == null ? frequencyPenalty : openAPrompt.getFrequencyPenalty());
        requestContent.setPresence_penalty(openAPrompt.getPresencePenalty() == null ? presencePenalty : openAPrompt.getPresencePenalty());

        List<Message> messages = new ArrayList<>();
        for (String role : messageMap.keySet()) {
            Message message = new Message();
            message.setRole(role);
            message.setContent(messageMap.get(role));
            messages.add(message);
        }

        Message kwMessage = new Message();
        kwMessage.setRole(MESSAGE_ROLE_USER);
        kwMessage.setContent(kwContent);
        messages.add(kwMessage);
        log.info("====messages:" + gson.toJson(messages));

        requestContent.setMessages(messages);
        return requestContent;
    }

    private String getResponse(String bodyStr) {
        int errorTime = 0;
        String response = "";
        while (true) {
            if (errorTime >= RETRY_TIMES) {
                return null;
            }
            try {
                response = startPost(bodyStr);
                return response;
            } catch (RuntimeException re){
                log.error("=====errorAction");
                return null;
            } catch (Exception e) {
                errorTime++;
                e.printStackTrace();
                log.error("ET1:" + errorTime + "SL10S");
                try {
                    Thread.sleep(1000 * 10);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }


    private static String startPost(String bodyStr) throws Exception {
        log.info("************* bodyStr:" + bodyStr);
        long startTime = System.currentTimeMillis();
        String resultString = "";
        HttpResponse<String> response = null;
        try {
            Gson gson = new Gson();
            RequestContent requestContent = gson.fromJson(bodyStr, RequestContent.class);

            Map<String, String> headersMap = new HashMap<>();
            headersMap.put("authorization", API_KEY);
            headersMap.put("content-type", "application/json");

            List<OpenAiRequestWrapper> openAiRequestWrapperList = new ArrayList<>();
            OpenAiRequestWrapper openAiRequestWrapper = new OpenAiRequestWrapper();
            openAiRequestWrapper.setProvider(PROVIDER);
            openAiRequestWrapper.setEndpoint(ENDPOINT);
            openAiRequestWrapper.setHeaders(headersMap);
            openAiRequestWrapper.setQuery(requestContent);
            openAiRequestWrapperList.add(openAiRequestWrapper);
//            bodyStr = gson.toJson(openAiRequestWrapper);
            bodyStr = gson.toJson(openAiRequestWrapperList);
            log.info("************* WrapperbodyStr:" + bodyStr);

            response = Unirest.post(REQUEST_OPEN_AI_URL)
                    .header("Content-Type", "application/json")
                    .body(bodyStr)
                    .asString();
            resultString = response.getBody();

            int statusCode = response.getStatus();

            log.info("====TIME:" + ((System.currentTimeMillis() - startTime) / 1000) + "s");
            log.info("====response:" + resultString);
            if(!isJsonValid(resultString)){
                throw new Exception("=====ResJsonError:" + resultString + ",body:" + bodyStr);
            }
            return resultString;

        } catch (Exception e) {
            log.error("=====Failed post:" + bodyStr);
            e.printStackTrace();
            throw e;
        }
    }


    private static List<String> formatResponse(String content) throws Exception {
        Gson gson1 = new GsonBuilder().disableHtmlEscaping().create();
        List<String> resultList = new ArrayList<>();
        try {
            ResponseContent responseContent = gson1.fromJson(content, ResponseContent.class);
            List<Choices> choicesList = responseContent.getChoices();
            if (CollectionUtils.isNotEmpty(choicesList)) {
                for (Choices choices : choicesList) {
                    String finishReason = choices.getFinish_reason();
                    if (!finishReason.equals(FINISH_REASON_STOP)) {
                        log.error("=====finish error, split and try again:" + finishReason);
                    }
                    if (choices.getMessage() == null) {
                        log.error("===MSGNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    String messageContent = choices.getMessage().getContent();
                    if (org.apache.commons.lang.StringUtils.isBlank(messageContent)) {
                        log.error("===messageContentNULL" + gson1.toJson(responseContent));
                        continue;
                    }

                    log.info("======responseKW: " + messageContent);
                    JsonData jsonData = gson1.fromJson(messageContent, JsonData.class);
                    List<String> jsonResultList = jsonData.getKeywords();
                    for(String str : jsonResultList){
                        if(StringUtils.containsAny(str, Constants.NON_STANDARD_SYMBOLS_SEARCH_VOLUME)){//符号替换为空格
                            str = replaceSymbolsWithSpace(str);
                        }
                        if(countWords(str) > 10){
                            log.info("=====over max keyword phrase" + str);
                            continue;
                        }
                        if(str.length() > 80){
                            log.info("=====over maximum number of characters:" + str);
                            continue;
                        }
                        resultList.add(str.toLowerCase());
                    }

//                    String[] resKwArr = messageContent.split("\n");
//                    for (String resKw : resKwArr){
//                        resKw = resKw.replaceAll("- ", "");
////                        log.info(" === resKw:" + resKw);
//                        resultList.add(resKw);
//                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("====formatResponseError:" + content);
            throw e;
        }
        return resultList;
    }


    //2025/06/05 updated prompt
    private static List<String> formatResponseV2(String content) throws Exception {
        Gson gson1 = new GsonBuilder().disableHtmlEscaping().create();
        List<String> resultList = new ArrayList<>();
        try {
            ResponseContent responseContent = gson1.fromJson(content, ResponseContent.class);
            List<Choices> choicesList = responseContent.getChoices();
            if (CollectionUtils.isNotEmpty(choicesList)) {
                for (Choices choices : choicesList) {
                    String finishReason = choices.getFinish_reason();
                    if (!finishReason.equals(FINISH_REASON_STOP)) {
                        log.error("=====finish error, split and try again:" + finishReason);
                    }
                    if (choices.getMessage() == null) {
                        log.error("===MSGNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    String messageContent = choices.getMessage().getContent();
                    if (org.apache.commons.lang.StringUtils.isBlank(messageContent)) {
                        log.error("===messageContentNULL" + gson1.toJson(responseContent));
                        continue;
                    }

                    log.info("======responseKW: " + messageContent);
                    List<String> jsonResultList = formatKeywords(messageContent);
                    for(String str : jsonResultList){
                        if(StringUtils.containsAny(str, Constants.NON_STANDARD_SYMBOLS_SEARCH_VOLUME)){//符号替换为空格
                            str = replaceSymbolsWithSpace(str);
                        }
                        if(countWords(str) > 10){
                            log.info("=====over max keyword phrase" + str);
                            continue;
                        }
                        if(str.length() > 80){
                            log.info("=====over maximum number of characters:" + str);
                            continue;
                        }
                        resultList.add(str.toLowerCase());
                    }

//                    String[] resKwArr = messageContent.split("\n");
//                    for (String resKw : resKwArr){
//                        resKw = resKw.replaceAll("- ", "");
////                        log.info(" === resKw:" + resKw);
//                        resultList.add(resKw);
//                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("====formatResponseError:" + content);
            throw e;
        }
        return resultList;
    }

    public static String replaceSymbolsWithSpace(String input) {
        for (char c : Constants.NON_STANDARD_SYMBOLS_SEARCH_VOLUME) {
            input = input.replace(c, ' ');
        }
        return input;
    }

    public static int countWords(String str) {
        if (str == null || str.trim().isEmpty()) {
            return 0;
        }
        String[] words = str.trim().split("\\s+");
        return words.length;
    }

    private static ApiJsonResponse getSVByAPI(List<String> keywordList, String countryCd) {
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("accessToken", "c09yxv13-opr3-d745-9734-8pu48420nj67");
        paramMap.put("keywords", keywordList);
        paramMap.put("country_cd", countryCd);
        paramMap.put("language_cd", "en");
        paramMap.put("sv_ratio", 1);
        paramMap.put("sv_source", "google");

        log.info("===request paramJson:" + gson.toJson(paramMap));

        String response = null;
        int max = 3;
        int tryCnt = 0;
        while (tryCnt <= max) {
            tryCnt++;
            try {
                Thread.sleep(5000);
                response = ClarityDBAPIUtils.simplePost(REQUEST_API_URL, gson.toJson(paramMap));
                log.info("====response:" + response);
                try {
                    ApiJsonResponse apiJsonResponse = gson.fromJson(response, ApiJsonResponse.class);
                    log.info("====apiJsonResponse:" + gson.toJson(apiJsonResponse));
                    if(apiJsonResponse.getSv() != null){
                        return apiJsonResponse;
                    }else {
                        //api error,retry
                        log.error("====apiError:" + response);
                    }

                }catch (Exception e){
                    e.printStackTrace();
                    return null;
                }


            } catch (Exception e) {
                log.info("===request failed. tryCnt:" + tryCnt + ", paramJson:" + gson.toJson(paramMap) + ", response:" + response);
                log.error(e.getMessage());
                e.printStackTrace();
            }
        }

//        Map<String, Object> responseMap = gson.fromJson(response, Map.class);
//        Float resultSv = Float.valueOf(responseMap.get("sv").toString());
//        log.info("=====resultSv:" + resultSv);
        return null;
    }


    public static boolean isJsonValid(String jsonString) {
        try {
            new JsonParser().parse(jsonString);
            return true;
        } catch (JsonSyntaxException e) {
            return false;
        }
    }


    public static List<String> formatKeywords(String input) {
        List<String> keywords = new ArrayList<>();
        String[] sections = input.split("Keyword Strings:\\n");
        if (sections.length > 1) {
            String keywordSection = sections[1];
            String[] lines = keywordSection.split("\\n");

            for (String line : lines) {
                if (line.startsWith("- ")) {
                    String keyword = line.substring(2).trim();
                    keywords.add(keyword);
                }
            }
        }
        return keywords;
    }

}
