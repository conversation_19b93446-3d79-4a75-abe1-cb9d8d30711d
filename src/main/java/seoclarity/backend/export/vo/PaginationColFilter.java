/**
 * 
 */
package seoclarity.backend.export.vo;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;

import seoclarity.backend.entity.clickhouse.ri.BaseClarityDBVO;
import seoclarity.backend.utils.FormatUtils;

/**
 * com.actonia.saas.vo.PaginationColFilter.java
 *
 * <AUTHOR>
 *
 * @version $Revision:$
 *          $Author:$
 */
public class PaginationColFilter {
	
	public static final String VARCHAR_NOT = "!";
	public static final String VARCHAR_EQUAL = "=";

	String colname;
	String colvalue;
	
	//add for left navi
	String action;
	
	String date;
	
	String cond;
	
	//Cee - for Leo's Visibility Share - Navi Filter to send DomainName
	private String[] additionalValues; 
	
	//Cee - https://www.wrike.com/open.htm?id=260929578
	private PaginationColFilter[] items;

	private boolean caseSensitive;

	@JsonIgnore
	public PaginationColFilter copy() {
		return copy(false);
	}

	@JsonIgnore
	public PaginationColFilter copy(boolean isForExclude) {
		PaginationColFilter one = new PaginationColFilter();
		one.colname = this.colname;
		one.colvalue = this.colvalue;
		one.action = isForExclude ? reverseAction(this.action) : this.action;
		one.date = this.date;
		// one.cond = isForExclude ? reverseCondition(this.cond) : this.cond;
		one.cond = this.cond;
		one.caseSensitive = this.caseSensitive;
		if (additionalValues != null && additionalValues.length > 0) {
			one.additionalValues = Arrays.copyOf(this.additionalValues, this.additionalValues.length);
		}
		one.colvalue = this.colvalue;
		if (items != null && items.length > 0) {
			one.items = new PaginationColFilter[this.items.length];
			for (int i = 0; i < items.length; i++) {
				one.items[i] = items[i].copy(isForExclude);
			}
		}

		return one;
	}

	// @JsonIgnore
	// private String reverseCondition(String cond) {
	// 	if (StringUtils.equalsIgnoreCase(cond, "and")) {
	// 		return "or";
	// 	} else if (StringUtils.equalsIgnoreCase(cond, "or")) {
	// 		return "and";
	// 	}
	// 	return cond;
	// }

	@JsonIgnore
	private String reverseAction(String action) {
		String newAction = action;
		if (BaseClarityDBVO.STRING_ACTION.eq.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.neq.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.neq.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.eq.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.ct.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.nct.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.nct.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.ct.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.sw.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.nsw.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.nsw.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.sw.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.ew.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.news.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.news.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.ew.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.inc.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.ninc.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.ninc.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.inc.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.pt.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.npt.toString();
		} else if (BaseClarityDBVO.STRING_ACTION.npt.toString().equalsIgnoreCase(action)) {
			newAction = BaseClarityDBVO.STRING_ACTION.pt.toString();
		}

		return newAction;
	}
	
	public String getAction() {
		return action;
	}
	
	public void setAction(String action) {
		this.action = action;
	}
	
	public String getColname() {
		return colname;
	}
	
	public void setColname(String colname) {
		this.colname = colname;
	}
	
	public String getColvalue() {
		return colvalue;
	}
	
	public void setColvalue(String colvalue) {
		this.colvalue = colvalue;
	}
	
	public String getCond() {
		return cond;
	}

	public void setCond(String cond) {
		this.cond = cond;
	}
	
	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public PaginationColFilter[] getItems() {
		return items;
	}

	public void setItems(PaginationColFilter[] items) {
		this.items = items;
	}

	public String[] getAdditionalValues() {
		return additionalValues;
	}

	public void setAdditionalValues(String[] additionalValues) {
		this.additionalValues = additionalValues;
	}

	public boolean isCaseSensitive() {
		return caseSensitive;
	}

	public void setCaseSensitive(boolean caseSensitive) {
		this.caseSensitive = caseSensitive;
	}

	@JsonIgnore
	public String getFirstAdditionalValue() {
		if (getAdditionalValues() != null && getAdditionalValues().length > 0) {
			return getAdditionalValues()[0];
		}
		return null;
	}

	@JsonIgnore
	public String getColFilterSql(String[] column) {
		
		String colKey = column[1];
		String colType = column[2];
		
		if (column == null || !validateColValue()) {
			return "";
		}

		StringBuilder sql = new StringBuilder();
		if (StringUtils.equals(SysColumnType.NULL.name(), colType)) {
			
		}
		
		if (StringUtils.equals(SysColumnType.VARCHAR.name(), colType) ||
				StringUtils.equals(SysColumnType.CHAR.name(), colType)) {
			if (isColValueStartWith(VARCHAR_NOT)) {
				sql.append(" and ").append(colKey).append(" NOT LIKE ? ");
			} else if (isColValueStartWith(VARCHAR_EQUAL)) {
				sql.append(" and ").append(colKey).append(" = ? ");
			} else {
				sql.append(" and ").append(colKey).append(" LIKE ? ");
			}
		} else if (StringUtils.equals(SysColumnType.INTEGER.name(), colType)) {
			if (StringUtils.startsWith(colvalue, "[") &&
					StringUtils.endsWith(colvalue, "]") &&
					StringUtils.contains(colvalue, "-")) {
			    //Cee = https://www.wrike.com/open.htm?id=134418698
			    String valueStr = StringUtils.removeStart(colvalue, "[");
			    valueStr = StringUtils.trim(StringUtils.substringBefore(valueStr, "-"));
			    int intValue = NumberUtils.toInt(valueStr);
                if (intValue == 0) {
                    sql.append(" and ( ").append(colKey).append(" >= ? and  ").append(colKey).append(" <= ? or ").append(colKey).append(" is null ) ");
                } else {
                    sql.append(" and ( ").append(colKey).append(" >= ? and  ").append(colKey).append(" <= ? ) ");
                }
				
			} else if (StringUtils.startsWith(colvalue, "<") ||
					StringUtils.startsWith(colvalue, ">")||
					StringUtils.startsWith(colvalue, "=")) {
			    //Cee = https://www.wrike.com/open.htm?id=134418698
                String valueStr = StringUtils.trim(StringUtils.substring(colvalue, 1));
                if (StringUtils.isNotBlank(valueStr)) {
                    int intValue = NumberUtils.toInt(valueStr);
                    if (intValue == 0) {
                        sql.append(" and (").append(colKey).append(" ").append(colvalue.substring(0, 1)).append(" ? or ").append(colKey).append(" is null ) ");
                    } else {
                        sql.append(" and ").append(colKey).append(" ").append(colvalue.substring(0, 1)).append(" ? ");
                    }
                }
				
			} else {
			    //Cee = https://www.wrike.com/open.htm?id=134418698
//				sql.append(" and ( ").append(colKey).append(" = ? or  ").append(colKey).append(" is null ) ");
				sql.append(" and ").append(colKey).append(" = ? ");
			}
		}
		
		return sql.toString();
	}
	
	@JsonIgnore
	private boolean validateColValue() {
		if (isColValueStartWith(VARCHAR_NOT)) {
			return StringUtils.isNotBlank(StringUtils.removeStart(colvalue, VARCHAR_NOT)); //by cee
		}
		if (isColValueStartWith(VARCHAR_EQUAL)) {
			return StringUtils.isNotBlank(StringUtils.removeStart(colvalue, VARCHAR_EQUAL));
		}
		return StringUtils.isNotBlank(colvalue);
	}
	
	@JsonIgnore
	private boolean isColValueStartWith(String param) {
		return StringUtils.startsWith(colvalue, param);
	}
	
	@JsonIgnore
	public void getColFilterValue(String[] column, List paramList) {
		if (column == null) {
			return ;
		}
		getColFilterValueString(column, paramList);
//		return getColFilterValueInt(column);
	}
	
	private void getColFilterValueString(String[] column, List paramList) {
		String colType = column[2];
		
		if (StringUtils.equals(colType, SysColumnType.VARCHAR.name()) || 
				StringUtils.equals(colType, SysColumnType.CHAR.name())) {
			
			if (validateColValue()) {
				
				String value = colvalue;
				if (isColValueStartWith(VARCHAR_NOT)) {
					value = StringUtils.removeStart(colvalue, VARCHAR_NOT); //by cee
				} else if (isColValueStartWith(VARCHAR_EQUAL)) {
					value = StringUtils.removeStart(colvalue, VARCHAR_EQUAL);
				}
				
				if (isColValueStartWith(VARCHAR_EQUAL)) {
					if (value != null) {
						if (StringUtils.equals(colType, SysColumnType.VARCHAR.name())) {
							paramList.add(FormatUtils.encodeKeyword(value));
						} else {
							paramList.add(value);
						}
						
					}
					
				} else {
					if (value != null) {
						if (StringUtils.equals(colType, SysColumnType.VARCHAR.name())) {
							paramList.add("%" + FormatUtils.encodeKeyword(value).toLowerCase() + "%");
						} else {
							paramList.add("%" + value + "%");
						}
						
					}
				}
			}
		} else if (StringUtils.equals(SysColumnType.INTEGER.name(), colType)) {
			if (StringUtils.startsWith(colvalue, "[") &&
					StringUtils.endsWith(colvalue, "]") &&
					StringUtils.contains(colvalue, "-")) {
				String[] values = colvalue.substring(1, colvalue.length()-1).split("-");
				paramList.add(NumberUtils.toInt(values[0]));
				paramList.add(NumberUtils.toInt(values[1]));
			} else if (StringUtils.startsWith(colvalue, "<") ||
					StringUtils.startsWith(colvalue, ">")||
					StringUtils.startsWith(colvalue, "=")) {
			    
			    //Cee - https://www.wrike.com/open.htm?id=134418698
			    String valueStr = StringUtils.trim(StringUtils.substring(colvalue, 1));
			    if (StringUtils.isNotBlank(valueStr)) {
			        paramList.add(NumberUtils.toInt(valueStr));
//                  paramList.add(NumberUtils.toInt(colvalue.substring(1, colvalue.length())));
                }
			} else {
				paramList.add(NumberUtils.toInt(colvalue));
			}
			
		}
	}

}
