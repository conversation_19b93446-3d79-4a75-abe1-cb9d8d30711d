package seoclarity.backend.export.vo;

import java.util.Objects;

public class KeywordRankingExportForCompetitorVO {

    private String keyword_name;
    private int true_rank;
    private String url;

    public String getKeyword_name() {
        return keyword_name;
    }

    public void setKeyword_name(String keyword_name) {
        this.keyword_name = keyword_name;
    }

    public int getTrue_rank() {
        return true_rank;
    }

    public void setTrue_rank(int true_rank) {
        this.true_rank = true_rank;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof KeywordRankingExportForCompetitorVO)) return false;
        KeywordRankingExportForCompetitorVO that = (KeywordRankingExportForCompetitorVO) o;
        return true_rank == that.true_rank &&
                Objects.equals(keyword_name, that.keyword_name) &&
                Objects.equals(url, that.url);
    }

    @Override
    public int hashCode() {
        return Objects.hash(keyword_name, true_rank, url);
    }
}
