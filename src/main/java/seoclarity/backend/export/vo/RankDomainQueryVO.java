package seoclarity.backend.export.vo;

import org.apache.commons.lang.StringUtils;

public class RankDomainQueryVO {

    private String type;
    private String value;
    private Integer answerboxFlg;
    private Integer llFlg;
    private Integer plaFlg;
    private Integer ppcFlg;
    private Integer peoplealsoaskFlg;
    private Integer appFlg;
    private Integer apmFlg;
    private Integer imgFlg;
    private Integer newsFlg;
    private Integer videoFlg;
    private PaginationColFilter[] filters;

    public RankDomainQueryVO() {
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public PaginationColFilter[] getFilters() {
        return this.filters;
    }

    public void setFilters(PaginationColFilter[] filters) {
        this.filters = filters;
    }

    public Integer getAnswerboxFlg() {
        this.answerboxFlg = this.getUniversalTypeFlg("uni-type", "answerbox");
        return this.answerboxFlg;
    }

    public Integer getLlFlg() {
        this.llFlg = this.getUniversalTypeFlg("uni-type", "locallist");
        return this.llFlg;
    }

    public Integer getPlaFlg() {
        this.plaFlg = this.getUniversalTypeFlg("uni-type", "pla");
        return this.plaFlg;
    }

    public Integer getPpcFlg() {
        this.ppcFlg = this.getUniversalTypeFlg("uni-type", "ppc");
        return this.ppcFlg;
    }

    public Integer getPeoplealsoaskFlg() {
        this.peoplealsoaskFlg = this.getUniversalTypeFlg("uni-type", "peoplealsoask");
        return this.peoplealsoaskFlg;
    }

    public Integer getAppFlg() {
        this.appFlg = this.getUniversalTypeFlg("uni-type", "app");
        return this.appFlg;
    }

    public Integer getApmFlg() {
        this.apmFlg = this.getUniversalTypeFlg("uni-type", "amp");
        return this.apmFlg;
    }

    public Integer getImgFlg() {
        this.imgFlg = this.getUniversalTypeFlg("uni-type", "img");
        return this.imgFlg;
    }

    public Integer getNewsFlg() {
        this.newsFlg = this.getUniversalTypeFlg("uni-type", "news");
        return this.newsFlg;
    }

    public Integer getVideoFlg() {
        this.videoFlg = this.getUniversalTypeFlg("uni-type", "video");
        return this.videoFlg;
    }

    public void setKeywordFilterToLowerCase() {
        if (this.filters != null) {
            PaginationColFilter[] var1 = this.filters;
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                PaginationColFilter filter = var1[var3];
                if (StringUtils.equals(filter.getColname(), "keyword_name")) {
                    filter.setColvalue(filter.getColvalue() != null ? filter.getColvalue().toLowerCase() : "");
                }
            }
        }

    }

    private Integer getUniversalTypeFlg(String universalRankTypeName, String type) {
        Integer typeFlg = 0;
        if (this.filters != null) {
            PaginationColFilter[] var4 = this.filters;
            int var5 = var4.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                PaginationColFilter filter = var4[var6];
                if (StringUtils.equals(filter.getColname(), universalRankTypeName)) {
                    String value = filter.getColvalue();
                    if (StringUtils.contains(value, type)) {
                        typeFlg = 1;
                    }
                }
            }
        }

        return typeFlg;
    }

    public static enum TYPE {
        URL,
        domain,
        folder;

        private TYPE() {
        }
    }

}
