package seoclarity.backend.export.vo;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * 
 * com.actonia.core.Page.java
 *
 * @version $Revision:$
 *          $Author:$
 */
public class Page implements Serializable
{
	public static int DEFAULT_PAGE_SIZE = 30;// default page size

	private int pageSize = DEFAULT_PAGE_SIZE; //Number of records in a page

	private long start; // The first data position in list. From '0' start.

	private Object data; //Record in the current page

	private long totalCount; //Number of record
	
	//https://www.wrike.com/open.htm?id=25494034
	//Solr Facet Query Result
	//by cee
	private Object summaryData;

	/**
	 * Constructor. Only create empty page.
	 */
	public Page() {
		this(0, 0, DEFAULT_PAGE_SIZE, new ArrayList());
	}

	/**
	 * Default constructor.
	 * 
	 * @param start	 the section of the page's date start in DB.
	 * @param totalSize the total number of records in DB.
	 * @param pageSize  the page's capacity
	 * @param data	  the page's date.
	 */
	public Page(long start, long totalSize, int pageSize, Object data) {
		this.pageSize = pageSize;
		this.start = start;
		this.totalCount = totalSize;
		this.data = data;
	}

	/**
	 * Get record total count.
	 */
	public long getTotalCount() {
		return this.totalCount;
	}

	/**
	 * Get page total count.
	 */
	public long getTotalPageCount() {
		if (totalCount % pageSize == 0)
			return totalCount / pageSize;
		else
			return totalCount / pageSize + 1;
	}

	/**
	 * Get page size.
	 */
	public int getPageSize() {
		return pageSize;
	}

	public Object getResult() {
		return data;
	}
	
	public void setResult(Object arg) {
		data = arg;
	}
	
	public long getCurrentPageNo() {
		return start / pageSize + 1;
	}

	/**
	 * Returns false if no next page.
	 */	
	public boolean hasNextPage() {
		return this.getCurrentPageNo() < this.getTotalPageCount() - 1;
	}

	/**
	 * Returns false if no previous page.
	 */
	public boolean hasPreviousPage() {
		return this.getCurrentPageNo() > 1;
	}
	
	public long getNextPageNo() {
		if (hasNextPage()) {
			return this.getCurrentPageNo() + 1;
		} else {
			return this.getTotalPageCount();
		}
	}
	
	public long getPreviousPageNo() {
		if (hasPreviousPage()) {
			return this.getCurrentPageNo() - 1;
		} else {
			return 1;
		}
	}

	/**
	 * Get the first data position in record. Pagesize using default value.
	 *
	 * @see #getStartOfPage(int,int)
	 */
	protected static int getStartOfPage(int pageNo) {
		return getStartOfPage(pageNo, DEFAULT_PAGE_SIZE);
	}

	/**
	 * Get the first data position of in record.
	 *
	 * @param pageNo Page No.
	 * @param pageSize Page size.
	 * @return int 
	 */
	public static int getStartOfPage(int pageNo, int pageSize) {
		int startOfPage = (pageNo - 1) * pageSize;
		if (startOfPage < 0) {
			return 0;
		}
		return startOfPage;
	}
	
	public static int getStartOfPage(int pageNo, int pageSize, int totalCount) {
		
		int pageCount = totalCount / pageSize;
		if (totalCount % pageSize != 0) {
			pageCount = totalCount / pageSize + 1;
		}

		if (pageNo > pageCount) {
			pageNo = pageCount;
		}
		
		int offset = getStartOfPage(pageNo, pageSize);
		if (offset < 0) {
			return 0;
		}
		
		return offset;
	}
	
	protected static int getEndPageOffset(int total, int pageSize) {
		if (total % pageSize == 0) {
			return (total / pageSize - 1) * pageSize;
		} else {
			return (total / pageSize) * pageSize;
		}
	}

	public Object getSummaryData() {
		return summaryData;
	}

	public void setSummaryData(Object summaryData) {
		this.summaryData = summaryData;
	}

	
}
