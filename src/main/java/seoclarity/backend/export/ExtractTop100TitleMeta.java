package seoclarity.backend.export;

import com.google.gson.Gson;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.QuoteMode;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.RIDailyRankingService;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.ri.RankIntelligenceQueryForClarityDBVO;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * https://www.wrike.com/open.htm?id=712288552
 * --hao
 */
public class ExtractTop100TitleMeta {
    public static final String TABLE_INFO_TABLE = "ranking_info";
    public static final String TABLE_DETAIL_TYPE = "ranking_detail";
    public static final String TABLE_SUBRANK_TYPE = "ranking_subrank";
    private static final String DATABASE_NAME = "seo_daily_ranking";
    static CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader(
            "DomainId",
            "KeywordName",
            "Rank",
            "URL",
            "URL Title",
            "URL Meta"
    )
            .withDelimiter(',');
    static final String[] domainIds = new String[]{
            "765",
            "9409",
            "10252",
            "212",
            "7585"
    };
    private static RIDailyRankingService riDailyRankingService;
    private static OwnDomainEntityDAO ownDomainEntityDAO;

    public ExtractTop100TitleMeta() {
        riDailyRankingService = new RIDailyRankingService(true);
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    }

    public static void main(String[] args) {
        System.out.println(" gogogo ==========================");
//        List<Map<String, Object>> extractData = ExtractTop100TitleMeta.extractManagedKetword(domainIds);
        List<Map<String, Object>> extractData = ExtractTop100TitleMeta.extractManagedKetwordv2(domainIds);
        try {
            String response = extractData(extractData);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private static String extractData( List<Map<String, Object>> extractData) throws Exception {
        File file = new File("extractManagedKeywords765.csv");
        CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file)), csvFullFormat);
            for (Map<String, Object> objectMap :extractData) {
//				keyword_name,url, web_rank, avg_search_volume,domain_reverse,label, meta
                csvPrinter.printRecord(
                        objectMap.get("domainId"),
                        objectMap.get("keywordName"),
                        objectMap.get("rank"),
                        objectMap.get("url"),
                        objectMap.get("label"),
                        objectMap.get("meta"),
                        StringUtils.reverse(objectMap.get("domain_reverse").toString())
                );
            }
        csvPrinter.flush();
        csvPrinter.close();
        return "ok";
    }

    private static List<Map<String, Object>> extractManagedKetword(String[] domainIds) {
            StringBuffer sql = new StringBuffer();
            sql.append(" select ");
            sql.append(" own_domain_id as domainId ,keyword_name as keywordName,true_rank as rank,");
            sql.append(" url , label,meta ");
            sql.append(" from merge(seo_daily_ranking, '^d_ranking_detail_us_202106$')");
            sql.append(" where 1 = 1 ");
            sql.append(" and own_domain_id = 765 and sign = 1 and ranking_date = '2021-06-27' and true_rank < 101");
            sql.append(" and location_id = 0 and engine_id = 1 language_id = 1" );
            List<Map<String, Object>> dataList = riDailyRankingService.queryForAll(sql.toString());
            System.out.println(" domain : 765 count : " + dataList.size());
        return dataList;
    }

    private static List<Map<String, Object>> extractManagedKetwordv2(String[] domainIds) {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT  ");
        sql.append("     keyword_name,  ");
        sql.append("     keyword_rankcheck_id,  ");
        sql.append("     true_rank AS true_rank,  ");
        sql.append("     url,  ");
        sql.append("     label AS title,  ");
        sql.append("     type ");
        sql.append(" FROM d_ranking_detail_202106_us ");
        sql.append(" WHERE (1 = 1) AND (engine_id = 1) AND (language_id = 1) AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(1), toUInt64(1), toUInt64(keyword_rankcheck_id))) = 0) AND (location_id = 0) AND (true_rank < 101) AND (keyword_rankcheck_id GLOBAL IN  ");
        sql.append(" ( ");
        sql.append("     SELECT keyword_rankcheck_id ");
        sql.append("     FROM  ");
        sql.append("     ( ");
        sql.append("         SELECT  ");
        sql.append("             grouptag_id,  ");
        sql.append("             own_domain_id,  ");
        sql.append("             location_id,  ");
        sql.append("             grouptag_status,  ");
        sql.append("             keyword_rankcheck_id ");
        sql.append("         FROM seo_daily_ranking.cdb_tracked_keyword ");
        sql.append("         WHERE (own_domain_id = 765) AND (grouptag_id = 0) AND (grouptag_status = 1) AND (keyword_type = 1) ");
        sql.append("         GROUP BY  ");
        sql.append("             grouptag_id,  ");
        sql.append("             own_domain_id,  ");
        sql.append("             location_id,  ");
        sql.append("             grouptag_status,  ");
        sql.append("             keyword_rankcheck_id ");
        sql.append("         HAVING sum(sign) > 0 ");
        sql.append("     ) ");
        sql.append(" )) ");
        List<Map<String, Object>> dataList = riDailyRankingService.queryForAll(sql.toString());
        System.out.println(" domain : 765 count : " + dataList.size());
        return dataList;
    }
}
