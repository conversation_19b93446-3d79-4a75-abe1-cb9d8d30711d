package seoclarity.backend.export.autoExtract;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.utils.CommonUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@CommonsLog
public class TestExtract extends AbstractExtractScript {

    public TestExtract(){
        super();
    }

    @Override
    public void exec(ExtractScriptInstanceEntity extractScriptInstance, AbstractExtractScript extractScript) {
        log.info("start process testExtract, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        extractScript.startExtract(extractScriptInstance);
    }

    @Override
    public void setScriptDetail(ExtractScriptDetailEntity scriptDetailEntity) {
        extractScriptDetail = scriptDetailEntity;
    }

    @Override
    protected String getFileName() {
        return ownDomainEntity.getId() + "_" + extractDateStr + "_TOP_" + topX + "_RankReport_" + device + ".txt";
    }

    @Override
    protected void addHeadersForExactFile() throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Ranking URL").append(SPLIT);
        header.append("True Rank").append(SPLIT);
        header.append("Web Rank").append(SPLIT);
        header.append("URL Type").append(SPLIT);
        header.append("Search Volume");
        lines.add(header.toString());
        FileUtils.writeLines(localFile, lines, true);
    }

    @Override
    protected List<String> getDataFromDB() {

        List<String> extractLines = new ArrayList<String>();

        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();

        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

        List<CLRankingDetailEntity> dataList = new ArrayList<>();
        boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
        String extractKey = engineId + "_" + languageId + "_" + isMobile;
        int locationId = 0;

        ExtractQueryVO extractQueryVO = new ExtractQueryVO();
        extractQueryVO.setDomainId(domainId);
        extractQueryVO.setEngineId(engineId);
        extractQueryVO.setLanguageId(languageId);
        extractQueryVO.setDevice(device);
        extractQueryVO.setRankDate(extractDateStr);
        extractQueryVO.setRank(topX);
        extractQueryVO.setLocationId(locationId);

        dataList = clDailyRankingEntityDao.exportTopXKeywords(domainId, engineId, languageId, locationId, extractDateStr, isMobile, topX);
        log.info("===primary dataList size: " + dataList.size());

        for (CLRankingDetailEntity detail : dataList) {
            String locationName =  detail.getLocationId() == 0 ? "United States" : geoService.getCityName(detail.getLocationId());
            if (StringUtils.isBlank(locationName)) {
                locationName = "-";
            }
            detail.setLocationName(locationName);
            extractLines.add(appendData(detail));
        }

        return extractLines;
    }

    @Override
    protected void setAdditionalParameter() {

    }


}
