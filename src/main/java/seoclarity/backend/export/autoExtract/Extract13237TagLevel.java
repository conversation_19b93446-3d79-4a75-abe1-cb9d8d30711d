package seoclarity.backend.export.autoExtract;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.RankIndexParamVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;

/**
 *
 * https://www.wrike.com/open.htm?id=1510489141
 */
@CommonsLog
public class Extract13237TagLevel extends AbstractExtractScript {

    protected static final String SPLIT = ",";

    public Extract13237TagLevel(){
        super();
//        serverType = 1; //test
//        remotePath = "/home/<USER>/13237/";
        serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP_GCS;
        remotePath = "223126";
    }

    protected void setAdditionalParameter(){

    }

    @Override
    public void exec(ExtractScriptInstanceEntity extractScriptInstance, AbstractExtractScript extractScript) {
        log.info("start process Extract13237TagLevel, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        extractScript.startExtract(extractScriptInstance);
    }

    @Override
    public void setScriptDetail(ExtractScriptDetailEntity scriptDetailEntity) {
        extractScriptDetail = scriptDetailEntity;
    }

    @Override
    protected String getFileName() {
        String processingDate = FormatUtils.formatDate(FormatUtils.toDate(extractDateStr, FormatUtils.DATE_PATTERN_2), FormatUtils.DATE_FORMAT_YYYYMMDD);
        return "13237_" + processingDate + "_Metrics_Report_" + device + ".csv";
    }

    @Override
    protected void addHeadersForExactFile() throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Engine").append(SPLIT);
        header.append("Tag").append(SPLIT);
        header.append("Average rank").append(SPLIT);
        header.append("Estimated Traffic").append(SPLIT);
        header.append("Share of Market").append(SPLIT);
        header.append("Share of Voice").append(SPLIT);
        header.append("WTD Average rank");
        lines.add(header.toString());
        FileUtils.writeLines(localFile, lines, true);
    }

    @Override
    protected List<String> getDataFromDB() {

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);

        List<String> extractLines = new ArrayList<String>();

        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);
        String engineName = ownDomainEntity.getSearchEngine();

        List<CLRankingDetailEntity> dataList = new ArrayList<>();
        boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
        int locationId = 0;

        LocalDate localDate = LocalDate.parse(extractDateStr);
        int monthDayCount = localDate.lengthOfMonth();
        List<RankIndexParamEntity> paramList = rankIndexParamEntityDAO.getRankIndexParams(domainId, 0);
        RankIndexParamVO rankIndexParamVO = new RankIndexParamVO(paramList);
        List<String> ctrList = rankIndexParamVO.getParamList();

        String index = rankIndexParamVO.getTotalIndex(ctrList);
        log.error("=====totalCtrIndex:" + index);
        if (index == null) {
            log.error("=====ctr index null");
        }

        //tagLevel
        dataList = clDailyRankingEntityDao.get11547CustomExtract(domainId, engineId, languageId, extractDateStr, isMobile, rootDomainReverse, ctrList, monthDayCount, index, false);
        log.info("===primary dataList size: " + dataList.size());
        for (CLRankingDetailEntity detail : dataList) {
            extractLines.add(appendDataForTagLevel(detail, engineName));
        }

        //domain Level
        Map<String, Object> metricsMap = clDailyRankingEntityDao.getDomainLevelCalculatedMetrics(domainId, engineId, languageId,
                extractDateStr, isMobile, rootDomainReverse, ownDomainSettingEntity.isEnableDifferentKs());

        Map<String, Object> kwCntMap = clDailyRankingEntityDao.getDomainLevelTotalKw(domainId, engineId, languageId,
                extractDateStr, isMobile, ownDomainSettingEntity.isEnableDifferentKs());

        Map<String, String> domainLevel = formatDomainLevel(metricsMap, kwCntMap, ctrList, monthDayCount, index);

        extractLines.add(appendDataForDomainLevel(domainLevel, engineName));

        String extractKey = engineId + "_" + languageId + "_" + isMobile;
        List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
        if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
            for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                engineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                languageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                boolean secondaryIsMobile = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? false : true;
                if (secondaryIsMobile != isMobile) {
                    log.info("===secondary not same device isMobile: " + isMobile + ",secondaryIsMobile:" + secondaryIsMobile);
                    continue;
                }
                String secondaryExtractKey = engineId + "_" + languageId + "_" + secondaryIsMobile;
                if (secondaryExtractKey.equalsIgnoreCase(extractKey)) {
                    log.info("===already extract engine skip: " + secondaryExtractKey + ",extractKey:" + extractKey);
                    continue;
                }
                String secondaryEngineName = domainSearchEngineRelEntity.getSearchEngine();

                log.info("===process secondary engine :" + engineId + ",languageId: " + languageId + ",isMobile:" + secondaryIsMobile + ",domain:" + domainId);
                List<CLRankingDetailEntity> secondaryDataList = new ArrayList<>();

                //tagLevel
                dataList = clDailyRankingEntityDao.get11547CustomExtract(domainId, engineId, languageId, extractDateStr, isMobile, rootDomainReverse, ctrList, monthDayCount, index, false);
                log.info("===primary dataList size: " + dataList.size());
                for (CLRankingDetailEntity detail : dataList) {
                    extractLines.add(appendDataForTagLevel(detail, secondaryEngineName));
                }
                //domain Level
                Map<String, Object> secMetricsMap = clDailyRankingEntityDao.getDomainLevelCalculatedMetrics(domainId, engineId, languageId,
                        extractDateStr, isMobile, rootDomainReverse, ownDomainSettingEntity.isEnableDifferentKs());

                Map<String, Object> secKwCntMap = clDailyRankingEntityDao.getDomainLevelTotalKw(domainId, engineId, languageId,
                        extractDateStr, isMobile, ownDomainSettingEntity.isEnableDifferentKs());

                Map<String, String> secDomainLevel = formatDomainLevel(secMetricsMap, secKwCntMap, ctrList, monthDayCount, index);

                extractLines.add(appendDataForDomainLevel(secDomainLevel, secondaryEngineName));

                log.info("===secondaryDataList size:" + secondaryDataList.size());
            }
        }

        return extractLines;
    }

    private Map<String, String> formatDomainLevel(Map<String, Object> metricsMap, Map<String, Object> kwCntMap, List<String> ctrList, int monthDayCount, String ctrIndex){
        Map<String, String> resultMap = new HashMap<>();

        int keywordTotalSv = Integer.parseInt(kwCntMap.get("keywordTotalSv").toString());
        int keywordCnt = Integer.parseInt(kwCntMap.get("keywordCnt").toString());

        int totalRankcheckedKeywords = Integer.parseInt( metricsMap.get("total_rankchecked_keywords").toString());
        long totalRankedSearchvol = Long.parseLong( metricsMap.get("total_ranked_searchvol").toString());
        int rank1 = Integer.parseInt( metricsMap.get("rank1").toString());
        int rank2 = Integer.parseInt( metricsMap.get("rank2").toString());
        int rank3 = Integer.parseInt( metricsMap.get("rank3").toString());
        int rank4 = Integer.parseInt( metricsMap.get("rank4").toString());
        int rank5 = Integer.parseInt( metricsMap.get("rank5").toString());
        int rank6 = Integer.parseInt( metricsMap.get("rank6").toString());
        int rank7 = Integer.parseInt( metricsMap.get("rank7").toString());
        int rank8 = Integer.parseInt( metricsMap.get("rank8").toString());
        int rank9 = Integer.parseInt( metricsMap.get("rank9").toString());
        int rank10 = Integer.parseInt( metricsMap.get("rank10").toString());
        int rankCountInTop = Integer.parseInt( metricsMap.get("rankCountInTop").toString());
        int page2 = Integer.parseInt( metricsMap.get("page2").toString());
        int page3 = Integer.parseInt( metricsMap.get("page3").toString());
        int page4 = Integer.parseInt( metricsMap.get("page4").toString());
        int page5 = Integer.parseInt( metricsMap.get("page5").toString());
        int page6 = Integer.parseInt( metricsMap.get("page6").toString());
        int rank1Sv = Integer.parseInt( metricsMap.get("rank1Sv").toString());
        int rank2Sv = Integer.parseInt( metricsMap.get("rank2Sv").toString());
        int rank3Sv = Integer.parseInt( metricsMap.get("rank3Sv").toString());
        int rank4Sv = Integer.parseInt( metricsMap.get("rank4Sv").toString());
        int rank5Sv = Integer.parseInt( metricsMap.get("rank5Sv").toString());
        int rank6Sv = Integer.parseInt( metricsMap.get("rank6Sv").toString());
        int rank7Sv = Integer.parseInt( metricsMap.get("rank7Sv").toString());
        int rank8Sv = Integer.parseInt( metricsMap.get("rank8Sv").toString());
        int rank9Sv = Integer.parseInt( metricsMap.get("rank9Sv").toString());
        int rank10Sv = Integer.parseInt( metricsMap.get("rank10Sv").toString());
        int rank1120Sv = Integer.parseInt( metricsMap.get("rank1120Sv").toString());
        int rank2130Sv = Integer.parseInt( metricsMap.get("rank2130Sv").toString());
        int totalRankWithout101 = Integer.parseInt( metricsMap.get("total_rank_without101").toString());
        double avgRankWithout101 = Double.parseDouble( metricsMap.get("avg_rank_without101").toString());
        long wtdVolWithout101 = Long.parseLong( metricsMap.get("wtd_vol_without101").toString());

        Double estdTraffic = 0.0;
        if(ctrList.size() == 9){
            log.info("====es1");
            estdTraffic = (rank1Sv *  Double.parseDouble(ctrList.get(0)) + rank2Sv * Double.parseDouble((ctrList.get(1))
                    + rank3Sv *  Double.parseDouble(ctrList.get(2)) + rank4Sv *  Double.parseDouble(ctrList.get(3))
                    + rank5Sv *  Double.parseDouble(ctrList.get(4)) + rank6Sv *  Double.parseDouble(ctrList.get(5))
                    + rank7Sv *  Double.parseDouble(ctrList.get(6)) + rank8Sv *  Double.parseDouble(ctrList.get(7))
                    + rank9Sv *  Double.parseDouble(ctrList.get(8)))) /monthDayCount;
        } else if (ctrList.size() == 10) {
            log.info("====es1");
            estdTraffic = (rank1Sv *  Double.parseDouble(ctrList.get(0)) + rank2Sv *  Double.parseDouble(ctrList.get(1))
                    + rank3Sv *  Double.parseDouble(ctrList.get(2)) + rank4Sv *  Double.parseDouble(ctrList.get(3))
                    + rank5Sv *  Double.parseDouble(ctrList.get(4)) + rank6Sv *  Double.parseDouble(ctrList.get(5))
                    + rank7Sv *  Double.parseDouble(ctrList.get(6)) + rank8Sv *  Double.parseDouble(ctrList.get(7))
                    + rank9Sv *  Double.parseDouble(ctrList.get(8)) + rank10Sv *  Double.parseDouble(ctrList.get(9)) ) /monthDayCount;
        }

        log.info(" ========= estdTraffic: " + estdTraffic);
        int estdTrafficResult = (int) Math.round(estdTraffic);
        // 使用 DecimalFormat 保留四位小数
        DecimalFormat df = new DecimalFormat("0.0000");

        if(keywordTotalSv != 0 && keywordCnt != 0){
            double sovWith101 = estdTraffic/((keywordTotalSv * Double.parseDouble(ctrList.get(0)) / monthDayCount ));
            String sovWith101Result = df.format(sovWith101);
            System.out.println("sovWith101: " + sovWith101Result);

            double somWith101 = estdTraffic/((keywordTotalSv * Double.parseDouble(ctrIndex) ) / monthDayCount);
            String somWith101Result = df.format(somWith101);
            System.out.println("somWith101: " + somWith101Result);

            DecimalFormat dfAvgRank = new DecimalFormat("0.0");
            double avgRankWith101 = ((keywordCnt - totalRankcheckedKeywords) * 101.0 + totalRankWithout101) / keywordCnt;
            String avgRankWith101Result = dfAvgRank.format(avgRankWith101);
            System.out.println("avgRankWith101Result: " + avgRankWith101Result);

            double wtdAvgRankWith101 = ((keywordTotalSv - totalRankedSearchvol) * 101.0 + wtdVolWithout101) / keywordTotalSv;
            String wtdAvgRankWith101Result = dfAvgRank.format(wtdAvgRankWith101);
            System.out.println("wtdAvgRankWith101Result: " + wtdAvgRankWith101Result);

            resultMap.put("avgRank", avgRankWith101Result);
            resultMap.put("estdTraffic", String.valueOf(estdTrafficResult));
            resultMap.put("som", somWith101Result);
            resultMap.put("sov", sovWith101Result);
            resultMap.put("wtdAvg", wtdAvgRankWith101Result);
        }else if(keywordTotalSv == 0 && keywordCnt != 0) {
            DecimalFormat dfAvgRank = new DecimalFormat("0.0");
            double avgRankWith101 = ((keywordCnt - totalRankcheckedKeywords) * 101.0 + totalRankWithout101) / keywordCnt;
            String avgRankWith101Result = dfAvgRank.format(avgRankWith101);
            System.out.println("avgRankWith101Result: " + avgRankWith101Result);

            resultMap.put("avgRank", avgRankWith101Result);
            resultMap.put("estdTraffic", "-");
            resultMap.put("som", "-");
            resultMap.put("sov", "-");
            resultMap.put("wtdAvg", "-");
        } else if (keywordTotalSv != 0 && keywordCnt == 0) {
            double sovWith101 = estdTraffic/((keywordTotalSv * Double.parseDouble(ctrList.get(0)) / monthDayCount ));
            String sovWith101Result = df.format(sovWith101);
            System.out.println("sovWith101: " + sovWith101Result);

            double somWith101 = estdTraffic/((keywordTotalSv * Double.parseDouble(ctrIndex) ) / monthDayCount);
            String somWith101Result = df.format(somWith101);
            System.out.println("somWith101: " + somWith101Result);

            resultMap.put("avgRank", "-");
            resultMap.put("estdTraffic", String.valueOf(estdTrafficResult));
            resultMap.put("som", somWith101Result);
            resultMap.put("sov", sovWith101Result);
            resultMap.put("wtdAvg", "-");
        }else {
            resultMap.put("avgRank", "-");
            resultMap.put("estdTraffic", "-");
            resultMap.put("som", "-");
            resultMap.put("sov", "-");
            resultMap.put("wtdAvg", "-");
        }


        return resultMap;
    }

    protected String appendDataForTagLevel(CLRankingDetailEntity clRankingDetailEntity, String engineName) {
        StringBuffer line = new StringBuffer();
        line.append(extractDateStr).append(SPLIT);
        line.append(engineName).append(SPLIT);
        line.append(FormatUtils.escapeCsvField(clRankingDetailEntity.getTagName())).append(SPLIT);
        line.append(clRankingDetailEntity.getAvgRank().intValue() == 999 || clRankingDetailEntity.getAvgRank().intValue() == 101 ? "-" : clRankingDetailEntity.getAvgRank()).append(SPLIT);
        line.append(clRankingDetailEntity.getEstdTraffic()).append(SPLIT);
        line.append(clRankingDetailEntity.getShareOfMarket()).append(SPLIT);
        line.append(clRankingDetailEntity.getShareOfVoice()).append(SPLIT);
        line.append(clRankingDetailEntity.getWtdAvgRank().intValue() == 999 || clRankingDetailEntity.getWtdAvgRank().intValue() == 101 ? "-" : clRankingDetailEntity.getWtdAvgRank());
        return line.toString();
    }

    protected String appendDataForDomainLevel(Map<String, String> domainLevelMapResult, String engineName) {
        StringBuffer line = new StringBuffer();
        line.append(extractDateStr).append(SPLIT);
        line.append(engineName).append(SPLIT);
        line.append("-").append(SPLIT);
        line.append(domainLevelMapResult.get("avgRank")).append(SPLIT);
        line.append(domainLevelMapResult.get("estdTraffic")).append(SPLIT);
        line.append(domainLevelMapResult.get("som")).append(SPLIT);
        line.append(domainLevelMapResult.get("sov")).append(SPLIT);
        line.append(domainLevelMapResult.get("wtdAvg"));
        return line.toString();
    }

}
