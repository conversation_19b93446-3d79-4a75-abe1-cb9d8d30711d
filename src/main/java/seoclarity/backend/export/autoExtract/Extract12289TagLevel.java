package seoclarity.backend.export.autoExtract;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.RankIndexParamVO;
import seoclarity.backend.entity.actonia.RankIndexParamEntity;
import seoclarity.backend.entity.actonia.ServerAuthenticationInfoEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * only extract Google.ca (en) Mobile
 * https://www.wrike.com/open.htm?id=1163418320
 */
@CommonsLog
public class Extract12289TagLevel extends AbstractExtractScript {

    public Extract12289TagLevel(){
        super();
        //test
//        serverType = 1;
//        remotePath = "/home/<USER>/12289/";

        serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_COMMON_PARAM_S3;
        remotePath = "";
    }

    @Override
    public void exec(ExtractScriptInstanceEntity extractScriptInstance, AbstractExtractScript extractScript) {
        log.info("start process Extract12289TagLevel, current datetime: " + DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        extractScript.startExtract(extractScriptInstance);
    }

    @Override
    public void setScriptDetail(ExtractScriptDetailEntity scriptDetailEntity) {
        extractScriptDetail = scriptDetailEntity;
    }

    @Override
    protected String getFileName() {
        String processingDate = FormatUtils.formatDate(FormatUtils.toDate(extractDateStr, FormatUtils.DATE_PATTERN_2), FormatUtils.DATE_FORMAT_YYYYMMDD);
        return "12289_" + processingDate + "_KeywordTag_Metrics_Report_" + device + ".txt";
    }

    @Override
    protected void addHeadersForExactFile() throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Tag").append(SPLIT);
        header.append("Average rank").append(SPLIT);
        header.append("Estimated Traffic").append(SPLIT);
        header.append("Share of Market").append(SPLIT);
        header.append("Share of Voice").append(SPLIT);
        header.append("WTD Average rank");
        lines.add(header.toString());
        FileUtils.writeLines(localFile, lines, true);
    }

    @Override
    protected List<String> getDataFromDB() {

        List<String> extractLines = new ArrayList<String>();

        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

        List<CLRankingDetailEntity> dataList = new ArrayList<>();
        boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
        int locationId = 0;

        LocalDate localDate = LocalDate.parse(extractDateStr);
        int monthDayCount = localDate.lengthOfMonth();
        List<RankIndexParamEntity> paramList = rankIndexParamEntityDAO.getRankIndexParams(domainId, 0);
        RankIndexParamVO rankIndexParamVO = new RankIndexParamVO(paramList);
        List<String> ctrList = rankIndexParamVO.getParamList();

        String index = rankIndexParamVO.getTotalIndex(ctrList);
        log.error("=====totalCtrIndex:" + index);
        if (index == null) {
            log.error("=====ctr index null");
        }
        dataList = clDailyRankingEntityDao.get11547CustomExtract(domainId, engineId, languageId, extractDateStr, isMobile, rootDomainReverse, ctrList, monthDayCount, index, false);

        log.info("===primary dataList size: " + dataList.size());

        for (CLRankingDetailEntity detail : dataList) {
            extractLines.add(appendData(detail));
        }

        return extractLines;
    }

    @Override
    protected void setAdditionalParameter() {

    }

    protected String appendData(CLRankingDetailEntity clRankingDetailEntity) {
        StringBuffer line = new StringBuffer();
        line.append(extractDateStr).append(SPLIT);
        line.append(clRankingDetailEntity.getTagName()).append(SPLIT);
        line.append(clRankingDetailEntity.getAvgRank().intValue() == 999 || clRankingDetailEntity.getAvgRank().intValue() == 101 ? "-" : clRankingDetailEntity.getAvgRank()).append(SPLIT);
        line.append(clRankingDetailEntity.getEstdTraffic()).append(SPLIT);
        line.append(clRankingDetailEntity.getShareOfMarket()).append(SPLIT);
        line.append(clRankingDetailEntity.getShareOfVoice()).append(SPLIT);
        line.append(clRankingDetailEntity.getWtdAvgRank().intValue() == 999 || clRankingDetailEntity.getWtdAvgRank().intValue() == 101 ? "-" : clRankingDetailEntity.getWtdAvgRank()).append(SPLIT);
        return line.toString();
    }

}
