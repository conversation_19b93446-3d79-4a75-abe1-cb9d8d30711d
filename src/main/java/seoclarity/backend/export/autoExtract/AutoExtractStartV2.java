package seoclarity.backend.export.autoExtract;

import com.alibaba.fastjson.JSON;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.stream.Collectors;

@CommonsLog
public class AutoExtractStartV2 {

    private ExtractService extractService;
    public AutoExtractStartV2(){
        extractService = SpringBeanFactory.getBean("extractService");
    }

    public static void main(String[] args){
        AutoExtractStartV2 autoExtractStart = new AutoExtractStartV2();
        autoExtractStart.startProcess();//start auto extract
    }


    public void startProcess(){
        List<ExtractScriptInstanceEntity> extractScriptInstanceList = extractService.getNeedExtractListGroup120();
        if(CollectionUtils.isEmpty(extractScriptInstanceList)){
            log.info("====no Instance need to run.");
            return;
        }
        for(ExtractScriptInstanceEntity needToRunInstance: extractScriptInstanceList){

            String classStr = needToRunInstance.getFullQulifiedClass();
            List<ExtractScriptDetailEntity> detailList = needToRunInstance.getScriptDetailList();

            log.info("*************ST Class:" + classStr + ",oId:" + needToRunInstance.getOwnDomainId() + ",extractDate:" +
                    JSON.toJSON(needToRunInstance.getScriptDetailList().stream().map(v1 -> v1.getTargetDate()).collect(Collectors.toList())));
            for (ExtractScriptDetailEntity scriptDetailEntity : detailList) {
                try {
                    Class<?> clazz = Class.forName(classStr);
                    Object o = clazz.newInstance();
                    Method setScriptDetail = clazz.getMethod("setScriptDetail", ExtractScriptDetailEntity.class);
                    setScriptDetail.invoke(o, scriptDetailEntity);
                    Method exec = clazz.getMethod("exec", ExtractScriptInstanceEntity.class, AbstractExtractScriptV2.class);
                    exec.invoke(o, needToRunInstance, o);
                } catch (ClassNotFoundException e) {
                    log.error(classStr + " ClassNotFoundException:" + CollectionSplitUtils.getErrorMsg(e));
                } catch (NoSuchMethodException e) {
                    log.error(classStr + " NoSuchMethodException:" + CollectionSplitUtils.getErrorMsg(e));
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    log.error(classStr + " IllegalAccessException:" + CollectionSplitUtils.getErrorMsg(e));
                } catch (InvocationTargetException e) {
                    log.error(classStr + " InvocationTargetException:" + CollectionSplitUtils.getErrorMsg(e));
                } catch (InstantiationException e) {
                    log.error(classStr + " InstantiationException:" + CollectionSplitUtils.getErrorMsg(e));
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error(classStr + " Exception:" + CollectionSplitUtils.getErrorMsg(e));
                }

            }
        }

    }

}
