package seoclarity.backend.export.ri;

import kafka.controller.LeaderIsrAndControllerEpoch;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.RIDailyRankingService;
import seoclarity.backend.dao.clickhouse.monthlyranking.CentralKeywordTokenizerDAO;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=737114366
 * ---Hao
 */
public class ExtractCategoriesByZ {

    private RIDailyRankingService riDailyRankingService;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private  boolean isUseHotDB = true;
    private  int ASSIGN_NUM = 1000;
    private CentralKeywordTokenizerDAO centralKeywordTokenizerDAO;


    public ExtractCategoriesByZ() {
        riDailyRankingService = new RIDailyRankingService(isUseHotDB);
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        centralKeywordTokenizerDAO = SpringBeanFactory.getBean("centralKeywordTokenizerDAO");
    }
    static CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader(
            "Category Id",
            "Domain",
            "Category",
            "Cnt"
    ).withDelimiter(',');

    public static void main(String[] args) {
        ExtractCategoriesByZ categoriesByZ = new ExtractCategoriesByZ();
        try {
            System.out.println(" ========================== gogogo ===============================");
            FileInputStream inputStream = new FileInputStream("categary.txt");
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

            String str = null;
            String[] arrs = null;
            List<String> domainList = new ArrayList<>();
            while ((str = bufferedReader.readLine()) != null) {
                arrs = str.split("\t");
                domainList.add(arrs[0]);
            }

            inputStream.close();
            String sql = categoriesByZ.getCategories(domainList);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private String getCategories(List<String> domainList) throws IOException {
        File file = new File("exportCat/CategoriesExtractV2.csv");
        int count = 0;
        CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file)), csvFullFormat);
        List<List<String>> assignLists = averageAssign(domainList,ASSIGN_NUM);
        for (List<String> assignList : assignLists) {
            String[] assignStr =(String[])assignList.toArray(new String[assignList.size()]);
            StringBuffer sql = new StringBuffer();
            sql.append(" SELECT ");
            sql.append("     category_id, ");
            sql.append("     domain, ");
            sql.append("     parent_id, ");
            sql.append("     category, ");
            sql.append("     cnt");
            sql.append(" FROM ");
            sql.append(" (");
            sql.append("     SELECT ");
            sql.append("         id AS category_id, ");
            sql.append("         parent_id, ");
            sql.append("         category");
            sql.append("     FROM keyword_searchvolume.dis_keyword_categorey");
            sql.append("     WHERE (1 = 1) AND ((parent_id = 0) OR (parent_id GLOBAL IN ");
            sql.append("     (");
            sql.append("         SELECT id");
            sql.append("         FROM keyword_searchvolume.dis_keyword_categorey");
            sql.append("         WHERE parent_id = 0");
            sql.append("     )))");
            sql.append(" )");
            sql.append(" ALL INNER JOIN ");
            sql.append(" (");
            sql.append("     SELECT ");
            sql.append("         domain, ");
            sql.append("         category_id, ");
            sql.append("         count() AS cnt");
            sql.append("     FROM keyword_searchvolume.temp_test_dis_domain_kid_category");
            sql.append("     ARRAY JOIN category AS category_id");
            sql.append("     WHERE domain in( '"+StringUtils.join(assignStr,"','")+"')");
            sql.append("     GROUP BY ");
            sql.append("         domain, ");
            sql.append("         category_id");
            sql.append(" ) USING (category_id)");
            sql.append(" SETTINGS distributed_aggregation_memory_efficient = 1, max_bytes_before_external_group_by = 10000000000");
            System.out.println("==================== sql : =======================");
            System.out.println(sql.toString());
//            List<Map<String, Object>> dataList = riDailyRankingService.queryForAll(sql.toString());
            List<Map<String, Object>> dataList =centralKeywordTokenizerDAO.getJdbcTemplate().queryForList(sql.toString());
            count += dataList.size();
            extractData(dataList,csvPrinter);
        }
        csvPrinter.flush();
        csvPrinter.close();
        System.out.println(" count : " + count);
        return "finished";
    }

    private void extractData(List<Map<String, Object>> extractData , CSVPrinter csvPrinter) throws IOException {
        System.out.println(" extractData.size : " + extractData.size());

        for (Map<String, Object> objectMap : extractData) {
            String domain = StringUtils.reverseDelimited(objectMap.get("domain").toString(), '.');
            csvPrinter.printRecord(
                    objectMap.get("category_id"),
                    domain,
                    objectMap.get("category"),
                    objectMap.get("cnt")
            );
        }
    }


    public static List<List<String>> averageAssign(List<String> source, int n){
        List<List<String>> result=new ArrayList<List<String>>();
        int remaider=source.size()%n;
        int number=source.size()/n;
        int offset=0;
        for(int i=0;i<n;i++){
            List<String> value=null;
            if(remaider>0){
                value=source.subList(i*number+offset, (i+1)*number+offset+1);
                remaider--;
                offset++;
            }else{
                value=source.subList(i*number+offset, (i+1)*number+offset);
            }
            result.add(value);
        }
        return result;
    }







}
