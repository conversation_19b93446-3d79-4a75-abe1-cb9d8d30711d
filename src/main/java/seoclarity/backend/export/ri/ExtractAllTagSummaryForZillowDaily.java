package seoclarity.backend.export.ri;

import java.io.File;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.util.Assert;

import cn.hutool.core.text.csv.CsvWriteConfig;
import cn.hutool.core.text.csv.CsvWriter;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.utils.*;

/**
 * <AUTHOR>
 * @date 2021-10-18
 * @path seoclarity.backend.export.ri.ExtractAllTagSummaryForZillowDaily
 *  https://www.wrike.com/open.htm?id=766370126
 *  https://www.wrike.com/open.htm?id=1326968879
 */
public class ExtractAllTagSummaryForZillowDaily {
	private GroupTagEntityDAO groupTagEntityDAO;
	
	private static final int OID = 476;
	private static final String FILE_NAME_TEMPLE = "CustomExtractTagSummary_zillow.com_%DATE_INDEX%.csv";
	private static final String LOCAL_OUT_FILE_PATH = "/home/<USER>/476/tagSummaryExtactDaily/";
	private static final String FTP_PATH = "/home/<USER>/476/tagSummaryExtact/";
	private static final String OUT_TYPE = "csv";
	// discard this internal key ,replaced with the admin token for OID 476
//	private static final String TOKEN = "7453898e-bf59-11ed-9b11-ac1f6bc53227";
	private static final String TOKEN = "b0513a61-bbc3-4a32-ae49-cdf497c1d98b";
	private static final String API_ADDRESS_DEV = "*************:8183";
	private static final String API_ADDRESS_APP = ClarityDBAPIUtils.API_ENDPOINT_IBM_INTRANET;
	private static final String API_URL = "%ADDRESS%seoClarity/tagsummary?access_token=%TOKEN%&tagid=%TAGID%&limit=100&offset=0&sdate=%SDATE%&edate=%EDATE%&wt=%OUTTYPE%"; // remove &specOid=%OID%, use the token to get the query oid
	private static boolean isTest = false;
	private static final int MAX_TRY = 10;
	private static final int RE_TRY_WAIT_MILLIS_TIME = 60 * 1000;
	private static boolean isCheckProcess = false;
	
	private static final String ERROR_EMAIL_SUBJEDT = "ERROR With Daily Extract Tag Summary for Zillow";
//	private static final String ERROR_SEND_TO = "<EMAIL>";
	private static final String ERROR_SEND_TO = "<EMAIL>";
	
	private List<String> headerCols = new ArrayList<>();
	private CSVParser parser = new CSVParser(',');
	
	public ExtractAllTagSummaryForZillowDaily() {
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
	}
	
	public static void main(String[] args) {
		isCheckProcess = args.length == 0 ? false : Boolean.valueOf(args[0]);
		String sDate = null;
		String eDate = null;
		if (args.length == 2) {
			sDate = args[1];
			eDate = sDate;
		} else if (args.length > 2) {
			sDate = args[1];
			eDate = args[2];
		}
		SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
		if (StringUtils.isBlank(sDate)) {
			Calendar cal = Calendar.getInstance();
			cal.add(Calendar.DAY_OF_MONTH, -1);
			sDate = sf.format(cal.getTime());
		}
		if (StringUtils.isBlank(eDate)) {
			eDate = sDate;
		}
		System.out.println("=======Start to process extract, sDate:" + sDate + ", eDate:" + eDate + ", isTest:" + isTest + ", isCheckProcess:" + isCheckProcess);
		ExtractAllTagSummaryForZillowDaily ins = new ExtractAllTagSummaryForZillowDaily();
		if (isCheckProcess) {
			ins.processCheckStatus(sDate);
		} else {
			ins.process(sDate, eDate);
		}
	}
	
	// https://www.wrike.com/open.htm?id=789849059
	// only check current date process status
	// if current date file missing, re-process
	private void processCheckStatus(String processDate) {
		File folder = new File(LOCAL_OUT_FILE_PATH);
		if (!folder.exists()) {
			folder.mkdirs();
		}
		File[] files = folder.listFiles();
		Map<String, String> fileMap = createDaysForFiles(Arrays.asList(processDate));
		String currentProcessFile = fileMap.get(StringUtils.replace(processDate, "-", ""));
		String fileName = StringUtils.substringAfterLast(currentProcessFile, "/");
		System.out.println("===start to check process, processDate:" + processDate + ", folder:" + folder.getAbsolutePath() + ", currentProcessFile:" + fileName);
		File processedFile = null;
		for (File f : files) {
			System.out.println("===folder file:" + f.getName());
			if (f.getName().contains(fileName)) {
				System.out.println("===find processed file:" + f.getName());
				processedFile = f;
				break;
			}
		}
		if (processedFile != null) {
			System.out.println("=======Found processed file, delete file:" + processDate);
			processedFile.delete();
		} else {
			System.out.println("=======Not found processed file, would re-process, processDate:" + processDate);
			process(processDate, processDate);
		}
	}
	
	private void process(String startDate, String endDate) {
		// check folder
		File folder = new File(LOCAL_OUT_FILE_PATH);
		if (!folder.exists()) {
			folder.mkdirs();
		}
		// get all tags
		List<GroupTagEntity> tagList = groupTagEntityDAO.getTagEntityByType(OID, GroupTagEntity.TAG_TYPE_KEYWORD);
		List<Integer> tagIdList = new ArrayList<>();
		if (tagList != null) {
			tagIdList =tagList.stream().map(GroupTagEntity::getId).collect(Collectors.toList());
		}
		export(startDate, endDate, tagIdList);
	}
	
	private String createUrl(Integer tagId, Integer sDate, Integer eDate) {
		String url = StringUtils.replace(API_URL, "%TOKEN%", TOKEN);
		url = StringUtils.replace(url, "%TAGID%", tagId.toString());
		url = StringUtils.replace(url, "%SDATE%", sDate.toString());
		url = StringUtils.replace(url, "%EDATE%", eDate.toString());
		url = StringUtils.replace(url, "%OUTTYPE%", OUT_TYPE);
//		url = StringUtils.replace(url, "%OID%", String.valueOf(OID));
		if (isTest) {
			url = StringUtils.replace(url, "%ADDRESS%", API_ADDRESS_DEV);
		} else {
			url = StringUtils.replace(url, "%ADDRESS%", API_ADDRESS_APP);
		}
		return url;
	}
	
	private void export(String startDate, String endDate, List<Integer> tagList) {
		List<String> exportInfoList = new ArrayList<>();
		boolean isComplete = false;
		try {
			// get file names
			// each day a file
			List<String> dateList = ClarityDBUtils.getTrendDateList(StringUtils.replace(startDate, "-", ""), StringUtils.replace(endDate, "-", ""), null);
			Map<String, String> fileMap = createDaysForFiles(dateList);
			System.out.println("===created fileMap:" + fileMap);
			Map<String, List<String>> monthMap = new LinkedHashMap<>();
			dateList.stream().forEach(x -> {
				String month = StringUtils.replace(x, "-", "").subSequence(0, 6).toString();
				if (monthMap.containsKey(month)) {
					monthMap.get(month).add(x);
				} else {
					List<String> list = new ArrayList<>();
					list.add(x);
					monthMap.put(month, list);
				}
			});
			
			// query api and export to files
			for (String month : monthMap.keySet()) {
				List<String> dateRangeList = monthMap.get(month);
				String sDate = dateRangeList.get(0);
				String eDate = dateRangeList.get(dateRangeList.size() - 1);
				// create files for each day
				System.out.println("==============Current process month:" + month + ", sDate:" + sDate + ", eDate:" + eDate + ", tagList:" + tagList.size());
				Map<String, List<String[]>> dateRowMap = new LinkedHashMap<String, List<String[]>>();
				int tagIdICnt = 0;
				for (Integer tagId : tagList) {
					tagIdICnt++;
					long a = System.currentTimeMillis();
					String queryUrl = createUrl(tagId, Integer.parseInt(StringUtils.replace(sDate, "-", "")), Integer.parseInt(StringUtils.replace(eDate, "-", "")));
					String response = getResponse(queryUrl);
					long b = System.currentTimeMillis();
					System.out.println("===queryDates:" + sDate + "-" + eDate +  ", tagId:" + tagId + ", tagIdICnt:" + tagIdICnt + "/" + tagList.size() + ", cost:" + (b - a));
//					System.out.println("=queryUrl:" + queryUrl);
					boolean isValid = resposneValid(response);
					if (isTest || !isValid) {
						System.out.println("=response:" + response);
					}
					Assert.hasLength(response, "Response is empty! tagId:" + tagId + ", response:" + response);
					if (isValid) {
						parseLines(response, dateRowMap);
					} else {
						System.out.println("===Warring! tag doesn't has summary.TagId:" + tagId);
					}
				}
				System.out.println("===month:" + month + ", dateRowMap:" + dateRowMap.size());
				if (dateRowMap.size() > 0) {
					exportInfoList.addAll(exportToFile(dateRowMap, fileMap));
				}
			}
			
			// valid files
			List<String> emptyFiles = new ArrayList<>(0);
			for (String fileName : fileMap.values()) {
				File file = new File(fileName);
				if (!file.exists() || file.length() == 0) {
					emptyFiles.add(fileName);
					String info = "Find empty file, skip file:" + fileName;
					exportInfoList.add("========================");
					exportInfoList.add(info);
					System.out.println(info);
				}
			}
			// transfer files to FTP
			List<String> transferFiles = new ArrayList<>(fileMap.values());
			transferFiles.removeAll(emptyFiles);
			for (String fileName : transferFiles) {
				FTPUtils.saveFileToFTP(OID, fileName, FTP_PATH);
				// only check process will delete the local file
				if (!isTest && isCheckProcess) {
					try {
						new File(fileName).delete();
					} catch (Exception e) {
						System.out.println("===delete file failed. file:" + fileName);
						e.printStackTrace();
					}
				}
			}
			
			isComplete = true;
		} catch (Exception e) {
			String info = "===Process export failed. startDate:" + startDate + ", endDate:" + endDate;
			exportInfoList.add("==================");
			exportInfoList.add(info + ", msg:" + e.getMessage());
			System.out.println(info);
			e.printStackTrace();
		}
		
		if (!isComplete) {
			sendAlertMail(startDate, endDate, exportInfoList);
		}
	}
	
	private List<String> exportToFile(Map<String, List<String[]>> dateRowMap, Map<String, String> fileMap) {
		List<String> exportInfoList = new ArrayList<>();
		CsvWriteConfig config = new CsvWriteConfig();
		config.setAlwaysDelimitText(true);
		for (String date : dateRowMap.keySet()) {
			List<String[]> rows = dateRowMap.get(date);
			String fileName = fileMap.get(date);
			String info = "export file, date:" + date + ", rows:" + (rows == null ? 0 : rows.size()) + ", fileName:" + fileName;
			System.out.println(info);
			exportInfoList.add(info);
			if (rows != null && rows.size() > 0) {
				try {
					CsvWriter csvWr = new CsvWriter(new File(fileName), Charset.forName("UTF-8"), true, config);
					csvWr.write(headerCols.toArray(new String[headerCols.size()]));
					csvWr.write(rows);
					csvWr.close();
				} catch (Exception e) {
					exportInfoList.add("===error, date export failed, date:" + date + ", msg:" + e.getMessage());
					e.printStackTrace();
				}
			}
		}
		return exportInfoList;
	}
	
	private void sendAlertMail(String sDate, String eDate, List<String> infoList) {
		String errorMsg = "Faild to process daily extract for zillow tag summary. sDate:" + sDate + ", eDate:" + eDate + ", class:" + this.getClass().getName();
		ZeptoMailSenderComponent.sendEmailReport("All", new Date(), ERROR_EMAIL_SUBJEDT, infoList, errorMsg, ERROR_SEND_TO);
	}
	
	private String getResponse(String queryUrl) {
		String response = null;
		int tryCnt = 0;
		do {
			tryCnt++;
			try {
				response = ClarityDBUtils.getWithStatus(queryUrl);
				if (StringUtils.isNotBlank(response) || tryCnt >= MAX_TRY) {
					break;
				} else {
					System.out.println("===Unvalid response, tryCnt:" + tryCnt + ", queryUrl:" + queryUrl + ", response:" + response);
					Thread.sleep(RE_TRY_WAIT_MILLIS_TIME);
				}
			} catch (Exception e) {
				System.out.println("===query failed. queryUrl:" + queryUrl + ", response:" + response);
				e.printStackTrace();
				if(tryCnt >= MAX_TRY) {
					break;
				}
			}
		} while (true);
		return response;
	}
	
	private boolean resposneValid(String response) {
		if (StringUtils.isBlank(response)) {
			return false;
		}
		// check header
		String[] rows = StringUtils.split(response, '\n');
		if (rows.length == 1) {
			return false;
		}
		return true;
	}
	
	// tagName,tagType,logDate,totalKeywords,totalKeywordsWithout101...
	private void parseLines(String response, Map<String, List<String[]>> dateRowMap) throws Exception{
		String[] rows = StringUtils.split(response, '\n');
		List<String> lines = Arrays.asList(rows).stream().filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList());
		
		// remove header
		String header = lines.remove(0);
		
		// since 2022-01, csv extract added a new column: [tagName,geo_location,tagType,logDate,totalKeywords,totalKeywordsWithout101...]
		// remove the geo_location column
		String geoLocationColName = "geo_location";
		int geoLocationColIdx = Arrays.asList(parser.parseLine(header)).indexOf(geoLocationColName);
		
		// init file header
		if (headerCols.size() == 0) {
			headerCols.addAll(Arrays.asList(parser.parseLine(header)));
			// remove the geo_location column
			if (geoLocationColIdx >= 0) {
				headerCols.remove(geoLocationColName);
			}
		}
		
		// get the logDate index
		String logDateColName = "logDate";
		int logDateIdx = headerCols.indexOf(logDateColName);
		if (logDateIdx < 0) {
			throw new Exception("Can not find the \"logDate\" header, header:" + headerCols);
		}
		
		int lineIdx = 1;
		for (String line : lines) {
			lineIdx++;
			try {
				String[] cols = parser.parseLine(line);
				List<String> colList = new ArrayList<String>(Arrays.asList(cols));
				// remove geo_location column
				if (geoLocationColIdx >= 0) {
					colList.remove(geoLocationColIdx);
				}
				cols = colList.toArray(new String[0]);
				String logDate= cols[logDateIdx];
				if (dateRowMap.containsKey(logDate)) {
					dateRowMap.get(logDate).add(cols);
				} else {
					List<String[]> list = new ArrayList<>();
					list.add(cols);
					dateRowMap.put(logDate, list);
				}
			} catch (Exception e) {
				System.out.println("=parse line failed. lineIdx:" + lineIdx + ", line:" + line);
				throw new Exception(e);
			}
		}
	}
	
	private Map<String, String> createDaysForFiles(List<String> dateList) {
		Map<String, String> fileMap = new HashMap<>();
		dateList.stream().forEach(x -> {
			String idx = StringUtils.replace(x, "-", "");
			String fileName = LOCAL_OUT_FILE_PATH + StringUtils.replace(FILE_NAME_TEMPLE, "%DATE_INDEX%", idx);
			fileMap.put(idx, fileName);
		});
		return fileMap;
	}

}
