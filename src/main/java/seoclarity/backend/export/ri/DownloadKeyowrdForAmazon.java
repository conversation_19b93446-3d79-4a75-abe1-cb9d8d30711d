package seoclarity.backend.export.ri;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.auth.PropertiesCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import scala.Int;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.ClDailySeoAdsEntityDao;
import seoclarity.backend.dao.clickhouse.RIDailyRankingService;
import seoclarity.backend.dao.clickhouse.ri.ClarityDBRIDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.export.ExtractTop100TitleMeta;
import seoclarity.backend.export.TopXKeywordRankingExportDailyAmazon;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

public class DownloadKeyowrdForAmazon {
    private final static Map<Integer, String> DOMAIN_MAP = new HashMap<Integer, String>();

    static {
        DOMAIN_MAP.put(9452, "amazon.ae");
        DOMAIN_MAP.put(9453, "amazon.ae");
        DOMAIN_MAP.put(4766,"amazon.ca");
        DOMAIN_MAP.put(4721,"amazon.co.jp");
        DOMAIN_MAP.put(4348,"amazon.co.uk");
        DOMAIN_MAP.put(4355,"amazon.com");
        DOMAIN_MAP.put(4344,"amazon.de");
        DOMAIN_MAP.put(4345,"amazon.es");
        DOMAIN_MAP.put(4346,"amazon.it -");
        DOMAIN_MAP.put(7195,"www.amazon.com.au");
        DOMAIN_MAP.put(1901,"www.amazon.com.br");
        DOMAIN_MAP.put(7196,"www.amazon.com.mx");
        DOMAIN_MAP.put(9454,"www.amazon.com.tr");
        DOMAIN_MAP.put(4347,"www.amazon.fr");
        DOMAIN_MAP.put(5732,"www.amazon.in");
        DOMAIN_MAP.put(9557,"www.amazon.sa");
        DOMAIN_MAP.put(9558,"www.amazon.sa/-/en/");
        DOMAIN_MAP.put(9451,"www.amazon.sg");
    }
    static AmazonS3 s3client = null;
    public static final String[] dateList = new String[]{
            "2021-05-01",
            "2021-05-02",
            "2021-05-03",
            "2021-05-04",
            "2021-05-05",
            "2021-05-06",
            "2021-05-07",
            "2021-05-08",
            "2021-05-09",
            "2021-05-10",
            "2021-05-11",
            "2021-05-12",
            "2021-05-13",
            "2021-05-14",
            "2021-05-15",
            "2021-05-16",
            "2021-05-17",
            "2021-05-18",
            "2021-05-19",
            "2021-05-20",
            "2021-05-21",
            "2021-05-22",
            "2021-05-23",
            "2021-05-24",
            "2021-05-25",
            "2021-05-26",
            "2021-05-27",
            "2021-05-28",
            "2021-05-29",
            "2021-05-30",
            "2021-05-31",
            "2021-06-01",
            "2021-06-02",
            "2021-06-03",
            "2021-06-04",
            "2021-06-05",
            "2021-06-06",
            "2021-06-07",
            "2021-06-08",
            "2021-06-09",
            "2021-06-10",
            "2021-06-11",
            "2021-06-12",
            "2021-06-13",
            "2021-06-14",
            "2021-06-15",
            "2021-06-16",
            "2021-06-17",
            "2021-06-18",
            "2021-06-19",
            "2021-06-20",
            "2021-06-21",
            "2021-06-22",
            "2021-06-23",
            "2021-06-24",
            "2021-06-25",
            "2021-06-26",
            "2021-06-27",
            "2021-06-28",
            "2021-06-29",
            "2021-06-30",
            "2021-07-01",
            "2021-07-02",
            "2021-07-03",
            "2021-07-04",
            "2021-07-05",
            "2021-07-06",
            "2021-07-07",
            "2021-07-08",
            "2021-07-09",
            "2021-07-10",
            "2021-07-11",
            "2021-07-12",
            "2021-07-13",
            "2021-07-14",
            "2021-07-15",
            "2021-07-16",
            "2021-07-17",
            "2021-07-18"
    };
//    public static final String[] domainIds = new String[]{
//            "9452",
//            "9453",
//            "4766",
//            "4721",
//            "4348",
//            "4355",
//            "4344",
//            "4345",
//            "4346",
//            "7195",
//            "1901",
//            "7196",
//            "9454",
//            "4347",
//            "5732",
//            "9557",
//            "9558",
//            "9451"
//    };
    static CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader(
            "Date",
            "Keyword",
            "Ranking URL",
            "Rank (Web Rank)",
            "URL Type",
            "Search Volume"
    )
            .withDelimiter(',');
    private static boolean isUseHotDB = true;
    private static SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
    private RIDailyRankingService riDailyRankingService;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
//    private ClarityDBRIDao clarityDBNewRIDao;

    private ClDailySeoAdsEntityDao clarityDBNewRIDao;
    public DownloadKeyowrdForAmazon() {
        riDailyRankingService = new RIDailyRankingService(isUseHotDB);
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        clarityDBNewRIDao = SpringBeanFactory.getBean("clDailySeoAdsEntityDao");
    }


    public static void main(String[] args) {
        DownloadKeyowrdForAmazon s3class = new DownloadKeyowrdForAmazon();
        try {
            System.out.println(" ========================== gogogo ===============================");
            String fileName = s3class.selectData(DOMAIN_MAP);
            System.out.println(" success  : " + fileName);
        } catch (AmazonServiceException ase) {
            System.out.println("Caught an AmazonServiceException, which " + "means your request made it "
                    + "to Amazon S3, but was rejected with an error response" + " for some reason.");
            System.out.println("Error Message:    " + ase.getMessage());
            System.out.println("HTTP Status Code: " + ase.getStatusCode());
            System.out.println("AWS Error Code:   " + ase.getErrorCode());
            System.out.println("Error Type:       " + ase.getErrorType());
            System.out.println("Request ID:       " + ase.getRequestId());
        } catch (AmazonClientException ace) {
            System.out.println("Caught an AmazonClientException, which " + "means the client encountered "
                    + "an internal error while trying to " + "communicate with S3, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message: " + ace.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private String selectData(Map<Integer, String> domainMap) throws Exception {
        StringBuffer filename = new StringBuffer();
        filename.append(" filename : ");
        for (Integer str : domainMap.keySet()) {
            Integer domainid = str;
            OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(domainid);
            int engine = ScKeywordRankManager.getSearchEngineId(domainEntity);
            String engineName = ScKeywordRankManager.getSearchEngineNameByEngineId(engine,null);
            int language = ScKeywordRankManager.getSearchLanguageId(domainEntity);
            boolean isMobile = domainEntity.isMobileDomain();
            String dOrm = isMobile ? "mobile" : "desktop";
            String domainName = domainEntity.getName();
            String rootDomain = ClarityDBUtils.getRootDomain(DOMAIN_MAP.get(domainid));
            String reverseDomain = StringUtils.reverseDelimited(DOMAIN_MAP.get(domainid), '.');
            String reverseRootDomain = StringUtils.reverseDelimited(rootDomain, '.');
            if (dOrm.equals("mobile")) {
                for (String date : dateList) {
                    String detilTable = clDailyRankingEntityDao.getTableForHot(date, date, engine, language,
                            isMobile, ClarityDBUtils.RANK_DETAIL_TYPE);

                    System.out.println(
                            "domainid : " + domainid
                                    + "domainName : " + domainName
                                    + " engine : " + engineName
                                    + "language :" + language
                                    + "dOrm : " + dOrm
                                    + " rootDomain: " + rootDomain
                                    + "detilTable :" + detilTable);
                    StringBuffer sql = new StringBuffer();
                    sql.append(" select ");
                    sql.append("ranking_date as date, keyword_name as keywordName,url ,web_rank as rank,type,avg_search_volume as searchVolume ");
                    sql.append(" from ");
                    sql.append(" test_fix_amazon_local_d_ranking_detail_intl ");
                    sql.append(detilTable);
                    sql.append(" where 1 = 1 and own_domain_id = " + domainid + " and engine_id = " + engine + " and language_id = " + language);
                    if (StringUtils.endsWithIgnoreCase(reverseDomain, reverseRootDomain)) {
                        sql.append(" AND (root_domain_reverse = '" + reverseRootDomain + "') ");
                        sql.append(" AND (hrrd = 1) ");
                    } else {
                        sql.append(" AND (root_domain_reverse = '" + reverseRootDomain + "') AND (domain_reverse = '" + reverseDomain + "') ");
                        sql.append(" AND (hrd = 1)");
                    }
                    sql.append(" and location_id = 0 and sign = 1 ");
                    sql.append(" and ranking_date = '" + date + "' ");
                    sql.append(" AND (dictGetUInt32('file_dic_tracked_keyword_without_ses', 'own_domain_id'," +
                            "(toUInt32(own_domain_id), toUInt64(URLHash(keyword_name)), " +
                            "dictGetUInt32('geo_master_mapping', 'geoMasterId', tuple(location_id)))) > 0)");
                    sql.append("  AND ((attrs.value[indexOf(attrs.key, 'is_price')]) = '1')");
                    sql.append(" order by ranking_date");
                    System.out.println(" sql :  " + sql.toString());
                    List<Map<String, Object>> dataList = clarityDBNewRIDao.queryListMap(sql.toString());
                    String response = extractData(dataList, domainName, engineName, dOrm, date, domainid);
                    filename.append(response);
                }
            }
        }

        return filename.toString();
    }


    private static String extractData(List<Map<String, Object>> extractData, String domainName, String engine, String dOrm,String date,Integer domainId) throws Exception {
        File file = new File("newExportv4/"+domainName + "_" + engine + "_" + date + "_Price_Schema_Report_" + dOrm + ".txt");
        CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file)), csvFullFormat);
        System.out.println("name : "+file.getName() +" size : " + extractData.size());
        for (Map<String, Object> objectMap : extractData) {
            String type = seoclarity.backend.utils.RankTypeManager.getRankType(String.valueOf(objectMap.get("url")), NumberUtils.toInt(String.valueOf(objectMap.get("type"))));
//				keyword_name,url, web_rank, avg_search_volume,domain_reverse,label, meta
            csvPrinter.printRecord(
                    objectMap.get("date"),
                    objectMap.get("keywordName"),
                    objectMap.get("url"),
                    objectMap.get("rank"),
                    type,
                    objectMap.get("searchVolume")
            );
        }
        csvPrinter.flush();
        csvPrinter.close();
//        s3client.putObject(new PutObjectRequest("aaa-seoclarity", "SERP-PriceAnalysis/", file).withCannedAcl(CannedAccessControlList.BucketOwnerFullControl));
//        if (extractData.size()>0){
        return "-("+domainId+"_"+date.substring(5,date.length())+"_"+extractData.size()+")";
//        }else {
//            return "";
//        }

    }

}
