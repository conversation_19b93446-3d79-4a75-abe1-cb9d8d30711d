package seoclarity.backend.export.ri;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;

import com.csvreader.CsvWriter;

import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.dao.actonia.CompetitorEntityDAO;
import seoclarity.backend.dao.actonia.GrouptagCompetitorRelDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.RIDailyRankingService;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.actonia.CompetitorEntity;
import seoclarity.backend.entity.actonia.GrouptagCompetitorRel;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;
import seoclarity.backend.utils.pgp.PgpUtils;

/**
 * <AUTHOR>
 * @date 2020-10-01
 * @path seoclarity.backend.export.ri.KeywordExportForLowesWeekly
 * export all keywords for 7135 lowes.com, same as keywordResourceV2 api
 */
public class KeywordExportForLowesWeekly {
	private static final int OID = 7135;
	
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private GrouptagCompetitorRelDAO grouptagCompetitorRelDAO;
	private CompetitorEntityDAO competitorEntityDAO;
	private RIDailyRankingService riDailyRankingService;
	
	private static final int COLD_MIN_DB_DATE = 20200401;
	private static boolean isUseHotDB = false;
	private static String processDate = null;
	private static String exportPath = "";
	private static boolean isTransferFTP = true;
	private static String EX_FILE_PRIFIX = "%oid%_keywordExport_%desktop%_%processDate%";
	private static String EX_FILE_SUFFIX = ".csv";
//	private static int tag_id = 7108878;//https://www.wrike.com/open.htm?id=878867590
	private static int tag_id = 7503835;//https://www.wrike.com/open.htm?id=1159085342
	private static final String[] HEADER = new String[] {
			"name", "date", "highestTrueRank", "highestWebRank", "highestRankUrl", 
			"highestLocalRank", "highestNewsRank", "highestImageRank", "highestVideoRank", "monthlySearchVolume",
			"competitorName", "landingPage", "rank", "fps", "author_rank", "highest_star_rank"
	};
	private  static LogglyVO logglyVO = new LogglyVO();
	private static int totalCnt = 0;
	private static String device ="m";
	private static String pubKeyFile = "/home/<USER>/source/emailAlertSchedule/clarity-backend-scripts/7135_seo_public_key.key";// 加密key
	
	private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
	
	public KeywordExportForLowesWeekly() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		grouptagCompetitorRelDAO = SpringBeanFactory.getBean("grouptagCompetitorRelDAO");
		competitorEntityDAO = SpringBeanFactory.getBean("competitorEntityDAO");
		riDailyRankingService = new RIDailyRankingService(isUseHotDB);
	}
	
	public static void main(String[] args) {
		if (args.length >= 1) {
			processDate = args[0];
		}
		if (args.length >= 2) {
			exportPath = args[1];
			isTransferFTP = false;
		}
		System.out.println("processDate:" +processDate + ", exportPath:" + exportPath + ", isTransferFTP:" + isTransferFTP);
		
		KeywordExportForLowesWeekly ins = new KeywordExportForLowesWeekly();
		try {
			String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
			ins.process();
			logglyVO.setStatus(LogglyVO.STATUS_OK);
			logglyVO.setsTime(stTime);
			logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
			logglyVO.setRows(String.valueOf(totalCnt));
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

		} catch (Exception e) {
			e.printStackTrace();
			logglyVO.setStatus(LogglyVO.STATUS_NG);
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
		}
	}
	
	private void process() throws Exception{
		OwnDomainEntity domain = ownDomainEntityDAO.getById(OID);

		String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);


		logglyVO.setoId(String.valueOf(OID));
		logglyVO.setName("KeywordExportForLowesWeekly");
		logglyVO.setDevice(domain.isMobileDomain()?"m":"d");

		logglyVO.setpDate(pDate);
		List<String> groupList = new ArrayList<>();
		groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
		logglyVO.setGroups(groupList);

		boolean isBroadMatch = domain.isBroadMatch();
		String domainName = domain.getName();
		if (isBroadMatch) {
			domainName = StringUtils.removeStartIgnoreCase(domainName, "www.");
		}
		int engineId = ScKeywordRankManager.getSearchEngineId(domain);
		int languageId = ScKeywordRankManager.getSearchLanguageId(domain);
		if (StringUtils.isBlank(processDate)) {
			//extract last week(process on Sunday) https://www.wrike.com/open.htm?id=952183313
			processDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -7), FormatUtils.DATE_PATTERN_2);
		}
		List<String[]> competitorDomainList = new ArrayList<String[]>();
		List<GrouptagCompetitorRel> grouptagCompetitorRels = grouptagCompetitorRelDAO.getAllUniCompetitorByGroupTagAndOwnDomainIdForDomainLevel(OID);
		if (grouptagCompetitorRels != null && grouptagCompetitorRels.size() > 0) {
             for (GrouptagCompetitorRel grouptagCompetitorRel : grouptagCompetitorRels) {
                 CompetitorEntity competitorEntity = competitorEntityDAO.findById(grouptagCompetitorRel.getCompetitorId());
                 if (competitorEntity != null) {
                     String rootDomainReverse = StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(competitorEntity.getDomain()), '.');
                     String domainReverse = StringUtils.reverseDelimited(competitorEntity.getDomain(), '.');
                     competitorDomainList.add(new String[]{
                             rootDomainReverse, rootDomainReverse.equals(domainReverse) ? null : domainReverse
                     });
                 }
             }
		 }
		
		System.out.println("=OID:" + OID + ", domainName:" + domainName + ", isBroadMatch:" + isBroadMatch + ", engineId:" + engineId + ", languageId:" + languageId 
				+ ", processDate:" + processDate + ", competitorDomainList:" + competitorDomainList.size());
		for (String[] competitor : competitorDomainList) {
			System.out.println("=competitor:" + competitor[0] + ", " + (competitor.length > 1 ? competitor[1] : competitor[0]));
		}
		
		if (processDate.contains(",")) {
			String sta = StringUtils.split(processDate, ',')[0];
			String end = StringUtils.split(processDate, ',')[1];
			List<String> dateList = ClarityDBUtils.getAllWeeklyDateListByDateRange(Integer.valueOf(StringUtils.replace(sta, "-", "")), Integer.valueOf(StringUtils.replace(end, "-", "")), Calendar.SUNDAY);
			System.out.println("===dateList:" + dateList);

			for (String date : dateList) {
				processDate = date;
				if (COLD_MIN_DB_DATE > Integer.valueOf(StringUtils.replace(processDate, "-", ""))) {
					isUseHotDB = true;
				} else {
					isUseHotDB = false;
				}
				riDailyRankingService = new RIDailyRankingService(isUseHotDB);
				try {
					// desktop 
					processExport(OID, engineId, languageId, processDate, domainName, competitorDomainList, false);
					// mobile
					processExport(OID, engineId, languageId, processDate, domainName, competitorDomainList, true);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		} else {
			try {
				// desktop 
				processExport(OID, engineId, languageId, processDate, domainName, competitorDomainList, false);
				// mobile
				processExport(OID, engineId, languageId, processDate, domainName, competitorDomainList, true);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
		
	
	private void processExport(int oid, int engine, int language, String processDate, String domainName, List<String[]> competitorDomainList, boolean isMobile) throws Exception{
		String prefix = new String(EX_FILE_PRIFIX);
		prefix = StringUtils.replace(prefix, "%oid%", String.valueOf(oid));
		prefix = StringUtils.replace(prefix, "%desktop%", isMobile ? "mobile" : "desktop");
		prefix = StringUtils.replace(prefix, "%processDate%", processDate);
		if (tag_id > 0) {
			prefix += "_tag_" + tag_id;
		}
		
		String file = StringUtils.isBlank(exportPath) ? File.createTempFile(prefix, null).getAbsolutePath() : (exportPath + "/" + prefix);
		
		FileOutputStream fo = new FileOutputStream(new File(file), true);
		CsvWriter wr = new CsvWriter(fo, ',', Charset.forName("UTF-8"));
		
		wr.writeRecord(HEADER);
		wr.flush();
		
		System.out.println("===Export to file:" + file);
		try {
			// get keywords
			Map<String, String[]> kwNameMap = new HashMap<String, String[]>();
			
			String kwSql = createKeywordSql(oid, processDate, engine, language, isMobile, domainName);
			System.out.println("====kwSql:" + kwSql);
			List<Map<String, Object>> keywordsRes = riDailyRankingService.queryForAll(kwSql);
			
			System.out.println("=OID:" + oid + ", processDate:" + processDate + ", isMobile:" + isMobile +  ", keywordsRes:" + keywordsRes.size());
			
			if (keywordsRes != null && keywordsRes.size() > 0) {
				int pageSize = 10;
				List<String> queryKidList = new ArrayList<String>();
				int total = 0;
				for (Map<String, Object> obj : keywordsRes) {
	                String keyword_rankcheck_id = obj.get("keyword_rankcheck_id").toString();
	                String location_id = obj.get("location_id").toString();
	                String keyword_name = obj.get("keyword_name").toString();
	                String avg_search_volume = obj.get("avg_search_volume").toString();

	                String key = keyword_rankcheck_id + "-" + location_id;
	                kwNameMap.put(key, new String[]{keyword_name, avg_search_volume});

	                queryKidList.add(keyword_rankcheck_id);
	                if (queryKidList.size() >= pageSize) {
	                	total += queryKidList.size();
	                	String sql = createRankingDetailsSql(oid, processDate, engine, language, isMobile, domainName, competitorDomainList, queryKidList);
	                	List<Map<String, Object>> resultList = riDailyRankingService.queryForAll(sql);
	                	mergeResult(resultList, queryKidList, kwNameMap, competitorDomainList, Integer.valueOf(StringUtils.replace(processDate, "-", "")), wr);
	                	queryKidList.clear();
	                	System.out.println("===total process:" + total);
	                }
	            }
				
				if (queryKidList.size() > 0) {
					String sql = createRankingDetailsSql(oid, processDate, engine, language, isMobile, domainName, competitorDomainList, queryKidList);
	            	List<Map<String, Object>> resultList = riDailyRankingService.queryForAll(sql);
	            	mergeResult(resultList, queryKidList, kwNameMap, competitorDomainList, Integer.valueOf(StringUtils.replace(processDate, "-", "")), wr);
	            	queryKidList.clear();
	            	System.out.println("===total process:" + total);
				}

				totalCnt = total;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		wr.close();
		fo.close();
		System.out.println("filename : "+file + "filepath : "+ "/home/<USER>/" + OID);
		System.out.println("prefix:"+prefix);
		System.out.println("exportPath : "+ exportPath);

		String FTPfileName = StringUtils.isBlank(exportPath) ? getfileAbsolutePath(prefix,EX_FILE_SUFFIX) : (exportPath + "/" + prefix + EX_FILE_SUFFIX);
		System.out.println("FTPfileName :" + FTPfileName);

		//PGP Encryption https://www.wrike.com/open.htm?id=1171949448
		String outPutEncryptedFIlePath = new File(file).getAbsolutePath() + EX_FILE_SUFFIX;
		System.out.println("outPutEncryptedFIlePath:"+outPutEncryptedFIlePath);
		PgpUtils.encrypt(pubKeyFile, outPutEncryptedFIlePath, file);

//		FTPUtils.saveFileToFTP(OID, file, "/home/<USER>/" + OID,prefix+EX_FILE_SUFFIX);
		FTPUtils.saveFileToFTP(OID, outPutEncryptedFIlePath, "/home/<USER>/" + OID,prefix+EX_FILE_SUFFIX);
	}

	private String getfileAbsolutePath(String prefix, String exFileSuffix) {
		String name = prefix + exFileSuffix;
		File f = new File(name);
		return f.getAbsolutePath();
	}

	private void mergeResult(List<Map<String, Object>> resultList, List<String> kidList, Map<String, String[]> kwNameMap, List<String[]> competitorDomainList, int queryIndex, CsvWriter wr) {
		Map<String, List> keywordResultMap = new HashMap<>();
		for (Map<String, Object> map : resultList) {
			String kid = map.get("keyword_rankcheck_id").toString();
			String locationId = map.get("location_id").toString();
			String key = kid + "-" + locationId;
			
			if (keywordResultMap.containsKey(key)) {
				keywordResultMap.get(key).add(map);
			} else {
				List list = new ArrayList<>();
				list.add(map);
				keywordResultMap.put(key, list);
			}
		}
		List<Map<String, Object>> parsedList = new ArrayList<>();
		for (String kid : kidList) {
			String key = kid + "-" + 0;
			List<Map<String, Object>> resultMap = keywordResultMap.get(key);
			Map<String, Object> parsedMap = parseValue(key, kwNameMap, resultMap, competitorDomainList, queryIndex);
			parsedList.add(parsedMap);
		}
		
		// convert to csv
		
		List<String[]> rows = new ArrayList<>();
		for (Map<String, Object> map : parsedList) {
			String kw = RankCheckUtils.decoderString(map.get("name").toString());
			String date = map.get("date").toString();
			String highestTrueRank = map.get("highestTrueRank").toString();
			String highestWebRank = map.get("highestWebRank").toString();
			String highestRankUrl = map.get("highestRankUrl").toString();
			if (StringUtils.isBlank(highestRankUrl) ) {
				highestRankUrl = "-";
			}
			
			String highestLocalRank = map.get("highestLocalRank").toString();
			String highestNewsRank = map.get("highestNewsRank").toString();
			String highestImageRank = map.get("highestImageRank").toString();
			String highestVideoRank = map.get("highestVideoRank").toString();
			
			String monthlySearchVolume = map.get("monthlySearchVolume").toString();
			
			if (competitorDomainList != null && competitorDomainList.size() > 0) {
				Map<String, String[]> competitorList = (Map) map.get("competitorRanMap");
				for (String[] competitorDomain : competitorDomainList) {
					String competitorName = competitorDomain[1] == null ? competitorDomain[0] : competitorDomain[1];
                    String competitorDomainName = StringUtils.reverseDelimited(competitorName, '.');
                    String landingPage = "";
                    String rank = "101";
                    if (competitorList.containsKey(competitorName)) {
                        String[] val = competitorList.get(competitorName);
                        landingPage = val[0];
                        rank = val[1];
                    }
                    if (StringUtils.isBlank(landingPage)) {
                        landingPage = "http://" + competitorDomainName + "/";
                    }
                    rows.add(new String[] {
                    		kw, date, highestTrueRank, highestWebRank, highestRankUrl,
                    		highestLocalRank, highestNewsRank, highestImageRank, highestVideoRank,
                    		monthlySearchVolume,
                    		competitorDomainName,
                    		landingPage,
                    		rank,
                    		"null", "null", "null"
                    });
				}
			} else {
				rows.add(new String[] {
                		kw, date, highestTrueRank, highestWebRank, highestRankUrl,
                		highestLocalRank, highestNewsRank, highestImageRank, highestVideoRank,
                		monthlySearchVolume,
                });
			}
		}
		// export to file
		for (String[] row : rows) {
			try {
				wr.writeRecord(row);
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		wr.flush();
	}
	
	private Map<String, Object> parseValue(String key, Map<String, String[]> kwNameMap, List<Map<String, Object>> resultList, List<String[]> competitorDomainList, int queryIndex) {
		String kw = kwNameMap.get(key)[0];
		String sv = kwNameMap.get(key)[1];

		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("name", kw);
		resultMap.put("monthlySearchVolume", sv);
		resultMap.put("date", queryIndex);
		parseRow(competitorDomainList, resultList, null, queryIndex, resultMap);
		return resultMap;
	}
	
	private void parseRow(List<String[]> competitorList, List<Map<String, Object>> resultList, List<Map<String, Object>> ratingResultList, int dateIdx, Map<String, Object> resultMap) {
        int highestTrueRank = 101;
        int highestWebRank = 101;
        String highestRankUrl = "";
        int highestLocalRank = 101;
        int highestNewsRank = 101;
        int highestImageRank = 101;
        int highestVideoRank = 101;
        int highestStarRank = 101;

        Map<String, String[]> competitorRanMap = new LinkedHashMap<String, String[]>();

        if (resultList != null) {
	        for (int i = 0; i < resultList.size(); i++) {
	            Map<String, Object> map = resultList.get(i);
	            
	            int true_rank = Integer.valueOf(map.get("true_rank").toString());
	            int web_rank = Integer.valueOf(map.get("web_rank").toString());
	            int type = Integer.valueOf(map.get("type").toString());
	            String domain_reverse = map.get("domain_reverse").toString();
	
//	            String rating = map.get("rating").toString();
	
	            String ranking_date = map.get("ranking_date").toString();
	            int rowDateIdx = Integer.valueOf(StringUtils.replace(ranking_date, "-", ""));
	            String url = map.get("url").toString();
	            int ownDomainFlg = Integer.valueOf(map.get("ownDomainFlg").toString());
	
	            if (rowDateIdx > dateIdx) {
	                break;
	            } else if (rowDateIdx != dateIdx) {
	                continue;
	            }
	
	            if (type == KeywordRankEntityVO.TYPE_ADDRESS&& highestLocalRank > true_rank) {
	                highestLocalRank = true_rank;
	            }
	            if (type == KeywordRankEntityVO.TYPE_NEWS && highestNewsRank > true_rank) {
	                highestNewsRank = true_rank;
	            }
	            if (type == KeywordRankEntityVO.TYPE_IMGAGE && highestImageRank > true_rank) {
	                highestImageRank = true_rank;
	            }
	            if (type == KeywordRankEntityVO.TYPE_VIDEO && highestVideoRank > true_rank) {
	                highestVideoRank = true_rank;
	            }
//	            if (rating.equals("1") && highestStarRank > true_rank) {
//	                highestStarRank = true_rank;
//	            }
	
	            // for own domain highest rank
	            if (ownDomainFlg == 1) {
	                if (highestTrueRank  > true_rank) {
	                    highestTrueRank = true_rank;
	                }
	                if (highestWebRank  > web_rank) {
	                    highestWebRank = web_rank;
	                }
	                if (StringUtils.isBlank(highestRankUrl)) {
	                    highestRankUrl = url;
	                }
	            }
	
	            // for competitor highest rank
	            for (String[] competitorDomain : competitorList) {
	                if (competitorDomain[1] != null && !competitorRanMap.containsKey(competitorDomain[1]) && domain_reverse.equalsIgnoreCase(competitorDomain[1])) {
	                    competitorRanMap.put(competitorDomain[1], new String[]{
	                        url, String.valueOf(true_rank)
	                    });
	                } else if (!competitorRanMap.containsKey(competitorDomain[0]) && StringUtils.startsWithIgnoreCase(domain_reverse, competitorDomain[0])) {
	                    competitorRanMap.put(competitorDomain[0], new String[]{
	                            url, String.valueOf(true_rank)
	                    });
	                }
	            }
	            resultList.remove(i);
	            i--;
	        }
        }
        
        // parse rating
        if (ratingResultList != null && ratingResultList.size() > 0) {
	        for (int i = 0; i < ratingResultList.size(); i++) {
	            Map<String, Object> map = ratingResultList.get(i);
	
	            int true_rank = Integer.valueOf(map.get("true_rank").toString());
	            String ranking_date = map.get("ranking_date").toString();
	            int rowDateIdx = Integer.valueOf(StringUtils.replace(ranking_date, "-", ""));
	            if (rowDateIdx > dateIdx) {
	                break;
	            } else if (rowDateIdx != dateIdx) {
	                continue;
	            }
	            if (highestStarRank > true_rank) {
	                highestStarRank = true_rank;
	            }
	            ratingResultList.remove(i);
	            i--;
	        }
        }

        resultMap.put("highestTrueRank", highestTrueRank);
        resultMap.put("highestWebRank", highestWebRank);
        resultMap.put("highestRankUrl", highestRankUrl);
        resultMap.put("highestLocalRank", highestLocalRank);
        resultMap.put("highestNewsRank", highestNewsRank);
        resultMap.put("highestImageRank", highestImageRank);
        resultMap.put("highestVideoRank", highestVideoRank);
        resultMap.put("highestStarRank", highestStarRank);
        resultMap.put("competitorRanMap", competitorRanMap);
    }
	
	private String createRankingDetailsSql(int oid, String queryDate, int engine, int language, boolean isMobile, String domainName, List<String[]> competitorDomainList, List<String> kidList) {
		StringBuffer sql = new StringBuffer();
//		int queryIndex = Integer.valueOf(StringUtils.replace(queryDate, "-", ""));
//        String detailTable = ClarityDBUtils.getTableNameForKPCold(queryIndex, queryIndex, engine, language,
//                isMobile, false, ClarityDBUtils.RANK_DETAIL_TYPE, oid);
        
        String detailTable = ClDailyRankingEntityDao.getDetailTable(queryDate, engine, language, isMobile, false);

        
        String rootDomainReverse = StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(domainName), '.');
        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String reversDomainCol = rootDomainReverse.equals(domainReverse) ? "root_domain_reverse" : "domain_reverse";

        sql.append(" SELECT ");
        sql.append("     keyword_rankcheck_id, ");
        sql.append("     location_id, ");
        sql.append("     true_rank, ");
        sql.append("     web_rank, ");
        sql.append("     type, ");
        sql.append("     domain_reverse, ");
        sql.append("     ranking_date, ");
        sql.append("     url, ");
        String hrdSql = "";
        hrdSql = rootDomainReverse.equals(domainReverse) ? " AND hrrd = 1 " : " AND hrd = 1 ";
        String domainSqlStr = reversDomainCol + " = '" + domainReverse + "' ";
        sql.append("     multiIf(" + domainSqlStr + " " + hrdSql + ", 1, 0) AS ownDomainFlg  ");
        sql.append(" FROM " + detailTable + " ");
        sql.append(" WHERE (1 = 1) ");
        sql.append(ClarityDBUtils.getCommonQueryParams(engine, language, oid, null, ""	, null));
        sql.append("  AND ranking_date = '" + queryDate + "' ");
        sql.append(" AND (keyword_rankcheck_id IN ( " + StringUtils.join(kidList, ',') + "))  AND (location_id = 0) ");
        if (tag_id > 0) {
        	sql.append(" AND (keyword_rankcheck_id, location_id) GLOBAL IN ( ");
        	sql.append("    SELECT keyword_rankcheck_id, location_id FROM (");
        	sql.append("    SELECT  grouptag_id, own_domain_id, location_id, grouptag_status, keyword_rankcheck_id ");
        	sql.append("    FROM seo_daily_ranking.cdb_tracked_keyword ");
        	sql.append("    WHERE own_domain_id = " + OID + " AND grouptag_id = " + tag_id + " AND grouptag_status = 1 AND keyword_type = 1 ");
        	sql.append("    GROUP BY grouptag_id, own_domain_id, location_id, grouptag_status, keyword_rankcheck_id having sum(sign) > 0 ");
        	sql.append(" )) ");
        }
        StringBuffer domainSql = new StringBuffer();
        domainSql.append(" AND ( ");
        List<String> rootDomains = new ArrayList<String>();
        List<String> domains = new ArrayList<String>();
        // add competitors
        if (competitorDomainList != null && competitorDomainList.size() > 0) {
            for (String[] competitors : competitorDomainList) {
                rootDomains.add(competitors[0]);
                if (StringUtils.isNotBlank(competitors[1])) {
                    domains.add(competitors[1]);
                }
            }
        }
        //  add own domain name
        rootDomains.add(rootDomainReverse);
        if (!rootDomainReverse.equals(domainReverse)) {
            domains.add(domainReverse);
        }
        // for ll/image/video/news
        rootDomains.add("com.google");
        domainSql.append(" root_domain_reverse in ('" + StringUtils.join(rootDomains, "','") + "') ");
        domainSql.append(" ) ");
        sql.append(domainSql.toString());
        sql.append(" ORDER BY ");
        sql.append("     keyword_rankcheck_id asc, ");
        sql.append("     true_rank ASC ");

        System.out.println(sql.toString());
        return sql.toString();
	}
	
	private String createKeywordSql(int oid, String processDate, int engine, int language, boolean isMobile, String domainName) {
		int index = Integer.valueOf(StringUtils.replace(processDate, "-", ""));
        StringBuffer sql = new StringBuffer();
		String infoTable = ClDailyRankingEntityDao.getTable(processDate, engine, language, isMobile, false);

        sql.append(" SELECT ");
        sql.append("     keyword_rankcheck_id, ");
        sql.append("     location_id, ");
        sql.append("     keyword_name, ");
        sql.append("     avg_search_volume, ");
        sql.append("     ranking_date ");
        sql.append(" FROM " + infoTable + " ");
        sql.append(" WHERE (1 = 1) ");
        sql.append(ClarityDBUtils.getCommonQueryParams(engine, language, oid, null, "", null));
        sql.append(" AND ranking_date = '" + processDate + "' ");
        if (tag_id > 0) {
        	sql.append(" AND (keyword_rankcheck_id, location_id) GLOBAL IN ( ");
        	sql.append("    SELECT keyword_rankcheck_id, location_id FROM (");
        	sql.append("    SELECT  grouptag_id, own_domain_id, location_id, grouptag_status, keyword_rankcheck_id ");
        	sql.append("    FROM seo_daily_ranking.cdb_tracked_keyword ");
        	sql.append("    WHERE own_domain_id = " + OID + " AND grouptag_id = " + tag_id + " AND grouptag_status = 1 AND keyword_type = 1 ");
        	sql.append("    GROUP BY grouptag_id, own_domain_id, location_id, grouptag_status, keyword_rankcheck_id having sum(sign) > 0 ");
        	sql.append(" )) ");
        }
        sql.append(" ORDER BY ");
        sql.append("     keyword_rankcheck_id ASC, ");
        sql.append("     location_id ASC, ");
        sql.append("     ranking_date ASC ");
        
        return sql.toString();
	}

}
