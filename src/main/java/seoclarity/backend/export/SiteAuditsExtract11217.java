package seoclarity.backend.export;


import cn.hutool.core.util.ZipUtil;
import com.amazonaws.regions.Regions;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.entity.ClarityDBConstants;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.utils.AmazonS3UploadTool;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * https://www.wrike.com/open.htm?id=1049163096
 */
@CommonsLog
public class SiteAuditsExtract11217 {

    /** if additional_status_v2 = 2 , run extract
     * select * from crawl_request_log
     * where project_id =  12993697908167219 and crawl_request_date <= 20230508
     * order by id desc limit 5
     */

    private static final String S3_ROLE_ARN ="arn:aws:iam::739939173819:role/seoclarity_role";
    private static final String S3_EXTERNAL_ID ="seoclarityDev-externalId-assumeRole";
    private static final String S3_BUCKET_NAME = "upwork-usw2-prod-seo-clarity-crawl";
    private static final int S3_SESSION_DURATION_SECONDS = 3600;
    private static final int S3_RETRY_COUNT = 10;

    private static final long PROJECTId = 2324031798415174l;

    private static final String SUC_CRAWL_FOLDER_PATH =  "/home/<USER>/source/extract_scripts/clarity-backend-scripts/SucCrawl11217/";
    private CrawlRequestLogDAO crawlRequestLogDAO;

    public SiteAuditsExtract11217(){
        crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
    }

    private void process(int domainId){
        StringBuffer sql = new StringBuffer();

        try {
            File sucCrawlFolder = new File(SUC_CRAWL_FOLDER_PATH);
            if(!sucCrawlFolder.exists()){
                sucCrawlFolder.mkdirs();
            }
            String sucFilePath = SUC_CRAWL_FOLDER_PATH + "sucCrawlId.txt";
            File sucFile = new File(sucFilePath);

            Integer lastCrawlId = 0;

            if(sucFile.exists()){
                List<String> crawlIdList = FileUtils.readLines(new File(sucFilePath), "utf-8");
                if(CollectionUtils.isNotEmpty(crawlIdList)){
                    lastCrawlId = Integer.parseInt(crawlIdList.get(crawlIdList.size() -1));
                }
            }

            CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getLogIdByProjectId(domainId, PROJECTId, lastCrawlId);
            if(crawlRequestLog == null){
                log.info("===logId not exist or crawl not complete ,domainId:" + domainId + ",projectId:" + PROJECTId + ",lastCrawlId:" + lastCrawlId);
                return;
            }

    //        if(crawlRequestLog.getAdditionalStatusV2() != null && crawlRequestLog.getAdditionalStatusV2() != CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE){
    //            log.info("====data not ready.");
    //            return;
    //        }

            int logId = crawlRequestLog.getId();
            int processDate = crawlRequestLog.getCrawlRequestDate();
            log.info("====start logId:" + logId);

            sql.append("	SELECT  ");
            sql.append("	    replaceAll(url, '\\n','') as `URL`,replaceAll(title, '\\n','') as `Title`,status as `StatusCode`,  ");
            sql.append("	    length(arrayFilter(x -> not has(['page_analysis_rule_9_b','page_analysis_rule_13_b','page_analysis_rule_14_b',  ");
            sql.append("	    'page_analysis_rule_15_b','page_analysis_rule_16_b','page_analysis_rule_17_b','page_analysis_rule_18_b',  ");
            sql.append("	    'page_analysis_rule_37_b','page_analysis_rule_38_b','page_analysis_rule_39_b','page_analysis_rule_40_b',  ");
            sql.append("	    'page_analysis_rule_41_b','page_analysis_rule_42_b','page_analysis_rule_49_b','page_analysis_rule_50_b',  ");
            sql.append("	    'page_analysis_rule_51_b','page_analysis_rule_53_b','page_analysis_rule_54_b','page_analysis_rule_56_b',  ");
            sql.append("	    'page_analysis_rule_58_b','page_analysis_rule_60_b','page_analysis_rule_63_b','page_analysis_rule_66_b',  ");
            sql.append("	    'page_analysis_rule_67_b','page_analysis_rule_105_b'], x), page_analysis_issues_array )) as CountOfIssues,  ");
            sql.append("	    replaceAll(arrayStringConcat(h1, '|'), '\\n','') as `H1`,replaceAll(arrayStringConcat(h2, '|'), '\\n','') as `H2`,  ");
            sql.append("	    replaceAll(description, '\\n','') as `Description`,replaceAll(canonical, '\\n','') as `Canonical`,  ");
            sql.append("	    replaceAll(robots_contents, '\\n','') as `Robots`,crawl_depth as `Depth`,content_word_count as `WordCount`,  ");
            sql.append("	    url_length as `URLLength`,title_length as `TitleLength`,description_length as `Descriptionlength`,  ");
            sql.append("	    replaceAll(amphtml_href, '\\n','') as `AMPURL`,replaceAll(viewport_content, '\\n','') as `ViewPortContent`,  ");
            sql.append("	    h1_length as `h1Length`,JSONLength(external_link) as `ExternalLinks`   ");
            sql.append("     FROM dis_site_crawl_doc ");
            sql.append("  WHERE crawl_request_id = " + logId + " AND domain_id = " + domainId);
            sql.append("  AND not empty(url) " );

            File extractFile = new File("/home/<USER>/11217/11217_SiteMap_" + processDate + ".csv");
            String filePath = extractFile.getAbsolutePath();
            List<String> header = new ArrayList<String>();
            header.add("URL");
            header.add("Title");
            header.add("Status Code");
            header.add("Count of Issues");
            header.add("H1");
            header.add("H2");

            header.add("Description");
            header.add("Canonical");
            header.add("Robots");
            header.add("Depth");
            header.add("Word Count");
            header.add("URL Length");
            header.add("Title Length");
            header.add("Description length");

            header.add("AMP URL");
            header.add("View Port Content");
            header.add("H1 Length");
            header.add("External Links");


            ClarityDBUtils.postToFile(ClarityDBConstants.SITE_CRAWL_HOST, sql + " format CSV", filePath, header.toArray(new String[header.size()]));
            System.out.println("=out fileName:" + filePath);

//            File zipFile = new File(filePath+".gz");
//            if (zipFile!= null && zipFile.exists() && zipFile.isFile()) {
//                System.out.println("file already exist, deleted!!!");
//                zipFile.delete();
//            }

//            GZipUtil.zip(filePath, zipFile.getAbsolutePath());
//            ZipUtil.zip(filePath, zipFile.getAbsolutePath());
//            System.out.println("======OutGzipFile:" + zipFile.getAbsolutePath());

//            extractFile.delete();
            sentToS3(extractFile.getAbsolutePath());

            FileUtils.writeLines(sucFile, Arrays.asList(String.valueOf(logId)), true);

        } catch (Exception e) {
            System.out.println(sql);
            e.printStackTrace();
        }

    }


    private void sentToS3(String filePath){

        try {
            GZipUtil.zip(filePath, filePath + GZipUtil.GZFile_POSTFIX);
            File gzFile = new File(filePath + GZipUtil.GZFile_POSTFIX);
            log.info("====gzFile:" + gzFile.getAbsolutePath() + ",name:" + gzFile.getName());


            String s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
            String s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
            boolean savedFilesToS3 = AmazonS3UploadTool.sendFileToS3WithRoleAndSSEByTransferManager(
                    s3AccessKey, s3SecretKey, S3_EXTERNAL_ID, S3_ROLE_ARN, S3_BUCKET_NAME, gzFile.getName(),
                    gzFile.getAbsolutePath(), Regions.US_WEST_2, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
            if (!savedFilesToS3) {
                log.error("===send to s3 failed.");
            } else {
                log.info("=====send to s3 success!" + gzFile.getName());
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public static void main(String[] args){
        SiteAuditsExtract11217 siteAuditsExtract11217 = new SiteAuditsExtract11217();
        siteAuditsExtract11217.process(11217);
//        siteAuditsExtract11217.sentToS3("/home/<USER>/11217/11217_SiteMap_20241101_v2.csv");
//        siteAuditsExtract11217.sentToS3("test.txt", "D:\\workspace\\extract\\s3\\test.txt");
    }


}
