package seoclarity.backend.export;

import cn.hutool.core.text.csv.CsvWriteConfig;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.entity.RankIndexParamVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=776031147
 * --hao
 */
public class StandardRankingExtract {

    private static final String FILE_NAME_TEMPLE = "StandardRankingExtract_%DATE_INDEX%_%ENGINE%.csv";
    private static final String LOCAL_OUT_FILE_PATH = "/home/<USER>/%DOMAINID%/StandardRankingExtract/";
    private static final String FTP_PATH = "/home/<USER>/%DOMAINID%/standardRankingExtact";
    private static final String OUT_TYPE = "csv";
    private static final int MAX_TRY = 50;
    private static final String API_URL = "http://10.186.101.149:8183/seoClarity/dailyrankingv2/getKeywordTagTrendTable";
    private static final String API_URL_APP = "http://10.186.101.149:8182/seoClarity/dailyrankingv2/getKeywordTagTrendTable";
    public static final int SEPARATE_GEO_TYPE_NATIONAL = 1;
    public static final int SEPARATE_GEO_TYPE_GEO = 2;
    public static final int SEPARATE_GEO_TYPE_MIX = 3;
    private static SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
    CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader(
            "Date",
            "Keyword Tag",
            "Estimated Traffic",
            "Share of Voice",
            "Share of Market",
            "Average Rank",
            "Weighted Average Rank",
            "DomainName"
    )
            .withDelimiter(',');
    String[] domainComp = new String[]{
//            "thezebra.com",
//            "quotewizard.com",
//            "www.esurance.com",
//            "www.nerdwallet.com",
//            "www.progressive.com",
//            "www.valuepenguin.com"
    };

    private GroupTagEntityDAO groupTagEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private GrouptagCompetitorRelDAO grouptagCompetitorRelDAO;
    private ScKeywordRankManager scKeywordRankManager;
    private RankIndexParamEntityDAO rankIndexParamEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;


    public StandardRankingExtract() {
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        grouptagCompetitorRelDAO = SpringBeanFactory.getBean("grouptagCompetitorRelDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        rankIndexParamEntityDAO = SpringBeanFactory.getBean("rankIndexParamEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
    }

    public static void main(String[] args) throws ParseException {

        System.out.println("===========================  gogogo ========================================");
        String sDate = null;
        Calendar cal = Calendar.getInstance();
        if (null != args && args.length > 2) {
            sDate = args[2];
            cal.setTime(yyyy_MM_dd.parse(sDate));
        } else {
            sDate = yyyy_MM_dd.format(cal.getTime());
        }
        String device = args[0];
        String[] domainids = args[1].split(",");

        // Find out if it's Monday
        boolean monday = false;

        StandardRankingExtract ins = new StandardRankingExtract();
        for (String id : domainids) {
            OwnDomainEntity domainENtity = ins.ownDomainEntityDAO.getById(Integer.parseInt(id));

            if (cal.get(Calendar.DAY_OF_WEEK) == 2) {
                monday = true;
            }
            if (domainENtity.getKeywordRankFrequency() == 1) {
                cal.add(Calendar.DAY_OF_MONTH, -1);
                sDate = yyyy_MM_dd.format(cal.getTime());
            }
            System.out.println(" domain : " + id + " device :" + device + " queryDate " + sDate + " Frequency : " + domainENtity.getKeywordRankFrequency() + " monday : " + monday);

            try {
                if (domainENtity.getKeywordRankFrequency() == 2 && monday) {
                    System.out.println("    // week domain  Run every monday ");
                    ins.process(sDate, device, id, domainENtity);
                } else if (domainENtity.getKeywordRankFrequency() == 1) {
                    System.out.println("    // daily domain  Run every Day ");
                    ins.process(sDate, device, id, domainENtity);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void process(String queryDate, String device, String domainId, OwnDomainEntity domainENtity) {

        domainComp = new String[]{};
        // check folder
        String domainFolder = StringUtils.replace(LOCAL_OUT_FILE_PATH, "%DOMAINID%", domainId);
        File folder = new File(domainFolder);
        if (!folder.exists()) {
            folder.mkdirs();
        }

        String fileName = createDaysForFiles(queryDate, device, domainFolder);
        System.out.println(" filename : " + fileName);

        Map<String, String> params = getParams(queryDate, device, domainENtity);
        try {
            List<Map<String, Object>> dateRowMap = new ArrayList<>();
            String response = getResponse(params);
            if (StringUtils.isBlank(response)) {
                throw new Exception(" API error ! there are no data from api !");
            }
            String cleanjson = clearJson(response);
            String ecodejson = StringEscapeUtils.unescapeJavaScript(cleanjson);
//            System.out.println("===###ecodejson : " + ecodejson);
            JSONObject jsonResponse = JSONObject.parseObject(ecodejson);
            if (StringUtils.isNotBlank(jsonResponse.get("data").toString())) {
                JSONArray responseArray = jsonResponse.getJSONArray("data");
                parseLines(responseArray, dateRowMap, queryDate);
            }
            if (dateRowMap.size() > 0) {
                exportToFile(dateRowMap, fileName);
                String ftpFolder = StringUtils.replace(FTP_PATH, "%DOMAINID%", domainId);
                FTPUtils.saveFileToFTP(Integer.parseInt(domainId), fileName, ftpFolder);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private String getResponse(Map<String, String> params) {
        String response = null;
        int tryCnt = 0;
        do {
            tryCnt++;
            try {
                response = HttpRequestUtils.queryWebServiceFunctionPost(API_URL, null, params);
                if ((StringUtils.isNotBlank(response) && response.contains("tag_name")) || tryCnt >= MAX_TRY) {
                    break;
                } else {
                    System.out.println("===Unvalid response, tryCnt:" + tryCnt + ", queryUrl:" + API_URL + ", response:" + response);
                    Thread.sleep(5000);
                }
            } catch (Exception e) {
                System.out.println("===query failed. queryUrl:" + API_URL + ", response:" + response);
                e.printStackTrace();
            }
        } while (true);
        return response;
    }

    private List<String> exportToFile(List<Map<String, Object>> dateRowMap, String fileName) {
        List<String> exportInfoList = new ArrayList<>();
        CsvWriteConfig config = new CsvWriteConfig();
        config.setAlwaysDelimitText(true);
        try {
            CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(fileName)), csvFullFormat);
            for (Map<String, Object> objectMap : dateRowMap) {
                DecimalFormat df = new DecimalFormat("0.00%");
                csvPrinter.printRecord(
                        objectMap.get("date"),
                        objectMap.get("keywordTag"),
                        objectMap.get("estTraffic"),
                        df.format(Double.parseDouble(objectMap.get("voice").toString())),
                        df.format(Double.parseDouble(objectMap.get("market").toString())),
                        objectMap.get("avgRank"),
                        objectMap.get("wtdRank"),
                        objectMap.get("domainName")
                );
            }
            csvPrinter.flush();
            csvPrinter.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return exportInfoList;
    }

    // get request from   http://10.186.101.149:8183/seoClarity/dailyrankingv2/getKeywordTagTrendTableownDomainId:5367
    //useHierarchy:t
    //domainType:universalcompetitors
    //ownDomainName:www.thezebra.com
    //urlHash:null
    //indexList:********,********
    //broadmatch:t
    //isWeeklyRankFrequency:false
    //top:100
    //domainList:thezebra.com,www.bankrate.com,www.esurance.com,www.nerdwallet.com,www.progressive.com,www.usnews.com,www.valuepenguin.com
    //locationId:0
    //include101:f
    //kwAddedToday:false
    //kwDeledToday:false
    //ctr:0.3,0.24,0.15,0.1,0.08,0.05,0.04,0.03,0.01,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0
    //uniqueKeyword:t
    //offset:0
    //mobile:f
    //languageId:1
    //customSearchVol:f
    //sort:domain0AvgRank_0|desc
    //queryType:null
    //competitorGroupList:
    //access_token:c09yxv13-opr3-d745-9734-8pu48420nj67
    //startDayOfWeek:1
    //trueRank:t
    //countInTop:1
    //competitorGroupsMap:null
    //separateGeoType:1,3
    //dwm:day
    //avoidDateList:null
    //tagIdList:0
    //engineId:1
    //=filterMap:{}

    private Map<String, String> getParams(String queryDate, String device, OwnDomainEntity domainENtity) {

        String dateindex = StringUtils.replace(queryDate, "-", "");
        int engineId = scKeywordRankManager.getSearchEngineId(domainENtity);
        int languageId = scKeywordRankManager.getSearchLanguageId(domainENtity);
        int domainId = domainENtity.getId();
        String domainName = domainENtity.getDomain();
        String dwn = "day";
        if (domainENtity.getKeywordRankFrequency() == 1) {
            dwn = "day";
        } else if (domainENtity.getKeywordRankFrequency() == 2) {
            dwn = "week";
            try {
                dateindex = getDateOfWeeklyDomain(queryDate);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (domainENtity.getKeywordRankFrequency() == 3) {
            dwn = "month";
        } else if (domainENtity.getKeywordRankFrequency() == 14) {
            dwn = "bi-week";
        }

        String trueRank = "";
        if (domainENtity.getRankCalculation() != null && domainENtity.getRankCalculation().intValue() == 1) {
            // web rank
            trueRank = "f";
        } else {
            // true rank
            trueRank = "t";
        }
        String startDayOfWeek = null == domainENtity.getStartDayOfWeek() ? "1" : String.valueOf(domainENtity.getStartDayOfWeek());


        // get uniCompetitorNames
        List<GrouptagCompetitorRel> uniCompetitorNames = grouptagCompetitorRelDAO.getUniCompetitorNames(domainId, 0);
        StringBuilder sb = new StringBuilder();
        List<String> unidomains = new ArrayList<>();
        if (uniCompetitorNames.size() > 0) {
            for (GrouptagCompetitorRel en : uniCompetitorNames) {
                unidomains.add(en.getCompetitorDomain());
                sb.append(en.getCompetitorDomain());
                sb.append(",");
            }
        }
        sb.append(domainName);
        unidomains.add(domainName);
        domainComp = unidomains.toArray(new String[unidomains.size()]);
        System.out.println(sb);
        System.out.println(domainComp.toString());
        String domainList = sb.toString();
        Map<String, String> dataMap = new HashMap<>();

        List<RankIndexParamEntity> paramList = rankIndexParamEntityDAO.getRankIndexParams(domainId, 0);
        String ctr = "0.3,0.24,0.15,0.1,0.08,0.05,0.04,0.03,0.01,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0";
        if (null != paramList && paramList.size() > 0) {
            RankIndexParamVO rankIndexParamVO = new RankIndexParamVO(paramList);
            List<String> ctrList = rankIndexParamVO.getParamList();
            StringBuilder ctrs = new StringBuilder();
            for (String s : ctrList){
                ctrs.append(s).append(",");
            }
            ctr = ctrs.toString().substring(0,ctrs.length()-1);
//            ctr = rankIndexParamVO.getTotalIndex(ctrList);
            System.out.println(" ===###ctr : " + ctr);
        }

        String kwAddedToday = "false";
        String kwDeledToday = "false";
        ResourceBatchInfoEntity hasKwAddedToday = resourceBatchInfoEntityDAO.hasKeywordAddedToday(domainId);
        ResourceBatchInfoEntity hasKwDeledToday = resourceBatchInfoEntityDAO.hasKeywordDeletedToday(domainId);
        if (null != hasKwAddedToday && hasKwAddedToday.getOwnDomainId() > 0) {
            kwAddedToday = "true";
        }
        if (null != hasKwDeledToday && hasKwDeledToday.getOwnDomainId() > 0) {
            kwDeledToday = "true";
        }

        OwnDomainSettingEntity domainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);

        String customSearchVol = "f"; //https://www.wrike.com/open.htm?id=1307801313
        if (domainSettingEntity.getEnableCustomSearchVolume() != 0 && null != domainSettingEntity.getEnableCustomSearchVolume()) {
            customSearchVol = "t";
        }

        String separateGeoType = "";
//        String locationIdList =  "";
        if (null == domainSettingEntity.getRegionId()) {
            //not geo
            separateGeoType = "1,3";
        } else {
            separateGeoType = "2,3";
        }

        dataMap.put("ownDomainId", String.valueOf(domainId));
        dataMap.put("useHierarchy", "t");
        dataMap.put("domainType", "universalcompetitors");
        dataMap.put("ownDomainName", domainName);
        dataMap.put("indexList", dateindex);
        dataMap.put("domainList", domainList);
        dataMap.put("broadmatch", domainENtity.isBroadMatch() ? "t" : "f");
        dataMap.put("isWeeklyRankFrequency", domainENtity.getKeywordRankFrequency() == 2 ? "true" : "false");
        dataMap.put("locationId", "0");
        dataMap.put("include101", domainENtity.getAvg_rank_include101() == 1 ? "true" : "false");
        dataMap.put("kwAddedToday", kwAddedToday);
        dataMap.put("kwDeledToday", kwDeledToday);
        dataMap.put("ctr", ctr);
        dataMap.put("uniqueKeyword", "t");
        dataMap.put("mobile", "mobile".equals(device) ? "t" : "f");
        dataMap.put("languageId", String.valueOf(languageId));
        dataMap.put("customSearchVol", customSearchVol);
        // old token let out ,so change new token
//        dataMap.put("access_token", "a10bef62-aed0-b459-6586-3eb31687af79");
        dataMap.put("access_token", "c09yxv13-opr3-d745-9734-8pu48420nj67");
        dataMap.put("startDayOfWeek",startDayOfWeek);
        dataMap.put("trueRank", trueRank);
        dataMap.put("countInTop", "1");
        dataMap.put("separateGeoType", separateGeoType);
        dataMap.put("dwm", dwn);
        dataMap.put("tagIdList", "0");
        dataMap.put("engineId", String.valueOf(engineId));
        dataMap.put("sort", "tag_name|asc");
        dataMap.put("offset", "0");
        dataMap.put("top", "1000000");

        System.out.println(dataMap);
        return dataMap;
    }

    private String getDateOfWeeklyDomain(String queryDate) throws ParseException {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(yyyy_MM_dd.parse(queryDate));
        Date day = calendar1.getTime();
        Date lastsunday = DateUtils.getLastSunday(day);
        // Meo api will -
        lastsunday = org.apache.commons.lang.time.DateUtils.addDays(lastsunday, 6);
        String d1 = yyyy_MM_dd.format(lastsunday);
//        Date lastweek = org.apache.commons.lang.time.DateUtils.addDays(lastsunday, -7);
//        String d2 = yyyy_MM_dd.format(lastweek);

        String date1 = StringUtils.replace(d1, "-", "");
        String date2 = StringUtils.replace(d1, "-", "");
        System.out.println(" ===###weekdomain_date :" + date2 + "," + date1);
        return date2 + "," + date1;
    }


    private void parseLines(JSONArray responseArray, List<Map<String, Object>> dateRowMap, String queryDate) throws
            Exception {

        for (Object line : responseArray) {
            int lineIdx = 0;
            for (String domainName : domainComp) {
                Map<String, Object> dataMap = new HashMap<>();
                try {
                    JSONObject lineObj = (JSONObject) JSONObject.toJSON(line);
//                    System.out.println("===###lineObj : " +lineObj);
                    String date = queryDate;
                    String KeywordTag = lineObj.get("tag_name").toString();
                    dataMap.put("date", date);
                    dataMap.put("keywordTag", KeywordTag);
                    String domain0EstTraffic = lineObj.get("domain" + lineIdx + "Sum_avg_estTraffic_0").toString();
                    dataMap.put("estTraffic", domain0EstTraffic);
                    String domain0Voice = lineObj.get("domain" + lineIdx + "Share_of_voice_0").toString();
                    dataMap.put("voice", domain0Voice);
                    String domain0Maket = lineObj.get("domain" + lineIdx + "Share_of_market_0").toString();
                    dataMap.put("market", domain0Maket);
                    String domain0AvgRank = lineObj.get("domain" + lineIdx + "AvgRank_0").toString();
                    dataMap.put("avgRank", domain0AvgRank);
                    String domain0WtdRank = lineObj.get("domain" + lineIdx + "Wtd_avg_rank_0").toString();
                    dataMap.put("wtdRank", domain0WtdRank);
                    dataMap.put("domainName", domainName);
                    lineIdx++;
                    dateRowMap.add(dataMap);
                } catch (Exception e) {
                    throw new Exception(e);
                }
            }
        }
    }

    private String createDaysForFiles(String queryDate, String device, String domainFolder) {
        String idx = StringUtils.replace(queryDate, "-", "");
        String date = StringUtils.replace(FILE_NAME_TEMPLE, "%DATE_INDEX%", idx);
        String name = StringUtils.replace(date, "%ENGINE%", device);
        String fileName = domainFolder + name;
        return fileName;
    }

    private static String clearJson(String json) {
        json = StringUtils.removeStart(json, "\"");
        json = StringUtils.removeEnd(json, "\"");
        return json;
    }
}
