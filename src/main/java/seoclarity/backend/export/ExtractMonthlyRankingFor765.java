package seoclarity.backend.export;

import com.amazonaws.regions.Regions;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ServerAuthenticationInfoEntity;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyRankingEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.*;

import java.io.File;
import java.io.IOException;
import java.util.*;

@CommonsLog
public class ExtractMonthlyRankingFor765 {

    private static final String SPLIT = "\t";
    private static String LOC = "/home/<USER>/RGExtract/";

    private static String processMonth;
    private static String[] domainNameList;
    private static String domainName;
    private static boolean isMobile = false;

    private static int engineId = 1;
    private static int languageId = 1;
    private static Integer pageSize = 1000000;

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private MonthlyRankingDao monthlyRankingDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private ExtractService extractService;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;


    public static final Map<String, String> ENGINE_NAME_MAP = new HashMap();
    static {
        ENGINE_NAME_MAP.put("1_1","google.com");
        ENGINE_NAME_MAP.put("3_3","google.ca");
    }
    public ExtractMonthlyRankingFor765() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        extractService = SpringBeanFactory.getBean("extractService");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    private void processForDomainName(String domainName) {
        domainName = domainName.toLowerCase();
        log.info("=======processingMonth:" + processMonth + ",domainName:" + domainName);
        String device = isMobile ? "mobile" : "desktop";
        String engineName = ENGINE_NAME_MAP.get(engineId + "_" + languageId);
        String fileName = domainName + "_" + engineName + "_" + device + "_" + processMonth + "_ResearchGrid.csv";

        try {
            int ownDomainId = 765;
            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
            if (ownDomainEntity == null) {
                log.error(" domain not exist : " + ownDomainId);
                try {
                    extractService.sendMailReport("ERROR:Export for inactive OID:" + ownDomainId, "Please disable export for inactive OID:" + ownDomainId + "(" + getClass().getName() + ")");
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
                return;
            }

            File outFile = new File(LOC + fileName);
            if(outFile.exists()){
                outFile.delete();
            }
            addHeadersForExactFile(outFile);

            Integer pageNum = 0;
            List<MonthlyRankingEntity> monthlyRankingList = new ArrayList<>();
            while (true) {

                monthlyRankingList = monthlyRankingDao.extractFor765(domainName, engineId, languageId, processMonth, isMobile, pageNum, pageSize);
                if (CollectionUtils.isEmpty(monthlyRankingList)) {
                    break;
                }

                pageNum++;
                System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize);

                List<String> dataList = new ArrayList<>();
                for (MonthlyRankingEntity monthlyRankingEntity : monthlyRankingList) {
                    dataList.add(appendData(processMonth, monthlyRankingEntity, domainName));
                }

                FileUtils.writeLines(outFile, dataList, true);
            }

//            String s3KeyPath = "seo/research_grid/" + fileName;
//            boolean isSendS3Success = serverAuthenticationInfoService.putFileForS3WithSSE(ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3,
//                    765, outFile.getAbsolutePath(), s3KeyPath);

//            if(!isSendS3Success){
//                log.error("====send to s3 failed!!");
//            }

            String role = "arn:aws:iam::902399682238:role/marketingtechnology-seo-clarity-prod";
            String externalId = "seoclarityDev-externalId-assumeRole";
            Regions region = Regions.US_EAST_2;
            String s3KeyPath = "seo_clarity/research_grid/" + fileName;
            boolean isSendS3Success = serverAuthenticationInfoService.putFileForS33WithRoleAndSSE(ServerAuthenticationInfoEntity.SERVER_TYPE_S3_ROLE_AND_SSE, ownDomainId, outFile.getAbsolutePath(), s3KeyPath,
                    role, externalId, region, 3600, 3);

            if(!isSendS3Success){
                log.error("====send to s3 failed!!");
                String subject = "Send to s3 role sse ERROR, Oid:" + ownDomainId + ",processMonth:" + processMonth + ",ExtractMonthlyRankingFor765.class";
                sendEmailForError(subject, subject);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }


    }


    private void addHeadersForExactFile(File outFile) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Month").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Search Volume").append(SPLIT);
        header.append("Rank").append(SPLIT);
        header.append("Ranking URL").append(SPLIT);
        header.append("Keyword Intent");

        if (org.apache.commons.lang.StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }

    }

    public static String appendData(String month, MonthlyRankingEntity monthlyRankingEntity, String domainName) {
        StringBuffer line = new StringBuffer();

        line.append(month).append(SPLIT);
        line.append(monthlyRankingEntity.getKeywordName()).append(SPLIT);
        line.append(monthlyRankingEntity.getAvgSearchVolume()).append(SPLIT);
        line.append(monthlyRankingEntity.getRank()).append(SPLIT);
        line.append(monthlyRankingEntity.getUrl() == null ? "-" : ExtractService.formatGoogleUrl(monthlyRankingEntity.getUrl())).append(SPLIT);
        line.append(monthlyRankingEntity.getIntentType());

        return line.toString();
    }

    private void sendEmailForError(String subject, String message) {
        try {
            Map<String, Object> reportMap = new HashMap<String, Object>();
            reportMap.put("userName", "Mitul");
            reportMap.put("successMessage", message);
            String emailTo = "<EMAIL>"; // TODO
            String[] ccTo = new String[]{"<EMAIL>"};
            zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html",
                    reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {

        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainNameList = args[0].split(",");
            } else {
                domainName = args[0];
            }

        }

        engineId = Integer.parseInt(args[1]);
        languageId = Integer.parseInt(args[2]);
        isMobile = Boolean.parseBoolean(args[3]);

        ExtractMonthlyRankingFor765 extractMonthlyRanking = new ExtractMonthlyRankingFor765();
        if (domainNameList != null && domainNameList.length > 0) {

            for (String processingDomainName : domainNameList) {

                if (args.length >= 5 && !StringUtils.containsIgnoreCase(args[4], ",")) {
                    processMonth = args[4];
                    extractMonthlyRanking.processForDomainName(processingDomainName);
                } else {
                    processMonth = FormatUtils.formatDate(DateUtils.addMonths(new Date(), -1), FormatUtils.MONTH_PATTERN);
                    extractMonthlyRanking.processForDomainName(processingDomainName);
                }
            }

        } else {

            if (args.length >= 5 && !StringUtils.containsIgnoreCase(args[4], ",")) {
                processMonth = args[4];
                extractMonthlyRanking.processForDomainName(domainName);
            } else {
                processMonth = FormatUtils.formatDate(DateUtils.addMonths(new Date(), -1), FormatUtils.MONTH_PATTERN);
                extractMonthlyRanking.processForDomainName(domainName);
            }

        }

    }

}
