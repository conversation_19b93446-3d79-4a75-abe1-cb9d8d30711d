package seoclarity.backend.export;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyRankingEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;

import javax.annotation.Nullable;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * www.amazon.co.uk - 1849
 * www.amazon.com - 475
 *
 * @Desc: ASIN data from research grid
 */
@CommonsLog
public class ExtractAmazonASINDataFromResearchGrid {

    private static final List<Integer> domainList = new ArrayList<>();
    private static final String asinFilePath = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/tmp/";
    private static final int pageNum = 500;
    private static final String month = "202101";
    private static final String SPLIT = "	";
    private static final String LOC_BASE = "/home/<USER>/475/asin-extract/";
    private static final int DB_TRY_COUNT = 3;

    private static Map<Integer, Integer> engineMap = new HashMap<>();

    private MonthlyRankingDao monthlyRankingDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;

    public ExtractAmazonASINDataFromResearchGrid() {
        monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    }

    public void process() {
        for (Integer domainId : domainList) {
            //loop domain
            OwnDomainEntity domainEntity = ownDomainEntityDAO.getById(domainId);
            List<String> asinIdList = new ArrayList<>();
            try {
                asinIdList = FileUtils.readLines(new File(asinFilePath + domainId + ".csv"), "UTF-8");
            } catch (IOException e) {
                e.printStackTrace();
            }

            String engineName = ScKeywordRankManager.getSearchEngineNameByEngineId(ScKeywordRankManager.getSearchEngineId(domainEntity), "google");

            String fileName = LOC_BASE + domainEntity.getDomain() + "_" + engineName + "_202101_TOP100_RankReport_Desktop.txt";
            File outFile = new File(fileName);
            outFile = addHeadersForExactFile(outFile);


            int searchEngineId = engineMap.get(domainId);
            int searchLanguageId = ScKeywordRankManager.getSearchLanguageId(domainEntity);
            String domain = domainEntity.getDomain();
            String rootDomain = ClarityDBUtils.getRootDomain(domain);
            String rootDomainReverse = FormatUtils.reverseDomainNameByDot(rootDomain);
            String domainReverse = FormatUtils.reverseDomainNameByDot(domain);
            String tableName = monthlyRankingDao.getTableNameByMonth(month, false, searchEngineId);

            log.info("params=> domain:" + domain + ", searchEngineId: " + searchEngineId + ", searchLanguageId: " + searchLanguageId + ", rootDomainReverse: " + rootDomainReverse + ", tableName:" + tableName);

            List<List<String>> asinIdLists = CollectionSplitUtils.splitCollectionBySize(asinIdList, pageNum);
            for (List<String> idList : asinIdLists) {
                //loop page
                String urlLikeSql = idList.stream().map(var -> "or url like '%" + var + "%' ").collect(Collectors.joining());
                urlLikeSql = StringUtils.removeStart(urlLikeSql, "or");
                List<MonthlyRankingEntity> rankingDataList = new ArrayList<>();

                for (int i = 0; i < DB_TRY_COUNT; i++) {
                    try {
                        rankingDataList = monthlyRankingDao.getRankingDataByUrlASIN(urlLikeSql, searchEngineId, searchLanguageId, rootDomainReverse, tableName);
                        break;
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            Thread.sleep(500);
                        } catch (Exception exp) {
                            exp.printStackTrace();
                        }
                        continue;
                    }
                }

                Map<String, MonthlyRankingEntity> urlMap = rankingDataList.stream().collect(Collectors.toMap(var1 -> var1.getUrl(), var2 -> var2));
                log.info("rankingDataList size: " + rankingDataList.size());

                Set<String> urlSet = urlMap.keySet();
                for (String asin : idList) {
                    //boolean isExist = false;
                    for (String url : urlSet) {
                        if (url.contains(asin)) {
                            MonthlyRankingEntity monthlyRankingEntity = urlMap.get(url);
                            appendData(outFile, monthlyRankingEntity, asin);
                            //isExist = true;
                        }
                    }
                    /*if (!isExist) {
                        appendData(outFile, null, asin);
                    }*/
                }
            }
            FTPUtils.saveFileToFTP(domainId, fileName, LOC_BASE);
        }
    }

    private void appendData(File outFile, @Nullable MonthlyRankingEntity monthlyRankingEntity, String asin) {
        List<String> lines = new ArrayList<String>();
        StringBuffer line = new StringBuffer();
        if (monthlyRankingEntity == null) {
            line.append("-").append(SPLIT);
            line.append("-").append(SPLIT);
            line.append("-").append(SPLIT);
            line.append("-").append(SPLIT);
            line.append("-").append(SPLIT);
            line.append("-").append(SPLIT);
            line.append(asin).append(SPLIT);
            lines.add(line.toString());
        }else {
            line.append(monthlyRankingEntity.getRankingDate()).append(SPLIT);
            line.append(monthlyRankingEntity.getKeywordName()).append(SPLIT);
            line.append(monthlyRankingEntity.getAvgSearchVolume()).append(SPLIT);
            line.append(monthlyRankingEntity.getRank()).append(SPLIT);
            line.append(monthlyRankingEntity.getUrl()).append(SPLIT);
            line.append(asin).append(SPLIT);
            lines.add(line.toString());
        }
        try {
            FileUtils.writeLines(outFile, lines, true);
        } catch (IOException e) {
            log.error("append data error!");
            e.printStackTrace();
        }
    }

    private File addHeadersForExactFile(File outFile){
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Search Volume").append(SPLIT);
        header.append("Rank (Web Rank)").append(SPLIT);
        header.append("Ranking URL").append(SPLIT);
        header.append("ASIN").append(SPLIT);
        lines.add(header.toString());
        try {
            FileUtils.writeLines(outFile, lines, true);
        } catch (IOException e) {
            log.error("add head error!");
            e.printStackTrace();
        }
        return outFile;
    }

    public static void main(String[] args) {
        ExtractAmazonASINDataFromResearchGrid extract = new ExtractAmazonASINDataFromResearchGrid();
        domainList.add(1849);
        ///domainList.add(475);
       // engineMap.put(475, 1);
        engineMap.put(1849, 6);
        extract.process();
    }

}
