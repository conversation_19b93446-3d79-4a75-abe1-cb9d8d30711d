package seoclarity.backend.export;

import com.amazonaws.regions.Regions;
import com.google.common.net.InternetDomainName;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.ScriptDeployInfoEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.util.*;

@CommonsLog
@Deprecated
public class ExportGSCData {

    private static final String SPLIT = "\t";
    private static String LOC = "/home/<USER>/";

    private static Date processDate = new Date();
    private static String[] domainIdList;
    private static int domainId;
    private static boolean isRerun = false;
    private static int extractType = 1;
    private static boolean isKeyword = false;
    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private ScriptDeployInfoEntity scriptDeployInfoEntity;
    private ScriptRunInstanceEntity monitorEntity = new ScriptRunInstanceEntity();

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private CommonDataService commonDataService;
    private ScriptDeployInfoEntityDAO scriptDeployInfoEntityDAO;
    private GscBaseDao gscBaseDao;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public ExportGSCData() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        commonDataService = SpringBeanFactory.getBean("commonDataService");
        scriptDeployInfoEntityDAO = SpringBeanFactory.getBean("scriptDeployInfoEntityDAO");
        gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();
    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(765, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
        SPECIAL_DOMAIN_SERVER_MAP.put(2047, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
    }

    public static final Map<Integer, Integer> SCRIPT_DEPLOY_INFO_MAP = new HashMap();//(domainId,infoId)

    static {
//        SCRIPT_DEPLOY_INFO_MAP.put(476, 12);
    }

    public static final Map<Integer, String> EXTRACT_TYPE_MAP = new HashMap();

    static {
        EXTRACT_TYPE_MAP.put(1, "Rank Extract");
        EXTRACT_TYPE_MAP.put(2, "GSC Extract");
    }

    private void processForDomain(int ownDomainId) {

        log.info("********************** process domain " + domainId + ",processDate: " + processDate + " *******************");

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);
        }catch (Exception e){
            e.printStackTrace();
        }


        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);

        boolean isDesktop = false;
        boolean isMobile = false;

        if (ownDomainEntity.isMobileDomain() && ownDomainSettingEntity.isMobileEnable()) {
            isMobile = true;
        } else if (!ownDomainEntity.isMobileDomain() && ownDomainSettingEntity.isMobileEnable()) {
            isMobile = true;
            isDesktop = true;
        } else if (ownDomainEntity.isMobileDomain() && !ownDomainSettingEntity.isMobileEnable()) {
            isMobile = true;
        } else if (!ownDomainEntity.isMobileDomain() && !ownDomainSettingEntity.isMobileEnable()) {
            isDesktop = true;
        }

        Integer scriptInfoId = SCRIPT_DEPLOY_INFO_MAP.get(ownDomainId);
        if (scriptInfoId != null) {
            scriptDeployInfoEntity = scriptDeployInfoEntityDAO.getById(scriptInfoId);
        }
        try {
            if (isDesktop) {//todo
                processExtract(ownDomainEntity, false);
            } else if (isMobile) {
                processExtract(ownDomainEntity, true);
            }
        }catch (Exception e){
            e.printStackTrace();;
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        }


    }

    private void processExtract(OwnDomainEntity ownDomainEntity, boolean isMobile) {

        long startTime = System.currentTimeMillis();
        int ownDomainId = ownDomainEntity.getId();
        int processingDate = FormatUtils.formatDateToYyyyMmDd(processDate);


        String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
        String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

        logglyVO.setoId(String.valueOf(ownDomainEntity.getId()));
        logglyVO.setName("ExportGSCData");
        logglyVO.setDevice(isMobile?"m":"d");

        logglyVO.setpDate(pDate);
        List<String> groupList = new ArrayList<>();
        groupList.add(LogglyVO.GROUP_GSC_EXTRACT);
        logglyVO.setGroups(groupList);

        if (scriptDeployInfoEntity != null) {
            try {
                monitorEntity = commonDataService.saveScriptRunInfo(scriptDeployInfoEntity.getId(),
                        FormatUtils.formatDateToYyyyMmDd(processDate), ScriptRunInstanceEntity.STATUS_STARTED,
                        ownDomainId, null, InetAddress.getLocalHost().getHostAddress(), CommonUtils.getServerPath(),
                        isMobile ? "m" : "d", ScriptRunInstanceEntity.TAG_NO,
                        scriptDeployInfoEntity.getFrequency());
            } catch (Exception e) {
                e.printStackTrace();
                logglyVO.setStatus(LogglyVO.STATUS_NG);
                String body = new Gson().toJson(logglyVO);
                LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                String subject = getEmailSubject(domainId, false);
                String message = subject;
                sendMailReport(subject, message);
                return;
            }
        }


        String fileName = getFileName(ownDomainId, processingDate, isMobile);//todo

        File localFolder = new File(LOC + ownDomainId);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }

        String localFilePath = LOC + ownDomainId + File.separator + fileName;
        String remoteFilePath = localFilePath;//todo
        if (ownDomainId == 765 || ownDomainId == 2047) {
            remoteFilePath = "/incoming/seoClarity";
        }

        File localFile = new File(localFilePath);
        if (localFile.exists()) {
            localFile.delete();
        }

        String s3KeyPath = "seo/gsc/" + fileName;

        try {
            processFile(localFile, ownDomainEntity, isMobile, localFilePath, remoteFilePath, s3KeyPath);
        } catch (Exception e) {
            e.printStackTrace();
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//            String subject = getEmailSubject(domainId, false);
//            String message = subject;
//            sendMailReport(subject, message);

            monitorEntity.setFatalError(e.getMessage());
            return;
        }

        long endTime = System.currentTimeMillis();
        int elapsedSeconds = (int) (endTime - startTime);
        if (StringUtils.isNotBlank(monitorEntity.getFatalError())) {
            monitorEntity.setStatus(ScriptRunInstanceEntity.STATUS_FAILURE);
        } else {
            monitorEntity.setStatus(ScriptRunInstanceEntity.STATUS_SUCCESS);
        }

        monitorEntity.setElapsedSeconds(elapsedSeconds);
        if (scriptDeployInfoEntity != null) {
            commonDataService.updateScriptRunInfo(monitorEntity);
        }

        logglyVO.setStatus(LogglyVO.STATUS_OK);
        logglyVO.setsTime(stTime);
        logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
        logglyVO.setRows(String.valueOf(totalCnt));
        String body = new Gson().toJson(logglyVO);
        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, boolean isMobile, String localFilePath,
                             String remoteFilePath, String s3KeyPath) throws Exception {

        int ownDomainId = ownDomainEntity.getId();
        addHeadersForExactFile(localFile, ownDomainEntity, processDate);//todo

        List<String> dataList = new ArrayList<>();
        if (extractType == 1) {
            dataList = getDataFromDB(ownDomainEntity, isMobile);
        } else if (extractType == 2) {
            dataList = getGSCDataFromDB(ownDomainId);
        }
        totalCnt = dataList.size();
        FileUtils.writeLines(localFile, dataList, true);

        int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
        if (SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId) != null) {
            serverType = SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId);
        }

        monitorEntity.setOutputDataCount(dataList.size());
        monitorEntity.setOutputFile(localFile.getName());
        monitorEntity.setOutputFileSizeKB(FormatUtils.getFileSizeKB(localFile.length()));

        copyFileToRemoteServer(serverType, ownDomainId, localFilePath, remoteFilePath, s3KeyPath);
    }

    private List<String> getGSCDataFromDB(int ownDomainId) {
        List<String> extractLines = new ArrayList<String>();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);
        int type = 0;
        if(isKeyword){
            type = GscEntity.TYPE_KEYWORD;
        }else {
            type = GscEntity.TYPE_URL;
        }
        List<GscEntity> gscEntityList = gscBaseDao.getExtractData(type, ownDomainId, rankingDate);

        for (GscEntity detail : gscEntityList) {
            try {
                extractLines.add(appendGSCData(detail));
            } catch (Exception e) {
                monitorEntity.setFatalError(e.getMessage());
                e.printStackTrace();
                continue;
            }
        }

        return extractLines;
    }

    private String getFileName(int ownDomainId, int processingDate, boolean isMobile) {

        String device = isMobile ? "mobile" : "desktop";

        String fileName = "";// TODO: 2019/12/4
        if (extractType == 1) {
            fileName = ownDomainId + "_RankExtract_" + processingDate + "_" + device + ".csv";
        } else if (extractType == 2) {
            if(isKeyword){
                fileName = ownDomainId + "_GSC_Keyword_Extract_" + processingDate + ".csv";
            }else {
                fileName = ownDomainId + "_GSC_Url_Extract_" + processingDate + ".csv";
            }

        }

        return fileName;
    }

    private String getCurrentFileName(int ownDomainId, boolean isMobile) {

        if (ownDomainId != 765 && ownDomainId != 2047) {
            return null;
        }

        String device = isMobile ? "mobile" : "desktop";
        String fileName = ownDomainId + "_RankExtract_current_" + device + ".csv";

        return fileName;
    }


    private void addHeadersForExactFile(File outFile, OwnDomainEntity ownDomainEntity, Date processDate) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();

        if (extractType == 1) {
            if (ownDomainEntity.getId() == 765 || ownDomainEntity.getId() == 2047) {
                header.append("Keyword").append(SPLIT);
                header.append("Average Search Volume").append(SPLIT);
                header.append(ownDomainEntity.getDomain() + " " + FormatUtils.formatDate(processDate, "MM/dd/yyyy")).append(SPLIT);
                header.append("Ranked Page");
            } else if (ownDomainEntity.getId() == 4) {//todo

            } else {
                header.append("Date").append(SPLIT);
                header.append("Keyword").append(SPLIT);
                header.append("Average Search Volume").append(SPLIT);
                header.append("Rank").append(SPLIT);
                header.append("Ranked Page");
            }
        } else if (extractType == 2) {

            if (isKeyword) {
                header.append("Keyword").append(SPLIT);
            } else {
                header.append("Page").append(SPLIT);
            }
            header.append("Average Position").append(SPLIT);
            header.append("Clicks").append(SPLIT);
            header.append("Impressions");
        }


        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity, boolean isMobile) {

        List<String> extractLines = new ArrayList<String>();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();

        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        String domainReverse = null;
        String rootDomainReverse = null;

        String[] domainInfo = domainName.split("\\.");
        log.info("===domainInfo: " + new Gson().toJson(domainInfo));
        domainReverse = domainInfo[2] + "." + domainInfo[1] + "." + domainInfo[0];
        rootDomainReverse = domainInfo[2] + "." + domainInfo[1];

//        int type = RankTypeManager.TYPE_WEB_RESOURCE;
//        String[] domainUrlList = CommonUtils.splitString(domainName);
//        String newDomainName = RankTypeManager.convertToNewDomainName(domainName, type, domainUrlList[1]);
//        String rootDomain = getRootDomain(newDomainName);

        List<CLRankingDetailEntity> dataList = new ArrayList<>();

//        if (ownDomainId == 765 || ownDomainId == 2047) {
//            dataList = clDailyRankingEntityDao.exportKeywordsFor765(true, ownDomainEntity.getId(), engineId, languageId,
//                    rankingDate, isBroadMatch, isMobile, domainReverse, rootDomainReverse);
//        } else if (ownDomainId == 7464) {
//            dataList = clDailyRankingEntityDao.exportTop100KeywordsFor7464(ownDomainEntity.getId(), engineId, languageId, rankingDate, isMobile);
//        }

        for (CLRankingDetailEntity detail : dataList) {
            try {
                extractLines.add(appendData(detail, ownDomainId));
            } catch (Exception e) {
                monitorEntity.setFatalError(e.getMessage());
                e.printStackTrace();
                continue;
            }
        }

        return extractLines;
    }

    public static String appendData(CLRankingDetailEntity clRankingDetailEntity, int domainId) {
        StringBuffer line = new StringBuffer();

        if (!(domainId == 765 || domainId == 2047)) {
            line.append(clRankingDetailEntity.getDate()).append(SPLIT);
        }

        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        if (clRankingDetailEntity.getAvgSearchVolume().equals(-1l)) {
            line.append("0").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
        }
        if (clRankingDetailEntity.getRank().equals(-1) || clRankingDetailEntity.getRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getRank()).append(SPLIT);
        }
        line.append(clRankingDetailEntity.getUrl() == null ? "" : clRankingDetailEntity.getUrl());

        return line.toString();
    }

    public static String appendGSCData(GscEntity gscEntity) {
        StringBuffer line = new StringBuffer();
        if (isKeyword) {
            line.append(gscEntity.getKeywordName()).append(SPLIT);
        } else {
            line.append(gscEntity.getUrl()).append(SPLIT);
        }
        line.append(gscEntity.getPosition()).append(SPLIT);
        line.append(gscEntity.getClicks()).append(SPLIT);
        line.append(gscEntity.getImpressions());

        return line.toString();
    }

    private void copyFileToRemoteServer(int serverType, int domainId, String localFilePath, String remoteFilePath, String s3KeyPath) throws Exception {//todo

        int getServerInfoDomainId = domainId;
        if (domainId == 2047) {
            getServerInfoDomainId = 765;
        }

        boolean isSendS3Success = false;
        if(domainId == 765 || domainId == 2047){//https://www.wrike.com/open.htm?id=1657176186
            isSendS3Success = serverAuthenticationInfoService.putFileForS33WithRoleAndSSE(ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3, getServerInfoDomainId, localFilePath, s3KeyPath,
                    "arn:aws:iam::902399682238:role/marketingtechnology-seo-clarity-prod", "seoclarityDev-externalId-assumeRole",   Regions.US_EAST_2, 3600, 3);
        }else {
            //https://www.wrike.com/open.htm?id=995813703
            isSendS3Success = serverAuthenticationInfoService.putFileForS3WithSSE(ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3, getServerInfoDomainId, localFilePath, s3KeyPath);
        }

        if(!isSendS3Success){
            log.error("====send to s3 failed!!");
            String subject = getEmailSubject(domainId, false);
            String message = subject;
            sendMailReport(subject, message);
        }

    }

    private String getEmailSubject(int domainId, boolean success) {

        String subject = "";
        String status = success ? "Success" : "Failed";
        switch (domainId) {
            case 765:
                subject = status + " Export Cubesmart GSC " + FormatUtils.formatDateToYyyyMmDd(processDate);
            case 2047:
                subject = status + " Export Cubesmart GSC " + FormatUtils.formatDateToYyyyMmDd(processDate);
            default:
                break;
        }

        return subject;
    }

    private static String getRootDomain(String fullDomain) {
        String domainName = null;
        try {
            domainName = StringUtils.reverseDelimited(fullDomain, '.');
            return StringUtils.reverseDelimited(InternetDomainName.from(domainName).topPrivateDomain().toString(), '.');
        } catch (Exception e) {
            try {
                if (StringUtils.startsWithIgnoreCase(domainName, "www.")) {
                    return StringUtils.reverseDelimited(StringUtils.removeStartIgnoreCase(domainName, "www."), '.');
                } else {
                    return StringUtils.reverseDelimited(domainName, '.');
                }
            } catch (Exception ex) {
            }
        }
        return null;
    }

    private void sendMailReport(String subject, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason"); // TODO
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>"; // TODO
//        String[] ccTo = new String[]{""};
        String[] ccTo = null;
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap,
                null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    public static void main(String[] args) {

//        domainId = 765;
//        processDate = FormatUtils.toDate("2019-12-04", FormatUtils.DATE_PATTERN_2);
//        ExportTopXRankData exportTopXRankData = new ExportTopXRankData();
//        exportTopXRankData.processForDomain(domainId);

        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainIdList = args[0].split(",");
            } else {
                domainId = Integer.parseInt(args[0]);
            }

        }

//        if (domainId == 765) {
//            domainIdList = new String[]{ "765", "2047" };
//        }

        if (args.length >= 3 && args[2] != null) {
            extractType = Integer.parseInt(args[2]);
        }

        if (args.length >= 4 && args[3] != null) {
            isKeyword = Boolean.parseBoolean(args[3]);
        }

        ExportGSCData exportTopXRankData = new ExportGSCData();

        if (domainIdList != null && domainIdList.length > 0) {

            for (String processingDomainId : domainIdList) {

                if (args.length >= 2 && StringUtils.containsIgnoreCase(args[1], ",")) {
                    Date sDate = FormatUtils.toDate(args[1].split(",")[0], FormatUtils.DATE_PATTERN_2);
                    Date eDate = FormatUtils.toDate(args[1].split(",")[1], FormatUtils.DATE_PATTERN_2);
                    isRerun = true;
                    while (sDate.compareTo(eDate) <= 0) {
                        processDate = sDate;
                        exportTopXRankData.processForDomain(Integer.parseInt(processingDomainId));
                        sDate = DateUtils.addDays(sDate, 1);
                    }

                } else {
                    processDate = DateUtils.addDays(new Date(), -5);
                    exportTopXRankData.processForDomain(Integer.parseInt(processingDomainId));
                }
            }

        } else {

            if (args.length >= 2 && StringUtils.containsIgnoreCase(args[1], ",")) {
                Date sDate = FormatUtils.toDate(args[1].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[1].split(",")[1], FormatUtils.DATE_PATTERN_2);
                isRerun = true;
                while (sDate.compareTo(eDate) <= 0) {
                    processDate = sDate;
                    exportTopXRankData.processForDomain(domainId);
                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else {
                processDate = DateUtils.addDays(new Date(), -5);
                exportTopXRankData.processForDomain(domainId);
            }

        }


    }


}
