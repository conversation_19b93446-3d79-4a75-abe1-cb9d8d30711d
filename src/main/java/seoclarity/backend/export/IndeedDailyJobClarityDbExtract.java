package seoclarity.backend.export;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.export.IndeedDailyJobClarityDbExtract" -Dexec.args="" -Dexec.cleanupDaemonThreads=false
 *
 * <AUTHOR>
 */
@CommonsLog
public class IndeedDailyJobClarityDbExtract {


    private static final String FTP_SERVER_HOST = FTPUtils.FTP_SERVER;
    private static final String FTP_SERVER_USR = FTPUtils.FTP_SERVER_USER;
    private static final String FTP_SERVER_PWD = FTPUtils.FTP_SERVER_PW;
    private static final int QUERY_TRY_COUNT = 10;

    private static String queryDateStr;
    private static DateTime processDate;

    private static String queryToDateStr;
    private static String outputFile;
    private static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ExtractService extractService;

    private static final String SPLIT = "	";

    public IndeedDailyJobClarityDbExtract() {
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
    }

    public void process(int domainId, int engineId, int languageId, String device, String table, String countryCode, String targetDomainName) throws Exception {

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + domainId);
            try {
                extractService.sendMailReport("ERROR:Export for inactive OID:" + domainId, "Please disable export for inactive OID:" + domainId + "(" + getClass().getName() + ")");
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            return;
        }

        String tableName = device + "_ranking_info_" + table + "_" + processDate.toString("yyyyMM");
        String tableDetailName = device + "_ranking_detail_" + table + "_" + processDate.toString("yyyyMM");

        String sql = "SELECT "
                + "    keyword_name, "
                + "    avg_search_volume, "
                + "    jobs "
                + " FROM "
                + "("
                + "    SELECT "
                + "        keyword_rankcheck_id, "
                + "        keyword_name, "
                + "        avg_search_volume"
                + "    FROM seo_daily_ranking." + tableName
                + "    WHERE (1 = 1) AND (own_domain_id = " + domainId + ") AND (engine_id = " + engineId + ") AND (language_id = " + languageId + ") AND (location_id = 0) AND (sign = 1) AND (ranking_date = '" + processDate.toString("yyyy-MM-dd") + "')"
                + ")t1 "
                + "ANY LEFT JOIN "
                + "("
                + "    SELECT "
                + "        keyword_rankcheck_id, "
                + "        any(attrs.value[indexOf(attrs.key, 'job_link')]) AS jobs"
                + "    FROM merge(seo_daily_ranking, '^" + tableDetailName + "$')"
                + "    WHERE (own_domain_id = " + domainId + ") AND (engine_id = " + engineId + ") AND (language_id = " + languageId + ") AND (location_id = 0) AND (sign = 1) AND (ranking_date = '" + processDate.toString("yyyy-MM-dd") + "') AND ((attrs.value[indexOf(attrs.key, 'job_link')]) != '-')"
                + "    GROUP BY keyword_rankcheck_id"
                + ")t2 USING (keyword_rankcheck_id)";
        System.out.println("Query : " + sql);
        List<Map<String, Object>> resultList = clDailyRankingEntityDao.queryForAll(sql);


        File outPutFile = new File("/tmp/" + processDate.toString("yyyyMMdd") + "_" + countryCode + "_" + device + "_" + domainId + "_job_result.csv");
        outPutFile.delete();
        CSVPrinter csvPrinter = new CSVPrinter(new FileWriter(outPutFile), CSVFormat.DEFAULT);
        csvPrinter.printRecord("Country", "Domain Id", "Own domain", "Search Engine", "Date", "Domain Name", "Domain Listing Count", "Total Listing", "Domain Share", "Total Visibility");
        Map<String, Integer> domainCount = new HashMap<>();
        Map<String, Long> domainVsCount = new HashMap<>();
        int totalListing = 0;
        for (Map<String, Object> objectMap : resultList) {
            if (objectMap.containsKey("jobs") == false || StrUtil.isBlank(objectMap.get("jobs").toString())) {
                continue;
            }
            String jobStr = objectMap.get("jobs").toString();
            String sv = objectMap.get("avg_search_volume").toString();

            String[] jobArray = jobStr.split("!_!");
            Map<String, Integer> domainTmpCount = new HashMap<>();
            for (String jobName : jobArray) {
                if (StrUtil.isBlank(jobName)) {
                    continue;
                }
                if (domainTmpCount.containsKey(jobName) == false) {
                    domainTmpCount.put(jobName, 0);
                }
                domainTmpCount.put(jobName, domainTmpCount.get(jobName) + 1);
            }

            for (Map.Entry<String, Integer> entry : domainTmpCount.entrySet()) {
                String domainName = entry.getKey();
                int count = entry.getValue();
                if (domainCount.containsKey(domainName) == false) {
                    domainCount.put(domainName, 0);
                }
                domainCount.put(domainName, domainCount.get(domainName) + count);

                if (domainVsCount.containsKey(domainName) == false) {
                    domainVsCount.put(domainName, 0L);
                }
                long totalVs = NumberUtil.parseLong(sv) * count;
                domainVsCount.put(domainName, domainVsCount.get(domainName) + totalVs);
                totalListing += count;
            }
        }
        for (Map.Entry<String, Integer> entry : domainCount.entrySet()) {
            String domainName = entry.getKey();
            int count = entry.getValue();
            long vs = domainVsCount.get(domainName);
            csvPrinter.printRecord(countryCode, domainId, targetDomainName, device.equals("m") ? "Google Mobile" : "Google Desktop", processDate.toString("MM/dd/yyyy"), domainName, count, totalListing, NumberUtil.div(count, totalListing, 4) * 100 + "%", vs);
        }

        csvPrinter.flush();
        csvPrinter.close();

        copyBySSH("/home/<USER>/8711/", outPutFile.getAbsolutePath());
    }

    private void copyBySSH(String remotePath, String localFile) throws Exception {
        Connection connection = new Connection(FTP_SERVER_HOST);
        connection.connect();
        if (connection.authenticateWithPassword(FTP_SERVER_USR, FTP_SERVER_PWD)) {
            SCPClient scpClient = connection.createSCPClient();
            System.out.println(" ===remote:" + remotePath);

            try {
                scpClient.put(localFile, remotePath);
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception(" Failed to copy files from host... file:" + remotePath, e);
            } finally {
                connection.close();
            }
            System.out.println(" ===Saved file:" + remotePath + " to " + localFile);
        }
    }

    public static void main(String[] args) {
        IndeedDailyJobClarityDbExtract copyKeywordsInMongo = new IndeedDailyJobClarityDbExtract();
        if (args != null && args.length >= 1) {
            queryDateStr = args[0]; // yyyy-mm-dd ()
            String[] dateStr = queryDateStr.split(",");
            List<String> dateList = new ArrayList<>();
            if (dateStr.length > 1) {
                dateList = seoclarity.backend.utils.DateUtils.getAllDateBetweenDate1Date2(dateStr[0], dateStr[1], "yyyy-MM-dd");
            } else {
                dateList.add(queryDateStr);
            }
            for (String date : dateList) {
                processDate = DateUtil.parseDate(date);
                System.out.println(" date :" + processDate);
                try {
                    copyKeywordsInMongo.process(8711, 1, 1, "m", "us", "US", "indeed.com");
                    copyKeywordsInMongo.process(8711, 1, 1, "d", "us", "US", "indeed.com");
                    copyKeywordsInMongo.process(11284, 15, 16, "m", "intl", "BR", "br.indeed.com");
                    copyKeywordsInMongo.process(11284, 15, 16, "d", "intl", "BR", "br.indeed.com");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            System.out.println("no param daily job ");
            Date LastSunday = FormatUtils.getLastSundayForWeeklyDomainExtract(new Date());
            processDate = DateUtil.parseDate(FormatUtils.formatDate(LastSunday, FormatUtils.DATE_PATTERN_2));
            queryDateStr = processDate.toString("yyyyMMdd");
            try {
                System.out.println(" queryDateStr: " + queryDateStr + " copyToDateStr: " + queryToDateStr + " filePath:" + outputFile);
                copyKeywordsInMongo.process(8711, 1, 1, "m", "us", "US", "indeed.com");
                copyKeywordsInMongo.process(8711, 1, 1, "d", "us", "US", "indeed.com");
                copyKeywordsInMongo.process(11284, 15, 16, "m", "intl", "BR", "br.indeed.com");
                copyKeywordsInMongo.process(11284, 15, 16, "d", "intl", "BR", "br.indeed.com");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }

}
