package seoclarity.backend.export.rankQcExtract;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.monthlyranking.AioContentDAO;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.upload.ExtractRedVenturesKeywords;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1656296952
 * deployed daily job
 */
@CommonsLog
public class AIOContentJSONExtract {

    private static final String LOCAL_FOLDER = "/home/<USER>/";
    private static final String S3_BUCKET_KEY_PREFIX = "top_keywords_aio_content";

//    private static String device;

    private AioContentDAO aioContentDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;

    private Gson gson = new Gson();

    public AIOContentJSONExtract() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        aioContentDAO = SpringBeanFactory.getBean("aioContentDAO");
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
    }

    public static void main(String[] args) throws Exception{
        AIOContentJSONExtract aioContentJSONExtract = new AIOContentJSONExtract();
        if (args.length >= 1 && StringUtils.containsIgnoreCase(args[0], ",")) {
            Date sDate = FormatUtils.toDate(args[0].split(",")[0], FormatUtils.DATE_PATTERN_2);
            Date eDate = FormatUtils.toDate(args[0].split(",")[1], FormatUtils.DATE_PATTERN_2);
            while (sDate.compareTo(eDate) <= 0) {
                String processingDate = FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2);
                aioContentJSONExtract.processForRVCompany(processingDate, CommonDataService.RV_COMPANY_NAME);
                aioContentJSONExtract.processForRVCompany(processingDate, CommonDataService.RV_SECONDARY_OMPANY_NAME);
                sDate = DateUtils.addDays(sDate, 1);
            }

        } else if(args.length >= 1 && !StringUtils.containsIgnoreCase(args[0], ",")){
            String processingDate = args[0];
            aioContentJSONExtract.processForRVCompany(processingDate, CommonDataService.RV_COMPANY_NAME);
            aioContentJSONExtract.processForRVCompany(processingDate, CommonDataService.RV_SECONDARY_OMPANY_NAME);

        } else {
            String processingDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -1), FormatUtils.DATE_PATTERN_2);
            aioContentJSONExtract.processForRVCompany(processingDate, CommonDataService.RV_COMPANY_NAME);
            aioContentJSONExtract.processForRVCompany(processingDate, CommonDataService.RV_SECONDARY_OMPANY_NAME);
        }
    }

    private void processForRVCompany(String processingDate, String companyName) throws Exception{

        log.info("========================processForRVCompany" + companyName + ",processingDate:" + processingDate);
        String processDateIntStr = FormatUtils.formatDate(FormatUtils.toDate(processingDate, FormatUtils.DATE_PATTERN_2), FormatUtils.DATE_FORMAT_YYYYMMDD);

        File folder = new File("/home/<USER>/RVAioExtract/");

        String fileNameCompany = "";
        if(companyName.equalsIgnoreCase(CommonDataService.RV_COMPANY_NAME)){
            fileNameCompany = "RVAIOContent";
        } else if (companyName.equalsIgnoreCase(CommonDataService.RV_SECONDARY_OMPANY_NAME)) {
            fileNameCompany = "RVHealthAIOContent";
        }


        String fileName = fileNameCompany + "_" + processDateIntStr + ".json";
        File extractFile = new File("/home/<USER>/RVAioExtract/" + fileName);
        if(!folder.exists()){
            folder.mkdirs();
        }
        if(extractFile.exists()){
            extractFile.delete();
        }


        processForDevice(processingDate, companyName, "desktop", extractFile);
        processForDevice(processingDate, companyName, "mobile", extractFile);


        FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, extractFile.getAbsolutePath(), LOCAL_FOLDER + 7583, 0, 3);

        //RVS3
        boolean savedFilesToS3 = ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(processDateIntStr, extractFile.getAbsolutePath(), S3_BUCKET_KEY_PREFIX, false);
        if(!savedFilesToS3){
            log.error("=====savedFilesToS3 failed!   " + companyName + " ,processDateIntStr: " + processDateIntStr);
        }
        boolean savedFilesToAdditionalS3 = ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(processDateIntStr, extractFile.getAbsolutePath(), S3_BUCKET_KEY_PREFIX, true);
        if(!savedFilesToAdditionalS3){
            log.error("=====savedFilesToAdditionalS3 failed!   " + companyName + " ,processDateIntStr: " + processDateIntStr);
        }

    }

    private void processForDevice(String processingDate, String companyName, String device, File extractFile){
        String queryDevice = device.equalsIgnoreCase("mobile") ? "m" : "d";
        int locationId = 0;
        int frequency = 1;
        int pageSize = 100;

        List<Integer> domainIdList = new ArrayList<Integer>();
        List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(companyName);
//        List<OwnDomainEntity> secondaryDomainList = ownDomainEntityDAO.getDomainListBasedCompanyName(CommonDataService.RV_SECONDARY_OMPANY_NAME);
        Set<String> elSet  = new HashSet<String>();
        if (domainList != null) {
            domainIdList = domainList.stream().map(var -> var.getId()).collect(Collectors.toList());
            System.out.println("======== Company: " + companyName + " ========domainIdList:" + gson.toJson(domainIdList));

            for(OwnDomainEntity ownDomainEntity : domainList){
                int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
                int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
                String elKey = engineId + "_" + languageId;
                elSet.add(elKey);
            }
        }

        log.info("===========ST ELSet:" + gson.toJson(elSet));
        for(String elKey: elSet){
            log.info("===========ST ELKey:" + elKey);
            int engineId = Integer.parseInt(elKey.split("_")[0]);
            int languageId = Integer.parseInt(elKey.split("_")[1]);

            EngineCountryLanguageMappingEntity engineCountryLanguageMapping = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageId(engineId, languageId);
            String countryName = engineCountryLanguageMapping.getCountryQueryName();
            String engineName = engineCountryLanguageMapping.getEngineDisplayName();
            String languageName = engineCountryLanguageMapping.getLanguageQueryName();

            ExtractQueryVO extractQueryVO = new ExtractQueryVO();
            extractQueryVO.setDomainIdList(domainIdList);
            extractQueryVO.setEngineId(engineId);
            extractQueryVO.setLanguageId(languageId);
            extractQueryVO.setDevice(queryDevice);
            extractQueryVO.setRankDate(processingDate);
            extractQueryVO.setLocationId(locationId);
            extractQueryVO.setFrequency(frequency);
            extractQueryVO.setPageSize(pageSize);

            int pageNum = 0;
            while (true){
                try {
                    extractQueryVO.setPageNum(pageNum);
                    List<CLRankingDetailEntity> aioContentList = aioContentDAO.getRIAIOContent(extractQueryVO);
                    if(CollectionUtils.isEmpty(aioContentList)){
                        break;
                    }
                    pageNum++;
                    log.info("===Page num : " + pageNum + ", pageSize : " + pageSize + ", size:" + aioContentList.size());
                    List<String> lineList = new ArrayList<>();
                    for (CLRankingDetailEntity aioContent : aioContentList) {
                        AIOContentJSON aioContentJSON = new AIOContentJSON();
                        aioContentJSON.setId(aioContent.getKeywordRankcheckId());
                        aioContentJSON.setKeywordName(aioContent.getKeywordName());
                        aioContentJSON.setRankingDate(processingDate);
                        aioContentJSON.setDevice(device);
                        aioContentJSON.setAioContent(FormatUtils.safeStringToJson(aioContent.getAioContent()));
                        aioContentJSON.setSearchEngine(engineName);
                        aioContentJSON.setLanguage(languageName);
                        aioContentJSON.setCountry(countryName);
                        lineList.add(gson.toJson(aioContentJSON));
                    }

                    FileUtils.writeLines(extractFile, lineList, true);

                }catch (Exception e){
                    e.printStackTrace();
                }
            }

        }


    }


}

class AIOContentJSON {

    private Long id;
    private String searchEngine;
    private String language;
    private String country;
    private String device;
    private String keywordName;
    private String rankingDate;
    private String aioContent;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKeywordName() {
        return keywordName;
    }

    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    public String getRankingDate() {
        return rankingDate;
    }

    public void setRankingDate(String rankingDate) {
        this.rankingDate = rankingDate;
    }

    public String getAioContent() {
        return aioContent;
    }

    public void setAioContent(String aioContent) {
        this.aioContent = aioContent;
    }

    public String getSearchEngine() {
        return searchEngine;
    }

    public void setSearchEngine(String searchEngine) {
        this.searchEngine = searchEngine;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }
}
