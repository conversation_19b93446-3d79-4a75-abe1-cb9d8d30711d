package seoclarity.backend.export.rankQcExtract;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.rankcheck.SeoClaritySearchEngineEntityDAO;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityCityEntity;
import seoclarity.backend.service.*;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@CommonsLog
public class QcExtract9832 {

    private static final String SPLIT = "\t";
    private static final String KEY_SPLIT = "#_#";

    private static final Integer EXTRACT_RANK = 100;
    private static final int QUERY_TRY_COUNT = 10;

    private static String LOC = "/home/<USER>/";
    private static boolean ignoreQC = false;
    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    private SeoClaritySearchEngineEntityDAO seoClaritySearchEngineEntityDAO;
    private CdbTrackedKeywordEntityDAO cdbTrackedKeywordEntityDAO;
    private ExtractService extractService;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private GeoService geoService;

    private static String startDate;
    private static String endDate;
    private static boolean isCity = false;
    private static String processingDate;

    private int searchEngineId;
    private int searchLanguageId;
    private String processCountry;
    private String searchEngineName;

    public QcExtract9832() {
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        seoClaritySearchEngineEntityDAO = SpringBeanFactory.getBean("seoClaritySearchEngineEntityDAO");
        cdbTrackedKeywordEntityDAO = SpringBeanFactory.getBean("cdbTrackedKeywordEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        geoService = SpringBeanFactory.getBean("geoService");
    }

    public static final Map<Integer, String> COUNTRY_MAP = new HashMap();
    static {
        COUNTRY_MAP.put(9832, "US");
    }

    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();

    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(9832, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
    }

    public static void main(String[] args) {


        String[] domainIds = null;

        if (args != null && args.length > 0) {
            if(args[0].contains(",")){
                domainIds = args[0].split(",");
            }else {
                domainIds = new String[]{args[0]};
            }
        }else {
            System.out.println("===param error!!");
            return;
        }


        Date sDate = null;
        Date eDate = null;
        if (args != null && args.length > 2 && !args[1].equalsIgnoreCase("null") && !args[2].equalsIgnoreCase("null")) {
            startDate = args[1];
            endDate = args[2];
            sDate = FormatUtils.toDate(args[1], FormatUtils.DATE_PATTERN_2);
            eDate = FormatUtils.toDate(args[2], FormatUtils.DATE_PATTERN_2);
        } else {
            Date sTime = FormatUtils.getYesterday(true);
            startDate = FormatUtils.formatDate(sTime, FormatUtils.DATE_PATTERN_2);
            endDate = FormatUtils.formatDate(sTime, FormatUtils.DATE_PATTERN_2);
            sDate = sTime;
            eDate = sTime;
        }


        System.out.println("=====domainIds: " + domainIds  + " ,startDate: " + startDate + ",endDate: " + endDate);

        QcExtract9832 qcExtract9832 = new QcExtract9832();

        while (sDate.compareTo(eDate) <= 0) {

            qcExtract9832.processDate(sDate, domainIds, "desktop");
            qcExtract9832.processDate(sDate, domainIds, "mobile");
            sDate = DateUtils.addDays(sDate, 1);
        }


    }

    private void processDate(Date sDate, String[] domainIds, String device){

        processingDate = FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2);
        try{
            for (String domainId : domainIds) {
                int retryCount = 0;
                while (true) {
                    try {
                        System.out.println("********************** for domain " + domainId + " *******************");
                        String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

                        process(Integer.parseInt(domainId), processingDate, device,sDate);

                        logglyVO.setStatus(LogglyVO.STATUS_OK);
                        logglyVO.setsTime(stTime);
                        logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
                        logglyVO.setRows(String.valueOf(totalCnt));
                        String body = new Gson().toJson(logglyVO);
                        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                        break;
                    } catch (Exception e) {
                        if (retryCount >= QUERY_TRY_COUNT) {
                            System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
                            logglyVO.setStatus(LogglyVO.STATUS_NG);
                            String body = new Gson().toJson(logglyVO);
                            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                            String message = "9832 extract " + domainId + "  " + device + " " + processingDate + " Failed !!! ";
                            sendMailReport("Failed !!!! ", message);
                            break;
                        }
                        e.printStackTrace();
                        System.out.println("====domain error :" + domainId + ", sleep 20s ");
                        Thread.sleep(1000 * 20);
                        retryCount++;
                    }
                }
            }
        }catch (Exception ex){
            ex.printStackTrace();
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
            String message = "9832 extract "  + device + " " + processingDate + " Failed !!! ";
            sendMailReport("Failed !!!! ", message);
        }

    }

    private void process(int domainId, String rankDate, String device,Date sDate) throws Exception{

        System.out.println("========start to extract domain : " + domainId + ",rankDate: " + rankDate);

        try {

            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(domainId);
            if (ownDomainEntity == null) {
                System.out.println("=== domain not exist , exit !!");
                try {
                    extractService.sendMailReport("ERROR:Export for inactive OID:" + domainId, "Please disable export for inactive OID:" + domainId + "(" + getClass().getName() + ")");
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
                return;
            }
            int frequency = 1;//daily
            if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
                sDate = FormatUtils.getLastSundayForWeeklyDomainExtract(sDate);
                System.out.println("====weekly domain processDate:" + sDate);
                frequency = 7;//weekly
            }

            String pDate = FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2);
            logglyVO.setoId(String.valueOf(domainId));
            logglyVO.setName("QcExtract9832");
            logglyVO.setDevice(device.startsWith("m")?"m":"d");

            logglyVO.setpDate(pDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
            logglyVO.setGroups(groupList);

            searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
            searchLanguageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);

            if (!ignoreQC) {
                int rankDateInt = FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankDate, FormatUtils.DATE_PATTERN_2));
                boolean isPassedNational = extractService.isRankQcPass(rankDateInt, domainId, searchEngineId, searchLanguageId, device, RankQcStateEntity.RANK_TYPE_NATIONAL, RankQcStateEntity.FREQUENCE_DAILY);
                boolean isPassedGeo = extractService.isRankQcPass(rankDateInt, domainId, searchEngineId, searchLanguageId, device, RankQcStateEntity.RANK_TYPE_GEO, RankQcStateEntity.FREQUENCE_DAILY);
//            isPassed = true;
                if (!isPassedNational || !isPassedGeo) {
                    log.error("===not pass rank qc!! 9832 extract " + domainId + "  " + device + " " + processingDate);
                    logglyVO.setStatus(LogglyVO.STATUS_NG);
                    String body = new Gson().toJson(logglyVO);
                    LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//                    String message = "NOt Pass QC 9832 extract " + domainId + "  " + device + " " + processingDate + " !!! ";
//                    sendMailReport("NOt Pass QC 9832 !!!! ", message);
                    return;
                }
            }

            processCountry = COUNTRY_MAP.get(domainId);

            String fileName = ownDomainEntity.getDomain() + "_" + rankDate  + ".txt";
            String filePath = LOC + domainId + File.separator + ownDomainEntity.getDomain()  + "_" + rankDate + ".txt";
            String remoteFilePath = LOC + domainId;
            File outFile = new File(filePath);
            if (device.equalsIgnoreCase("desktop") && outFile.exists()) {
                outFile.delete();
            }

            if(device.equalsIgnoreCase("desktop")){
                addHeadersForExactFile(outFile);
            }

            searchEngineName = seoClaritySearchEngineEntityDAO.getEngineNameById(searchEngineId);
            //get ranking data from kp
            ExtractQueryVO queryEntity = new ExtractQueryVO();
            queryEntity.setDomainId(domainId);
            queryEntity.setEngineId(searchEngineId);
            queryEntity.setLanguageId(searchLanguageId);
            queryEntity.setDevice(device);
            queryEntity.setRankDate(rankDate);
            queryEntity.setRank(EXTRACT_RANK);
            queryEntity.setHasWebRank(false);

            //subRank
            Map<String, List<CLRankingDetailEntity>> subRankMap = new HashMap<>();
            List<CLRankingDetailEntity> subRankList = clDailyRankingEntityDao.getSubRankInfo(queryEntity, searchEngineId, searchLanguageId);
            for (CLRankingDetailEntity subRank : subRankList) {

                String subRankKey = subRank.getKeywordRankcheckId() + KEY_SPLIT + subRank.getLocationId() + KEY_SPLIT + subRank.getRank();

                List<CLRankingDetailEntity> subRankInfoList = new ArrayList<>();
                if (subRankMap.get(subRankKey) == null) {
                    subRankInfoList.add(subRank);
                    subRankMap.put(subRankKey, subRankInfoList);
                } else {
                    subRankInfoList = subRankMap.get(subRankKey);
                    subRankInfoList.add(subRank);
                    subRankMap.put(subRankKey, subRankInfoList);
                }

            }

            Map<String, String> tagMap = new HashMap<>();
            List<CdbTrackedKeywordEntity> cdbTrackedKeywordList = cdbTrackedKeywordEntityDAO.getCdbListByDomainId(domainId);
            System.out.println("====cdbTrackedKeywordList size: " + cdbTrackedKeywordList.size());
            if(!CollectionUtils.isEmpty(cdbTrackedKeywordList)){
                for(CdbTrackedKeywordEntity cdb : cdbTrackedKeywordList){
                    String key = cdb.getKeywordRankcheckId() + KEY_SPLIT + cdb.getLocationId();
                    if(tagMap.get(key) != null){
                        String tags = tagMap.get(key);
                        tags = tags + "," + cdb.getTagName();
                        tagMap.put(key, tags);
                    }else {
                        tagMap.put(key, cdb.getTagName());
                    }

                }
            }

            //geo rank
            getDetailData(tagMap,queryEntity,true, subRankMap, outFile, device, domainId);
            //national location = 0
            getDetailData(tagMap,queryEntity,false, subRankMap, outFile, device, domainId);

//            String zipFileName =  GZipUtil.zipWithFileName(filePath);
//            System.out.println("===zipFileName: " + zipFileName);
//            outFile.delete();

            //send to ftp
//            FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, zipFileName, LOC + 7605 + File.separator +"dailyRank",0 , 3);

            int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
            if (SPECIAL_DOMAIN_SERVER_MAP.get(domainId) != null) {
                serverType = SPECIAL_DOMAIN_SERVER_MAP.get(domainId);
            }

            if(device.equalsIgnoreCase("mobile")){
                serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, domainId, filePath, null, null);
                // send files to ftp for tss check data
                FTPUtils.saveFileToFTP(domainId, filePath, remoteFilePath);
            }

        } catch (Exception e) {
//            e.printStackTrace();
            throw e;

        }

    }

    private void getDetailData(Map<String, String> tagMap, ExtractQueryVO queryEntity, boolean isCity,
                               Map<String, List<CLRankingDetailEntity>> subRankMap, File outFile, String device, int domainId) throws Exception{

        List<String> extractList = new ArrayList<>();

        //https://www.wrike.com/open.htm?id=614656875
        List<Integer> locationIdList = geoService.getLocationIdList(domainId);
        System.out.println("locationIdList=>"+locationIdList);
        Integer [] idArr = locationIdList.toArray(new Integer[locationIdList.size()]);

        //detail rank
        List<CLRankingDetailEntity> rankingDetailList = clDailyRankingEntityDao.exportForDomainV2(queryEntity, isCity, searchEngineId, searchLanguageId, idArr);
        System.out.println("====rankingDetailList size: " + rankingDetailList.size());

        int nationalKwCnt = 0;
        int geoCityCnt = 0;
        int geoKwCityCnt = 0;
        if (isCity) {
            /**geo city cnt*/
            geoCityCnt = rankingDetailList.stream().filter(var->var.getLocationId() > 0).collect(Collectors.groupingBy(var->var.getLocationId())).size();
            /**geo kw-city cnt*/
            geoKwCityCnt = rankingDetailList.stream().filter(var->var.getLocationId() > 0).collect(Collectors.groupingBy(var->var.getLocationId()+"-"+var.getKeywordRankcheckId())).size();

            System.out.println("statistics=>device: " + device + ", geoCityCnt: " + geoCityCnt);
            System.out.println("statistics=>device: " + device + ", geoKwCityCnt: " + geoKwCityCnt);
        }else {
            /**national kw cnt*/
            nationalKwCnt = rankingDetailList.stream().filter(var->var.getLocationId() == 0).collect(Collectors.groupingBy(var->var.getKeywordRankcheckId())).size();
            System.out.println("statistics=>device: " + device + ", nationalKwCnt: " + nationalKwCnt);
        }

        Set<String> locationNameSet = new HashSet<>();
        for (CLRankingDetailEntity clRankingDetailEntity : rankingDetailList) {
            Integer locationId = clRankingDetailEntity.getLocationId();
            if (locationId != null && locationId.intValue() != 0) {
                //System.out.println("locationId: " + locationId + " need to be converted");
                locationId = geoService.getGeoMasterId(locationId);
            }
            String key = clRankingDetailEntity.getKeywordRankcheckId() + KEY_SPLIT + locationId;

            String tagNames = tagMap.get(key);
            String locationName;
            if (clRankingDetailEntity.getLocationId().equals(0)) {
                locationName = "United States";
//                System.out.println("===getLocationId 0 ");
            } else {
                locationName = geoService.getCityName(clRankingDetailEntity.getLocationId());
            }

            KeywordRankEntityVO keywordRankEntityVO = new KeywordRankEntityVO();
            keywordRankEntityVO.setType(clRankingDetailEntity.getType());
            String typeName = keywordRankEntityVO.getUrlType();
            clRankingDetailEntity.setTypeName(typeName);

            locationNameSet.add(locationName);
            //CSV file
            extractList.add(writeLine(clRankingDetailEntity, locationName, tagNames, null, null, null, device));


            String subRankKey = clRankingDetailEntity.getKeywordRankcheckId() + KEY_SPLIT + clRankingDetailEntity.getLocationId()
                    + KEY_SPLIT + clRankingDetailEntity.getTrueRank();
            List<CLRankingDetailEntity> subRankInfoList = subRankMap.get(subRankKey);
            if (CollectionUtils.isNotEmpty(subRankInfoList)) {

                for (CLRankingDetailEntity subRankInfo : subRankInfoList) {

                    Integer subRank = subRankInfo.getSubRank();

//                    if (typeName.equals("LocalListing")) {
//                        String title = subRankInfo.getDomainReverse();
//                        extractList.add(writeLine(clRankingDetailEntity, locationName, tagNames, subRank, null, null,title));
//                    } else {
                        String subRankUrl = subRankInfo.getUrl();
                        String domainReverse = subRankInfo.getDomainReverse();
                        String subRankDomain = getDomainName(subRankUrl, domainReverse);
                        extractList.add(writeLine(clRankingDetailEntity, locationName, tagNames, subRank, subRankUrl, subRankDomain, device));

//                    }

                }

            }
            totalCnt = totalCnt + extractList.size();
            FileUtils.writeLines(outFile, extractList, true);
            extractList.clear();

        }
//        System.out.println("===locationNameSet:" + JSON.toJSONString(locationNameSet));
    }

    public String writeLine(CLRankingDetailEntity clRankingDetailEntity,String locationName, String tagNames,
                            Integer subRank, String subRankUrl, String subRankDomain, String device) {

        StringBuffer line = new StringBuffer();
        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        line.append(searchEngineName).append(SPLIT);
        line.append(device).append(SPLIT);
        line.append(processCountry).append(SPLIT);
        line.append(locationName).append(SPLIT);
        line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
        line.append(clRankingDetailEntity.getCpc()).append(SPLIT);
        line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
        line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);

        if (subRank == null) {
            line.append("101").append(SPLIT);
        } else {
            line.append(subRank).append(SPLIT);//sub rank
        }
        if (StringUtils.isBlank(subRankDomain)) {
            line.append("-").append(SPLIT);
        } else {
            line.append(subRankDomain).append(SPLIT);//sub rank
        }

        if (StringUtils.isBlank(subRankUrl)) {
            line.append("-").append(SPLIT);
        } else {
            line.append(subRankUrl).append(SPLIT);//sub rank
        }

        line.append(ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
//        if (StringUtils.containsIgnoreCase(clRankingDetailEntity.getUrl(), "www.google.com")) {
//            line.append("https://www.google.com/").append(SPLIT);
//        } else {
//            line.append(clRankingDetailEntity.getUrl()).append(SPLIT);
//        }

        line.append(getDomainName(clRankingDetailEntity.getUrl(), clRankingDetailEntity.getDomainReverse())).append(SPLIT);
        line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);

        if (clRankingDetailEntity.getAnswerBoxFlg() != null && !clRankingDetailEntity.getAnswerBoxFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getRatingNumber().equals("-")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getLlFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getPeopleAlsoAskFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getImgFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getNewsFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getVideoFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getPriceFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getQaFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getStockFlg().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }
        if (!clRankingDetailEntity.getRating().equals("0")) {
            line.append("y").append(SPLIT);
        } else {
            line.append("n").append(SPLIT);
        }

        line.append(processingDate).append(SPLIT);

        if (StringUtils.isNotBlank(tagNames)) {
            line.append(tagNames);
        } else {
            line.append("");
        }


        return line.toString();
    }


    public static void addHeadersForExactFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Keyword").append(SPLIT);
        header.append("Search Engine").append(SPLIT);
        header.append("Device").append(SPLIT);
        header.append("Country").append(SPLIT);
        header.append("Location").append(SPLIT);
        header.append("Search Volume").append(SPLIT);
        header.append("CPC").append(SPLIT);
        header.append("True Rank").append(SPLIT);
        header.append("Web Rank").append(SPLIT);
        header.append("Sub-rank").append(SPLIT);
        header.append("Sub-rank Domain").append(SPLIT);
        header.append("Sub-rank Url").append(SPLIT);
        header.append("url").append(SPLIT);
        header.append("domain").append(SPLIT);
        header.append("type").append(SPLIT);
        header.append("Answer Box Present").append(SPLIT);
        header.append("Ratings/Reviews Present").append(SPLIT);
        header.append("Local Listings Present").append(SPLIT);
        header.append("People Also Ask Present").append(SPLIT);
        header.append("Images Present").append(SPLIT);
        header.append("News Present").append(SPLIT);
        header.append("Video Present").append(SPLIT);
        header.append("URL Feature - Price").append(SPLIT);
        header.append("URL Feature - FAQ").append(SPLIT);
        header.append("URL Feature - Stock").append(SPLIT);
        header.append("URL Feature - Star").append(SPLIT);
        header.append("Date of rank check").append(SPLIT);
        header.append("Tags").append(SPLIT);

        lines.add(header.toString());

        FileUtils.writeLines(outFile, lines, true);
    }


    public static String getDomainName(String url, String domainReverse) {

        if (StringUtils.containsIgnoreCase(url, "www.google.com")) {

            String[] domainArray = domainReverse.split("\\.");
            if(domainArray.length <2 ){
                System.out.println("===url: " + url);
                return "google.com";
            }else {
                return domainArray[1] + "." + domainArray[0];
            }
        } else {
            if (!(url.startsWith("http://") || url.startsWith("https://"))) {
                return domainReverse;
            }
            String domainName = CommonUtils.getDomainByUrl(url);
            if (domainName.contains("www.")) {
                domainName = domainName.replaceAll("www.", "");
            }
            return domainName;
        }

    }

    private void sendMailReport(String status, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String subject = "9832 Extract " + status;
        String[] ccTo = new String[]{"<EMAIL>"};
//        String[] ccTo = null;
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_common.txt", "mail_common.html", reportMap, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private class DetailEntity {

        private String domain;
        private String url;
        private String https;
        private Integer rank;
        private String type;
        private List<SubRankEntity> sub_rank = null;
        private String title;
        private String details;

        public String getHttps() {
            return https;
        }

        public void setHttps(String https) {
            this.https = https;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDetails() {
            return details;
        }

        public void setDetails(String details) {
            this.details = details;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public List<SubRankEntity> getSub_rank() {
            return sub_rank;
        }

        public void setSub_rank(List<SubRankEntity> sub_rank) {
            this.sub_rank = sub_rank;
        }
    }

    private class SubRankEntity {

        private Integer rank;
        private String title;
        private String domain;
        private String url;
        private String https;

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getHttps() {
            return https;
        }

        public void setHttps(String https) {
            this.https = https;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }


}
