package seoclarity.backend.export.rankQcExtract;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.dao.clickhouse.pixelheight.PixelDetailDao;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.RankIndexParamVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.extract.ExtractScriptConfigEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.GeoService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1502213335
 */
@CommonsLog
public class VsV3JsonExtract {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static final String KEY_SPLIT = "#_#";
    private static String LOC = "/home/<USER>/";
    private static final String EXPEDIA_FTP_SERVER_PATH = "/VisibilityShare/";
    private static final int QUERY_TRY_COUNT = 10;
    private static final int KEYWORD_SPLIT_COUNT = 300;

    private static Date processDate;
    private static String[] domainIdList;
    private static int domainId;
    private static boolean extractTag = false;
    private static String device;
    private static boolean ignoreQC = false;
    private static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;


    private int rankDate;
    private int engineId;
    private int languageId;
    private int rankType;
    private static int topX;
    private static boolean isGeo = false;
    private static int frequence;
    private long startTime;
    private ExtractScriptDetailEntity extractScriptDetail;

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private PixelDetailDao pixelDetailDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private KeywordEntityDAO keywordEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private ExtractService extractService;
    private GeoService geoService;
    private MonthlyRankingDao monthlyRankingDao;
    private RankIndexParamEntityDAO rankIndexParamEntityDAO;
    private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
    private TagParentChildRelDAO tagParentChildRelDAO;
    private CommonParamDAO commonParamDAO;

    public VsV3JsonExtract() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        pixelDetailDao = SpringBeanFactory.getBean("pixelDetailDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
        geoService = SpringBeanFactory.getBean("geoService");
        monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
        rankIndexParamEntityDAO = SpringBeanFactory.getBean("rankIndexParamEntityDAO");
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
        tagParentChildRelDAO = SpringBeanFactory.getBean("tagParentChildRelDAO");
        commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
    }

    private static final List<Integer> EXPEDIA_DOMAIN_LIST = Arrays.asList(
            4739,4765,4727,4729,4730,4733,4735,4736,4738,4744,4747,4750,4752,4754,4755,4756,4761,4762,4763,5071,6373);

    //send to special remote server
    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();

    public static final Map<Integer, String> INDEED_DOMAIN_NAME_MAP = new HashMap();//(domainId, domainName)

    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(13235, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(9463, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
        SPECIAL_DOMAIN_SERVER_MAP.put(4739, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(11983, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP_WITH_KEY_FILE);
        SPECIAL_DOMAIN_SERVER_MAP.put(13297, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
    }

    //need extract secondary domains
    public static final List<Integer> EXTRACT_SECONDARY_DOMAIN_LIST = Arrays.asList();

    private static Map<String, String> engineNameMap = new HashMap<>();

    private void processForDomain(int ownDomainId) {

        log.info("********************** process domain " + ownDomainId + ",processDate: " + processDate + " *******************");

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);

        extractScriptDetail = new ExtractScriptDetailEntity();
        processExtract(ownDomainEntity, ownDomainSettingEntity);
    }

    private void processExtract(OwnDomainEntity ownDomainEntity, OwnDomainSettingEntity ownDomainSettingEntity) {

        int ownDomainId = ownDomainEntity.getId();
        int processingDate = FormatUtils.formatDateToYyyyMmDd(processDate);

        String fileName = getFileName(ownDomainEntity, processingDate);

        File localFolder = new File(LOC + ownDomainId);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }

        String localFilePath = LOC + ownDomainId + File.separator;

        String remoteFilePath = localFilePath;
        if (ownDomainId == 6997) {
            remoteFilePath = LOC + ownDomainId + File.separator + "StandardRankExtract" + File.separator;
        }
        if(EXPEDIA_DOMAIN_LIST.contains(ownDomainId)){
            remoteFilePath = "external/seo-clarity/";
        }

        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        try {
            extractScriptDetail.setOutputFile(localFileName);
            processFile(localFile, ownDomainEntity, ownDomainSettingEntity, localFileName, remoteFilePath);
        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "002 error";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, ownDomainId, device, false, processDate);
            return;
        }

    }

    private String getFileName(OwnDomainEntity ownDomainEntity, int processingDate) {

        String fileName = "";
        if (domainId == 7006) {
            fileName = ownDomainEntity.getDomain() + "_" + ownDomainEntity.getId() + "_" + ownDomainEntity.getSearchEngine() + "_" + processingDate + "_TOP_" + topX + "_RankReport_" + device + ".txt";
        }else if (domainId == 13557) {
            fileName = "ticketmaster_googlemobile_" + processingDate + "_geolocationextract.json";
        }
        else {
            fileName = ownDomainEntity.getId() + "_" + processingDate + "_TOP_" + topX + "_VsV3Report_" + device + ".txt";
        }

        return fileName;
    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, OwnDomainSettingEntity ownDomainSettingEntity, String localFilePath, String remoteFilePath) throws Exception {

        int ownDomainId = ownDomainEntity.getId();
//        addHeadersForExactFile(localFile, ownDomainEntity);

        List<String> dataList = new ArrayList<>();
        int retryCount = 1;

        int pageNum = 0;
        int pageSize = 1000;
        while (true) {
            try {
                Thread.sleep(1000);
                dataList = getDataFromDB(ownDomainEntity, ownDomainSettingEntity, pageNum, pageSize);
                if (CollectionUtils.isEmpty(dataList)) {
                    break;
                }
                pageNum++;
                System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize
                        + ", size:" + dataList.size());
                FileUtils.writeLines(localFile, dataList, true);
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + ownDomainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + ownDomainId);

                    String fatalError = "002 error";
                    extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                            fatalError, startTime, ownDomainId, device, false, processDate);
                    return;
                }

                e.printStackTrace();
                System.out.println("====extract error oid:" + ownDomainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        if(!localFile.exists()){
            log.info("============dataListEmpty");
            return;
        }

//        FileUtils.writeLines(localFile, dataList, true);
        totalCnt = totalCnt + dataList.size();
        extractScriptDetail.setOutputDataCount(dataList.size());
        extractScriptDetail.setOutputFileSizeKB(seoclarity.backend.utils.FileUtils.GetFileKBSize(localFile));

        int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
        if (SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId) != null) {
            serverType = SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId);
        }

        try {

            if(EXPEDIA_DOMAIN_LIST.contains(ownDomainId)){
                int serverDomainId = 4739;
                GZipUtil.zip(localFilePath, localFilePath + GZipUtil.GZFile_POSTFIX);
                serverType = SPECIAL_DOMAIN_SERVER_MAP.get(serverDomainId);
                System.out.println("===WeeklyExpedia will copy " + localFilePath + " to serverType: " + serverType);
                serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, serverDomainId, localFilePath + GZipUtil.GZFile_POSTFIX, remoteFilePath, extractScriptDetail);

            }else {

                if(serverType == ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3){//for s3 path no folder
                    remoteFilePath = null;
                }
                if(ownDomainId == 13297){//https://www.wrike.com/open.htm?id=1590468861
                    remoteFilePath = "extracts/";
                }
                serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, ownDomainId, localFilePath, remoteFilePath, extractScriptDetail);
            }

            extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_SUCCESS);
            long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
            extractScriptDetail.setEndedTime(new Date());
            extractScriptDetail.setElapsedSeconds((int) elapsedSeconds);
            log.info("===update for success id:" + extractScriptDetail.getId());
            extractService.updateForSuccess(extractScriptDetail);

        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "send to remote server error!!";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, domainId, device, false, processDate);
        }

    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity, OwnDomainSettingEntity ownDomainSettingEntity, Integer pageNum, Integer pageSize) throws Exception {

        List<String> extractLines = new ArrayList<String>();

        Integer enableCustomSearchVolume = ownDomainSettingEntity.getEnableCustomSearchVolume();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

        LocalDate localDate = LocalDate.parse(rankingDate);
        int monthDayCount = localDate.lengthOfMonth();
        List<RankIndexParamEntity> paramList = rankIndexParamEntityDAO.getRankIndexParams(domainId, 0);
        RankIndexParamVO rankIndexParamVO = new RankIndexParamVO(paramList);
        List<String> ctrList = rankIndexParamVO.getParamList();

        String sumCtr = rankIndexParamVO.getTotalIndex(ctrList);
        log.error("=====totalCtrIndex:" + sumCtr);
        if (sumCtr == null) {
            log.error("=====ctr index null");
        }

        List<CommonParamEntity> visibilityFoldCtrCommParamList = commonParamDAO.getListByFuncNameAndOwndomainId(ownDomainId, "VisibilityFoldCtr");
        List<Double> foldRateList = new LinkedList<>();
        if (CollectionUtils.isEmpty(visibilityFoldCtrCommParamList)) {
            log.error("===visibilityFoldCtrCommParamList111 none:" + ownDomainId);
            foldRateList.add(1d);
            foldRateList.add(0.66d);
            foldRateList.add(0.33d);
            foldRateList.add(0.10d);
        }else {
            String visibilityFoldCtrParamJson = visibilityFoldCtrCommParamList.get(0).getParamJson();
            foldRateList = extractValues(visibilityFoldCtrParamJson);
        }


        List<CommonParamEntity> vsibilityFoldRankCtrCommParamList = commonParamDAO.getListByFuncNameAndOwndomainId(ownDomainId, "VisibilityFoldRankCtr");
        List<Double> foldRankRateList = new LinkedList<>();
        if (CollectionUtils.isEmpty(vsibilityFoldRankCtrCommParamList)) {
            log.error("===vsibilityFoldRankCtrCommParamList222 none:" + ownDomainId);
            foldRankRateList.add(1d);
            foldRankRateList.add(0.80d);
            foldRankRateList.add(0.66d);
            foldRankRateList.add(0.50d);
            foldRankRateList.add(0.40d);
            foldRankRateList.add(0.33d);
            foldRankRateList.add(0.25d);
            foldRankRateList.add(0.15d);
            foldRankRateList.add(0.1d);
            foldRankRateList.add(0.05d);
            foldRankRateList.add(0.05d);
            foldRankRateList.add(0.05d);
            foldRankRateList.add(0.05d);
            foldRankRateList.add(0.05d);
            foldRankRateList.add(0.05d);
        }else {
            String vsibilityFoldRankCtrParamJson = vsibilityFoldRankCtrCommParamList.get(0).getParamJson();
            foldRankRateList = extractValues(vsibilityFoldRankCtrParamJson);
        }


        List<CLRankingDetailEntity> infoDataList = new ArrayList<>();
        boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
        String extractKey = engineId + "_" + languageId + "_" + isMobile;
        int locationId = 0;

        ExtractQueryVO extractQueryVO = new ExtractQueryVO();
        extractQueryVO.setDomainId(ownDomainId);
        extractQueryVO.setEngineId(engineId);
        extractQueryVO.setLanguageId(languageId);
        extractQueryVO.setDevice(device);
        extractQueryVO.setRankDate(rankingDate);
        extractQueryVO.setRank(topX);
        extractQueryVO.setLocationId(locationId);
        extractQueryVO.setEnableCustomSearchVolume(enableCustomSearchVolume);
        extractQueryVO.setEnabledDiffSes(ownDomainSettingEntity.differentVsKeywordSetEnabled());
        extractQueryVO.setFoldRateList(foldRateList);
        extractQueryVO.setFoldRankRateList(foldRankRateList);

        //estd queryVO
        ExtractQueryVO estdQueryVO = new ExtractQueryVO();
        BeanUtils.copyProperties(extractQueryVO, estdQueryVO);
        estdQueryVO.setIsBroadMatch(isBroadMatch);
        estdQueryVO.setRootDomainReverse(rootDomainReverse);
        estdQueryVO.setDomainReverse(domainReverse);
        estdQueryVO.setCtrList(ctrList);
        estdQueryVO.setSumCtr(sumCtr);
        estdQueryVO.setMonthDayCount(monthDayCount);

        List<GroupTagEntity> tagList = groupTagEntityDAO.getTagEntityByType(domainId, GroupTagEntity.TAG_TYPE_KEYWORD);
        Map<Integer, String> tagMap = constructTagMap(tagList);
        System.out.println("======extractAllKW OID:" + domainId + " tagCnt:" + tagList.size() + "=>" + tagMap.size());


        if (ownDomainId == 13557) {
            extractQueryVO.setTagNames("Venue Analysis Type:Comp");
            extractQueryVO.setExtractGeo(true);
        }

        if(domainId == 4739){//https://www.wrike.com/open.htm?id=1627754140
            extractQueryVO.setNeedMeta(false);
        }

        infoDataList = pixelDetailDao.exportTopXKeywordJsonWithMultFilterByPage(extractQueryVO, pageNum, pageSize);

        log.info("===primary dataList size: " + infoDataList.size());

        EngineCountryLanguageMappingEntity engineCountryLanguageMapping = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageId(engineId, languageId);
        String countryName = engineCountryLanguageMapping.getCountryQueryName();
        String engineName = engineCountryLanguageMapping.getEngineDisplayName();
        String languageName = engineCountryLanguageMapping.getLanguageQueryName();

        if(CollectionUtils.isNotEmpty(infoDataList)){

            Map<Long, List<CLRankingDetailEntity>> infoRankKwMap = new HashMap<>();
            Map<String, CLRankingDetailEntity> splitEstdRankKwMap = new HashMap<>();
            Map<String, List<CLRankingDetailEntity>> splitDetailRankKwMap = new HashMap<>();
            Map<String, List<CLRankingDetailEntity>> splitSubRankKwMap = new HashMap<>();

            infoRankKwMap.putAll(infoDataList.stream().collect(Collectors.groupingBy(v1 -> v1.getKeywordRankcheckId())));
            log.info("===infoRankKwMap dataList size: " + infoRankKwMap.size());

            List<Long> keywordRankCheckIdList = infoDataList.stream()
                    .map(CLRankingDetailEntity::getKeywordRankcheckId)
                    .distinct()
                    .collect(Collectors.toList());

            List<List<Long>> splitList = CollectionSplitUtils.splitCollectionBySize(keywordRankCheckIdList, KEYWORD_SPLIT_COUNT);
            for(List<Long> kwRankCheckIdSet: splitList){

                //estd sov som
                List<CLRankingDetailEntity> estdRankList = pixelDetailDao.getEstdInfoWithMultFilter(estdQueryVO, kwRankCheckIdSet);
                splitEstdRankKwMap.putAll(estdRankList.stream()
                        .collect(Collectors.toMap(
                                entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId(),
                                entity -> entity,
                                (entity1, entity2) -> entity1
                        ))
                );

                //get detailRankMap
                List<CLRankingDetailEntity> detailRankList = pixelDetailDao.exportDetailJsonWithMultFilter(extractQueryVO, kwRankCheckIdSet);
                splitDetailRankKwMap.putAll(detailRankList.stream()
                        .collect(Collectors.groupingBy(
                                entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId()
                        ))
                );

                //get subRankMap
                List<CLRankingDetailEntity> subRankList = pixelDetailDao.exportSunRankJsonWithMultFilter(extractQueryVO, kwRankCheckIdSet);
                splitSubRankKwMap.putAll(subRankList.stream()
                        .collect(Collectors.groupingBy(
                                entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId()
                        ))
                );


                for(Long rcId: kwRankCheckIdSet){
                    List<CLRankingDetailEntity> infoList = infoRankKwMap.get(rcId);
                    for (CLRankingDetailEntity info : infoList) {
                        info.setSearchEngine(engineName);
                        info.setLanguageName(languageName);
                        info.setCountry(countryName);
                        info.setDevice(device);

                        String locationName =  info.getLocationId() == 0 ? "National" : geoService.getCityName(info.getLocationId());
                        if (StringUtils.isBlank(locationName)) {
                            locationName = "-";
                        }
                        info.setLocationName(locationName);

                        long kwRankCheckId = info.getKeywordRankcheckId();
                        int kwLocationId = info.getLocationId();
                        String kwMapKey = kwRankCheckId + TAG_SPLIT + kwLocationId;
                        log.info("============kwMapKey:" + kwMapKey);

                        //estd
                        CLRankingDetailEntity estdRank = splitEstdRankKwMap.get(kwMapKey);
                        if(estdRank == null){
                            log.info("==== null estd:" + kwMapKey);
                        }else {
                            info.setEstdTraffic(estdRank.getEstdTraffic());
                            info.setShareOfVoice(estdRank.getShareOfVoice());
                            info.setShareOfMarket(estdRank.getShareOfMarket());
                        }

                        List<CLRankingDetailEntity> kwDetailRankList = splitDetailRankKwMap.get(kwMapKey);
                        List<CLRankingDetailEntity> kwSubRankList = splitSubRankKwMap.get(kwMapKey);

                        extractLines.add(appendData(info, kwDetailRankList, kwSubRankList, tagMap));
                    }
                }



            }

        }

        if (EXTRACT_SECONDARY_DOMAIN_LIST.contains(ownDomainId)) {
            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
            if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
                for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                    engineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    languageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    boolean secondaryIsMobile = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? false : true;
                    if (secondaryIsMobile != isMobile) {
                        log.info("===secondary not same device isMobile: " + isMobile + ",secondaryIsMobile:" + secondaryIsMobile);
                        continue;
                    }
                    String secondaryExtractKey = engineId + "_" + languageId + "_" + secondaryIsMobile;
                    if (secondaryExtractKey.equalsIgnoreCase(extractKey)) {
                        log.info("===already extract engine skip: " + secondaryExtractKey + ",extractKey:" + extractKey);
                        continue;
                    }
                    EngineCountryLanguageMappingEntity secEngineCountryLanguageMapping = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageId(engineId, languageId);
                    String secCountryName = secEngineCountryLanguageMapping.getCountryQueryName();
                    String secEngineName = secEngineCountryLanguageMapping.getEngineDisplayName();
                    String secLanguageName = secEngineCountryLanguageMapping.getLanguageQueryName();

                    log.info("===process secondary engine :" + engineId + ",languageId: " + languageId + ",isMobile:" + secondaryIsMobile + ",domain:" + ownDomainId);

                    List<CLRankingDetailEntity> secondaryDataList = new ArrayList<>();
                    secondaryDataList = pixelDetailDao.exportTopXKeywordJsonWithMultFilterByPage(extractQueryVO, pageNum, pageSize);
                    log.info("===secondaryDataList size:" + secondaryDataList.size());
//                    dataList.addAll(secondaryDataList);

                    if(CollectionUtils.isNotEmpty(secondaryDataList)){
                        //get subRankMap
                        Map<String, List<CLRankingDetailEntity>> secSplitEstdRankKwMap = new HashMap<>();
                        Map<String, List<CLRankingDetailEntity>> secSplitDetailRankKwMap = new HashMap<>();
                        Map<String, List<CLRankingDetailEntity>> secSplitSubRankKwMap = new HashMap<>();
                        List<Long> secKeywordRankCheckIdList = secondaryDataList.stream()
                                .map(CLRankingDetailEntity::getKeywordRankcheckId)
                                .distinct()
                                .collect(Collectors.toList());
                        List<List<Long>> secSplitList = CollectionSplitUtils.splitCollectionBySize(secKeywordRankCheckIdList, KEYWORD_SPLIT_COUNT);
                        for(List<Long> secKwRankCheckIdSet: secSplitList){

                            //estd sov som
                            List<CLRankingDetailEntity> secEstdRankList = pixelDetailDao.getEstdInfoWithMultFilter(extractQueryVO, secKwRankCheckIdSet);
                            secSplitEstdRankKwMap.putAll(secEstdRankList.stream()
                                    .collect(Collectors.groupingBy(
                                            entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId()
                                    ))
                            );

                            List<CLRankingDetailEntity> secDetailRankList = pixelDetailDao.exportDetailJsonWithMultFilter(extractQueryVO, secKwRankCheckIdSet);
                            secSplitDetailRankKwMap.putAll(secDetailRankList.stream()
                                    .collect(Collectors.groupingBy(
                                            entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId()
                                    ))
                            );

                            List<CLRankingDetailEntity> secSubRankList = pixelDetailDao.exportSunRankJsonWithMultFilter(extractQueryVO, secKwRankCheckIdSet);
                            secSplitSubRankKwMap.putAll(secSubRankList.stream()
                                    .collect(Collectors.groupingBy(
                                            entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId() + "!_!" + entity.getTrueRank()
                                    ))
                            );
                        }

                        for (CLRankingDetailEntity detail : secondaryDataList) {
                            detail.setSearchEngine(secCountryName);
                            detail.setLanguageName(secEngineName);
                            detail.setCountry(secLanguageName);
                            detail.setDevice(device);

                            String locationName =  detail.getLocationId() == 0 ? "National" : geoService.getCityName(detail.getLocationId());
                            if (StringUtils.isBlank(locationName)) {
                                locationName = "-";
                            }
                            detail.setLocationName(locationName);

                            long kwRankCheckId = detail.getKeywordRankcheckId();
//                            int kwTrueRank = detail.getTrueRank();
                            int kwLocationId = detail.getLocationId();
                            String kwMapKey = kwRankCheckId + TAG_SPLIT + kwLocationId;

                            List<CLRankingDetailEntity> secDetailRankList = secSplitDetailRankKwMap.get(kwMapKey);
                            List<CLRankingDetailEntity> secSubRankList = secSplitSubRankKwMap.get(kwMapKey);

                            extractLines.add(appendData(detail, secDetailRankList, secSubRankList, tagMap));
                        }
                    }

                }
            }
        }
        return extractLines;
    }

    public String appendData(CLRankingDetailEntity info, List<CLRankingDetailEntity> detailRankList, List<CLRankingDetailEntity> subRankList, Map<Integer, String> tagMap) {
        StringBuffer line = new StringBuffer();
        line.append(extractService.setExtractJsonResult(info, detailRankList, subRankList, tagMap)).append(SPLIT);
        return line.toString();
    }


    private void processByFrequency(int frequency) {
        Date nowDate = new Date();
//        nowDate = FormatUtils.toDate("2024-03-11", "yyyy-MM-dd");
        boolean isSunday = FormatUtils.isSunday(DateUtils.addDays(nowDate, -1));
        if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY && !isSunday) {
            log.info("=====process weekly not sunday,exit!" + nowDate);
            return;
        }

        log.info("**************** start time :" + FormatUtils.formatDate(nowDate, "yyyy-MM-dd HH:mm:ss") + " **********");

        List<ExtractScriptInstanceEntity> instanceList = extractService.getNeedRunExtract(
                ExtractScriptConfigEntity.CATEGORY_RANK, getClass().getName(),
                ExtractScriptConfigEntity.SPECIAL_CATEGORY_DOMAIN_LEVEL + "", frequency, nowDate);

        if (CollectionUtils.isEmpty(instanceList)) {
            log.info("===no instant exit!");
            return;
        }

        for (ExtractScriptInstanceEntity instance : instanceList) {
            String pDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_FORMAT_YYYYMMDD);
            String sTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
            log.info("====processDate:" + processDate + ",pDate:" + pDate + ",sTime:" + sTime);

            logglyVO.setoId(String.valueOf(instance.getOwnDomainId()));
            logglyVO.setName("TopXKeywordRankingJsonExtract");
            logglyVO.setDevice(instance.getDevice());

            logglyVO.setpDate(pDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
            logglyVO.setGroups(groupList);

            List<ExtractScriptDetailEntity> detailList = instance.getScriptDetailList();
            for (ExtractScriptDetailEntity scriptDetailEntity : detailList) {
                processForInstance(instance, scriptDetailEntity);
            }

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(sTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        }
    }

    private void processForInstance(ExtractScriptInstanceEntity instance, ExtractScriptDetailEntity scriptDetailEntity) {

        domainId = instance.getOwnDomainId();
        processDate = FormatUtils.toDate(String.valueOf(scriptDetailEntity.getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD);
        log.info("********************** process domain " + domainId + ",processDate: " + processDate + " *******************");

        int tagId = instance.getTagId();
        if (tagId > 0) {
            extractTag = true;
        }else {
            extractTag = false;
        }

        rankType = instance.getRankType();
        topX = instance.getTopXRank();
        extractScriptDetail = scriptDetailEntity;
        frequence = instance.getFrequency();
//        boolean isExpectedProcessHour = instance.getIsExpectedProcessHour();
//        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);

            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
            if (ownDomainEntity == null) {
                log.error(" domain not exist : " + domainId);
                return;
            }
            if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY
                    || frequence == ExtractScriptInstanceEntity.FREQUENCY_WEEKLY) {
                processDate = FormatUtils.getLastSundayForWeeklyDomainExtract(processDate);
                System.out.println("====weekly domain processDate:" + processDate);
            }

            OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);

            engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
            languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
            rankDate = FormatUtils.formatDateToYyyyMmDd(processDate);


            extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
            extractScriptDetail.setStartedTime(new Date());

            startTime = System.currentTimeMillis();

            extractService.updateForStart(extractScriptDetail);

            String instanceDevice = instance.getDevice();
            if (instanceDevice.equals("0")) {//todo write desktop and mobile into ine file
//                device = "desktop";
//                processForDevice(ownDomainEntity);
//                device = "mobile";
//                processForDevice(ownDomainEntity);

            } else if (instanceDevice.equals("d")) {
//                if(rankDate == 20230802){
//                    log.info("=====skip 20230802 desktop.");//todo rank data error, waiting for fix
//                    return;
//                }
                device = "desktop";
                processForDevice(ownDomainEntity, ownDomainSettingEntity);

            } else if (instanceDevice.equals("m")) {
                device = "mobile";
                processForDevice(ownDomainEntity, ownDomainSettingEntity);
            }

        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "001 exception";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, domainId, device, false, processDate);


            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

        }

    }

    private void processForDevice(OwnDomainEntity ownDomainEntity, OwnDomainSettingEntity ownDomainSettingEntity) {

        int domainId = ownDomainEntity.getId();
        log.info("======process device:" + device);
//        if (frequence == RankQcStateEntity.FREQUENCE_DAILY && !ignoreQC) {
//            boolean isPassed = extractService.isRankQcPass(rankDate, domainId, engineId, languageId, device, rankType, frequence);
////            isPassed = true;// TODO: 2020/7/21 test
//            if (!isPassed) {
//                log.error("===not pass rank qc!!" + topX);
//                extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.SKIP_TO_RUN,
//                        null, startTime, domainId, device, false, processDate);
//                return;
//            }
//        }

        if (EXTRACT_SECONDARY_DOMAIN_LIST.contains(domainId)) {
            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
            if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
                for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                    int searchEngineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    int searchLanguageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    String secondaryDevice = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? "desktop" : "mobile";
                    if (secondaryDevice.equalsIgnoreCase(device)) {
                        log.info("===secondary not same device: " + device + ",secondaryIsMobile:" + secondaryDevice);
                        continue;
                    }
//                    boolean isPassed = extractService.isRankQcPass(rankDate, domainId, searchEngineId, searchLanguageId, device, rankType, frequence);
//                    if (!isPassed) {
//                        log.error("===secondary not pass rank qc,engineId:" + searchEngineId + ",languageId:" + searchLanguageId);
//                        extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.SKIP_TO_RUN,
//                                null, startTime, domainId, device, false, processDate);
//                        return;
//                    }
                }
            }
        }

        processExtract(ownDomainEntity, ownDomainSettingEntity);
    }

    private Map<Integer, String> constructTagMap(List<GroupTagEntity> tagList) {
        Map<Integer, String> tagMap = new HashMap<Integer, String>();
        if (tagList != null && tagList.size() > 0) {
            for (GroupTagEntity tagEntity : tagList) {
                tagMap.put(tagEntity.getId(), tagEntity.getTagName());
            }
        }
        return tagMap;
    }

    public static List<Double> extractValues(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);
        // 使用 TreeMap 自动排序
        Map<Integer, Double> sortedMap = new TreeMap<>();
        for (String key : jsonObject.keySet()) {
            int intKey = Integer.parseInt(key);
            double value = jsonObject.getDouble(key);
            sortedMap.put(intKey, value);
        }
        // 创建一个列表来存储 values
        List<Double> values = new LinkedList<>(sortedMap.values());
//        log.info("===values= : " + JSON.toJSONString(values));
        return values;
    }

    public static void main(String[] args) {

//        VsV3JsonExtract vsV3JsonExtract = new VsV3JsonExtract();
//        vsV3JsonExtract.processByFrequency(ExtractScriptConfigEntity.FREQUENCY_WEEKLY);

        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainIdList = args[0].split(",");
            } else {
                domainId = Integer.parseInt(args[0]);
            }

        }

        topX = Integer.parseInt(args[1]);
        device = args[2];

        VsV3JsonExtract vsV3JsonExtract = new VsV3JsonExtract();

        if (domainIdList != null && domainIdList.length > 0) {

            for (String processingDomainId : domainIdList) {
                domainId = Integer.parseInt(processingDomainId);
                if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
                    Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
                    Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
                    while (sDate.compareTo(eDate) <= 0) {
                        processDate = sDate;
                        vsV3JsonExtract.processForDomain(Integer.parseInt(processingDomainId));
                        sDate = DateUtils.addDays(sDate, 1);
                    }

                } else if(args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")){
                    processDate = FormatUtils.toDate(args[3], FormatUtils.DATE_PATTERN_2);
                    vsV3JsonExtract.processForDomain(Integer.parseInt(processingDomainId));
                } else {
                    processDate = DateUtils.addDays(new Date(), -1);
                    vsV3JsonExtract.processForDomain(Integer.parseInt(processingDomainId));
                }
            }

        } else {

            if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
                Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
                while (sDate.compareTo(eDate) <= 0) {
                    processDate = sDate;
                    vsV3JsonExtract.processForDomain(domainId);
                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else if(args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")){
                processDate = FormatUtils.toDate(args[3], FormatUtils.DATE_PATTERN_2);
                vsV3JsonExtract.processForDomain(domainId);
            } else {
                processDate = DateUtils.addDays(new Date(), -1);
                vsV3JsonExtract.processForDomain(domainId);
            }

        }

    }

}
