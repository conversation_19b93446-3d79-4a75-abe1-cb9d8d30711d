package seoclarity.backend.export;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyRankingEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.utils.*;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@CommonsLog
public class ExtractMonthlyRanking {

    private static final String SPLIT = "\t";
    private static String LOC = "/home/<USER>/RGExtract/";

    private static String processMonth;
    private static String[] domainNameList;
    private static String domainName;
    private static boolean isMobile = false;

    private int engineId = 1;
    private int languageId = 1;
    private static int topX;
    private static Integer pageSize = 1000000;

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private MonthlyRankingDao monthlyRankingDao;

    public ExtractMonthlyRanking() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
    }

    private void processForDomainName(String domainName) {
        domainName = domainName.toLowerCase();
        log.info("=======processingMonth:" + processMonth + ",domainName:" + domainName);
        String device = isMobile ? "mobile" : "desktop";
        String fileName = domainName + "_" + device + "_" + processMonth + ".csv";

        try {
            File outFile = new File(LOC + fileName);
            if(outFile.exists()){
                outFile.delete();
            }
            addHeadersForExactFile(outFile);

            String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);
            Integer pageNum = 0;
            List<MonthlyRankingEntity> monthlyRankingList = new ArrayList<>();
            while (true) {

                monthlyRankingList = monthlyRankingDao.getInfoForExtract(domainName, engineId, languageId, processMonth, isMobile, pageNum, pageSize);
                if (CollectionUtils.isEmpty(monthlyRankingList)) {
                    break;
                }

                pageNum++;
                System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize);

                List<String> dataList = new ArrayList<>();
                for (MonthlyRankingEntity monthlyRankingEntity : monthlyRankingList) {
                    dataList.add(appendData(processMonth, monthlyRankingEntity, domainName));
                }

                FileUtils.writeLines(outFile, dataList, true);
            }

            try {
                FTPUtils.saveFileToFTP(4, outFile.getAbsolutePath(), LOC);
            } catch (Exception e) {
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }


    }


    private void addHeadersForExactFile(File outFile) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Month").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Rank Position").append(SPLIT);
        header.append("Search Volume").append(SPLIT);
        header.append("Ranking URL").append(SPLIT);
        header.append("Domain");

        if (org.apache.commons.lang.StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }

    }

    public static String appendData(String month, MonthlyRankingEntity monthlyRankingEntity, String domainName) {
        StringBuffer line = new StringBuffer();

        line.append(month).append(SPLIT);
        line.append(monthlyRankingEntity.getKeywordName()).append(SPLIT);
        line.append(monthlyRankingEntity.getRank()).append(SPLIT);
        line.append(monthlyRankingEntity.getAvgSearchVolume()).append(SPLIT);
        line.append(monthlyRankingEntity.getUrl() == null ? "-" : ExtractService.formatGoogleUrl(monthlyRankingEntity.getUrl())).append(SPLIT);
        line.append(domainName).append(SPLIT);

        return line.toString();
    }

    public static void main(String[] args) {

        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainNameList = args[0].split(",");
            } else {
                domainName = args[0];
            }

        }

        topX = Integer.parseInt(args[1]);
        isMobile = Boolean.parseBoolean(args[2]);

        ExtractMonthlyRanking extractMonthlyRanking = new ExtractMonthlyRanking();
        if (domainNameList != null && domainNameList.length > 0) {

            for (String processingDomainName : domainNameList) {

                if (args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")) {
                    processMonth = args[3];
                    extractMonthlyRanking.processForDomainName(processingDomainName);
                } else {

                    processMonth = FormatUtils.formatDate(DateUtils.addMonths(new Date(), -1), FormatUtils.MONTH_PATTERN);
                    extractMonthlyRanking.processForDomainName(processingDomainName);
                }
            }

        } else {

            if (args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")) {
                processMonth = args[3];
                extractMonthlyRanking.processForDomainName(domainName);
            } else {
                processMonth = FormatUtils.formatDate(DateUtils.addMonths(new Date(), -1), FormatUtils.MONTH_PATTERN);
                extractMonthlyRanking.processForDomainName(domainName);
            }

        }

    }

}
