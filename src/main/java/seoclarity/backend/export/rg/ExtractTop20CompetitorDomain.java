package seoclarity.backend.export.rg;

import com.microsoft.schemas.office.office.STInsetMode;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.jboss.netty.util.internal.StringUtil;
import scala.Int;
import seoclarity.backend.dao.actonia.MonthlySenderInfoEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.ClarityDBConstants;
import seoclarity.backend.entity.actonia.MonthlySenderInfoEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.ExtractClarityDBsqlResult;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

public class ExtractTop20CompetitorDomain {

    private MonthlySenderInfoEntityDAO monthlySenderInfoEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;

    private static final boolean isTrueRank = true;
    private static final String US_COUNTRY = "US";
    private static String rankColumn = isTrueRank ? "true_rank" : "web_rank";
    private static final boolean isMobile = false;
    private static final int currentMonth = 202106;
    private static final int topcompetitorCnt = 30;
    private static final int domainListSize = 2;
    private Set<String> errorDomain = new HashSet<>();
    private static final String[] EXCLUDE_DOMAINS = new String[]{
            ".google",
            ".yahoo",
            ".bing",
            ".baidu",
            ".yandex",
            ".naver",
    };

    static CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader(
            "Dmain",
            "Country",
            "Competitor Domain",
            "Level"
    )
            .withDelimiter(',');

    public ExtractTop20CompetitorDomain() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        monthlySenderInfoEntityDAO = SpringBeanFactory.getBean("monthlySenderInfoEntityDAO");
    }

    public static void main(String[] args) {
        ExtractTop20CompetitorDomain extractTop20CompetitorDomain = new ExtractTop20CompetitorDomain();
        extractTop20CompetitorDomain.getTop20Domain();
    }

    private void getTop20Domain() {
        List<OwnDomainEntity> usDomainList = ownDomainEntityDAO.getAllDomain();
        if (usDomainList.size() <= 0) {
            return;
        }
        System.out.println("*********************usDomainList.size :" + usDomainList.size());
        Map<Integer, String> domainMap = new HashMap<>();
        List<Map<String, String>> checkList = new ArrayList<>();
        for (OwnDomainEntity domain : usDomainList) {
            domainMap.put(domain.getId(), domain.getSearchEngineCountry());
        }
        System.out.println(" filter foder domain : " + checkList.size() + "_________" + domainMap.size());
        List<Map<String, String>> domainName = new ArrayList<>();

        for (Integer ownDomainId : domainMap.keySet()) {
            Map<String, String> group = new HashMap<>();
            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
            if (ownDomainEntity.isBroadMatch()) {
                System.out.println(" ownDomainEntity.getDomain() : " + ownDomainEntity.getDomain());
                String rootDomainReverse = ClarityDBUtils.getReverseRootDomain(ownDomainEntity.getDomain(), true);
                group.put("domainid", ownDomainId.toString());
                group.put("domainname", StringUtils.reverseDelimited(rootDomainReverse, '.'));
                group.put("country", ownDomainEntity.getSearchEngineCountry());
                domainName.add(group);
            } else {
                String domainReverse = ClarityDBUtils.getReverseRootDomain(ownDomainEntity.getDomain(), false);
                group.put("domainid", ownDomainId.toString());
                group.put("domainname", StringUtils.reverseDelimited(domainReverse, '.'));
                group.put("country", ownDomainEntity.getSearchEngineCountry());
                domainName.add(group);
            }
        }
        List<Map<String, String>> competitorDomainList = getCompetitorDomain(domainName);
        try {
            export(competitorDomainList, "Intl");
            System.out.println("competitorDomainList.size :" + competitorDomainList.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (errorDomain.size() > 0) {
            for (String str : errorDomain) {
                System.out.println("error domain : " + str);
            }

        }
    }


    private List<Map<String, String>> getCompetitorDomain(List<Map<String, String>> domainList) {
        int engine = 1;
        int language = 1;
        List<Map<String, String>> competitorDomainList = new ArrayList<>();

        for (Map<String, String> domain0 : domainList) {
            String country = domain0.get("country");
            String queryDomain = domain0.get("domainname");
            try {
                List<Map<String, String>> domainTop20Map = new ArrayList<>();
                List<String> domains = getTopCompetitorDomains(queryDomain, engine, language);
                System.out.println("***********************competitor domains.size :    " + domains.size());
                if (domains.size() > 0) {
                    System.out.println("queryDomain:" + queryDomain + ", engine:" + engine + ", language:" + language + ", competitors:" + domains.size());
                    int ownDomainCnt = processQueryOwnDomainCnt(engine, language, queryDomain);
                    System.out.println("queryDomain:" + queryDomain + ", cnt:" + ownDomainCnt);
                    List<String[]> jiaojiList = processQueryV2(engine, language, queryDomain, domains, true);
                    List<String[]> otherList = processQueryV2(engine, language, queryDomain, domains, false);

                    Map<String, Integer> jiaojiMap = new HashMap<String, Integer>();
                    Map<String, Integer> hejiMap = new HashMap<String, Integer>();

                    for (String[] row : jiaojiList) {
                        jiaojiMap.put(row[0], Integer.valueOf(row[1]));
                    }
                    for (String[] row : otherList) {
                        hejiMap.put(row[0], Integer.valueOf(row[1]) + ownDomainCnt);
                    }
                    Map<String, String> sortLevel = new HashMap<>();
                    for (String domain : jiaojiMap.keySet()) {
                        int jiaoji = jiaojiMap.get(domain);
                        int heji = hejiMap.get(domain) == null ? 0 : hejiMap.get(domain);
                        DecimalFormat df = new DecimalFormat("#.00");
                        sortLevel.put(domain, (heji == 0 ? 0 : df.format(jiaoji * 1.0 / heji)).toString());

                        System.out.println("=domain:" + domain + ", jiaoji:" + jiaoji + ", heji:" + heji +
                                ", competitor level:" + (heji == 0 ? 0 : df.format(jiaoji * 1.0 / heji)));
                    }
                    domainTop20Map = sortByKey(sortLevel, queryDomain, country);
                    for (Map<String, String> map : domainTop20Map) {
                        competitorDomainList.add(map);
                    }
                }
            } catch (Exception e) {
                errorDomain.add(queryDomain);
            }
        }
        System.out.println(" competitorDomainList.size  : " + competitorDomainList.size());
        return competitorDomainList;
    }

    private List<Map<String, String>> sortByKey(Map<String, String> sortLevelList, String queryDomain, String country) {
        List<Map<String, String>> top20List = new ArrayList<>();
        System.out.println("***************sortLevelList.size : " + sortLevelList.size());
        if (sortLevelList == null || sortLevelList.isEmpty()) {
            return null;
        }
        HashMap<String, String> finalOut = new LinkedHashMap<>();
        sortLevelList.entrySet()
                .stream()
                .sorted((p1, p2) -> p2.getValue().compareTo(p1.getValue()))
                .collect(Collectors.toList()).forEach(ele -> finalOut.put(ele.getKey(), ele.getValue()));
        for (String str : finalOut.keySet()) {
            System.out.println("domain  ： " + str + " level :" + sortLevelList.get(str));
            Map<String, String> domainLevel = new HashMap<>();
            domainLevel.put("ownDmain", queryDomain);
            domainLevel.put("country", country);
            domainLevel.put("competitorDomain", StringUtils.reverseDelimited(str, '.'));
            domainLevel.put("level", sortLevelList.get(str));
            if (top20List.size() < 20) {
                top20List.add(domainLevel);
            }
        }
        return top20List;
    }


    private List<String> getTopCompetitorDomains(String queryDomain, int engine, int language) {
        String ownDomain = StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(queryDomain), '.');
        boolean isHrrd = true;
        if (!ownDomain.equalsIgnoreCase(StringUtils.reverseDelimited(queryDomain, '.'))) {
            isHrrd = false;
        }

        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT ");
        sql.append("     count() AS cnt, ");
        sql.append("     root_domain_reverse AS domain ");
        sql.append(" FROM " + getTable(engine, language, false) + " ");
        sql.append(" WHERE (engine_id = " + engine + ") AND (language_id = " + language + ") AND (" + rankColumn + " <= 10) AND (keyword_rankcheck_id GLOBAL IN ");
        sql.append(" ( ");
        sql.append("     SELECT keyword_rankcheck_id AS id ");
        sql.append("     FROM " + getTable(engine, language, true) + " ");
        sql.append("     WHERE (" + rankColumn + " <= 20) AND (root_domain_reverse = '" + ownDomain + "')  " + (isHrrd ? "" : " AND (domain_reverse = '" + StringUtils.reverseDelimited(queryDomain, '.') + "') ")
                + " AND (engine_id = " + engine + ") AND (language_id = " + language + ") ");
        sql.append(" 	AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(" + engine + "), toUInt64(" + engine + "), toUInt64(keyword_rankcheck_id))) = 0) AND (location_id = 0) ");
//		sql.append(" )) AND domain not in ('" + StringUtils.join(EXCLUDE_DOMAINS, "','") + "', '" + ownDomain + "') ");
        sql.append(" )) ");
        sql.append("     AND domain != '" + ownDomain + "' ");
        for (String excludeDomain : EXCLUDE_DOMAINS) {
            sql.append(" AND (domain NOT LIKE '" + "%" + excludeDomain + "') ");
        }
        sql.append(" GROUP BY domain ");
        sql.append(" ORDER BY cnt DESC ");
        sql.append(" LIMIT " + topcompetitorCnt + " ");

        System.out.println(sql.toString());
//        List<Map<String, String>> result = monthlySenderInfoEntityDAO.getConnect(sql.toString());
//
//        List<String> domainList = new ArrayList<String>();
//        if (null != result && result.size() > 0) {
//            for (Map<String, String> entity : result) {
//                domainList.add(entity.get("domain"));
//            }
//        }
        String result = ExtractClarityDBsqlResult.post(ClarityDBConstants.CKADSHOSTMonthly, sql.toString() + " format TSV");

        List<String> domainList = new ArrayList<String>();
        if (StringUtils.isNotBlank(result)) {
            for (String row : StringUtil.split(result, '\n')) {
                String[] cols = StringUtil.split(row, '\t');
                if (cols.length > 1) {
                    domainList.add(cols[1]);
                } else {
                    System.out.println(StringUtils.join(cols, ","));
                }
            }
        }
        return domainList;
    }

    private static String getTable(int engine, int language, boolean isDetail) {
        String table = (isMobile ? "m_" : "d_") + (isDetail ? "ranking_detail_" : "rankcheckid_ranking_detail_") + currentMonth;
        if (engine == 1 && language == 1) {
            table += "_us";
        } else {
            table += "_intl";
        }
        return table;

    }

    private static int processQueryOwnDomainCnt(int engine, int language, String queryDomain) {
        StringBuffer sql = new StringBuffer();
        String table = getTable(engine, language, true);

        sql.append("			SELECT count() as cnt ");
        sql.append("			FROM " + table + " ");
        sql.append("			WHERE (1 = 1)  ");
        sql.append(getDomainSql(engine, language, queryDomain));
        sql.append("			and " + rankColumn + " < 21 ");
        sql.append("			AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(engine_id), toUInt64(language_id), toUInt64(keyword_rankcheck_id))) = 0) ");
        sql.append("			AND (location_id = 0) ");

        String result = ExtractClarityDBsqlResult.post(ClarityDBConstants.CKADSHOSTMonthly, sql.toString() + " format TSV");
        if (StringUtils.isNotBlank(result)) {
            int cnt = NumberUtils.toInt(StringUtils.trim(result));
            return cnt;
        }
        return 0;
    }

    private static String getDomainSql(int engine, int language, String queryDomain) {
        String sql = "";
        if (StringUtils.isNotBlank(queryDomain)) {
            String ownDomain = StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(queryDomain), '.');
            String hrd = " AND hrrd = 1 ";
            boolean isHrrd = true;
            if (!ownDomain.equalsIgnoreCase(StringUtils.reverseDelimited(queryDomain, '.'))) {
                hrd = " AND hrd = 1 ";
                isHrrd = false;
            }
            sql = "AND (root_domain_reverse = '" + ownDomain + "') ";
            if (!isHrrd) {
                sql += " AND ( domain_reverse = '" + StringUtils.reverseDelimited(queryDomain, '.') + "') ";
            }
            sql += hrd;
        }
        sql += " AND engine_id = " + engine + " AND language_id = " + language + "  ";
        return sql;
    }

    private static List<String[]> processQueryV2(int engine, int language, String queryDomain, List<String> competitorDomains, boolean isCommon) {
        StringBuffer sql = new StringBuffer();
        String table = getTable(engine, language, true);

        sql.append("    SELECT root_domain_reverse, count() as cnt ");
        sql.append("    FROM ");
        sql.append("    ( ");
        sql.append("        SELECT ");
        sql.append("			keyword_rankcheck_id, ");
        sql.append("			root_domain_reverse ");
        sql.append("        FROM " + table + " ");
        sql.append("        WHERE (1 = 1) ");
        sql.append("		AND  ( ");
        for (int i = 0; i < competitorDomains.size(); i++) {
            if (i > 0) {
                sql.append(" OR ");
            }
            sql.append("			 (root_domain_reverse = '" + competitorDomains.get(i) + "') ");
        }
        sql.append("		) ");
        sql.append(getDomainSql(engine, language, null));
        sql.append("		AND (hrrd = 1) ");
        sql.append("		AND " + rankColumn + " < 21 ");
        sql.append("		AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(engine_id), toUInt64(language_id), toUInt64(keyword_rankcheck_id))) = 0) ");
        sql.append("		AND (location_id = 0) ");
        sql.append("        and keyword_rankcheck_id global " + (isCommon ? "" : " NOT ") + " in ( ");
        sql.append("			SELECT keyword_rankcheck_id ");
        sql.append("			FROM " + table + " ");
        sql.append("			WHERE (1 = 1)  ");
        sql.append(getDomainSql(engine, language, queryDomain));
        sql.append("			and " + rankColumn + " < 21 ");
        sql.append("			AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(engine_id), toUInt64(language_id), toUInt64(keyword_rankcheck_id))) = 0) ");
        sql.append("			AND (location_id = 0) ");
        sql.append("		) ");
        sql.append("    ) ");
        sql.append("    GROUP BY root_domain_reverse ");

        System.out.println(sql.toString());

        String result = ExtractClarityDBsqlResult.post(ClarityDBConstants.CKADSHOSTMonthly, sql.toString() + " format TSV");
        List<String[]> lines = new ArrayList<String[]>();
        for (String row : StringUtil.split(result, '\n')) {
            if (StringUtils.isNotBlank(row)) {
                String[] cols = StringUtil.split(row, '\t');
                if (cols != null && cols.length == 2) {
                    lines.add(cols);
                }
            }
        }

        return lines;
    }

    private void export(List<Map<String, String>> competitorDomainList, String country) throws Exception {
        File file = new File("RGTop20DomainLevel/Intlcountry.csv");

        CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file)), csvFullFormat);
        System.out.println("name : " + file.getName() + " size : " + competitorDomainList.size());
        for (Map<String, String> objectMap : competitorDomainList) {
            csvPrinter.printRecord(
                    objectMap.get("ownDmain"),
                    objectMap.get("country"),
                    objectMap.get("competitorDomain"),
                    objectMap.get("level")
            );
        }
        csvPrinter.flush();
        csvPrinter.close();
    }
}

