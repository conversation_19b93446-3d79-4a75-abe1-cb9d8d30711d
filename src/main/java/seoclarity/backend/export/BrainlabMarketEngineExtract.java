package seoclarity.backend.export;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import seoclarity.backend.dao.actonia.contentdownload.CommonParamDao;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.utils.DesUtils;
import seoclarity.backend.utils.GCSUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 *  mvn exec:java -Dexec.mainClass="seoclarity.backend.export.BrainlabMarketEngineExtract" -Dexec.cleanupDaemonThreads=false -Dexec.args="2024-02-09"
 */
@CommonsLog
public class BrainlabMarketEngineExtract {

    private CommonParamDao commonParamDao;

    public BrainlabMarketEngineExtract() {
        commonParamDao = SpringBeanFactory.getBean("commonParamDao");
    }

    public static void main(String[] args) throws IOException {

        String date = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd");
//        String date = "2024-02-16";
        if(args != null && args.length >= 1) {
            date = args[0];
        }

        String extractPath = "/home/<USER>/extract/markets/"+date;

        FileUtil.mkdir(extractPath+"/walmart");
        FileUtil.mkdir(extractPath+"/target");
        FileUtil.mkdir(extractPath+"/amazon");

        extract(date, "/disk1/scribeKeyword/walmart_commoncrawl_keywordRank_111_1", extractPath+"/walmart");
        extract(date, "/disk1/scribeKeyword/target_commoncrawl_keywordRank_112_1", extractPath+"/target");
        extractAmazon(date, "/disk1/scribeKeyword/amazon_commoncrawl_keywordRank_113_1", extractPath+"/amazon");

        BrainlabMarketEngineExtract brainlabMarketEngineExtract = new BrainlabMarketEngineExtract();
        brainlabMarketEngineExtract.extractAndUpload(date,extractPath);
    }

    public static void extractAmazon(String date, String path, String extractPath) throws IOException {
        File[] files = FileUtil.ls(path);
        for (File file : files) {
            if(file.getName().contains(date) == false) {
                continue;
            }
            List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
            for (String line : lines) {
                JSONObject jsonObject = JSON.parseObject(line, JSONObject.class);
                String kName = jsonObject.getString("keyword");

                CSVPrinter csvPrinter = new CSVPrinter(new FileWriter(extractPath+"/"+kName+".csv"), CSVFormat.DEFAULT.withDelimiter('\t'));
                csvPrinter.printRecord(
                        "RankPosition", "Type", "Title", "Brand", "Link", "Image",
                        "Asin", "BadgeLabel", "Rating", "RateNumber", "NowPrice", "HistoryPrice", "Coupon", "Sponsored", "Prime", "Fresh"
                );

                JSONArray jsonArray = jsonObject.getJSONArray("keywordRankEntityVOs");
                if(jsonArray.isEmpty()) {
                    csvPrinter.flush();
                    csvPrinter.close();
                    continue;
                }
                int rank = 1;
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject rankObj = jsonArray.getJSONObject(i);
                    csvPrinter.printRecord(
                            rank++,
                            rankObj.getString("type"),
                            rankObj.getString("title"),
                            rankObj.getString("brand"),
                            rankObj.getString("link"),
                            rankObj.getString("image"),
                            rankObj.getString("asin"),
                            rankObj.getString("badgeLabel"),
                            rankObj.getString("rating"),
                            rankObj.getString("rateNumber"),
                            rankObj.getString("nowPrice"),
                            rankObj.getString("historyPrice"),
                            rankObj.getString("coupon"),
                            rankObj.getString("sponsored"),
                            rankObj.getString("prime"),
                            rankObj.getString("fresh")
                    );
                }

                csvPrinter.flush();
                csvPrinter.close();
            }
        }
    }

    public static void extract(String date, String path, String extractPath) throws IOException {
        File[] files = FileUtil.ls(path);
        for (File file : files) {
            if(file.getName().contains(date) == false) {
                continue;
            }
            List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
            for (String line : lines) {
                JSONObject jsonObject = JSON.parseObject(line, JSONObject.class);
                String kName = jsonObject.getString("keyword");

                CSVPrinter csvPrinter = new CSVPrinter(new FileWriter(extractPath+"/"+kName+".csv"), CSVFormat.DEFAULT.withDelimiter('\t'));
                csvPrinter.printRecord("RankPosition", "Title", "Link");

                JSONArray jsonArray = jsonObject.getJSONArray("keywordRankEntityVOs");
                if(jsonArray.isEmpty()) {
                    csvPrinter.flush();
                    csvPrinter.close();
                    continue;
                }
                int rank = 1;
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject rankObj = jsonArray.getJSONObject(i);
                    csvPrinter.printRecord(
                            rank++,
                            rankObj.getString("landingPage"),
                            rankObj.getString("label")
                    );
                }

                csvPrinter.flush();
                csvPrinter.close();
            }
        }
    }

    public void extractAndUpload(String date, String extractPath){

        try {
            FileUtil.mkdir(extractPath+"/merge");
            File walmartExtractFile = extractMergeOneFile(date,"/disk1/scribeKeyword/walmart_commoncrawl_keywordRank_111_1", extractPath+"/merge/walmart");
            File targetExtractFile = extractMergeOneFile(date,"/disk1/scribeKeyword/target_commoncrawl_keywordRank_112_1", extractPath+"/merge/target");
            File amazonExtractFile = extractAmazonMergeOneFile(date,"/disk1/scribeKeyword/amazon_commoncrawl_keywordRank_113_1", extractPath+"/merge/amazon");

            uploadToGcs(203177,walmartExtractFile);
            uploadToGcs(203178,amazonExtractFile);
            uploadToGcs(203179,targetExtractFile);

        }catch (Exception e){
            e.printStackTrace();
        }


    }

    private void uploadToGcs(int paramId, File outFile){

        CommonParamEntity commonParamEntity = commonParamDao.getParamJsonById(paramId);
        if(commonParamEntity == null){
            log.info("commonParamEntity is null");
            return;
        }
        String paramJson = commonParamEntity.getParamJson();
        Map<String, String> s3Info = new Gson().fromJson(paramJson, Map.class);
        String bucketName = s3Info.get("bucket");
        String path = s3Info.get("path");
        if (path.equalsIgnoreCase("/")) {
            path = "";
        }
        log.info("===gscPath:" + path + outFile.getName());
        try {
            String serverPath = GCSUtils.putFileByParamJson(paramJson,outFile.getAbsolutePath(), path + outFile.getName());
            System.out.println("=====serverPath:" +serverPath);
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload file to SetGCS , error message:" + e.getMessage());
        }

    }

    public File extractMergeOneFile(String date, String path, String extractPath) throws IOException {
        File[] files = FileUtil.ls(path);
        String extractFilePath = extractPath+"_"+date+".csv";
        File outFile = new File(extractFilePath);
        if(outFile.exists()){
            outFile.delete();
        }
        CSVPrinter csvPrinter = new CSVPrinter(new FileWriter(extractFilePath), CSVFormat.DEFAULT.withDelimiter('\t'));
        csvPrinter.printRecord("RankDate","KeywordName", "RankPosition", "Title", "Link");
        Set<String> uniqueSet = new HashSet<>();
        for (File file : files) {
            if(file.getName().contains(date) == false) {
                continue;
            }
            List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
            for (String line : lines) {
                JSONObject jsonObject = JSON.parseObject(line, JSONObject.class);
                String kName = URLDecoder.decode(jsonObject.getString("keyword"), "utf-8");
                if(uniqueSet.contains(kName)) {
                    continue;
                }
                uniqueSet.add(kName);
                JSONArray jsonArray = jsonObject.getJSONArray("keywordRankEntityVOs");
                if(jsonArray.isEmpty()) {
//                    csvPrinter.flush();
//                    csvPrinter.close();
                    continue;
                }
                int rank = 1;
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject rankObj = jsonArray.getJSONObject(i);
                    csvPrinter.printRecord(
                            date,
                            kName,
                            rank++,
                            rankObj.getString("landingPage"),
                            rankObj.getString("label")
                    );
                }
                csvPrinter.flush();
            }
        }
        csvPrinter.close();
        return outFile;
    }

    public File extractAmazonMergeOneFile(String date, String path, String extractPath) throws IOException {
        File[] files = FileUtil.ls(path);
        String extractFilePath = extractPath+"_"+date+".csv";
        File outFile = new File(extractFilePath);
        if(outFile.exists()){
            outFile.delete();
        }
        CSVPrinter csvPrinter = new CSVPrinter(new FileWriter(extractFilePath), CSVFormat.DEFAULT.withDelimiter('\t'));
        csvPrinter.printRecord(
                "RankDate","KeywordName","RankPosition", "Type", "Title", "Brand", "Link", "Image",
                "Asin", "BadgeLabel", "Rating", "RateNumber", "NowPrice", "HistoryPrice", "Coupon", "Sponsored", "Prime", "Fresh"
        );
        Set<String> uniqueSet = new HashSet<>();
        for (File file : files) {
            if(file.getName().contains(date) == false) {
                continue;
            }
            List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
            for (String line : lines) {
                JSONObject jsonObject = JSON.parseObject(line, JSONObject.class);
                String kName = URLDecoder.decode(jsonObject.getString("keyword"),"utf-8");
                if(uniqueSet.contains(kName)) {
                    continue;
                }
                uniqueSet.add(kName);
                JSONArray jsonArray = jsonObject.getJSONArray("keywordRankEntityVOs");
                if(jsonArray.isEmpty()) {
//                    csvPrinter.flush();
//                    csvPrinter.close();
                    continue;
                }
                int rank = 1;
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject rankObj = jsonArray.getJSONObject(i);
                    csvPrinter.printRecord(
                            date,
                            kName,
                            rank++,
                            rankObj.getString("type"),
                            rankObj.getString("title"),
                            rankObj.getString("brand"),
                            rankObj.getString("link"),
                            rankObj.getString("image"),
                            rankObj.getString("asin"),
                            rankObj.getString("badgeLabel"),
                            rankObj.getString("rating"),
                            rankObj.getString("rateNumber"),
                            rankObj.getString("nowPrice"),
                            rankObj.getString("historyPrice"),
                            rankObj.getString("coupon"),
                            rankObj.getString("sponsored"),
                            rankObj.getString("prime"),
                            rankObj.getString("fresh")
                    );
                }
                csvPrinter.flush();
            }
        }
        csvPrinter.close();
        return outFile;
    }

}
