package seoclarity.backend.export.onetime;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyRankingEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@CommonsLog
public class ExtractRgOneTime {

    private static final String SPLIT = "\t";

    private String domainName = "products.google.com";
    private String subrankDomainName = "cvs.com";
    private String device = "d";
    private static String LOC = "/home/<USER>/RGExtract/";

    private int engineId = 1;
    private int languageId = 1;

    private MonthlyRankingDao monthlyRankingDao;
    public ExtractRgOneTime(){
        monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
    }


    private void process(String month, File outFile){
//        String fileName = domainName + "_" + device + "_" + month + ".csv";
        try {
//            File outFile = new File(LOC + fileName);
//            if(outFile.exists()){
//                outFile.delete();
//            }
//
//            addHeadersForExactFile(outFile);

            List<MonthlyRankingEntity> subRankList = monthlyRankingDao.getProductSubRank(engineId, languageId, month, false, subrankDomainName);
            if(CollectionUtils.isNotEmpty(subRankList)){
                List<String> dataList = new ArrayList<>();
                log.info("===subRankList size:" + subRankList.size());
                for(MonthlyRankingEntity subRank: subRankList){
                    dataList.add(appendData(month, subRank, domainName));
                }
                FileUtils.writeLines(outFile, dataList, true);
            }


//            String domainReverse = StringUtils.reverseDelimited(domainName, '.');
//            String rootDomainReverse = CommonUtils.getReversedRootDomain(subrankDomainName);
//            log.info("===domainReverse:" + domainReverse + ",rootDomainReverse:" + rootDomainReverse);
//            int yearInt = Integer.parseInt(month.substring(0,4));
//            int monthInt = Integer.parseInt(month.substring(4,6));
//            List<String> dayList = FormatUtils.getDayByMonth(yearInt, monthInt);
////            log.info("===yearInt:" + yearInt + ",monthInt:" + monthInt + ",dayList:" + dayList);
//            for(String rankingDate: dayList){
//                List<MonthlyRankingEntity> detailList = monthlyRankingDao.getDetailByDomainReverseByDate(engineId, languageId, month, false, domainReverse, rankingDate);
//                if(CollectionUtils.isEmpty(detailList)){
//                    log.info("===empty day:" + rankingDate);
//                    continue;
//                }
//                log.info("===rankingDate:" + rankingDate + ",detailList size:" + detailList.size());
//                List<String> dataList = new ArrayList<>();
//                for(MonthlyRankingEntity detail: detailList){
//                    Integer keywordId = detail.getKeywordRankcheckId();
//                    Integer rank = detail.getRank();
//                    List<MonthlyRankingEntity> subRankList = monthlyRankingDao.getProductSubRank(engineId, languageId, month, false, rootDomainReverse, rankingDate, keywordId, rank);
//                    if(CollectionUtils.isNotEmpty(subRankList)){
//                        log.info("===rankingDate:" + rankingDate + ",subRankList size:" + subRankList.size());
//                        for(MonthlyRankingEntity subRank: subRankList){
//                            dataList.add(appendData(month, subRank, domainName));
//                        }
//                    }
//                }
//                FileUtils.writeLines(outFile, dataList, true);
//            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private static void addHeadersForExactFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Month").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Rank").append(SPLIT);
        header.append("Sub Rank").append(SPLIT);
        header.append("Search Volume").append(SPLIT);
//        header.append("Search Volume");
        header.append("Brand name");

        if (org.apache.commons.lang.StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }
    }

    public static String appendData(String month, MonthlyRankingEntity monthlyRankingEntity, String domainName) {
        StringBuffer line = new StringBuffer();

        line.append(month).append(SPLIT);
        line.append(monthlyRankingEntity.getKeywordName()).append(SPLIT);
        line.append(monthlyRankingEntity.getRank()).append(SPLIT);
        line.append(monthlyRankingEntity.getSubRank()).append(SPLIT);
        line.append(monthlyRankingEntity.getAvgSearchVolume() == null ? "-" : monthlyRankingEntity.getAvgSearchVolume()).append(SPLIT);
        line.append(monthlyRankingEntity.getDomainReverse());

        return line.toString();
    }

    public static void main(String[] args) throws Exception{

        ExtractRgOneTime extractRgOneTime = new ExtractRgOneTime();

        String fileName = "RG_product_cvs" + ".csv";
        File outFile = new File(LOC + fileName);
        if(outFile.exists()){
            outFile.delete();
        }

        addHeadersForExactFile(outFile);

        extractRgOneTime.process("202211", outFile);
        extractRgOneTime.process("202210", outFile);
        extractRgOneTime.process("202209", outFile);
        extractRgOneTime.process("202208", outFile);
        extractRgOneTime.process("202207", outFile);
        extractRgOneTime.process("202206", outFile);
        extractRgOneTime.process("202205", outFile);
        extractRgOneTime.process("202204", outFile);
        extractRgOneTime.process("202203", outFile);
        extractRgOneTime.process("202202", outFile);
        extractRgOneTime.process("202201", outFile);

    }
}
