package seoclarity.backend.export.onetime;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyRankingEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.ClarityDBAPIUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;

@CommonsLog
public class ExtractRIOneTime {

    private static final String SPLIT = "\t";
    private static String LOC = "/home/<USER>/1909/";
    private static String processDate = "2024-05-20";

    public static final List<Integer> APPLE_DOMAIN_ID_LIST = Arrays.asList(5739,5740,5741,5742,5743,5744,5745,5746,5747,
            5749,5750,5751,5752,5753,5754,5756,5757,5758,5759,5762,
            5763,5764,5765,5766,5767,5768,5769,5770,5771,5772,5774,5776,5784,5785,5786,5787,5843);

    public static final List<Integer> FANATICS_DOMAIN_ID_LIST = Arrays.asList(1909,2169,2172,2174,2175,2177,2180,2181,2185,
            2188,2189,2190,2191,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2204,2205,2208,2209,2210,2211,2212,2213,
            2214,2216,2217,2219,2221,2223,2226,2260,2264,2268,2269,2270,2276,2281,2287,2288,2299,2311,2317,2318,2323,2327,
            2329,2336,2337,2338,2339,2342,2349,2350,2355,2356,2358,2363,2364,2379,2394,2424,2671,3567,4354,4711,5063,5088,
            5089,5097,5098,5131,5255,5451,5635,5725,5849,5919,5938,6558,6564,6568,6570,6588,6592,8975,8976,8978,8980,8981,
            8982,8984,8985,8986,8987,8988,8992,8993,8994,8995,9009,9630,9631,9633,9634,9635,9636,9637,10015,10017,10018,
            10019,10020,10021,10023,10024,10025,10026,10028,10029,10030,10031,10034,10035,10036,10037,10038,10039,10041,
            10043,10044,10045,10046,10047,10048,10049,10050,10052,10053,10054,10055,10056,10057,10058,10059,10060,10061,
            10063,10064,10065,10066,10067,10068,10071,10113,10126,10215,10219,10220,10221,10222,10345,10352,10486,10487,
            10488,10489,10558,10559,10560,10561,10562,10563,10564,10565,10679,10680,10681,10682,10714,10911,10912,10913,
            10914,11045,11315,11381,11382,11521,11864,12079,12080,12081,12082,12115,12174,12196,12197,12275,12276,12660,
            12890,12891,12892,12893,12894,12895,12896,12897,12898,12899,12900,12901,12902,12903,12904,12905,12906,12907,
            12908,12909,12910,12911,12912,12913,12914,12915,12916,12917,12918,12919,12920,12921,12922,12923,12924,12925,
            12926,12927,12928,12929,12930,12931,12932,12933,12934,12935,12936,12937,12938,12939,12940,12941,12942,12943,
            12944,12945,12946,12947,12948,12949,12950,12951,12952,12953,12954,12955,12956,12957,12958,12959,12960,12961,
            12962,12963,12964,12965,12966,12967,12968,12969,12970,12971,12972,12973,12974,12975,12976,12977,12978,12979,
            12980,12981,12982,12983,12984,12985,12986,12987,12988,12989,12990,12991,12992,12993,12994,12995,12996,12997,
            12998,12999,13000,13001,13002,13003,13004,13005,13006,13007,13008,13009,13010,13011,13012,13013,13014,13015,
            13016,13017,13018,13019,13020,13021,13022,13023,13024,13025,13026,13027,13028,13029,13030,13031,13032,13033,
            13034,13035,13036,13037,13038,13039,13040,13041,13042,13043,13044,13045,13046,13047,13048,13049,13050,13051,
            13052,13053,13054,13055,13056,13057,13058,13059,13060,13061,13062,13063,13064,13065,13066,13067,13068,13069,
            13070,13071,13072,13073,13074,13075,13076,13077,13078,13079,13080,13081,13082,13083,13084,13085,13086,13087,
            13088,13089,13090,13091,13092,13093,13094,13095,13096,13097,13098,13099,13100,13101,13102,13103,13104,13105,
            13106,13107,13108,13109,13110,13111);

    public static final List<Integer> FANATICS_DOMAIN_ID_LIST_V2 = Arrays.asList(
            1909,2169,2172,2174,2175,2177,2180,2181,2185,2188,2189,2190,2191,2193,2194,2195,2196,2197,2198,2199,2200,2201,
            2202,2204,2205,2208,2209,2210,2211,2212,2213,2214,2216,2217,2219,2221,2223,2226,2260,2264,2268,2269,2270,2276,
            2281,2288,2299,2311,2317,2318,2323,2327,2329,2336,2337,2338,2339,2342,2350,2356,2363,2364,2379,2394,2424,2671,
            3567,4354,4711,5063,5088,5089,5097,5098,5131,5255,5451,5635,5725,5849,5919,5938,6558,6564,6568,6570,6588,6592,
            8975,8976,8978,8980,8981,8982,8984,8985,8986,8987,8988,8992,8993,8994,8995,9009,9630,9631,9633,9634,9635,9636,
            9637,10018,10020,10023,10024,10025,10026,10028,10030,10031,10034,10037,10045,10046,10047,10048,10049,10050,10052,
            10053,10054,10055,10056,10057,10058,10059,10060,10061,10064,10065,10066,10067,10113,10126,10215,10219,10221,
            10222,10352,10486,10487,10488,10558,10560,10561,10563,10565,10679,10680,10681,10682,10714,10911,10914,11315,
            11381,11521,11864,12080,12275,12276
    );

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ScKeywordRankManager scKeywordRankManager;
    private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    public ExtractRIOneTime(){
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
    }


    private void process(int ownDomainId, String rankingDate, File outFile){
        try {
            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
            if (ownDomainEntity == null) {
                log.error(" domain not exist : " + ownDomainId);
                return;
            }
            OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);

            int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
            int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
            String domainName = ownDomainEntity.getDomain();
            boolean isBroadMatch = ownDomainEntity.isBroadMatch();
            int locationId = 0;

            ExtractQueryVO extractQueryVO = new ExtractQueryVO();
            extractQueryVO.setDomainId(ownDomainId);
            extractQueryVO.setRankDate(rankingDate);
            extractQueryVO.setLocationId(locationId);
            extractQueryVO.setEnabledDiffSes(ownDomainSettingEntity.differentVsKeywordSetEnabled());

            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getAllSERels(ownDomainEntity);
            if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
                for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                    engineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    languageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    boolean isMobile = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? false : true;
                    if(!isMobile){
                        log.error("=====skip desktop engine:engine :" + engineId + ",languageId: " + languageId + ",isMobile:" + isMobile + ",domain:" + ownDomainId);
                        continue;
                    }
                    log.info("===device:" + domainSearchEngineRelEntity.getDevice());
                    String device = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? "desktop" : "mobile";
                    EngineCountryLanguageMappingEntity engineCountryLanguageMapping =engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageId(engineId, languageId);
                    String engineName = engineCountryLanguageMapping.getEngineDisplayName() + "(" +device + ")";

                    extractQueryVO.setEngineId(engineId);
                    extractQueryVO.setLanguageId(languageId);
                    extractQueryVO.setDevice(device);
                    log.info("===process engine :" + engineId + ",languageId: " + languageId + ",isMobile:" + isMobile + ",domain:" + ownDomainId);
                    List<CLRankingDetailEntity> dataList = new ArrayList<>();
                    dataList = clDailyRankingEntityDao.exportAppleExtractOneTime(extractQueryVO);
                    log.info("===dataList size:" + dataList.size());
                    if(CollectionUtils.isNotEmpty(dataList)){
                        List<String> lineList = new ArrayList<>();
                        for(CLRankingDetailEntity rankingDetail: dataList){
                            lineList.add(appendDataForFanatics(ownDomainId, domainName, rankingDetail));
                        }
                        FileUtils.writeLines(outFile, lineList, true);
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private static void addHeadersForExactFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("DomainId").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Date Added").append(SPLIT);
        header.append("Average Search Volume").append(SPLIT);
        header.append("Search Engine").append(SPLIT);
        header.append("Assigned Tags");

        if (StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }
    }

    private static void addHeadersForFanatics(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("DomainId").append(SPLIT);
        header.append("Domain").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Search Volume").append(SPLIT);
        header.append("Assigned Tags").append(SPLIT);
        header.append("Assigned Count");

        if (StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }
    }

    public static String appendData(int domainId, String engineName, CLRankingDetailEntity clRankingDetail) {
        StringBuffer line = new StringBuffer();
        line.append(domainId).append(SPLIT);
        line.append(clRankingDetail.getKeywordName()).append(SPLIT);
        line.append(clRankingDetail.getCreateDate()).append(SPLIT);
        String avgSv = "";
        if(clRankingDetail.getAvgSearchVolume() == null || clRankingDetail.getAvgSearchVolume().intValue() == -1){
            avgSv = "-";
        }else {
            avgSv = clRankingDetail.getAvgSearchVolume().toString();
        }
        line.append(avgSv).append(SPLIT);
        line.append(engineName).append(SPLIT);
        line.append(StringUtils.join(clRankingDetail.getTagList(), ","));
        return line.toString();
    }

    public static String appendDataForFanatics(int domainId, String domainName, CLRankingDetailEntity clRankingDetail) {
        StringBuffer line = new StringBuffer();
        line.append(domainId).append(SPLIT);
        line.append(domainName).append(SPLIT);
        line.append(clRankingDetail.getKeywordName()).append(SPLIT);
        String avgSv = "";
        if(clRankingDetail.getAvgSearchVolume() == null || clRankingDetail.getAvgSearchVolume().intValue() == -1){
            avgSv = "-";
        }else {
            avgSv = clRankingDetail.getAvgSearchVolume().toString();
        }
        line.append(avgSv).append(SPLIT);
        line.append(StringUtils.join(clRankingDetail.getTagList(), ",")).append(SPLIT);
        line.append(clRankingDetail.getTagList().size());
        return line.toString();
    }


    private static String getExtractSqlByAPI(int ownDomainId){
        // get sql
        String url = "https://s11-dev.seoclarity.dev/seoClarity/rankIntelligenceAPI/getDomainHighestRankingSql";
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("accessToken","c09yxv13-opr3-d745-9734-8pu48420nj67");
        paramMap.put("domainId",ownDomainId);
        paramMap.put("mobile",true);
        List<String> rankingDateList = new ArrayList<>();
        rankingDateList.add("20240527");
        paramMap.put("indexList",rankingDateList);
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        log.info("===request paramJson:" + gson.toJson(paramMap));

        String response = null;
        int max = 10;
        int tryCnt = 0;
        while (tryCnt <= max) {
            tryCnt++;
            try {
                response = ClarityDBAPIUtils.simplePost(url, gson.toJson(paramMap));
                break;
            } catch (Exception e) {
                log.info("===request failed. tryCnt:" + tryCnt + ", paramJson:" + gson.toJson(paramMap) + ", response:" + response);
                log.error(e.getMessage());
                e.printStackTrace();
            }
        }
        Map<String, String> responseMap = gson.fromJson(response, Map.class);
        String sql = responseMap.get("data");
        sql = sql.replace("\\u003d", "=");
        log.info("===response:" + sql);
        return sql;
    }

    private void processByApi(int ownDomainId, File outFile) throws Exception{
        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }
        List<String> lineList = new ArrayList<>();
        String sql = getExtractSqlByAPI(ownDomainId);

        List<Map<String, Object>> resultList = clDailyRankingEntityDao.executeSql(sql, null);
//        log.info("===resultList: " + new Gson().toJson(resultList));

        for(Map<String, Object> map: resultList){
            lineList.add(appendDataForFanaticsV2(ownDomainId, ownDomainEntity.getDomain(), map));
        }
        FileUtils.writeLines(outFile, lineList, true);
    }

    private static void addHeadersForFanaticsV2(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("DomainId").append(SPLIT);
        header.append("Domain").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Date Added").append(SPLIT);
        header.append("Search Volume").append(SPLIT);
        header.append("Rank").append(SPLIT);
        header.append("Estd. Traffic").append(SPLIT);
        header.append("Share Of Voice").append(SPLIT);
        header.append("Share Of Market").append(SPLIT);
        header.append("Page").append(SPLIT);
        header.append("Assigned Tags");

        if (StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }
    }
    public static String appendDataForFanaticsV2(int domainId, String domainName, Map<String, Object> map) {
        Gson gson = new Gson();
        StringBuffer line = new StringBuffer();
        line.append(domainId).append(SPLIT);
        line.append(domainName).append(SPLIT);
        line.append(map.get("keyword_name")).append(SPLIT);
        line.append(map.get("createDate")).append(SPLIT);
        line.append(map.get("avg_search_volume")).append(SPLIT);
        line.append(map.get("rank")).append(SPLIT);
        line.append(map.get("domainEstTraffic")).append(SPLIT);
        line.append(map.get("domainSov")).append(SPLIT);
        line.append(map.get("domainSom")).append(SPLIT);
        line.append(map.get("domainUrl")).append(SPLIT);
        TagMapEntity tagMapEntity = gson.fromJson(gson.toJson(map.get("tagNames")), TagMapEntity.class);
//        line.append(gson.toJson(tagMapEntity.getArray()));
        line.append(StringUtils.join(tagMapEntity.getArray(), ",")).append(SPLIT);
        return line.toString();
    }

    public static void main(String[] args) throws Exception{

        ExtractRIOneTime extractRIOneTime = new ExtractRIOneTime();
        String fileName = "FanaticsHighestRanking_0527" + ".csv";
        File outFile = new File(LOC + fileName);
        if(outFile.exists()){
            outFile.delete();
        }
        addHeadersForFanaticsV2(outFile);
        for(Integer oid: FANATICS_DOMAIN_ID_LIST_V2){
            extractRIOneTime.processByApi(oid, outFile);
        }


//        addHeadersForExactFile(outFile);
//        for(Integer oid: APPLE_DOMAIN_ID_LIST){
//            extractRIOneTime.process(oid, processDate, outFile);
//        }
//        addHeadersForFanatics(outFile);
//        for(Integer oid: FANATICS_DOMAIN_ID_LIST){
//            extractRIOneTime.process(oid, processDate, outFile);
//        }
    }

}


class TagMapEntity {
    private String elementType;
    private List<String> array;

    public String getElementType() {
        return elementType;
    }

    public void setElementType(String elementType) {
        this.elementType = elementType;
    }

    public List<String> getArray() {
        return array;
    }

    public void setArray(List<String> array) {
        this.array = array;
    }
}
