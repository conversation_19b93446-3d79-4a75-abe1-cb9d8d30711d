package seoclarity.backend.export.onetime;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.adhoc.AdhocInfoDao;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.ServerAuthenticationInfoEntity;
import seoclarity.backend.service.*;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;

@CommonsLog
public class ExtractAdhoc4765 {

    private static final String SPLIT = "[EXPEDIA-SEPERATOR]";
    private static String LOC = "/home/<USER>/";
    private static final int QUERY_TRY_COUNT = 3;

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private GeoService geoService;
    private AdhocInfoDao adhocInfoDao;

    public static final Map<String, String> SUB_FEATURE_MAP = new HashMap();
    public ExtractAdhoc4765(){
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        geoService = SpringBeanFactory.getBean("geoService");
        adhocInfoDao = SpringBeanFactory.getBean("adhocInfoDao");


    }

    static {
        SUB_FEATURE_MAP.put("answerbox", "Answer Box");
        SUB_FEATURE_MAP.put("buyingGuide", "Buying Guide");
        SUB_FEATURE_MAP.put("discussionAndForums", "Discussion And Forums");
        SUB_FEATURE_MAP.put("app", "App");
        SUB_FEATURE_MAP.put("popularStore", "Popular Store");
        SUB_FEATURE_MAP.put("amp", "Amp");
        SUB_FEATURE_MAP.put("faq", "FAQ");
        SUB_FEATURE_MAP.put("freeShop", "Free Product Listings");
        SUB_FEATURE_MAP.put("job", "Job Pack");
        SUB_FEATURE_MAP.put("knog", "Knowledge Graph");
        SUB_FEATURE_MAP.put("peoplealsoask", "People Also Ask");
        SUB_FEATURE_MAP.put("products", "Products");
        SUB_FEATURE_MAP.put("ppc", "PPC");
        SUB_FEATURE_MAP.put("pla", "Shopping");
        SUB_FEATURE_MAP.put("thingsToKnow", "Things To Know");
        SUB_FEATURE_MAP.put("ai_genai_search", "AI Overview");
        SUB_FEATURE_MAP.put("address", "Maps");
        SUB_FEATURE_MAP.put("img", "Image");
        SUB_FEATURE_MAP.put("recipes", "Recipes");
        SUB_FEATURE_MAP.put("ll", "Local Listing");
        SUB_FEATURE_MAP.put("hotel", "Hotel");
        SUB_FEATURE_MAP.put("news", "News");
        SUB_FEATURE_MAP.put("estimatedSalary", "Estimated Salary");
        SUB_FEATURE_MAP.put("video", "Video");
        SUB_FEATURE_MAP.put("shortVideo", "Short Video");
        SUB_FEATURE_MAP.put("interestingFinds", "Interesting Finds");
        SUB_FEATURE_MAP.put("comparison", "Comparison");
        SUB_FEATURE_MAP.put("populardestinations", "Popular Destinations");
        SUB_FEATURE_MAP.put("topsights", "Top Sights");
        SUB_FEATURE_MAP.put("twitter", "Twitter");
        SUB_FEATURE_MAP.put("finance", "Finance");
        SUB_FEATURE_MAP.put("findresultson", "Find Results On");
        SUB_FEATURE_MAP.put("nearyou", "Near You");
        SUB_FEATURE_MAP.put("fromsourcesacrosstheweb", "From Sources Across The Web");
    }

    private void processForDomain(int ownDomainId, Date processDate, String device) throws Exception{

        log.info("********************** process domain " + ownDomainId + ",processDate: " + processDate + " *******************");

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);


        int processingDate = FormatUtils.formatDateToYyyyMmDd(processDate);

        String localFilePath = LOC + ownDomainId + File.separator;
        String fileName = getFileName(ownDomainEntity, processingDate, device);

        File localFolder = new File(LOC + ownDomainId);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }
        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        addHeadersForExactFile(localFile, ownDomainEntity);

        List<String> dataList = new ArrayList<>();
        int retryCount = 1;
        while (true) {
            try {
                Thread.sleep(1000);
                dataList = getDataFromDB(ownDomainEntity, processDate, device);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + ownDomainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + ownDomainId);
                    return;
                }

                e.printStackTrace();
                System.out.println("====extract error oid:" + ownDomainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        if(CollectionUtils.isEmpty(dataList)){
            log.info("============dataListEmpty");
            return;
        }
        FileUtils.writeLines(localFile, dataList, true);

    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity, Date processDate, String device) throws Exception {

        List<String> extractLines = new ArrayList<String>();

        int ownDomainId = ownDomainEntity.getId();
//        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
//        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);

        int engineId = 1;
        int languageId = 1;

        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

        List<Map<String, Object>> dataList = new ArrayList<>();
        boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
        String extractKey = engineId + "_" + languageId + "_" + isMobile;
        int locationId = 0;
        int topX = 100;
        int projectId = 3180;

        ExtractQueryVO extractQueryVO = new ExtractQueryVO();
        extractQueryVO.setDomainId(ownDomainId);
        extractQueryVO.setEngineId(engineId);
        extractQueryVO.setLanguageId(languageId);
        extractQueryVO.setDevice(device);
        extractQueryVO.setRankDate(rankingDate);
        extractQueryVO.setRank(topX);
        extractQueryVO.setLocationId(locationId);
        dataList = adhocInfoDao.export4765SubFeatureKeywords(projectId, extractQueryVO, engineId, languageId);

        log.info("===dataList size: " + dataList.size());


        for (Map<String, Object> detail : dataList) {
            String locationName =  detail.get("location_id").toString().equals("0") ? "National" :
                    geoService.getCityName(Integer.parseInt(detail.get("location_id").toString()));
            if (StringUtils.isBlank(locationName)) {
                locationName = "-";
            }
            extractLines.add(appendData(detail, ownDomainId, locationName));
        }


        return extractLines;
    }

    private String getFileName(OwnDomainEntity ownDomainEntity, int processingDate, String device) {
        String fileName = ownDomainEntity.getId() + "_" + processingDate + "_RankReport_" + device + ".txt";
        return fileName;
    }
    private void addHeadersForExactFile(File outFile, OwnDomainEntity ownDomainEntity) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        int domainId = ownDomainEntity.getId();
        header.append("Keyword").append(SPLIT);
        header.append("RankingURL").append(SPLIT);
        header.append("TrueRank").append(SPLIT);
        header.append("WebRank").append(SPLIT);
        header.append("Title").append(SPLIT);
        header.append("Meta").append(SPLIT);
        header.append("SubRank").append(SPLIT);
        header.append("SubRankRankingUrl").append(SPLIT);
        header.append("Schema");
        if (StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }
    }

    public static String appendData(Map<String, Object> clRankingDetailEntity, int domainId, String locationName) {
        StringBuffer line = new StringBuffer();
        Gson gson = new Gson();

        String keywordName = clRankingDetailEntity.get("keyword_name").toString();
        String url = clRankingDetailEntity.get("url").toString();
        String trueRank = clRankingDetailEntity.get("true_rank").toString();
        String webRank = clRankingDetailEntity.get("web_rank").toString();
        String subRank = clRankingDetailEntity.get("sub_rank").toString();
        String subRankUrl = clRankingDetailEntity.get("subRankUrl").toString();
        String label = clRankingDetailEntity.get("label").toString();
        String meta = clRankingDetailEntity.get("meta").toString();
        String event = clRankingDetailEntity.get("event").toString();
        String price = clRankingDetailEntity.get("price").toString();
        String stock = clRankingDetailEntity.get("stock").toString();
        String faq = clRankingDetailEntity.get("FAQ").toString();
        String customPromoCode = clRankingDetailEntity.get("customPromoCode").toString();
        String startRating = clRankingDetailEntity.get("startRating").toString();
        String videoThumbnail = clRankingDetailEntity.get("videoThumbnail").toString();
        String imageThumbnail = clRankingDetailEntity.get("imageThumbnail").toString();
        String imageCarousel = clRankingDetailEntity.get("imageCarousel").toString();
        String typeNameMap = clRankingDetailEntity.get("typeNameMap").toString();
//        log.info("===typeNameMap:" + typeNameMap);
//        clRankingDetailEntity.get("");

        line.append(keywordName).append(SPLIT);

        line.append(StringUtils.isBlank(url) ? "-" : ExtractService.formatGoogleUrl(url)).append(SPLIT);
        if (trueRank.equals("-1") || trueRank.equals("0")) {
            line.append("101").append(SPLIT);
        } else {
            line.append(trueRank).append(SPLIT);
        }
        if (webRank.equals("-1") || webRank.equals("0")) {
            line.append("101").append(SPLIT);
        } else {
            line.append(webRank).append(SPLIT);
        }
        line.append(StringUtils.isBlank(label) ? "-" : CommonUtils.formatTitleMeta(label)).append(SPLIT);
        line.append(StringUtils.isBlank(meta) ? "-" : CommonUtils.formatTitleMeta(meta)).append(SPLIT);

        if (subRank.equals("-1") || subRank.equals("0")) {
            line.append("101").append(SPLIT);
        } else {
            line.append(subRank).append(SPLIT);
        }
        line.append(StringUtils.isBlank(subRankUrl) ? "-" : ExtractService.formatGoogleUrl(subRankUrl)).append(SPLIT);

        if(event.equals("1")){
            line.append("Event").append(SPLIT);
        }
        if(price.equals("1")){
            line.append("Price").append(SPLIT);
        }
        if(stock.equals("1")){
            line.append("Stock").append(SPLIT);
        }
        if(faq.equals("1")){
            line.append("FAQ").append(SPLIT);
        }
        if(customPromoCode.equals("1")){
            line.append("CustomPromoCode").append(SPLIT);
        }
        if(startRating.equals("1")){
            line.append("Start Rating").append(SPLIT);
        }
        if(videoThumbnail.equals("1")){
            line.append("Video Thumbnail").append(SPLIT);
        }
        if(imageThumbnail.equals("1")){
            line.append("Image Thumbnail").append(SPLIT);
        }
        if(imageCarousel.equals("1")){
            line.append("Image Carousel").append(SPLIT);
        }

        if(StringUtils.isNotBlank(typeNameMap)){
            line.append(SUB_FEATURE_MAP.get(typeNameMap));
        }

        String returnLIne = line.toString();
        if(returnLIne.endsWith(SPLIT)){
            returnLIne = returnLIne.substring(0, returnLIne.length() - SPLIT.length());
        }

        return returnLIne;
    }


    private void process(){



    }


    public static void main(String[] args) throws Exception{

        ExtractAdhoc4765 extractAdhoc4765 = new ExtractAdhoc4765();
        extractAdhoc4765.processForDomain(4, FormatUtils.toDate("2024-09-11", "yyyy-MM-dd"), "desktop");
    }


}
