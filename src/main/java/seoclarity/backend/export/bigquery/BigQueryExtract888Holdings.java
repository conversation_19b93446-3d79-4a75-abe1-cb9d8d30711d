package seoclarity.backend.export.bigquery;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.bigquery.Bigquery;
import com.google.api.services.bigquery.model.*;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.jets3t.service.impl.rest.httpclient.GoogleStorageService;
import org.jets3t.service.model.GSObject;
import org.jets3t.service.model.StorageBucket;
import org.jets3t.service.security.GSCredentials;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.actonia.geo.KeywordGeoMappingDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.geo.KeywordGeoMappingEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.GeoService;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.*;

/**
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.export.bigquery.BigQueryExtract8625" -Dexec.args="8625 desktop null JSON false" -Dexec.cleanupDaemonThreads=false > log/BigQueryExtract8625Test.log &
 */
public class BigQueryExtract888Holdings {

    private static Bigquery bigquery;
    private static final List<String> SCOPE = Arrays.asList("https://www.googleapis.com/auth/bigquery");
    private static final HttpTransport TRANSPORT = new NetHttpTransport();
    private static final JsonFactory JSON_FACTORY = new JacksonFactory();
    private static final String AccountId = "<EMAIL>";
    private static final String bucketName = "seoclarity-data-extract";

    private static final String ACCESS_KEY = "GOOGUHISCO6OKJ24O5PO2UIT";
    private static final String SECRET_KEY = "xTgO4yASLxWRtUoND81h1xuk8Q+APBNpinQCYvaX";

    private static final String projectId = "seoclarity-888";
    private static final String dataSetId = "seoClarity_888_dataset";
    private static final String tableId = "seoclarity_extract_v3";

//    private static final String projectId = "seoclarity-rank-intelligence";
//    private static final String dataSetId = "seoclarity_demo_dataset";
//    private static final String tableId = "jason_test_table_20200629";

    private static final String PRIVATE_KEY_PATH = "/home/<USER>/source/extractScripts/clarity-backend-scripts/seoClarity-888-f47de2cae19e.p12";

    private static final String SPLIT = ",";
    private static final String KEY_SPLIT = "#_#";

    private static final String EXTRACT_TYPE = "organic";

    private static String LOC = "/home/<USER>/";
    private static int topRank = 10;

    private KeywordEntityDAO keywordEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private GroupTagRelationEntityDAO groupTagRelationEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private GeoService geoService;
    private KeywordGeoMappingDAO keywordGeoMappingDAO;
    private ExtractService extractService;

    Map<String, JsonExtractEntity> extractJsonMap = new HashMap<>();
    private static String rankDate;
    private static String device;
    private static String fileType = "CSV";
    private static boolean isCity = false;
    private static boolean isRerun = false;
    private LogglyVO logglyVO = new LogglyVO();

    private int searchEngineId;
    private int searchLanguageId;

    public BigQueryExtract888Holdings() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        groupTagRelationEntityDAO = SpringBeanFactory.getBean("groupTagRelationEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        geoService = SpringBeanFactory.getBean("geoService");
        keywordGeoMappingDAO = SpringBeanFactory.getBean("keywordGeoMappingDAO");
        extractService = SpringBeanFactory.getBean("extractService");
    }

    public static void main(String[] args) throws Exception {


        String[] domainIds = null;

        if (args != null && args.length > 0) {
            domainIds = args[0].split(",");
        }


        if (args != null && args.length > 1) {
            device = args[1];
        }

        String processDate = "";
        if (args != null && args.length > 2 && !args[2].equalsIgnoreCase("null")) {
            processDate = args[2];
            isRerun = true;
        } else {
            processDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -1), "yyyy-MM-dd");
        }

        if (args != null && args.length > 3) {
            fileType = args[3];
        }
        if (args != null && args.length > 4) {
            isCity = Boolean.parseBoolean(args[4]);
        }
        if (args != null && args.length > 5) {
            topRank = Integer.parseInt(args[5]);
        }

        System.out.println("=====domainIds: " + domainIds + " ,device: " + device + " ,rankDate: " + rankDate
                + " ,fileType: " + fileType + " ,isCity: " + isCity + ",topRank:" + topRank);

        //create table
        BigQueryUtils bigQueryUtils = new BigQueryUtils(projectId, dataSetId, tableId, AccountId, PRIVATE_KEY_PATH);
        try {
            bigQueryUtils.createTableByRestApi();
        } catch (GoogleJsonResponseException e) {
            if (e.getDetails().getCode() == 409) {
                System.out.println("===table Already Exists:" + e.getMessage());
            } else {
                System.out.println("===create table error:" + e.getMessage());
                System.exit(1);
            }
        } catch (Exception e){
            e.printStackTrace();
            System.exit(1);
        }


        BigQueryExtract888Holdings bigQueryExtract = new BigQueryExtract888Holdings();

        for (String domainId : domainIds) {
            bigQueryExtract.process(Integer.parseInt(domainId), processDate);
        }
    }

    private void process(int domainId, String processDate) {

        extractJsonMap = new HashMap<>();
        System.out.println("========start to extract domain : " + domainId);
        String sTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
        try {
            String fileName = "Extract_" + domainId + "_" + device + "_" + rankDate + "_" + fileType + ".txt";
            String filePath = LOC + domainId + File.separator + fileName;
            File outFile = new File(filePath);
            if (outFile.exists()) {
                outFile.delete();
            }

            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(domainId);
            if (ownDomainEntity == null) {
                System.out.println("=== domain not exist , exit !!");
                try {
                    extractService.sendMailReport("ERROR:Export for inactive OID:" + domainId, "Please disable export for inactive OID:" + domainId + "(" + getClass().getName() + ")");
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
                return;
            }
            int frequency = 1;//daily
            rankDate = processDate;
            if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
                if(!isRerun){//定时任务运行日期为周日，main方法中默认-1天
                    rankDate = FormatUtils.getLastSundayForWeeklyOfString(FormatUtils.formatDate(new Date(), FormatUtils.DATE_PATTERN_2));
                }else {
                    rankDate = FormatUtils.getLastSundayForWeeklyOfString(processDate);
                }
                System.out.println("====weekly domain processDate:" + rankDate);
                frequency = 7;//weekly
            }
            LogglyVO logglyVO = new LogglyVO();
            logglyVO.setoId(String.valueOf(domainId));
            logglyVO.setName("BigQueryExtract888Holdings");
            logglyVO.setDevice(device.equalsIgnoreCase("desktop") ? "d" : "m");

            logglyVO.setpDate(rankDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_BIG_QUERY_EXTRACT);
            logglyVO.setGroups(groupList);

            OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);
            boolean isRegionDomain = ScKeywordRankManager.isRegionDomain(ownDomainEntity,ownDomainSettingEntity);
            if(isRegionDomain){
                isCity = true;
            }else {
                isCity = false;
            }

            String domainName = ownDomainEntity.getDomain();
            String locale = ownDomainEntity.getLanguage() + "-" + ownDomainEntity.getSearchEngineCountry().toLowerCase();
            searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
            searchLanguageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
            String abbrDevice = device.equalsIgnoreCase("desktop") ? "d" : "m";

            List<KeywordEntity> keywordEntityList = keywordEntityDAO.getKeywordByDomainId(domainId);
            System.out.println("====keywordEntityList size: " + keywordEntityList.size());

            Map<Long, String> keywordMap = new HashMap<>();
            Map<Long, String> tagMap = new HashMap<>();

            List<Long> keywordIdList = new ArrayList<>();
            List<KeywordGeoMappingEntity> allKeywords = new ArrayList<>();
            List<String> extractList = new ArrayList<>();

            for (KeywordEntity keywordEntity : keywordEntityList) {

                Long keywordId = keywordEntity.getId();
                String decodeKeyword = URLDecoder.decode(keywordEntity.getKeywordName(), "utf-8");

                keywordMap.put(keywordId, decodeKeyword);

                keywordIdList.add(keywordId);

                if (keywordIdList.size() >= 100) {
                    //tag
                    List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeyword(domainId, keywordIdList);
                    if (CollectionUtils.isNotEmpty(tagList)) {
                        for (GroupTagEntity groupTagEntity : tagList) {
                            tagMap.put(groupTagEntity.getResourceId(), groupTagEntity.getTagName());
                        }

                    }
                    //city
                    List<KeywordGeoMappingEntity> keywordGeoMappingList = keywordGeoMappingDAO.getAllCityByKeywordList(domainId, searchEngineId, searchLanguageId, abbrDevice, keywordIdList);
                    allKeywords.addAll(keywordGeoMappingList);

                    keywordIdList.clear();
                }

            }

            if (CollectionUtils.isNotEmpty(keywordIdList)) {
                //tag
                List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeyword(domainId, keywordIdList);
                if (CollectionUtils.isNotEmpty(tagList)) {
                    for (GroupTagEntity groupTagEntity : tagList) {
                        tagMap.put(groupTagEntity.getResourceId(), groupTagEntity.getTagName());
                    }

                }
                //city
                List<KeywordGeoMappingEntity> keywordGeoMappingList = keywordGeoMappingDAO.getAllCityByKeywordList(domainId, searchEngineId, searchLanguageId, abbrDevice, keywordIdList);
                allKeywords.addAll(keywordGeoMappingList);
            }

            Map<String, String> mysqlDataMap = new HashMap<>();
            List<Integer> cityKeywordIdList = new ArrayList<>();
//            for (KeywordCityRelation keywordCityRelation : allKeywords) {
//
//                Integer keywordId = keywordCityRelation.getKeywordId();
//                Integer cityId = keywordCityRelation.getCityId();
//                String decodeKeywordName = keywordMap.get(keywordId);
//
//                String key = decodeKeywordName + KEY_SPLIT + cityId;
//
//                ExtractQueryVO queryEntity = new ExtractQueryVO();
//                queryEntity.setTagNames(tagMap.get(keywordId));
//
//                mysqlDataMap.put(key, tagMap.get(keywordId));
//                cityKeywordIdList.add(keywordId);
//            }

            for (KeywordEntity keywordEntity : keywordEntityList) {
                Long keywordId = keywordEntity.getId();
                String decodeKeywordName = URLDecoder.decode(keywordEntity.getKeywordName(), "utf-8");
                if (!cityKeywordIdList.contains(keywordId)) {
//                    System.out.println("=== decodeKeywordName : " + decodeKeywordName);
                    String key = decodeKeywordName + KEY_SPLIT + 0;
                    mysqlDataMap.put(key, tagMap.get(keywordId));
                }

            }

            //get ranking data from kp
            ExtractQueryVO queryEntity = new ExtractQueryVO();
            queryEntity.setDomainId(domainId);
            queryEntity.setEngineId(searchEngineId);
            queryEntity.setLanguageId(searchLanguageId);
            queryEntity.setDevice(device);
            queryEntity.setRankDate(rankDate);
            queryEntity.setRank(topRank);

            //subRank
            Map<String, List<CLRankingDetailEntity>> subRankMap = new HashMap<>();
            List<CLRankingDetailEntity> subRankList = clDailyRankingEntityDao.getSubRankInfoForBigQuery(queryEntity, searchEngineId, searchLanguageId);
            for (CLRankingDetailEntity subRank : subRankList) {

                String subRankKey = subRank.getKeywordRankcheckId() + KEY_SPLIT + subRank.getLocationId() + KEY_SPLIT + subRank.getRank();

                List<CLRankingDetailEntity> subRankInfoList = new ArrayList<>();
                if (subRankMap.get(subRankKey) == null) {
                    subRankInfoList.add(subRank);
                    subRankMap.put(subRankKey, subRankInfoList);
                } else {
                    subRankInfoList = subRankMap.get(subRankKey);
                    subRankInfoList.add(subRank);
                    subRankMap.put(subRankKey, subRankInfoList);
                }

            }

            //https://www.wrike.com/open.htm?id=614656875
            List<Integer> locationIdList = geoService.getLocationIdList(domainId);
            System.out.println("locationIdList=>"+locationIdList);
            Integer [] idArr = locationIdList.toArray(new Integer[locationIdList.size()]);
            //detail rank
            List<CLRankingDetailEntity> rankingDetailList = clDailyRankingEntityDao.exportForDomainV2(queryEntity, isCity, searchEngineId, searchLanguageId, idArr);
            System.out.println("====rankingDetailList size: " + rankingDetailList.size());

            for (CLRankingDetailEntity clRankingDetailEntity : rankingDetailList) {

                String key = clRankingDetailEntity.getKeywordName() + KEY_SPLIT + clRankingDetailEntity.getLocationId();

                String tagNames = mysqlDataMap.get(key);
                String locationName;
                if (clRankingDetailEntity.getLocationId().equals(0)) {
                    locationName = "National";
                } else {
                    locationName = geoService.getCityName(clRankingDetailEntity.getLocationId());
                }

                KeywordRankEntityVO keywordRankEntityVO = new KeywordRankEntityVO();
                keywordRankEntityVO.setType(clRankingDetailEntity.getType());
                String typeName = RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType());
                clRankingDetailEntity.setTypeName(typeName);

                boolean isHaveSubRank = false;

                String subRankKey = clRankingDetailEntity.getKeywordRankcheckId() + KEY_SPLIT + clRankingDetailEntity.getLocationId()
                        + KEY_SPLIT + clRankingDetailEntity.getTrueRank();
                List<CLRankingDetailEntity> subRankInfoList = subRankMap.get(subRankKey);
                if (CollectionUtils.isNotEmpty(subRankInfoList)) {
                    isHaveSubRank = true;
                    //JSON file
                    formatJson(domainId, domainName, clRankingDetailEntity, tagNames, typeName, subRankInfoList, locationName, locale);
                }

                if (!isHaveSubRank) {
                    //JSON file
                    formatJson(domainId, domainName, clRankingDetailEntity, tagNames, null, null, locationName, locale);
                }

            }

            if (fileType.equalsIgnoreCase("json")) {
                extractList.clear();
                System.out.println("=====extractJsonMap size : " + extractJsonMap.size());
                for (String keywordName : extractJsonMap.keySet()) {

                    String line = new Gson().toJson(extractJsonMap.get(keywordName));
                    extractList.add(line);
                    FileUtils.writeLines(outFile, extractList, true);
                    extractList.clear();
                }
            }

            if(!outFile.exists()){
                System.out.println("===extract file not exist !!");
                return;
            }

            //send to google cloud storage
            uploadFileToGS(filePath);
            String remotePath = LOC + domainId;

            FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, filePath, remotePath, 0, 3);

            //push to google big query
            uploadToBigQuery(fileName);

//            String message = "";
//            if(extractJsonMap.size() != 806){
//                message = "HM extract " + rankDate + " Missing keyword not 806 , is " + extractJsonMap.size();
//                sendMailReport("Failed !!!! ", message);
//            }else {
//            message = "888 holdings bigquery extract " + rankDate + " Success";
//            sendMailReport("Success !!!! ", message);
//            }

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(sTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(extractJsonMap.size()));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

        } catch (Exception e) {
            e.printStackTrace();
//            String message = "888 holdings bigquery extract " + rankDate + " Failed !!! size : " + extractJsonMap.size();
//            sendMailReport("Failed !!!! ", message);

            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        }

    }

    public static void addHeadersForExactFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("keyword").append(SPLIT);
        header.append("type").append(SPLIT);
        header.append("date").append(SPLIT);
        header.append("topkey").append(SPLIT);
        header.append("locale").append(SPLIT);
        header.append("geo").append(SPLIT);
        header.append("browser").append(SPLIT);
        header.append("https").append(SPLIT);
        header.append("domain").append(SPLIT);
        header.append("url").append(SPLIT);
        header.append("title").append(SPLIT);
        header.append("details").append(SPLIT);
        header.append("type").append(SPLIT);
        header.append("subrank_domain").append(SPLIT);
        header.append("subrank_url").append(SPLIT);
        header.append("subrank_title").append(SPLIT);
        header.append("subrank_rank").append(SPLIT);
        header.append("subrank_https").append(SPLIT);
        header.append("rank").append(SPLIT);
        header.append("tag_name").append(SPLIT);
        header.append("people_aslo_ask_flg").append(SPLIT);
        header.append("answerbox_flg").append(SPLIT);

        lines.add(header.toString());

        FileUtils.writeLines(outFile, lines, true);
    }

    public static String getDomainName(String url, String domainReverse) {

        if (StringUtils.containsIgnoreCase(url, "www.google.com")) {
            String[] domainArray = domainReverse.split("\\.");

            return domainArray[1] + "." + domainArray[0];
        } else {
            String domainName = CommonUtils.getDomainByUrl(url);
            if (domainName.contains("www.")) {
                domainName = domainName.replaceAll("www.", "");
            }
            return domainName;
        }

    }

    private String isHttp(String url) {
        String isHttp;
        if (StringUtils.startsWithIgnoreCase(url, "https")) {
            isHttp = "TRUE";
        } else {
            isHttp = "FALSE";
        }
        return isHttp;
    }


    private void formatJson(Integer domainId, String domainName, CLRankingDetailEntity clRankingDetailEntity, String tagNames, String typeName,
                            List<CLRankingDetailEntity> subRankInfoList, String locationName, String locale) throws Exception {

        String keywordName = clRankingDetailEntity.getKeywordName();
        if (extractJsonMap.get(keywordName) == null) {

            JsonExtractEntity jsonExtractEntity = new JsonExtractEntity();

            jsonExtractEntity.setDomain_id(domainId);
            jsonExtractEntity.setDomain_name(domainName);
            jsonExtractEntity.setKeyword(keywordName);
            jsonExtractEntity.setType(EXTRACT_TYPE);
            jsonExtractEntity.setDate(rankDate);
            jsonExtractEntity.setTopkey(clRankingDetailEntity.getKeywordName() + rankDate);
            jsonExtractEntity.setLocale(locale);
            jsonExtractEntity.setGeo(locationName);
            jsonExtractEntity.setBrowser(device);
            jsonExtractEntity.setAvg_search_vol(clRankingDetailEntity.getAvgSearchVolume());

            List<DetailEntity> detailEntityList = new ArrayList<>();

            //setDetailValue()
            detailEntityList.add(setDetailValue(clRankingDetailEntity, typeName, subRankInfoList));
            jsonExtractEntity.setUrls(detailEntityList);

            if (StringUtils.isNotBlank(tagNames)) {
                String[] tagNameArray = tagNames.split(",");
                List<Map<String, Object>> tagList = new ArrayList<>();
                int index = 1;
                for (String tagName : tagNameArray) {
                    Map<String, Object> tagMap = new HashMap<>();
                    tagMap.put("index", index);
                    tagMap.put("name", tagName);
                    tagList.add(tagMap);
                    index++;
                }
                jsonExtractEntity.setTags(tagList);
            }


            if (!clRankingDetailEntity.getPeopleAlsoAskFlg().equals("0")) {
                jsonExtractEntity.setPeople_aslo_ask_flg("TRUE");
            } else {
                jsonExtractEntity.setPeople_aslo_ask_flg("FALSE");
            }

            if (!clRankingDetailEntity.getAnswerBoxFlg().equals("0")) {
                jsonExtractEntity.setAnswerbox_flg("TRUE");
            } else {
                jsonExtractEntity.setAnswerbox_flg("FALSE");
            }

            extractJsonMap.put(keywordName, jsonExtractEntity);
        } else {

            JsonExtractEntity jsonExtractEntity = extractJsonMap.get(keywordName);
            List<DetailEntity> detailEntityList = jsonExtractEntity.getUrls();

            detailEntityList.add(setDetailValue(clRankingDetailEntity, typeName, subRankInfoList));
            jsonExtractEntity.setUrls(detailEntityList);

            extractJsonMap.put(keywordName, jsonExtractEntity);
        }

    }

    private DetailEntity setDetailValue(CLRankingDetailEntity clRankingDetailEntity, String typeName, List<CLRankingDetailEntity> subRankInfoList) throws Exception {

        DetailEntity detailEntity = new DetailEntity();

        detailEntity.setDomain(getDomainName(clRankingDetailEntity.getUrl(), clRankingDetailEntity.getDomainReverse()));
        if (StringUtils.containsIgnoreCase(clRankingDetailEntity.getUrl(), "www.google.com")) {
            detailEntity.setUrl("https://www.google.com/");
        } else {
            detailEntity.setUrl(clRankingDetailEntity.getUrl());
        }

        if (StringUtils.containsIgnoreCase(clRankingDetailEntity.getUrl(), "www.google.com")) {
            detailEntity.setHttps("TRUE");
        } else {
            detailEntity.setHttps(isHttp(clRankingDetailEntity.getUrl()));
        }

        detailEntity.setRank(clRankingDetailEntity.getTrueRank());

        detailEntity.setType(clRankingDetailEntity.getTypeName().toUpperCase());

        List<SubRankEntity> rankEntityList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(subRankInfoList)) {
            for (CLRankingDetailEntity subRankInfo : subRankInfoList) {

                Integer subRank = subRankInfo.getSubRank();
                Integer rankingType = subRankInfo.getRankingType();
                Integer urlType = subRankInfo.getUrlType();
                SubRankEntity subRankEntity = new SubRankEntity();
                /**
                 * https://www.wrike.com/open.htm?id=1154017014
                 * ranking_type = 1, url_type = 0, locallisting
                 * ranking_type = 2, url_type = 0, question_list
                 * ranking_type = 4, url_type = 15, product list
                 */
                if ((rankingType.intValue() == 1 && urlType.intValue() == 0)
                        || (rankingType.intValue() == 4 && urlType.intValue() == 15)) {
                    String title = subRankInfo.getDomainReverse();
                    subRankEntity.setDomain("");
                    subRankEntity.setUrl("");
                    subRankEntity.setRank(subRank);
                    subRankEntity.setTitle(title);
                } else if (rankingType.intValue() == 2 && urlType.intValue() == 0){//question_list
                    subRankEntity.setDomain(StringUtils.reverseDelimited(subRankInfo.getDomainReverse(),'.'));
                    subRankEntity.setUrl("");
                    subRankEntity.setRank(subRank);
                } else {
                    String subRankUrl = subRankInfo.getUrl();
                    String domainReverse = subRankInfo.getDomainReverse();
                    String subRankDomain = getDomainName(subRankUrl, domainReverse);

                    subRankEntity.setDomain(subRankDomain);
                    subRankEntity.setUrl(subRankUrl);
                    subRankEntity.setHttps(isHttp(subRankUrl));
                    subRankEntity.setRank(subRank);
                }

                rankEntityList.add(subRankEntity);
            }
        }

//        if (listingEntities != null && listingEntities.length > 0) {
//            for (LocalListingEntity listingEntity : listingEntities) {
//                String title = URLDecoder.decode(listingEntity.getTitle(), "utf-8");
//                int subRank = listingEntity.getRank();
//                System.out.println("****ll subrank title : " + title + ",keywordName: " + clRankingDetailEntity.getKeywordName());
//
//                SubRankEntity subRankEntity = new SubRankEntity();
//                subRankEntity.setDomain("");
//                subRankEntity.setUrl("");
//                subRankEntity.setRank(subRank);
//                subRankEntity.setTitle(title);
//
//                rankEntityList.add(subRankEntity);
//            }
//        }

//        System.out.println("========subRank size : " + rankEntityList.size() + ",keywordName: " + clRankingDetailEntity.getKeywordName());
        if (CollectionUtils.isNotEmpty(rankEntityList)) {
            detailEntity.setSub_rank(rankEntityList);
        }
        detailEntity.setTitle(clRankingDetailEntity.getLabel());
        detailEntity.setDetails(clRankingDetailEntity.getMeta());

        return detailEntity;
    }

    public static void uploadFileToGS(String uploadFileName) throws Exception {
        GSCredentials gsCredentials = new GSCredentials(
                ACCESS_KEY,
                SECRET_KEY);
        System.out.println(ACCESS_KEY);
        GoogleStorageService gsService = new GoogleStorageService(gsCredentials);

        // TODO
//        String uploadFileName = "e://abc.txt";

        try {
            long a = System.currentTimeMillis();

            File fileData = new File(uploadFileName);
            GSObject fileObject = new GSObject(fileData);

            StorageBucket sb = gsService.getBucket(bucketName);
            if (sb == null) {
                System.out.println(bucketName);

                gsService.createBucket(bucketName);
            }
            gsService.putObject(bucketName, fileObject);

            long b = System.currentTimeMillis();
            System.out.println("Upload to GoogleCloudStorage: " + (b - a) * 1.0 / 1000 + " s ");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void uploadToBigQuery(String fileName) throws Exception {

        GoogleCredential credential = new GoogleCredential.Builder().setTransport(TRANSPORT)
                .setJsonFactory(JSON_FACTORY)
                .setServiceAccountId(AccountId)
                .setServiceAccountScopes(SCOPE)
                .setServiceAccountPrivateKeyFromP12File(new File(PRIVATE_KEY_PATH)).build();

        bigquery = new Bigquery.Builder(TRANSPORT, JSON_FACTORY, credential)
                .setApplicationName("BigQuery-Service-Accounts/0.1").setHttpRequestInitializer(credential).build();

        uploadRankingToBigQuery(fileName);

    }

    private void uploadRankingToBigQuery(String fileName) throws Exception {
        long a = System.currentTimeMillis();
        TableSchema schema = getJsonRankTableSchema();
        // this is for CSV template. no need to merge for now
//		TableSchema schema = getCSVRankTableSchema();

        TableReference destTable = getDestTable();
        JobConfigurationLoad configLoad = getConfigLoad(destTable, schema, fileName);

        Job job = new Job();
        JobConfiguration config = new JobConfiguration();
        config.setLoad(configLoad);
        job.setConfiguration(config);

        System.out.println("data uploading");

        Bigquery.Jobs.Insert insert = bigquery.jobs().insert(projectId, job);
        insert.setProjectId(projectId);
        JobReference jobRef = insert.execute().getJobReference();
        System.out.println("data loaded");

        Job returnJob = checkQueryResults(projectId, jobRef);
        System.out.println("data verified");

        long b = System.currentTimeMillis();
        System.out.println("Uploading Data to BigQuery Used : " + (b - a) * 1.0 / (1000 * 60) + " mins.");
    }

    public static TableSchema getJsonRankTableSchema() {
        TableSchema schema = new TableSchema();

        List<TableFieldSchema> fields = new ArrayList<TableFieldSchema>();

        TableFieldSchema domainId = new TableFieldSchema();
        domainId.setName("domain_id");
        domainId.setType("INTEGER");
        domainId.setMode("REQUIRED");

        TableFieldSchema domainName = new TableFieldSchema();
        domainName.setName("domain_name");
        domainName.setType("STRING");
        domainName.setMode("REQUIRED");

        TableFieldSchema kName = new TableFieldSchema();
        kName.setName("keyword");
        kName.setType("STRING");
        kName.setMode("REQUIRED");

        TableFieldSchema type = new TableFieldSchema();
        type.setName("type");
        type.setType("STRING");
        type.setMode("REQUIRED");

        TableFieldSchema date = new TableFieldSchema();
        date.setName("date");
        date.setType("DATE");
        date.setMode("REQUIRED");

        TableFieldSchema topkey = new TableFieldSchema();
        topkey.setName("topkey");
        topkey.setType("STRING");
        topkey.setMode("NULLABLE");

        TableFieldSchema locale = new TableFieldSchema();
        locale.setName("locale");
        locale.setType("STRING");
        locale.setMode("NULLABLE");

        TableFieldSchema geo = new TableFieldSchema();
        geo.setName("geo");
        geo.setType("STRING");
        geo.setMode("NULLABLE");

        TableFieldSchema browser = new TableFieldSchema();
        browser.setName("browser");
        browser.setType("STRING");
        browser.setMode("NULLABLE");

        TableFieldSchema urls = new TableFieldSchema();
        urls.setName("urls");
        urls.setType("RECORD");
        urls.setMode("REPEATED");

        // // ////////////////////////////
        TableFieldSchema domain = new TableFieldSchema();
        domain.setName("domain");
        domain.setType("STRING");
        domain.setMode("NULLABLE");

        TableFieldSchema rank = new TableFieldSchema();
        rank.setName("rank");
        rank.setType("INTEGER");
        rank.setMode("NULLABLE");

        TableFieldSchema url = new TableFieldSchema();
        url.setName("url");
        url.setType("STRING");
        url.setMode("NULLABLE");

        TableFieldSchema title = new TableFieldSchema();
        title.setName("title");
        title.setType("STRING");
        title.setMode("NULLABLE");

        TableFieldSchema details = new TableFieldSchema();
        details.setName("details");
        details.setType("STRING");
        details.setMode("NULLABLE");

        TableFieldSchema https = new TableFieldSchema();
        https.setName("https");
        https.setType("BOOLEAN");
        https.setMode("NULLABLE");

        TableFieldSchema sub_rank = new TableFieldSchema();
        sub_rank.setName("sub_rank");
        sub_rank.setType("RECORD");
        sub_rank.setMode("REPEATED");

        List<TableFieldSchema> subRankFileds = new ArrayList<TableFieldSchema>();
        subRankFileds.add(domain);
        subRankFileds.add(url);
        subRankFileds.add(rank);
        subRankFileds.add(https);
        subRankFileds.add(title);
        sub_rank.setFields(subRankFileds);

        List<TableFieldSchema> childrenFileds = new ArrayList<TableFieldSchema>();
        childrenFileds.add(domain);
        childrenFileds.add(url);
        childrenFileds.add(https);
        childrenFileds.add(rank);
        childrenFileds.add(type);
        childrenFileds.add(sub_rank);
        childrenFileds.add(title);
        childrenFileds.add(details);

        // /////////////////////////
        urls.setFields(childrenFileds);

        TableFieldSchema people_aslo_ask_flg = new TableFieldSchema();
        people_aslo_ask_flg.setName("people_aslo_ask_flg");
        people_aslo_ask_flg.setType("BOOLEAN");
        people_aslo_ask_flg.setMode("NULLABLE");

        TableFieldSchema answerbox_flg = new TableFieldSchema();
        answerbox_flg.setName("answerbox_flg");
        answerbox_flg.setType("BOOLEAN");
        answerbox_flg.setMode("NULLABLE");

        TableFieldSchema avg_search_vol = new TableFieldSchema();
        avg_search_vol.setName("avg_search_vol");
        avg_search_vol.setType("FLOAT");
        avg_search_vol.setMode("NULLABLE");

        ///
        TableFieldSchema tags = new TableFieldSchema();
        tags.setName("tags");
        tags.setType("RECORD");
        tags.setMode("REPEATED");

        TableFieldSchema tagIndex = new TableFieldSchema();
        tagIndex.setName("index");
        tagIndex.setType("INTEGER");
        tagIndex.setMode("NULLABLE");

        TableFieldSchema tagName = new TableFieldSchema();
        tagName.setName("name");
        tagName.setType("STRING");
        tagName.setMode("NULLABLE");

        List<TableFieldSchema> tagsFileds = new ArrayList<TableFieldSchema>();
        tagsFileds.add(tagIndex);
        tagsFileds.add(tagName);

        tags.setFields(tagsFileds);

        fields.add(domainId);
        fields.add(domainName);
        fields.add(kName);
        fields.add(type);
        fields.add(date);
        fields.add(topkey);
        fields.add(locale);
        fields.add(geo);
        fields.add(browser);
        fields.add(urls);
        fields.add(people_aslo_ask_flg);
        fields.add(answerbox_flg);
        fields.add(avg_search_vol);
        fields.add(tags);

        schema.setFields(fields);

        return schema;
    }

    public JobConfigurationLoad getConfigLoad(TableReference destTable, TableSchema schema, String fileName) {
        JobConfigurationLoad configLoad = new JobConfigurationLoad();
        configLoad.setSchema(schema);
        configLoad.setDestinationTable(destTable);
        configLoad.setSourceFormat("NEWLINE_DELIMITED_JSON");
        // this is for CSV template. no need to merge for now
//		configLoad.setSourceFormat("CSV");


        List<String> uris = new ArrayList<String>();
        // TODO
        String exportFileName = "gs://" + bucketName + "/" + fileName;

        uris.add(exportFileName);
        configLoad.setSourceUris(uris);
        return configLoad;
    }

    public TableReference getDestTable() {
        TableReference destTable = new TableReference();
        destTable.setProjectId(projectId);
        destTable.setDatasetId(dataSetId);
        destTable.setTableId(tableId);

        return destTable;
    }

    private static Job checkQueryResults(String projectId, JobReference jobId)
            throws IOException, InterruptedException {
        long startTime = System.currentTimeMillis();
        long elapsedTime;

        while (true) {
            Job pollJob = bigquery.jobs().get(projectId, jobId.getJobId()).execute();
            elapsedTime = System.currentTimeMillis() - startTime;
            System.out.format("Job status (%dms) %s: %s\n", elapsedTime, jobId.getJobId(),
                    pollJob.getStatus().getState());
            if (pollJob.getStatus().getState().equals("DONE")) {
                if (pollJob.getStatus().getErrors() != null) {
                    System.out.println(pollJob.getStatus().getErrors().toString());
                    System.out.println(pollJob.getStatus().getErrorResult().toPrettyString());
                    System.out.println("STOP Processing");
                    throw new InterruptedException();
                }
                return pollJob;
            }
            Thread.sleep(1000);
        }
    }

    private class JsonExtractEntity {

        private Integer domain_id;
        private String domain_name;
        private String keyword;
        private String type;
        private String date;
        private String topkey;
        private String locale;
        private String geo;
        private String browser;
        private List<DetailEntity> urls;
        private List<Map<String, Object>> tags;
        private String people_aslo_ask_flg;
        private String answerbox_flg;
        private Long avg_search_vol;

        public Integer getDomain_id() {
            return domain_id;
        }

        public void setDomain_id(Integer domain_id) {
            this.domain_id = domain_id;
        }

        public String getDomain_name() {
            return domain_name;
        }

        public void setDomain_name(String domain_name) {
            this.domain_name = domain_name;
        }

        public String getKeyword() {
            return keyword;
        }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getTopkey() {
            return topkey;
        }

        public void setTopkey(String topkey) {
            this.topkey = topkey;
        }

        public String getLocale() {
            return locale;
        }

        public void setLocale(String locale) {
            this.locale = locale;
        }

        public String getGeo() {
            return geo;
        }

        public void setGeo(String geo) {
            this.geo = geo;
        }

        public String getBrowser() {
            return browser;
        }

        public void setBrowser(String browser) {
            this.browser = browser;
        }

        public List<DetailEntity> getUrls() {
            return urls;
        }

        public void setUrls(List<DetailEntity> urls) {
            this.urls = urls;
        }

        public List<Map<String, Object>> getTags() {
            return tags;
        }

        public void setTags(List<Map<String, Object>> tags) {
            this.tags = tags;
        }

        public String getPeople_aslo_ask_flg() {
            return people_aslo_ask_flg;
        }

        public void setPeople_aslo_ask_flg(String people_aslo_ask_flg) {
            this.people_aslo_ask_flg = people_aslo_ask_flg;
        }

        public String getAnswerbox_flg() {
            return answerbox_flg;
        }

        public void setAnswerbox_flg(String answerbox_flg) {
            this.answerbox_flg = answerbox_flg;
        }

        public Long getAvg_search_vol() {
            return avg_search_vol;
        }

        public void setAvg_search_vol(Long avg_search_vol) {
            this.avg_search_vol = avg_search_vol;
        }
    }

    private class DetailEntity {

        private String domain;
        private String url;
        private String https;
        private Integer rank;
        private String type;
        private List<SubRankEntity> sub_rank = null;
        private String title;
        private String details;

        public String getHttps() {
            return https;
        }

        public void setHttps(String https) {
            this.https = https;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDetails() {
            return details;
        }

        public void setDetails(String details) {
            this.details = details;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public List<SubRankEntity> getSub_rank() {
            return sub_rank;
        }

        public void setSub_rank(List<SubRankEntity> sub_rank) {
            this.sub_rank = sub_rank;
        }
    }

    private class SubRankEntity {

        private Integer rank;
        private String title;
        private String domain;
        private String url;
        private String https;

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getHttps() {
            return https;
        }

        public void setHttps(String https) {
            this.https = https;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }


}
