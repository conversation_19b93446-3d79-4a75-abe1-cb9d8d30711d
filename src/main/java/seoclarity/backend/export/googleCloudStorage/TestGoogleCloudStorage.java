package seoclarity.backend.export.googleCloudStorage;

import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.storage.*;
import lombok.extern.apachecommons.CommonsLog;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

@CommonsLog
public class TestGoogleCloudStorage {



    private static void process() {

        try {

//            String filePath = "D:\\Extract\\google\\seoclarity.txt";
            String filePath = "/home/<USER>/seoclarity.dev";
//            Storage storage = StorageOptions.newBuilder()
//                    .setCredentials(ServiceAccountCredentials.fromStream(new FileInputStream("D:\\Extract\\google\\rvu-seo-seoclarity-74a2a327064b.json"))).build().getService();
            Storage storage = StorageOptions.newBuilder()
                    .setCredentials(ServiceAccountCredentials.fromStream(new FileInputStream("/home/<USER>/seoclarity.net_seoclarity-343de40b58c4.json"))).build().getService();
//            Storage storage = StorageOptions.newBuilder().setProjectId("rvu-seo-seoclarity").build().getService();
            log.info("========start");
            long a = System.currentTimeMillis();

            String bucketName = "ranksense-com.appspot.com";
            String objectName = "seoclarity.dev";//file name

            //upload
            BlobId blobId = BlobId.of(bucketName, objectName);
            BlobInfo blobInfo = BlobInfo.newBuilder(blobId).build();
            storage.create(blobInfo, Files.readAllBytes(Paths.get(filePath)));

            //download
//            Blob blob = storage.get(BlobId.of(bucketName, objectName));
//            blob.downloadTo(Paths.get("/home/<USER>/seoclarity1.txt"));

            //list object
//            Bucket bucket = storage.get(bucketName);
//            Page<Blob> blobs = bucket.list();
//            for (Blob blob : blobs.iterateAll()) {
//                System.out.println(blob.getName());
//            }

            long b = System.currentTimeMillis();
            System.out.println("Upload to GoogleCloudStorage: " + (b - a) * 1.0 / 1000 + " s ");

        }catch (Exception e){
            e.printStackTrace();
        }


    }

    public static void downloadObject(
            String projectId, String bucketName, String objectName, String destFilePath) {
        // The ID of your GCP project
        // String projectId = "your-project-id";

        // The ID of your GCS bucket
        // String bucketName = "your-unique-bucket-name";

        // The ID of your GCS object
        // String objectName = "your-object-name";

        // The path to which the file should be downloaded
        // String destFilePath = "/local/path/to/file.txt";

        Storage storage = StorageOptions.newBuilder().setProjectId(projectId).build().getService();

        Blob blob = storage.get(BlobId.of(bucketName, objectName));
        blob.downloadTo(Paths.get(destFilePath));

        System.out.println(
                "Downloaded object "
                        + objectName
                        + " from bucket name "
                        + bucketName
                        + " to "
                        + destFilePath);
    }


    public static void uploadObject(
            String projectId, String bucketName, String objectName, String filePath) throws IOException {
        // The ID of your GCP project
        // String projectId = "your-project-id";

        // The ID of your GCS bucket
        // String bucketName = "your-unique-bucket-name";

        // The ID of your GCS object
        // String objectName = "your-object-name";

        // The path to your file to upload
        // String filePath = "path/to/your/file"

        Storage storage = StorageOptions.newBuilder().setProjectId(projectId).build().getService();
        BlobId blobId = BlobId.of(bucketName, objectName);
        BlobInfo blobInfo = BlobInfo.newBuilder(blobId).build();
        storage.create(blobInfo, Files.readAllBytes(Paths.get(filePath)));

        System.out.println(
                "File " + filePath + " uploaded to bucket " + bucketName + " as " + objectName);
    }


    public static void main(String[] args) {
        process();
    }


}
