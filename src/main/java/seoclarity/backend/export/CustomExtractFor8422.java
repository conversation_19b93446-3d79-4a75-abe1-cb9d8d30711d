package seoclarity.backend.export;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.bot.Cdb21BotDetailDao;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=1234271203
 */
@CommonsLog
public class CustomExtractFor8422 {

    private static final String SPLIT = "\t";
    private static final int QUERY_TRY_COUNT = 10;
    private static String LOC = "/home/<USER>/";
    private static String startDate;
    private static String endDate;
    private static String FIRST_SEEN_DATE = "2024-02-16";
    private Cdb21BotDetailDao cdb21BotDetailDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private CommonParamDAO commonParamDAO;

    File outFile;

    public static final List<String> CONTENT_TYPE_LIST = Arrays.asList(
//            "T:ListViewPlus, Control",
//            "T:ListViewPlus, Test",
//            "T:ListViewPlus-US, Control",
//            "T:ListViewPlus-US, Test"
            "T3.3.01: Prefix Emoji (Control)",
            "T3.3.01: Prefix Emoji (Test)"
    );

    public static final List<String> ENGINE_SOURCE_LIST = Arrays.asList(
            "google",
            "bing"
    );

    public CustomExtractFor8422() {
        cdb21BotDetailDao = SpringBeanFactory.getBean("cdb21BotDetailDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
    }

    private void processDomainByDate(int domainId, String processingDate, String source, String contentType) {

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(domainId);
        if (ownDomainEntity == null) {
            System.out.println("=== domain not exist , exit !!");
            return;
        }

        int retryCount = 0;
        while (true) {
            try {
                System.out.println("********************** for domain " + domainId + " *******************");
                String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
                process(domainId, processingDate, source, contentType);

                break;
            } catch (Exception e) {
                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
                    String message = "Rover extract " + domainId + " " + processingDate + " Failed !!! ";
//                    sendMailReport("Failed !!!! ", message);
                    break;
                }
                e.printStackTrace();
                System.out.println("====domain error :" + domainId + ", sleep 20s ");
                try {
                    Thread.sleep(1000 * 20);
                } catch (Exception ex) {

                }
                retryCount++;
            }
        }

    }

    private void process(int domainId, String logDate, String processSource, String contentType) throws Exception {
        System.out.println("========start to extract domain : " + domainId + ",logDate: " + logDate);

        try {

            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(domainId);
            if (ownDomainEntity == null) {
                System.out.println("=== domain not exist , exit !!");
                return;
            }

            for (String source : ENGINE_SOURCE_LIST) {
                if (StringUtils.isNotBlank(processSource) && !processSource.equalsIgnoreCase(source)) {
                    log.info("=====skipSource:" + source + ",processSource:" + processSource);
                    continue;
                }
                Map<String, String> resultMap = new HashMap<>();
                List<Integer> groupIdList = new ArrayList<>();
                if (source.equalsIgnoreCase("google")) {
                    groupIdList.add(1);//google desktop
                    groupIdList.add(2);//google mobile
//                    groupIdList.add(12);//google d + m
                } else if (source.equalsIgnoreCase("bing")) {
                    groupIdList.add(3);
                }

                List<String> extractLines = new ArrayList<String>();
                if (StringUtils.isBlank(contentType)) {
                    for (String processContentType : CONTENT_TYPE_LIST) {
                        CommonParamEntity commonParamEntity = commonParamDAO.getDomainRegExList(domainId, processContentType);
                        if (commonParamEntity == null) {
                            log.info("======processContentType not exist" + processContentType);
                            continue;
                        }

                        JSONObject paramJson = JSONObject.parseObject(commonParamEntity.getParamJson());
                        String regEx = paramJson.get("value").toString();

                        Integer totalTraffic = 0;
                        List<Map<String, Object>> urlTrafficList = cdb21BotDetailDao.getMinDateByContentType(domainId, regEx, source, logDate, groupIdList, FIRST_SEEN_DATE);
                        for (Map<String, Object> urlMap : urlTrafficList) {
                            String url = urlMap.get("url").toString();
                            if (StringUtils.isBlank(url)) {
                                continue;
                            }
                            Date firstSeenDate = FormatUtils.toDate(urlMap.get("firstSeenDate").toString(), "yyyy-MM-dd");
                            Date logDateDateTime = FormatUtils.toDate(logDate, "yyyy-MM-dd");
                            if (logDateDateTime.getTime() < firstSeenDate.getTime()) {
                                log.info("===skiplogDate:" + logDate);
                                continue;
                            }
                            totalTraffic += Integer.parseInt(urlMap.get("traffic").toString());
                        }

                        if (processContentType.equalsIgnoreCase("T3.3.01: Prefix Emoji (Control)")) {
                            resultMap.put("traffic1", totalTraffic.toString());
                        } else if (processContentType.equalsIgnoreCase("T3.3.01: Prefix Emoji (Test)")) {
                            resultMap.put("traffic2", totalTraffic.toString());
                        }
                    }
                    extractLines.add(appendData(logDate, source, resultMap));

                } else {
                    CommonParamEntity commonParamEntity = commonParamDAO.getDomainRegExList(domainId, contentType);
                    if (commonParamEntity == null) {
                        log.info("======contentType not exist" + contentType);
                        continue;
                    }

                    JSONObject paramJson = JSONObject.parseObject(commonParamEntity.getParamJson());
                    String regEx = paramJson.get("value").toString();
                    List<Map<String, Object>> urlTrafficList = cdb21BotDetailDao.getMinDateByContentType(domainId, regEx, source, logDate, groupIdList, FIRST_SEEN_DATE);
                    for (Map<String, Object> urlMap : urlTrafficList) {
                        String url = urlMap.get("url").toString();
                        if (StringUtils.isBlank(url)) {
                            continue;
                        }
                        Date firstSeenDate = FormatUtils.toDate(urlMap.get("firstSeenDate").toString(), "yyyy-MM-dd");
                        Date logDateDateTime = FormatUtils.toDate(logDate, "yyyy-MM-dd");
                        if (logDateDateTime.getTime() < firstSeenDate.getTime()) {
                            log.info("===skiplogDate:" + logDate);
                            continue;
                        }
                        extractLines.add(appendDataUrlLevel(urlMap.get("url").toString(), logDate, urlMap));
                    }
                }

                FileUtils.writeLines(outFile, extractLines, true);
            }


            //send to ftp
//            FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, zipFileName, LOC + 7605 + File.separator +"dailyRank",0 , 3);
//            String message = "";
//            message = "Rover extract " + domainId + "  " + device + " " + rankDate + " Success";
//            sendMailReport("Success !!!! ", message);

        } catch (Exception e) {
//            e.printStackTrace();
            throw e;
        }

    }


    public static void addHeadersForExactFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
//        header.append("Source").append(SPLIT);
//        header.append("T:ListViewPlus-US, Control").append(SPLIT);
//        header.append("T:ListViewPlus-US, Test");
        header.append("T3.3.01: Prefix Emoji (Control)").append(SPLIT);
        header.append("T3.3.01: Prefix Emoji (Test)");
        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public static void addHeadersForExactUrlLevelFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
//        header.append("Date").append(SPLIT);
        header.append("Url").append(SPLIT);
        header.append(" CrawlDate").append(SPLIT);
        header.append("VisitDate").append(SPLIT);
        header.append("Visits");
        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public static void addHeadersForExactFileV2(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
//        header.append("Source").append(SPLIT);
        header.append("ContentType").append(SPLIT);
        header.append("Url").append(SPLIT);
        header.append("Traffic");
//        header.append("T:ListViewPlus-State, Control").append(SPLIT);
//        header.append("T:ListViewPlus-State, Test");
        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public static String appendData(String logDate, String engineSource, Map<String, String> resultMap) {
        StringBuffer line = new StringBuffer();
        line.append(logDate).append(SPLIT);
//        line.append(engineSource).append(SPLIT);
        line.append(resultMap.get("traffic1") == null ? "0" : resultMap.get("traffic1")).append(SPLIT);
        line.append(resultMap.get("traffic2") == null ? "0" : resultMap.get("traffic2"));
//        line.append(resultMap.get("traffic3") == null ? "0" : resultMap.get("traffic3")).append(SPLIT);
//        line.append(resultMap.get("traffic4") == null ? "0" : resultMap.get("traffic4"));
        return line.toString();
    }

    public static String appendDataV2(String logDate, String engineSource, String contentType, Map<String, Object> resultMap) {
        StringBuffer line = new StringBuffer();
        line.append(logDate).append(SPLIT);
//        line.append(engineSource).append(SPLIT);
        line.append(contentType).append(SPLIT);
        line.append(resultMap.get("url").toString()).append(SPLIT);
        line.append(resultMap.get("traffic") == null ? "0" : resultMap.get("traffic").toString());
        return line.toString();
    }

    public static String appendDataUrlLevel(String url, String logDate, Map<String, Object> urlMap) {
        StringBuffer line = new StringBuffer();
        line.append(url).append(SPLIT);
        line.append(urlMap.get("firstSeenDate").toString()).append(SPLIT);
        line.append(logDate).append(SPLIT);
        line.append(urlMap.get("traffic").toString());
        return line.toString();
    }

    private void processDomain(int domainId, String source) {

        Date sDate = FormatUtils.toDate(startDate, FormatUtils.DATE_PATTERN_2);
        Date eDate = FormatUtils.toDate(endDate, FormatUtils.DATE_PATTERN_2);

        String fileName = source + "_PrefixEmoji_" + LocalDate.now() + ".csv";
        String filePath = LOC + domainId + File.separator + fileName;
        outFile = new File(filePath);
        if (outFile.exists()) {
            outFile.delete();
        }

        try {
            addHeadersForExactFile(outFile);
        } catch (Exception e) {
            e.printStackTrace();
        }

        while (sDate.compareTo(eDate) <= 0) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(sDate);
//            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) {//run for each Friday
            processDomainByDate(domainId, FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2), source, null);
//            }
            sDate = DateUtils.addDays(sDate, 1);
        }

    }

    private void processDomainUrlLevel(int domainId, String source, String contentType) {

        Date sDate = FormatUtils.toDate(startDate, FormatUtils.DATE_PATTERN_2);
        Date eDate = FormatUtils.toDate(endDate, FormatUtils.DATE_PATTERN_2);

        String fileName = source + "_" + contentType + "_urlLevel_" + LocalDate.now() + ".csv";
        String filePath = LOC + domainId + File.separator + fileName;
        outFile = new File(filePath);
        if (outFile.exists()) {
            outFile.delete();
        }

        try {
            addHeadersForExactUrlLevelFile(outFile);
        } catch (Exception e) {
            e.printStackTrace();
        }

        while (sDate.compareTo(eDate) <= 0) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(sDate);
//            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) {//run for each Friday
            processDomainByDate(domainId, FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2), source, contentType);
//            }
            sDate = DateUtils.addDays(sDate, 1);
        }

    }

    public static void main(String[] args) {
        String[] domainIds = null;

        if (args != null && args.length > 0) {
            if (args[0].contains(",")) {
                domainIds = args[0].split(",");
            } else {
                domainIds = new String[]{args[0]};
            }
        } else {
            System.out.println("===param error!!");
            return;
        }

        if (args != null && args.length > 2 && !args[1].equalsIgnoreCase("null") && !args[2].equalsIgnoreCase("null")) {
            startDate = args[1];
            endDate = args[2];
        } else {
            Date sTime = FormatUtils.getYesterday(true);
            startDate = FormatUtils.formatDate(sTime, FormatUtils.DATE_PATTERN_2);
            endDate = FormatUtils.formatDate(sTime, FormatUtils.DATE_PATTERN_2);
        }

        System.out.println("=====domainIds: " + domainIds + ",startDate: " + startDate + ",endDate: " + endDate);

        CustomExtractFor8422 customExtractFor8422 = new CustomExtractFor8422();

        for (String oid : domainIds) {
            customExtractFor8422.processDomain(Integer.parseInt(oid), args[3] == null ? null : args[3]);
//            customExtractFor8422.processDomainUrlLevel(Integer.parseInt(oid), args[3] == null ? null : "google", "T3.3.01: Prefix Emoji (Control)");
//            customExtractFor8422.processDomainUrlLevel(Integer.parseInt(oid), args[3] == null ? null : "google", "T3.3.01: Prefix Emoji (Test)");
//            customExtractFor8422.processDomainUrlLevel(Integer.parseInt(oid), args[3] == null ? null : "bing", "T3.3.01: Prefix Emoji (Control)");
//            customExtractFor8422.processDomainUrlLevel(Integer.parseInt(oid), args[3] == null ? null : "bing", "T3.3.01: Prefix Emoji (Test)");
//            customExtractFor8422.processDomain(8422, "google");
//            customExtractFor8422.processDomain(8422, "bing");
        }

    }

}
