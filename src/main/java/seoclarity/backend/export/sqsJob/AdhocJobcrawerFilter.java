package seoclarity.backend.export.sqsJob;

import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONObject;
import seoclarity.backend.entity.actonia.adhoc.AdHocJobCrawlerEntity;

import java.io.*;
import java.util.HashSet;
import java.util.Set;

public class AdhocJobcrawerFilter {
    private static String newFolder = "/home/<USER>/source/hao/master/clarity-backend-scripts/files/adhoc/8758Test/new";
    private static String oldFolder = "/home/<USER>/source/hao/master/clarity-backend-scripts/files/adhoc/8758Test/old";
    private static String LOCAL_PATH = "files/adhoc/8758Test/";

    Set<String> oldErrKWSet = new HashSet<>();
    public static Set<String> newDataWithoutRankKW= new HashSet<>();
    public static Set<String> newDataWithRankKW= new HashSet<>();

    public static void main(String[] args) {
        AdhocJobcrawerFilter in = new AdhocJobcrawerFilter();
        in.process();
    }

    private void process() {
        for (File file : new File(oldFolder).listFiles()) {

            initialize();

            String fileName = file.getName();
            String[] fileNameArr = fileName.substring(0, fileName.lastIndexOf('.')).split("_");
            String date = fileNameArr[fileNameArr.length - 1];
            getNotRankKW(file.getAbsolutePath());
            for (File newfile : new File(newFolder).listFiles()) {
                if (newfile.getName().contains(date)){
                    System.out.println("===###newfile : " + newfile.getName());
                    checkKW(newfile.getAbsolutePath());
                }
            }
            // 文件输出
            File errkwFile = new File(LOCAL_PATH + "errKW_8758_"+date+".txt");
            writeKWToFile(errkwFile,oldErrKWSet);

            // 文件输出
            File newDataWithoutRankKWFile = new File(LOCAL_PATH + "newDataWithoutRankKW_8758_"+date+".txt");
            writeKWToFile(newDataWithoutRankKWFile,newDataWithoutRankKW);

            // 文件输出
            File newDataWithRankKWFile = new File(LOCAL_PATH + "newDataWithRankKW_8758_"+date+".txt");
            writeKWToFile(newDataWithRankKWFile,newDataWithRankKW);

        }

    }

    private void initialize() {
        oldErrKWSet = new HashSet<>();
        newDataWithoutRankKW = new HashSet<>();
        newDataWithRankKW = new HashSet<>();

    }

    private void writeKWToFile(File errkwFile, Set<String> fileSet) {
        try (FileWriter fileWriter = new FileWriter(errkwFile)) {
            for (String kw : fileSet) {
                fileWriter.write(kw + "\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void checkKW(String absolutePath) {
        File fi = new File(absolutePath);
        try (BufferedReader br = new BufferedReader(new FileReader(fi))) {
            String line;
            while ((line = br.readLine()) != null) {
                JSONObject json = new JSONObject(line);
                String kw = json.get("keyword").toString();

                if (oldErrKWSet.contains(kw)){
                    if (!json.has("jobEntityVOs") ) {
                        newDataWithoutRankKW.add(kw);
                    }else {
                        JSONArray jobEntityVOsArr = json.getJSONArray("jobEntityVOs");
                        if (jobEntityVOsArr.length()<=0){
                            newDataWithoutRankKW.add(kw);
                        }else {
                            newDataWithRankKW.add(kw);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getNotRankKW(String file) {
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = br.readLine()) != null) {
                JSONObject json = new JSONObject(line);
                String kw = json.get("keyword").toString();
                if (!json.has("jobEntityVOs") ) {
                    oldErrKWSet.add(kw);
                }else {
                    JSONArray jobEntityVOsArr = json.getJSONArray("jobEntityVOs");
                    if (jobEntityVOsArr.length()<=0){
                        oldErrKWSet.add(kw);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
