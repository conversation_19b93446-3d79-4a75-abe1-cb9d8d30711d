package seoclarity.backend.export.sqsJob;

import org.apache.commons.lang.StringUtils;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONObject;
import seoclarity.backend.entity.actonia.adhoc.AdHocJobCrawlerEntity;

import java.io.*;
import java.util.*;

public class AdhocJobcrawerMergeFile {
    private static String newFolder = "/home/<USER>/source/hao/master/clarity-backend-scripts/files/adhoc/8756Test/new";
    private static String oldFolder = "/home/<USER>/source/hao/master/clarity-backend-scripts/files/adhoc/8756Test/old";
    private static String LOCAL_PATH = "files/adhoc/8756Test/";

    Set<String> oldErrKWSet = new HashSet<>();
    public static Set<String> newDataWithoutRankKW= new HashSet<>();
    public static Set<String> newDataWithRankKW= new HashSet<>();

    public static Map<String,String> notRankKWMap = new HashMap<>();

    public static void main(String[] args) {
        AdhocJobcrawerMergeFile in = new AdhocJobcrawerMergeFile();
        in.process();
    }

    private void process() {
        for (File file : new File(oldFolder).listFiles()) {

            initialize();

            String fileName = file.getName();
            String[] fileNameArr = fileName.substring(0, fileName.lastIndexOf('.')).split("_");
            String date = fileNameArr[fileNameArr.length - 1];
            List<String> oldKWList = getListWithoutNotRankKW(file.getAbsolutePath());

            for (File newfile : new File(newFolder).listFiles()) {
                if (newfile.getName().contains(date)){
                    System.out.println("===###newfile : " + newfile.getName());
                    checkKW(newfile.getAbsolutePath(),oldKWList);
                }
            }

            // 文件输出
            File errkwFile = new File(LOCAL_PATH + "merge_8756_"+date+".txt");
            writeKWToFile(errkwFile,oldKWList);

        }

    }

    private void initialize() {
        oldErrKWSet = new HashSet<>();
        newDataWithoutRankKW = new HashSet<>();
        newDataWithRankKW = new HashSet<>();
        notRankKWMap = new HashMap<>();

    }

    private void writeKWToFile(File errkwFile, List<String> fileSet) {
        try (FileWriter fileWriter = new FileWriter(errkwFile)) {
            for (String line : fileSet) {
                fileWriter.write(line + "\n");
            }
            if (notRankKWMap.size()>0){
                for (String s : notRankKWMap.keySet()){
                    fileWriter.write(notRankKWMap.get(s) + "\n");
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void checkKW(String absolutePath,List<String> oldList) {
        File fi = new File(absolutePath);

        try (BufferedReader br = new BufferedReader(new FileReader(fi))) {
            String line;
            while ((line = br.readLine()) != null) {
                JSONObject json = new JSONObject(line);
                String kw = json.get("keyword").toString();

                if (oldErrKWSet.contains(kw)){
                    oldList.add(line);
                    notRankKWMap.remove(kw);
                }else {
//                    oldList.add(notRankKWMap.get(kw));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<String> getListWithoutNotRankKW(String file) {
        List<String> adHocJobCrawlerEntityList = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = br.readLine()) != null) {
                JSONObject json = new JSONObject(line);
                String kw = json.get("keyword").toString();
                if (!json.has("jobEntityVOs") ) {
                    oldErrKWSet.add(kw);
                    notRankKWMap.put(kw,line);
                }else {
                    JSONArray jobEntityVOsArr = json.getJSONArray("jobEntityVOs");
                    if (jobEntityVOsArr.length()<=0){
                        oldErrKWSet.add(kw);
                        notRankKWMap.put(kw,line);
                    }else {
                        adHocJobCrawlerEntityList.add(line);
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return adHocJobCrawlerEntityList;
    }

}
