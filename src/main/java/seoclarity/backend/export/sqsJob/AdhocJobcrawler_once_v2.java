package seoclarity.backend.export.sqsJob;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.UserDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankProjectEntityDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordAdwordsEntityDAO;
import seoclarity.backend.entity.AgencyInfoEntity;
import seoclarity.backend.entity.actonia.adhoc.AdHocJobCrawlerEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordAdwordsEntity;
import seoclarity.backend.service.AgencyInfoManager;
import seoclarity.backend.utils.*;

import java.io.*;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.*;


/**
 *
 */
public class AdhocJobcrawler_once_v2 {

    private static Map<String, String> domainFileMap = new HashMap<>();
    private static Map<Integer, Map<String, String>> domainDateProjectMap = new HashMap<>();

    static {
        domainFileMap.put("8758", "fixrank_job_adhoc_mobile_commoncrawl_keywordRank_14_15");
        domainFileMap.put("8762", "fixrank_job_adhoc_mobile_commoncrawl_keywordRank_24_25");
        domainFileMap.put("8756", "fixrank_job_adhoc_mobile_commoncrawl_keywordRank_3_3");
        domainFileMap.put("8761", "fixrank_job_adhoc_mobile_commoncrawl_keywordRank_6_8");


    }

    private static String queueName = "adhocJobTest";
    private static String orgQueueName = "orgAdhocJobTest";
    private static String FTP_LOC = "/home/<USER>/";
    private static String FOLDER = "/adHocDownload/";
    private static String LOCAL_PATH = "files/adhoc/";
    private static final Gson gson = new Gson();

    private static String LINE_BREAK = "<BR>";
    private static String countryCd = "";
    private static String query_big_or_normal_table = ""; // project 查询哪个表
    private static String dis_keyword_summary_annual_big_v2 = "dis_keyword_summary_annual_big_v2";
    private static String dis_keyword_summary_annual_normal_v2 = "dis_keyword_summary_annual_normal_v2";
    //https://www.wrike.com/open.htm?id=1193831742
    private static Integer big_v2_hash_mod = 2000; // big 表分成2000层
    private static Integer normal_v2_hash_mod = 500; // mormal 表分成500 层
    private static Integer max_query_size = 200; // 最多200 关键字查询一次

    //    private static String COUNTRY = "US";
    private static Set<String> keywordSet = new HashSet<>();
    //所有的 kw sv 数据
    static Map<String, Integer> allKwSearchVolumMap = new HashMap<>();
    // 所有 kw truedemand 数据
    static Map<String, Integer> allKwTrueDemandMap = new HashMap<>();
    private static Map<Integer, List<String>> keywordHashModMap = new HashMap<>(); //表根据keywordHash%2000分成不同片区存放keywords ， big:2000片 ， normal :500片


    //statistical data fot Leo
    private Integer keywordCnt = 0;
    private Integer jobEntityVOsCnt = 0;
    private Integer jobTitleCnt = 0;
    private Integer jobCompanyCnt = 0;
    private Integer jobLocationCnt = 0;
    private Integer jobSourceCnt = 0;
    private Integer jobPostTimeCnt = 0;
    private Integer jobTypeCnt = 0;
    private Integer jobPrimaryJobLinkCnt = 0;
    private Integer applyOnListCnt = 0;
    private Integer jsCompanyCnt = 0;
    private Integer companyIdCnt = 0;
    private Integer companyCityCnt = 0;
    private Integer companyStateCnt = 0;
    private Integer minSalaryCnt = 0;
    private Integer maxSalaryCnt = 0;
    private Integer salaryUnitCnt = 0;
    private Integer currencyCodeCnt = 0;
    private Integer ratingArrayCnt = 0;
    private Integer ratingArray_linkCnt = 0;
    private Integer ratingArray_sourceCnt = 0;
    private Integer ratingArray_starRatingCnt = 0;
    private Integer ratingArray_reviewCountCnt = 0;
    private Integer salaryArrayCnt = 0;
    private Integer salaryArray_urlCnt = 0;
    private Integer salaryArray_titleCnt = 0;
    private Integer salaryArray_metaCnt = 0;
    private Integer salaryArray_salaryCnt = 0;
    private Integer salaryArray_salaryUnitCnt = 0;
    private Integer salaryArray_sourceCnt = 0;


    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
    private AgencyInfoManager agencyInfoManager;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    private UserDAO userDAO;
    private SeoClarityKeywordAdwordsEntityDAO seoClarityKeywordAdwordsEntityDAO;
    private GscClickSteamDAO gscClickSteamDAO;

    public AdhocJobcrawler_once_v2() {
        autoAdhocRankProjectEntityDAO = SpringBeanFactory.getBean("autoAdhocRankProjectEntityDAO");
        agencyInfoManager = SpringBeanFactory.getBean("agencyInfoManager");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        userDAO = SpringBeanFactory.getBean("userDAO");
        seoClarityKeywordAdwordsEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordAdwordsEntityDAO");
        gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
    }

    public static void main(String[] args) {
        AdhocJobcrawler_once_v2 in = new AdhocJobcrawler_once_v2();
        in.process();
    }

    private void process() {

        getDomainDateProjectMap();

        String baseFolder = "/home/<USER>/source/hao/indeed8711";
        long start = System.currentTimeMillis();
//        for (String domain : domainFileMap.keySet()) {
//            String folder = domainFileMap.get(domain);
            initializer();
//            System.out.println("===###rundomain : " + domain + " , folder : " + folder);
            // 将一个domain 所有数据 放进一个entity
            List<AdHocJobCrawlerEntity> domainEntity = getDomainEntity(baseFolder , 8711);

            // 根绝 queryDate 分成几个文件
            try {
                saveToFile(domainEntity, 8711);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
//        }
        long end = System.currentTimeMillis();
        System.out.println("process time: " + (end - start) / 1000);
    }

    private void initializer() {
        keywordHashModMap = new HashMap<>();
        keywordSet = new HashSet<>();
        allKwSearchVolumMap = new HashMap<>();
        allKwTrueDemandMap = new HashMap<>();
    }

    private void getDomainDateProjectMap() {

        Map<String, String> dateProjectMap8756 = new HashMap<>();
        dateProjectMap8756.put("20240705", "2547");
        dateProjectMap8756.put("20240712", "2548");
        dateProjectMap8756.put("20240719", "2549");
        dateProjectMap8756.put("20240726", "2550");
        domainDateProjectMap.put(8756, dateProjectMap8756);

        Map<String, String> dateProjectMap8758 = new HashMap<>();
        dateProjectMap8758.put("20240705", "2600");
        dateProjectMap8758.put("20240712", "2601");
        dateProjectMap8758.put("20240719", "2602");
        dateProjectMap8758.put("20240726", "2603");
        domainDateProjectMap.put(8758, dateProjectMap8758);

        Map<String, String> dateProjectMap8761 = new HashMap<>();
        dateProjectMap8761.put("20240705", "2653");
        dateProjectMap8761.put("20240712", "2654");
        dateProjectMap8761.put("20240719", "2655");
        dateProjectMap8761.put("20240726", "2656");
        domainDateProjectMap.put(8761, dateProjectMap8761);

        Map<String, String> dateProjectMap8762 = new HashMap<>();
        dateProjectMap8762.put("20240705", "2706");
        dateProjectMap8762.put("20240712", "2707");
        dateProjectMap8762.put("20240719", "2708");
        dateProjectMap8762.put("20240726", "2709");
        domainDateProjectMap.put(8762, dateProjectMap8762);


        Map<String, String> dateProjectMap8711 = new HashMap<>();
//        dateProjectMap8762.put("20240705", "2706");
        dateProjectMap8711.put("20240712", "2848");
        dateProjectMap8711.put("20240719", "2849");
        dateProjectMap8711.put("20240726", "2850");
        domainDateProjectMap.put(8711, dateProjectMap8711);



    }

    private void saveToFile(List<AdHocJobCrawlerEntity> domainEntity, int domainId) throws Exception {

        Integer engineId = 1;
        Integer languageId = 1;
        Integer cityId = 0;

        if (domainId == 8758) {
            engineId = 14;
            languageId = 15;
        } else if (domainId == 8762) {
            engineId = 24;
            languageId = 25;
        } else if (domainId == 8756) {
            engineId = 3;
            languageId = 3;
        } else if (domainId == 8761) {
            engineId = 6;
            languageId = 8;
        }

        Map<String, List<AdHocJobCrawlerEntity>> entitiesByDate = new HashMap<>();
        for (AdHocJobCrawlerEntity entity : domainEntity) {
            String date = entity.getQueryDate();
            if (!entitiesByDate.containsKey(date)) {
                entitiesByDate.put(date, new ArrayList<>());
            }
            entitiesByDate.get(date).add(entity);
        }


        // 根据keyword hash 进行分片
        queryByBigOrNormalTable(engineId, languageId, keywordSet);

        for (Integer mod : keywordHashModMap.keySet()) {
            List<String> keywordListOfMod = keywordHashModMap.get(mod);
            if (keywordListOfMod.size() <= 0) {
                continue;
            }
            if (keywordListOfMod.size() > max_query_size) {
                // mod 中 keyword 过多， 分成200 个一组查询
                List<List<String>> keywordsSpit200 = CollectionSplitUtils.splitCollectionBySize(keywordListOfMod, max_query_size);
                for (List<String> list : keywordsSpit200) {
                    getKwSearchVolumeMap(99, languageId, cityId, list, allKwSearchVolumMap);
                    getTrueDemandMap(mod, list, allKwTrueDemandMap);
                }
            } else {
                getKwSearchVolumeMap(99, languageId, cityId, keywordListOfMod, allKwSearchVolumMap);
                getTrueDemandMap(mod, keywordListOfMod, allKwTrueDemandMap);

            }

        }

        // 遍历 map 并将实体保存到文件
        for (Map.Entry<String, List<AdHocJobCrawlerEntity>> entry : entitiesByDate.entrySet()) {
            saveEntitiesToFile(entry.getValue(), entry.getKey(), domainId);
        }
    }

    private void saveEntitiesToFile(List<AdHocJobCrawlerEntity> entities, String date, Integer domainId) {
        cleancount();
        String projectId = domainDateProjectMap.get(domainId).get(date);
        String fileName = queueName + "_" + domainId + "_" + projectId + "_" + date + ".txt";
        String path = LOCAL_PATH + fileName;
        File file = new File(path);
        System.out.println("===###文件名：" + domainId + " : " + path);
        try (FileWriter fileWriter = new FileWriter(file)) {
            for (AdHocJobCrawlerEntity entity : entities) {
                String keyword = entity.getKeyword();
                Integer searchVolume = allKwSearchVolumMap.get(keyword);
                Integer trueDemand = allKwTrueDemandMap.get(keyword);
                entity.setSearchVolume(searchVolume);
                entity.setTrueDemand(trueDemand);

                checkCount(entity);

                String jsonString = gson.toJson(entity);
                fileWriter.write(jsonString + "\n");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        try {
            sendEmailToLeo(null, null, path, fileName,
                    domainId, fileName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void checkCount(AdHocJobCrawlerEntity entity) {
        if (null != entity.getKeyword()) {
            keywordCnt++;
        }
        if (null != entity.getJobEntityVOs()) {
            jobEntityVOsCnt++;
            for (AdHocJobCrawlerEntity.JobEntityVO jobEntityVO : entity.getJobEntityVOs()) {
                if (null != jobEntityVO.getJobTitle()) {
                    jobTitleCnt++;
                }
                if (null != jobEntityVO.getJobCompany()){
                    jobCompanyCnt++;
                }
                if (null != jobEntityVO.getJobLocation()){
                    jobLocationCnt++;
                }
                if (null != jobEntityVO.getJobSource()){
                    jobSourceCnt++;
                }
                if (null != jobEntityVO.getJobPostTime()){
                    jobPostTimeCnt++;
                }
                if (null != jobEntityVO.getJobType()){
                    jobTypeCnt++;
                }
                if (null != jobEntityVO.getJobPrimaryJobLink()){
                    jobPrimaryJobLinkCnt++;
                }
                if (null != jobEntityVO.getCompanyId()){
                    companyIdCnt++;
                }
                if (null != jobEntityVO.getApplyOnList()){
                    applyOnListCnt++;
                }
                if (null != jobEntityVO.getJsCompany()){
                    jsCompanyCnt++;
                }
                if (null != jobEntityVO.getCompanyCity()){
                    companyCityCnt++;
                }
                if (null != jobEntityVO.getCompanyState()){
                    companyStateCnt++;
                }
                if (null != jobEntityVO.getMinSalary()){
                    minSalaryCnt++;
                }
                if (null != jobEntityVO.getMaxSalary()){
                    maxSalaryCnt++;
                }
                if (null != jobEntityVO.getCurrencyCode()){
                    currencyCodeCnt++;
                }
                if (null != jobEntityVO.getSalaryUnit()){
                    salaryUnitCnt++;
                }
                if (null != jobEntityVO.getSalaryArray()){
                    salaryArrayCnt = salaryArrayCnt + jobEntityVO.getSalaryArray().size();
                    for (AdHocJobCrawlerEntity.JobEntityVO.SalaryEntity salaryEntity : jobEntityVO.getSalaryArray()) {
                        if (null != salaryEntity.getUrl()){
                            salaryArray_urlCnt++;
                        }
                        if (null != salaryEntity.getTitle()){
                            salaryArray_titleCnt++;
                        }
                        if (null !=salaryEntity.getMeta()){
                            salaryArray_metaCnt ++;
                        }
                        if (null != salaryEntity.getSalary()){
                            salaryArray_salaryCnt++;
                        }
                        if (null != salaryEntity.getSalaryUnit()){
                            salaryArray_salaryUnitCnt ++;
                        }
                        if (null != salaryEntity.getSource()){
                            salaryArray_sourceCnt++;
                        }
                    }
                }
                if (null != jobEntityVO.getRatingArray()){
                    ratingArrayCnt = ratingArrayCnt + jobEntityVO.getRatingArray().size();
                    for (AdHocJobCrawlerEntity.JobEntityVO.RatingEntity ratingEntity : jobEntityVO.getRatingArray()) {
                        if (null != ratingEntity.getLink()){
                            ratingArray_linkCnt++;
                        }
                        if (null != ratingEntity.getSource()){
                            ratingArray_sourceCnt++;
                        }
                        if (null != ratingEntity.getStarRating()){
                            ratingArray_starRatingCnt++;
                        }
                        if (null != ratingEntity.getReviewCount()){
                            ratingArray_reviewCountCnt++;
                        }
                    }
                }
            }
        }
    }

    private List<AdHocJobCrawlerEntity> getDomainEntity(String folder, Integer domainId) {
        List<AdHocJobCrawlerEntity> domainEntity = new ArrayList<AdHocJobCrawlerEntity>();
        for (File file : new File(folder).listFiles()) {
            try {
                System.out.println("===###读取文件进行合并:" + file.getAbsolutePath());
                domainEntity.addAll(getDataFromSQS(file.getAbsolutePath(), domainId));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return domainEntity;
    }


    private List<AdHocJobCrawlerEntity> getDataFromSQS(String file, Integer domainid) throws Exception {

        List<AdHocJobCrawlerEntity> adHocJobCrawlerEntityList = new ArrayList<>();

        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = br.readLine()) != null) {
                AdHocJobCrawlerEntity entity;
                entity = getFileInfo(line, domainid);
                keywordSet.add(entity.getKeyword());
                adHocJobCrawlerEntityList.add(entity);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }


        return adHocJobCrawlerEntityList;
    }

    private void queryByBigOrNormalTable(Integer engineId, Integer languageId, Set<String> keywordSearhList) {
        List<String> countryCds = gscClickSteamDAO.getCountryCode(engineId, languageId);

        if (countryCds.size() <= 0) {

        } else {
            countryCd = countryCds.get(0);
//            System.out.println("===###countryCd : " + countryCd);
            List<String> countryCds_big = gscClickSteamDAO.getCountryCode_v2(countryCd, dis_keyword_summary_annual_big_v2);
            if (countryCds_big.size() > 0) {
                System.out.println("===###使用 dis_keyword_summary_annual_big_v2 表 2000 片");
                // 使用 big 表 分成2000 片
                query_big_or_normal_table = dis_keyword_summary_annual_big_v2;
                modKeywordList(keywordSearhList, big_v2_hash_mod);
            } else {
                List<String> countryCds_normal = gscClickSteamDAO.getCountryCode_v2(countryCd, dis_keyword_summary_annual_normal_v2);
                if (countryCds_normal.size() > 0) {
                    System.out.println("===###使用 dis_keyword_summary_annual_normal_v2 表 500 片 ");
                    // 使用 normal 表 分成500 片
                    query_big_or_normal_table = dis_keyword_summary_annual_normal_v2;
                    modKeywordList(keywordSearhList, normal_v2_hash_mod);
                }
            }
        }
    }

    private void modKeywordList(Set<String> keywordSearhList, Integer modSize) {

        for (String keywordName : keywordSearhList) {
            BigInteger keywordHash = seoclarity.backend.utils.cityhash.CityHashUtil.getUrlHashForBigIntegerLowercase(FormatUtils.decodeKeyword(keywordName));
            BigInteger mod_bigint = keywordHash.mod(BigInteger.valueOf(modSize));
            Integer mod = mod_bigint.intValue();
//            System.out.println("===###keywordhash : " + keywordHash + "keywordName " + keywordName + ", decodeKeyword  : " + FormatUtils.decodeKeyword(keywordName) + ",  mod :" + mod);
            if (null == keywordHashModMap.get(mod) || keywordHashModMap.get(mod).size() <= 0) {
                List<String> keywordList = new ArrayList<>();
                keywordList.add(keywordName);
                keywordHashModMap.put(mod, keywordList);
            } else {
                List<String> oldKeywordList = keywordHashModMap.get(mod);
                oldKeywordList.add(keywordName);
                keywordHashModMap.put(mod, oldKeywordList);
            }
        }
    }

    private void cleancount() {
        keywordCnt = 0;
        jobEntityVOsCnt = 0;
        jobTitleCnt = 0;
        jobCompanyCnt = 0;
        jobLocationCnt = 0;
        jobSourceCnt = 0;
        jobPostTimeCnt = 0;
        jobTypeCnt = 0;
        jobPrimaryJobLinkCnt = 0;
        applyOnListCnt = 0;
        jsCompanyCnt = 0;
        companyIdCnt = 0;
        companyCityCnt = 0;
        companyStateCnt = 0;
        minSalaryCnt = 0;
        maxSalaryCnt = 0;
        salaryUnitCnt = 0;
        currencyCodeCnt = 0;
        ratingArrayCnt = 0;
        ratingArray_linkCnt = 0;
        ratingArray_sourceCnt = 0;
        ratingArray_starRatingCnt = 0;
        ratingArray_reviewCountCnt = 0;
        salaryArrayCnt = 0;
        salaryArray_urlCnt = 0;
        salaryArray_titleCnt = 0;
        salaryArray_metaCnt = 0;
        salaryArray_salaryCnt = 0;
        salaryArray_salaryUnitCnt = 0;
        salaryArray_sourceCnt = 0;
    }

    private Map<String, Integer> getKwSearchVolumeMap(Integer engineId, Integer languageId, Integer cityId, List<String> keywordSearhList, Map<String, Integer> kwSearckVolum) {
        List<SeoClarityKeywordAdwordsEntity> seoClarityKeywordAdwordsEntities = seoClarityKeywordAdwordsEntityDAO.getExistedAdwordListByKeywordName(keywordSearhList, engineId, languageId, cityId);
        seoClarityKeywordAdwordsEntities.forEach(obj -> {
            kwSearckVolum.put(obj.getKeywordName(), obj.getAvgMonthlySearchVolume());
        });
        return kwSearckVolum;
    }

    private Map<String, Integer> getTrueDemandMap(Integer mod, List<String> keywordSearhList, Map<String, Integer> result) throws Exception {

        List<Map<String, Object>> trueDemandList = gscClickSteamDAO.getTrueDemandByKeywordAndCountryCd(mod, countryCd, keywordSearhList, query_big_or_normal_table);
        if (trueDemandList.size() > 0) {
            for (Map<String, Object> trueDemandMap : trueDemandList) {
                String keyword = trueDemandMap.get("keywordName").toString();
                keyword = URLEncoder.encode(keyword, "utf-8").toLowerCase();
                String trueDemand = trueDemandMap.get("trueDemand").toString();
                if (trueDemand.contains(",")) {
                    trueDemand = trueDemand.replace(",", "");
                } else if (trueDemand.contains(".")) {
                    trueDemand = trueDemand.substring(0, trueDemand.indexOf("."));
                }
                result.put(keyword, Integer.parseInt(trueDemand));
            }
        }
        return result;
    }

    private void saveFilesToFtp(String localFileName, Integer domainid) {
        String targetPath = FTP_LOC + domainid + FOLDER;
        System.out.println("===###savefileToFTP =====" + targetPath + " file : " + localFileName);
        FTPUtils.saveFileToFTP(domainid, localFileName, targetPath);
    }


    private AdHocJobCrawlerEntity getFileInfo(String k, Integer domainId) {

        AdHocJobCrawlerEntity en = new AdHocJobCrawlerEntity();
        JSONObject jasonk = JSONObject.parseObject(k);
        //keyword level
        if (null != jasonk.get("keyword")) {
            String keyword = jasonk.get("keyword").toString();
            en.setKeyword(keyword);
            keywordCnt++;
        }
        if (null != jasonk.get("sendToQDate")) {
            String queryDate = jasonk.get("sendToQDate").toString();
            en.setQueryDate(queryDate);
        }
//        if (null != jasonk.get("searchVolume")) {
//            String searchVolume = jasonk.get("searchVolume").toString();
//            en.setSearchVolume(searchVolume);
//        }
//        if (null != jasonk.get("trueDemand")) {
//            String cityId = jasonk.get("cityId").toString();
//            en.setCityId(cityId);
//        }
        if (null != jasonk.get("cityName")) {
            String cityName = jasonk.get("cityName").toString();
            en.setCityName(cityName);
        }

        // url level
        if (null == jasonk.get("jobEntityVOs")) {
            System.out.println("===###keyword has null jobEntityVOs :  " + jasonk.get("keyword").toString());
        } else {

            JSONArray jobEntityVOsArr = jasonk.getJSONArray("jobEntityVOs");
            List<AdHocJobCrawlerEntity.JobEntityVO> voList = new ArrayList<>();
            jobEntityVOsCnt = jobEntityVOsCnt + jobEntityVOsArr.size();
            for (Object obj : jobEntityVOsArr) {
                AdHocJobCrawlerEntity.JobEntityVO jobEntityVO = en.new JobEntityVO();
                JSONObject jobJson = (JSONObject) JSONObject.toJSON(obj);
                if (null != jobJson.get("jobTitle")) {
                    String jobTitle = jobJson.get("jobTitle").toString();
                    jobEntityVO.setJobTitle(jobTitle);
                    jobTitleCnt++;
                }
                if (null != jobJson.get("jobCompany")) {
                    String jobCompany = jobJson.get("jobCompany").toString();
                    jobEntityVO.setJobCompany(jobCompany);
                    jobCompanyCnt++;
                }
                if (null != jobJson.get("jobLocation")) {
                    String jobLocation = jobJson.get("jobLocation").toString();
                    jobEntityVO.setJobLocation(jobLocation);
                    jobLocationCnt++;
                }
                if (null != jobJson.get("jobSource")) {
                    String jobSource = jobJson.get("jobSource").toString();
                    jobEntityVO.setJobSource(jobSource);
                    jobSourceCnt++;
                }
                if (null != jobJson.get("jobPostTime")) {
                    String jobPostTime = jobJson.get("jobPostTime").toString();
                    jobEntityVO.setJobPostTime(jobPostTime);
                    jobPostTimeCnt++;
                }
                if (null != jobJson.get("jobType")) {
                    String jobType = jobJson.get("jobType").toString();
                    jobEntityVO.setJobType(jobType);
                    jobTypeCnt++;
                }
                if (null != jobJson.get("jobPrimaryJobLink")) {
                    String jobPrimaryJobLink = jobJson.get("jobPrimaryJobLink").toString();
                    jobEntityVO.setJobPrimaryJobLink(jobPrimaryJobLink);
                    jobPrimaryJobLinkCnt++;
                }
                if (null != jobJson.get("jsCompany")) {
                    String jsCompany = jobJson.get("jsCompany").toString();
                    jobEntityVO.setJsCompany(jsCompany);
                    jsCompanyCnt++;
                }
                if (null != jobJson.get("companyId")) {
                    String companyId = null == jobJson.get("companyId") ? "" : jobJson.get("companyId").toString();
                    jobEntityVO.setCompanyId(companyId);
                    companyIdCnt++;
                }
                if (null != jobJson.get("companyCity")) {
                    String companyCity = null == jobJson.get("companyCity") ? "" : jobJson.get("companyCity").toString();
                    jobEntityVO.setCompanyCity(companyCity);
                    companyCityCnt++;
                }
                if (null != jobJson.get("companyState")) {
                    String companyState = null == jobJson.get("companyState") ? "" : jobJson.get("companyState").toString();
                    jobEntityVO.setCompanyState(companyState);
                    companyStateCnt++;
                }
                if (null != jobJson.get("minSalary")) {
                    String minSalary = null == jobJson.get("minSalary") ? "" : jobJson.get("minSalary").toString();
                    jobEntityVO.setMinSalary(minSalary);
                    minSalaryCnt++;
                }
                if (null != jobJson.get("maxSalary")) {
                    String maxSalary = null == jobJson.get("maxSalary") ? "" : jobJson.get("maxSalary").toString();
                    jobEntityVO.setMaxSalary(maxSalary);
                    maxSalaryCnt++;
                }
                if (null != jobJson.get("salaryUnit")) {
                    String salaryUnit = null == jobJson.get("salaryUnit") ? "" : jobJson.get("salaryUnit").toString();
                    jobEntityVO.setSalaryUnit(salaryUnit);
                    salaryUnitCnt++;
                }
                if (null != jobJson.get("currencyCode")) {
                    String currencyCode = null == jobJson.get("currencyCode") ? "" : jobJson.get("currencyCode").toString();
                    jobEntityVO.setCurrencyCode(currencyCode);
                    currencyCodeCnt++;
                }
                if (null != jobJson.get("applyOnList")) {
                    Object applyOnObj = jobJson.get("applyOnList");
                    Map<String, String> applyOnList = new HashMap<>();
                    Map<String, String> applyOnMap = JSONObject.parseObject(JSONObject.toJSONString(applyOnObj), Map.class);
                    applyOnListCnt = applyOnListCnt + applyOnMap.size();
                    for (String s : applyOnMap.keySet()) {
                        applyOnList.put(s, applyOnMap.get(s));
                    }
                    jobEntityVO.setApplyOnList(applyOnList);
                }

                JSONArray rantingArr = new JSONArray();
                if (null != jobJson.get("ratingArray") && jobJson.getJSONArray("ratingArray").size() > 0) {
                    rantingArr = jobJson.getJSONArray("ratingArray");
                    List<AdHocJobCrawlerEntity.JobEntityVO.RatingEntity> rantingList = new ArrayList<>();
                    ratingArrayCnt = ratingArrayCnt + rantingArr.size();
                    for (Object rantingObj : rantingArr) {
                        JSONObject rantingJson = (JSONObject) JSONObject.toJSON(rantingObj);
                        AdHocJobCrawlerEntity.JobEntityVO.RatingEntity rantingEntity = jobEntityVO.new RatingEntity();
                        if (null != rantingJson.get("link")) {
                            String link = rantingJson.get("link").toString();
                            rantingEntity.setLink(link);
                            ratingArray_linkCnt++;
                        }
                        if (null != rantingJson.get("source")) {
                            String source = rantingJson.get("source").toString();
                            rantingEntity.setSource(source);
                            ratingArray_sourceCnt++;
                        }

                        if (null != rantingJson.get("rank")) {
                            String rank = rantingJson.get("rank").toString();
                            rantingEntity.setRank(rank);
                        }
                        if (null != rantingJson.get("starRating")) {
                            String starRating = rantingJson.get("starRating").toString();
                            rantingEntity.setStarRating(starRating);
                            ratingArray_starRatingCnt++;
                        }
                        if (null != rantingJson.get("reviewCount")) {
                            String reviewCount = rantingJson.get("reviewCount").toString();
                            rantingEntity.setReviewCount(reviewCount);
                            ratingArray_reviewCountCnt++;
                        }
                        if (null != rantingEntity && (StringUtils.isNotBlank(rantingEntity.getLink()) || StringUtils.isNotBlank(rantingEntity.getSource()))) {
                            rantingList.add(rantingEntity);
                        }
                    }
                    jobEntityVO.setRatingArray(rantingList);
                }

                //   salaryArray
                JSONArray salaryArr = new JSONArray();
                if (null != jobJson.get("salaryArray") && jobJson.getJSONArray("salaryArray").size() > 0) {
                    salaryArr = jobJson.getJSONArray("salaryArray");
                    List<AdHocJobCrawlerEntity.JobEntityVO.SalaryEntity> salaryList = new ArrayList<>();
                    salaryArrayCnt = salaryArrayCnt + salaryArr.size();
                    for (Object salaryObj : salaryArr) {
                        JSONObject salaryJson = (JSONObject) JSONObject.toJSON(salaryObj);
                        AdHocJobCrawlerEntity.JobEntityVO.SalaryEntity salaryEntity = jobEntityVO.new SalaryEntity();
                        if (null != salaryJson.get("url")) {
                            String url = salaryJson.get("url").toString();
                            salaryEntity.setUrl(url);
                            salaryArray_urlCnt++;
                        }
                        if (null != salaryJson.get("title")) {
                            String title = salaryJson.get("title").toString();
                            salaryEntity.setTitle(title);
                            salaryArray_titleCnt++;
                        }
                        if (null != salaryJson.get("meta")) {
                            String meta = salaryJson.get("meta").toString();
                            salaryEntity.setMeta(meta);
                            salaryArray_metaCnt++;
                        }
                        if (null != salaryJson.get("salary")) {
                            String salary = salaryJson.get("salary").toString();
                            salaryEntity.setSalary(salary);
                            salaryArray_salaryCnt++;
                        }
                        if (null != salaryJson.get("salary_unit")) {
                            String salary_unit = salaryJson.get("salary_unit").toString();
                            salaryEntity.setSalaryUnit(salary_unit);
                            salaryArray_salaryUnitCnt++;
                        }
                        if (null != salaryJson.get("source")) {
                            String source = salaryJson.get("source").toString();
                            salaryEntity.setSource(source);
                            salaryArray_sourceCnt++;
                        }
                        if (null != salaryJson.get("rank")) {
                            String rank = salaryJson.get("rank").toString();
                            if (rank.contains(".")) {
                                rank = rank.substring(0, rank.indexOf("."));
                            }
                            salaryEntity.setRank(rank);
                        }

                        if (null != salaryEntity && (StringUtils.isNotBlank(salaryEntity.getUrl()) || StringUtils.isNotBlank(salaryEntity.getTitle()))) {
                            salaryList.add(salaryEntity);
                        }
                    }
                    jobEntityVO.setSalaryArray(salaryList);
                }
                voList.add(jobEntityVO);
            }
            en.setJobEntityVOs(voList);
        }
        return en;
    }

    public void addSVTrueDemand(AdHocJobCrawlerEntity entity, Map<String, Integer> kwSearckVolumMap, Map<String, Integer> kwTrueDemand) {
        String keyword = entity.getKeyword();
        Integer searchVolume = kwSearckVolumMap.get(keyword);
        Integer trueDemand = kwTrueDemand.get(keyword);
        entity.setSearchVolume(searchVolume);
        entity.setTrueDemand(trueDemand);
    }

    private void sendEmailToLeo(String userName, String emailAddress, String path, String fname, int ownDomainId, String projectName) throws Exception {

        String subject = "AdHoc Data Retrieval Download Ready - " + projectName;
        String info = "===###statistical data for OID " + ownDomainId;
        info += "===### keyword count : " + keywordCnt + LINE_BREAK;
        info += "===### jobEntityVOs count : " + jobEntityVOsCnt + LINE_BREAK;
        info += "===### jobTitle count : " + jobTitleCnt + LINE_BREAK;
        info += "===### jobCompany count : " + jobCompanyCnt + LINE_BREAK;
        info += "===### jobLocation count : " + jobLocationCnt + LINE_BREAK;
        info += "===### jobSource count : " + jobSourceCnt + LINE_BREAK;
        info += "===### jobPostTime count : " + jobPostTimeCnt + LINE_BREAK;
        info += "===### jobPrimaryJobLink count : " + jobPrimaryJobLinkCnt + LINE_BREAK;
        info += "===### applyOnList count : " + applyOnListCnt + LINE_BREAK;
        info += "===### jsCompany count : " + jsCompanyCnt + LINE_BREAK;
        info += "===### companyId count : " + companyIdCnt + LINE_BREAK;
        info += "===### companyCity count : " + companyCityCnt + LINE_BREAK;
        info += "===### companyState count : " + companyStateCnt + LINE_BREAK;
        info += "===### minSalary count : " + minSalaryCnt + LINE_BREAK;
        info += "===### maxSalary count : " + maxSalaryCnt + LINE_BREAK;
        info += "===### salaryUnit count : " + salaryUnitCnt + LINE_BREAK;
        info += "===### currencyCode count : " + currencyCodeCnt + LINE_BREAK;
        info += "===### ratingArray count : " + ratingArrayCnt + LINE_BREAK;
        info += "===### ratingArray_link count : " + ratingArray_linkCnt + LINE_BREAK;
        info += "===### ratingArray_source count : " + ratingArray_sourceCnt + LINE_BREAK;
        info += "===### ratingArray_starRating count : " + ratingArray_starRatingCnt + LINE_BREAK;
        info += "===### ratingArray_reviewCount count : " + ratingArray_reviewCountCnt + LINE_BREAK;
        info += "===### salaryArray count : " + salaryArrayCnt + LINE_BREAK;
        info += "===### salaryArray_url count : " + salaryArray_urlCnt + LINE_BREAK;
        info += "===### salaryArray_title count : " + salaryArray_titleCnt + LINE_BREAK;
        info += "===### salaryArray_meta count : " + salaryArray_metaCnt + LINE_BREAK;
        info += "===### salaryArray_salary count : " + salaryArray_salaryCnt + LINE_BREAK;
        info += "===### salaryArray_salaryUnit count : " + salaryArray_salaryUnitCnt + LINE_BREAK;
        info += "===### salaryArray_source count : " + salaryArray_sourceCnt + LINE_BREAK;
        System.out.println(info);
        System.out.println(path);
        System.out.println(fname);

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "");
        reportMap.put("info", info);

        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(ownDomainId);
        int sizeMb = 0;
        File file = new File(path);
        String downloadFileName = "";
        try {
            if (file.exists()) {
                long fileSize = file.length();
                sizeMb = (int) (fileSize / 1024 / 1024);
                System.out.println(" OID:" + ownDomainId + " filename: " + (path) +
                        " size:" + fileSize + " MB:" + sizeMb);

                GZipUtil.zipFile(file.getAbsolutePath(), GZipUtil.ZIPFile_POSTFIX);
                downloadFileName = file.getAbsolutePath() + GZipUtil.ZIPFile_POSTFIX;
            }
        } catch (Exception e) {
            System.out.println(" zip file error downloadFileName : " + downloadFileName);
        }

        String linkUrl = "";
        try {
            boolean isSaved = SeagateUtils.saveFileToDefaultSeagate(ownDomainId, downloadFileName);
            if (!isSaved) {
                System.out.println("===send to Seagate Failed!projectName:" + projectName);
            }
            linkUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, fname + GZipUtil.ZIPFile_POSTFIX);
            System.out.println("==isSaved:" + isSaved + ",linkUrl:" + linkUrl);
        } catch (Exception e) {
//            e.printStackTrace();
            System.out.println("===send to Seagate Failed!projectName:" + projectName);
        }


        //https://www.wrike.com/open.htm?id=960781633
        String linkText = null;
//        try {
//            String name = URLEncoder.encode(filename, "utf-8");
        linkText = linkUrl;
//        } catch (UnsupportedEncodingException e) {
//            System.out.println(e.getMessage());
//            linkText = linkUrl;
//        }

        System.out.println("  fileLink:" + linkText);
        reportMap.put("fileLink", linkText);
        String emailTo = "<EMAIL>";
        String[] bccTo = new String[]{"<EMAIL>", "<EMAIL>"};

        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo,
                ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, null, null);

        System.out.println("=========Send to : " + emailAddress + " success!");
    }


}
