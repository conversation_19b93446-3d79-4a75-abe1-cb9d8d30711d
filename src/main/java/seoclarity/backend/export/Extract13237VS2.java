package seoclarity.backend.export;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.json.JSONObject;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityLanguageEntityDAO;
import seoclarity.backend.entity.RankIndexParamVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.export.autoExtract.AbstractExtractScript;
import seoclarity.backend.export.vo.PaginationQueryVO;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.ClarityDBAPIUtils;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.util.*;

/**
 * 每周一运行, 重跑日期为周日
 */
@CommonsLog
public class Extract13237VS2 {

    private static String LOC = "/home/<USER>/";
    private static final String VS2_API_URL = "https://s11-dev.seoclarity.dev/seoClarity/pixel/v2_keywordVisibilityTabledownloadAll";
    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private CommonParamDAO commonParamDAO;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private SeoClarityLanguageEntityDAO seoClarityLanguageEntityDAO;
    private static Date processingDate = null;

    public Extract13237VS2(){
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        seoClarityLanguageEntityDAO = SpringBeanFactory.getBean("seoClarityLanguageEntityDAO");
    }

    private void startProcess(int ownDomainId, String device) {

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }

        int primaryEngineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int primaryLanguageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);

        String domainWithSubfolder = ownDomainEntity.getDomainWithSubfolder();
        domainWithSubfolder = StringUtils.removeStartIgnoreCase(domainWithSubfolder, "www.");
        log.info("====domainWithSubfolder:" + domainWithSubfolder);
//        String domainName = ownDomainEntity.getDomain();
        String primaryEngineName = ownDomainEntity.getSearchEngine();
        String primaryLanguageName = seoClarityLanguageEntityDAO.getLanguageById(primaryLanguageId);
        try {
            if (StringUtils.contains(primaryLanguageName, "_")) {
                primaryLanguageName = StringUtils.lowerCase(StringUtils.split(primaryLanguageName, "_")[1]);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean isMobile = device.equalsIgnoreCase("d") ? false: true;
        List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getAllSERels(ownDomainEntity);
        if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
            for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                int engineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                int languageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                boolean secondaryIsMobile = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? false : true;
                if (secondaryIsMobile != isMobile) {
                    log.info("===secondary not same device isMobile: " + isMobile + ",secondaryIsMobile:" + secondaryIsMobile);
                    continue;
                }

                /**
                *  1-1 / 1-2 was same engine but not same language
                * */
                String secEngineName = domainSearchEngineRelEntity.getSearchEngine();
                String secLanguageName = seoClarityLanguageEntityDAO.getLanguageById(languageId);
                try {
                    if (StringUtils.contains(secLanguageName, "_")) {
                        secLanguageName = StringUtils.lowerCase(StringUtils.split(secLanguageName, "_")[1]);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if(StringUtils.isBlank(secEngineName) && primaryEngineId == engineId && primaryLanguageId == languageId){
                    secEngineName = primaryEngineName;
                    secLanguageName = primaryLanguageName;
                }

                String elName = secEngineName + "_" + secLanguageName;
                log.info("=======engineId:" + engineId + ",languageId:" + languageId + ",secEngineName:" + secEngineName + ",secLanguageName:" + secLanguageName);
                String vsFilePath = processVsDownloadByType(ownDomainId, engineId, languageId, device, domainWithSubfolder, elName);
                if (StringUtils.isBlank(vsFilePath)) {
                    log.error("===vsFilePathNone!!!");
                } else {
                    File vsFile = new File(vsFilePath);
//                    String remoteFilePath = LOC + ownDomainId + "/" + vsFile.getName();//test
                    String remoteFilePath = "223126";//common_param id
                    try {
//                        serverAuthenticationInfoService.copyFileToRemoteServerNew(ServerAuthenticationInfoEntity.SERVER_TYPE_FTP, ownDomainId, vsFile.getAbsolutePath(), remoteFilePath, null);
                        serverAuthenticationInfoService.copyFileToRemoteServerNew(ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP_GCS, ownDomainId, vsFile.getAbsolutePath(), remoteFilePath, null);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private String processVsDownloadByType(int ownDomainId, int engineId, int languageId, String device, String domainWithSubfolder, String engineLanguageName) {
        String urlString = getExtractSqlByAPI(ownDomainId, device, engineId, languageId, domainWithSubfolder);
        if (StringUtils.isBlank(urlString)) {
            log.info("===urlStringNone:" + ownDomainId);
            return null;
        }
        String fileDate = FormatUtils.formatDate(processingDate, "yyyyMMdd");
        String filePath =  LOC + ownDomainId + "/" + engineLanguageName + "_" + device + "_" + fileDate + "_visibility_share.csv";
        urlString = urlString.substring(1, urlString.length() - 1);
        try {
            downloadFile(urlString, filePath);
            System.out.println("文件下载成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return filePath;
    }

    public static void downloadFile(String urlString, String filePath) throws IOException {
        log.info("====downloadURL:" + urlString);
        URL url = new URL(urlString);
        InputStream inputStream = url.openStream();
        FileOutputStream fileOutputStream = new FileOutputStream(filePath);
        byte[] buffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            fileOutputStream.write(buffer, 0, bytesRead);
        }
        fileOutputStream.close();
        inputStream.close();
    }

    private String getExtractSqlByAPI(int ownDomainId, String device, int engineId, int languageId, String domainWithSubfolder) {


        List<CommonParamEntity> visibilityFoldCtrCommParamList = commonParamDAO.getListByFuncNameAndOwndomainId(ownDomainId, "VisibilityFoldCtr");
        if (CollectionUtils.isEmpty(visibilityFoldCtrCommParamList)) {
            log.error("===visibilityFoldCtrCommParamList111 none:" + ownDomainId);
            return null;
        }

        String visibilityFoldCtrParamJson = visibilityFoldCtrCommParamList.get(0).getParamJson();
        List<Double> foldRateList = extractValues(visibilityFoldCtrParamJson);

        List<CommonParamEntity> vsibilityFoldRankCtrCommParamList = commonParamDAO.getListByFuncNameAndOwndomainId(ownDomainId, "VisibilityFoldRankCtr");
        if (CollectionUtils.isEmpty(vsibilityFoldRankCtrCommParamList)) {
            log.error("===vsibilityFoldRankCtrCommParamList222 none:" + ownDomainId);
        }
        String vsibilityFoldRankCtrParamJson = vsibilityFoldRankCtrCommParamList.get(0).getParamJson();
        List<Double> foldRankRateList = extractValues(vsibilityFoldRankCtrParamJson);


        Gson gson = new GsonBuilder().disableHtmlEscaping().create();

        Date endDate = FormatUtils.getLastSundayForWeeklyDomainExtract(processingDate);
        Date startDate = DateUtils.addDays(endDate, -7);

        String startDateStr = FormatUtils.formatDate(startDate, "yyyy-MM-dd");
        String endDateStr = FormatUtils.formatDate(endDate, "yyyy-MM-dd");
        System.out.println("====startDateStr:" + startDateStr + ",endDateStr:" + endDateStr);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("accessToken", "c09yxv13-opr3-d745-9734-8pu48420nj67");
        paramMap.put("domainId", ownDomainId);
        paramMap.put("startDate", startDateStr);
        paramMap.put("endDate", endDateStr);
        List<String> domainNameList = new ArrayList<>();
        domainNameList.add(domainWithSubfolder);
        paramMap.put("domainNames", domainNameList);
        paramMap.put("searchEngineId", engineId);
        paramMap.put("languageId", languageId);
        paramMap.put("device", device);
        paramMap.put("foldRate", foldRateList);
        paramMap.put("foldRankRate", foldRankRateList);
        paramMap.put("excludeDeletedKeywords", true);
        paramMap.put("isGeo", false);
        PaginationQueryVO pagePaginationQueryVO = new PaginationQueryVO();
        paramMap.put("paginationQueryVO", pagePaginationQueryVO);

        log.info("===request paramJson:" + gson.toJson(paramMap));

        String response = null;
        int max = 10;
        int tryCnt = 0;
        while (tryCnt <= max) {
            tryCnt++;
            try {
                response = ClarityDBAPIUtils.simplePost(VS2_API_URL, gson.toJson(paramMap));
                break;
            } catch (Exception e) {
                log.info("===request failed. tryCnt:" + tryCnt + ", paramJson:" + gson.toJson(paramMap) + ", response:" + response);
                log.error(e.getMessage());
                e.printStackTrace();
            }
        }
        log.info("====response:" + response);
//        Map<String, String> responseMap = gson.fromJson(response, Map.class);
//        String sql = responseMap.get("data");
//        sql = sql.replace("\\u003d", "=");
//        log.info("===response:" + sql);
        return response;
    }

    public static List<Double> extractValues(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);
        Map<Integer, Double> sortedMap = new TreeMap<>();
        for (String key : jsonObject.keySet()) {
            int intKey = Integer.parseInt(key);
            double value = jsonObject.getDouble(key);
            sortedMap.put(intKey, value);
        }
        List<Double> values = new ArrayList<>(sortedMap.values());
        log.info("===values= : " + JSON.toJSONString(values));
        return values;
    }


    /**
     * DEVICE SHOULD BE "d" OR "m"
     * @param args
     */
    public static void main(String[] args) throws Exception{
//        String urlString = "https://cloudv2.seoclarity.net/temporary-files-central/13237/13237_83fec908-7684-48d5-b23d-2ce3e7f72ab3.csv?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250224T021932Z&X-Amz-SignedHeaders=host&X-Amz-Expires=432000&X-Amz-Credential=STX1NXQFSSL4TBWVR1SDKR35%2F20250224%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=6bb6675bcfc00d4a82172a748465572db8ee1d433824536ae751334a04373921";
//        String filePath = "/home/<USER>/13237/0224.csv";
//        downloadFile(urlString, filePath);

        Extract13237VS2 extract13237VS2 = new Extract13237VS2();
        if (args != null && args.length >= 3) {
            processingDate = FormatUtils.toDate(args[2], FormatUtils.DATE_PATTERN_2);
        } else {
            processingDate = DateUtils.addDays(new Date(), -1);
        }
        extract13237VS2.startProcess(Integer.parseInt(args[0]), args[1]);
    }

}
