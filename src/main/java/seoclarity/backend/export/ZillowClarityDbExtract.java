package seoclarity.backend.export;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.bean.FTPServerInfoBean;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.export.ZillowClarityDbExtract" -Dexec.args="2017-03-06 2017-03-06 non" -Dexec.cleanupDaemonThreads=false
 *
 * <AUTHOR>
 */
public class ZillowClarityDbExtract {

    private static final int QUERY_TRY_COUNT = 10;

    private static String queryDateStr;
    private static String queryToDateStr;
    private static String outputFile;

    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ScKeywordRankManager scKeywordRankManager;
    private ExtractService extractService;

    private static final String SPLIT = "	";
    private static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;
    private List<String> competitors = new ArrayList<>(30);
    private int owndomainid = 476;

    public ZillowClarityDbExtract() {

        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        extractService = SpringBeanFactory.getBean("extractService");

        competitors.add("com.hotpads");
        competitors.add("com.streeteasy");
        competitors.add("com.apartmentfinder.www");
        competitors.add("com.apartmentguide.www");
        competitors.add("com.apartmentratings.www");
        competitors.add("com.apartments.www");
        competitors.add("com.bankrate.www");
        competitors.add("com.century21.www");
        competitors.add("com.coldwellbankerhomes.www");
        competitors.add("com.forrent.www");
        competitors.add("com.forsalebyowner.www");
        competitors.add("com.homes.www");
        competitors.add("com.houzz.www");
        competitors.add("com.investopedia.www");
        competitors.add("com.kw.www");
        competitors.add("com.lendingtree.www");
        competitors.add("com.mlcalc.www");
        competitors.add("com.mortgagecalculator.org");
        competitors.add("com.movoto.www");
        competitors.add("com.nerdwallet.www");
        competitors.add("com.newhomesource.www");
        competitors.add("com.quickenloans.www");
        competitors.add("com.realtor.www");
        competitors.add("com.redfin.www");
        competitors.add("com.remax.www");
        competitors.add("com.rent.www");
        competitors.add("com.rentjungle.www");
        competitors.add("com.thebalance.www");
        competitors.add("com.trulia.www");
        competitors.add("com.yelp.www");

    }

    public void process() throws Exception {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Date queryDate = null;
        Date copyToDate = null;
        File outputF = null;
        if (StringUtils.isBlank(queryDateStr) || StringUtils.isBlank(queryToDateStr)) {
            queryDate = CommonUtils.getYesterday(false);
            copyToDate = queryDate;
            outputF = new File("/tmp/Zillow-CompetitorRankCustom" + dateFormat.format(copyToDate) + ".csv");
        } else {
            queryDate = DateUtils.truncate(dateFormat.parse(queryDateStr), Calendar.DAY_OF_MONTH);
            copyToDate = DateUtils.truncate(dateFormat.parse(queryToDateStr), Calendar.DAY_OF_MONTH);
//            outputF = new File(outputFile);
        }

        Map<Integer, String> tagsId = new HashMap<Integer, String>();
//		tagsId.put(0, "Domain Level");
        tagsId.put(87445, "Rentals: All Apartments #r");
        tagsId.put(87453, "Rentals: All Houses #r");
        tagsId.put(117001, "Rentals: Rental Building (in the BAD) #r");
        tagsId.put(36534, "Big City - Biggest Keywords");
        tagsId.put(3843, "Big City – Homes for Sale");
        tagsId.put(115731, "Big City – Real Estate");
        tagsId.put(423764, "FR - All - No Zip #HDP");
        tagsId.put(415436, "FS - Pop > 300k - No Zip #HDP");
        tagsId.put(487509, "NFS – Excluding recently sold/premarket foreclosure - No Zip #HDP");
        tagsId.put(439093, "FSBA - Redfin Covered County - No Zip #HDP");
        tagsId.put(423767, "FS New Construction - No Zip #HDP");
        tagsId.put(110362, "New Construction - All New Homes Queries (new homes in + city & city + new homes)");
        tagsId.put(763284, "Top FS-City Search Pages (Top Cities contributing 30% MRR)");
        tagsId.put(50799, "Directory – “Big City” Real Estate Agents (Legacy)");
        tagsId.put(87850, "$Mortgage: All Mortgage");

        OwnDomainEntity ownDomain = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(owndomainid);
        if (ownDomain == null) {
            System.out.println("=== domain not exist , exit !!");
            try {
                extractService.sendMailReport("ERROR:Export for inactive OID:" + owndomainid, "Please disable export for inactive OID:" + owndomainid + "(" + getClass().getName() + ")");
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            return;
        }


        boolean isMobile = ownDomain.isMobileDomain();
        boolean trueRank = true;
        if (ownDomain.getRankCalculation() != null && ownDomain.getRankCalculation().intValue() == 1) {
            // web rank
            trueRank = false;
        }
        int searchId = scKeywordRankManager.getSearchEngineId(ownDomain);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomain);

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Tag").append(SPLIT);
        header.append("Domain").append(SPLIT);
        header.append("Rank 1").append(SPLIT);
        header.append("Rank 2").append(SPLIT);
        header.append("Rank 3").append(SPLIT);
        header.append("Rank 4").append(SPLIT);
        header.append("Rank 5").append(SPLIT);
        header.append("Rank 6").append(SPLIT);
        header.append("Rank 7").append(SPLIT);
        header.append("Rank 8").append(SPLIT);
        header.append("Rank 9").append(SPLIT);
        header.append("Rank 10");
        lines.add(header.toString());

        Date tmpDate = queryDate;
        int frequency = 1;//daily
        if (ownDomain.getKeywordRankFrequency() != null && ownDomain.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
            tmpDate = FormatUtils.getLastSundayForWeeklyDomainExtract(tmpDate);
            System.out.println("====weekly domain processDate:" + tmpDate);
            frequency = 7;//weekly
        }
        String pDate = FormatUtils.formatDate(tmpDate, FormatUtils.DATE_FORMAT_YYYYMMDD);
        String sTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

        logglyVO.setoId(String.valueOf(owndomainid));
        logglyVO.setName("ZillowClarityDbExtract");
        logglyVO.setDevice(isMobile ? "m" : "d");

        logglyVO.setpDate(pDate);
        List<String> groupList = new ArrayList<>();
        groupList.add(LogglyVO.GROUP_CUSTOMIZED_EXTRACT);
        logglyVO.setGroups(groupList);

        while (copyToDate.compareTo(tmpDate) >= 0) {
            outputF = new File("/tmp/Zillow-CompetitorRankCustom" + dateFormat.format(tmpDate) + ".csv");
            FileUtils.writeLines(outputF, lines);

            for (Integer tagId : tagsId.keySet()) {

                String tagName = tagsId.get(tagId);

				//change to cold https://www.wrike.com/open.htm?id=488535008
                int retryCount = 1;
                List<Map<String, Object>> results = new ArrayList<>();
                while (true) {
                    try {
                        results = clDailyRankingEntityDao
                                .selectTop100CompetitorsForExtract(tmpDate, owndomainid,
                                        searchId, languageId, trueRank, isMobile, tagId, competitors);
                        break;
                    } catch (Exception e) {

                        if (retryCount >= QUERY_TRY_COUNT) {
                            System.out.println("====error extract oid : " + ownDomain.getId());
                            System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + ownDomain.getId());
//                            String message = "Error extract oid:" + ownDomain.getId() + ",date:" + dateFormat.format(tmpDate);
//                            sendMailReport(message);

                            logglyVO.setStatus(LogglyVO.STATUS_NG);
                            String body = new Gson().toJson(logglyVO);
                            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                            return;
                        }

                        e.printStackTrace();
                        System.out.println("====extract error oid:" + ownDomain.getId() + ", sleep " + (1000 * 60 * retryCount));
                        try {
                            Thread.sleep(1000 * 60 * retryCount);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        retryCount++;
                    }
                }

                lines = new ArrayList<String>();

                String date = DateFormatUtils.format(tmpDate, "MM/dd/yyyy");

                List<String> handleCompetitors = new ArrayList<>();

                for (Map<String, Object> oneMap : results) {
                    StringBuilder line = new StringBuilder();

                    String domain_reverse = oneMap.get("domain_reverse").toString();
                    String rank1 = oneMap.get("rank1").toString();
                    String rank2 = oneMap.get("rank2").toString();
                    String rank3 = oneMap.get("rank3").toString();
                    String rank4 = oneMap.get("rank4").toString();
                    String rank5 = oneMap.get("rank5").toString();
                    String rank6 = oneMap.get("rank6").toString();
                    String rank7 = oneMap.get("rank7").toString();
                    String rank8 = oneMap.get("rank8").toString();
                    String rank9 = oneMap.get("rank9").toString();
                    String rank10 = oneMap.get("rank10").toString();

                    String reverseDomain = StringUtils.reverseDelimited(
                            domain_reverse, '.');

                    line.append(date).append(SPLIT);
                    line.append(tagName).append(SPLIT);
                    line.append(reverseDomain).append(SPLIT);
                    line.append(rank1).append(SPLIT);
                    line.append(rank2).append(SPLIT);
                    line.append(rank3).append(SPLIT);
                    line.append(rank4).append(SPLIT);
                    line.append(rank5).append(SPLIT);
                    line.append(rank6).append(SPLIT);
                    line.append(rank7).append(SPLIT);
                    line.append(rank8).append(SPLIT);
                    line.append(rank9).append(SPLIT);
                    line.append(rank10).append(SPLIT);

                    lines.add(line.toString());

                    handleCompetitors.add(domain_reverse);
                }

                if (!handleCompetitors.containsAll(competitors)) {
                    System.out.println("competitor num less, will make it.");
                    for (String competitor : competitors) {
                        if (!handleCompetitors.contains(competitor)) {
                            StringBuilder line = new StringBuilder();
                            line.append(date).append(SPLIT);
                            line.append(tagName).append(SPLIT);
                            line.append(competitor).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            line.append(0).append(SPLIT);
                            lines.add(line.toString());
                        }
                    }
                }
                totalCnt = lines.size();
                insertFile(lines, outputF);


            }

            // upload to file
//	        uploadToFtp(outputF);
            String remotePath = "/home/<USER>/476/Competitor-Report-Custom/";
            copyBySSH(remotePath, outputF.getAbsolutePath());

            try {
                outputF.delete();
                System.out.println("delete OutFile: " + outputF.getAbsolutePath());
            } catch (Exception e) {
                e.printStackTrace();
            }


            System.out.println("ProcessDate: " + tmpDate
                    + " =============is Done==============");
            tmpDate = DateUtils.addDays(tmpDate, 1);
        }

        logglyVO.setStatus(LogglyVO.STATUS_OK);
        logglyVO.setsTime(sTime);
        logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
        logglyVO.setRows(String.valueOf(totalCnt));
        String body = new Gson().toJson(logglyVO);
        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

    }

    private void insertFile(List<String> lines, File outputF) {
        if (lines.size() == 0) {
            return;
        }
        try {
            FileUtils.writeLines(outputF, lines, true);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void copyBySSH(String remotePath, String localFile) throws Exception {
        //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/500505448
        FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
        String host = ftpServerInfo.getPrivateHost();
        String ftpUsername = ftpServerInfo.getServerUserName();
        String ftpPassword = ftpServerInfo.getServerPassword();

        Connection connection = new Connection(host);
        connection.connect();
        if (connection.authenticateWithPassword(ftpUsername, ftpPassword)) {
            SCPClient scpClient = connection.createSCPClient();
            System.out.println(" ===remote:" + remotePath);

            try {
                scpClient.put(localFile, remotePath);
            } catch (Exception e) {
                e.printStackTrace();
                throw new Exception(" Failed to copy files from host... file:" + remotePath, e);
            } finally {
                connection.close();
            }
            System.out.println(" ===Saved file:" + remotePath + " to " + localFile);
        }
    }

    public static void main(String[] args) {
        if (args != null && args.length >= 3) {
            queryDateStr = args[0];
            queryToDateStr = args[1];
            outputFile = args[2];

        } else {
            System.out.println("Error: no param daily job ");
//        	return;
        }

        ZillowClarityDbExtract zillowClarityDbExtract = new ZillowClarityDbExtract();
        try {
            System.out.println(" queryDateStr: " + queryDateStr + " copyToDateStr: " + queryToDateStr + " filePath:" + outputFile);
            Thread.sleep(5 * 1000);
            zillowClarityDbExtract.process();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
