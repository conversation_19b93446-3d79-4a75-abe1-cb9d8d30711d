package seoclarity.backend.export.poprank;

import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;

public class KeywordRankEntityVO implements Serializable {
	public static final int TYPE_WEB_RESOURCE = 1;

	public static final int TYPE_IMGAGE = 2;

	public static final int TYPE_ADDRESS = 3;

	public static final int TYPE_VIDEO = 4;

	public static final int TYPE_NEWS = 5;

	public static final int TYPE_SHOPPING = 6;

	public static final int TYPE_LOCALLISTING = 7;
	
	public static final int TYPE_TWITTER = 8;

	public static final int TYPE_ANSWERBOX = 9;
	
	public static final int TYPE_JOB_URL = 10;
	
	public static final int TYPE_KNOWLEDGE = 11;
	
	public static final int TYPE_FLIGHTS = 12;

	public static final int TYPE_INTERESTING_FINDS = 13;

	public static final int TYPE_PAA = 14;

	public static final int TYPE_POPULAR_PRODUCTS = 15;

	public static final int TYPE_PPC = 16;
	
	public static final int TYPE_FINANCE = 17;
	
	public static final int TYPE_BOOKS = 18;
	
	// https://www.wrike.com/open.htm?id=646613658
	// ONLY for google image ranking
	public static final int TYPE_GOOGLE_IMAGE_VIDEO = 19;
	public static final int TYPE_GOOGLE_IMAGE_PRODUCT = 20;
	public static final int TYPE_GOOGLE_IMAGE_RECIPE = 21;
	
	public static final int TYPE_EVENT = 22;
	
	//  https://findresultson.google.com/
	public static final int TYPE_FINDRESULTSON = 23;
	public static final int TYPE_DEALS = 24;
	
	public static final int TYPE_ALSO_SEARCH = 33;
	public static final int TYPE_THING_TO_KNOW = 34;
	public static final int TYPE_PODCASTS = 35;
	public static final int TYPE_WEATHER = 36;
	public static final int TYPE_LOCATION_BAR = 37;
	public static final int TYPE_SHORT_VIDEO = 38;
	public static final int TYPE_SKIP = 39;
	public static final int TYPE_REVIEWS = 40;
	public static final int TYPE_SALARY_ESTIMATES = 41;
	
	public static final int TYPE_REFINE = 42;
	public static final int TYPE_PLA = 43;
	
	// https://www.wrike.com/open.htm?id=905142249
	public static final int TYPE_NEAR_YOU = 44;

	// https://www.wrike.com/open.htm?id=1469173669
	public static final int TYPE_FROM_SOURCES_ACROSS_THE_WEB = 46;

	// https://www.wrike.com/open.htm?id=1269705961
	public static final int TYPE_COMPARISON = 48;

	// https://www.wrike.com/open.htm?id=1334356671
	public static final int TYPE_HOTEL = 50;
	// https://populardestinations.google.com/
	public static final int TYPE_POPULARDESTINATIONS = 51;
	// https://discussionsandforums.google.com/
	public static final int TYPE_DISCUSSIONSANDFORUMS = 52;
	// https://www.wrike.com/open.htm?id=1400336612
	public static final int TYPE_TOPSIGHTS = 53;
	// https://www.wrike.com/open.htm?id=1473793304
	public static final int TYPE_AIO = 54;
	// https://www.wrike.com/open.htm?id=1639694183
	public static final int TYPE_COURSES = 55;

	// https://www.wrike.com/open.htm?id=1269635801
	public static final int TYPE_POPULAR_RECIPES = 111;

	private static final long serialVersionUID = -403142995672844730L;
	private int rank;
	private String landingPage;
	private String domain;
	private String localTitle;
	private String localAdd;
	private String label;
	private String authorNm;
	private String authorLink;
	private String rating;
	private String ratingNumber;
	private String mobileFriendly;
	private String solrId;
	// https://www.wrike.com/open.htm?id=399697352
	private String questionlist;
	
	// https://www.wrike.com/open.htm?id=221768860
	private String metaDesc;
	
	// https://www.wrike.com/open.htm?id=492074605
	private String eventFlg;
	
	private String universalPackFlg;
	
	// https://www.wrike.com/open.htm?id=634698058
	// for image/shopping
	private String authorName;
	private String price;
	private String promotion;
	private String additionalLabel;
	private boolean googleCheckOut;
	// https://www.wrike.com/open.htm?id=887097265
	private String actualPrice;
	
	// https://www.wrike.com/open.htm?id=900934935
	// null/none, video, image
	private String thumbnail;
	
	// https://www.wrike.com/open.htm?id=979058460
	private String  maliciousUrlFlg;
	// https://www.wrike.com/open.htm?id=979104614
	private Boolean trustedStore; 
	// https://www.wrike.com/open.htm?id=1176074033
	private Boolean popularStoreFlg;

	private String vedStr;

	// https://www.wrike.com/open.htm?id=1293352486
	private Boolean urlImageCarousel;

	// https://www.wrike.com/open.htm?id=1322745613
	private Boolean couponFlg;

	private String heading;

	private String msg;

	// https://www.wrike.com/open.htm?id=1494875831
	private Boolean collapsedPopularBrandFlg;

	private Boolean aiOrganized;

	private Boolean aiSales;

	public String getMetaDesc() {
		return metaDesc;
	}

	public void setMetaDesc(String metaDesc) {
		this.metaDesc = metaDesc;
	}
	
	public Integer getLabelLen() {
		if(StringUtils.isBlank(label)) {
			return 0;
		}
		return label.length();
	}
	
	public Integer getMetaDescLen() {
		if(StringUtils.isBlank(metaDesc)) {
			return 0;
		}
		return metaDesc.length();
	}

	private List<KeywordSubRankEntityVO> subRankVOs;

	public List<KeywordSubRankEntityVO> getSubRankVOs() {
		return subRankVOs;
	}

	public void setSubRankVOs(List<KeywordSubRankEntityVO> subRankVOs) {
		this.subRankVOs = subRankVOs;
	}
	public String getSolrId() {
		return solrId;
	}

	public void setSolrId(String solrId) {
		this.solrId = solrId;
	}

	public String getRatingNumber() {
		if(StringUtils.isBlank(ratingNumber)) {
			return "-";
		}
		return ratingNumber;
	}

	public void setRatingNumber(String ratingNumber) {
		this.ratingNumber = ratingNumber;
	}

	public String getMobileFriendly() {
		return mobileFriendly;
	}

	public void setMobileFriendly(String mobileFriendly) {
		this.mobileFriendly = mobileFriendly;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getAuthorNm() {
		return authorNm;
	}

	public void setAuthorNm(String authorNm) {
		this.authorNm = authorNm;
	}

	public String getAuthorLink() {
		return authorLink;
	}

	public void setAuthorLink(String authorLink) {
		this.authorLink = authorLink;
	}

	private int type;

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

	public String getLandingPage() {
		return landingPage;
	}

	public void setLandingPage(String landingPage) {
		this.landingPage = landingPage;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getLocalTitle() {
		return localTitle;
	}

	public void setLocalTitle(String localTitle) {
		this.localTitle = localTitle;
	}

	public String getLocalAdd() {
		return localAdd;
	}

	public void setLocalAdd(String localAdd) {
		this.localAdd = localAdd;
	}

	public String getUrlType() {
		if (StringUtils.containsIgnoreCase(getLandingPage(), "maps.google.com")) {
			return "Local";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "images.google.com")) {
			return "Image";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "news.google.com")) {
			return "News";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "www.google.com/shopping")) {
			return "Shopping";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "www.youtube.com")) {
			return "Video";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "www.google.com")) {
			return "Universal";
		}

		return "Web";
	}
	
	private String ampFlg;

	public String getAmpFlg() {
		return ampFlg;
	}

	public void setAmpFlg(String ampFlg) {
		this.ampFlg = ampFlg;
	}
	
	private String stockFlg;
	private String priceFlg;
	
	private String videoFlg;

	public String getStockFlg() {
		return stockFlg;
	}

	public void setStockFlg(String stockFlg) {
		this.stockFlg = stockFlg;
	}

	public String getPriceFlg() {
		return priceFlg;
	}

	public void setPriceFlg(String priceFlg) {
		this.priceFlg = priceFlg;
	}

	public String getQuestionlist() {
		return questionlist;
	}

	public void setQuestionlist(String questionlist) {
		this.questionlist = questionlist;
	}

	public String getVideoFlg() {
		return videoFlg;
	}

	public void setVideoFlg(String videoFlg) {
		this.videoFlg = videoFlg;
	}

	public String getEventFlg() {
		return eventFlg;
	}

	public void setEventFlg(String eventFlg) {
		this.eventFlg = eventFlg;
	}

	public String getUniversalPackFlg() {
		return universalPackFlg;
	}

	public void setUniversalPackFlg(String universalPackFlg) {
		this.universalPackFlg = universalPackFlg;
	}

	public String getAuthorName() {
		return authorName;
	}

	public void setAuthorName(String authorName) {
		this.authorName = authorName;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getPromotion() {
		return promotion;
	}

	public void setPromotion(String promotion) {
		this.promotion = promotion;
	}

	public String getAdditionalLabel() {
		return additionalLabel;
	}

	public void setAdditionalLabel(String additionalLabel) {
		this.additionalLabel = additionalLabel;
	}

	public boolean isGoogleCheckOut() {
		return googleCheckOut;
	}

	public void setGoogleCheckOut(boolean googleCheckOut) {
		this.googleCheckOut = googleCheckOut;
	}

	public String getActualPrice() {
		return actualPrice;
	}

	public void setActualPrice(String actualPrice) {
		this.actualPrice = actualPrice;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	public String getMaliciousUrlFlg() {
		return maliciousUrlFlg;
	}

	public void setMaliciousUrlFlg(String maliciousUrlFlg) {
		this.maliciousUrlFlg = maliciousUrlFlg;
	}

	public Boolean getTrustedStore() {
		return trustedStore;
	}

	public void setTrustedStore(Boolean trustedStore) {
		this.trustedStore = trustedStore;
	}

	public Boolean getPopularStoreFlg() {
		return popularStoreFlg;
	}

	public void setPopularStoreFlg(Boolean popularStoreFlg) {
		this.popularStoreFlg = popularStoreFlg;
	}

	public String getVedStr() {
		return vedStr;
	}

	public void setVedStr(String vedStr) {
		this.vedStr = vedStr;
	}

	public Boolean getUrlImageCarousel() {
		return urlImageCarousel;
	}

	public void setUrlImageCarousel(Boolean urlImageCarousel) {
		this.urlImageCarousel = urlImageCarousel;
	}

	public Boolean getCouponFlg() {
		return couponFlg;
	}

	public void setCouponFlg(Boolean couponFlg) {
		this.couponFlg = couponFlg;
	}

	public String getHeading() {
		return heading;
	}

	public void setHeading(String heading) {
		this.heading = heading;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Boolean getCollapsedPopularBrandFlg() {
		return collapsedPopularBrandFlg;
	}

	public void setCollapsedPopularBrandFlg(Boolean collapsedPopularBrandFlg) {
		this.collapsedPopularBrandFlg = collapsedPopularBrandFlg;
	}

	public Boolean getAiOrganized() {
		return aiOrganized;
	}

	public void setAiOrganized(Boolean aiOrganized) {
		this.aiOrganized = aiOrganized;
	}

	public Boolean getAiSales() {
		return aiSales;
	}

	public void setAiSales(Boolean aiSales) {
		this.aiSales = aiSales;
	}
}
