package seoclarity.backend.export.poprank;

import lombok.Data;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.entity.KeywordRankLocalListingSubEntityVO;
import seoclarity.backend.utils.CommonUtils;

import java.io.Serializable;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class KeywordRankVO implements Serializable {
	private static final long serialVersionUID = 1351401185672317490L;

	public static final String COUNTRY_US_EN = "us_en";
	public static final String COUNTRY_CA_EN = "ca_en";
	public static final String COUNTRY_CA_FR = "ca_fr";
	
	public static final int GOOGLE_SEARCH_RECOMMEND_TYPE = 0;
	public static final int GOOGLE_SEARCH_INSTEAD_TYPE = 1;
	public static final int GOOGLE_SEARCH_INCLUDE_TYPE = 2;
	
	private int keywordDictId;
	private String country;
	private String language;
	private String cityName;
	private Integer cityId;
	private String knogTag;
	private String engine;
	private String keyword;
	private String queryDate;
	private int qeuryState;
	private Integer firstPageSize;
	private String plaFlg;
	private String ppcFlg;
	@Deprecated
	private String llFlg;
	private String hotelFlg;
	private String flightSearchFlg;
	private String socialInKg;
	private String topPpcCnt;
	private String createDate;
	private Integer id;
	private String appFlg;
	private List<String> relatedSearch;
	private Integer sendToQDate;
	private String jobLink;
	private String category;
	private String googleRecommend;
	private String googleSearchInstead;
	private String googleSearchInclude;

	private Date rankingDate;

	private String ossKeyPath;

	private String uid;

	private Integer frequency;

	private Integer projectId;
	
	private String researchFlg;
	
	private List<String> appList;
	
	private String quickLinksFlg;
	
	private boolean notRealSearchVolume = false;

	private String refineBy;
	
	private String commercialFlg;
	
	private String googleResultCnt;
	
	private String notGreatMatch;

	private String clarityDBKeywordHash;

	private String rightFreeShop;

	private List<String> triggerCodes;
	private List<String> triggerClasses;

	private List<String> linksInKnog;
	private List<String> vedInfo;
	
	private List<KeywordSubRankEntityVO> productList;

	//	https://www.wrike.com/open.htm?id=972442268
	private List<String> suggestList ;
	
	private Integer nearYou;
	
	private boolean fuleCostCal;
	
	private Integer searchEngine;
	private Integer searchLanguage;

	private String device;
	
	// https://www.wrike.com/open.htm?id=1020105101
	private List<AppbarKeyword> appbarKeywords;
	
	private List<PixelRankEntityVO> pixelRankList;
	
	// https://www.wrike.com/open.htm?id=1107581155
	private Boolean mediaApp;
	
	// https://www.wrike.com/open.htm?id=1104529221
	private List<String> kgEntityValues;

	// https://www.wrike.com/open.htm?id=1201780051
	private Map<String, String> buyingGuides;

	// https://www.wrike.com/open.htm?id=1205150256
	private List<KeywordRankSubEntityVOs.ThingsToKnowEntity> thingsToKnow;

	// https://www.wrike.com/open.htm?id=1228697629
	private String discussionsAndForums;
	private List<DiscussionsEntity> discussionsEntities;

	// https://www.wrike.com/open.htm?id=1259080951
	private String exploreBrandsFlg;

	private RankKafkaMsgKeyHandler.KeyModel keyModel;

	// https://www.wrike.com/open.htm?id=1369308841
//	private List<KeywordRankSubEntityVOs.AiGenaiSearchLinkEntity> aiGenaiSearchLinks;

	// https://www.wrike.com/open.htm?id=1384070569
	private List<KeywordRankSubEntityVOs.AIOGeneratedCarousalEntity> aiGeneratedCarousal;
	private List<KeywordRankSubEntityVOs.AIOGeneratedContentEntity> aiGeneratedContent;
	// https://www.wrike.com/open.htm?id=1473152786
	private Boolean aiOverview;
	private String aioType;

	private Boolean errorKeyword;

	private List<KeywordRankEntityVO> keywordRankEntityVOs;
	private List<JobEntityVO> jobEntityVOs;
	private List<YoutubeEntity> YoutubeEntityList;
	private List<String> things;
	private List<String> inDepthArt;
	private List<String> localListing;
	private List<KeywordRankLocalListingSubEntityVO> localListingV2;
	private List<KeywordRankLocalListingSubEntityVO> hotelListing;
	private List<SeoKeywordAdsEntity> allAds;
	private List<SeoKeywordPlaEntity> plaList;

	private String searchVol;
	private String cpc;
	private List<String> domainList;
	private String answerBox;
	private String questions;
	private String additionalQuestions;
	private Integer expansionId;
	private String adhocHourlyKeywordKey;
	private Boolean aiSales;
	private String aioContent;

	private Boolean psnFlg;
	private List<PSNVo> psnList;
	private Boolean perspectiveFlg;
	private Boolean peopleSuggestFlg;
	private Integer plaType;

	private Long parseCost;

	private String deviceType;
	private String queueName;
	//============================================= GET SET START ====================================================//

	public RankKafkaMsgKeyHandler.KeyModel getKeyModel() {
		return keyModel;
	}

	public void setKeyModel(RankKafkaMsgKeyHandler.KeyModel keyModel) {
		this.keyModel = keyModel;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getJobLink() {
		return jobLink;
	}

	public void setJobLink(String jobLink) {
		this.jobLink = jobLink;
	}

	public Integer getSendToQDate() {
		return sendToQDate;
	}

	public Date getSendToQDateAsDate() {
		if(sendToQDate == null || sendToQDate.intValue() == 0) {
			return CommonUtils.getYesterday(true);
		}
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			return sdf.parse(sendToQDate.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return CommonUtils.getYesterday(true);
	}


	public void setSendToQDate(Integer sendToQDate) {
		this.sendToQDate = sendToQDate;
	}

	public Date getRankingDate() {
		return rankingDate;
	}

	public void setRankingDate(Date rankingDate) {
		this.rankingDate = rankingDate;
	}

	public List<String> getRelatedSearch() {
		return relatedSearch;
	}

	public void setRelatedSearch(List<String> relatedSearch) {
		this.relatedSearch = relatedSearch;
	}

	public String getAppFlg() {
		return appFlg;
	}

	public void setAppFlg(String appFlg) {
		this.appFlg = appFlg;
	}

	public Integer getCityId() {
		if (cityId == null) {
			return 0;
		}
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public Integer getExpansionId() {
		return expansionId;
	}

	public void setExpansionId(Integer expansionId) {
		this.expansionId = expansionId;
	}

	public String getAioContent() {
		return aioContent;
	}

	public void setAioContent(String aioContent) {
		this.aioContent = aioContent;
	}

	public String getQuestions() {
		return questions;
	}

	public void setQuestions(String questions) {
		this.questions = questions;
	}

	public String getAnswerBox() {
		return answerBox;
	}

	public void setAnswerBox(String answerBox) {
		this.answerBox = answerBox;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public List<String> getDomainList() {
		return domainList;
	}

	public void setDomainList(List<String> domainList) {
		this.domainList = domainList;
	}

	public String getSearchVol() {
		if (NumberUtils.toInt(searchVol) < 0) {
			return "0";
		}
		return searchVol;
	}

	public void setSearchVol(String searchVol) {
		this.searchVol = searchVol;
	}

	public String getCpc() {
		return cpc;
	}

	public void setCpc(String cpc) {
		this.cpc = cpc;
	}

	public List<String> getLocalListing() {
		return localListing;
	}

	public void setLocalListing(List<String> localListing) {
		this.localListing = localListing;
	}

	@Deprecated
	public String getLlFlg() {
		return llFlg;
	}

	@Deprecated
	public void setLlFlg(String llFlg) {
		this.llFlg = llFlg;
	}

	public String getTopPpcCnt() {
		return topPpcCnt;
	}

	public void setTopPpcCnt(String topPpcCnt) {
		this.topPpcCnt = topPpcCnt;
	}

	public Integer getTopPPCCnt() {
		if (topPpcCnt == null) {
			return 0;
		}
		return NumberUtils.toInt(topPpcCnt);
	}

	public String getPlaFlg() {
		return plaFlg;
	}

	public void setPlaFlg(String plaFlg) {
		this.plaFlg = plaFlg;
	}

	public String getPpcFlg() {
		return ppcFlg;
	}

	public void setPpcFlg(String ppcFlg) {
		this.ppcFlg = ppcFlg;
	}

	public List<String> getInDepthArt() {
		return inDepthArt;
	}

	public void setInDepthArt(List<String> inDepthArt) {
		this.inDepthArt = inDepthArt;
	}

	public List<String> getThings() {
		return things;
	}

	public void setThings(List<String> things) {
		this.things = things;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public int getKeywordDictId() {
		return keywordDictId;
	}

	public void setKeywordDictId(int keywordDictId) {
		this.keywordDictId = keywordDictId;
	}

	public String getCountryAndLanguage() {
		return country + "_" + language;
	}

	public String getEngine() {
		return engine;
	}

	public void setEngine(String engine) {
		this.engine = engine;
	}

	public String getKeyword() {
		return keyword;
	}

	public String getKeywordForHtml() {
		try {
			return StringEscapeUtils
					.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(keyword, "UTF-8")));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getQueryDate() {
		return queryDate;
	}

	public void setQueryDate(String queryDate) {
		this.queryDate = queryDate;
	}

	public int getQeuryState() {
		return qeuryState;
	}

	public void setQeuryState(int qeuryState) {
		this.qeuryState = qeuryState;
	}

	public List<KeywordRankEntityVO> getKeywordRankEntityVOs() {
		return keywordRankEntityVOs;
	}

	public void setKeywordRankEntityVOs(List<KeywordRankEntityVO> keywordRankEntityVOs) {
		this.keywordRankEntityVOs = keywordRankEntityVOs;
	}

	public List<JobEntityVO> getJobEntityVOs() {
		return jobEntityVOs;
	}

	public void setJobEntityVOs(List<JobEntityVO> jobEntityVOs) {
		this.jobEntityVOs = jobEntityVOs;
	}

	public List<YoutubeEntity> getYoutubeEntityList() {
		return YoutubeEntityList;
	}

	public void setYoutubeEntityList(List<YoutubeEntity> youtubeEntityList) {
		YoutubeEntityList = youtubeEntityList;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public Integer getFirstPageSize() {
		if (firstPageSize == null || firstPageSize.intValue() == 0) {
			return 0;
		}
		return firstPageSize;
	}

	public void setFirstPageSize(Integer firstPageSize) {
		this.firstPageSize = firstPageSize;
	}

	public String getKnogTag() {
		return knogTag;
	}

	public void setKnogTag(String knogTag) {
		this.knogTag = knogTag;
	}

	public String getCreateDate() {
		if (createDate == null) {
			return "0";
		}
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public List<SeoKeywordAdsEntity> getAllAds() {
		return allAds;
	}

	public void setAllAds(List<SeoKeywordAdsEntity> allAds) {
		this.allAds = allAds;
	}
	
	public List<SeoKeywordPlaEntity> getPlaList() {
		return plaList;
	}

	public void setPlaList(List<SeoKeywordPlaEntity> plaList) {
		this.plaList = plaList;
	}

	@Override
	public String toString() {
		return "KeywordRankVO [keywordDictId=" + keywordDictId + ", country=" + country + ", language=" + language
				+ ", cityName=" + cityName + ", cityId=" + cityId + ", knogTag=" + knogTag + ", engine=" + engine
				+ ", keyword=" + keyword + ", queryDate=" + queryDate + ", qeuryState=" + qeuryState
				+ ", firstPageSize=" + firstPageSize + ", plaFlg=" + plaFlg + ", ppcFlg=" + ppcFlg + ", llFlg=" + llFlg
				+ ", topPpcCnt=" + topPpcCnt + ", createDate=" + createDate + ", id=" + id + ", appFlg=" + appFlg
				+ ", relatedSearch=" + relatedSearch + ", sendToQDate=" + sendToQDate + ", keywordRankEntityVOs="
				+ keywordRankEntityVOs + ", things=" + things + ", inDepthArt=" + inDepthArt + ", localListing="
				+ localListing + ", allAds=" + allAds + ", searchVol=" + searchVol + ", cpc=" + cpc + ", domainList="
				+ domainList + ", answerBox=" + answerBox + ", questions=" + questions + "]";
	}

	public String getGoogleRecommend() {
		return googleRecommend;
	}

	public void setGoogleRecommend(String googleRecommend) {
		this.googleRecommend = googleRecommend;
	}

	public Integer getFrequency() {
		return frequency;
	}

	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}

	public String getFlightSearchFlg() {
		return flightSearchFlg;
	}

	public void setFlightSearchFlg(String flightSearchFlg) {
		this.flightSearchFlg = flightSearchFlg;
	}

	public String getSocialInKg() {
		return socialInKg;
	}

	public void setSocialInKg(String socialInKg) {
		this.socialInKg = socialInKg;
	}

	public Integer getProjectId() {
		return projectId;
	}

	public void setProjectId(Integer projectId) {
		this.projectId = projectId;
	}

	public String getAdditionalQuestions() {
		return additionalQuestions;
	}

	public void setAdditionalQuestions(String additionalQuestions) {
		this.additionalQuestions = additionalQuestions;
	}

	public List<String> getAppList() {
		return appList;
	}

	public void setAppList(List<String> appList) {
		this.appList = appList;
	}

	public String getResearchFlg() {
		return researchFlg;
	}

	public void setResearchFlg(String researchFlg) {
		this.researchFlg = researchFlg;
	}

	public String getQuickLinksFlg() {
		return quickLinksFlg;
	}

	public void setQuickLinksFlg(String quickLinksFlg) {
		this.quickLinksFlg = quickLinksFlg;
	}

	public boolean isNotRealSearchVolume() {
		return notRealSearchVolume;
	}

	public void setNotRealSearchVolume(boolean notRealSearchVolume) {
		this.notRealSearchVolume = notRealSearchVolume;
	}

	public String getRefineBy() {
		return refineBy;
	}

	public void setRefineBy(String refineBy) {
		this.refineBy = refineBy;
	}

	public String getCommercialFlg() {
		return commercialFlg;
	}

	public void setCommercialFlg(String commercialFlg) {
		this.commercialFlg = commercialFlg;
	}

	public String getGoogleSearchInstead() {
		return googleSearchInstead;
	}

	public void setGoogleSearchInstead(String googleSearchInstead) {
		this.googleSearchInstead = googleSearchInstead;
	}

	public String getGoogleResultCnt() {
		return googleResultCnt;
	}

	public void setGoogleResultCnt(String googleResultCnt) {
		this.googleResultCnt = googleResultCnt;
	}

	public String getGoogleSearchInclude() {
		return googleSearchInclude;
	}

	public void setGoogleSearchInclude(String googleSearchInclude) {
		this.googleSearchInclude = googleSearchInclude;
	}

	public String getNotGreatMatch() {
		return notGreatMatch;
	}

	public void setNotGreatMatch(String notGreatMatch) {
		this.notGreatMatch = notGreatMatch;
	}

	public String getClarityDBKeywordHash() {
		return clarityDBKeywordHash;
	}

	public void setClarityDBKeywordHash(String clarityDBKeywordHash) {
		this.clarityDBKeywordHash = clarityDBKeywordHash;
	}

	public String getRightFreeShop() {
		return rightFreeShop;
	}

	public void setRightFreeShop(String rightFreeShop) {
		this.rightFreeShop = rightFreeShop;
	}

	public List<String> getTriggerCodes() {
		return triggerCodes;
	}

	public void setTriggerCodes(List<String> triggerCodes) {
		this.triggerCodes = triggerCodes;
	}

	public List<String> getLinksInKnog() {
		return linksInKnog;
	}

	public void setLinksInKnog(List<String> linksInKnog) {
		this.linksInKnog = linksInKnog;
	}

	public List<String> getVedInfo() {
		return vedInfo;
	}

	public void setVedInfo(List<String> vedInfo) {
		this.vedInfo = vedInfo;
	}

	public List<KeywordSubRankEntityVO> getProductList() {
		return productList;
	}

	public void setProductList(List<KeywordSubRankEntityVO> productList) {
		this.productList = productList;
	}

	public List<KeywordRankLocalListingSubEntityVO> getLocalListingV2() {
		return localListingV2;
	}

	public void setLocalListingV2(List<KeywordRankLocalListingSubEntityVO> localListingV2) {
		this.localListingV2 = localListingV2;
	}

	public List<String> getTriggerClasses() {
		return triggerClasses;
	}

	public void setTriggerClasses(List<String> triggerClasses) {
		this.triggerClasses = triggerClasses;
	}

	public Integer getNearYou() {
		return nearYou;
	}

	public void setNearYou(Integer nearYou) {
		this.nearYou = nearYou;
	}

	public List<String> getSuggestList() {
		return suggestList;
	}

	public void setSuggestList(List<String> suggestList) {
		this.suggestList = suggestList;
	}

	public boolean isFuleCostCal() {
		return fuleCostCal;
	}

	public void setFuleCostCal(boolean fuleCostCal) {
		this.fuleCostCal = fuleCostCal;
	}

	public Integer getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(Integer searchEngine) {
		this.searchEngine = searchEngine;
	}

	public Integer getSearchLanguage() {
		return searchLanguage;
	}

	public void setSearchLanguage(Integer searchLanguage) {
		this.searchLanguage = searchLanguage;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public List<AppbarKeyword> getAppbarKeywords() {
		return appbarKeywords;
	}

	public void setAppbarKeywords(List<AppbarKeyword> appbarKeywords) {
		this.appbarKeywords = appbarKeywords;
	}
	
	public List<PixelRankEntityVO> getPixelRankList() {
		return pixelRankList;
	}

	public void setPixelRankList(List<PixelRankEntityVO> pixelRankList) {
		this.pixelRankList = pixelRankList;
	}
	
	public List<String> getKgEntityValues() {
		return kgEntityValues;
	}

	public void setKgEntityValues(List<String> kgEntityValues) {
		this.kgEntityValues = kgEntityValues;
	}

	public Boolean getMediaApp() {
		return mediaApp;
	}

	public void setMediaApp(Boolean mediaApp) {
		this.mediaApp = mediaApp;
	}

	public Map<String, String> getBuyingGuides() {
		return buyingGuides;
	}

	public void setBuyingGuides(Map<String, String> buyingGuides) {
		this.buyingGuides = buyingGuides;
	}

	public List<KeywordRankSubEntityVOs.ThingsToKnowEntity> getThingsToKnow() {
		return thingsToKnow;
	}

	public void setThingsToKnow(List<KeywordRankSubEntityVOs.ThingsToKnowEntity> thingsToKnow) {
		this.thingsToKnow = thingsToKnow;
	}

	public String getDiscussionsAndForums() {
		return discussionsAndForums;
	}

	public void setDiscussionsAndForums(String discussionsAndForums) {
		this.discussionsAndForums = discussionsAndForums;
	}

//	public List<String> getDiscussionsAndForumsList() {
//		return discussionsAndForumsList;
//	}
//
//	public void setDiscussionsAndForumsList(List<String> discussionsAndForumsList) {
//		this.discussionsAndForumsList = discussionsAndForumsList;
//	}


	public List<DiscussionsEntity> getDiscussionsEntities() {
		return discussionsEntities;
	}

	public void setDiscussionsEntities(List<DiscussionsEntity> discussionsEntities) {
		this.discussionsEntities = discussionsEntities;
	}

	public String getExploreBrandsFlg() {
		return exploreBrandsFlg;
	}

	public void setExploreBrandsFlg(String exploreBrandsFlg) {
		this.exploreBrandsFlg = exploreBrandsFlg;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public List<KeywordRankLocalListingSubEntityVO> getHotelListing() {
		return hotelListing;
	}

	public void setHotelListing(List<KeywordRankLocalListingSubEntityVO> hotelListing) {
		this.hotelListing = hotelListing;
	}

	public String getHotelFlg() {
		return hotelFlg;
	}

	public void setHotelFlg(String hotelFlg) {
		this.hotelFlg = hotelFlg;
	}

	public List<KeywordRankSubEntityVOs.AIOGeneratedCarousalEntity> getAiGeneratedCarousal() {
		return aiGeneratedCarousal;
	}

	public void setAiGeneratedCarousal(List<KeywordRankSubEntityVOs.AIOGeneratedCarousalEntity> aiGeneratedCarousal) {
		this.aiGeneratedCarousal = aiGeneratedCarousal;
	}

	public List<KeywordRankSubEntityVOs.AIOGeneratedContentEntity> getAiGeneratedContent() {
		return aiGeneratedContent;
	}

	public void setAiGeneratedContent(List<KeywordRankSubEntityVOs.AIOGeneratedContentEntity> aiGeneratedContent) {
		this.aiGeneratedContent = aiGeneratedContent;
	}

	public String getOssKeyPath() {
		return ossKeyPath;
	}

	public void setOssKeyPath(String ossKeyPath) {
		this.ossKeyPath = ossKeyPath;
	}

	public Boolean getAiOverview() {
		return aiOverview;
	}

	public void setAiOverview(Boolean aiOverview) {
		this.aiOverview = aiOverview;
	}

	public String getAioType() {
		return aioType;
	}

	public void setAioType(String aioType) {
		this.aioType = aioType;
	}

	public Boolean getErrorKeyword() {
		return errorKeyword;
	}

	public void setErrorKeyword(Boolean errorKeyword) {
		this.errorKeyword = errorKeyword;
	}

	public String getAdhocHourlyKeywordKey() {
		return adhocHourlyKeywordKey;
	}

	public void setAdhocHourlyKeywordKey(String adhocHourlyKeywordKey) {
		this.adhocHourlyKeywordKey = adhocHourlyKeywordKey;
	}

	public Boolean getAiSales() {
		return aiSales;
	}

	public void setAiSales(Boolean aiSales) {
		this.aiSales = aiSales;
	}

	public Boolean getPsnFlg() {
		return psnFlg;
	}

	public void setPsnFlg(Boolean psnFlg) {
		this.psnFlg = psnFlg;
	}

	public List<PSNVo> getPsnList() {
		return psnList;
	}

	public void setPsnList(List<PSNVo> psnList) {
		this.psnList = psnList;
	}

	public Boolean getPerspectiveFlg() {
		return perspectiveFlg;
	}

	public void setPerspectiveFlg(Boolean perspectiveFlg) {
		this.perspectiveFlg = perspectiveFlg;
	}

	public Boolean getPeopleSuggestFlg() {
		return peopleSuggestFlg;
	}

	public void setPeopleSuggestFlg(Boolean peopleSuggestFlg) {
		this.peopleSuggestFlg = peopleSuggestFlg;
	}

	public Integer getPlaType() {
		return plaType;
	}

	public void setPlaType(Integer plaType) {
		this.plaType = plaType;
	}
	//============================================= GET SET END ====================================================//

	public class AppbarKeyword {
		private String displayTxt;
		private String searchTxt;

		public String getDisplayTxt() {
			return displayTxt;
		}

		public void setDisplayTxt(String displayTxt) {
			this.displayTxt = displayTxt;
		}

		public String getSearchTxt() {
			return searchTxt;
		}

		public void setSearchTxt(String searchTxt) {
			this.searchTxt = searchTxt;
		}
	}

	public Long getParseCost() {
		return parseCost;
	}

	public void setParseCost(Long parseCost) {
		this.parseCost = parseCost;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getQueueName() {
		return queueName;
	}

	public void setQueueName(String queueName) {
		this.queueName = queueName;
	}

	@Data
	public static class PSNVo {
		private String link;
		private String title;
	}

}
