package seoclarity.backend.export.poprank;

public class SeoKeywordPlaEntity {
	private String pos;
	private Integer rank;
	private String landingPage;
	private String label;
	private String brandName;
	private String price;
	private String rating;
	private String ratingNumber;
	
	private Integer freeShippingFlg;
	private Integer saleFlg;
	private Integer curbsideFlg;
	private Integer pickUpFlg;
	private Integer inStoreFlg;

	private String merchantId;
	private String dtld;

	public String getPos() {
		return pos;
	}

	public void setPos(String pos) {
		this.pos = pos;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public String getLandingPage() {
		return landingPage;
	}

	public void setLandingPage(String landingPage) {
		this.landingPage = landingPage;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public String getRatingNumber() {
		return ratingNumber;
	}

	public void setRatingNumber(String ratingNumber) {
		this.ratingNumber = ratingNumber;
	}

	public Integer getFreeShippingFlg() {
		return freeShippingFlg;
	}

	public void setFreeShippingFlg(Integer freeShippingFlg) {
		this.freeShippingFlg = freeShippingFlg;
	}

	public Integer getSaleFlg() {
		return saleFlg;
	}

	public void setSaleFlg(Integer saleFlg) {
		this.saleFlg = saleFlg;
	}

	public Integer getCurbsideFlg() {
		return curbsideFlg;
	}

	public void setCurbsideFlg(Integer curbsideFlg) {
		this.curbsideFlg = curbsideFlg;
	}

	public Integer getPickUpFlg() {
		return pickUpFlg;
	}

	public void setPickUpFlg(Integer pickUpFlg) {
		this.pickUpFlg = pickUpFlg;
	}

	public Integer getInStoreFlg() {
		return inStoreFlg;
	}

	public void setInStoreFlg(Integer inStoreFlg) {
		this.inStoreFlg = inStoreFlg;
	}

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	public String getDtld() {
		return dtld;
	}

	public void setDtld(String dtld) {
		this.dtld = dtld;
	}
}