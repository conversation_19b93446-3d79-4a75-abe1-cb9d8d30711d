package seoclarity.backend.export.poprank;

public class SeoKeywordAdsEntity {
    private Integer id;
    private String pos;
    private Integer rank;
    private String anchorText;
    private String dispalyUrl;
    private String desc;
    private String siteLink;
    private String landingPage;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPos() {
        return pos;
    }

    public void setPos(String pos) {
        this.pos = pos;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getAnchorText() {
        return anchorText;
    }

    public void setAnchorText(String anchorText) {
        this.anchorText = anchorText;
    }

    public String getDispalyUrl() {
        return dispalyUrl;
    }

    public void setDispalyUrl(String dispalyUrl) {
        this.dispalyUrl = dispalyUrl;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getSiteLink() {
        return siteLink;
    }

    public void setSiteLink(String siteLink) {
        this.siteLink = siteLink;
    }

    public String getLandingPage() {
        return landingPage;
    }

    public void setLandingPage(String landingPage) {
        this.landingPage = landingPage;
    }

    @Override
    public String toString() {
        return "SeoKeywordAdsEntity [id=" + id + ", pos=" + pos + ", rank=" + rank + ", anchorText=" + anchorText
                + ", dispalyUrl=" + dispalyUrl + ", desc=" + desc + "]";
    }
}
