package seoclarity.backend.export.poprank;

public class RankKafkaMsgKeyHandler {

    public static final int FREQ_DAILY = 1;

    public static KeyModel parseKafkaKey(String key) {
        String[] keyArr = key.split("_");
        int engine = Integer.parseInt(keyArr[0]);
        int language = Integer.parseInt(keyArr[1]);
        String device = keyArr[2];
        int frequency = Integer.parseInt(keyArr[3]);
        int cityId = Integer.parseInt(keyArr[4]);
        return new KeyModel(key, engine, language, device, frequency, cityId);
    }

    public static String getKeyGroup(String key) {
        KeyModel keyModel = parseKafkaKey(key);
        return keyModel.getEngine() + "_" + keyModel.getLanguage() + "_" + keyModel.getDevice() + "_" + keyModel.getFrequency() + "_" + keyModel.getCityId();
    }

    static public class KeyModel {
        String key;
        int engine;
        int language;
        String device;
        int frequency;
        int cityId;
        int type;

        public KeyModel(){

        }

        public KeyModel(String key, int engine, int language, String device, int frequency, int cityId) {
            this.key = key;
            this.engine = engine;
            this.language = language;
            this.device = device;
            this.frequency = frequency;
            this.cityId = cityId;
            this.type = type;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public int getEngine() {
            return engine;
        }

        public void setEngine(int engine) {
            this.engine = engine;
        }

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getDevice() {
            return device;
        }

        public void setDevice(String device) {
            this.device = device;
        }

        public int getFrequency() {
            return frequency;
        }

        public void setFrequency(int frequency) {
            this.frequency = frequency;
        }

        public int getCityId() {
            return cityId;
        }

        public void setCityId(int cityId) {
            this.cityId = cityId;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public boolean isNationalKeyword() {
            return cityId == 0;
        }
    }

}
