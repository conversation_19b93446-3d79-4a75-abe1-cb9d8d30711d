package seoclarity.backend.export.poprank;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-02-03
 * @path seoclarity.actonia_competitor_summary.entity.KeywordRankSubEntityVOs
 * contains some sub entity vo
 */
public class KeywordRankSubEntityVOs {
    @Deprecated
    public static final int VIRTUAL_RANK_FOR_AIGENAISEARCHLINK = 201;
    public static final int VIRTUAL_RANK_FOR_AIO_CAROUSAL = 202;
    public static final int VIRTUAL_RANK_FOR_AIO_CONTENT = 203;

    public class RightFreeShopEntity {
        private String domain;
        private String link;

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

    }

    public class ThingsToKnowEntity {
        private String label;
        private List<String> links;

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public List<String> getLinks() {
            return links;
        }

        public void setLinks(List<String> links) {
            this.links = links;
        }
    }

//    @Deprecated
//    public class AiGenaiSearchLinkEntity {
//        private String label;
//        private String metaDesc;
//        private String landingPage;
//        private int rank;
//
//        public String getLabel() {
//            return label;
//        }
//
//        public void setLabel(String label) {
//            this.label = label;
//        }
//
//        public String getMetaDesc() {
//            return metaDesc;
//        }
//
//        public void setMetaDesc(String metaDesc) {
//            this.metaDesc = metaDesc;
//        }
//
//        public String getLandingPage() {
//            return landingPage;
//        }
//
//        public void setLandingPage(String landingPage) {
//            this.landingPage = landingPage;
//        }
//
//        public int getRank() {
//            return rank;
//        }
//
//        public void setRank(int rank) {
//            this.rank = rank;
//        }
//    }

    public class AIOGeneratedCarousalEntity {
        private String label;
        private String link;
        private int rank;

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public int getRank() {
            return rank;
        }

        public void setRank(int rank) {
            this.rank = rank;
        }
    }

    public class AIOGeneratedContentEntity {
        private String label;
        private Boolean hidden;
        private String link;

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public Boolean getHidden() {
            return hidden;
        }

        public void setHidden(Boolean hidden) {
            this.hidden = hidden;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }
    }

}
