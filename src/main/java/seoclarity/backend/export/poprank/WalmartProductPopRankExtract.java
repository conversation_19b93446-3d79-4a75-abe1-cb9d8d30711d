package seoclarity.backend.export.poprank;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.actonia.cloudvo.PopRankEntityVO;
import seoclarity.backend.entity.actonia.extract.rankingextractjson.*;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.kafka.clientconstructor.consumer.RankCustomConsumerConstructor;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.utils.*;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.text.ParseException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 每次处理当前周的数据, 文件名日期为当前周的起始时间.
 * 无需考虑旧文件, 可以直接重跑
 */
@CommonsLog
public class WalmartProductPopRankExtract {


    private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;

    public WalmartProductPopRankExtract() {
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
    }

    Map<String, EngineCountryLanguageMappingEntity> engineCountryLanguageMappingEntityMap = new HashMap<>();

    public static void main(String[] args) {
        new WalmartProductPopRankExtract().process();
    }

    private void process() {
        List<String> topics = Arrays.asList("productpop_7_us_d_nat");
        String consumerGroupId = "poprank_consumer_group_r3"; // TODO: 2025/5/30
        Consumer<String, String> consumer = RankCustomConsumerConstructor.getInstance().buildKafkaConsumerWithTopicsAndGroup(topics, consumerGroupId);
        Gson gson = new Gson();
        Gson gsonForExtract = new GsonBuilder().disableHtmlEscaping().create();

        Date weekFirstDay = getWeekFirstDay(new Date());

        // TODO: 2025/6/3 选一
        String dateStr = DateFormatUtils.format(weekFirstDay, "yyyyMMdd");
        String fileName = "/home/<USER>/popranks/poprank_json_full_" + dateStr + ".txt";
//        String fileName = "/home/<USER>/popranks/poprank_json_full_20250603-test.txt";

        File file = new File(fileName);
        List<KeywordRankVO> keywordRankVOList = new ArrayList<>();

        if (file.length() > 0) {
            try {
                clearFile(fileName);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        int count = 0;
        Set<Integer> kwIdSet = new HashSet<>();

        int x = 0;
        wh: while (true) {
            ConsumerRecords<String, String> consumerRecords;
            try {
                consumerRecords = consumer.poll(Duration.ofMillis(2000));
                int msgCnt = consumerRecords.count();
                System.out.println("pulled " + consumerRecords.count() + " records~, last click cnt: " + count);

                if (msgCnt > 0) {
                    for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
                        String htmlStr = consumerRecord.value();
                        KeywordRankVO vo = gson.fromJson(htmlStr, KeywordRankVO.class);
                        Integer keywordId = vo.getId();
                        if (!kwIdSet.contains(keywordId)) {
                            kwIdSet.add(keywordId);
                            keywordRankVOList.add(vo);

                            List<KeywordRankEntityVO> keywordRankEntityVOs = vo.getKeywordRankEntityVOs();
                            if (keywordRankEntityVOs != null && !keywordRankEntityVOs.isEmpty()) {
                                for (KeywordRankEntityVO keywordRankEntityVO : keywordRankEntityVOs) {
                                    List<KeywordSubRankEntityVO> subRankVOs = keywordRankEntityVO.getSubRankVOs();
                                    if (subRankVOs != null && !subRankVOs.isEmpty()) {
                                        for (KeywordSubRankEntityVO subRankVO : subRankVOs) {
                                            if (subRankVO.getClickPop() != null && subRankVO.getClickPop()) {
                                                count++;
                                            }
                                        }
                                    }
                                }
                            }

                            if (keywordRankVOList.size() >= 1500) { // TODO: 2025/5/22  300
                                exportJsonLines(keywordRankVOList, gsonForExtract, file);
                                keywordRankVOList.clear();
//                                break wh; // TODO: 2025/5/22 For Test
                            }
                        }
                    }
                    x = 0;
                    consumer.commitAsync();
                } else {
                    if (x++ > 20) {
                        break;
                    }
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (!keywordRankVOList.isEmpty()) {
            exportJsonLines(keywordRankVOList, gsonForExtract, file);
        }
        consumer.close();

        System.out.println("distinct kw size: " + kwIdSet.size() + ", total click count: " + count);
        System.out.println("Start zip file ... ");

        String zipFileName = fileName + ".zip";

        ZipFileUtils.zipFile(zipFileName, Collections.singletonList(file));

        FTPUtils.saveFileToFTP(11818, zipFileName, "/home/<USER>/11818/"); // TODO: 2025/5/30  file  path / domain id
        SeagateUtils.saveFileToDefaultSeagate(SeagateUtils.SEAGATE_PRODUCT_POP_RANK_BUCKET_NAME, 11818, zipFileName); // TODO: 2025/5/30  file  path / domain id
        System.out.println("saved zip file: " + zipFileName + " to ftp!");
    }

    public void clearFile(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        Files.newBufferedWriter(path, StandardCharsets.UTF_8, StandardOpenOption.TRUNCATE_EXISTING).close();
    }

    public Date getWeekFirstDay(Date processDate) {
        LocalDate localDate = processDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        int value = localDate.getDayOfWeek().getValue();
        int minusValue;
        if (value == 7) {
            minusValue = 0;
        } else {
            minusValue = value;
        }
        return DateUtils.addDays(processDate, -minusValue);
    }

    private void exportJsonLines(List<KeywordRankVO> keywordRankVOList, Gson gsonForExtract, File file){
        List<RankExtractJsonVO> rankExtractJsonVOList = extractLines(keywordRankVOList);
        List<String> lines = new ArrayList<>();
        for (RankExtractJsonVO rankExtractJsonVO : rankExtractJsonVOList) {
            String json = gsonForExtract.toJson(rankExtractJsonVO);
            lines.add(json);
        }
        try {
            FileUtils.writeLines(file, "UTF-8", lines, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, EngineCountryLanguageMappingEntity> getEngineCountryLanguageMapping() {
        if (engineCountryLanguageMappingEntityMap.isEmpty()) {
            List<EngineCountryLanguageMappingEntity> all = engineCountryLanguageMappingEntityDAO.getAll();
            engineCountryLanguageMappingEntityMap = all.stream().collect(Collectors.toMap(var1 -> var1.getEngineId() + "!_!" + var1.getLanguageId(), var2->var2, (v3,v4)->v3));
        }
        return engineCountryLanguageMappingEntityMap;
    }

    private List<RankExtractJsonVO> extractLines(List<KeywordRankVO> keywordRankVOList) {
        List<RankExtractJsonVO> rankExtractJsonVOList = new ArrayList<>();
        for (KeywordRankVO keywordRankVO : keywordRankVOList) {

            RankExtractJsonVO rankExtractJsonVO = new RankExtractJsonVO();
            RankExtractJsonInfo extractJsonInfo = new RankExtractJsonInfo();

            Date sendToQDate = null;
            try {
                sendToQDate = DateUtils.parseDate(String.valueOf(keywordRankVO.getSendToQDate()), "yyyyMMdd");
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            extractJsonInfo.setDate(DateFormatUtils.format(sendToQDate, "yyyy-MM-dd"));
            extractJsonInfo.setStatus("success");

            RankExtractJsonSearchParameters rankExtractJsonSearchParameters = new RankExtractJsonSearchParameters();
            rankExtractJsonSearchParameters.setId(keywordRankVO.getId().longValue());

            String engineLanguageKey = keywordRankVO.getSearchEngine() + "!_!" + keywordRankVO.getSearchLanguage();

            EngineCountryLanguageMappingEntity engineCountryLanguageMappingEntity = getEngineCountryLanguageMapping().get(engineLanguageKey);

            rankExtractJsonSearchParameters.setSearchEngine(engineCountryLanguageMappingEntity.getEngineDisplayName());
            rankExtractJsonSearchParameters.setLanguage(engineCountryLanguageMappingEntity.getLanguageQueryName());
            rankExtractJsonSearchParameters.setCountry(engineCountryLanguageMappingEntity.getCountryQueryName());
            rankExtractJsonSearchParameters.setDevice(keywordRankVO.getDeviceType().equals("d") ? "desktop" : "mobile");
            rankExtractJsonSearchParameters.setKeyword(keywordRankVO.getKeyword());
            rankExtractJsonSearchParameters.setLocation("National");

            RankExtractJsonSearchInfo rankExtractJsonSearchInfo = new RankExtractJsonSearchInfo();
            rankExtractJsonSearchInfo.setCpc(Float.parseFloat(keywordRankVO.getCpc()));
            rankExtractJsonSearchInfo.setSearchVolume(Long.parseLong(keywordRankVO.getSearchVol()));
//            rankExtractJsonSearchInfo.setTrueDemand(); // TODO: 2025/5/22
//            if(StringUtils.isNotBlank(info.getGoogleRecommend())){
//                rankExtractJsonSearchInfo.setGoogleRecommend(info.getGoogleRecommend());
//            }

//            String serpFilterButtonTextStr = info.getSerpFilterButtonTextStr();
//            String serpFilterKeywordsStr = info.getSerpFilterKeywordsStr();
//            if(StringUtils.isNotBlank(serpFilterButtonTextStr) && !serpFilterButtonTextStr.equals("-")){
//                String[] arr = serpFilterButtonTextStr.split("!_!");
//                List<String> serpFilterButtonTextList = Arrays.stream(arr).collect(Collectors.toList());
//                rankExtractJsonSearchInfo.setSerpFilterButtonText(serpFilterButtonTextList);
//            }
//            if(StringUtils.isNotBlank(serpFilterKeywordsStr) && !serpFilterKeywordsStr.equals("-")){
//                String[] arr = serpFilterKeywordsStr.split("!_!");
//                List<String> serpFilterKeywordsList = Arrays.stream(arr).collect(Collectors.toList());
//                rankExtractJsonSearchInfo.setSerpFilterKeywords(serpFilterKeywordsList);
//            }

            //https://www.wrike.com/open.htm?id=1593439741
//            rankExtractJsonSearchInfo.setKeywordDateAdded(info.getCreateDate());
//            if(CollectionUtils.isNotEmpty(info.getTagList())){
//                rankExtractJsonSearchInfo.setKeywordTags(info.getTagList());
//            }

//            if(CollectionUtils.isNotEmpty(info.getTagIdHierarchy())){
//                List<String> tagHierarchyList = new ArrayList<>();
//                for (List<Integer> tagTree: info.getTagIdHierarchy()){
//                    String tagHierarchy = StringUtils.join(tagTree.stream().map(x->tagMap.get(x)).collect(Collectors.toList()), "->");
////                log.info("======tagHierarchy: " + tagHierarchy);
//                    if(StringUtils.isNotBlank(tagHierarchy)){
//                        tagHierarchyList.add(tagHierarchy);
//                    }
//                }
//                if(CollectionUtils.isNotEmpty(tagHierarchyList)){
//                    rankExtractJsonSearchInfo.setTagHierarchy(tagHierarchyList);
//                }
//            }

//            if(CollectionUtils.isNotEmpty(info.getPlpUrlList())){
//                rankExtractJsonSearchInfo.setPreferredPages(info.getPlpUrlList());
//            }
//            rankExtractJsonSearchInfo.setEstdTraffic(info.getEstdTraffic());
//            rankExtractJsonSearchInfo.setShareOfVoice(info.getShareOfVoice());
//            rankExtractJsonSearchInfo.setShareOfMarket(info.getShareOfMarket());

            List<String> relatedSearch = (keywordRankVO.getRelatedSearch() == null || keywordRankVO.getRelatedSearch().size() == 0) ? null : keywordRankVO.getRelatedSearch().stream().filter(StringUtils::isNotBlank).map(StringUtils::trim).collect(Collectors.toList());

            // https://www.wrike.com/open.htm?id=1500665126
            if (CollectionUtils.isNotEmpty(relatedSearch)) {
                rankExtractJsonSearchInfo.setRelatedSearches(relatedSearch); // TODO: 2025/5/22 确认
            }

//            RankExtractJsonSerpFeaturesFlag rankExtractJsonSerpFeaturesFlag = new RankExtractJsonSerpFeaturesFlag();
//            rankExtractJsonSerpFeaturesFlag.setApp(info.getAppFlg());
//            rankExtractJsonSerpFeaturesFlag.setImg(info.getImgFlg());
//            rankExtractJsonSerpFeaturesFlag.setNews(info.getNewsFlg());
//            rankExtractJsonSerpFeaturesFlag.setVideo(info.getVideoFlg());
//            rankExtractJsonSerpFeaturesFlag.setLocalListing(info.getLlFlg());
//            rankExtractJsonSerpFeaturesFlag.setPpcAds(info.getPpcFlg());
//            rankExtractJsonSerpFeaturesFlag.setAnswerBox(info.getAnswerBoxFlg());
//            rankExtractJsonSerpFeaturesFlag.setHotel(info.getHotelFlag());
//            rankExtractJsonSerpFeaturesFlag.setFlights(info.getFlightSearchFlg());
//            rankExtractJsonSerpFeaturesFlag.setPla(info.getPla_flg());
//            rankExtractJsonSerpFeaturesFlag.setKnowledgeGraph(info.getKnog_flg());
//            rankExtractJsonSerpFeaturesFlag.setFromsourcesacrosstheweb(info.getFromsourcesacrossthewebFlag());
//            rankExtractJsonSerpFeaturesFlag.setFindresultson(info.getFindresultsonFlag());
//            rankExtractJsonSerpFeaturesFlag.setPopulardestinations(info.getPopulardestinationsFlag());
//            rankExtractJsonSerpFeaturesFlag.setAio(info.getAioFlag());
//            rankExtractJsonSerpFeaturesFlag.setPopularRecipes(info.getPopularRecipesFlag());
//            rankExtractJsonSerpFeaturesFlag.setPopularStore(info.getPopularStoreFlag());
//            rankExtractJsonSerpFeaturesFlag.setDiscussionsandforums(info.getDiscussionsandforumsFlag());
//            rankExtractJsonSerpFeaturesFlag.setBuyingguide(info.getBuyingguideFlag());

            List<RankExtractJsonPeopleAlsoAsk> rankExtractJsonPeopleAlsoAskList = new ArrayList<>();
            String additionalQuestions = keywordRankVO.getAdditionalQuestions();
            if (additionalQuestions != null && !StringUtils.isEmpty(additionalQuestions)) {
                String[] split = additionalQuestions.split("@_@");
                for(String paaStr: split){
                    String[] paaArr =  paaStr.split("!_!");
                    if(paaArr.length < 2){
//                    log.info("*****************paaArr:" + gson.toJson(paaArr));
                    }else {
                        RankExtractJsonPeopleAlsoAsk extractJsonPeopleAlsoAsk = new RankExtractJsonPeopleAlsoAsk();
                        extractJsonPeopleAlsoAsk.setUrl(paaArr[0]);
                        extractJsonPeopleAlsoAsk.setTitle(paaArr[1]);
                        rankExtractJsonPeopleAlsoAskList.add(extractJsonPeopleAlsoAsk);
                    }
                }
            }

            //=============== thingsToKnow ====================
            String thingsToKnowValues = null;
            if (keywordRankVO.getThingsToKnow() != null && !keywordRankVO.getThingsToKnow().isEmpty()) {
                thingsToKnowValues = keywordRankVO.getThingsToKnow().stream().map(vo -> {
                    String label = vo.getLabel();
                    String urls = StringUtils.join(vo.getLinks(), "!_!");
                    return label + "#_#" + urls;
                }).collect(Collectors.joining("@_@"));
            }
            List<RankExtractJsonThingsToKnow> thingsToKnowList = new ArrayList<RankExtractJsonThingsToKnow>();
            if(StringUtils.isNotBlank(thingsToKnowValues) && StringUtils.contains(thingsToKnowValues, "#_#")) {
                String[] qArr = thingsToKnowValues.split("@_@");
                if(qArr.length > 0) {
                    Integer rank = 1;
                    for (String s : qArr) {
                        String[] tArr = s.split("#_#");
                        if(tArr.length >= 2) {
                            RankExtractJsonThingsToKnow rankExtractJsonThingsToKnow = new RankExtractJsonThingsToKnow();
                            rankExtractJsonThingsToKnow.setTitle(getStringValue(tArr[0]));
                            rankExtractJsonThingsToKnow.setUrl(StringUtils.substringBefore(getStringValue(tArr[1]), "#"));
                            rankExtractJsonThingsToKnow.setRank(rank);

                            rank++;
                            thingsToKnowList.add(rankExtractJsonThingsToKnow);
                        }
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(thingsToKnowList)){
                rankExtractJsonVO.setThingsToKnow(thingsToKnowList);
            }

//            if(StringUtils.isNotBlank(info.getRefineByTitle()) ||
//                    CollectionUtils.isNotEmpty(info.getRefineByDetails())){
//                RankExtractJsonRefineBy rankExtractJsonRefineBy = new RankExtractJsonRefineBy();
//                rankExtractJsonRefineBy.setTitle(info.getRefineByTitle());
//                rankExtractJsonRefineBy.setDetail(info.getRefineByDetails());
//                rankExtractJsonVO.setRefineBy(rankExtractJsonRefineBy);
//            }

            List<RankExtractJsonRankings> rankExtractJsonRankingsList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(keywordRankVO.getKeywordRankEntityVOs())){
                int webRankingCount = 0;

                for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {

                    RankExtractJsonRankings rankExtractJsonRankings = new RankExtractJsonRankings();
                    String parsedUrl = parseUrl(keywordRankEntityVO.getLandingPage(), keywordRankVO.getSearchEngine(), keywordRankEntityVO.getAuthorName());
                    rankExtractJsonRankings.setUrl(StringUtils.isBlank(parsedUrl) ? "" : ExtractService.formatGoogleUrl(parsedUrl));
                    rankExtractJsonRankings.setType(RankTypeManager.getUrlTypeName(keywordRankEntityVO.getType()));
                    int trueRank = 0;
                    if (keywordRankEntityVO.getRank() == 0) {
                        trueRank = 101;
                    } else {
                        trueRank = keywordRankEntityVO.getRank();
                    }

                    boolean isUniversalRank = isUniversalUrl(parsedUrl, keywordRankEntityVO.getType());

                    int webRankFromJson = 0;
                    if (!isUniversalRank) {
                        webRankFromJson = keywordRankEntityVO.getRank() - webRankingCount;
                    } else {
                        webRankingCount++;
                    }
                    if (isGoogleShoppingImageRanking(keywordRankVO.getSearchEngine())) {
                        webRankFromJson = keywordRankEntityVO.getRank();
                    }

                    int webRank = 0;
                    if (webRankFromJson == 0) {
                        webRank = 101;
                    } else {
                        webRank = webRankFromJson;
                    }
                    rankExtractJsonRankings.setTrueRank(trueRank);
                    if(webRank != 101){
                        rankExtractJsonRankings.setWebRank(webRank);
                    }

//                    if(detailRank.getVisualRank() != null && detailRank.getVisualRank().intValue() != 0){
//                        rankExtractJsonRankings.setVisualRank(detailRank.getVisualRank());
//                    }
//                    if(detailRank.getPixelDepth() != null){
//                        rankExtractJsonRankings.setPixelDepth(detailRank.getPixelDepth());
//                    }

                    String title = StringUtils.isBlank(keywordRankEntityVO.getLabel()) ? "-" : CommonUtils.formatTitleMeta(keywordRankEntityVO.getLabel());
                    String meta = StringUtils.isBlank(keywordRankEntityVO.getMetaDesc()) ? "-" : CommonUtils.formatTitleMeta(keywordRankEntityVO.getMetaDesc());

                    RankExtractJsonUrlInformation rankExtractJsonUrlInformation = new RankExtractJsonUrlInformation();
                    if(StringUtils.isNotBlank(title) && !title.equals("-")){
                        rankExtractJsonUrlInformation.setTitle(title);
                    }
                    if(StringUtils.isNotBlank(meta) && !meta.equals("-")){
                        rankExtractJsonUrlInformation.setMeta(meta);
                    }

                    if(StringUtils.isNotBlank(keywordRankEntityVO.getActualPrice())){
                        rankExtractJsonUrlInformation.setPriceFlag("y");
                    }else {
                        rankExtractJsonUrlInformation.setPriceFlag("n");
                    }
                    if(convertValue(keywordRankEntityVO.getRating()) != 0){
                        rankExtractJsonUrlInformation.setRatingFlag("y");
                    }else {
                        rankExtractJsonUrlInformation.setRatingFlag("n");
                    }

                    boolean hasCouponFlg = keywordRankEntityVO.getCouponFlg() != null && keywordRankEntityVO.getCouponFlg();

                    rankExtractJsonUrlInformation.setCouponFlag(hasCouponFlg ? "y" : "n");
                    rankExtractJsonRankings.setUrlInformation(rankExtractJsonUrlInformation);


                    List<KeywordSubRankEntityVO> subRankVOs = keywordRankEntityVO.getSubRankVOs();
                    //subranks
                    if(CollectionUtils.isNotEmpty(subRankVOs)){
                        List<RankExtractJsonSubRanks> subRanksList = new ArrayList<>();

                        int subRank = 1;
                        for (KeywordSubRankEntityVO subRankVO : subRankVOs) {

                            int subTrueRank = keywordRankEntityVO.getRank();
                            if(trueRank != subTrueRank){
                                continue;
                            }
                            RankExtractJsonSubRanks rankExtractJsonSubRanks = new RankExtractJsonSubRanks();
                            rankExtractJsonSubRanks.setSubrank(subRank++);
                            String subRankUrl = StringUtils.isBlank(subRankVO.getLandingPage()) ? "" : ExtractService.formatGoogleUrl(subRankVO.getLandingPage());
                            if(StringUtils.isNotBlank(subRankUrl)){
                                rankExtractJsonSubRanks.setUrl(subRankUrl);
                            }

                            String label = subRankVO.getLabel();
                            String subTitle = StringUtils.isBlank(label) ? "-" : CommonUtils.formatTitleMeta(label);
//                            String subMeta = StringUtils.isBlank(subrank.getSubRankMeta()) ? "-" : CommonUtils.formatTitleMeta(subrank.getSubRankMeta());
                            if(StringUtils.isNotBlank(subTitle) && !subTitle.equals("-")){
                                rankExtractJsonSubRanks.setTitle(subTitle);
                            }
//                            if(StringUtils.isNotBlank(subMeta) && !subMeta.equals("-")){
//                                rankExtractJsonSubRanks.setMeta(subMeta);
//                            }

                            if (keywordRankEntityVO.getRank() == KeywordRankEntityVO.TYPE_POPULAR_PRODUCTS) {
                                if(StringUtils.isNotBlank(subRankVO.getReviews())){
                                    rankExtractJsonSubRanks.setRating(subRankVO.getReviews());
                                }else {
                                    rankExtractJsonSubRanks.setRating("-");
                                }
                                if(StringUtils.isNotBlank(subRankVO.getPrice())){
                                    rankExtractJsonSubRanks.setPrice(subRankVO.getPrice());
                                } else {
                                    rankExtractJsonSubRanks.setPrice("-");
                                }
                                if(StringUtils.isNotBlank(subRankVO.getTag())){
                                    rankExtractJsonSubRanks.setTagName(subRankVO.getTag());
                                } else {
                                    rankExtractJsonSubRanks.setTagName("-");
                                }
                            }

                            List<PopRankEntityVO> popRanks = subRankVO.getPopRanks();
                            if (!CollectionUtils.isEmpty(popRanks)) {
                                List<RankExtractJsonPopRanks> popRanksList = new ArrayList<>();
                                for (PopRankEntityVO popRank : popRanks) {
                                    RankExtractJsonPopRanks popRankEntity = new RankExtractJsonPopRanks();
                                    if (StringUtils.isNotBlank(popRank.getUrl())) {
                                        popRankEntity.setUrl(popRank.getUrl());
                                    }

                                    if (StringUtils.isNotBlank(popRank.getBrandName())) {
                                        popRankEntity.setBrandName(popRank.getBrandName());
                                    }

                                    if (StringUtils.isNotBlank(popRank.getRating())) {
                                        popRankEntity.setRating(popRank.getRating());
                                    }

                                    if (StringUtils.isNotBlank(popRank.getPopPrice())) {
                                        popRankEntity.setPopPrice(popRank.getPopPrice());
                                    }

                                    popRanksList.add(popRankEntity);
                                }
                                rankExtractJsonSubRanks.setPopRanks(popRanksList);
                            }
                            subRanksList.add(rankExtractJsonSubRanks);
                        }
                        if(CollectionUtils.isNotEmpty(subRanksList)){
                            rankExtractJsonRankings.setSubRanks(subRanksList);
                        }
                    }
                    rankExtractJsonRankingsList.add(rankExtractJsonRankings);
                    rankExtractJsonVO.setRankings(rankExtractJsonRankingsList);
                }
            }

            rankExtractJsonVO.setInfo(extractJsonInfo);
            rankExtractJsonVO.setSearchParameters(rankExtractJsonSearchParameters);
            rankExtractJsonVO.setSearchInfo(rankExtractJsonSearchInfo);
//            rankExtractJsonVO.setSerpFeaturesFlag(rankExtractJsonSerpFeaturesFlag);
            if(CollectionUtils.isNotEmpty(rankExtractJsonPeopleAlsoAskList)){
                rankExtractJsonVO.setPeopleAlsoAsk(rankExtractJsonPeopleAlsoAskList);
            }

//            if (info.getBrandMap() != null && !info.getBrandMap().isEmpty()) {
//                rankExtractJsonVO.setBrandSerpFeatures(Arrays.asList(info.getBrandMap()));
//            }
//            if (info.getNonbrandMap() != null && !info.getNonbrandMap().isEmpty()) {
//                rankExtractJsonVO.setSerpFeatures(Arrays.asList(info.getNonbrandMap()));
//            }
            rankExtractJsonVOList.add(rankExtractJsonVO);
        }
        return rankExtractJsonVOList;
    }

    private Integer convertValue(String value) {
        if (StringUtils.isNotBlank(value) && StringUtils.equalsIgnoreCase(value, "y")) {
            return 1;
        } else if (StringUtils.isNotBlank(value) && !StringUtils.equalsIgnoreCase(value, "n")
                && !StringUtils.equalsIgnoreCase(value, "-")
                && !StringUtils.equalsIgnoreCase(value, "0")
                && !StringUtils.equalsIgnoreCase(value, "0.")) {
            return 1;
        }
        return 0;
    }

    public static boolean isGoogleShoppingImageRanking(int engineId) {
        if (engineId == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_IMAGE || engineId == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_SHOPPING) {
            return true;
        }
        return false;
    }


    private String getStringValue(Object str) {
        return (str == null || StringUtils.isBlank(String.valueOf(str))) ? "" : str.toString().replaceAll("�", "");
    }

    private String parseUrl(String url, int searchEngine, String authorName) {
        if (StringUtils.equalsIgnoreCase(url, "#") || StringUtils.equalsIgnoreCase(url, ";")) {
            url = "http://instantanswers.google.com/";
        }
        String domainName = "";
        String uriPattern = "";
        boolean urlhaschanged = false;
        String convertedUrl = null;

        if (StringUtils.equalsIgnoreCase(url, "#") || StringUtils.equalsIgnoreCase(url, ";")) {
            url = "http://instantanswers.google.com/";
        }

        // https://www.wrike.com/open.htm?id=1489354912
        if (isGoogleShopping(searchEngine) && StringUtils.isBlank(url) && StringUtils.isNotBlank(authorName)) {
            //
        } else {
            String[] domainUrlList = CommonUtils.splitString(url);
            if (domainUrlList == null) {
                return null;
            }
            domainName = domainUrlList[0];
            uriPattern = domainUrlList[1];
            urlhaschanged = false;
            convertedUrl = null;
            String[] reprocessUri = CommonUtils.getDomainReverseAndUriFromUrl(uriPattern, domainName);
            if (reprocessUri != null && reprocessUri.length >= 2) {
                convertedUrl = CommonUtils.getConvertUrl(uriPattern, domainName);
                urlhaschanged = true;
            }
        }

//        // to solve google map issue.
//        if (StringUtils.containsIgnoreCase(domainName, ".google.www")
//                && keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_WEB_RESOURCE
//                && StringUtils.startsWithIgnoreCase(uriPattern, "/maps/")) {
//            keywordRankEntityVO.setType(KeywordRankEntityVO.TYPE_ADDRESS);
//        }


        String finalUrl;

        if(urlhaschanged && convertedUrl != null && !StringUtils.containsIgnoreCase(convertedUrl, "null")) {
            finalUrl = convertedUrl;
        } else {
            finalUrl = url;
        }

        try {
            finalUrl = removeQueryParamWithRetry(finalUrl, "srsltid");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("Remove special param(O3): " + "srsltid" + " error, finalUrl: " + finalUrl);
        }

        return finalUrl;

    }

    public static boolean isGoogleShopping(int engine) {
        return engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_SHOPPING;
    }



    public static String removeQueryParamWithRetry(String url, String queryParam) {
        if (url == null) {
            return null;
        }
        int maxCnt = 3;
        int index = 0;
        while (url.contains(queryParam)) {
            index ++;
            try {
                url = removeQueryParam(url, "srsltid");
            } catch (Exception e) {
                System.out.println("Remove special param(in): " + queryParam + " error, url: " + url + ", err: " + e.getMessage());
            }
            if (index >= maxCnt) {
                break;
            }
        }
        return url;
    }


    public static String removeQueryParam(String url, String param) {
        if (!url.contains(param)) {
            return url;
        }
        List<String> list = Arrays.asList("?" + param + "=", "%3F" + param + "=", "?" + param + "%3D", "%3F" + param + "%3D", "&" + param + "=", "%26" + param + "=", "&" + param + "%3D", "%26" + param + "%3D");
        for (String matchText : list) {
            if (url.contains(matchText)) {
                url = processMatchText(url, matchText);
            }
        }
        return url;
    }

    public static String processMatchText(String url, String matchText) {
        String endParamVal = StringUtils.substringAfter(url, matchText);
        String urlStart = StringUtils.substringBefore(url, matchText);
        if (!StringUtils.containsIgnoreCase(endParamVal, "?")
                && !StringUtils.containsIgnoreCase(endParamVal, "&")
                && !StringUtils.containsIgnoreCase(endParamVal, "=")
                && !StringUtils.containsIgnoreCase(endParamVal, "#")
                && !StringUtils.containsIgnoreCase(endParamVal, "/")
                && !StringUtils.containsIgnoreCase(endParamVal, "!")
                && !StringUtils.containsIgnoreCase(endParamVal, "%3F")
                && !StringUtils.containsIgnoreCase(endParamVal, "%26")
                && !StringUtils.containsIgnoreCase(endParamVal, "%3D")
                && !StringUtils.containsIgnoreCase(endParamVal, "%23")
                && !StringUtils.containsIgnoreCase(endParamVal, "%2F")
                && !StringUtils.containsIgnoreCase(endParamVal, "%21")
                && !StringUtils.containsIgnoreCase(endParamVal, "%")) {
            return StringUtils.substringBefore(url, matchText);
        } else {
            int cIndex1 = endParamVal.indexOf('?');
            int cIndex2 = endParamVal.indexOf('&');
            int cIndex3 = endParamVal.indexOf('=');
            int cIndex4 = endParamVal.indexOf('#');
            int cIndex5 = endParamVal.indexOf('/');
            int cIndex6 = endParamVal.indexOf('!');
            int cIndex7 = endParamVal.indexOf("%3F");
            int cIndex8 = endParamVal.indexOf("%26");
            int cIndex9 = endParamVal.indexOf("%3D");
            int cIndex10 = endParamVal.indexOf("%23");
            int cIndex11 = endParamVal.indexOf("%2F");
            int cIndex12 = endParamVal.indexOf("%21");
            int cIndex13 = endParamVal.indexOf("%");
            int endIndex = Integer.MAX_VALUE;
            if (cIndex1 != -1) endIndex = Math.min(endIndex, cIndex1);
            if (cIndex2 != -1) endIndex = Math.min(endIndex, cIndex2);
            if (cIndex3 != -1) endIndex = Math.min(endIndex, cIndex3);
            if (cIndex4 != -1) endIndex = Math.min(endIndex, cIndex4);
            if (cIndex5 != -1) endIndex = Math.min(endIndex, cIndex5);
            if (cIndex6 != -1) endIndex = Math.min(endIndex, cIndex6);
            if (cIndex7 != -1) endIndex = Math.min(endIndex, cIndex7);
            if (cIndex8 != -1) endIndex = Math.min(endIndex, cIndex8);
            if (cIndex9 != -1) endIndex = Math.min(endIndex, cIndex9);
            if (cIndex10 != -1) endIndex = Math.min(endIndex, cIndex10);
            if (cIndex11 != -1) endIndex = Math.min(endIndex, cIndex11);
            if (cIndex12 != -1) endIndex = Math.min(endIndex, cIndex12);
            if (cIndex13 != -1) endIndex = Math.min(endIndex, cIndex13);
            if (endIndex == Integer.MAX_VALUE) {
                return urlStart;
            }
            String urlEnd = endParamVal.substring(endIndex);
            return urlStart + urlEnd;
        }
    }

    public static boolean isUniversalUrl(String url, int urlType) {
        if (RankTypeManager.isWebRank(url, urlType)) {
            return false;
        }
        return true;
    }
}
