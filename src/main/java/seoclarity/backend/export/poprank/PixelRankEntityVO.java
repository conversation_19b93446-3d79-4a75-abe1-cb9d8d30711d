package seoclarity.backend.export.poprank;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class PixelRankEntityVO {

	private Integer pixelRank;
	private String cssType;
	private String cssPath;

	private List<String> typeList;
	private List<String> typeMainList;
	private List<String> typeFullList;
	
	private Integer rank;
	private Integer type;
	private Double offsetTop;
	private Double offsetBottom;
	private Double height;
	private Integer fold;
	private Integer foldRank;

	private String landingPage;
	private String domain;
	private String label;
	private String metaDesc;
	private String questionlist;
	
	public String getUrlType() {
		if (StringUtils.containsIgnoreCase(getLandingPage(), "maps.google.com")) {
			return "Local";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "images.google.com")) {
			return "Image";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "news.google.com")) {
			return "News";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "www.google.com/shopping")) {
			return "Shopping";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "www.youtube.com")) {
			return "Video";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "www.google.com")) {
			return "Universal";
		}

		return "Web";
	}
	
	private String ampFlg;
	
	private String stockFlg;
	private String priceFlg;
	
	private String videoFlg;
	private String universalPackFlg;
	
	private String authorNm;
	private String authorLink;
	private String rating;
	private String ratingNumber;
	private String eventFlg;
	private String thumbnail;
	private String  maliciousUrlFlg;
	private Boolean trustedStore; 

	private String authorName;
	private String price;
	private String promotion;
	private String additionalLabel;
	private boolean googleCheckOut;
	private String actualPrice;
	
	// https://www.wrike.com/open.htm?id=1176074033
	private Boolean popularStoreFlg;
	
	private String vedStr;

	// https://www.wrike.com/open.htm?id=1293352486
	private Boolean urlImageCarousel;

	// https://www.wrike.com/open.htm?id=1322745613
	private Boolean couponFlg;

	private String heading;

	// https://www.wrike.com/open.htm?id=1494875831
	private Boolean collapsedPopularBrandFlg;

	private Boolean aiOrganized;

	private Boolean aiSales;

	public String getRatingNumber() {
		if(StringUtils.isBlank(ratingNumber)) {
			return "-";
		}
		return ratingNumber;
	}

	public void setRatingNumber(String ratingNumber) {
		this.ratingNumber = ratingNumber;
	}
	
	private List<KeywordSubRankEntityVO> subRankVOs;

	public List<KeywordSubRankEntityVO> getSubRankVOs() {
		return subRankVOs;
	}

	public void setSubRankVOs(List<KeywordSubRankEntityVO> subRankVOs) {
		this.subRankVOs = subRankVOs;
	}

	public Integer getPixelRank() {
		return pixelRank;
	}

	public void setPixelRank(Integer pixelRank) {
		this.pixelRank = pixelRank;
	}

	public String getCssType() {
		return cssType;
	}

	public void setCssType(String cssType) {
		this.cssType = cssType;
	}

	public String getCssPath() {
		return cssPath;
	}

	public void setCssPath(String cssPath) {
		this.cssPath = cssPath;
	}

	public List<String> getTypeList() {
		return typeList;
	}

	public void setTypeList(List<String> typeList) {
		this.typeList = typeList;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Double getOffsetTop() {
		return offsetTop;
	}

	public void setOffsetTop(Double offsetTop) {
		this.offsetTop = offsetTop;
	}

	public Double getOffsetBottom() {
		return offsetBottom;
	}

	public void setOffsetBottom(Double offsetBottom) {
		this.offsetBottom = offsetBottom;
	}

	public Double getHeight() {
		return height;
	}

	public void setHeight(Double height) {
		this.height = height;
	}

	public Integer getFold() {
		return fold;
	}

	public void setFold(Integer fold) {
		this.fold = fold;
	}

	public Integer getFoldRank() {
		return foldRank;
	}

	public void setFoldRank(Integer foldRank) {
		this.foldRank = foldRank;
	}

	public String getLandingPage() {
		return landingPage;
	}

	public void setLandingPage(String landingPage) {
		this.landingPage = landingPage;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getMetaDesc() {
		return metaDesc;
	}

	public void setMetaDesc(String metaDesc) {
		this.metaDesc = metaDesc;
	}

	public String getQuestionlist() {
		return questionlist;
	}

	public void setQuestionlist(String questionlist) {
		this.questionlist = questionlist;
	}

	public String getAmpFlg() {
		return ampFlg;
	}

	public void setAmpFlg(String ampFlg) {
		this.ampFlg = ampFlg;
	}

	public String getStockFlg() {
		return stockFlg;
	}

	public void setStockFlg(String stockFlg) {
		this.stockFlg = stockFlg;
	}

	public String getPriceFlg() {
		return priceFlg;
	}

	public void setPriceFlg(String priceFlg) {
		this.priceFlg = priceFlg;
	}

	public String getVideoFlg() {
		return videoFlg;
	}

	public void setVideoFlg(String videoFlg) {
		this.videoFlg = videoFlg;
	}

	public String getUniversalPackFlg() {
		return universalPackFlg;
	}

	public void setUniversalPackFlg(String universalPackFlg) {
		this.universalPackFlg = universalPackFlg;
	}

	public String getAuthorNm() {
		return authorNm;
	}

	public void setAuthorNm(String authorNm) {
		this.authorNm = authorNm;
	}

	public String getAuthorLink() {
		return authorLink;
	}

	public void setAuthorLink(String authorLink) {
		this.authorLink = authorLink;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public String getEventFlg() {
		return eventFlg;
	}

	public void setEventFlg(String eventFlg) {
		this.eventFlg = eventFlg;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	public String getMaliciousUrlFlg() {
		return maliciousUrlFlg;
	}

	public void setMaliciousUrlFlg(String maliciousUrlFlg) {
		this.maliciousUrlFlg = maliciousUrlFlg;
	}

	public Boolean getTrustedStore() {
		return trustedStore;
	}

	public void setTrustedStore(Boolean trustedStore) {
		this.trustedStore = trustedStore;
	}

	public String getAuthorName() {
		return authorName;
	}

	public void setAuthorName(String authorName) {
		this.authorName = authorName;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getPromotion() {
		return promotion;
	}

	public void setPromotion(String promotion) {
		this.promotion = promotion;
	}

	public String getAdditionalLabel() {
		return additionalLabel;
	}

	public void setAdditionalLabel(String additionalLabel) {
		this.additionalLabel = additionalLabel;
	}

	public boolean isGoogleCheckOut() {
		return googleCheckOut;
	}

	public void setGoogleCheckOut(boolean googleCheckOut) {
		this.googleCheckOut = googleCheckOut;
	}

	public String getActualPrice() {
		return actualPrice;
	}

	public void setActualPrice(String actualPrice) {
		this.actualPrice = actualPrice;
	}

	public Integer getLabelLen() {
		if(StringUtils.isBlank(label)) {
			return 0;
		}
		return label.length();
	}
	
	public Integer getMetaDescLen() {
		if(StringUtils.isBlank(metaDesc)) {
			return 0;
		}
		return metaDesc.length();
	}

	public List<String> getTypeMainList() {
		
		if (typeMainList != null && typeMainList.size() == 1 && typeMainList.get(0) == null) {
			typeMainList = new ArrayList<>();
		}
		return typeMainList;
	}

	public void setTypeMainList(List<String> typeMainList) {
		this.typeMainList = typeMainList;
	}

	public List<String> getTypeFullList() {
		return typeFullList;
	}

	public void setTypeFullList(List<String> typeFullList) {
		this.typeFullList = typeFullList;
	}
	
	public Boolean getPopularStoreFlg() {
		return popularStoreFlg;
	}

	public void setPopularStoreFlg(Boolean popularStoreFlg) {
		this.popularStoreFlg = popularStoreFlg;
	}

	public String getVedStr() {
		return vedStr;
	}

	public void setVedStr(String vedStr) {
		this.vedStr = vedStr;
	}

	public Boolean getUrlImageCarousel() {
		return urlImageCarousel;
	}

	public void setUrlImageCarousel(Boolean urlImageCarousel) {
		this.urlImageCarousel = urlImageCarousel;
	}

	public Boolean getCouponFlg() {
		return couponFlg;
	}

	public void setCouponFlg(Boolean couponFlg) {
		this.couponFlg = couponFlg;
	}

	public String getHeading() {
		return heading;
	}

	public void setHeading(String heading) {
		this.heading = heading;
	}

	public Boolean getCollapsedPopularBrandFlg() {
		return collapsedPopularBrandFlg;
	}

	public void setCollapsedPopularBrandFlg(Boolean collapsedPopularBrandFlg) {
		this.collapsedPopularBrandFlg = collapsedPopularBrandFlg;
	}

	public Boolean getAiOrganized() {
		return aiOrganized;
	}

	public void setAiOrganized(Boolean aiOrganized) {
		this.aiOrganized = aiOrganized;
	}

	public Boolean getAiSales() {
		return aiSales;
	}

	public void setAiSales(Boolean aiSales) {
		this.aiSales = aiSales;
	}
}
