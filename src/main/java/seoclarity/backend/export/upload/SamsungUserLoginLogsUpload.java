package seoclarity.backend.export.upload;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import seoclarity.backend.dao.actonia.UserDAO;
import seoclarity.backend.entity.AgencyInfoEntity;
import seoclarity.backend.entity.actonia.UserEntity;
import seoclarity.backend.service.AgencyInfoManager;
import seoclarity.backend.utils.*;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

public class SamsungUserLoginLogsUpload {

    private static String FILE_PATH = "files/samsung/";
    private static String FILE_NAME = "userLoginLogs";
    private static String FTP_LOC = "/home/<USER>/";

    private static int DOMAINID = 8773;

    private static String EMAIL_NAME = "Samsung | All Domains | Usage Stats";
    private static SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private static String startDate = "";
    private static String endDate = "";

    private static final Map<Integer, String> domainMap = new HashMap<>();

    static {
        domainMap.put(8773, "www.samsung.com/uk/");
        domainMap.put(8787, "www.samsung.com/de/");
        domainMap.put(8788, "www.samsung.com/fr/");
        domainMap.put(8789, "www.samsung.com/nl/");
        domainMap.put(8811, "www.samsung.com/be_fr/");
        domainMap.put(8812, "www.samsung.com/es/");
        domainMap.put(8928, "www.samsung.com/al/");
        domainMap.put(8929, "www.samsung.com/at/");
        domainMap.put(8930, "www.samsung.com/be/");
        domainMap.put(8931, "www.samsung.com/bg/");
        domainMap.put(8932, "www.samsung.com/ch/");
        domainMap.put(8933, "www.samsung.com/ch_fr/");
        domainMap.put(8934, "www.samsung.com/cz/");
        domainMap.put(8935, "www.samsung.com/dk/");
        domainMap.put(8936, "www.samsung.com/ee/");
        domainMap.put(8937, "www.samsung.com/fi/");
        domainMap.put(8938, "www.samsung.com/gr/");
        domainMap.put(8939, "www.samsung.com/hr/");
        domainMap.put(8940, "www.samsung.com/hu/");
        domainMap.put(8941, "www.samsung.com/ie/");
        domainMap.put(8942, "www.samsung.com/it/");
        domainMap.put(8943, "www.samsung.com/lt/");
        domainMap.put(8944, "www.samsung.com/lv/");
        domainMap.put(8945, "www.samsung.com/no/");
        domainMap.put(8946, "www.samsung.com/pl/");
        domainMap.put(8947, "www.samsung.com/pt/");
        domainMap.put(8948, "www.samsung.com/ro/");
        domainMap.put(8949, "www.samsung.com/rs/");
        domainMap.put(8950, "www.samsung.com/se/");
        domainMap.put(8951, "www.samsung.com/sk/");
        domainMap.put(8959, "www.samsung.com/si/");
        domainMap.put(8960, "www.samsung.com/mk/");
        domainMap.put(9654, "www.samsung.com");
        domainMap.put(9661, "www.samsung.com/ba/");
        domainMap.put(11731, "www.samsung.com/ua");


    }

    private static Integer[] domains = new Integer[]{
            8773,
            8787,
            8788,
            8789,
            8811,
            8812,
            8928,
            8929,
            8930,
            8931,
            8932,
            8933,
            8934,
            8935,
            8936,
            8937,
            8938,
            8939,
            8940,
            8941,
            8942,
            8943,
            8944,
            8945,
            8946,
            8947,
            8948,
            8949,
            8950,
            8951,
            8959,
            8960,
            9654,
            9661,
            11731
    };


    private UserDAO userDAO;
    private AgencyInfoManager agencyInfoManager;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    private SamsungUserLoginLogsUpload() {
        userDAO = SpringBeanFactory.getBean("userDAO");
        agencyInfoManager = SpringBeanFactory.getBean("agencyInfoManager");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");

    }

    static CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader(
                    "Domain ID",
                    "Domain Name",
                    "Date",
                    "Email"
            )
            .withDelimiter(',');

    public static void main(String[] args) {
        SamsungUserLoginLogsUpload in = new SamsungUserLoginLogsUpload();
        in.process();
    }

    private void process() {

        getqueryDate();

        List<UserEntity> userLoginLogs = userDAO.getUserLoginLogs(domains, startDate, endDate);

        if (userLoginLogs.size() <= 0) {
            return;
        }
        System.out.println("*********************usDomainList.size :" + userLoginLogs.size());

        try {
            String fileName = export(userLoginLogs);

            System.out.println("====###文件上传ftp ================================");
            saveFilesToFtp(fileName, DOMAINID);

            System.out.println("===###创建文件成功, 发送邮件 ========== " + fileName);
            sendEmailAndFile(fileName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private void sendEmailAndFile(String fileName) {
        //" ===###文件可能为空文件，检查一下 ======================="
        File file = new File(fileName);

        if (null == file || 0 == file.length() || !file.exists()) {
            System.out.println("===###file文件为空！");
        }

        String userName = "Shannon Roberts";
        String emailTo = "<EMAIL>";
        try {
            sendEmail(userName, emailTo, fileName, FILE_NAME,
                    DOMAINID, EMAIL_NAME);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void sendEmail(String userName, String emailAddress, String path, String fname, int ownDomainId, String emailTitle) throws Exception {


        String emailTo = emailAddress;
        System.out.println("=========Send to : " + emailTo + " start!");

        String subject = emailTitle;
        String info = "All Samsung domain access records were uploaded last month.";

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", userName);
        reportMap.put("info", info);

        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(ownDomainId);
        int sizeMb = 0;
        File file = new File(path);
        String downloadFileName = "";
        if (file.exists()) {
            long fileSize = file.length();
            sizeMb = (int) (fileSize / 1024 / 1024);
            System.out.println(" OID:" + ownDomainId + " filename: " + (path) +
                    " size:" + fileSize + " MB:" + sizeMb);

            GZipUtil.zipFile(file.getAbsolutePath(), GZipUtil.ZIPFile_POSTFIX);
            downloadFileName = file.getAbsolutePath() + GZipUtil.ZIPFile_POSTFIX;
        } else {
            System.out.println("===###can not find file");
        }

        String linkUrl = "";
        try {
            boolean isSaved = SeagateUtils.saveFileToDefaultSeagate(ownDomainId, downloadFileName);
            if (!isSaved) {
                System.out.println("===send to Seagate Failed!");
            }
            linkUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, fname + GZipUtil.ZIPFile_POSTFIX);
            System.out.println("==isSaved:" + isSaved + ",linkUrl:" + linkUrl);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("===send to Seagate Failed!");
        }


        //https://www.wrike.com/open.htm?id=960781633
        String linkText = null;
//        try {
//            String name = URLEncoder.encode(filename, "utf-8");
        linkText = linkUrl;
//        } catch (UnsupportedEncodingException e) {
//            System.out.println(e.getMessage());
//            linkText = linkUrl;
//        }

        System.out.println("  fileLink:" + linkText);
        reportMap.put("fileLink", linkText);
        String[] bccTo = new String[]{};
//        bccTo = new String[]{};
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo,
                ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, null, null);

        System.out.println("=========Send to : " + emailAddress + " success!");
    }


    private void getqueryDate() {

        Date date = new Date();
        String today = yyyy_MM_dd.format(date);
        FILE_NAME = FILE_NAME + today + ".csv";

        Date endDay = DateUtils.getLastDayOfPreviousMonth(date);
        Date startDay = DateUtils.getMonthFirstDay(endDay);
        startDate = yyyy_MM_dd.format(startDay);
        endDate = yyyy_MM_dd.format(endDay);
        System.out.println("===###startDate : " + startDate + " endDate : " + endDate + " FILE_NAME ：" + FILE_NAME);
    }

    private String export(List<UserEntity> competitorDomainList) throws Exception {
        String filename = FILE_PATH + FILE_NAME;
        System.out.println("===###filename " + filename);
        File file = new File(filename);

        if (!file.exists()) {
            file.getParentFile().mkdir();
            try {
                file.createNewFile();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file)), csvFullFormat);
        System.out.println("name : " + file.getName() + " size : " + competitorDomainList.size());
        for (UserEntity user : competitorDomainList) {
            csvPrinter.printRecord(
                    user.getOwnDomainId(),
                    domainMap.get(user.getOwnDomainId()),
                    user.getLogDate(),
                    user.getEmail()
            );
        }
        csvPrinter.flush();
        csvPrinter.close();

        return FILE_PATH + FILE_NAME;
    }

    private void saveFilesToFtp(String localFileName, Integer domainid) {
        String targetPath = FTP_LOC + domainid;
        System.out.println("===###savefileToFTP =====" + targetPath + " file : " + localFileName);
        FTPUtils.saveFileToFTP(domainid, localFileName, targetPath);
    }

}
