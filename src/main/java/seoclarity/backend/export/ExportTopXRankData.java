package seoclarity.backend.export;

import com.google.common.net.InternetDomainName;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

@Deprecated
@CommonsLog
public class ExportTopXRankData {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static final String KEY_SPLIT = "#_#";
    private static String LOC = "/home/<USER>/";
    private static final int QUERY_TRY_COUNT = 10;

    private static Date processDate = new Date();
    private static String[] domainIdList;
    private static int domainId;
    private static boolean isRerun = false;
    private static boolean extractTag = false;

    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private ScriptDeployInfoEntity scriptDeployInfoEntity;
    private ScriptRunInstanceEntity monitorEntity = new ScriptRunInstanceEntity();

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private CommonDataService commonDataService;
    private ScriptDeployInfoEntityDAO scriptDeployInfoEntityDAO;
    private KeywordEntityDAO keywordEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;

    public ExportTopXRankData() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        commonDataService = SpringBeanFactory.getBean("commonDataService");
        scriptDeployInfoEntityDAO = SpringBeanFactory.getBean("scriptDeployInfoEntityDAO");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
    }

    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();

    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(765, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
        SPECIAL_DOMAIN_SERVER_MAP.put(2047, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
    }

    public static final Map<Integer, Integer> SCRIPT_DEPLOY_INFO_MAP = new HashMap();//(domainId,infoId)
    static {
//        SCRIPT_DEPLOY_INFO_MAP.put(476, 12);
    }

    public static final List<Integer> EXTRACT_SECONDARY_DOMAIN_LIST = Arrays.asList(8682);

    private void processForDomain(int ownDomainId) {

        log.info("********************** process domain " + ownDomainId + ",processDate: " + processDate + " *******************");

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);
        }catch (Exception e){
            e.printStackTrace();
        }


        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }
        int frequency = 1;//daily
        if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
            processDate = FormatUtils.getLastSundayForWeeklyDomainExtract(processDate);
            System.out.println("====weekly domain processDate:" + processDate);
            frequency = 7;//weekly
        }


        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);

        if(ownDomainId == 765 || ownDomainId == 2047){
            extractTag = true;
        }

        boolean isDesktop = false;
        boolean isMobile = false;

        if (ownDomainEntity.isMobileDomain() && ownDomainSettingEntity.isMobileEnable()) {
            isMobile = true;
        } else if (!ownDomainEntity.isMobileDomain() && ownDomainSettingEntity.isMobileEnable()) {
            isMobile = true;
            isDesktop = true;
        } else if (ownDomainEntity.isMobileDomain() && !ownDomainSettingEntity.isMobileEnable()) {
            isMobile = true;
        } else if (!ownDomainEntity.isMobileDomain() && !ownDomainSettingEntity.isMobileEnable()) {
            isDesktop = true;
        }

        Integer scriptInfoId = SCRIPT_DEPLOY_INFO_MAP.get(ownDomainId);
        if (scriptInfoId != null) {
            scriptDeployInfoEntity = scriptDeployInfoEntityDAO.getById(scriptInfoId);
        }

        if(ownDomainId == 765){
            isDesktop = true;
            isMobile = false;
        }
        if(ownDomainId == 2047){
            isDesktop = false;
            isMobile = true;
        }
        try {
            if (isDesktop) {//todo
                processExtract(ownDomainEntity, false);
            }
            if (isMobile) {
                processExtract(ownDomainEntity, true);
            }
        }catch (Exception e){
            e.printStackTrace();
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        }


    }

    private void processExtract(OwnDomainEntity ownDomainEntity, boolean isMobile) {

        String pDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_FORMAT_YYYYMMDD);
        String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

        logglyVO.setoId(String.valueOf(ownDomainEntity.getId()));
        logglyVO.setName("ExportTopXRankData");
        logglyVO.setDevice(isMobile?"m":"d");

        logglyVO.setpDate(pDate);
        List<String> groupList = new ArrayList<>();
        groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
        logglyVO.setGroups(groupList);


        long startTime = System.currentTimeMillis();
        int ownDomainId = ownDomainEntity.getId();
        int processingDate = FormatUtils.formatDateToYyyyMmDd(processDate);

        if (scriptDeployInfoEntity != null) {
            try {
                monitorEntity = commonDataService.saveScriptRunInfo(scriptDeployInfoEntity.getId(),
                        FormatUtils.formatDateToYyyyMmDd(processDate), ScriptRunInstanceEntity.STATUS_STARTED,
                        ownDomainId, null, InetAddress.getLocalHost().getHostAddress(), CommonUtils.getServerPath(),
                        isMobile ? "m" : "d", ScriptRunInstanceEntity.TAG_NO,
                        scriptDeployInfoEntity.getFrequency());
            } catch (Exception e) {
                e.printStackTrace();
                logglyVO.setStatus(LogglyVO.STATUS_NG);
                String body = new Gson().toJson(logglyVO);
                LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                return;
            }
        }


        String fileName = getFileName(ownDomainId, processingDate, isMobile);//todo

        File localFolder = new File(LOC + ownDomainId);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }

        String localFilePath = LOC + ownDomainId + File.separator;
        String remoteFilePath = localFilePath;
        if (ownDomainId == 765 || ownDomainId == 2047) {
            remoteFilePath = "/incoming/seoClarity";
        }

        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        String s3KeyPath = "seo/ranking/" + fileName;

        try {

            processFile(localFile, ownDomainEntity, isMobile, localFileName, remoteFilePath, s3KeyPath);

            //https://www.wrike.com/open.htm?id=424428733
            if (!isRerun && (domainId == 765 || domainId == 2047)) {
                String currentFileName = getCurrentFileName(ownDomainId, isMobile);
                File currentLocalFile = null;
                log.info("====currentFileName: " + currentFileName);
                if (StringUtils.isNotBlank(currentFileName)) {
                    localFilePath = LOC + ownDomainId + File.separator + currentFileName;
                    currentLocalFile = new File(localFilePath);
                    if (currentLocalFile.exists()) {
                        currentLocalFile.delete();
                    }
                    if (ownDomainId == 765 || ownDomainId == 2047) {
                        remoteFilePath = "/incoming/seoClarity";
                    } else {
                        remoteFilePath = localFilePath;
                    }
                    processFile(currentLocalFile, ownDomainEntity, isMobile, localFilePath, remoteFilePath, s3KeyPath);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//            String subject = getEmailSubject(ownDomainId, false);
//            String message = subject;
//            sendMailReport(subject, message);

            monitorEntity.setFatalError(e.getMessage());
            return;
        }

        long endTime = System.currentTimeMillis();
        int elapsedSeconds = (int) (endTime - startTime);
        if (StringUtils.isNotBlank(monitorEntity.getFatalError())) {
            monitorEntity.setStatus(ScriptRunInstanceEntity.STATUS_FAILURE);
        } else {
            monitorEntity.setStatus(ScriptRunInstanceEntity.STATUS_SUCCESS);
        }

        monitorEntity.setElapsedSeconds(elapsedSeconds);
        if (scriptDeployInfoEntity != null) {
            commonDataService.updateScriptRunInfo(monitorEntity);
        }

        logglyVO.setStatus(LogglyVO.STATUS_OK);
        logglyVO.setsTime(stTime);
        logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
        logglyVO.setRows(String.valueOf(totalCnt));
        String body = new Gson().toJson(logglyVO);
        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, boolean isMobile, String localFilePath,
                             String remoteFilePath, String s3KeyPath) throws Exception {

        int ownDomainId = ownDomainEntity.getId();
        addHeadersForExactFile(localFile, ownDomainEntity, processDate);//todo

        List<String> dataList = new ArrayList<>();
        int retryCount = 1;
        while (true) {
            try {
                dataList = getDataFromDB(ownDomainEntity, isMobile);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + ownDomainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + ownDomainId);
                    logglyVO.setStatus(LogglyVO.STATUS_NG);
                    String body = new Gson().toJson(logglyVO);
                    LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
                    return;
                }

                e.printStackTrace();
                System.out.println("====extract error oid:" + ownDomainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        FileUtils.writeLines(localFile, dataList, true);

        int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
        if (SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId) != null) {
            serverType = SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId);
        }
        totalCnt = dataList.size();
        monitorEntity.setOutputDataCount(dataList.size());
        monitorEntity.setOutputFile(localFile.getName());
        monitorEntity.setOutputFileSizeKB(FormatUtils.getFileSizeKB(localFile.length()));

        copyFileToRemoteServer(serverType, ownDomainId, localFilePath, remoteFilePath, s3KeyPath);
    }

    private String getFileName(int ownDomainId, int processingDate, boolean isMobile) {

        String device = isMobile ? "mobile" : "desktop";

        String fileName = ownDomainId + "_RankExtract_" + processingDate + "_" + device + ".csv";// TODO: 2019/12/4
        return fileName;
    }

    private String getCurrentFileName(int ownDomainId, boolean isMobile) {

        if (ownDomainId != 765 && ownDomainId != 2047) {
            return null;
        }

        String device = isMobile ? "mobile" : "desktop";
        String fileName = ownDomainId + "_RankExtract_current_" + device + ".csv";
        return fileName;
    }


    private void addHeadersForExactFile(File outFile, OwnDomainEntity ownDomainEntity, Date processDate) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        if (ownDomainEntity.getId() == 765 || ownDomainEntity.getId() == 2047) {
            header.append("Keyword").append(SPLIT);
            header.append("Average Search Volume").append(SPLIT);
            header.append("True Rank").append(SPLIT);
            header.append("Web Rank").append(SPLIT);
            header.append("URL").append(SPLIT);
            header.append("Tag");
        } else if (ownDomainEntity.getId() == 4) {//todo

        } else {
            header.append("Date").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Average Search Volume").append(SPLIT);
            header.append("Rank").append(SPLIT);
            header.append("Ranking URL");
        }

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity, boolean isMobile) throws Exception{

        List<String> extractLines = new ArrayList<String>();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        String domainReverse = null;
        String rootDomainReverse = null;

        String[] domainInfo = domainName.split("\\.");
        log.info("===domainInfo: " + new Gson().toJson(domainInfo));
        domainReverse = domainInfo[2] + "." + domainInfo[1] + "." + domainInfo[0];
        rootDomainReverse = domainInfo[2] + "." + domainInfo[1];


        List<CLRankingDetailEntity> dataList = new ArrayList<>();
        if (ownDomainId == 765 || ownDomainId == 2047) {
            dataList = clDailyRankingEntityDao.exportKeywordsFor765(true, ownDomainId, engineId, languageId,
                    rankingDate, isBroadMatch, isMobile, domainReverse, rootDomainReverse);
        } else if (ownDomainId == 7464) { //not deployed https://www.wrike.com/open.htm?id=427886180
            dataList = clDailyRankingEntityDao.exportTop100KeywordsFor7464(ownDomainId, engineId, languageId, rankingDate, isMobile);
        } else if(ownDomainId == 8682){
            int locationId = 0;
            int rank = 100;
            dataList = clDailyRankingEntityDao.exportTopXKeywords(
                    ownDomainId, engineId, languageId, locationId, rankingDate, isMobile, rank);
        }

        if(EXTRACT_SECONDARY_DOMAIN_LIST.contains(ownDomainId)){
            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
            if(CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)){
                for(DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList){
                    engineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    languageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    boolean secondaryIsMobile = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? false : true;
                    if(secondaryIsMobile != isMobile){
                        log.info("===secondary not same device isMobile: " + isMobile + ",secondaryIsMobile:" + secondaryIsMobile);
                        continue;
                    }
                    log.info("===process secondary engine :" + engineId + ",languageId: " + languageId + ",isMobile:" + secondaryIsMobile + ",domain:" + ownDomainId);
                    List<CLRankingDetailEntity> secondaryDataList = new ArrayList<>();
                    if (ownDomainId == 8682) {
                        int locationId = 0;
                        int rank = 100;
                        secondaryDataList = clDailyRankingEntityDao.exportTopXKeywords(
                                ownDomainId, engineId, languageId, locationId, rankingDate, secondaryIsMobile, rank);
                    }
                    log.info("===secondaryDataList size:" + secondaryDataList.size());
                    dataList.addAll(secondaryDataList);
                }
            }

        }

        List<String> keywordNameList = new ArrayList<>();
        Map<String, String[]> tagMap = new HashMap<>();
        Map<String, CLRankingDetailEntity> keywordMap = new HashMap<>();

        log.info("===dataList size: " + dataList.size());

        if(extractTag){
            //get tag
            for (CLRankingDetailEntity detail : dataList) {
                try {

                    keywordNameList.add(URLEncoder.encode(detail.getKeywordName(), "utf-8").toLowerCase());
                    if (keywordNameList.size() >= 100) {
                        //tag
                        List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeywordNameList(domainId, keywordNameList);
                        if (CollectionUtils.isNotEmpty(tagList)) {
                            for (GroupTagEntity groupTagEntity : tagList) {
                                String[] tagNameArray = groupTagEntity.getTagName().split(TAG_SPLIT);
                                String decodeKw = URLDecoder.decode(groupTagEntity.getKeywordName(), "utf-8").toLowerCase();
                                tagMap.put(decodeKw, tagNameArray);
                            }
                        }
                        keywordNameList.clear();
                    }
                    String key = detail.getKeywordName() + TAG_SPLIT + detail.getRank();
                    keywordMap.put(key, detail);

                } catch (Exception e) {
                    monitorEntity.setFatalError(e.getMessage());
                    e.printStackTrace();
                    continue;
                }
            }

            if(CollectionUtils.isNotEmpty(keywordNameList)){
                List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeywordNameList(domainId, keywordNameList);
                if (CollectionUtils.isNotEmpty(tagList)) {
                    for (GroupTagEntity groupTagEntity : tagList) {
                        String[] tagNameArray = groupTagEntity.getTagName().split(TAG_SPLIT);
                        String decodeKw = URLDecoder.decode(groupTagEntity.getKeywordName(), "utf-8").toLowerCase();
                        tagMap.put(decodeKw, tagNameArray);
                    }
                }
            }

            for(String key : keywordMap.keySet()){

                String kw = key.split(TAG_SPLIT)[0];
                CLRankingDetailEntity detail = keywordMap.get(key);

                String[] tags = tagMap.get(kw);
                if(tags != null && tags.length > 0){
                    for(String tag: tags){
                        extractLines.add(appendData(detail, ownDomainId, tag));
                    }
                }else {
                    extractLines.add(appendData(detail, ownDomainId, null));
                }
            }
        }else {
            for (CLRankingDetailEntity detail : dataList) {
                extractLines.add(appendData(detail, ownDomainId, null));
            }
        }

        return extractLines;
    }

    public static String appendData(CLRankingDetailEntity clRankingDetailEntity, int domainId, String tagName) {
        StringBuffer line = new StringBuffer();

        if (!(domainId == 765 || domainId == 2047)) {
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
        }

        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        if (clRankingDetailEntity.getAvgSearchVolume().equals(-1l)) {
            line.append("0").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
        }
        if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
        }
        if (domainId == 765 || domainId == 2047) {
            if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            }
        }

        line.append(clRankingDetailEntity.getUrl() == null ? "" : clRankingDetailEntity.getUrl()).append(SPLIT);
        if(extractTag){
            line.append(StringUtils.isBlank(tagName) ? "-" : tagName);
        }

        return line.toString();
    }

    private void copyFileToRemoteServer(int serverType, int domainId, String localFilePath, String remoteFilePath, String s3KeyPath) throws Exception{//todo

        int getServerInfoDomainId = domainId;
        if (domainId == 2047) {
            getServerInfoDomainId = 765;
        }

        boolean success = serverAuthenticationInfoService.copyFileToRemoteServer(serverType, getServerInfoDomainId, localFilePath, remoteFilePath, monitorEntity);
        if (!success) {
            log.error("====send to ftp failed!!");
        }

        //https://www.wrike.com/open.htm?id=995813703
        if(localFilePath.contains("_current_")){//skip current file
            return;
        }
        boolean isSendS3Success = serverAuthenticationInfoService.putFileForS3WithSSE(ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3, getServerInfoDomainId, localFilePath, s3KeyPath);
        if(!isSendS3Success){
            log.error("====send to s3 failed!!");
        }

    }

    private String getEmailSubject(int domainId, boolean success) {

        String subject = "";
        String status = success ? "Success" : "Failed";
        switch (domainId) {
            case 765:
                subject = status + " Export Cubesmart Keyword " + FormatUtils.formatDateToYyyyMmDd(processDate);
            case 2047:
                subject = status + " Export Cubesmart Keyword " + FormatUtils.formatDateToYyyyMmDd(processDate);
            default:
                break;
        }

        return subject;
    }

    private static String getRootDomain(String fullDomain) {
        String domainName = null;
        try {
            domainName = StringUtils.reverseDelimited(fullDomain, '.');
            return StringUtils.reverseDelimited(InternetDomainName.from(domainName).topPrivateDomain().toString(), '.');
        } catch (Exception e) {
            try {
                if (StringUtils.startsWithIgnoreCase(domainName, "www.")) {
                    return StringUtils.reverseDelimited(StringUtils.removeStartIgnoreCase(domainName, "www."), '.');
                } else {
                    return StringUtils.reverseDelimited(domainName, '.');
                }
            } catch (Exception ex) {
            }
        }
        return null;
    }

    public static void main(String[] args) {

//        domainId = 765;
//        processDate = FormatUtils.toDate("2019-12-04", FormatUtils.DATE_PATTERN_2);
//        ExportTopXRankData exportTopXRankData = new ExportTopXRankData();
//        exportTopXRankData.processForDomain(domainId);

        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainIdList = args[0].split(",");
            } else {
                domainId = Integer.parseInt(args[0]);
            }

        }

        if (domainId == 765) {
            domainIdList = new String[]{ "765", "2047" };
        }

        ExportTopXRankData exportTopXRankData = new ExportTopXRankData();

        if (domainIdList != null && domainIdList.length > 0) {

            for (String processingDomainId : domainIdList) {

                if (args.length >= 2 && StringUtils.containsIgnoreCase(args[1], ",")) {
                    Date sDate = FormatUtils.toDate(args[1].split(",")[0], FormatUtils.DATE_PATTERN_2);
                    Date eDate = FormatUtils.toDate(args[1].split(",")[1], FormatUtils.DATE_PATTERN_2);
                    isRerun = true;
                    while (sDate.compareTo(eDate) <= 0) {
                        processDate = sDate;
                        exportTopXRankData.processForDomain(Integer.parseInt(processingDomainId));
                        sDate = DateUtils.addDays(sDate, 1);
                    }

                } else {
                    processDate = DateUtils.addDays(new Date(), -1);
                    exportTopXRankData.processForDomain(Integer.parseInt(processingDomainId));
                }
            }

        } else {

            if (args.length >= 2 && StringUtils.containsIgnoreCase(args[1], ",")) {
                Date sDate = FormatUtils.toDate(args[1].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[1].split(",")[1], FormatUtils.DATE_PATTERN_2);
                isRerun = true;
                while (sDate.compareTo(eDate) <= 0) {
                    processDate = sDate;
                    exportTopXRankData.processForDomain(domainId);
                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else {
                processDate = DateUtils.addDays(new Date(), -1);
                exportTopXRankData.processForDomain(domainId);
            }

        }


    }


}
