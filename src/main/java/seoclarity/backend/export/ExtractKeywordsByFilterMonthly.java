package seoclarity.backend.export;

import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.bean.FTPServerInfoBean;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Jason
 * https://www.wrike.com/open.htm?id=352468146
 */
public class ExtractKeywordsByFilterMonthly {

    private static final String SPLIT = "\t";
    private static String LOC = "/home/<USER>/";
    private static final int QUERY_TRY_COUNT = 10;

    private final static String PRICE_TYPE = "Price";
    private final static String SHOPPING_TYPE = "Shopping";
    private final static String STAR_TYPE = "Star";

    private final static String DESKTOP_DEVICE = "Desktop";
    private final static String MOBILE_DEVICE = "Mobile";

    private static String startMonth;
    private static String device = "desktop";

    private int domain = 185;
    private static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private ClDailyRankingEntityDao clDailyRankingEntityDao;

    private List<String> priceZipFileNames = new ArrayList<>();
    private List<String> shoppingZipFileNames = new ArrayList<>();
    private List<String> starZipFileNames = new ArrayList<>();

    public ExtractKeywordsByFilterMonthly() {
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
    }

    private void processExtract() throws Exception{

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(FormatUtils.toDate(startMonth, "yyyy-MM"));
        int dayCount = FormatUtils.getDaysCountOfMonth(calendar.getTime());
        System.out.println("======== dayCount :" + dayCount);
        String message = "Extract 185 Monthly startMonth : " + startMonth +  " , device : " + device;

        try{
            String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
            String sTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

            logglyVO.setoId(String.valueOf(185));
            logglyVO.setName("ExtractKeywordsByFilterMonthly");
            logglyVO.setDevice(device.startsWith("d")?"d":"m");

            logglyVO.setpDate(pDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_CUSTOMIZED_EXTRACT);
            logglyVO.setGroups(groupList);

            for (int i = 1; i <= dayCount; i++) {

                calendar.set(Calendar.DATE, i);
                System.out.println("======== day :" + FormatUtils.formatDate(calendar.getTime(), "yyyy-MM-dd"));

                extractForOneDay(FormatUtils.formatDate(calendar.getTime(), "yyyy-MM-dd"));

            }

            //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/500505448
            FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
            String host = ftpServerInfo.getPrivateHost();
            String ftpUsername = ftpServerInfo.getServerUserName();
            String ftpPassword = ftpServerInfo.getServerPassword();


            String priceZipName = "Overstock_PSSMonthly" + device + "_" + PRICE_TYPE + "_" + startMonth + ".zip";
            String priceFilePath = LOC + domain + File.separator + priceZipName;
            GZipUtil.createZip(priceZipFileNames.toArray(new String[priceZipFileNames.size()]), priceFilePath);
            FTPUtils.copyBySSH(host, ftpUsername, ftpPassword, priceFilePath, LOC + domain + File.separator, 0 , 3);
            for(String filePath : priceZipFileNames){
                File file = new File(filePath);
                if(file.exists()){
                    file.delete();
                }
            }

            String shoppingZipName = "Overstock_PSSMonthly" + device + "_" + SHOPPING_TYPE + "_" + startMonth + ".zip";
            String shoppingFilePath = LOC + domain + File.separator + shoppingZipName;
            GZipUtil.createZip(shoppingZipFileNames.toArray(new String[shoppingZipFileNames.size()]), shoppingFilePath);
            FTPUtils.copyBySSH(host, ftpUsername, ftpPassword, shoppingFilePath, LOC + domain + File.separator,0 , 3);
            for(String filePath : shoppingZipFileNames){
                File file = new File(filePath);
                if(file.exists()){
                    file.delete();
                }
            }

            String starZipName = "Overstock_PSSMonthly" + device + "_" + STAR_TYPE + "_" + startMonth + ".zip";
            String starFilePath = LOC + domain + File.separator + starZipName;
            GZipUtil.createZip(starZipFileNames.toArray(new String[starZipFileNames.size()]), starFilePath);
            FTPUtils.copyBySSH(host, ftpUsername, ftpPassword, starFilePath, LOC + domain + File.separator,0 , 3);
            for(String filePath : starZipFileNames){
                File file = new File(filePath);
                if(file.exists()){
                    file.delete();
                }
            }
            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(sTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

//            sendMailReport("Success", message);
        }catch (Exception e){
            e.printStackTrace();
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//            sendMailReport("Failed", "Error " + message);
        }


    }

    private void extractForOneDay(String rankDate) {

        try {

            int fileDate = FormatUtils.formatDateToYyyyMmDd(FormatUtils.toDate(rankDate,FormatUtils.DATE_PATTERN_2));
            String headerDate = FormatUtils.formatDate(FormatUtils.toDate(rankDate,FormatUtils.DATE_PATTERN_2),"MM/dd/yyyy");

            String priceFileName = LOC + domain + File.separator + getFileName("price", device, fileDate);
            File priceFile = new File(priceFileName);
            addHeadersForExactFile(priceFile,headerDate);
            priceZipFileNames.add(priceFileName);

            String shoppingFileName = LOC + domain + File.separator  + getFileName("shopping", device, fileDate);
            File shoppingFile = new File(shoppingFileName);
            addHeadersForExactFile(shoppingFile,headerDate);
            shoppingZipFileNames.add(shoppingFileName);

            String starFileName = LOC + domain + File.separator  + getFileName("star", device, fileDate);
            File starFile = new File(starFileName);
            addHeadersForExactFile(starFile,headerDate);
            starZipFileNames.add(starFileName);

            int retryCount = 1;
            List<CLRankingDetailEntity> priceList = new ArrayList<>();
            while (true) {
                try {
                    priceList = clDailyRankingEntityDao.exportForDomain185(rankDate, "price", device);
                    break;
                } catch (Exception e) {

                    if (retryCount >= QUERY_TRY_COUNT) {
                        System.out.println("====error extract priceList oid : " + 185);
                        System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + 185);
                        logglyVO.setStatus(LogglyVO.STATUS_NG);
                        String body = new Gson().toJson(logglyVO);
                        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//                        String message = "Error extract priceList oid:" + 185 + ",date:" + rankDate;
//                        sendMailReport("Failed", "Error " + message);
                        return;
                    }

                    e.printStackTrace();
                    System.out.println("====extract error oid:" + 185 + ", sleep " + (1000 * 60 * retryCount));
                    try {
                        Thread.sleep(1000 * 60 * retryCount);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    retryCount++;
                }
            }

            List<String> priceExtract = new ArrayList<>();
            totalCnt = totalCnt + priceList.size();
            for(CLRankingDetailEntity price : priceList){
                priceExtract.add(writeLine(price));
            }
            FileUtils.writeLines(priceFile, "UTF-8", priceExtract, true);


            retryCount = 1;
            List<CLRankingDetailEntity> shoppingList = new ArrayList<>();
            while (true) {
                try {
                    shoppingList = clDailyRankingEntityDao.exportForDomain185(rankDate, "shopping", device);
                    break;
                } catch (Exception e) {

                    if (retryCount >= QUERY_TRY_COUNT) {
                        System.out.println("====error extract shoppingList oid : " + 185);
                        System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + 185);
                        logglyVO.setStatus(LogglyVO.STATUS_NG);
                        String body = new Gson().toJson(logglyVO);
                        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//                        String message = "Error extract shoppingList oid:" + 185 + ",date:" + rankDate;
//                        sendMailReport("Failed", "Error " + message);
                        return;
                    }

                    e.printStackTrace();
                    System.out.println("====extract error shoppingList oid:" + 185 + ", sleep " + (1000 * 60 * retryCount));
                    try {
                        Thread.sleep(1000 * 60 * retryCount);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    retryCount++;
                }
            }
            List<String> shoppingExtract = new ArrayList<>();
            totalCnt = totalCnt + shoppingList.size();
            for(CLRankingDetailEntity shopping : shoppingList){
                shoppingExtract.add(writeLine(shopping));
            }
            FileUtils.writeLines(shoppingFile, "UTF-8", shoppingExtract, true);


            retryCount = 1;
            List<CLRankingDetailEntity> starList = new ArrayList<>();
            while (true) {
                try {
                    starList = clDailyRankingEntityDao.exportForDomain185(rankDate, "star", device);
                    break;
                } catch (Exception e) {

                    if (retryCount >= QUERY_TRY_COUNT) {
                        System.out.println("====error extract starList oid : " + 185);
                        System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + 185);
                        logglyVO.setStatus(LogglyVO.STATUS_NG);
                        String body = new Gson().toJson(logglyVO);
                        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//                        String message = "Error extract starList oid:" + 185 + ",date:" + rankDate;
//                        sendMailReport("Failed", "Error " + message);
                        return;
                    }

                    e.printStackTrace();
                    System.out.println("====extract error starList oid:" + 185 + ", sleep " + (1000 * 60 * retryCount));
                    try {
                        Thread.sleep(1000 * 60 * retryCount);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    retryCount++;
                }
            }
            List<String> starExtract = new ArrayList<>();
            totalCnt = totalCnt + starList.size();
            for(CLRankingDetailEntity star : starList){
                starExtract.add(writeLine(star));
            }
            FileUtils.writeLines(starFile, "UTF-8", starExtract, true);

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public static String writeLine(CLRankingDetailEntity extractVoFor185) {

        StringBuffer line = new StringBuffer();
        line.append(extractVoFor185.getKeywordName()).append(SPLIT);
        line.append(extractVoFor185.getAvgSearchVolume()).append(SPLIT);
        line.append(extractVoFor185.getTrueRank()).append(SPLIT);
        line.append(extractVoFor185.getEstdTraffic()).append(SPLIT);
        line.append(extractVoFor185.getUrl()).append(SPLIT);

        return line.toString();
    }


    public static void addHeadersForExactFile(File outFile, String date) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Keyword").append(SPLIT);
        header.append("Average Search Volume").append(SPLIT);
        header.append("www.overstock.com " + date).append(SPLIT);
        header.append("Estd Traffic " + date).append(SPLIT);
        header.append("Ranked Page").append(SPLIT);
        lines.add(header.toString());

        FileUtils.writeLines(outFile, lines, true);
    }

    private String getFileName(String type, String device, int date) {

        String fileSuffix = ".csv";
        String fileName = "Overstock_PSSMonthly";

        if (device.equalsIgnoreCase(DESKTOP_DEVICE)) {
            fileName += DESKTOP_DEVICE + "_";
        } else if (device.equalsIgnoreCase(MOBILE_DEVICE)) {
            fileName += MOBILE_DEVICE + "_";
        }

        if (type.equalsIgnoreCase(PRICE_TYPE)) {
            fileName += PRICE_TYPE + "Extract_";
        } else if (type.equalsIgnoreCase(SHOPPING_TYPE)) {
            fileName += SHOPPING_TYPE + "Extract_";
        } else if (type.equalsIgnoreCase(STAR_TYPE)) {
            fileName += STAR_TYPE + "Extract_";
        }

        fileName += date + fileSuffix;

        return fileName;
    }

    public static void main(String[] args) {

//        startMonth = "2019-06";
//        device = "desktop";

        if (args != null && args.length >= 1) {
            device = args[0];
        }

        if (args != null && args.length >= 2) {
            startMonth = args[1];
        }else {

            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);// last month

            startMonth = FormatUtils.formatDate(calendar.getTime(), "yyyy-MM");
            System.out.println("======== start to process for oid 185 , processMonth :" + startMonth);
        }

        try {
            ExtractKeywordsByFilterMonthly extractKeywordsByFilterMonthly = new ExtractKeywordsByFilterMonthly();
            extractKeywordsByFilterMonthly.processExtract();

        }catch (Exception e){
            e.printStackTrace();
        }

    }


}
