package seoclarity.backend.export;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import ru.yandex.clickhouse.ClickHouseArray;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.youtube.DataSouceType;
import seoclarity.backend.dao.clickhouse.youtube.YoutubeSummaryDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.entity.clickhouse.ved.YoutubeEntity;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

// https://www.wrike.com/open.htm?id=572629506
public class Extract1391YoutubeData {
    private static final String LOCAL_OUTPUT_FOLDER = "/home/<USER>/";
    private static final String KEY_DELIMITER = "/";

    private static final int OWN_DOMAIN_ID = 1391;
    private static final String FTP_FOLDER = "/home/<USER>/1391";
    private static final int FTP_RETRY_COUNT = 30;

    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    CSVFormat header = CSVFormat.DEFAULT.withHeader("Keyword_name", "Length_text", "Video_id", "Channel_id", "Playlist_id", "General_id", "Video_title",
            "Description_snippet", "Owner_text", "Badges", "Published_time", "View_count", "Rank_type_str", "Verify_status", "Subscriber_count_text", "Video_count_text",
            "True_rank", "Ranking_date").withDelimiter('\t');

    private static final String EMPTY_STRING = "";
    private static final SimpleDateFormat SDF_YYYYMMDD = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat SDF_MMDDYYYY = new SimpleDateFormat("MM-dd-yyyy");

    private YoutubeSummaryDao youtubeSummaryDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ExtractService extractService;
    public Extract1391YoutubeData() {
        youtubeSummaryDao = SpringBeanFactory.getBean("youtubeSummaryDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
    }

    public static void main(String[] args) throws Exception {
        System.out.println("======Parameters:" + new Gson().toJson(args));
        Extract1391YoutubeData instance = new Extract1391YoutubeData();
        if (args != null && args.length == 1) {
            String[] dateArr = args[0].split(",");
            for (String dateStr : dateArr) {
                instance.process(SDF_YYYYMMDD.parse(dateStr));
            }
        } else if (args == null || args.length == 0) {
            instance.process(DateUtil.offsetDay(new Date(), -1));
        }
    }

    private void process(Date dateTime) throws IOException {
        CSVPrinter printer = null;
        try {
            youtubeSummaryDao.changeDataSource(DataSouceType.CdbRi114);//use backup server nj

            String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
            String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(OWN_DOMAIN_ID);
            if (ownDomainEntity == null) {
                System.out.println("=== domain not exist , exit !!");
                try {
                    extractService.sendMailReport("ERROR:Export for inactive OID:" + OWN_DOMAIN_ID, "Please disable export for inactive OID:" + OWN_DOMAIN_ID + "(" + getClass().getName() + ")");
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
                return;
            }

            logglyVO.setoId(String.valueOf(OWN_DOMAIN_ID));
            logglyVO.setName("Extract1391YoutubeData");
            logglyVO.setDevice(ownDomainEntity.isMobileDomain()?"m":"d");

            logglyVO.setpDate(pDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
            logglyVO.setGroups(groupList);


            String processDateStr = SDF_YYYYMMDD.format(dateTime);
            String fileName = OWN_DOMAIN_ID + "-" + SDF_MMDDYYYY.format(dateTime) + "-youtube-ranking.txt";
            String fullPathLocalFilename = LOCAL_OUTPUT_FOLDER + File.separator + fileName;
            System.out.println(" ===ProcessDate:" + processDateStr + " localFile:" + fullPathLocalFilename);
            
            renameExistedFile(fullPathLocalFilename);

            List<YoutubeEntity> dataList = youtubeSummaryDao.getYoutubeDetail(OWN_DOMAIN_ID, processDateStr);
            totalCnt = dataList.size();
            if (dataList != null && dataList.size() > 0) {
                System.out.println(" YoutubeData OID:" + OWN_DOMAIN_ID + " date:" + dateTime + " cnt:" + dataList.size());
                List<String[]> list = new ArrayList<String[]>();
                for (YoutubeEntity entity : dataList) {
                    String[] arr = new String[] {
                            entity.getKeyword(),
                            entity.getVideolength() != null ? entity.getVideolength() : EMPTY_STRING,
                            entity.getVideoId() != null ? entity.getVideoId() : EMPTY_STRING,
                            entity.getChannelId() != null ? entity.getChannelId() : EMPTY_STRING,
                            entity.getPlaylistId() != null ? entity.getPlaylistId() : EMPTY_STRING,
                            entity.getGeneralId() != null ? entity.getGeneralId() : EMPTY_STRING,
                            entity.getVideoTitle() != null ? entity.getVideoTitle() : EMPTY_STRING,
                            entity.getDescriptionSnippet() != null ? entity.getDescriptionSnippet() : EMPTY_STRING,
                            entity.getOwnerText() != null ? entity.getOwnerText() : EMPTY_STRING,
                            getBadgesString(entity.getVideoTag()),
                            entity.getPublishedTime() != null ? entity.getPublishedTime() : EMPTY_STRING,
                            entity.getViewCount() != null ? entity.getViewCount() : EMPTY_STRING,
                            entity.getRankType() != null ? entity.getRankType() : EMPTY_STRING,
                            entity.getVerifyStatus() != null ? entity.getVerifyStatus() : EMPTY_STRING,
                            entity.getSubscriberCount() != null ? entity.getSubscriberCount() : EMPTY_STRING,
                            entity.getVideoCount() != null ? entity.getVideoCount() : EMPTY_STRING,
                            String.valueOf(entity.getTrueRank()),
                            entity.getRankDateStr() != null ? entity.getRankDateStr() : EMPTY_STRING
                    };
                    list.add(arr);
                }

                printer = new CSVPrinter(new FileWriter(fullPathLocalFilename), header);
                printer.printRecords(list);
                printer.flush();
                printer.close();

                boolean savedToFTP = FTPUtils.saveFileToFTPWithRetryCount(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, OWN_DOMAIN_ID,
                        fullPathLocalFilename, FTP_FOLDER, FTP_RETRY_COUNT);
                System.out.println(" ===SaveFileToFTP:" + savedToFTP + " localFile:" + fullPathLocalFilename + " FTP_FOLDER:" + FTP_FOLDER);

                try {
                    FileUtils.deleteQuietly(new File(fullPathLocalFilename));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                System.out.println(" NoYoutubeData OID:" + OWN_DOMAIN_ID + " Date:" + dateTime);
//                sendEmailReport(new Date(), " NoYoutubeData OID:" + OWN_DOMAIN_ID + " date:" + dateTime);
            }

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(stTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

        } catch (Exception e) {
            e.printStackTrace();

            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

//            sendEmailReport(new Date(), e.getMessage());
        } finally {
            if (printer != null) {
                printer.close();
            }
        }
    }
    
    private void renameExistedFile(String fullPathFileName) {
    	File outputFile = new File(fullPathFileName);
        if (outputFile.exists()) {
            String backupFileName = fullPathFileName + "bk" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
            outputFile.renameTo(new File(backupFileName));
            System.out.println(" ===RenamedFileAsBk:" + fullPathFileName + "-->" + backupFileName);
        }
    }

    private String getBadgesString(ClickHouseArray badgesList) {
//        StringBuffer sb = new StringBuffer();
//        sb.append("[");
//        if (badgesList != null && badgesList.size() > 0) {
//            for (int k = 0; k < badgesList.size(); k++) {
//                if (k > 0) {
//                    sb.append(",");
//                }
//                sb.append("'").append(badgesList.get(k)).append("'");
//            }
//        }
//        sb.append("]");
        String videoStr = "[]";
        try {
            if(badgesList != null && badgesList.getArray() != null) {
                videoStr = JSONUtil.toJsonStr(badgesList.getArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return videoStr;
    }
    
}