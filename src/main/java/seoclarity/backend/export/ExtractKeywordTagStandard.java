package seoclarity.backend.export;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.TagParentChildRelDAO;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.GroupTagRelationEntity;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ServerAuthenticationInfoEntity;
import seoclarity.backend.entity.actonia.TagParentChildRelEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;
import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.*;

@CommonsLog
public class ExtractKeywordTagStandard {
	
	private static String LOC = "/home/<USER>/";
	private static final int DOMAIN_NYTIMES = 13237;

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "=>";
    
    private static boolean extractAllKeyword = false;
    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private KeywordEntityDAO keywordEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private TagParentChildRelDAO tagParentChildRelDAO;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private ExtractService extractService;

    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();
    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(DOMAIN_NYTIMES, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP_GCS);
    }
    public ExtractKeywordTagStandard() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        tagParentChildRelDAO = SpringBeanFactory.getBean("tagParentChildRelDAO");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        extractService = SpringBeanFactory.getBean("extractService");
    }
    
    public static void main(String[] args) {
        ExtractKeywordTagStandard extractKeywordTagStandard = new ExtractKeywordTagStandard();
        try {
            int domainId = Integer.parseInt(args[0]);
            String rankingDate = "";
            if(args.length >= 2){
                extractAllKeyword = Boolean.parseBoolean(args[1]);
            }
            if (args.length >= 3 && StringUtils.containsIgnoreCase(args[2], ",")) {
                Date sDate = FormatUtils.toDate(args[2].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[2].split(",")[1], FormatUtils.DATE_PATTERN_2);
                while (sDate.compareTo(eDate) <= 0) {
                    rankingDate = FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2);
                    extractKeywordTagStandard.process(domainId, rankingDate);
                    sDate = DateUtils.addDays(sDate, 1);
                }
            } else {
                rankingDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -1), FormatUtils.DATE_PATTERN_2);
                extractKeywordTagStandard.process(domainId, rankingDate);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private void process(int domainId, String processingDate) {
        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
        if(ownDomainEntity == null){
            log.info("====ownDomainEntity not exist:" + domainId);
            try {
                extractService.sendMailReport("ERROR:Export for inactive OID:" + domainId, "Please disable export for inactive OID:" + domainId + "(" + getClass().getName() + ")");
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            return;
        }
        String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
        String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

        logglyVO.setoId(String.valueOf(domainId));
        logglyVO.setName("ExtractKeywordTagStandard");
        logglyVO.setDevice(ownDomainEntity.isMobileDomain()?"m":"d");

        logglyVO.setpDate(pDate);
        List<String> groupList = new ArrayList<>();
        groupList.add(LogglyVO.GROUP_KEYWORD_AND_TAG_EXTRACT);
        logglyVO.setGroups(groupList);

        boolean withTagHierarchy = false;
        String fileName = getFileName(ownDomainEntity, processingDate);
        String localFilePath = LOC + domainId + File.separator;
        String remoteFilePath = localFilePath;
        if (domainId == 7006) {
            remoteFilePath = localFilePath + "www.truecar.com_keyword_tags" + File.separator;
        } else if (domainId == DOMAIN_NYTIMES){
            remoteFilePath = "223126";//common_param id
            withTagHierarchy = true;
        }
        
        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }
        System.out.println("======StartExtract OID:" + domainId + " targetDate:" + processingDate + " allKW:" + extractAllKeyword + 
        	" withTagHierarchy:" + withTagHierarchy + " localFile:" + localFileName + " remoteFilePath:" + remoteFilePath);
        
        try {
            addHeadersForExactFile(localFile, withTagHierarchy);
            List<String> extractLines = new ArrayList<String>();
            if(extractAllKeyword){
            	if (domainId == DOMAIN_NYTIMES) { // TODO
            		getExtractDataWithTagHierarchy(domainId, processingDate, withTagHierarchy, extractLines);               
            	} else {
            		log.info("====extractAllKW:" + domainId);
                    List<KeywordEntity> keywordList = keywordEntityDAO.getKeywordAndTagListByDomainId(domainId, GroupTagRelationEntity.RESOURCE_TYPE_KEYWORD);
                    System.out.println("***** keywordList size : " + keywordList.size());
                    if(CollectionUtils.isNotEmpty(keywordList)){
                        for(KeywordEntity keywordEntity : keywordList){
                            String decodeKeywordName = URLDecoder.decode(keywordEntity.getKeywordName(), "utf-8");
                            String tagNames = keywordEntity.getTagName();
                            if(StringUtils.isNotBlank(tagNames)){
                                String[] tagNameArr = tagNames.split("\\|");
                                for(String tag : tagNameArr){
                                    extractLines.add(appendData(decodeKeywordName, tag, processingDate));
                                }
                            }else {
                                extractLines.add(appendData(decodeKeywordName, null, processingDate));
                            }
                        }
                    }
            	}
            } else {
                log.info("====extractKeywordOnlyTag : " + domainId);
                List<KeywordEntity> keywordEntityList = keywordEntityDAO.geKeywordTagList(domainId);
                if(CollectionUtils.isNotEmpty(keywordEntityList)){
                    for(KeywordEntity keywordEntity : keywordEntityList){
                        String decodeKeywordName = URLDecoder.decode(keywordEntity.getKeywordName(), "utf-8");
                        extractLines.add(appendData(decodeKeywordName, keywordEntity.getTagName(), processingDate));
                    }
                }
            }
            totalCnt = extractLines.size();
            FileUtils.writeLines(localFile, extractLines, true);
            
            int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
            if (SPECIAL_DOMAIN_SERVER_MAP.get(domainId) != null) {
                serverType = SPECIAL_DOMAIN_SERVER_MAP.get(domainId);
            }
            try {
                if(serverType != ServerAuthenticationInfoEntity.SERVER_TYPE_FTP){
                    serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, domainId, localFileName, remoteFilePath, null);
                }else {
                    serverAuthenticationInfoService.copyFileToRemoteServer(serverType, domainId, localFileName, remoteFilePath, null);
                }
            }catch (Exception e){
                log.error("====trans file error :" + domainId + ",fileName:" +fileName + ",processingDate:" + processingDate);
                logglyVO.setStatus(LogglyVO.STATUS_NG);
                String body = new Gson().toJson(logglyVO);
                LogglyUtils.sendLoggly(body, LogglyUtils.EXTRACT_TAG_NAME);
//                String subject = getEmailSubject(domainId, false, fileName, processingDate);
//                String message = subject;
//                sendMailReport(subject, message);
                e.printStackTrace();
            }

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(stTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        } catch (Exception e){
            e.printStackTrace();
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
            extractService.sendMailReport("ExtractKeywordTagStandard ERROR(OID:" + domainId + ")", e.getMessage());
        }
    }
    
    private void getExtractDataWithTagHierarchy(int domainId, String processingDate, boolean withTagHierarchy, List<String> extractLineList) throws Exception {
        List<KeywordEntity> keywordList = keywordEntityDAO.getKeywordTagList(domainId, GroupTagRelationEntity.RESOURCE_TYPE_KEYWORD);
        if(CollectionUtils.isNotEmpty(keywordList)){
        	List<GroupTagEntity> tagList = groupTagEntityDAO.getTagEntityByType(domainId, GroupTagEntity.TAG_TYPE_KEYWORD);
            List<TagParentChildRelEntity> tagParentChildRelList = tagParentChildRelDAO.getTagEntityByType(domainId, GroupTagEntity.TAG_TYPE_KEYWORD);
            Map<Integer, String> tagMap = constructTagMap(tagList);
            Map<Integer, Integer> tagRelMap = constructTagRelMap(tagParentChildRelList);
            System.out.println("===extractKWTagWithHierarchy OID:" + domainId + " kwCnt:" + keywordList.size() + " tagCnt:" + tagList.size() + 
            	"=>" + tagMap.size() + " tagRelCnt:" + tagParentChildRelList.size() + "=>" + tagRelMap.size());
            
            Map<Integer, String> tagHierarchyMap = new HashMap<Integer, String>();
            for (KeywordEntity keywordEntity : keywordList){
                String decodeKeywordName = URLDecoder.decode(keywordEntity.getKeywordName(), "utf-8");
                Long tagId = keywordEntity.getTagId();
                String tagName = keywordEntity.getTagName();
                if(StringUtils.isNotBlank(tagName)){
                	extractLineList.add(constructOutputLine(withTagHierarchy, decodeKeywordName, tagName, 
                    	getTagHierarchy(tagId.intValue(), tagHierarchyMap, tagRelMap, tagMap), processingDate));
                } else {
                	System.out.println(" KWNoTag:" + decodeKeywordName);
                	extractLineList.add(constructOutputLine(withTagHierarchy, decodeKeywordName, null, null, processingDate));
                }
            }
        } else {
        	System.out.println("======Abnormal NoKeyword OID:" + domainId);
        }
    }
    
    private String getTagHierarchy(int tagId, Map<Integer, String> tagHierarchyMap, Map<Integer, Integer> tagRelMap, Map<Integer, String> tagMap) {
    	if (tagHierarchyMap.containsKey(tagId)) {
    		return tagHierarchyMap.get(tagId);
    	} else {
    		String hierarchyStr = getTagHierarchyStr(tagId, tagHierarchyMap, tagRelMap, tagMap);
    		if (StringUtils.isNotEmpty(hierarchyStr)) {
    			tagHierarchyMap.put(tagId, hierarchyStr);
    			return hierarchyStr;
    		}
    	}
    	return "";
    }
    
    private String getTagHierarchyStr(int tagId, Map<Integer, String> tagHierarchyMap, Map<Integer, Integer> tagRelMap, Map<Integer, String> tagMap) {
    	StringBuffer sBuffer = new StringBuffer();
    	boolean hasParent = false;
    	if (tagRelMap.containsKey(tagId)) {
    		int idx = 0;
    		int childTagId = tagId;
    		Set<Integer> tagIdSet = new HashSet<Integer>(); // To avoid infinite loop
    		tagIdSet.add(tagId);
    		while (true) {
        		Integer parentTagId = tagRelMap.get(childTagId);
        		if (parentTagId != null) {
        			if (tagIdSet.contains(parentTagId)) {
        				System.out.println(" ExitForDupTagInChain(start:" + tagId + " idx:" + idx + ")" + childTagId + "->" + parentTagId);
        				break;
        			} else {
        				tagIdSet.add(parentTagId);
        			}
        			String tagName = tagMap.get(parentTagId);
        			if (StringUtils.isNotEmpty(tagName)) {
        				sBuffer.append(idx > 0 ? TAG_SPLIT : "").append(tagName);
        				hasParent = true;
        			}
        			System.out.println(" FoundParentTag(start:" + tagId + " idx:" + idx + ")" + childTagId + "->" + parentTagId);
        		} else {
        			break;
        		}
        		childTagId = parentTagId;
        		idx++;
        	}
    	}
    	if (hasParent) {
    		String tagHierarchyStr = sBuffer.toString();
    		tagHierarchyMap.put(tagId, tagHierarchyStr);
    		return tagHierarchyStr;
    	}
    	System.out.println(" NoParentTag:" + tagId);
    	return null;
    }
    
    private Map<Integer, String> constructTagMap(List<GroupTagEntity> tagList) {
    	Map<Integer, String> tagMap = new HashMap<Integer, String>();
    	if (tagList != null && tagList.size() > 0) {
    		for (GroupTagEntity tagEntity : tagList) {
    			tagMap.put(tagEntity.getId(), tagEntity.getTagName());
    		}
    	}
    	return tagMap;
    }
    
    private Map<Integer, Integer> constructTagRelMap(List<TagParentChildRelEntity> tagParentChildRelList) {
    	Map<Integer, Integer> tagRelMap = new HashMap<Integer, Integer>();
    	if (tagParentChildRelList != null && tagParentChildRelList.size() > 0) {
    		for (TagParentChildRelEntity tagParentChildRelEntity : tagParentChildRelList) {
    			tagRelMap.put(tagParentChildRelEntity.getChildTagId(), tagParentChildRelEntity.getParentTagId());
    		}
    	}
    	return tagRelMap;
    }

    private String getFileName(OwnDomainEntity ownDomainEntity, String processingDate) {
        String fileName = "";
        int domainId = ownDomainEntity.getId();
        if (domainId == 7006) {
            fileName = ownDomainEntity.getDomain() + "_" + processingDate + "_Keyword-Tags.txt";
        } else {
            fileName = domainId + "_KeywordTagExtract_" + processingDate  + ".csv"; // TODO
        }
        return fileName;
    }

    private void addHeadersForExactFile(File outFile, boolean withTagHierarchy) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Keyword").append(SPLIT);
        header.append("GroupTag").append(SPLIT);
        if (withTagHierarchy) {
        	header.append("TagHierarchy").append(SPLIT);
        }
        header.append("Date");
        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    private static String constructOutputLine(boolean withTagHierarchy, String keywordName, String tagName, String tagHierarchy, String processingDate) {
        StringBuffer line = new StringBuffer();
        line.append(keywordName).append(SPLIT);
        line.append(StringUtils.isBlank(tagName) ? "-" : tagName).append(SPLIT);
        if (withTagHierarchy) {
        	line.append(StringUtils.isBlank(tagHierarchy) ? "-" : tagHierarchy).append(SPLIT);
        }
        line.append(processingDate);
        return line.toString();
    }
    
    private static String appendData(String keywordName, String tagName, String processingDate) {
        StringBuffer line = new StringBuffer();
        line.append(keywordName).append(SPLIT);
        line.append(StringUtils.isBlank(tagName) ? "-" : tagName).append(SPLIT);
        line.append(processingDate);
        return line.toString();
    }

    private String getEmailSubject(int domainId, boolean success, String fileName, String rankingDate) {
        String subject = "";
        String status = success ? "Success" : "Failed";
        switch (domainId) {
            case 7006 :
                subject = status + " Export 7006  " + fileName + "  " + rankingDate;
            default:
                break;
        }
        return subject;
    }
}