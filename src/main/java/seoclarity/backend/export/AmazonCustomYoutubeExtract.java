package seoclarity.backend.export;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.youtube.YoutubeSummaryDao;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.ved.YoutubeEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;

@CommonsLog
public class AmazonCustomYoutubeExtract {

    private static final String SPLIT = "\t";
    private static String KEY_SPLIT = "!_!";
    private static final int QUERY_TRY_COUNT = 10;
    private static String LOC = "/home/<USER>/";
    public static final String VIDEO_URL_PREFIX = "https://youtu.be/";
    public static final String FULL_VIDEO_URL_PREFIX = "https://www.youtube.com/watch?v=";

    private static String startDate;
    private static String endDate;

    public static final Map<String, String> VIDEO_ID_MAP = new HashMap();

    static {
        VIDEO_ID_MAP.put("vHaP_oYD4EY", "");
        VIDEO_ID_MAP.put("tCEBAbvqjnk", "");
        VIDEO_ID_MAP.put("K7Uf-OzvwJQ", "");
        VIDEO_ID_MAP.put("7_nzbCYEEw4", "");
        VIDEO_ID_MAP.put("iBGu7txWmNw", "");
        VIDEO_ID_MAP.put("-z2K7PoYfZo", "");
        VIDEO_ID_MAP.put("neM6H40M-ug", "");
        VIDEO_ID_MAP.put("lkej5z4q8AY", "");
        VIDEO_ID_MAP.put("btvrECYpk5M", "");
        VIDEO_ID_MAP.put("qX7RfIcOToo", "");
        VIDEO_ID_MAP.put("IilZnsl9wQg", "");
        VIDEO_ID_MAP.put("SiQrcmnvj0U", "");
        VIDEO_ID_MAP.put("3Zeut_-6j98", "");
        VIDEO_ID_MAP.put("LeS37OR5yUI", "");
        VIDEO_ID_MAP.put("v-ufH_Ovyls", "");
        VIDEO_ID_MAP.put("pPgS6ZCoUbY", "");
    }

    public static final List<String> VIDEO_ID_LIST = Arrays.asList(
            "vHaP_oYD4EY", "tCEBAbvqjnk", "K7Uf-OzvwJQ", "7_nzbCYEEw4",
            "iBGu7txWmNw", "-z2K7PoYfZo", "neM6H40M-ug", "lkej5z4q8AY",
            "btvrECYpk5M", "qX7RfIcOToo", "IilZnsl9wQg", "SiQrcmnvj0U",
            "3Zeut_-6j98", "LeS37OR5yUI", "v-ufH_Ovyls", "pPgS6ZCoUbY"
    );
    public static final List<Long> KEYWORD_RANKCHECK_ID_LIST = Arrays.asList(
            308704l, 950685l, 2645715l, 3341739l, 6217406l,
            18293401l, 26670978l, 99923415l, 100255073l, 104513404l,
            104914331l, 111210857l, 127038517l, 155560560l, 160312857l,
            160467661l, 165754272l, 165770982l, 165781498l,
            165840759l, 172830271l, 187940272l, 201358569l,
            214469950l, 215061901l, 216032451l, 216955462l,
            232694342l, 232694350l, 232694353l, 232694355l,
            232694358l
    );

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private YoutubeSummaryDao youtubeSummaryDao;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;

    public AmazonCustomYoutubeExtract() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        youtubeSummaryDao = SpringBeanFactory.getBean("youtubeSummaryDao");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
    }


    private void processDomain(int domainId, String processingDate) {

        int retryCount = 0;
        while (true) {
            try {
                System.out.println("********************** for domain " + domainId + " *******************");
                String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
                process(domainId, processingDate);
                break;
            } catch (Exception e) {
                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
                    String message = "Rover extract " + domainId + " " + processingDate + " Failed !!! ";
//                    sendMailReport("Failed !!!! ", message);
                    break;
                }
                e.printStackTrace();
                System.out.println("====domain error :" + domainId + ", sleep 20s ");
                try {
                    Thread.sleep(1000 * 20);
                } catch (Exception ex) {

                }
                retryCount++;
            }
        }

    }

    private void process(int domainId, String logDate) throws Exception {
        System.out.println("========start to extract domain : " + domainId + ",logDate: " + logDate);
        try {

            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(domainId);
            if (ownDomainEntity == null) {
                System.out.println("=== domain not exist , exit !!");
                return;
            }

            String fileName = ownDomainEntity.getDomain() + "_YoutubeExtract_" + logDate + "_Desktop" + ".csv";
            String filePath = LOC + domainId + File.separator + fileName;
            File outFile = new File(filePath);
            if (outFile.exists()) {
                outFile.delete();
            }
            addHeadersForExactFile(outFile);
            List<String> extractLines = new ArrayList<String>();

            int youtubeEngineId = 130;
            int youtubeLanguageId = 1;

            int googleEngineId = 217;
            int googleLanguageId = 1;

            List<YoutubeEntity> youtubeEntities = youtubeSummaryDao.getExtractYoutubeDetail(domainId, youtubeEngineId, youtubeLanguageId, logDate, KEYWORD_RANKCHECK_ID_LIST, VIDEO_ID_LIST);
            log.info("===youtubeEntitiesSize:" + youtubeEntities.size());
            List<CLRankingDetailEntity> clRankingDetailEntities = clDailyRankingEntityDao.exportYoutubeWithKeywordList(googleEngineId, googleLanguageId, false, logDate, domainId, KEYWORD_RANKCHECK_ID_LIST);

            Map<String, CLRankingDetailEntity> margeResultMap = new HashMap<>();
            for (CLRankingDetailEntity clRankingDetail : clRankingDetailEntities) {
                String videoId = clRankingDetail.getUrl().replaceAll("https://www.youtube.com/watch\\?v=", "");
                int trueRank = clRankingDetail.getTrueRank();
                String keyword = clRankingDetail.getKeywordName();
                log.info("===videoId:" + videoId);
                if(videoId.contains("youtube.com")){//mobile
                    videoId = clRankingDetail.getUrl().replaceAll("https://m.youtube.com/watch\\?v=", "");
                }
                clRankingDetail.setVideoId(videoId);
                if(VIDEO_ID_LIST.contains(videoId)){
                    margeResultMap.put(keyword + KEY_SPLIT + videoId, clRankingDetail);
                }
            }
            log.info("===margeResultMapGoogleSIZE:" + margeResultMap.size());

            for(YoutubeEntity youtubeEntity: youtubeEntities){
                String keyword = youtubeEntity.getKeyword();
                String videoId = youtubeEntity.getVideoId();
                CLRankingDetailEntity clRankingDetail = margeResultMap.get(keyword + KEY_SPLIT + videoId);
                if(clRankingDetail == null){
                    clRankingDetail = new CLRankingDetailEntity();
                    clRankingDetail.setYoutubeRank(youtubeEntity.getTrueRank());
                    clRankingDetail.setKeywordName(keyword);
                    clRankingDetail.setVideoId(videoId);
                }else {
                    clRankingDetail.setYoutubeRank(youtubeEntity.getTrueRank());
                }
                margeResultMap.put(keyword + KEY_SPLIT + videoId, clRankingDetail);
            }

            log.info("===margeResultMapSIZE:" + margeResultMap.size());
            for(String key: margeResultMap.keySet()){
                String keywordName = key.split(KEY_SPLIT)[0];
                String videoId = key.split(KEY_SPLIT)[1];
                extractLines.add(appendData(videoId, keywordName, margeResultMap.get(key).getYoutubeRank(),
                        margeResultMap.get(key).getTrueRank(), margeResultMap.get(key).getSubRank()));
            }
            FileUtils.writeLines(outFile, extractLines, true);

            //send to ftp
//            FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, zipFileName, LOC + 7605 + File.separator +"dailyRank",0 , 3);
//            String message = "";
//            message = "Rover extract " + domainId + "  " + device + " " + rankDate + " Success";
//            sendMailReport("Success !!!! ", message);

        } catch (Exception e) {
//            e.printStackTrace();
            throw e;
        }

    }

    public static void addHeadersForExactFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Video URL").append(SPLIT);
        header.append("Full Video URL").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("YouTube Ranking").append(SPLIT);
        header.append("Google Ranking");
        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public static String appendData(String videoId, String keyword, Integer youtubeRanking,
                                    Integer googleDetailRanking, Integer googleSubRanking) {
        StringBuffer line = new StringBuffer();
        line.append(VIDEO_URL_PREFIX + videoId).append(SPLIT);
        line.append(FULL_VIDEO_URL_PREFIX + videoId).append(SPLIT);
        line.append(keyword).append(SPLIT);
        line.append(youtubeRanking == null ? "-" : youtubeRanking).append(SPLIT);
        line.append((googleDetailRanking == null || googleSubRanking == null) ? "-": googleDetailRanking + "-" + googleSubRanking);
        return line.toString();
    }

    public static void main(String[] args) {
        String[] domainIds = null;

        if (args != null && args.length > 0) {
            if (args[0].contains(",")) {
                domainIds = args[0].split(",");
            } else {
                domainIds = new String[]{args[0]};
            }
        } else {
            System.out.println("===param error!!");
            return;
        }

        Date sDate = null;
        Date eDate = null;
        if (args != null && args.length > 2 && !args[1].equalsIgnoreCase("null") && !args[2].equalsIgnoreCase("null")) {
            startDate = args[1];
            endDate = args[2];
            sDate = FormatUtils.toDate(args[1], FormatUtils.DATE_PATTERN_2);
            eDate = FormatUtils.toDate(args[2], FormatUtils.DATE_PATTERN_2);
        } else {
            Date sTime = FormatUtils.getYesterday(true);
            startDate = FormatUtils.formatDate(sTime, FormatUtils.DATE_PATTERN_2);
            endDate = FormatUtils.formatDate(sTime, FormatUtils.DATE_PATTERN_2);
            sDate = sTime;
            eDate = sTime;
        }

        System.out.println("=====domainIds: " + domainIds + ",startDate: " + startDate + ",endDate: " + endDate);

        AmazonCustomYoutubeExtract amazonCustomYoutubeExtract = new AmazonCustomYoutubeExtract();

        while (sDate.compareTo(eDate) <= 0) {
            for (String oid : domainIds) {
                amazonCustomYoutubeExtract.processDomain(Integer.parseInt(oid), FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2));
            }
            sDate = DateUtils.addDays(sDate, 1);
        }

    }

}
