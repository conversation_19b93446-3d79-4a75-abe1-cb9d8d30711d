//package seoclarity.backend.export;
//
//import com.actonia.common.vo.RankIntelligenceQueryForClarityDBVO;
//import com.google.gson.Gson;
//import org.apache.commons.collections.map.HashedMap;
//import org.apache.commons.lang.StringUtils;
//
//import org.w3c.dom.Document;
//import org.w3c.dom.Element;
//import seoclarity.backend.dao.actonia.ExportInfoDAO;
//import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
//import seoclarity.backend.entity.KeywordRankEntityVO;
//import seoclarity.backend.entity.actonia.TExportInfoEntity;
//import seoclarity.backend.utils.ClarityDBUtils;
//import seoclarity.backend.utils.ExportTaskUtils;
//import seoclarity.backend.utils.RankCheckUtils;
//import seoclarity.backend.utils.SpringBeanFactory;
//
//import javax.xml.parsers.DocumentBuilder;
//import javax.xml.parsers.DocumentBuilderFactory;
//import javax.xml.transform.OutputKeys;
//import javax.xml.transform.Transformer;
//import javax.xml.transform.TransformerFactory;
//import javax.xml.transform.dom.DOMSource;
//import javax.xml.transform.stream.StreamResult;
//import java.io.*;
//import java.util.*;
//
//public class ExportThread extends Thread {
//
//
//	private static ClDailyRankingEntityDao clDailyRankingEntityDao;
//	private static ExportInfoDAO exportInfoDAO;
//
//
//	public static final Integer STATUS_CREATED = 0;
//
//	public static final Integer STATUS_PROCESSING = 1;
//
//	public static final Integer STATUS_DONE = 2;
//
//	public static final Integer STATUS_ERROR = 5;
//
//	public static Gson gson = new Gson();
//	private TExportInfoEntity tExportInfoEntity;
//
//	public ExportThread(TExportInfoEntity tExportInfoEntity) {
//		this.tExportInfoEntity = tExportInfoEntity;
//		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
//		exportInfoDAO = SpringBeanFactory.getBean("exportInfoDAO");
//	}
//
//	@Override
//	public void run() {
//		try {
//			// 一个任务来了
//			synchronized (this) {
//				int flg = checkParam(tExportInfoEntity);
//				if (flg == 1) {
//					System.out.println(" param check success , next step : taskid = " + tExportInfoEntity.getId());
////					System.out.println(tExportInfoEntity.getExportParam());
//					exprtAndUpdateStatus(tExportInfoEntity);
//				} else {
//					System.out.println("Param check failed! ");
//				}
//			}
//			Thread.sleep(1000);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//
//	private static int checkParam(TExportInfoEntity tExportInfoEntity) {
//		System.out.println(" ===================check params ==============");
//		if (tExportInfoEntity.getExportParam() == null) {
//			return 0;
//		} else {
//			String param = tExportInfoEntity.getExportParam();
//			RankIntelligenceQueryForClarityDBVO paramVO = null;
//			paramVO = gson.fromJson(param, RankIntelligenceQueryForClarityDBVO.class);
////			if (null == paramVO.getQueryKeywords() || paramVO.getQueryKeywords().length <= 0) {
////				System.out.println(" no : QueryKeywords ");
////				return 0;
////			}
//			if (null == paramVO.getIndexList() || paramVO.getIndexList().length <= 0) {
//				System.out.println("  no :Index List");
//				return 0;
//			}
//			if (null == paramVO.getDateRangeTableMapList() || paramVO.getDateRangeTableMapList().size() < 0) {
//				System.out.println(" no :DateRangeTableMapList ");
//				return 0;
//			} else {
//				for (Map<String, String> map : paramVO.getDateRangeTableMapList()) {
//					if (StringUtils.isBlank(map.get("info"))) {
//						System.out.println("  no : info ");
//						return 0;
//					}
//					if (StringUtils.isBlank(map.get("detailTable"))) {
//						System.out.println(" no : detailTable ");
//						return 0;
//					}
//					if (StringUtils.isBlank(map.get("queryStart"))) {
//						System.out.println(" no : queryStart ");
//						return 0;
//					}
//					if (StringUtils.isBlank(map.get("queryEnd"))) {
//						System.out.println(" no : queryEnd ");
//						return 0;
//					}
//				}
//			}
//		}
//		return 1;
//	}
//
//	private void exprtAndUpdateStatus(TExportInfoEntity tExportInfoEntity) {
//		System.out.println("==========================get export_param========================");
//		List<TExportInfoEntity> paramList = exportInfoDAO.exprtAndUpdateStatus(tExportInfoEntity);
//		if (paramList.size() > 0) {
//			System.out.println(" ================change status = 1 ====================");
//			//todo
//			int count = exportInfoDAO.updateStatus(tExportInfoEntity.getId(), TExportInfoEntity.STATUS_PROCESSING);
//			if (count > 0) {
//				System.out.println(tExportInfoEntity.getId() + " update success  ");
//			}
//			//todo
//			String json = tExportInfoEntity.getExportParam();
//			RankIntelligenceQueryForClarityDBVO paramVO = null;
//			paramVO = gson.fromJson(json, RankIntelligenceQueryForClarityDBVO.class);
//			List<Map<String, String>> dateRangeTableMapList = new ArrayList<>();
//			List<Map<String, Object>> kidList = new ArrayList();
//			if (paramVO.getDateRangeTableMapList().size() > 0 && paramVO.getDateRangeTableMapList() != null) {
//				dateRangeTableMapList = paramVO.getDateRangeTableMapList();
//				// get  dateRangeTableMapList
//				for (Map<String, String> map : dateRangeTableMapList) {
//					List<Map<String, Object>> kidlist = clDailyRankingEntityDao.getKeywordsByPageV2(map, paramVO);
//					kidList.addAll(kidlist);
//				}
//			}
//			try {
//				System.out.println("kidList.size() : " + kidList.size());
//				if (kidList.size() > 0) {
//					synchronized (this) {
//						paramForExport(kidList, paramVO, tExportInfoEntity.getId());
//					}
//				}
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//
//
//		}
//
//
//	}
//
//	private static String paramForExport(List<Map<String, Object>> kidList, RankIntelligenceQueryForClarityDBVO paramVo, Integer id) throws Exception {
//
//		Map<String, String[]> kwNameMap = new HashMap<String, String[]>();
//		Map<String, List<String>> queryMap = new HashMap<String, List<String>>();
//		List<Map<String, Object>> resValList = new ArrayList<Map<String, Object>>();
//		System.out.println(" kidList --------------");
//
//		if (kidList != null && kidList.size() > 0) {
//
//			List<Map<String, String>> dateRangeTableMapList = paramVo.getDateRangeTableMapList();
//
//			for (Map<String, Object> obj : kidList) {
//				String keyword_rankcheck_id = String.valueOf(obj.get("keyword_rankcheck_id"));
//				String location_id = String.valueOf(obj.get("location_id"));
//				String keyword_name = String.valueOf(obj.get("keyword_name"));
//				String ranking_date = String.valueOf(obj.get("ranking_date"));
//				String avg_search_volume = String.valueOf(obj.get("avg_search_volume"));
//
//				String key = keyword_rankcheck_id + "-" + location_id;
//				kwNameMap.put(key + "-" + StringUtils.replace(ranking_date, "-", ""), new String[]{keyword_name, avg_search_volume});
//				if (queryMap.containsKey(key)) {
//					queryMap.get(key).add(ranking_date);
//				} else {
//					List<String> dateList = new ArrayList<String>();
//					dateList.add(ranking_date);
//					queryMap.put(key, dateList);
//				}
//			}
//
//			try {
//				Date dateFlg = new Date();
//				System.out.println(" export begin : " + dateFlg);
//				DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
//				DocumentBuilder builder = factory.newDocumentBuilder();
//				Document d = builder.newDocument();
//				TransformerFactory transFactory = TransformerFactory.newInstance();
//				Transformer transformer = transFactory.newTransformer();
//				transformer.setOutputProperty("indent", "yes");
//				transformer.setOutputProperty(OutputKeys.ENCODING, "utf-8");
//				d.setXmlStandalone(true);
//				File file = new File("word/" + id + "-" + dateFlg + ".xml");
//				Element element = d.createElement("keywords");
//				Element elementChildOne = d.createElement("test");
//				elementChildOne.setAttribute("attr", "test");
//				element.appendChild(elementChildOne);
//				d.appendChild(element);
//				String str = toStringFromDoc(d);
//				System.out.println("str : " + str);
//				String subStr = str.substring(0, str.indexOf("<keywords>") + 10);
//				FileOutputStream fos = new FileOutputStream(file, true);
//				fos.write(subStr.getBytes());//将字符串写入到指定的路径下的文件中
//
//				Integer locationid = null;
//				List<Integer> kids = new ArrayList();
//				for (String key : queryMap.keySet()) {
//					Map map = new HashedMap();
//					Integer kid = Integer.valueOf(StringUtils.split(key, '-')[0]);
//					map.put("kid", kid);
//					locationid = Integer.valueOf(StringUtils.split(key, '-')[1]);
//					kids.add(kid);
//				}
//
//				System.out.println("------------------------ Split the tenKeysList into a group of 10 ----------------------");
//				List<List<Integer>> tenKeysList = tnewList(kids);
//				System.out.println("tenKeysList : " + tenKeysList.size());
//
//				System.out.println("---------------------------------- get rootDomains ------------------------------");
//				boolean isFolderDomain = ExportTaskUtils.isFolderDomain(paramVo.getOwnDomainName());
//				String domainName = isFolderDomain ? StringUtils.substringBefore(paramVo.getOwnDomainName(), "/") : paramVo.getOwnDomainName();
//				String rootDomainReverse = StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(domainName), '.');
//				List<String> rootDomains = new ArrayList<String>();
//				if (paramVo.getCompetitorDomainList() != null && paramVo.getCompetitorDomainList().size() > 0) {
//					for (String[] competitors : paramVo.getCompetitorDomainList()) {
//						rootDomains.add(competitors[0]);
//					}
//				}
//				rootDomains.add(rootDomainReverse);
//				rootDomains.add("com.google");
//				System.out.println(rootDomains);
//				for (List<Integer> tenList : tenKeysList) {
//					System.out.println(tenList);
//					Long startTime10 = System.currentTimeMillis();
//					for (Map<String, String> map : dateRangeTableMapList) {
////						Map<String, String> map = dateRangeTableMapList.get(0);
//						System.out.println(" map : " + map);
//						String list = map.get("queryDateList");
//						List t = Arrays.asList(list.split(","));
//						paramVo.setWeeklyDates(t);
////							System.out.println(" 执行非rating 开始 " + new Date());
////							List<Map<String, Object>> resultList = clDailyRankingEntityDao.getKeywordDetailRankingsV2(paramVo, false, tenList, locationid, map);
////							System.out.println(" 执行非rating 结束 " + new Date());
////							List<Map<String, Object>> ratingResultList = clDailyRankingEntityDao.getKeywordDetailRankingsV2(paramVo, true, tenList, locationid, map);
////							System.out.println(" 执行rating 结束 " + new Date());
//						List<Map<String, Object>> nRatingList = new ArrayList<>();
//						for (String rootDomain : rootDomains) {
//							// 放在要检测的代码段前，取开始前的时间戳
//							Long startTime = System.currentTimeMillis();
//							List<Map<String, Object>> resultList = clDailyRankingEntityDao.getKeywordDetailRankingsV3(paramVo, false, tenList, locationid, map, rootDomain);
//							// 放在要检测的代码段后，取结束后的时间戳
//							Long endTime = System.currentTimeMillis();
//							Long tempTime = (endTime - startTime);
//							System.out.println(" rootDomain : "+rootDomain +"-10 个关键字"+map.get("queryStart")+" ----一个月---非rating花费时间："+
//									(((tempTime/86400000)>0)?((tempTime/86400000)+"d"):"")+
//									((((tempTime/86400000)>0)||((tempTime%86400000/3600000)>0))?((tempTime%86400000/3600000)+"h"):(""))+
//									((((tempTime/3600000)>0)||((tempTime%3600000/60000)>0))?((tempTime%3600000/60000)+"m"):(""))+
//									((((tempTime/60000)>0)||((tempTime%60000/1000)>0))?((tempTime%60000/1000)+"s"):(""))+
//									((tempTime%1000)+"ms"));
//							nRatingList.addAll(resultList);
//						}
//						Long startTime = System.currentTimeMillis();
//						List<Map<String, Object>> ratingResultList = clDailyRankingEntityDao.getKeywordDetailRankingsV3(paramVo, true, tenList, locationid, map, null);
//						Long endTime = System.currentTimeMillis();
//						Long tempTime = (endTime - startTime);
//						System.out.println("-------10个关键字---一个月"+map.get("queryStart")+" -----rating花费时间："+
//								(((tempTime/86400000)>0)?((tempTime/86400000)+"d"):"")+
//								((((tempTime/86400000)>0)||((tempTime%86400000/3600000)>0))?((tempTime%86400000/3600000)+"h"):(""))+
//								((((tempTime/3600000)>0)||((tempTime%3600000/60000)>0))?((tempTime%3600000/60000)+"m"):(""))+
//								((((tempTime/60000)>0)||((tempTime%60000/1000)>0))?((tempTime%60000/1000)+"s"):(""))+
//								((tempTime%1000)+"ms"));
//						resValList.addAll(parseValueV2(locationid, tenList, kwNameMap, nRatingList, ratingResultList, paramVo.getCompetitorDomainList(), paramVo.getWeeklyDates()));
//					}
//					Long endTime10 = System.currentTimeMillis();
//					Long tempTime10 = (endTime10 - startTime10);
//					System.out.println(" 10个关键字1年数据----总花费时间："+
//							(((tempTime10/86400000)>0)?((tempTime10/86400000)+"d"):"")+
//							((((tempTime10/86400000)>0)||((tempTime10%86400000/3600000)>0))?((tempTime10%86400000/3600000)+"h"):(""))+
//							((((tempTime10/3600000)>0)||((tempTime10%3600000/60000)>0))?((tempTime10%3600000/60000)+"m"):(""))+
//							((((tempTime10/60000)>0)||((tempTime10%60000/1000)>0))?((tempTime10%60000/1000)+"s"):(""))+
//							((tempTime10%1000)+"ms"));
//					System.out.println(resValList.size());
////					System.out.println("paramVo.getCompetitorDomainList() " + paramVo.getCompetitorDomainList());
//					appendKeyWord(file, resValList, paramVo.getCompetitorDomainList());
//				}
//
////			for (String key : queryMap.keySet()) {
////				System.out.println("key : " + key);
////				Integer kid = Integer.valueOf(StringUtils.split(key, '-')[0]);
//////				System.out.println("kid : " + kid);
////				Integer locationId = Integer.valueOf(StringUtils.split(key, '-')[1]);
//////				System.out.println("locationId : " + locationId);
//////				List<Map<String, String>> dateRangeTableMapList = paramVo.getDateRangeTableMapList();
////				System.out.println("dateRangeTableMapList : " + dateRangeTableMapList.size() );
////
////				for (Map<String, String> map : dateRangeTableMapList) {
////					String list = map.get("queryDateList");
////					List t = Arrays.asList(list.split(","));
////					paramVo.setWeeklyDates(t);
////					// first query own domain ranking, universal ranking and competitors' ranking
////					System.out.println(" 执行非rating "+new Date());
////					List<Map<String, Object>> resultList = clDailyRankingEntityDao.getKeywordDetailRankings(paramVo, false, kid, locationId, map);
////					System.out.println(" 执行非rating 结束 "+new Date());
////					List<Map<String, Object>> ratingResultList = clDailyRankingEntityDao.getKeywordDetailRankings(paramVo, true, kid, locationId, map);
////					System.out.println(" 执行rating结束 "+new Date());
////					resValList.addAll(parseValue(key, kwNameMap, resultList, ratingResultList, paramVo.getCompetitorDomainList(), paramVo.getWeeklyDates()));
////				}
//////				System.out.println(resValList);
//////				System.out.println("------------------------------------------------------------------------------------------------------------------------");
//////				System.out.println(" 一个关键字向xml 写入一段记录");
////				appendKeyWord(file,resValList, paramVo.getCompetitorDomainList());
////
////			}
//				FileOutputStream endfos = new FileOutputStream(file, true);
//				String end = "</keywords>";
//				endfos.write(end.getBytes());
//				System.out.println("//更改任务状态为2");
//
//				int count = exportInfoDAO.updateStatus(id, TExportInfoEntity.STATUS_DONE);
//				if (count > 0) {
//					System.out.println(id + " update success  ");
//				}
//				endfos.close();
//
//			} catch (Exception e) {
//				int count = exportInfoDAO.updateStatus(id, TExportInfoEntity.STATUS_ERROR);
//				if (count > 0) {
//					System.out.println(id + " update success  ");
//				}
//				e.printStackTrace();
//			}
//		}
//		System.out.println("-------------------------------------------------------------");
//		System.out.println(" 导出结束 ： " + new Date());
//		return "ok";
//	}
//
//	private static List<List<Integer>> tnewList(List<Integer> kids) {
//		int n = 10;
//		if (null == kids || kids.size() == 0 || n <= 0) {
//			return null;
//		}
//		List<List<Integer>> result = new ArrayList<>();
//
//		int sourceSize = kids.size();
//		int size = (sourceSize % n) == 0 ? (sourceSize / n) : ((kids.size() / n) + 1);
//		for (int i = 0; i < size; i++) {
//			List<Integer> subset = new ArrayList<>();
//			for (int j = i * n; j < (i + 1) * n; j++) {
//				if (j < sourceSize) {
//					subset.add(kids.get(j));
//				}
//			}
//			result.add(subset);
//		}
//		System.out.println("result : ---------------");
//		System.out.println(result);
//		return result;
//	}
//
//	private static void appendKeyWord(File file, List<Map<String, Object>> resValList, List<String[]> competitorDomainList) {
//		try {
//			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
//			DocumentBuilder builder = factory.newDocumentBuilder();
//			Document d = builder.newDocument();
//			Element r = d.createElement("keywords");
//			d.appendChild(r);
//			TransformerFactory transFactory = TransformerFactory.newInstance();
//			Transformer transformer = transFactory.newTransformer();
//			transformer.setOutputProperty("indent", "yes");
//			transformer.setOutputProperty(OutputKeys.ENCODING, "utf-8");
//			d.setXmlStandalone(true);
//			FileOutputStream fos = new FileOutputStream(file, true);
//			for (Map<String, Object> map : resValList) {
//				Element eltItem = d.createElement("keyword");
//
//				Element eltName = d.createElement("name");
//				eltName.appendChild(d.createTextNode(RankCheckUtils.decoderString(map.get("name").toString())));
//				eltItem.appendChild(eltName);
//
//				Element eltDate = d.createElement("date");
//				eltDate.appendChild(d.createTextNode(map.get("date").toString()));
//				eltItem.appendChild(eltDate);
//
//				Element highestTrueRank = d.createElement("highestTrueRank");
//				highestTrueRank.appendChild(d.createTextNode(map.get("highestTrueRank").toString()));
//				eltItem.appendChild(highestTrueRank);
//
//				Element highestWebRank = d.createElement("highestWebRank");
//				highestWebRank.appendChild(d.createTextNode(map.get("highestWebRank").toString()));
//				eltItem.appendChild(highestWebRank);
//
//				Element highestRankUrl = d.createElement("highestRankUrl");
//				if (StringUtils.isBlank(map.get("highestRankUrl").toString())) {
//					highestRankUrl.appendChild(d.createTextNode("-"));
//				} else {
//					highestRankUrl.appendChild(d.createTextNode(map.get("highestRankUrl").toString()));
//				}
//				eltItem.appendChild(highestRankUrl);
//
//				Element highestLocalRank = d.createElement("highestLocalRank");
//				highestLocalRank.appendChild(d.createTextNode(map.get("highestLocalRank").toString()));
//				eltItem.appendChild(highestLocalRank);
//
//				Element highestNewsRank = d.createElement("highestNewsRank");
//				highestNewsRank.appendChild(d.createTextNode(map.get("highestNewsRank").toString()));
//				eltItem.appendChild(highestNewsRank);
//
//				Element highestImageRank = d.createElement("highestImageRank");
//				highestImageRank.appendChild(d.createTextNode(map.get("highestImageRank").toString()));
//				eltItem.appendChild(highestImageRank);
//
//				Element highestVideoRank = d.createElement("highestVideoRank");
//				highestVideoRank.appendChild(d.createTextNode(map.get("highestVideoRank").toString()));
//				eltItem.appendChild(highestVideoRank);
//
//				Element highestStarRank = d.createElement("highestStarRank");
//				highestStarRank.appendChild(d.createTextNode(map.get("highestStarRank").toString()));
//				eltItem.appendChild(highestStarRank);
//
//				Element monthlySearchVolume = d.createElement("monthlySearchVolume");
//				monthlySearchVolume.appendChild(d.createTextNode(map.get("monthlySearchVolume").toString()));
//				eltItem.appendChild(monthlySearchVolume);
//
//				if (competitorDomainList != null && competitorDomainList.size() > 0) {
//					Element competitors = d.createElement("competitors");
//
//					Map<String, String[]> competitorList = (Map) map.get("competitorRanMap");
//					for (String[] competitorDomain : competitorDomainList) {
//						String competitorName = competitorDomain[1] == null ? competitorDomain[0] : competitorDomain[1];
//						String competitorDomainName = StringUtils.reverseDelimited(competitorName, '.');
//						String landingPage = "";
//						String rank = "101";
//						if (competitorList.containsKey(competitorName)) {
//							String[] val = competitorList.get(competitorName);
//							landingPage = val[0];
//							rank = val[1];
//						}
//
//						Element competitor = d.createElement("competitor");
//						Element eltCompetitorName = d.createElement("competitorName");
//						eltCompetitorName.appendChild(d.createTextNode(competitorDomainName));
//						competitor.appendChild(eltCompetitorName);
//
//						Element eltlandingPage = d.createElement("landingPage");
//						if (StringUtils.isBlank(landingPage)) {
//							landingPage = "http://" + competitorDomainName + "/";
//						}
//						eltlandingPage.appendChild(d.createTextNode(landingPage));
//						competitor.appendChild(eltlandingPage);
//
//						Element eltrank = d.createElement("rank");
//						eltrank.appendChild(d.createTextNode(rank));
//						competitor.appendChild(eltrank);
//
//						competitors.appendChild(competitor);
//					}
//					eltItem.appendChild(competitors);
//				}
//
//				r.appendChild(eltItem);
//
//			}
//			System.out.println(" 接下来要xml 转String :");
//			String str = toStringFromDoc(d);
////			System.out.println("已经转换成  strr : " + str);
//			String subStr = str.substring(str.indexOf("<keywords>"), str.indexOf("</keywords>"));
////			System.out.println("subStr  subStr : " + subStr);
//			String replaceStr = subStr.replace("<keywords>", "").replace("</keywords>", "");
////			System.out.println("replaceStr  replaceStr : " + replaceStr);
////			System.out.println("subStr---------------------------------------" +subStr );
//			fos.write(replaceStr.getBytes());
//
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
////		System.out.println("rrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrr");
//
//	}
//
//	private static String exportForXML(List<Map<String, Object>> resValList, List<String[]> competitorDomainList) throws Exception {
//		System.out.println(" ============== read in  xml ==============");
//		System.out.println("resValList .size" + resValList.size());
//		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
//		DocumentBuilder builder = factory.newDocumentBuilder();
//		Document d = builder.newDocument();
//		d.setXmlStandalone(true);
//		Element r = d.createElement("keywords");
//		d.appendChild(r);
//
//		try {
//			for (Map<String, Object> map : resValList) {
//				Element eltItem = d.createElement("keyword");
//
//				Element eltName = d.createElement("name");
//				eltName.appendChild(d.createTextNode(RankCheckUtils.decoderString(map.get("name").toString())));
//				eltItem.appendChild(eltName);
//
//				Element eltDate = d.createElement("date");
//				eltDate.appendChild(d.createTextNode(map.get("date").toString()));
//				eltItem.appendChild(eltDate);
//
//				Element highestTrueRank = d.createElement("highestTrueRank");
//				highestTrueRank.appendChild(d.createTextNode(map.get("highestTrueRank").toString()));
//				eltItem.appendChild(highestTrueRank);
//
//				Element highestWebRank = d.createElement("highestWebRank");
//				highestWebRank.appendChild(d.createTextNode(map.get("highestWebRank").toString()));
//				eltItem.appendChild(highestWebRank);
//
//				Element highestRankUrl = d.createElement("highestRankUrl");
//				if (StringUtils.isBlank(map.get("highestRankUrl").toString())) {
//					highestRankUrl.appendChild(d.createTextNode("-"));
//				} else {
//					highestRankUrl.appendChild(d.createTextNode(map.get("highestRankUrl").toString()));
//				}
//				eltItem.appendChild(highestRankUrl);
//
//				Element highestLocalRank = d.createElement("highestLocalRank");
//				highestLocalRank.appendChild(d.createTextNode(map.get("highestLocalRank").toString()));
//				eltItem.appendChild(highestLocalRank);
//
//				Element highestNewsRank = d.createElement("highestNewsRank");
//				highestNewsRank.appendChild(d.createTextNode(map.get("highestNewsRank").toString()));
//				eltItem.appendChild(highestNewsRank);
//
//				Element highestImageRank = d.createElement("highestImageRank");
//				highestImageRank.appendChild(d.createTextNode(map.get("highestImageRank").toString()));
//				eltItem.appendChild(highestImageRank);
//
//				Element highestVideoRank = d.createElement("highestVideoRank");
//				highestVideoRank.appendChild(d.createTextNode(map.get("highestVideoRank").toString()));
//				eltItem.appendChild(highestVideoRank);
//
//				Element highestStarRank = d.createElement("highestStarRank");
//				highestStarRank.appendChild(d.createTextNode(map.get("highestStarRank").toString()));
//				eltItem.appendChild(highestStarRank);
//
//				Element monthlySearchVolume = d.createElement("monthlySearchVolume");
//				monthlySearchVolume.appendChild(d.createTextNode(map.get("monthlySearchVolume").toString()));
//				eltItem.appendChild(monthlySearchVolume);
//
//				if (competitorDomainList != null && competitorDomainList.size() > 0) {
//					Element competitors = d.createElement("competitors");
//
//					Map<String, String[]> competitorList = (Map) map.get("competitorRanMap");
//					for (String[] competitorDomain : competitorDomainList) {
//						String competitorName = competitorDomain[1] == null ? competitorDomain[0] : competitorDomain[1];
//						String competitorDomainName = StringUtils.reverseDelimited(competitorName, '.');
//						String landingPage = "";
//						String rank = "101";
//						if (competitorList.containsKey(competitorName)) {
//							String[] val = competitorList.get(competitorName);
//							landingPage = val[0];
//							rank = val[1];
//						}
//
//						Element competitor = d.createElement("competitor");
//						Element eltCompetitorName = d.createElement("competitorName");
//						eltCompetitorName.appendChild(d.createTextNode(competitorDomainName));
//						competitor.appendChild(eltCompetitorName);
//
//						Element eltlandingPage = d.createElement("landingPage");
//						if (StringUtils.isBlank(landingPage)) {
//							landingPage = "http://" + competitorDomainName + "/";
//						}
//						eltlandingPage.appendChild(d.createTextNode(landingPage));
//						competitor.appendChild(eltlandingPage);
//
//						Element eltrank = d.createElement("rank");
//						eltrank.appendChild(d.createTextNode(rank));
//						competitor.appendChild(eltrank);
//
//						competitors.appendChild(competitor);
//					}
//					eltItem.appendChild(competitors);
//				}
//
//				r.appendChild(eltItem);
//			}
//			TransformerFactory formerFactory = TransformerFactory.newInstance();
//			Transformer transformer = formerFactory.newTransformer();
//			transformer.setOutputProperty(OutputKeys.INDENT, "YES");
//			transformer.setOutputProperty(OutputKeys.ENCODING, "utf-8");
//			File file = new File("zz.xml");
//			transformer.transform(new DOMSource(d), new StreamResult(file));
//
//			System.out.println("XML CreateDocument success!");
//			System.out.println("d:" + d.toString());
//			System.out.println("builder" + builder);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//		return "export success";
//	}
//
////	private static List<Map<String, Object>> parseValue(String key, Map<String, String[]> kwNameMap, List<Map<String, Object>> resultList, List<Map<String, Object>> ratingResultList,
////	                                                    List<String[]> competitorDomainList, List<String> dateList) {
////		List<Map<String, Object>> resValList = new ArrayList<Map<String, Object>>();
////		for (String date : dateList) {
////			int dateIdx = Integer.valueOf(StringUtils.replace(date, "-", ""));
////			if (kwNameMap.get(key + "-" + dateIdx) == null) {
////				continue;
////			}
////			String kw = kwNameMap.get(key + "-" + dateIdx)[0];
////			String sv = kwNameMap.get(key + "-" + dateIdx)[1];
////
////			Map<String, Object> resultMap = new HashMap<String, Object>();
////			resultMap.put("name", kw);
////			resultMap.put("monthlySearchVolume", sv);
////			resultMap.put("date", dateIdx);
////			parseRow(competitorDomainList, resultList, ratingResultList, dateIdx, resultMap);
////			resValList.add(resultMap);
////		}
////		return resValList;
////	}
//
//	private static List<Map<String, Object>> parseValueV2(Integer locationid, List<Integer> keys, Map<String, String[]> kwNameMap, List<Map<String, Object>> resultList, List<Map<String, Object>> ratingResultList,
//	                                                      List<String[]> competitorDomainList, List<String> dateList) {
//		List<Map<String, Object>> resValList = new ArrayList<Map<String, Object>>();
//		List<Map<String, Object>> newResultList  = new ArrayList<>();
//		for (Integer key : keys) {
//			for (Map<String,Object> mp :resultList ){
//				if (mp.get("keyword_rankcheck_id").equals(String.valueOf(key))){
//					newResultList.add(mp);
//				}
//			}
//
//			for (String date : dateList) {
//				int dateIdx = Integer.valueOf(StringUtils.replace(date, "-", ""));
//				if (kwNameMap.get(key + "-" + locationid + "-" + dateIdx) == null) {
//					continue;
//				}
//				String kw = kwNameMap.get(key + "-" + locationid + "-" + dateIdx)[0];
//				String sv = kwNameMap.get(key + "-" + locationid + "-" + dateIdx)[1];
//
//				Map<String, Object> resultMap = new HashMap<String, Object>();
//				resultMap.put("name", kw);
//				resultMap.put("monthlySearchVolume", sv);
//				resultMap.put("date", dateIdx);
//				parseRow(competitorDomainList, newResultList, ratingResultList, dateIdx, resultMap);
//				resValList.add(resultMap);
//			}
//		}
//		return resValList;
//	}
//
//	private static void parseRow(List<String[]> competitorList, List<Map<String, Object>> resultList, List<Map<String, Object>> ratingResultList,
//	                             int dateIdx, Map<String, Object> resultMap) {
//		int highestTrueRank = 101;
//		int highestWebRank = 101;
//		String highestRankUrl = "";
//		int highestLocalRank = 101;
//		int highestNewsRank = 101;
//		int highestImageRank = 101;
//		int highestVideoRank = 101;
//		int highestStarRank = 101;
//
//		Map<String, String[]> competitorRanMap = new LinkedHashMap<String, String[]>();
//
//		for (int i = 0; i < resultList.size(); i++) {
//			Map<String, Object> map = resultList.get(i);
//			int true_rank = Integer.valueOf(map.get("true_rank").toString());
//			int web_rank = Integer.valueOf(map.get("web_rank").toString());
//			int type = Integer.valueOf(map.get("type").toString());
//			String domain_reverse = map.get("domain_reverse").toString();
//
//			String rating = map.get("rating").toString();
//
//			String ranking_date = map.get("ranking_date").toString();
//			int rowDateIdx = Integer.valueOf(StringUtils.replace(ranking_date, "-", ""));
//			String url = map.get("url").toString();
//			int ownDomainFlg = Integer.valueOf(map.get("ownDomainFlg").toString());
//
//			if (rowDateIdx > dateIdx) {
//				break;
//			} else if (rowDateIdx != dateIdx) {
//				continue;
//			}
//
//			if (type == KeywordRankEntityVO.TYPE_ADDRESS && highestLocalRank > true_rank) {
//				highestLocalRank = true_rank;
//			}
//			if (type == KeywordRankEntityVO.TYPE_NEWS && highestNewsRank > true_rank) {
//				highestNewsRank = true_rank;
//			}
//			if (type == KeywordRankEntityVO.TYPE_IMGAGE && highestImageRank > true_rank) {
//				highestImageRank = true_rank;
//			}
//			if (type == KeywordRankEntityVO.TYPE_VIDEO && highestVideoRank > true_rank) {
//				highestVideoRank = true_rank;
//			}
//			if (rating.equals("1") && highestStarRank > true_rank) {
//				highestStarRank = true_rank;
//			}
//
//			// for own domain highest rank
//			if (ownDomainFlg == 1) {
//				if (highestTrueRank > true_rank) {
//					highestTrueRank = true_rank;
//				}
//				if (highestWebRank > web_rank) {
//					highestWebRank = web_rank;
//				}
//				if (StringUtils.isBlank(highestRankUrl)) {
//					highestRankUrl = url;
//				}
//			}
//
//			// for competitor highest rank
//			for (String[] competitorDomain : competitorList) {
//				if (competitorDomain[1] != null && !competitorRanMap.containsKey(competitorDomain[1]) && domain_reverse.equalsIgnoreCase(competitorDomain[1])) {
//					competitorRanMap.put(competitorDomain[1], new String[]{
//							url, String.valueOf(true_rank)
//					});
//				} else if (!competitorRanMap.containsKey(competitorDomain[0]) && StringUtils.containsIgnoreCase(domain_reverse, competitorDomain[0] + ".")) {
//					competitorRanMap.put(competitorDomain[0], new String[]{
//							url, String.valueOf(true_rank)
//					});
//				}
//			}
//			resultList.remove(i);
//			i--;
//		}
//
//		// parse rating
//		for (int i = 0; i < ratingResultList.size(); i++) {
//			Map<String, Object> map = ratingResultList.get(i);
//
//			int true_rank = Integer.valueOf(map.get("true_rank").toString());
//			String ranking_date = map.get("ranking_date").toString();
//			int rowDateIdx = Integer.valueOf(StringUtils.replace(ranking_date, "-", ""));
//			if (rowDateIdx > dateIdx) {
//				break;
//			} else if (rowDateIdx != dateIdx) {
//				continue;
//			}
//			if (highestStarRank > true_rank) {
//				highestStarRank = true_rank;
//			}
//			ratingResultList.remove(i);
//			i--;
//		}
//
//		resultMap.put("highestTrueRank", highestTrueRank);
//		resultMap.put("highestWebRank", highestWebRank);
//		resultMap.put("highestRankUrl", highestRankUrl);
//		resultMap.put("highestLocalRank", highestLocalRank);
//		resultMap.put("highestNewsRank", highestNewsRank);
//		resultMap.put("highestImageRank", highestImageRank);
//		resultMap.put("highestVideoRank", highestVideoRank);
//		resultMap.put("highestStarRank", highestStarRank);
//		resultMap.put("competitorRanMap", competitorRanMap);
//	}
//
//	/*
//	 * 把dom文件转换为xml字符串
//	 */
//	public static String toStringFromDoc(Document document) {
//		String result = null;
//
//		if (document != null) {
//			StringWriter strWtr = new StringWriter();
//			StreamResult strResult = new StreamResult(strWtr);
//			TransformerFactory tfac = TransformerFactory.newInstance();
//			try {
//				javax.xml.transform.Transformer t = tfac.newTransformer();
//				t.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
//				t.setOutputProperty(OutputKeys.INDENT, "yes");
//				t.setOutputProperty(OutputKeys.METHOD, "xml"); // xml, html,
//				// text
//				t.setOutputProperty(
//						"{http://xml.apache.org/xslt}indent-amount", "4");
//				t.transform(new DOMSource(document.getDocumentElement()),
//						strResult);
//			} catch (Exception e) {
//				System.err.println("XML.toString(Document): " + e);
//			}
//			result = strResult.getWriter().toString();
//			try {
//				strWtr.close();
//			} catch (IOException e) {
//				e.printStackTrace();
//			}
//		}
//
//		return result;
//	}
//}
