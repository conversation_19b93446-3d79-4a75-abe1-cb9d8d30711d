package seoclarity.backend.multithread;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Thread Factory for Java Thread Pool
 *
 * <AUTHOR>
 * @create 2017-12-29 20:40
 **/
public class BackendThreadFactory implements ThreadFactory {

    private final AtomicInteger count = new AtomicInteger(0);

    @Override
    public Thread newThread(Runnable runnable) {
        int threadId = count.incrementAndGet();
        Thread newThread = new Thread(runnable);
        newThread.setName("thread-"+threadId);
        System.out.println("create new thread id: "+threadId);
        return newThread;
    }

}
