package seoclarity.backend.multithread.core.thread.threadpool;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.AbortPolicy;

public class ThreadPoolRejectedExecutionHandler extends AbortPolicy {

	@Override
	public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
		try {
			super.rejectedExecution(r, e);
		} catch (RejectedExecutionException ex) {
			ex.printStackTrace();
		}
	}
}
