package seoclarity.backend.gmail;

import cn.hutool.core.codec.Base64Decoder;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.extensions.java6.auth.oauth2.AuthorizationCodeInstalledApp;
import com.google.api.client.extensions.jetty.auth.oauth2.LocalServerReceiver;
import com.google.api.client.googleapis.auth.oauth2.GoogleAuthorizationCodeFlow;
import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.util.store.FileDataStoreFactory;
import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.GmailScopes;
import com.google.api.services.gmail.model.*;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jettison.json.JSONObject;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

// https://www.wrike.com/open.htm?id=887202460
public class GmailApi {
    //<EMAIL> xx
    /**
     * Application name.
     */
    private static final String APPLICATION_NAME = "Gmail Api V2";
    /**
     * Global instance of the JSON factory.
     */
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    /**
     * Directory to store authorization tokens for this application.
     */
    private static final String TOKENS_DIRECTORY_PATH = "/home/<USER>/token";
    private static final String[] MINE_TYPES = {"application/txt", "text/csv", "application/csv", "application/msexcel",
            "APPLICATION/OCTET-STREAM", "Application/msexcel", "APPLICATION/VND.MS-EXCEL",
            "APPLICATION/VND.OPENXMLFORMATS-OFFICEDOCUMENT.SPREADSHEETML.SHEET",
            "application/octet-stream", "APPLICATION/ZIP"};
    private static final String SEND_DATE_KEY = "internalDate";
    private static final String SUBJECT_KEY = "Subject";
    private static final String SENDER_KEY = "From";
    private static final String MAILTO_KEY = "To"; // https://www.wrike.com/open.htm?id=1099047866

    private static String SEND_TO = "<EMAIL>";
    //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/1081638150
    private static String[] CC_TOS = new String[]{ "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };

    // Global instance of the scopes required by this quickstart. If modifying these scopes, delete your previously saved tokens/ folder.
    private static final List<String> SCOPES = new ArrayList<>();

    static {
        SCOPES.add(GmailScopes.GMAIL_READONLY);
    }

    public static final String CREDENTIALS_FILE_PATH = "/credentials.json";

    private Gmail service = null;
    private String email = null;
    private Map<Message, String> subjectMap = new HashMap<>();
    private Map<Message, Date> sendDateMap = new HashMap<>();
    private Map<Message, String> senderMap = new HashMap<>();
    private Map<Message, Message> messageDataMap = new HashMap<>();
    private Map<Message, String> mailToMap = new HashMap<>();
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public static class AttachmentDesc {
        private MessagePartBody messagePartBody;
        private String fileName = "";

        public MessagePartBody getMessagePartBody() {
            return messagePartBody;
        }

        public void setMessagePartBody(MessagePartBody messagePartBody) {
            this.messagePartBody = messagePartBody;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }
    }

    public GmailApi(String email, String refreshToken) {
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        try {
            this.email = email;
//            final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
//            service = new Gmail.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(HTTP_TRANSPORT, credentials))
//                    .setApplicationName(APPLICATION_NAME)
//                    .build();
            service = getGmailService(refreshToken);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static Gmail getGmailService(String refreshToken) throws IOException, GeneralSecurityException {
        InputStream in = GmailQuickStart.class.getResourceAsStream(CREDENTIALS_FILE_PATH);
        if (in == null) {
            throw new FileNotFoundException("Resource not found: " + CREDENTIALS_FILE_PATH);
        }
        GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));
        // Credential builder
        Credential authorize = new GoogleCredential.Builder().setTransport(GoogleNetHttpTransport.newTrustedTransport())
                .setJsonFactory(JSON_FACTORY)
                .setClientSecrets(clientSecrets.getDetails().getClientId().toString(),
                        clientSecrets.getDetails().getClientSecret().toString())
                .build().setAccessToken(getAccessToken(clientSecrets.getDetails().getClientId().toString(),
                        clientSecrets.getDetails().getClientSecret().toString(), refreshToken)).setRefreshToken(
                        refreshToken);// Replace this
        // Create Gmail service
        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
        return new Gmail.Builder(HTTP_TRANSPORT, JSON_FACTORY, authorize)
                .setApplicationName(APPLICATION_NAME).build();
    }

    private static String getAccessToken(String clientId, String clientSecret, String refreshToken) {
        try {
            Map<String, Object> params = new LinkedHashMap<>();
            params.put("grant_type", "refresh_token");
            params.put("client_id", clientId); //Replace this
            params.put("client_secret", clientSecret); //Replace this
            params.put("refresh_token",
                    refreshToken); //Replace this

            StringBuilder postData = new StringBuilder();
            for (Map.Entry<String, Object> param : params.entrySet()) {
                if (postData.length() != 0) {
                    postData.append('&');
                }
                postData.append(URLEncoder.encode(param.getKey(), "UTF-8"));
                postData.append('=');
                postData.append(URLEncoder.encode(String.valueOf(param.getValue()), "UTF-8"));
            }
            byte[] postDataBytes = postData.toString().getBytes("UTF-8");

            URL url = new URL("https://accounts.google.com/o/oauth2/token");
            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            con.setDoOutput(true);
            con.setUseCaches(false);
            con.setRequestMethod("POST");
            con.getOutputStream().write(postDataBytes);

            BufferedReader reader = new BufferedReader(new InputStreamReader(con.getInputStream()));
            StringBuffer buffer = new StringBuffer();
            for (String line = reader.readLine(); line != null; line = reader.readLine()) {
                buffer.append(line);
            }

            JSONObject json = new JSONObject(buffer.toString());
            String accessToken = json.getString("access_token");
            return accessToken;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    /**
     * Creates an authorized Credential object.
     *
     * @param HTTP_TRANSPORT The network HTTP Transport.
     * @return An authorized Credential object.
     * @throws IOException              If the credentials.json file cannot be found.
     * @throws GeneralSecurityException
     */
    private Credential getCredentials(final NetHttpTransport HTTP_TRANSPORT, String credentials) throws IOException, GeneralSecurityException {
        // Load client secrets.
        InputStream in = new ByteArrayInputStream(credentials.getBytes(StandardCharsets.UTF_8));
        if (in == null) {
            throw new FileNotFoundException("Resource not found: " + CREDENTIALS_FILE_PATH);
        }
        GoogleClientSecrets clientSecrets = GoogleClientSecrets.load(JSON_FACTORY, new InputStreamReader(in));
        String fullPath = String.join(File.separator, TOKENS_DIRECTORY_PATH, email);
        File file = new File(fullPath);
        if (!file.exists())
            file.mkdirs();
        GoogleAuthorizationCodeFlow flow = new GoogleAuthorizationCodeFlow.Builder(
                HTTP_TRANSPORT, JSON_FACTORY, clientSecrets, SCOPES)
                .setDataStoreFactory(new FileDataStoreFactory(new File(fullPath)))
                .setAccessType("offline")
                .build();
        LocalServerReceiver receiver = new LocalServerReceiver.Builder().setPort(8888).build();
        Credential credential = new AuthorizationCodeInstalledApp(flow, receiver).authorize("user");
        return credential;
    }

    public Message getMessageData(Message rootMessage) {
        if (messageDataMap.containsKey(rootMessage))
            return messageDataMap.get(rootMessage);
        try {
            Message result = service.users().messages().get("me", rootMessage.getId()).execute();
            messageDataMap.put(rootMessage, result);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public Date getSendDate(Message rootMessage) {
        if (sendDateMap.containsKey(rootMessage))
            return sendDateMap.get(rootMessage);
        Date date = null;
        if (rootMessage.containsKey(SEND_DATE_KEY)) {
            long stp = Long.parseLong(rootMessage.get(SEND_DATE_KEY) + "");
            date = new Date(stp);
            sendDateMap.put(rootMessage, date);
        } else {
            Message content = getMessageData(rootMessage);
            if (content != null) {
                if (content.containsKey(SEND_DATE_KEY)) {
                    long stp = Long.parseLong(content.get(SEND_DATE_KEY) + "");
                    date = new Date(stp);
                    sendDateMap.put(rootMessage, date);
                }
            } else {
                System.out.println("no internalDate");
            }
        }
        return date;
    }
    
    public String getMailTo(Message rootMessage) {
        if (mailToMap.containsKey(rootMessage))
            return mailToMap.get(rootMessage);
        try {
            Message content = getMessageData(rootMessage);
            if (content != null) {
                for (MessagePartHeader messagePartHeader : content.getPayload().getHeaders()) {
                    if (messagePartHeader.getName().equals(MAILTO_KEY)) {
                    	mailToMap.put(rootMessage, messagePartHeader.getValue());
                        return messagePartHeader.getValue();
                    }
                }
            } else {
                System.out.println("no mailTo");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public String getSubject(Message rootMessage) {
        if (subjectMap.containsKey(rootMessage))
            return subjectMap.get(rootMessage);
        try {
            Message content = getMessageData(rootMessage);
            if (content != null) {
                for (MessagePartHeader messagePartHeader : content.getPayload().getHeaders()) {
                    if (messagePartHeader.getName().equals(SUBJECT_KEY)) {
                        subjectMap.put(rootMessage, messagePartHeader.getValue());
                        return messagePartHeader.getValue();
                    }
                }
            } else {
                System.out.println("no Subject");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public String getSender(Message rootMessage) {
        try {
            if (senderMap.containsKey(rootMessage))
                return senderMap.get(rootMessage);
            Message content = getMessageData(rootMessage);
            if (content != null) {
                for (MessagePartHeader messagePartHeader : content.getPayload().getHeaders()) {
                    if (messagePartHeader.getName().equals(SENDER_KEY)) {
                        String value = messagePartHeader.getValue();
                        if (value.contains("<") && value.contains(">")) {
                            String[] strs = value.split("<");
                            if (strs.length >= 2) {
                                if (strs[1].contains(">")) {
                                    strs = strs[1].split(">");
                                    if (strs.length >= 1) {
                                        senderMap.put(rootMessage, strs[0]);
                                        return strs[0];
                                    }
                                }
                            }
                        }
                        senderMap.put(rootMessage, value);
                        return value;
                    }
                }
            } else {
                System.out.println("no From");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getBodyContent(Message message) {
        // --------del-----------
//        Gson g = new Gson();
//        if (message.getPayload() == null) {
//            System.out.println("message.getPayload() is null！");
//            System.out.println(g.toJson(message));
//        }
        // --------del-----------

        if (message.getPayload() != null && message.getPayload().getParts() != null && message.getPayload().getParts().size() > 0) {
            return Base64Decoder.decodeStr(message.getPayload().getParts().get(0).getBody().getData());
        }
        return "";
    }

    public String getLinkUrl(Message message, String wordsBeforeLink, String separator) {
        String linkUrl = null;
        String bodyContent = getBodyContent(message);

        System.out.println("----------getLinkUrl:------------");
        if (StringUtils.isBlank(bodyContent) || StringUtils.isBlank(wordsBeforeLink) ||
                !bodyContent.toLowerCase().contains(wordsBeforeLink.toLowerCase())) {
            System.out.println("wordsBeforeLink:" + wordsBeforeLink);
            System.out.println("bodyContent:" + bodyContent);
            return null;
        }
        String wordsBeforeLink_lower = wordsBeforeLink.toLowerCase();
        String bodyContent_lower = bodyContent.toLowerCase();

        int posWordsBeforeLink = bodyContent_lower.indexOf(wordsBeforeLink_lower);
        if (posWordsBeforeLink >= 0) {
            int urlStartPos = bodyContent_lower.indexOf(separator, posWordsBeforeLink);//"https"
            if (urlStartPos >= 0) {
                linkUrl = bodyContent.substring(urlStartPos);
                if (!StringUtils.isEmpty(linkUrl)) {
                    for (int i = 0; i < linkUrl.length(); i++) {
                        char ch = linkUrl.charAt(i);
                        if (ch == '>') {
                            linkUrl = linkUrl.substring(0, i);
                            break;
                        }
                    }
                }
            }
        }
        if (StringUtils.isBlank(linkUrl)) {
            System.out.println("wordsBeforeLink:" + wordsBeforeLink + "\r\n" + bodyContent);
        } else {
            if (linkUrl.contains("<")) {
                String[] strs = linkUrl.split("<");
                if (strs.length >= 2)
                    linkUrl = strs[1];
            }
        }
        return linkUrl;
    }

    public static String getLinkUrlV2(Message rootMessage, String wordsBeforeLink) {
        String linkUrl = null;
        String bodyContent = rootMessage.getPayload().getParts().get(0).getBody().getData();
        bodyContent = Base64Decoder.decodeStr(bodyContent);
        System.out.println("----------getLinkUrlV2:bodyContent------------");
        System.out.println(bodyContent);

        if (StringUtils.isBlank(bodyContent) || StringUtils.isBlank(wordsBeforeLink) ||
                !bodyContent.contains(wordsBeforeLink))
            return null;
        String[] lines = StringUtils.split(bodyContent, '\n');
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            if (StringUtils.containsIgnoreCase(line, wordsBeforeLink) && i + 1 < lines.length) {
                if (StringUtils.startsWithIgnoreCase(lines[i + 1], "<https")) {
                    linkUrl = lines[i + 1];
                    linkUrl = StringUtils.removeStart(linkUrl, "<");
                    linkUrl = StringUtils.remove(linkUrl, ">");
                    break;
                }
            }
        }
        System.out.println("linkUrl:" + linkUrl);
        return linkUrl;
    }

    public AttachmentDesc getAttachmentInfo(Message rootMessage, boolean isReadData) {
        Message messageData = getMessageData(rootMessage);
        try {
            return getAttachmentInfo(rootMessage, messageData, isReadData);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private AttachmentDesc getAttachmentInfo(Message rootMessage, Message message, boolean isReadData) {
        try {
            for (MessagePart messagePart : message.getPayload().getParts()) {
                String mineType = messagePart.getMimeType();
                if (Arrays.stream(MINE_TYPES).anyMatch(e -> e.equals(mineType)) && StringUtils.isNotBlank(messagePart.getFilename())) {
                    AttachmentDesc attachmentDesc = new AttachmentDesc();
                    if (isReadData) {
                        MessagePartBody messagePartBody = service.users().messages().attachments().
                                get("me", rootMessage.getId(), messagePart.getBody().getAttachmentId()).execute();
                        attachmentDesc.setMessagePartBody(messagePartBody);
                    }
                    attachmentDesc.setFileName(messagePart.getFilename());
                    return attachmentDesc;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        //----del------
//        try {
//            Gson g = new Gson();
//            //System.out.println(g.toJson(message));
//            String fullName = String.join(File.separator, TOKENS_DIRECTORY_PATH, "attachmentInfo.txt");
//            File file = new File(fullName);
//            if (!file.exists())
//                file.createNewFile();
//            FileWriter fw = new FileWriter(file, true);
//            StringBuilder sb = new StringBuilder();
//            sb.append("---------------------\r\n");
//            for (MessagePart messagePart : message.getPayload().getParts()) {
//                String mineType = messagePart.getMimeType();
//                String fileName = messagePart.getFilename();
//                sb.append("mineType:" + mineType).append("\r\nfileName:" + fileName + "\r\n");
//            }
//            sb.append("---------------------\r\n");
//            fw.write(sb.toString());
//            fw.close();
//        } catch (Exception ex) {
//        }
        //----del------
        return null;
    }

    public List<AttachmentDesc> getAttachmentInfos(Message rootMessage, boolean isReadData) {
        Message messageData = getMessageData(rootMessage);
        try {
            return getAttachmentInfos(rootMessage, messageData, isReadData);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList<>();
    }

    public List<AttachmentDesc> getAttachmentInfos(Message rootMessage, Message message, boolean isReadData) {
        List<AttachmentDesc> attachmentDescs = new ArrayList<>();
        try {
            for (MessagePart messagePart : message.getPayload().getParts()) {
                String mineType = messagePart.getMimeType();
                if (Arrays.stream(MINE_TYPES).anyMatch(e -> e.equals(mineType)) && StringUtils.isNotBlank(messagePart.getFilename())) {
                    AttachmentDesc attachmentDesc = new AttachmentDesc();
                    if (isReadData) {
                        MessagePartBody messagePartBody = service.users().messages().attachments().
                                get("me", rootMessage.getId(), messagePart.getBody().getAttachmentId()).execute();
                        attachmentDesc.setMessagePartBody(messagePartBody);
                    }
                    attachmentDesc.setFileName(messagePart.getFilename());
                    attachmentDescs.add(attachmentDesc);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return attachmentDescs;
    }

    public List<Message> getMessages(String[] subjects, Date sendDate) {
        StringBuilder sb = new StringBuilder();
        for (String subject : subjects) {
            sb.append("subject:\"" + subject + "\" ");
        }
        String sq = sb.toString();
        if (StringUtils.isNotBlank(sq)) {
            sq = "{" + sq + "}";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        try {
            String query = sq + " AND after:" + sdf.format(sendDate) + " ";
            System.out.println("query:" + query);
            List<Message> messages = new ArrayList<>();
            ListMessagesResponse response = service.users().messages().list("me").
                    setQ(query).execute();
            while (response.getMessages() != null) {
                messages.addAll(response.getMessages());
                if (response.getNextPageToken() != null) {
                    String pageToken = response.getNextPageToken();
                    response = service.users().messages().list("me").
                            setQ(query).setPageToken(pageToken).execute();
                } else {
                    break;
                }
            }
            if (messages == null || messages.size() == 0) {
                System.out.println("no messages");
            }
            return messages;
        } catch (Exception ex) {
            Map<String, String> map = new HashMap<String, String>();
            String subject ="Gmail download ERROR";
            String result= ex.getMessage();
            if (ex.getCause() != null) {
                result=result+"\r\n"+ ex.getCause().getMessage();
            }
            map.put("result",result);
            map.put("dateString", getTodayDateString());
            zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(SEND_TO, CC_TOS, subject, "mail_upload_report.txt", "mail_upload_report.html", map, null,
                    ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
            ex.printStackTrace();
        }
        return new ArrayList<>();
    }

    private String getTodayDateString() {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(new Date());
    }

//    //mvn exec:java -Dexec.mainClass="seoclarity.backend.gmail.GmailApi" -Dexec.args=""
//    public static void main(String... args) throws Exception, GeneralSecurityException {
//        InputStream inputStream = GmailApi.class.getResourceAsStream(CREDENTIALS_FILE_PATH);
//        String credentials = new BufferedReader(new InputStreamReader(inputStream))
//                .lines().collect(Collectors.joining(System.lineSeparator()));
//        GmailApi gmailQuickStart = new GmailApi("x", credentials);
//        String user = "me";
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
//        Date date0 = sdf.parse("2022/05/1");
////        testUrlLink(gmailQuickStart, new String[]{"Your Google Ads report is ready: gcu.edu- Google Ads report"
////                , "Your Google Ads report is ready"}, "<EMAIL>", date0);//
//        testAttachment(gmailQuickStart, "SeoClarity report - Apple", "<EMAIL>");
//    }
//
//    public static void testUrlLink(GmailApi gmailQuickStart, String[] subjects, String email, Date sendDate) throws Exception {
//        String wordsBeforeLink = "View report";
//        StringBuilder sb = new StringBuilder();
//        for (String subject : subjects) {
//            //subject=subject.replace("'","\\'");
//            sb.append("subject:'" + subject + "' OR ");
//        }
//        String sq = sb.toString();
//        if (StringUtils.isNotBlank(sq)) {
//            sq = sq.substring(0, sq.length() - 3);
//            sq = "(" + sq + ")";
//        }
//        System.out.println("sq=" + sq);
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
//        List<Message> messages = gmailQuickStart.getMessages(subjects, sendDate);//+
//
//        if (messages == null || messages.isEmpty()) {
//            System.out.println("No messages found.");
//            return;
//        }
//        Gson g = new Gson();
//        for (Message rootMessage : messages) {
//            System.out.println("MsgID:" + rootMessage.getId());
//            System.out.println(g.toJson(rootMessage));
//
//            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date date = gmailQuickStart.getSendDate(rootMessage);
//            if (date != null) {
//                String dateString = formatter.format(date);
//                System.out.println("邮件发送日期：" + dateString);
//            }
//            System.out.println("邮件主题：" + gmailQuickStart.getSubject(rootMessage));
//            Message message = gmailQuickStart.getMessageData(rootMessage);
//            System.out.println("-------数据---------------");
//            System.out.println(g.toJson(message));
//            String url = gmailQuickStart.getLinkUrl(message, wordsBeforeLink, "https");
//            System.out.println("url：" + url);
//            break;
//        }
//    }
//
//    public static void testAttachment(GmailApi gmailQuickStart, String subject, String email) throws Exception {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
//        Date date0 = sdf.parse("2022/05/16");
//
//        List<Message> messages = gmailQuickStart.getMessages(new String[]{subject}, date0);
//        if (messages.isEmpty()) {
//            System.out.println("No messages found.");
//            return;
//        }
//        for (Message rootMessage : messages) {
//            if (!subject.equals(gmailQuickStart.getSubject(rootMessage))) {
//                continue;
//            }
//            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date date = gmailQuickStart.getSendDate(rootMessage);
//            if (date != null) {
//                String dateString = formatter.format(date);
//                System.out.println("邮件发送日期：" + dateString);
//            }
//            System.out.println("邮件主题：" + gmailQuickStart.getSubject(rootMessage));
//
//            Gson g = new Gson();
//            Message message = gmailQuickStart.getMessageData(rootMessage);
//            //System.out.println(g.toJson(message));
//            AttachmentDesc attachmentDesc = gmailQuickStart.getAttachmentInfo(rootMessage, message, true);
//            if (attachmentDesc == null) {
//                System.out.println("没有找到附件");
//                return;
//            }
//            System.out.println("附件名称：" + attachmentDesc.fileName);
//            String content = Base64Decoder.decodeStr(attachmentDesc.messagePartBody.getData());//解码
//            System.out.println("-----------附件内容如下------------------");
//            //System.out.println(content);
//            //break;
//        }
//    }
}
