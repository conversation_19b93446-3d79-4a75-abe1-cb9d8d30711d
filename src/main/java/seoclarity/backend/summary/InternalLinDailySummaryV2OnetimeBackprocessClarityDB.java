package seoclarity.backend.summary;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkImpl;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer1Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer2Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer3Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer4Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer5Dao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.entity.NodeStatusVO;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.entity.clickhouse.internallink.ProcessListVO;
import seoclarity.backend.utils.ClarityDBAPIUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;
// mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.InternalLinDailySummaryV2OnetimeBackprocessClarityDB" -Dexec.args="20230313 20230413"
public class InternalLinDailySummaryV2OnetimeBackprocessClarityDB {
	
	private InternalLinkNewClusterServer1Dao internalLinkNewClusterServer1Dao;
	private InternalLinkNewClusterServer2Dao internalLinkNewClusterServer2Dao;
	private InternalLinkNewClusterServer3Dao internalLinkNewClusterServer3Dao;
	private InternalLinkNewClusterServer4Dao internalLinkNewClusterServer4Dao;
	private InternalLinkNewClusterServer5Dao internalLinkNewClusterServer5Dao;
	
	private CrawlRequestLogDAO crawlRequestLogDAO;
	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;
	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	
//	private CrawlRequestModifylog crawlRequestModifylog;
	
	private List<ProcessListVO> finalProssList = new ArrayList<>();
	private List<ProcessListVO> tmpProcessList = new ArrayList<>();
	
	private Gson gson = new Gson();
	
	private final static Integer DEFAULT_VERSION_FOR_INTERNAL_LINK = 0;
//	private static Integer version = DEFAULT_VERSION_FOR_INTERNAL_LINK;
//	private static Integer sepCrawlLogId;
//	private static Integer sepDomainId;
	
//	private static boolean reprocessFlag = false;
	
	private static final String databaseName = "actonia_internal_link";
	
	//RE-create this distribute table for api to query for temp data only
	
	//claritydb_upload_log id -- mdb010
	
	public static List<Integer> ignoreCrawlIdList = new ArrayList<>();
	static {
		
		ignoreCrawlIdList.add(10101010);
		ignoreCrawlIdList.add(10101011);
		ignoreCrawlIdList.add(10101013);
		ignoreCrawlIdList.add(10101016);
		ignoreCrawlIdList.add(10101017);
		ignoreCrawlIdList.add(10101018);
		ignoreCrawlIdList.add(10101019);
		ignoreCrawlIdList.add(10101020);
		ignoreCrawlIdList.add(10101091);
		ignoreCrawlIdList.add(11111157);
		ignoreCrawlIdList.add(11122233);
		ignoreCrawlIdList.add(99923100);
		ignoreCrawlIdList.add(99923102);
		ignoreCrawlIdList.add(99923103);
		ignoreCrawlIdList.add(101010118);
		ignoreCrawlIdList.add(101013121);
		ignoreCrawlIdList.add(101013130);
		ignoreCrawlIdList.add(101023121);
		ignoreCrawlIdList.add(555445565);
		ignoreCrawlIdList.add(555445566);
		ignoreCrawlIdList.add(555445568);
		ignoreCrawlIdList.add(555577777);
		ignoreCrawlIdList.add(991346011);
		ignoreCrawlIdList.add(991758011);
		ignoreCrawlIdList.add(991758012);
		ignoreCrawlIdList.add(991758611);
		ignoreCrawlIdList.add(1000263483);
		ignoreCrawlIdList.add(1234134657);
		
		//https://www.wrike.com/open.htm?id=775219263
		ignoreCrawlIdList.add(9917580);
		ignoreCrawlIdList.add(9917586);
	}
	
	public InternalLinDailySummaryV2OnetimeBackprocessClarityDB(){
		internalLinkNewClusterServer1Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer1Dao");
		internalLinkNewClusterServer2Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer2Dao");
		internalLinkNewClusterServer3Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer3Dao");
		internalLinkNewClusterServer4Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer4Dao");
		internalLinkNewClusterServer5Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer5Dao");
		crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
//		crawlRequestModifylog = SpringBeanFactory.getBean("crawlRequestModifylog");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		disSiteCrawlDoc1Dao = SpringBeanFactory.getBean("disSiteCrawlDoc1Dao");
	}
	
	private static boolean processBigCrawl = false;
	
	private static boolean onlyRunForFinalHourly = false;
	
	private static Integer startDate;
	private static Integer endDate;

	public static void main(String[] args) {
		
		if (args != null && args.length >= 2) {
			startDate = NumberUtils.toInt(args[0]);
			endDate = NumberUtils.toInt(args[1]);
			
			if (args.length >= 3) {
				processBigCrawl = BooleanUtils.toBoolean(args[1]);
			}
			
		}
		
		InternalLinDailySummaryV2OnetimeBackprocessClarityDB internalLinkDailySummaryForClarityDB = new InternalLinDailySummaryV2OnetimeBackprocessClarityDB();
		
		internalLinkDailySummaryForClarityDB.process();
	}
	
	private static final Integer LIMITATION_5M = 5000000;
	private static final Integer LIMITATION_2E = 200000000;
	
	private static final String FINAL_TABLE_NAME = "scott_temp_dis_internal_link_sampled_view_final";
	/*
	 * daily summary for each crawl request id
	 */
	private void summary() {
		// -------------------------------  final  -----------------------------------
		
		System.out.println("@@@ processing to final table ");
		for (ProcessListVO vo : finalProssList) {
			
			if (processBigCrawl) {
				summaryBigCrawl(vo);
				continue;
			}
			
			int partitionNum = 0;
			if (vo.getCnt() >= LIMITATION_5M) {
				partitionNum = (vo.getCnt() / LIMITATION_5M) + 1;
			}
			
			System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
			try {
				if (partitionNum == 0) {
					long startTime = System.currentTimeMillis();
					internalLinkNewClusterServer4Dao.summary(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), FINAL_TABLE_NAME);
					long endTime = System.currentTimeMillis();
					int elapsedSeconds = (int) (endTime - startTime);
					System.out.println("Take :" + (elapsedSeconds/1000) + " sec !");
				} else {
					for(int i = 0; i < partitionNum; i ++) {
						long startTime = System.currentTimeMillis();
						System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
						internalLinkNewClusterServer4Dao.summaryByHash(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), 
								FINAL_TABLE_NAME, partitionNum, i);
						long endTime = System.currentTimeMillis();
						int elapsedSeconds = (int) (endTime - startTime);
						System.out.println("Take :" + (elapsedSeconds/1000) + " sec !");
					}
				}
				
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		}
		// -------------------------------  final  -----------------------------------
		
	}
	
	
	private void summaryBigCrawl(ProcessListVO vo) {
		
		//step 1 summary to dis_summary_outbound_detail
		System.out.println(" ============= summary dis_summary_outbound_detail ");
//		internalLinkNewClusterServer4Dao.summary(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), TMP_DIS_TABLE_NAME);
		
		
		//step 2 summary to dis_summary_detail
		System.out.println(" ============== summary dis_summary_detail ");
		
	}
	
	
	private void process() {
		
		
		//step2. load summary data 
		try {
			
			List<CrawlRequestLog> needProcessList = crawlRequestLogDAO.getCrawlIdListByDateRange(startDate, endDate);
			
			System.out.println("needProcessCrawler size : " + (needProcessList!= null ? needProcessList.size() : 0));
			
			for(CrawlRequestLog crawlRequestLog : needProcessList) {
				
				ProcessListVO vo = internalLinkNewClusterServer4Dao.getSummaryVO(crawlRequestLog.getId(), crawlRequestLog.getOwnDomainId());
				
				if (vo == null || vo.getCnt() == 0) {
					System.out.println("skip!");
					continue;
				}
				
				if (!processBigCrawl && vo.getCnt() >= LIMITATION_2E) {
					System.out.println("====== skip crawl over 2E, ID:" + vo.getCrawl_request_log_id_i());
					continue;
				}
				
				if (processBigCrawl && vo.getCnt() < LIMITATION_2E) {
					System.out.println("====== skip crawl less than 2E, ID:" + vo.getCrawl_request_log_id_i());
					continue;
				}
				
				Date lastDate = FormatUtils.toDate(vo.getToday(), "yyyy-MM-dd");
				
				Date last3Day = DateUtils.addDays(new Date(), -3);
				
				boolean haveDataInLast3Days = lastDate.after(last3Day);
				
				Integer crawlRequestId = crawlRequestLog.getId();
				
				System.out.println("===crawlRequestId:" + crawlRequestId + ", contains:" + ignoreCrawlIdList.contains(crawlRequestId));
				if (ignoreCrawlIdList.contains(crawlRequestId)) {
					System.out.println("==== Skip for crawl in the ignore list, cid:" + crawlRequestId);
					continue;
				}
						
				if (!haveDataInLast3Days) {
					System.out.println("==== crawler is finished, sumamry in final table : " + crawlRequestId);
					finalProssList.add(vo);
					continue;
				} else {
					System.out.println("==== crawler is not finished, last date in CDB:" + vo.getToday());
				}
				
			}
			
			if (CollectionUtils.isEmpty(finalProssList)) {
				System.out.println("No more crawler need to process today!!!");
			} else {
				
				try {
					crawlRequestLogDAO.updateAdditionalStatus(finalProssList);
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				System.out.println("==== start summary, final process list : " + finalProssList.size());
				System.out.println("=== final list:" + gson.toJson(finalProssList));
				summary();
			}
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
