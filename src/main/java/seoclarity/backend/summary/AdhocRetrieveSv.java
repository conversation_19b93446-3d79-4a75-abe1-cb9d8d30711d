package seoclarity.backend.summary;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.PropertiesCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.StorageClass;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.UserDAO;
import seoclarity.backend.dao.actonia.adhoc.AdhocRankSvProjectRelDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankKeywordSVEntityDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankProjectEntityDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocSvRetrieveCtrlDAO;
import seoclarity.backend.dao.rankcheck.AdwordsGeoIdDAO;
import seoclarity.backend.entity.AgencyInfoEntity;
import seoclarity.backend.entity.actonia.AdwordsGeoIdTableEntity;
import seoclarity.backend.entity.actonia.KeywordWithoutSearchVolumeEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.UserEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankKeywordSVEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity;
import seoclarity.backend.entity.actonia.adhoc.DateForSeoTaskData;
import seoclarity.backend.entity.bean.FTPServerInfoBean;
import seoclarity.backend.entity.rankcheck.GeoMasterEntity;
import seoclarity.backend.service.AgencyInfoManager;
import seoclarity.backend.service.CleanUpUtil;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.GeoService;
import seoclarity.backend.upload.Utils.SecretKeyUtil;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.S3Utils;
import seoclarity.backend.utils.cityhash.CityHashUtil;

import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static seoclarity.backend.utils.SeagateUtils.SIGNATURE_BUCKET_TYPE_TEMPORARY_FILES;


@CommonsLog
public class AdhocRetrieveSv {

    private static final int RETRY_COUNT = 3;
    private static final String GOOGLE_POST_URL = "https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/task_post";
    private static final String BING_POST_URL = "https://api.dataforseo.com/v3/keywords_data/bing/search_volume/task_post";
    private static final String GOOGLE_TASK_READY_URL = "https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/tasks_ready";
    private static final String BING_TASK_READY_URL = "https://api.dataforseo.com/v3/keywords_data/bing/search_volume/tasks_ready";
    private static final String GOOGLE_GET_URL = "https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/task_get/%s";
    private static final String BING_GET_URL = "https://api.dataforseo.com/v3/keywords_data/bing/search_volume/task_get/%s";

    private static final String DFS_USER_NAME = "<EMAIL>";
    private static String credential;
    private static int GOOGLE_REQUEST_KEYWORD_COUNT = 1000;
    private static int BING_REQUEST_KEYWORD_COUNT = 200;

    private static final String METRICS_NETWORK = "googlesearchnetwork";
    private static final String SPLIT = "\t";
    private static final int DEFAULT_RANK_DEAD_MESSAGE_COUNT = 0;
    private static final int SSH_TRY_COUNT = 20;
    private static final int RESULT_OK = 1;
    private static final int RESULT_ERROR = 0;
    private static final String UPLOAD_FILE_S3_BUCHET_NAME = "adhoc-ranking";
    private static final int SMALL_FILE_LINE_COUNT = 1000;

    private static final String AUTO_ADHOC_RANK_KEYWORD_SV_RESOURCE_WORKDIR = "/home/";
    private static final String AUTO_ADHOC_RANK_KEYWORD_SV_IN_FILE_PREFIX = "auto_adhoc_rank_keyword_sv_input_";
    private static final String AUTO_ADHOC_RANK_KEYWORD_SV_OUT_FILE_PREFIX = "auto_adhoc_rank_keyword_sv_output_";
    private static final String AUTO_ADHOC_RANK_KEYWORD_SV_FILE_SUFFIX = ".txt";
    private static final int CLARITYDB_URLHASH_RETRY_COUNT = 3;
    private static final String SPLIT_FIELD = "\t";
    private static final String GLOBAL_COUNTRY = "00";
    private static final int GLOBAL_CRITERIAID = 0;
    private static String LOC = "/home/<USER>/";
    private static String COPY_FILE_PATH = "/home/<USER>/AutoAdhocFile/";
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");

    private static List<String> emailList = new ArrayList<>();
    private List<String> rejectKeywordList = new ArrayList<>();

    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
    private AutoAdhocRankKeywordSVEntityDAO autoAdhocRankKeywordSVEntityDAO;
    private AutoAdhocSvRetrieveCtrlDAO autoAdhocSvRetrieveCtrlDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    private AgencyInfoManager agencyInfoManager;
    private UserDAO userDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private AdwordsGeoIdDAO adwordsGeoIdDAO;
    private GeoService geoService;
    private AdhocRankSvProjectRelDAO adhocRankSvProjectRelDAO;

    public AdhocRetrieveSv() {
        autoAdhocRankProjectEntityDAO = SpringBeanFactory.getBean("autoAdhocRankProjectEntityDAO");
        autoAdhocRankKeywordSVEntityDAO = SpringBeanFactory.getBean("autoAdhocRankKeywordSVEntityDAO");
        autoAdhocSvRetrieveCtrlDAO = SpringBeanFactory.getBean("autoAdhocSvRetrieveCtrlDAO");
        agencyInfoManager = SpringBeanFactory.getBean("agencyInfoManager");
        userDAO = SpringBeanFactory.getBean("userDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        adwordsGeoIdDAO = SpringBeanFactory.getBean("adwordsGeoIdDAO");
        geoService = SpringBeanFactory.getBean("geoService");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        adhocRankSvProjectRelDAO = SpringBeanFactory.getBean("adhocRankSvProjectRelDAO");

        credential = Credentials.basic(DFS_USER_NAME, SecretKeyUtil.getTokenBySecretNameAndJsonKeyName("prod/DSF/SV_Pwd", "password"));
    }

    public static void main(String[] args) {

//        getResult("07150110-0351-0110-0000-822b4677bb6b");

        AdhocRetrieveSv adhocRetrieveSv = new AdhocRetrieveSv();
        boolean isSmallTask = false;
        if (args != null && args.length >= 1) {
            isSmallTask = Boolean.parseBoolean(args[0]);
            adhocRetrieveSv.processProject(isSmallTask);
            adhocRetrieveSv.retrieveFromDifferentSource();
            adhocRetrieveSv.updateProjectStatus();
            adhocRetrieveSv.extractFile();
        }else {
            adhocRetrieveSv.processProject(isSmallTask);
            adhocRetrieveSv.retrieveFromDifferentSource();
            adhocRetrieveSv.updateProjectStatus();
            adhocRetrieveSv.extractFile();
        }

//        test();
    }

    private static void test() {
        try {
            List<String> keywordList = new ArrayList<>();
            keywordList.add("iphone");
            keywordList.add("bird");
            keywordList.add("axis+web");

            LocalDate now = LocalDate.now();
            LocalDate minDate = now.minusMonths(24);
            String dateFrom = minDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            HashMap<String, Object> parameters = new HashMap<>();
            parameters.put("keywords", keywordList);
            parameters.put("location_code", 2480);
            parameters.put("date_from", dateFrom);
            String requestBody = new Gson().toJson(parameters);
//            String requestBody = generateParametersForPost(parameters);
            log.info("====requestBody:" + "[" + requestBody + "]");

//            keywordList.add(URLEncoder.encode("Cameras deals", "utf-8"));
//            keywordList.add(URLEncoder.encode("Optics deals", "utf-8"));
//            keywordList.add(URLEncoder.encode("Camera Accessories deals", "utf-8"));
//            keywordList.add(URLEncoder.encode("Photography deals", "utf-8"));
//            Integer criteriaId = 2840;
//            String taskId = "123";
//            List<Result> responseSvList = getResult(taskId, 1);
//            log.info("==responseSvList count:" + responseSvList.size());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void processProject(boolean isSmallTask) {

        List<Integer> statusList = new ArrayList<Integer>();
        statusList.add(AutoAdhocRankProjectEntity.STATUS_FILE_UPLOAD_TO_FTP);
        statusList.add(AutoAdhocRankProjectEntity.STATUS_PROCESSING);
        statusList.add(AutoAdhocRankProjectEntity.STATUS_SAVE_KEYWORDS_TO_DB_ERROR);

        List<AutoAdhocRankProjectEntity> taskList = autoAdhocRankProjectEntityDAO.getPrepareRunTask(statusList);
        if (org.springframework.util.CollectionUtils.isEmpty(taskList)) {
            System.out.println("===no task need to run,exit!!!");
            return;
        }

        for (AutoAdhocRankProjectEntity adhocRankProject : taskList) {

            rejectKeywordList = new ArrayList<>();
            Integer projectId = adhocRankProject.getId();
            Integer domainId = adhocRankProject.getOwnDomainId();
            Integer retrieveType = adhocRankProject.getRetrieveType();
            int projectStatus = adhocRankProject.getStatus();
            Integer rankStatus = adhocRankProject.getRankStatus();
            Integer seedKeywordType = adhocRankProject.getSeedKeywordType() == null ? AutoAdhocRankProjectEntity.SEED_KEYWORD_TYPE_FILE : adhocRankProject.getSeedKeywordType();

            int keywordType = adhocRankProject.getKeywordType() == null ? AutoAdhocRankProjectEntity.KEYWORD_TYPE_NATIONAL : adhocRankProject.getKeywordType().intValue();
            Integer geoPattern = adhocRankProject.getGeoPattern();
            boolean isGeo = (keywordType == AutoAdhocRankProjectEntity.KEYWORD_TYPE_GEO);
            System.out.println("====ProjectType:" + projectId + " kwType:" + keywordType + " geoPattern:" + geoPattern + " isGeo:" + isGeo);

            try {
                String fullPathLocalFileName = null;
                if (projectStatus == AutoAdhocRankProjectEntity.STATUS_FILE_UPLOAD_TO_FTP) {
                    System.out.println("========StartProcessProjectId:" + projectId + " OID:" + domainId + " retrieveType:" + retrieveType +
                            " status:" + projectStatus + " rankStatus:" + rankStatus);
                    OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
                    if (ownDomainEntity == null) {
                        System.out.println(" ==NoActiveOID:" + projectId + " OID:" + domainId + " updStatus:" + AutoAdhocRankProjectEntity.STATUS_SKIP_INACTIVE_DOMAIN);
                        autoAdhocRankProjectEntityDAO.updateStatus(projectId, AutoAdhocRankProjectEntity.STATUS_SKIP_INACTIVE_DOMAIN, DEFAULT_RANK_DEAD_MESSAGE_COUNT);
                        continue;
                    }

                    if (seedKeywordType == AutoAdhocRankProjectEntity.SEED_KEYWORD_TYPE_FILE) {
//                        fullPathLocalFileName = copyFileFromFTP(adhocRankProject.getFtpFullPathFilename(), true);
//                        fullPathLocalFileName = downloadFileFromSeagate(projectId, domainId, adhocRankProject.getFtpFullPathFilename());
                        fullPathLocalFileName = copyFileWithDifferentType(projectId, domainId, adhocRankProject.getFtpFullPathFilename(), true);
                        if (fullPathLocalFileName == null) {
                            System.out.println(" ==CopyFTPFileError:" + projectId + " OID:" + domainId);
                            sendErrorMail("Failed to copy file from FTP", "Copy file from FTP failed(projectId:" + projectId + " OID:" + domainId + ")");
                            continue;
                        }

                        if (isSmallTask) {
                            long lines = seoclarity.backend.utils.FileUtils.countOfLines(fullPathLocalFileName);
                            if (lines > SMALL_FILE_LINE_COUNT) {
                                log.info("===skip large task:" + lines + ",projectId:" + projectId);
                                continue;
                            }
                        }

                    }

                    Integer criteriaId = null;
                    if (AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV.contains(retrieveType)) {
                        if(adhocRankProject.getCountry().equalsIgnoreCase(GLOBAL_COUNTRY)){//Global SV https://www.wrike.com/open.htm?id=1189677908
                            criteriaId = GLOBAL_CRITERIAID;
                            autoAdhocRankProjectEntityDAO.updateAdwordsInfo(projectId, GLOBAL_CRITERIAID, "Global");
                            System.out.println(" ==UpdAdwordsInfo:" + projectId + " OID:" + domainId + " countryCode: Global");
                        }else {
                            String countryCode = getCountryCode(adhocRankProject.getCountry());
                            AdwordsGeoIdTableEntity adwordsGeoIdTableEntity = adwordsGeoIdDAO.findCriteriaByCountryCode(countryCode);
                            if (adwordsGeoIdTableEntity == null || adwordsGeoIdTableEntity.getCriteriaId() == null || adwordsGeoIdTableEntity.getCriteriaId().intValue() == 0 ||
                                    StringUtils.isEmpty(adwordsGeoIdTableEntity.getCanonicalName())) {
                                if (AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV.contains(retrieveType)) {
                                    // status=>1004, retrieveSVStatus=>5
                                    autoAdhocRankProjectEntityDAO.updateProjectSVStatus(projectId, AutoAdhocRankProjectEntity.STATUS_COMPLETED_WITH_ERROR,
                                            AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_SKIP_NO_VALID_ADWORDS_GEO_ID);
                                    System.out.println(" ==SkipProjectForNoValidAdwordsGeo:" + projectId + " OID:" + domainId + " countryCode:" + countryCode);
                                    sendErrorMail("Skip for no AdwordsGeo", "Not found AdwordsGeo(projectId:" + projectId + " OID:" + domainId + " countryCode:" + countryCode + ")");
                                    continue;
                                } else {
                                    // retrieveSVStatus=>5
                                    autoAdhocRankProjectEntityDAO.updateRetrieveSVStatus(projectId, AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_SKIP_NO_VALID_ADWORDS_GEO_ID);
                                    System.out.println(" ==SkipRetrieveSVForNoValidAdwordsGeo:" + projectId + " OID:" + domainId + " countryCode:" + countryCode);
                                    sendErrorMail("Skip RetrieveSV for no valid AdwordsGeo(continue to rank)", "Not found valid AdwordsGeo(projectId:" + projectId +
                                            " OID:" + domainId + " countryCode:" + countryCode + ")");
                                }
                            } else {
                                criteriaId = adwordsGeoIdTableEntity.getCriteriaId();
                                autoAdhocRankProjectEntityDAO.updateAdwordsInfo(projectId, adwordsGeoIdTableEntity.getCriteriaId(), adwordsGeoIdTableEntity.getCanonicalName());
                                System.out.println(" ==UpdAdwordsInfo:" + projectId + " OID:" + domainId + " countryCode:" + countryCode +
                                        " criteriaId:" + adwordsGeoIdTableEntity.getCriteriaId() + " canonicalName:" + adwordsGeoIdTableEntity.getCanonicalName());
                            }
                        }
                    }

                    adhocRankProject.setCriteriaId(criteriaId);

                    Integer frequencyType = adhocRankProject.getFrequencyType();
                    //send to amazon S3 //cancel send to s3 https://www.wrike.com/open.htm?id=1372992310
//                    if (seedKeywordType == AutoAdhocRankProjectEntity.SEED_KEYWORD_TYPE_FILE) {
//                        int sendResult = sendToS3(frequencyType, projectId, domainId, fullPathLocalFileName);
//                        if (sendResult == RESULT_ERROR) {
//                            System.out.println(" ==SendToS3Error:" + projectId + " OID:" + domainId);
//                            sendErrorMail("Send file to s3 error", "Send file to s3 error(projectId:" + projectId + " OID:" + domainId + ")");
//                        }
//                    }

                    if (retrieveType  == AutoAdhocRankProjectEntity.RETRIEVE_TYPE_RANKCHECK_ONLY) {
                        autoAdhocRankProjectEntityDAO.updateRetrieveSVStatus(projectId, AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_NO_NEED_RETRIEVE_SV);
                    } else if (AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV.contains(retrieveType)) {
                        autoAdhocRankProjectEntityDAO.updateRankStatus(projectId, AutoAdhocRankProjectEntity.RANK_STATUS_NO_NEED_RANKCHECK);
                    }

                } else {
//                    fullPathLocalFileName = copyFileFromFTP(adhocRankProject.getFtpFullPathFilename(), false);
//                    fullPathLocalFileName = COPY_FILE_PATH + projectId + "_" + domainId + ".txt";
                    fullPathLocalFileName = copyFileWithDifferentType(projectId, domainId, adhocRankProject.getFtpFullPathFilename(), false);
                    if (isSmallTask) {
                        long lines = seoclarity.backend.utils.FileUtils.countOfLines(fullPathLocalFileName);
                        if (lines > SMALL_FILE_LINE_COUNT) {
                            log.info("===skip large task2:" + lines + ",projectId:" + projectId);
                            continue;
                        }
                    }

                }


                // save keywords in file to auto_adhoc_rank_keyword_sv table
                if (projectStatus == AutoAdhocRankProjectEntity.STATUS_FILE_UPLOAD_TO_FTP || projectStatus == AutoAdhocRankProjectEntity.STATUS_SAVE_KEYWORDS_TO_DB_ERROR) {
                    if (projectStatus == AutoAdhocRankProjectEntity.STATUS_FILE_UPLOAD_TO_FTP) {
                        System.out.println(" ##StartSaveKWsTODB:" + projectId + " OID:" + domainId + " retrieveType:" + retrieveType + " status:" + projectStatus);
                    } else {
                        System.out.println(" ##ReSaveKWsTODB:" + projectId + " OID:" + domainId + " retrieveType:" + retrieveType + " status:" + projectStatus);
                    }

                    boolean savedOK = true;
                    try {
                        savedOK = processKeywords(adhocRankProject, fullPathLocalFileName);
                    } catch (Exception exp) {
                        exp.printStackTrace();
                        savedOK = false;
                    }
                    if (savedOK == false) {
                        autoAdhocRankProjectEntityDAO.updateStatus(projectId, AutoAdhocRankProjectEntity.STATUS_SAVE_KEYWORDS_TO_DB_ERROR, DEFAULT_RANK_DEAD_MESSAGE_COUNT);
                        System.out.println(" ==SaveKWsToDBFailed:" + projectId + " OID:" + domainId);
                        sendErrorMail("Save keywords to DB failed", "Save keywords to DB failed(projectId:" + projectId + " OID:" + domainId + ")");
                        continue;
                    } else {
                        autoAdhocRankProjectEntityDAO.updateStatus(projectId, AutoAdhocRankProjectEntity.STATUS_PROCESSING, DEFAULT_RANK_DEAD_MESSAGE_COUNT); // status=>processing
                        projectStatus = AutoAdhocRankProjectEntity.STATUS_PROCESSING;
                        System.out.println(" ==UpdStatusToProcessing after SaveKWsToDBSucceeded:" + projectId + " OID:" + domainId);
                    }
                }

                //https://www.wrike.com/open.htm?id=654182714
                Integer notRejectKeywordCount = autoAdhocRankKeywordSVEntityDAO.getKeywordCount(projectId, false);
                if (notRejectKeywordCount == null || notRejectKeywordCount == 0) {
                    autoAdhocRankProjectEntityDAO.updateProjectAndRankStatus(projectId, AutoAdhocRankProjectEntity.STATUS_CANCELLED,
                            AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR);

                    sendCreateEmail(projectId, true);
                    projectStatus = AutoAdhocRankProjectEntity.STATUS_COMPLETED_SUCCESSFULLY;
                }


            } catch (Exception e) {
                e.printStackTrace();
                autoAdhocRankProjectEntityDAO.updateStatus(projectId, AutoAdhocRankProjectEntity.STATUS_COMPLETED_WITH_ERROR, DEFAULT_RANK_DEAD_MESSAGE_COUNT);
                System.out.println(" ==ErrorOccurredWhenProcessProject:" + projectId + " OID:" + domainId);
                sendErrorMail("Error occurred when process project", "Error occurred when process project(projectId:" + projectId + " OID:" + domainId + ")");
                continue;
            }

        }


    }

    /**
     * 从google 和 bing retrieve,先更新已经post但是没有更新的，再请求
     */
    private void retrieveFromDifferentSource(){
        List<Integer> retrieveTypeList = new ArrayList<>();
        retrieveTypeList.add(AutoAdhocRankProjectEntity.RETRIEVE_GOOGLE_SV_ONLY);//google
//        retrieveTypeList.add(AutoAdhocRankProjectEntity.RETRIEVE_RANKCHECK_AND_GOOGLE_SV);//google
        updatePostedProject(retrieveTypeList);
        startRetrieve(retrieveTypeList);

        retrieveTypeList.clear();
        retrieveTypeList.add(AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY);//bing
        updatePostedProject(retrieveTypeList);
        startRetrieve(retrieveTypeList);
    }

    /**
     * 获取已经post但是还没有拿到结果的project(taskId 已存在)
     * @param retrieveTypeList
     */
    private void updatePostedProject(List<Integer> retrieveTypeList){
        List<Integer> postedProjectIdList = autoAdhocRankProjectEntityDAO.getPostedProjectId(retrieveTypeList);
        if(CollectionUtils.isEmpty(postedProjectIdList)){
            log.info("=====no postedProjectIdList,exit.");
            return;
        }
        int retrieveType = retrieveTypeList.contains(AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY) ?
                AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY : AutoAdhocRankProjectEntity.RETRIEVE_GOOGLE_SV_ONLY;
        for(Integer projectId: postedProjectIdList){
            updateSVByTaskId(projectId, retrieveType);
        }
    }

    private void startRetrieve(List<Integer> retrieveTypeList) {

//        Date date = new Date();
//        Integer retrieveDate = FormatUtils.formatDateToYyyyMmDd(date);
//        Integer retrievedCount = autoAdhocSvRetrieveCtrlDAO.selectEntityByDate(retrieveDate);
//        if (retrievedCount == null) {
//            autoAdhocSvRetrieveCtrlDAO.insert(retrieveDate, 0);
//            retrievedCount = 0;
//        }
//        System.out.println("==bbbbbbbbb:" + retrievedCount);

        List<AutoAdhocRankProjectEntity> processProjectIdList = autoAdhocRankProjectEntityDAO.getUnRetrieveProject(retrieveTypeList);

        Map<String, AutoAdhocRankKeywordSVEntity> keywordSvMap = new HashMap<>();
        List<Result> allKeywordResultSvList = new ArrayList<>();

//        int lastCriteriaId = 0;
        for (AutoAdhocRankProjectEntity adhocRankProjectEntity : processProjectIdList) {

            int projectId = adhocRankProjectEntity.getId();
            int retrieveType = adhocRankProjectEntity.getRetrieveType();
            String language = adhocRankProjectEntity.getLanguage();//bing 使用，暂时只支持US-en,UK-en
            Integer keywordType = adhocRankProjectEntity.getKeywordType();
            Integer geoPattern = adhocRankProjectEntity.getGeoPattern();
            Integer criteriaId = adhocRankProjectEntity.getCriteriaId();
            if (geoPattern == AutoAdhocRankProjectEntity.GEO_PATTERN_ONLY_KEYWORD_IN_FILE && criteriaId == null) {
                log.error("criteriaId is null , skip , projectId : " + projectId);
                continue;
            }

            log.info("=====start project : " + projectId);
            //update not update sv by taskId
//            updateSVByTaskId(projectId);

            List<Integer> cityIdList = new ArrayList<>();

            if (geoPattern == AutoAdhocRankProjectEntity.GEO_PATTERN_BOTH_GEO_ID_AND_KEYWORD_IN_FILE && keywordType == AutoAdhocRankProjectEntity.KEYWORD_TYPE_GEO) {
                cityIdList = autoAdhocRankKeywordSVEntityDAO.getCitIdList(projectId);
            }

            List<AutoAdhocRankKeywordSVEntity> svEntityList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(cityIdList)) {
                for (Integer cityId : cityIdList) {
                    svEntityList = autoAdhocRankKeywordSVEntityDAO.getUnRetrieveKeywordList(projectId, cityId);
                    System.out.println("=======geo svEntityList:" + svEntityList.size() + ",cityId:" + cityId + ",criteriaId:" + criteriaId);

                    sendRequest(projectId, svEntityList, keywordSvMap, allKeywordResultSvList, retrieveType, language);
                    log.info("===keywordSvMap: " + keywordSvMap.size());
                    log.info("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                }
            } else {
                svEntityList = autoAdhocRankKeywordSVEntityDAO.getUnRetrieveKeywordList(projectId, null);
                System.out.println("==========================:" + svEntityList.size());
                sendRequest(projectId, svEntityList, keywordSvMap, allKeywordResultSvList, retrieveType, language);
            }

        }

        log.info("===keywordSvMap: " + keywordSvMap.size());
        log.info("===allKeywordResultSvList: " + allKeywordResultSvList.size());

        try {
            updateSvColumns(keywordSvMap, allKeywordResultSvList);
        } catch (Exception e) {
            String errorMsg = "updateSvColumns error";
            emailList.add(errorMsg);
            e.printStackTrace();
        }

        if (!CollectionUtils.isEmpty(emailList)) {
            sendMailReport();
        }

    }

    private void sendRequest(int projectId, List<AutoAdhocRankKeywordSVEntity> svEntityList,
                             Map<String, AutoAdhocRankKeywordSVEntity> keywordSvMap,
                             List<Result> allKeywordResultSvList, int retrieveType, String language) {

        List<Long> ids = new ArrayList<>();
        List<String> processing = new ArrayList<>();
        Integer criteriaId = null;
        List<String> rejectKwList = new ArrayList<>();
        for (AutoAdhocRankKeywordSVEntity adhocRankKeywordSVEntity : svEntityList) {

            criteriaId = adhocRankKeywordSVEntity.getCriteriaId();
            Integer svRetrieveCount = 0;
            try {
                String keyword = adhocRankKeywordSVEntity.getKeywordName().toLowerCase();
                String encodeKw = URLEncoder.encode(adhocRankKeywordSVEntity.getKeywordName(), "utf-8");
                autoAdhocRankProjectEntityDAO.updateProjectStatus(projectId, null, null, AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_PROCESSING);

                keyword = checkKeywordByRetrieveType(retrieveType, keyword);
                if (org.springframework.util.StringUtils.isEmpty(keyword)) {
                    log.info("===invalidkw:" + adhocRankKeywordSVEntity.getId());
                    autoAdhocRankKeywordSVEntityDAO.updateRejectKeywordById(adhocRankKeywordSVEntity.getId());
                    continue;
                }

                ids.add(adhocRankKeywordSVEntity.getId());
                keywordSvMap.put(keyword + "!_!" + criteriaId, adhocRankKeywordSVEntity);
                processing.add(keyword);
                List<Result> responseSvList = new ArrayList<>();
                int retry = 0;
                String result = null;
                int maxRequestKeywordCnt = 0;
                if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_GOOGLE_SV_ONLY){
                    maxRequestKeywordCnt = GOOGLE_REQUEST_KEYWORD_COUNT;
                }else if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY){
                    maxRequestKeywordCnt = BING_REQUEST_KEYWORD_COUNT;
                }else {
                    log.error("====error retrieveType:" + retrieveType);
                    return;
                }

                if (processing.size() >= maxRequestKeywordCnt) {
                    System.out.println(processing.size());
                    while (true) {
                        if (result == null && retry <= RETRY_COUNT) {
                            if (adhocRankKeywordSVEntity.getSvRetrieveStatus() != null && adhocRankKeywordSVEntity.getSvRetrieveStatus() == AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_PROCESSING && new Date().getTime() - new Date(adhocRankKeywordSVEntity.getSvRetrieveBatchNo()).getTime() >= 1000 * 60 * 60 * 72) {
                                result = adhocRankKeywordSVEntity.getSvRetrieveTaskId();
                                svRetrieveCount = adhocRankKeywordSVEntity.getSvRetrieveCount();
                                System.out.println("====no out:" + result);
                            } else {
                                result = postUrl(processing, criteriaId, projectId, rejectKwList, retrieveType, language);
                                System.out.println("===out：" + result);
                            }
                            if(CollectionUtils.isNotEmpty(rejectKwList)){
                                System.out.println("====UPReject Size:" + rejectKwList.size());
                                autoAdhocRankKeywordSVEntityDAO.updateRejectKeywords(projectId, rejectKwList);
                                rejectKwList = new ArrayList<>();
                            }

                            if (result != null) {
                                System.out.println("===postUrlSuc");
//                                retrievedCount++;
//                                autoAdhocSvRetrieveCtrlDAO.updateRetrievedCountById(retrievedCount, retrieveDate);
                                svRetrieveCount++;
                                autoAdhocRankKeywordSVEntityDAO.updateSv(AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_PROCESSING, new Date(), formatter.format(new Date()), svRetrieveCount, result, ids);
                                System.out.println("====:updateSv:" + result);
                                Thread.sleep(10 * 1000);
                                List<String> list = taskReady(retrieveType);
                                System.out.println("===taskReadySuc");
                                if (list != null && list.contains(result)) {
                                    responseSvList = getResult(result, projectId, retrieveType);
                                    if (CollectionUtils.isNotEmpty(responseSvList)) {
                                        System.out.println("===getResultSuc");
                                        allKeywordResultSvList.addAll(responseSvList);
                                        System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                                    } else {
                                        responseSvList = getResult(result, projectId, retrieveType);
                                        if (CollectionUtils.isNotEmpty(responseSvList)) {
                                            System.out.println("===getResultSuc");
                                            allKeywordResultSvList.addAll(responseSvList);
                                            System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                                        }
                                    }
                                    break;
                                } else {
                                    System.out.println("===two");
                                    Thread.sleep(30 * 1000);
                                    list = taskReady(retrieveType);
                                    System.out.println("===two getUrlSuc");
                                    if (list != null && list.contains(result)) {
                                        responseSvList = getResult(result, projectId, retrieveType);
                                        if (CollectionUtils.isNotEmpty(responseSvList)) {
                                            System.out.println("===two getResultSuc");
                                            allKeywordResultSvList.addAll(responseSvList);
                                            System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                                        } else {
                                            responseSvList = getResult(result, projectId, retrieveType);
                                            if (CollectionUtils.isNotEmpty(responseSvList)) {
                                                System.out.println("===two getResultSuc");
                                                allKeywordResultSvList.addAll(responseSvList);
                                                System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                                            }
                                        }
                                        break;
                                    }
                                }
                            }
                        } else {
                            break;
                        }
                        retry++;
                    }
                    processing.clear();
                    ids.clear();
                    System.out.println("===clear");
                }

            } catch (Exception ex) {
                log.error("===svEntityList error:" + projectId);
                String errorMsg = "retrieve sv error projectId:" + projectId;
                emailList.add(errorMsg);
                ex.printStackTrace();
            }
        }

        System.out.println("==============<800");
        if (CollectionUtils.isNotEmpty(processing) && CollectionUtils.isNotEmpty(ids)) {
            try {
                Integer svRetrieveCount = 0;
                List<Result> responseSvList = new ArrayList<>();
                int retry = 0;
                String result = null;
                while (true) {
                    if (result == null && retry <= RETRY_COUNT) {
                        log.info("***postUrl criteriaId:" + criteriaId + ",projectId:" + projectId + ",retrieveType:" + retrieveType + ",language:" + language);
                        result = postUrl(processing, criteriaId, projectId, rejectKwList, retrieveType, language);
                        if(CollectionUtils.isNotEmpty(rejectKwList)){
                            System.out.println("====UPReject Size:" + rejectKwList.size());
                            autoAdhocRankKeywordSVEntityDAO.updateRejectKeywords(projectId, rejectKwList);
                        }
                        if (result != null) {
                            System.out.println("===<800 postUrlSuc");
//                        retrievedCount++;
//                        autoAdhocSvRetrieveCtrlDAO.updateRetrievedCountById(retrievedCount, retrieveDate);
                            svRetrieveCount++;
                            autoAdhocRankKeywordSVEntityDAO.updateSv(AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_PROCESSING, new Date(), formatter.format(new Date()), svRetrieveCount, result, ids);
                            System.out.println("====:updateSv:" + result);
                            Thread.sleep(10 * 1000);
                            List<String> list = taskReady(retrieveType);
                            System.out.println("===<1000 getUrlSuc");
                            if (list != null && list.contains(result)) {
                                responseSvList = getResult(result, projectId, retrieveType);
                                if (CollectionUtils.isNotEmpty(responseSvList)) {
                                    System.out.println("===<1000 getResultSuc");
                                    allKeywordResultSvList.addAll(responseSvList);
                                    System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                                } else {
                                    responseSvList = getResult(result, projectId, retrieveType);
                                    if (CollectionUtils.isNotEmpty(responseSvList)) {
                                        System.out.println("===<1000 getResultSuc");
                                        allKeywordResultSvList.addAll(responseSvList);
                                        System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                                    }
                                }
                                break;
                            } else {
                                System.out.println("===<1000 two");
                                Thread.sleep(30 * 1000);
                                list = taskReady(retrieveType);
                                if (list != null && list.contains(result)) {
                                    responseSvList = getResult(result, projectId, retrieveType);
                                    if (CollectionUtils.isNotEmpty(responseSvList)) {
                                        System.out.println("===<1000 two getResultSuc");
                                        allKeywordResultSvList.addAll(responseSvList);
                                        System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                                    } else {
                                        responseSvList = getResult(result, projectId, retrieveType);
                                        if (CollectionUtils.isNotEmpty(responseSvList)) {
                                            System.out.println("===<1000 two getResultSuc");
                                            allKeywordResultSvList.addAll(responseSvList);
                                            System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                    } else {
                        break;
                    }
                    retry++;
                }

            } catch (Exception ex) {
                log.error("===svEntityList error:" + projectId);
                String errorMsg = "retrieve sv error projectId:" + projectId;
                emailList.add(errorMsg);
                ex.printStackTrace();
            }
        }

    }

    private void updateSVByTaskId(int projectId, int retrieveType) {

        Map<String, AutoAdhocRankKeywordSVEntity> keywordSVMap = new HashMap<>();
        Set<String> taskIdSet = new HashSet<>();
        List<AutoAdhocRankKeywordSVEntity> svEntityList = autoAdhocRankKeywordSVEntityDAO.getNotUpdateSVList(projectId);
        log.info("===updateSVByTaskId:" + projectId + ",svEntityList:" + svEntityList.size() + ",retrieveType:" + retrieveType);
        if (CollectionUtils.isNotEmpty(svEntityList)) {
            for (AutoAdhocRankKeywordSVEntity adhocRankKeywordSVEntity : svEntityList) {

                try {
                    taskIdSet.add(adhocRankKeywordSVEntity.getSvRetrieveTaskId());
//                    String encodeKeywordName = URLEncoder.encode(adhocRankKeywordSVEntity.getKeywordName(), "utf-8");
                    Integer criteriaId = adhocRankKeywordSVEntity.getCriteriaId();
                    if (criteriaId == null) {
                        log.error("====error no criteriaId:" + criteriaId);
                    }
                    String kwName = adhocRankKeywordSVEntity.getKeywordName().toLowerCase();
                    kwName = kwName.replaceAll("\\u00A0", " ");//remove nbsp to space
                    keywordSVMap.put(kwName + "!_!" + criteriaId, adhocRankKeywordSVEntity);
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }

            }
        }

        List<Result> allKeywordResultSvList = new ArrayList<>();
        log.info("===taskId size:" + taskIdSet.size());
        for (String taskId : taskIdSet) {
            List<Result> results = getResult(taskId, projectId, retrieveType);
            if (results == null) {
                log.error("===skip task:" + taskId + ",projectId:" + projectId);
            }else {
                allKeywordResultSvList.addAll(results);
                try {
                    log.info("======updateSvColumnsTaskId:" + taskId
                            + ",allKeywordResultSvListSize:" + allKeywordResultSvList.size());
                    updateSvColumns(keywordSVMap, allKeywordResultSvList);
                } catch (Exception e) {
                    String errorMsg = "updateSvColumns error";
                    emailList.add(errorMsg);
                    e.printStackTrace();
                }
                allKeywordResultSvList = new ArrayList<>();
            }

        }

//        try {
//            updateSvColumns(keywordSVMap, allKeywordResultSvList);
//        } catch (Exception e) {
//            String errorMsg = "updateSvColumns error";
//            emailList.add(errorMsg);
//            e.printStackTrace();
//        }


    }

    private static String postUrl(List<String> keywordList, int location, int projectId, List<String> rejectKwList, int retrieveType, String languageCode) throws UnsupportedEncodingException {

        LocalDate now = LocalDate.now();
//        LocalDate minDate = now.minusMonths(24);
        LocalDate minDate = now.minusMonths(48);//https://www.wrike.com/open.htm?id=1262127772
        String dateFrom = minDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        System.out.println("===postUrl:projectId:" + projectId + ",location:" +  ",retrieveType:" + retrieveType + ",languageCode:" + languageCode + ",date_from:" + dateFrom + ",time:" + LocalDate.now());
        MediaType mediaType = MediaType.parse("text/x-markdown; charset=utf-8");

        OkHttpClient client = new OkHttpClient();

        List<String> processingKeywordList = new ArrayList<>();
        processingKeywordList.addAll(keywordList);
        String requestUrl = "";
        int maxRequestCnt = 0;
        if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_GOOGLE_SV_ONLY){
            requestUrl = GOOGLE_POST_URL;
            maxRequestCnt = GOOGLE_REQUEST_KEYWORD_COUNT;
        }else if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY){
            requestUrl = BING_POST_URL;
            maxRequestCnt = BING_REQUEST_KEYWORD_COUNT;
        }
        log.info("*****************RqURL:" + requestUrl);
        int requestCnt = 0;
        while (true) {

            HashMap<String, Object> parameters = new HashMap<>();
            parameters.put("keywords", processingKeywordList);
            if(location != 0){//not global
                parameters.put("location_code", location);
            }
            parameters.put("date_from", dateFrom);
            if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY){
                parameters.put("language_code", languageCode);//todo 需要确认
            }
            String requestBody = "[" + new Gson().toJson(parameters) + "]";
//        String requestBody = generateParametersForPost(parameters);


            System.out.println(requestBody);

            Request request = new Request.Builder()
                    .url(requestUrl)
                    .header("Authorization", credential)
                    .post(RequestBody.create(mediaType, requestBody))
                    .build();
            Call call = client.newCall(request);
            try {
                Response response = call.execute();
                requestCnt++;

                if (requestCnt >= maxRequestCnt) {
                    log.error("========over maxRetryCnt:" + requestCnt);
                    return null;
                }

                //判断是否成功
                if (response.isSuccessful()) {
                    JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                    String length = jsonObject.get("tasks").toString();
                    JSONObject tasks = JSONObject.parseObject(length.substring(1, length.length() - 1));

                    String statusCode = tasks.get("status_code").toString();
                    log.info("-----------status_code:" + statusCode);
                    if (statusCode.equals("20100")) {
                        return tasks.get("id").toString();
                    } else if (statusCode.equals("40501")) {
                        //"status_message": "Invalid Field: 'keywords'. Keyword text has invalid characters or symbols: 'best+men%27s+face+routine'.",
                        //"status_message": Invalid Field: 'keywords'. Keyword text exceeds the allowed limit: 'cinnamon+beach+lane+home+near+ginn+ocean+hammock+beach+resort+site%3Avacationrentalpros.com'.
                        String statusMessage = tasks.get("status_message").toString();
//                        log.error("====status_message:" + statusMessage);
                        String errorKw = "";
                        if (statusMessage.contains("Keyword text has invalid characters or symbols")) {
                            errorKw = statusMessage.replaceAll("Invalid Field: 'keywords'. Keyword text has invalid characters or symbols: '", "");
                            errorKw = errorKw.substring(0, errorKw.length() - 2);
                            log.info("=====errorKw1:" + errorKw);

                        } else if (statusMessage.contains("Keyword text exceeds the allowed limit")) {
                            errorKw = statusMessage.replaceAll("Invalid Field: 'keywords'. Keyword text exceeds the allowed limit: '", "");
                            errorKw = errorKw.substring(0, errorKw.length() - 2);
                            log.info("=====errorKw2:" + errorKw);

                        } else if (statusMessage.contains("Keyword text has too many words")) {
                            errorKw = statusMessage.replaceAll("Invalid Field: 'keywords'. Keyword text has too many words: '", "");
                            errorKw = errorKw.substring(0, errorKw.length() - 2);
                            log.info("=====errorKw3:" + errorKw);

                        } else if (statusMessage.contains("Invalid Field: 'keywords'. Keyword is empty")) {
                            errorKw = statusMessage.replaceAll("Invalid Field: 'keywords'. Keyword is empty: '", "");
                            errorKw = errorKw.substring(0, errorKw.length() - 2);
                            log.info("=====errorKw4:" + errorKw);

                        }  else if(statusMessage.contains("Invalid Field: 'location_code'.")){
                            log.info("=====errorMsg:" + statusMessage + ",location:" + location);
                            String errorMsg = "post api Invalid location_code projectId:" + projectId + ",location:" + location + ",keywords size:" + keywordList.size();
                            emailList.add(errorMsg);
                            return null;
                        }else {
                            log.info("=====new error:" + statusMessage + ",info:" + JSON.toJSONString(tasks));
                            String errorMsg = "post api new error projectId:" + projectId + ",location:" + location + ",keywords size:" + keywordList.size();
                            emailList.add(errorMsg);
                            return null;
                        }
                        if(errorKw.contains("'")){
                            rejectKwList.add(errorKw.replaceAll("'", "\\\\'"));
                        }else {
                            rejectKwList.add(errorKw);
                        }
                        processingKeywordList.remove(errorKw);
                        if (processingKeywordList.size() <= 0) {
                            log.info("====no kw need to post!");
                            return null;
                        }

                    } else {
                        log.error("********post error status_code:" + statusCode + ",info:" + JSON.toJSONString(tasks));
                        String errorMsg = "post api error projectId:" + projectId + ",location:" + location + ",keywords size:" + keywordList.size();
                        emailList.add(errorMsg);
                        return null;
                    }

                } else {
                    System.out.println("post请求失败");
                    String errorMsg = "post api error projectId:" + projectId + ",location:" + location + ",keywords size:" + keywordList.size();
                    emailList.add(errorMsg);
                    return null;
                }
            } catch (Exception e) {
                System.out.println("post异常");
                String errorMsg = "post api error projectId:" + projectId + ",location:" + location + ",keywords size:" + keywordList.size();
                emailList.add(errorMsg);
                log.error(e.getMessage(), e);
                return null;
            }
        }
    }

    private void updateSvColumns(Map<String, AutoAdhocRankKeywordSVEntity> keywordSvMap, List<Result> allKeywordResultSvList) throws Exception {
        Long startTime = System.currentTimeMillis();
        List<AutoAdhocRankKeywordSVEntity> updateList = new ArrayList<>();

        Map<String, Result> keywordResultSvMap = new HashMap<>();
        for (Result result : allKeywordResultSvList) {
            String uniqueKey = result.getKeyword() + "!_!";
            if (result.getLocation_code() == null) {//global sv
                uniqueKey += GLOBAL_CRITERIAID;
            } else {
                uniqueKey += result.getLocation_code();
            }
            keywordResultSvMap.put(uniqueKey, result);
        }
        log.info("=====keywordResultSvMapSize:" + keywordResultSvMap.size());

        for(String key : keywordResultSvMap.keySet()){
            System.out.println("=========in:" + key);
            Date now = new Date();

            Result result = keywordResultSvMap.get(key);
            AutoAdhocRankKeywordSVEntity adhocRankKeywordSVEntity = keywordSvMap.get(key);
            if(adhocRankKeywordSVEntity == null){
                log.info("=====skipKw:" + key);
                continue;
            }
            adhocRankKeywordSVEntity.setSvRetrieveStatus(AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_SUCCESSFULLY);
            adhocRankKeywordSVEntity.setSvRetrieveTime(now);
            adhocRankKeywordSVEntity.setSvUpdateDate(FormatUtils.formatDateToYyyyMmDd(now));

            adhocRankKeywordSVEntity.setAvgSV(result.getSearch_volume());

            String se = result.getSe();
            log.info("********* se:" + se);
            if(se.equalsIgnoreCase("google_ads")){
                adhocRankKeywordSVEntity.setCpc(result.getCpc());
                adhocRankKeywordSVEntity.setCompetition(result.getCompetition_index());
            }else if(se.equalsIgnoreCase("bing")){
                adhocRankKeywordSVEntity.setCpc(result.getCpc());
                adhocRankKeywordSVEntity.setCompetition(result.getCompetition() == null ? null : Double.parseDouble(result.getCompetition()));
            }
//                    adhocRankKeywordSVEntity.setCategory(StringUtils.join(result.getCategories(), ","));

            List<Map<String, Integer>> jsonList = new ArrayList<>();
            List<Integer> svMonthList = new ArrayList<>();

            if (result.getMonthly_searches() != null) {
                System.out.println("=========not null:" + key);
                int index = 0;
                for (Result.MonthlySearches monthlySearches : result.getMonthly_searches()) {
                    index++;
                    int monthYear = 0;
                    if (monthlySearches.getMonth().toString().length() == 1) {
                        monthYear = NumberUtils.toInt(monthlySearches.getYear() + "0" + monthlySearches.getMonth());
                    } else {
                        monthYear = NumberUtils.toInt(monthlySearches.getYear() + "" + monthlySearches.getMonth());
                    }
                    svMonthList.add(monthYear);

                    Map<String, Integer> svMap = new HashMap<>();
                    svMap.put("month", monthYear);
                    svMap.put("sv", monthlySearches.getSearch_volume());
                    jsonList.add(svMap);

                    if (index <= 12) {//最近12个月
                        if (monthlySearches.getMonth() == 1) {
                            adhocRankKeywordSVEntity.setMonthlySV1(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 2) {
                            adhocRankKeywordSVEntity.setMonthlySV2(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 3) {
                            adhocRankKeywordSVEntity.setMonthlySV3(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 4) {
                            adhocRankKeywordSVEntity.setMonthlySV4(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 5) {
                            adhocRankKeywordSVEntity.setMonthlySV5(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 6) {
                            adhocRankKeywordSVEntity.setMonthlySV6(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 7) {
                            adhocRankKeywordSVEntity.setMonthlySV7(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 8) {
                            adhocRankKeywordSVEntity.setMonthlySV8(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 9) {
                            adhocRankKeywordSVEntity.setMonthlySV9(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 10) {
                            adhocRankKeywordSVEntity.setMonthlySV10(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 11) {
                            adhocRankKeywordSVEntity.setMonthlySV11(monthlySearches.getSearch_volume());
                        } else if (monthlySearches.getMonth() == 12) {
                            adhocRankKeywordSVEntity.setMonthlySV12(monthlySearches.getSearch_volume());
                        }
                    }

                }

                adhocRankKeywordSVEntity.setMonthlySVJson(new Gson().toJson(jsonList));
            } else {
                log.info("====no sv result:" + key);
                int svUpdateDate = FormatUtils.formatDateToYyyyMmDd(now);
                autoAdhocRankKeywordSVEntityDAO.updateNoSv(svUpdateDate, AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_SUCCESSFULLY, new Date(), adhocRankKeywordSVEntity.getId());
                continue;
            }

            adhocRankKeywordSVEntity.setStartMonth(Collections.min(svMonthList));
            adhocRankKeywordSVEntity.setEndMonth(Collections.max(svMonthList));

            updateList.add(adhocRankKeywordSVEntity);

            if (updateList.size() >= 1000) {
                System.out.println("======:updateList.size" + updateList.size());
                autoAdhocRankKeywordSVEntityDAO.updateSvForBatch(updateList);
                updateList = new ArrayList<>();
            }

        }

//        for (String key : keywordSvMap.keySet()) {
//
//            for (Result result : allKeywordResultSvList) {
//                String uniqueKey = result.getKeyword() + "!_!";
//                if(result.getLocation_code() == null){//global sv
//                    uniqueKey += GLOBAL_CRITERIAID;
//                }else {
//                    uniqueKey += result.getLocation_code();
//                }
//                if (key.equalsIgnoreCase(uniqueKey)) {
//                    System.out.println("=========in:" + key);
//                    Date now = new Date();
//                    AutoAdhocRankKeywordSVEntity adhocRankKeywordSVEntity = keywordSvMap.get(key);
//                    adhocRankKeywordSVEntity.setSvRetrieveStatus(AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_SUCCESSFULLY);
//                    adhocRankKeywordSVEntity.setSvRetrieveTime(now);
//                    adhocRankKeywordSVEntity.setSvUpdateDate(FormatUtils.formatDateToYyyyMmDd(now));
//
//                    adhocRankKeywordSVEntity.setAvgSV(result.getSearch_volume());
//
//                    String se = result.getSe();
//                    log.info("********* se:" + se);
//                    if(se.equalsIgnoreCase("google_ads")){
//                        adhocRankKeywordSVEntity.setCpc(result.getHigh_top_of_page_bid());
//                        adhocRankKeywordSVEntity.setCompetition(result.getCompetition_index());
//                    }else if(se.equalsIgnoreCase("bing")){
//                        adhocRankKeywordSVEntity.setCpc(result.getCpc());
//                        adhocRankKeywordSVEntity.setCompetition(result.getCompetition() == null ? null : Double.parseDouble(result.getCompetition()));
//                    }
////                    adhocRankKeywordSVEntity.setCategory(StringUtils.join(result.getCategories(), ","));
//
//                    List<Map<String, Integer>> jsonList = new ArrayList<>();
//                    List<Integer> svMonthList = new ArrayList<>();
//
//                    if (result.getMonthly_searches() != null) {
//                        System.out.println("=========not null:" + key);
//                        int index = 0;
//                        for (Result.MonthlySearches monthlySearches : result.getMonthly_searches()) {
//                            index++;
//                            int monthYear = 0;
//                            if (monthlySearches.getMonth().toString().length() == 1) {
//                                monthYear = NumberUtils.toInt(monthlySearches.getYear() + "0" + monthlySearches.getMonth());
//                            } else {
//                                monthYear = NumberUtils.toInt(monthlySearches.getYear() + "" + monthlySearches.getMonth());
//                            }
//                            svMonthList.add(monthYear);
//
//                            Map<String, Integer> svMap = new HashMap<>();
//                            svMap.put("month", monthYear);
//                            svMap.put("sv", monthlySearches.getSearch_volume());
//                            jsonList.add(svMap);
//
//                            if (index <= 12) {//最近12个月
//                                if (monthlySearches.getMonth() == 1) {
//                                    adhocRankKeywordSVEntity.setMonthlySV1(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 2) {
//                                    adhocRankKeywordSVEntity.setMonthlySV2(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 3) {
//                                    adhocRankKeywordSVEntity.setMonthlySV3(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 4) {
//                                    adhocRankKeywordSVEntity.setMonthlySV4(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 5) {
//                                    adhocRankKeywordSVEntity.setMonthlySV5(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 6) {
//                                    adhocRankKeywordSVEntity.setMonthlySV6(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 7) {
//                                    adhocRankKeywordSVEntity.setMonthlySV7(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 8) {
//                                    adhocRankKeywordSVEntity.setMonthlySV8(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 9) {
//                                    adhocRankKeywordSVEntity.setMonthlySV9(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 10) {
//                                    adhocRankKeywordSVEntity.setMonthlySV10(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 11) {
//                                    adhocRankKeywordSVEntity.setMonthlySV11(monthlySearches.getSearch_volume());
//                                } else if (monthlySearches.getMonth() == 12) {
//                                    adhocRankKeywordSVEntity.setMonthlySV12(monthlySearches.getSearch_volume());
//                                }
//                            }
//
//                        }
//
//                        adhocRankKeywordSVEntity.setMonthlySVJson(new Gson().toJson(jsonList));
//                    } else {
//                        log.info("====no sv result:" + key);
//                        int svUpdateDate = FormatUtils.formatDateToYyyyMmDd(now);
//                        autoAdhocRankKeywordSVEntityDAO.updateNoSv(svUpdateDate, AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_SUCCESSFULLY, new Date(), adhocRankKeywordSVEntity.getId());
//                        continue;
//                    }
//
//                    adhocRankKeywordSVEntity.setStartMonth(Collections.min(svMonthList));
//                    adhocRankKeywordSVEntity.setEndMonth(Collections.max(svMonthList));
//
//                    updateList.add(adhocRankKeywordSVEntity);
//
//                    if (updateList.size() >= 1000) {
//                        System.out.println("======:updateList.size" + updateList.size());
//                        autoAdhocRankKeywordSVEntityDAO.updateSvForBatch(updateList);
//                        updateList.clear();
//                    }
//                }
//
//            }
//
//        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            System.out.println("======<1000:updateList.size" + updateList.size());
            autoAdhocRankKeywordSVEntityDAO.updateSvForBatch(updateList);
        }

        log.info("=====耗时：" + (System.currentTimeMillis() - startTime)/1000 + "s");

    }

    private static List<String> taskReady(int retrieveType) {
        System.out.println("===taskReady time:" + LocalDateTime.now() + ",retrieveType:" + retrieveType);
        try {
            String requestUrl = "";
            if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_GOOGLE_SV_ONLY){
                requestUrl = GOOGLE_TASK_READY_URL;
            }else if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY){
                requestUrl = BING_TASK_READY_URL;
            }
            log.info("*****************taskReadyURL:" + requestUrl);
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(new Request.Builder().url(requestUrl).header("Authorization", credential).get().build()).execute();

            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                String tasksLength = jsonObject.get("tasks").toString();
                JSONObject tasks = JSONObject.parseObject(tasksLength.substring(1, tasksLength.length() - 1));
                if (tasks.get("result") == null) {
                    System.out.println("===no result time:" + LocalDateTime.now());
                    return null;
                }
                String resultLength = tasks.get("result").toString();
                String[] strings = resultLength.substring(1, resultLength.length() - 1).split("},");
                List<String> list = new ArrayList<>();
                for (String s : strings) {
                    if (!s.endsWith("}")) {
                        s = s + "}";
                    }
                    JSONObject result = JSONObject.parseObject(s);
                    list.add(result.get("id").toString());
                    System.out.println(result.get("id"));
                }
                return list;
            } else {
                System.out.println("get请求失败");
                String errorMsg = "gey tasks_ready api error";
                emailList.add(errorMsg);
            }
        } catch (Exception e) {
            System.out.println("get异常");
            String errorMsg = "gey tasks_ready api error";
            emailList.add(errorMsg);
            log.error(e.getMessage(), e);
        }
        return null;
    }

    private List<Result> getResult(String id, int projectId, int retrieveType) {
        System.out.println("===getResult projectId:" + projectId + ",id:" + id + " time:" + LocalDateTime.now() + ",retrieveType:" + retrieveType);
        try {
            String requestUrl = "";
            if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_GOOGLE_SV_ONLY){
                requestUrl = GOOGLE_GET_URL;
            }else if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY){
                requestUrl = BING_GET_URL;
            }
            log.info("**********************getResultURL:" + requestUrl);
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(new Request.Builder().url(String.format(requestUrl, id)).header("Authorization", credential).get().build()).execute();
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                String tasksLength = jsonObject.get("tasks").toString();
                JSONObject tasks = JSONObject.parseObject(tasksLength.substring(1, tasksLength.length() - 1));
                int statusCode = NumberUtils.toInt(tasks.get("status_code").toString());
                if (statusCode != 20000 && statusCode != 20100) {
                    String errMsg = tasks.get("status_message").toString();
                    log.error("task error:" + id + ",errMsg:" + errMsg);
                    //update kw sv status to not started and re-post
                    autoAdhocRankKeywordSVEntityDAO.updateSvStatusToNotStarted(projectId, id);
                    return null;
                }
                String resultLength = tasks.get("result").toString();
                List<Result> apiResultList = JSONArray.parseArray(resultLength, Result.class);
                DateForSeoTaskData dateForSeoTaskData = JSONObject.parseObject(tasks.get("data").toString(), DateForSeoTaskData.class);
                log.info("===========dateForSeoTaskData:" + JSON.toJSONString(dateForSeoTaskData));
                String se = dateForSeoTaskData.getSe();
                apiResultList.stream().forEach(v1 -> v1.setSe(se));

                return apiResultList;
            } else {
                System.out.println("api请求失败" + response.toString());
                String errorMsg = "get by taskId error projectId:" + projectId + ",taskId:" + id;
                emailList.add(errorMsg);
            }
        } catch (Exception e) {
            System.out.println("api异常");
            String errorMsg = "get by taskId api error projectId:" + projectId + ",taskId:" + id;
            emailList.add(errorMsg);
            log.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }


    //拼接参数用于POST请求
    private static String generateParametersForPost(HashMap<String, Object> parameters) {

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("[{\"");
        if (parameters.size() > 0) {
            Object[] keys = parameters.keySet().toArray();
            for (int i = 0; i < keys.length; i++) {
                if (i == (keys.length - 1)) {
                    stringBuffer.append(keys[i].toString()).append("\":").append(parameters.get(keys[i]).toString()).append("}]");
                    break;
                }
                stringBuffer.append(keys[i].toString()).append("\":").append(parameters.get(keys[i]).toString()).append(",\"");
            }
        }
        return stringBuffer.toString();
    }

    private void updateProjectStatus() {

        List<AutoAdhocRankProjectEntity> processingSvProjectList = autoAdhocRankProjectEntityDAO.getUnFinishedSvProject();
        if (CollectionUtils.isNotEmpty(processingSvProjectList)) {

            for (AutoAdhocRankProjectEntity autoAdhocRankProjectEntity : processingSvProjectList) {
                Integer projectId = autoAdhocRankProjectEntity.getId();
                Integer retrieveType = autoAdhocRankProjectEntity.getRetrieveType();
                Integer rankStatus = autoAdhocRankProjectEntity.getRankStatus();
                if (AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV.contains(retrieveType)) {
                    Integer unRetrieveCount = autoAdhocRankKeywordSVEntityDAO.getUnRetrieveKeywordCountByProject(projectId);
                    if (unRetrieveCount == null || unRetrieveCount.equals(0)) {
                        //https://www.wrike.com/open.htm?id=965836241
                        int uniqueKeywordCount = autoAdhocRankKeywordSVEntityDAO.getKeywordCount(projectId, false);
                        autoAdhocRankProjectEntityDAO.updateUniqueKeywordCount(projectId, uniqueKeywordCount);
                        System.out.println(" ====UpdKeywordCount projectId:" + projectId + " uniqueKWCnt:" + uniqueKeywordCount);
                        autoAdhocRankProjectEntityDAO.updateProjectStatus(projectId, AutoAdhocRankProjectEntity.STATUS_COMPLETED_SUCCESSFULLY, null, AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR);
                    }

                }
//                else if (retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_RANKCHECK_AND_GOOGLE_SV) {
//                    Integer unRetrieveCount = autoAdhocRankKeywordSVEntityDAO.getUnRetrieveKeywordCountByProject(projectId);
//                    if (unRetrieveCount == null || unRetrieveCount.equals(0)) {
//                        if (rankStatus == AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR) {
//                            autoAdhocRankProjectEntityDAO.updateProjectStatus(projectId, AutoAdhocRankProjectEntity.STATUS_COMPLETED_SUCCESSFULLY, null, AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR);
//                        } else {
//                            autoAdhocRankProjectEntityDAO.updateProjectStatus(projectId, null, null, AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR);
//                        }
//
//                    }
//                }
            }
        } else {
            log.info("==no project status need to update.");
        }

    }

    private void extractFile() {

        List<AutoAdhocRankProjectEntity> extractSvProjectList = autoAdhocRankProjectEntityDAO.getNotExtractSVProject();
        if (CollectionUtils.isNotEmpty(extractSvProjectList)) {

            int pageSize = 300;
            Long relId = 0l;
            int currentTotal = 0;

            for (AutoAdhocRankProjectEntity adhocRankProject : extractSvProjectList) {

                Integer projectId = adhocRankProject.getId();
                int retrieveType = adhocRankProject.getRetrieveType();
                if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_RANKCHECK_AND_GOOGLE_SV){
                    log.info("=====skip Parent svProjectId:" + projectId);
                    autoAdhocRankProjectEntityDAO.updateExportSVStatus(projectId, AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_COMPLETED_WITHOUT_ERROR);
                    continue;
                }
//                List<Integer> parentProjectId = adhocRankSvProjectRelDAO.getParentProjectId(projectId);
//                if (CollectionUtils.isNotEmpty(parentProjectId) && adhocRankProject.getRetrieveType() == AutoAdhocRankProjectEntity.RETRIEVE_GOOGLE_SV_ONLY) {
//                    log.info("=====skip child svProjectId:" + projectId);
//                    continue;
//                }
                try {
                    int ownDomainId = adhocRankProject.getOwnDomainId();
                    String projectName = adhocRankProject.getProjectName();
                    List<String> extractList = new ArrayList<>();

                    String localFilePath = LOC + ownDomainId + File.separator;
                    String fileName = "RetrieveSv_" + projectName + "_" + ownDomainId + "_" + FormatUtils.formatDate(new Date(), "yyyy-MM-dd") + ".csv";
                    File outFile = new File(localFilePath + fileName);
                    if (outFile.exists()) {
                        outFile.delete();
                    }

                    String startMonth = null;
                    String endMonth = null;
                    boolean isAppendHeader = false;
                    while (true) {

                        long a = System.currentTimeMillis();
                        List<AutoAdhocRankKeywordSVEntity> relList = autoAdhocRankKeywordSVEntityDAO.getKeywordByPages(projectId, relId, pageSize);
                        if (relList.size() == 0) {
                            break;
                        }

                        long b = System.currentTimeMillis();
                        currentTotal += relList.size();
                        System.out.println("Current size, relList:" + relList.size() + ", currentTotal:" + currentTotal + ", relId:" + relId
                                + ", projectId" + projectId + ", cost:" + (b - a) / 1000);

                        for (AutoAdhocRankKeywordSVEntity adhocRankKeywordSVEntity : relList) {
//                            if(StringUtils.isBlank(startMonth) && adhocRankKeywordSVEntity.getStartMonth() == null){
//                                log.error("===no start month:" + projectId);
//                                break;
//                            }
                            if (StringUtils.isBlank(startMonth) && adhocRankKeywordSVEntity.getStartMonth() != null) {
                                startMonth = adhocRankKeywordSVEntity.getStartMonth().toString();
                            }
                            if (StringUtils.isBlank(endMonth) && adhocRankKeywordSVEntity.getEndMonth() != null) {
                                endMonth = adhocRankKeywordSVEntity.getEndMonth().toString();
                            }

                            extractList.add(writeSvLine(adhocRankKeywordSVEntity));
                        }

                        if (!isAppendHeader && StringUtils.isNotBlank(startMonth) && StringUtils.isNotBlank(endMonth)) {
                            addHeadersForExactFile(outFile, startMonth, endMonth);
                            isAppendHeader = true;
                        } else if (isAppendHeader && StringUtils.isBlank(startMonth) && StringUtils.isBlank(endMonth)) {
                            log.error("==append header error:" + startMonth + "," + endMonth);
                        }

                        FileUtils.writeLines(outFile, "UTF-8", extractList, true);
                        extractList = new ArrayList<>();
                        relId = relList.get(relList.size() - 1).getId();
                    }

                    UserEntity user = userDAO.getUser(adhocRankProject.getCreateUserId());
                    sendEmail(user.getName(), user.getEmail(), localFilePath, fileName, ownDomainId, projectName);

                    autoAdhocRankProjectEntityDAO.updateExportSVStatus(projectId, AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_COMPLETED_WITHOUT_ERROR);

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    autoAdhocRankProjectEntityDAO.updateExportSVStatus(projectId, AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_ERROR);
                    continue;
                }

            }

            if(CollectionUtils.isNotEmpty(emailList)){
                sendExtractReport();
            }

        } else {
            log.info("==no sv project need to extract.");
        }


    }

    public void addHeadersForExactFile(File outFile, String startMonth, String endMonth) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Average Search Volume").append(SPLIT);

        List<LocalDate> dateList = getMonthList(startMonth, endMonth);
        Collections.sort(dateList);
        Collections.reverse(dateList);
        for (LocalDate localDate : dateList) {
            header.append(localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"))).append(SPLIT);
        }
        header.append("Competition").append(SPLIT);
        header.append("Cpc");
        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public String writeSvLine(AutoAdhocRankKeywordSVEntity autoAdhocRankKeywordSVEntity) {

        StringBuffer line = new StringBuffer();
        line.append(autoAdhocRankKeywordSVEntity.getSvUpdateDate()).append(SPLIT);
        line.append(autoAdhocRankKeywordSVEntity.getKeywordName()).append(SPLIT);
        line.append(autoAdhocRankKeywordSVEntity.getAvgSV() == null ? "-" : autoAdhocRankKeywordSVEntity.getAvgSV()).append(SPLIT);

        line.append(getMonthSv(autoAdhocRankKeywordSVEntity));

        Double competition = autoAdhocRankKeywordSVEntity.getCompetition() == null ? null : autoAdhocRankKeywordSVEntity.getCompetition();
        if (competition == null) {
            line.append("-").append(SPLIT);
        } else {
            BigDecimal bigDecimal = new BigDecimal(competition);
            line.append(bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()).append(SPLIT);
        }

        Double cpc = autoAdhocRankKeywordSVEntity.getCpc() == null ? null : autoAdhocRankKeywordSVEntity.getCpc();
        if (cpc == null) {
            line.append("-");
        } else {
            BigDecimal bigDecimal = new BigDecimal(cpc);
            line.append(bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        }


        return line.toString();
    }

    private List<LocalDate> getMonthList(String startMonth, String endMonth) {

        LocalDate startLocalDate = LocalDate.of(NumberUtils.toInt(startMonth.substring(0, 4)), NumberUtils.toInt(startMonth.substring(4, 6)), 1);
        LocalDate endLocalDate = LocalDate.of(NumberUtils.toInt(endMonth.substring(0, 4)), NumberUtils.toInt(endMonth.substring(4, 6)), 1);

        List<LocalDate> dateList = new LinkedList<>();
        while (startLocalDate.compareTo(endLocalDate) <= 0) {
            dateList.add(startLocalDate);
            startLocalDate = startLocalDate.plusMonths(1);
        }

        return dateList;
    }

    private String getMonthSv(AutoAdhocRankKeywordSVEntity autoAdhocRankKeywordSVEntity) {

        StringBuffer line = new StringBuffer();
        if(autoAdhocRankKeywordSVEntity.getStartMonth() == null
                || (autoAdhocRankKeywordSVEntity.getSearchEngineId() == 255
                && autoAdhocRankKeywordSVEntity.getMonthlySV1() == null && autoAdhocRankKeywordSVEntity.getMonthlySV2() == null)){// no sv result
            for(int i=0;i<24;i++){
                line.append("-").append(SPLIT);
            }
        }else {
            String startMonth = autoAdhocRankKeywordSVEntity.getStartMonth().toString();
            String endMonth = autoAdhocRankKeywordSVEntity.getEndMonth().toString();

            List<LocalDate> dateList = getMonthList(startMonth, endMonth);
            LocalDate startLocalDate = dateList.get(0);
            LocalDate endLocalDate = dateList.get(dateList.size() - 1);

            if (CollectionUtils.isEmpty(autoAdhocRankKeywordSVEntity.getSvList())) {
                log.error("=====error getSvList null!" + autoAdhocRankKeywordSVEntity.getProjectId());
                String errorMsg = "Extract sv error SvList null projectId, " + autoAdhocRankKeywordSVEntity.getProjectId();
                emailList.add(errorMsg);
                return null;
            }

            autoAdhocRankKeywordSVEntity.getSvList().stream().forEach(var -> {
                line.append(var).append(SPLIT);
            });
        }

        return line.toString();
    }

    private void sendEmail(String userName, String emailAddress, String fileLoc, String filename, int ownDomainId, String projectName) {

        String emailTo = emailAddress;
//        emailTo = "<EMAIL>";
        System.out.println("=========Send to : " + emailTo + " start!");

        String subject = "On-Demand Data Retrieval Download Ready - " + projectName;
        String info = "The raw data for On-Demand retrieval project - " + projectName + " is now ready for download.";

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", userName);
        reportMap.put("info", info);

        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(ownDomainId);

        int sizeMb = 0;
        File file = new File(fileLoc + filename);
        if (file.exists()) {
            long fileSize = file.length();
            sizeMb = (int) (fileSize / 1024 / 1024);
            System.out.println(" OID:" + ownDomainId + " filename: " + (fileLoc + filename) +
                    " size:" + fileSize + " MB:" + sizeMb);
        }else {
            System.out.println("=========file not exist , skip:" + projectName + "," + file.getAbsolutePath());
            return;
        }

        FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
        String host = ftpServerInfo.getPrivateHost();
        String ftpUsername = ftpServerInfo.getServerUserName();
        String ftpPassword = ftpServerInfo.getServerPassword();
//        FTPUtils.saveFileToFTPForAdhoc(host, ftpUsername, ftpPassword, ownDomainId, (fileLoc + filename), false);
        //https://www.wrike.com/open.htm?id=1066001811 move to seagate
        String linkUrl = "";
        try {
            boolean isSaved = SeagateUtils.saveFileToDefaultSeagate(ownDomainId, (fileLoc + filename));
            if(!isSaved){
                log.error("===send to Seagate Failed!projectName:" + projectName);
            }
            linkUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, filename);
            log.info("==isSaved:" + isSaved + ",linkUrl:" + linkUrl);
        }catch (Exception e){
            e.printStackTrace();
            log.error("===send to Seagate Failed!projectName:" + projectName);
        }

        //https://www.wrike.com/open.htm?id=960781633
        FTPUtils.saveFileToFTP(ownDomainId, fileLoc + filename, LOC + ownDomainId);
        String linkText = null;
        try {
            String name = URLEncoder.encode(filename, "utf-8");
//            linkText = "https://downloads.seoclarity.net/adhocSvExtract/" + StringUtils.replace(name, "+", "%20");
            linkText = linkUrl;
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
//            linkText = "https://downloads.seoclarity.net/adhocSvExtract/" + filename;
            linkText = linkUrl;
        }

        System.out.println("  fileLink:" + linkText);
        reportMap.put("fileLink", linkText);
//        String[] bccTo = new String[]{};
        String[] bccTo = new String[]{Constants.DEV_TEAM_EMAIL};
//        String[] bccTo = new String[]{"<EMAIL>", "<EMAIL>"};//test
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, subject,
                "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html",
                reportMap, agencyInfo, ZeptoMailSenderComponent.FUNCTION_TYPE_ALERTS, null, null);

        System.out.println("=========Send to : " + emailAddress + " success!");
    }

    private void sendMailReport() {

        StringBuffer sb = new StringBuffer();
        for (String msg : emailList) {
            sb.append("<BR>").append(msg);
        }

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", sb.toString());
        String emailTo = "<EMAIL>";
        String subject = "Adhoc RetrieveSv Error";
        String[] ccTo = new String[]{"<EMAIL>"};
//        String[] ccTo = null;
//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt",
//                "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject,
                "mail_resource_operate_report.txt", "mail_resource_operate_report.html",
                reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private void sendExtractReport() {

        StringBuffer sb = new StringBuffer();
        for (String msg : emailList) {
            sb.append("<BR>").append(msg);
        }

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", sb.toString());
        String emailTo = "<EMAIL>";
        String subject = "Adhoc Extract RetrieveSv Error";
        String[] ccTo = new String[]{"<EMAIL>"};
//        String[] ccTo = null;

//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject,
                "mail_resource_operate_report.txt", "mail_resource_operate_report.html",
                reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private String copyFileFromFTP(String ftpFullPathFilename, boolean needCopyFromFTP) {
        System.out.println(" ==FtpFileName:" + ftpFullPathFilename + " copyFromFTP:" + needCopyFromFTP);
        try {
            if (StringUtils.isBlank(ftpFullPathFilename)) {
                return null;
            }
            String[] filePathArray = ftpFullPathFilename.split("/");
            String fileName = filePathArray[filePathArray.length - 1];

            File copyFolder = new File(COPY_FILE_PATH);
            if (!copyFolder.exists() || !copyFolder.isDirectory()) {
                copyFolder.mkdirs();
            }

            String fullPathLocalFileName = COPY_FILE_PATH + fileName;
            if (needCopyFromFTP) {
                System.out.println(" ===StartCopyFromFTP:" + fullPathLocalFileName);
                //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/500505448
                FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
                String host = ftpServerInfo.getPrivateHost();
                String ftpUsername = ftpServerInfo.getServerUserName();
                String ftpPassword = ftpServerInfo.getServerPassword();
                if (copyBySSH(host, ftpUsername, ftpPassword, ftpFullPathFilename, COPY_FILE_PATH)) {
                    return fullPathLocalFileName;
                } else {
                    return null;
                }
            } else {
                return fullPathLocalFileName;
            }
        } catch (Exception exp) {
            exp.printStackTrace();
            return null;
        }
    }

    private String downloadFileFromSeagate(int projectId, int domainId, String ftpFullPathFilename, boolean needCopyFromFTP){
        System.out.println(" ==SeagateFileUrl:" + ftpFullPathFilename);
        try {
            if (StringUtils.isBlank(ftpFullPathFilename)) {
                return null;
            }

            if(!needCopyFromFTP){
                return COPY_FILE_PATH + projectId + "_" + domainId + ".txt";
            }

            File copyFolder = new File(COPY_FILE_PATH);
            if (!copyFolder.exists() || !copyFolder.isDirectory()) {
                copyFolder.mkdir();
            }

            String fullPathLocalFileName = COPY_FILE_PATH + projectId + "_" + domainId + ".txt";
            SeagateUtils.downloadFile(ftpFullPathFilename, fullPathLocalFileName);
            System.out.println(" ==localFilePath:" + fullPathLocalFileName);
            return fullPathLocalFileName;
        } catch (Exception exp) {
            exp.printStackTrace();
            return null;
        }
    }

    private String copyFileWithDifferentType(int projectId, int domainId, String ftpFullPathFilename, boolean needCopyFromFTP) throws Exception{
        if(StringUtils.startsWith(ftpFullPathFilename, "seagate:")){//download from seagate
            System.out.println("========copyFileBySeagate:" + projectId);
            String downloadUrl = SeagateUtils.getDownloadUrlFromUIPath(ftpFullPathFilename);
            return downloadFileFromSeagate(projectId, domainId, downloadUrl, needCopyFromFTP);
        } else if (StringUtils.startsWith(ftpFullPathFilename, "/home/<USER>/public_html")) {//download from ftp
            System.out.println("========copyFileByFTP:" + projectId);
            return copyFileFromFTP(ftpFullPathFilename, needCopyFromFTP);
        } else {
            System.out.println("=====not catch type, need check, use ftp as default");
            return copyFileFromFTP(ftpFullPathFilename, needCopyFromFTP);
        }
    }

    private static boolean copyBySSH(String host, String userName, String pw, String from, String saveTo) {
        System.out.println("copy from remote to local , file name:" + from);
        for (int i = 0; i < SSH_TRY_COUNT; i++) {
            try {
                ch.ethz.ssh2.Connection connection = new Connection(host);
                connection.connect();
                if (connection.authenticateWithPassword(userName, pw)) {
                    System.out.println(" login successfully.");
                    SCPClient scpClient = connection.createSCPClient();
                    scpClient.get(from, saveTo);
                    connection.close();
                    System.out.println(" Copy " + from + " to local successfully.");
                    return true;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            System.out.println(" Failed to login to target host...");
            try {
                Thread.sleep(300000);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return false;
    }

    private void sendErrorMail(String subject, String errMessage) {
        List<String> infos = new ArrayList<String>();
        infos.add(errMessage);
        sendEmailForError(new Date(), subject, infos, null);
    }

    private void sendEmailForError(Date startTime, String subject, List<String> infos, String[] ccTo) {
        try {
            Map<String, Object> reportMap = new HashMap<String, Object>();
            reportMap.put("userName", "Mitul");
            reportMap.put("dateString", DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
            reportMap.put("startTime", DateFormatUtils.format(startTime, "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("info", infos);
            reportMap.put("title", subject);
            reportMap.put("errormessage", "");
            String emailTo = "<EMAIL>";
            if (ccTo == null) {
                ccTo = new String[]{"<EMAIL>"};
            }

//            emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_common.txt", "mail_common.html", reportMap);
            zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject,
                    "mail_common.txt", "mail_common.html",
                    reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private String getCountryCode(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            countryCode = "US";
        } else if (StringUtils.equalsIgnoreCase(countryCode, "UK")) {
            countryCode = "GB";
        }
        return countryCode;
    }

    private int sendToS3(int frequencyType, int projectId, int ownDomainId, String filePath) throws Exception{
        try {
            System.out.println("----------sendToS3 frequencyType: " + frequencyType + ",projectId:" + projectId + ",ownDomainId:" + ownDomainId + ",filePath:" + filePath);
            String frequencyName = AutoAdhocRankProjectEntity.getFrequencyName(frequencyType);
            if (StringUtils.isBlank(frequencyName)) {
                System.out.println("error frequencyType: " + frequencyType);
                return RESULT_ERROR;
            }

            System.out.println("----------filePath : " + filePath);
            String[] filePathArray = filePath.split("/");
            String fileName = filePathArray[filePathArray.length - 1];
            String prefix = fileName.substring(0, fileName.lastIndexOf("."));
            String suffix = fileName.substring(fileName.lastIndexOf("."), fileName.length());
            String sendFileName = prefix + "_" + ownDomainId + "_" + projectId + "_" + FormatUtils.formatDateToYyyyMmDd(new Date()) + suffix;
            File sendFile = new File(filePath);

//            AWSCredentials credentials = new BasicAWSCredentials("********************", "nV9bfcSTl1yINTVux7ltv5Wj29eAS6XEBMAGIAxG");
//            AmazonS3 s3client = new AmazonS3Client(credentials);

            ClientConfiguration clientConfig = new ClientConfiguration();
            clientConfig.setConnectionTimeout(1000 * 60 * 5);
            clientConfig.setMaxErrorRetry(5);

            AmazonS3 s3client = S3Utils.getAmazonS3WithSqsUserKey();
            String keyPath = frequencyName + "/" + sendFileName;

            s3client.putObject(new PutObjectRequest(UPLOAD_FILE_S3_BUCHET_NAME, keyPath, sendFile).withStorageClass(StorageClass.ReducedRedundancy));
            System.out.println("S3 eekey: " + keyPath + " , file: " + sendFile.getName());

            autoAdhocRankProjectEntityDAO.updateS3FullPathFilename(projectId, UPLOAD_FILE_S3_BUCHET_NAME + "/" + keyPath);

            return RESULT_OK;
        } catch (AmazonServiceException ase) {
            System.out.println("Caught an AmazonServiceException, which means your request made it to Amazon S3, but was rejected with an error response for some reason.");
            System.out.println(" Error Message:    " + ase.getMessage());
            System.out.println(" HTTP Status Code: " + ase.getStatusCode());
            System.out.println(" AWS Error Code:   " + ase.getErrorCode());
            System.out.println(" Error Type:       " + ase.getErrorType());
            System.out.println(" Request ID:       " + ase.getRequestId());
            return RESULT_ERROR;
        } catch (AmazonClientException ace) {
            System.out.println("Caught an AmazonClientException, which " + "means the client encountered "
                    + "an internal error while trying to " + "communicate with S3,such as not being able to access the network.");
            System.out.println("Error Message: " + ace.getMessage());
            return RESULT_ERROR;
        }
    }

    private boolean processKeywords(AutoAdhocRankProjectEntity adhocRankProject, String fullPathLocalFileName) throws Exception {
        boolean succeeded = true;

        Integer keywordType = adhocRankProject.getKeywordType();
        Integer geoPattern = adhocRankProject.getGeoPattern();

        Integer projectId = adhocRankProject.getId();
        Integer engineId = adhocRankProject.getSearchEngineId();
        Integer languageId = adhocRankProject.getLanguageId();
        Integer cityId = adhocRankProject.getCityId();
        Integer criteriaId = adhocRankProject.getCriteriaId();
        Integer seedKeywordType = adhocRankProject.getSeedKeywordType() == null ? AutoAdhocRankProjectEntity.SEED_KEYWORD_TYPE_FILE : adhocRankProject.getSeedKeywordType();
        String country = adhocRankProject.getCountry();

        Boolean isMultiCities = false;

        if (keywordType == AutoAdhocRankProjectEntity.KEYWORD_TYPE_GEO && geoPattern == AutoAdhocRankProjectEntity.GEO_PATTERN_BOTH_GEO_ID_AND_KEYWORD_IN_FILE) {
            isMultiCities = true;
        }

        List<String> keywordList = new ArrayList<>();
        int keywordCountInFile = 0;
        try {

            if (seedKeywordType == AutoAdhocRankProjectEntity.SEED_KEYWORD_TYPE_FILE && StringUtils.isNotBlank(fullPathLocalFileName)) {

                File file = new File(fullPathLocalFileName);
                InputStreamReader inputReader = new InputStreamReader(new FileInputStream(file));
                BufferedReader bf = new BufferedReader(inputReader);
                String str;

                while ((str = bf.readLine()) != null) {
                    if (StringUtils.isBlank(str)) {
                        System.out.println("==SkipEmptyLine");
                        continue;
                    }

//                String encodeKeyword = URLEncoder.encode(str, "UTF-8"); // TODO
//                keywordList.add(encodeKeyword);
//                    String keywordName = formatKeyword(str);
                    if (StringUtils.isNotBlank(str)) {
                        keywordList.add(str);
                    }

                    keywordCountInFile++;

                }
                bf.close();
                inputReader.close();
            }

            if (!org.springframework.util.CollectionUtils.isEmpty(keywordList)) {
                // checkKeywords(adhocRankProject, keywordList);
                System.out.println("===saveAutoAdhocKW isMultiCities:" + isMultiCities);
                succeeded = saveAutoAdhocKeyword(projectId, engineId, languageId, keywordList, cityId, isMultiCities, criteriaId, country);
                if (succeeded) {
                    try {
                        int uniqueKeywordCount = autoAdhocRankKeywordSVEntityDAO.getKeywordCount(projectId, false);
                        autoAdhocRankProjectEntityDAO.updateKeywordCount(projectId, keywordCountInFile, uniqueKeywordCount);
                        System.out.println(" ====UpdKeywordCount projectId:" + projectId + " KWsInFile:" + keywordCountInFile + " uniqueKWCnt:" + uniqueKeywordCount);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
                System.out.println(" ====saveAutoAdhocKW projectId:" + projectId + " succeeded:" + succeeded);
            }
        } catch (Exception exp) {
            exp.printStackTrace();
            System.out.println(" ====processKeywordsError projectId:" + projectId);
            succeeded = false;
        }

        return succeeded;
    }

    private boolean saveAutoAdhocKeyword(int projectId, int searchEngineId, int languageId, List<String> kwList,
                                         int cityId, Boolean isMultiCities, Integer criteriaId, String country) {
        boolean saved = true;
        try {
            List<Integer> notFindGeoIdList = new ArrayList<Integer>();
            List<String> outLines = new ArrayList<String>();
            List<AutoAdhocRankKeywordSVEntity> adhocKwSvList = new ArrayList<AutoAdhocRankKeywordSVEntity>();
            int rejectedHashIdx = 0;
            for (String keywordName : kwList) {
                String line = keywordName;

                boolean isReject = false;
                if (isMultiCities) {
                    try {
                        String[] lineArr = keywordName.split(SPLIT_FIELD);
                        if (lineArr.length != 2) {
                            cityId = 0;
                            isReject = true;
                        } else if (StringUtils.isBlank(lineArr[0]) || StringUtils.isBlank(lineArr[1])) {
                            cityId = 0;
                            isReject = true;
                        } else {
                            cityId = Integer.parseInt(lineArr[0]);
                            keywordName = lineArr[1];
                            GeoMasterEntity geoMasterEntity = geoService.getGeoMasterEntityById(cityId);
                            if (geoMasterEntity == null) {
                                notFindGeoIdList.add(cityId);
                                isReject = true;
                            }
                        }
                    } catch (Exception e) {
                        isReject = true;
                        cityId = 0;
                    }
                }

                //for geo kw get criteriaId
                if(cityId > 0){
                    String fileCountryCode = geoService.getCountryCode(cityId);
                    if (!country.equalsIgnoreCase(fileCountryCode)) {
                        System.out.println("===city not in country cityId:" + cityId + ",country:" + country);
                        isReject = true;
                    } else {
                        criteriaId = geoService.getAdwordsCriteriaId(cityId);
                        if (criteriaId == null) {
                            notFindGeoIdList.add(cityId);
                            isReject = true;
                            System.out.println("===not CriteriaId:" + cityId);
                        }
                    }
                }

                keywordName = formatKeyword(keywordName);
                if (StringUtils.isBlank(keywordName)) {
                    continue;
                }

                keywordName = keywordName.toLowerCase().replaceAll("\\u00A0", " ");//remove nbsp to space
                keywordName = CleanUpUtil.removeUTF8BOM(keywordName);//https://www.wrike.com/open.htm?id=1638584184

                //check keyword
                if (keywordName.startsWith("\"") && keywordName.endsWith("\"") && keywordName.length() > 2) {
                    keywordName = keywordName.substring(1);
                    keywordName = keywordName.substring(0, keywordName.length() - 1);
                    System.out.println("===removeStartEndDoubleQuote:" + keywordName);
                }

                String keywordHash = CityHashUtil.getUrlHashForString(keywordName);

                AutoAdhocRankKeywordSVEntity keywordSVEntity = new AutoAdhocRankKeywordSVEntity();
                keywordSVEntity.setProjectId(projectId);
                keywordSVEntity.setSearchEngineId(searchEngineId);
                keywordSVEntity.setLanguageId(languageId);
                keywordSVEntity.setKeywordName(keywordName);
                keywordSVEntity.setClarityDBKeywordHash(String.valueOf(rejectedHashIdx++));
                keywordSVEntity.setCityId(cityId);
                keywordSVEntity.setClarityDBKeywordHash(keywordHash);
                keywordSVEntity.setCriteriaId(criteriaId);

                if (isReject || FormatUtils.containsNonStandardSymbols(keywordName) || FormatUtils.startWithGoogleAdvOperators(keywordName)
                        || FormatUtils.containIllegalCharacter(keywordName) || keywordName.startsWith("+") || keywordName.startsWith("-")
                        || keywordName.equals("\"")) {//https://www.wrike.com/open.htm?id=524888503
                    System.out.println(" ==KWContainSpecialCharacters:" + keywordName);
                    keywordSVEntity.setIsRejected(AutoAdhocRankKeywordSVEntity.IS_REJECTED_YES);
                    rejectKeywordList.add(line);
                } else {
                    keywordSVEntity.setIsRejected(AutoAdhocRankKeywordSVEntity.IS_REJECTED_NO);
                }
                adhocKwSvList.add(keywordSVEntity);

                if (adhocKwSvList.size() >= 200) {
                    autoAdhocRankKeywordSVEntityDAO.bulkInsert(adhocKwSvList);
                    adhocKwSvList = new ArrayList<AutoAdhocRankKeywordSVEntity>();
                }
            }

            if (CollectionUtils.isNotEmpty(adhocKwSvList)) {
                autoAdhocRankKeywordSVEntityDAO.bulkInsert(adhocKwSvList);
            }

            System.out.println("===Project:" + projectId + " kwList:" + kwList.size() + " cntForHash:" + outLines.size() + " rejectedCnt:" + rejectedHashIdx
                    + ",notFindGeoIdList:" + notFindGeoIdList.size());

            if (!org.springframework.util.CollectionUtils.isEmpty(notFindGeoIdList)) {
                String errorMsg = "projectId:" + projectId + ",geoIds:";
                for (Integer geoId : notFindGeoIdList) {
                    errorMsg += geoId + ",";
                }
                sendErrorMail("Not find geoId for adhoc", errorMsg);
            }

        } catch (Exception exp) {
            exp.printStackTrace();
            saved = false;
        }

        return saved;
    }

    private String formatKeyword(String keyword) {

        // https://www.wrike.com/open.htm?id=484247851
        if (CommonDataService.containsEmoji(keyword)) {
            String newStr = CommonDataService.filterEmoji(keyword);
            System.out.println(" ##containsEmoji:\"" + keyword + "\" len:" + keyword.length() + " new:\"" + newStr + "\" len:" + newStr.length() +
                    " changed:" + (keyword.length() > newStr.length()));
            keyword = newStr;
        }
        // Change to save keywords into auto_adhoc_rank_keyword_sv.keywordName with un-encoded format
        String keywordName = CommonDataService.formatKeywordForInsert(keyword);
        if (StringUtils.isNotEmpty(keywordName)) {
            if (keywordName.length() > 254) {
                System.out.println(" ##SkipTooLongKW:" + keyword);
                return null;
            }
        } else {
            System.out.println("==SkipEmptyKW:" + keyword + "-->" + keywordName);
            return null;
        }
        return keywordName;
    }

    private void sendCreateEmail(int projectId, boolean isCanceled) {

        try {
            AutoAdhocRankProjectEntity adhocRankProject = autoAdhocRankProjectEntityDAO.getProjectById(projectId);
            int frequencyType = adhocRankProject.getFrequencyType();
            int retrieveType = adhocRankProject.getRetrieveType();
            int keywordType = adhocRankProject.getKeywordType();
            int geoPattern = adhocRankProject.getGeoPattern();
            int seedKeywordType = adhocRankProject.getSeedKeywordType();

            String frequencyTypeName = "";
            if (frequencyType == AutoAdhocRankProjectEntity.FREQUENCY_TYPE_HOURLY) {
                frequencyTypeName = "Hourly";
            } else if (frequencyType == AutoAdhocRankProjectEntity.FREQUENCY_TYPE_DAILY) {
                frequencyTypeName = "Daily";
            }

            String retrieveTypeName = "";
            if (retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_TYPE_RANKCHECK_ONLY) {
                retrieveTypeName = "Rank";
            } else if (AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV.contains(retrieveType)) {
                retrieveTypeName = "Retrieve SV";
            }

            String keywordTypeName = "";
            if (keywordType == AutoAdhocRankProjectEntity.KEYWORD_TYPE_NATIONAL) {
                keywordTypeName = "National";
            } else if (keywordType == AutoAdhocRankProjectEntity.KEYWORD_TYPE_GEO) {
                keywordTypeName = "Geo";
            }

            String geoPatternName = "";
            if (geoPattern == AutoAdhocRankProjectEntity.GEO_PATTERN_ONLY_KEYWORD_IN_FILE) {
                geoPatternName = "Single Geo";
            } else if (geoPattern == AutoAdhocRankProjectEntity.GEO_PATTERN_BOTH_GEO_ID_AND_KEYWORD_IN_FILE) {
                geoPatternName = "Multi-Geo";
            }

            String seedKeywordTypeName = "";
            if (seedKeywordType == AutoAdhocRankProjectEntity.SEED_KEYWORD_TYPE_FILE) {
                seedKeywordTypeName = "File";
            } else if (seedKeywordType == AutoAdhocRankProjectEntity.SEED_KEYWORD_TYPE_SAVED_LIST) {
                seedKeywordTypeName = "Saved List";
            }

            Map<String, Object> reportMap = new HashMap<String, Object>();
            StringBuffer sb = new StringBuffer();
            String title = "New AdHoc rank project created";
            if (isCanceled) {
                title = "New AdHoc rank project cancelled";
            }

            UserEntity userEntity = userDAO.getUser(adhocRankProject.getCreateUserId());

            sb.append(title).append(": ");
            sb.append("<BR>Project Name: ").append(adhocRankProject.getProjectName());
            sb.append("<BR>Domain: ").append(adhocRankProject.getOwnDomainId());
            sb.append("<BR>Create User: ").append(userEntity.getEmail());
            sb.append("<BR>Schedule : ").append(frequencyTypeName);
            sb.append("<BR>Type: ").append(retrieveTypeName);
            sb.append("<BR>National/Geo: ").append(keywordTypeName);
            if (keywordType == AutoAdhocRankProjectEntity.KEYWORD_TYPE_GEO) {
                sb.append("<BR>Geo: ").append(geoPatternName);
            }
            sb.append("<BR>Source: ").append(seedKeywordTypeName);
            sb.append("<BR>Count of unique keywords: ").append(adhocRankProject.getKeywordCountInFile());
            Integer kwCnt = autoAdhocRankKeywordSVEntityDAO.getKeywordCount(projectId, false);
            sb.append(", processing keywords: ").append(kwCnt == null ? 0 : kwCnt);
            List<String> reKwList = autoAdhocRankKeywordSVEntityDAO.getRejectKeywordList(projectId);
            sb.append(", rejected keywords: ").append(reKwList.size());

            reportMap.put("info", sb.toString());
            String subject = title;
            reportMap.put("userName", "");
            reportMap.put("title", title);
            reportMap.put("errormessage", "");

            String emailTo[] = new String[]{"<EMAIL>"};
//            String[] ccTo = new String[] {};
            String[] ccTo = new String[]{Constants.DEV_TEAM_EMAIL, "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};

            AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(4);
//            emailSenderComponent.sendMimeMultiPartMail(emailTo, ccTo, subject, "mail_adhoc_report.txt", "mail_adhoc_report.html", reportMap, agencyInfo);

            String rejectFileName = null;
            File rejectFile = null;
            if (!org.springframework.util.CollectionUtils.isEmpty(rejectKeywordList)) {
                rejectFile = new File("rejectKw.txt");
                FileUtils.writeLines(rejectFile, rejectKeywordList, true);
            }
            if (rejectFile != null) {
                rejectFileName = rejectFile.getName();
            }
//            emailSenderComponent.sendMimeMultiPartMailAndBccAttachment(emailTo, ccTo, subject,
//                    "mail_adhoc_create_report.txt", "mail_adhoc_create_report.html",
//                    reportMap, rejectFileName, rejectFile, agencyInfo);

            File[] files = new File[]{ rejectFile };
            String[] attachmentNames = new String[]{rejectFile.getName() };
            zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject,
                    "mail_adhoc_create_report.txt", "mail_adhoc_create_report.html",
                    reportMap, agencyInfo, ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, attachmentNames, files);
            System.out.println("===sendEmailSuccess:" + projectId);

            if (rejectFile != null && rejectFile.exists()) {
                rejectFile.delete();
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private String checkKeywordByRetrieveType(int retrieveType, String keyword){
        if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_GOOGLE_SV_ONLY){
            return FormatUtils.checkKeyword(keyword);
        }else if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_BING_SV_ONLY){
            if(keyword.length() > 100){
                System.out.println("===skip too long kName:" + keyword);
                return null;
            }else {
                return keyword;
            }
        }else {
            return null;
        }
    }

}
