package seoclarity.backend.summary;

import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.poi.ss.formula.functions.T;

import com.google.gson.Gson;

import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360Lweb05DAO;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.Clarity360FakeDataUploadCustomData" -Dexec.args=""
public class Clarity360FakeDataUploadCustomData {
	
	private Clarity360Lweb05DAO clarity360Lweb05DAO;

	public Clarity360FakeDataUploadCustomData() {
		clarity360Lweb05DAO = SpringBeanFactory.getBean("clarity360Lweb05DAO");
	}
	
	public static List<String> resultList = new ArrayList<String>();
	
	static {
		
		resultList.add("/home/<USER>/document/split_fileaa");
		resultList.add("/home/<USER>/document/split_fileab");
		resultList.add("/home/<USER>/document/split_fileac");
		resultList.add("/home/<USER>/document/split_filead");
		resultList.add("/home/<USER>/document/split_fileae");
		resultList.add("/home/<USER>/document/split_fileaf");
		resultList.add("/home/<USER>/document/split_fileag");
		resultList.add("/home/<USER>/document/split_fileah");
		resultList.add("/home/<USER>/document/split_fileai");
		resultList.add("/home/<USER>/document/split_fileaj");
		resultList.add("/home/<USER>/document/split_fileak");
		resultList.add("/home/<USER>/document/split_fileal");
		resultList.add("/home/<USER>/document/split_fileam");
		resultList.add("/home/<USER>/document/split_filean");
		resultList.add("/home/<USER>/document/split_fileao");
		resultList.add("/home/<USER>/document/split_fileap");
		resultList.add("/home/<USER>/document/split_fileaq");
		resultList.add("/home/<USER>/document/split_filear");
		resultList.add("/home/<USER>/document/split_fileas");
		resultList.add("/home/<USER>/document/split_fileat");
		
	}

	public static void main(String[] args) throws IOException {
		
		Clarity360FakeDataUploadCustomData clarity360FakeDataUpload = new  Clarity360FakeDataUploadCustomData();
		for(String filePath :resultList) {
			
			clarity360FakeDataUpload.process(filePath);
		}
	}
	
	
	public static final String[] fileHeader = new String[] {
			
			"id",
			"url",
			"word_count",
			"selector_type",
			"match_found",
			"index",
			"selector",
			"links",
			"content",
			"original_url",
			
	 };
	 
	private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',').withRecordSeparator('\n')
				.withHeader(fileHeader);
		
	
	private static Integer maxInsertCount = 50000;
	private void process(String filePath) throws IOException {
		System.out.println("filePath:" + filePath);
		try {
			FileReader fr = new FileReader(filePath);
			CSVParser csvParser = new CSVParser(fr, csvFormat);
			
			Integer totalSize = 0;
			List<Clarity360Entity> clarityEntities = new ArrayList<>();
			// don't read header
			
			List<CSVRecord> csvRecords = csvParser.getRecords();
			System.out.println("csvRecords.size():" + csvRecords.size());
			for (int i = 1; i < csvRecords.size(); i++) {
				
				CSVRecord csvRecord = csvRecords.get(i);
				Clarity360Entity clarity360Entity;
				try {
					clarity360Entity = getClarityEntity(csvRecord);
					
					if (clarity360Entity == null) {
						continue;
					}
				} catch (Exception e) {
					System.out.println("line i : " + i);
					e.printStackTrace();
					continue;
				}
				
				clarityEntities.add(clarity360Entity);
				if (clarityEntities.size() >= maxInsertCount) {
					clarity360Lweb05DAO.insertCustomDataForBatch(clarityEntities);
					System.out.println("finish insert for top : " + maxInsertCount);
					clarityEntities.clear();
				}
			}
			if (CollectionUtils.isNotEmpty(clarityEntities)) {
				clarity360Lweb05DAO.insertCustomDataForBatch(clarityEntities);
				System.out.println("finish insert for left count :" + clarityEntities.size());
				totalSize = clarityEntities.size();
				clarityEntities.clear();
			}

			System.out.println("totalSize:" + totalSize);
			csvParser.close();
			fr.close();
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
		
//		String str = null;
//		String[] tempArray = null;
//		while ((str = br.readLine()) != null) {
//			
//			if(StringUtils.startsWith(str, "url	alt_img_detail")){
//				System.out.println("===skip header!");
//				continue;
//			}
//			
//			tempArray = StringUtils.split(str, "\t");
//			
//			if(tempArray == null || tempArray.length < 323) {
//				System.out.println("====line can not be parsed! " + str);
//				continue;
//			}
//			
//			
//			
//		}
	}
	
	private Gson gson = new Gson();
	
	/**
	 * "id",
		"url",
		"word_count",
		"selector_type",
		"match_found",
		"index",
		"selector",
		"links",
		"content",
		"original_url",
		
		[0,0,0,0,0,6,1,1]
		['XPATH','XPATH','XPATH','XPATH','XPATH','XPATH','XPATH','XPATH']
		[0,0,0,0,0,1,1,1]
		[1,2,3,4,5,6,7,8]
		['//div[@id=\'cars-1\']//div[@class=\'uitk-card uitk-card-roundcorner-all uitk-card-has-border uitk-card-has-link uitk-card-has-overflow uitk-spacing uitk-spacing-padding-three uitk-card-has-primary-theme\']','//div[@class=\'uitk-layout-flex uitk-layout-flex-align-items-center uitk-card uitk-card-roundcorner-all uitk-card-has-border uitk-card-has-primary-theme\']','//div[@class=\'uitk-card uitk-card-roundcorner-all uitk-card-has-link uitk-card-has-primary-theme\']','//div[@class=\'uitk-layout-grid uitk-layout-grid-has-auto-columns uitk-layout-grid-has-columns uitk-layout-grid-display-grid uitk-card uitk-card-roundcorner-all uitk-card-padded uitk-spacing uitk-spacing-margin-blockstart-three uitk-card-has-primary-theme\']','//div[@id=\'activities-by-category-1\']//div[@class=\'uitk-carousel-item\']','//meta[@name=\'flex:template-id\']/@content','//meta[@name=\'flex:template-fm-id\']/@content','//meta[@name=\'flex:template-version\']/@content']
		['[]','[]','[]','[]','[]','[]','[]','[]']
		['[]','[]','[]','[]','[]','["70125-en_AU-WTF-Hotel-Information-Blossom"]','["42301"]','["8"]']
	 * @param csvRecord
	 * @return
	 * @throws ParseException
	 */
	
	private Clarity360Entity getClarityEntity(CSVRecord csvRecord) throws ParseException {
		Clarity360Entity clarity360Entity = new Clarity360Entity();

		clarity360Entity.setUrl(csvRecord.get("url")); 
		Integer[] indexArray = new Integer[] {1, 2, 3};
		String[] xpathArray = new String[] {"XPATH", "XPATH", "XPATH"};
		String[] linkArray = new String[] {"[]", "[]", "[]"};
		
		Integer[] wordCountArray = gson.fromJson(csvRecord.get("word_count"), Integer[].class);
		
		if (wordCountArray != null && wordCountArray.length >= 8) {
			try{
				
				clarity360Entity.setCustom_data_word_count(removeFirst5IntegerContent(wordCountArray));
				clarity360Entity.setCustom_data_selector_type(xpathArray);
				
				Integer[] matchFound = gson.fromJson(csvRecord.get("match_found"), Integer[].class);
				clarity360Entity.setCustom_data_match_found(removeFirst5IntegerContent(matchFound));
				
				clarity360Entity.setCustom_data_index(indexArray);
				
				String[] selectorArray =  gson.fromJson(csvRecord.get("selector"), String[].class);
				clarity360Entity.setCustom_data_selector(removeFirst5StringContent(selectorArray));
				
				clarity360Entity.setCustom_data_links(linkArray);
				
				String[] contentArray =  gson.fromJson(csvRecord.get("content"), String[].class);
				clarity360Entity.setCustom_data_content(removeSpeContent(removeFirst5StringContent(contentArray), "(?i)wotif"));
				
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		} else {
			
			return null;
		}
		
		return clarity360Entity;
	}
	
	private Integer[] removeFirst5IntegerContent(Integer[] inputArray) {
		
		inputArray = ArrayUtils.removeAll(inputArray, 0, 1, 2, 3, 4);
		
		return inputArray;
		
	}
	
	private String[] removeFirst5StringContent(String[] inputArray) {
		
		inputArray = ArrayUtils.removeAll(inputArray, 0, 1, 2, 3, 4);
		
		return inputArray;
		
	}
	
	
	private String[] removeSpeContent(String[] inputArray, String removeContent) {
		for(int i = 0; i < inputArray.length; i ++) {
			
			String input = inputArray[i];
			
			inputArray[i] = input.replaceAll(removeContent, "");
			
		}
		
		return inputArray;
	}
	

}
