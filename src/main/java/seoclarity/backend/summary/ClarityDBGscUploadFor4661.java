package seoclarity.backend.summary;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;

import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * for search appearance
 * <AUTHOR>
 * @date 2018-08-27
 * seoclarity.backend.summary.ClarityDBGscDailyUploadForHistory
 *
 */
public class ClarityDBGscUploadFor4661 {

    private GscBaseDao gscBaseDao;

    private final String[] dateParser = new String[]{"yyyy-MM-dd", "yyyyMMdd"};
    private final int maxInsertCount = 100000;
    
    private static List<String[]> list = new ArrayList<>();
    
    static {
    	
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-01-01"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-01-02"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-01-03"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-01-04"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-01-05"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-01-06"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-01-07"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-01-08"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-01-09"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-01-10"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-01-11"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-01-12"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-01-13"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-01-14"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-01-15"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-01-16"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-01-17"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-01-18"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-01-19"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-01-20"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-01-21"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-01-22"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-01-23"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-01-24"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-01-25"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-01-26"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-01-27"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-01-28"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-01-29"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-01-30"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-01-31"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-02-01"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-02-02"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-02-03"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-02-04"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-02-05"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-02-06"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-02-07"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-02-08"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-02-09"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-02-10"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-02-11"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-02-12"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-02-13"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-02-14"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-02-15"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-02-16"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-02-17"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-02-18"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-02-19"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-02-20"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-02-21"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-02-22"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-02-23"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-02-24"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-02-25"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-02-26"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-02-27"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-02-28"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-03-01"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-03-02"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-03-03"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-03-04"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-03-05"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-03-06"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-03-07"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-03-08"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-03-09"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-03-10"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-03-11"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-03-12"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-03-13"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-03-14"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-03-15"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-03-16"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-03-17"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-03-18"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-03-19"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-03-20"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-03-21"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-03-22"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-03-23"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-03-24"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-03-25"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-03-26"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-03-27"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-03-28"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-03-29"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-03-30"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-03-31"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-04-01"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-04-02"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-04-03"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-04-04"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-04-05"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-04-06"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-04-07"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-04-08"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-04-09"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-04-10"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-04-11"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-04-12"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-04-13"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-04-14"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-04-15"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-04-16"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-04-17"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-04-18"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-04-19"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-04-20"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-04-21"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-04-22"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-04-23"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-04-24"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-04-25"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-04-26"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-04-27"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-04-28"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-04-29"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-04-30"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-05-01"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-05-02"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-05-03"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-05-04"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-05-05"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-05-06"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-05-07"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-05-08"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-05-09"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-05-10"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-05-11"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-05-12"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-05-13"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-05-14"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-05-15"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-05-16"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-05-17"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-05-18"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-05-19"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-05-20"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-05-21"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-05-22"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-05-23"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-05-24"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-05-25"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-05-26"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-05-27"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-05-28"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-05-29"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-05-30"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-05-31"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-06-01"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-06-02"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-06-03"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-06-04"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-06-05"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-06-06"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-06-07"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-06-08"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-06-09"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-06-10"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-06-11"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-06-12"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-06-13"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-06-14"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-06-15"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-06-16"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-06-17"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-06-18"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-06-19"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-06-20"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-06-21"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-06-22"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-06-23"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-06-24"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-06-25"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-06-26"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-06-27"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-06-28"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-06-29"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-06-30"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-07-01"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-07-02"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-07-03"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-07-04"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-07-05"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-07-06"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-07-07"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-07-08"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-07-09"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-07-10"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-07-11"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-07-12"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-07-13"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-07-14"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-07-15"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-07-16"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-07-17"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-07-18"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-07-19"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-07-20"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-07-21"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-07-22"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-07-23"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-07-24"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-07-25"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-07-26"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-07-27"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-07-28"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-07-29"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-07-30"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-07-31"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-08-01"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-08-02"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-08-03"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-08-04"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-08-05"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-08-06"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-08-07"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-08-08"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-08-09"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-08-10"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-08-11"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-08-12"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-08-13"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-08-14"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-08-15"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-08-16"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-08-17"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-08-18"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-08-19"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-08-20"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-08-21"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-08-22"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-08-23"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-08-24"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-08-25"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-08-26"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-08-27"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-08-28"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-08-29"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-08-30"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-08-31"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-09-01"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-09-02"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-09-03"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-09-04"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-09-05"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-09-06"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-09-07"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-09-08"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-09-09"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-09-10"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-09-11"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-09-12"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-09-13"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-09-14"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-09-15"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-09-16"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-09-17"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-09-18"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-09-19"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-09-20"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-09-21"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-09-22"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-09-23"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-09-24"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-09-25"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-09-26"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-09-27"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-09-28"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-09-29"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-09-30"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-10-01"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-10-02"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-10-03"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-10-04"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-10-05"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-10-06"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-10-07"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-10-08"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-10-09"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-10-10"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-10-11"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-10-12"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-10-13"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-10-14"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-10-15"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-10-16"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-10-17"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-10-18"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-10-19"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-10-20"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-10-21"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-10-22"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-10-23"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-10-24"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-10-25"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-10-26"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-10-27"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-10-28"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-10-29"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-10-30"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-10-31"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-11-01"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-11-02"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-11-03"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-11-04"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-11-05"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-11-06"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-11-07"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-11-08"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-11-09"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-11-10"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-11-11"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-11-12"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-11-13"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-11-14"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-11-15"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-11-16"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-11-17"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-11-18"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-11-19"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-11-20"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-11-21"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-11-22"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-11-23"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-11-24"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-11-25"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-11-26"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-11-27"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-11-28"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-11-29"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-11-30"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-12-01"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-12-02"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-12-03"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-12-04"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-12-05"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-12-06"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-12-07"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-12-08"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-12-09"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-12-10"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-12-11"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-12-12"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-12-13"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-12-14"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-12-15"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-12-16"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-12-17"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-12-18"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-12-19"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-12-20"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-12-21"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-12-22"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-12-23"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-12-24"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-12-25"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2019-12-26"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2019-12-27"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2019-12-28"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2019-12-29"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2019-12-30"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2019-12-31"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-01-01"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-01-02"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-01-03"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-01-04"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-01-05"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-01-06"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-01-07"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-01-08"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-01-09"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-01-10"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-01-11"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-01-12"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-01-13"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-01-14"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-01-15"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-01-16"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-01-17"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-01-18"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-01-19"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-01-20"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-01-21"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-01-22"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-01-23"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-01-24"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-01-25"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-01-26"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-01-27"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-01-28"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-01-29"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-01-30"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-01-31"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-02-01"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-02-02"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-02-03"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-02-04"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-02-05"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-02-06"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-02-07"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-02-08"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-02-09"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-02-10"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-02-11"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-02-12"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-02-13"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-02-14"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-02-15"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-02-16"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-02-17"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-02-18"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-02-19"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-02-20"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-02-21"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-02-22"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-02-23"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-02-24"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-02-25"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-02-26"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-02-27"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-02-28"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-02-29"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-03-01"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-03-02"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-03-03"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-03-04"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-03-05"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-03-06"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-03-07"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-03-08"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-03-09"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-03-10"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-03-11"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-03-12"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-03-13"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-03-14"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-03-15"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-03-16"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-03-17"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-03-18"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-03-19"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-03-20"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-03-21"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-03-22"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-03-23"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-03-24"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-03-25"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-03-26"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-03-27"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-03-28"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-03-29"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-03-30"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-03-31"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-04-01"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-04-02"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-04-03"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-04-04"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-04-05"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-04-06"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-04-07"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-04-08"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-04-09"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-04-10"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-04-11"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-04-12"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-04-13"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-04-14"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-04-15"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-04-16"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-04-17"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-04-18"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-04-19"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-04-20"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-04-21"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-04-22"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-04-23"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-04-24"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-04-25"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-04-26"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-04-27"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-04-28"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-04-29"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-04-30"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-05-01"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-05-02"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-05-03"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-05-04"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-05-05"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-05-06"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-05-07"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-05-08"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-05-09"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-05-10"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-05-11"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-05-12"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-05-13"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-05-14"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-05-15"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-05-16"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-05-17"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-05-18"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-05-19"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-05-20"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-05-21"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-05-22"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-05-23"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-05-24"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-05-25"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-05-26"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-05-27"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-05-28"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-05-29"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-05-30"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-05-31"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-06-01"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-06-02"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-06-03"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-06-04"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-06-05"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-06-06"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-06-07"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-06-08"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-06-09"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-06-10"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-06-11"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-06-12"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-06-13"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-06-14"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-06-15"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-06-16"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-06-17"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-06-18"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-06-19"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-06-20"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-06-21"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-06-22"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-06-23"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-06-24"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-06-25"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-06-26"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-06-27"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-06-28"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-06-29"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-06-30"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-07-01"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-07-02"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-07-03"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-07-04"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-07-05"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-07-06"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-07-07"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-07-08"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-07-09"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-07-10"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-07-11"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-07-12"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-07-13"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-07-14"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-07-15"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-07-16"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-07-17"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-07-18"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-07-19"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-07-20"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-07-21"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-07-22"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-07-23"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-07-24"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-07-25"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-07-26"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-07-27"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-07-28"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-07-29"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-07-30"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-07-31"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-08-01"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-08-02"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-08-03"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-08-04"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-08-05"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-08-06"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-08-07"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-08-08"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-08-09"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-08-10"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-08-11"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-08-12"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-08-13"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-08-14"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-08-15"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-08-16"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-08-17"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-08-18"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-08-19"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-08-20"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-08-21"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-08-22"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-08-23"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-08-24"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-08-25"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-08-26"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-08-27"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-08-28"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-08-29"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-08-30"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-08-31"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-09-01"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-09-02"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-09-03"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-09-04"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-09-05"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-09-06"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-09-07"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-09-08"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-09-09"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-09-10"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-09-11"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-09-12"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-09-13"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-09-14"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-09-15"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-09-16"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-09-17"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-09-18"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-09-19"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-09-20"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-09-21"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-09-22"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-09-23"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-09-24"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-09-25"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-09-26"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-09-27"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-09-28"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-09-29"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-09-30"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-10-01"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-10-02"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-10-03"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-10-04"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-10-05"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-10-06"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-10-07"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-10-08"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-10-09"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-10-10"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-10-11"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-10-12"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-10-13"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-10-14"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-10-15"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-10-16"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-10-17"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-10-18"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-10-19"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-10-20"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-10-21"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-10-22"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-10-23"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-10-24"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-10-25"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-10-26"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-10-27"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-10-28"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-10-29"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-10-30"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-10-31"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-11-01"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-11-02"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-11-03"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-11-04"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-11-05"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-11-06"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-11-07"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-11-08"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-11-09"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-11-10"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-11-11"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-11-12"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-11-13"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-11-14"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-11-15"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-11-16"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-11-17"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-11-18"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-11-19"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-11-20"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-11-21"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-11-22"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-11-23"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-11-24"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-11-25"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-11-26"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-11-27"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-11-28"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-11-29"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-11-30"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-12-01"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-12-02"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-12-03"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-12-04"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-12-05"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-12-06"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-12-07"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-12-08"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-12-09"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-12-10"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-12-11"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-12-12"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-12-13"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-12-14"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-12-15"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-12-16"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-12-17"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-12-18"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-12-19"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-12-20"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-12-21"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-12-22"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-12-23"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-12-24"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-12-25"});
    	list.add(new String[] {"/home/<USER>/day6.txt", "2020-12-26"});
    	list.add(new String[] {"/home/<USER>/day1.txt", "2020-12-27"});
    	list.add(new String[] {"/home/<USER>/day2.txt", "2020-12-28"});
    	list.add(new String[] {"/home/<USER>/day3.txt", "2020-12-29"});
    	list.add(new String[] {"/home/<USER>/day4.txt", "2020-12-30"});
    	list.add(new String[] {"/home/<USER>/day5.txt", "2020-12-31"});
    }
    
    ClarityDBGscUploadFor4661() {
        gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
    }

    public void process(File file, String date) {

            
        try {
            processFile(file, date);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void processFile(File file, String date) throws Exception {

//        gscBaseDao.createDailyTable(logDate);
    	
    	
    	List<GscEntity> gscEntities = new ArrayList<>();
        System.out.println("========================================");
		FileReader reader = new FileReader(file);
		BufferedReader br = new BufferedReader(reader);
		String str = null;
		
		String[] svs = null;

		while ((str = br.readLine()) != null) {
			
			svs = StringUtils.split(str, "\t");
		
			if (svs == null || svs.length != 6) {
				System.out.println("========== skiped, content is not correct !! " + str);
				continue;
			}
			GscEntity gscEntity;
            try {
                gscEntity = getGscEntity(svs, date);
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
            gscEntities.add(gscEntity);
            if (gscEntities.size() == maxInsertCount) {
                gscBaseDao.insertForBatch(gscEntities);
                System.out.println("finish insert for top : " + gscEntities.size() + " for file :"+ file.getName() );
                gscEntities.clear();
            }
		}
		
		if (CollectionUtils.isNotEmpty(gscEntities)) {
            gscBaseDao.insertForBatch(gscEntities);
            System.out.println("finish insert for left count :"+gscEntities.size());
//            System.out.println(new Gson().toJson(gscEntities));
            gscEntities.clear();
        }

		System.out.println("!!!!!! Total gscEntities size : " + gscEntities.size());
		System.out.println("========================================");
		br.close();
		reader.close();
		

    }

    private GscEntity getGscEntity(String[] column, String date) throws ParseException {
        GscEntity gscEntity = new GscEntity();
        gscEntity.setClicks(NumberUtils.toDouble(column[3]));
        gscEntity.setCtr(NumberUtils.toDouble(column[4]));
        gscEntity.setImpressions(NumberUtils.toDouble(column[2]));
        gscEntity.setCountryCd("us");
        gscEntity.setDevice("desktop");
        //Leo - https://www.wrike.com/open.htm?id=166383881
        int type = GscEntity.TYPE_ALL;
        gscEntity.setType(type);
        gscEntity.setKeywordName(FormatUtils.decoderString(column[1]));
        gscEntity.setUrl(column[0]);

        try {
            gscEntity.setLogDate(DateUtils.parseDate(date, dateParser));
        } catch (Exception e) {
            System.out.println(date);
            throw e;
        }
        gscEntity.setPosition(NumberUtils.toDouble(column[5]));
        gscEntity.setOwnDomainId(4661);
        gscEntity.setRelId(0);
        gscEntity.setSign(1);
        gscEntity.setVersioning(2);
        return gscEntity;
    }


    public static void main(String[] args) throws IOException {
        ClarityDBGscUploadFor4661 clarityDBGscDailyUpload = new ClarityDBGscUploadFor4661();
        
        for(String[] dateList : list) {
        	 clarityDBGscDailyUpload.process(new File(dateList[0]), dateList[1]);
        }
       
    }

}
