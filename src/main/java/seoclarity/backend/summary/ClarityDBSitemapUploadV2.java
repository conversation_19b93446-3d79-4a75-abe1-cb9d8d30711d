package seoclarity.backend.summary;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.StringReader;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.actonia.SiteMapInfoDao;
import seoclarity.backend.dao.clickhouse.bot.SiteMapDetailDao;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.actonia.SiteMapInfoEntity;
import seoclarity.backend.entity.clickhouse.bot.SiteMapEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;


/**
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.ClarityDBSitemapUploadV2" -Dexec.args="" > ClarityDBSitemapUploadV2.log 2>&1 &
 */

public class ClarityDBSitemapUploadV2 {

    public static String needUploadFolder = "/home/<USER>/sitemap_v2/needUpload";
    public static String uploadingFolder = "/home/<USER>/sitemap_v2/uploading/" + FormatUtils.formatDate(new Date(), "yyyyMMddHH") + "/";
    public static String backUpFolder = "/home/<USER>/sitemap_v2/backUpFolder";
    public static String errorFolder = "/home/<USER>/sitemap_v2/error";
    public static String duplicateFolder = "/home/<USER>/sitemap_v2/duplicate";

    private static final String databaseName = "clarity_bot";
    private static final String finalTableName = "dis_sitemap_v2";

    //sitemap_1054_7791_20201130_1_5336637938752783327.txt
    private final static String dailyTxtRegex = "sitemap_\\d+_\\d+_\\d+_\\d+\\.txt";
    private final String[] dateParser = new String[]{"yyyy-MM-dd", "yyyyMMdd"};
    private final int maxInsertCount = 500000;

    private static List<String> processingFileList = new ArrayList<String>();

    private static List<String> uniqueKeyList = new ArrayList<String>();

    private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
    private SiteMapDetailDao siteMapDetailDao;
    private SiteMapInfoDao siteMapInfoDao;
    private static boolean occurError = false;

//    private static Boolean isTopLevel = false;
    
    private Integer logId;

    
    private static final String[] headers = new String[] {
			"own_domain_id",
			"request_date",
			"root_sitemap",
			"leaf_sitemap",
			"url",
			"upload_date"
		};
    
    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n').withHeader(headers);

    ClarityDBSitemapUploadV2() {
    	siteMapDetailDao = SpringBeanFactory.getBean("siteMapDetailDao");
        clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
        siteMapInfoDao = SpringBeanFactory.getBean("siteMapInfoDao");
    }

    public static void main(String[] args) throws IOException {
        ClarityDBSitemapUploadV2 clarityDBSitemapUpload = new ClarityDBSitemapUploadV2();

        // if processing on other server, then wait till next hour
        if (clarityDBSitemapUpload.checkIsProcessingUpload()) {
            System.out.println("There found more than one processing still not finished, exit !!!");
            System.exit(-1);
        }

        System.out.println(" base folder is : " + needUploadFolder);
        clarityDBSitemapUpload.process();
    }

    public void process() {


        //1. move all files into uploading folder one by one
        //2. check unique key for OID/PID/DATE, ignore the same key and move file to duplicate folder
        //3. check if already have data in clarityDB, ignore if exist and move file to duplicate folder
        //4. upload to clarityDB
        //5. if catch any exception, then move file to error folder
        //6. all success move file to bk folder
        try {
            File folder = new File(needUploadFolder);
            if (folder == null || !folder.isDirectory()) {
                System.out.println("needUpload folder is not exist, mkdir: " + needUploadFolder);
                folder.mkdir();
            }

            if (folder.listFiles() == null || folder.listFiles().length == 0) {
                System.out.println(needUploadFolder + " is empty, please check.");
                System.exit(1);
            }

            //move all files to uploading folder
            moveFilesToProcessingFolder();
            if (!CollectionUtils.isNotEmpty(processingFileList) || processingFileList.size() <= 0) {
                insertEmptyLog();
                System.out.println("There do not have any files need to be process, skiped+++");
                System.exit(-1);
            }

            ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();

            clarityDBUploadLogEntity.setDatabaseName(databaseName);
            clarityDBUploadLogEntity.setFinalTableName(finalTableName);
            clarityDBUploadLogEntity.setTmpTableName(finalTableName);
            clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_EMBEDDING_OPENAI_VECTORDB);
            clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);

            try {
                clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
            } catch (UnknownHostException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
                clarityDBUploadLogEntity.setServerIp("Unknow server");
            }

            clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
            clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);


            //insert one new records to upoad log table
            logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);

            System.out.println("===== create clarityDB upload log ID: " + logId);


            System.out.println("==== moving need upload files into uploading foler, folder : " + uploadingFolder + ", processing total size : " + processingFileList.size());

            File tmpFile = null;
            List<SiteMapEntity> siteMapEntities = new ArrayList<>();
            
            for (String fileName : processingFileList) {

                tmpFile = new File(fileName);
                processFile(tmpFile, siteMapEntities);
            }
            
            if (CollectionUtils.isNotEmpty(siteMapEntities)) {
            	siteMapDetailDao.insertForBatchV2(siteMapEntities);
                System.out.println("finish insert for left count :" + siteMapEntities.size());
                siteMapEntities.clear();
                try {
                	System.out.println(" sleep 1 seconds!");
                	Thread.sleep(1 * 100);
				} catch (Exception e) {
					e.printStackTrace();
				}
            }
        } catch (Exception e) {
            System.out.println("Updated status in log table to " + ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE);
            clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
            System.out.println("Insert table failed, folder: " + needUploadFolder);

            moveFilesBackProcessingFolder();
            e.printStackTrace();
            return;
        }

        File file = new File(uploadingFolder);
        if (file != null && file.isDirectory()) {
            if (ArrayUtils.isNotEmpty(file.list())) {
                System.out.println("!!!!!! still get file left which should be empty, folderPath : " + uploadingFolder);
                clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
                return;
            } else {
                if (occurError) {
                    System.out.println("!!!! occur error when processing files ");
                    clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FINISHED_WITH_ERROR, logId);
                } else {
                    System.out.println("@@@ upload successed");
                    clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, logId);
                }
                deleteTempProcessingFolder();
            }
        }
    }

    private boolean checkIsProcessingUpload() {

//        List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(ClarityDBUploadLogEntity.UPLOAD_TYPE_EMBEDDING_OPENAI_VECTORDB);
//        if (CollectionUtils.isNotEmpty(list)) {
//            return true;
//        }
        return false;
    }
    

    private void moveFilesBackProcessingFolder() {

        File processingFolder = new File(uploadingFolder);

        System.out.println("====moving files back from processing folder, total file:" + processingFolder.length() + "!! from " + uploadingFolder + " to " + needUploadFolder);

        for (File gscFile : processingFolder.listFiles()) {
            try {
                FileUtils.moveFile(gscFile, new File(needUploadFolder + "/" + gscFile.getName()));
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        deleteTempProcessingFolder();
    }

    private void moveFilesToErrorFolder(String filePath) {

        File bkFolder = new File(errorFolder);
        if (bkFolder == null || !bkFolder.isDirectory()) {
            System.out.println("error folder is not exist, mkdir: " + errorFolder);
            bkFolder.mkdir();
        }

        System.out.println("====moving files to error folder,  from " + uploadingFolder + " to " + errorFolder);

        File gscFile = new File(filePath);
        try {
            FileUtils.moveFile(gscFile, new File(errorFolder + "/" + gscFile.getName()));
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    private void moveFileAndZip(String filePath) {

        File bkFolder = new File(backUpFolder);
        if (bkFolder == null || !bkFolder.isDirectory()) {
            System.out.println("BK folder is not exist, mkdir: " + backUpFolder);
            bkFolder.mkdir();
        }

        File tmpFile;
        File targetFile;

        // zip file
        try {
            tmpFile = new File(filePath);
            System.out.println("delete file : [" + tmpFile + "]");
            tmpFile.delete();
            
//            targetFile = new File(bkFolder + "/" + tmpFile.getName());
//
//            FileUtils.moveFile(tmpFile, targetFile);
//
//            System.out.println("zipped file : " + targetFile.getAbsolutePath());
//            GZipUtil.zipFile(targetFile.getAbsolutePath());
//
//            targetFile.delete();
//            System.out.println("delete file : [" + filePath + "]");
        } catch (Exception e) {
            System.out.println("delete file failed. file: [" + filePath + "]");
            e.printStackTrace();
        }

    }

    private void deleteTempProcessingFolder() {
        //deleted the temp processing folder
        File tempFolder = new File(uploadingFolder);

        if (tempFolder != null && tempFolder.isDirectory()) {
            tempFolder.delete();
        }
    }

    private void processFile(File file, List<SiteMapEntity> siteMapEntities) throws Exception {
    	System.out.println("Uploading : " + file.getName());

//        gscBaseDao.createDailyTable(logDate);
        int totalSize = 0;
        String[] parts = StringUtils.split(file.getName(), "_");
        //String prefix = "sitemap_" + entity.getId() + "_" + entity.getLogDate() + "_" + entity.getVersion() + "_";

        Integer siteMapId = StringUtils.isNotBlank(parts[2]) ? NumberUtils.toInt(parts[1]) : 0;
        Integer logDate = StringUtils.isNotBlank(parts[3]) ? NumberUtils.toInt(parts[2]) : 0;
        Integer version = StringUtils.isNotBlank(parts[4]) ? NumberUtils.toInt(parts[3]) : 1;

        try {
        	List<Integer> resultList = siteMapInfoDao.getAllSiteMapListBySiteMapId(logDate, siteMapId);
        	siteMapInfoDao.updateStatusV2(resultList, logDate, SiteMapInfoEntity.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
		}
        
        try {
        	
            BufferedReader bf = new BufferedReader(new FileReader(file));
    		String content = "";
    		int i = 0;
    		while (content != null) {
    			content = bf.readLine();
    			if (content == null) {
    				break;
    			}
    			i++;
    			
    			SiteMapEntity siteMapEntity;
                try {
                	CSVParser csvParser = new CSVParser(new StringReader(content), csvFormat);
        			siteMapEntity = getSiteMapEntity(csvParser.getRecords().get(0), siteMapId, version);
                	csvParser.close();
                } catch (Exception e) {
                    System.out.println("line i : " + i);
                    e.printStackTrace();
                    continue;
                }
                siteMapEntities.add(siteMapEntity);
                if (siteMapEntities.size() >= maxInsertCount) {
                	siteMapDetailDao.insertForBatchV2(siteMapEntities);
                    System.out.println("finish insert for top : " + maxInsertCount + " for file :" + file.getName());
                    siteMapEntities.clear();
                    try {
                    	System.out.println(" sleep 1 seconds!");
                    	Thread.sleep(1 * 100);
					} catch (Exception e) {
						e.printStackTrace();
					}
                }
    		}

    		bf.close();
        	
        } catch (Exception e) {
            e.printStackTrace();
            moveFilesToErrorFolder(file.getAbsolutePath());
            occurError = true;
            return;
        }
        
        System.out.println("size: " + siteMapEntities.size());

        moveFileAndZip(file.getAbsolutePath());

    }
    
    private Gson gson = new Gson();

    /**
     * 	private static final String[] columns = new String[] {
			"own_domain_id",
			"request_date",
			"root_sitemap",
			"leaf_sitemap",
			"url",
			"upload_date"
		};
     */
    private SiteMapEntity getSiteMapEntity(CSVRecord csvRecord, Integer siteMapId, Integer version) throws ParseException {
        SiteMapEntity siteMapEntity = new SiteMapEntity();
        siteMapEntity.setOwnDomainId(NumberUtils.toInt(csvRecord.get("own_domain_id")));
        String date = csvRecord.get("request_date");

        try {
        	siteMapEntity.setRequestDate(DateUtils.parseDate(date, dateParser));
        } catch (Exception e) {
            System.out.println(date);
            throw e;
        }
        
        
        String upload_date = csvRecord.get("upload_date");
        try {
        	siteMapEntity.setUploadDate(DateUtils.parseDate(upload_date, dateParser));
        } catch (Exception e) {
            System.out.println(date);
            throw e;
        }
        
        siteMapEntity.setRootSitemap(csvRecord.get("root_sitemap"));
        siteMapEntity.setLeafSitemap(csvRecord.get("leaf_sitemap"));
        
        siteMapEntity.setVersion(version);
        siteMapEntity.setUrl(csvRecord.get("url"));
        return siteMapEntity;
    }


    /**
     * check if the file need process
     *
     * @return
     */
    private boolean validateFile(File file) {
        if (Pattern.matches(dailyTxtRegex, file.getName())) {
            return true;
        }
        return false;
    }

//    private Date getDataDateFromFileName(String fileName) throws ParseException {
//        String[] params = StringUtils.split(fileName, "-");
//        if (params != null && params.length >= 2) {
//            return DateUtils.parseDate(params[1], dateParser);
//        }
//        return null;
//    }


    private void moveFilesToProcessingFolder() {

        File targetFolder = new File(uploadingFolder);
        if (targetFolder == null || !targetFolder.isDirectory()) {
            System.out.println("uploadingFolder is not exist, mkdir: " + uploadingFolder);
            targetFolder.mkdir();
        }

        File doneFolder = new File(needUploadFolder);

        System.out.println("====moving files to processing folder, total file:" + doneFolder.listFiles().length + "!! from " + needUploadFolder + " to " + uploadingFolder);


        for (File siteMapFile : doneFolder.listFiles()) {
            try {
                //verify if it's sitemap upload file
                if (validateFile(siteMapFile) && StringUtils.startsWith(siteMapFile.getName(), "sitemap_") && siteMapFile.isFile()) {

                    FileUtils.moveFile(siteMapFile, new File(uploadingFolder + "/" + siteMapFile.getName()));
                    processingFileList.add(uploadingFolder + "/" + siteMapFile.getName());
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }

    }

    private void moveFileToDuplicateFolder(File gscFile) {

        File targetFolder = new File(duplicateFolder);
        if (targetFolder == null || !targetFolder.isDirectory()) {
            System.out.println("duplicateFolder is not exist, mkdir: " + duplicateFolder);
            targetFolder.mkdir();
        }

        try {
            FileUtils.moveFile(gscFile, new File(duplicateFolder + "/" + gscFile.getName()));
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }


    }


    private boolean isDataExistInClarityDB(Integer ownDomainId, Integer sitemapId, String logDate, Integer version) {

        //select count() from gsc_all_in_one where own_domain_id = ? and rel_id = ? and log_date = ?

    	SiteMapEntity siteMapEntity = siteMapDetailDao.checkExistData(ownDomainId, sitemapId, logDate, version);
        if (siteMapEntity != null && StringUtils.isNotBlank(siteMapEntity.getSitemap())) {
            System.out.println(" !!!! data existed in clarityDB for OID:" + ownDomainId + ", logDate:" + logDate + ", version:" + version + ", siteMapId:" + sitemapId);
            return true;
        }

        return false;

    }


    private void insertEmptyLog() {

        ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();

        clarityDBUploadLogEntity.setTmpTableName("");
        clarityDBUploadLogEntity.setDatabaseName(databaseName);
        clarityDBUploadLogEntity.setFinalTableName(finalTableName);
        clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_SUCCESS);
        clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS);
        clarityDBUploadLogEntity.setTmpTableUploadDailyRows(0);
        clarityDBUploadLogEntity.setFinalTableUploadRows(0);
        clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_EMBEDDING_OPENAI_VECTORDB);

        try {
            clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
        } catch (UnknownHostException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
            clarityDBUploadLogEntity.setServerIp("Unknow server");
        }

        clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
        clarityDBUploadLogEntity.setTmpTableUploadEndTime(new Date());

        clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
    }


}
