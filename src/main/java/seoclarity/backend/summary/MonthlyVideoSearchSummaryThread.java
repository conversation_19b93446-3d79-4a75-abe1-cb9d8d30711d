package seoclarity.backend.summary;

import cn.hutool.core.util.StrUtil;
import com.google.common.net.InternetDomainName;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.dao.clickhouse.monthlyvideoranking.MonthlyVideoRankingDao;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.KeywordSubRankEntityVO;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyVideoSearchEntity;
import seoclarity.backend.utils.FormatUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2018-09-05 15:01
 **/
@CommonsLog
public class MonthlyVideoSearchSummaryThread implements Runnable {

    private MonthlyVideoRankingDao monthlyVideoRankingDao;

    private File file;
    private int engineId;
    private int languageId;
    private int locationId;
    private Gson gson = new Gson();
    private String scribeDate = "yyyy-MM-dd";

    MonthlyVideoSearchSummaryThread(MonthlyVideoRankingDao monthlyVideoRankingDao, File file, int engineId, int languageId) {
        this.monthlyVideoRankingDao = monthlyVideoRankingDao;
        this.file = file;
        this.engineId = engineId;
        this.languageId = languageId;
    }

    @Override
    public void run() {
//        PreparedStatement preparedStatement;
//        try {
//            preparedStatement = monthlyVideoRankingDao.getStatement();
//        } catch (SQLException e) {
//            e.printStackTrace();
//            return;
//        }

        log.info("Process file : "+file.getAbsolutePath()+" Start.");
        List<String> lines;
        try {
            lines = IOUtils.readLines(new FileInputStream(file));
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
        if (CollectionUtils.isEmpty(lines)) {
            log.info("Empty file, exit.");
            return;
        }
        log.info("total count : "+lines.size());

        List<MonthlyVideoSearchEntity> videoSearchEntities = new ArrayList<>(lines.size() * 5);
        for (String line : lines) {
            KeywordRankVO keywordRankVO = gson.fromJson(line, KeywordRankVO.class);
            Set<String> uniqueDomainSet = new HashSet<>(100);
            Set<String> uniqueDomainIncludeSDomainSet = new HashSet<>(100);
            for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
                String domainName = FormatUtils.getDomainByUrlWithoutPort(keywordRankEntityVO.getLandingPage(), false);
                String rootDomain = getRootDomain(domainName);

                if (keywordRankEntityVO.getType() != KeywordRankEntityVO.TYPE_VIDEO && StrUtil.containsIgnoreCase(domainName, ".youtube.") == false) {
                    continue;
                }

                if(StrUtil.containsIgnoreCase(domainName, ".youtube.") && CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                    continue;
                }

                MonthlyVideoSearchEntity searchEntity = getEntity(keywordRankEntityVO.getLandingPage(), domainName, keywordRankVO);
                searchEntity.setSubRank(0);
                searchEntity.setRank(keywordRankEntityVO.getRank());
                // hrd
                if (uniqueDomainSet.contains(domainName)) {
                    searchEntity.setHrd(0);
                } else {
                    uniqueDomainSet.add(domainName);
                    searchEntity.setHrd(1);
                }
                // hrrd (root domain)
                if (uniqueDomainIncludeSDomainSet.contains(rootDomain)) {
                    searchEntity.setHrrd(0);
                } else {
                    uniqueDomainIncludeSDomainSet.add(rootDomain);
                    searchEntity.setHrrd(1);
                }

                searchEntity.setArrayKey(new String[] {"video_name"});
                searchEntity.setArrayValues(new String[] {"-"});
                videoSearchEntities.add(searchEntity);
                if (CollectionUtils.isNotEmpty(keywordRankEntityVO.getSubRankVOs())) {
                    Set<String> uniqueSubDomainSet = new HashSet<>(100);
                    Set<String> uniqueSubDomainIncludeSDomainSet = new HashSet<>(100);
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        String subDomainName = FormatUtils.getDomainByUrlWithoutPort(keywordSubRankEntityVO.getLandingPage(), false);
                        String subRootDomain = getRootDomain(domainName);
                        String publisherName = keywordSubRankEntityVO.getLabel();
                        if(StrUtil.isBlank(publisherName)) {
                            publisherName = subDomainName;
                        }
                        MonthlyVideoSearchEntity subSearchEntity = getEntity(keywordSubRankEntityVO.getLandingPage(), publisherName, keywordRankVO);
                        subSearchEntity.setSubRank(keywordSubRankEntityVO.getSubRank());
                        subSearchEntity.setRank(keywordSubRankEntityVO.getRank());
                        if (StringUtils.isNotBlank(keywordSubRankEntityVO.getLabel()) && !StringUtils.equalsIgnoreCase(keywordSubRankEntityVO.getLabel(), "-")) {
                            subSearchEntity.setPublisher(keywordSubRankEntityVO.getLabel());
                        }

                        String videoName = "-";
                        if(StrUtil.isNotBlank(keywordSubRankEntityVO.getVideoName())) {
                            videoName = keywordSubRankEntityVO.getVideoName();
                        }
                        subSearchEntity.setArrayKey(new String[] {"video_name"});
                        subSearchEntity.setArrayValues(new String[] {videoName});

                        if (uniqueSubDomainSet.contains(subDomainName)) {
                            subSearchEntity.setHrd(0);
                        } else {
                            uniqueSubDomainSet.add(subDomainName);
                            subSearchEntity.setHrd(1);
                        }
                        // hrrd (root domain)
                        if (uniqueSubDomainIncludeSDomainSet.contains(subRootDomain)) {
                            subSearchEntity.setHrrd(0);
                        } else {
                            uniqueSubDomainIncludeSDomainSet.add(subRootDomain);
                            subSearchEntity.setHrrd(1);
                        }
                        videoSearchEntities.add(subSearchEntity);
                    }
                }
            }
        }
        log.info("Total get : "+videoSearchEntities.size());
        try {
            for (MonthlyVideoRankingDao.DATA_SOURCE_TYPE value : MonthlyVideoRankingDao.DATA_SOURCE_TYPE.values()) {
                monthlyVideoRankingDao.changeDataSource(value);
                monthlyVideoRankingDao.insertVideoSearchV2(videoSearchEntities);
            }
//            monthlyVideoRankingDao.insertVideoSearch(preparedStatement, videoSearchEntities);
//            preparedStatement.executeBatch();
//            Connection connection = preparedStatement.getConnection();
//            preparedStatement.close();
//            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        log.info("Process file : "+file.getAbsolutePath()+" Finish.");
    }

    private MonthlyVideoSearchEntity getEntity(String url, String publisherName, KeywordRankVO keywordRankVO) {
        MonthlyVideoSearchEntity searchEntity = new MonthlyVideoSearchEntity();
        String domainName = FormatUtils.getDomainByUrlWithoutPort(url, false);
        searchEntity.setLocationId(0);
        searchEntity.setOwnDomainId(0);
        searchEntity.setKeywordRankcheckId(keywordRankVO.getId());
        searchEntity.setKeywordName(FormatUtils.decoderString(keywordRankVO.getKeyword()));
        searchEntity.setAvgSearchVolume(NumberUtils.toLong(keywordRankVO.getSearchVol()));
        searchEntity.setCpc(NumberUtils.toDouble(keywordRankVO.getCpc()));
        searchEntity.setEngineId(engineId);
        searchEntity.setLanguageId(languageId);
        searchEntity.setProtocol(getProtocolValue(url));
        searchEntity.setPublisher(publisherName);
        searchEntity.setUri(FormatUtils.getUriFromUrl(url));
        searchEntity.setSign(1);
        searchEntity.setRankingDate(new Date(FormatUtils.toDate(keywordRankVO.getQueryDate(), scribeDate).getTime()));
        searchEntity.setUrl(url);
        searchEntity.setDomainReverse(FormatUtils.reverseDomainNameByDot(domainName));
        searchEntity.setRootDomainReverse(FormatUtils.reverseDomainNameByDot(getRootDomain(domainName)));

        return searchEntity;
    }

    public static String getRootDomain(String fullDomain) {
        try {
            return InternetDomainName.from(fullDomain).topPrivateDomain().toString();
        } catch (Exception e) {
//            e.printStackTrace();
        }
        return fullDomain;
    }

    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000000; i++) {
            getRootDomain("www.google.com");
        }
        System.out.println(System.currentTimeMillis() - start);
    }

    private int getProtocolValue(String url) {
        return StringUtils.startsWithIgnoreCase(url, "https://") ? 1 : 0;
    }
}
