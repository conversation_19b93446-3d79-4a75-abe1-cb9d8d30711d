package seoclarity.backend.summary;

import com.alibaba.fastjson.JSON;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.entity.clickhouse.pagespeed.PsSqsMessageVO;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SendMissingPageSpeedCommand extends BaseThreadCommand {

    private AmazonSQS amazonSQS;
    private List<PsSqsMessageVO> urlList;
    private String queryUrl;
    private String ip;

    public SendMissingPageSpeedCommand(AmazonSQS amazonSQS, String queryUrl, List<PsSqsMessageVO> urlList, String ip) {
        this.amazonSQS = amazonSQS;
        this.queryUrl = queryUrl;
        this.urlList = urlList;
        this.ip = ip;
    }

    private void sendUrls() {
        System.out.println("================ urlList   " + urlList.size());
        Map<String, String> messages = new HashMap<String, String>();

        int sendCount = 0;

        try {
            for (PsSqsMessageVO url : this.urlList) {

                String gson = coventToGsonStr(url);
                if (StringUtils.isBlank(gson)) {
                    continue;
                }
                messages.put(url.getMessageId().toString(), gson);
                System.out.println("send " + url.getMessageId());


                if (messages.size() >= 10) {
                    sendQAndInsertShuffle(messages);
                    sendCount += messages.size();
                    messages = new HashMap<String, String>();
                }

            }
            if (messages.size() > 0) {
                sendQAndInsertShuffle(messages);
                sendCount += messages.size();
            }

            Long startId = 0l;
            long endShuffleId = 0;


            System.out.println("   ******** sendCount  " + sendCount);

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private void sendQAndInsertShuffle(Map<String, String> messages) {
        try {
            SQSUtils.sendBatchMessageToQueue(amazonSQS, queryUrl, messages);
//			pageSpeedShuffleEntityDAO.insertForBatch(pageSpeedShuffleEntityList);

        } catch (Exception e) {
            System.out.println("Send sqs faild, queryUrl:" + queryUrl + ", messages:" + messages.size());
            e.printStackTrace();
        }
    }

    private String coventToGsonStr(PsSqsMessageVO url) {
        if (url != null && StringUtils.isNotBlank(url.getUrl())) {
            try {
                Map<String, Object> val = new HashMap<String, Object>();
                val.put("oid", url.getOwnDomainId());
                val.put("sendDate", url.getSendDate());
                val.put("urlId", url.getUrlId());
                val.put("url", url.getUrl());
                val.put("message", new Message());
                val.put("version", url.getVersion());
                if (FormatUtils.isURL(url.getUrl())) {
                    val.put("isValid", 1);
                } else {
                    val.put("isValid", 0);
                    System.out.println(" wrong url, OID:" + url.getOwnDomainId() + ", url:" + url.getUrl());
                }
                return new Gson().toJson(val);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            System.out.println("Url is empty, urlId:" + JSON.toJSONString(url));
            return null;
        }
    }


    @Override
    protected void execute() throws Exception {
        long a = System.currentTimeMillis();
        System.out.println("Start command IP: " + ip + ", queryUrl:" + queryUrl + ", currentWorkDate:" + new Date()
                + ", urlList:" + (urlList == null ? 0 : urlList.size()) );

        try {
            if (urlList != null && urlList.size() > 0) {
                sendUrls();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        CacheModleFactory.getInstance().setAliveIpAddress(ip);
        long b = System.currentTimeMillis();
        System.out.println("End command IP: " + ip + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
    }

    @Override
    protected void undo() throws Exception {

    }
}
