package seoclarity.backend.summary;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import seoclarity.backend.dao.clickhouse.youtube.YoutubeSummaryDao;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.util.*;


/**
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.YoutubeSummary" -Dexec.args="false /disk1/scribeKeyword/ 1 2023-09-03,2023-09-12" > 0903_12.log &
 */
@CommonsLog
public class YoutubeSummary extends BaseScribeSummary{

    private static boolean mobile;
    private YoutubeSummaryDao youtubeSummaryDao;
    public static Set<String> processedSet = new HashSet<>();

    public YoutubeSummary() {
        youtubeSummaryDao = SpringBeanFactory.getBean("youtubeSummaryDao");
    }

    private static final String YOUTUBE_DIR_PREFIX = "youtube_commoncrawl_keywordRank_";
    private static final String YOUTUBE_DIR_FORMAT = YOUTUBE_DIR_PREFIX+"%d_%d";
    private static final String YOUTUBE_DIR_PATTERN = "^"+YOUTUBE_DIR_PREFIX+"[\\d]+_[\\d]+$";

    private static final String YOUTUBE_MOBILE_DIR_PREFIX = "youtube_commoncrawl_keywordRank_";
    private static final String YOUTUBE_MOBILE_DIR_FORMAT = YOUTUBE_MOBILE_DIR_PREFIX+"%d_%d";
    private static final String YOUTUBE_MOBILE_DIR_PATTERN = "^"+YOUTUBE_MOBILE_DIR_PREFIX+"[\\d]+_[\\d]+$";

    public static void main(String[] args) {
            mobile = BooleanUtils.toBoolean(args[0]);
            baseLocation = args[1];
            threadCount = NumberUtils.toInt(args[2], 1);
            List<String> processDateList = new ArrayList<>();
            if (args.length >= 4) {
                String pd = args[3];
                if(pd.contains(",")) {
                    String[] dateArray = pd.split(",");
                    if(dateArray.length == 2) {
                        Date sDate = DateUtil.parse(dateArray[0], "yyyy-MM-dd");
                        Date eDate = DateUtil.parse(dateArray[1], "yyyy-MM-dd");
                        while (DateUtil.compare(eDate, sDate) >= 0) {
                            processDateList.add(DateUtil.format(eDate, "yyyy-MM-dd"));
                            log.info("date added : "+DateUtil.format(eDate, "yyyy-MM-dd"));
                            eDate = DateUtil.offsetDay(eDate, -1);
                        }
                        ThreadUtil.sleep(30000);
                    } else {
                        Collections.addAll(processDateList, StringUtils.splitByWholeSeparator(pd, ","));
                        System.out.println("add new date : "+ JSONUtil.toJsonStr(processDateList));
                    }
                } else {
                    processDateList.add(pd);
                }
            } else {
                String pd = DateUtil.yesterday().toString("yyyy-MM-dd");
                processDateList.add(pd);
            }
            for (String ps : processDateList) {
                processDateString = ps;
                log.info("Processing date : "+processDateString);
                String[] engines;
                if (args.length >= 5) {
                    engines = args[4].split(",");
                    if (ArrayUtils.isEmpty(engines)) {
                        log.error("Empty engines!!!");
                        return;
                    }
                } else {
                    File baseDir = new File(baseLocation);
                    System.out.println("baseLocation:"+baseLocation);
                    String[] fileNames = baseDir.list((file, s) -> ReUtil.isMatch(mobile ? YOUTUBE_MOBILE_DIR_PATTERN : YOUTUBE_DIR_PATTERN, s));
                    System.out.println("fileLength:"+fileNames.length);
                    ThreadUtil.sleep(10000);
                    List<String> engineList = new ArrayList<>();
                    for (String fileName : fileNames) {
                        System.out.println("fileName = " + fileName);
                        engineList.add(StringUtils.removeStartIgnoreCase(fileName, mobile ? YOUTUBE_MOBILE_DIR_PREFIX : YOUTUBE_DIR_PREFIX).replace("_","#"));
                    }
                    engines = engineList.toArray(new String[]{});
                    log.info("Find engines : "+StringUtils.join(engineList,","));
                }
                YoutubeSummary youtubeSummary = new YoutubeSummary();
                youtubeSummary.start(engines);
            }
    }

    @Override
    protected String getScribeFolder(int engine, int language) {
        return String.format(mobile ? YOUTUBE_MOBILE_DIR_FORMAT : YOUTUBE_DIR_FORMAT, engine, language);
    }

    @Override
    protected void processFile(File file, int engineId, int languageId) {
        String numberStr = ReUtil.get("[0-9]{5}$", file.getName(), 0);
        int num = NumberUtils.toInt(numberStr, 0);
        System.out.println("fileName = " + file.getName()+" number : "+num);
        YoutubeSummaryToolV2 youtubeSummaryTool = new YoutubeSummaryToolV2(youtubeSummaryDao, file, engineId, languageId, mobile);
        youtubeSummaryTool.run();
    }
}
