package seoclarity.backend.summary;

public class Result {

    private String keyword;

    private Integer location_code;

    private String language_code;

    private String competition;

    private Double cpc;

    private Integer search_volume;

    private Integer[] categories;

    private MonthlySearches[] monthly_searches;

    private Double competition_index;
    private Double low_top_of_page_bid;
    private Double high_top_of_page_bid;

    private String se;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getLocation_code() {
        return location_code;
    }

    public void setLocation_code(Integer location_code) {
        this.location_code = location_code;
    }

    public String getLanguage_code() {
        return language_code;
    }

    public void setLanguage_code(String language_code) {
        this.language_code = language_code;
    }

    public String getCompetition() {
        return competition;
    }

    public void setCompetition(String competition) {
        this.competition = competition;
    }

    public Double getCpc() {
        return cpc;
    }

    public void setCpc(Double cpc) {
        this.cpc = cpc;
    }

    public MonthlySearches[] getMonthly_searches() {
        return monthly_searches;
    }

    public void setMonthly_searches(MonthlySearches[] monthly_searches) {
        this.monthly_searches = monthly_searches;
    }

    public Integer getSearch_volume() {
        return search_volume;
    }

    public void setSearch_volume(Integer search_volume) {
        this.search_volume = search_volume;
    }

    public Integer[] getCategories() {
        return categories;
    }

    public void setCategories(Integer[] categories) {
        this.categories = categories;
    }

    public Double getCompetition_index() {
        return competition_index;
    }

    public void setCompetition_index(Double competition_index) {
        this.competition_index = competition_index;
    }

    public Double getLow_top_of_page_bid() {
        return low_top_of_page_bid;
    }

    public void setLow_top_of_page_bid(Double low_top_of_page_bid) {
        this.low_top_of_page_bid = low_top_of_page_bid;
    }

    public Double getHigh_top_of_page_bid() {
        return high_top_of_page_bid;
    }

    public void setHigh_top_of_page_bid(Double high_top_of_page_bid) {
        this.high_top_of_page_bid = high_top_of_page_bid;
    }

    public static class MonthlySearches{

        private Integer year;

        private Integer month;

        private Integer search_volume;

        public Integer getYear() {
            return year;
        }

        public void setYear(Integer year) {
            this.year = year;
        }

        public Integer getMonth() {
            return month;
        }

        public void setMonth(Integer month) {
            this.month = month;
        }

        public Integer getSearch_volume() {
            return search_volume;
        }

        public void setSearch_volume(Integer search_volume) {
            this.search_volume = search_volume;
        }
    }

    public String getSe() {
        return se;
    }

    public void setSe(String se) {
        this.se = se;
    }
}
