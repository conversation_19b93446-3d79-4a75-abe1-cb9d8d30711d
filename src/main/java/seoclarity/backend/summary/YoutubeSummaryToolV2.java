package seoclarity.backend.summary;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.dao.clickhouse.youtube.DataSouceType;
import seoclarity.backend.dao.clickhouse.youtube.YoutubeSummaryDao;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.YoutubeObject;
import seoclarity.backend.entity.clickhouse.ved.YoutubeEntity;
import seoclarity.backend.utils.FormatUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.YoutubeSummaryToolV2"
 */
public class YoutubeSummaryToolV2 {

    private YoutubeSummaryDao youtubeSummaryDao;

    private File file;
    private int engineId;
    private int languageId;
    private boolean mobile;


    private static final org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(YoutubeSummaryToolV2.class);


    public YoutubeSummaryToolV2(YoutubeSummaryDao youtubeSummaryDao, File file, int engineId, int languageId, boolean mobile) {
        this.youtubeSummaryDao = youtubeSummaryDao;
        this.file = file;
        this.engineId = engineId;
        this.languageId = languageId;
        this.mobile = mobile;
    }

    public void run() {
        log.info("Process file : " + file.getAbsolutePath() + " Start.");
        List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
        if (CollectionUtils.isEmpty(lines)) {
            log.info("Empty file, exit.");
            return;
        }
        log.info("total count : " + lines.size());
        DataSouceType[] dataSouceTypes = DataSouceType.values();

        for (String line : lines) {
            JSONObject youtubeObject = JSONUtil.parseObj(line);
            if (youtubeObject == null) {
                log.error("vo is empty :" + line);
                continue;
            }

            JSONArray domainList = youtubeObject.getJSONArray("domainList");
            if (domainList == null || domainList.size() == 0) {
                log.error("Domain list is empty for id :" + youtubeObject.getInt("id"));
                continue;
            }

            int id = youtubeObject.getInt("id");
            String keyword = FormatUtils.decoderString(youtubeObject.getStr("keyword"));
            Integer searchVol = youtubeObject.getInt("searchVol", 0);
            double cpc = youtubeObject.getDouble("cpc", 0.0);
            Integer countOfSearch = youtubeObject.getInt("qeuryState");
            Date rankingDate = DateUtil.parse(youtubeObject.getStr("queryDate"), "yyyy-MM-dd");

            for (Iterator<Object> it = domainList.iterator(); it.hasNext(); ) {
                Object domainStr = it.next();
                int domainId = NumberUtils.toInt(domainStr.toString(), -1);
                if (domainId <= 0) {
                    log.error("domain id error :" + domainId);
                    continue;
                }

                List<YoutubeEntity> youtubeEntities = youtubeObject.getBeanList("keywordRankEntityVOs", YoutubeEntity.class);
                if (CollectionUtils.isEmpty(youtubeEntities)) {
                    System.out.println("info : " + JSONUtil.toJsonStr(youtubeObject));
                    continue;
                }
                try {
                    Set<String> uniqueDomainSet = new HashSet<>();
                    for (YoutubeEntity youtubeEntity : youtubeEntities) {

                        if(uniqueDomainSet.contains(youtubeEntity.getAuthor())) {
                            youtubeEntity.setHrd(0);
                        } else {
                            uniqueDomainSet.add(youtubeEntity.getAuthor());
                            youtubeEntity.setHrd(1);
                        }
                        youtubeEntity.setHrrd(0);
                        youtubeEntity.setOwnDomainId(domainId);
                        youtubeEntity.setKeyword(keyword);
                        youtubeEntity.setKeywordRankcheckId(id);
                        youtubeEntity.setEngineId(engineId);
                        youtubeEntity.setLanguageId(languageId);
                        youtubeEntity.setLocationId(0);
                        youtubeEntity.setSearchVol(searchVol);
                        youtubeEntity.setCpc(cpc);
                        youtubeEntity.setCountOfSearch(countOfSearch);
                        youtubeEntity.setSign(1);
                        youtubeEntity.setRankingDate(rankingDate);
                        if(StrUtil.isBlank(youtubeEntity.getVideoTitle())) {
                            youtubeEntity.setVideoTitle("");
                        }
                        if(StrUtil.isBlank(youtubeEntity.getYoutubeTitle())) {
                            youtubeEntity.setYoutubeTitle(youtubeEntity.getVideoTitle());
                        }
                        if(null == youtubeEntity.getBadges()) {
                            youtubeEntity.setBadges(Collections.EMPTY_LIST);
                        }
                        if(StrUtil.isBlank(youtubeEntity.getVideolength())) {
                            youtubeEntity.setVideolength("");
                        }
                        if(StrUtil.isBlank(youtubeEntity.getCanonicalBaseUrl())) {
                            youtubeEntity.setCanonicalBaseUrl("");
                        }
                        if(StrUtil.isBlank(youtubeEntity.getCanonicalBaseUrlType())) {
                            youtubeEntity.setCanonicalBaseUrlType("");
                        }
                        if(StrUtil.isBlank(youtubeEntity.getCanonicalBaseUrlName())) {
                            youtubeEntity.setCanonicalBaseUrlName("");
                        }

                    }
//                    List<YoutubeEntity> youtubeList = getEntity(youtubeEntities, id, domainId, engineId, languageId, keyword, searchVol, cpc, countOfSearch, rankingDate);
                    System.out.println("JSON rank size : "+youtubeEntities.size());

//                    log.info(JSONUtil.toJsonStr(youtubeEntities.get(0)));
//                    log.info(JSONUtil.toJsonStr(youtubeEntities));

                    for (DataSouceType dataSouceType : dataSouceTypes) {
                        youtubeSummaryDao.changeDataSource(dataSouceType);
                        Integer existId = youtubeSummaryDao.checkExistBatchV2(engineId, languageId, domainId, 0, rankingDate, id);
                        if(existId == null) {
                            youtubeSummaryDao.insertYoutubeRankingInfo(youtubeEntities.get(0));
                            youtubeSummaryDao.insertYoutubeRankingInfoDetail(youtubeEntities);
                        } else {
                            System.out.println("Exist Info = "+domainStr+" - "+id+" - "+rankingDate);
                        }
                    }
                } catch (Exception e) {
                    System.out.println("Error JSON : "+line);
                    e.printStackTrace();
                }
            }
        }
    }

    public static void main(String[] args) {
        String json = FileUtil.readString("/home/<USER>/test.txt", StandardCharsets.UTF_8);
        YoutubeObject youtubeObject = JSONUtil.toBean(json, YoutubeObject.class);
        JSONArray youtubeJsonArray = youtubeObject.getKeywordRankEntityVOs();

//        System.out.println(JSONUtil.toJsonStr(getEntity(youtubeJsonArray, 1, 1, 1, 1, "", 1, 1.0, 1, new Date())));
    }

}







