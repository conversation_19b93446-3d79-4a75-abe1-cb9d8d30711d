package seoclarity.backend.summary;

import java.net.UnknownHostException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.apachecommons.CommonsLog;
import seoclarity.backend.dao.actonia.CdbTrackedKeywordEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.rankingforecast.RankingForecastDao;
import seoclarity.backend.entity.actonia.CdbTrackedKeywordEntity;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.RankingForecastEntity;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.multithread.core.thread.rejecthandler.RetryRejectedExecutionHandler;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @create 2018-07-06 17:29
 * <p>
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.DomainKeywordSimilarityAnalysis" -Dexec.args="20180701 20180702" -Dexec.cleanupDaemonThreads=false
 **/
@CommonsLog
public class DomainKeywordSimilarityAnalysis {

    private final static int TOPX = 10;
    private final static int MAX_QUERY_COUNT = 10000;
    private static int threadCount = 10;
    private final int threadMaxWaitCount = 10;
    private ExecutorService newFixedThreadPool =
            new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(threadMaxWaitCount), new BackendThreadFactory(), new RetryRejectedExecutionHandler());
    private final int threadKeywordSize = 50000;
    private int sign = 1;
    private int versioning = 1;
    private final String DEVICE_DESKTOP = "d";
    private final String DEVICE_MOBILE = "m";
    //    private String device = "d";
    private int locationId = 0;


    private KeywordEntityDAO keywordEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private RankingForecastDao rankingForecastDao;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private CdbTrackedKeywordEntityDAO cdbTrackedKeywordEntityDAO;
    private ScKeywordRankManager scKeywordRankManager;


    public DomainKeywordSimilarityAnalysis() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        rankingForecastDao = SpringBeanFactory.getBean("rankingForecastDao");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        cdbTrackedKeywordEntityDAO = SpringBeanFactory.getBean("cdbTrackedKeywordEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
    }

    public static void main(String[] args) throws ParseException, InterruptedException {

        boolean allDomain = true;
        List<String> domainIds = null;
        Date sDate = DateUtils.addDays(new Date(), -1);
        Date eDate = DateUtils.addDays(new Date(), -1);
        if (args != null && args.length > 0) {
            allDomain = StringUtils.equalsIgnoreCase(args[0], "all");
            if (!allDomain) {
                domainIds = Arrays.asList(StringUtils.split(args[0]));
            }
            if (args.length >= 2) {
                threadCount = NumberUtils.toInt(args[1], threadCount);
            }
            if (args.length >= 4) {
                sDate = DateUtils.parseDate(args[2], FormatUtils.DATE_FORMATS);
                eDate = DateUtils.parseDate(args[3], FormatUtils.DATE_FORMATS);
            }
        }
        log.info("process info : allDomain :" + allDomain + " domainIds: " + domainIds + " threadCount:" + threadCount + " sDate: " + sDate + " eDate:" + eDate);
        Thread.sleep(10000);

        DomainKeywordSimilarityAnalysis domainKeywordSimilarityAnalysis = new DomainKeywordSimilarityAnalysis();
        while (sDate.compareTo(eDate) <= 0) {
            if (allDomain) {
                domainKeywordSimilarityAnalysis.processAll(DateUtils.addDays(sDate, -1), sDate);
            } else {
                for (String domainId : domainIds) {
                    try {
                        domainKeywordSimilarityAnalysis.process(NumberUtils.toInt(domainId, 0), DateUtils.addDays(sDate, -1), sDate);
                    } catch (UnknownHostException e) {
                        e.printStackTrace();
                    }
                }
            }
            sDate = DateUtils.addDays(sDate, 1);
        }
        domainKeywordSimilarityAnalysis.waitingForExit();

    }

    /**
     * @param ownDomainId
     * @param baseDate    yesterday
     * @param compareDate today
     * @throws UnknownHostException
     */
    public void process(int ownDomainId, Date baseDate, Date compareDate) throws UnknownHostException {
        log.info("process domain : " + ownDomainId + " baseDate : " + baseDate + " compareDate : " + compareDate);
        boolean existData = rankingForecastDao.domainExistData(ownDomainId, compareDate);
        if (existData) {
            log.error("Domain : " + ownDomainId + " date : " + compareDate + " exist data, skip this day.");
            return;
        }
        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getOwnDomainEntityById(ownDomainId);
        Assert.notNull(ownDomainEntity, "OwnDomain is Null, skip." + ownDomainId);
//        if (ownDomainEntity.isMobileDomain()) {
//            log.error("SKIP!! Mobile domain.");
//            return;
//        }
        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);
        //skip region domain
        if (ownDomainSettingEntity != null && ownDomainSettingEntity.getRegionId() != null && ownDomainSettingEntity.getRegionId() > 0) {
            log.error("SKIP!! region domain." + ownDomainId);
            return;
        }

        int primaryEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int primaryLanguageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String baseRankingDate = FormatUtils.formatDate(baseDate, "yyyy-MM-dd");
        String compareRankingDate = FormatUtils.formatDate(compareDate, "yyyy-MM-dd");
//        boolean isMobile = ownDomainEntity.isMobileDomain();
        //https://www.wrike.com/open.htm?id=1312727234 weekly domain process Sunday
        if(ownDomainEntity.getKeywordRankFrequency().intValue() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY && !FormatUtils.isSunday(compareDate)){
            log.info("====skip weekly domain:" + ownDomainId + ",isSunday:" + FormatUtils.isSunday(compareDate));
            return;
        }else if(ownDomainEntity.getKeywordRankFrequency().intValue() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY && FormatUtils.isSunday(compareDate)){
            baseDate = DateUtils.addDays(baseDate, -13);
            compareDate = DateUtils.addDays(compareDate, -7);
            baseRankingDate = FormatUtils.formatDate(baseDate, "yyyy-MM-dd");
            compareRankingDate = FormatUtils.formatDate(compareDate, "yyyy-MM-dd");
            log.info("====start weekly domain:" + ownDomainId + ",isSunday:" + FormatUtils.isSunday(compareDate)
                    + ",baseRankingDate:" + baseRankingDate + ",compareRankingDate:" + compareRankingDate);
        }

        List<DomainSearchEngineRelEntity> domainSearchEngineRelList = scKeywordRankManager.getAllSERels(ownDomainEntity);
        if (CollectionUtils.isEmpty(domainSearchEngineRelList)) {
            log.error("SKIP!! Primary engine null:" + ownDomainId);
            return;
        }
//        log.info("==domainSearchEngineRelList:" + JSON.toJSONString(domainSearchEngineRelList));
        for (DomainSearchEngineRelEntity engineRelEntity : domainSearchEngineRelList) {
            int engineId = engineRelEntity.getRankcheckSearchEngineId();
            int languageId = engineRelEntity.getRankcheckSearchLanguageid();
            if(!engineRelEntity.getDevice().equalsIgnoreCase("d") && !engineRelEntity.getDevice().equalsIgnoreCase("m")){
                log.info("===skipEngine:" + engineRelEntity.getDevice());
                continue;
            }
            boolean isMobile = engineRelEntity.getDevice().equalsIgnoreCase("m") ? true : false;
//            if ((primaryEngineId == engineId && primaryLanguageId == languageId && !isMobile)
//                    || (primaryEngineId == 1 && engineId == 1 && primaryLanguageId == 1 && languageId == 1 &&  isMobile)) {//support 1-1 mobile
                log.info("=======ProcessEngineId:" + engineId + ",languageId:" + languageId + ",device:" + engineRelEntity.getDevice());
                RankingForecastEntity baseForecastEntity = new RankingForecastEntity();
                baseForecastEntity.setOwnDomainId(ownDomainId);
                baseForecastEntity.setEngineId(engineId);
                baseForecastEntity.setLanguageId(languageId);
                baseForecastEntity.setDevice(engineRelEntity.getDevice());
                baseForecastEntity.setLocationId(locationId);
                baseForecastEntity.setSign(sign);
                baseForecastEntity.setVersioning(versioning);
                List<CLRankingDetailEntity> detailList = new ArrayList<>();

                long startId = 0;
                long kpStartId = 0;
                long kpEndId = 0;
                int pageSize = 5000;
                while (true) {
                    log.info("=== startId:" + startId);
                    CdbTrackedKeywordEntity cdbTrackedKeywordEntity = cdbTrackedKeywordEntityDAO.getKeywordCountByDomainId(ownDomainId, startId, pageSize);
                    if (cdbTrackedKeywordEntity == null || cdbTrackedKeywordEntity.getKwCnt() == null || cdbTrackedKeywordEntity.getKwCnt() == 0) {
                        break;
                    }
                    startId = cdbTrackedKeywordEntity.getMaxRCId();
                    kpStartId = cdbTrackedKeywordEntity.getMinRCId();
                    kpEndId = cdbTrackedKeywordEntity.getMaxRCId();

                    try {
                        Thread.sleep(2 * 1000);
                    } catch (Exception e) {
                    }

                    detailList = clDailyRankingEntityDao.exportTopXKeywordsByDateRange(ownDomainId, engineId, languageId, locationId,
                            baseRankingDate, compareRankingDate, isMobile, TOPX, kpStartId, kpEndId);
                    if (CollectionUtils.isEmpty(detailList)) {
                        break;
                    }

                    log.info("===detailList:" + detailList.size() + ",kpStartId:" + kpStartId + "kpEndId:" + kpEndId);
                    log.info("Start Thread for Domain:" + ownDomainId + " engineId: " + baseForecastEntity.getEngineId() + " size : " + detailList.size());
                    newFixedThreadPool.execute(new DomainKeywordSimilarityAnalysisCommand(detailList, baseDate, compareDate, baseForecastEntity));


//                    List<List<CLRankingDetailEntity>> splitLists = CollectionUtil.split(detailList, threadKeywordSize);
//                    detailList.clear();
//
//                    //process main engine
//                    log.info("Domain : " + ownDomainId + " got lists: " + splitLists.size());
//                    for (List<CLRankingDetailEntity> splitList : splitLists) {
//                        log.info("Start Thread for Domain:" + ownDomainId + " engineId: " + baseForecastEntity.getEngineId() + " size : " + splitList.size());
//                        newFixedThreadPool.execute(new DomainKeywordSimilarityAnalysisCommand(splitList, baseDate, compareDate, baseForecastEntity));
//                    }

                }
//            } else {
//                log.info("=====skip engine:" + engineId + ",languageId:" + languageId + ",device:" + engineRelEntity.getDevice());
//            }
        }


    }

    protected void waitingThread(ExecutorService executorThreadPool) {
        while (((ThreadPoolExecutor) executorThreadPool).getActiveCount() > 0) {
            try {
                log.info("Still have alive thread, waiting...");
                Thread.sleep(60 * 1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    protected void waitingForExit() {
        waitingThread(newFixedThreadPool);
        newFixedThreadPool.shutdown();
    }

    public void waitForThreadPool() throws InterruptedException {
        while (!newFixedThreadPool.isShutdown()) {
            Thread.sleep(10 * 1000);
            int aliveCount = ((ThreadPoolExecutor) newFixedThreadPool).getActiveCount();
            System.out.println("Thread aliveCount : " + aliveCount);
            if (aliveCount == 0) {
                newFixedThreadPool.shutdown();
            }
        }
    }

    public void processAll(Date baseDate, Date compareDate) {
        List<OwnDomainEntity> domainEntities = ownDomainEntityDAO.getByIdDesc();
        for (OwnDomainEntity domainEntity : domainEntities) {
            try {
                process(domainEntity.getId(), baseDate, compareDate);
            } catch (UnknownHostException e) {
                e.printStackTrace();
            }
        }
    }

}
