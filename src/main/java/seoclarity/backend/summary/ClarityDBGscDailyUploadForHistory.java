package seoclarity.backend.summary;

import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * for search appearance
 * <AUTHOR>
 * @date 2018-08-27
 * seoclarity.backend.summary.ClarityDBGscDailyUploadForHistory
 *
 */
public class ClarityDBGscDailyUploadForHistory {

    private GscBaseDao gscBaseDao;

    public String dailyFolder = "/home/<USER>/source/actonia-googlewebmaster-crawler/tempFile";
    private final String dailyTxtRegex = "gsc-20\\d{6}-\\d{3,}-\\d+-\\w+\\.txt";
    private final String[] dateParser = new String[]{"yyyy-MM-dd", "yyyyMMdd"};
    private final int maxInsertCount = 100000;
    
    private static boolean skipCheckModifyTime = false;


    private String[] headers = new String[] {
            "clicks","ctr","impressions","countrycd","device","kName","url","date","position","domainid","reldomainid","versioning","type","searchAppearance"
    };
    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n').withHeader(headers);

    ClarityDBGscDailyUploadForHistory() {
        gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
    }

    public void process() {

        File folder = new File(dailyFolder);
        if (!folder.isDirectory()) {
            System.out.println(dailyFolder+" is not folder, please check.");
            System.exit(1);
        }

        if (folder.listFiles() == null || folder.listFiles().length == 0) {
            System.out.println(dailyFolder+" is empty, please check.");
            System.exit(1);
        }

        for (File file : folder.listFiles()) {

            if(!validateFile(file)) {
                System.out.println("skip file : "+file.getName());
                continue;
            }
            
//         if (isDataExists(file)) {
            if (isSearchAppearanceDataExists(file)) {
            	System.out.println("skip exists profile file:" + file.getName());
            	continue;
            }

//            Date logDate = null;
//            try {
//                logDate = getDataDateFromFileName(file.getName());
//            } catch (ParseException e) {
//                e.printStackTrace();
//                continue;
//            }

            // begin handle this file
            file = renameFile(file);
            try {
                processFile(file);
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }

            //zip file after insert all
            try {
                GZipUtil.zipFile(file.getAbsolutePath());
                System.out.println("delete file : "+file.getAbsolutePath()+" result : "+file.delete());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void processFile(File file) throws Exception {

//        gscBaseDao.createDailyTable(logDate);

        FileReader fr = new FileReader(file);
        CSVParser csvParser = new CSVParser(fr, csvFormat);

        List<GscEntity> gscEntities = new ArrayList<>();
        // don't read header
        List<CSVRecord> csvRecords = csvParser.getRecords();
        for (int i = 1; i < csvRecords.size(); i++) {
            CSVRecord csvRecord = csvRecords.get(i);
            GscEntity gscEntity;
            try {
                gscEntity = getGscEntity(csvRecord);
            } catch (Exception e) {
                System.out.println("line i : "+i);
                e.printStackTrace();
                continue;
            }
            gscEntities.add(gscEntity);
            if (gscEntities.size() == maxInsertCount) {
                gscBaseDao.insertForBatch(gscEntities);
                System.out.println("finish insert for top : "+maxInsertCount+ " for file :"+ file.getName());
                gscEntities.clear();
            }
        }
        if (CollectionUtils.isNotEmpty(gscEntities)) {
            gscBaseDao.insertForBatch(gscEntities);
            System.out.println("finish insert for left count :"+gscEntities.size());
//            System.out.println(new Gson().toJson(gscEntities));
            gscEntities.clear();
        }

        fr.close();
    }

    private GscEntity getGscEntity(CSVRecord csvRecord) throws ParseException {
        GscEntity gscEntity = new GscEntity();
        gscEntity.setClicks(NumberUtils.toDouble(csvRecord.get("clicks")));
        gscEntity.setCtr(NumberUtils.toDouble(csvRecord.get("ctr")));
        gscEntity.setImpressions(NumberUtils.toDouble(csvRecord.get("impressions")));
        gscEntity.setCountryCd(csvRecord.get("countrycd"));
        gscEntity.setDevice(csvRecord.get("device"));
        //Leo - https://www.wrike.com/open.htm?id=166383881
        int type = NumberUtils.toInt(csvRecord.get("type"));
        gscEntity.setType(type);
        if (type == GscEntity.TYPE_ALL) {
            gscEntity.setKeywordName(FormatUtils.decoderString(csvRecord.get("kName")));
            gscEntity.setUrl(csvRecord.get("url"));
        } else if (type == GscEntity.TYPE_KEYWORD) {
            gscEntity.setKeywordName(FormatUtils.decoderString(csvRecord.get("kName")));
        } else if (type == GscEntity.TYPE_URL) {
            gscEntity.setUrl(csvRecord.get("url"));
        }
        String date = csvRecord.get("date");

        try {
            gscEntity.setLogDate(DateUtils.parseDate(date, dateParser));
        } catch (Exception e) {
            System.out.println(date);
            throw e;
        }
        gscEntity.setPosition(NumberUtils.toDouble(csvRecord.get("position")));
        gscEntity.setOwnDomainId(NumberUtils.toInt(csvRecord.get("domainid")));
        gscEntity.setRelId(NumberUtils.toInt(csvRecord.get("reldomainid")));
        gscEntity.setSign(1);
        gscEntity.setVersioning(NumberUtils.toInt(csvRecord.get("versioning")));
        gscEntity.setSearchAppearance(csvRecord.get("searchAppearance"));
        return gscEntity;
    }

    private File renameFile(File file) {
        String newFilePath = file.getAbsolutePath()+"_running";
        System.out.println("rename file : "+newFilePath);
        if(file.renameTo(new File(newFilePath))) {
            return new File(newFilePath);
        }
        return null;
    }
    
    // only check current domain profile data exists
    private boolean isDataExists(File file) {
    	boolean isExists = true;
    	String fileName = file.getName();
    	String[] cols = StringUtils.split(fileName, '-');
    	String date = cols[1];
    	String oid = cols[2];
    	String relId = cols[3];
    	SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
    	SimpleDateFormat sf2 = new SimpleDateFormat("yyyy-MM-dd");
    	try {
    		Integer[] count = gscBaseDao.checkExistsSimple(Integer.valueOf(oid), Integer.valueOf(relId), sf2.format(sf.parse(date)));
    		if (count == null || count.length == 0 || (count[0] == 0 && count[1] == 0)) {
    			isExists = false;
    		}
    		System.out.println("current process OID:" + oid + ", relId:" + relId + ", date:" + date + ", count:" + count + ", isExists:" + isExists);
    	} catch (Exception e) {
    		e.printStackTrace();
		}
    	return isExists;
    }
    
    // only check current domain profile search appearance esists
    private boolean isSearchAppearanceDataExists(File file) {
    	boolean isExists = true;
    	String fileName = file.getName();
    	String[] cols = StringUtils.split(fileName, '-');
    	String date = cols[1];
    	String oid = cols[2];
    	String relId = cols[3];
    	SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
    	SimpleDateFormat sf2 = new SimpleDateFormat("yyyy-MM-dd");
    	try {
    		Integer[] count = gscBaseDao.checkExistsSimple(Integer.valueOf(oid), Integer.valueOf(relId), sf2.format(sf.parse(date)));
    		if (count == null || count.length == 0 || (count[0] > 0 && count[1] == 0)) {
    			isExists = false;
    		}
    		System.out.println("current process OID:" + oid + ", relId:" + relId + ", date:" + date + ", count:" + StringUtils.join(count, ",") + ", isExists:" + isExists);
    	} catch (Exception e) {
    		e.printStackTrace();
		}
    	return isExists;
    }

    /**
     * check if the file need process
     * @return
     */
    private boolean validateFile(File file) {
        if (Pattern.matches(dailyTxtRegex, file.getName())) {
            //check file last modify
            long lastModify = file.lastModified();
            long between = System.currentTimeMillis() - lastModify;
            if (between > (60 * 60 * 1000)) {
                return true;
            } else {
            	if (skipCheckModifyTime) {
            		return true;
            	}
                System.out.println("file : "+file.getName()+" was use by other project.");
            }
        }
        return false;
    }

//    private Date getDataDateFromFileName(String fileName) throws ParseException {
//        String[] params = StringUtils.split(fileName, "-");
//        if (params != null && params.length >= 2) {
//            return DateUtils.parseDate(params[1], dateParser);
//        }
//        return null;
//    }

    public static void main(String[] args) throws IOException {
        ClarityDBGscDailyUploadForHistory clarityDBGscDailyUpload = new ClarityDBGscDailyUploadForHistory();
        if (args != null && args.length >= 1) {
            clarityDBGscDailyUpload.dailyFolder = args[0];
        }
        if (args != null && args.length == 2) {
        	skipCheckModifyTime = Boolean.valueOf(args[1]);
        }
        System.out.println("folder is : "+clarityDBGscDailyUpload.dailyFolder + ", skipCheckModifyTime:" + skipCheckModifyTime);
        clarityDBGscDailyUpload.process();
    }

}
