package seoclarity.backend.summary.onetime;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.clickhouse.pagespeed.PageSpeedDao;
import seoclarity.backend.entity.clickhouse.pagespeed.PageSpeedEntity;
import seoclarity.backend.entity.clickhouse.pagespeed.v5.Audits;
import seoclarity.backend.entity.clickhouse.pagespeed.v5.ErrorResponse;
import seoclarity.backend.entity.clickhouse.pagespeed.v5.LightHouseResult;
import seoclarity.backend.entity.clickhouse.pagespeed.v5.PageSpeedV5ResponseEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@CommonsLog
public class FormatV3PageSpeedData {

    private static final float INDICATE_FLOAT_NULL_VALUE = -99;
    private PageSpeedDao pageSpeedDao;

    int crawlMonth = 202005;

    List<Integer> fullDomainList;

    public FormatV3PageSpeedData(){
        pageSpeedDao = SpringBeanFactory.getBean("pageSpeedDao");
    }


    private void process(){

        fullDomainList = pageSpeedDao.getUniqueDomainListByMonth(202005);
        log.info("===fullDomainList size: " + fullDomainList.size());
//        List<Integer> juneDomainList = pageSpeedDao.getUniqueDomainListByMonth(202006);
//        log.info("===juneDomainList size: " + juneDomainList.size());
//        fullDomainList.removeAll(juneDomainList);

        log.info("===fullDomainList size: " + fullDomainList.size());

        Date sDate = FormatUtils.toDate("2020-05-01", FormatUtils.DATE_PATTERN_2);
        Date eDate = FormatUtils.toDate("2020-05-31", FormatUtils.DATE_PATTERN_2);

        while (sDate.compareTo(eDate) <= 0) {

            String crawlDate = FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2);
            for(Integer domainId: fullDomainList){
//                if(!crawlDate.equals("2020-05-16") || domainId != 551){
//                    continue;
//                }
                processForDomain(domainId, crawlDate, 1);
                processForDomain(domainId, crawlDate, 0);
            }

            sDate = DateUtils.addDays(sDate, 1);
        }

    }

    private void processForDomain(int domainId, String crawlDate, int queryDevice){

        log.info("=======processForDomain: domainId:" + domainId + ",crawlDate:" + crawlDate + ",device:" + queryDevice);
        List<PageSpeedEntity> pageSpeedEntityList = pageSpeedDao.getByDomainId(crawlDate, crawlMonth, domainId, queryDevice);
        log.info("====pageSpeedEntityList size: " + pageSpeedEntityList.size());

        if(CollectionUtils.isNotEmpty(pageSpeedEntityList)){

            List<PageSpeedEntity> insertSuccessList = new ArrayList<>();
            List<PageSpeedEntity> insertErrorList = new ArrayList<>();

            try {

                for(PageSpeedEntity pageSpeedEntity : pageSpeedEntityList){

                    String entityCrawlDate = pageSpeedEntity.getCrawlDate();
                    int entityCrawlMonth = pageSpeedEntity.getCrawlMonth();
                    int entityDomainId = pageSpeedEntity.getOwnDomainId();
                    String device =pageSpeedEntity.getDevice();
                    String oUrl = pageSpeedEntity.getoUrl();
                    String rUrl = pageSpeedEntity.getrUrl();
                    int redfirectFlg = pageSpeedEntity.getRedfirectFlg();
                    int responseCode = pageSpeedEntity.getResponseCode();
                    int score = pageSpeedEntity.getScore();
                    String uploadDate = pageSpeedEntity.getUploadDate();
                    int version = pageSpeedEntity.getVersion();


                    PageSpeedEntity pageSpeed = new PageSpeedEntity();
                    pageSpeed.setSendDate(FormatUtils.toDate(entityCrawlDate, FormatUtils.DATE_PATTERN_2));
                    pageSpeed.setCrawlMonth(entityCrawlMonth);
                    pageSpeed.setOwnDomainId(entityDomainId);
                    pageSpeed.setDevice(device.equals("0") ? "m" : "d");
                    pageSpeed.setoUrl(oUrl);
                    pageSpeed.setrUrl(StringUtils.isBlank(rUrl) ? "" : rUrl);
                    pageSpeed.setRedfirectFlg(redfirectFlg);
                    pageSpeed.setResponseCode(responseCode);
                    pageSpeed.setUploadDate(uploadDate);
                    pageSpeed.setVersion(version);

                    if(responseCode == 801 || responseCode == 811 || StringUtils.isBlank(pageSpeedEntity.getRawJson())){
                        insertErrorList.add(pageSpeed);
                        continue;
                    }
                    String rowJson = "";
                    try{
                        rowJson = pageSpeedEntity.getRawJson();
                    }catch (Exception e){
                        log.error("===error rowJson: domain" + pageSpeedEntity.getOwnDomainId() + ",crawlDate:" + pageSpeedEntity.getSendDate()
                                + ",device:" + pageSpeedEntity.getDevice() + ",url:" + pageSpeedEntity.getoUrl());
                        e.printStackTrace();
                        continue;
                    }

                    ErrorResponse errorResponse = null;
                    try {
                        errorResponse = new Gson().fromJson(rowJson, ErrorResponse.class);
                    }catch (Exception e){
                        log.error("===error parseRowJson: domain" + pageSpeedEntity.getOwnDomainId() + ",crawlDate:" + pageSpeedEntity.getSendDate()
                                + ",device:" + pageSpeedEntity.getDevice() + ",url:" + pageSpeedEntity.getoUrl());
                        e.printStackTrace();
                        continue;
                    }

                    if (errorResponse != null && errorResponse.getError() != null) {
                        insertErrorList.add(pageSpeed);
                    }else {
                        pageSpeed.setScore(score);
                        formatNewValue(rowJson, pageSpeed);
                        insertSuccessList.add(pageSpeed);
                    }

                    if(insertSuccessList.size() >= 200){
                        pageSpeedDao.insertBatchV3(insertSuccessList, true, false, false);
                        insertSuccessList.clear();
                    }

                    if(insertErrorList.size() >= 200){
                        pageSpeedDao.insertBatchV3(insertErrorList, false, false, false);
                        insertErrorList.clear();
                    }

                }

                if(CollectionUtils.isNotEmpty(insertSuccessList)){
                    pageSpeedDao.insertBatchV3(insertSuccessList, true, false, false);
                }

                if(CollectionUtils.isNotEmpty(insertErrorList)){
                    pageSpeedDao.insertBatchV3(insertErrorList, false, false, false);
                }

            }catch (Exception e){
                e.printStackTrace();
            }


        }


    }


    private void formatNewValue(String rowJson, PageSpeedEntity pageSpeedEntity){

        try {
            PageSpeedV5ResponseEntity resultJsonEntity = new Gson().fromJson(rowJson, PageSpeedV5ResponseEntity.class);
            LightHouseResult lightHouseResult = resultJsonEntity.getLighthouseResult();
            Audits audits = lightHouseResult.getAuditsObjects();

            //score
            pageSpeedEntity.setSpeedIndexScore((audits.getSpeedIndex() == null || audits.getSpeedIndex().getScore() == null ) ? INDICATE_FLOAT_NULL_VALUE : audits.getSpeedIndex().getScore());
            pageSpeedEntity.setEstimatedInputLatencyScore((audits.getEstimatedInputLatency() == null || audits.getEstimatedInputLatency().getScore() == null )? INDICATE_FLOAT_NULL_VALUE : audits.getEstimatedInputLatency().getScore());
            pageSpeedEntity.setMainthreadWorkBreakdownScore((audits.getMainthreadWorkBreakdown() == null || audits.getMainthreadWorkBreakdown().getScore() == null) ? INDICATE_FLOAT_NULL_VALUE :  audits.getMainthreadWorkBreakdown().getScore());
            pageSpeedEntity.setUsesOptimizedImagesScore((audits.getUsesOptimizedImages() == null || audits.getUsesOptimizedImages().getScore() == null) ? INDICATE_FLOAT_NULL_VALUE : audits.getUsesOptimizedImages().getScore());
            pageSpeedEntity.setInteractiveScore((audits.getInteractive() == null || audits.getInteractive().getScore() == null )? INDICATE_FLOAT_NULL_VALUE : audits.getInteractive().getScore());
            pageSpeedEntity.setUsesRelPreconnectScore((audits.getUsesRelPreconnect() == null || audits.getUsesRelPreconnect().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUsesRelPreconnect().getScore());
            pageSpeedEntity.setUsesRelPreloadScore((audits.getUsesRelPreload() == null || audits.getUsesRelPreload().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUsesRelPreload().getScore());
            pageSpeedEntity.setMaxPotentialFidScore((audits.getMaxPotentialFid() == null || audits.getMaxPotentialFid().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getMaxPotentialFid().getScore());
            pageSpeedEntity.setOffscreenImagesScore((audits.getOffscreenImages() == null || audits.getOffscreenImages().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getOffscreenImages().getScore());
            pageSpeedEntity.setUsesLongCacheTtlScore((audits.getUsesLongCacheTtl() == null || audits.getUsesLongCacheTtl().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUsesLongCacheTtl().getScore());
            pageSpeedEntity.setFirstCpuIdleScore((audits.getFirstCpuIdle() == null || audits.getFirstCpuIdle().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getFirstCpuIdle().getScore());
            pageSpeedEntity.setUsesWebpImagesScore((audits.getUsesWebpImages() == null || audits.getUsesWebpImages().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUsesWebpImages().getScore());
            pageSpeedEntity.setDomSizeScore((audits.getDomSize() == null || audits.getDomSize().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getDomSize().getScore());
            pageSpeedEntity.setFirstMeaningfulPaintScore((audits.getFirstMeaningfulPaint() == null || audits.getFirstMeaningfulPaint().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getFirstMeaningfulPaint().getScore());
            pageSpeedEntity.setFirstContentfulPaintScore((audits.getFirstContentfulPaint() == null || audits.getFirstContentfulPaint().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getFirstContentfulPaint().getScore());
            pageSpeedEntity.setCumulativeLayoutShiftScore((audits.getCumulativeLayoutShift() == null || audits.getCumulativeLayoutShift().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getCumulativeLayoutShift().getScore());
            pageSpeedEntity.setRedirectsScore((audits.getRedirects() == null || audits.getRedirects().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getRedirects().getScore());
            pageSpeedEntity.setUsesResponsiveImagesScore((audits.getUsesResponsiveImages() == null || audits.getUsesResponsiveImages().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUsesResponsiveImages().getScore());
            pageSpeedEntity.setUnusedCssRulesScore((audits.getUnusedCssRules() == null || audits.getUnusedCssRules().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUnusedCssRules().getScore());
            pageSpeedEntity.setTotalBlockingTimeScore((audits.getTotalBlockingTime() == null || audits.getTotalBlockingTime().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getTotalBlockingTime().getScore());
            pageSpeedEntity.setTotalByteWeightScore((audits.getTotalByteWeight() == null || audits.getTotalByteWeight().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getTotalByteWeight().getScore());
            pageSpeedEntity.setUnminifiedCssScore((audits.getUnminifiedCss() == null || audits.getUnminifiedCss().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUnminifiedCss().getScore());
            pageSpeedEntity.setRenderBlockingResourcesScore((audits.getRenderBlockingResources() == null || audits.getRenderBlockingResources().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getRenderBlockingResources().getScore());
            pageSpeedEntity.setUnusedJavascriptScore((audits.getUnusedJavascript() == null || audits.getUnusedJavascript().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUnusedJavascript().getScore());
            pageSpeedEntity.setLargestContentfulPaintScore((audits.getLargestContentfulPaint() == null || audits.getLargestContentfulPaint().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getLargestContentfulPaint().getScore());
            pageSpeedEntity.setUnminifiedJavascriptScore((audits.getUnminifiedJavascript() == null || audits.getUnminifiedJavascript().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUnminifiedJavascript().getScore());
            pageSpeedEntity.setUsesTextCompressionScore((audits.getUsesTextCompression() == null || audits.getUsesTextCompression().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getUsesTextCompression().getScore());
            pageSpeedEntity.setBootupTimeScore((audits.getBootupTime() == null || audits.getBootupTime().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getBootupTime().getScore());
            pageSpeedEntity.setEfficientAnimatedContentScore((audits.getEfficientAnimatedContent() == null || audits.getEfficientAnimatedContent().getScore() == null)? INDICATE_FLOAT_NULL_VALUE : audits.getEfficientAnimatedContent().getScore());
            pageSpeedEntity.setFontDisplayScore((audits.getFontDisplay() == null || audits.getFontDisplay().getScore() == null) ? INDICATE_FLOAT_NULL_VALUE : audits.getFontDisplay().getScore());
            pageSpeedEntity.setServerResponseTimeScore((audits.getServerResponseTime() == null || audits.getServerResponseTime().getScore() == null) ? INDICATE_FLOAT_NULL_VALUE : audits.getServerResponseTime().getScore());

            //DisplayValue
            pageSpeedEntity.setSpeedIndexDisplayValue(audits.getEstimatedInputLatency() == null ?  null : audits.getSpeedIndex().getDisplayValue());
            pageSpeedEntity.setEstimatedInputLatencyDisplayValue(audits.getEstimatedInputLatency() == null ?  null : audits.getEstimatedInputLatency().getDisplayValue());
            pageSpeedEntity.setMainthreadWorkBreakdownDisplayValue(audits.getMainthreadWorkBreakdown() == null ?  null : audits.getMainthreadWorkBreakdown().getDisplayValue());
            pageSpeedEntity.setUsesOptimizedImagesDisplayValue(audits.getUsesOptimizedImages() == null ?  null : audits.getUsesOptimizedImages().getDisplayValue());
            pageSpeedEntity.setInteractiveDisplayValue(audits.getInteractive() == null ?  null : audits.getInteractive().getDisplayValue());
            pageSpeedEntity.setUsesRelPreconnectDisplayValue(audits.getUsesRelPreconnect() == null ?  null : audits.getUsesRelPreconnect().getDisplayValue());
            pageSpeedEntity.setUsesRelPreloadDisplayValue(audits.getUsesRelPreload() == null ?  null : audits.getUsesRelPreload().getDisplayValue());
            pageSpeedEntity.setMaxPotentialFidDisplayValue(audits.getMaxPotentialFid() == null ?  null : audits.getMaxPotentialFid().getDisplayValue());
            pageSpeedEntity.setOffscreenImagesDisplayValue(audits.getOffscreenImages() == null ?  null : audits.getOffscreenImages().getDisplayValue());
            pageSpeedEntity.setUsesLongCacheTtlDisplayValue(audits.getUsesLongCacheTtl() == null ?  null : audits.getUsesLongCacheTtl().getDisplayValue());
            pageSpeedEntity.setFirstCpuIdleDisplayValue(audits.getFirstCpuIdle() == null ?  null : audits.getFirstCpuIdle().getDisplayValue());
            pageSpeedEntity.setUsesWebpImagesDisplayValue(audits.getUsesWebpImages() == null ?  null : audits.getUsesWebpImages().getDisplayValue());
            pageSpeedEntity.setDomSizeDisplayValue(audits.getDomSize() == null ?  null : audits.getDomSize().getDisplayValue());
            pageSpeedEntity.setFirstMeaningfulPaintDisplayValue(audits.getFirstMeaningfulPaint() == null ?  null : audits.getFirstMeaningfulPaint().getDisplayValue());
            pageSpeedEntity.setFirstContentfulPaintDisplayValue(audits.getFirstContentfulPaint() == null ?  null : audits.getFirstContentfulPaint().getDisplayValue());
            pageSpeedEntity.setCumulativeLayoutShiftDisplayValue(audits.getCumulativeLayoutShift() == null ?  null : audits.getCumulativeLayoutShift().getDisplayValue());
            pageSpeedEntity.setRedirectsDisplayValue(audits.getRedirects() == null ?  null : audits.getRedirects().getDisplayValue());
            pageSpeedEntity.setUsesResponsiveImagesDisplayValue(audits.getUsesResponsiveImages() == null ?  null : audits.getUsesResponsiveImages().getDisplayValue());
            pageSpeedEntity.setUnusedCssRulesDisplayValue(audits.getUnusedCssRules() == null ?  null : audits.getUnusedCssRules().getDisplayValue());
            pageSpeedEntity.setTotalBlockingTimeDisplayValue(audits.getTotalBlockingTime() == null ?  null : audits.getTotalBlockingTime().getDisplayValue());
            pageSpeedEntity.setTotalByteWeightDisplayValue(audits.getTotalByteWeight() == null ?  null : audits.getTotalByteWeight().getDisplayValue());
            pageSpeedEntity.setUnminifiedCssDisplayValue(audits.getUnminifiedCss() == null ?  null : audits.getUnminifiedCss().getDisplayValue());
            pageSpeedEntity.setRenderBlockingResourcesDisplayValue(audits.getRenderBlockingResources() == null ?  null : audits.getRenderBlockingResources().getDisplayValue());
            pageSpeedEntity.setUnusedJavascriptDisplayValue(audits.getUnusedJavascript() == null ?  null : audits.getUnusedJavascript().getDisplayValue());
            pageSpeedEntity.setLargestContentfulPaintDisplayValue(audits.getLargestContentfulPaint() == null ?  null : audits.getLargestContentfulPaint().getDisplayValue());
            pageSpeedEntity.setUnminifiedJavascriptDisplayValue(audits.getUnminifiedJavascript() == null ?  null : audits.getUnminifiedJavascript().getDisplayValue());
            pageSpeedEntity.setUsesTextCompressionDisplayValue(audits.getUsesTextCompression() == null ?  null : audits.getUsesTextCompression().getDisplayValue());
            pageSpeedEntity.setBootupTimeDisplayValue(audits.getBootupTime() == null ?  null : audits.getBootupTime().getDisplayValue());
            pageSpeedEntity.setEfficientAnimatedContentDisplayValue(audits.getEfficientAnimatedContent() == null ?  null : audits.getEfficientAnimatedContent().getDisplayValue());
            pageSpeedEntity.setFontDisplayDisplayValue((audits.getFontDisplay() == null || audits.getFontDisplay().getDisplayValue() == null) ? null : audits.getFontDisplay().getDisplayValue());
            pageSpeedEntity.setServerResponseTimeDisplayValue((audits.getServerResponseTime() == null || audits.getServerResponseTime().getDisplayValue() == null) ? null : audits.getServerResponseTime().getDisplayValue());

            //NumericValue
            pageSpeedEntity.setSpeedIndexNumericValue((audits.getSpeedIndex() == null || audits.getSpeedIndex().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getSpeedIndex().getNumericValue().toString()));
            pageSpeedEntity.setEstimatedInputLatencyNumericValue((audits.getEstimatedInputLatency() == null || audits.getEstimatedInputLatency().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getEstimatedInputLatency().getNumericValue().toString()));
            pageSpeedEntity.setMainthreadWorkBreakdownNumericValue((audits.getMainthreadWorkBreakdown() == null || audits.getMainthreadWorkBreakdown().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getMainthreadWorkBreakdown().getNumericValue().toString()));
            pageSpeedEntity.setUsesOptimizedImagesNumericValue((audits.getUsesOptimizedImages() == null || audits.getUsesOptimizedImages().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUsesOptimizedImages().getNumericValue().toString()));
            pageSpeedEntity.setInteractiveNumericValue((audits.getInteractive() == null || audits.getInteractive().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getInteractive().getNumericValue().toString()));
            pageSpeedEntity.setUsesRelPreconnectNumericValue((audits.getUsesRelPreconnect() == null || audits.getUsesRelPreconnect().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUsesRelPreconnect().getNumericValue().toString()));
            pageSpeedEntity.setUsesRelPreloadNumericValue((audits.getUsesRelPreload() == null || audits.getUsesRelPreload().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUsesRelPreload().getNumericValue().toString()));
            pageSpeedEntity.setMaxPotentialFidNumericValue((audits.getMaxPotentialFid() == null || audits.getMaxPotentialFid().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getMaxPotentialFid().getNumericValue().toString()));
            pageSpeedEntity.setOffscreenImagesNumericValue((audits.getOffscreenImages() == null || audits.getOffscreenImages().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getOffscreenImages().getNumericValue().toString()));
            pageSpeedEntity.setUsesLongCacheTtlNumericValue((audits.getUsesLongCacheTtl() == null || audits.getUsesLongCacheTtl().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUsesLongCacheTtl().getNumericValue().toString()));
            pageSpeedEntity.setFirstCpuIdleNumericValue((audits.getFirstCpuIdle() == null || audits.getFirstCpuIdle().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getFirstCpuIdle().getNumericValue().toString()));
            pageSpeedEntity.setUsesWebpImagesNumericValue((audits.getUsesWebpImages() == null || audits.getUsesWebpImages().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUsesWebpImages().getNumericValue().toString()));
            pageSpeedEntity.setDomSizeNumericValue((audits.getDomSize() == null || audits.getDomSize().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getDomSize().getNumericValue().toString()));
            pageSpeedEntity.setFirstMeaningfulPaintNumericValue((audits.getFirstMeaningfulPaint() == null || audits.getFirstMeaningfulPaint().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getFirstMeaningfulPaint().getNumericValue().toString()));
            pageSpeedEntity.setFirstContentfulPaintNumericValue((audits.getFirstContentfulPaint() == null || audits.getFirstContentfulPaint().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getFirstContentfulPaint().getNumericValue().toString()));
            pageSpeedEntity.setCumulativeLayoutShiftNumericValue((audits.getCumulativeLayoutShift() == null || audits.getCumulativeLayoutShift().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getCumulativeLayoutShift().getNumericValue().toString()));
            pageSpeedEntity.setRedirectsNumericValue((audits.getRedirects() == null || audits.getRedirects().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getRedirects().getNumericValue().toString()));
            pageSpeedEntity.setUsesResponsiveImagesNumericValue((audits.getUsesResponsiveImages() == null || audits.getUsesResponsiveImages().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUsesResponsiveImages().getNumericValue().toString()));
            pageSpeedEntity.setUnusedCssRulesNumericValue((audits.getUnusedCssRules() == null || audits.getUnusedCssRules().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUnusedCssRules().getNumericValue().toString()));
            pageSpeedEntity.setTotalBlockingTimeNumericValue((audits.getTotalBlockingTime() == null || audits.getTotalBlockingTime().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getTotalBlockingTime().getNumericValue().toString()));
            pageSpeedEntity.setTotalByteWeightNumericValue((audits.getTotalByteWeight() == null || audits.getTotalByteWeight().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getTotalByteWeight().getNumericValue().toString()));
            pageSpeedEntity.setUnminifiedCssNumericValue((audits.getUnminifiedCss() == null || audits.getUnminifiedCss().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUnminifiedCss().getNumericValue().toString()));
            pageSpeedEntity.setRenderBlockingResourcesNumericValue((audits.getRenderBlockingResources() == null || audits.getRenderBlockingResources().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getRenderBlockingResources().getNumericValue().toString()));
            pageSpeedEntity.setUnusedJavascriptNumericValue((audits.getUnusedJavascript() == null || audits.getUnusedJavascript().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUnusedJavascript().getNumericValue().toString()));
            pageSpeedEntity.setLargestContentfulPaintNumericValue((audits.getLargestContentfulPaint() == null || audits.getLargestContentfulPaint().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getLargestContentfulPaint().getNumericValue().toString()));
            pageSpeedEntity.setUnminifiedJavascriptNumericValue((audits.getUnminifiedJavascript() == null || audits.getUnminifiedJavascript().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUnminifiedJavascript().getNumericValue().toString()));
            pageSpeedEntity.setUsesTextCompressionNumericValue((audits.getUsesTextCompression() == null || audits.getUsesTextCompression().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getUsesTextCompression().getNumericValue().toString()));
            pageSpeedEntity.setBootupTimeNumericValue((audits.getBootupTime() == null || audits.getBootupTime().getNumericValue() == null) ? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getBootupTime().getNumericValue().toString()));
            pageSpeedEntity.setEfficientAnimatedContentNumericValue((audits.getEfficientAnimatedContent() == null || audits.getEfficientAnimatedContent().getNumericValue() == null)? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getEfficientAnimatedContent().getNumericValue().toString()));
            pageSpeedEntity.setFontDisplayNumericValue((audits.getFontDisplay() == null || audits.getFontDisplay().getNumericValue() == null) ? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getFontDisplay().getNumericValue().toString()));
            pageSpeedEntity.setServerResponseTimeNumericValue((audits.getServerResponseTime() == null || audits.getServerResponseTime().getNumericValue() == null) ? INDICATE_FLOAT_NULL_VALUE : NumberUtils.toFloat(audits.getServerResponseTime().getNumericValue().toString()));

            //Json
            pageSpeedEntity.setSpeedIndexJson(lightHouseResult.getSpeedIndexJson());
            pageSpeedEntity.setEstimatedInputLatencyJson(lightHouseResult.getEstimatedInputLatencyJson());
            pageSpeedEntity.setMainthreadWorkBreakdownJson(lightHouseResult.getMainthreadWorkBreakdownJson());
            pageSpeedEntity.setUsesOptimizedImagesJson(lightHouseResult.getUsesOptimizedImagesJson());
            pageSpeedEntity.setInteractiveJson(lightHouseResult.getInteractiveJson());
            pageSpeedEntity.setUsesRelPreconnectJson(lightHouseResult.getUsesRelPreconnectJson());
            pageSpeedEntity.setUsesRelPreloadJson(lightHouseResult.getUsesRelPreloadJson());
            pageSpeedEntity.setMaxPotentialFidJson(lightHouseResult.getMaxPotentialFidJson());
            pageSpeedEntity.setOffscreenImagesJson(lightHouseResult.getOffscreenImagesJson());
            pageSpeedEntity.setUsesLongCacheTtlJson(lightHouseResult.getUsesLongCacheTtlJson());
            pageSpeedEntity.setFirstCpuIdleJson(lightHouseResult.getFirstCpuIdleJson());
            pageSpeedEntity.setUsesWebpImagesJson(lightHouseResult.getUsesWebpImagesJson());
            pageSpeedEntity.setDomSizeJson(lightHouseResult.getDomSizeJson());
            pageSpeedEntity.setFirstMeaningfulPaintJson(lightHouseResult.getFirstMeaningfulPaintJson());
            pageSpeedEntity.setFirstContentfulPaintJson(lightHouseResult.getFirstContentfulPaintJson());
            pageSpeedEntity.setCumulativeLayoutShiftJson(lightHouseResult.getCumulativeLayoutShiftJson());
            pageSpeedEntity.setRedirectsJson(lightHouseResult.getRedirectsJson());
            pageSpeedEntity.setUsesResponsiveImagesJson(lightHouseResult.getUsesResponsiveImagesJson());
            pageSpeedEntity.setUnusedCssRulesJson(lightHouseResult.getUnusedCssRulesJson());
            pageSpeedEntity.setTotalBlockingTimeJson(lightHouseResult.getTotalBlockingTimeJson());
            pageSpeedEntity.setTotalByteWeightJson(lightHouseResult.getTotalByteWeightJson());
            pageSpeedEntity.setUnminifiedCssJson(lightHouseResult.getUnminifiedCssJson());
            pageSpeedEntity.setRenderBlockingResourcesJson(lightHouseResult.getRenderBlockingResourcesJson());
            pageSpeedEntity.setUnusedJavascriptJson(lightHouseResult.getUnusedJavascriptJson());
            pageSpeedEntity.setLargestContentfulPaintJson(lightHouseResult.getLargestContentfulPaintJson());
            pageSpeedEntity.setUnminifiedJavascriptJson(lightHouseResult.getUnminifiedJavascriptJson());
            pageSpeedEntity.setUsesTextCompressionJson(lightHouseResult.getUsesTextCompressionJson());
            pageSpeedEntity.setBootupTimeJson(lightHouseResult.getBootupTimeJson());
            pageSpeedEntity.setEfficientAnimatedContentJson(lightHouseResult.getEfficientAnimatedContentJson());
            pageSpeedEntity.setFontDisplayJson(lightHouseResult.getFontDisplayJson());
            pageSpeedEntity.setServerResponseTimeJson(lightHouseResult.getServerResponseTimeJson());

            pageSpeedEntity.setRuleResultskey(new String[]{});
            pageSpeedEntity.setRuleResultsvalue(new String[]{});

            pageSpeedEntity.setRawJson(rowJson);


        }catch (Exception e){
            log.error("===error line: domain" + pageSpeedEntity.getOwnDomainId() + ",crawlDate:" + pageSpeedEntity.getSendDate()
                    + ",device:" + pageSpeedEntity.getDevice() + ",url:" + pageSpeedEntity.getoUrl());
//            log.error("===rowJson: " + rowJson);
            e.printStackTrace();
            System.exit(0);
        }

//        return pageSpeedEntity;
    }



    public static void main(String[] args) throws Exception {

        FormatV3PageSpeedData formatV3PageSpeedData = new FormatV3PageSpeedData();
        formatV3PageSpeedData.process();

    }

}
