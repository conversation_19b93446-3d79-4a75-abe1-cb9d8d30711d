package seoclarity.backend.summary.onetime;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * https://www.wrike.com/open.htm?id=817931880
 */
@CommonsLog
public class UploadGSCByFile {

    public static String localFolder = "/home/<USER>/9999/";
    private final String[] dateParser = new String[]{"yyyy-MM-dd", "yyyyMMdd"};
    private final int maxInsertCount = 100000;

    private static List<Map<String, String>> list = new ArrayList<>();
    public static final Map<String, String> fileMap = new HashMap();

    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',').withHeader("Date", "Keyword", "URL", "Impressions", "Clicks", "CTR", "Average Position");

    static {
//        fileMap.put("GSC 01012019 - 06302019 MOBILE botify-2021-11-17.csv.gz","mobile");
//        fileMap.put("GSC 01012020 - 06302020 MOBILE botify-2021-11-17.csv.gz","mobile");
        fileMap.put("GSC_06052018-12122018_botify-2021-11-16_DESKTOP.csv.gz", "desktop");
//        fileMap.put("GSC 06052018 - 12122018 botify-2021-11-17 MOBILE.csv.gz","mobile");
//        fileMap.put("GSC 07012019 - 12312019 MOBILE botify-2021-11-17.csv.gz","mobile");
//        fileMap.put("GSC 07012020 - 11302020 MOBILE botify-2021-11-17.csv.gz","mobile");
//        fileMap.put("GSC 12012020 - 12312020 MOBILE botify-2021-11-17.csv.gz","mobile");
    }

    private GscBaseDao gscBaseDao;

    UploadGSCByFile() {
        gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
    }

    private void processFile(String fileName, String device) throws Exception {

        File localFile = new File(localFolder + fileName);


        FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, localFolder + fileName, localFolder, 1, 3);

        String textFilePath = GZipUtil.unGzipFile(localFile.getAbsolutePath());
        log.info("Find Gzip file, will uncompress to : " + textFilePath);
        File file = new File(textFilePath);

        List<GscEntity> gscEntities = new ArrayList<>();

        System.out.println("========================================");
        FileReader reader = new FileReader(file);
        BufferedReader br = new BufferedReader(reader);
        String str = null;

        String[] svs = null;

        FileReader fr = new FileReader(file);
        String errorMsg = "";
        Long startTime = System.currentTimeMillis();

        List<CSVRecord> csvRecords = null;
        CSVParser csvParser = new CSVParser(fr, csvFormat);
        csvRecords = csvParser.getRecords();
        for (int i = 1; i < csvRecords.size(); i++) {
            try {
                GscEntity gscEntity;
                CSVRecord csvRecord = csvRecords.get(i);
                gscEntity = getGscEntity(csvRecord, device);
                gscEntities.add(gscEntity);
                if (gscEntities.size() == maxInsertCount) {
                gscBaseDao.insertForBatch(gscEntities);
                    System.out.println("finish insert for top : " + gscEntities.size() + " for file :" + file.getName());
                    gscEntities.clear();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (CollectionUtils.isNotEmpty(gscEntities)) {
            gscBaseDao.insertForBatch(gscEntities);
            System.out.println("finish insert for left count :"+gscEntities.size());
//            System.out.println(new Gson().toJson(gscEntities));
            gscEntities.clear();
        }

//        while ((str = br.readLine()) != null) {
//            if(str.contains("Date")){
//                continue;
//            }
//            svs = StringUtils.split(str, ",");
//
//            if (svs == null || svs.length != 7) {
//                System.out.println("========== skiped, content is not correct !! " + str.length());
//                continue;
//            }
//            GscEntity gscEntity;
//            try {
//                gscEntity = getGscEntity(svs, device);
//            } catch (Exception e) {
//                e.printStackTrace();
//                continue;
//            }
//            gscEntities.add(gscEntity);
//            if (gscEntities.size() == maxInsertCount) {
////                gscBaseDao.insertForBatch(gscEntities);
//                System.out.println("finish insert for top : " + gscEntities.size() + " for file :"+ file.getName() );
//                gscEntities.clear();
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(gscEntities)) {
////            gscBaseDao.insertForBatch(gscEntities);
//            System.out.println("finish insert for left count :"+gscEntities.size());
////            System.out.println(new Gson().toJson(gscEntities));
//            gscEntities.clear();
//        }

        System.out.println("!!!!!! Total gscEntities size : " + gscEntities.size());
        System.out.println("========================================");
        br.close();
        reader.close();


    }

    private GscEntity getGscEntity(String[] column, String device) throws ParseException {
        GscEntity gscEntity = new GscEntity();

        gscEntity.setLogDate(DateUtils.parseDate(column[0], dateParser));
        gscEntity.setKeywordName(column[1]);
        gscEntity.setUrl(column[2]);
        gscEntity.setImpressions(NumberUtils.toDouble(column[3]));
        gscEntity.setClicks(NumberUtils.toDouble(column[4]));
        gscEntity.setCtr(NumberUtils.toDouble(column[5]));
        gscEntity.setPosition(NumberUtils.toDouble(column[6]));

        gscEntity.setCountryCd("usa");
        gscEntity.setDevice(device);
        int type = GscEntity.TYPE_ALL;
        gscEntity.setType(type);
        gscEntity.setOwnDomainId(9999);
        gscEntity.setRelId(8623);
        gscEntity.setSign(1);
        gscEntity.setVersioning(1);
        return gscEntity;
    }


    private GscEntity getGscEntity(CSVRecord csvRecord, String device) throws ParseException {
        GscEntity gscEntity = new GscEntity();

        gscEntity.setLogDate(DateUtils.parseDate(csvRecord.get("Date"), dateParser));
        gscEntity.setKeywordName(csvRecord.get("Keyword"));
        gscEntity.setUrl(csvRecord.get("URL"));
        gscEntity.setImpressions(NumberUtils.toDouble(csvRecord.get("Impressions")));
        gscEntity.setClicks(NumberUtils.toDouble(csvRecord.get("Clicks")));
        gscEntity.setCtr(NumberUtils.toDouble(csvRecord.get("CTR")));
        gscEntity.setPosition(NumberUtils.toDouble(csvRecord.get("Average Position")));

        gscEntity.setCountryCd("usa");
        gscEntity.setDevice(device);
        int type = GscEntity.TYPE_ALL;
        gscEntity.setType(type);
        gscEntity.setOwnDomainId(9999);
        gscEntity.setRelId(8623);
        gscEntity.setSign(1);
        gscEntity.setVersioning(1);
        return gscEntity;
    }

    public static void main(String[] args) throws Exception {
        UploadGSCByFile uploadGSCByFile = new UploadGSCByFile();

        for (String fileName : fileMap.keySet()) {
            uploadGSCByFile.processFile(fileName, fileMap.get(fileName));
        }

    }

}
