package seoclarity.backend.summary.onetime;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.api.client.util.Key;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import seoclarity.backend.dao.actonia.GoogleTrendIndexDAO;
import seoclarity.backend.dao.clickhouse.pixelheight.PixelHeightRankingDao;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.entity.actonia.GoogleTrendIndexEntity;
import seoclarity.backend.summary.BaseScribeSummary;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-09-21 17:53
 * vim ./src/main/java/seoclarity/backend/summary/GoogleTrendSummary.java
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.onetime.GoogleTrendSummary" -Dexec.cleanupDaemonThreads=false -Dexec.args="/disk1/scribeKeyword/ 2020-04-03"
 **/
@CommonsLog
public class GoogleTrendSummary {

    private static final String PIXEL_DIR_PREFIX = "google_trend_commoncrawl_keywordRank_";
    private static final int DATARANGE_90 = 90;
    private static final int DATARANGE_360 = 360;
    private static final int DATARANGE_365 = 365;
    private static final int DATARANGE_900 = 900;
    private static final int DATARANGE_1500 = 1500;

    private GoogleTrendIndexDAO googleTrendIndexDAO;
    public static Set<String> processedSet = new HashSet<>();
    private static String baseLocation;
    protected static String processDateString;

    public GoogleTrendSummary() {
        googleTrendIndexDAO = SpringBeanFactory.getBean("googleTrendIndexDAO");
    }


    public static void main(String[] args) {
        baseLocation = args[0];
        List<String> processDateList = new ArrayList<>();
        GoogleTrendSummary googleTrendSummary = new GoogleTrendSummary();
        if (args.length >= 2) {
            String pd = args[1];
            if(pd.contains(",")) {
                Collections.addAll(processDateList, StringUtils.splitByWholeSeparator(pd, ","));
                System.out.println("add new date : "+ JSONUtil.toJsonStr(processDateList));
            } else {
                processDateList.add(pd);
            }
        } else {
            String pd = DateUtil.yesterday().toString("yyyy-MM-dd");
            processDateList.add(pd);
        }
        for (String processDateString : processDateList) {
            log.info("Processing date : "+processDateString);
            String[] countries;
            List<File> files = new ArrayList<>();
            if (args.length >= 3) {
                countries = args[2].split(",");
                if (ArrayUtils.isEmpty(countries)) {
                    log.error("Empty engines!!!");
                    return;
                }
                for (String country : countries) {
                    File tmpFile = new File(PIXEL_DIR_PREFIX+"_"+country);
                    if(tmpFile.exists()) {
                        File[] subFiles = tmpFile.listFiles((f, s) -> StringUtils.containsIgnoreCase(s, processDateString));
                        if(subFiles != null) {
                            files.addAll(Arrays.asList(subFiles));
                        }
                    }
                }
            } else {
                File baseDir = new File(baseLocation);
                System.out.println("baseLocation:"+baseLocation);
                File[] dirs = baseDir.listFiles((file, s) -> StringUtils.startsWithIgnoreCase(s, PIXEL_DIR_PREFIX));
                if(dirs == null || dirs.length == 0) {
                  log.info("No file found..");
                } else {
                    for (File file : dirs) {
                        File[] subFiles = file.listFiles((f, s) -> StringUtils.containsIgnoreCase(s, processDateString));
                        if(subFiles != null) {
                            files.addAll(Arrays.asList(subFiles));
                        }
                    }
                }
            }
            for (File file : files) {
                googleTrendSummary.processFile(file);
            }
        }
    }

//    public static void main(String[] args) {
//        File f = new File("/Users/<USER>/Desktop/trend.txt");
//        GoogleTrendSummary googleTrendSummary = new GoogleTrendSummary();
//        googleTrendSummary.processFile(f);
//    }

    private void processFile(File file) {
        log.info("Process file : " + file.getAbsolutePath() + " Start.");
        List<String> lines;
        try {
            lines = IOUtils.readLines(new FileInputStream(file));
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
        if (CollectionUtils.isEmpty(lines)) {
            log.info("Empty file, exit.");
            return;
        }
        log.info("total count : " + lines.size());

        for (String line : lines) {
            try {
                JSONObject mainObject = JSONUtil.parseObj(line);
                if (mainObject == null || mainObject.size() == 0) {
                    log.error("vo is empty :" + line);
                    continue;
                }

                JSONObject keywordProperty = mainObject.getJSONObject("keyword");
                JSONArray results = (JSONArray) mainObject.get("results");

                if (CollectionUtils.isEmpty(results) || keywordProperty == null) {
                    log.error("Error Json data : " + line);
                    continue;
                }

//                Map<String, JSONObject> keywordMap = new HashMap<>();
//                for (Object keyword : keywords) {
//                    JSONObject obj = (JSONObject) keyword;
//                    keywordMap.put(obj.getStr("keywordText"), obj);
//                }

                for (JSONObject resultObject : results.jsonIter()) {
                    JSONArray bullets = resultObject.getJSONArray("bullets");
                    if (CollectionUtil.isEmpty(bullets)) {
                        log.info("Didn't find any bullets datas.");
                        continue;
                    }
                    String dataJsonStr = resultObject.getStr("resultObject");
                    if (StrUtil.isBlank(dataJsonStr)) {
                        log.info("Didn't find any datas.");
                        continue;
                    }
                    JSONObject dataMap = new JSONObject();
                    List<String> values = StrUtil.split(dataJsonStr, "\\n");
                    if(values.size() == 1) {
                        values = StrUtil.split(dataJsonStr, "\n");
                    }
                    int valueCount = 0;
                    for (String record : values) {
                        if(record.startsWith("20") && record.contains(",")) {
                            String op1 = record.split(",")[1];
                            op1 = StrUtil.removePrefix(op1, "<");
                            try {
                                int value = NumberUtil.parseInt(op1);
                                if(value > 0) {
                                    valueCount++;
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                log.error("Parser Number : "+op1);
                            }
                            dataMap.set(record.split(",")[0], op1);
                        }
                    }
                    String jsonDataInsert = null;
                    if(dataMap.size() > 0) {
                        jsonDataInsert = dataMap.toString();
                    }


//                    dataJsonStr = StringUtils.removeStartIgnoreCase(dataJsonStr, ")]}'");
//                    dataJsonStr = StringUtils.removeStartIgnoreCase(dataJsonStr, ",");
//
//                    JSONObject dataObject = JSONUtil.parseObj(dataJsonStr);
//                    JSONArray timelineData = dataObject.getJSONObject("default").getJSONArray("timelineData");
//
//                    int valueCount = 0;
//                    if (timelineData.size() > 0) {
//                        Map<String, String> jsonDataMap = new HashMap<>();
//                        for (int j = 0; j < timelineData.size(); j++) {
//                            JSONObject jsonObject = (JSONObject) timelineData.get(j);
//                            String date = jsonObject.getStr("formattedTime");
//                            JSONArray values = jsonObject.getJSONArray("value");
//                            JSONArray hasDatas = jsonObject.getJSONArray("hasData");
//                            String value = "";
//                            boolean hasData = true;
//                            if (hasDatas.size() >= 1) {
//                                hasData = hasDatas.getBool(0);
//                            }
//                            if (hasData && values.size() >= 1) {
//                                value = values.getStr(0);
//                                valueCount++;
//                            }
//                            jsonDataMap.put(date, value);
//                        }
//                        if(jsonDataMap.isEmpty() == false) {
//                            jsonDataInsert = JSONUtil.toJsonStr(jsonDataMap);
//                        }
//                    }



                    if(StrUtil.isBlank(jsonDataInsert)) {
                        log.info("JSON Length is 0, not delete old data and insert.");
                        break;
                    }

                    Integer frequency = keywordProperty.getInt("frequency");
                    String keyword = FormatUtils.decodeKeyword(keywordProperty.getStr("keywordText"));
//                    log.info("jsonDataInsert : "+jsonDataInsert);
                    GoogleTrendIndexEntity googleTrendIndexEntity = new GoogleTrendIndexEntity();
                    googleTrendIndexEntity.setKeywordName(keyword);
                    googleTrendIndexEntity.setGeo(keywordProperty.getStr("country"));
                    googleTrendIndexEntity.setJsonData(jsonDataInsert);
                    googleTrendIndexEntity.setCreateDate(new Date());
                    googleTrendIndexEntity.setRankDate(keywordProperty.getInt("sendToQDate"));
                    if (frequency == null) {
                        googleTrendIndexEntity.setDataRange(DATARANGE_90);
                    } else if(NumberUtil.compare(frequency,DATARANGE_90) == 0) {
                        googleTrendIndexEntity.setDataRange(DATARANGE_90);
                    } else if (NumberUtil.compare(frequency,DATARANGE_360) == 0) {
                        googleTrendIndexEntity.setDataRange(DATARANGE_360);
                    } else if (NumberUtil.compare(frequency,DATARANGE_365) == 0) {
                        googleTrendIndexEntity.setDataRange(DATARANGE_365);
                    } else if (NumberUtil.compare(frequency,DATARANGE_900) == 0) {
                        googleTrendIndexEntity.setDataRange(DATARANGE_900);
                    } else if (NumberUtil.compare(frequency,DATARANGE_1500) == 0) {
                        googleTrendIndexEntity.setDataRange(DATARANGE_1500);
                    } else {
                        googleTrendIndexEntity.setDataRange(DATARANGE_90);
                    }

                    log.info("insert map : " + JSONUtil.toJsonPrettyStr(googleTrendIndexEntity));

                    if(frequency != null && DATARANGE_1500 == frequency) {
                        try {
                            //针对5年数据进行校验
                            String lastJson = googleTrendIndexDAO.getLastData(keyword, googleTrendIndexEntity.getGeo(), frequency);
                            if(StrUtil.isNotBlank(lastJson)) {
                                int lastCount = 0;
                                JSONObject jsonObject = JSONUtil.parseObj(lastJson);
                                for (Object value : jsonObject.values()) {
                                    if(value != null && StrUtil.isNotBlank(value.toString()) && NumberUtil.parseInt(value.toString()) > 0) {
                                        lastCount++;
                                    }
                                }
                                double vs = NumberUtil.div(valueCount, lastCount);
                                log.info("5 years data check database count : "+lastCount+", new count : "+valueCount+" result : "+vs);
                                if(NumberUtil.compare(vs, 0.8) < 0) {
                                    //包含旧结果数80%以上的结果数量才能取代
                                    log.info("5 years data check database False!");
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    try {
                        googleTrendIndexDAO.delete(keyword, keywordProperty.getInt("sendToQDate"), googleTrendIndexEntity.getGeo(), frequency);
                        googleTrendIndexDAO.insert(googleTrendIndexEntity);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        log.info("===Process file : " + file.getAbsolutePath() + " Finish.");
    }

}
