package seoclarity.backend.summary.onetime;

import info.debatty.java.stringsimilarity.Jaccard;
import info.debatty.java.stringsimilarity.Levenshtein;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.entity.clickhouse.monthlyranking.ExportFile;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyRankingEntity;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.onetime.FindSimilarKeywordsUs" -Dexec.cleanupDaemonThreads=false
 */
public class FindSimilarKeywordsUs {

//    private static final String[] ALL_FILE_HEADER = {"keywordA", "keywordB", "KeywordAUrlString", "KeywordBUrlString", "Score"};
    private static final String[] OVER_FILE_HEADER = {"keywordA", "keywordB", "Similarity score", "Levenstein score"};

//    private static final String ALL_EXPORT_FILE_PATH = "/home/<USER>/source/jason/similar-keyword/allExtracts";
    private static final String OVER_EXPORT_FILE_PATH = "/home/<USER>/overValueExtracts_less_85";
    private static final String OVER_85_EXPORT_FILE_PATH = "/home/<USER>/overValueExtracts_more_85";
//    private static final String OVER_INDEX_5_EXPORT_FILE_PATH = "/home/<USER>/source/jason/similar-keyword/overValueExtracts_index5";

    private static final String SUFFIX_FILE_NAME = ".csv";

//    private static FileWriter allExportWriter = null;
    private static FileWriter overExportWriter = null;
    private static FileWriter over85ExportWriter = null;
//    private static FileWriter overIndex5Writer = null;

//    private static CSVPrinter allCsvPrinter = null;
    private static CSVPrinter overCsvPrinter = null;
    private static CSVPrinter over85CsvPrinter = null;
//    private static CSVPrinter overIndex5CsvPrinter = null;


    private MonthlyRankingDao monthlyRankingDao;


    public FindSimilarKeywordsUs() {
        monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
    }

    public static void main(String[] args) throws Exception {

        FindSimilarKeywordsUs findSimilarKeywords = new FindSimilarKeywordsUs();
        findSimilarKeywords.process();

    }

    public void process() throws Exception {

        System.out.println("start time :" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        List<MonthlyRankingEntity> keywordRankCheckIdList = monthlyRankingDao.getKeywordRankCheckId(" = 110000 ");
        Map<Long, List<MonthlyRankingEntity>> svMaps = new HashMap<>();
        for (MonthlyRankingEntity monthlyRankingEntity : keywordRankCheckIdList) {
            if(null == svMaps.get(monthlyRankingEntity.getAvgSearchVolume())) {
                svMaps.put(monthlyRankingEntity.getAvgSearchVolume(), new ArrayList<MonthlyRankingEntity>());
            }
            List<MonthlyRankingEntity> entities = svMaps.get(monthlyRankingEntity.getAvgSearchVolume());
            entities.add(monthlyRankingEntity);
        }

        for (Map.Entry<Long, List<MonthlyRankingEntity>> longListEntry : svMaps.entrySet()) {
            System.out.println("Sv: "+longListEntry.getKey()+" have size : "+longListEntry.getValue().size());
            if(CollectionUtils.isEmpty(longListEntry.getValue())) {
                continue;
            }
            Set<Integer> keywordIdList = new HashSet<>();
            for (MonthlyRankingEntity monthlyRankingEntity : longListEntry.getValue()) {
                keywordIdList.add(monthlyRankingEntity.getKeywordRankcheckId());
            }
            List<MonthlyRankingEntity> monthlyRankingEntities = monthlyRankingDao.getTop10UrlsForUs(keywordIdList);

            similarityBasedTop10Domains(longListEntry.getValue(), monthlyRankingEntities, longListEntry.getKey());

        }

    }

    Jaccard instance = new Jaccard(3);
    Levenshtein levenshtein = new Levenshtein();
    private void similarityBasedTop10Domains(List<MonthlyRankingEntity> keywordList, List<MonthlyRankingEntity> monthlyRankingEntities, Long quertNum) {

        Map<String, List<String>> keywordRanks = new HashMap<>();

        for (MonthlyRankingEntity rankingEntity : keywordList) {
            String kNameString = rankingEntity.getKeywordName();
            Integer rankcheckId = rankingEntity.getKeywordRankcheckId();

            List<String> top10UrlList = new ArrayList<>();

            for (MonthlyRankingEntity monthlyRankingEntity : monthlyRankingEntities) {
                if (rankcheckId.intValue() == monthlyRankingEntity.getKeywordRankcheckId().intValue()) {

                    String url = monthlyRankingEntity.getUrl();

                    url = StringUtils.removeStartIgnoreCase(url, "https://");

                    url = StringUtils.removeStartIgnoreCase(url, "http://");

                    if (StringUtils.containsIgnoreCase(monthlyRankingEntity.getUrl(), ".google.")) {
                        System.out.println("find google domain : " + monthlyRankingEntity.getUrl());
                        url = CommonUtils.getDomainByUrl(monthlyRankingEntity.getUrl());
                    }

                    top10UrlList.add(url);
                }
            }
            keywordRanks.put(kNameString, top10UrlList);
        }


        System.out.println("total result: " + keywordRanks.size());

        //TODO
//        Jaccard instanceIndex5 = new Jaccard(5);
        Map<String, List<String>> values = new HashMap<>();

//        List<ExportFile> allValueList = new ArrayList<>();
        List<ExportFile> overValueList = new ArrayList<>();
        List<ExportFile> over85ValueList = new ArrayList<>();
//        List<ExportFile> allValueIndex5List = new ArrayList<>();

        Set<String> keywordSet = new HashSet<>();
        try {

            int i = 0;
            for (String kName : keywordRanks.keySet()) {
                List<String> primaryDomainList = keywordRanks.get(kName);
                Collections.sort(primaryDomainList);

                String urlListString = StringUtils.join(primaryDomainList, ",");

//            System.out.println("=== urlListString:   " + urlListString);

//                System.out.println("get keyword : " + kName);

                for (String secondaryLoopKeyword : keywordRanks.keySet()) {
                    if(StringUtils.equals(secondaryLoopKeyword, kName)) {
                        continue;
                    }
                    String combineKName = kName + "!_!" + secondaryLoopKeyword;
                    String tmpKeyName = secondaryLoopKeyword + "!_!" + kName;


                    if (keywordSet.contains(tmpKeyName)) {
//                        System.out.println("exist keyword : " + combineKName + " ====== " + tmpKeyName);
                        continue;
                    }
                    keywordSet.add(combineKName);
                    List<String> secondaryDomainList = keywordRanks.get(secondaryLoopKeyword);
                    Collections.sort(secondaryDomainList);
                    String secondaryLoopDomainListString = StringUtils.join(secondaryDomainList, ",");

                    i++;

                    Double urlNameCosine = instance.similarity(urlListString, secondaryLoopDomainListString);
//                    System.out.println("UrlA: "+urlListString);
//                    System.out.println("UrlB: "+secondaryLoopDomainListString);
//                    System.out.println("Score: "+urlNameCosine);

//                    Double urlNameCosineIndex5 = instanceIndex5.similarity(urlListString, secondaryLoopDomainListString);

//                    ExportFile exportFileAll = new ExportFile();
//                    exportFileAll.setKeywordA(kName);
//                    exportFileAll.setKeywordB(secondaryLoopKeyword);
//                    exportFileAll.setKeywordAUrlString(urlListString);
//                    exportFileAll.setKeywordBUrlString(secondaryLoopDomainListString);
//                    exportFileAll.setScore(urlNameCosine);
//                    allValueList.add(exportFileAll);

//                    ExportFile exportFileAllIndex5 = new ExportFile();
//                    exportFileAllIndex5.setKeywordA(kName);
//                    exportFileAllIndex5.setKeywordB(secondaryLoopKeyword);
//                    exportFileAllIndex5.setKeywordAUrlString(urlListString);
//                    exportFileAllIndex5.setKeywordBUrlString(secondaryLoopDomainListString);
//                    exportFileAllIndex5.setScore(urlNameCosineIndex5);
//                    allValueIndex5List.add(exportFileAll);

//                    if (urlNameCosineIndex5 >= 0.85) {
//                        System.out.println(" ----- urlNameCosineIndex5 >= 0.85 " + urlNameCosineIndex5);
//                    }

                    if (urlNameCosine <= 0.85) {
                        System.out.println(" ----- urlNameCosine <= 0.85 " + urlNameCosine);
                        ExportFile exportFileOverValue = new ExportFile();
                        exportFileOverValue.setKeywordA(kName);
                        exportFileOverValue.setKeywordB(secondaryLoopKeyword);
                        exportFileOverValue.setScore(urlNameCosine);
                        exportFileOverValue.setLevenstein(levenshtein.distance(urlListString, secondaryLoopDomainListString));
                        overValueList.add(exportFileOverValue);
                    } else {
                        System.out.println(" ----- urlNameCosine > 0.85 " + urlNameCosine);
                        ExportFile exportFileOverValue = new ExportFile();
                        exportFileOverValue.setKeywordA(kName);
                        exportFileOverValue.setKeywordB(secondaryLoopKeyword);
                        exportFileOverValue.setScore(urlNameCosine);
                        exportFileOverValue.setLevenstein(levenshtein.distance(urlListString, secondaryLoopDomainListString));
                        over85ValueList.add(exportFileOverValue);
                    }
                }

            }
            System.out.println("======== times :   " + i);
//            System.out.println(" ------- allValueList size " + allValueList.size());
            System.out.println(" ------- overValueList size " + overValueList.size());
//            System.out.println(" ------- allValueIndex5List size " + allValueIndex5List.size());

//            CSVFormat allFormat = CSVFormat.DEFAULT.withHeader(ALL_FILE_HEADER).withSkipHeaderRecord();
            CSVFormat overFormat = CSVFormat.DEFAULT.withHeader(OVER_FILE_HEADER).withSkipHeaderRecord();

//            allExportWriter = new FileWriter(ALL_EXPORT_FILE_PATH +"_" + quertNum + SUFFIX_FILE_NAME);
            overExportWriter = new FileWriter(OVER_EXPORT_FILE_PATH +"_" + quertNum + SUFFIX_FILE_NAME);
            over85ExportWriter = new FileWriter(OVER_85_EXPORT_FILE_PATH +"_" + quertNum + SUFFIX_FILE_NAME);
//            overIndex5Writer = new FileWriter(OVER_INDEX_5_EXPORT_FILE_PATH +"_" + quertNum + SUFFIX_FILE_NAME);

//            allCsvPrinter = new CSVPrinter(allExportWriter, allFormat);
            overCsvPrinter = new CSVPrinter(overExportWriter, overFormat);
            over85CsvPrinter = new CSVPrinter(over85ExportWriter, overFormat);
//            overIndex5CsvPrinter = new CSVPrinter(overIndex5Writer, allFormat);

//            printCsvFile(ALL_FILE_HEADER, ALL_EXPORT_FILE_PATH, allValueList);
            printCsvFile(overValueList, overCsvPrinter);
            printCsvFile(over85ValueList, over85CsvPrinter);
//            printCsvFile(ALL_FILE_HEADER, OVER_INDEX_5_EXPORT_FILE_PATH, allValueIndex5List);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private static void printCsvFile(List<ExportFile> recordList, CSVPrinter csvPrinter) throws Exception {

//        if(fileName.equals(OVER_EXPORT_FILE_PATH)){
        System.out.println(" ------- recordList   " + recordList.size());
//        }
        int i = 1;
        for (ExportFile exportFile : recordList) {

//            System.out.println(" ------- exportFile   " + JSON.toJSONString(exportFile));
            List<String> dataRecord = new ArrayList<String>();
            dataRecord.add(exportFile.getKeywordA());
            dataRecord.add(exportFile.getKeywordB());
            dataRecord.add(exportFile.getScore().toString());
            dataRecord.add(exportFile.getLevenstein().toString());

            csvPrinter.printRecord(dataRecord);
            csvPrinter.flush();

//            System.out.println(" ------- dataRecord   " + JSON.toJSONString(dataRecord));
//            if (fileName.equals(ALL_EXPORT_FILE_PATH)) {
//                allCsvPrinter.printRecord(dataRecord);
//                allCsvPrinter.flush();
////                System.out.println("print time " +i);
//                i++;
//            } else if (fileName.equals(OVER_EXPORT_FILE_PATH)) {
//                overCsvPrinter.printRecord(dataRecord);
//                overCsvPrinter.flush();
//            } else if (fileName.equals(OVER_INDEX_5_EXPORT_FILE_PATH)) {
//                overIndex5CsvPrinter.printRecord(dataRecord);
//                overIndex5CsvPrinter.flush();
//            }

        }


    }

}
