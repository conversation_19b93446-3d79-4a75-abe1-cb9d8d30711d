package seoclarity.backend.summary.onetime;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import okhttp3.internal.http2.StreamResetException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

@CommonsLog
public class NozzleParserAPICommand implements Callable<Long> {

    public static final String ALI_ACCESS_KEY = "LTAI4GGYqGWn99UKbJsBgyQ9";
    public static final String ALI_SECRET_KEY = "******************************";
    private final static String s3BucketName_daily = "daily-html-virginia";
    public static final String S3_FOLDER = "20231112/1-1/";
    public String zipFolderPath;
    public String unzipFolderPath;
    public String requestFolderPath;
    public String responseFolderPath;
    public String failedFolderPath;
    private static final String POST_URL = "https://api.nozzle.app/extractor/clarity/serp";
    private static final String REQUEST_TOKEN = "Token 5U4ibs4SWOd8if43ld5H";
    private static final int RETRY_COUNT = 3;
    private List<Long> rcKwIdList;
    private String device;

    AWSCredentials aliCredentials;
    AmazonS3 aliS3Client;

    public NozzleParserAPICommand(List<Long> rcKwIdList, String device, String zipFolderPath,String unzipFolderPath,
                                  String requestFolderPath, String responseFolderPath, String failedFolderPath) {
        this.rcKwIdList = rcKwIdList;
        this.device = device;
        this.zipFolderPath = zipFolderPath;
        this.unzipFolderPath = unzipFolderPath;
        this.requestFolderPath = requestFolderPath;
        this.responseFolderPath = responseFolderPath;
        this.failedFolderPath = failedFolderPath;

        aliCredentials = new BasicAWSCredentials(ALI_ACCESS_KEY, ALI_SECRET_KEY);
        aliS3Client = AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(aliCredentials))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration("oss-us-east-1.aliyuncs.com", "oss")).build();
    }

    @Override
    public Long call() throws Exception {
        long startTime = System.currentTimeMillis();
        log.info("Thread Start :" + Thread.currentThread().getName() + ",rcKwIdList:" + rcKwIdList.size());
        int index = 0;

        Thread.sleep(1000);

        File failedFile = new File(failedFolderPath + Thread.currentThread().getId() + ".txt");
        List<Long> failedRcIdList = new ArrayList<>();
        for (Long rcKwId : rcKwIdList) {
//            log.info("STrcKwId:" + rcKwId);

//            File unzipFile1120 = new File("/home/<USER>/20231112/1-1/unzipFile1120/" + rcKwId + ".txt");
//            if (unzipFile1120.exists()) {
//                log.info("unzipFile1120Exist:" + rcKwId);
//                continue;
//            }
//
//            File unzipFileExist = new File("/home/<USER>/20231112/1-1/unzipFile/" + rcKwId + ".txt");
//            if (unzipFileExist.exists()) {
//                log.info("unzipFileExist:" + rcKwId);
//                continue;
//            }
//
//            File unzipFileDeviceExist = new File(unzipFolderPath + rcKwId + ".txt");
//            if (unzipFileDeviceExist.exists()) {
//                log.info("unzipFileDeviceExist:" + rcKwId);
//                continue;
//            }

            File responseFile;
            if(device.equalsIgnoreCase("d")){
                responseFile = new File("/home/<USER>/20231112/1-1/response/" + rcKwId + ".txt");
            }else {
                responseFile = new File("/home/<USER>/20231112/1-1/" + device + "/response/" + rcKwId + ".txt");
            }
            if (responseFile.exists()) {
                log.info("skipResponse:" + rcKwId);
                continue;
            }

            File unzipFile = new File(unzipFolderPath + rcKwId + ".txt");
            if(!unzipFile.exists() && device.equalsIgnoreCase("d")){
                unzipFile = new File("/home/<USER>/20231112/1-1/unzipFile/" + rcKwId + ".txt");
                if(!unzipFile.exists()){
                    unzipFile = new File("/home/<USER>/20231112/1-1/unzipFile1120/" + rcKwId + ".txt");
                    if(!unzipFile.exists()){
                        //OSS下载
                        int downloadRetry = 0;
                        String downloadFilePath = "";
                        while (true) {
                            Thread.sleep(100);
                            downloadFilePath = downloadHtmlFromOSS(rcKwId);
                            log.info("============downloadHtmlFilePath:" + downloadFilePath);
                            if (StringUtils.isNotBlank(downloadFilePath)) {
                                break;
                            }
                            if (StringUtils.isBlank(downloadFilePath) && downloadRetry <= RETRY_COUNT) {
                                downloadRetry++;
                            } else {
                                log.info("====downloadFailed:" + rcKwId);
                                break;
                            }
                        }

                        if (StringUtils.isBlank(downloadFilePath)) {
                            continue;
                        }

                        //解压
                        unzipFile = unGzipFile(downloadFilePath);
                        log.info("============unzipFile:" + unzipFile.getAbsolutePath());
                    }
                }
            }else {
                //OSS下载
                int downloadRetry = 0;
                String downloadFilePath = "";
                while (true) {
                    Thread.sleep(100);
                    downloadFilePath = downloadHtmlFromOSS(rcKwId);
                    log.info("============downloadHtmlFilePath:" + downloadFilePath);
                    if (StringUtils.isNotBlank(downloadFilePath)) {
                        break;
                    }
                    if (StringUtils.isBlank(downloadFilePath) && downloadRetry <= RETRY_COUNT) {
                        downloadRetry++;
                    } else {
                        log.info("====downloadFailed:" + rcKwId);
                        break;
                    }
                }

                if (StringUtils.isBlank(downloadFilePath)) {
                    continue;
                }

                //解压
                unzipFile = unGzipFile(downloadFilePath);
                log.info("============unzipFile:" + unzipFile.getAbsolutePath());
            }

            index++;
//            File unzipFile = new File(unzipFolderPath + rcKwId + ".txt");
            //拼装请求
            String wholeHtml = "";
            try {
                List<String> lines = FileUtils.readLines(unzipFile, "utf-8");
                StringBuffer stringBuffer = new StringBuffer();
                for (String line : lines) {
                    stringBuffer.append(line);
                }
                wholeHtml = stringBuffer.toString();
                wholeHtml = wholeHtml.substring(0,wholeHtml.lastIndexOf("</html>") + 7);
//                log.info("===wholeHtml:" + wholeHtml);
//            String formatHtml = wholeHtml.replaceAll("\"", "\\\\\"");
//                String formatHtml = wholeHtml.replaceAll("\"", "\\\"");
//                log.info("===formatHtml:" + formatHtml);

            } catch (Exception e) {
                e.printStackTrace();
            }
//
//            File responseFile = new File(RESPONSE_FOLDER + rcKwId + ".txt");
            int retry = 0;
            String response = null;
            try {
                while (true) {
                    if (response == null && retry <= RETRY_COUNT) {
                        long stTime = System.currentTimeMillis();
                        response = postUrl(device, wholeHtml, rcKwId);
                        log.info("=====耗时:" + (System.currentTimeMillis() - stTime) / 1000 + "s");
                        if (response != null) {
//                            System.out.println("===response：" + response);
                            System.out.println("===Suc:" + rcKwId);
                            List<String> responseList = new ArrayList<>();
                            responseList.add(response);
                            FileUtils.writeLines(responseFile, responseList, true);
                        } else {
                            log.error("===post failed:" + retry + "," + rcKwId);
                            Thread.sleep(1000);
                        }
                        retry++;
                    } else if (response == null && retry > RETRY_COUNT) {
                        log.info("====OVERRETRY:" + rcKwId);
                        failedRcIdList.add(rcKwId);
                        FileUtils.writeLines(failedFile, failedRcIdList, true);
                        failedRcIdList = new ArrayList<>();
                        break;
                    } else {
                        break;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.info("====errorPostRcId: " + rcKwId);
                break;
            }

        }
        log.info("Thread End :" + Thread.currentThread().getName() + " total time :" + (System.currentTimeMillis() - startTime) / 1000);

        return null;
    }

    private String downloadHtmlFromOSS(long rcKwId) {

        S3Object o = aliS3Client.getObject(s3BucketName_daily, S3_FOLDER + rcKwId);
        String filePath = "";
        try {
            S3ObjectInputStream s3is = o.getObjectContent();
            File localFile = new File(zipFolderPath + rcKwId + ".gz");
            FileOutputStream fos = new FileOutputStream(localFile);
            byte[] read_buf = new byte[1024];
            int read_len = 0;
            while ((read_len = s3is.read(read_buf)) > 0) {
                fos.write(read_buf, 0, read_len);
            }
            s3is.close();
            fos.close();
            log.info("dlLen:" + localFile.length());
            filePath = localFile.getAbsolutePath();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("downloadHtmlError:" + rcKwId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        System.out.println("DownloadFromS3 Completed->" + S3_FOLDER + rcKwId);
        return filePath;
    }

    private File unGzipFile(String filePath) {
        File localFile = new File(filePath);
        File outputFile = new File(unzipFolderPath + localFile.getName().replaceAll(".gz", ".txt"));
        try {
            GZipUtil.unGzipBigFile(FileUtils.readFileToByteArray(localFile), outputFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return outputFile;
    }

    private String postUrl(String device, String html, Long rcId) throws Exception {

        if (StringUtils.isBlank(html)) {
            log.info("====html empty, don't post.");
            return null;
        }

        log.info("===postUrl:" + ",device:" + device + ",htmlLength:" + html.length());

        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put("languageCode", "en");
        parameters.put("countryCode", "us");
        parameters.put("device", device);
        parameters.put("pulledAt", System.currentTimeMillis() / 1000 + "");
        parameters.put("RawSERP", html);

        String requestBody = JSON.toJSONString(parameters);
        File requestFile = new File(requestFolderPath + rcId + ".txt");
        if (requestFile.exists()) {
            requestFile.delete();
        }
        List<String> line = new ArrayList<>();
        line.add(requestBody);
        FileUtils.writeLines(requestFile, line, true);

        List<Protocol> protocols = new ArrayList<Protocol>()
        {{
            add(Protocol.HTTP_1_1); // <-- The only protocol used
            //add(Protocol.HTTP_2);
        }};

        OkHttpClient client = new OkHttpClient().newBuilder()
                .callTimeout(5, TimeUnit.MINUTES)
                .connectTimeout(10, TimeUnit.MINUTES)
                .readTimeout(10, TimeUnit.MINUTES)
                .writeTimeout(10, TimeUnit.MINUTES)
                .retryOnConnectionFailure(true)
                .protocols(protocols)
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        Request request = new Request.Builder()
                .url(POST_URL)
                .header("Authorization", REQUEST_TOKEN)
                .header("Accept", "application/json")
                .header("Content-Type", "application/json")
                .post(RequestBody.create(mediaType, JSON.toJSONString(parameters)))
                .build();

        Call call = client.newCall(request);
        try {
            Response response = call.execute();
            //判断是否成功
            if (response.isSuccessful()) {
                return response.body().string();
            } else {
                log.error("post请求失败" + response.body().string());
                String errorMsg = "post api error:" + ",device:" + device + ",rcId:" + rcId + ",htmlL:" + html.length();
                log.error(errorMsg);
                return null;
            }

        }catch (StreamResetException streamResetException){
            log.error("streamResetException:" + streamResetException.getMessage(), streamResetException);
            Thread.sleep(1000 * 60);
            return null;
        } catch (Exception e) {
            System.out.println("post异常");
            String errorMsg = "post api error:" + ",device:" + device + ",rcId:" + rcId + ",htmlL:" + html.length();
            log.error(e.getMessage(), e);
            Thread.sleep(1000 * 60);
            return null;
        }

    }


}
