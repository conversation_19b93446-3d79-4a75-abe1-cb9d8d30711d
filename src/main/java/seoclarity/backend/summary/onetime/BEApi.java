package seoclarity.backend.summary.onetime;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.TimeUnit;


@CommonsLog
public class BEApi {

    private static final String URL = "https://api.brightedge.com/3.0/query/149851";
    private static final String USER_NAME = " <EMAIL>";
    private static final String PASSWORD = "StaySafeBE.2024";

    private static String extractFolder = "/home/<USER>/source/jason/extract/BEApi/12710";
//    private static String extractFolder = "D:\\workspace\\upload\\rank\\12709\\BEAPI";
    private static int startWeek = 0;
    private static int endWeek = 0;

    public static void main(String[] args) {


//        String responseBody = "{\"count\": 15, \"total\": 10355, \"values\": [{\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"benefits of mobile apps for customers\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"best backend as a service\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"best backend database for mobile app\", \"rank\": 19, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"https://aws.amazon.com/answers/mobile/aws-mobile-app-backend/\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"best mobile backend as a service\", \"rank\": 29, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"https://aws.amazon.com/amplify/\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"free backend as a service\", \"rank\": 50, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"https://aws.amazon.com/marketplace/pp/B01A9HJZ3Q\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"native mobile app architecture\", \"rank\": 20, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"https://aws.amazon.com/mobile/mobile-application-development/\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"app backend\", \"rank\": 3, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"https://aws.amazon.com/answers/mobile/aws-mobile-app-backend/\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"baas architecture\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"backend\", \"rank\": 92, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"https://aws.amazon.com/getting-started/projects/build-serverless-web-app-lambda-apigateway-s3-dynamodb-cognito/module-3/\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"firebase\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"google firebase\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"javascript app development\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"javascript application development\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"javascript libraries\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}, {\"search_engine\": \"Google United States (US) (D)\", \"category\": \"Regular Web Listing\", \"keyword\": \"javascript mobile app development\", \"rank\": 101, \"time\": 201852, \"serp_type\": 1, \"page_url\": \"\"}], \"offset\": 10340}";
//        ResponseEntity responseEntity = new Gson().fromJson(responseBody, ResponseEntity.class);
//        log.info("==== " + JSON.toJSONString(responseEntity));
//        test(201852,10340);

        int startWeek = 202246;
        int endWeek = 202252;

//        if(args!=null && args.length >= 1){
//            startWeek = Integer.parseInt(args[0]);
//            endWeek = Integer.parseInt(args[1]);
//        }

        for(int i=startWeek; i<= endWeek; i++){
            log.info("==============start to process week " + i);
            processByWeek(i);
        }

        startWeek = 202301;
        endWeek = 202351;
        for(int i=startWeek; i<= endWeek; i++){
            log.info("==============start to process week " + i);
            processByWeek(i);
        }

    }


    private static void processByWeek(int week) {

        String folderPath = extractFolder + File.separator + week;
        File folder = new File(folderPath);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        if(folder.exists()){
            folder.deleteOnExit();
            folder.mkdir();
        }

        Map<String, List<ResponseValue>> engineMap = new HashMap<>();

        File outFile = new File(folderPath + File.separator + week + ".txt");

        int offset = 0;
        while (true) {
            log.info("===getDataFromApi week: " + week + ",offset: " + offset);
            try {
                Thread.sleep(3 * 1000);
                List<ResponseValue> responseValueList = getDataFromApi(week, offset);
                if (CollectionUtils.isEmpty(responseValueList)) {
                    break;
                }

                List<String> extractList = new ArrayList<>();

                for (ResponseValue responseValue : responseValueList) {
                    extractList.add(new Gson().toJson(responseValue));
//                    String searchEngine = responseValue.getSearch_engine();
//                    if (engineMap.get(searchEngine) == null) {
//
//                        List<ResponseValue> list = new ArrayList<>();
//                        list.add(responseValue);
//                        engineMap.put(searchEngine, list);
//                    } else {
//                        List<ResponseValue> list = engineMap.get(searchEngine);
//                        list.add(responseValue);
//                        engineMap.put(searchEngine, list);
//                    }
                }
                FileUtils.writeLines(outFile, extractList, true);
                offset += 100;

            } catch (Exception e) {
                e.printStackTrace();
                log.error("===error getDataFromApi week: " + week + ",offset: " + offset);
                try {
                    Thread.sleep(30 * 1000);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                continue;
            }

        }


//        for (String engine : engineMap.keySet()) {
//            try {
//                File outFile = new File(folderPath + File.separator + engine.trim().replaceAll("\\s*", "") + "_" + week + ".txt");
//                List<String> extractList = new ArrayList<>();
//                List<ResponseValue> margedResponseValueList = engineMap.get(engine);
//                log.info("=====" + engine + " value size : " + margedResponseValueList.size());
//                for (ResponseValue margedResponseValue : margedResponseValueList) {
//                    extractList.add(new Gson().toJson(margedResponseValue));
//                }
//                FileUtils.writeLines(outFile, extractList, true);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }


    }


    private static List<ResponseValue> getDataFromApi(int week, int offset) {

        List<ResponseValue> responseValueList = new ArrayList<>();

        int retryCount = 3;
        for (int i = 0; i < retryCount; i++) {

            try {

                String authString = USER_NAME + ":" + PASSWORD;

//                String encodeAuth = Base64.getEncoder().encodeToString(authString.getBytes());

//                String authStringEnc = new String(authEncBytes);

                String encodeAuth = "****************************************************************";
                log.info("===authStringEnc: " + encodeAuth);

                String requestBody = "query={\"dataset\":\"keyword\",\"dimension\":" +
                        "[\"keyword\", \"time\",\"search_engine\",\"page_url\",\"serp_type\", \"category\"]," +
                        "\"measures\":[\"blended_rank\",\"rank\"],\"dimensionOptions\":{\"time\":\"weekly\"}," +
                        "\"filter\":[[\"time\",\"eq\",\"" + week + "\"]],\"offset\":\"" + offset + "\"}";

                log.info("===requestBody: " + requestBody);

//                OkHttpClient client = new OkHttpClient();

                OkHttpClient client = new OkHttpClient().newBuilder().callTimeout(5, TimeUnit.MINUTES)
//            OkHttpClient client = new OkHttpClient().newBuilder().proxy(proxy).callTimeout(5, TimeUnit.MINUTES)
                        .connectTimeout(10, TimeUnit.MINUTES)
                        .readTimeout(10, TimeUnit.MINUTES)
                        .writeTimeout(10, TimeUnit.MINUTES)
                        .build();

                MediaType mediaType = MediaType.parse("text/plain");
                RequestBody body = RequestBody.create(mediaType, requestBody);
                Request request = new Request.Builder()
                        .url(URL)
                        .post(body)
                        .addHeader("authorization", "Basic " + encodeAuth)
                        .addHeader("content-type", "text/plain")
                        .build();

                Response response = client.newCall(request).execute();
//                log.info("===response : " + response);
                String responseBody = response.body().string();
//                log.info("===response : " + responseBody);

                ResponseEntity responseEntity = new Gson().fromJson(responseBody, ResponseEntity.class);
                responseValueList = responseEntity.getValues();
                if (CollectionUtils.isEmpty(responseValueList)) {
                    log.info("===response empty , exit weekly: " + week + ",offset : " + offset);
                    return null;
                }
                break;
            } catch (Exception e) {
                e.printStackTrace();
            }

            log.error("===request error week: " + week + ",offset: " + offset);

            try {
                Thread.sleep(60 * 1000);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

        }
        return responseValueList;
    }

    public class ResponseEntity {

        private Integer count;
        private Integer total;
        private Integer offset;
        private List<ResponseValue> values;

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }

        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }

        public Integer getOffset() {
            return offset;
        }

        public void setOffset(Integer offset) {
            this.offset = offset;
        }

        public List<ResponseValue> getValues() {
            return values;
        }

        public void setValues(List<ResponseValue> values) {
            this.values = values;
        }
    }

    public class ResponseValue {

        private String search_engine;
        private String category;
        private String keyword;
        private Integer rank;
        private Integer blended_rank;
        private Integer time;
        private Integer serp_type;
        private String page_url;

        public String getSearch_engine() {
            return search_engine;
        }

        public void setSearch_engine(String search_engine) {
            this.search_engine = search_engine;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getKeyword() {
            return keyword;
        }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Integer getTime() {
            return time;
        }

        public void setTime(Integer time) {
            this.time = time;
        }

        public Integer getSerp_type() {
            return serp_type;
        }

        public void setSerp_type(Integer serp_type) {
            this.serp_type = serp_type;
        }

        public String getPage_url() {
            return page_url;
        }

        public void setPage_url(String page_url) {
            this.page_url = page_url;
        }

        public Integer getBlended_rank() {
            return blended_rank;
        }

        public void setBlended_rank(Integer blended_rank) {
            this.blended_rank = blended_rank;
        }
    }

}
