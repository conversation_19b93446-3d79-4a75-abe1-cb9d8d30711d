package seoclarity.backend.summary.onetime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.onetime.RefreshLastFinalDate" -Dexec.args=""
public class RefreshLastFinalDate {
	
    private GscBaseDao gscBaseDao;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;

	public RefreshLastFinalDate() {
		gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
	}

	public static void main(String[] args) {
		RefreshLastFinalDate refreshLastFinalDate = new RefreshLastFinalDate();
		refreshLastFinalDate.process();
	}
	
	private void process() {
		
		List<GscEntity> gscdateList = gscBaseDao.getLatestFinalDate();
		Integer ownDomainId = 0;
		String lastFinalDate = "";
		
		Map<Integer, String> oidDateMap = new HashMap<Integer, String>();
		for(GscEntity gscEntity : gscdateList) {
			ownDomainId = gscEntity.getOwnDomainId();
			lastFinalDate = gscEntity.getLogDateStr();
			
			System.out.println("==== updating OID:" + ownDomainId + ", lastFinalDate:" + lastFinalDate);
			oidDateMap.put(ownDomainId, lastFinalDate);
		}

		ownDomainSettingEntityDAO.updateGscLatestFinalDateByOid(oidDateMap);
		
		System.out.println("Updated!");
		
	}

}
