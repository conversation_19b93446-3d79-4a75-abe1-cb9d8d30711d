package seoclarity.backend.summary.onetime;

import com.amazonaws.services.sqs.AmazonSQS;
import seoclarity.backend.entity.clickhouse.pagespeed.PsSqsMessageVO;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.summary.SendMissingPageSpeedCommand;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Semaphore;

public class PageSpeedSenderOneTime {

    private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

    private static String sqsMobileUrlName = "PAGE_SPEED_MOBILE_MISSING_URL_V5";

    public PageSpeedSenderOneTime(){


    }

    public static void main(String[] args) throws Exception {

        PageSpeedSenderOneTime pageSpeedSenderOneTime = new PageSpeedSenderOneTime();
        pageSpeedSenderOneTime.process();

    }


    private void process(){

        prepareSendToSqs("PAGE_SPEED_DESKTOP_URL_V5", null);

    }


    private void prepareSendToSqs(String sqsUrlName, List<PsSqsMessageVO> sendTargetUrlList) {

        threadPool.init();
        CommonUtils.initThreads(50);

        try {
            //send to sqs
            AmazonSQS amazonSQS = null;
            String queryUrl = null;
            Boolean isDeleteQueue = null;

            amazonSQS = SQSUtils.getAmazonSQS();

            System.out.println("###Will start to send SQS  queryUrl:" + queryUrl
                    + ", currentProcessDate:" + CacheModleFactory.getInstance().getWorkDate()
                    + ", sqsName:" + sqsUrlName
                    + ", deleteAllQueue:" + isDeleteQueue + ", 2 Minitues to choose continue...");


//            queryUrl = SQSUtils.createQueue(sqsUrlName, amazonSQS);

            String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
            ipAddress = "*******";
            while (ipAddress == null) {
                try {
                    Thread.sleep(1000);
                    ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                continue;
            }
            processSendCommand(amazonSQS, queryUrl, sendTargetUrlList, ipAddress);

            do {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } while (threadPool.getThreadPool().getActiveCount() > 0);


        } catch (Exception e) {
            e.printStackTrace();
        }

        threadPool.destroy();
    }

    private void processSendCommand(AmazonSQS amazonSQS, String queryUrl, List<PsSqsMessageVO> urlList,
                                    String ip) {
        SendOneTimePageSpeedCommand cmd = new SendOneTimePageSpeedCommand(amazonSQS, queryUrl, urlList, ip);
        cmd.setStatus(true);
        try {
            threadPool.execute(cmd);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
