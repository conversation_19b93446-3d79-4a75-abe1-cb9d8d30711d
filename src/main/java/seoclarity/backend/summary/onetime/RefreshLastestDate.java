package seoclarity.backend.summary.onetime;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainTrackingEntityDAO;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainTrackingEntity;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.onetime.RefreshLastestDate" -Dexec.args=""
public class RefreshLastestDate {
	
    private GscBaseDao gscBaseDao;
//    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainTrackingEntityDAO ownDomainTrackingEntityDAO;
    
	public RefreshLastestDate() {
		gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
//		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		ownDomainTrackingEntityDAO = SpringBeanFactory.getBean("ownDomainTrackingEntityDAO");
	}

	public static void main(String[] args) {
		RefreshLastestDate refreshLastFinalDate = new RefreshLastestDate();
		
		refreshLastFinalDate.init();
		
		refreshLastFinalDate.processForLastFreshDate();
		refreshLastFinalDate.processForLastFinalDate();
		refreshLastFinalDate.processForSharedProfile();
	}
	
	
	private static Map<Integer, Date> latestFinalMap = new HashMap<>();
	private static Map<Integer, Date> latestFreshMap = new HashMap<>();
	private static List<Integer> gscDomainIdList = new ArrayList<>();
	
	private void init() {
		List<OwnDomainEntity> domainAndProfileList = ownDomainEntityDAO.getGSCProcessingDomain(null, 0);
        Map<Integer, OwnDomainEntity> domainMap = new HashMap<>();
        domainMap = domainAndProfileList.stream().collect(Collectors.toMap(OwnDomainEntity::getId, Function.identity()));
		
        Map<Integer, OwnDomainTrackingEntity> cacheMap = new HashMap<>();
//		List<OwnDomainSettingEntity> domainList = ownDomainSettingEntityDAO.getAllGscLatestDate();
//		cacheMap = domainList.stream().collect(Collectors.toMap(OwnDomainSettingEntity::getOwnDomainId, Function.identity()));
		
		List<OwnDomainTrackingEntity> ownDomainTrackingList = ownDomainTrackingEntityDAO.getAllActiveList();
		cacheMap = ownDomainTrackingList.stream().collect(Collectors.toMap(OwnDomainTrackingEntity::getOwnDomainId, Function.identity()));
		List<Integer> trackingExistDomainIdList = ownDomainTrackingList.stream().map(o -> o.getOwnDomainId()).collect(Collectors.toList());
		
		List<Integer> emptyDomainList = new ArrayList<Integer>();
		
		for(Integer domainId: trackingExistDomainIdList) {
			if (!cacheMap.keySet().contains(domainId)) {
				//need to insert at first
				emptyDomainList.add(domainId);
			}
		}
		
		if (CollectionUtils.isNotEmpty(trackingExistDomainIdList)) {
			System.out.println("Insert new tracking, size:" + trackingExistDomainIdList.size());
			ownDomainTrackingEntityDAO.batchinsertIgnore(trackingExistDomainIdList);
		}
		
		
		if (CollectionUtils.isNotEmpty(ownDomainTrackingList)) {
			System.out.println(" ownDomainTrackingList size:" + ownDomainTrackingList.size());
		} else {
			System.out.println(" ownDomainTrackingList is empty!");
		}
		
		for(Integer ownDomainId : domainMap.keySet()) {
			
			if (cacheMap.keySet().contains(ownDomainId)) {
				latestFinalMap.put(ownDomainId, cacheMap.get(ownDomainId).getGscLatestFinalDate());
				latestFreshMap.put(ownDomainId, cacheMap.get(ownDomainId).getGscLatestFreshDate());
			} else {
				latestFinalMap.put(ownDomainId, null);
				latestFreshMap.put(ownDomainId, null);
			}
			
			gscDomainIdList.add(ownDomainId);
		}
		
		System.out.println("Cache domain GSC latest data size:" + ownDomainTrackingList.size());
	}
	
	private void processForLastFreshDate() {
		
		try {
			//---------------------------------------update last fresh date
			List<GscEntity> gscdateList = gscBaseDao.getLatestDate();
			Integer ownDomainId = 0;
			String lastFreshDateStr = "";
			Date newLastFreshDate;
			Date lastFreshDateInDb;
			
			Map<Integer, String> oidDateMap = new HashMap<Integer, String>();
			for(GscEntity gscEntity : gscdateList) {
				
				ownDomainId = gscEntity.getOwnDomainId();
				if (!gscDomainIdList.contains(ownDomainId)) {
					continue;
				}
				
				lastFreshDateStr = gscEntity.getLogDateStr();
				newLastFreshDate = FormatUtils.toDate(lastFreshDateStr, "yyyy-MM-dd");
				lastFreshDateInDb = latestFreshMap.get(ownDomainId);
				
//				System.out.println("= updating OID:" + ownDomainId + ", FRdb:" + lastFreshDateStr 
//						+ " ==> FRnew:" + FormatUtils.formatDate(lastFreshDateInDb, "yyyy-MM-dd"));
				
				if (lastFreshDateInDb == null || newLastFreshDate.getTime() > lastFreshDateInDb.getTime()) {
					System.out.println("==== updating OID:" + ownDomainId + ", FRdb:" + lastFreshDateStr 
							+ " ==> FRnew:" + FormatUtils.formatDate(lastFreshDateInDb, "yyyy-MM-dd"));
					oidDateMap.put(ownDomainId, lastFreshDateStr);
				}
			}

			if (oidDateMap.size() > 0) {
//				ownDomainSettingEntityDAO.updateGscLatestDateByOid(oidDateMap);
				try {
					ownDomainTrackingEntityDAO.updateGscLatestDateByOid(oidDateMap);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			System.out.println("update last fresh date ! size:" + oidDateMap.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	private void processForLastFinalDate() {
		
		try {
			//---------------------------------------update last final date
			List<GscEntity> gscdateList = gscBaseDao.getLatestFinalDate();
			Integer ownDomainId = 0;
			String lastFinalDateStr = "";
			Date newLastFinalDate;
			Date lastFinalDateInDb;
			
			Map<Integer, String> oidDateMap = new HashMap<Integer, String>();
			for(GscEntity gscEntity : gscdateList) {
				ownDomainId = gscEntity.getOwnDomainId();
				if (!gscDomainIdList.contains(ownDomainId)) {
					continue;
				}
				
				lastFinalDateStr = gscEntity.getLogDateStr();
				newLastFinalDate = FormatUtils.toDate(lastFinalDateStr, "yyyy-MM-dd");
				lastFinalDateInDb = latestFinalMap.get(ownDomainId);
				
//				System.out.println("= updating OID:" + ownDomainId + ", FNdb:" + lastFinalDateStr 
//						+ " ==> FNnew:" + FormatUtils.formatDate(lastFinalDateInDb, "yyyy-MM-dd"));
				
				if (lastFinalDateInDb == null || newLastFinalDate.getTime() > lastFinalDateInDb.getTime()) {
					System.out.println("==== updating OID:" + ownDomainId + ", FNdb:" + lastFinalDateStr 
							+ " ==> FNnew:" + FormatUtils.formatDate(lastFinalDateInDb, "yyyy-MM-dd"));
					oidDateMap.put(ownDomainId, lastFinalDateStr);
				}
			}

			if (oidDateMap.size() > 0) {
//				ownDomainSettingEntityDAO.updateGscLatestFinalDateByOid(oidDateMap);
				try {
					ownDomainTrackingEntityDAO.updateGscLatestFinalDateByOid(oidDateMap);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			System.out.println("update last final date ! size:" + oidDateMap.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		
	}
	
	private void processForSharedProfile() {
		
		try {
			List<OwnDomainTrackingEntity> gscdateList = ownDomainTrackingEntityDAO.getSharedProfileLatestDate();
			Integer ownDomainId = 0;
			String lastFinalDateStr = "";
			String lastFreshDateStr = "";
			
			Date newLastFinalDate;
			
			Date lastFinalDateInDb;
			Date newLastFreshDate;
			Date lastFreshDateInDb;
			
			
			Map<Integer, String[]> oidDateMap = new HashMap<Integer, String[]>();
			for(OwnDomainTrackingEntity ownDomainTrackingEntity : gscdateList) {
				ownDomainId = ownDomainTrackingEntity.getOwnDomainId();
//				if (!gscDomainIdList.contains(ownDomainId)) {
//					continue;
//				}
				
				lastFinalDateStr = FormatUtils.formatDate(ownDomainTrackingEntity.getGscLatestFinalDate(), "yyyy-MM-dd");
				lastFreshDateStr = FormatUtils.formatDate(ownDomainTrackingEntity.getGscLatestFreshDate(), "yyyy-MM-dd");
				
				newLastFinalDate = FormatUtils.toDate(lastFinalDateStr, "yyyy-MM-dd");
				lastFinalDateInDb = latestFinalMap.get(ownDomainId);
				
				newLastFreshDate = FormatUtils.toDate(lastFreshDateStr, "yyyy-MM-dd");
				lastFreshDateInDb = latestFreshMap.get(ownDomainId);
				
//				System.out.println("= updating OID:" + ownDomainId 
//						+ ", FNdb:" + lastFinalDateStr + " ==> FNnew:" + FormatUtils.formatDate(lastFinalDateInDb, "yyyy-MM-dd")  
//						+ ", FRdb:" + lastFreshDateStr + " ==> FRnew:" + FormatUtils.formatDate(lastFreshDateInDb, "yyyy-MM-dd"));
				
				
				if (lastFinalDateInDb == null || newLastFinalDate.getTime() > lastFinalDateInDb.getTime() || 
						lastFreshDateInDb == null || newLastFreshDate.getTime() > lastFreshDateInDb.getTime()) {
					System.out.println("==== updating OID:" + ownDomainId 
							+ ", FNdb:" + lastFinalDateStr + " ==> FNnew:" + FormatUtils.formatDate(lastFinalDateInDb, "yyyy-MM-dd")  
							+ ", FRdb:" + lastFreshDateStr + " ==> FRnew:" + FormatUtils.formatDate(lastFreshDateInDb, "yyyy-MM-dd"));
					
					oidDateMap.put(ownDomainId, new String[] {lastFreshDateStr, lastFinalDateStr});
				}
			}

			if (oidDateMap.size() > 0) {
//				ownDomainSettingEntityDAO.updateGscLatestAndFinalDateByOid(oidDateMap);
				try {
					ownDomainTrackingEntityDAO.updateGscLatestAndFinalDateByOid(oidDateMap);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			System.out.println("Shared profile Updated! size:" + oidDateMap.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}

}
