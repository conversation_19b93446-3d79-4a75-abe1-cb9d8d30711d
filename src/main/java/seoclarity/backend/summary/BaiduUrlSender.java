package seoclarity.backend.summary;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.amazonaws.services.sqs.AmazonSQS;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.BaiduUrlSender" -Dexec.cleanupDaemonThreads=false -Dexec.args="20211026 20211031" > baidu_url.log &
 */
public class BaiduUrlSender {

    public static void main(String[] args) {

        Date startDate = DateUtil.yesterday();
        Date endDate = DateUtil.yesterday();
        if(args != null && args.length >= 2) {
            startDate = DateUtil.parse(args[0], "yyyyMMdd");
            endDate = DateUtil.parse(args[1], "yyyyMMdd");
        }
        AmazonSQS amazonSQS = SQSUtils.getAmazonSQS(AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextSQSAccessKey(), AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey());
        String queryUrl = SQSUtils.createQueue("LEO_GEO_TEST_CO", amazonSQS);

        ThreadPoolExecutor executorService = new ThreadPoolExecutor(5, 5, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

        File[] files = FileUtil.ls("/disk1/scribeKeyword/monthly_baidu_commoncrawl_keywordRank_150_6/");
        while (startDate.compareTo(endDate) <= 0) {
            String dateStr = DateUtil.format(startDate, "yyyy-MM-dd");
            for (File file : files) {
                if(StrUtil.contains(file.getName(), dateStr)) {
//                    System.out.println("Process file : "+file.getName());
                    executorService.execute(new BaiduUrlThread(file, amazonSQS, queryUrl));
                }
            }
            startDate = DateUtil.offsetDay(startDate, 1);
        }


        while (executorService.getActiveCount() > 0) {
            System.out.println("Threads alive..");
            ThreadUtil.sleep(60000);
            continue;
        }

        executorService.shutdownNow();
        System.exit(0);
    }

    static class BaiduUrlThread implements Runnable {

        private File file;
        private AmazonSQS amazonSQS;
        private String queryUrl;

        public BaiduUrlThread(File file, AmazonSQS amazonSQS, String queryUrl) {
            this.file = file;
            this.amazonSQS = amazonSQS;
            this.queryUrl = queryUrl;
        }

        @Override
        public void run() {
            List<String> keywordList = FileUtil.readLines(file, StandardCharsets.UTF_8);
            if(CollectionUtil.isEmpty(keywordList)) {
                return;
            }
            List<List<String>> messages = CollectionUtil.split(keywordList, 5);
            for (List<String> message : messages) {
                while (true) {
                    try {
                        SQSUtils.sendMessagetoQueueByBatch(amazonSQS, queryUrl, message);
                        break;
                    } catch (Exception e) {
                        e.printStackTrace();
                        ThreadUtil.sleep(5000);
                    }
                }

            }
            System.out.println("finished : "+file.getName());
            ThreadUtil.sleep(10000);
        }
    }

}
