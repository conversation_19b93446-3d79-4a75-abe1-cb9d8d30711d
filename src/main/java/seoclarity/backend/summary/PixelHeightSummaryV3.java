package seoclarity.backend.summary;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.gson.Gson;

import cn.hutool.core.util.ReUtil;
import lombok.extern.apachecommons.CommonsLog;
import seoclarity.backend.dao.actoniamonitor.VsUploadMonitorDAO;
import seoclarity.backend.dao.clickhouse.pixelheight.PixelHeightRankingDao;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.SQSUtils;

/**
 * <AUTHOR>
 * @create 2018-09-21 17:53
 * vim ./src/main/java/seoclarity/backend/summary/PixelHeightSummaryV3.java
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.PixelHeightSummaryV3" -Dexec.cleanupDaemonThreads=false -Dexec.args="false"
 **/
@CommonsLog
public class PixelHeightSummaryV3  {

    private String queryUrl;
	private AmazonSQS amazonSQS;
	public static int VisibilityTimeout = 60 * 30;
	
//	private static String baseLocation;
	private static String processDateString;
	private static int threadCount = 1;
	private ExecutorService newFixedThreadPool =
            new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), new BackendThreadFactory());


    private PixelHeightRankingDao pixelHeightRankingDao;
    private VsUploadMonitorDAO vsUploadMonitorDAO;
    public static Set<String> processedSet = new HashSet<>();

    public PixelHeightSummaryV3() {
        pixelHeightRankingDao = SpringBeanFactory.getBean("pixelHeightRankingDao");
        vsUploadMonitorDAO = SpringBeanFactory.getBean("vsUploadMonitorDAO");
    }

    private static boolean mobile;

    public static void main(String[] args) throws Exception {
        mobile = BooleanUtils.toBoolean(args[0]);
//        baseLocation = args[1];
        threadCount = 1;
        
        PixelHeightSummaryV3 pixelHeightSummary = new PixelHeightSummaryV3();
        pixelHeightSummary.initAmazonSQS();
        pixelHeightSummary.processNormalQueue();
    }
    
	private void initAmazonSQS() throws Exception {

		try {

			String accessKeyId = "********************";
			String secretAccessKey = "v7Qts+qswF9qDojbKZQ1BofRbWcQLJu8CLM+eXrf";

			amazonSQS = SQSUtils.getAmazonSQS(accessKeyId, secretAccessKey);
			
			try {
				queryUrl = SQSUtils.createQueue("SQS_UPLOAD_PIXEL3_GOOGLE_DESKTOP", amazonSQS);
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println("Init Normal Queue failed!");
				return;
			}
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("Init AmazonSQS failed!");
			return;
		}
		if (amazonSQS == null) {
			System.out.println("Init AmazonSQS failed!");
			return;
		}

	}
	
	private void processNormalQueue() {

		Properties prop = new Properties();
		try {
			prop.load(PixelHeightSummaryV3.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			System.out.println("no properties file found");
		}
		
		List<Message> processList = new ArrayList<Message>();
		while (true) {
			List<Message> messages = SQSUtils.getMessageFromQueue(amazonSQS, queryUrl, 10, VisibilityTimeout);
			System.out.println(" message size: " + messages.size() + " ,VisibilityTimeout: " + VisibilityTimeout);
			
			if (messages != null && messages.size() > 0) {
				
				for (Message m : messages) {
					processList.add(m);
				}
				
				if (processList.size() >= 500) {
					Message[] messagesArray = processList.toArray(new Message[processList.size()]);
					processMsg(messagesArray);
					processList = new ArrayList<>();
					break;
				}
				
			} else {
				System.out.println("======All domain process done!!!");
				
				break;
			}
		}
		
        while (!newFixedThreadPool.isShutdown()) {
            try {
                Thread.sleep(10 * 1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            int aliveCount = ((ThreadPoolExecutor)newFixedThreadPool).getActiveCount();
            System.out.println("Thread aliveCount : "+aliveCount);
            if (aliveCount == 0) {
                newFixedThreadPool.shutdown();
            }
        }
	}

    private void processMsg(Message[] messages) {
    	/**
    	 * PixelHeightRankingDao pixelHeightRankingDao, 
    		AmazonSQS amazonSQS, String queryUrl, Message[] messages, boolean mobile
    	 */
        PixelHeightSummaryV3Command pixelHeightSummaryTool = new PixelHeightSummaryV3Command(pixelHeightRankingDao, vsUploadMonitorDAO,
        		amazonSQS, queryUrl, messages, mobile);
        pixelHeightSummaryTool.run();
    }

}