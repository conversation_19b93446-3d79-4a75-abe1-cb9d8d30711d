package seoclarity.backend.summary.pixelv2;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.net.InternetDomainName;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ArrayUtils;
import seoclarity.backend.dao.clickhouse.pixelheight.PixelHeightRankingDao;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.VedObject;
import seoclarity.backend.entity.clickhouse.PixelHeightEntityV2;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.RankTypeManager;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.pixelv2.PixelHeightSummaryToolV2" -Dexec.cleanupDaemonThreads=false
 * <AUTHOR>
 * @create 2018-09-21 17:53
 **/
public class PixelHeightSummaryToolV2 {

    private PixelHeightRankingDao pixelHeightRankingDao;
    public static final String PROTOCOL_HTTP = "0";
    public static final String PROTOCOL_HTTPS = "1";

    private File file;
    private int engineId;
    private boolean mobile;
    private int languageId;
//    private int locationId = 0;
    private static final org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(PixelHeightSummaryToolV2.class);


    public PixelHeightSummaryToolV2(PixelHeightRankingDao pixelHeightRankingDao, File file, int engineId, int languageId, boolean mobile) {
        this.pixelHeightRankingDao = pixelHeightRankingDao;
        this.file = file;
        this.engineId = engineId;
        this.languageId = languageId;
        this.mobile = mobile;
    }

    public void run() {
        log.info("Process file : " + file.getAbsolutePath() + " Start.");
        List<String> lines;
        try {
            lines = IOUtils.readLines(new FileInputStream(file));
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
        if (CollectionUtils.isEmpty(lines)) {
            log.info("Empty file, exit.");
            return;
        }
        log.info("total count : " + lines.size());

        List<PixelHeightEntityV2> pixelHeightEntities = new ArrayList<>(lines.size() * 5);
        for (String line : lines) {
//            VedObject vedObject = JSONUtil.toBean(line,VedObject.class);
            VedObject vedObject = JSON.parseObject(line, VedObject.class);
//            VedObject vedObject = gson.fromJson(line, VedObject.class);
            if (vedObject == null) {
                log.error("vo is empty :" + line);
                continue;
            }

            if (CollectionUtils.isEmpty(conversion(vedObject.getVedJson().get("domainList")))) {
                log.error("Domain list is empty for id :" + vedObject.getVedJson().get("id"));
                continue;
            }

            for (String domainStr : conversion(vedObject.getVedJson().get("domainList"))) {
                int domainId = NumberUtils.toInt(domainStr, -1);
                if (domainId <= 0) {
                    log.error("domain id error :" + domainId);
                    continue;
                }

                int locationId = 0;
                if(vedObject.getVedJson().containsKey("cityId")) {
                    locationId = vedObject.getVedJson().getInt("cityId");
                    if(locationId > 0) {
                        log.info("city level keyword : "+locationId);
                    }
                }
//                if(locationId > 0) {
//                    System.out.println("Skip city keyword now.");
//                    continue;
//                }
                String unqKey = domainId + "!_!" + vedObject.getVedJson().get("queryDate") + "!_!" + vedObject.getVedJson().get("id") + "!_!" + engineId + "!_!" + languageId + "!_!" + locationId + "!_!"+ mobile;
                if (PixelHeightSummaryV2.processedSet.contains(unqKey)) {
                    log.error("unqKey :" + unqKey + " had been processed!!");
                    continue;
                }
                PixelHeightSummaryV2.processedSet.add(unqKey);

                Set<String> uniqueDomainSet = new HashSet<>(15);
                Set<String> uniqueDomainIncludeSDomainSet = new HashSet<>(15);

                if(vedObject.getVedObjects().size() == 0) {
                    System.out.println("info : "+JSONUtil.toJsonStr(vedObject));
                    continue;
                }

                Double appbarTop = 0.0;
                Double totalOffsetTop = vedObject.getVedObjects().get(vedObject.getVedObjects().size()-1).getKeywordRankEntityVO().getOffsetTop();

                if(vedObject.getVedObjects().get(0).getCssPath().equals("appbar")){
                    appbarTop = vedObject.getVedObjects().get(0).getKeywordRankEntityVO().getOffsetTop();
                }

                for (VedObject object : vedObject.getVedObjects()) {
                    String[] domainUrlList = splitString(object.getKeywordRankEntityVO().getLandingPage());
                    if(domainUrlList == null || domainUrlList[0].equals("UNKNOWN")){
                        PixelHeightEntityV2 pixelHeightEntity;
                        try {
                            pixelHeightEntity = getEntity(domainId, locationId,"", "", vedObject, object.getKeywordRankEntityVO(), object);
                            pixelHeightEntity.setCreateDate(new Date());
                            pixelHeightEntity.setHrrd(0);
                            pixelHeightEntity.setHrd(0);
                            pixelHeightEntity.setAppbarTop(appbarTop.intValue());
                            pixelHeightEntity.setTotalOffsetTop(totalOffsetTop.intValue());
                            pixelHeightEntities.add(pixelHeightEntity);
                        } catch (Exception e) {
                            System.out.println("===============================================:" +FormatUtils.decoderString(vedObject.getVedJson().get("keyword").toString()));
                            e.printStackTrace();
                        }
                    } else {
                        // domain
                        String domainName = domainUrlList[0];
                        String rootReverseDomainName = FormatUtils.reverseDomainNameByDot(getRootDomain(FormatUtils.reverseDomainNameByDot(domainName)));
                        // url
                        String uriPattern = domainUrlList[1];
                        String newReverseDomainName = RankTypeManager.convertToNewDomainName(domainName, object.getKeywordRankEntityVO().getType(), uriPattern);

                        PixelHeightEntityV2 pixelHeightEntity;
                        boolean isPaa = false;
                        try {
                            pixelHeightEntity = getEntity(domainId, locationId, newReverseDomainName, rootReverseDomainName, vedObject, object.getKeywordRankEntityVO(), object);
                            if(pixelHeightEntity.getVedTypeArray() != null && pixelHeightEntity.getVedTypeArray().size() == 1
                                    && StrUtil.equalsIgnoreCase(pixelHeightEntity.getVedTypeArray().get(0), "13262")
                                    && StrUtil.containsIgnoreCase(pixelHeightEntity.getDomainReverse(), ".google.")
                                    && StrUtil.containsIgnoreCase(pixelHeightEntity.getUrl(), "/search")
                                    && StrUtil.containsIgnoreCase(pixelHeightEntity.getUrl(), "%3F&")) {
                                //For google mobile new PAA
//                                log.info("This is mobile PAA :");
//                                log.info("old url :"+pixelHeightEntity.getUrl());
                                rootReverseDomainName = "com.google";
                                newReverseDomainName = "com.google.peoplealsoask";
                                pixelHeightEntity.setDomainReverse(newReverseDomainName);
                                pixelHeightEntity.setRootDomainReverse(rootReverseDomainName);
                                pixelHeightEntity.setUrl("https://peoplealsoask.google.com");
                                pixelHeightEntity.setType(10001);
//                                log.info("new url :"+pixelHeightEntity.getUrl());
                                isPaa = true;
                            }
                            pixelHeightEntity.setAppbarTop(appbarTop.intValue());
                            pixelHeightEntity.setTotalOffsetTop(totalOffsetTop.intValue());
                        } catch (Exception e) {
                            System.out.println("===============================================:" +FormatUtils.decoderString(vedObject.getVedJson().get("keyword").toString()));
                            e.printStackTrace();
                            continue;
                        }
                        if (uniqueDomainSet.contains(newReverseDomainName)) {
                            pixelHeightEntity.setHrd(0);
                        } else {
                            uniqueDomainSet.add(newReverseDomainName);
                            pixelHeightEntity.setHrd(1);
                        }
                        // hrrd (root domain)
                        if (uniqueDomainIncludeSDomainSet.contains(rootReverseDomainName)) {
                            pixelHeightEntity.setHrrd(0);
                        } else {
                            uniqueDomainIncludeSDomainSet.add(rootReverseDomainName);
                            pixelHeightEntity.setHrrd(1);
                        }
                        pixelHeightEntity.setCreateDate(new Date());
//                        if(isPaa) {
//                            log.info(JSONUtil.toJsonStr(pixelHeightEntity));
//                        }
                        pixelHeightEntities.add(pixelHeightEntity);
                    }
                }
            }
        }

        Map<String, List<PixelHeightEntityV2>> dataMap = new HashMap<>();
        System.out.println("====start upload");
        System.out.println("====pixelHeightEntities.size: " + pixelHeightEntities.size());
        for (PixelHeightEntityV2 pixelHeightEntity : pixelHeightEntities) {
            List<PixelHeightEntityV2> domainPixels;
            String key = pixelHeightEntity.getOwnDomainId()+"!_!"+FormatUtils.formatDateToYyyyMmDd(pixelHeightEntity.getRankingDate())+"!_!"+pixelHeightEntity.getLocationId();
            if (dataMap.get(key) == null) {
                domainPixels = new ArrayList<>();
            } else {
                domainPixels = dataMap.get(key);
            }
            domainPixels.add(pixelHeightEntity);
            dataMap.put(key, domainPixels);
        }
        System.out.println("====dataMap.size: " + pixelHeightEntities.size());
        for (String key : dataMap.keySet()) {
            List<PixelHeightEntityV2> totalDomainPixels = dataMap.get(key);
            Set<Integer> ids = totalDomainPixels.stream().map(PixelHeightEntityV2::getKeywordRankcheckId).collect(Collectors.toSet());
            List<Integer> existKeywordIds = pixelHeightRankingDao.checkExistBatchV2(engineId, languageId, totalDomainPixels.get(0).getOwnDomainId(), totalDomainPixels.get(0).getLocationId(), totalDomainPixels.get(0).getRankingDate(), ids, mobile ? "m" : "d");
            System.out.println("Find exist id size :" + existKeywordIds.size()+" for domain :"+key);
            List<PixelHeightEntityV2> pixelInsertEntities = new ArrayList<>();
            for (PixelHeightEntityV2 domainPixel : totalDomainPixels) {
                if (existKeywordIds.contains(domainPixel.getKeywordRankcheckId()) == false) {
                    pixelInsertEntities.add(domainPixel);
                }
            }
            System.out.println("Total count : " + totalDomainPixels.size() + " after filter count : " + pixelInsertEntities.size()+" for domain :"+key);
            try {
                pixelHeightRankingDao.insertPixelsV2(pixelInsertEntities);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("Insert error for domainId "+totalDomainPixels.get(0).getOwnDomainId()+" # "+totalDomainPixels.get(0).getLocationId()+" file : "+file.getAbsolutePath());
            }
        }
        log.info("===Process file : " + file.getAbsolutePath() + " Finish.");
    }

    private PixelHeightEntityV2 getEntity(int domainId, int locationId, String domainName, String rootDomainName, VedObject vedObject, KeywordRankEntityVO keywordRankEntityVO, VedObject object) {
        PixelHeightEntityV2 pixelHeightEntity = new PixelHeightEntityV2();
        pixelHeightEntity.setOrgFps(Integer.parseInt(vedObject.getVedJson().get("orgFps").toString()));
        pixelHeightEntity.setPixelFps(vedObject.getVedObjects().size());
        pixelHeightEntity.setRank(keywordRankEntityVO.getRank());
        pixelHeightEntity.setOffsetTop(keywordRankEntityVO.getOffsetTop() == null ? 0 : keywordRankEntityVO.getOffsetTop().intValue());
        pixelHeightEntity.setOffsetBottom(keywordRankEntityVO.getOffsetBottom() == null ? 0 : keywordRankEntityVO.getOffsetBottom().intValue());
        pixelHeightEntity.setHeight(keywordRankEntityVO.getHeight() == null ? 0 : keywordRankEntityVO.getHeight().intValue());
        pixelHeightEntity.setLabel(StringUtils.isBlank(keywordRankEntityVO.getLabel()) ? "" : keywordRankEntityVO.getLabel());
        pixelHeightEntity.setFold(keywordRankEntityVO.getFold() == null ? 0 : keywordRankEntityVO.getFold());
        pixelHeightEntity.setFoldRank(keywordRankEntityVO.getFoldRank() == null ? 0 : keywordRankEntityVO.getFoldRank());
        pixelHeightEntity.setLocationId(locationId);
        pixelHeightEntity.setOwnDomainId(domainId);
        pixelHeightEntity.setSearchVol(NumberUtils.toInt(vedObject.getVedJson().get("searchVol").toString(), 0));
        pixelHeightEntity.setKeywordRankcheckId(vedObject.getVedJson().get("id") == null ? 0 : Integer.parseInt(vedObject.getVedJson().get("id").toString()));
        pixelHeightEntity.setKeywordName(FormatUtils.decoderString(vedObject.getVedJson().get("keyword").toString()));
        pixelHeightEntity.setEngineId(engineId);
        pixelHeightEntity.setLanguageId(languageId);
        String dateStr = vedObject.getVedJson().get("queryDate").toString();
        String dateFormat = "yyyyMMdd";
        if(dateStr.contains("-")) {
            dateFormat = "yyyy-MM-dd";
        }
        Date rankingDate = FormatUtils.toDate(dateStr, dateFormat);
        Date week1Start = DateUtil.parseDate("2022-08-01");
        Date week1End = DateUtil.parseDate("2022-08-06");
        Date week2Start = DateUtil.parseDate("2022-08-08");
        Date week2End = DateUtil.parseDate("2022-08-13");
        if(DateUtil.compare(rankingDate, week1Start) >= 0 && DateUtil.compare(rankingDate, week1End) <= 0) {
            rankingDate = DateUtil.parseDate("2022-07-31");
        } else if(DateUtil.compare(rankingDate, week2Start) >= 0 && DateUtil.compare(rankingDate, week2End) <= 0) {
            rankingDate = DateUtil.parseDate("2022-08-07");
        }

        pixelHeightEntity.setRankingDate(rankingDate);
        pixelHeightEntity.setUrl(StringUtils.isBlank(keywordRankEntityVO.getLandingPage()) ? "" : keywordRankEntityVO.getLandingPage());
        pixelHeightEntity.setDomainReverse(domainName);
        pixelHeightEntity.setRootDomainReverse(rootDomainName);
        pixelHeightEntity.setDevice(mobile ? "m" : "d");
        pixelHeightEntity.setType(keywordRankEntityVO.getType());
//        pixelHeightEntity.setFirstPageSize(Integer.parseInt(vedObject.getVedJson().get("orgFps").toString()));
        pixelHeightEntity.setVedRawJson(object.getVedStr());
        pixelHeightEntity.setVedAttName(object.getCssType());
        pixelHeightEntity.setVedAttValue(object.getCssPath());
        List<String> typeList = conversion(object.getVedJson().get("typeList"));
        pixelHeightEntity.setVedTypeArray(typeList);

        if(pixelHeightEntity.getOffsetTop() < 0 && pixelHeightEntity.getOffsetBottom() > 0) {
            pixelHeightEntity.setOffsetTop(0);
        }
        if(pixelHeightEntity.getHeight() <= 0) {
            pixelHeightEntity.setHeight(pixelHeightEntity.getOffsetBottom() - pixelHeightEntity.getOffsetTop());
        }
        if(pixelHeightEntity.getFold() <= 0) {
            pixelHeightEntity.setFold(1);
        }

        return pixelHeightEntity;
    }

    public static String getRootDomain(String fullDomain) {
        try {
            return InternetDomainName.from(fullDomain).topPrivateDomain().toString();
        } catch (Exception e) {
            log.error("Error parser root domain name : "+fullDomain);
        }
        return fullDomain;
    }

    public static List<String> conversion(Object obj){
        if(obj == null){
            return new ArrayList<>();
        }
        String[] arr = obj.toString().substring(1,obj.toString().length()-1).replaceAll("\"","").split(",");
        return Arrays.asList(arr);
    }

    public static String[] splitString(String url) {
        boolean isHttp = false;
        String[] domainUrlList = null;
        try {
            if(url == null){
                return new String[]{"UNKNOWN"};
            }
            if (org.apache.commons.lang3.StringUtils.startsWithIgnoreCase(url, "https")) {
                url = org.apache.commons.lang3.StringUtils.removeStartIgnoreCase(url, "https://");
            } else if (org.apache.commons.lang3.StringUtils.startsWithIgnoreCase(url, "http")) {
                url = org.apache.commons.lang3.StringUtils.removeStartIgnoreCase(url, "http://");
                isHttp = true;
            }else if("UNKNOWN".equals(url) || "".equals(url)){
                return new String[]{"UNKNOWN"};
            }
//            else {
//                url = StringUtils.substringAfterLast(url, "http");
//                url = StringUtils.remove(url, "://");
//            }

            domainUrlList = org.apache.commons.lang3.StringUtils.splitPreserveAllTokens(url, "/", 2);
            if (domainUrlList == null || domainUrlList.length == 0) {
                return null;
            }
            String reverse = org.apache.commons.lang3.StringUtils.reverseDelimited(domainUrlList[0], '.');
            domainUrlList[0] = reverse;
            if (domainUrlList.length == 1) {
                domainUrlList = ArrayUtils.add(domainUrlList, "/");
            } else {
                domainUrlList[1] = "/" + domainUrlList[1];
            }
            if (isHttp) {
                domainUrlList = ArrayUtils.add(domainUrlList, PROTOCOL_HTTP);
            } else {
                domainUrlList = ArrayUtils.add(domainUrlList, PROTOCOL_HTTPS);
            }
        } catch (Exception e) {
            return null;
        }
        return domainUrlList;
    }

}