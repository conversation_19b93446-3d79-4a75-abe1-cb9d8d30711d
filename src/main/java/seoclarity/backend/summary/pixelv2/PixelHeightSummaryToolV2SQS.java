package seoclarity.backend.summary.pixelv2;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.common.net.InternetDomainName;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ArrayUtils;
import seoclarity.backend.dao.clickhouse.pixelheight.PixelHeightRankingDao;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.VedObject;
import seoclarity.backend.entity.clickhouse.PixelHeightEntityV2;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.util.*;

/**
 *
 * vim ./src/main/java/seoclarity/backend/summary/pixelv2/PixelHeightSummaryToolV2SQS.java
 *  mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.pixelv2.PixelHeightSummaryToolV2SQS" -Dexec.cleanupDaemonThreads=false
 * <AUTHOR>
 * @create 2018-09-21 17:53
 **/
@CommonsLog
public class PixelHeightSummaryToolV2SQS implements Runnable {

//    private static final org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(PixelHeightSummaryToolV2SQS.class);

    private PixelHeightRankingDao pixelHeightRankingDao;
    public static final String PROTOCOL_HTTP = "0";
    public static final String PROTOCOL_HTTPS = "1";
    private boolean mobile;
    private String sqsUrl;
    private AmazonSQS amazonSQS;

    public static Set<String> processedSet;

    private static final String PIXEL_DIR_PREFIX = "ved_temp_pixel_height_commoncrawl_keywordRank_";
    private static final String PIXEL_MOBILE_DIR_PREFIX = "ved_temp_mobile_pixel_height_commoncrawl_keywordRank_";

//    private TSocket socket;
//    private TTransport transport;
//    private TProtocol proto;

    private Scribe.Client client;

    public PixelHeightSummaryToolV2SQS(PixelHeightRankingDao pixelHeightRankingDao, boolean mobile, String sqsUrl,AmazonSQS amazonSQS, Set<String> processedSet) {
        this.pixelHeightRankingDao = pixelHeightRankingDao;
        this.sqsUrl = sqsUrl;
        this.mobile = mobile;
        this.amazonSQS = amazonSQS;
        this.processedSet = processedSet;

//        try {
//            socket = new TSocket("127.0.0.1", 1463);
//            transport = new TFramedTransport(socket);
//            proto = new TBinaryProtocol(transport);
//            client = new Scribe.Client(proto);
//            transport.open();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

    }


    @Override
    public void run() {

        List<PixelHeightEntityV2> pixelInsertEntities = new ArrayList<>(2000);
        List<Message> messages = SQSUtils.getMessageFromQueue(amazonSQS, sqsUrl, 10, 600);
        do {
            long start = System.currentTimeMillis();
            for (Message message : messages) {
                try {

//                    long st = System.currentTimeMillis();
//                    VedObject vedObject = JSONUtil.toBean(message.getBody(), VedObject.class);
//                    log.info("to json cost : "+(System.currentTimeMillis() - st));
//                    st = System.currentTimeMillis();
                    VedObject vedObject = JSON.parseObject(message.getBody(), VedObject.class);
//                    log.info("to json2 cost : "+(System.currentTimeMillis() - st));
                    int engineId = vedObject.getVedJson().getInt("searchEngine");
                    int languageId = vedObject.getVedJson().getInt("searchLanguage");
//                    String scribePath = (mobile ? PIXEL_MOBILE_DIR_PREFIX : PIXEL_DIR_PREFIX) + engineId + "_"+languageId;
//                    sendDataToMongoScribeV3(scribePath, message.getBody(), client);
//                    SQSUtils.deleteMessageFromQueue(amazonSQS, sqsUrl, message);
                    addToList(vedObject, pixelInsertEntities, engineId, languageId);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
//            log.info("to bean cost : "+(System.currentTimeMillis() - start));
//            start = System.currentTimeMillis();
            SQSUtils.deleteMessageListFromQueue(amazonSQS, sqsUrl, messages);
            log.info("to bean+delete cost : "+(System.currentTimeMillis() - start));
            if(pixelInsertEntities.size() >= 2000) {
                start = System.currentTimeMillis();
                pixelHeightRankingDao.insertPixelsV2(pixelInsertEntities);
                log.info("insert "+pixelInsertEntities.size()+" cost : "+(System.currentTimeMillis() - start));
                pixelInsertEntities.clear();
            }
            while (true) {
                try {
                    messages = SQSUtils.getMessageFromQueue(amazonSQS, sqsUrl, 10, 600);
                    break;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ThreadUtil.sleep(500);
            }
        } while (CollectionUtil.isNotEmpty(messages));
        log.info("Thread End : "+Thread.currentThread().getName());
        if(pixelInsertEntities.size() >= 0) {
            pixelHeightRankingDao.insertPixelsV2(pixelInsertEntities);
        }
//        try {
//            transport.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    private void addToList(VedObject vedObject, List<PixelHeightEntityV2> pixelHeightEntities, int engineId, int languageId) {
        if (vedObject == null) {
            return;
        }
        List<String> domainList = vedObject.getVedJson().getJSONArray("domainList").toList(String.class);

        if (CollectionUtils.isEmpty(domainList)) {
            log.error("Domain list is empty for id :" + vedObject.getVedJson().get("id"));
            return;
        }

        for (String domainStr : domainList) {
            int domainId = NumberUtils.toInt(domainStr, -1);
            if (domainId <= 0) {
                log.error("domain id error :" + domainId);
                continue;
            }

            int locationId = 0;
            if(vedObject.getVedJson().containsKey("cityId")) {
                locationId = vedObject.getVedJson().getInt("cityId");
                if(locationId > 0) {
                    log.info("city level keyword : "+locationId);
                }
            }

//            if(processedSet != null) {
//                String unqKey = domainId + "!_!" + vedObject.getVedJson().get("queryDate") + "!_!" + vedObject.getVedJson().get("id") + "!_!" + engineId + "!_!" + languageId + "!_!" + locationId + "!_!"+ mobile;
//                if (processedSet.contains(unqKey)) {
//                    log.error("unqKey :" + unqKey + " had been processed!!");
//                    continue;
//                }
//                processedSet.add(unqKey);
//            }


            Set<String> uniqueDomainSet = new HashSet<>(15);
            Set<String> uniqueDomainIncludeSDomainSet = new HashSet<>(15);

            if(vedObject.getVedObjects().size() == 0) {
                System.out.println("info : "+JSONUtil.toJsonStr(vedObject));
                continue;
            }

            Double appbarTop = 0.0;
            Double totalOffsetTop = vedObject.getVedObjects().get(vedObject.getVedObjects().size()-1).getKeywordRankEntityVO().getOffsetTop();

            if(vedObject.getVedObjects().get(0).getCssPath().equals("appbar")) {
                appbarTop = vedObject.getVedObjects().get(0).getKeywordRankEntityVO().getOffsetTop();
            }
            for (VedObject object : vedObject.getVedObjects()) {
                String[] domainUrlList = splitString(object.getKeywordRankEntityVO().getLandingPage());
                if(domainUrlList == null || domainUrlList[0].equals("UNKNOWN")) {
                    PixelHeightEntityV2 pixelHeightEntity;
                    try {
                        pixelHeightEntity = getEntity(domainId, locationId,"", "", vedObject, object.getKeywordRankEntityVO(), object, engineId, languageId);
                        pixelHeightEntity.setCreateDate(new Date());
                        pixelHeightEntity.setHrrd(0);
                        pixelHeightEntity.setHrd(0);
                        pixelHeightEntity.setAppbarTop(appbarTop.intValue());
                        pixelHeightEntity.setTotalOffsetTop(totalOffsetTop.intValue());
                        pixelHeightEntities.add(pixelHeightEntity);
                    } catch (Exception e) {
                        System.out.println("===============================================:" +FormatUtils.decoderString(vedObject.getVedJson().get("keyword").toString()));
                        e.printStackTrace();
                    }
//                    System.out.println("c2 "+(System.currentTimeMillis() - st));
                } else {
                    // domain
                    String reveresDomainName = domainUrlList[0];
                    String rootReverseDomainName = FormatUtils.reverseDomainNameByDot(getRootDomain(FormatUtils.reverseDomainNameByDot(reveresDomainName)));
                    // url
                    String uriPattern = domainUrlList[1];
                    String newReverseDomainName = RankTypeManager.convertToNewDomainName(reveresDomainName, object.getKeywordRankEntityVO().getType(), uriPattern);

                    PixelHeightEntityV2 pixelHeightEntity;
                    try {
                        pixelHeightEntity = getEntity(domainId, locationId, newReverseDomainName, rootReverseDomainName, vedObject, object.getKeywordRankEntityVO(), object, engineId, languageId);
                        if(pixelHeightEntity.getVedTypeArray() != null && pixelHeightEntity.getVedTypeArray().size() == 1
                                && StrUtil.equalsIgnoreCase(pixelHeightEntity.getVedTypeArray().get(0), "13262")
                                && StrUtil.containsIgnoreCase(pixelHeightEntity.getDomainReverse(), ".google.")
                                && StrUtil.containsIgnoreCase(pixelHeightEntity.getUrl(), "/search")
                                && StrUtil.containsIgnoreCase(pixelHeightEntity.getUrl(), "%3F&")) {
                            rootReverseDomainName = "com.google";
                            newReverseDomainName = "com.google.peoplealsoask";
                            pixelHeightEntity.setDomainReverse(newReverseDomainName);
                            pixelHeightEntity.setRootDomainReverse(rootReverseDomainName);
                            pixelHeightEntity.setUrl("https://peoplealsoask.google.com");
                            pixelHeightEntity.setType(10001);
//                            System.out.println("c4 "+(System.currentTimeMillis() - st));
                        }
                        pixelHeightEntity.setAppbarTop(appbarTop.intValue());
                        pixelHeightEntity.setTotalOffsetTop(totalOffsetTop.intValue());
                    } catch (Exception e) {
                        System.out.println("===============================================:" +FormatUtils.decoderString(vedObject.getVedJson().get("keyword").toString()));
                        e.printStackTrace();
                        continue;
                    }
                    if (uniqueDomainSet.contains(newReverseDomainName)) {
                        pixelHeightEntity.setHrd(0);
                    } else {
                        uniqueDomainSet.add(newReverseDomainName);
                        pixelHeightEntity.setHrd(1);
                    }
                    // hrrd (root domain)
                    if (uniqueDomainIncludeSDomainSet.contains(rootReverseDomainName)) {
                        pixelHeightEntity.setHrrd(0);
                    } else {
                        uniqueDomainIncludeSDomainSet.add(rootReverseDomainName);
                        pixelHeightEntity.setHrrd(1);
                    }
                    pixelHeightEntity.setCreateDate(new Date());
                    pixelHeightEntities.add(pixelHeightEntity);
                }
            }
        }
    }

    private PixelHeightEntityV2 getEntity(int domainId, int locationId, String domainName, String rootDomainName, VedObject vedObject, KeywordRankEntityVO keywordRankEntityVO, VedObject object, int engineId, int languageId) {
        PixelHeightEntityV2 pixelHeightEntity = new PixelHeightEntityV2();
        pixelHeightEntity.setOrgFps(Integer.parseInt(vedObject.getVedJson().get("orgFps").toString()));
        pixelHeightEntity.setPixelFps(vedObject.getVedObjects().size());
        pixelHeightEntity.setRank(keywordRankEntityVO.getRank());
        pixelHeightEntity.setOffsetTop(keywordRankEntityVO.getOffsetTop() == null ? 0 : keywordRankEntityVO.getOffsetTop().intValue());
        pixelHeightEntity.setOffsetBottom(keywordRankEntityVO.getOffsetBottom() == null ? 0 : keywordRankEntityVO.getOffsetBottom().intValue());
        pixelHeightEntity.setHeight(keywordRankEntityVO.getHeight() == null ? 0 : keywordRankEntityVO.getHeight().intValue());
        pixelHeightEntity.setLabel(StringUtils.isBlank(keywordRankEntityVO.getLabel()) ? "" : keywordRankEntityVO.getLabel());
        pixelHeightEntity.setFold(keywordRankEntityVO.getFold() == null ? 0 : keywordRankEntityVO.getFold());
        pixelHeightEntity.setFoldRank(keywordRankEntityVO.getFoldRank() == null ? 0 : keywordRankEntityVO.getFoldRank());
        pixelHeightEntity.setLocationId(locationId);
        pixelHeightEntity.setOwnDomainId(domainId);
        pixelHeightEntity.setSearchVol(NumberUtils.toInt(vedObject.getVedJson().get("searchVol").toString(), 0));
        pixelHeightEntity.setKeywordRankcheckId(vedObject.getVedJson().get("id") == null ? 0 : Integer.parseInt(vedObject.getVedJson().get("id").toString()));
        pixelHeightEntity.setKeywordName(FormatUtils.decoderString(vedObject.getVedJson().get("keyword").toString()));
        pixelHeightEntity.setEngineId(engineId);
        pixelHeightEntity.setLanguageId(languageId);
        String dateStr = vedObject.getVedJson().get("queryDate").toString();
        String dateFormat = "yyyyMMdd";
        if(dateStr.contains("-")) {
            dateFormat = "yyyy-MM-dd";
        }
        Date rankingDate = FormatUtils.toDate(dateStr, dateFormat);
//        Date week1Start = DateUtil.parseDate("2022-08-01");
//        Date week1End = DateUtil.parseDate("2022-08-06");
//        Date week2Start = DateUtil.parseDate("2022-08-08");
//        Date week2End = DateUtil.parseDate("2022-08-13");
//        if(DateUtil.compare(rankingDate, week1Start) >= 0 && DateUtil.compare(rankingDate, week1End) <= 0) {
//            rankingDate = DateUtil.parseDate("2022-07-31");
//        } else if(DateUtil.compare(rankingDate, week2Start) >= 0 && DateUtil.compare(rankingDate, week2End) <= 0) {
//            rankingDate = DateUtil.parseDate("2022-08-07");
//        }
        pixelHeightEntity.setRankingDate(rankingDate);
        pixelHeightEntity.setUrl(StringUtils.isBlank(keywordRankEntityVO.getLandingPage()) ? "" : keywordRankEntityVO.getLandingPage());
        pixelHeightEntity.setDomainReverse(domainName);
        pixelHeightEntity.setRootDomainReverse(rootDomainName);
        pixelHeightEntity.setDevice(mobile ? "m" : "d");
        pixelHeightEntity.setType(keywordRankEntityVO.getType());
//        pixelHeightEntity.setFirstPageSize(Integer.parseInt(vedObject.getVedJson().get("orgFps").toString()));
        pixelHeightEntity.setVedRawJson(object.getVedStr());
        pixelHeightEntity.setVedAttName(object.getCssType());
        pixelHeightEntity.setVedAttValue(object.getCssPath());
        List<String> typeList = null;
        if(object.getVedJson().getJSONArray("typeList") != null) {
            typeList = object.getVedJson().getJSONArray("typeList").toList(String.class);
        }
        pixelHeightEntity.setVedTypeArray(typeList);

        if(pixelHeightEntity.getOffsetTop() < 0 && pixelHeightEntity.getOffsetBottom() > 0) {
            pixelHeightEntity.setOffsetTop(0);
        }
        if(pixelHeightEntity.getHeight() <= 0) {
            pixelHeightEntity.setHeight(pixelHeightEntity.getOffsetBottom() - pixelHeightEntity.getOffsetTop());
        }
        if(pixelHeightEntity.getFold() <= 0) {
            pixelHeightEntity.setFold(1);
        }

        return pixelHeightEntity;
    }

    public static String getRootDomain(String fullDomain) {
        try {
            return InternetDomainName.from(fullDomain).topPrivateDomain().toString();
        } catch (Exception e) {
            log.error("Error parser root domain name : "+fullDomain);
        }
        return fullDomain;
    }

    public static List<String> conversion(Object obj){
        if(obj == null){
            return new ArrayList<>();
        }
        String[] arr = obj.toString().substring(1,obj.toString().length()-1).replaceAll("\"","").split(",");
        return Arrays.asList(arr);
    }

    public static String[] splitString(String url) {
        boolean isHttp = false;
        String[] domainUrlList = null;
        try {
            if(url == null){
                return new String[]{"UNKNOWN"};
            }
            if (org.apache.commons.lang3.StringUtils.startsWithIgnoreCase(url, "https")) {
                url = org.apache.commons.lang3.StringUtils.removeStartIgnoreCase(url, "https://");
            } else if (org.apache.commons.lang3.StringUtils.startsWithIgnoreCase(url, "http")) {
                url = org.apache.commons.lang3.StringUtils.removeStartIgnoreCase(url, "http://");
                isHttp = true;
            }else if("UNKNOWN".equals(url) || "".equals(url)){
                return new String[]{"UNKNOWN"};
            }
//            else {
//                url = StringUtils.substringAfterLast(url, "http");
//                url = StringUtils.remove(url, "://");
//            }

            domainUrlList = org.apache.commons.lang3.StringUtils.splitPreserveAllTokens(url, "/", 2);
            if (domainUrlList == null || domainUrlList.length == 0) {
                return null;
            }
            String reverse = org.apache.commons.lang3.StringUtils.reverseDelimited(domainUrlList[0], '.');
            domainUrlList[0] = reverse;
            if (domainUrlList.length == 1) {
                domainUrlList = ArrayUtils.add(domainUrlList, "/");
            } else {
                domainUrlList[1] = "/" + domainUrlList[1];
            }
            if (isHttp) {
                domainUrlList = ArrayUtils.add(domainUrlList, PROTOCOL_HTTP);
            } else {
                domainUrlList = ArrayUtils.add(domainUrlList, PROTOCOL_HTTPS);
            }
        } catch (Exception e) {
            return null;
        }
        return domainUrlList;
    }

//    public static boolean sendDataToMongoScribeV2(String folderName, String keywordGson) {
//        TSocket socket = new TSocket("127.0.0.1", 1463);
//        TTransport transport = new TFramedTransport(socket);
//        TProtocol proto = new TBinaryProtocol(transport);
//        Scribe.Client client = new Scribe.Client(proto);
//
//        List<LogEntry> list = new ArrayList<>();
//        LogEntry log = new LogEntry(folderName, keywordGson);
//        list.add(log);
//        try {
//            transport.open();
//            ResultCode rc = client.Log(list);
//            if(rc.getValue() == ResultCode.OK.getValue()) {
//                transport.close();
//                return true;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
//        return false;
//    }

    public static boolean sendDataToMongoScribeV3(String folderName, String keywordGson, Scribe.Client client) {
        try {
            ResultCode rc = client.Log(Collections.singletonList(new LogEntry(folderName, keywordGson)));
            if(rc.getValue() == ResultCode.OK.getValue()) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return false;
    }

//    public static void main(String[] args) {
//        TSocket socket = new TSocket("127.0.0.1", 1463);
//        TTransport transport = new TFramedTransport(socket);
//        TProtocol proto = new TBinaryProtocol(transport);
//        try {
//            Scribe.Client client = new Scribe.Client(proto);
//            transport.open();
//
//            for (int i = 0; i < 1000; i++) {
//                sendDataToMongoScribeV3("leolocal_test", i+"", client);
//            }
//
//            transport.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

}