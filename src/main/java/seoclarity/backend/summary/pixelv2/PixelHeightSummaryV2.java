package seoclarity.backend.summary.pixelv2;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import seoclarity.backend.dao.clickhouse.pixelheight.PixelHeightRankingDao;
import seoclarity.backend.summary.BaseScribeSummary;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-09-21 17:53
 * vim ./src/main/java/seoclarity/backend/summary/PixelHeightSummaryV2.java
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.pixelv2.PixelHeightSummaryV2" -Dexec.cleanupDaemonThreads=false -Dexec.args="false /disk1/scribeKeyword/ 1 2019-07-02 1#1"
 **/
@CommonsLog
public class PixelHeightSummaryV2 extends BaseScribeSummary {

    private static final String PIXEL_DIR_PREFIX = "ved_pixel_height_commoncrawl_keywordRank_";
    private static final String PIXEL_DIR_FORMAT = PIXEL_DIR_PREFIX+"%d_%d";
    private static final String PIXEL_DIR_PATTERN = "^"+PIXEL_DIR_PREFIX+"[\\d]+_[\\d]+$";

    private static final String PIXEL_MOBILE_DIR_PREFIX = "ved_mobile_pixel_height_commoncrawl_keywordRank_";
    private static final String PIXEL_MOBILE_DIR_FORMAT = PIXEL_MOBILE_DIR_PREFIX+"%d_%d";
    private static final String PIXEL_MOBILE_DIR_PATTERN = "^"+PIXEL_MOBILE_DIR_PREFIX+"[\\d]+_[\\d]+$";

    private PixelHeightRankingDao pixelHeightRankingDao;
    public static Set<String> processedSet = new HashSet<>();

    public PixelHeightSummaryV2() {
        pixelHeightRankingDao = SpringBeanFactory.getBean("pixelHeightRankingDao");
    }

    private static boolean mobile;

    public static void main(String[] args) {
        mobile = BooleanUtils.toBoolean(args[0]);
        baseLocation = args[1];
        threadCount = NumberUtils.toInt(args[2], 1);
        List<String> processDateList = new ArrayList<>();
        if (args.length >= 4) {
            String pd = args[3];
            if(pd.contains(",")) {
                String[] dateArray = pd.split(",");
                if(dateArray.length == 2) {
                    Date sDate = DateUtil.parse(dateArray[0], "yyyy-MM-dd");
                    Date eDate = DateUtil.parse(dateArray[1], "yyyy-MM-dd");
                    while (DateUtil.compare(eDate, sDate) >= 0) {
                        processDateList.add(DateUtil.format(eDate, "yyyy-MM-dd"));
                        log.info("date added : "+DateUtil.format(eDate, "yyyy-MM-dd"));
                        eDate = DateUtil.offsetDay(eDate, -1);
                    }
                    ThreadUtil.sleep(30000);
                } else {
                    Collections.addAll(processDateList, StringUtils.splitByWholeSeparator(pd, ","));
                    System.out.println("add new date : "+ JSONUtil.toJsonStr(processDateList));
                }
            } else {
                processDateList.add(pd);
            }
        } else {
            String pd = DateUtil.yesterday().toString("yyyy-MM-dd");
            processDateList.add(pd);
        }
        for (String ps : processDateList) {
            processDateString = ps;
            log.info("Processing date : "+processDateString);
            String[] engines;
            if (args.length >= 5) {
                engines = args[4].split(",");
                if (ArrayUtils.isEmpty(engines)) {
                    log.error("Empty engines!!!");
                    return;
                }
            } else {
                File baseDir = new File(baseLocation);
                System.out.println("baseLocation:"+baseLocation);
                String[] fileNames = baseDir.list((file, s) -> ReUtil.isMatch(mobile ? PIXEL_MOBILE_DIR_PATTERN : PIXEL_DIR_PATTERN, s));
                System.out.println("fileLength:"+fileNames.length);
                ThreadUtil.sleep(10000);
                List<String> engineList = new ArrayList<>();
                for (String fileName : fileNames) {
                    System.out.println("fileName = " + fileName);
                    engineList.add(StringUtils.removeStartIgnoreCase(fileName, mobile ? PIXEL_MOBILE_DIR_PREFIX : PIXEL_DIR_PREFIX).replace("_","#"));
                }
                engines = engineList.toArray(new String[]{});
                log.info("Find engines : "+StringUtils.join(engineList,","));
            }
            PixelHeightSummaryV2 pixelHeightSummary = new PixelHeightSummaryV2();
            pixelHeightSummary.start(engines);
        }
    }

    @Override
    protected void processFile(File file, int engineId, int languageId) {
        String numberStr = ReUtil.get("[0-9]{5}$", file.getName(), 0);
        int num = NumberUtils.toInt(numberStr, 0);
        System.out.println("fileName = " + file.getName()+" number : "+num);
        PixelHeightSummaryToolV2 pixelHeightSummaryTool = new PixelHeightSummaryToolV2(pixelHeightRankingDao, file, engineId, languageId, mobile);
        pixelHeightSummaryTool.run();
    }

    @Override
    protected String getScribeFolder(int engine, int language) {
        return String.format(mobile ? PIXEL_MOBILE_DIR_FORMAT : PIXEL_DIR_FORMAT, engine, language);
    }
}