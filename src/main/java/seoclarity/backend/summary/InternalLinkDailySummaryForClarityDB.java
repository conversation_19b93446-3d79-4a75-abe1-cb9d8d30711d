package seoclarity.backend.summary;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.actonia.ClarityDBVersioningDAO;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.dao.actonia.InternalLinkNodeStatusDAO;
import seoclarity.backend.dao.clickhouse.bot.SiteMapDetailDao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkImpl;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkServer1Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkServer2Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkServer3Dao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.entity.NodeStatusVO;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.actonia.ClaritydbVersioning;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.entity.actonia.InternalLinkNodeStatus;
import seoclarity.backend.entity.clickhouse.internallink.ProcessListVO;
import seoclarity.backend.utils.ClarityDBAPIUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public class InternalLinkDailySummaryForClarityDB {
	
	private InternalLinkServer1Dao internalLinkServer1Dao;
	private InternalLinkServer2Dao internalLinkServer2Dao;
	private InternalLinkServer3Dao internalLinkServer3Dao;
	
	private CrawlRequestLogDAO crawlRequestLogDAO;
	
	private ClarityDBVersioningDAO clarityDBVersioningDAO;
	
	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;
	
//	private CrawlRequestModifylog crawlRequestModifylog;
	
	private List<ProcessListVO> finalProssList = new ArrayList<>();

	private List<ProcessListVO> tmpProcessList = new ArrayList<>();
	
	private Gson gson = new Gson();
	
	private final static Integer MAX_RECORDS_IN_ONE_TIME = 5000000;
	private final static Integer DEFAULT_VERSION_FOR_INTERNAL_LINK = 0;
	private static Integer version = DEFAULT_VERSION_FOR_INTERNAL_LINK;
	private static Integer sepCrawlLogId;
	private static Integer sepDomainId;
	
	private static boolean reprocessFlag = false;
	
	private static final Date DEFAULT_VERSION_DATE_FOR_INTERNAL_LINK = FormatUtils.toDate("19700101", "yyyyMMdd");
	
	private static final String databaseName = "actonia_internal_link";
	
	private static final String FINAL_TABLE_NAME = "dis_internal_link_sampled_view_final";
	//RE-create this distribute table for api to query for temp data only
	private static final String TMP_TABLE_NAME_FOR_QUERY = "dis_internal_link_sampled_view_temp";
	private static final String TMP_DIS_TABLE_NAME = "dis_internal_link_sampled_intermediate_view";
	//20190624_02
	private static final String HOURLY_TMP_LOCAL_TABLE_NAME = "new_local_internal_link_sampled_view_" + FormatUtils.formatDate(new Date(), "yyyyMMdd_HH");
	private static final String HOURLY_LOCAL_TABLE_NAME = "local_internal_link_sampled_view_" + FormatUtils.formatDate(new Date(), "yyyyMMdd_HH");
	private static final String HOURLY_LOCAL_TABLE_NAME_PREFIX = "local_internal_link_sampled_view_2%";
	
	private InternalLinkNodeStatusDAO internalLinkNodeStatusDAO;
	//claritydb_upload_log id -- mdb010
	private static Integer logId;
	
	public static List<Integer> ignoreCrawlIdList = new ArrayList<>();
	static {
		ignoreCrawlIdList.add(11111157);
		ignoreCrawlIdList.add(11122233);
		ignoreCrawlIdList.add(99923102);
		ignoreCrawlIdList.add(99923103);
		ignoreCrawlIdList.add(101010118);
		ignoreCrawlIdList.add(101013121);
		ignoreCrawlIdList.add(101023121);
		ignoreCrawlIdList.add(991758012);
		ignoreCrawlIdList.add(333445);
		ignoreCrawlIdList.add(991122);
		ignoreCrawlIdList.add(991123);
		ignoreCrawlIdList.add(1112202);
		ignoreCrawlIdList.add(1112206);
		ignoreCrawlIdList.add(143334);
		ignoreCrawlIdList.add(9921132);
		ignoreCrawlIdList.add(123291);
		ignoreCrawlIdList.add(999830);
		
		//https://www.wrike.com/open.htm?id=775219263
		ignoreCrawlIdList.add(9917580);
		ignoreCrawlIdList.add(9917586);
	}
	
	public InternalLinkDailySummaryForClarityDB(){
		internalLinkServer1Dao = SpringBeanFactory.getBean("internalLinkServer1Dao");
		internalLinkServer2Dao = SpringBeanFactory.getBean("internalLinkServer2Dao");
		internalLinkServer3Dao = SpringBeanFactory.getBean("internalLinkServer3Dao");
		crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
//		crawlRequestModifylog = SpringBeanFactory.getBean("crawlRequestModifylog");
		clarityDBVersioningDAO = SpringBeanFactory.getBean("clarityDBVersioningDAO");
		internalLinkNodeStatusDAO = SpringBeanFactory.getBean("internalLinkNodeStatusDAO");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		disSiteCrawlDoc1Dao = SpringBeanFactory.getBean("disSiteCrawlDoc1Dao");
	}
	
	private static boolean onlyRunForFinalHourly = false;

	public static void main(String[] args) {
		InternalLinkDailySummaryForClarityDB internalLinkDailySummaryForClarityDB = new InternalLinkDailySummaryForClarityDB();
		
		if (args != null && args.length == 1) {
			onlyRunForFinalHourly = BooleanUtils.toBoolean(args[0]);
			System.out.println("====== start process on onlyRunForFinalHourly : " + onlyRunForFinalHourly);
		}
		
		if (args != null && args.length >= 4) {
			reprocessFlag = BooleanUtils.toBoolean(args[0]);
			sepCrawlLogId =  NumberUtils.toInt(args[1]);
			sepDomainId = NumberUtils.toInt(args[2]);
			version = NumberUtils.toInt(args[3]);
		}
		
		System.out.println("====== start process on reprocessFlag : " + reprocessFlag + ", sepCrawlLogId : " 
				+ sepCrawlLogId + ", sepDomainId : " + sepDomainId + ", version : " + version);
		
//		if (internalLinkDailySummaryForClarityDB.checkIsProcessingUpload()) {
//			System.out.println("There found more than one processing still not finished, exit !!!");
//			System.exit(-1);
//		}
		
		if (!reprocessFlag) {
			internalLinkDailySummaryForClarityDB.init();
		}
		
		if (reprocessFlag) {
			internalLinkDailySummaryForClarityDB.reprocess();
		} else  if(onlyRunForFinalHourly) {
			internalLinkDailySummaryForClarityDB.processHourly();
		} else {
			internalLinkDailySummaryForClarityDB.process();
		}
		
		
	}
	
	private void init() {
		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
		
		clarityDBUploadLogEntity.setTmpTableName(FINAL_TABLE_NAME);
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName(" ");
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
		clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK_SUMMARY);
		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);
		
		logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}
	
	
	private void processHourly() {
		
		SERVER_LIST.add(internalLinkServer1Dao);
		SERVER_LIST.add(internalLinkServer2Dao);
		SERVER_LIST.add(internalLinkServer3Dao);
		
		long startTime = System.currentTimeMillis();
		
		//step2. load summary data 
		try {
			List<ProcessListVO> needProcessCrawler = internalLinkServer1Dao.getSummaryList();
			
			System.out.println("needProcessCrawler size : " + needProcessCrawler!= null ? needProcessCrawler.size() : 0);
			
			for(ProcessListVO vo : needProcessCrawler) {
				
				Integer ownDomainId = vo.getDomain_id_i();
				Integer crawlRequestId = vo.getCrawl_request_log_id_i();
				
				System.out.println("===crawlRequestId:" + crawlRequestId + ", contains:" + ignoreCrawlIdList.contains(crawlRequestId));
				if (ignoreCrawlIdList.contains(crawlRequestId)) {
					System.out.println("==== Skip for crawl in the ignore list, cid:" + crawlRequestId);
					continue;
				}
				
				CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getCrawlerByOwnDomainIdAndCrawlId(ownDomainId, crawlRequestId);
				
				if (crawlRequestLog == null) {
					System.out.println("Crawl not found! crawlRequestId:" + crawlRequestId);
					continue;
				}
				
				if (crawlRequestLog != null && crawlRequestLog.getId() > 0 && crawlRequestLog.getAdditionalStatus() != null
						&& crawlRequestLog.getAdditionalStatus() == CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE) {
					continue;
					
				} else if (crawlRequestLog != null && crawlRequestLog.getId() > 0 && (crawlRequestLog.getAdditionalStatus() == null
						|| crawlRequestLog.getAdditionalStatus() != CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE)) {
					
					String webServiceUrl = String.format(STR_NODE_STATUS_API, crawlRequestId);
					
					//response: {"totalNodes":13,"completedNodes":13,"completed":true}
					String response = HttpRequestUtils.queryWebServiceFunctionByMethod(webServiceUrl, "GET", null);
					
					System.out.println("response:" + response);
					
					NodeStatusVO nodeStatusVO = gson.fromJson(response, NodeStatusVO.class);
					if (nodeStatusVO != null && nodeStatusVO.getCompleted() != null && nodeStatusVO.getCompleted()) {
//						InternalLinkNodeStatus nodeStatus = internalLinkNodeStatusDAO.getAggInfoByCrawlRequestId(crawlRequestId);
//						System.out.println("===========================");
//						System.out.println("nodeStatus.getCnt().intValue():" + nodeStatus.getCnt().intValue());
//						System.out.println("nodeStatusVO.getCompletedNodes().intValue():" + nodeStatusVO.getCompletedNodes().intValue());
//						System.out.println(nodeStatusVO.getCompletedNodes().intValue() >= nodeStatus.getCnt().intValue());
//						System.out.println("===========================");
//						
//						if (nodeStatus!= null && nodeStatus.getCnt() != null && nodeStatus.getCnt() > 0
//								&& nodeStatusVO.getCompletedNodes() != null 
//								&& nodeStatus.getCnt().intValue() == nodeStatusVO.getCompletedNodes().intValue()) {
//							Long totalDocumentCnt = nodeStatus.getPageLinkItem();
//							Long totalCount = internalLinkServer1Dao.getUniqueDocumentCountByCrawlRequestIdAndDomainId(ownDomainId, crawlRequestId);
//						
//							// if we have documents more than percentage of total count, then it's finished
//							if (totalDocumentCnt != null && totalCount != null 
//									&& totalDocumentCnt >= totalCount * percentage) {
//								System.out.println("==== crawler is finished, summary in final table");
//								finalProssList.add(vo);
//								continue;
//							} else {
//								System.out.println("==== crawler is not finished, summary in tmp table");
//							}
//						} else {
//							System.out.println("==== not found in node status");
//						}
						
						Integer count = disSiteCrawlDoc1Dao.getCountOfSiteHealthPageLink(ownDomainId, crawlRequestId);
						Long totalCount = internalLinkServer1Dao.getUniqueDocumentCountByCrawlRequestIdAndDomainId(ownDomainId, crawlRequestId);
//						
//							// if we have documents more than percentage of total count, then it's finished
						if (count != null && 
								totalCount != null && 
								totalCount >= count * percentage) {
							System.out.println("==== crawler is finished, sumamry in final table : " + crawlRequestId + ", ttSH:" + count + ", ttITL:" + totalCount);
							finalProssList.add(vo);
							continue;
						} else {
							System.out.println("==== crawler is not finished, ttSH:" + count + ", ttITL:" + totalCount);
						}
					}
					
				} else {
					System.out.println("==== crawl not found, skiped!!! cid:" + crawlRequestLog.getId());
				}
				
			}
			
			if (CollectionUtils.isEmpty(finalProssList)) {
				System.out.println("No more crawler need to process today!!!");
			} else {
				
				try {
					crawlRequestLogDAO.updateAdditionalStatus(finalProssList);
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				System.out.println("==== start summary, final process list : " + finalProssList.size());
				System.out.println("=== final list:" + gson.toJson(finalProssList));
				summary();
			}
			
			long endTime = System.currentTimeMillis();
			int elapsedSeconds = (int) (endTime - startTime);
			
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, needProcessCrawler.size(), elapsedSeconds, logId);
		} catch (Exception e) {
			e.printStackTrace();
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
		}
	}
	
	
//	private boolean checkIsProcessingUpload(){
//		
//		List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK_SUMMARY);
//		if (!CollectionUtils.isEmpty(list)) {
//			return true;
//		}
//		return false;
//	}
	
	private static final String templateTableName = "local_internal_link_sampled_template";
	private static List<InternalLinkImpl> SERVER_LIST = new ArrayList<>();
	
	private static String STR_NODE_STATUS_API = ClarityDBAPIUtils.API_ENDPOINT_IBM_INTRANET + "seoClarity/siteClarity/getCrawlerStats?accessToken=c09yxv13-opr3-d745-9734-8pu48420nj67&crawl_request_log_id_i=%d&clusterCrawl=true&request=backend";
//	private static String STR_NODE_STATUS_API = "https://api-gw.seoclarity.net/clarityapi/getCrawlerStats?crawl_request_log_id_i=%d&clusterCrawl=true";
	private static float percentage = 0.99f;
	
	private void process() {
		
		SERVER_LIST.add(internalLinkServer1Dao);
		SERVER_LIST.add(internalLinkServer2Dao);
		SERVER_LIST.add(internalLinkServer3Dao);
		
		long startTime = System.currentTimeMillis();
		
		//step1. check if current table exist in db
		for(InternalLinkImpl internalLinkDao : SERVER_LIST) {
			String tableName = "";
			try {
				tableName = internalLinkDao.getTableList(HOURLY_TMP_LOCAL_TABLE_NAME);
			} catch (Exception e) {
				System.out.println(" Table is not found: " + HOURLY_TMP_LOCAL_TABLE_NAME);
			}
			
			if (StringUtils.isNotBlank(tableName)) {
				System.out.println(" Table :" + HOURLY_TMP_LOCAL_TABLE_NAME + " already exist in server : " + internalLinkDao);
				return;
			} else {
				System.out.println("Create table :" + HOURLY_TMP_LOCAL_TABLE_NAME + ", on server : " + internalLinkDao);
				try {
					System.out.println(internalLinkDao.createLocalTable(HOURLY_TMP_LOCAL_TABLE_NAME, templateTableName));
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}			
		
		
		//step2. load summary data 
		try {
			List<ProcessListVO> needProcessCrawler = internalLinkServer1Dao.getSummaryList();
			
			System.out.println("needProcessCrawler size : " + needProcessCrawler!= null ? needProcessCrawler.size() : 0);
			
			for(ProcessListVO vo : needProcessCrawler) {
				
				Integer ownDomainId = vo.getDomain_id_i();
				Integer crawlRequestId = vo.getCrawl_request_log_id_i();
				
				System.out.println("===crawlRequestId:" + crawlRequestId + ", contains:" + ignoreCrawlIdList.contains(crawlRequestId));
				if (ignoreCrawlIdList.contains(crawlRequestId)) {
					System.out.println("==== Skip for crawl in the ignore list, cid:" + crawlRequestId);
					continue;
				}
				
				CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getCrawlerByOwnDomainIdAndCrawlId(ownDomainId, crawlRequestId);
				
				if (crawlRequestLog != null && crawlRequestLog.getId() > 0 && crawlRequestLog.getAdditionalStatus() != null
						&& crawlRequestLog.getAdditionalStatus() == CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE) {
					continue;
				}
				
				String webServiceUrl = String.format(STR_NODE_STATUS_API, crawlRequestId);
				
				//response: {"totalNodes":13,"completedNodes":13,"completed":true}
				String response = HttpRequestUtils.queryWebServiceFunctionByMethod(webServiceUrl, "GET", null);
				System.out.println("response:" + response);
				
				NodeStatusVO nodeStatusVO = gson.fromJson(response, NodeStatusVO.class);
				if (nodeStatusVO != null && nodeStatusVO.getCompleted() != null && nodeStatusVO.getCompleted()) {
//					InternalLinkNodeStatus nodeStatus = internalLinkNodeStatusDAO.getAggInfoByCrawlRequestId(crawlRequestId);
//					
//					if (nodeStatus!= null && nodeStatus.getCnt() != null && nodeStatus.getCnt() > 0
//							&& nodeStatusVO.getCompletedNodes() != null 
//							&& nodeStatus.getCnt().intValue() <= nodeStatusVO.getCompletedNodes().intValue()) {
//						
//						System.out.println("===========================");
//						System.out.println("nodeStatus.getCnt().intValue():" + nodeStatus.getCnt().intValue());
//						System.out.println("nodeStatusVO.getCompletedNodes().intValue():" + nodeStatusVO.getCompletedNodes().intValue());
//						System.out.println(nodeStatusVO.getCompletedNodes().intValue() >= nodeStatus.getCnt().intValue());
//						System.out.println("===========================");
//						Long totalDocumentCnt = nodeStatus.getPageLinkItem();
//						Long totalCount = internalLinkServer1Dao.getUniqueDocumentCountByCrawlRequestIdAndDomainId(ownDomainId, crawlRequestId);
//					
//						// if we have documents more than percentage of total count, then it's finished
//						if (totalDocumentCnt != null && 
//								totalCount != null && 
//								totalDocumentCnt >= totalCount * percentage) {
//							System.out.println("==== crawler is finished, sumamry in final table");
//							finalProssList.add(vo);
//							continue;
//						}
//					
//					} else {
//						System.out.println("==== crawler is finished, sumamry in final table");
//						finalProssList.add(vo);
//					}
					
					Integer count = disSiteCrawlDoc1Dao.getCountOfSiteHealthPageLink(ownDomainId, crawlRequestId);
					Long totalCount = internalLinkServer1Dao.getUniqueDocumentCountByCrawlRequestIdAndDomainId(ownDomainId, crawlRequestId);
//					
//						// if we have documents more than percentage of total count, then it's finished
					if (count != null && 
							totalCount != null && 
							totalCount >= count * percentage) {
						System.out.println("==== crawler is finished, sumamry in final table : " + crawlRequestId + ", ttSH:" + count + ", ttITL:" + totalCount);
						finalProssList.add(vo);
						continue;
					} else {
						System.out.println("==== crawler is not finished, ttSH:" + count + ", ttITL:" + totalCount);
					}
					
					System.out.println("CrawlRequestDate:" + crawlRequestLog.getCrawlRequestDate() + ", now:" + FormatUtils.formatDateToYyyyMmDd(new Date()));
					System.out.println(DateUtils.addMonths(FormatUtils.toDate(crawlRequestLog.getCrawlRequestDate() + "", "yyyyMMdd"), 1).before(new Date()));
					if (DateUtils.addMonths(FormatUtils.toDate(crawlRequestLog.getCrawlRequestDate() + "", "yyyyMMdd"), 1).before(new Date())) {
						System.out.println("Crawl not updated in one Month, summary into final table!");
						finalProssList.add(vo);
					}
					
				} else {
					System.out.println("==== crawler is not finished, sumamry in tmp table");
					tmpProcessList.add(vo);
				}
				
			
				
//				CrawlRequestLog crawlRequestLog = crawlRequestModifylog.getFinishedCrawlerByOwnDomainIdAndCrawlId(ownDomainId, crawlRequestId);
//				
//				if (crawlRequestLog != null) {
//					System.out.println("==== crawler is finished, sumamry in final table");
//					finalProssList.add(vo);
//				} else {
//					System.out.println("==== crawler is not finished, sumamry in tmp table");
//					tmpProcessList.add(vo);
//				}
				
			}
			
			if (CollectionUtils.isEmpty(finalProssList) && CollectionUtils.isEmpty(tmpProcessList)) {
				System.out.println("No more crawler need to process today!!!");
			} else {
				try {
					crawlRequestLogDAO.updateAdditionalStatus(finalProssList);
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				System.out.println("==== start summary, final process list : " + finalProssList.size() + ", tmp process list :" + tmpProcessList.size());
				System.out.println("=== final list:" + gson.toJson(finalProssList));
				System.out.println("=== tmp list:" + gson.toJson(tmpProcessList));
				summary();
			}
			
			long endTime = System.currentTimeMillis();
			int elapsedSeconds = (int) (endTime - startTime);
			
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, needProcessCrawler.size(), elapsedSeconds, logId);
		} catch (Exception e) {
			e.printStackTrace();
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
		}
		
		//step3. delete tmp table and rename the new tmp table for this hour
		for(InternalLinkImpl internalLinkDao : SERVER_LIST) {
			
			//drop the local table for all table which prefix is HOURLY_LOCAL_TABLE_NAME_PREFIX(local_internal_link_sampled_view_2%)
			try {
				List<String> tableNames = internalLinkDao.getTableListV2(HOURLY_LOCAL_TABLE_NAME_PREFIX);
				
				for(String table : tableNames) {
					if (StringUtils.isNotBlank(table)) {
						try {
							internalLinkDao.dropTable(table);
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}
				}
			} catch (Exception e) {
				System.out.println("No local table is founded, prefix:" + HOURLY_LOCAL_TABLE_NAME_PREFIX);
			}
			
					
			
			// rename from new_ to local_ table
			System.out.println("Rename tmp table to local table : ");
			try {
				System.out.println(internalLinkDao.renameTable(HOURLY_LOCAL_TABLE_NAME, HOURLY_TMP_LOCAL_TABLE_NAME));
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
			
		}
		
		
		String tableName = internalLinkServer1Dao.getTableList(TMP_TABLE_NAME_FOR_QUERY);
		if (StringUtils.isNotBlank(tableName)) {
			System.out.println("!!!! find " + TMP_TABLE_NAME_FOR_QUERY + ", waiting drop, recreate and point to the hourly local table");
			try {
				internalLinkServer1Dao.dropTable(TMP_TABLE_NAME_FOR_QUERY);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		System.out.println("!!! creating tmp dis table for query : TMP_TABLE_NAME_FOR_QUERY ---------------------   ");
		
		try {
			internalLinkServer1Dao.createInsertDistributeTable(TMP_TABLE_NAME_FOR_QUERY, FINAL_TABLE_NAME, HOURLY_LOCAL_TABLE_NAME);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return;
		}
		
	}
	
	
	
	private void reprocess() {
		
		try {
			int maxPopularity = 100;
			
			internalLinkServer1Dao.summary(sepDomainId, sepCrawlLogId, maxPopularity, FINAL_TABLE_NAME, true, version);
			
			clarityDBVersioningDAO.insert(sepDomainId, DEFAULT_VERSION_DATE_FOR_INTERNAL_LINK, ClaritydbVersioning.DATATYPE_INTERNAL_LINK, sepCrawlLogId, version);
			
//			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, needProcessCrawler.size(), elapsedSeconds, logId);
		} catch (Exception e) {
			e.printStackTrace();
//			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
		}
		
	}
	
	private static final Integer SUPPORTED_SITE_LINK_FILTER_NUM = 20000000;
	
	private boolean shouldSupportSiteLinkFilter(Integer totalCount) {
		
		if (totalCount >= SUPPORTED_SITE_LINK_FILTER_NUM) {
			return false;
		}
		return true;
	}
	
	
	/*
	 * daily summary for each crawl request id
	 */
	private void summary() {
		
		// -------------------------------  final  -----------------------------------
		
		
		
		System.out.println("@@@ processing to final table ");
		for (ProcessListVO vo : finalProssList) {
			int maxPopularity = 100;
			
//			if (vo.getCnt() > MAX_RECORDS_IN_ONE_TIME) {
//				
//				List<ProcessListVO> depthList = internalLinkDao.getCountByPopularity(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i());
//				
//				int totalCnt = 0;
//				
//				System.out.println("!!! try to find the correct depth we can process");
//				for (ProcessListVO depthVO : depthList) {
//					
//					totalCnt += depthVO.getCnt();
//					
//					if (totalCnt >= MAX_RECORDS_IN_ONE_TIME) {
//						maxPopularity = depthVO.getPopularity();
//						
//						System.out.println("!!!!!! find the correct maxPopularity, maxPopularity:" + maxPopularity + ", totalCnt:" + totalCnt);
//						break;
//					}
//				}
//				
//			}
			
			Integer totalCount = internalLinkServer1Dao.getTotalCountByCrawlId(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i());
			
			if(shouldSupportSiteLinkFilter(totalCount)) {
				System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
				try {
					internalLinkServer1Dao.summary(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), maxPopularity, FINAL_TABLE_NAME, true, DEFAULT_VERSION_FOR_INTERNAL_LINK);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
				try {
					internalLinkServer1Dao.summaryDoNotSupportSiteLinkFilter(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), maxPopularity, FINAL_TABLE_NAME, true, DEFAULT_VERSION_FOR_INTERNAL_LINK);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
		}
		// -------------------------------  final  -----------------------------------
		
		
		if (!onlyRunForFinalHourly) {
			// -------------------------------  tmp  -----------------------------------
			
			System.out.println("@@@ processing to tmp table ");
			System.out.println("===== create tmp table if not exist");
			
			String tableName = internalLinkServer1Dao.getTableList(TMP_DIS_TABLE_NAME);
			if (StringUtils.isNotBlank(tableName)) {
				System.out.println("!!!! find the table, waiting drop, recreate and point to the daily local table");
				try {
					internalLinkServer1Dao.dropTable(TMP_DIS_TABLE_NAME);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
			System.out.println("!!! creating tmp dis table ---------------------   ");
			
			try {
				internalLinkServer1Dao.createInsertDistributeTable(TMP_DIS_TABLE_NAME, FINAL_TABLE_NAME, HOURLY_TMP_LOCAL_TABLE_NAME);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				return;
			}
			
			
			for (ProcessListVO vo : tmpProcessList) {
				int maxPopularity = 100;
				
//				if (vo.getCnt() > MAX_RECORDS_IN_ONE_TIME) {
//					
//					List<ProcessListVO> depthList = internalLinkDao.getCountByPopularity(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i());
//					
//					int totalCnt = 0;
//					
//					System.out.println("!!! try to find the correct depth we can process, crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
//					for (ProcessListVO depthVO : depthList) {
//						
//						totalCnt += depthVO.getCnt();
//						
//						if (totalCnt >= MAX_RECORDS_IN_ONE_TIME) {
//							maxPopularity = depthVO.getPopularity();
//							
//							System.out.println("!!!!!! find the correct maxPopularity, crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i() + ", maxPopularity:" + maxPopularity + ", totalCnt:" + totalCnt);
//							break;
//						}
//					}
//					
//				}
				
				Integer totalCount = internalLinkServer1Dao.getTotalCountByCrawlId(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i());
				
				if(shouldSupportSiteLinkFilter(totalCount)) {
					System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
					try {
						internalLinkServer1Dao.summary(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), maxPopularity, TMP_DIS_TABLE_NAME, true, DEFAULT_VERSION_FOR_INTERNAL_LINK);
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else {
					System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
					try {
						internalLinkServer1Dao.summaryDoNotSupportSiteLinkFilter(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), maxPopularity, TMP_DIS_TABLE_NAME, true, DEFAULT_VERSION_FOR_INTERNAL_LINK);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				
				
				
			}
			
			// -------------------------------  tmp  -----------------------------------
		}
		
		
		
		
//		System.out.println("===== rename table and delete the summary table for yesterday");
		
//		try {
//			internalLinkDao.renameTable(RENAMED_TABLE_NAME, TMP_TABLE_NAME);
//			internalLinkDao.dropTable(OLD_TMP_TABLE_NAME);
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
	}

}
