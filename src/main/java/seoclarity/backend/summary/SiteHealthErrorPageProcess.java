package seoclarity.backend.summary;

import seoclarity.backend.dao.actonia.ResourceProcessInstanceDao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDocDao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSitehealth3xxDao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSitehealthNon200Dao;
import seoclarity.backend.entity.actonia.ResourceProcessInstanceEntity;
import seoclarity.backend.utils.SpringBeanFactory;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * https://www.wrike.com/open.htm?id=**********
 * https://www.wrike.com/open.htm?id=**********
 * https://www.wrike.com/open.htm?id=**********
 */
public class SiteHealthErrorPageProcess {

    public int MOD_SIZE = 1000;
    public static final int SUB_MOD_SIZE = 100;

    private int processType = 0; // 0: test 1: errorPage 2: 3xx
    private static ResourceProcessInstanceDao resourceInstanceDao;
    private DisSitehealthNon200Dao disSitehealthNon200Dao;
    private DisSiteCrawlDocDao disSiteCrawlDocDao;

    static {
        initProcess();
    }

    public SiteHealthErrorPageProcess() {
        disSitehealthNon200Dao = SpringBeanFactory.getBean("disSitehealthNon200Dao");
        disSiteCrawlDocDao = SpringBeanFactory.getBean("disSiteCrawlDocDao");
    }

    private static void initProcess() {
        resourceInstanceDao = SpringBeanFactory.getBean("resourceProcessInstanceDao");
    }

    public static void main(String[] args) throws Exception {
        new SiteHealthErrorPageProcess().startProcess(args);
    }

    private void startProcess(String[] args) throws Exception {
        if (args != null && args.length > 0) {
            processType = Integer.parseInt(args[0]);
        }
        if (processType == 0) {
            test();
        } else if (processType == 1) {
            processErrorPage(); // https://www.wrike.com/open.htm?id=**********
        } else if (processType == 2) {
            process3xx();
        } else if (processType == 3) {
            deleteInstance(Integer.parseInt(args[1]));
        }
    }

    private void deleteInstance(int id) {
        resourceInstanceDao.deleteById(id);
    }

    private void test() throws Exception {
        System.out.println("start process test");
        resourceInstanceDao.updateStatusAndStartDateById(3, ResourceProcessInstanceEntity.STATUS_PROCESSING, new Date());
        processC3603xx(12631, 9978280);
        resourceInstanceDao.updateStatusAndEndDateById(3, ResourceProcessInstanceEntity.STATUS_SUCCESS, new Date());
        System.out.println("end process test");
    }

    private void processErrorPage() {
        List<ResourceProcessInstanceEntity> processList = resourceInstanceDao.getListByProcessTypeAndStatus(
                ResourceProcessInstanceEntity.PROCESS_TYPE_ERROR_PAGE,
                ResourceProcessInstanceEntity.STATUS_CREATED);
        if (processList == null || processList.isEmpty()) {
            System.out.println("===nothing to process===");
            return;
        }
        for (ResourceProcessInstanceEntity instanceEntity : processList) {
            int id = instanceEntity.getId();
            int ownDomainId = instanceEntity.getOwnDomainId();
            int crawlRequestLogId = Integer.parseInt(instanceEntity.getResourceId());
            resourceInstanceDao.updateStatusAndStartDateById(id, ResourceProcessInstanceEntity.STATUS_PROCESSING, new Date());
            processC360ErrorPage(id, ownDomainId, crawlRequestLogId);
            resourceInstanceDao.updateStatusAndEndDateById(id, ResourceProcessInstanceEntity.STATUS_SUCCESS, new Date());

            // https://www.wrike.com/open.htm?id=**********
            int summary3xxInstanceId = 0;
            try {
                // process dis3xx
                resourceInstanceDao.insert(createInstanceEntity(ownDomainId, crawlRequestLogId, ResourceProcessInstanceEntity.PROCESS_TYPE_3XX_SUMMARY));

                // get summary3xx id
                ResourceProcessInstanceEntity summaryInstanceEntity = resourceInstanceDao.getEntityByProcessTypeAndStatus(ResourceProcessInstanceEntity.PROCESS_TYPE_3XX_SUMMARY, ownDomainId, String.valueOf(crawlRequestLogId), ResourceProcessInstanceEntity.STATUS_CREATED);
                if (summaryInstanceEntity != null) {
                    summary3xxInstanceId = summaryInstanceEntity.getId();
                }
            } catch (Exception e) {
                System.out.println("===summary3xxInertError domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
                e.printStackTrace();
            }
            if (summary3xxInstanceId == 0) {
                System.out.println("===summary3xxInertFail domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " this task will be skip");
                continue;
            }

            // https://www.wrike.com/open.htm?id=**********
            try {
                resourceInstanceDao.updateStatusAndStartDateById(summary3xxInstanceId, ResourceProcessInstanceEntity.STATUS_PROCESSING, new Date());
                processC3603xx(ownDomainId, crawlRequestLogId);
                resourceInstanceDao.updateStatusAndEndDateById(summary3xxInstanceId, ResourceProcessInstanceEntity.STATUS_SUCCESS, new Date());
            } catch (Exception e) {
                System.out.println("===process3xxError instanceId:" + summary3xxInstanceId + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
                resourceInstanceDao.updateStatusAndEndDateById(summary3xxInstanceId, ResourceProcessInstanceEntity.STATUS_ERROR, new Date());
                resourceInstanceDao.updateErrorMsgById(summary3xxInstanceId, e.getMessage());
                e.printStackTrace();
            }
        }
    }


    private void processC360ErrorPage(int instanceId,int ownDomainId, int crawlRequestLogId) {
        try {
            disSitehealthNon200Dao.insertErrorPage(ownDomainId, crawlRequestLogId);
        } catch (Exception e) {
            System.out.println("==processC360ErrorPageError instanceId:" + instanceId + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
            resourceInstanceDao.updateStatusAndEndDateById(instanceId, ResourceProcessInstanceEntity.STATUS_ERROR, new Date());
            resourceInstanceDao.updateErrorMsgById(instanceId, e.getMessage());
            e.printStackTrace();
        }
    }

    private void process3xx() {
        List<ResourceProcessInstanceEntity> processList = resourceInstanceDao.getListByProcessTypeAndStatus(
                ResourceProcessInstanceEntity.PROCESS_TYPE_3XX_SUMMARY,
                ResourceProcessInstanceEntity.STATUS_CREATED);
        if (processList == null || processList.isEmpty()) {
            System.out.println("===nothing to process===");
            return;
        }
        System.out.println("==currentProcessTask size:" + processList.size());
        for (ResourceProcessInstanceEntity instanceEntity : processList) {
            int id = instanceEntity.getId();
            int ownDomainId = instanceEntity.getOwnDomainId();
            int crawlRequestLogId = Integer.parseInt(instanceEntity.getResourceId());

            try {
                resourceInstanceDao.updateStatusAndStartDateById(id, ResourceProcessInstanceEntity.STATUS_PROCESSING, new Date());
                processC3603xx(ownDomainId, crawlRequestLogId);
                resourceInstanceDao.updateStatusAndEndDateById(id, ResourceProcessInstanceEntity.STATUS_SUCCESS, new Date());
            } catch (Exception e) {
                System.out.println("===process3xxError instanceId:" + id + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
                resourceInstanceDao.updateStatusAndEndDateById(id, ResourceProcessInstanceEntity.STATUS_ERROR, new Date());
                resourceInstanceDao.updateErrorMsgById(id, e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public void processC3603xx(Integer ownDomainId, Integer crawlRequestLogId) throws Exception {
        long totalStart = System.currentTimeMillis();

        int crawlDataCount = disSiteCrawlDocDao.getInternalLinkCount(ownDomainId, crawlRequestLogId);
        System.out.println("===processC3603xxCnt ownDomainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " count:" + crawlDataCount);
        if (crawlDataCount <= 0) {
            System.out.println("===NoDataToRun ownDomainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
            return;
        }

        boolean isMod = false;
        if (crawlDataCount > 50000) {
            isMod = true;
        }

        List<String> dateList = disSiteCrawlDocDao.getDateList(ownDomainId, crawlRequestLogId);
        List<Integer> reprocessModList = new ArrayList<>();
        int crawlIdMod = crawlRequestLogId % 1000;
        int tmpModRetry = 0;
        long tmpStart = System.currentTimeMillis();

        if (isMod) {
            for (int i = 0; i < MOD_SIZE; i++) {
                long start = System.currentTimeMillis();
                while (true) {
                    try {
                        disSiteCrawlDocDao.insertTmpTable(ownDomainId, crawlRequestLogId, i, dateList);
                        tmpModRetry = 0;
                        break;
                    } catch (Exception e) {
                        tmpModRetry++;
                        System.out.println("===inertTmpTableError mod:" + i + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " retry:" + tmpModRetry);
                        e.printStackTrace();
                        try {
                            TimeUnit.SECONDS.sleep(10);
                        } catch (InterruptedException ie) {

                        }
                    }
                    if (tmpModRetry > 3) {
                        System.out.println("====tmpInsertRetryError mod:" + i + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " retry:" + tmpModRetry);
                        tmpModRetry = 0;
                        break;
                    }
                }
                long end = System.currentTimeMillis();
                System.out.println("==addTmpInfo mod:" + i + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " time:" + ((end - start) / 1000) + "s");
                try {
                    TimeUnit.MILLISECONDS.sleep(150);
                } catch (InterruptedException e) {

                }
            }
        } else {
            try {
                disSiteCrawlDocDao.insertTmpTableNoMod(ownDomainId, crawlRequestLogId, dateList);
            } catch (Exception e) {
                System.out.println("===inertTmpTableErrorNoMod domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        }

        System.out.println("==addTmpInfo domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " time:" + ((System.currentTimeMillis() - tmpStart) / 1000) + "s");


        try {
            System.out.println("===insertTmpEnd begin sleep domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
            TimeUnit.MINUTES.sleep(2);
        } catch (InterruptedException e) {
            System.out.println("===insertTmpEnd sleepError domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
            e.printStackTrace();
        }
        System.out.println("===insertTmpEnd end sleep domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);

        long dis3xxStart = System.currentTimeMillis();
        if (isMod) {
            for (int i = 0; i < MOD_SIZE; i++) {
                long start = System.currentTimeMillis();
                try {
                    disSiteCrawlDocDao.insert3xxBatch(ownDomainId, crawlRequestLogId, crawlIdMod, i, dateList);
                } catch (Exception e) {
                    reprocessModList.add(i);
                    System.out.println("===inert3xxBatchError mod:" + i + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " willUseSubMod");
                    e.printStackTrace();
                }
                long end = System.currentTimeMillis();
                System.out.println("==add3xxInfo mod:" + i + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " time:" + ((end - start) / 1000) + "s");
                try {
                    TimeUnit.MILLISECONDS.sleep(150);
                } catch (InterruptedException e) {

                }
            }

            if (!reprocessModList.isEmpty()) {
                reprocessBigDataPart(ownDomainId, crawlRequestLogId, crawlIdMod, reprocessModList, dateList);
            }
        } else {
            try {
                disSiteCrawlDocDao.insert3xxBatchNoMod(ownDomainId, crawlRequestLogId, crawlIdMod, dateList);
            } catch (Exception e) {
                System.out.println("===inert3xxBatchErrorNoMod domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
                e.printStackTrace();
                throw new RuntimeException(e);
            }
        }
        System.out.println("==add3xxInfo domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " time:" + ((System.currentTimeMillis() - dis3xxStart) / 1000) + "s");
        long totalEnd = System.currentTimeMillis();
        System.out.println("==process3xxNew domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " time:" + ((totalEnd - totalStart) / 1000) + "s");
    }

    private void reprocessBigDataPart(int ownDomainId, int crawlRequestLogId, int crawlIdMod, List<Integer> reprocessModList, List<String> dateList) {
        System.out.println("===startProcessBigDataPart domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " mod:" + reprocessModList);
        long start = System.currentTimeMillis();
        for (Integer outerMod : reprocessModList) {
            long outerModStart = System.currentTimeMillis();
            for (int j = 0; j < SUB_MOD_SIZE; j++) {
                long subStart = System.currentTimeMillis();
                try {
                    disSiteCrawlDocDao.insert3xxBatchBigData(ownDomainId, crawlRequestLogId, crawlIdMod, outerMod, j, dateList);
                } catch (Exception ex) {
                    System.out.println("===subModError mod:" + outerMod + " subMod:" + j + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId);
                    ex.printStackTrace();
                }
                System.out.println("==subMod mod:" + outerMod + " subMod:" + j + " time:" + ((System.currentTimeMillis() - subStart) / 1000) + "s");
                try {
                    TimeUnit.SECONDS.sleep(2);
                } catch (InterruptedException e) {

                }
            }
            System.out.println("====outerModReprocess mod:" + outerMod + " domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " time:" + ((System.currentTimeMillis() - outerModStart) / 1000) + "s");
        }
        System.out.println("==reprocessBigDataPart domainId:" + ownDomainId + " crawlId:" + crawlRequestLogId + " time:" + ((System.currentTimeMillis() - start) / 1000) + "s");
    }

    // https://www.wrike.com/open.htm?id=**********
    public static void insertErrorPageInfo(int ownDomainId, int requestCrawlLogId) {
        ResourceProcessInstanceEntity entity = resourceInstanceDao.getEntityByProcessType(ResourceProcessInstanceEntity.PROCESS_TYPE_ERROR_PAGE, ownDomainId, String.valueOf(requestCrawlLogId));
        if (entity != null) {
            System.out.println("===errorPageInfoExist domainId:" + ownDomainId + " crawlId:" + requestCrawlLogId + " obj:" + entity);
            int status = entity.getStatus();
            if (status == ResourceProcessInstanceEntity.STATUS_CREATED) {
                ResourceProcessInstanceEntity dis3xxEntity = resourceInstanceDao.getEntityByProcessType(ResourceProcessInstanceEntity.PROCESS_TYPE_3XX_SUMMARY, ownDomainId, String.valueOf(requestCrawlLogId));
                if (dis3xxEntity != null) {
                    System.out.println("===check3xxExist domainId:" + ownDomainId + " crawlId:" + requestCrawlLogId + " obj:" + dis3xxEntity);
                    int dis3xxStatus = dis3xxEntity.getStatus();
                    if (dis3xxStatus == ResourceProcessInstanceEntity.STATUS_CREATED) {
                        resourceInstanceDao.deleteById(dis3xxEntity.getId());
                    }
                }
            }
        } else {
            ResourceProcessInstanceEntity instanceEntity = createInstanceEntity(ownDomainId, requestCrawlLogId, ResourceProcessInstanceEntity.PROCESS_TYPE_ERROR_PAGE);
            resourceInstanceDao.insert(instanceEntity);
        }

    }

    private static ResourceProcessInstanceEntity createInstanceEntity(int ownDomainId, int requestCrawlLogId, int processType) {
        ResourceProcessInstanceEntity resourceProcessInstanceEntity = new ResourceProcessInstanceEntity();
        resourceProcessInstanceEntity.setOwnDomainId(ownDomainId);
        resourceProcessInstanceEntity.setResourceId(String.valueOf(requestCrawlLogId));
        resourceProcessInstanceEntity.setProcessType(processType);
        resourceProcessInstanceEntity.setStatus(0);
        return resourceProcessInstanceEntity;
    }
}
