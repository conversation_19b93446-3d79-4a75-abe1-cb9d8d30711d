package seoclarity.backend.summary;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.dao.actonia.InternalLinkNodeStatusDAO;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkImpl;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkServer1Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkServer2Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkServer3Dao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.entity.NodeStatusVO;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.entity.actonia.InternalLinkNodeStatus;
import seoclarity.backend.entity.clickhouse.internallink.ProcessListVO;
import seoclarity.backend.utils.ClarityDBAPIUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public class InternalLinkTopDailySummaryForClarityDB {
	
	private InternalLinkServer1Dao internalLinkServer1Dao;
	private InternalLinkServer2Dao internalLinkServer2Dao;
	private InternalLinkServer3Dao internalLinkServer3Dao;
	
	private CrawlRequestLogDAO crawlRequestLogDAO;
	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;
	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	
//	private CrawlRequestModifylog crawlRequestModifylog;
	
	private List<ProcessListVO> finalProssList = new ArrayList<>();

	private Gson gson = new Gson();
	
	private final static Integer DEFAULT_VERSION_FOR_INTERNAL_LINK = 0;
//	private static Integer version = DEFAULT_VERSION_FOR_INTERNAL_LINK;
//	private static Integer sepCrawlLogId;
//	private static Integer sepDomainId;
	
//	private static boolean reprocessFlag = false;
	
	private static final String databaseName = "actonia_internal_link";
	
	private static final String FINAL_TABLE_NAME = "distributed_internal_link_merge";
	//RE-create this distribute table for api to query for temp data only
	
	private InternalLinkNodeStatusDAO internalLinkNodeStatusDAO;
	//claritydb_upload_log id -- mdb010
	private static Integer logId;
	
	public static List<Integer> ignoreCrawlIdList = new ArrayList<>();
	static {
		
		
		
		ignoreCrawlIdList.add(10101010);
		ignoreCrawlIdList.add(10101011);
		ignoreCrawlIdList.add(10101013);
		ignoreCrawlIdList.add(10101016);
		ignoreCrawlIdList.add(10101017);
		ignoreCrawlIdList.add(10101018);
		ignoreCrawlIdList.add(10101019);
		ignoreCrawlIdList.add(10101020);
		ignoreCrawlIdList.add(10101091);
		ignoreCrawlIdList.add(11111157);
		ignoreCrawlIdList.add(11122233);
		ignoreCrawlIdList.add(99923100);
		ignoreCrawlIdList.add(99923102);
		ignoreCrawlIdList.add(99923103);
		ignoreCrawlIdList.add(101010118);
		ignoreCrawlIdList.add(101013121);
		ignoreCrawlIdList.add(101013130);
		ignoreCrawlIdList.add(101023121);
		ignoreCrawlIdList.add(555445565);
		ignoreCrawlIdList.add(555445566);
		ignoreCrawlIdList.add(555445568);
		ignoreCrawlIdList.add(555577777);
		ignoreCrawlIdList.add(991346011);
		ignoreCrawlIdList.add(991758011);
		ignoreCrawlIdList.add(991758012);
		ignoreCrawlIdList.add(991758611);
		ignoreCrawlIdList.add(1000263483);
		ignoreCrawlIdList.add(1234134657);
		
		//https://www.wrike.com/open.htm?id=775219263
		ignoreCrawlIdList.add(9917580);
		ignoreCrawlIdList.add(9917586);
	}
	
	public InternalLinkTopDailySummaryForClarityDB(){
		internalLinkServer1Dao = SpringBeanFactory.getBean("internalLinkServer1Dao");
		internalLinkServer2Dao = SpringBeanFactory.getBean("internalLinkServer2Dao");
		internalLinkServer3Dao = SpringBeanFactory.getBean("internalLinkServer3Dao");
		crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
//		crawlRequestModifylog = SpringBeanFactory.getBean("crawlRequestModifylog");
		internalLinkNodeStatusDAO = SpringBeanFactory.getBean("internalLinkNodeStatusDAO");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		disSiteCrawlDoc1Dao = SpringBeanFactory.getBean("disSiteCrawlDoc1Dao");
	}
	
	private static boolean onlyRunForFinalHourly = false;

	public static void main(String[] args) {
		InternalLinkTopDailySummaryForClarityDB internalLinkDailySummaryForClarityDB = new InternalLinkTopDailySummaryForClarityDB();
		
//		if (args != null && args.length == 1) {
//			onlyRunForFinalHourly = BooleanUtils.toBoolean(args[0]);
//			System.out.println("====== start process on onlyRunForFinalHourly : " + onlyRunForFinalHourly);
//		}
//		
//		if (args != null && args.length >= 4) {
//			reprocessFlag = BooleanUtils.toBoolean(args[0]);
//			sepCrawlLogId =  NumberUtils.toInt(args[1]);
//			sepDomainId = NumberUtils.toInt(args[2]);
//			version = NumberUtils.toInt(args[3]);
//		}
//		
//		System.out.println("====== start process on reprocessFlag : " + reprocessFlag + ", sepCrawlLogId : " 
//				+ sepCrawlLogId + ", sepDomainId : " + sepDomainId + ", version : " + version);
//		
//		if (internalLinkDailySummaryForClarityDB.checkIsProcessingUpload()) {
//			System.out.println("There found more than one processing still not finished, exit !!!");
//			System.exit(-1);
//		}
		
//		if (!reprocessFlag) {
			internalLinkDailySummaryForClarityDB.init();
//		}
		
//		if (reprocessFlag) {
//			internalLinkDailySummaryForClarityDB.reprocess();
//		} else  if(onlyRunForFinalHourly) {
//			internalLinkDailySummaryForClarityDB.processHourly();
//		} else {
			internalLinkDailySummaryForClarityDB.process();
//		}
		
		
	}
	
	private void init() {
		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
		
		clarityDBUploadLogEntity.setTmpTableName(FINAL_TABLE_NAME);
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName(" ");
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
		clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK_SUMMARY_V2);
		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);
		
		logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}
	
	private static final Integer CUT_OVER_CRAWL_ID = 9935334;
	private static final Long CUT_OVER_CRAWL_DATE_LONG = 1677628800L;
	
	private static String STR_NODE_STATUS_API = ClarityDBAPIUtils.API_ENDPOINT_IBM_INTRANET + "seoClarity/siteClarity/getCrawlerStats?accessToken=c09yxv13-opr3-d745-9734-8pu48420nj67&crawl_request_log_id_i=%d&clusterCrawl=true&request=backend";
//	private static String STR_NODE_STATUS_API = "https://api-gw.seoclarity.net/clarityapi/getCrawlerStats?crawl_request_log_id_i=%d&clusterCrawl=true";
	private static float percentage = 0.99f;
	
	private void process() {
		
		long startTime = System.currentTimeMillis();
		
		//step2. load summary data 
		try {
			List<ProcessListVO> needProcessCrawler = internalLinkServer1Dao.getSummaryListV2(CUT_OVER_CRAWL_ID, ignoreCrawlIdList, CUT_OVER_CRAWL_DATE_LONG);
			
			System.out.println("needProcessCrawler size : " + (needProcessCrawler!= null ? needProcessCrawler.size() : 0));
			
			for(ProcessListVO vo : needProcessCrawler) {
				
				Integer ownDomainId = vo.getDomain_id_i();
				Integer crawlRequestId = vo.getCrawl_request_log_id_i();
				
				System.out.println("===crawlRequestId:" + crawlRequestId + ", contains:" + ignoreCrawlIdList.contains(crawlRequestId));
				if (ignoreCrawlIdList.contains(crawlRequestId)) {
					System.out.println("==== Skip for crawl in the ignore list, cid:" + crawlRequestId);
					continue;
				}
				
				CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getCrawlerByOwnDomainIdAndCrawlId(ownDomainId, crawlRequestId);
				
				if (crawlRequestLog == null) {
					System.out.println("=== SKIP Crawl not found!");
					continue;
				}
				
				String webServiceUrl = String.format(STR_NODE_STATUS_API, crawlRequestId);
				
				//response: {"totalNodes":13,"completedNodes":13,"completed":true}
				String response = HttpRequestUtils.queryWebServiceFunctionByMethod(webServiceUrl, "GET", null);
				System.out.println("response:" + response);
				
				NodeStatusVO nodeStatusVO = gson.fromJson(response, NodeStatusVO.class);
				if (nodeStatusVO != null && nodeStatusVO.getCompleted() != null && nodeStatusVO.getCompleted()) {
					
					Integer count = disSiteCrawlDoc1Dao.getCountOfSiteHealthPageLink(ownDomainId, crawlRequestId);
					
//					InternalLinkNodeStatus nodeStatus = internalLinkNodeStatusDAO.getAggInfoByCrawlRequestId(crawlRequestId);
//					
//					if (nodeStatus!= null && nodeStatus.getCnt() != null && nodeStatus.getCnt() > 0
//							&& nodeStatusVO.getCompletedNodes() != null 
//							&& nodeStatus.getCnt().intValue() == nodeStatusVO.getCompletedNodes().intValue()) {
						
//						System.out.println("===========================");
//						System.out.println("nodeStatus.getCnt().intValue():" + nodeStatus.getCnt().intValue());
//						System.out.println("nodeStatusVO.getCompletedNodes().intValue():" + nodeStatusVO.getCompletedNodes().intValue());
//						System.out.println(nodeStatusVO.getCompletedNodes().intValue() == nodeStatus.getCnt().intValue());
//						System.out.println("===========================");
//						Long totalDocumentCnt = nodeStatus.getPageLinkItem();
						Long totalCount = internalLinkServer1Dao.getUniqueDocumentCountByCrawlRequestIdAndDomainId(ownDomainId, crawlRequestId);
//					
//						// if we have documents more than percentage of total count, then it's finished
						if (count != null && 
								totalCount != null && 
								totalCount >= count * percentage) {
							System.out.println("==== crawler is finished, sumamry in final table : " + crawlRequestId + ", ttSH:" + count + ", ttITL:" + totalCount);
							finalProssList.add(vo);
							continue;
						}
//					} else {
//						System.out.println("==== not found in node status");
//					}
					
				} else {
					System.out.println("==== crawler is not finished : " + crawlRequestId);
				}
				
				System.out.println("CrawlRequestDate:" + crawlRequestLog.getCrawlRequestDate() + ", now:" + FormatUtils.formatDateToYyyyMmDd(new Date()));
				System.out.println(DateUtils.addMonths(FormatUtils.toDate(crawlRequestLog.getCrawlRequestDate() + "", "yyyyMMdd"), 1).before(new Date()));
				if (DateUtils.addMonths(FormatUtils.toDate(crawlRequestLog.getCrawlRequestDate() + "", "yyyyMMdd"), 1).before(new Date())) {
					System.out.println("Crawl not updated in one Month, summary into final table!");
					finalProssList.add(vo);
				}
			}
			
//			if (CollectionUtils.isEmpty(finalProssList) && CollectionUtils.isEmpty(tmpProcessList)) {
			if (CollectionUtils.isEmpty(finalProssList)) {
				System.out.println("No more crawler need to process today!!!");
			} else {
				
				
//				System.out.println("==== start summary, final process list : " + finalProssList.size() + ", tmp process list :" + tmpProcessList.size());
				System.out.println("==== start summary, final process list : " + finalProssList.size());
				System.out.println("=== final list:" + gson.toJson(finalProssList));
//				System.out.println("=== tmp list:" + gson.toJson(tmpProcessList));
				summary();
				
				try {
					crawlRequestLogDAO.updateAdditionalStatusV2(finalProssList);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			long endTime = System.currentTimeMillis();
			int elapsedSeconds = (int) (endTime - startTime);
			
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, needProcessCrawler.size(), elapsedSeconds, logId);
		} catch (Exception e) {
			e.printStackTrace();
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
		}
		
		
	}
	
	
	/*
	 * daily summary for each crawl request id
	 */
	private void summary() {
		List<InternalLinkImpl> SERVER_LIST = new ArrayList<>();
		SERVER_LIST.add(internalLinkServer1Dao);
		SERVER_LIST.add(internalLinkServer2Dao);
		SERVER_LIST.add(internalLinkServer3Dao);
		
		// -------------------------------  final  -----------------------------------
		
		System.out.println("@@@ processing to final table ");
		for (ProcessListVO vo : finalProssList) {
			
			System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
			try {
				for(InternalLinkImpl dao : SERVER_LIST) {
					System.out.println("===== Summary by dest url from local!");
					dao.summaryTopLevelByDestationUrl(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i());
				}
				
				System.out.println("===== Summary by source url from dis!");
				internalLinkServer1Dao.summaryTopLevelBySourceUrl(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i());
				
				System.out.println("===== Summary to final merge table!");
				internalLinkServer1Dao.summaryTopLevelFinalMerge(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i());
				
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		}
		// -------------------------------  final  -----------------------------------
		
	}

}
