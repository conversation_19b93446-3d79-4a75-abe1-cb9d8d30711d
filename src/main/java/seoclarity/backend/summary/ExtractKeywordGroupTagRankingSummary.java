package seoclarity.backend.summary;

import java.io.BufferedWriter;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import seoclarity.backend.dao.actonia.CompetitorDailySummaryBingEntityDAO;
import seoclarity.backend.dao.actonia.CompetitorDailySummaryBingEntityTestDAO;
import seoclarity.backend.dao.actonia.GroupTagCompetitorDailySummaryEntityDAO;
import seoclarity.backend.dao.actonia.GroupTagCompetitorDailySummaryEntityTestDAO;
import seoclarity.backend.entity.actonia.CompetitorDailySummaryBingEntity;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.actonia.GroupTagCompetitorDailySummaryEntity;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.BaseService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public class ExtractKeywordGroupTagRankingSummary extends BaseService {

	private static SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
	
	public final static String SPLIT = "\t";

	private static Date exportDate;
	private static int type = 1;	
	
	private GroupTagCompetitorDailySummaryEntityTestDAO groupTagCompetitorDailySummaryEntityTestDAO;
	
	private CompetitorDailySummaryBingEntityTestDAO competitorDailySummaryBingEntityTestDAO;
	
	private GroupTagCompetitorDailySummaryEntityDAO groupTagCompetitorDailySummaryEntityDAO;
	
	private CompetitorDailySummaryBingEntityDAO competitorDailySummaryBingEntityDAO;

	
	public ExtractKeywordGroupTagRankingSummary() {
		super();
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		competitorDailySummaryBingEntityTestDAO = SpringBeanFactory.getBean("competitorDailySummaryBingEntityTestDAO");
		groupTagCompetitorDailySummaryEntityTestDAO = SpringBeanFactory.getBean("groupTagCompetitorDailySummaryEntityTestDAO");
		competitorDailySummaryBingEntityDAO = SpringBeanFactory.getBean("competitorDailySummaryBingEntityDAO");
		groupTagCompetitorDailySummaryEntityDAO = SpringBeanFactory.getBean("groupTagCompetitorDailySummaryEntityDAO");
	}

	@Override
	public void process(OwnDomainEntity ownDomainEntity) throws Exception {
		// https://www.wrike.com/open.htm?id=22873697 (phase 1)
		int domainId = ownDomainEntity.getId();
		int searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
		int searchLanguageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);

		System.out.println("=====OID:" + domainId + " engine:" + searchEngineId + " language:" + searchLanguageId + 
			" exportDate:" + DateFormatUtils.format(exportDate, "yyyyMMdd"));

		processRequest(ownDomainEntity, exportDate);
	}
	

	public static void main(String[] args) throws Exception {
		if (args != null && args.length >= 2) {
			if (org.apache.commons.lang.StringUtils.isBlank(args[0])) {
				exportDate = CommonUtils.getYesterday(true);
			} else {
				exportDate = df.parse(args[0]);
			}
			
			type = NumberUtils.toInt(args[1]);
			
		} else {
			System.out.println("Invalid parameters!");
			return;
		}
		
		System.out.println("=================================exportDate:\"" + exportDate);
		ExtractKeywordGroupTagRankingSummary keywordGroupTagRankingSummary = new ExtractKeywordGroupTagRankingSummary();
		try {
			keywordGroupTagRankingSummary.init();
		} catch (Exception e) {
			
			e.printStackTrace();
		}
	}

	public void processRequest(OwnDomainEntity ownDomainEntity, Date exportDate) throws Exception {
		int domainId = ownDomainEntity.getId();
		List<DomainSearchEngineRelEntity> engineList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
		try {
			System.out.println("================OID" + domainId + " , processDate: " + exportDate);
			
			
			long a = System.currentTimeMillis();
			summaryCompetitorKeyword(ownDomainEntity, true, exportDate, 0);
			long b = System.currentTimeMillis();
			System.out.println("summaryOwnDomainKeyword: " + (b - a) * 1.0 / 1000);

			for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : engineList) {
				summaryCompetitorKeyword(ownDomainEntity, false, exportDate, domainSearchEngineRelEntity.getSearchEngineId());
			}
			long c = System.currentTimeMillis();
			System.out.println(ownDomainEntity.getId() + "  - Success cost: " + (c - b) * 1.0 / 1000);
		} catch (Exception e) {
			System.out.println(ownDomainEntity.getId() + " - Fail");
			e.printStackTrace();
		}
		
		
	}
	
	
	private void summaryCompetitorKeyword(OwnDomainEntity ownDomainEntity, boolean isPrimeEngine, Date processDate, int searchEngineId) {
		
		if (isPrimeEngine) {
			
			if (type == 1) {
				List<GroupTagCompetitorDailySummaryEntity> summaryEntities = groupTagCompetitorDailySummaryEntityTestDAO.getListByOwnDomainIdAndGroupTagIdAndLogDate(ownDomainEntity.getId(), processDate);
				
				System.out.println("======OID:" + ownDomainEntity.getId() + ", processDate:" + FormatUtils.formatDateToYyyyMmDd(processDate) + ", size:" + summaryEntities.size());
				
				writeIntoFileV2(summaryEntities, true);
				
			} else if (type == 2) {
				List<GroupTagCompetitorDailySummaryEntity> summaryEntities2 = groupTagCompetitorDailySummaryEntityDAO.getListByOwnDomainIdAndGroupTagIdAndLogDate(ownDomainEntity.getId(), processDate);
				
				System.out.println("======OID:" + ownDomainEntity.getId() + ", processDate:" + FormatUtils.formatDateToYyyyMmDd(processDate) + ", size:" + summaryEntities2.size());
				writeIntoFileV2(summaryEntities2, false);
			}
			
		} else {
			
			List<Integer> allTagList = groupTagEntityDAO.getTagIdViaOwnDomainIdAndType(ownDomainEntity.getId(), GroupTagEntity.TAG_TYPE_KEYWORD, null);
			
			for (Integer tagid : allTagList) {
				if (type == 3) {
					List<CompetitorDailySummaryBingEntity> summaryEntities = competitorDailySummaryBingEntityTestDAO.getListByOwnDomainIdAndGroupTagIdAndLogDate(tagid, ownDomainEntity.getId(), processDate, searchEngineId);
					System.out.println("======OID:" + ownDomainEntity.getId() + ", processDate:" + FormatUtils.formatDateToYyyyMmDd(processDate) + ", tagid :" + tagid + ", size:" + summaryEntities.size());
					writeIntoFile(summaryEntities, true);
					
				} else if (type == 4) {
					List<CompetitorDailySummaryBingEntity> summaryEntities2 = competitorDailySummaryBingEntityDAO.getListByOwnDomainIdAndGroupTagIdAndLogDate(tagid, ownDomainEntity.getId(), processDate, searchEngineId);
					System.out.println("======OID:" + ownDomainEntity.getId() + ", processDate:" + FormatUtils.formatDateToYyyyMmDd(processDate) + ", tagid :" + tagid + ", size:" + summaryEntities2.size());
					writeIntoFile(summaryEntities2, false);
				}
			}
		}
	}
	
	
	private void writeIntoFile(List<CompetitorDailySummaryBingEntity> summaryEntities, boolean isTestData){
		try {

			StringBuffer sb = new StringBuffer("");

			for (CompetitorDailySummaryBingEntity entity : summaryEntities) {
				sb.append(entity.getOwnDomainId()).append(SPLIT);
				sb.append(entity.getOwnDomainName()).append(SPLIT);
				sb.append(entity.getCompetitorId()).append(SPLIT);
				sb.append(entity.getDomain()).append(SPLIT);
				sb.append(entity.getGroupTagId()).append(SPLIT);
				sb.append(entity.getGroupTagName()).append(SPLIT);
				sb.append(entity.getSearchEngineId()).append(SPLIT);
				sb.append(entity.getTrackDate()).append(SPLIT);
				sb.append(entity.getCountRankTop3()).append(SPLIT);
				sb.append(entity.getCountRankTop10()).append(SPLIT);
				sb.append(entity.getCountRankTop30()).append(SPLIT);
				sb.append(entity.getCountRankTop50()).append(SPLIT);
				sb.append(entity.getCountRankTop100()).append(SPLIT);
				sb.append(entity.getTotalKeyword()).append(SPLIT);
				sb.append(entity.getRankInPage1()).append(SPLIT);
				sb.append(entity.getRankInPage2()).append(SPLIT);
				sb.append(entity.getRankInPage3()).append(SPLIT);
				sb.append(entity.getRankInPage4()).append(SPLIT);
				sb.append(entity.getRankInPage5()).append(SPLIT);
				sb.append(entity.getCountRankTop1()).append(SPLIT);
				sb.append(entity.getCountKeywordRank2()).append(SPLIT);
				sb.append(entity.getCountKeywordRank3()).append(SPLIT);
				sb.append(entity.getCountKeywordRank4()).append(SPLIT);
				sb.append(entity.getCountKeywordRank5()).append(SPLIT);
				sb.append(entity.getCountKeywordRank6()).append(SPLIT);
				sb.append(entity.getCountKeywordRank7()).append(SPLIT);
				sb.append(entity.getCountKeywordRank8()).append(SPLIT);
				sb.append(entity.getCountKeywordRank9()).append(SPLIT);
				sb.append(entity.getCountKeywordRank10()).append(SPLIT);
				sb.append(entity.getWtdAvgRank()).append(SPLIT);
				sb.append(entity.getAvgRank()).append(SPLIT);
				
				sb.append("\n");
			}
				
			FileWriter writer = new FileWriter(isTestData ? "/home/<USER>/temp/competitorBingEntityClarity.txt" : "/home/<USER>/temp/competitorBingEntityVertica.txt", true);

			BufferedWriter bw = new BufferedWriter(writer);

			bw.write(sb.toString());

			bw.close();

			writer.close();

		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	
	private void writeIntoFileV2(List<GroupTagCompetitorDailySummaryEntity> summaryEntities, boolean isTestData){
		try {
			StringBuffer sb = new StringBuffer("");

			for (GroupTagCompetitorDailySummaryEntity entity : summaryEntities) {
				sb.append(entity.getOwnDomainId()).append(SPLIT);
				sb.append(entity.getOwnDomainName()).append(SPLIT);
				sb.append(entity.getCompetitorId()).append(SPLIT);
				sb.append(entity.getDomain()).append(SPLIT);
				sb.append(entity.getGroupTagId()).append(SPLIT);
				sb.append(entity.getGroupTagName()).append(SPLIT);
				sb.append(entity.getLogDate()).append(SPLIT);
				sb.append(entity.getKeywordCountRankTop3()).append(SPLIT);
				sb.append(entity.getKeywordCountRankTop10()).append(SPLIT);
				sb.append(entity.getKeywordCountRankTop30()).append(SPLIT);
				sb.append(entity.getKeywordCountRankTop50()).append(SPLIT);
				sb.append(entity.getKeywordCountRankTop100()).append(SPLIT);
				sb.append(entity.getTotalKeyword()).append(SPLIT);
				sb.append(entity.getRankInPage1()).append(SPLIT);
				sb.append(entity.getRankInPage2()).append(SPLIT);
				sb.append(entity.getRankInPage3()).append(SPLIT);
				sb.append(entity.getRankInPage4()).append(SPLIT);
				sb.append(entity.getRankInPage5()).append(SPLIT);
				sb.append(entity.getCountKeywordRank1()).append(SPLIT);
				sb.append(entity.getCountKeywordRank2()).append(SPLIT);
				sb.append(entity.getCountKeywordRank3()).append(SPLIT);
				sb.append(entity.getCountKeywordRank4()).append(SPLIT);
				sb.append(entity.getCountKeywordRank5()).append(SPLIT);
				sb.append(entity.getCountKeywordRank6()).append(SPLIT);
				sb.append(entity.getCountKeywordRank7()).append(SPLIT);
				sb.append(entity.getCountKeywordRank8()).append(SPLIT);
				sb.append(entity.getCountKeywordRank9()).append(SPLIT);
				sb.append(entity.getCountKeywordRank10()).append(SPLIT);
				sb.append(entity.getWtdAvgRank()).append(SPLIT);
				sb.append(entity.getAvgRank()).append(SPLIT);
				
				sb.append("\n");
			}
				
			FileWriter writer = new FileWriter(isTestData ? "/home/<USER>/temp/groupTagCompetitorSummaryClarityDB.txt" : "/home/<USER>/temp/groupTagCompetitorSummaryVertica.txt", true);

			BufferedWriter bw = new BufferedWriter(writer);

			bw.write(sb.toString());

			bw.close();

			writer.close();

		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

}
