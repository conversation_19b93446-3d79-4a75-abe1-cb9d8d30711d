package seoclarity.backend.summary;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;

import seoclarity.backend.dao.actonia.AutoRunInstanceEntityDAO;
import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.actonia.AutorunInfoEntity;
import seoclarity.backend.entity.actonia.AutorunInstanceEntity;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

public class ClarityDBGscDailyUploadV2 {

    public static String needUploadFolder = "/home/<USER>/gsc/needUpload";
    public static String uploadingFolder = "/home/<USER>/gsc/uploading/" + FormatUtils.formatDate(new Date(), "yyyyMMddHH") + "/";
    public static String backUpFolder = "/home/<USER>/gsc/backUpFolder";
    public static String errorFolder = "/home/<USER>/gsc/error";
    public static String duplicateFolder = "/home/<USER>/gsc/duplicate";

    private static final String databaseName = "actonia_gsc";
    private static final String finalTableName = "gsc_all_in_one";

    //gsc_476_2042_20180710_1_xxxxxxxxxx.txt;
    private final static String dailyTxtRegex = "gsc_\\d+_\\d+_20\\d{6}_\\d+_\\w+\\.txt";
    private final String[] dateParser = new String[]{"yyyy-MM-dd", "yyyyMMdd"};
    private final int maxInsertCount = 100000;

    private static List<String> processingFileList = new ArrayList<String>();

    private static List<String> uniqueKeyList = new ArrayList<String>();

    private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
    private GscBaseDao gscBaseDao;
    private AutoRunInstanceEntityDAO autoRunInstanceEntityDAO;

    private static boolean occurError = false;

    private Integer logId;

    private String[] headers = new String[] {
            "clicks","ctr","impressions","countrycd","device","kName","url","date","position","domainid","reldomainid","versioning","type","searchAppearance"
    };
    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n').withHeader(headers);

    ClarityDBGscDailyUploadV2() {
        gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
        clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
        autoRunInstanceEntityDAO = SpringBeanFactory.getBean("autoRunInstanceEntityDAO");
    }

    public static void main(String[] args) throws IOException {
        ClarityDBGscDailyUploadV2 clarityDBGscDailyUpload = new ClarityDBGscDailyUploadV2();
        if (args != null && args.length >= 1) {
            needUploadFolder = args[0];
        }

        // if processing on other server, then wait till next hour
        if (clarityDBGscDailyUpload.checkIsProcessingUpload()) {
            System.out.println("There found more than one processing still not finished, exit !!!");
            System.exit(-1);
        }

        System.out.println(" base folder is : " + needUploadFolder );
        clarityDBGscDailyUpload.process();
    }

    public void process() {


        //1. move all files into uploading folder one by one
        //2. check unique key for OID/PID/DATE, ignore the same key and move file to duplicate folder
        //3. check if already have data in clarityDB, ignore if exist and move file to duplicate folder
        //4. upload to clarityDB
        //5. if catch any exception, then move file to error folder
        //6. all success move file to bk folder
        try{
            File folder = new File(needUploadFolder);
            if (folder == null || !folder.isDirectory()) {
                System.out.println("needUpload folder is not exist, mkdir: " + needUploadFolder);
                folder.mkdir();
            }

            if (folder.listFiles() == null || folder.listFiles().length == 0) {
                System.out.println(needUploadFolder+" is empty, please check.");
                System.exit(1);
            }

            //move all files to uploading folder
            moveFilesToProcessingFolder();
            if (!CollectionUtils.isNotEmpty(processingFileList) || processingFileList.size() <= 0) {
                insertEmptyLog();
                System.out.println("There do not have any files need to be process, skiped+++");
                System.exit(-1);
            }

            ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();

            clarityDBUploadLogEntity.setDatabaseName(databaseName);
            clarityDBUploadLogEntity.setFinalTableName(finalTableName);
            clarityDBUploadLogEntity.setTmpTableName(finalTableName);
            clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_GSC);
            clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);

            try {
                clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
            } catch (UnknownHostException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
                clarityDBUploadLogEntity.setServerIp("Unknow server");
            }

            clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
            clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);


            //insert one new records to upoad log table
            logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);

            System.out.println("===== create clarityDB upload log ID: " + logId);


            System.out.println("==== moving need upload files into uploading foler, folder : " + uploadingFolder + ", processing total size : " + processingFileList.size());

            File tmpFile = null;
            for (String fileName : processingFileList) {

                tmpFile = new File(fileName);
                processFile(tmpFile);
            }
        } catch (Exception e) {
            System.out.println("Updated status in log table to " + ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE);
            clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
            System.out.println("Insert table failed, folder: "  + needUploadFolder);

            moveFilesBackProcessingFolder();
            e.printStackTrace();
            return ;
        }

        File file = new File(uploadingFolder);
        if (file != null && file.isDirectory()) {
            if (ArrayUtils.isNotEmpty(file.list())) {
                System.out.println("!!!!!! still get file left which should be empty, folderPath : " + uploadingFolder);
                clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
                return ;
            } else {
                if (occurError) {
                    System.out.println("!!!! occur error when processing files ");
                    clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FINISHED_WITH_ERROR, logId);
                } else {
                    System.out.println("@@@ upload successed");
                    clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, logId);
                }
                deleteTempProcessingFolder();
            }
        }
    }

    private boolean checkIsProcessingUpload(){

        List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(ClarityDBUploadLogEntity.UPLOAD_TYPE_GSC);
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }
        return false;
    }

    private void moveFilesBackProcessingFolder(){

        File processingFolder = new File(uploadingFolder);

        System.out.println("====moving files back from processing folder, total file:" + processingFolder.length() + "!! from " + uploadingFolder + " to " + needUploadFolder);

        for (File gscFile : processingFolder.listFiles()) {
            try {
                FileUtils.moveFile(gscFile, new File(needUploadFolder + "/" + gscFile.getName()));
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        deleteTempProcessingFolder();
    }

    private void moveFilesToErrorFolder(String filePath){

        File bkFolder = new File(errorFolder);
        if (bkFolder == null || !bkFolder.isDirectory()) {
            System.out.println("error folder is not exist, mkdir: " + errorFolder);
            bkFolder.mkdir();
        }

        System.out.println("====moving files to error folder,  from " + uploadingFolder + " to " + errorFolder);

        File gscFile = new File(filePath);
        try {
            FileUtils.moveFile(gscFile, new File(errorFolder + "/" + gscFile.getName()));
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    private void moveFileAndZip(String filePath){

        File bkFolder = new File(backUpFolder);
        if (bkFolder == null || !bkFolder.isDirectory()) {
            System.out.println("BK folder is not exist, mkdir: " + backUpFolder);
            bkFolder.mkdir();
        }

        File tmpFile;
        File targetFile;

        // zip file
        try {
            tmpFile = new File(filePath);
            targetFile = new File(bkFolder + "/" + tmpFile.getName());

            FileUtils.moveFile(tmpFile, targetFile);

            System.out.println("zipped file : " + targetFile.getAbsolutePath());
            GZipUtil.zipFile(targetFile.getAbsolutePath());

            targetFile.delete();
            System.out.println("delete file : [" + filePath + "]");
        } catch (Exception e) {
            System.out.println("delete file failed. file: [" + filePath + "]");
            e.printStackTrace();
        }

    }

    private void deleteTempProcessingFolder(){
        //deleted the temp processing folder
        File tempFolder = new File(uploadingFolder);

        if (tempFolder != null && tempFolder.isDirectory()) {
            tempFolder.delete();
        }
    }

    private void processFile(File file) throws Exception {

//        gscBaseDao.createDailyTable(logDate);
        int totalSize = 0;
        try {
            FileReader fr = new FileReader(file);
            CSVParser csvParser = new CSVParser(fr, csvFormat);

            List<GscEntity> gscEntities = new ArrayList<>();
            // don't read header
            List<CSVRecord> csvRecords = csvParser.getRecords();
            for (int i = 1; i < csvRecords.size(); i++) {
                CSVRecord csvRecord = csvRecords.get(i);
                GscEntity gscEntity;
                try {
                    gscEntity = getGscEntity(csvRecord);
                } catch (Exception e) {
                    System.out.println("line i : "+i);
                    e.printStackTrace();
                    continue;
                }
                gscEntities.add(gscEntity);
                if (gscEntities.size() == maxInsertCount) {
                    gscBaseDao.insertForBatch(gscEntities);
                    System.out.println("finish insert for top : "+maxInsertCount+ " for file :"+ file.getName());
                    gscEntities.clear();
                }
            }
            if (CollectionUtils.isNotEmpty(gscEntities)) {
                gscBaseDao.insertForBatch(gscEntities);
                System.out.println("finish insert for left count :"+gscEntities.size());
                totalSize = gscEntities.size();
//                System.out.println(new Gson().toJson(gscEntities));
                gscEntities.clear();
            }

            csvParser.close();
            fr.close();
        } catch (Exception e) {
            e.printStackTrace();
            moveFilesToErrorFolder(file.getAbsolutePath());
            occurError = true;
            return ;
        }

        moveFileAndZip(file.getAbsolutePath());

        String[] parts = StringUtils.split(file.getName(), "_");

        Integer ownDomainId = StringUtils.isNotBlank(parts[1]) ? NumberUtils.toInt(parts[1]) : 0;
        Integer relId = StringUtils.isNotBlank(parts[2]) ? NumberUtils.toInt(parts[2]) : 0;
        Integer logDate = StringUtils.isNotBlank(parts[3]) ? NumberUtils.toInt(parts[3]) : 0;
        Integer version = StringUtils.isNotBlank(parts[4]) ? NumberUtils.toInt(parts[4]) : 1;

        try {
            boolean isUpdateSuccess = autoRunInstanceEntityDAO.updateUploadInfoForUploader(AutorunInfoEntity.CATEGORY_GSC, ownDomainId, relId, logDate,
                    version, AutorunInstanceEntity.STATUS_FINISH_WITHOUT_ERROR, new Date(), totalSize);

            if (!isUpdateSuccess) {
                System.out.println(" ===== update failed, not update " + AutorunInfoEntity.CATEGORY_GSC + "-" + ownDomainId + "-" + relId + "-" + logDate + "-" +
                        version);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private GscEntity getGscEntity(CSVRecord csvRecord) throws ParseException {
        GscEntity gscEntity = new GscEntity();
        gscEntity.setClicks(NumberUtils.toDouble(csvRecord.get("clicks")));
        gscEntity.setCtr(NumberUtils.toDouble(csvRecord.get("ctr")));
        gscEntity.setImpressions(NumberUtils.toDouble(csvRecord.get("impressions")));
        gscEntity.setCountryCd(csvRecord.get("countrycd"));
        gscEntity.setDevice(csvRecord.get("device"));
        //Leo - https://www.wrike.com/open.htm?id=166383881
        int type = NumberUtils.toInt(csvRecord.get("type"));
        gscEntity.setType(type);
        if (type == GscEntity.TYPE_ALL) {
            gscEntity.setKeywordName(FormatUtils.decoderString(csvRecord.get("kName")));
            gscEntity.setUrl(csvRecord.get("url"));
        } else if (type == GscEntity.TYPE_KEYWORD) {
            gscEntity.setKeywordName(FormatUtils.decoderString(csvRecord.get("kName")));
        } else if (type == GscEntity.TYPE_URL) {
            gscEntity.setUrl(csvRecord.get("url"));
        }
        String date = csvRecord.get("date");

        try {
            gscEntity.setLogDate(DateUtils.parseDate(date, dateParser));
        } catch (Exception e) {
            System.out.println(date);
            throw e;
        }
        gscEntity.setPosition(NumberUtils.toDouble(csvRecord.get("position")));
        gscEntity.setOwnDomainId(NumberUtils.toInt(csvRecord.get("domainid")));
        gscEntity.setRelId(NumberUtils.toInt(csvRecord.get("reldomainid")));
        gscEntity.setSign(1);
        gscEntity.setVersioning(NumberUtils.toInt(csvRecord.get("versioning")));
        gscEntity.setSearchAppearance(csvRecord.get("searchAppearance"));
        return gscEntity;
    }


    /**
     * check if the file need process
     * @return
     */
    private boolean validateFile(File file) {
        if (Pattern.matches(dailyTxtRegex, file.getName())) {
            return true;
        }
        return false;
    }

//    private Date getDataDateFromFileName(String fileName) throws ParseException {
//        String[] params = StringUtils.split(fileName, "-");
//        if (params != null && params.length >= 2) {
//            return DateUtils.parseDate(params[1], dateParser);
//        }
//        return null;
//    }



    private void moveFilesToProcessingFolder(){

        File targetFolder = new File(uploadingFolder);
        if (targetFolder == null || !targetFolder.isDirectory()) {
            System.out.println("uploadingFolder is not exist, mkdir: " + uploadingFolder);
            targetFolder.mkdir();
        }

        File doneFolder = new File(needUploadFolder);

        System.out.println("====moving files to processing folder, total file:" + doneFolder.listFiles().length + "!! from " + needUploadFolder + " to " + uploadingFolder);

        String uniqueKey = "";

        for (File gscFile : doneFolder.listFiles()) {
            try {
                //verify if it's gsc upload file
                if (validateFile(gscFile) && StringUtils.startsWith(gscFile.getName(), "gsc_") && gscFile.isFile()) {

                    String[] parts = StringUtils.split(gscFile.getName(), "_");

                    uniqueKey = parts[1] + "-" + parts[2] + "-" + parts[3] + "-" + parts[4];

                    Integer ownDomainId = StringUtils.isNotBlank(parts[1]) ? NumberUtils.toInt(parts[1]) : 0;
                    Integer relId = StringUtils.isNotBlank(parts[2]) ? NumberUtils.toInt(parts[2]) : 0;
                    String logDate = FormatUtils.formatDate(FormatUtils.toDate(parts[3], "yyyyMMdd"), "yyyy-MM-dd");
                    Integer version = StringUtils.isNotBlank(parts[4]) ? NumberUtils.toInt(parts[4]) : 0;

                    // check if it's exist in clarityDB or exist in
                    if (uniqueKeyList.contains(uniqueKey)) {
                        //unique key exist, oid/relId/logdate should only have one file to upload

                        System.out.println("!!!! key has already exist!!! key : " + uniqueKey);
                        moveFileToDuplicateFolder(gscFile);
                        continue;
                    } else if (isDataExistInClarityDB(ownDomainId, relId, logDate, version)) {
                        // data exist in clarityDB
                        moveFileToDuplicateFolder(gscFile);
                        continue;
                    } else {
                        System.out.println("=== adding uniqueKey : " + uniqueKey);
                    }

                    FileUtils.moveFile(gscFile, new File(uploadingFolder + "/" + gscFile.getName()));
                    processingFileList.add(uploadingFolder + "/" + gscFile.getName());
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }

    }

    private void moveFileToDuplicateFolder(File gscFile){

        File targetFolder = new File(duplicateFolder);
        if (targetFolder == null || !targetFolder.isDirectory()) {
            System.out.println("duplicateFolder is not exist, mkdir: " + duplicateFolder);
            targetFolder.mkdir();
        }

        try {
            FileUtils.moveFile(gscFile, new File(duplicateFolder + "/" + gscFile.getName()));
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }


    }


    private boolean isDataExistInClarityDB(Integer ownDomainId, Integer relId, String logDate, Integer version){

        //select count() from gsc_all_in_one where own_domain_id = ? and rel_id = ? and log_date = ?

        GscEntity gscEntity = gscBaseDao.checkExistInAllInOne(ownDomainId, relId, logDate, version);
        if (gscEntity != null && StringUtils.isNotBlank(gscEntity.getKeywordName())) {
            System.out.println(" !!!! data existed in clarityDB for OID:" + ownDomainId + ", relId:" + relId + ", logDate:" + logDate + ", version:" + version);
            return true;
        }

        return false;

    }


    private void insertEmptyLog(){

        ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();

        clarityDBUploadLogEntity.setTmpTableName("");
        clarityDBUploadLogEntity.setDatabaseName(databaseName);
        clarityDBUploadLogEntity.setFinalTableName(finalTableName);
        clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_SUCCESS);
        clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS);
        clarityDBUploadLogEntity.setTmpTableUploadDailyRows(0);
        clarityDBUploadLogEntity.setFinalTableUploadRows(0);
        clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_GSC);

        try {
            clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
        } catch (UnknownHostException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
            clarityDBUploadLogEntity.setServerIp("Unknow server");
        }

        clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
        clarityDBUploadLogEntity.setTmpTableUploadEndTime(new Date());

        clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
    }

}
