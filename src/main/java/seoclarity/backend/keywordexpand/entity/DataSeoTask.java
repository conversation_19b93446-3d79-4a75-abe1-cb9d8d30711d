package seoclarity.backend.keywordexpand.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class DataSeoTask {

    private String id;
    private String status_code;
    private String status_message;
    private String time;
    private Float cost;
    private Integer result_count;
    private List<String> path;
    private DataSeoData data;
    private List<DataSeoResult> result;
}
