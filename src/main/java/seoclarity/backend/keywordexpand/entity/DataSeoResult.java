package seoclarity.backend.keywordexpand.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class DataSeoResult {
    private String keyword;
    private Integer location_code;
    private Integer language_code;
    private Boolean search_partners;
    private String competition;
    private Integer competition_index;
    private Integer search_volume;
    private Float low_top_of_page_bid;
    private Float high_top_of_page_bid;
    private Float cpc;
    private List<DataSeoMonthlySearch> monthly_searches;
    private DataSeoKeywordAnnotations keyword_annotations;
}
