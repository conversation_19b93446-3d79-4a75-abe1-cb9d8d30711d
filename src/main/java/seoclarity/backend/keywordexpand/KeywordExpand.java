package seoclarity.backend.keywordexpand;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import seoclarity.backend.dao.actonia.keywordexpand.RiRelatedKeywordMonthlyExpansionDao;
import seoclarity.backend.dao.actonia.keywordexpand.RiRelatedMonthlyExpandKeywordDao;
import seoclarity.backend.dao.clickhouse.keywordexpand.KeywordExpandStageDao;
import seoclarity.backend.dao.clickhouse.keywordexpand.KeywordExpansionDao;
import seoclarity.backend.dao.rankcheck.KeywordMonthlyRecommendDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.actonia.keywordexpand.RiRelatedKeywordMonthlyExpansionEntity;
import seoclarity.backend.entity.actonia.keywordexpand.RiRelatedMonthlyExpandKeyword;
import seoclarity.backend.entity.clickhouse.keywordexpand.RelatedKeywordExpandStageEntity;
import seoclarity.backend.entity.clickhouse.keywordexpand.RelatedKeywordExpansionEntity;
import seoclarity.backend.keywordexpand.command.CleanupAndStemKwCommand;
import seoclarity.backend.keywordexpand.command.SplitFileCommand;
import seoclarity.backend.keywordexpand.utils.ExportDataUtil;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.DateUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.*;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1108677448
 * 1、source data table：d_ranking_info_us_{dataInt} 、d_ranking_info_intl_{dataInt}
 * 2、ri_related_keyword_monthly_expansion   db64
 * 3、dis_related_keyword_expand_stage       cdb
 * 4、dis_related_keyword_expand_stage2      cdb
 * 5、dis_related_keyword_expansion          cdb
 * 6、ri_related_monthly_expand_keyword      db64
 */
@Slf4j
public class KeywordExpand {


    private static final int MONTH_MAX_ROWS = 1500000;
    private static final int BATCH_SAVE_SIZE = 2000;

    public static int countryType; // 0: us other: intl
    public static int engineId;
    public static int languageId;

    private static final Integer[] NOT_EXC_DOMAIN = new Integer[]{476, 7079, 7407, 8422, 4392, 11607};
    private static final List<String> INTL_EXEC_LIST = Arrays.asList("6_8","3_3","18_19","3_4","4_7","14_15","16_17","8_9","2_5","45_46");

    private static List<Integer> NOT_EXC_DOMAIN_LIST = Arrays.asList(NOT_EXC_DOMAIN);

    private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
    private static final String CH_DB_RG_URL = "http://cdb-ri-master-first-external:8123";

    private static final String CH_DB_RG = "seo_daily_ranking";

    private static final String USER = "default";

    private static final String PSW = "clarity99!";

    private static final String FILE_TYPE_TXT = ".txt";

    private static final String SPLIT_KEYWORD = "!_!";

    private static final String SPLIT_PATH = "/";

    private static final String PARENT_FILE_PATH = "/home/<USER>/source/keywordExpandMonthly/tmp_file";
//    private static final String PARENT_FILE_PATH = "/home/<USER>/source/radeL/keyword_expand/tmp_file"; // test

    private static final String RELATED_KEYWORD_FILE_NAME = "relatedKeyword";

    private static final String KEYWORD_STEAM_FILE = "RG_NLKeywordStream";

    private static final SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd'T'HHmmss");
    private static final SimpleDateFormat dateFormatter_YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    private static final Gson gson = new Gson();
    private static final Random random = new Random();

    private static final Date currentDate = new Date();

    private SeoClarityKeywordEntityDAO keywordEntityDAO;
    private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO keywordMonthlySearchEngineRelationEntityDAO;
    private KeywordMonthlyRecommendDAO keywordMonthlyRecommendDAO;
    private RiRelatedKeywordMonthlyExpansionDao riRelatedKeywordMonthlyExpansionDao;
    private RiRelatedMonthlyExpandKeywordDao riRelatedMonthlyExpandKeywordDao;
    private KeywordExpandStageDao keywordExpandStageDao;
    private KeywordExpansionDao keywordExpansionDao;

    private int oldRiKeywordCount = 0;
    private int oldAfterCleanupKeywordCount = 0;
    private int oldRankCount = 0;
    private static final int THREAD_COUNT = 10;
    private int riKeywordCount = 0;
    private int afterCleanupKeywordCount = 0;
    private int stageCount = 0;
    private boolean isTest = false;

    public KeywordExpand() {
        keywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
        keywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
        keywordMonthlyRecommendDAO = SpringBeanFactory.getBean("keywordMonthlyRecommendDAO");
        riRelatedKeywordMonthlyExpansionDao = SpringBeanFactory.getBean("riRelatedKeywordMonthlyExpansionDao");
        riRelatedMonthlyExpandKeywordDao = SpringBeanFactory.getBean("riRelatedMonthlyExpandKeywordDao");
        keywordExpandStageDao = SpringBeanFactory.getBean("keywordExpandStageDao");
        keywordExpansionDao = SpringBeanFactory.getBean("keywordExpansionDao");
    }

    /**
     * null default us
     * args[0] us or intl 0: US 1: intl
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        KeywordExpand keywordExpand = new KeywordExpand();
        if (null == args || args.length == 0) {
            countryType = 0;
            engineId = 1;
            languageId = 1;
        } else {
            if (args.length != 1) {
                log.info("==>errorParam program is end!");
                return;
            }
            countryType = Integer.parseInt(args[0]);
        }
        System.out.println("start-------");
        if (countryType == 0) {
            keywordExpand.process();
        } else {
            for (String s : INTL_EXEC_LIST) {
                String[] s1 = s.split("_");
                engineId = Integer.parseInt(s1[0]);
                languageId = Integer.parseInt(s1[1]);
                keywordExpand.process();
            }
        }
        System.out.println("taskEnd-------");
    }

    // todo 目前只支持deviceType = d
    private String getTableName () {
        String tableName = "";
        int dateInt = Integer.parseInt(getLastMonth());
        if (countryType == 0) {
            tableName = "d_ranking_info_us_" + dateInt;
        } else {
            tableName = "d_ranking_info_intl_" + dateInt;
        }
        return tableName;
    }

    private String getLastMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);
        date = calendar.getTime();
        return format.format(date);
    }

    private void process() {
        threadPool.init();
        CommonUtils.initThreads(THREAD_COUNT);
        String tableName = getTableName();
        System.out.println("queryTable:" + tableName);
        getAllKeywordsToLocal(tableName);
        int id = 0;
        if (!isTest) {
            id = insertIntoDB();
        }
        cleanupKeyword(id);
        loadWordStemToExpandStage(id);
        commonWaitMethod(5, 2, "ForStage1");
        loadWordStemToExpandStage2(id);
        commonWaitMethod(5, 2, "ForStage2");
        loadKeywordToExpansion(id);
        if (!isTest) {
            commonWaitMethod(10, 2, "ForExpansion"); // test
            copyDataToDb64(id);
            updateDB(id);
        }
        threadPool.destroy();
    }

    /**
     *
     * @param waitTime
     * @param waitUnit 0: ms 1: s 2: m 3: h
     * @param prefix
     */
    private void commonWaitMethod(long waitTime, int waitUnit, String prefix) {
        try {
            System.out.println("=====startWait" + prefix + " waitUnit:" + waitUnit + " waitTime:" + waitTime);
            switch (waitUnit) {
                case 0: TimeUnit.MILLISECONDS.sleep(waitTime); break;
                case 1: TimeUnit.SECONDS.sleep(waitTime); break;
                case 2: TimeUnit.MINUTES.sleep(waitTime); break;
                case 3: TimeUnit.HOURS.sleep(waitTime); break;
                default:
                    System.out.println("====unKnowType: " + prefix + " waitUnit:" + waitUnit + " waitTime:" + waitTime);
            }
            System.out.println("=====endWait " + prefix + " waitUnit:" + waitUnit + " waitTime:" + waitTime);
        } catch (InterruptedException e) {
            System.out.println("=====waitError " + prefix + " waitUnit:" + waitUnit + " waitTime:" + waitTime);
            e.printStackTrace();
        }
    }

    private int insertIntoDB() {
        int resultId;
        int rankMonth = Integer.parseInt(getLastMonth());
        int rankDate = Integer.parseInt(dateFormatter_YYYYMMDD.format(DateUtils.getLastSundayOfPreviousMonth(currentDate)));
        String countryType = KeywordExpand.countryType == 0 ? "US" : "INTL";
        String device = "d";
        RiRelatedKeywordMonthlyExpansionEntity keywordMonthlyExpansionEntity = riRelatedKeywordMonthlyExpansionDao.getByUniqueKey(rankMonth, countryType, device);
        if (null == keywordMonthlyExpansionEntity || null == keywordMonthlyExpansionEntity.getId() || keywordMonthlyExpansionEntity.getId() == 0) {
            keywordMonthlyExpansionEntity = new RiRelatedKeywordMonthlyExpansionEntity();
            keywordMonthlyExpansionEntity.setStatus(RiRelatedKeywordMonthlyExpansionEntity.STATUS_START);
            keywordMonthlyExpansionEntity.setRankMonth(rankMonth);
            keywordMonthlyExpansionEntity.setRankDate(rankDate);
            keywordMonthlyExpansionEntity.setCountryType(countryType);
            keywordMonthlyExpansionEntity.setDevice(device);
            keywordMonthlyExpansionEntity.setRiKeywordCount(riKeywordCount);
            keywordMonthlyExpansionEntity.setCreateDate(currentDate);
            resultId = riRelatedKeywordMonthlyExpansionDao.insert(keywordMonthlyExpansionEntity);
        } else {
            resultId = keywordMonthlyExpansionEntity.getId();
            if (keywordMonthlyExpansionEntity.getNonRGKeywordCount() != null) {
                oldAfterCleanupKeywordCount = keywordMonthlyExpansionEntity.getNonRGKeywordCount();
            } else {
                oldAfterCleanupKeywordCount = 0;
            }
            if (keywordMonthlyExpansionEntity.getRiKeywordCount() != null) {
                oldRiKeywordCount = keywordMonthlyExpansionEntity.getRiKeywordCount();
            } else {
                oldRiKeywordCount = 0;
            }
            if (keywordMonthlyExpansionEntity.getKeywordCountToRank() != null) {
                oldRankCount = keywordMonthlyExpansionEntity.getKeywordCountToRank();
            } else {
                oldRankCount = 0;
            }

            keywordMonthlyExpansionEntity.setStatus(RiRelatedKeywordMonthlyExpansionEntity.STATUS_START);
            keywordMonthlyExpansionEntity.setRiKeywordCount(riKeywordCount);
            keywordMonthlyExpansionEntity.setNonRGKeywordCount(afterCleanupKeywordCount);
            keywordMonthlyExpansionEntity.setKeywordCountToRank(0);
            riRelatedKeywordMonthlyExpansionDao.updateCountById(keywordMonthlyExpansionEntity);
        }
        System.out.println("==>insertRelatedKWInfoSuccess ->relatedKwId: " + resultId);
        return resultId;
    }

    private void updateDB(int id) {
        processRiKeywordCount();
        processNonRgCount();
        int rankCount = keywordExpansionDao.getDataCountByRandDate(DateUtils.getLastSundayOfPreviousMonth(currentDate), engineId, languageId);
        RiRelatedKeywordMonthlyExpansionEntity updateEntity = new RiRelatedKeywordMonthlyExpansionEntity();
        if (countryType == 0) {
            updateEntity.setRiKeywordCount(riKeywordCount);
            updateEntity.setNonRGKeywordCount(afterCleanupKeywordCount);
            updateEntity.setKeywordCountToRank(rankCount);
        } else {
            updateEntity.setRiKeywordCount(riKeywordCount + oldRiKeywordCount);
            updateEntity.setNonRGKeywordCount(afterCleanupKeywordCount + oldAfterCleanupKeywordCount);
            updateEntity.setKeywordCountToRank(rankCount + oldRankCount);
        }

        updateEntity.setId(id);
        riRelatedKeywordMonthlyExpansionDao.updateCountById(updateEntity);
    }

    private void processRiKeywordCount() {
        File parentFile = new File(PARENT_FILE_PATH + SPLIT_PATH + "riCountParentFile" + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId);
        if (!parentFile.exists() || !parentFile.isDirectory()) {
            System.out.println("==>getRiCountError: countParentFile not exist!");
            return;
        }
        List<String> tatalCountList = new ArrayList<>();
        File[] files = parentFile.listFiles();
        if (null == files || files.length <= 0) {
            System.out.println("==>getRiCountError: countFile not exist!");
            return;
        }
        for (File file : files) {
            try {
                List<String> countList = FileUtils.readLines(file, "UTF-8");
                if (countList.size() > 0) {
                    tatalCountList.addAll(countList);
                }
            } catch (IOException e) {
                System.out.println("==>readRiCountErrorFileError fileName: " + file.getName());
                e.printStackTrace();
            }
        }
        if (tatalCountList.size() > 0) {
            tatalCountList.forEach(count -> riKeywordCount += Integer.parseInt(count));
        }
//        parentFile.deleteOnExit();
    }

    private void processNonRgCount() {
        File parentFile = new File(PARENT_FILE_PATH + SPLIT_PATH + "countPrentFile" + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId);
        if (!parentFile.exists() || !parentFile.isDirectory()) {
            System.out.println("==>getNonRgCountError: countParentFile not exist!");
            return;
        }
        List<String> tatalCountList = new ArrayList<>();
        File[] files = parentFile.listFiles();
        if (null == files || files.length <= 0) {
            System.out.println("==>getNonRgCountError: countFile not exist!");
            return;
        }
        for (File file : files) {
            try {
                List<String> countList = FileUtils.readLines(file, "UTF-8");
                if (countList.size() > 0) {
                    tatalCountList.addAll(countList);
                }
            } catch (IOException e) {
                System.out.println("==>readNonRgCountErrorFileError fileName: " + file.getName());
                e.printStackTrace();
            }
        }
        if (tatalCountList.size() > 0) {
            tatalCountList.forEach(count -> afterCleanupKeywordCount += Integer.parseInt(count));
        }
//        parentFile.deleteOnExit();
    }

    private String getLocalFilePath(String fileName) {
        String localFilePath = PARENT_FILE_PATH + SPLIT_PATH + fileName + "_" + engineId + "_" + languageId + "_" + dateFormatter.format(currentDate) + FILE_TYPE_TXT;
        log.info("==>allKeywordLocalFilePath: {}", localFilePath);
        return localFilePath;
    }

    private void getAllKeywordsToLocal(String fileName) {
        StringBuilder sb = new StringBuilder();
        String sql = "";
        if (countryType == 0) {
            sb.append("select * from (select attrstr.value[indexOf(attrstr.key, 'relatedSearch')] as relatedSearch from " + CH_DB_RG + ".").append(fileName)
                    .append(" where ranking_date = '").append(FormatUtils.formatDate(DateUtils.getLastSundayOfPreviousMonth(currentDate), FormatUtils.DATE_PATTERN_2)).append("'");
            if (null != NOT_EXC_DOMAIN_LIST && NOT_EXC_DOMAIN_LIST.size() > 0) {
                sb.append(" and own_domain_id not in (").append(StringUtils.join(NOT_EXC_DOMAIN_LIST, ',')).append(")");
            }
            sb.append(" ) a where a.relatedSearch != '-'");
            sql = sb.toString();
        } else {
            sql = "select * from (select engine_id, language_id, attrstr.value[indexOf(attrstr.key, 'relatedSearch')] as relatedSearch from " + CH_DB_RG + "." + fileName + " where ranking_date = '" + FormatUtils.formatDate(DateUtils.getLastSundayOfPreviousMonth(currentDate), "yyyy-MM-dd") + "' ) a where a.relatedSearch != '-' and a.engine_id = " + engineId + " and a.language_id = " + languageId;
        }
        System.out.println("querySql: " + sql);
        try {
            ExportDataUtil.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, sql, getLocalFilePath(fileName), true, false, new String[]{""});
            splitLargeFile(getLocalFilePath(fileName));
            System.out.println("==>tableDataFileCreatedSuccess -> filePath: " + getLocalFilePath(fileName));
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("==>sourceDataError");
            System.exit(-1);
        }
    }

    private void copyDataToDb64(int id) {
        long start = System.currentTimeMillis();
        riRelatedKeywordMonthlyExpansionDao.updateStatus(RiRelatedKeywordMonthlyExpansionEntity.STATUS_LOADING_TO64, id);
        List<RiRelatedMonthlyExpandKeyword> expandKeywordList = new ArrayList<>();
        String sourceFilePath = getLocalFilePath("sourceExpansionList_" + countryType);
        String finalInsertFilePath = getLocalFilePath("finalInsertExpansionList_" + countryType);
        try {
//            List<RelatedKeywordExpansionEntity> expansionList = keywordExpansionDao.getExpansionListByDate(currentDate);
            exportData(sourceFilePath);
            if (checkExportDataIsEmpty(sourceFilePath)) {
                Date lastMonthSundayDate = DateUtils.getLastSundayOfPreviousMonth(currentDate);
                Date currentMonthSundayDate = DateUtils.getLastSundayOfCurrentMonth(currentDate);
                int lastMonthSunday = Integer.parseInt(FormatUtils.formatDate(lastMonthSundayDate, FormatUtils.DATE_PATTERN_2).replaceAll("-", ""));
                int currentMonthSunday = Integer.parseInt(FormatUtils.formatDate(currentMonthSundayDate, FormatUtils.DATE_PATTERN_2).replaceAll("-", ""));
                int monthDataRows = getExportDataCount(sourceFilePath);
                String countryType = KeywordExpand.countryType == 0 ? "US" : "INTL";
                List<RiRelatedMonthlyExpandKeyword> alreadyExistDataList = riRelatedMonthlyExpandKeywordDao.getExpandKeywordListByRankDate(lastMonthSunday, countryType);
                int alreadyExistSize = (null != alreadyExistDataList && !alreadyExistDataList.isEmpty()) ? alreadyExistDataList.size() : 0;
                System.out.println("=====totalCdbDataRows: " + monthDataRows + " alreadyExistSize: " + alreadyExistSize + " lastMonthSunday: " + lastMonthSunday + " currentMonthSunday: " + currentMonthSunday);

                if (null != alreadyExistDataList && !alreadyExistDataList.isEmpty()) {
                    // 1、first distinct keyword in expansion_List.
                    Set<String> existKwHashList = alreadyExistDataList.stream().map(RiRelatedMonthlyExpandKeyword::getCdbKeywordMurmur3hash).collect(Collectors.toSet());
                    int needInsertRows = genFinalInsertFile(sourceFilePath, finalInsertFilePath, existKwHashList);
                    System.out.println("=====processExistDataLogic beforeDistinctKwCount: " + monthDataRows + " afterDistinctKwCount: " + needInsertRows + " alreadyExistSize: " + alreadyExistSize);
                    // 2、insert data to table and update rankDate
                    if (needInsertRows <= 0) {
                        System.out.println("====allDataIsExist monthDataSize: " + monthDataRows + " alreadyExistSize: " + alreadyExistSize);
                        return;
                    }
                    if (alreadyExistSize > MONTH_MAX_ROWS) {
                        try (BufferedReader reader = new BufferedReader(new FileReader(finalInsertFilePath))) {
                            String line;
                            while (StringUtils.isNotBlank(line = reader.readLine())) {
                                line = line.trim();
                                RelatedKeywordExpansionEntity expansionEntity = gson.fromJson(line, RelatedKeywordExpansionEntity.class);
                                RiRelatedMonthlyExpandKeyword expandKeyword = createRiRelatedMonthlyExpandKeyword(expansionEntity, currentMonthSunday, countryType);
                                expandKeywordList.add(expandKeyword);
                                if (expandKeywordList.size() >= BATCH_SAVE_SIZE) {
                                    riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                    expandKeywordList.clear();
                                }
                            }
                            if (!expandKeywordList.isEmpty()) {
                                riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                expandKeywordList.clear();
                            }
                        } catch (IOException e) {
                            System.out.println("======insertError: " + e.getMessage());
                            e.printStackTrace();
                        }
                        Long updateStartId = alreadyExistDataList.get(MONTH_MAX_ROWS).getId();
                        riRelatedMonthlyExpandKeywordDao.updateRankDateByStartId(currentMonthSunday, updateStartId);
                        System.out.println("====existDataIsOverLimit: alreadyExistSize: " + alreadyExistSize + " updateNextMonthStartId:" + updateStartId + " nextMonthInsertSize: " + needInsertRows + " nextMonth: " + currentMonthSunday);
                    } else {
                        int allowInsertRows = MONTH_MAX_ROWS - alreadyExistSize;
                        if (needInsertRows <= allowInsertRows) {
                            try (BufferedReader reader = new BufferedReader(new FileReader(finalInsertFilePath))) {
                                String line;
                                while (StringUtils.isNotBlank(line = reader.readLine())) {
                                    line = line.trim();
                                    RelatedKeywordExpansionEntity expansionEntity = gson.fromJson(line, RelatedKeywordExpansionEntity.class);
                                    RiRelatedMonthlyExpandKeyword expandKeyword = createRiRelatedMonthlyExpandKeyword(expansionEntity, lastMonthSunday, countryType);
                                    expandKeywordList.add(expandKeyword);
                                    if (expandKeywordList.size() >= BATCH_SAVE_SIZE) {
                                        riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                        expandKeywordList.clear();
                                    }
                                }
                                if (!expandKeywordList.isEmpty()) {
                                    riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                    expandKeywordList.clear();
                                }
                            } catch (IOException e) {
                                System.out.println("======insertError: " + e.getMessage());
                                e.printStackTrace();
                            }
                            System.out.println("====lessThanAllow: alreadyExistSize: " + alreadyExistSize + " allowInsertRows" + allowInsertRows + " needInsertRows: " + needInsertRows);
                        } else {
                            try (BufferedReader reader = new BufferedReader(new FileReader(finalInsertFilePath))) {
                                int loopIndex = 0;
                                String line;
                                while (StringUtils.isNotBlank(line = reader.readLine())) {
                                    line = line.trim();
                                    RelatedKeywordExpansionEntity expansionEntity = gson.fromJson(line, RelatedKeywordExpansionEntity.class);
                                    RiRelatedMonthlyExpandKeyword expandKeyword = null;
                                    if (loopIndex < allowInsertRows) {
                                        // rank date is last month
                                        expandKeyword = createRiRelatedMonthlyExpandKeyword(expansionEntity, lastMonthSunday, countryType);
                                    } else {
                                        // rank date is current month last sunday date
                                        expandKeyword = createRiRelatedMonthlyExpandKeyword(expansionEntity, currentMonthSunday, countryType);
                                    }
                                    expandKeywordList.add(expandKeyword);
                                    if (expandKeywordList.size() >= BATCH_SAVE_SIZE) {
                                        riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                        expandKeywordList.clear();
                                    }
                                    loopIndex++;
                                }
                                if (!expandKeywordList.isEmpty()) {
                                    riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                    expandKeywordList.clear();
                                }
                            } catch (IOException e) {
                                System.out.println("======insertError: " + e.getMessage());
                                e.printStackTrace();
                            }
                            System.out.println("====greatThanAllow: alreadyExistSize: " + alreadyExistSize + " allowInsertRows" + allowInsertRows + " nextMonthInsertSize: " + needInsertRows + " nextMonth: " + currentMonthSunday);
                        }
                    }
                } else {
                    genFinalInsertFile(sourceFilePath, finalInsertFilePath, null);
                    System.out.println("=======processNotExistDataLogic needInsert: " + monthDataRows + " alreadyExist: " + alreadyExistSize + " lastMonthSunday: " + lastMonthSunday + " currentMonthSunday: " + currentMonthSunday);
                    if (monthDataRows <= MONTH_MAX_ROWS) {
                        try (BufferedReader reader = new BufferedReader(new FileReader(finalInsertFilePath))) {
                            String line;
                            while (StringUtils.isNotBlank(line = reader.readLine())) {
                                line = line.trim();
                                RelatedKeywordExpansionEntity expansionEntity = gson.fromJson(line, RelatedKeywordExpansionEntity.class);
                                RiRelatedMonthlyExpandKeyword expandKeyword = createRiRelatedMonthlyExpandKeyword(expansionEntity, lastMonthSunday, countryType);
                                expandKeywordList.add(expandKeyword);
                                if (expandKeywordList.size() >= BATCH_SAVE_SIZE) {
                                    riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                    expandKeywordList.clear();
                                }
                            }
                            if (!expandKeywordList.isEmpty()) {
                                riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                expandKeywordList.clear();
                            }
                        } catch (IOException e) {
                            System.out.println("======insertError: " + e.getMessage());
                            e.printStackTrace();
                        }
                        System.out.println("====notOverMaxLimit needInsert: " + monthDataRows);
                    } else {
                        try (BufferedReader reader = new BufferedReader(new FileReader(finalInsertFilePath))) {
                            int loopIndex = 0;
                            String line;
                            while (StringUtils.isNotBlank(line = reader.readLine())) {
                                line = line.trim();
                                RelatedKeywordExpansionEntity expansionEntity = gson.fromJson(line, RelatedKeywordExpansionEntity.class);
                                RiRelatedMonthlyExpandKeyword expandKeyword = null;
                                if (loopIndex < MONTH_MAX_ROWS) {
                                    // rank date is last month
                                    expandKeyword = createRiRelatedMonthlyExpandKeyword(expansionEntity, lastMonthSunday, countryType);
                                } else {
                                    // rank date is current month last sunday date
                                    expandKeyword = createRiRelatedMonthlyExpandKeyword(expansionEntity, currentMonthSunday, countryType);
                                }
                                expandKeywordList.add(expandKeyword);
                                if (expandKeywordList.size() >= BATCH_SAVE_SIZE) {
                                    riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                    expandKeywordList.clear();
                                }
                                loopIndex++;
                            }
                            if (!expandKeywordList.isEmpty()) {
                                riRelatedMonthlyExpandKeywordDao.insertBatch(expandKeywordList);
                                expandKeywordList.clear();
                            }
                        } catch (IOException e) {
                            System.out.println("======insertError: " + e.getMessage());
                            e.printStackTrace();
                        }
                        System.out.println("====overMaxLimit insertAsLastMonth: " + MONTH_MAX_ROWS + " lastMonthSunday: " + lastMonthSunday +  " insertAsCurrentMonth: " + (monthDataRows - MONTH_MAX_ROWS) + " currentMonthSunday: " + currentMonthSunday);
                    }
                }
            }
            riRelatedKeywordMonthlyExpansionDao.updateStatus(RiRelatedKeywordMonthlyExpansionEntity.STATUS_COLLECT_COMPLETED, id);
        } catch (Exception e) {
            System.out.println("=====copyDataFromCdbToDb64Error: " + e.getMessage());
            e.printStackTrace();
        }
        long end = System.currentTimeMillis();
        System.out.println("copyDataToDb64 use time " + (end - start));
    }

    private void exportData(String sourceFilePath) throws Exception {
        keywordExpansionDao.getExpansionListToFile(currentDate, sourceFilePath, engineId, languageId);
    }

    private boolean checkExportDataIsEmpty(String sourceFilePath) {
        boolean checkResult = true;
        File file = new File(sourceFilePath);
        if (!file.exists()) {
            checkResult = false;
        } else {
            try (BufferedReader reader = new BufferedReader(new FileReader(sourceFilePath))) {
                String line = reader.readLine();
                if (line == null || StringUtils.isEmpty(line)) {
                    checkResult = false;
                }
            } catch (IOException e) {
                // 处理文件读取异常
                checkResult = false;
                System.out.println("======checkTableDataIsEmptyError: " + e.getMessage());
                e.printStackTrace();
            }
        }
        System.out.println("======checkTableDataIsEmpty: " + checkResult + " filePath: " + sourceFilePath + " tableName: " + "dis_related_keyword_expansion");
        return checkResult;
    }

    private int getExportDataCount(String sourceFilePath) {
        int count = 0;
        try (BufferedReader reader = new BufferedReader(new FileReader(sourceFilePath))) {
            while ((reader.readLine()) != null) {
                count++;
            }
        } catch (IOException e) {
            System.out.println("======getExportDataCountError: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println("======exportDataCount: " + count);
        return count;
    }


    private int genFinalInsertFile(String sourceFilePath, String finalInsertFilePath, Set<String> existKwHashList) {
        System.out.println("====genFinalInsertFile sourceFilePath:" + sourceFilePath + " finalInsertFilePath:" + finalInsertFilePath);
        int totalCount = 0;
        int skipCount = 0;
        int skipExistCount = 0;
        int count = 0;
        // when if existKwHashList is null or existKwHashList is empty make hashFlag is false otherwise is true
        boolean hashFlag = existKwHashList != null && !existKwHashList.isEmpty();
        List<String> expansionList = new ArrayList<>();
        File file = new File(finalInsertFilePath);
        try (BufferedReader reader = new BufferedReader(new FileReader(sourceFilePath))) {
            String line;
            while (StringUtils.isNotBlank(line = reader.readLine())) {
                line = line.trim();
                totalCount++;
                RelatedKeywordExpansionEntity expansionEntity = getRelatedKeywordExpansionEntity(line);
                if (expansionEntity == null) {
                    skipCount++;
                    continue;
                }
                if (hashFlag) {
                    String kwMurmur3Hash = MurmurHashUtils.getMurmurHash3_64(expansionEntity.getKeywordName().toLowerCase());
                    if (existKwHashList.contains(kwMurmur3Hash)) {
                        skipExistCount++;
                        continue;
                    } else {
                        expansionList.add(gson.toJson(expansionEntity));
                        count++;
                    }
                } else {
                    expansionList.add(gson.toJson(expansionEntity));
                    count++;
                }

                if (expansionList.size() >= BATCH_SAVE_SIZE) {
                    FileUtils.writeLines(file, expansionList, true);
                    expansionList.clear();
                }
            }
            if (!expansionList.isEmpty()) {
                FileUtils.writeLines(file, expansionList, true);
                expansionList.clear();
            }
        } catch (IOException e) {
            System.out.println("======getExportDataCountError: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println("====genInsertFile totalCount:" + totalCount + " needInsert:" + count + " skipNull:" + skipCount + " existCount:" + skipExistCount);
        return count;
    }

    private RelatedKeywordExpansionEntity getRelatedKeywordExpansionEntity(String line) {
        RelatedKeywordExpansionEntity entity = null;
        String[] split = line.split(SPLIT_KEYWORD);
        if (split.length == 5) {
            entity = new RelatedKeywordExpansionEntity();
            try {
                entity.setRankingDate(DATE_FORMAT.parse(split[0].trim()));
            } catch (ParseException e) {
                Date lastMonthSundayDate = DateUtils.getLastSundayOfPreviousMonth(currentDate);
                entity.setRankingDate(lastMonthSundayDate);
            }
            entity.setEngineId(Integer.parseInt(split[1].trim()));
            entity.setLanguageId(Integer.parseInt(split[2].trim()));
            entity.setLocationId(Integer.parseInt(split[3].trim()));
            entity.setKeywordName(split[4].trim());
        }
        return entity;
    }

    private RiRelatedMonthlyExpandKeyword createRiRelatedMonthlyExpandKeyword(RelatedKeywordExpansionEntity expansionEntity, int rankDate, String countryType) {
        RiRelatedMonthlyExpandKeyword expandKeyword = new RiRelatedMonthlyExpandKeyword();
        expandKeyword.setRankDate(rankDate);
        expandKeyword.setCountryType(countryType);
        expandKeyword.setEngineId(expansionEntity.getEngineId());
        expandKeyword.setLanguageId(expansionEntity.getLanguageId());
        expandKeyword.setKeywordName(expansionEntity.getKeywordName());
        expandKeyword.setCdbKeywordMurmur3hash(MurmurHashUtils.getMurmurHash3_64(expansionEntity.getKeywordName().toLowerCase()));
        return expandKeyword;
    }

    private void splitLargeFile(String filePath) {
        try {
            File file = new File(filePath);
            LineNumberReader lnr = new LineNumberReader(new FileReader(file));
            int totalLines = 0;
            while (lnr.readLine() != null){
                totalLines++ ;
            }
            System.out.println("Total number of lines : " + totalLines);
            lnr.close();
            if (totalLines > 0) {
                LineIterator lineIterator = new LineIterator(new BufferedReader(new FileReader(file)));
                int linesPerThread = totalLines / THREAD_COUNT;
                System.out.println("linesPerThread: " + linesPerThread);
                int threadNo = 0;
                while (threadNo < THREAD_COUNT) {
                    System.out.println("threadNo: " + threadNo);
                    String ip = CacheModleFactory.getInstance().getAliveIpAddress();
                    System.out.println("ipAddress: " + ip);
                    if (ip != null) {
                        int startLine = threadNo * linesPerThread;
                        System.out.println("startLine: " + startLine);
                        int endLine = (threadNo == THREAD_COUNT - 1) ? totalLines : (startLine + linesPerThread - 1);
                        System.out.println("endLine: " + endLine);
                        SplitFileCommand splitFileCommand = new SplitFileCommand(startLine, endLine, lineIterator, ip, threadNo, PARENT_FILE_PATH, RELATED_KEYWORD_FILE_NAME, countryType);
                        splitFileCommand.setStatus(true);
                        threadPool.execute(splitFileCommand);
                        threadNo++;
                    }
                }
            }

            do {
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } while (threadPool.getThreadPool().getActiveCount() > 0);

//            file.deleteOnExit();
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("==>splitLargeFileError: " + e.getMessage());
            System.exit(-1);
        }
    }

    private void cleanupKeyword(int id) {
        long start = System.currentTimeMillis();
        File parentFile = new File(PARENT_FILE_PATH + SPLIT_PATH + RELATED_KEYWORD_FILE_NAME + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId);
        File[] files = parentFile.listFiles();
        if (null != files) {
            for (File file : files) {
                String ip = CacheModleFactory.getInstance().getAliveIpAddress();
                if (ip == null) {
                    continue;
                }
                int randomNum = random.nextInt(900000) + 100000;
                CleanupAndStemKwCommand cleanupAndStemKwCommand = new CleanupAndStemKwCommand(file, PARENT_FILE_PATH + SPLIT_PATH + "cleanupAndStemKw",
                        keywordEntityDAO, keywordMonthlySearchEngineRelationEntityDAO, keywordMonthlyRecommendDAO, ip, randomNum, id, riRelatedKeywordMonthlyExpansionDao,
                        0, 0, countryType, engineId, languageId, isTest);
                cleanupAndStemKwCommand.setStatus(true);
                threadPool.execute(cleanupAndStemKwCommand);
            }
        }
        do {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } while (threadPool.getThreadPool().getActiveCount() > 0);
//        parentFile.deleteOnExit();
        System.out.println("==>afterCleanupKeywordCount: " + afterCleanupKeywordCount + ", use time: " + (System.currentTimeMillis() - start) + " ms");
    }

    private void loadWordStemToExpandStage(int id) {
        long start = System.currentTimeMillis();
        try {
            File parentFile = new File(PARENT_FILE_PATH + SPLIT_PATH + KEYWORD_STEAM_FILE + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId);
            if (!parentFile.exists()) {
                System.out.println("==>processFailed: keywordStream file is not exist");
                return;
            }
            if (!parentFile.isDirectory()) {
                System.out.println("==>processFailed: keywordStream is not directory");
                return;
            }
            if (!isTest) {
                riRelatedKeywordMonthlyExpansionDao.updateStatus(RiRelatedKeywordMonthlyExpansionEntity.STATUS_LOADING_STAGE, id);
            }
            File[] files = parentFile.listFiles();
            if (files != null && files.length > 0) {
                List<String> lineList = new ArrayList<>();
                for (File file : files) {
                    LineIterator lineIterator = new LineIterator(new BufferedReader(new FileReader(file)));
                    while (lineIterator.hasNext()) {
                        String line = lineIterator.nextLine();
                        stageCount++;
                        lineList.add(line);
                        if (lineList.size() >= BATCH_SAVE_SIZE) {
                            batchCreatedExpandStage(lineList);
                            lineList.clear();
                        }
                    }
//                    file.deleteOnExit();
                }
                if (lineList.size() > 0) {
                    batchCreatedExpandStage(lineList);
                }
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            System.out.println("==>loadWordStemToExpandStageError: " + e.getMessage());
        }
        long end = System.currentTimeMillis();
        System.out.println("insertIntoExpandStageRows: " + stageCount + " use time " + (end - start));
    }

    private void loadWordStemToExpandStage2(int id) {
        long start = System.currentTimeMillis();
        if (!isTest) {
            riRelatedKeywordMonthlyExpansionDao.updateStatus(RiRelatedKeywordMonthlyExpansionEntity.STATUS_DEDUPING_STAGE, id);
        }
        keywordExpandStageDao.insertIntoStage2(DateUtils.getLastSundayOfPreviousMonth(currentDate), engineId, languageId);
        long end = System.currentTimeMillis();
        System.out.println("loadWordStemToExpandStage2 use time " + (end - start));
    }

    private void batchCreatedExpandStage(List<String> streamKeywordList) {
        if (streamKeywordList == null || streamKeywordList.size() == 0) {
            return;
        }
        List<RelatedKeywordExpandStageEntity> stageEntityList = new ArrayList<>();
        for (String keywordInfo : streamKeywordList) {
            RelatedKeywordExpandStageEntity stageEntity = new RelatedKeywordExpandStageEntity();

            String[] infoArr = keywordInfo.split(SPLIT_KEYWORD);
            Integer engineId = Integer.parseInt(infoArr[0]);
            Integer languageId = Integer.parseInt(infoArr[1]);
            String keywordName = infoArr[2];

            stageEntity.setRankingDate(DateUtils.getLastSundayOfPreviousMonth(currentDate));
            stageEntity.setEngineId(engineId);
            stageEntity.setLanguageId(languageId);
            stageEntity.setKeywordName(keywordName);
            stageEntity.setStream(getStringArr(infoArr[3]));

            stageEntityList.add(stageEntity);
        }
        keywordExpandStageDao.insertForBatch(stageEntityList);
    }

    private String[] getStringArr(String str) {
        if (StringUtils.isBlank(str) || !str.contains("[") || !str.contains("]")) {
            return null;
        }
//        System.out.println("==>getStreamFun() -> stream: " + str);

        String[] array = str.substring(1, str.length() - 2).split(",");
        String[] stringArray = new String[array.length];

        for (int i = 0; i < array.length; i++) {
            stringArray[i] = array[i].replaceAll("'", "").trim();
        }
        return stringArray;
    }

    private void loadKeywordToExpansion(int id) {
        long start = System.currentTimeMillis();
        if (!isTest) {
            riRelatedKeywordMonthlyExpansionDao.updateStatus(RiRelatedKeywordMonthlyExpansionEntity.STATUS_EXCLUDING_RESERVED, id);
        }
        for (int i = 0; i < 100; i++) {
            keywordExpansionDao.insertByExpandStage(DateUtils.getLastSundayOfPreviousMonth(currentDate), i, countryType, engineId, languageId);
        }
        long end = System.currentTimeMillis();
        System.out.println("loadKeywordToExpansion use time " + (end - start));
    }
}
