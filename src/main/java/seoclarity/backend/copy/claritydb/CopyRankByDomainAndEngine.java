package seoclarity.backend.copy.claritydb;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang.BooleanUtils;

import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CommonUtils;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.copy.claritydb.CopyRankByDomainAndEngine" -Dexec.cleanupDaemonThreads=false -Dexec.args="false 2,5#4,7"
 * <AUTHOR>
 *
 */
public class CopyRankByDomainAndEngine {

    private static boolean isMobile=false;
    private static String domainEngines;
    
    private static Date copyFromDate;
    private static Date copyToDate;

    private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

    public CopyRankByDomainAndEngine() {
    }

    public static void main(String[] args) throws Exception {
        if (args != null && args.length >= 2) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            
            isMobile = BooleanUtils.toBoolean(args[0]);
            domainEngines = args[1];
            
            if(args.length >= 4){
                copyFromDate = dateFormat.parse(args[2]);
                copyToDate = dateFormat.parse(args[3]);
            }
            
        }else{
        	System.out.println("invaild parameter!!!");
        	System.exit(-1);
        }
        CommonUtils.initThreads(3);

        CopyRankByDomainAndEngine copyKeywordsInMongo = new CopyRankByDomainAndEngine();
        try {
            threadPool.init();
            System.out.println("isMobile: " + isMobile+"  domainEngines: " + domainEngines+" copyFromDate:"+copyFromDate+" copyToDate:"+copyToDate);
            System.out.println("Waiting for you confirm parameter 10 secs:");
            Thread.sleep(1*1000);
            copyKeywordsInMongo.multiDate();
            threadPool.destroy();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void multiDate() throws Exception {
    	String delimeter ="#";
    	String[] searchEngineArr = domainEngines.split(delimeter);
    	
    	for (int i = 0; i < searchEngineArr.length; i++) {
    		String[]  el = searchEngineArr[i].split(",");
            int langauge = Integer.parseInt(el[1]);
            int searchEngine = Integer.parseInt(el[0]);

            if (langauge == 0 || searchEngine == 0 || copyFromDate == null || copyToDate == null) {
                System.out.println("error parameter");
                return;
            }
            
            //PRocess
            
            String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
    		while (ipAddress == null) {
    			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
    		}
    		
    		CopyRankByDomainAndEngineCommand command = getUpdateCommand(ipAddress, searchEngine, langauge, copyFromDate, copyToDate, isMobile);
    		try {
    			threadPool.execute(command);
    		} catch (Exception e) {
    			e.printStackTrace();
    		}
    		
    		System.out.println("Exec ip: "+ipAddress);
            
            
		}
    	
    	System.out.println("Main in process..."+threadPool.getThreadPool().getActiveCount());

		do {
			try {
				Thread.sleep(5000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		
		System.out.println("Main End");
    	
    	
    }
    
	private CopyRankByDomainAndEngineCommand getUpdateCommand(String ipAddress, int searchEngine, int LanguageID,Date copyFrom,Date copyTo,boolean isMobile) {
		CopyRankByDomainAndEngineCommand crawlCommand = new CopyRankByDomainAndEngineCommand(ipAddress, searchEngine, LanguageID, copyFrom, copyTo, isMobile);
        crawlCommand.setStatus(true);
        return crawlCommand;
    }

}
