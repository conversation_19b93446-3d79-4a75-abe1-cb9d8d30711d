package seoclarity.backend.copy.claritydb;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import seoclarity.backend.dao.actonia.RankingDailyMonitorEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.SeoRankingMonitorEntity;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @date 2018-02-07
 * seoclarity.backend.copy.claritydb.ReUploadKPRankingData
 * 
 * https://www.wrike.com/open.htm?id=210436658
 * Now we only fix location_id = 0 ranking keywords
 */
public class ReUploadKPRankingData {
	private static int fixDate = 0;
	private static int queryDate = 0;
	private static int pageSize = 200;
	
	public static final String MONITOR_TABLE_PREFIX = "daily_ranking_imp_monitor_";
	public static final String MONITOR_TABLE_HOT = "hot_";
	public static final String MONITOR_TABLE_COLD = "col_";
	private static boolean hotCluster = false;
	
	private SimpleDateFormat yyyymmdd = new SimpleDateFormat("yyyyMMdd");
	private SimpleDateFormat yyyy_mm_dd = new SimpleDateFormat("yyyy-MM-dd");
	
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private RankingDailyMonitorEntityDAO rankingDailyMonitorEntityDAO;
	
	public ReUploadKPRankingData() {
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		rankingDailyMonitorEntityDAO = SpringBeanFactory.getBean("rankingDailyMonitorEntityDAO");
	}
	
	public static void main(String[] args) {
		fixDate = Integer.valueOf(args[0]);
		queryDate = Integer.valueOf(args[1]);
		System.out.println("fixDate:" + fixDate + ", queryDate:" + queryDate);
		ReUploadKPRankingData ins = new ReUploadKPRankingData();
		try {
			ins.process();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void process() throws Exception{
		
		int locationId = 0;
		boolean isMobile = false;
		String monitorTableName = MONITOR_TABLE_PREFIX + (hotCluster ? MONITOR_TABLE_HOT : MONITOR_TABLE_COLD) 
				+ fixDate;
		String fixDateStr = yyyy_mm_dd.format(yyyymmdd.parse(String.valueOf(fixDate)));
		String queryDateStr = yyyy_mm_dd.format(yyyymmdd.parse(String.valueOf(queryDate)));
		// get all engine-language list;
		boolean isUS = false;
		// desktop
		List<Map<String, Object>> engineList = clDailyRankingEntityDao.getEngineLanguageListInfoTable(isUS, isMobile, locationId, String.valueOf(queryDateStr));
		// query lost keywords and delete in monitor table
		queryLostKeywordsAndDeleteMonitor(1, 1, isMobile, locationId, String.valueOf(fixDateStr), String.valueOf(queryDateStr), monitorTableName);
		for (Map<String, Object> row : engineList) {
			int engine = Integer.valueOf(row.get("engine_id").toString());
			int language = Integer.valueOf(row.get("language_id").toString());
			queryLostKeywordsAndDeleteMonitor(engine, language, isMobile, locationId, String.valueOf(fixDateStr), String.valueOf(queryDateStr), monitorTableName);
		}
		
		// mobile
		isMobile = true;
		engineList = clDailyRankingEntityDao.getEngineLanguageListInfoTable(isUS, isMobile, locationId, String.valueOf(queryDateStr));
		// query lost keywords and delete in monitor table
		queryLostKeywordsAndDeleteMonitor(1, 1, isMobile, locationId, String.valueOf(fixDateStr), String.valueOf(queryDateStr), monitorTableName);
		for (Map<String, Object> row : engineList) {
			int engine = Integer.valueOf(row.get("engine_id").toString());
			int language = Integer.valueOf(row.get("language_id").toString());
			queryLostKeywordsAndDeleteMonitor(engine, language, isMobile, locationId, String.valueOf(fixDateStr), String.valueOf(queryDateStr), monitorTableName);
		}
	}
	
	private boolean queryLostKeywordsAndDeleteMonitor(int engine, int language, boolean isMobile, int locationId, String fixDate, String queryDate, String monitorTableName) {
		// query all lost keywords in fixDate
		List<Integer> lostKidList = clDailyRankingEntityDao.getAllLostKeywordByInfoTable(engine, language, isMobile, locationId, String.valueOf(fixDate), String.valueOf(queryDate));
		Set<String> kidSet = new HashSet<String>();
		System.out.println("#####fixDate:" + fixDate + ", engine:" + engine + ", language:" + language + ", locationId:" + locationId + ", isMobile:" + isMobile + ", lostKidList:" + lostKidList.size());
		if (lostKidList.size() == 0) {
			return false;
		}
		// confirm in clarityDB
		List<Integer> tempKidList = new ArrayList<Integer>();
		for (int kid : lostKidList) {
			tempKidList.add(kid);
			kidSet.add(String.valueOf(kid));
			if (tempKidList.size() >= pageSize) {
				List<Integer> existsList = clDailyRankingEntityDao.ckeckKidExistsInInfoTable(engine, language, isMobile, locationId, fixDate, tempKidList.toArray(new Integer[tempKidList.size()]));
				if (existsList.size() > 0) {
					for (Integer extKid : existsList) {
						kidSet.remove(extKid.toString());
					}
					System.out.println("existsList:" + existsList.size());
				}
				tempKidList.clear();
			}
		}
		if (tempKidList.size() > 0) {
			List<Integer> existsList = clDailyRankingEntityDao.ckeckKidExistsInInfoTable(engine, language, isMobile, locationId, fixDate, tempKidList.toArray(new Integer[tempKidList.size()]));
			if (existsList.size() > 0) {
				for (Integer extKid : existsList) {
					kidSet.remove(extKid.toString());
				}
				System.out.println("existsList:" + existsList.size());
			}
			tempKidList.clear();
		}
		System.out.println("###engine:" + engine + ", language:" + language + ", locationId:" + locationId + ", isMobile:" + isMobile + ", lostKidList:" + lostKidList.size() + ", kidSet:" + kidSet.size());

		// delete in monitor 
		int type = isMobile ? SeoRankingMonitorEntity.TYPE_MOBILE : SeoRankingMonitorEntity.TYPE_REGULAR;
		int requency = 1;
		List<String> tempList = new ArrayList<String>();
		System.out.println("Start to delete monitor table, table:" + monitorTableName + ", type:" + type + ", requency:" + requency);
		for (String kid : kidSet) {
			tempList.add(kid);
			if (tempList.size() >= pageSize) {
				rankingDailyMonitorEntityDAO.delete(monitorTableName, tempList, engine, language, locationId, requency, type);
//				System.out.println("tempList:" + tempList.subList(0, 10));
				tempList.clear();
			}
		}
		if (tempList.size() > 0) {
			rankingDailyMonitorEntityDAO.delete(monitorTableName, tempList, engine, language, locationId, requency, type);
			tempList.clear();
		}
		return true;
	}
	
	

}
