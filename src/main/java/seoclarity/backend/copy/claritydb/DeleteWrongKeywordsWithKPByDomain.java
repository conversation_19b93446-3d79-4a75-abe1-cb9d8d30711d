package seoclarity.backend.copy.claritydb;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.actonia.RankingDailyMonitorEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.actonia.SeoRankingMonitorEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.ClarityDBConnection;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @date 2018-06-23
 * seoclarity.backend.copy.claritydb.DeleteWrongKeywordsWithKPByDomain
 * https://www.wrike.com/open.htm?id=247292905
 * 
 * upload info, detail, subrank table  data which sign = -1, then merge these tables.
 */
public class DeleteWrongKeywordsWithKPByDomain {
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private RankingDailyMonitorEntityDAO rankingDailyMonitorEntityDAO;
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	
	public DeleteWrongKeywordsWithKPByDomain() {
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		rankingDailyMonitorEntityDAO = SpringBeanFactory.getBean("rankingDailyMonitorEntityDAO");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
	}
	
	public static void main(String[] args) throws Exception{
		SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
		String dir = args[0];
		String startDate = args[1];
		String endDate = args[2];
		
		Calendar cal = Calendar.getInstance();
		cal.setTime(sf.parse(startDate));
		Date end = sf.parse(endDate);
		
		while (cal.getTime().getTime() <= end.getTime()) {
			try {
				
				DeleteWrongKeywordsWithKPByDomain ins = new DeleteWrongKeywordsWithKPByDomain();
				threadPool.init();
				CommonUtils.initThreads(3);
				ins.processDate(dir, cal.getTime());
				threadPool.destroy();
			} catch (Exception e) {
				e.printStackTrace();
			}
			cal.add(Calendar.DAY_OF_MONTH, 1);
		}
	}
	
	private void processDate(String dir, Date wordDate) {
		SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
		File[] folders = new File(dir).listFiles();
		File processFolder = null;
		String currentDate = sf.format(wordDate);
		for (File folder : folders) {
			if (folder.getName().equals(currentDate)) {
				processFolder = folder;
				break;
			}
		}
		System.out.println("wordDate:" + wordDate + ", currentDate:" + currentDate + ", processFolder:" + processFolder.getName());
		
		File[] files = processFolder.listFiles();
		for (File file : files) {
			if (!file.getName().endsWith("_" + currentDate + ".csv.process")) {
				continue;
			}
			try {
				processDomain(file, currentDate);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
	}
	
	// Date,DomainId,EngineId,LanguageId,KeywordId,KeywordName,Fps,Cpc,AvgSearchVolume
	private void processDomain(File file, String currentDate) throws Exception{
		int pageSize = 200;
		String[] temp = StringUtils.split(file.getName(), '.');
		int oid = Integer.valueOf(StringUtils.split(temp[0], "_")[0]);
		if (oid == 4) {
			return;
		}
		if (oid == 150) {
			return;
		}
		System.out.println("file:" + file.getName() + ", oid:" + oid);
		List<String> lines = FileUtils.readLines(file, "utf-8"); 
		CSVParser parser = new CSVParser(',');
		
		Set<String> kwSet = new LinkedHashSet<String>();
		Set<String> engList = new HashSet<String>();
		
		for (String line : lines) {
			if (StringUtils.isBlank(line)) {
				continue;
			}
			if (StringUtils.startsWithIgnoreCase(line, "Date,")) {
				continue;
			}
			String[] row = parser.parseLine(line);
			int engine = Integer.valueOf(row[2]);
			int language = Integer.valueOf(row[3]);
			engList.add(engine + "-" + language);
			
			if (engine == 110 || engine == 150 || engine == 155 || engine == 160 || engine == 888 || engine == 999
					|| engine == 255 || engine == 100 || engine == 120 || engine == 170) {
				continue;
			}
			
			String kw = row[5].toLowerCase();
			
			String key = new String(engine + "," + language + "," + kw);
			if (kwSet.contains(key)) {
				System.out.println("duplicated kw, oid:" + oid + ", kw:" + key);
			} else {
				kwSet.add(key);
			}
		}
		
		if (kwSet.size() == 0) {
			System.out.println("Skip domain:" + oid + ", kwSet:" + kwSet.size() + ", engList:" + engList);
			return;
		}
		if (engList.size() > 1) {
			System.out.println("Skip multi engine language domain:" + oid + ", kwSet:" + kwSet.size() + ", engList:" + engList.size());
			return;
		}
		
		// check rank check
		List<String> kwList = new ArrayList<String>();
		List<SeoClarityKeywordEntity> kwEntityList = new ArrayList<SeoClarityKeywordEntity>();
		for (String kw : kwSet) {
			String kwStr = StringUtils.split(kw, ',')[2];
			kwList.add(kwStr);
			if (kwList.size() >= pageSize) {
				List<SeoClarityKeywordEntity> tempList = seoClarityKeywordEntityDAO.getKeywordEntityListByNames(kwList);
				kwEntityList.addAll(tempList);
				kwList.clear();
			}
		}
		if (kwList.size() > 0) {
			List<SeoClarityKeywordEntity> tempList = seoClarityKeywordEntityDAO.getKeywordEntityListByNames(kwList);
			kwEntityList.addAll(tempList);
		}
		System.out.println("===file:" + file.getName() + ", oid:" + oid + ", lines:" + lines.size() + ", kwSet:" + kwSet.size() + ", kwEntityList:" + kwEntityList.size());
		if (kwSet.size() != kwEntityList.size()) {
			System.out.println("Wrong domain, OID:" + oid + ", lines:" + lines.size() + ", kwSet:" + kwSet.size() + ", kwEntityList:" + kwEntityList.size());
		}
		
		String[] engArray = engList.toArray(new String[engList.size()]);
		String[] x = StringUtils.split(engArray[0], "-");
		processCheckAndDeleteInKP(Integer.valueOf(x[0]), Integer.valueOf(x[1]), oid, currentDate, kwEntityList);
	}
	
	private void processCheckAndDeleteInKP(int engine, int language, int oid, String currentDate, List<SeoClarityKeywordEntity> kwEntityList) throws Exception{
		boolean isMobile = false;
		int locationId = 0;
		Set<Integer> kidList = new LinkedHashSet<Integer>();
		
		for (SeoClarityKeywordEntity entity : kwEntityList) {
			kidList.add(entity.getId());
		}
		
//		String rankingTableName = "daily_ranking_imp_monitor_col_" + currentDate;
//		// check exists in monitor
//		long a = System.currentTimeMillis();
//		for (SeoClarityKeywordEntity kw : kwEntityList) {
//			boolean isExists = rankingDailyMonitorEntityDAO.keywordExists(rankingTableName, kw.getId(), engine, language, locationId, 1, SeoRankingMonitorEntity.TYPE_REGULAR, oid);
//			if (!isExists) {
//				System.out.println("=kw not exists in monitor, OID:" + oid + ", kid:" + kw.getId() + ", kw:" + kw.getKeywordText() + ", engine:" + engine + ", language:" + language + ", currentDate:" + currentDate);
//				continue;
//			}
//			kidList.add(kw.getId());
//		}
//		long b = System.currentTimeMillis();
//		System.out.println("===OID:" + oid + ", currentDate:" + currentDate + ", total:" + kwEntityList.size() + ", kidList:" + kidList.size() + ", cost:" + (b - a) / 1000);
		
		SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
		SimpleDateFormat yyyyMMdd2 = new SimpleDateFormat("yyyy-MM-dd");
		
		String queryDate = yyyyMMdd2.format(yyyyMMdd.parse(currentDate));
		
		// check exists in clarityDB info table
		List<Map<String, Object>> existsKwList = clDailyRankingEntityDao.checkExistsFromInfoTableForKidList(oid, engine, language, locationId, queryDate, queryDate, isMobile, kidList);
		Set<String> processKidList = new LinkedHashSet<String>(); 
		for (Map<String, Object> map : existsKwList) {
			String kid = map.get("keyword_rankcheck_id").toString();
			processKidList.add(kid);
		}
		List<Map<String, Object>> wrongList = clDailyRankingEntityDao.checkExistsFromInfoTableForKidList(oid, engine, language, locationId, queryDate, queryDate, isMobile, kidList, -1);
		if (wrongList != null && wrongList.size() > 0) {
			System.out.println("=wrongList, OID:" + oid + ", size:" + wrongList.size() + ", kids:" + wrongList);
			for (Map<String, Object> map : existsKwList) {
				String kid = map.get("keyword_rankcheck_id").toString();
				if (processKidList.contains(kid)) {
					System.out.println("Wrong, already added kw in KP, OID:" + oid + ", engine:" + engine + ", language:" + language + ", kid:" + kid + ", queryDate:" + queryDate);
					processKidList.remove(kid);
				}
			}
		}
		
		for (Integer kid : kidList) {
			if (!processKidList.contains(kid.toString())) {
				System.out.println("=kw not exists in KP, OID:" + oid + ", kid:" + kid + ", engine:" + engine + ", language:" + language + ", currentDate:" + currentDate);
			}
		}
		System.out.println("===OID:" + oid + ", currentDate:" + currentDate + ", total:" + kwEntityList.size() + ", kidList:" + kidList.size() + ", processKidList:" + processKidList.size());
		
		if (processKidList.size() == 0) {
			return;
		}
		
		// batch insert -1
		List<String> tempList = new ArrayList<String>();
		for (String kid : processKidList) {
			tempList.add(kid);
			if (tempList.size() >= pageSize) {
				try {
					bulkInsert(engine, language, oid, currentDate, tempList.toArray(new String[tempList.size()]));
				} catch (Exception e) {
					e.printStackTrace();
				}
				tempList.clear();
			}
		}
		if (tempList.size() > 0) {
			try {
				bulkInsert(engine, language, oid, currentDate, tempList.toArray(new String[tempList.size()]));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
		do {
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
	}
	
	private void bulkInsert(int engine, int language, int oid, String currentDate, String[] processKidList) {
		String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
		while(true) {
			if (ipAddress == null) {
				try {
					Thread.sleep(1000);
					ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
				} catch (Exception e) {
				}
			} else {
				break;
			}
		}
		List<String> list = Arrays.asList(processKidList);
		Command cmd = new Command(ipAddress, engine, language, oid, currentDate, list);
		cmd.setStatus(true);
		
		try {
			threadPool.execute(cmd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	private static StringBuffer INFO_SQL = new StringBuffer();
	private static StringBuffer DETAIL_SQL = new StringBuffer();
	private static StringBuffer SUBRANK_SQL = new StringBuffer();
	private static int pageSize = 150;
	static {
		INFO_SQL.append(" insert into seo_daily_ranking.{%d} (own_domain_id, engine_id, language_id, location_id, ranking_date, keyword_rankcheck_id, sign)                                   ");
		INFO_SQL.append(" select own_domain_id, engine_id, language_id, location_id, ranking_date, keyword_rankcheck_id, toInt8(-1)                                                      ");
		INFO_SQL.append(" from seo_daily_ranking.{%d}                                                                                                                                         ");
		INFO_SQL.append(" where own_domain_id = ? and ranking_date = ? and sign = 1 and location_id = 0 and engine_id = ? and language_id = ? and keyword_rankcheck_id in ({%kid})    ");
		
		DETAIL_SQL.append(" insert into seo_daily_ranking.{%d} (own_domain_id, engine_id, language_id, location_id, ranking_date, root_domain_reverse, keyword_rankcheck_id, true_rank, url,folder_level1,folder_level2,folder_level3,label,meta,sign)     ");
		DETAIL_SQL.append(" select own_domain_id, engine_id, language_id, location_id, ranking_date, root_domain_reverse, keyword_rankcheck_id, true_rank, url,folder_level1,folder_level2,folder_level3,label,meta,toInt8(-1)                                 ");
		DETAIL_SQL.append(" from seo_daily_ranking.{%d}                                                                                                                                           ");
		DETAIL_SQL.append(" where own_domain_id = ? and ranking_date = ? and sign = 1 and location_id = 0 and engine_id = ? and language_id = ? and keyword_rankcheck_id in ({%kid}) ");
		
		SUBRANK_SQL.append(" insert into seo_daily_ranking.{%d} (own_domain_id, engine_id, language_id, location_id, ranking_date, rank, sub_rank, keyword_rankcheck_id, sign)    ");
		SUBRANK_SQL.append(" select  own_domain_id, engine_id, language_id, location_id, ranking_date, rank, sub_rank, keyword_rankcheck_id, toInt8(-1)                                                ");
		SUBRANK_SQL.append(" from  seo_daily_ranking.{%d}                                                                                                                                                            ");
		SUBRANK_SQL.append(" where own_domain_id = ? and ranking_date = ? and sign = 1 and location_id = 0 and engine_id = ? and language_id = ? and keyword_rankcheck_id in ({%kid}) ");
	}
	
	class Command extends BaseThreadCommand {
		private String ipAddress;
		private Collection<String> processKidList;
		private int engine;
		private int language;
		private int oid;
		private String currentDate;
		private Connection connection;
		
		private String infoTable = "d_ranking_info_";
		private String detailTable = "d_ranking_detail_";
		private String subRankTable = "d_ranking_subrank_";
		
//		private String infoTable = "test_fix_local_d_ranking_info_";
//		private String detailTable = "test_fix_local_d_ranking_detail_";
//		private String subRankTable = "test_fix_local_d_ranking_subrank_";
		
		public Command(String ipAddress, int engine, int language, int oid, String currentDate, Collection<String> processKidList) {
			this.ipAddress = ipAddress;
			this.engine = engine;
			this.language = language;
			this.oid = oid;
			this.currentDate = currentDate;
			this.processKidList = processKidList;
			String tableSiffix = "";
			if (engine == 1 && language == 1) {
				tableSiffix = "us";
			} else {
				tableSiffix = "intl";
			}
			// tableName
			infoTable = infoTable + tableSiffix;
			detailTable = detailTable + tableSiffix;
			subRankTable = subRankTable + tableSiffix;
		}
		
		@Override
		protected void execute() throws Exception {
			long a = System.currentTimeMillis();
			processInsert(processKidList, engine, language, oid, currentDate);
			long b = System.currentTimeMillis();
			System.out.println("End command IP: " + ipAddress + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
			CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
		}
		
		
		private void processInsert(Collection<String> processKidList, int engine, int language, int oid, String currentDate) throws Exception{
//			connection = ClarityDBConnection.getDBConnection("***********:8123"); 
			connection = ClarityDBConnection.getDBConnection("************:8123");
			PreparedStatement keywordLevelStatement = null;
			PreparedStatement keywordDetailStatement = null;
			PreparedStatement keywordSubRankStatement = null;
			
			Date workDate = new SimpleDateFormat("yyyyMMdd").parse(currentDate);
			String kidList = StringUtils.join(processKidList, ',');
			
			try {
				String info = StringUtils.replace(INFO_SQL.toString(), "{%d}", infoTable);
				info = StringUtils.replace(info, "{%kid}", kidList);
				
				String detail = StringUtils.replace(DETAIL_SQL.toString(), "{%d}", detailTable);
				detail = StringUtils.replace(detail, "{%kid}", kidList);
				
				String sub = StringUtils.replace(SUBRANK_SQL.toString(), "{%d}", subRankTable);
				sub = StringUtils.replace(sub, "{%kid}", kidList);
				
//				System.out.println(info);
//				System.out.println(detail);
//				System.out.println(sub);
				
				keywordLevelStatement = connection.prepareStatement(info);
				keywordDetailStatement = connection.prepareStatement(detail);
				keywordSubRankStatement = connection.prepareStatement(sub);
				
				
				int index = 1;
				keywordLevelStatement.setInt(index++, oid);
				keywordLevelStatement.setDate(index++, new java.sql.Date(workDate.getTime()));
				keywordLevelStatement.setInt(index++, engine);
				keywordLevelStatement.setInt(index++, language);
				
				index = 1;
				keywordDetailStatement.setInt(index++, oid);
				keywordDetailStatement.setDate(index++, new java.sql.Date(workDate.getTime()));
				keywordDetailStatement.setInt(index++, engine);
				keywordDetailStatement.setInt(index++, language);
				
				index = 1;
				keywordSubRankStatement.setInt(index++, oid);
				keywordSubRankStatement.setDate(index++, new java.sql.Date(workDate.getTime()));
				keywordSubRankStatement.setInt(index++, engine);
				keywordSubRankStatement.setInt(index++, language);
					
				keywordLevelStatement.execute();
				keywordDetailStatement.execute();
				keywordSubRankStatement.execute();
				
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				if (keywordLevelStatement != null) {
					keywordLevelStatement.close();
				}
				if (keywordDetailStatement != null) {
					keywordDetailStatement.close();
				}
				if (keywordSubRankStatement != null) {
					keywordSubRankStatement.close();
				}
				if (connection != null) {
					ClarityDBConnection.closeConnection(connection);
				}
			}
		}

		@Override
		protected void undo() throws Exception {
		}
	}
	
}
