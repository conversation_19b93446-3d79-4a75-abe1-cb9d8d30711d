package seoclarity.backend.copy.claritydb;

import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.SearchEngineContinentRelDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.SearchEngineContinentRelEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
//import java.util.stream.Collectors;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.copy.claritydb.MonitorDetailClarityDB" -Dexec.cleanupDaemonThreads=false 
 * -Dexec.args="2016-12-12 2016-12-12"
 * <AUTHOR>
 *
 */
public class MonitorDetailClarityDB {
	
	private SearchEngineContinentRelDAO searchEngineContinentRelDAO = SpringBeanFactory.getBean("searchEngineContinentRelDAO");
	private ClDailyRankingEntityDao clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
	
	private Map<String, String> continentMap = new HashMap<String, String>();
	
	private static final float RADIO = 0.98F;
	
	public static void main(String[] args) {
		
		MonitorDetailClarityDB monitorDetailClarityDB = new MonitorDetailClarityDB();
		monitorDetailClarityDB.process();
		
		
	}
	
	private void init(){
		// continent
		List<SearchEngineContinentRelEntity>  allSearchEngineContinentRelEntities = searchEngineContinentRelDAO.getAllSearchEngines();
		
		for (SearchEngineContinentRelEntity searchEngineContinentRelEntity : allSearchEngineContinentRelEntities) {
			continentMap.put(searchEngineContinentRelEntity.getSearchEngineId()+"-"+searchEngineContinentRelEntity.getLanguageId(), searchEngineContinentRelEntity.getContinent());
		}
		
		// 
		
		
	}

	private void process() {
		
		Date curDate = new Date();
//		curDate = DateUtils.addDays(curDate, -1);
		
		Date yesterDay = DateUtils.addDays(curDate, -1);
		
		// process destop intl
		System.out.println(" ==== <<<<<<<< DeskTop Intl >>>>>>>>");
		processByEngine(curDate, yesterDay,99,false);
		
		System.out.println();
		System.out.println(" ==== <<<<<<<< Mobile Intl >>>>>>>>");
		processByEngine(curDate, yesterDay,99,true);
		
		System.out.println();
		System.out.println(" ==== <<<<<<<< DeskTop US >>>>>>>>");
		processByEngine(curDate, yesterDay,1,false);
		
		System.out.println();
		System.out.println(" ==== <<<<<<<< Mobile US >>>>>>>>");
		processByEngine(curDate, yesterDay,1,true);
		
	}

	private void processByEngine(Date curDate, Date yesterDay,int engine,boolean isMobile) {
		
		List<Map<String, Object>> todyasMap = clDailyRankingEntityDao.selectDetailCntByEngineLanguage(engine, curDate, isMobile);
		List<Map<String, Object>> yesterDayMap = clDailyRankingEntityDao.selectDetailCntByEngineLanguage(engine, yesterDay, isMobile);
		
		Map<String, Integer> todayCntMap = convSearchEngineMap(todyasMap);
		Map<String, Integer> yestodayCntMap = convSearchEngineMap(yesterDayMap);
		
		List<String> sortedList = null;//yestodayCntMap.keySet().stream().sorted().collect(Collectors.toList());
		
		for (String key : sortedList) {
			String yestCnt = yestodayCntMap.get(key).toString();
			String todatCnt = "0";
			if(todayCntMap.get(key) !=null){
				todatCnt = todayCntMap.get(key).toString();
			}
			
			System.out.println("== "+ key + " -> Y: "+yestCnt+" T: "+todatCnt+"  "+getTotalCntRadio(yestCnt,todatCnt));
			
		}
	}
	
	private Map<String, Integer> convSearchEngineMap(List<Map<String, Object>> pMap){
		Map<String, Integer> reMap = new HashMap<String, Integer>();
		
		for (Map<String, Object> map : pMap) {
			String engine = map.get("engine_id").toString();
			String language_id = map.get("language_id").toString();
			int cnt = Integer.parseInt(map.get("cnt").toString());
			
			
			reMap.put(engine+"-"+language_id, cnt);
		}
		
		return reMap;
	} 
	
	public String getTotalCntRadio(String p1,String p2) {
		if(p1 != null && p2 !=null){
			try {
				Float fen = Float.parseFloat(p2.toString())/Float.parseFloat(p1.toString());
//				fen.s
				DecimalFormat fnum = new DecimalFormat("##0.00");
				String dd = fnum.format(fen);
				if(fen > RADIO){
					return dd+"";
				}
				return dd+"    ------->NG";
			} catch (Exception e) {
				return "NG";
			}
		}
		return "NG";
	}

}
