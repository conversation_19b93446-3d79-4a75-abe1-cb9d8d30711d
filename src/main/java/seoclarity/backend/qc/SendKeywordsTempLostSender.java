package seoclarity.backend.qc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.DeleteQueueRequest;
import com.google.gson.Gson;

import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.service.GeoService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.service.ScKeywordSearchVolumeManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.RankCheckUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.SQSUtils;
import seoclarity.backend.utils.cityhash.CityHashUtil;

/**
 * <AUTHOR>
 * @date 2021-05-24
 * @path seoclarity.backend.qc.SendKeywordsTempLostSender
 * https://www.wrike.com/open.htm?id=694189696
 * send lost keywords to re-crawl
 */
public class SendKeywordsTempLostSender {
	// use dead letter sqs
	private static final String SQS_COMMON_DESKTOP = "DEAD_LETTER_KEYWROD_RANK_QUEUE_GOOGLE_COM_USEN";
//	private static final String SQS_COMMON_DESKTOP = "SQS_TEMP_LOST_CHECK_DESKTOP";
	private static final String SQS_COMMON_MOBILE = "DEAD_LETTER_QUEUE_NAME_GOOGLE_MOBILE_COMMON_CRAWL";
//	private static final String SQS_COMMON_MOBILE = "SQS_TEMP_LOST_CHECK_MOBILE";
	private static final String SQS_COMMON_DESKTOP_CITY = "SQS_TEMP_LOST_CHECK_DESKTOP_CITY";
	private static final String SQS_COMMON_MOBILE_CITY = "SQS_TEMP_LOST_CHECK_MOBILE_CITY";
	
	private ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private ScKeywordRankManager scKeywordRankManager;
	private GeoService geoService;
	private boolean isDeleteSqs = false;
	
	private static int SENDT_TO_SQS_DATE = 0;
	
	public SendKeywordsTempLostSender() {
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
		geoService = SpringBeanFactory.getBean("geoService");
	}
	
	public SendKeywordsTempLostSender(boolean isDeleteSqs) {
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
		geoService = SpringBeanFactory.getBean("geoService");
		this.isDeleteSqs = isDeleteSqs;
	}
	
	public List<String> sendToSqs(boolean isMobile, boolean isGeo, Map<String, Set<String>> lostMap, int sendToSqsDate) {
		SENDT_TO_SQS_DATE = sendToSqsDate;
		CommonUtils.initThreads(10);
		threadPool.init();
		String SQS_NAME = null;
		if (isMobile) {
			SQS_NAME = isGeo ? SQS_COMMON_MOBILE_CITY : SQS_COMMON_MOBILE;
		} else {
			SQS_NAME = isGeo ? SQS_COMMON_DESKTOP_CITY : SQS_COMMON_DESKTOP;
		}
		
		AmazonSQS amazonSQS = null;
		String queryUrl = null;
		while(true) {
			try {
				amazonSQS = SQSUtils.getAmazonSQS();
				queryUrl = SQSUtils.createQueue(SQS_NAME, amazonSQS);
				System.out.println("===Create sqs:" + SQS_NAME + ", url:" + queryUrl + ", sendToSqsDate:" + sendToSqsDate + ", isDeleteSqs:" + isDeleteSqs + ", will delete it in 60s...");
				try {
					Thread.sleep(60000);
				} catch (Exception e) {
				}
				if (isDeleteSqs) {
					amazonSQS.deleteQueue(new DeleteQueueRequest(queryUrl));
					try {
						Thread.sleep(65000);
					} catch (Exception e) {
					}
					queryUrl = SQSUtils.createQueue(SQS_NAME, amazonSQS);
				}
				break;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
		List<String> infoList = new ArrayList<>();
		for (String key : lostMap.keySet()) {
			int engine = Integer.valueOf(StringUtils.split(key, "-")[0]);
			int language = Integer.valueOf(StringUtils.split(key, "-")[1]);
			Set<String> kidSet = lostMap.get(key);
			String info = "";
			try {
				if (!isGeo) {
					info = processSendWithKeywordsId(amazonSQS, queryUrl, isMobile, kidSet, engine, language, sendToSqsDate);
				} else {
					info = processSendWithKeywordsIdForGeo(amazonSQS, queryUrl, isMobile, kidSet, engine, language, sendToSqsDate);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			infoList.add(info);
		}
		
		threadPool.destroy();
		return infoList;
	}
	
	private String processSendWithKeywordsId(AmazonSQS amazonSQS, String queryUrl, boolean isMobile, Set<String> lines, int engine, int language, int sendToSqsDate) throws Exception{
		int total = 0;
		int fact = 0;
		List<SeoClarityKeywordEntity> tempList = null;
		List<Integer> kidList = new ArrayList<Integer>();
		
//		Map<String, String> kwNameMap = new HashMap<String, String>();
		String device = isMobile ? "m" : "d";
		
		System.out.println("===lines:" + lines.size() + ", engine:" + engine + ", language:" + language);
		
		for (String line : lines) {
			String kid = line;
			kidList.add(Integer.valueOf(kid));
			
			if (StringUtils.isBlank(line)) {
				continue;
			}
			
			total++;
			
			if (kidList.size() >= 300) {
				
//				tempList = seoClarityKeywordEntityDAO.getKeywordsOfGoogleQueryForRel(kidList, device, language, engine);
//				// get kwName
//				List<SeoClarityKeywordEntity> list = seoClarityKeywordEntityDAO.getKeywordEntityListByIds(kidList);
//				for (SeoClarityKeywordEntity kw : list) {
//					kwNameMap.put(String.valueOf(kw.getId()), kw.getKeywordText());
//				}
//				tempList.stream().forEach(kw -> {kw.setKeywordText(kwNameMap.get(String.valueOf(kw.getId())));});
				
				tempList = seoClarityKeywordEntityDAO.getNationalKeywordSERelList(engine, language, device, kidList);
				tempList = tempList.stream().filter(x -> StringUtils.isNotBlank(x.getKeywordText())).collect(Collectors.toList());
				fact += tempList.size();
				System.out.println("=engine:" + engine + ", language:" + language + ", kidList:" + kidList.size() + ", tempList:" + tempList.size() + ", total:" + total + ", fact:" + fact);
				processCommandV3(engine, language, amazonSQS, queryUrl, tempList);
				kidList.clear();
			}
		}
		
		if (kidList.size() > 0) {
			
//			tempList = seoClarityKeywordEntityDAO.getKeywordsOfGoogleQueryForRel(kidList, device, language, engine);
//			// get kwName
//			List<SeoClarityKeywordEntity> list = seoClarityKeywordEntityDAO.getKeywordEntityListByIds(kidList);
//			for (SeoClarityKeywordEntity kw : list) {
//				kwNameMap.put(String.valueOf(kw.getId()), kw.getKeywordText());
//			}
//			tempList.stream().forEach(kw -> {kw.setKeywordText(kwNameMap.get(String.valueOf(kw.getId())));});
			
			tempList = seoClarityKeywordEntityDAO.getNationalKeywordSERelList(engine, language, device, kidList);
			tempList = tempList.stream().filter(x -> StringUtils.isNotBlank(x.getKeywordText())).collect(Collectors.toList());
			fact += tempList.size();
			System.out.println("=engine:" + engine + ", language:" + language + ", kidList:" + kidList.size() + ", tempList:" + tempList.size() + ", total:" + total + ", fact:" + fact);
			processCommandV3(engine, language, amazonSQS, queryUrl, tempList);
			kidList.clear();
		}
		
		do {
			try {
				Thread.sleep(5000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		
		String info = "send finished. engine:" + engine + ", language:" + language + ", isMobile:" + isMobile + ", total send:" + total + ", fact:" + fact + ", SQS:" + StringUtils.substringAfterLast(queryUrl, "/");
		System.out.println("===" + info);
		return info;
	}
	
	private String processSendWithKeywordsIdForGeo(AmazonSQS amazonSQS, String queryUrl, boolean isMobile, Set<String> lines, int engine, int language, int sendToSqsDate) throws Exception{
		int total = 0;
		int fact = 0;
		List<SeoClarityKeywordEntity> tempList = new ArrayList<SeoClarityKeywordEntity>();
		List<Integer> kidList = new ArrayList<Integer>();
		
//		Map<String, String> kwMap = new HashMap<String, String>();
		
		System.out.println("===lines:" + lines.size() + ", engine:" + engine + ", language:" + language);
		
		for (String line : lines) {
			String kid = StringUtils.split(line, "-")[0];
			String location_id = StringUtils.split(line, "-")[1];
			kidList.add(Integer.valueOf(kid));
			
			if (StringUtils.isBlank(line)) {
				continue;
			}
			total++;
			//https://www.wrike.com/open.htm?id=615024458
			List<SeoClarityKeywordEntity> kwListNew = getKeywordFromDB(engine, language, isMobile, kid, location_id);
			tempList.addAll(kwListNew);
			
			if (tempList.size() >= 300) {
				
//				// get kwName
//				List<SeoClarityKeywordEntity> list = seoClarityKeywordEntityDAO.getKeywordEntityListByIds(kidList);
//				for (SeoClarityKeywordEntity kw : list) {
//					kwMap.put(String.valueOf(kw.getId()), kw.getKeywordText());
//				}
//				tempList.stream().forEach(kw -> {kw.setKeywordText(kwMap.get(String.valueOf(kw.getId())));});
				
				tempList = tempList.stream().filter(x -> StringUtils.isNotBlank(x.getKeywordText())).collect(Collectors.toList());
				fact += tempList.size();
				System.out.println("=engine:" + engine + ", language:" + language + ", kidList:" + kidList.size() + ", tempList:" + tempList.size() + ", total:" + total + ", fact:" + fact);
				scKeywordRankManager.findSearchVolV2(tempList, language, true);
				processCommandV3(engine, language, amazonSQS, queryUrl, tempList);
				kidList.clear();
				tempList.clear();
			}
		}
		
		if (tempList.size() > 0) {
			
//			// get kwName
//			List<SeoClarityKeywordEntity> list = seoClarityKeywordEntityDAO.getKeywordEntityListByIds(kidList);
//			for (SeoClarityKeywordEntity kw : list) {
//				kwMap.put(String.valueOf(kw.getId()), kw.getKeywordText());
//			}
//			tempList.stream().forEach(kw -> {kw.setKeywordText(kwMap.get(String.valueOf(kw.getId())));});
			
			tempList = tempList.stream().filter(x -> StringUtils.isNotBlank(x.getKeywordText())).collect(Collectors.toList());			
			fact += tempList.size();
			System.out.println("=engine:" + engine + ", language:" + language + ", kidList:" + kidList.size() + ", tempList:" + tempList.size() + ", total:" + total + ", fact:" + fact);
			scKeywordRankManager.findSearchVolV2(tempList, language, true);
			processCommandV3(engine, language, amazonSQS, queryUrl, tempList);
			kidList.clear();
		}
		
		do {
			try {
				Thread.sleep(5000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		
		String info = "send finished. engine:" + engine + ", language:" + language + ", isMobile:" + isMobile + ", isGeo:" + true + ", total send:" + total + ", fact:" + fact + ", SQS:" + StringUtils.substringAfterLast(queryUrl, "/");
		System.out.println("===" + info);
		return info;
	}
	
	private List<SeoClarityKeywordEntity> getKeywordFromDB(int engine, int language, boolean isMobile, String kid, String location_id) {
		List<SeoClarityKeywordEntity> resultList = new ArrayList<>();
		String device = isMobile ? RcKeywordSeRelEntity.DEVICE_MOBILE : RcKeywordSeRelEntity.DEVICE_NOT_MOBILE;
		try {
			
//			SeoClarityKeywordEntity seoClarityKeywordEntity = seoClarityKeywordEntityDAO.getKeywordsOfGoogleCityRankingQueryV3Rel(language, engine, Integer.valueOf(kid), Integer.valueOf(location_id), isMobile);
			SeoClarityKeywordEntity seoClarityKeywordEntity = seoClarityKeywordEntityDAO.getGeoKeywordSERel(engine, language, device, Integer.valueOf(kid), Integer.valueOf(location_id));
			
			if (seoClarityKeywordEntity == null) {
				System.out.println("===not exists kw, engine:" + engine + ", language:" + language + ", isMobile:" + isMobile + ", kid:" + kid + ", location_id:" + location_id);
				return resultList;
			}
			resultList.add(seoClarityKeywordEntity);
			geoService.populateGeoInfoForRankSender(resultList);
			System.out.println(" DailyGeoKWRels SE:" + engine + "/" + language + "/" + isMobile + " cnt:" + (resultList != null ? resultList.size() : 0));
		} catch (Exception exp) {
			exp.printStackTrace();
		}

		return resultList;
	}
	
	private void processCommandV3(int engine, int language, AmazonSQS amazonSQS, String queryUrl, List<SeoClarityKeywordEntity> tempList) {
		String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
		while(ipAddress == null) {
			try {
				Thread.sleep(1000);
			} catch (Exception e) {
			}
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
		}
		SQSCommonCrawlCommandForTempSend crawlCommand = new SQSCommonCrawlCommandForTempSend(ipAddress, amazonSQS, queryUrl, new ArrayList<SeoClarityKeywordEntity>(tempList), engine, language, 
				SENDT_TO_SQS_DATE);
		crawlCommand.setStatus(true);
		try {
			threadPool.execute(crawlCommand);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}

class SQSCommonCrawlCommandForTempSend extends BaseThreadCommand {
	private AmazonSQS amazonSQS;
	private String queryUrl;
	private List<SeoClarityKeywordEntity> keywordList;
	private String ip;
	private Integer keywordEngine;
	private Integer keywordLanguage;
	private ScKeywordSearchVolumeManager scKeywordSearchVolumeManager;
	private String sendToQDate;
	
	public SQSCommonCrawlCommandForTempSend(String ip, AmazonSQS amazonSQS, String queryUrl, List<SeoClarityKeywordEntity> keywordList, Integer keywordEngine,
	        Integer keywordLanguage, Integer sendToQDate) {
		super();
		this.amazonSQS = amazonSQS;
		this.queryUrl = queryUrl;
		this.keywordList = keywordList;
		this.ip = ip;
		this.keywordEngine = keywordEngine;
		this.keywordLanguage = keywordLanguage;
		this.sendToQDate = sendToQDate.toString();
		scKeywordSearchVolumeManager = SpringBeanFactory.getBean("scKeywordSearchVolumeManager");
	}
	
	@Override
	protected void execute() throws Exception {
		long a = System.currentTimeMillis();
		System.out.println("Start command IP: " + ip + ", keywordEngine:" + keywordEngine + ", keywordLanguage:" + keywordLanguage);
		List<KeywordProperty> keywords = convertToUrl(keywordList);
		
		// get sv and cpc by solr
		scKeywordSearchVolumeManager.getSearchVolumeAndCpcValueInAdwords(keywords, keywordLanguage, 0);
		
		Map<String, String> messages = new HashMap<String, String>();
		for (KeywordProperty keywordProperty : keywords) {
			try {
				if (keywordProperty.getCityId() > 0) {
					messages.put(keywordProperty.getId().toString() + keywordProperty.getCityId(), new Gson().toJson(keywordProperty));
				} else {
					messages.put(keywordProperty.getId().toString(), new Gson().toJson(keywordProperty));
				}
				System.out.println("=send msg, kid:" + keywordProperty.getId() + ", locationId:" + keywordProperty.getCityId() 
					+ ", engine:" + (keywordProperty.getSearchEngine() + "-" + keywordProperty.getSearchLanguage())
					+ ", kw:" + keywordProperty.getKeywordText() + ", domainList:" + StringUtils.join(keywordProperty.getDomainList(), ","));
				if (messages.size() == 10) {
					SQSUtils.sendBatchMessageToQueue(amazonSQS, queryUrl, messages);
					messages = new HashMap<String, String>();
					Thread.sleep(100);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
		if (messages.size() != 0 && messages.size() <= 10) {
			SQSUtils.sendBatchMessageToQueue(amazonSQS, queryUrl, messages);
		}

		CacheModleFactory.getInstance().setAliveIpAddress(ip);
		long b = System.currentTimeMillis();
		System.out.println("End command IP: " + ip + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
	}

	private List<KeywordProperty> convertToUrl(List<SeoClarityKeywordEntity> keywordList) {
		List<KeywordProperty> keywords = new ArrayList<KeywordProperty>();
		for (SeoClarityKeywordEntity seoClarityKeywordEntity : keywordList) {
			if (StringUtils.isBlank(seoClarityKeywordEntity.getKeywordText())) {
				continue;
			}
			try {
				KeywordProperty keywordProperty = new KeywordProperty();
				keywordProperty.setKeywordText(seoClarityKeywordEntity.getKeywordText().toLowerCase());
				// https://www.wrike.com/open.htm?id=1047312047
				keywordProperty.setClarityDBKeywordHash(CityHashUtil.getUnsignedUrlHash(RankCheckUtils.decoderString(keywordProperty.getKeywordText()).toLowerCase())); 
				keywordProperty.setSearchEngine(keywordEngine);
				keywordProperty.setSearchLanguage(keywordLanguage);
				keywordProperty.setId(seoClarityKeywordEntity.getId());
				if (seoClarityKeywordEntity.getCityId() != null) {
					keywordProperty.setCityId(seoClarityKeywordEntity.getCityId());
				}
				keywordProperty.setCityName(seoClarityKeywordEntity.getCityName());
				keywordProperty.setUule(seoClarityKeywordEntity.getUule());
				if (StringUtils.isNotBlank(seoClarityKeywordEntity.getDomainList()) && 
						!seoClarityKeywordEntity.getDomainList().trim().equals(",")) {
					keywordProperty.setDomainList(StringUtils.split(seoClarityKeywordEntity.getDomainList(), ","));
				}
				keywordProperty.setSearchVol(seoClarityKeywordEntity.getSearchVolume() == null ? 0 : seoClarityKeywordEntity.getSearchVolume());
				keywordProperty.setCpc(seoClarityKeywordEntity.getCostPerClick() == null ? 0 : seoClarityKeywordEntity.getCostPerClick());
				keywordProperty.setSendToQDate(Integer.valueOf(sendToQDate));
				
				keywords.add(keywordProperty);
				
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return keywords;
	}

	@Override
	protected void undo() throws Exception {
	}
}
