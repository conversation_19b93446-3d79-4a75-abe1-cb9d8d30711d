package seoclarity.backend.entity.pagespeed;

import org.apache.commons.lang.StringUtils;

import java.util.Date;

public class PageSpeedEntity {

    public final static int MOBILE = 0;
    public final static int DESKTOP = 1;

    private Date sendDate;
    private Integer crawlMonth;
    private Integer ownDomainId;
    private Integer urlId;
    private String device;
    private String oUrl;
    private Integer oUrlHash;
    private String rUrl;
    private Integer rUrlHash;
    private Integer redfirectFlg;
    private Integer responseCode;
    private Integer score;
    private Integer numberResources;
    private Integer numberHosts;
    private Integer totalRequestBytes;
    private Integer numberStaticResources;
    private Integer htmlResponseBytes;
    private Integer textResponseBytes;
    private Integer cssResponseBytes;
    private Integer imageResponseBytes;
    private Integer javascriptResponseBytes;
    private Integer numberJsResources;
    private Integer numberCssResources;
    private String LoadingExperience;
    private String rawJson;
    private Integer version;

    private String inputFolderUlrLevel1;
    private String inputFolderUlrLevel2 = null;
    private String outputFolderUlrLevel1 = null;
    private String outputFolderUlrLevel2 = null;
    private String overallCategory;
    private String fcpCategory;
    private String dclCategory;
    private Float avoidLandingPageRedirects;
    private Float enableGzipCompression;
    private Float leverageBrowserCaching;
    private Float mainResourceServerResponseTime;
    private Float minifyCss;
    private Float minifyHtml;
    private Float minifyJavascript;
    private Float minimizeRenderBlockingResources;
    private Float optimizeImages;
    private Float prioritizeVisibleContent;

    private String[] ruleResultskey;
    private String[] ruleResultsvalue;


    public Date getSendDate() {
        return sendDate;
    }

    public void setSendDate(Date sendDate) {
        this.sendDate = sendDate;
    }

    public Integer getCrawlMonth() {
        return crawlMonth;
    }

    public void setCrawlMonth(Integer crawlMonth) {
        this.crawlMonth = crawlMonth;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getUrlId() {
        return urlId;
    }

    public void setUrlId(Integer urlId) {
        this.urlId = urlId;
    }

    public String getDevice() {
        return device;
    }
    public int getDeviceIntType() {
        return StringUtils.equalsIgnoreCase(device, "m") ? MOBILE : DESKTOP;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getoUrl() {
        return oUrl;
    }

    public void setoUrl(String oUrl) {
        this.oUrl = oUrl;
    }

    public Integer getoUrlHash() {
        return oUrlHash;
    }

    public void setoUrlHash(Integer oUrlHash) {
        this.oUrlHash = oUrlHash;
    }

    public String getrUrl() {
        return rUrl;
    }

    public void setrUrl(String rUrl) {
        this.rUrl = rUrl;
    }

    public Integer getrUrlHash() {
        return rUrlHash;
    }

    public void setrUrlHash(Integer rUrlHash) {
        this.rUrlHash = rUrlHash;
    }

    public Integer getRedfirectFlg() {
        return redfirectFlg;
    }

    public void setRedfirectFlg(Integer redfirectFlg) {
        this.redfirectFlg = redfirectFlg;
    }

    public Integer getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(Integer responseCode) {
        this.responseCode = responseCode;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getNumberResources() {
        return numberResources;
    }

    public void setNumberResources(Integer numberResources) {
        this.numberResources = numberResources;
    }

    public Integer getNumberHosts() {
        return numberHosts;
    }

    public void setNumberHosts(Integer numberHosts) {
        this.numberHosts = numberHosts;
    }

    public Integer getNumberStaticResources() {
        return numberStaticResources;
    }

    public void setNumberStaticResources(Integer numberStaticResources) {
        this.numberStaticResources = numberStaticResources;
    }

    public Integer getHtmlResponseBytes() {
        return htmlResponseBytes;
    }

    public void setHtmlResponseBytes(Integer htmlResponseBytes) {
        this.htmlResponseBytes = htmlResponseBytes;
    }

    public Integer getTextResponseBytes() {
        return textResponseBytes;
    }

    public void setTextResponseBytes(Integer textResponseBytes) {
        this.textResponseBytes = textResponseBytes;
    }

    public Integer getCssResponseBytes() {
        return cssResponseBytes;
    }

    public void setCssResponseBytes(Integer cssResponseBytes) {
        this.cssResponseBytes = cssResponseBytes;
    }

    public Integer getImageResponseBytes() {
        return imageResponseBytes;
    }

    public void setImageResponseBytes(Integer imageResponseBytes) {
        this.imageResponseBytes = imageResponseBytes;
    }

    public Integer getJavascriptResponseBytes() {
        return javascriptResponseBytes;
    }

    public void setJavascriptResponseBytes(Integer javascriptResponseBytes) {
        this.javascriptResponseBytes = javascriptResponseBytes;
    }

    public Integer getNumberJsResources() {
        return numberJsResources;
    }

    public void setNumberJsResources(Integer numberJsResources) {
        this.numberJsResources = numberJsResources;
    }

    public Integer getNumberCssResources() {
        return numberCssResources;
    }

    public void setNumberCssResources(Integer numberCssResources) {
        this.numberCssResources = numberCssResources;
    }

    public String getRawJson() {
        return rawJson;
    }

    public void setRawJson(String rawJson) {
        this.rawJson = rawJson;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getTotalRequestBytes() {
        return totalRequestBytes;
    }

    public void setTotalRequestBytes(Integer totalRequestBytes) {
        this.totalRequestBytes = totalRequestBytes;
    }

    public String getLoadingExperience() {
        return LoadingExperience;
    }

    public void setLoadingExperience(String loadingExperience) {
        LoadingExperience = loadingExperience;
    }

    public String getInputFolderUlrLevel1() {
        return inputFolderUlrLevel1;
    }

    public void setInputFolderUlrLevel1(String inputFolderUlrLevel1) {
        this.inputFolderUlrLevel1 = inputFolderUlrLevel1;
    }

    public String getInputFolderUlrLevel2() {
        return inputFolderUlrLevel2;
    }

    public void setInputFolderUlrLevel2(String inputFolderUlrLevel2) {
        this.inputFolderUlrLevel2 = inputFolderUlrLevel2;
    }

    public String getOutputFolderUlrLevel1() {
        return outputFolderUlrLevel1;
    }

    public void setOutputFolderUlrLevel1(String outputFolderUlrLevel1) {
        this.outputFolderUlrLevel1 = outputFolderUlrLevel1;
    }

    public String getOutputFolderUlrLevel2() {
        return outputFolderUlrLevel2;
    }

    public void setOutputFolderUlrLevel2(String outputFolderUlrLevel2) {
        this.outputFolderUlrLevel2 = outputFolderUlrLevel2;
    }

    public String getOverallCategory() {
        return overallCategory;
    }

    public void setOverallCategory(String overallCategory) {
        this.overallCategory = overallCategory;
    }

    public String getFcpCategory() {
        return fcpCategory;
    }

    public void setFcpCategory(String fcpCategory) {
        this.fcpCategory = fcpCategory;
    }

    public String getDclCategory() {
        return dclCategory;
    }

    public void setDclCategory(String dclCategory) {
        this.dclCategory = dclCategory;
    }

    public Float getAvoidLandingPageRedirects() {
        return avoidLandingPageRedirects;
    }

    public void setAvoidLandingPageRedirects(Float avoidLandingPageRedirects) {
        this.avoidLandingPageRedirects = avoidLandingPageRedirects;
    }

    public Float getEnableGzipCompression() {
        return enableGzipCompression;
    }

    public void setEnableGzipCompression(Float enableGzipCompression) {
        this.enableGzipCompression = enableGzipCompression;
    }

    public Float getLeverageBrowserCaching() {
        return leverageBrowserCaching;
    }

    public void setLeverageBrowserCaching(Float leverageBrowserCaching) {
        this.leverageBrowserCaching = leverageBrowserCaching;
    }

    public Float getMainResourceServerResponseTime() {
        return mainResourceServerResponseTime;
    }

    public void setMainResourceServerResponseTime(Float mainResourceServerResponseTime) {
        this.mainResourceServerResponseTime = mainResourceServerResponseTime;
    }

    public Float getMinifyCss() {
        return minifyCss;
    }

    public void setMinifyCss(Float minifyCss) {
        this.minifyCss = minifyCss;
    }

    public Float getMinifyHtml() {
        return minifyHtml;
    }

    public void setMinifyHtml(Float minifyHtml) {
        this.minifyHtml = minifyHtml;
    }

    public Float getMinifyJavascript() {
        return minifyJavascript;
    }

    public void setMinifyJavascript(Float minifyJavascript) {
        this.minifyJavascript = minifyJavascript;
    }

    public Float getMinimizeRenderBlockingResources() {
        return minimizeRenderBlockingResources;
    }

    public void setMinimizeRenderBlockingResources(Float minimizeRenderBlockingResources) {
        this.minimizeRenderBlockingResources = minimizeRenderBlockingResources;
    }

    public Float getOptimizeImages() {
        return optimizeImages;
    }

    public void setOptimizeImages(Float optimizeImages) {
        this.optimizeImages = optimizeImages;
    }

    public Float getPrioritizeVisibleContent() {
        return prioritizeVisibleContent;
    }

    public void setPrioritizeVisibleContent(Float prioritizeVisibleContent) {
        this.prioritizeVisibleContent = prioritizeVisibleContent;
    }

    public String[] getRuleResultskey() {
        return ruleResultskey;
    }

    public void setRuleResultskey(String[] ruleResultskey) {
        this.ruleResultskey = ruleResultskey;
    }

    public String[] getRuleResultsvalue() {
        return ruleResultsvalue;
    }

    public void setRuleResultsvalue(String[] ruleResultsvalue) {
        this.ruleResultsvalue = ruleResultsvalue;
    }

}
