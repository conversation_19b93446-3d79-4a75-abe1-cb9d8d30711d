package seoclarity.backend.entity.clarity360;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@NoArgsConstructor
public class UrlTagRelEntity {
    private int domainId;
    private int fileId;
    private String url;
    private String tag;
    private String parentTag;
    private boolean parentFlag = false;
    private int isTagHasChild;
}
