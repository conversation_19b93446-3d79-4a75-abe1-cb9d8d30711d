package seoclarity.backend.entity;

import lombok.Data;
import java.util.Date;

/**
 * mysql> CREATE TABLE backblaze_source_file_s3 (
 *     ->   id bigint(20) NOT NULL AUTO_INCREMENT,
 *     ->   fullPathFolder varchar(64) NOT NULL COMMENT 's3 full path folder, ends with /, example://daily-html-virginia/20200712/1-1/',
 *     ->   sourceFileName varchar(128) NOT NULL COMMENT 's3 file name',
 *     ->   sourceFileSize bigint(20) NOT NULL,
 *     ->   processStatus tinyint NOT NULL default '2' comment '2: process finished without error, 3: process finished with error',
 *     ->   createDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *     ->   PRIMARY KEY (id),
 *     ->   UNIQUE KEY uidx_fullPathFolder_sourceFileName (fullPathFolder, sourceFileName),
 */
@Data
public class BackBlazeSourceFileS3Entity {
    public static int DOWNLOAD_SUCCESS = 2;
    public static int DOWNLOAD_FAILED = 3;

    private Long id;
    //s3 full path folder, ends with /, example://daily-html-virginia/20200712/1-1/
    private String fullPathFolder;
    //s3 file name
    private String sourceFileName;
    private int processStatus;
    private Long sourceFileSize;
    private Date createDate;
}
