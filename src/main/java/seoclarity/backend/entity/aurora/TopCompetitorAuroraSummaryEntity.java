package seoclarity.backend.entity.aurora;

import java.math.BigDecimal;

/**
 * 
 * <AUTHOR>
 *
 */
public class TopCompetitorAuroraSummaryEntity {
	
	private Long id;
	private Integer ownDomainId;
	private Integer tagId;

	// 999 for mobile
	private Integer engineId;

	private Integer languageId;

	private Integer logDate;
	
	private Integer type;

	// reversed domain name
	private String competitorName;

	private Integer count_keyword_rank1;
	private Integer count_keyword_rank2;
	private Integer count_keyword_rank3;
	private Integer count_keyword_rank4;
	private Integer count_keyword_rank5;
	private Integer count_keyword_rank6;
	private Integer count_keyword_rank7;
	private Integer count_keyword_rank8;
	private Integer count_keyword_rank9;
	private Integer count_keyword_rank10;

	private Long keyword_sum_searchvol_top3;
	private Long keyword_sum_searchvol_top10;
	private Long keyword_sum_searchvol_top30;
	private Long keyword_sum_searchvol_top50;
	private Long keyword_sum_searchvol_top100;

	private Integer keyword_count_rank_top3;
	private Integer keyword_count_rank_top10;
	private Integer keyword_count_rank_top30;
	private Integer keyword_count_rank_top50;
	private Integer keyword_count_rank_top100;

	private Integer rank_in_page1;
	private Integer rank_in_page2;
	private Integer rank_in_page3;
	private Integer rank_in_page4;
	private Integer rank_in_page5;

	private Long search_volume_rank1;
	private Long search_volume_rank2;
	private Long search_volume_rank3;
	private Long search_volume_rank4;
	private Long search_volume_rank5;
	private Long search_volume_rank6;
	private Long search_volume_rank7;
	private Long search_volume_rank8;
	private Long search_volume_rank9;
	private Long search_volume_rank10;

	private Float wtd_avg_rank;
	private Float avg_rank;
	private Float wtd_avg_rank_with101;
	private Float avg_rank_with101;
	private BigDecimal sov;
	
	private Integer total_keywords;
	private Long total_searchvol;

	private Integer urlTop10Count;
	
	private Long sum_of_rank_inc101;
	private Long sum_of_rank_exc101;
	private Long wtd_searchvol_inc101;
	private Long wtd_searchvol_exc101;
	
	private Long entrancesRankTop10;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getTagId() {
		return tagId;
	}

	public void setTagId(Integer tagId) {
		this.tagId = tagId;
	}

	public Integer getEngineId() {
		return engineId;
	}

	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public Integer getLogDate() {
		return logDate;
	}

	public void setLogDate(Integer logDate) {
		this.logDate = logDate;
	}

	public String getCompetitorName() {
		return competitorName;
	}

	public void setCompetitorName(String competitorName) {
		this.competitorName = competitorName;
	}

	public Integer getCount_keyword_rank1() {
		return count_keyword_rank1;
	}

	public void setCount_keyword_rank1(Integer count_keyword_rank1) {
		this.count_keyword_rank1 = count_keyword_rank1;
	}

	public Integer getCount_keyword_rank2() {
		return count_keyword_rank2;
	}

	public void setCount_keyword_rank2(Integer count_keyword_rank2) {
		this.count_keyword_rank2 = count_keyword_rank2;
	}

	public Integer getCount_keyword_rank3() {
		return count_keyword_rank3;
	}

	public void setCount_keyword_rank3(Integer count_keyword_rank3) {
		this.count_keyword_rank3 = count_keyword_rank3;
	}

	public Integer getCount_keyword_rank4() {
		return count_keyword_rank4;
	}

	public void setCount_keyword_rank4(Integer count_keyword_rank4) {
		this.count_keyword_rank4 = count_keyword_rank4;
	}

	public Integer getCount_keyword_rank5() {
		return count_keyword_rank5;
	}

	public void setCount_keyword_rank5(Integer count_keyword_rank5) {
		this.count_keyword_rank5 = count_keyword_rank5;
	}

	public Integer getCount_keyword_rank6() {
		return count_keyword_rank6;
	}

	public void setCount_keyword_rank6(Integer count_keyword_rank6) {
		this.count_keyword_rank6 = count_keyword_rank6;
	}

	public Integer getCount_keyword_rank7() {
		return count_keyword_rank7;
	}

	public void setCount_keyword_rank7(Integer count_keyword_rank7) {
		this.count_keyword_rank7 = count_keyword_rank7;
	}

	public Integer getCount_keyword_rank8() {
		return count_keyword_rank8;
	}

	public void setCount_keyword_rank8(Integer count_keyword_rank8) {
		this.count_keyword_rank8 = count_keyword_rank8;
	}

	public Integer getCount_keyword_rank9() {
		return count_keyword_rank9;
	}

	public void setCount_keyword_rank9(Integer count_keyword_rank9) {
		this.count_keyword_rank9 = count_keyword_rank9;
	}

	public Integer getCount_keyword_rank10() {
		return count_keyword_rank10;
	}

	public void setCount_keyword_rank10(Integer count_keyword_rank10) {
		this.count_keyword_rank10 = count_keyword_rank10;
	}

	public Long getKeyword_sum_searchvol_top3() {
		return keyword_sum_searchvol_top3;
	}

	public void setKeyword_sum_searchvol_top3(Long keyword_sum_searchvol_top3) {
		this.keyword_sum_searchvol_top3 = keyword_sum_searchvol_top3;
	}

	public Long getKeyword_sum_searchvol_top10() {
		return keyword_sum_searchvol_top10;
	}

	public void setKeyword_sum_searchvol_top10(Long keyword_sum_searchvol_top10) {
		this.keyword_sum_searchvol_top10 = keyword_sum_searchvol_top10;
	}

	public Long getKeyword_sum_searchvol_top30() {
		return keyword_sum_searchvol_top30;
	}

	public void setKeyword_sum_searchvol_top30(Long keyword_sum_searchvol_top30) {
		this.keyword_sum_searchvol_top30 = keyword_sum_searchvol_top30;
	}

	public Long getKeyword_sum_searchvol_top50() {
		return keyword_sum_searchvol_top50;
	}

	public void setKeyword_sum_searchvol_top50(Long keyword_sum_searchvol_top50) {
		this.keyword_sum_searchvol_top50 = keyword_sum_searchvol_top50;
	}

	public Long getKeyword_sum_searchvol_top100() {
		return keyword_sum_searchvol_top100;
	}

	public void setKeyword_sum_searchvol_top100(Long keyword_sum_searchvol_top100) {
		this.keyword_sum_searchvol_top100 = keyword_sum_searchvol_top100;
	}

	public Integer getKeyword_count_rank_top3() {
		return keyword_count_rank_top3;
	}

	public void setKeyword_count_rank_top3(Integer keyword_count_rank_top3) {
		this.keyword_count_rank_top3 = keyword_count_rank_top3;
	}

	public Integer getKeyword_count_rank_top10() {
		return keyword_count_rank_top10;
	}

	public void setKeyword_count_rank_top10(Integer keyword_count_rank_top10) {
		this.keyword_count_rank_top10 = keyword_count_rank_top10;
	}

	public Integer getKeyword_count_rank_top30() {
		return keyword_count_rank_top30;
	}

	public void setKeyword_count_rank_top30(Integer keyword_count_rank_top30) {
		this.keyword_count_rank_top30 = keyword_count_rank_top30;
	}

	public Integer getKeyword_count_rank_top50() {
		return keyword_count_rank_top50;
	}

	public void setKeyword_count_rank_top50(Integer keyword_count_rank_top50) {
		this.keyword_count_rank_top50 = keyword_count_rank_top50;
	}

	public Integer getKeyword_count_rank_top100() {
		return keyword_count_rank_top100;
	}

	public void setKeyword_count_rank_top100(Integer keyword_count_rank_top100) {
		this.keyword_count_rank_top100 = keyword_count_rank_top100;
	}

	public Integer getRank_in_page1() {
		return rank_in_page1;
	}

	public void setRank_in_page1(Integer rank_in_page1) {
		this.rank_in_page1 = rank_in_page1;
	}

	public Integer getRank_in_page2() {
		return rank_in_page2;
	}

	public void setRank_in_page2(Integer rank_in_page2) {
		this.rank_in_page2 = rank_in_page2;
	}

	public Integer getRank_in_page3() {
		return rank_in_page3;
	}

	public void setRank_in_page3(Integer rank_in_page3) {
		this.rank_in_page3 = rank_in_page3;
	}

	public Integer getRank_in_page4() {
		return rank_in_page4;
	}

	public void setRank_in_page4(Integer rank_in_page4) {
		this.rank_in_page4 = rank_in_page4;
	}

	public Integer getRank_in_page5() {
		return rank_in_page5;
	}

	public void setRank_in_page5(Integer rank_in_page5) {
		this.rank_in_page5 = rank_in_page5;
	}

	public Float getWtd_avg_rank() {
		return wtd_avg_rank;
	}

	public void setWtd_avg_rank(Float wtd_avg_rank) {
		this.wtd_avg_rank = wtd_avg_rank;
	}

	public Float getAvg_rank() {
		return avg_rank;
	}

	public void setAvg_rank(Float avg_rank) {
		this.avg_rank = avg_rank;
	}

	public Float getWtd_avg_rank_with101() {
		return wtd_avg_rank_with101;
	}

	public void setWtd_avg_rank_with101(Float wtd_avg_rank_with101) {
		this.wtd_avg_rank_with101 = wtd_avg_rank_with101;
	}

	public Float getAvg_rank_with101() {
		return avg_rank_with101;
	}

	public void setAvg_rank_with101(Float avg_rank_with101) {
		this.avg_rank_with101 = avg_rank_with101;
	}

	public BigDecimal getSov() {
		return sov;
	}

	public void setSov(BigDecimal sov) {
		this.sov = sov;
	}

	public Long getSearch_volume_rank9() {
		return search_volume_rank9;
	}

	public void setSearch_volume_rank9(Long search_volume_rank9) {
		this.search_volume_rank9 = search_volume_rank9;
	}

	public Long getSearch_volume_rank10() {
		return search_volume_rank10;
	}

	public void setSearch_volume_rank10(Long search_volume_rank10) {
		this.search_volume_rank10 = search_volume_rank10;
	}

	public Long getSearch_volume_rank1() {
		return search_volume_rank1;
	}

	public void setSearch_volume_rank1(Long search_volume_rank1) {
		this.search_volume_rank1 = search_volume_rank1;
	}

	public Long getSearch_volume_rank2() {
		return search_volume_rank2;
	}

	public void setSearch_volume_rank2(Long search_volume_rank2) {
		this.search_volume_rank2 = search_volume_rank2;
	}

	public Long getSearch_volume_rank3() {
		return search_volume_rank3;
	}

	public void setSearch_volume_rank3(Long search_volume_rank3) {
		this.search_volume_rank3 = search_volume_rank3;
	}

	public Long getSearch_volume_rank4() {
		return search_volume_rank4;
	}

	public void setSearch_volume_rank4(Long search_volume_rank4) {
		this.search_volume_rank4 = search_volume_rank4;
	}

	public Long getSearch_volume_rank5() {
		return search_volume_rank5;
	}

	public void setSearch_volume_rank5(Long search_volume_rank5) {
		this.search_volume_rank5 = search_volume_rank5;
	}

	public Long getSearch_volume_rank6() {
		return search_volume_rank6;
	}

	public void setSearch_volume_rank6(Long search_volume_rank6) {
		this.search_volume_rank6 = search_volume_rank6;
	}

	public Long getSearch_volume_rank7() {
		return search_volume_rank7;
	}

	public void setSearch_volume_rank7(Long search_volume_rank7) {
		this.search_volume_rank7 = search_volume_rank7;
	}

	public Long getSearch_volume_rank8() {
		return search_volume_rank8;
	}

	public void setSearch_volume_rank8(Long search_volume_rank8) {
		this.search_volume_rank8 = search_volume_rank8;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getUrlTop10Count() {
		return urlTop10Count;
	}

	public void setUrlTop10Count(Integer urlTop10Count) {
		this.urlTop10Count = urlTop10Count;
	}

	public Integer getTotal_keywords() {
		return total_keywords;
	}

	public void setTotal_keywords(Integer total_keywords) {
		this.total_keywords = total_keywords;
	}

	public Long getTotal_searchvol() {
		return total_searchvol;
	}

	public void setTotal_searchvol(Long total_searchvol) {
		this.total_searchvol = total_searchvol;
	}

	public Long getSum_of_rank_inc101() {
		return sum_of_rank_inc101;
	}

	public void setSum_of_rank_inc101(Long sum_of_rank_inc101) {
		this.sum_of_rank_inc101 = sum_of_rank_inc101;
	}

	public Long getSum_of_rank_exc101() {
		return sum_of_rank_exc101;
	}

	public void setSum_of_rank_exc101(Long sum_of_rank_exc101) {
		this.sum_of_rank_exc101 = sum_of_rank_exc101;
	}

	public Long getWtd_searchvol_inc101() {
		return wtd_searchvol_inc101;
	}

	public void setWtd_searchvol_inc101(Long wtd_searchvol_inc101) {
		this.wtd_searchvol_inc101 = wtd_searchvol_inc101;
	}

	public Long getWtd_searchvol_exc101() {
		return wtd_searchvol_exc101;
	}

	public void setWtd_searchvol_exc101(Long wtd_searchvol_exc101) {
		this.wtd_searchvol_exc101 = wtd_searchvol_exc101;
	}
	
	public Long getEntrancesRankTop10() {
		return entrancesRankTop10;
	}

	public void setEntrancesRankTop10(Long entrancesRankTop10) {
		this.entrancesRankTop10 = entrancesRankTop10;
	}

	
}
