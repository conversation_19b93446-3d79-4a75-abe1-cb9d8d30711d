/**
 * 
 */
package seoclarity.backend.entity;

public class SitemapEntity {
	
	private Long id;
	
	private String sitemap;
	
	private String murmur3Hash;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getSitemap() {
		return sitemap;
	}

	public void setSitemap(String sitemap) {
		this.sitemap = sitemap;
	}

	public String getMurmur3Hash() {
		return murmur3Hash;
	}

	public void setMurmur3Hash(String murmur3Hash) {
		this.murmur3Hash = murmur3Hash;
	}
	
	
}
