package seoclarity.backend.entity;

public class NodeStatusVO {

//	{"totalNodes":13,"completedNodes":13,"completed":true}

	private Integer totalNodes;
	
	private Integer completedNodes;
	
	private Boolean completed;

	public Integer getTotalNodes() {
		return totalNodes;
	}

	public void setTotalNodes(Integer totalNodes) {
		this.totalNodes = totalNodes;
	}

	public Integer getCompletedNodes() {
		return completedNodes;
	}

	public void setCompletedNodes(Integer completedNodes) {
		this.completedNodes = completedNodes;
	}

	public Boolean getCompleted() {
		return completed;
	}

	public void setCompleted(<PERSON>ole<PERSON> completed) {
		this.completed = completed;
	}
	
}
