package seoclarity.backend.entity;

import java.sql.Timestamp;

/**
 * hourly_adhoc_rank_keyword
 * 
 * <AUTHOR>
 * @version 1.0.0 2020-08-26
 */
public class AdhocRankKeyword implements java.io.Serializable {
    
    private static final long serialVersionUID = 2588666805726575184L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    /** id */
    private Integer id;

    /** hourly_adhoc_rank_project.id */
    private Integer projectId;

    /** searchEngineId */
    private Integer searchEngineId;

    /** languageId */
    private Integer languageId;

    /** keywordName */
    private String keywordName;

    /** claritydbkeywordhash */
    private String claritydbkeywordhash;

    /** rankcheckKeywordId */
    private Integer rankcheckKeywordId;

    /** createdAt */
    private Timestamp createdAt;

    /* This code was generated by TableGo tools, mark 1 end. */

    /* This code was generated by TableGo tools, mark 2 begin. */

    /**
     * id
     * 
     * @return id
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * id
     * 
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * hourly_adhoc_rank_project.id
     * 
     * @return hourly_adhoc_rank_project
     */
    public Integer getProjectId() {
        return this.projectId;
    }

    /**
     * hourly_adhoc_rank_project.id
     * 
     * @param projectId
     *          hourly_adhoc_rank_project
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * searchEngineId
     * 
     * @return searchEngineId
     */
    public Integer getSearchEngineId() {
        return this.searchEngineId;
    }

    /**
     * searchEngineId
     * 
     * @param searchEngineId
     */
    public void setSearchEngineId(Integer searchEngineId) {
        this.searchEngineId = searchEngineId;
    }

    /**
     * languageId
     * 
     * @return languageId
     */
    public Integer getLanguageId() {
        return this.languageId;
    }

    /**
     * languageId
     * 
     * @param languageId
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * keywordName
     * 
     * @return keywordName
     */
    public String getKeywordName() {
        return this.keywordName;
    }

    /**
     * keywordName
     * 
     * @param keywordName
     */
    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    /**
     * claritydbkeywordhash
     * 
     * @return claritydbkeywordhash
     */
    public String getClaritydbkeywordhash() {
        return this.claritydbkeywordhash;
    }

    /**
     * claritydbkeywordhash
     * 
     * @param claritydbkeywordhash
     */
    public void setClaritydbkeywordhash(String claritydbkeywordhash) {
        this.claritydbkeywordhash = claritydbkeywordhash;
    }

    /**
     * rankcheckKeywordId
     * 
     * @return rankcheckKeywordId
     */
    public Integer getRankcheckKeywordId() {
        return this.rankcheckKeywordId;
    }

    /**
     * rankcheckKeywordId
     * 
     * @param rankcheckKeywordId
     */
    public void setRankcheckKeywordId(Integer rankcheckKeywordId) {
        this.rankcheckKeywordId = rankcheckKeywordId;
    }

    /**
     * createdAt
     * 
     * @return createdAt
     */
    public Timestamp getCreatedAt() {
        return this.createdAt;
    }

    /**
     * createdAt
     * 
     * @param createdAt
     */
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    /* This code was generated by TableGo tools, mark 2 end. */
}