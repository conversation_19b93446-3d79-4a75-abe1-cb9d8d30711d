package seoclarity.backend.entity;

import java.util.Date;

public class TUsers {
	private Integer userId;
	private Integer ownDomainId;
	private String accessToken;
	private Integer tokensAllowed;
	private Integer totalUsed;
	private Integer id;
	private String apiType;
	private Integer enablePaidApi;
	private Date createDate;

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public Integer getTokensAllowed() {
		return tokensAllowed;
	}

	public void setTokensAllowed(Integer tokensAllowed) {
		this.tokensAllowed = tokensAllowed;
	}

	public Integer getTotalUsed() {
		return totalUsed;
	}

	public void setTotalUsed(Integer totalUsed) {
		this.totalUsed = totalUsed;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getApiType() {
		return apiType;
	}

	public void setApiType(String apiType) {
		this.apiType = apiType;
	}

	public Integer getEnablePaidApi() {
		return enablePaidApi;
	}

	public void setEnablePaidApi(Integer enablePaidApi) {
		this.enablePaidApi = enablePaidApi;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
}
