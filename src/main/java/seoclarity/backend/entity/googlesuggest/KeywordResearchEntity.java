package seoclarity.backend.entity.googlesuggest;

import lombok.*;
import lombok.extern.apachecommons.CommonsLog;

import java.util.Date;
import java.util.Objects;

@CommonsLog
@Getter
@Setter
@NoArgsConstructor
@ToString
public class KeywordResearchEntity {
    private int ownDomainId;
    private int projectId;
    private int engineId;
    private int languageId;
    private int locationId;
    private String keywordName;//suggest ky, decode
    private String keywordHash;
    private String sourceKeyword;
    private String sourceKeywordHash;
    private int dataSource;
    private Date crawlDate;
    private int sequenceNo;
    private Date requestDate;
    private String[] word;
    private String[] stream;
    private String[] keywordVariationOneword;
    private String[] keywordVariationNgram;
    private String[] attrStrKey;
    private String[] attrStrValue;
    private String[] attrIntKey;
    private Integer[] attrIntValue;
    private int sign;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof KeywordResearchEntity)) return false;
        KeywordResearchEntity that = (KeywordResearchEntity) o;
        return ownDomainId == that.ownDomainId &&
                projectId == that.projectId &&
                engineId == that.engineId &&
                languageId == that.languageId &&
                locationId == that.locationId &&
                dataSource == that.dataSource &&
                Objects.equals(keywordName, that.keywordName) &&
                Objects.equals(sourceKeyword, that.sourceKeyword);
    }

    @Override
    public int hashCode() {
        int n = 31;
        n = n * 31 + this.ownDomainId;
        n = n * 31 + this.projectId;
        n = n * 31 + this.engineId;
        n = n * 31 + this.languageId;
        n = n * 31 + this.locationId;
        n = n * 31 + this.keywordName.hashCode();
        n = n * 31 + this.sourceKeyword.hashCode();
        n = n * 31 + this.dataSource;
        return n;
    }

}
