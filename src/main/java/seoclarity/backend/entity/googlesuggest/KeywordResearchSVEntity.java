package seoclarity.backend.entity.googlesuggest;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.apachecommons.CommonsLog;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@CommonsLog
@Getter
@Setter
@NoArgsConstructor
@ToString
public class KeywordResearchSVEntity {

    public final static int SV_SOURCE_API = 0;
    public final static int SV_SOURCE_ADWORDS = 1;

    public final static int DEFAULT_SIGN = 1;
    public final static int DEFAULT_VERSIONING = 1;

    private int ownDomainId;
    private int projectId;
    private int engineId;
    private int languageId;
    private int locationId;
    private String keywordName;
    private String keywordHash;
    private String sourceKeyword;
    private String sourceKeywordHash;
    private Integer avgSearchVolume;
    private Integer monthlySearchVolume1;
    private Integer monthlySearchVolume2;
    private Integer monthlySearchVolume3;
    private Integer monthlySearchVolume4;
    private Integer monthlySearchVolume5;
    private Integer monthlySearchVolume6;
    private Integer monthlySearchVolume7;
    private Integer monthlySearchVolume8;
    private Integer monthlySearchVolume9;
    private Integer monthlySearchVolume10;
    private Integer monthlySearchVolume11;
    private Integer monthlySearchVolume12;
    private Double cpc;
    private Integer[] category;
    private String[] word;
    private String[] stream;
    private String key;
    private String[] keywordVariationOneword;
    private String[] keywordVariationNgram;
    private Date createDate;
    private int sequenceNo;
    private int versioning;
    private int svFromRankcheck;
    private int sign;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof KeywordResearchSVEntity)) return false;
        KeywordResearchSVEntity that = (KeywordResearchSVEntity) o;
        return ownDomainId == that.ownDomainId &&
                projectId == that.projectId &&
                engineId == that.engineId &&
                languageId == that.languageId &&
                locationId == that.locationId &&
                Objects.equals(keywordName, that.keywordName) &&
                Objects.equals(sourceKeyword, that.sourceKeyword);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ownDomainId, projectId, engineId, languageId, locationId, keywordName, sourceKeyword);
    }
}
