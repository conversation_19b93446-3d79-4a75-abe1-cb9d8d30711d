package seoclarity.backend.entity;

public class GeoAttributeEntity {
	
	public static String DATA_TYPE_GAIA = "GAIA";
	public static String DATA_TYPE_HOTEL = "HOTEL";

	private Integer id;
	private String dataType;
	private Long geoId;
	private String geoName;
	private String neighborhoodName;
	private String cityName;
	private String multiCityVicinityName;
	private String provinceStateName;
	private String countryName;
	private String continentName;
	private String superRegionName;
	private String geoTypeName;
	private String hotelType;
	private String brand;
	private String parentChain;
	private Float starRating;
	private String vrFlag;
	private String singleMultiUnitIndicator;
	
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getDataType() {
		return dataType;
	}
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	public Long getGeoId() {
		return geoId;
	}
	public void setGeoId(Long geoId) {
		this.geoId = geoId;
	}
	public String getGeoName() {
		return geoName;
	}
	public void setGeoName(String geoName) {
		this.geoName = geoName;
	}
	public String getNeighborhoodName() {
		return neighborhoodName;
	}
	public void setNeighborhoodName(String neighborhoodName) {
		this.neighborhoodName = neighborhoodName;
	}
	public String getCityName() {
		return cityName;
	}
	public void setCityName(String cityName) {
		this.cityName = cityName;
	}
	public String getMultiCityVicinityName() {
		return multiCityVicinityName;
	}
	public void setMultiCityVicinityName(String multiCityVicinityName) {
		this.multiCityVicinityName = multiCityVicinityName;
	}
	public String getProvinceStateName() {
		return provinceStateName;
	}
	public void setProvinceStateName(String provinceStateName) {
		this.provinceStateName = provinceStateName;
	}
	public String getCountryName() {
		return countryName;
	}
	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}
	public String getContinentName() {
		return continentName;
	}
	public void setContinentName(String continentName) {
		this.continentName = continentName;
	}
	public String getSuperRegionName() {
		return superRegionName;
	}
	public void setSuperRegionName(String superRegionName) {
		this.superRegionName = superRegionName;
	}
	public String getGeoTypeName() {
		return geoTypeName;
	}
	public void setGeoTypeName(String geoTypeName) {
		this.geoTypeName = geoTypeName;
	}
	public String getHotelType() {
		return hotelType;
	}
	public void setHotelType(String hotelType) {
		this.hotelType = hotelType;
	}
	public String getBrand() {
		return brand;
	}
	public void setBrand(String brand) {
		this.brand = brand;
	}
	public String getParentChain() {
		return parentChain;
	}
	public void setParentChain(String parentChain) {
		this.parentChain = parentChain;
	}
	public Float getStarRating() {
		return starRating;
	}
	public void setStarRating(Float starRating) {
		this.starRating = starRating;
	}
	public String getVrFlag() {
		return vrFlag;
	}
	public void setVrFlag(String vrFlag) {
		this.vrFlag = vrFlag;
	}
	public String getSingleMultiUnitIndicator() {
		return singleMultiUnitIndicator;
	}
	public void setSingleMultiUnitIndicator(String singleMultiUnitIndicator) {
		this.singleMultiUnitIndicator = singleMultiUnitIndicator;
	}
	
	

}
