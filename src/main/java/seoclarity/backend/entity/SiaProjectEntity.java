/**
 * 
 */
package seoclarity.backend.entity;

import java.util.Date;

public class SiaProjectEntity {
	
	//10: newly created, 11: collecting URLs to sia_url, 12: collect completed, 13: collect error, 21: crawling/uploading, 22: process(collect, send, upload) completed',
	public static final Integer STATUS_NEW = 10;
	public static final Integer STATUS_COLLECT_DETAIL_PENDING = 11;
	public static final Integer STATUS_COLLECT_DETAIL_COMLETED = 12;
	public static final Integer STATUS_COLLECT_DETAIL_ERROR = 13;
	public static final Integer STATUS_CRAWLING = 21;
	public static final Integer STATUS_COMPLETED = 22;
	
	
	//1: cdb_list.id, 2: Page tag, 3: URL list pasted on UI, 4: URL list from file, 5: crawl_request_log.id
	public static final Integer SOURCE_TYPE_CDB_LIST = 1;
	public static final Integer SOURCE_TYPE_PAGE_TAG = 2;
	public static final Integer SOURCE_TYPE_URL_FROM_UI = 3;
	public static final Integer SOURCE_TYPE_URL_FROM_FILE = 4;
	public static final Integer SOURCE_TYPE_CRAWL_ID = 5;
	
	public static final Integer DATA_TYPE_URL = 1;
	
	private Integer id;
	
	private Integer ownDomainId;
	
	private Integer dataType;
	
	private Integer sourceType;
	
	private String source;
	
	private Integer status;
	
	private Integer userId;
	
	private Date updateTime;

	private Date createDate;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public Integer getSourceType() {
		return sourceType;
	}

	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
}
