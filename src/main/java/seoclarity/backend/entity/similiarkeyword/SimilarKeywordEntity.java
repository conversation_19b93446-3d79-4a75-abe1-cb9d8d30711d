package seoclarity.backend.entity.similiarkeyword;

import java.util.Date;
import java.util.List;

public class SimilarKeywordEntity {
	
	private long keywordRankcheckId;
	
	private int engineId;    
	
	private int languageId;
	
	private long locationId;
	
	private List<String> attrstrKey;
	
	private List<String> attrstrValue;
	
	private List<String> attrintKey;
	
	private List<Integer> attrintValue;   
	
	private Date rankingDate;

	public int getEngineId() {
		return engineId;
	}

	public void setEngineId(int engineId) {
		this.engineId = engineId;
	}

	public int getLanguageId() {
		return languageId;
	}

	public void setLanguageId(int languageId) {
		this.languageId = languageId;
	}

	public long getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(long keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public long getLocationId() {
		return locationId;
	}

	public void setLocationId(long locationId) {
		this.locationId = locationId;
	}

	public List<String> getAttrstrKey() {
		return attrstrKey;
	}

	public void setAttrstrKey(List<String> attrstrKey) {
		this.attrstrKey = attrstrKey;
	}

	public List<String> getAttrstrValue() {
		return attrstrValue;
	}

	public void setAttrstrValue(List<String> attrstrValue) {
		this.attrstrValue = attrstrValue;
	}

	public List<String> getAttrintKey() {
		return attrintKey;
	}

	public void setAttrintKey(List<String> attrintKey) {
		this.attrintKey = attrintKey;
	}

	public List<Integer> getAttrintValue() {
		return attrintValue;
	}

	public void setAttrintValue(List<Integer> attrintValue) {
		this.attrintValue = attrintValue;
	}

	public Date getRankingDate() {
		return rankingDate;
	}

	public void setRankingDate(Date rankingDate) {
		this.rankingDate = rankingDate;
	}

	
}
