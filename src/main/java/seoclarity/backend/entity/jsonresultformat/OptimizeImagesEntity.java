package seoclarity.backend.entity.jsonresultformat;

public class OptimizeImagesEntity {

    private String localizedRuleName;

    private Float ruleImpact;

    public static String getRuleResultName(){
        return "OptimizeImages";
    }

    public String getLocalizedRuleName() {
        return localizedRuleName;
    }

    public void setLocalizedRuleName(String localizedRuleName) {
        this.localizedRuleName = localizedRuleName;
    }

    public Float getRuleImpact() {
        return ruleImpact;
    }

    public void setRuleImpact(Float ruleImpact) {
        this.ruleImpact = ruleImpact;
    }

}
