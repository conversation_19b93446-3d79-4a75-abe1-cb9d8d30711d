package seoclarity.backend.entity.jsonresultformat;

public class GoogleErrorsEntity {

    private Object[] errors;
    private Integer code;
    private String message;

    public Object[] getErrors() {
        return errors;
    }

    public void setErrors(Object[] errors) {
        this.errors = errors;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}
