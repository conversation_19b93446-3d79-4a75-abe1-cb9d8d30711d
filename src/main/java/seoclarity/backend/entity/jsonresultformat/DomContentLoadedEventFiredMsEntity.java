package seoclarity.backend.entity.jsonresultformat;

public class DomContentLoadedEventFiredMsEntity {

    private Integer median;

    private String category;

    private DistributionsEntity[] distributions;

    public Integer getMedian() {
        return median;
    }

    public void setMedian(Integer median) {
        this.median = median;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public DistributionsEntity[] getDistributions() {
        return distributions;
    }

    public void setDistributions(DistributionsEntity[] distributions) {
        this.distributions = distributions;
    }
}
