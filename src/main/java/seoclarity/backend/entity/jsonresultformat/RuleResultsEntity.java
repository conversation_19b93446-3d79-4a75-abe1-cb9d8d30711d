package seoclarity.backend.entity.jsonresultformat;

public class RuleResultsEntity {

    private AvoidLandingPageRedirectsEntity AvoidLandingPageRedirects;

    private EnableGzipCompressionEntity EnableGzipCompression;

    private LeverageBrowserCachingEntity LeverageBrowserCaching;

    private MainResourceServerResponseTimeEntity MainResourceServerResponseTime;

    private MinifyCssEntity MinifyCss;

    private MinifyHTMLEntity MinifyHTML;

    private MinifyJavaScriptEntity MinifyJavaScript;

    private MinimizeRenderBlockingResourcesEntity MinimizeRenderBlockingResources;

    private OptimizeImagesEntity OptimizeImages;

    private PrioritizeVisibleContentEntity PrioritizeVisibleContent;

    public AvoidLandingPageRedirectsEntity getAvoidLandingPageRedirects() {
        return AvoidLandingPageRedirects;
    }

    public void setAvoidLandingPageRedirects(AvoidLandingPageRedirectsEntity avoidLandingPageRedirects) {
        AvoidLandingPageRedirects = avoidLandingPageRedirects;
    }

    public EnableGzipCompressionEntity getEnableGzipCompression() {
        return EnableGzipCompression;
    }

    public void setEnableGzipCompression(EnableGzipCompressionEntity enableGzipCompression) {
        EnableGzipCompression = enableGzipCompression;
    }

    public LeverageBrowserCachingEntity getLeverageBrowserCaching() {
        return LeverageBrowserCaching;
    }

    public void setLeverageBrowserCaching(LeverageBrowserCachingEntity leverageBrowserCaching) {
        LeverageBrowserCaching = leverageBrowserCaching;
    }

    public MainResourceServerResponseTimeEntity getMainResourceServerResponseTime() {
        return MainResourceServerResponseTime;
    }

    public void setMainResourceServerResponseTime(MainResourceServerResponseTimeEntity mainResourceServerResponseTime) {
        MainResourceServerResponseTime = mainResourceServerResponseTime;
    }

    public MinifyCssEntity getMinifyCss() {
        return MinifyCss;
    }

    public void setMinifyCss(MinifyCssEntity minifyCss) {
        MinifyCss = minifyCss;
    }

    public MinifyHTMLEntity getMinifyHTML() {
        return MinifyHTML;
    }

    public void setMinifyHTML(MinifyHTMLEntity minifyHTML) {
        MinifyHTML = minifyHTML;
    }

    public MinifyJavaScriptEntity getMinifyJavaScript() {
        return MinifyJavaScript;
    }

    public void setMinifyJavaScript(MinifyJavaScriptEntity minifyJavaScript) {
        MinifyJavaScript = minifyJavaScript;
    }

    public MinimizeRenderBlockingResourcesEntity getMinimizeRenderBlockingResources() {
        return MinimizeRenderBlockingResources;
    }

    public void setMinimizeRenderBlockingResources(MinimizeRenderBlockingResourcesEntity minimizeRenderBlockingResources) {
        MinimizeRenderBlockingResources = minimizeRenderBlockingResources;
    }

    public OptimizeImagesEntity getOptimizeImages() {
        return OptimizeImages;
    }

    public void setOptimizeImages(OptimizeImagesEntity optimizeImages) {
        OptimizeImages = optimizeImages;
    }

    public PrioritizeVisibleContentEntity getPrioritizeVisibleContent() {
        return PrioritizeVisibleContent;
    }

    public void setPrioritizeVisibleContent(PrioritizeVisibleContentEntity prioritizeVisibleContent) {
        PrioritizeVisibleContent = prioritizeVisibleContent;
    }
}
