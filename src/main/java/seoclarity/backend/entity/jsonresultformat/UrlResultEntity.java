package seoclarity.backend.entity.jsonresultformat;


public class UrlResultEntity {

    private String id;

    private Integer responseCode;

    private RuleGroupsEntity ruleGroups;

    private PageStatsEntity pageStats;

    private LoadingExperienceEntity loadingExperience;

    private FormattedResultsEntity formattedResults;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(Integer responseCode) {
        this.responseCode = responseCode;
    }

    public RuleGroupsEntity getRuleGroups() {
        return ruleGroups;
    }

    public void setRuleGroups(RuleGroupsEntity ruleGroups) {
        this.ruleGroups = ruleGroups;
    }

    public PageStatsEntity getPageStats() {
        return pageStats;
    }

    public void setPageStats(PageStatsEntity pageStats) {
        this.pageStats = pageStats;
    }

    public LoadingExperienceEntity getLoadingExperience() {
        return loadingExperience;
    }

    public void setLoadingExperience(LoadingExperienceEntity loadingExperience) {
        this.loadingExperience = loadingExperience;
    }

    public FormattedResultsEntity getFormattedResults() {
        return formattedResults;
    }

    public void setFormattedResults(FormattedResultsEntity formattedResults) {
        this.formattedResults = formattedResults;
    }
}
