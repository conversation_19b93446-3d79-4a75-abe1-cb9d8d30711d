package seoclarity.backend.entity.jsonresultformat;

public class PageStatsEntity {

    private Integer numberResources;

    private Integer numberHosts;

    private String totalRequestBytes;

    private Integer numberStaticResources;

    private String htmlResponseBytes;

    private String textResponseBytes;

    private String cssResponseBytes;

    private String imageResponseBytes;

    private String javascriptResponseBytes;

    private Integer numberJsResources;

    private Integer numberCssResources;

    public Integer getNumberResources() {
        return numberResources;
    }

    public void setNumberResources(Integer numberResources) {
        this.numberResources = numberResources;
    }

    public Integer getNumberHosts() {
        return numberHosts;
    }

    public void setNumberHosts(Integer numberHosts) {
        this.numberHosts = numberHosts;
    }

    public String getTotalRequestBytes() {
        return totalRequestBytes;
    }

    public void setTotalRequestBytes(String totalRequestBytes) {
        this.totalRequestBytes = totalRequestBytes;
    }

    public Integer getNumberStaticResources() {
        return numberStaticResources;
    }

    public void setNumberStaticResources(Integer numberStaticResources) {
        this.numberStaticResources = numberStaticResources;
    }

    public String getHtmlResponseBytes() {
        return htmlResponseBytes;
    }

    public void setHtmlResponseBytes(String htmlResponseBytes) {
        this.htmlResponseBytes = htmlResponseBytes;
    }

    public String getTextResponseBytes() {
        return textResponseBytes;
    }

    public void setTextResponseBytes(String textResponseBytes) {
        this.textResponseBytes = textResponseBytes;
    }

    public String getCssResponseBytes() {
        return cssResponseBytes;
    }

    public void setCssResponseBytes(String cssResponseBytes) {
        this.cssResponseBytes = cssResponseBytes;
    }

    public String getImageResponseBytes() {
        return imageResponseBytes;
    }

    public void setImageResponseBytes(String imageResponseBytes) {
        this.imageResponseBytes = imageResponseBytes;
    }

    public String getJavascriptResponseBytes() {
        return javascriptResponseBytes;
    }

    public void setJavascriptResponseBytes(String javascriptResponseBytes) {
        this.javascriptResponseBytes = javascriptResponseBytes;
    }

    public Integer getNumberJsResources() {
        return numberJsResources;
    }

    public void setNumberJsResources(Integer numberJsResources) {
        this.numberJsResources = numberJsResources;
    }

    public Integer getNumberCssResources() {
        return numberCssResources;
    }

    public void setNumberCssResources(Integer numberCssResources) {
        this.numberCssResources = numberCssResources;
    }

}
