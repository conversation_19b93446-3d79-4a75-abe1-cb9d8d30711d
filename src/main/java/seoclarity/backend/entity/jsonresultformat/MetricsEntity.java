package seoclarity.backend.entity.jsonresultformat;

import com.alibaba.fastjson.annotation.J<PERSON><PERSON><PERSON>;

public class MetricsEntity {

    private FirstContentfulPaintMsEntity FIRST_CONTENTFUL_PAINT_MS;

    private DomContentLoadedEventFiredMsEntity DOM_CONTENT_LOADED_EVENT_FIRED_MS;

    public FirstContentfulPaintMsEntity getFIRST_CONTENTFUL_PAINT_MS() {
        return FIRST_CONTENTFUL_PAINT_MS;
    }

    public void setFIRST_CONTENTFUL_PAINT_MS(FirstContentfulPaintMsEntity FIRST_CONTENTFUL_PAINT_MS) {
        this.FIRST_CONTENTFUL_PAINT_MS = FIRST_CONTENTFUL_PAINT_MS;
    }

    public DomContentLoadedEventFiredMsEntity getDOM_CONTENT_LOADED_EVENT_FIRED_MS() {
        return DOM_CONTENT_LOADED_EVENT_FIRED_MS;
    }

    public void setDOM_CONTENT_LOADED_EVENT_FIRED_MS(DomContentLoadedEventFiredMsEntity DOM_CONTENT_LOADED_EVENT_FIRED_MS) {
        this.DOM_CONTENT_LOADED_EVENT_FIRED_MS = DOM_CONTENT_LOADED_EVENT_FIRED_MS;
    }

}
