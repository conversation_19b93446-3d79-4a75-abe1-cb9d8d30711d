package seoclarity.backend.entity;

public class ContentIdeaUrlListEntity {
	
	/**
	  (hash_id String, 
	  found_date Date DEFAULT today(), 
	  domain_reverse String, 
	  root_domain_reverse String, 
	  url String, urlhash UInt64 DEFAULT URLHash(url), 
	  folder_level1 String, 
	  folder_level1_hash UInt64 DEFAULT URLHash(folder_level1), 
	  folder_level2 String, 
	  folder_level2_hash UInt64 DEFAULT URLHash(folder_level2), 
	  uri String
	 */

	private String hashId;
	
	private String url;
	
	private String foundDate;
	
	private String domainReverse;
	
	private String rootDomainReverse;
	
	private String uri;
	
	private String folder_level1;

	private String folder_level2;
	private String title;
	private Integer engine_id;
	private Integer language_id;

	public String getHashId() {
		return hashId;
	}

	public void setHashId(String hashId) {
		this.hashId = hashId;
	}

	public String getFoundDate() {
		return foundDate;
	}

	public void setFoundDate(String foundDate) {
		this.foundDate = foundDate;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getDomainReverse() {
		return domainReverse;
	}

	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}

	public String getRootDomainReverse() {
		return rootDomainReverse;
	}

	public void setRootDomainReverse(String rootDomainReverse) {
		this.rootDomainReverse = rootDomainReverse;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public String getFolder_level1() {
		return folder_level1;
	}

	public void setFolder_level1(String folder_level1) {
		this.folder_level1 = folder_level1;
	}

	public String getFolder_level2() {
		return folder_level2;
	}

	public void setFolder_level2(String folder_level2) {
		this.folder_level2 = folder_level2;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getEngine_id() {
		return engine_id;
	}

	public void setEngine_id(Integer engine_id) {
		this.engine_id = engine_id;
	}

	public Integer getLanguage_id() {
		return language_id;
	}

	public void setLanguage_id(Integer language_id) {
		this.language_id = language_id;
	}

	
}
