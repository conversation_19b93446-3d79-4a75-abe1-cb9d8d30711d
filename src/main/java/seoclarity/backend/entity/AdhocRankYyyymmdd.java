package seoclarity.backend.entity;

import java.sql.Timestamp;

/**
 * hourly_adhoc_rank_yyyymmdd
 * 
 * <AUTHOR>
 * @version 1.0.0 2020-08-26
 */
public class AdhocRankYyyymmdd implements java.io.Serializable {
    
    private static final long serialVersionUID = 806747263916123204L;

    /* This code was generated by TableGo tools, mark 1 begin. */

    /** id */
    private Integer id;

    /** hourly_adhoc_rank_project.id */
    private Integer projectId;

    /** hourly_adhoc_rank_keyword.id */
    private Integer keywordId;

    /** keywordName */
    private String keywordName;

    /** Hour: 0 - 24 */
    private Boolean startHour;

    /** hourly_adhoc_rank_task.id */
    private Integer taskId;

    /** 0: not started, 1: processing 2: completed */
    private Integer status;

    /** startProcessTime */
    private Timestamp startProcessTime;

    /** endProcessTime */
    private Timestamp endProcessTime;

    /** createdAt */
    private Timestamp createdAt;

    /* This code was generated by TableGo tools, mark 1 end. */

    /* This code was generated by TableGo tools, mark 2 begin. */

    /**
     * id
     * 
     * @return id
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * id
     * 
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * hourly_adhoc_rank_project.id
     * 
     * @return hourly_adhoc_rank_project
     */
    public Integer getProjectId() {
        return this.projectId;
    }

    /**
     * hourly_adhoc_rank_project.id
     * 
     * @param projectId
     *          hourly_adhoc_rank_project
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * hourly_adhoc_rank_keyword.id
     * 
     * @return hourly_adhoc_rank_keyword
     */
    public Integer getKeywordId() {
        return this.keywordId;
    }

    /**
     * hourly_adhoc_rank_keyword.id
     * 
     * @param keywordId
     *          hourly_adhoc_rank_keyword
     */
    public void setKeywordId(Integer keywordId) {
        this.keywordId = keywordId;
    }

    /**
     * keywordName
     * 
     * @return keywordName
     */
    public String getKeywordName() {
        return this.keywordName;
    }

    /**
     * keywordName
     * 
     * @param keywordName
     */
    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    /**
     * Hour: 0 - 24
     * 
     * @return Hour
     */
    public Boolean getStartHour() {
        return this.startHour;
    }

    /**
     * Hour: 0 - 24
     * 
     * @param startHour
     *          Hour
     */
    public void setStartHour(Boolean startHour) {
        this.startHour = startHour;
    }

    /**
     * hourly_adhoc_rank_task.id
     * 
     * @return hourly_adhoc_rank_task
     */
    public Integer getTaskId() {
        return this.taskId;
    }

    /**
     * hourly_adhoc_rank_task.id
     * 
     * @param taskId
     *          hourly_adhoc_rank_task
     */
    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    /**
     * 0: not started, 1: processing 2: completed
     * 
     * @return 0: not started
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * 0: not started, 1: processing 2: completed
     * 
     * @param status
     *          0: not started
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * startProcessTime
     * 
     * @return startProcessTime
     */
    public Timestamp getStartProcessTime() {
        return this.startProcessTime;
    }

    /**
     * startProcessTime
     * 
     * @param startProcessTime
     */
    public void setStartProcessTime(Timestamp startProcessTime) {
        this.startProcessTime = startProcessTime;
    }

    /**
     * endProcessTime
     * 
     * @return endProcessTime
     */
    public Timestamp getEndProcessTime() {
        return this.endProcessTime;
    }

    /**
     * endProcessTime
     * 
     * @param endProcessTime
     */
    public void setEndProcessTime(Timestamp endProcessTime) {
        this.endProcessTime = endProcessTime;
    }

    /**
     * createdAt
     * 
     * @return createdAt
     */
    public Timestamp getCreatedAt() {
        return this.createdAt;
    }

    /**
     * createdAt
     * 
     * @param createdAt
     */
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    /* This code was generated by TableGo tools, mark 2 end. */
}