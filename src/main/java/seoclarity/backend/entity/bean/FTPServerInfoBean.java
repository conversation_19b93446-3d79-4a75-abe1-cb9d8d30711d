package seoclarity.backend.entity.bean;

/**
 * @Autor ewain
 */
public class FTPServerInfoBean {
    private String privateIp;
    private String privateHost;
    private String publicIp;
    private String publicHost;
    private String serverUserName;
    private String serverPassword;
    private String serverTargetFolder;

    public FTPServerInfoBean() {
    }

    public FTPServerInfoBean(String privateIp, String privateHost, String publicIp, String publicHost, String serverUserName, String serverPassword, String serverTargetFolder) {
        this.privateIp = privateIp;
        this.privateHost = privateHost;
        this.publicIp = publicIp;
        this.publicHost = publicHost;
        this.serverUserName = serverUserName;
        this.serverPassword = serverPassword;
        this.serverTargetFolder = serverTargetFolder;
    }

    public String getPrivateIp() {
        return privateIp;
    }

    public void setPrivateIp(String privateIp) {
        this.privateIp = privateIp;
    }

    public String getPrivateHost() {
        return privateHost;
    }

    public void setPrivateHost(String privateHost) {
        this.privateHost = privateHost;
    }

    public String getPublicIp() {
        return publicIp;
    }

    public void setPublicIp(String publicIp) {
        this.publicIp = publicIp;
    }

    public String getPublicHost() {
        return publicHost;
    }

    public void setPublicHost(String publicHost) {
        this.publicHost = publicHost;
    }

    public String getServerUserName() {
        return serverUserName;
    }

    public void setServerUserName(String serverUserName) {
        this.serverUserName = serverUserName;
    }

    public String getServerPassword() {
        return serverPassword;
    }

    public void setServerPassword(String serverPassword) {
        this.serverPassword = serverPassword;
    }

    public String getServerTargetFolder() {
        return serverTargetFolder;
    }

    public void setServerTargetFolder(String serverTargetFolder) {
        this.serverTargetFolder = serverTargetFolder;
    }

    @Override
    public String toString() {
        return "FTPServerInfoBean{" +
                "privateIp='" + privateIp + '\'' +
                ", privateHost='" + privateHost + '\'' +
                ", publicIp='" + publicIp + '\'' +
                ", publicHost='" + publicHost + '\'' +
                ", serverUserName='" + serverUserName + '\'' +
                '}';
    }
}
