package seoclarity.backend.entity;

import org.apache.commons.lang.StringUtils;

import java.sql.Date;
import java.util.List;

public class CLRankingDetailEntity {

	private Long keywordRankcheckId;
	private String keywordName;
	private Integer rankDate;
	private Integer ownDomainId;
	private Integer engine;
	private Integer language;
	private Integer locationId;
	private String domainReverse;
	private String rootDomainReverse;
	private Integer trueRank;
	private Integer webRank;
	private Integer hrd;
	private Integer count;
	private Integer rank;
	private Long avgSearchVolume;
	private Float cpc;
	private String keywordHash;

	private String uri;
	private String url;
	private String urlhash;
	private Integer protocol;
	private Integer type;
	private Integer hrdis;
	private Integer hrrd;
	private Integer tagId;
	private Integer competitorId;
	
	private String label;
	private String meta;
	private String date;
	private String domain;
	private String country;
	private String searchEngine;

	private Double estdTraffic;
	private String answerBoxFlg;
	private String rating;
	private String ratingNumber;
	private String llFlg;
	private String peopleAlsoAskFlg;
	private String imgFlg;
	private String newsFlg;
	private String tagNames;
	private Integer subRank;
	private String typeName;
    private String rankingDate;
    
    
    private String subRankUrl;
    private Integer subRankProtocol;
    private String cityName;
    private String additionalAb;
    private String googleRecommend;
    private String additionalQuestions;
    private String jobLink;
    private String appList;
    private String refineBy;
    private String ampFlg;
    private String appFlg;
    private String ppcFlg;
    private String answerboxFlg;
    private String commercialFlg;
    private String flightSearchFlg;
    private String researchFlg;
    private String social_in_kg;
    private String pla_flg;
    private String knog_flg;
    private String notRealSearchVolume;
    private String topPpcCnt;
    private String answerboxUrl;
    private String questions;
    private String subRankLabel;
    
    private String subDomainRev;
    private String subRootDomainRev;

    private String urlParameters;
	private Integer sequenceNo;

	//using for adhoc
	private Integer projectId;
	private Date requestDate;
	private Integer engineId;
	private Integer languageId;
	private Integer countOfSearch;
	private String[] attrstrkey; // Array(String)
	private String[] attrstrvalue; // Array(String)
	private String[] attrintkey; // Array(String)
	private Integer[] attrintvalue; // Array(UInt32)
	private int sign = 1; // Int8
	private String[] word; // Array(String)
	private Integer monthlySearchVolume1;
	private Integer monthlySearchVolume2;
	private Integer monthlySearchVolume3;
	private Integer monthlySearchVolume4;
	private Integer monthlySearchVolume5;
	private Integer monthlySearchVolume6;
	private Integer monthlySearchVolume7;
	private Integer monthlySearchVolume8;
	private Integer monthlySearchVolume9;
	private Integer monthlySearchVolume10;
	private Integer monthlySearchVolume11;
	private Integer monthlySearchVolume12;
	private List<String> keywordVariationOneword;
	private List<String> keywordVariationNgram;
	private List<Integer> category;
	private List<Integer> monthlySvAttrKey;
	private List<Integer> monthlySvAttrValue;
	private List<String> stream;

	private String aioContent;
	private List<String> aioLinks;

	public String getQuestions() {
		return questions;
	}

	public void setQuestions(String questions) {
		this.questions = questions;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getHrdis() {
		return hrdis;
	}

	public void setHrdis(Integer hrdis) {
		this.hrdis = hrdis;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getProtocol() {
		return protocol;
	}

	public void setProtocol(Integer protocol) {
		this.protocol = protocol;
	}

	public Long getAvgSearchVolume() {
		return avgSearchVolume;
	}

	public void setAvgSearchVolume(Long avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public Integer getRankDate() {
		return rankDate;
	}

	public void setRankDate(Integer rankDate) {
		this.rankDate = rankDate;
	}

	public Integer getEngine() {
		return engine;
	}

	public void setEngine(Integer engine) {
		this.engine = engine;
	}

	public Integer getLanguage() {
		return language;
	}

	public void setLanguage(Integer language) {
		this.language = language;
	}

	public String getDomainReverse() {
		return domainReverse;
	}

	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}

	public Integer getTrueRank() {
		return trueRank;
	}

	public void setTrueRank(Integer trueRank) {
		this.trueRank = trueRank;
	}

	public Integer getWebRank() {
		return webRank;
	}

	public void setWebRank(Integer webRank) {
		this.webRank = webRank;
	}

	public Integer getHrd() {
		return hrd;
	}

	public void setHrd(Integer hrd) {
		this.hrd = hrd;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Integer getTagId() {
		return tagId;
	}

	public void setTagId(Integer tagId) {
		this.tagId = tagId;
	}

	public Integer getCompetitorId() {
		return competitorId;
	}

	public void setCompetitorId(Integer competitorId) {
		this.competitorId = competitorId;
	}


	public String getLabel() {
		if (StringUtils.isBlank(label) || StringUtils.equals(label, "-") || StringUtils.equalsIgnoreCase(label, "null")) {
			return "";
		}
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getMeta() {
		if (StringUtils.isBlank(meta) || StringUtils.equals(meta, "-") || StringUtils.equalsIgnoreCase(meta, "null")) {
			return "";
		}
		return meta;
	}

	public void setMeta(String meta) {
		this.meta = meta;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}


	public Double getEstdTraffic() {
		return estdTraffic;
	}

	public void setEstdTraffic(Double estdTraffic) {
		this.estdTraffic = estdTraffic;
	}


	public String getAnswerBoxFlg() {
		return answerBoxFlg;
	}

	public void setAnswerBoxFlg(String answerBoxFlg) {
		this.answerBoxFlg = answerBoxFlg;
	}

	public String getRatingNumber() {
		return ratingNumber;
	}

	public void setRatingNumber(String ratingNumber) {
		this.ratingNumber = ratingNumber;
	}

	public String getLlFlg() {
		return llFlg;
	}

	public void setLlFlg(String llFlg) {
		this.llFlg = llFlg;
	}

	public String getPeopleAlsoAskFlg() {
		return peopleAlsoAskFlg;
	}

	public void setPeopleAlsoAskFlg(String peopleAlsoAskFlg) {
		this.peopleAlsoAskFlg = peopleAlsoAskFlg;
	}

	public String getImgFlg() {
		return imgFlg;
	}

	public void setImgFlg(String imgFlg) {
		this.imgFlg = imgFlg;
	}

	public String getNewsFlg() {
		return newsFlg;
	}

	public void setNewsFlg(String newsFlg) {
		this.newsFlg = newsFlg;
	}

	public Integer getLocationId() {
		if (locationId == null) {
			return 0;
		}
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}

	public Float getCpc() {
		return cpc;
	}

	public void setCpc(Float cpc) {
		this.cpc = cpc;
	}

    public String getTagNames() {
        return tagNames;
    }

    public void setTagNames(String tagNames) {
        this.tagNames = tagNames;
    }

	public Integer getSubRank() {
		return subRank;
	}

	public void setSubRank(Integer subRank) {
		this.subRank = subRank;
	}

	public Long getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(Long keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getUrlhash() {
		return urlhash;
	}

	public void setUrlhash(String urlhash) {
		this.urlhash = urlhash;
	}

    public String getRankingDate() {
        return rankingDate;
    }

    public void setRankingDate(String rankingDate) {
        this.rankingDate = rankingDate;
    }

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getDomainName() {
		if (StringUtils.isBlank(domainReverse)) {
			return "";
		}
		String domainname = StringUtils.reverseDelimited(domainReverse, '.');
		return domainname;
	}

	public String getRootDomainReverse() {
		return rootDomainReverse;
	}

	public void setRootDomainReverse(String rootDomainReverse) {
		this.rootDomainReverse = rootDomainReverse;
	}

	public Integer getHrrd() {
		return hrrd;
	}

	public void setHrrd(Integer hrrd) {
		this.hrrd = hrrd;
	}


	public String getSubRankUrl() {
		return subRankUrl;
	}

	public void setSubRankUrl(String subRankUrl) {
		this.subRankUrl = subRankUrl;
	}

	public Integer getSubRankProtocol() {
		return subRankProtocol;
	}

	public void setSubRankProtocol(Integer subRankProtocol) {
		this.subRankProtocol = subRankProtocol;
	}


	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getAdditionalAb() {
		if (StringUtils.isBlank(additionalAb) || StringUtils.equals(additionalAb, "-") || StringUtils.equalsIgnoreCase(additionalAb, "null")) {
			return "";
		}
		return additionalAb;
	}

	public void setAdditionalAb(String additionalAb) {
		this.additionalAb = additionalAb;
	}

	public String getGoogleRecommend() {
		if (StringUtils.equals(googleRecommend, "-")) {
			return "";
		}
		return googleRecommend;
	}

	public void setGoogleRecommend(String googleRecommend) {
		this.googleRecommend = googleRecommend;
	}

	public String getAdditionalQuestions() {
		if (StringUtils.equals(additionalQuestions, "-")) {
			return "";
		}
		return additionalQuestions;
	}

	public void setAdditionalQuestions(String additionalQuestions) {
		this.additionalQuestions = additionalQuestions;
	}

	public String getAppList() {
		return appList;
	}

	public void setAppList(String appList) {
		this.appList = appList;
	}

	public String getRefineBy() {
		if (StringUtils.equals(refineBy, "-")) {
			return "";
		}
		return refineBy;
	}

	public void setRefineBy(String refineBy) {
		this.refineBy = refineBy;
	}

	

	public String getAmpFlg() {
		return ampFlg;
	}

	public void setAmpFlg(String ampFlg) {
		this.ampFlg = ampFlg;
	}

	public String getAppFlg() {
		return appFlg;
	}

	public void setAppFlg(String appFlg) {
		this.appFlg = appFlg;
	}

	public String getPpcFlg() {
		return ppcFlg;
	}

	public void setPpcFlg(String ppcFlg) {
		this.ppcFlg = ppcFlg;
	}

	public String getAnswerboxFlg() {
		return answerboxFlg;
	}

	public void setAnswerboxFlg(String answerboxFlg) {
		this.answerboxFlg = answerboxFlg;
	}

	public String getCommercialFlg() {
		return commercialFlg;
	}

	public void setCommercialFlg(String commercialFlg) {
		this.commercialFlg = commercialFlg;
	}

	public String getFlightSearchFlg() {
		return flightSearchFlg;
	}

	public void setFlightSearchFlg(String flightSearchFlg) {
		this.flightSearchFlg = flightSearchFlg;
	}

	public String getResearchFlg() {
		return researchFlg;
	}

	public void setResearchFlg(String researchFlg) {
		this.researchFlg = researchFlg;
	}

	public String getSocial_in_kg() {
		return social_in_kg;
	}

	public void setSocial_in_kg(String social_in_kg) {
		this.social_in_kg = social_in_kg;
	}

	public String getPla_flg() {
		return pla_flg;
	}

	public void setPla_flg(String pla_flg) {
		this.pla_flg = pla_flg;
	}

	public String getKnog_flg() {
		return knog_flg;
	}

	public void setKnog_flg(String knog_flg) {
		this.knog_flg = knog_flg;
	}

	public String getTopPpcCnt() {
		return topPpcCnt;
	}

	public void setTopPpcCnt(String topPpcCnt) {
		this.topPpcCnt = topPpcCnt;
	}

	public String getAnswerboxUrl() {
		if (StringUtils.isBlank(answerboxUrl) || StringUtils.equals(answerboxUrl, "-") || StringUtils.equalsIgnoreCase(answerboxUrl, "null")) {
			return "";
		}
		return answerboxUrl;
	}

	public void setAnswerboxUrl(String answerboxUrl) {
		this.answerboxUrl = answerboxUrl;
	}

	public String getJobLink() {
		if (StringUtils.isBlank(jobLink) || StringUtils.equals(jobLink, "-") || StringUtils.equalsIgnoreCase(jobLink, "null")) {
			return "";
		}
		return jobLink;
	}

	public void setJobLink(String jobLink) {
		this.jobLink = jobLink;
	}

	public String getSubDomainRev() {
		return subDomainRev;
	}

	public void setSubDomainRev(String subDomainRev) {
		this.subDomainRev = subDomainRev;
	}

	public String getSubRootDomainRev() {
		return subRootDomainRev;
	}

	public void setSubRootDomainRev(String subRootDomainRev) {
		this.subRootDomainRev = subRootDomainRev;
	}

	public String getSubRankLabel() {
		if (StringUtils.equals(subRankLabel, "-")) {
			return "";
		}
		return subRankLabel;
	}

	public void setSubRankLabel(String subRankLabel) {
		this.subRankLabel = subRankLabel;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public String getNotRealSearchVolume() {
		return notRealSearchVolume;
	}

	public void setNotRealSearchVolume(String notRealSearchVolume) {
		this.notRealSearchVolume = notRealSearchVolume;
	}

	public String getKeywordHash() {
		return keywordHash;
	}

	public void setKeywordHash(String keywordHash) {
		this.keywordHash = keywordHash;
	}

	public String getUrlParameters() {
		return urlParameters;
	}

	public void setUrlParameters(String urlParameters) {
		this.urlParameters = urlParameters;
	}

	public Integer getSequenceNo() {
		return sequenceNo;
	}

	public void setSequenceNo(Integer sequenceNo) {
		this.sequenceNo = sequenceNo;
	}

	public Integer getMonthlySearchVolume1() {
		return monthlySearchVolume1;
	}

	public void setMonthlySearchVolume1(Integer monthlySearchVolume1) {
		this.monthlySearchVolume1 = monthlySearchVolume1;
	}

	public Integer getMonthlySearchVolume2() {
		return monthlySearchVolume2;
	}

	public void setMonthlySearchVolume2(Integer monthlySearchVolume2) {
		this.monthlySearchVolume2 = monthlySearchVolume2;
	}

	public Integer getMonthlySearchVolume3() {
		return monthlySearchVolume3;
	}

	public void setMonthlySearchVolume3(Integer monthlySearchVolume3) {
		this.monthlySearchVolume3 = monthlySearchVolume3;
	}

	public Integer getMonthlySearchVolume4() {
		return monthlySearchVolume4;
	}

	public void setMonthlySearchVolume4(Integer monthlySearchVolume4) {
		this.monthlySearchVolume4 = monthlySearchVolume4;
	}

	public Integer getMonthlySearchVolume5() {
		return monthlySearchVolume5;
	}

	public void setMonthlySearchVolume5(Integer monthlySearchVolume5) {
		this.monthlySearchVolume5 = monthlySearchVolume5;
	}

	public Integer getMonthlySearchVolume6() {
		return monthlySearchVolume6;
	}

	public void setMonthlySearchVolume6(Integer monthlySearchVolume6) {
		this.monthlySearchVolume6 = monthlySearchVolume6;
	}

	public Integer getMonthlySearchVolume7() {
		return monthlySearchVolume7;
	}

	public void setMonthlySearchVolume7(Integer monthlySearchVolume7) {
		this.monthlySearchVolume7 = monthlySearchVolume7;
	}

	public Integer getMonthlySearchVolume8() {
		return monthlySearchVolume8;
	}

	public void setMonthlySearchVolume8(Integer monthlySearchVolume8) {
		this.monthlySearchVolume8 = monthlySearchVolume8;
	}

	public Integer getMonthlySearchVolume9() {
		return monthlySearchVolume9;
	}

	public void setMonthlySearchVolume9(Integer monthlySearchVolume9) {
		this.monthlySearchVolume9 = monthlySearchVolume9;
	}

	public Integer getMonthlySearchVolume10() {
		return monthlySearchVolume10;
	}

	public void setMonthlySearchVolume10(Integer monthlySearchVolume10) {
		this.monthlySearchVolume10 = monthlySearchVolume10;
	}

	public Integer getMonthlySearchVolume11() {
		return monthlySearchVolume11;
	}

	public void setMonthlySearchVolume11(Integer monthlySearchVolume11) {
		this.monthlySearchVolume11 = monthlySearchVolume11;
	}

	public Integer getMonthlySearchVolume12() {
		return monthlySearchVolume12;
	}

	public void setMonthlySearchVolume12(Integer monthlySearchVolume12) {
		this.monthlySearchVolume12 = monthlySearchVolume12;
	}

	public List<String> getKeywordVariationOneword() {
		return keywordVariationOneword;
	}

	public void setKeywordVariationOneword(List<String> keywordVariationOneword) {
		this.keywordVariationOneword = keywordVariationOneword;
	}

	public List<String> getKeywordVariationNgram() {
		return keywordVariationNgram;
	}

	public void setKeywordVariationNgram(List<String> keywordVariationNgram) {
		this.keywordVariationNgram = keywordVariationNgram;
	}

	public List<Integer> getCategory() {
		return category;
	}

	public void setCategory(List<Integer> category) {
		this.category = category;
	}

	public Integer[] getMonthlySvAttrKey() {
		if (monthlySvAttrKey == null || monthlySvAttrKey.size() == 0) {
			return new Integer[] {};
		} else {
			return monthlySvAttrKey.toArray(new Integer[monthlySvAttrKey.size()]);
		}
	}

	public void setMonthlySvAttrKey(List<Integer> monthlySvAttrKey) {
		this.monthlySvAttrKey = monthlySvAttrKey;
	}

	public Integer[] getMonthlySvAttrValue() {
		if (monthlySvAttrValue == null || monthlySvAttrValue.size() == 0) {
			return new Integer[] {};
		} else {
			return monthlySvAttrValue.toArray(new Integer[monthlySvAttrValue.size()]);
		}
	}

	public void setMonthlySvAttrValue(List<Integer> monthlySvAttrValue) {
		this.monthlySvAttrValue = monthlySvAttrValue;
	}

	public Integer getProjectId() {
		return projectId;
	}

	public void setProjectId(Integer projectId) {
		this.projectId = projectId;
	}

	public Date getRequestDate() {
		return requestDate;
	}

	public void setRequestDate(Date requestDate) {
		this.requestDate = requestDate;
	}

	public Integer getEngineId() {
		return engineId;
	}

	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public Integer getCountOfSearch() {
		return countOfSearch;
	}

	public void setCountOfSearch(Integer countOfSearch) {
		this.countOfSearch = countOfSearch;
	}

	public String[] getAttrstrkey() {
		return attrstrkey;
	}

	public void setAttrstrkey(String[] attrstrkey) {
		this.attrstrkey = attrstrkey;
	}

	public String[] getAttrstrvalue() {
		return attrstrvalue;
	}

	public void setAttrstrvalue(String[] attrstrvalue) {
		this.attrstrvalue = attrstrvalue;
	}

	public String[] getAttrintkey() {
		return attrintkey;
	}

	public void setAttrintkey(String[] attrintkey) {
		this.attrintkey = attrintkey;
	}

	public Integer[] getAttrintvalue() {
		return attrintvalue;
	}

	public void setAttrintvalue(Integer[] attrintvalue) {
		this.attrintvalue = attrintvalue;
	}

	public int getSign() {
		return sign;
	}

	public void setSign(int sign) {
		this.sign = sign;
	}

	public String[] getWord() {
		return word;
	}

	public void setWord(String[] word) {
		this.word = word;
	}

	public List<String> getStream() {
		return stream;
	}

	public void setStream(List<String> stream) {
		this.stream = stream;
	}

	public String[] getKeywordVariationOnewordArray() {
		if (keywordVariationOneword == null || keywordVariationOneword.size() == 0) {
			return new String[] {};
		} else {
			return keywordVariationOneword.toArray(new String[keywordVariationOneword.size()]);
		}
	}

	public Integer[] getCategoryArray() {
		if (category == null || category.size() == 0) {
			return new Integer[] {};
		} else {
			return category.toArray(new Integer[category.size()]);
		}
	}

	public String[] getStreamArray() {
		if (stream == null || stream.size() == 0) {
			return new String[] {};
		} else {
			return stream.toArray(new String[stream.size()]);
		}
	}
	public String[] getKeywordVariationNgramArray() {
		if (keywordVariationNgram == null || keywordVariationNgram.size() == 0) {
			return new String[] {};
		} else {
			return keywordVariationNgram.toArray(new String[keywordVariationNgram.size()]);
		}
	}

	public String getAioContent() {
		return aioContent;
	}

	public void setAioContent(String aioContent) {
		this.aioContent = aioContent;
	}

	public List<String> getAioLinks() {
		return aioLinks;
	}

	public void setAioLinks(List<String> aioLinks) {
		this.aioLinks = aioLinks;
	}
}
