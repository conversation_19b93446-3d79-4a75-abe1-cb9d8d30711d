/**
 *
 */
package seoclarity.backend.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import seoclarity.backend.entity.actonia.RankIndexParamEntity;

/**
 * com.actonia.saas.vo.RankIndexParamVO.java
 *
 * <AUTHOR>
 *
 * @version $Revision: 8501 $ $Author: wangc@SHINETECHCHINA $
 */
public class RankIndexParamVO {

    List<RankIndexParamEntity> rankIndexParamList;

    public RankIndexParamVO(List<RankIndexParamEntity> eneityList) {
        this.rankIndexParamList = eneityList;
    }

    public float getRankIndexParam(Integer rankPosition) {
        if (rankPosition == null) {
            return 0;
        }
        if (rankIndexParamList != null && !rankIndexParamList.isEmpty()) {
            for (RankIndexParamEntity entity : rankIndexParamList) {
                if (entity.getRankPosition().intValue() == rankPosition) {
                    return entity.getParamValue();
                }
            }
        }
        return getDefaultParam(rankPosition);
    }

    public List<String> getParamList() {
        List<String> valList = new ArrayList<>();
        if (rankIndexParamList != null && rankIndexParamList.size() > 0) {
            Map<Integer, String> map = new HashMap<Integer, String>();
            for (seoclarity.backend.entity.actonia.RankIndexParamEntity entity : rankIndexParamList) {
                int rank = entity.getRankPosition().intValue();
                String val = entity.getParamValue() == null ? "0.0" : entity.getParamValue().toString();
                map.put(rank, val);
            }
            List<Integer> rankList = new ArrayList<>(map.keySet());
            Collections.sort(rankList);
            for (int rank : rankList) {
                String ctrVal = map.get(rank);
                valList.add(ctrVal);
            }
        } else {
            for (int i = 1; i <= 10; i++) {
                float val = getDefaultParam(i);
                valList.add(String.valueOf(val));
            }
        }

        return valList;
    }

    private float getDefaultParam(int rankPosition) {
        if (rankPosition == 1) {
            return 0.3f;
        }
        if (rankPosition == 2) {
            return 0.24f;
        }
        if (rankPosition == 3) {
            return 0.15f;
        }
        if (rankPosition == 4) {
            return 0.1f;
        }
        if (rankPosition == 5) {
            return 0.08f;
        }
        if (rankPosition == 6) {
            return 0.05f;
        }
        if (rankPosition == 7) {
            return 0.04f;
        }
        if (rankPosition == 8) {
            return 0.03f;
        }
        if (rankPosition == 9) {
            return 0.01f;
        }
        return 0;
    }

    public float getRank1Param() {
        return getRankIndexParam(1);
    }

    public float getRank2Param() {
        return getRankIndexParam(2);
    }

    public float getRank3Param() {
        return getRankIndexParam(3);
    }

    public float getRank4Param() {
        return getRankIndexParam(4);
    }

    public float getRank5Param() {
        return getRankIndexParam(5);
    }

    public float getRank6Param() {
        return getRankIndexParam(6);
    }

    public float getRank7Param() {
        return getRankIndexParam(7);
    }

    public float getRank8Param() {
        return getRankIndexParam(8);
    }

    public float getRank9Param() {
        return getRankIndexParam(9);
    }

    public float getRank10Param() {
        return getRankIndexParam(10);
    }

    public String getTotalIndex(List<String> ctrList) {
        BigDecimal result = new BigDecimal(0);
        for (String index : ctrList) {
            BigDecimal bigDecimal = new BigDecimal(index);
            result = result.add(bigDecimal);
        }
        return result.toString();
    }
}
