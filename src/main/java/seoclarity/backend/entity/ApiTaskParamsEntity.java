package seoclarity.backend.entity;

import java.util.Date;

public class ApiTaskParamsEntity {

	private Integer id;
	private Integer taskInstanceId;
	private String accessToken;
	private String requestBody;
	private String translatedParams;
	private String translatedJsonParams;

	public String getRequestSql() {
		return requestSql;
	}

	public void setRequestSql(String requestSql) {
		this.requestSql = requestSql;
	}

	private String requestSql;
	private Date createdAt;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getTaskInstanceId() {
		return taskInstanceId;
	}

	public void setTaskInstanceId(Integer taskInstanceId) {
		this.taskInstanceId = taskInstanceId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getRequestBody() {
		return requestBody;
	}

	public void setRequestBody(String requestBody) {
		this.requestBody = requestBody;
	}

	public String getTranslatedParams() {
		return translatedParams;
	}

	public void setTranslatedParams(String translatedParams) {
		this.translatedParams = translatedParams;
	}

	public String getTranslatedJsonParams() {
		return translatedJsonParams;
	}

	public void setTranslatedJsonParams(String translatedJsonParams) {
		this.translatedJsonParams = translatedJsonParams;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

}
