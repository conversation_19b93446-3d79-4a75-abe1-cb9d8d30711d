package seoclarity.backend.entity;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

public class KeywordSubRankEntityVO {
	public static final int RANKING_TYPE_SUB_RANK = 0;
	public static final int RANKING_TYPE_LOCAL_LISTING = 1;
	public static final int RANKING_TYPE_QUESTION_LIST = 2;
	public static final int RANKING_TYPE_RIGHT_FREE_SHOP_LIST = 3;
	public static final int RANKING_TYPE_PRODUCT_LIST = 4;
	public static final int RANKING_TYPE_HOTELLISTING = 5;
	public static final int RANKING_TYPE_AI = 6;
	public static final int RANKING_TYPE_AIO_CAROUSAL = 7;
	public static final int RANKING_TYPE_AIO_CONTENT = 8;

	public static final Map<Integer, String> SERP_SUBRANK_RANKING_TYPE_NAME_MAP = ImmutableMap.<Integer, String>builder()
			.put(RANKING_TYPE_RIGHT_FREE_SHOP_LIST, "Free Product List")
			.put(RANKING_TYPE_LOCAL_LISTING, "Local Listing")
			.put(RANKING_TYPE_HOTELLISTING, "Hotel")
			.put(RANKING_TYPE_PRODUCT_LIST, "Products")
			.put(RANKING_TYPE_AI, "AI Overview")
			.put(RANKING_TYPE_AIO_CAROUSAL, "AI Overview")
			.put(RANKING_TYPE_AIO_CONTENT, "AI Overview")
			.build();


	private int rank;

	private int subRank;

	private String landingPage;

	private String label;

	private String videoName;
	
	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

	public int getSubRank() {
		return subRank;
	}

	public void setSubRank(int subRank) {
		this.subRank = subRank;
	}

	public String getLandingPage() {
		return landingPage;
	}

	public void setLandingPage(String landingPage) {
		this.landingPage = landingPage;
	}

	public String getVideoName() {
		return videoName;
	}

	public void setVideoName(String videoName) {
		this.videoName = videoName;
	}
}
