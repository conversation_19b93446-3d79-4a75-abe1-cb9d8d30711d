package seoclarity.backend.entity.s3tob2;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class B2FileInfoVo {
    private String fullPathFolder;
    private String zipFileName;
    private List<Detail> details = new ArrayList<>();
    @Data
    public class Detail {
        /** s3 info */
        private String fullPathFolder;
        private String sourceFileName;
        private int processStatus;
        private Long sourceFileSize;
        /** new b2 info */
        private Integer rankDate;
        private Integer keywordType;
        private Integer engineId;
        private Integer languageId;
        private String device;
        private String cityQueryName;
        private String keywordName;
        private String rawKeywordName;
        private String cdbKeywordHash;
        private String cdbKeywordMurmur3hash;
        private String cdbEnocdeKeywordHash;
        private String fileName;
        private String alternativeName;
        private Integer renamed;
    }
}
