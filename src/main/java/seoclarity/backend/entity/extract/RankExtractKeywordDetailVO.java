package seoclarity.backend.entity.extract;

import java.io.Serializable;
import java.util.List;

public class RankExtractKeywordDetailVO implements Serializable {
	
	private static final long serialVersionUID = 1351401125672317266L;

	private int trueRank;
	
	private Integer webRank;

	private String url;

	//web
	private String type;

	private RankExtractUrlVO urlInformation;
	
	private List<RankExtractSubRankVO> subRanks;

	public List<RankExtractSubRankVO> getSubRanks() {
		return subRanks;
	}

	public void setSubRanks(List<RankExtractSubRankVO> subRanks) {
		this.subRanks = subRanks;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public int getTrueRank() {
		return trueRank;
	}

	public void setTrueRank(int trueRank) {
		this.trueRank = trueRank;
	}

	public Integer getWebRank() {
		return webRank;
	}

	public void setWebRank(Integer webRank) {
		this.webRank = webRank;
	}

	public RankExtractUrlVO getUrlInformation() {
		return urlInformation;
	}

	public void setUrlInformation(RankExtractUrlVO urlInformation) {
		this.urlInformation = urlInformation;
	}


	
	
	
	
}
