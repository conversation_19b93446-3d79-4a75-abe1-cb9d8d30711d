package seoclarity.backend.entity.extract;

import java.io.Serializable;
import java.util.List;


public class RankExtractMainVO implements Serializable {
    private static final long serialVersionUID = 1351401185672317290L;

    
    private RankExtractInfoVO info;
    
    private RankExtractSearchInfoVO searchInfo;
    
    private RankExtractSearchParamVO searchParameters;
    
    private RankExtractFeatureFlagVO serpFeaturesFlag;
    
    private List<RankExtractFeatureRankVO> peopleAlsoAsk;

    private List<RankExtractFeatureRankVO> thingsToKnow;
    
    private RankExtractRefineByVO refineBy;

    private List<RankExtractKeywordDetailVO> rankings;

	public List<RankExtractKeywordDetailVO> getRankings() {
		return rankings;
	}

	public void setRankings(List<RankExtractKeywordDetailVO> rankings) {
		this.rankings = rankings;
	}

	public RankExtractInfoVO getInfo() {
		return info;
	}

	public void setInfo(RankExtractInfoVO info) {
		this.info = info;
	}

	public RankExtractSearchInfoVO getSearchInfo() {
		return searchInfo;
	}

	public void setSearchInfo(RankExtractSearchInfoVO searchInfo) {
		this.searchInfo = searchInfo;
	}

	public RankExtractSearchParamVO getSearchParameters() {
		return searchParameters;
	}

	public void setSearchParameters(RankExtractSearchParamVO searchParameters) {
		this.searchParameters = searchParameters;
	}

	public RankExtractFeatureFlagVO getSerpFeaturesFlag() {
		return serpFeaturesFlag;
	}

	public void setSerpFeaturesFlag(RankExtractFeatureFlagVO serpFeaturesFlag) {
		this.serpFeaturesFlag = serpFeaturesFlag;
	}

	public List<RankExtractFeatureRankVO> getPeopleAlsoAsk() {
		return peopleAlsoAsk;
	}

	public void setPeopleAlsoAsk(List<RankExtractFeatureRankVO> peopleAlsoAsk) {
		this.peopleAlsoAsk = peopleAlsoAsk;
	}

	public RankExtractRefineByVO getRefineBy() {
		return refineBy;
	}

	public void setRefineBy(RankExtractRefineByVO refineBy) {
		this.refineBy = refineBy;
	}

	public List<RankExtractFeatureRankVO> getThingsToKnow() {
		return thingsToKnow;
	}

	public void setThingsToKnow(List<RankExtractFeatureRankVO> thingsToKnow) {
		this.thingsToKnow = thingsToKnow;
	}
    
    
}
