package seoclarity.backend.entity.extract;

import java.io.Serializable;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.math.NumberUtils;

import seoclarity.backend.utils.CommonUtils;


public class RankExtractFeatureFlagVO implements Serializable {
    private static final long serialVersionUID = 1351401185672317285L;

    
    private String app;
    
    private String img;
    
    private String news;
    
    private String video;
    
    private String localListing;
    
    private String ppcAds;
    
    private String answerBox;
    
    private String hotel;
    
    private String flights;
    
    private String pla;
    
    private String knowledgeGraph;
    
    private String fromsourcesacrosstheweb;
    
    private String findresultson;
    
    private String populardestinations;
    
    private String aio;
    
    private String popularRecipes;
    
    private String popularStore;
    
    private String discussionsandforums;
    
    private String buyingguide;

	public String getApp() {
		return app;
	}

	public void setApp(String app) {
		this.app = app;
	}

	public String getImg() {
		return img;
	}

	public void setImg(String img) {
		this.img = img;
	}

	public String getNews() {
		return news;
	}

	public void setNews(String news) {
		this.news = news;
	}

	public String getVideo() {
		return video;
	}

	public void setVideo(String video) {
		this.video = video;
	}

	public String getLocalListing() {
		return localListing;
	}

	public void setLocalListing(String localListing) {
		this.localListing = localListing;
	}

	public String getPpcAds() {
		return ppcAds;
	}

	public void setPpcAds(String ppcAds) {
		this.ppcAds = ppcAds;
	}

	public String getAnswerBox() {
		return answerBox;
	}

	public void setAnswerBox(String answerBox) {
		this.answerBox = answerBox;
	}

	public String getHotel() {
		return hotel;
	}

	public void setHotel(String hotel) {
		this.hotel = hotel;
	}

	public String getFlights() {
		return flights;
	}

	public void setFlights(String flights) {
		this.flights = flights;
	}

	public String getPla() {
		return pla;
	}

	public void setPla(String pla) {
		this.pla = pla;
	}

	public String getKnowledgeGraph() {
		return knowledgeGraph;
	}

	public void setKnowledgeGraph(String knowledgeGraph) {
		this.knowledgeGraph = knowledgeGraph;
	}

	public String getFromsourcesacrosstheweb() {
		return fromsourcesacrosstheweb;
	}

	public void setFromsourcesacrosstheweb(String fromsourcesacrosstheweb) {
		this.fromsourcesacrosstheweb = fromsourcesacrosstheweb;
	}

	public String getFindresultson() {
		return findresultson;
	}

	public void setFindresultson(String findresultson) {
		this.findresultson = findresultson;
	}

	public String getPopulardestinations() {
		return populardestinations;
	}

	public void setPopulardestinations(String populardestinations) {
		this.populardestinations = populardestinations;
	}

	public String getAio() {
		return aio;
	}

	public void setAio(String aio) {
		this.aio = aio;
	}

	public String getPopularRecipes() {
		return popularRecipes;
	}

	public void setPopularRecipes(String popularRecipes) {
		this.popularRecipes = popularRecipes;
	}

	public String getPopularStore() {
		return popularStore;
	}

	public void setPopularStore(String popularStore) {
		this.popularStore = popularStore;
	}

	public String getDiscussionsandforums() {
		return discussionsandforums;
	}

	public void setDiscussionsandforums(String discussionsandforums) {
		this.discussionsandforums = discussionsandforums;
	}

	public String getBuyingguide() {
		return buyingguide;
	}

	public void setBuyingguide(String buyingguide) {
		this.buyingguide = buyingguide;
	}
    

}
