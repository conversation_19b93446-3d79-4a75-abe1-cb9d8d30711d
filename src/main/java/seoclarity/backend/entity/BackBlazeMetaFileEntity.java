package seoclarity.backend.entity;

import lombok.Data;

import java.util.Date;
/**
 * CREATE TABLE backblaze_meta_file (
 *   ->  id bigint(20) NOT NULL AUTO_INCREMENT,
 *   ->  metaFolderId int(11) NOT NULL COMMENT 'backblaze_meta_folder.id',
 *   ->  zipFileName varchar(64) NOT NULL COMMENT 'zip file name, example: 1.zip',
 *   ->  createDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *   ->  PRIMARY KEY (id),
 *   ->  UNIQUE KEY uidx_metaFolderId_zipFileName (metaFolderId, zipFileName),
 *   ->  KEY idx_createDate (createDate)
 *   -> ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
 */
@Data
public class BackBlazeMetaFileEntity {
    private Long id;
    //backblaze_meta_folder.id
    private Integer metaFolderId;
    //zip file name, example: md5.zip
    private String zipFileName;
    private Date createDate;
}
