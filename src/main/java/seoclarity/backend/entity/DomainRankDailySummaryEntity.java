package seoclarity.backend.entity;

import java.math.BigDecimal;
import java.util.Date;

public class DomainRankDailySummaryEntity {
	private Long id;
	private Integer ownDomainId;
	private Integer rank;
	private Date logDate;
	private Integer tagId;
	private Integer entrance;
	private Integer transactions;
	private BigDecimal itemRevenue;
	private Integer goal1completions;
	private Integer goal2completions;
	private Integer goal3completions;
	private Integer goal4completions;
	private Integer goal5completions;
	private Integer goal6completions;
	private Integer goal7completions;
	private Integer goal8completions;
	private Integer goal9completions;
	private Integer goal10completions;
	private Integer goal11completions;
	private Integer goal12completions;
	private Integer goal13completions;
	private Integer goal14completions;
	private Integer goal15completions;
	private Integer goal16completions;
	private Integer goal17completions;
	private Integer goal18completions;
	private Integer goal19completions;
	private Integer goal20completions;

	private BigDecimal goal1value;
	private BigDecimal goal2value;
	private BigDecimal goal3value;
	private BigDecimal goal4value;
	private BigDecimal goal5value;
	private BigDecimal goal6value;
	private BigDecimal goal7value;
	private BigDecimal goal8value;
	private BigDecimal goal9value;
	private BigDecimal goal10value;
	private BigDecimal goal11value;
	private BigDecimal goal12value;
	private BigDecimal goal13value;
	private BigDecimal goal14value;
	private BigDecimal goal15value;
	private BigDecimal goal16value;
	private BigDecimal goal17value;
	private BigDecimal goal18value;
	private BigDecimal goal19value;
	private BigDecimal goal20value;

	private Long searchVolume;

	private Long avgSearchVolume;

	public Long getAvgSearchVolume() {
		return avgSearchVolume;
	}

	public void setAvgSearchVolume(Long avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public Date getLogDate() {
		return logDate;
	}

	public void setLogDate(Date logDate) {
		this.logDate = logDate;
	}

	public Integer getTagId() {
		return tagId;
	}

	public void setTagId(Integer tagId) {
		this.tagId = tagId;
	}

	public Integer getEntrance() {
		return entrance;
	}

	public void setEntrance(Integer entrance) {
		this.entrance = entrance;
	}

	public Integer getTransactions() {
		return transactions;
	}

	public void setTransactions(Integer transactions) {
		this.transactions = transactions;
	}

	public BigDecimal getItemRevenue() {
		return itemRevenue;
	}

	public void setItemRevenue(BigDecimal itemRevenue) {
		this.itemRevenue = itemRevenue;
	}

	public Integer getGoal1completions() {
		return goal1completions;
	}

	public void setGoal1completions(Integer goal1completions) {
		this.goal1completions = goal1completions;
	}

	public Integer getGoal2completions() {
		return goal2completions;
	}

	public void setGoal2completions(Integer goal2completions) {
		this.goal2completions = goal2completions;
	}

	public Integer getGoal3completions() {
		return goal3completions;
	}

	public void setGoal3completions(Integer goal3completions) {
		this.goal3completions = goal3completions;
	}

	public Integer getGoal4completions() {
		return goal4completions;
	}

	public void setGoal4completions(Integer goal4completions) {
		this.goal4completions = goal4completions;
	}

	public Integer getGoal5completions() {
		return goal5completions;
	}

	public void setGoal5completions(Integer goal5completions) {
		this.goal5completions = goal5completions;
	}

	public Integer getGoal6completions() {
		return goal6completions;
	}

	public void setGoal6completions(Integer goal6completions) {
		this.goal6completions = goal6completions;
	}

	public Integer getGoal7completions() {
		return goal7completions;
	}

	public void setGoal7completions(Integer goal7completions) {
		this.goal7completions = goal7completions;
	}

	public Integer getGoal8completions() {
		return goal8completions;
	}

	public void setGoal8completions(Integer goal8completions) {
		this.goal8completions = goal8completions;
	}

	public Integer getGoal9completions() {
		return goal9completions;
	}

	public void setGoal9completions(Integer goal9completions) {
		this.goal9completions = goal9completions;
	}

	public Integer getGoal10completions() {
		return goal10completions;
	}

	public void setGoal10completions(Integer goal10completions) {
		this.goal10completions = goal10completions;
	}

	public BigDecimal getGoal1value() {
		return goal1value;
	}

	public void setGoal1value(BigDecimal goal1value) {
		this.goal1value = goal1value;
	}

	public BigDecimal getGoal2value() {
		return goal2value;
	}

	public void setGoal2value(BigDecimal goal2value) {
		this.goal2value = goal2value;
	}

	public BigDecimal getGoal3value() {
		return goal3value;
	}

	public void setGoal3value(BigDecimal goal3value) {
		this.goal3value = goal3value;
	}

	public BigDecimal getGoal4value() {
		return goal4value;
	}

	public void setGoal4value(BigDecimal goal4value) {
		this.goal4value = goal4value;
	}

	public BigDecimal getGoal5value() {
		return goal5value;
	}

	public void setGoal5value(BigDecimal goal5value) {
		this.goal5value = goal5value;
	}

	public BigDecimal getGoal6value() {
		return goal6value;
	}

	public void setGoal6value(BigDecimal goal6value) {
		this.goal6value = goal6value;
	}

	public BigDecimal getGoal7value() {
		return goal7value;
	}

	public void setGoal7value(BigDecimal goal7value) {
		this.goal7value = goal7value;
	}

	public BigDecimal getGoal8value() {
		return goal8value;
	}

	public void setGoal8value(BigDecimal goal8value) {
		this.goal8value = goal8value;
	}

	public BigDecimal getGoal9value() {
		return goal9value;
	}

	public void setGoal9value(BigDecimal goal9value) {
		this.goal9value = goal9value;
	}

	public BigDecimal getGoal10value() {
		return goal10value;
	}

	public void setGoal10value(BigDecimal goal10value) {
		this.goal10value = goal10value;
	}

	public Long getSearchVolume() {
		return searchVolume;
	}

	public void setSearchVolume(Long searchVolume) {
		this.searchVolume = searchVolume;
	}

	public Integer getGoal11completions() {
		return goal11completions;
	}

	public void setGoal11completions(Integer goal11completions) {
		this.goal11completions = goal11completions;
	}

	public Integer getGoal12completions() {
		return goal12completions;
	}

	public void setGoal12completions(Integer goal12completions) {
		this.goal12completions = goal12completions;
	}

	public Integer getGoal13completions() {
		return goal13completions;
	}

	public void setGoal13completions(Integer goal13completions) {
		this.goal13completions = goal13completions;
	}

	public Integer getGoal14completions() {
		return goal14completions;
	}

	public void setGoal14completions(Integer goal14completions) {
		this.goal14completions = goal14completions;
	}

	public Integer getGoal15completions() {
		return goal15completions;
	}

	public void setGoal15completions(Integer goal15completions) {
		this.goal15completions = goal15completions;
	}

	public Integer getGoal16completions() {
		return goal16completions;
	}

	public void setGoal16completions(Integer goal16completions) {
		this.goal16completions = goal16completions;
	}

	public Integer getGoal17completions() {
		return goal17completions;
	}

	public void setGoal17completions(Integer goal17completions) {
		this.goal17completions = goal17completions;
	}

	public Integer getGoal18completions() {
		return goal18completions;
	}

	public void setGoal18completions(Integer goal18completions) {
		this.goal18completions = goal18completions;
	}

	public Integer getGoal19completions() {
		return goal19completions;
	}

	public void setGoal19completions(Integer goal19completions) {
		this.goal19completions = goal19completions;
	}

	public Integer getGoal20completions() {
		return goal20completions;
	}

	public void setGoal20completions(Integer goal20completions) {
		this.goal20completions = goal20completions;
	}

	public BigDecimal getGoal11value() {
		return goal11value;
	}

	public void setGoal11value(BigDecimal goal11value) {
		this.goal11value = goal11value;
	}

	public BigDecimal getGoal12value() {
		return goal12value;
	}

	public void setGoal12value(BigDecimal goal12value) {
		this.goal12value = goal12value;
	}

	public BigDecimal getGoal13value() {
		return goal13value;
	}

	public void setGoal13value(BigDecimal goal13value) {
		this.goal13value = goal13value;
	}

	public BigDecimal getGoal14value() {
		return goal14value;
	}

	public void setGoal14value(BigDecimal goal14value) {
		this.goal14value = goal14value;
	}

	public BigDecimal getGoal15value() {
		return goal15value;
	}

	public void setGoal15value(BigDecimal goal15value) {
		this.goal15value = goal15value;
	}

	public BigDecimal getGoal16value() {
		return goal16value;
	}

	public void setGoal16value(BigDecimal goal16value) {
		this.goal16value = goal16value;
	}

	public BigDecimal getGoal17value() {
		return goal17value;
	}

	public void setGoal17value(BigDecimal goal17value) {
		this.goal17value = goal17value;
	}

	public BigDecimal getGoal18value() {
		return goal18value;
	}

	public void setGoal18value(BigDecimal goal18value) {
		this.goal18value = goal18value;
	}

	public BigDecimal getGoal19value() {
		return goal19value;
	}

	public void setGoal19value(BigDecimal goal19value) {
		this.goal19value = goal19value;
	}

	public BigDecimal getGoal20value() {
		return goal20value;
	}

	public void setGoal20value(BigDecimal goal20value) {
		this.goal20value = goal20value;
	}

}
