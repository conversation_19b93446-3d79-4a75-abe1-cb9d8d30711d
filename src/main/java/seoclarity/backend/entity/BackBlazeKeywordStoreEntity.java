package seoclarity.backend.entity;

import lombok.Data;

import java.util.Date;

/**
 * CREATE TABLE backblaze_keyword_store (
 *   ->  id bigint(20) NOT NULL AUTO_INCREMENT,
 *   ->  rankDate int(11) NOT NULL COMMENT 'yyyyMMdd',
 *   ->  keywordType tinyint(1) NOT NULL DEFAULT '1' COMMENT '1: national keyword(cityId=0), 2: geo keyword(cityId>0)',
 *   ->  engineId smallint(3) NOT NULL COMMENT 'search engine id, Example:1',
 *   ->  languageId smallint(3) NOT NULL COMMENT 'search language id, Example:1',
 *   ->  device varchar(5) NOT NULL DEFAULT 'd' COMMENT 'd: desktop, m: mobile',
 *   ->  cityQueryName
 *   ->  keywordName varchar(600) NOT NULL COMMENT 'encoded keyword name',
 *   ->  rawKeywordName varchar(600) NOT NULL COMMENT 'decoded keyword name',
 *   ->  cdbKeywordHash bigint(20) unsigned NOT NULL COMMENT 'URLHash(lower(raw_keyword_name))',
 *   ->  cdbKeywordMurmur3hash bigint(20) unsigned NOT NULL COMMENT 'murmurHash3_64(lower(raw_keyword_name))',
 *   -> `cdbEnocdeKeywordHash` bigint(20) unsigned NOT NULL COMMENT 'URLHash(lower(keywordName))',
 *   ->  metaFileId bigint(20) NOT NULL COMMENT 'backblaze_meta_file.id',
 *   ->  fileName varchar(128) NOT NULL COMMENT 'file name in zip file(folder)',
 *   ->  alternativeName varchar(64) default NULL COMMENT 'alternative file name in zip file(folder) when fileName length>=250',
 *   ->  renamed tinyint(1) NOT NULL default '0' COMMENT '0: original file name, 1: use alternativeName instead of fileName to query from BB',
 *   ->  sourceFileId bigint(20) NOT NULL COMMENT 'backblaze_source_file_s3.id',
 *   ->  createDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *   ->  PRIMARY KEY (id),
 *   ->  UNIQUE KEY uidx_rankDate_kwType_se_device_cityId_kwHash_murmur3hash(rankDate, keywordType, engineId, languageId, device, cityId, cdbKeywordHash, cdbKeywordMurmur3hash),
 *   ->  UNIQUE KEY uidx_metaFileId_fileName (metaFileId, fileName),
 *   ->  KEY idx_kwHash_kwMurmur3hash(cdbKeywordHash, cdbKeywordMurmur3hash),
 *   ->  KEY idx_keywordName(keywordName),
 *   ->  KEY idx_rawKeywordName(rawKeywordName),
 *   ->  KEY idx_sourceFileId (sourceFileId),
 *   ->  KEY idx_createDate (createDate)
 *   -> ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
 */
@Data
public class BackBlazeKeywordStoreEntity {

    public static int TYPE_NATIONAL = 1;
    public static int TYPE_GEO = 2;

    public static int TYPE_FPR_NATIONAL = 3;
    public static int TYPE_FPR_GEO = 4;
    public static int TYPE_FPR_VED_NATIONAL = 5;
    public static int TYPE_FPR_VED_GEO = 6;
    public static int TYPE_JOB_NATIONAL = 7;
    public static int TYPE_JOB_GEO = 8;
    public static int TYPE_LL_NATIONAL = 9;
    public static int TYPE_LL_GEO = 10;
    public static int TYPE_LL_V2_NATIONAL = 11;
    public static int TYPE_LL_V2_GEO = 12;

    private Long id;
    private Integer rankDate;
    private Integer keywordType;
    private Integer engineId;
    private Integer languageId;
    private String device;
    private String cityQueryName;
    private String keywordName;
    private String rawKeywordName;
    private String cdbKeywordHash;
    private String cdbKeywordMurmur3hash;
    private String cdbEnocdeKeywordHash;
    private Long metaFileId;
    private String fileName;
    private String alternativeName;
    private Integer renamed;
    private Long sourceFileId;
    private Date createDate;

    private String fullPathFolder;

    @Override
    public String toString() {
        return "BackBlazeKeywordStoreEntity{" +
                "rankDate=" + rankDate +
                ", keywordType=" + keywordType +
                ", engineId=" + engineId +
                ", languageId=" + languageId +
                ", device='" + device + '\'' +
                ", cityQueryName='" + cityQueryName + '\'' +
                ", keywordName='" + keywordName + '\'' +
                ", metaFileId=" + metaFileId +
                ", alternativeName='" + alternativeName + '\'' +
                ", renamed=" + renamed +
                ", sourceFileId=" + sourceFileId +
                '}';
    }
}
