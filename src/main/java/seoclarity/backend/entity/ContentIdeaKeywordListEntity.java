package seoclarity.backend.entity;

import java.util.List;

public class ContentIdeaKeywordListEntity {

	private String hashId;
	
	private String keyword;
	
	private String searchVol;
	
	private String foundDate;
	private String title;
	private Integer engine_id;
	private Integer language_id;
	private String keyowrdList;

	public String getHashId() {
		return hashId;
	}

	public void setHashId(String hashId) {
		this.hashId = hashId;
	}

	public String getSearchVol() {
		return searchVol;
	}

	public void setSearchVol(String searchVol) {
		this.searchVol = searchVol;
	}

	public String getFoundDate() {
		return foundDate;
	}

	public void setFoundDate(String foundDate) {
		this.foundDate = foundDate;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getEngine_id() {
		return engine_id;
	}

	public void setEngine_id(Integer engine_id) {
		this.engine_id = engine_id;
	}

	public Integer getLanguage_id() {
		return language_id;
	}

	public void setLanguage_id(Integer language_id) {
		this.language_id = language_id;
	}

	public String getKeyowrdList() {
		return keyowrdList;
	}

	public void setKeyowrdList(String keyowrdList) {
		this.keyowrdList = keyowrdList;
	}

}
