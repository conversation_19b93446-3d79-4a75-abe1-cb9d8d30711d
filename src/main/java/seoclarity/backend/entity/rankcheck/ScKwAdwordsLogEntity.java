package seoclarity.backend.entity.rankcheck;

import java.util.Date;

public class ScKwAdwordsLogEntity {

	public static final int OPERATION_TYPE_INSERT = 1;
	public static final int OPERATION_TYPE_UPDATE = 2;
	public static final int OPERATION_TYPE_DELETE = 3;

	private Integer logId;
	private Integer id;
	private Integer adwordsId;
	private Integer keywordId;
	private Integer cityId;
	private Integer searchEngineId;
	private Integer languageId;
	private Integer avgMonthlySearchVolume;

	private Integer monthlySearchVolume1;
	private Integer monthlySearchVolume2;
	private Integer monthlySearchVolume3;
	private Integer monthlySearchVolume4;
	private Integer monthlySearchVolume5;
	private Integer monthlySearchVolume6;
	private Integer monthlySearchVolume7;
	private Integer monthlySearchVolume8;
	private Integer monthlySearchVolume9;
	private Integer monthlySearchVolume10;
	private Integer monthlySearchVolume11;
	private Integer monthlySearchVolume12;

	private Integer startMonth;
	private Integer endMonth;
	private Integer monthlyRelationshipCreateDate;
	private Double competition;
	private Double costPerClick;
	private String category;
	private Integer uploadInfoId;
	private Date createDate;
	private Integer operationType;
	private Integer log_date;
	private Date created_at;

	public Integer getLogId() {
		return logId;
	}

	public void setLogId(Integer logId) {
		this.logId = logId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getAdwordsId() {
		return adwordsId;
	}

	public void setAdwordsId(Integer adwordsId) {
		this.adwordsId = adwordsId;
	}

	public Integer getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(Integer keywordId) {
		this.keywordId = keywordId;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public Integer getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(Integer searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public Integer getAvgMonthlySearchVolume() {
		return avgMonthlySearchVolume;
	}

	public void setAvgMonthlySearchVolume(Integer avgMonthlySearchVolume) {
		this.avgMonthlySearchVolume = avgMonthlySearchVolume;
	}

	public Integer getMonthlySearchVolume1() {
		return monthlySearchVolume1;
	}

	public void setMonthlySearchVolume1(Integer monthlySearchVolume1) {
		this.monthlySearchVolume1 = monthlySearchVolume1;
	}

	public Integer getMonthlySearchVolume2() {
		return monthlySearchVolume2;
	}

	public void setMonthlySearchVolume2(Integer monthlySearchVolume2) {
		this.monthlySearchVolume2 = monthlySearchVolume2;
	}

	public Integer getMonthlySearchVolume3() {
		return monthlySearchVolume3;
	}

	public void setMonthlySearchVolume3(Integer monthlySearchVolume3) {
		this.monthlySearchVolume3 = monthlySearchVolume3;
	}

	public Integer getMonthlySearchVolume4() {
		return monthlySearchVolume4;
	}

	public void setMonthlySearchVolume4(Integer monthlySearchVolume4) {
		this.monthlySearchVolume4 = monthlySearchVolume4;
	}

	public Integer getMonthlySearchVolume5() {
		return monthlySearchVolume5;
	}

	public void setMonthlySearchVolume5(Integer monthlySearchVolume5) {
		this.monthlySearchVolume5 = monthlySearchVolume5;
	}

	public Integer getMonthlySearchVolume6() {
		return monthlySearchVolume6;
	}

	public void setMonthlySearchVolume6(Integer monthlySearchVolume6) {
		this.monthlySearchVolume6 = monthlySearchVolume6;
	}

	public Integer getMonthlySearchVolume7() {
		return monthlySearchVolume7;
	}

	public void setMonthlySearchVolume7(Integer monthlySearchVolume7) {
		this.monthlySearchVolume7 = monthlySearchVolume7;
	}

	public Integer getMonthlySearchVolume8() {
		return monthlySearchVolume8;
	}

	public void setMonthlySearchVolume8(Integer monthlySearchVolume8) {
		this.monthlySearchVolume8 = monthlySearchVolume8;
	}

	public Integer getMonthlySearchVolume9() {
		return monthlySearchVolume9;
	}

	public void setMonthlySearchVolume9(Integer monthlySearchVolume9) {
		this.monthlySearchVolume9 = monthlySearchVolume9;
	}

	public Integer getMonthlySearchVolume10() {
		return monthlySearchVolume10;
	}

	public void setMonthlySearchVolume10(Integer monthlySearchVolume10) {
		this.monthlySearchVolume10 = monthlySearchVolume10;
	}

	public Integer getMonthlySearchVolume11() {
		return monthlySearchVolume11;
	}

	public void setMonthlySearchVolume11(Integer monthlySearchVolume11) {
		this.monthlySearchVolume11 = monthlySearchVolume11;
	}

	public Integer getMonthlySearchVolume12() {
		return monthlySearchVolume12;
	}

	public void setMonthlySearchVolume12(Integer monthlySearchVolume12) {
		this.monthlySearchVolume12 = monthlySearchVolume12;
	}

	public Integer getStartMonth() {
		return startMonth;
	}

	public void setStartMonth(Integer startMonth) {
		this.startMonth = startMonth;
	}

	public Integer getEndMonth() {
		return endMonth;
	}

	public void setEndMonth(Integer endMonth) {
		this.endMonth = endMonth;
	}

	public Integer getMonthlyRelationshipCreateDate() {
		return monthlyRelationshipCreateDate;
	}

	public void setMonthlyRelationshipCreateDate(Integer monthlyRelationshipCreateDate) {
		this.monthlyRelationshipCreateDate = monthlyRelationshipCreateDate;
	}

	public Double getCompetition() {
		return competition;
	}

	public void setCompetition(Double competition) {
		this.competition = competition;
	}

	public Double getCostPerClick() {
		return costPerClick;
	}

	public void setCostPerClick(Double costPerClick) {
		this.costPerClick = costPerClick;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Integer getUploadInfoId() {
		return uploadInfoId;
	}

	public void setUploadInfoId(Integer uploadInfoId) {
		this.uploadInfoId = uploadInfoId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getOperationType() {
		return operationType;
	}

	public void setOperationType(Integer operationType) {
		this.operationType = operationType;
	}

	public Integer getLog_date() {
		return log_date;
	}

	public void setLog_date(Integer log_date) {
		this.log_date = log_date;
	}

	public Date getCreated_at() {
		return created_at;
	}

	public void setCreated_at(Date created_at) {
		this.created_at = created_at;
	}
}