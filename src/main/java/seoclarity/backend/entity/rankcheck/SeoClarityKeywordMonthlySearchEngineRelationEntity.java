package seoclarity.backend.entity.rankcheck;

/**
 * <AUTHOR>
 * @date 2018-09-13
 *       seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity
 * 
 */
public class SeoClarityKeywordMonthlySearchEngineRelationEntity {

	public static String DEVICE_DESKTOP = "d";
	public static String DEVICE_MOBILE = "m";

	private int id;

	private int keywordId;
	private int searchLanguageId;
	private int searchEngineId;
	private String device;
	private Integer createDate;

	private String keywordText;
	private Integer frequency;

	private Integer startMonth;
	private Integer endMonth;
	private Integer avgMonthlySearchVolume;
	private Integer monthlySearchVolume1;
	private Integer monthlySearchVolume2;
	private Integer monthlySearchVolume3;
	private Integer monthlySearchVolume4;
	private Integer monthlySearchVolume5;
	private Integer monthlySearchVolume6;
	private Integer monthlySearchVolume7;
	private Integer monthlySearchVolume8;
	private Integer monthlySearchVolume9;
	private Integer monthlySearchVolume10;
	private Integer monthlySearchVolume11;
	private Integer monthlySearchVolume12;
	private Double costPerClick;

	public Integer getFrequency() {
		return frequency;
	}

	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(int keywordId) {
		this.keywordId = keywordId;
	}

	public int getSearchLanguageId() {
		return searchLanguageId;
	}

	public void setSearchLanguageId(int searchLanguageId) {
		this.searchLanguageId = searchLanguageId;
	}

	public int getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(int searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public Integer getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Integer createDate) {
		this.createDate = createDate;
	}

	public String getKeywordText() {
		return keywordText;
	}

	public void setKeywordText(String keywordText) {
		this.keywordText = keywordText;
	}

	public Integer getStartMonth() {
		return startMonth;
	}

	public void setStartMonth(Integer startMonth) {
		this.startMonth = startMonth;
	}

	public Integer getEndMonth() {
		return endMonth;
	}

	public void setEndMonth(Integer endMonth) {
		this.endMonth = endMonth;
	}

	public Integer getAvgMonthlySearchVolume() {
		return avgMonthlySearchVolume;
	}

	public void setAvgMonthlySearchVolume(Integer avgMonthlySearchVolume) {
		this.avgMonthlySearchVolume = avgMonthlySearchVolume;
	}

	public Integer getMonthlySearchVolume1() {
		return monthlySearchVolume1;
	}

	public void setMonthlySearchVolume1(Integer monthlySearchVolume1) {
		this.monthlySearchVolume1 = monthlySearchVolume1;
	}

	public Integer getMonthlySearchVolume2() {
		return monthlySearchVolume2;
	}

	public void setMonthlySearchVolume2(Integer monthlySearchVolume2) {
		this.monthlySearchVolume2 = monthlySearchVolume2;
	}

	public Integer getMonthlySearchVolume3() {
		return monthlySearchVolume3;
	}

	public void setMonthlySearchVolume3(Integer monthlySearchVolume3) {
		this.monthlySearchVolume3 = monthlySearchVolume3;
	}

	public Integer getMonthlySearchVolume4() {
		return monthlySearchVolume4;
	}

	public void setMonthlySearchVolume4(Integer monthlySearchVolume4) {
		this.monthlySearchVolume4 = monthlySearchVolume4;
	}

	public Integer getMonthlySearchVolume5() {
		return monthlySearchVolume5;
	}

	public void setMonthlySearchVolume5(Integer monthlySearchVolume5) {
		this.monthlySearchVolume5 = monthlySearchVolume5;
	}

	public Integer getMonthlySearchVolume6() {
		return monthlySearchVolume6;
	}

	public void setMonthlySearchVolume6(Integer monthlySearchVolume6) {
		this.monthlySearchVolume6 = monthlySearchVolume6;
	}

	public Integer getMonthlySearchVolume7() {
		return monthlySearchVolume7;
	}

	public void setMonthlySearchVolume7(Integer monthlySearchVolume7) {
		this.monthlySearchVolume7 = monthlySearchVolume7;
	}

	public Integer getMonthlySearchVolume8() {
		return monthlySearchVolume8;
	}

	public void setMonthlySearchVolume8(Integer monthlySearchVolume8) {
		this.monthlySearchVolume8 = monthlySearchVolume8;
	}

	public Integer getMonthlySearchVolume9() {
		return monthlySearchVolume9;
	}

	public void setMonthlySearchVolume9(Integer monthlySearchVolume9) {
		this.monthlySearchVolume9 = monthlySearchVolume9;
	}

	public Integer getMonthlySearchVolume10() {
		return monthlySearchVolume10;
	}

	public void setMonthlySearchVolume10(Integer monthlySearchVolume10) {
		this.monthlySearchVolume10 = monthlySearchVolume10;
	}

	public Integer getMonthlySearchVolume11() {
		return monthlySearchVolume11;
	}

	public void setMonthlySearchVolume11(Integer monthlySearchVolume11) {
		this.monthlySearchVolume11 = monthlySearchVolume11;
	}

	public Integer getMonthlySearchVolume12() {
		return monthlySearchVolume12;
	}

	public void setMonthlySearchVolume12(Integer monthlySearchVolume12) {
		this.monthlySearchVolume12 = monthlySearchVolume12;
	}

	public Double getCostPerClick() {
		return costPerClick;
	}

	public void setCostPerClick(Double costPerClick) {
		this.costPerClick = costPerClick;
	}
}
