package seoclarity.backend.entity.rankcheck.retrievesv;

import java.util.Date;

/**
 * @program: backend
 * @description: keyword_adwords_expanded_log
 * @packagename: seoclarity.backend.dao.rankcheck
 * @author: cil
 * @date: 2021-06-21 15:19
 **/
public class KeywordAdwordsExpandedLogEntity {
private Long id;
private Long adwordsId;
    private Integer searchEngineId;
    private Integer languageId;
    private Integer keywordId;
    private Integer cityId;
    private Integer month;
    private Integer avgMonthlySearchVolume;
    private Double costPerClick;
    private Integer monthlyRelationshipCreateDate;
    private Date createDate;
    private Integer operationType;
    private Integer logDate;
    private Date logAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAdwordsId() {
        return adwordsId;
    }

    public void setAdwordsId(Long adwordsId) {
        this.adwordsId = adwordsId;
    }

    public Integer getSearchEngineId() {
        return searchEngineId;
    }

    public void setSearchEngineId(Integer searchEngineId) {
        this.searchEngineId = searchEngineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public Integer getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(Integer keywordId) {
        this.keywordId = keywordId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getAvgMonthlySearchVolume() {
        return avgMonthlySearchVolume;
    }

    public void setAvgMonthlySearchVolume(Integer avgMonthlySearchVolume) {
        this.avgMonthlySearchVolume = avgMonthlySearchVolume;
    }

    public Double getCostPerClick() {
        return costPerClick;
    }

    public void setCostPerClick(Double costPerClick) {
        this.costPerClick = costPerClick;
    }

    public Integer getMonthlyRelationshipCreateDate() {
        return monthlyRelationshipCreateDate;
    }

    public void setMonthlyRelationshipCreateDate(Integer monthlyRelationshipCreateDate) {
        this.monthlyRelationshipCreateDate = monthlyRelationshipCreateDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getLogDate() {
        return logDate;
    }

    public void setLogDate(Integer logDate) {
        this.logDate = logDate;
    }

    public Date getLogAt() {
        return logAt;
    }

    public void setLogAt(Date logAt) {
        this.logAt = logAt;
    }
}
