package seoclarity.backend.entity.rankcheck.retrievesv;

import java.util.Date;

public class SvRetrieveStateEntity {

    public static int KEYWORD_TYPE_NATIONAL = 1;
    public static int KEYWORD_TYPE_GEO = 2;

    public static int STATUS_RETRIEVED_WITH_RESULT = 2;
    public static int STATUS_SKIP_FOR_NO_RESULT = 3;
    public static int KEYWORD_BAD_KEYWORD = 4;

    private Integer id;
    private Integer refreshDate;
    private Integer KeywordType;
    private Integer languageId;
    private Integer keywordId;
    private Integer cityId;
    private Integer status;
    private Date createDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRefreshDate() {
        return refreshDate;
    }

    public void setRefreshDate(Integer refreshDate) {
        this.refreshDate = refreshDate;
    }

    public Integer getKeywordType() {
        return KeywordType;
    }

    public void setKeywordType(Integer keywordType) {
        KeywordType = keywordType;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public Integer getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(Integer keywordId) {
        this.keywordId = keywordId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
