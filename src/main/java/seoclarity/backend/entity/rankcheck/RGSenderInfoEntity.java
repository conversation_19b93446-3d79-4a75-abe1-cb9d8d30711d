package seoclarity.backend.entity.rankcheck;

/**
 * <AUTHOR>
 * @date 2024-01-02
 * @path seoclarity.backend.entity.rankcheck.RGSenderInfoEntity
 */
public class RGSenderInfoEntity {
    private int frequency;
    private int engineId;
    private int languageId;
    private String countryName;
    private String languageName;
    private String countryLanguageName;

    public int getFrequency() {
        return frequency;
    }

    public void setFrequency(int frequency) {
        this.frequency = frequency;
    }

    public int getEngineId() {
        return engineId;
    }

    public void setEngineId(int engineId) {
        this.engineId = engineId;
    }

    public int getLanguageId() {
        return languageId;
    }

    public void setLanguageId(int languageId) {
        this.languageId = languageId;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getLanguageName() {
        return languageName;
    }

    public void setLanguageName(String languageName) {
        this.languageName = languageName;
    }

    public String getCountryLanguageName() {
        return countryLanguageName;
    }

    public void setCountryLanguageName(String countryLanguageName) {
        this.countryLanguageName = countryLanguageName;
    }
}
