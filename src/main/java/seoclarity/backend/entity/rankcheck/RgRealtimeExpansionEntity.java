package seoclarity.backend.entity.rankcheck;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RgRealtimeExpansionEntity {

    public static final int STATUS_PROCESSING = 1;
    public static final int STATUS_PROCESS_SUCCESS = 2;
    public static final int STATUS_PROCESS_ERROR = 3;

    public static final int MUTUAL_STATUS_CLEANING_UP = 11;
    public static final int MUTUAL_STATUS_CLEAN_UP_SUCCESS = 12;
    public static final int MUTUAL_STATUS_CLEAN_UP_ERROR = 13;
    public static final int MUTUAL_STATUS_SAVE_TO_RANK_CHECK = 21;
    public static final int MUTUAL_STATUS_SAVE_RANK_CHECK_SUCCESS = 22;
    public static final int MUTUAL_STATUS_SAVE_RANK_CHECK_ERROR = 23;
    public static final int MUTUAL_STATUS_SEND_TO_SQS = 31;
    public static final int MUTUAL_STATUS_SEND_TO_SQS_SUCCESS = 32;
    public static final int MUTUAL_STATUS_SEND_TO_SQS_ERROR = 33;

    public static final int TYPE_UNKNOWN = 0;
    public static final int TYPE_UI = 1;
    public static final int TYPE_PAID = 2;

    private Integer id;
    private Integer sourceType; // 1: UI/API, 2: paidAPI
    private Long cacheBodyId;
    private Integer engineId;
    private Integer languageId;
    private String countryCd;
    private Integer status;
    private Integer mutualStatus;
    private Integer createDate;
    private Integer createUserId;
    private Date updateDate;
    private Date createdAt;
}
