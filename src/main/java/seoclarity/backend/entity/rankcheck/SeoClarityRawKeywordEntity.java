package seoclarity.backend.entity.rankcheck;

import java.util.Date;

public class SeoClarityRawKeywordEntity {
	private int id;
	private Integer keywordId;
	private String keywordName;
	private String rawKeywordName;
	private String cdbKeywordHash; //URLHash(lower(rawKeywordName))
	private String cdbKeywordMurmur3hash; //murmurHash3_64(lower(rawKeywordName))
	private Date createDate;

	public SeoClarityRawKeywordEntity() {
	}

	public SeoClarityRawKeywordEntity(Integer keywordId, String rawKeywordName, String cdbKeywordHash, String cdbKeywordMurmur3hash) {
		this.keywordId = keywordId;
		this.rawKeywordName = rawKeywordName;
		this.cdbKeywordHash = cdbKeywordHash;
		this.cdbKeywordMurmur3hash = cdbKeywordMurmur3hash;
	}

	public SeoClarityRawKeywordEntity(Integer keywordId, String keywordName, String rawKeywordName,
                                      String cdbKeywordHash, String cdbKeywordMurmur3hash) {
		this.keywordId = keywordId;
		this.keywordName = keywordName;
		this.rawKeywordName = rawKeywordName;
		this.cdbKeywordHash = cdbKeywordHash;
		this.cdbKeywordMurmur3hash = cdbKeywordMurmur3hash;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public Integer getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(Integer keywordId) {
		this.keywordId = keywordId;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public String getRawKeywordName() {
		return rawKeywordName;
	}

	public void setRawKeywordName(String rawKeywordName) {
		this.rawKeywordName = rawKeywordName;
	}

	public String getCdbKeywordHash() {
		return cdbKeywordHash;
	}

	public void setCdbKeywordHash(String cdbKeywordHash) {
		this.cdbKeywordHash = cdbKeywordHash;
	}

	public String getCdbKeywordMurmur3hash() {
		return cdbKeywordMurmur3hash;
	}

	public void setCdbKeywordMurmur3hash(String cdbKeywordMurmur3hash) {
		this.cdbKeywordMurmur3hash = cdbKeywordMurmur3hash;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
}
