package seoclarity.backend.entity.rankcheck;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigInteger;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RgRealtimeExpansionKeywordEntity {

    public static final int STATE_SKIP_FOR_CLEAN_UP = 10;
    public static final int STATE_SKIP_FOR_EXISTING_RG = 11;
    public static final int STATE_SKIP_FOR_AB_BA = 12;
    public static final int STATE_CANDIDATE_RG_KEYWORD = 20;
    public static final int STATE_DID_YOU_MEAN = 30;

    public static final int UPLOAD_STATUS_SUCCESS = 2;

    public static final int EXPLORER_TOPIC_NOT_PROCESS = 0;
    public static final int EXPLORER_TOPIC_SUCCESS = 2;


    private Integer id;
    private Integer expansionId;
    private Integer seedKeywordId;
    private Integer engineId;
    private Integer languageId;
    private Integer frequency;
    private String keywordName;
    private String keywordBeforeCleanup;
    private Long keywordId;
    private String cdbKeywordMurmur3hash;
    private Integer state;
    private int uploadStatus;
    private int syncToTopicExplorerStatus;
    private Date updateDate;
    private Date createdAt;

    private String decodeKeywordName;

}
