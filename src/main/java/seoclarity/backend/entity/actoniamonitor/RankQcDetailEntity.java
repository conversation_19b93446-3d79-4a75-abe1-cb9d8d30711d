package seoclarity.backend.entity.actoniamonitor;


import java.util.Date;

public class RankQcDetailEntity {

    public static Integer STATUS_NOT_STARTED = 0;
    public static Integer STATUS_PROCESSING = 1;
    public static Integer STATUS_OK = 2;
    public static Integer STATUS_ERROR = 3;


    private Integer id;
    private Integer infoId;
    private Integer ownDomainId;
    private Integer engineId;
    private Integer languageId;
    private String device;
    private Integer status;
    private Integer keywordCount;
    private Integer rankedCount;
    private Integer monitorCount;
    private Integer startProcessDate;
    private Date createDate;
    private Date updateDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInfoId() {
        return infoId;
    }

    public void setInfoId(Integer infoId) {
        this.infoId = infoId;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getEngineId() {
        return engineId;
    }

    public void setEngineId(Integer engineId) {
        this.engineId = engineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getKeywordCount() {
        return keywordCount;
    }

    public void setKeywordCount(Integer keywordCount) {
        this.keywordCount = keywordCount;
    }

    public Integer getRankedCount() {
        return rankedCount;
    }

    public void setRankedCount(Integer rankedCount) {
        this.rankedCount = rankedCount;
    }

    public Integer getMonitorCount() {
        return monitorCount;
    }

    public void setMonitorCount(Integer monitorCount) {
        this.monitorCount = monitorCount;
    }

    public Integer getStartProcessDate() {
        return startProcessDate;
    }

    public void setStartProcessDate(Integer startProcessDate) {
        this.startProcessDate = startProcessDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
