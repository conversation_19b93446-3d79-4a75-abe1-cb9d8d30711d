package seoclarity.backend.entity.actoniamonitor;

public class VsUploadMonitorEntity {

	private Integer id;
	private Integer ownDomainId;
	private Integer keywordType;
	private Integer engineId;
	private Integer languageId;
	private Integer device;
	private Integer cityId;
	private Integer keywordRankcheckId;
	private Integer rankDate;
	private Integer createDate;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getKeywordType() {
		return keywordType;
	}

	public void setKeywordType(Integer keywordType) {
		this.keywordType = keywordType;
	}

	public Integer getEngineId() {
		return engineId;
	}

	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public Integer getDevice() {
		return device;
	}

	public void setDevice(Integer device) {
		this.device = device;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public Integer getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(Integer keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public Integer getRankDate() {
		return rankDate;
	}

	public void setRankDate(Integer rankDate) {
		this.rankDate = rankDate;
	}

	public Integer getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Integer createDate) {
		this.createDate = createDate;
	}

}
