package seoclarity.backend.entity.actoniamonitor;

import java.util.Date;

public class UploadFileMonitorEntity {

    public final static int UPLOAD_TYPE_GSC_CLICK_STEAM = 1;

    public final static int UPLOAD_STATUS_PROCESSING = 1;
    public final static int UPLOAD_STATUS_SUCCESS = 2;
    public final static int UPLOAD_STATUS_ERROR = 3;

    public final static int FREQUENCY_DAILY = 1;
    public final static int FREQUENCY_WEEKLY = 7;

    private Integer id;
    private Integer uploadType;
    private Integer frequency;
    private String fileName;
    private Integer version;
    private Date createDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUploadType() {
        return uploadType;
    }

    public void setUploadType(Integer uploadType) {
        this.uploadType = uploadType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getFrequency() {
        return frequency;
    }

    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }
}
