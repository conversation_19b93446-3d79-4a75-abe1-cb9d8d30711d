package seoclarity.backend.entity.actoniamonitor;

import java.util.List;

public class ContentIdeaKeywordListEntity {

	private String hashId;
	
	private String keyword;
	
	private String searchVol;
	
	private String foundDate;

	public String getHashId() {
		return hashId;
	}

	public void setHashId(String hashId) {
		this.hashId = hashId;
	}

	public String getSearchVol() {
		return searchVol;
	}

	public void setSearchVol(String searchVol) {
		this.searchVol = searchVol;
	}

	public String getFoundDate() {
		return foundDate;
	}

	public void setFoundDate(String foundDate) {
		this.foundDate = foundDate;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	
}
