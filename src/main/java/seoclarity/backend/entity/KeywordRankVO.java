package seoclarity.backend.entity;

import java.io.Serializable;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.math.NumberUtils;

import seoclarity.backend.utils.CommonUtils;


public class KeywordRankVO implements Serializable {
    private static final long serialVersionUID = 1351401185672317490L;

    public static final String COUNTRY_US_EN = "us_en";
    public static final String COUNTRY_CA_EN = "ca_en";
    public static final String COUNTRY_CA_FR = "ca_fr";
    private int keywordDictId;
    private String country;
    private String flightSearchFlg;
    private String commercialFlg;
    private String language;
    private String cityName;
    private Integer cityId;
    private String knogTag;
    private String engine;
    private String keyword;
    private String queryDate;
    private int qeuryState;
    private Integer firstPageSize;
    private String plaFlg;
    private String ppcFlg;
    private String llFlg;
	private String socialInKg;
    private String topPpcCnt;
    private String createDate;
    private Integer id;
    private String appFlg;
    private List<String> relatedSearch;
    private Integer sendToQDate;
    private String researchFlg;
    private String keywordHash;

    private List<KeywordRankEntityVO> keywordRankEntityVOs;
    private List<String> things;
    private List<String> inDepthArt;
    private List<String> localListing;
    private List<KeywordRankLocalListingSubEntityVO> localListingV2;
    private List<SeoKeywordAdsEntity> allAds;

    private String searchVol;
    private String cpc;
    private List<String> domainList;
    private String answerBox;

    private String questions;
    private String additionalQuestions;

    private String label;
    private String authorNm;
    private String authorLink;
    private String lpu;
    private List<String> appList;
    private String googleRecommend;
    private String googleResultCnt;
    private List<String> boldText;
    private String refineBy;
	
    private String jobLink;
    private String category;
    
    private String trueDemand;

	//Leo - google ved
    private List<Map<String, Map<String,String>>> vedMap;

    public Integer getSendToQDate() {
        return sendToQDate;
    }

    public Date getSendToQDateAsDate() {
        if(sendToQDate == null || sendToQDate.intValue() == 0) {
            return CommonUtils.getYesterday(true);
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            return sdf.parse(sendToQDate.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return CommonUtils.getYesterday(true);
    }


    public void setSendToQDate(Integer sendToQDate) {
        this.sendToQDate = sendToQDate;
    }

    public List<String> getRelatedSearch() {
        return relatedSearch;
    }

    public void setRelatedSearch(List<String> relatedSearch) {
        this.relatedSearch = relatedSearch;
    }

    public String getAppFlg() {
        return appFlg;
    }

    public void setAppFlg(String appFlg) {
        this.appFlg = appFlg;
    }

    public Integer getCityId() {
        if (cityId == null) {
            return 0;
        }
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getQuestions() {
        return questions;
    }

    public void setQuestions(String questions) {
        this.questions = questions;
    }

    public String getAnswerBox() {
        return answerBox;
    }

    public void setAnswerBox(String answerBox) {
        this.answerBox = answerBox;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public List<String> getLocalListing() {
        return localListing;
    }

    public void setLocalListing(List<String> localListing) {
        this.localListing = localListing;
    }

    public String getLlFlg() {
        return llFlg;
    }

    public void setLlFlg(String llFlg) {
        this.llFlg = llFlg;
    }

    public String getTopPpcCnt() {
        return topPpcCnt;
    }

    public void setTopPpcCnt(String topPpcCnt) {
        this.topPpcCnt = topPpcCnt;
    }

    public Integer getTopPPCCnt() {
        if (topPpcCnt == null) {
            return 0;
        }
        return NumberUtils.toInt(topPpcCnt);
    }

    public String getPlaFlg() {
        return plaFlg;
    }

    public void setPlaFlg(String plaFlg) {
        this.plaFlg = plaFlg;
    }

    public String getPpcFlg() {
        return ppcFlg;
    }

    public void setPpcFlg(String ppcFlg) {
        this.ppcFlg = ppcFlg;
    }

    public List<String> getInDepthArt() {
        return inDepthArt;
    }

    public void setInDepthArt(List<String> inDepthArt) {
        this.inDepthArt = inDepthArt;
    }

    public List<String> getThings() {
        return things;
    }

    public void setThings(List<String> things) {
        this.things = things;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public int getKeywordDictId() {
        return keywordDictId;
    }

    public void setKeywordDictId(int keywordDictId) {
        this.keywordDictId = keywordDictId;
    }

    public String getCountryAndLanguage() {
        return country + "_" + language;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getKeyword() {
        return keyword;
    }

    public String getKeywordForHtml() {
        try {
            return StringEscapeUtils
                    .unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(keyword, "UTF-8")));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getQueryDate() {
        return queryDate;
    }

    public void setQueryDate(String queryDate) {
        this.queryDate = queryDate;
    }

    public int getQeuryState() {
        return qeuryState;
    }

    public void setQeuryState(int qeuryState) {
        this.qeuryState = qeuryState;
    }

    public List<KeywordRankEntityVO> getKeywordRankEntityVOs() {
        return keywordRankEntityVOs;
    }

    public void setKeywordRankEntityVOs(List<KeywordRankEntityVO> keywordRankEntityVOs) {
        this.keywordRankEntityVOs = keywordRankEntityVOs;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getFirstPageSize() {
        if (firstPageSize == null || firstPageSize.intValue() == 0) {
            return 0;
        }
        return firstPageSize;
    }

    public void setFirstPageSize(Integer firstPageSize) {
        this.firstPageSize = firstPageSize;
    }

    public String getKnogTag() {
        return knogTag;
    }

    public void setKnogTag(String knogTag) {
        this.knogTag = knogTag;
    }

    public String getCreateDate() {
        if (createDate == null) {
            return "0";
        }
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public List<SeoKeywordAdsEntity> getAllAds() {
        return allAds;
    }

    public void setAllAds(List<SeoKeywordAdsEntity> allAds) {
        this.allAds = allAds;
    }



    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getAuthorNm() {
        return authorNm;
    }

    public void setAuthorNm(String authorNm) {
        this.authorNm = authorNm;
    }

    public String getAuthorLink() {
        return authorLink;
    }

    public void setAuthorLink(String authorLink) {
        this.authorLink = authorLink;
    }

    public String getLpu() {
        return lpu;
    }

    public void setLpu(String lpu) {
        this.lpu = lpu;
    }

    public List<String> getAppList() {
        return appList;
    }

    public void setAppList(List<String> appList) {
        this.appList = appList;
    }

    public String getGoogleRecommend() {
        return googleRecommend;
    }

    public void setGoogleRecommend(String googleRecommend) {
        this.googleRecommend = googleRecommend;
    }

    public String getGoogleResultCnt() {
        return googleResultCnt;
    }

    public void setGoogleResultCnt(String googleResultCnt) {
        this.googleResultCnt = googleResultCnt;
    }

    public List<String> getBoldText() {
        return boldText;
    }

    public void setBoldText(List<String> boldText) {
        this.boldText = boldText;
    }

    public String getSearchVol() {
        return searchVol;
    }

    public void setSearchVol(String searchVol) {
        this.searchVol = searchVol;
    }

    public String getCpc() {
        return cpc;
    }

    public void setCpc(String cpc) {
        this.cpc = cpc;
    }

    public List<String> getDomainList() {
        return domainList;
    }

    public void setDomainList(List<String> domainList) {
        this.domainList = domainList;
    }

    @Override
    public String toString() {
        return "KeywordRankVO [keywordDictId=" + keywordDictId + ", country=" + country + ", language=" + language
                + ", cityName=" + cityName + ", cityId=" + cityId + ", knogTag=" + knogTag + ", engine=" + engine
                + ", keyword=" + keyword + ", queryDate=" + queryDate + ", qeuryState=" + qeuryState
                + ", firstPageSize=" + firstPageSize + ", plaFlg=" + plaFlg + ", ppcFlg=" + ppcFlg + ", llFlg=" + llFlg
                + ", topPpcCnt=" + topPpcCnt + ", createDate=" + createDate + ", id=" + id + ", appFlg=" + appFlg
                + ", relatedSearch=" + relatedSearch + ", sendToQDate=" + sendToQDate + ", keywordRankEntityVOs="
                + keywordRankEntityVOs + ", things=" + things + ", inDepthArt=" + inDepthArt + ", localListing="
                + localListing + ", allAds=" + allAds + ", searchVol=" + searchVol + ", cpc=" + cpc + ", domainList="
                + domainList + ", answerBox=" + answerBox + ", questions=" + questions + "]";
    }

    
    public String getKeywordHash() {
		return keywordHash;
	}

	public void setKeywordHash(String keywordHash) {
		this.keywordHash = keywordHash;
	}

	public String getJobLink() {
        return jobLink;
    }

    public void setJobLink(String jobLink) {
        this.jobLink = jobLink;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<Map<String, Map<String,String>>> getVedMap() {
        return vedMap;
    }

    public void setVedMap(List<Map<String, Map<String,String>>> vedMap) {
        this.vedMap = vedMap;
    }

    public String getFlightSearchFlg() {
        return flightSearchFlg;
    }

    public void setFlightSearchFlg(String flightSearchFlg) {
        this.flightSearchFlg = flightSearchFlg;
    }

    public String getCommercialFlg() {
        return commercialFlg;
    }

    public void setCommercialFlg(String commercialFlg) {
        this.commercialFlg = commercialFlg;
    }

    public String getAdditionalQuestions() {
        return additionalQuestions;
    }

    public void setAdditionalQuestions(String additionalQuestions) {
        this.additionalQuestions = additionalQuestions;
    }

	public String getSocialInKg() {
		return socialInKg;
	}

	public void setSocialInKg(String socialInKg) {
		this.socialInKg = socialInKg;
	}

	public String getResearchFlg() {
		return researchFlg;
	}

	public void setResearchFlg(String researchFlg) {
		this.researchFlg = researchFlg;
	}

	public String getRefineBy() {
		return refineBy;
	}

	public void setRefineBy(String refineBy) {
		this.refineBy = refineBy;
	}

	public List<KeywordRankLocalListingSubEntityVO> getLocalListingV2() {
		return localListingV2;
	}

	public void setLocalListingV2(List<KeywordRankLocalListingSubEntityVO> localListingV2) {
		this.localListingV2 = localListingV2;
	}
	
    public String getTrueDemand() {
		return trueDemand;
	}

	public void setTrueDemand(String trueDemand) {
		this.trueDemand = trueDemand;
	}
}
