package seoclarity.backend.entity.mongo;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.utils.FormatUtils;

public class SubLinksPageEntity {
	public static final int TYPE_WEB_RESOURCE = 1;

	public static final int TYPE_IMGAGE = 2;

	public static final int TYPE_ADDRESS = 3;

	public static final int TYPE_VIDEO = 4;

	public static final int TYPE_NEWS = 5;

	public static final int TYPE_SHOPPING = 6;
	
    public static final int TYPE_LOCALLISTING = 7;

	public static final String PROTOCOL_HTTP = "0";

	public static final String PROTOCOL_HTTPS = "1";

	private static final String PROTOCOL_HTTP_URL = "http://";

	private static final String PROTOCOL_HTTPS_URL = "https://";

	private String domainReverse;

	private String uri;

	private String protocol;

	private String rank;
	
	private String subRank;

	public String getDomainReverse() {
		return domainReverse;
	}

	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}

	public String getRank() {
		return rank;
	}

	public void setRank(String rank) {
		this.rank = rank;
	}

	public String getSubRank() {
		return subRank;
	}

	public void setSubRank(String subRank) {
		this.subRank = subRank;
	}

	public String getLandingPage() {
		String landingPage = "";
		if (StringUtils.isBlank(landingPage)) {

			String domain = FormatUtils.reverseDomainNameByDot(domainReverse);

			if (protocol == null || protocol.equals("0")) {
				landingPage = PROTOCOL_HTTP_URL + domain + getUri();
			} else {
				landingPage = PROTOCOL_HTTPS_URL + domain + getUri();
			}
		}
		return landingPage;
	}

	public String getDomainName() {
		if (StringUtils.isBlank(domainReverse)) {
			return "";
		}
		String domainname = StringUtils.reverseDelimited(domainReverse, '.');
		return domainname;
	}
}
