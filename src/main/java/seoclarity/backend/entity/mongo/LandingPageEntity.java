package seoclarity.backend.entity.mongo;

import java.beans.Transient;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import seoclarity.backend.utils.FormatUtils;

public class LandingPageEntity {
    private static final String PROTOCOL_HTTP = "http://";

    private static final String PROTOCOL_HTTPS = "https://";

    private String domainReverse;
    private String uri;
    private String rank;
    private String protocol;
    private String type;
    private Date queryDate;
    private String authorNm;
    private String authorLink;
    private String rating;
    private String ratingnumber;
    private CarouselsEntity[] carouselsEntities;
    private String label;
    private Integer firstPageSize;
    private String title;
    private String mf;
    private String amp;
    
    private String stock;
    private String price;
    private String add;
    
    //https://www.wrike.com/open.htm?id=237720895
    //by sunny
    private int webRank;
    @Transient
	public int getWebRank() {
		return webRank;
	}

    @Transient
	public void setWebRank(int webRank) {
		this.webRank = webRank;
	}
    
    
	public String getMf() {
		return mf;
	}

	public void setMf(String mf) {
		this.mf = mf;
	}

	// https://www.wrike.com/open.htm?id=40370991
	private LocalListingEntity[] locallistingEntities;

	public LocalListingEntity[] getLocallistingEntities() {
		return locallistingEntities;
	}

	public void setLocallistingEntities(LocalListingEntity[] locallistingEntities) {
		this.locallistingEntities = locallistingEntities;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public Integer getFirstPageSize() {
        return firstPageSize;
    }

    public void setFirstPageSize(Integer firstPageSize) {
        this.firstPageSize = firstPageSize;
    }

    public String getAuthorNm() {
        return authorNm;
    }

    public void setAuthorNm(String authorNm) {
        this.authorNm = authorNm;
    }

    public String getAuthorLink() {
        return authorLink;
    }

    public void setAuthorLink(String authorLink) {
        this.authorLink = authorLink;
    }

    public String getRating() {
        return rating;
    }

    public void setRating(String rating) {
        this.rating = rating;
    }

    private List<SubLinksPageEntity> subRank;

    public List<SubLinksPageEntity> getSubRank() {
        return subRank;
    }

    public void setSubRank(List<SubLinksPageEntity> subRank) {
        this.subRank = subRank;
    }

    public String getDomainReverse() {
        return domainReverse;
    }

    public void setDomainReverse(String domainReverse) {
        this.domainReverse = domainReverse;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "LandingPageEntity [domainReverse=" + domainReverse + ", protocol=" + protocol + ", rank=" + rank + ", type=" + type
                + ", uri=" + uri + ", rating=" + rating + "]";
    }
    
    public String getProtocolString() {
        if (protocol == null || protocol.equals("0")) {
        	return "http";
        }
        return "https";
    }

    public String getLandingPage() {
        String landingPage = "";
        if (StringUtils.isBlank(landingPage)) {

            String domain = FormatUtils.reverseDomainNameByDot(domainReverse);

            if (protocol == null || protocol.equals("0")) {
                landingPage = PROTOCOL_HTTP + domain + getUri();
            } else {
                landingPage = PROTOCOL_HTTPS + domain + getUri();
            }
        }
        return landingPage;
    }

    public String getDomainName() {
        if (StringUtils.isBlank(domainReverse)) {
            return "";
        }
        String domainname = StringUtils.reverseDelimited(domainReverse, '.');
        return domainname;
    }

    public Date getQueryDate() {
        return queryDate;
    }

    public void setQueryDate(Date queryDate) {
        this.queryDate = queryDate;
    }

    public Integer getRankNumber() {
        return NumberUtils.toInt(rank);
    }

    public String getUrlType() {
        if (StringUtils.isBlank(type)) {
            return "Web";
        }
        int urlType = NumberUtils.toInt(type);
        // maps.google.it/
        if (urlType == SubLinksPageEntity.TYPE_ADDRESS || StringUtils.containsIgnoreCase(getLandingPage(), "maps.google.com")
                || StringUtils.containsIgnoreCase(getLandingPage(), "maps.google.")) {
            return "Local";
        } else if (urlType == SubLinksPageEntity.TYPE_IMGAGE || StringUtils.containsIgnoreCase(getLandingPage(), "images.google.com")
                || StringUtils.containsIgnoreCase(getLandingPage(), "images.google.")) {
            return "Image";
        } else if (urlType == SubLinksPageEntity.TYPE_NEWS || StringUtils.containsIgnoreCase(getLandingPage(), "news.google.com")
                || StringUtils.containsIgnoreCase(getLandingPage(), "news.google.")) {
            return "News";
        } else if (urlType == SubLinksPageEntity.TYPE_SHOPPING
                || StringUtils.containsIgnoreCase(getLandingPage(), "www.google.com/shopping")) {
            return "Shopping";
        } else if (urlType == SubLinksPageEntity.TYPE_VIDEO) {
            return "Video";
        } else if (urlType == SubLinksPageEntity.TYPE_LOCALLISTING
				|| (StringUtils.containsIgnoreCase(getLandingPage(), "www.google.") && StringUtils.containsIgnoreCase(
						getLandingPage(), "tbm=lcl"))) {
			return "LocalListing";
		} else if (StringUtils.containsIgnoreCase(getLandingPage(), "www.google.com")
                || StringUtils.containsIgnoreCase(getLandingPage(), "www.google.")) {
            return "Universal";
        }

        return "Web";
    }

    public int getUrlTypeForStore() {
        if (StringUtils.isBlank(type)) {
            return SubLinksPageEntity.TYPE_WEB_RESOURCE;
        }
        int urlType = NumberUtils.toInt(type);
        if (urlType == SubLinksPageEntity.TYPE_ADDRESS || StringUtils.containsIgnoreCase(getLandingPage(), "maps.google.com")) {
            return SubLinksPageEntity.TYPE_ADDRESS;
        } else if (urlType == SubLinksPageEntity.TYPE_IMGAGE || StringUtils.containsIgnoreCase(getLandingPage(), "images.google.com")) {
            return SubLinksPageEntity.TYPE_IMGAGE;
        } else if (urlType == SubLinksPageEntity.TYPE_NEWS || StringUtils.containsIgnoreCase(getLandingPage(), "news.google.com")) {
            return SubLinksPageEntity.TYPE_NEWS;
        } else if (urlType == SubLinksPageEntity.TYPE_SHOPPING
                || StringUtils.containsIgnoreCase(getLandingPage(), "www.google.com/shopping")) {
            return SubLinksPageEntity.TYPE_SHOPPING;
        } else if (urlType == SubLinksPageEntity.TYPE_VIDEO) {
            return SubLinksPageEntity.TYPE_VIDEO;
        }

        return SubLinksPageEntity.TYPE_WEB_RESOURCE;
    }

	public CarouselsEntity[] getCarouselsEntities() {
		return carouselsEntities;
	}

	public void setCarouselsEntities(CarouselsEntity[] carouselsEntities) {
		this.carouselsEntities = carouselsEntities;
	}
	
	public boolean isEqual(String domainReverseParam) {
		return StringUtils.equalsIgnoreCase(domainReverse, domainReverseParam);
	}
	
	public boolean isLike(String domainReverseParam) {
		String domainReverseParamSubDomain = domainReverseParam + ".";
		if (StringUtils.equalsIgnoreCase(domainReverse, domainReverseParam)) {
			return true;
		}
		return StringUtils.startsWithIgnoreCase(domainReverse, domainReverseParamSubDomain);
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}
	
    public String getRatingnumber() {
		return ratingnumber;
	}

	public void setRatingnumber(String ratingnumber) {
		this.ratingnumber = ratingnumber;
	}

	public String getAmp() {
		return amp;
	}

	public void setAmp(String amp) {
		this.amp = amp;
	}

	public String getStock() {
		return stock;
	}

	public void setStock(String stock) {
		this.stock = stock;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getDomainPage() {
        String landingPage = "";
        if (StringUtils.isBlank(landingPage)) {

            String domain = FormatUtils.reverseDomainNameByDot(domainReverse);

            if (protocol == null || protocol.equals("0")) {
                landingPage = PROTOCOL_HTTP + domain;
            } else {
                landingPage = PROTOCOL_HTTPS + domain;
            }
        }
        return landingPage;
    }

    public String getProtocolStr() {
        if (protocol == null || protocol.equals("0")) {
            return "http";
        } else {
            return "https";
        }
    }

	public String getAdd() {
		return add;
	}

	public void setAdd(String add) {
		this.add = add;
	}
}
