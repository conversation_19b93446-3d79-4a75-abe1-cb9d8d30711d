package seoclarity.backend.entity.mongo;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;

import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.URLConnectionUtils;

public class KeywordRankMongoEntity {
	private static Set<Integer> hotterSpecialRankingLogicSet = new HashSet<Integer>();

	static {
		hotterSpecialRankingLogicSet.add(1690);
	}

	private String id;
	private Object _id;
	private String kName;
	private Integer count;
	private String date;
	private Integer engine;
	private Integer language;
	private Integer fps;

	private String city;
	private String answerbox;
	private String pla;
	private String ll;
	private String ppc;
	private String tpc;
	private String kong;
	private String questions;
	
	private String appflg;
	private List<String> applist;

	private CarouselsEntity[] carousels;
	private IndepthEntity[] indepth;
	private LocalListingEntity[] locallisting;

	public LocalListingEntity[] getLocallisting() {
		return locallisting;
	}

	public void setLocallisting(LocalListingEntity[] locallisting) {
		this.locallisting = locallisting;
	}

	public CarouselsEntity[] getCarousels() {
		return carousels;
	}

	public void setCarousels(CarouselsEntity[] carousels) {
		this.carousels = carousels;
	}

	public Integer getFps() {
		return fps;
	}

	public void setFps(Integer fps) {
		this.fps = fps;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	private List<LandingPageEntity> childrens;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getkName() {
		return kName;
	}

	public void setkName(String kName) {
		this.kName = kName;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public Integer getEngine() {
		return engine;
	}

	public void setEngine(Integer engine) {
		this.engine = engine;
	}

	public Integer getLanguage() {
		return language;
	}

	public void setLanguage(Integer language) {
		this.language = language;
	}

	public List<LandingPageEntity> getChildrens() {
		return childrens;
	}

	public void setChildrens(List<LandingPageEntity> childrens) {
		this.childrens = childrens;
	}

	@Override
	public String toString() {
		// return "KeywordRankMongoEntity [childrens=" + childrens.toString() +
		// ", date=" + date + ", engine=" + engine + ", id=" + id + ", kName=" +
		// kName
		// + ", language=" + language + ", count=" + count + "]";
		return "kName =" + kName;
	}

	private LandingPageEntity checkForHotterUrl(OwnDomainEntity ownDomain, LandingPageEntity matchedLandingPage) {
		if (StringUtils.startsWithIgnoreCase(matchedLandingPage.getUri(), "/us/")) {
			return matchedLandingPage;
		}
		return null;
	}

    public static LandingPageEntity checkForDellInternationalUrl(OwnDomainEntity ownDomain, LandingPageEntity matchedLandingPage) {
        if (StringUtils.containsIgnoreCase(matchedLandingPage.getDomainName(), "." + ownDomain.getSearchEngineCountry())) {
            return matchedLandingPage;
        } else if (StringUtils.startsWithIgnoreCase(matchedLandingPage.getUri(), "/" + ownDomain.getSearchEngineCountry())) {
            return matchedLandingPage;
        } else if (StringUtils.containsIgnoreCase(matchedLandingPage.getUri(), "c=" + ownDomain.getSearchEngineCountry())) {
            return matchedLandingPage;
        } else if (StringUtils.containsIgnoreCase(matchedLandingPage.getUri(), "/" + ownDomain.getSearchEngineCountry() + "/")) {
        	// https://www.wrike.com/open.htm?id=22529137
            return matchedLandingPage;
        } 
        return null;
    }
    
	public LandingPageEntity getMinRankByDomainName(String domainName, boolean broadMatch) {
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		String reverseDomain = FormatUtils.reverseDomainNameByDot(domainName);

		if (childrens != null && childrens.size() > 0) {
			try {
				for (LandingPageEntity landingPageEntity : childrens) {
					if (broadMatch) {
						if (StringUtils.containsIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain + ".")
								|| StringUtils.equalsIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain)) {
							landingPageEntity.setQueryDate(format.parse(date));

							return landingPageEntity;
						}
					} else {
						if (StringUtils.endsWithIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain)) {
							landingPageEntity.setQueryDate(format.parse(date));
							return landingPageEntity;
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	public int findRankByDomainAndUrl(String domainReverse, String uri, String protocol) {
		int urlRank = 101;
		if (childrens != null && childrens.size() > 0) {
			try {
				for (LandingPageEntity landingPageEntity : childrens) {
					if (StringUtils.containsIgnoreCase(landingPageEntity.getDomainReverse(), domainReverse)
							&& StringUtils.containsIgnoreCase(landingPageEntity.getUri(), uri)
							&& StringUtils.containsIgnoreCase(landingPageEntity.getProtocol(), protocol)) {
						urlRank = landingPageEntity.getRankNumber();
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return urlRank == 0 ? 101 : urlRank;
	}

	public int findMinRankByUrl(String url) {
		String[] array = CommonUtils.splitString(url);
		if (array == null || array.length < 3) {
			return 101;
		}

		if (childrens != null && childrens.size() > 0) {
			for (LandingPageEntity landingPageEntity : childrens) {
				// if
				// (StringUtils.startsWithIgnoreCase(landingPageEntity.getDomainReverse(),
				// array[0])) {
				// System.out.println(landingPageEntity.getDomainReverse()+ " "+
				// array[0]);
				// System.out.println(StringEscapeUtils.unescapeHtml(landingPageEntity.getUri())+
				// " "+StringEscapeUtils.unescapeHtml(array[1]));
				// System.out.println(landingPageEntity.getProtocol()+" "+
				// array[2]);
				// }

				if (StringUtils.startsWithIgnoreCase(landingPageEntity.getDomainReverse(), array[0])
						&& StringUtils.equalsIgnoreCase(StringEscapeUtils.unescapeHtml(landingPageEntity.getUri()),
								StringEscapeUtils.unescapeHtml(array[1]))
						&& StringUtils.equalsIgnoreCase(landingPageEntity.getProtocol(), array[2])) {
					return landingPageEntity.getRankNumber();
				}
			}
		}
		return 101;
	}

	public int findDomainRankValue(String domain, boolean broadMatch) {
		if (childrens == null || childrens.size() == 0) {
			return 101;
		}
		for (LandingPageEntity queryRankEntity : childrens) {
			if (isSameDomain(queryRankEntity.getDomainName(), domain, broadMatch)) {
				return queryRankEntity.getRankNumber();
			}
		}
		return 101;
	}

	public LandingPageEntity findDomainRankEntity(String domain, boolean broadMatch) {
		if (childrens == null || childrens.size() == 0) {
			return null;
		}
		for (LandingPageEntity queryRankEntity : childrens) {
			if (isSameDomain(queryRankEntity.getDomainName(), domain, broadMatch)) {
				return queryRankEntity;
			}
		}
		return null;
	}

	private boolean isSameDomain(String sourceDomain, String ownDomain, boolean broadMatch) {
		sourceDomain = URLConnectionUtils.getDomainByUrl(sourceDomain, broadMatch);
		ownDomain = URLConnectionUtils.getDomainByUrl(ownDomain, broadMatch);
		if (StringUtils.equalsIgnoreCase(sourceDomain, ownDomain)) {
			return true;
		}
		if (broadMatch && StringUtils.endsWithIgnoreCase(sourceDomain, "." + ownDomain)) {
			return true;
		} else if (StringUtils.startsWith(sourceDomain, "www.")) {
			// for Ticket 155
			sourceDomain = URLConnectionUtils.getDomainByUrl(sourceDomain, true);
			ownDomain = URLConnectionUtils.getDomainByUrl(ownDomain, true);
			if (StringUtils.equalsIgnoreCase(ownDomain, sourceDomain)) {
				return true;
			}
		}
		return false;
	}

	public List<LandingPageEntity> geRankByDomainName(OwnDomainEntity ownDomainEntity) {
		List<LandingPageEntity> links = new ArrayList<LandingPageEntity>();

		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		String domain = URLConnectionUtils.getDomainByUrl(ownDomainEntity.getDomain(), ownDomainEntity.isBroadMatch());
		String reverseDomain = FormatUtils.reverseDomainNameByDot(domain);

		if (childrens != null && childrens.size() > 0) {
			try {
				for (LandingPageEntity landingPageEntity : childrens) {
					if (ownDomainEntity.isBroadMatch()) {
						if (StringUtils.containsIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain + ".")
								|| StringUtils.equalsIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain)) {
							landingPageEntity.setQueryDate(format.parse(date));
							links.add(landingPageEntity);
						}
					} else {
						if (StringUtils.endsWithIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain)) {
							landingPageEntity.setQueryDate(format.parse(date));
							links.add(landingPageEntity);
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return links;
	}

	public int findUrlRankValue(String url, boolean broadMatch) {
		if (childrens == null || childrens.size() == 0) {
			return 101;
		}
		url = StringUtils.removeEndIgnoreCase(url, "/");
		for (LandingPageEntity queryRankEntity : childrens) {
			if (StringUtils.containsIgnoreCase(queryRankEntity.getLandingPage(), url)) {
				return queryRankEntity.getRankNumber();
			}
		}
		return 101;
	}

	public List<LandingPageEntity> geRankByDomainNameForTrend(OwnDomainEntity ownDomainEntity) {
		List<LandingPageEntity> links = new ArrayList<LandingPageEntity>();

		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		String domain = URLConnectionUtils.getDomainByUrl(ownDomainEntity.getDomain(), ownDomainEntity.isBroadMatch());
		String reverseDomain = FormatUtils.reverseDomainNameByDot(domain);

		if (childrens != null && childrens.size() > 0) {
			try {
				for (LandingPageEntity landingPageEntity : childrens) {
					if (ownDomainEntity.isBroadMatch()) {
						if (StringUtils.containsIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain + ".")
								|| StringUtils.equalsIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain)) {
							landingPageEntity.setQueryDate(format.parse(date));
							links.add(landingPageEntity);
						}
					} else {
						if (StringUtils.endsWithIgnoreCase(landingPageEntity.getDomainReverse(), reverseDomain)) {
							landingPageEntity.setQueryDate(format.parse(date));
							links.add(landingPageEntity);
						}
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		if (links.size() == 0) {
			LandingPageEntity landingPageEntity = new LandingPageEntity();
			landingPageEntity.setRank("101");

			links.add(landingPageEntity);
		}
		return links;
	}

	public Date getQueryDate() {
		if (StringUtils.isBlank(date)) {
			return null;
		}
		return FormatUtils.toDate(date, "yyyyMMdd");
	}

	public IndepthEntity[] getIndepth() {
		return indepth;
	}

	public void setIndepth(IndepthEntity[] indepth) {
		this.indepth = indepth;
	}

	public Object get_id() {
		return _id;
	}

	public void set_id(Object _id) {
		this._id = _id;
	}

	public String getAnswerbox() {
		return answerbox;
	}

	public void setAnswerbox(String answerbox) {
		this.answerbox = answerbox;
	}

	public String getPla() {
		return pla;
	}

	public void setPla(String pla) {
		this.pla = pla;
	}

	public String getLl() {
		return ll;
	}

	public void setLl(String ll) {
		this.ll = ll;
	}

	public String getPpc() {
		return ppc;
	}

	public void setPpc(String ppc) {
		this.ppc = ppc;
	}

	public String getTpc() {
		return tpc;
	}

	public void setTpc(String tpc) {
		this.tpc = tpc;
	}

	public String getKong() {
		return kong;
	}

	public void setKong(String kong) {
		this.kong = kong;
	}

	public String getQuestions() {
		return questions;
	}

	public void setQuestions(String questions) {
		this.questions = questions;
	}

	public String getAppflg() {
		return appflg;
	}

	public void setAppflg(String appflg) {
		this.appflg = appflg;
	}

	public List<String> getApplist() {
		return applist;
	}

	public void setApplist(List<String> applist) {
		this.applist = applist;
	}

}
