package seoclarity.backend.entity.actonia;

import java.util.Date;

public class UrlInspectionShuffleEntity {
	
	public static final int SEND_STATUS_NOT_STARTED = 0;
	public static final int SEND_STATUS_SENDING = 1;
	public static final int SEND_STATUS_SENT_SUCCESS = 2;
	public static final int SEND_STATUS_SEND_ERROR = 3;
	
	private long id;
	private int frequency;
	private Date periodStartDate;
	private int sendDate;
	private int dataSource;
	private String url;
	private String urlMurmur3Hash;
	private int ownDomainId;
	private int profileId;
	private String profileList;
	private int sendStatus;
	private Date sendTime;
	private Date createDate;
	
	private String gwmDomainName;
	private String formattedProfileName;
	private String gscRefreshToken;
	private String clientId;
	private String encryptSecret;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getFrequency() {
		return frequency;
	}

	public void setFrequency(int frequency) {
		this.frequency = frequency;
	}

	public Date getPeriodStartDate() {
		return periodStartDate;
	}

	public void setPeriodStartDate(Date periodStartDate) {
		this.periodStartDate = periodStartDate;
	}

	public int getSendDate() {
		return sendDate;
	}

	public void setSendDate(int sendDate) {
		this.sendDate = sendDate;
	}

	public int getDataSource() {
		return dataSource;
	}

	public void setDataSource(int dataSource) {
		this.dataSource = dataSource;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getUrlMurmur3Hash() {
		return urlMurmur3Hash;
	}

	public void setUrlMurmur3Hash(String urlMurmur3Hash) {
		this.urlMurmur3Hash = urlMurmur3Hash;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public int getProfileId() {
		return profileId;
	}

	public void setProfileId(int profileId) {
		this.profileId = profileId;
	}

	public String getProfileList() {
		return profileList;
	}

	public void setProfileList(String profileList) {
		this.profileList = profileList;
	}

	public int getSendStatus() {
		return sendStatus;
	}

	public void setSendStatus(int sendStatus) {
		this.sendStatus = sendStatus;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	public String getGwmDomainName() {
		return gwmDomainName;
	}

	public void setGwmDomainName(String gwmDomainName) {
		this.gwmDomainName = gwmDomainName;
	}

	public String getFormattedProfileName() {
		return formattedProfileName;
	}

	public void setFormattedProfileName(String formattedProfileName) {
		this.formattedProfileName = formattedProfileName;
	}

	public String getGscRefreshToken() {
		return gscRefreshToken;
	}

	public void setGscRefreshToken(String gscRefreshToken) {
		this.gscRefreshToken = gscRefreshToken;
	}

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	public String getEncryptSecret() {
		return encryptSecret;
	}

	public void setEncryptSecret(String encryptSecret) {
		this.encryptSecret = encryptSecret;
	}
}