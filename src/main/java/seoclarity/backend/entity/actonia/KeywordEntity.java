/**
 * 
 */
package seoclarity.backend.entity.actonia;

import java.math.BigInteger;
import java.net.URLDecoder;
import java.util.Date;

import org.apache.commons.lang.StringEscapeUtils;
/**
 * com.actonia.subserver.entity.KeywordEntity.java
 * 
 * @version $Revision: 11705 $ $Author: wangjc@SHINETECHCHINA $
 */
public class KeywordEntity {

	public static final int TYPE_ADD_BY_USER = 1;

	public static final int TYPE_ADD_BY_GA = 2;

	public static final int TYPE_ADD_BY_PAID_GA = 100;

	public static final int TYPE_ADD_BY_UPLOAD_GA = 101;

	public static final int RANK_CHECK_ACTIVE = 1;
	public static final int RANK_CHECK_GEO_KEYWORD = 9;
	public static final int RANK_CHECK_INACTIVE = 0;
	public static final int RANK_CHECK_SUSPEND_BY_ADMIN = -1;

	private Long id;

	private String keywordName;

	private String keywordValue;

	private Integer type;

	private String description;

	private Integer ownDomainId;

	private Integer rankCheck;

	private Long topRankTargeturlId;
	/**
	 * yesterday rank value
	 */
	private Integer rank1;

	/**
	 * the day before yesterday rank value
	 */
	private Integer rank2;

	private Integer yesterdayEntrances;

	// 7 days entrances
	private Integer weekEntrances;

	private Float currentCtr;

	private Integer monthlySearchVolume;

	private Integer trafficIncrease;

	private Integer rankImprovement;

	private Integer rank3;

	private Integer bing_rank1;

	private Integer bing_rank2;

	private Date rankUpdateDate;

	private Integer avgMonthlySearchVolume;

	private Date createDate;

	private Integer toprankcount;

	private Float wtdavgrank;

	private String tagName;

	private Integer rankcheckId;

	/** for queuebase v2*/
	private int resCategory;
	private String keywordNameForMatch;

	/** for new t_keyword */
	private String rawKeywordName;
	private String cdbKeywordHash;
	private String cdbKeywordMurmur3hash;

	private Long tagId;

	private String searchEngine;

	public int getResCategory() {
		return resCategory;
	}

	public void setResCategory(int resCategory) {
		this.resCategory = resCategory;
	}

	public String getKeywordNameForMatch() {
		return keywordNameForMatch;
	}

	public void setKeywordNameForMatch(String keywordNameForMatch) {
		this.keywordNameForMatch = keywordNameForMatch;
	}

	public String getRawKeywordName() {
		return rawKeywordName;
	}

	public void setRawKeywordName(String rawKeywordName) {
		this.rawKeywordName = rawKeywordName;
	}

	public String getCdbKeywordHash() {
		return cdbKeywordHash;
	}

	public void setCdbKeywordHash(String cdbKeywordHash) {
		this.cdbKeywordHash = cdbKeywordHash;
	}

	public String getCdbKeywordMurmur3hash() {
		return cdbKeywordMurmur3hash;
	}

	public void setCdbKeywordMurmur3hash(String cdbKeywordMurmur3hash) {
		this.cdbKeywordMurmur3hash = cdbKeywordMurmur3hash;
	}

	public Integer getToprankcount() {
		return toprankcount;
	}

	public void setToprankcount(Integer toprankcount) {
		this.toprankcount = toprankcount;
	}

	public Float getWtdavgrank() {
		return wtdavgrank;
	}

	public void setWtdavgrank(Float wtdavgrank) {
		this.wtdavgrank = wtdavgrank;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getBing_rank1() {
		return bing_rank1;
	}

	public void setBing_rank1(Integer bingRank1) {
		bing_rank1 = bingRank1;
	}

	public Integer getBing_rank2() {
		return bing_rank2;
	}

	public void setBing_rank2(Integer bingRank2) {
		bing_rank2 = bingRank2;
	}

	public Date getRankUpdateDate() {
		return rankUpdateDate;
	}

	public void setRankUpdateDate(Date rankUpdateDate) {
		this.rankUpdateDate = rankUpdateDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public String getKeywordValue() {
		return keywordValue;
	}

	public void setKeywordValue(String keywordValue) {
		this.keywordValue = keywordValue;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getRankCheck() {
		return rankCheck;
	}

	public void setRankCheck(Integer rankCheck) {
		this.rankCheck = rankCheck;
	}

	public Integer getRank1() {
		return rank1;
	}

	public void setRank1(Integer rank1) {
		this.rank1 = rank1;
	}

	public Integer getRank2() {
		return rank2;
	}

	public void setRank2(Integer rank2) {
		this.rank2 = rank2;
	}

	public Integer getYesterdayEntrances() {
		return yesterdayEntrances;
	}

	public void setYesterdayEntrances(Integer yesterdayEntrances) {
		this.yesterdayEntrances = yesterdayEntrances;
	}

	public Integer getWeekEntrances() {
		return weekEntrances;
	}

	public void setWeekEntrances(Integer weekEntrances) {
		this.weekEntrances = weekEntrances;
	}

	public Float getCurrentCtr() {
		return currentCtr;
	}

	public void setCurrentCtr(Float currentCtr) {
		this.currentCtr = currentCtr;
	}

	public Integer getMonthlySearchVolume() {
		return monthlySearchVolume;
	}

	public void setMonthlySearchVolume(Integer monthlySearchVolume) {
		this.monthlySearchVolume = monthlySearchVolume;
	}

	public Integer getTrafficIncrease() {
		return trafficIncrease;
	}

	public void setTrafficIncrease(Integer trafficIncrease) {
		this.trafficIncrease = trafficIncrease;
	}

	public Integer getRankImprovement() {
		return rankImprovement;
	}

	public void setRankImprovement(Integer rankImprovement) {
		this.rankImprovement = rankImprovement;
	}

	public Integer getRank3() {
		return rank3;
	}

	public void setRank3(Integer rank3) {
		this.rank3 = rank3;
	}

	public String getKeywordForHtml() {
		try {
			return StringEscapeUtils.escapeHtml(URLDecoder.decode(keywordName, "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public Long getTopRankTargeturlId() {
		return topRankTargeturlId;
	}

	public void setTopRankTargeturlId(Long topRankTargeturlId) {
		this.topRankTargeturlId = topRankTargeturlId;
	}

	public Integer getAvgMonthlySearchVolume() {
		return avgMonthlySearchVolume;
	}

	public void setAvgMonthlySearchVolume(Integer avgMonthlySearchVolume) {
		this.avgMonthlySearchVolume = avgMonthlySearchVolume;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public Integer getRankcheckId() {
		return rankcheckId;
	}

	public void setRankcheckId(Integer rankcheckId) {
		this.rankcheckId = rankcheckId;
	}

	public Long getTagId() {
		return tagId;
	}

	public void setTagId(Long tagId) {
		this.tagId = tagId;
	}

	public String getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}

	@Override
	public String toString() {
		return "KeywordEntity{" +
				"id=" + id +
				", keywordName='" + keywordName + '\'' +
				", ownDomainId=" + ownDomainId +
				", rankCheck=" + rankCheck +
				", rawKeywordName='" + rawKeywordName + '\'' +
				", cdbKeywordHash=" + cdbKeywordHash +
				", cdbKeywordMurmur3hash=" + cdbKeywordMurmur3hash +
				'}';
	}
}
