package seoclarity.backend.entity.actonia;

import java.math.BigDecimal;

public class MixTrafficEntity {
	public static int GA_ISMOBILE_NO = 0;
	public static int GA_ISMOBILE_YES = 1;

	public static int GA_GA_VISITORTYPE_NEW = 0;
	public static int GA_GA_VISITORTYPE_RETURN = 1;

	private Integer id;
	private Integer ownDomainId;
	private Integer trackMonth;
	private String medium;
	private String source;
	private Integer ismobile;
	private Integer visitortype;
	private Integer entrances;
	private Integer pageviews;
	private Integer bounces;
	private BigDecimal timeonpage;
	private Integer transactions;
	private BigDecimal transactionRevenue;
	private Integer goal1completions;
	private Integer goal2completions;
	private Integer goal3completions;
	private Integer goal4completions;
	private Integer goal5completions;
	private Integer goal6completions;
	private Integer goal7completions;
	private Integer goal8completions;
	private Integer goal9completions;
	private Integer goal10completions;
	private BigDecimal goal1value;
	private BigDecimal goal2value;
	private BigDecimal goal3value;
	private BigDecimal goal4value;
	private BigDecimal goal5value;
	private BigDecimal goal6value;
	private BigDecimal goal7value;
	private BigDecimal goal8value;
	private BigDecimal goal9value;
	private BigDecimal goal10value;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getTrackMonth() {
		return trackMonth;
	}

	public void setTrackMonth(Integer trackMonth) {
		this.trackMonth = trackMonth;
	}

	public String getMedium() {
		return medium;
	}

	public void setMedium(String medium) {
		this.medium = medium;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public Integer getIsmobile() {
		return ismobile;
	}

	public void setIsmobile(Integer ismobile) {
		this.ismobile = ismobile;
	}

	public Integer getVisitortype() {
		return visitortype;
	}

	public void setVisitortype(Integer visitortype) {
		this.visitortype = visitortype;
	}

	public Integer getEntrances() {
		return entrances;
	}

	public void setEntrances(Integer entrances) {
		this.entrances = entrances;
	}

	public Integer getPageviews() {
		return pageviews;
	}

	public void setPageviews(Integer pageviews) {
		this.pageviews = pageviews;
	}

	public Integer getBounces() {
		return bounces;
	}

	public void setBounces(Integer bounces) {
		this.bounces = bounces;
	}

	public BigDecimal getTimeonpage() {
		return timeonpage;
	}

	public void setTimeonpage(BigDecimal timeonpage) {
		this.timeonpage = timeonpage;
	}

	public Integer getTransactions() {
		if (transactions == null) {
			return 0;
		}
		return transactions;
	}

	public void setTransactions(Integer transactions) {
		this.transactions = transactions;
	}

	public BigDecimal getTransactionRevenue() {
		if (transactionRevenue == null) {
			return new BigDecimal("0");
		}
		return transactionRevenue;
	}

	public void setTransactionRevenue(BigDecimal transactionRevenue) {
		this.transactionRevenue = transactionRevenue;
	}

	public Integer getGoal1completions() {
		if (goal1completions == null) {
			return 0;
		}
		return goal1completions;
	}

	public void setGoal1completions(Integer goal1completions) {
		this.goal1completions = goal1completions;
	}

	public Integer getGoal2completions() {
		if (goal2completions == null) {
			return 0;
		}
		return goal2completions;
	}

	public void setGoal2completions(Integer goal2completions) {
		this.goal2completions = goal2completions;
	}

	public Integer getGoal3completions() {
		if (goal3completions == null) {
			return 0;
		}
		return goal3completions;
	}

	public void setGoal3completions(Integer goal3completions) {
		this.goal3completions = goal3completions;
	}

	public Integer getGoal4completions() {
		if (goal4completions == null) {
			return 0;
		}
		return goal4completions;
	}

	public void setGoal4completions(Integer goal4completions) {
		this.goal4completions = goal4completions;
	}

	public Integer getGoal5completions() {
		if (goal5completions == null) {
			return 0;
		}
		return goal5completions;
	}

	public void setGoal5completions(Integer goal5completions) {
		this.goal5completions = goal5completions;
	}

	public Integer getGoal6completions() {
		if (goal6completions == null) {
			return 0;
		}
		return goal6completions;
	}

	public void setGoal6completions(Integer goal6completions) {
		this.goal6completions = goal6completions;
	}

	public Integer getGoal7completions() {
		if (goal7completions == null) {
			return 0;
		}
		return goal7completions;
	}

	public void setGoal7completions(Integer goal7completions) {
		this.goal7completions = goal7completions;
	}

	public Integer getGoal8completions() {
		if (goal8completions == null) {
			return 0;
		}
		return goal8completions;
	}

	public void setGoal8completions(Integer goal8completions) {
		this.goal8completions = goal8completions;
	}

	public Integer getGoal9completions() {
		if (goal9completions == null) {
			return 0;
		}
		return goal9completions;
	}

	public void setGoal9completions(Integer goal9completions) {
		this.goal9completions = goal9completions;
	}

	public Integer getGoal10completions() {
		if (goal10completions == null) {
			return 0;
		}
		return goal10completions;
	}

	public void setGoal10completions(Integer goal10completions) {
		this.goal10completions = goal10completions;
	}

	public BigDecimal getGoal1value() {
		if (goal1value == null) {
			return new BigDecimal("0");
		}
		return goal1value;
	}

	public void setGoal1value(BigDecimal goal1value) {
		this.goal1value = goal1value;
	}

	public BigDecimal getGoal2value() {
		if (goal2value == null) {
			return new BigDecimal("0");
		}
		return goal2value;
	}

	public void setGoal2value(BigDecimal goal2value) {
		this.goal2value = goal2value;
	}

	public BigDecimal getGoal3value() {
		if (goal3value == null) {
			return new BigDecimal("0");
		}
		return goal3value;
	}

	public void setGoal3value(BigDecimal goal3value) {
		this.goal3value = goal3value;
	}

	public BigDecimal getGoal4value() {
		if (goal4value == null) {
			return new BigDecimal("0");
		}
		return goal4value;
	}

	public void setGoal4value(BigDecimal goal4value) {
		this.goal4value = goal4value;
	}

	public BigDecimal getGoal5value() {
		if (goal5value == null) {
			return new BigDecimal("0");
		}
		return goal5value;
	}

	public void setGoal5value(BigDecimal goal5value) {
		this.goal5value = goal5value;
	}

	public BigDecimal getGoal6value() {
		if (goal6value == null) {
			return new BigDecimal("0");
		}
		return goal6value;
	}

	public void setGoal6value(BigDecimal goal6value) {
		this.goal6value = goal6value;
	}

	public BigDecimal getGoal7value() {
		if (goal7value == null) {
			return new BigDecimal("0");
		}
		return goal7value;
	}

	public void setGoal7value(BigDecimal goal7value) {
		this.goal7value = goal7value;
	}

	public BigDecimal getGoal8value() {
		if (goal8value == null) {
			return new BigDecimal("0");
		}
		return goal8value;
	}

	public void setGoal8value(BigDecimal goal8value) {
		this.goal8value = goal8value;
	}

	public BigDecimal getGoal9value() {
		if (goal9value == null) {
			return new BigDecimal("0");
		}
		return goal9value;
	}

	public void setGoal9value(BigDecimal goal9value) {
		this.goal9value = goal9value;
	}

	public BigDecimal getGoal10value() {
		if (goal10value == null) {
			return new BigDecimal("0");
		}
		return goal10value;
	}

	public void setGoal10value(BigDecimal goal10value) {
		this.goal10value = goal10value;
	}

	@Override
	public String toString() {
		return "MixTrafficEntity [ownDomainId=" + ownDomainId + ", trackMonth=" + trackMonth + ", medium=" + medium
				+ ", source=" + source + ", ismobile=" + ismobile + ", visitortype=" + visitortype + ", entrances="
				+ entrances + ", pageviews=" + pageviews + ", bounces=" + bounces + ", timeonpage=" + timeonpage
				+ ", transactions=" + transactions + ", transactionRevenue=" + transactionRevenue
				+ ", goal1completions=" + goal1completions + ", goal2completions=" + goal2completions
				+ ", goal3completions=" + goal3completions + ", goal4completions=" + goal4completions
				+ ", goal5completions=" + goal5completions + ", goal6completions=" + goal6completions
				+ ", goal7completions=" + goal7completions + ", goal8completions=" + goal8completions
				+ ", goal9completions=" + goal9completions + ", goal10completions=" + goal10completions
				+ ", goal1value=" + goal1value + ", goal2value=" + goal2value + ", goal3value=" + goal3value
				+ ", goal4value=" + goal4value + ", goal5value=" + goal5value + ", goal6value=" + goal6value
				+ ", goal7value=" + goal7value + ", goal8value=" + goal8value + ", goal9value=" + goal9value
				+ ", goal10value=" + goal10value + "]";
	}

}
