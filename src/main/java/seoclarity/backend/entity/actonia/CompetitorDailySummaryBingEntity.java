package seoclarity.backend.entity.actonia;

import java.util.Date;

import org.springframework.data.annotation.Transient;

import seoclarity.backend.utils.FormatUtils;

/**
 * com.actonia.saas.model.CompetitorDailySummaryBingEntity.java
 * 
 * <AUTHOR>
 * 
 * @version $Revision: 7963 $ $Author: wangc@SHINETECHCHINA $
 */
public class CompetitorDailySummaryBingEntity {

	private Integer id;

	private Integer ownDomainId;
	
	@Transient
	private String ownDomainName;

	private Integer competitorId;
	
	private String domain; // competitor domain

	private Date trackDate;

	private Integer groupTagId;
	
	@Transient
	private String groupTagName;

	private Integer totalKeyword;

	private Integer countRankTop100;

	private Integer countRankTop50;

	private Integer countRankTop30;

	private Integer countRankTop10;

	private Integer countRankTop5;

	private Integer countRankTop3;

	private Integer countRankTop1;

	private Integer totalEntrances;

	private Integer rankInPage1;

	private Integer rankInPage2;

	private Integer rankInPage3;

	private Integer rankInPage4;

	private Integer rankInPage5;

	private Integer countKeywordRank2;

	private Integer countKeywordRank3;

	private Integer countKeywordRank4;

	private Integer countKeywordRank5;

	private Integer countKeywordRank6;

	private Integer countKeywordRank7;

	private Integer countKeywordRank8;

	private Integer countKeywordRank9;

	private Integer countKeywordRank10;

	private Integer searchEngineId;

	private Float avgRank;
	private Float wtdAvgRank;

	private Integer searchVolumeRank1;
	private Integer searchVolumeRank2;
	private Integer searchVolumeRank3;
	private Integer searchVolumeRank4;
	private Integer searchVolumeRank5;
	private Integer searchVolumeRank6;
	private Integer searchVolumeRank7;
	private Integer searchVolumeRank8;
	private Integer searchVolumeRank9;
	private Integer searchVolumeRank10;
	
	public Integer getSearchVolumeRank1() {
		return searchVolumeRank1;
	}

	public void setSearchVolumeRank1(Integer searchVolumeRank1) {
		this.searchVolumeRank1 = searchVolumeRank1;
	}

	public Integer getSearchVolumeRank2() {
		return searchVolumeRank2;
	}

	public void setSearchVolumeRank2(Integer searchVolumeRank2) {
		this.searchVolumeRank2 = searchVolumeRank2;
	}

	public Integer getSearchVolumeRank3() {
		return searchVolumeRank3;
	}

	public void setSearchVolumeRank3(Integer searchVolumeRank3) {
		this.searchVolumeRank3 = searchVolumeRank3;
	}

	public Integer getSearchVolumeRank4() {
		return searchVolumeRank4;
	}

	public void setSearchVolumeRank4(Integer searchVolumeRank4) {
		this.searchVolumeRank4 = searchVolumeRank4;
	}

	public Integer getSearchVolumeRank5() {
		return searchVolumeRank5;
	}

	public void setSearchVolumeRank5(Integer searchVolumeRank5) {
		this.searchVolumeRank5 = searchVolumeRank5;
	}

	public Integer getSearchVolumeRank6() {
		return searchVolumeRank6;
	}

	public void setSearchVolumeRank6(Integer searchVolumeRank6) {
		this.searchVolumeRank6 = searchVolumeRank6;
	}

	public Integer getSearchVolumeRank7() {
		return searchVolumeRank7;
	}

	public void setSearchVolumeRank7(Integer searchVolumeRank7) {
		this.searchVolumeRank7 = searchVolumeRank7;
	}

	public Integer getSearchVolumeRank8() {
		return searchVolumeRank8;
	}

	public void setSearchVolumeRank8(Integer searchVolumeRank8) {
		this.searchVolumeRank8 = searchVolumeRank8;
	}

	public Integer getSearchVolumeRank9() {
		return searchVolumeRank9;
	}

	public void setSearchVolumeRank9(Integer searchVolumeRank9) {
		this.searchVolumeRank9 = searchVolumeRank9;
	}

	public Integer getSearchVolumeRank10() {
		return searchVolumeRank10;
	}

	public void setSearchVolumeRank10(Integer searchVolumeRank10) {
		this.searchVolumeRank10 = searchVolumeRank10;
	}

	public Float getAvgRank() {
		return avgRank;
	}

	public void setAvgRank(Float avgRank) {
		this.avgRank = avgRank;
	}

	public Float getWtdAvgRank() {
		return wtdAvgRank;
	}

	public void setWtdAvgRank(Float wtdAvgRank) {
		this.wtdAvgRank = wtdAvgRank;
	}

	public Integer getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(Integer searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getCompetitorId() {
		return competitorId;
	}

	public void setCompetitorId(Integer competitorId) {
		this.competitorId = competitorId;
	}

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public Integer getGroupTagId() {
		return groupTagId;
	}

	public void setGroupTagId(Integer groupTagId) {
		this.groupTagId = groupTagId;
	}

	public Integer getTotalKeyword() {
		return totalKeyword;
	}

	public void setTotalKeyword(Integer totalKeyword) {
		this.totalKeyword = totalKeyword;
	}

	public Integer getCountRankTop100() {
		return countRankTop100;
	}

	public void setCountRankTop100(Integer countRankTop100) {
		this.countRankTop100 = countRankTop100;
	}

	public Integer getCountRankTop50() {
		return countRankTop50;
	}

	public void setCountRankTop50(Integer countRankTop50) {
		this.countRankTop50 = countRankTop50;
	}

	public Integer getCountRankTop30() {
		return countRankTop30;
	}

	public void setCountRankTop30(Integer countRankTop30) {
		this.countRankTop30 = countRankTop30;
	}

	public Integer getCountRankTop10() {
		return countRankTop10;
	}

	public void setCountRankTop10(Integer countRankTop10) {
		this.countRankTop10 = countRankTop10;
	}

	public Integer getCountRankTop5() {
		return countRankTop5;
	}

	public void setCountRankTop5(Integer countRankTop5) {
		this.countRankTop5 = countRankTop5;
	}

	public Integer getCountRankTop3() {
		return countRankTop3;
	}

	public void setCountRankTop3(Integer countRankTop3) {
		this.countRankTop3 = countRankTop3;
	}

	public Integer getCountRankTop1() {
		return countRankTop1;
	}

	public void setCountRankTop1(Integer countRankTop1) {
		this.countRankTop1 = countRankTop1;
	}

	public Integer getTotalEntrances() {
		return totalEntrances;
	}

	public void setTotalEntrances(Integer totalEntrances) {
		this.totalEntrances = totalEntrances;
	}

	public Integer getRankInPage1() {
		return rankInPage1;
	}

	public void setRankInPage1(Integer rankInPage1) {
		this.rankInPage1 = rankInPage1;
	}

	public Integer getRankInPage2() {
		return rankInPage2;
	}

	public void setRankInPage2(Integer rankInPage2) {
		this.rankInPage2 = rankInPage2;
	}

	public Integer getRankInPage3() {
		return rankInPage3;
	}

	public void setRankInPage3(Integer rankInPage3) {
		this.rankInPage3 = rankInPage3;
	}

	public Integer getRankInPage4() {
		return rankInPage4;
	}

	public void setRankInPage4(Integer rankInPage4) {
		this.rankInPage4 = rankInPage4;
	}

	public Integer getRankInPage5() {
		return rankInPage5;
	}

	public void setRankInPage5(Integer rankInPage5) {
		this.rankInPage5 = rankInPage5;
	}

	public Integer getCountKeywordRank2() {
		return countKeywordRank2;
	}

	public void setCountKeywordRank2(Integer countKeywordRank2) {
		this.countKeywordRank2 = countKeywordRank2;
	}

	public Integer getCountKeywordRank3() {
		return countKeywordRank3;
	}

	public void setCountKeywordRank3(Integer countKeywordRank3) {
		this.countKeywordRank3 = countKeywordRank3;
	}

	public Integer getCountKeywordRank4() {
		return countKeywordRank4;
	}

	public void setCountKeywordRank4(Integer countKeywordRank4) {
		this.countKeywordRank4 = countKeywordRank4;
	}

	public Integer getCountKeywordRank5() {
		return countKeywordRank5;
	}

	public void setCountKeywordRank5(Integer countKeywordRank5) {
		this.countKeywordRank5 = countKeywordRank5;
	}

	public Integer getCountKeywordRank6() {
		return countKeywordRank6;
	}

	public void setCountKeywordRank6(Integer countKeywordRank6) {
		this.countKeywordRank6 = countKeywordRank6;
	}

	public Integer getCountKeywordRank7() {
		return countKeywordRank7;
	}

	public void setCountKeywordRank7(Integer countKeywordRank7) {
		this.countKeywordRank7 = countKeywordRank7;
	}

	public Integer getCountKeywordRank8() {
		return countKeywordRank8;
	}

	public void setCountKeywordRank8(Integer countKeywordRank8) {
		this.countKeywordRank8 = countKeywordRank8;
	}

	public Integer getCountKeywordRank9() {
		return countKeywordRank9;
	}

	public void setCountKeywordRank9(Integer countKeywordRank9) {
		this.countKeywordRank9 = countKeywordRank9;
	}

	public Integer getCountKeywordRank10() {
		return countKeywordRank10;
	}

	public void setCountKeywordRank10(Integer countKeywordRank10) {
		this.countKeywordRank10 = countKeywordRank10;
	}

	public int getCountRankTop20() {
		return (rankInPage1 == null ? 0 : rankInPage1) + (rankInPage2 == null ? 0 : rankInPage2);
	}

	public String getDateString() {
		return FormatUtils.formatDate(trackDate, "yyyy-MM-dd");
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	@Transient
	public String getOwnDomainName() {
		return ownDomainName;
	}

	public void setOwnDomainName(String ownDomainName) {
		this.ownDomainName = ownDomainName;
	}

	@Transient
	public String getGroupTagName() {
		return groupTagName;
	}

	public void setGroupTagName(String groupTagName) {
		this.groupTagName = groupTagName;
	}
	
}
