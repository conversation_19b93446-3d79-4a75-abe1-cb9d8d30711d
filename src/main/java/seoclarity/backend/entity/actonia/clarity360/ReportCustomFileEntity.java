package seoclarity.backend.entity.actonia.clarity360;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ReportCustomFileEntity {

    public static final int ENABLED = 1;
    public static final int DIS_ENABLED = 0;

    public static final int STATUS_NEW_CREATE = 0;
    public static final int STATUS_WAIT_SCRIPT_RUN = 10;
    public static final int STATUS_LOADING = 11; // processing 11
    public static final int STATUS_LOADING_SUCCESS = 2;
    public static final int STATUS_LOADING_ERROR = 3;
    public static final int STATUS_LOADING_ERROR_COLUMN_DUPLICATE = 401; // file header have duplicate column
    public static final int STATUS_LOADING_ERROR_COLUMN_NOT_CONTAIN_URL = 402; // file data not contain url
    public static final int STATUS_LOADING_ERROR_FILE_FORMAT_ERROR = 403; // upload file format error
    public static final int STATUS_LOADING_ERROR_FILE_FILE_PATH_IS_EMPTY = 404; // file not found
    public static final int STATUS_LOADING_ERROR_URL_IS_REPEAT_IS_REPEAT = 405; // url is duplicate
    public static final int STATUS_LOADING_ERROR_PARSE_HIERARCHY_ERROR = 406;
    public static final int STATUS_LOADING_ERROR_MANY_PARENT_TAG = 407; // tag has many diff parent tag

    public static final int STORAGE_TYPE_FTP_OWN = 1;
    public static final int STORAGE_TYPE_FTP_CUSTOMER = 2; // The FTP file path needs to be concatenated
    public static final int STORAGE_TYPE_S3 = 3;
    public static final int STORAGE_TYPE_SFTP = 4;

    private boolean runFlag = true; // default true when false un update status and not process


    private int id;
    private int enabled;
    private int ownDomainId;
    private int storageType;
    private Integer storageConfigId;
    private int uploadStatus;
    private String sourceFilename;
    private String friendlyName;
    private String ftpFullPathFilename;
    private String httpFullPathFilename;
    private String s3FullPathFilename;
    private String ftpReserveFilename;
    private int createUserId;
    private Date createdAt;
}
