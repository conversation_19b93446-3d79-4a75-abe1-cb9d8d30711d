package seoclarity.backend.entity.actonia;

public class MgdKeywordSearchEngineRelEntity {

	
	public static String DEVICE_DESKTOP = "d";
	public static String DEVICE_MOBILE = "m";

	public static Integer NATIONAL_LEVEL = 1;
	public static Integer GEO_LEVELL = 2;
	
	public static Integer FREQ_DAILY = 1;
	public static Integer FREQ_WEEKLY = 7;
	public static Integer FREQ_BI_WEEKLY = 14;
	public static Integer FREQ_MONTHLY = 30;

	private long id;
	private int ownDomainId;
	private int engineId;
	private int languageId;
	private int tagId;
	private long keywordRankcheckId;
	private String device;
	private int cityId;
	private long keywordId;
	private long rgKeywordHash;
	private int frequence;
	private int createDate;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getEngineId() {
		return engineId;
	}

	public void setEngineId(int engineId) {
		this.engineId = engineId;
	}

	public int getLanguageId() {
		return languageId;
	}

	public void setLanguageId(int languageId) {
		this.languageId = languageId;
	}

	public int getTagId() {
		return tagId;
	}

	public void setTagId(int tagId) {
		this.tagId = tagId;
	}

	public long getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(long keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public int getCityId() {
		return cityId;
	}

	public void setCityId(int cityId) {
		this.cityId = cityId;
	}

	public long getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(long keywordId) {
		this.keywordId = keywordId;
	}

	public long getRgKeywordHash() {
		return rgKeywordHash;
	}

	public void setRgKeywordHash(long rgKeywordHash) {
		this.rgKeywordHash = rgKeywordHash;
	}

	public int getFrequence() {
		return frequence;
	}

	public void setFrequence(int frequence) {
		this.frequence = frequence;
	}

	public int getCreateDate() {
		return createDate;
	}

	public void setCreateDate(int createDate) {
		this.createDate = createDate;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	
	
}

