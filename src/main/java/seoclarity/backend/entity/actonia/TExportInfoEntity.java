package seoclarity.backend.entity.actonia;

import java.util.Date;

public class TExportInfoEntity {

	/**
	 * https://www.wrike.com/open.htm?id=555333123
	 * use for api keyword resource export
	 */
	public static final String EXPORT_VERSION_3 = "3.0";
	public static final String EXPORT_VERSION_2 = "2.0";
	public static final String EXPORT_VERSION_1 = "1.0";

	//scott - https://www.wrike.com/open.htm?id=183158614
	public static final String FILE_SUFFIX_TXT = "txt";

	public static final String FILE_SUFFIX_CSV = "csv";

	//code from backend server
	//the record in db's status
	//create
	public static final int EXPORT_CREATE = 0;
	//in process
	public static final int EXPORT_PROCESSING = 1;
	//export done
	public static final int EXPORT_DONE = 2;
	//export error
	public static final int EXPORT_ERROR = 3;

	//export error message
	public static final int EXPORT_MESSAGE_ERROR = 5;

	// https://www.wrike.com/open.htm?id=43479796 TODO
	//export re-try when exception following        by floyd
	public static final int EXPORT_RETRY = 4;
	//code from backend server end

	public static final int EXPORT_NOT_MATCH = 5;


	public static final int EXPORT_TABLE_RANK_CLARITY = 1;

	public static final int EXPORT_TABLE_LINK_CLARITY = 2;

	public static final int EXPORT_TABLE_EVENT_TABLE = 3;

	public static final int EXPORT_TABLE_TASK_TABLE = 4;

	public static final int EXPORT_TABLE_DUPLICATE_TITLES = 5;

	public static final int EXPORT_TABLE_BOT_DETAIL_TABLE = 6;

	public static final int EXPORT_TABLE_BOT_URL_DETAIL_INFO_TABLE = 7;

	public static final int EXPORT_TABLE_RANK_COMPARE_CLARITY = 8;

	///target/analytics.do
	public static final int EXPORT_TABLE_ANALYTICS_PAGE_REPORT = 9;

	//Keyword + URL Pairs /keyword/keywordurl.do
	public static final int EXPORT_TABLE_KEYWORD_URL_PAIRS = 10;

	///keyword/stat.do
	public static final int EXPORT_TABLE_ANALYTICS_KEYWORD_REPORT = 11;

	///organically/list.do TAB-1
	public static final int EXPORT_TABLE_ORGANICALLY_PARTNER_DOMAIN_SUMMARY = 12;

	///organically/list.do TAB-2
	public static final int EXPORT_TABLE_ORGANICALLY_PARTNER_LINK_SUMMARY = 13;

	//Ticket #741
	///keyword/rankreportgrouptag.do?gtid=2116
	public static final int EXPORT_TABLE_RANK_CLARITY_GROUP_TAG = 14;

	//Ticket #741
	///target/listbytag.do?gtid=2756
	public static final int EXPORT_TABLE_PAGE_CLARITY_GROUP_TAG = 15;

	public static final int EXPORT_TABLE_RANK_CLARITY_TRENDED_RANK_WEEKLY = 16;
	public static final int EXPORT_TABLE_RANK_CLARITY_TRENDED_ENTRANCES_WEEKLY = 17;

	public static final int EXPORT_TABLE_KEYWORD_COMPETITIVE_GAPS = 18;

	public static final int EXPORT_TABLE_UNIQUE_KEYWORDS_IN_HIERARCHY = 19;

	public static final int EXPORT_TABLE_KEYWORD_WITH_TRAFFIC_FOR_SPECIAL_TARGETURL = 20;

	public static final int EXPORT_PAGE_EXCEL_CLARITY_AUDITS = 21;

	public static final int EXPORT_TABLE_COMPETITOR_DOMAIN_SEMRUSH_KEYWORD = 22;

	public static final int EXPORT_TABLE_GOOGLE_WEB_MASTER_KEYWORD = 23;

	public static final int EXPORT_TABLE_KEYWORD_WEEKLY_SUMMARY_WIDGET = 24;



	public static final int EXPORT_KEYWORD_TAGS = 26;
	public static final int EXPORT_PAGE_TAGS_FULLLIST = 27;
	public static final int EXPORT_KEYWORD_TAGS_FULLLIST = 28;
	public static final int EXPORT_TABLE_FORECAST_KEYWORD_LIST_TABLE = 29;
	///keyword/ppcvsorganic.do
	public static final int EXPORT_TABLE_PPC_VS_ORGANIC = 30;

	//https://www.wrike.com/open.htm?id=7061636
	public static final int EXPORT_TABLE_PAGE_CLARITY_ALL = 31;

	//https://www.wrike.com/open.htm?id=7086681
	public static final int EXPORT_TABLE_HIERARCHY_UNIQUE_KEYWORDS = 32;

	//https://www.wrike.com/open.htm?id=7165090
	public static final int EXPORT_TABLE_FORECAST_LIST_TABLE = 33;

	//https://www.wrike.com/open.htm?id=8486051
	public static final int EXPORT_TABLE_ANALYTICS_KEYWORD_URL = 34;

	public static final int EXPORT_TABLE_SITE_CLARITY_TABLE = 35;

	//https://www.wrike.com/open.htm?id=8536714
	//https://www.wrike.com/open.htm?id=8869310
	public static final int EXPORT_TABLE_TREE_UNIQUE_KEYWORDS_TABLE = 36;
	public static final int EXPORT_TABLE_TREE_UNIQUE_URLS_TABLE = 37;

	//https://www.wrike.com/open.htm?id=8915233
	public static final int EXPORT_TABLE_KEYWORD_CLARITY_OPPORTUNITIES_TABLE = 38;
	public static final int EXPORT_TABLE_KEYWORD_GROUP_TAGS_LIST_TABLE = 39;

	public static final int EXPORT_TABLE_ACTIVE_USER_INFORMATION = 40;
	public static final int EXPORT_TABLE_BACK_LINKS = 41;
	public static final int EXPORT_TABLE_REF_DOMAINS = 42;

	public static final int EXPORT_TABLE_AI_TABLE = 43;

	public static final int EXPORT_TABLE_SEARCH_VOLUME_TRENDED_TABLE = 44;

	public static final int EXPORT_TABLE_ANALYTICS_KEYWORD_REPORT_BY_ENGINE_TABLE = 45;

	public static final int EXPORT_TABLE_NEW_RANK_CLARITY_TABLE = 46;

	//https://www.wrike.com/open.htm?id=16563633
	public static final int EXPORT_TABLE_CITY_RANK_CLARITY_TABLE = 47;

	//https://www.wrike.com/open.htm?id=17154111
	public static final int EXPORT_KEYWORD_URL_RANK_TREND_TABLE = 48;

	//https://www.wrike.com/open.htm?id=15916670
	public static final int EXPORT_BACKLINKS_TAB_TABLE = 49;

	//https://www.wrike.com/open.htm?id=15916670
	public static final int EXPORT_REFERRING_DOMAINS_TAB_TABLE = 50;

	//https://www.wrike.com/open.htm?id=15916670
	public static final int EXPORT_ANCHOR_TEXT_TAB_TABLE = 51;

	//https://www.wrike.com/open.htm?id=17878193
	public static final int EXPORT_LINK_CLARITY_PARNTER_URLS_TAB_TABLE = 52;

	//https://www.wrike.com/open.htm?id=17878193
	public static final int EXPORT_LINK_CLARITY_PARNTER_DOMAINS_TAB_TABLE = 53;

	//https://www.wrike.com/open.htm?id=17878193
	public static final int EXPORT_LINK_CLARITY_ANTHER_TEXT_TAB_TABLE = 54;

	//https://www.wrike.com/open.htm?id=18182879
	public static final int EXPORT_PAGE_CLARITY_DETAIL_URLMETRICS = 55;
	public static final int EXPORT_KEYWORD_CLARITY_DETAIL_URLMETRICS = 56;

	//https://www.wrike.com/open.htm?id=18372456
	public static final int EXPORT_TABLE_PLA_VS_ORGANIC = 57;

	//https://www.wrike.com/open.htm?id=18419457
	public static final int EXPORT_TABLE_GOOGLE_WEBMASTER_TOP_QUERIES_TABLE = 58;

	//https://www.wrike.com/open.htm?id=18419457
	public static final int EXPORT_TABLE_GOOGLE_WEBMASTER_TOP_Pages_TABLE = 59;

	//https://www.wrike.com/open.htm?id=18419457
	public static final int EXPORT_TABLE_GOOGLE_WEBMASTER_MESSAGES_TABLE = 60;

	//https://www.wrike.com/open.htm?id=19321297
//  public static final int EXPORT_PAGE_CLARITY_DETAIL_KEYWORD_FOUND_FOR = 61;
//  public static final int EXPORT_PAGE_CLARITY_DETAIL_COMPETITOR_KEYWORDS = 62;
	public static final int EXPORT_PAGE_TAG_DETAIL_KEYWORD_FOUND_FOR = 61;
	public static final int EXPORT_PAGE_CLARITY_DETAIL_ALL_TAG_COMPETITOR_KEYWORDS = 62;

	// https://www.wrike.com/open.htm?id=19319340
	public static final int EXPORT_CONTENT_IDEAS = 63;

	// https://www.wrike.com/open.htm?id=19435793
	public static final int EXPORT_KEYWORD_URL_RANK_COMPARISON = 64;

	//https://www.wrike.com/open.htm?id=15788895
	//by sunny
	public static final int EXPORT_KEYWORD_PLP_HR_URL_RANK_TREND_TABLE = 65;

	//https://www.wrike.com/open.htm?id=21735529
	//by sunny
	public static final int EXPORT_TABLE_GOOGLE_WEBMASTER_TOP_QUERIES_TREND_TABLE = 66;

	public static final int EXPORT_TABLE_GOOGLE_WEBMASTER_TOP_PAGES_TREND_TABLE = 67;

	// https://www.wrike.com/open.htm?id=22367701
	// by Harry
	public static final int EXPORT_KEYWORD_TAG_DETAIL_OPPORTUNITIES_PAGE = 68;

	// https://www.wrike.com/open.htm?id=22583525
	// by Harry
	public static final int EXPORT_COMPETITOR_CLARITY_OPPORTUNITY_RANKTAB = 69;

	// https://www.wrike.com/open.htm?id=22583525
	// by Harry
	public static final int EXPORT_COMPETITOR_CLARITY_OPPORTUNITY_TOPPAGESTAB = 70;

	// https://www.wrike.com/open.htm?id=22801197
	// by Harry
	public static final int EXPORT_BUZZ_CLARITY_SOCIAL_ACTIVITY_BY_URL = 71;

	// https://www.wrike.com/open.htm?id=23354744
	// by Harry
	public static final int EXPORT_TABLE_NEW_RANK_CLARITY_TRENDED_GOOGLE_TRUE_RANK = 72;

	// https://www.wrike.com/open.htm?id=23837551
	// by Harry
	public static final int EXPORT_TABLE_PPC_VS_ORGANIC_ALL_TAG = 73;

	// https://www.wrike.com/open.htm?id=24138943
	// by Harry
	public static final int EXPORT_PAGE_TAGS_GROUPED = 74;

	// https://www.wrike.com/open.htm?id=22431179
	// by Harry
	public static final int EXPORT_SITE_CLARITY_DUPLICATE_TITLE  = 75;
	public static final int EXPORT_SITE_CLARITY_DUPLICATE_META  = 76;
	public static final int EXPORT_SITE_CLARITY_DUPLICATE_H1  = 77;

	// https://www.wrike.com/open.htm?id=24792723
	// by Harry
	public static final int EXPORT_TABLE_NEW_RANK_CLARITY_TRENDED_ESTIMATED_ENTRANCES  = 78;
	public static final int EXPORT_TABLE_NEW_RANK_CLARITY_TRENDED_ENTRANCES  = 79;
	public static final int EXPORT_TABLE_SEARCH_VOLUME_TRENDED_F0R_KEYWORD_TAG_TREE = 80;

	// https://www.wrike.com/open.htm?id=20078833
	// by Harry
	public static final int EXPORT_TABLE_COMPETITOR_DOMAINS_ASSOCIATED_KEYWORDS = 81;

	// https://www.wrike.com/open.htm?id=25304916
	// by Harry
	public static final int EXPORT_TABLE_CLARITY_RUSH_TOPKEYWORDS = 82;

	// https://www.wrike.com/open.htm?id=26121783
	// by Harry
	public static final int EXPORT_TABLE_CLARITY_RESEARCH_LIST = 83;

	// https://www.wrike.com/open.htm?id=26501774
	// by Harry
	public static final int EXPORT_TABLE_KEYWORD_TAGS_KEYWORD_TAG_TAB = 84;

	// https://www.wrike.com/open.htm?id=26509166
	// by Harry
	public static final int EXPORT_TABLE_TOP_COMPETITORS_RANK = 85;

	// https://www.wrike.com/open.htm?id=26983045
	// by Harry
	public static final int EXPORT_TABLE_GOOGLE_WEBMASTER_TOPKEYWORD_URLPAIR = 86;

	// https://www.wrike.com/open.htm?id=27520410
	// by Harry
	public static final int EXPORT_TABLE_COMPETITOR_DOMAINS = 87;

	// https://www.wrike.com/open.htm?id=29903768
	// by Dong
	public static final int EXPORT_TABLE_DOMAIN_COMPARE_SEARCH = 88;

	// https://www.wrike.com/open.htm?id=34545994
	// by sunny
	public static final int EXPORT_TABLE_PAGE_SPEED_SCORE = 89;

	// https://www.wrike.com/open.htm?id=34545994
	// by sunny
	public static final int EXPORT_TABLE_PAGE_SPEED_ISSUES = 90;

	//https://www.wrike.com/open.htm?id=35786521
	//by sunny
	public static final int EXPORT_SITE_CLARITY_CANONICAL_URLS = 91;

	//https://www.wrike.com/open.htm?id=37101847
	//by sunny
	public static final int EXPORT_PAGE_TYPES_DETAIL = 92;

	// https://www.wrike.com/open.htm?id=41146703
	// by sunny
	public static final int EXPORT_TABLE_NEW_PACK_RANK_CLARITY_TABLE = 93;

	//do not have page just a back end download added by floyd
	//by sunny
	public static final int EXPORT_TABLE_NEW_RANK_CLARITY_TABLE_WEEKLY = 100;

	//https://www.wrike.com/open.htm?id=43021366
	//by sunny
	public static final int EXPORT_TABLE_GOOGLE_WEB_MASTER_KEYWORD_URL_TREND_TABLE = 94;

	//https://www.wrike.com/open.htm?id=46463151
	//by sunny
	public static final int EXPORT_TABLE_RANK_GAIN_AND_LOSS_WIDGET_TABLE = 95;

	//https://www.wrike.com/open.htm?id=52554350
	//by sunny
	public static final int EXPORT_TABLE_LOCALLIST_RANK_TABLE = 96;

	//https://www.wrike.com/open.htm?id=57733897
	//by sunny
	public static final int EXPORT_TABLE_ACROSS_DOMAIN_RANK_TABLE = 97;

	//https://www.wrike.com/open.htm?id=60047751
	//by sunny
	public static final int EXPORT_TABLE_COMPETITOR_BACKLINKS_PAGE_TABLE = 98;

	//https://www.wrike.com/open.htm?id=70232927
	//by sunny
	public static final int EXPORT_TABLE_ACTIONABLE_INSIGHTS_SUMMARY = 99;

	//https://www.wrike.com/open.htm?id=71112064
	//by jimmy
	public static final int EXPORT_TABLE_TOP_RANKED_PAGES = 101;

	//Leo - https://www.wrike.com/open.htm?id=90091351
	public static final int EXPORT_WISDOM_DOMAIN_COMPARION_TABLE = 102;
	
	//scott -https://www.wrike.com/open.htm?id=210454338
	public static final int EXPORT_MULTI_DOMAIN_RANK_CLARITY_TABLE = 103;

	public static final int EXPORT_NEW_TYPE = 10000;

	//Leo - https://www.wrike.com/open.htm?id=84344961
	public static final int EXPORT_TABLE_KEYWORD_TAGS = 10001;

	//Leo - https://www.wrike.com/open.htm?id=93032335
	public static final int EXPORT_API_CLARITY_RUSH_TOPKEYWORDS_TABLE = 10002;

	//Leo - https://www.wrike.com/open.htm?id=93032335
	public static final int EXPORT_API_DOMAIN_COMPARISON_TABLE = 10003;

	//Leo - https://www.wrike.com/open.htm?id=98112971
	public static final int EXPORT_UNIVERSAL_RANK_TYPE_TABLE = 10004;

	//Leo - https://www.wrike.com/open.htm?id=98112971
	public static final int EXPORT_RANK_CLARITY_TOP_COMPETITOR_TABLE = 10005;

	//Leo - https://www.wrike.com/open.htm?id=104094645
	public static final int EXPORT_SIM_CHECK_DETAILS_TABLE = 10006;

	//Leo - https://www.wrike.com/open.htm?id=104436053
	public static final int EXPORT_COMPETITOR_ANSWER_BOX_DETAILS_TABLE = 10007;

	//https://www.wrike.com/open.htm?id=116370176
	//by Sunny
	public static final int EXPORT_PAGE_TAG_SEARCH_TAG_TABLE = 10008;

	public static final int EXPORT_TASK_TABLE = 10009;
	public static final int EXPORT_COMPETITOR_IMAGES = 10010;
	public static final int EXPORT_COMPETITOR_LOCALLISTING = 10011;
	public static final int EXPORT_COMPETITOR_PPC = 10012;

	//Leo - https://www.wrike.com/open.htm?id=124259862
	public static final int EXPORT_SITECLARITY_API_WEBSERVICE = 10013;

	//Leo - https://www.wrike.com/open.htm?id=134966002
	public static final int EXPORT_KEYWORD_PORTFOLIO = 10014;

	//Leo - https://www.wrike.com/open.htm?id=135237081
	public static final int EXPORT_MULTI_DOMAIN_TRENDED_RANKINGS = 10015;

	//Leo - https://www.wrike.com/open.htm?id=144136286
	public static final int EXPORT_BOT_DETAIL_TABLE = 10016;

	//Leo - https://www.wrike.com/open.htm?id=155013558
	public static final int EXPORT_DISTINCT_SOURCE_URLS_TABLE = 10017;

	//Leo - https://www.wrike.com/open.htm?id=152214006
	public static final int EXPORT_PEOPLE_ALSO_ASK = 10018;

	//Leo - https://www.wrike.com/open.htm?id=170181541
	public static final int EXPORT_CLARITY_AUDIT_DUPLICATE_TITLE = 10020;
	
	//Scott - https://www.wrike.com/open.htm?id=178673934
	public static final int EXPORT_CLARITY_AUDIT_DUPLICATE_META = 10040;

	//Scott - https://www.wrike.com/open.htm?id=178673934
	public static final int EXPORT_CLARITY_AUDIT_DUPLICATE_H1 = 10050;

	//Leo - https://www.wrike.com/open.htm?id=170181541
	public static final int EXPORT_CLARITY_AUDIT_DUPLICATE_TITLE_PAGE_DETAIL = 10021;
	
	//Cee - https://www.wrike.com/open.htm?id=178673934
	public static final int EXPORT_CLARITY_AUDIT_DUPLICATE_META_PAGE_DETAIL = 10041;
	public static final int EXPORT_CLARITY_AUDIT_DUPLICATE_H1_PAGE_DETAIL = 10051;
	
	//scott - https://www.wrike.com/open.htm?id=180261463
	public static final int EXPORT_LOCAL_BUSINESS_RANKING_KEYWORD = 10060;
	
	//scott - https://www.wrike.com/open.htm?id=180261463
	public static final int EXPORT_LOCAL_BUSINESS_RANKING_KEYWORD_DETAIL = 10061;

	//scott - https://www.wrike.com/open.htm?id=180261463
	public static final int EXPORT_LOCAL_BUSINESS_RANKING_BRAND = 10062;

	//scott - https://www.wrike.com/open.htm?id=180261463
	public static final int EXPORT_LOCAL_BUSINESS_RANKING_LOCATION = 10063;
	
	//scott - https://www.wrike.com/open.htm?id=304444982
	public static final int EXPORT_TRAFFIC_POTENTIAL_PAGE = 10081;
	public static final int EXPORT_TRAFFIC_POTENTIAL_ROI_PAGE = 10082;
	
	//scott - https://www.wrike.com/open.htm?id=359160726
	public static final int EXPORT_UNIVERSAL_RANK_TYPE_DETAIL = 10083;
	
	//scott - https://www.wrike.com/open.htm?id=372554232
	public static final int EXPORT_SEARCH_VISBLATY_SHARE = 10084;

	public static final int EXPORT_PARENT_CHILD_RELATION = 10090;

	// Meo - https://www.wrike.com/open.htm?id=555333123
	public static final int EXPORT_API_KEYWORD_RESOURCE = 90001;

    private Integer id;

	private Date createDate;

	private Integer ownDomainId;

	private Integer exportTable;

	private String fileName;
	// https://www.wrike.com/open.htm?id=28578360
 	// by Harry
 	private String customizedFileName;
	// end by Harry
	private Integer status;

	private String exportParam;

	private Integer currentPage;

	private Integer totalPage;

	private String exportVersion;

	private boolean isHandle;
	
	private String fileSuffix;

	private String md5;
	
	private int retryCount;

	public Integer getResultCount() {
		return resultCount;
	}

	public void setResultCount(Integer resultCount) {
		this.resultCount = resultCount;
	}

	private Integer resultCount;

	public String getMd5() {
		return md5;
	}

	public void setMd5(String md5) {
		this.md5 = md5;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getExportTable() {
		return exportTable;
	}

	public void setExportTable(Integer exportTable) {
		this.exportTable = exportTable;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getCustomizedFileName() {
		return customizedFileName;
	}

	public void setCustomizedFileName(String customizedFileName) {
		this.customizedFileName = customizedFileName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getExportParam() {
		return exportParam;
	}

	public void setExportParam(String exportParam) {
		this.exportParam = exportParam;
	}

	public Integer getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(Integer currentPage) {
		this.currentPage = currentPage;
	}

	public Integer getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(Integer totalPage) {
		this.totalPage = totalPage;
	}

	public String getExportVersion() {
		return exportVersion;
	}

	public void setExportVersion(String exportVersion) {
		this.exportVersion = exportVersion;
	}
	
	public String getFileSuffix() {
		return fileSuffix;
	}

	public void setFileSuffix(String fileSuffix) {
		this.fileSuffix = fileSuffix;
	}

	public boolean isHandle() {
		return isHandle;
	}

	public void setHandle(boolean handle) {
		isHandle = handle;
	}

	public int getRetryCount() {
		return retryCount;
	}

	public void setRetryCount(int retryCount) {
		this.retryCount = retryCount;
	}
	
}