package seoclarity.backend.entity.actonia;

import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.entity.actonia
 * @author: cil
 * @date: 2021-07-08 10:44
 **/
public class ContentJsonPOJO implements Comparable<ContentJsonPOJO> {
    private String country;
    private String countryCode;
    private String search;
    private String enCode;
    private String engineId;
    private String languageId;
    private String location;
    private List<String> word;

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public List<String> getWord() {
        return word;
    }

    public void setWord(List<String> word) {
        this.word = word;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getEnCode() {
        return enCode;
    }

    public void setEnCode(String enCode) {
        this.enCode = enCode;
    }

    public String getEngineId() {
        return engineId;
    }

    public void setEngineId(String engineId) {
        this.engineId = engineId;
    }

    public String getLanguageId() {
        return languageId;
    }

    public void setLanguageId(String languageId) {
        this.languageId = languageId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ContentJsonPOJO that = (ContentJsonPOJO) o;

        if (country != null ? !country.equals(that.country) : that.country != null) return false;
        return search != null ? search.equals(that.search) : that.search == null;
    }

    @Override
    public int hashCode() {
        int result = country != null ? country.hashCode() : 0;
        result = 31 * result + (search != null ? search.hashCode() : 0);
        return result;
    }

    @Override
    public int compareTo(@NotNull ContentJsonPOJO o) {
        if ((this.country.hashCode() - o.getCountry().hashCode()) == 0)
            return this.country.compareTo(o.getCountry());
        else return this.country.compareTo(o.getCountry());
    }

    @Override
    public String toString() {
        return
                country + '\t' + search;
    }
}
