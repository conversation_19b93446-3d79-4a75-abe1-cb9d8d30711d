package seoclarity.backend.entity.actonia;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date 2019-11-25
 * @path seoclarity.backend.entity.actonia.CompetitorGroupsRelEntity
 *
 */
public class CompetitorGroupsRelEntity {
	private int id;

	private int ownDomainId;

	private String competitorDomain;

	private Integer competitorGroupId;

	private int createUser;

	private Date createDate;

	private String rootDomainReverse;

	private String domainReverse;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public Integer getCompetitorGroupId() {
		return competitorGroupId;
	}

	public void setCompetitorGroupId(Integer competitorGroupId) {
		this.competitorGroupId = competitorGroupId;
	}
	
	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getCompetitorDomain() {
		return competitorDomain;
	}

	public void setCompetitorDomain(String competitorDomain) {
		this.competitorDomain = competitorDomain;
	}

	public int getCreateUser() {
		return createUser;
	}

	public void setCreateUser(int createUser) {
		this.createUser = createUser;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getRootDomainReverse() {
		return rootDomainReverse;
	}

	public void setRootDomainReverse(String rootDomainReverse) {
		this.rootDomainReverse = rootDomainReverse;
	}

	public String getDomainReverse() {
		return domainReverse;
	}

	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}

}
