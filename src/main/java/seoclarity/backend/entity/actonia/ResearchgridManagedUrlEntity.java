package seoclarity.backend.entity.actonia;

public class ResearchgridManagedUrlEntity {

    private Long targetUrlId;

    private Integer ownDomainId;

    private Integer tagId;

    private String targetUrl;

    private String researchGridUrlHash;

    private String researchGridUriHash;

    public Long getTargetUrlId() {
        return targetUrlId;
    }

    public void setTargetUrlId(Long targetUrlId) {
        this.targetUrlId = targetUrlId;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getTagId() {
        return tagId;
    }

    public void setTagId(Integer tagId) {
        this.tagId = tagId;
    }

    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    public String getResearchGridUrlHash() {
        return researchGridUrlHash;
    }

    public void setResearchGridUrlHash(String researchGridUrlHash) {
        this.researchGridUrlHash = researchGridUrlHash;
    }

    public String getResearchGridUriHash() {
        return researchGridUriHash;
    }

    public void setResearchGridUriHash(String researchGridUriHash) {
        this.researchGridUriHash = researchGridUriHash;
    }
}
