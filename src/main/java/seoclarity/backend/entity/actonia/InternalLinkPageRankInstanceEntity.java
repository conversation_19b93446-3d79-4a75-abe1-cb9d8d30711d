package seoclarity.backend.entity.actonia;

import java.util.Date;

public class InternalLinkPageRankInstanceEntity {
	
	public static Integer STATUS_NOT_STARTED = 0;
	public static Integer STATUS_PROCESSING = 1;
	public static Integer STATUS_SUCCESS = 2;
	public static Integer STATUS_FAILURE = 3;

	public static Integer SUMMARY_STATUS_NOT_STARTED = 0;
	public static Integer SUMMARY_STATUS_PROCESSING = 1;
	public static Integer SUMMARY_STATUS_SUCCESS = 2;
	public static Integer SUMMARY_STATUS_FAILURE = 3;
	
	public static Integer MUTUAL_STATUS_NOT_START = 0;
	public static Integer MUTUAL_STATUS_SENT_EXTRACT_REQUEST = 11;
	public static Integer MUTUAL_STATUS_EXTRACT_SUCCESS = 12;
	public static Integer MUTUAL_STATUS_EXTRACT_FAILURE = 13;
	public static Integer MUTUAL_STATUS_SENT_UPLOAD_TO_ARANGO_REQUEST = 21;
	public static Integer MUTUAL_STATUS_UPLOAD_TO_ARANGO_SUCCESS = 22;
	public static Integer MUTUAL_STATUS_UPLOAD_TO_ARANGO_FAILURE = 23;
	public static Integer MUTUAL_STATUS_SENT_CALCULATE_PR_REQUEST = 31;
	public static Integer MUTUAL_STATUS_CALCULATE_PR_SUCCESS = 32;
	public static Integer MUTUAL_STATUS_CALCULATE_PR_FAILURE = 33;
	public static Integer MUTUAL_STATUS_SENT_UPLOAD_PR_TO_TMP_REQUEST = 41;
	public static Integer MUTUAL_STATUS_UPLOAD_PR_TO_TMP_SUCCESS = 42;
	public static Integer MUTUAL_STATUS_UPLOAD_PR_TO_TMP_FAILURE = 43;
	public static Integer MUTUAL_STATUS_SENT_UPLOAD_PR_TO_FINAL_REQUEST = 51;
	public static Integer MUTUAL_STATUS_UPLOAD_PR_TO_FINAL_SUCCESS = 52;
	public static Integer MUTUAL_STATUS_UPLOAD_PR_TO_FINAL_FAILURE = 53;

	private Integer id;
	private Integer enabled;
	private Integer ownDomainId;
	private Integer crawlRequestLogId;
	private Integer summaryDataCount;
	private Integer status;
	private Integer mutualStatus;
	private Integer summaryStatus;
	private String fullPathFileName;
	private Date extractStartTime;
	private Date extractEndTime;
	private Date loadToArangoStartTime;
	private Date loadToArangoEndTime;
	private Date calcPrStartTime;
	private Date calcPrEndTime;
	private Date loadPrTmpStartTime;
	private Date loadPrTmpEndTime;
	private Date loadPrFinalStartTime;
	private Date loadPrFinalEndTime;
	private Integer extractRetryTimes;
	private Integer loadToArangoRetryTimes;
	private Integer calcPrRetryTimes;
	private Integer loadPrTmpRetryTimes;
	private Integer loadPrFinalRetryTimes;
	private String errorMessage;
	private Date createDate;
	private Date updateDate;
	
	public Integer getSummaryStatus() {
		return summaryStatus;
	}

	public void setSummaryStatus(Integer summaryStatus) {
		this.summaryStatus = summaryStatus;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getCrawlRequestLogId() {
		return crawlRequestLogId;
	}

	public void setCrawlRequestLogId(Integer crawlRequestLogId) {
		this.crawlRequestLogId = crawlRequestLogId;
	}

	public Integer getSummaryDataCount() {
		return summaryDataCount;
	}

	public void setSummaryDataCount(Integer summaryDataCount) {
		this.summaryDataCount = summaryDataCount;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getMutualStatus() {
		return mutualStatus;
	}

	public void setMutualStatus(Integer mutualStatus) {
		this.mutualStatus = mutualStatus;
	}

	public String getFullPathFileName() {
		return fullPathFileName;
	}

	public void setFullPathFileName(String fullPathFileName) {
		this.fullPathFileName = fullPathFileName;
	}

	public Date getExtractStartTime() {
		return extractStartTime;
	}

	public void setExtractStartTime(Date extractStartTime) {
		this.extractStartTime = extractStartTime;
	}

	public Date getExtractEndTime() {
		return extractEndTime;
	}

	public void setExtractEndTime(Date extractEndTime) {
		this.extractEndTime = extractEndTime;
	}

	public Date getLoadToArangoStartTime() {
		return loadToArangoStartTime;
	}

	public void setLoadToArangoStartTime(Date loadToArangoStartTime) {
		this.loadToArangoStartTime = loadToArangoStartTime;
	}

	public Date getLoadToArangoEndTime() {
		return loadToArangoEndTime;
	}

	public void setLoadToArangoEndTime(Date loadToArangoEndTime) {
		this.loadToArangoEndTime = loadToArangoEndTime;
	}

	public Date getCalcPrStartTime() {
		return calcPrStartTime;
	}

	public void setCalcPrStartTime(Date calcPrStartTime) {
		this.calcPrStartTime = calcPrStartTime;
	}

	public Date getCalcPrEndTime() {
		return calcPrEndTime;
	}

	public void setCalcPrEndTime(Date calcPrEndTime) {
		this.calcPrEndTime = calcPrEndTime;
	}

	public Date getLoadPrTmpStartTime() {
		return loadPrTmpStartTime;
	}

	public void setLoadPrTmpStartTime(Date loadPrTmpStartTime) {
		this.loadPrTmpStartTime = loadPrTmpStartTime;
	}

	public Date getLoadPrTmpEndTime() {
		return loadPrTmpEndTime;
	}

	public void setLoadPrTmpEndTime(Date loadPrTmpEndTime) {
		this.loadPrTmpEndTime = loadPrTmpEndTime;
	}

	public Date getLoadPrFinalStartTime() {
		return loadPrFinalStartTime;
	}

	public void setLoadPrFinalStartTime(Date loadPrFinalStartTime) {
		this.loadPrFinalStartTime = loadPrFinalStartTime;
	}

	public Date getLoadPrFinalEndTime() {
		return loadPrFinalEndTime;
	}

	public void setLoadPrFinalEndTime(Date loadPrFinalEndTime) {
		this.loadPrFinalEndTime = loadPrFinalEndTime;
	}

	public Integer getExtractRetryTimes() {
		return extractRetryTimes;
	}

	public void setExtractRetryTimes(Integer extractRetryTimes) {
		this.extractRetryTimes = extractRetryTimes;
	}

	public Integer getLoadToArangoRetryTimes() {
		return loadToArangoRetryTimes;
	}

	public void setLoadToArangoRetryTimes(Integer loadToArangoRetryTimes) {
		this.loadToArangoRetryTimes = loadToArangoRetryTimes;
	}

	public Integer getCalcPrRetryTimes() {
		return calcPrRetryTimes;
	}

	public void setCalcPrRetryTimes(Integer calcPrRetryTimes) {
		this.calcPrRetryTimes = calcPrRetryTimes;
	}

	public Integer getLoadPrTmpRetryTimes() {
		return loadPrTmpRetryTimes;
	}

	public void setLoadPrTmpRetryTimes(Integer loadPrTmpRetryTimes) {
		this.loadPrTmpRetryTimes = loadPrTmpRetryTimes;
	}

	public Integer getLoadPrFinalRetryTimes() {
		return loadPrFinalRetryTimes;
	}

	public void setLoadPrFinalRetryTimes(Integer loadPrFinalRetryTimes) {
		this.loadPrFinalRetryTimes = loadPrFinalRetryTimes;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

}
