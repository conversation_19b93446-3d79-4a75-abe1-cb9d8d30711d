package seoclarity.backend.entity.actonia;

public class GrouptagCompetitorRel {
	private Integer id;
	private Integer ownDomainId;
	private Integer grouptagId;
	private Integer competitorId;
	private String competitorDomain;
	
	public String getCompetitorDomain() {
		return competitorDomain;
	}

	public void setCompetitorDomain(String competitorDomain) {
		this.competitorDomain = competitorDomain;
	}

	public static final Integer SATAUS_UNIVERSAL = 5;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getCompetitorId() {
		return competitorId;
	}

	public void setCompetitorId(Integer competitorId) {
		this.competitorId = competitorId;
	}

	public Integer getGrouptagId() {
		return grouptagId;
	}

	public void setGrouptagId(Integer grouptagId) {
		this.grouptagId = grouptagId;
	}

	
}
