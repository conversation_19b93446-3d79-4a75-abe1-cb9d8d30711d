package seoclarity.backend.entity.actonia;
/**
 * 
 */


import java.util.Date;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.utils.FormatUtils;

public class ResourceAddDetailEntity {
	
	public static final int STATUS_PROCESS_FINISHED = 2;
	public static final int STATUS_PROCESS_ERROR = 3;	
	public static final int STATUS_INVALID = 4;
	
	public static final int STATUS_RANK_FINISHED = 2;
	public static final int STATUS_RANK_ERROR = 3;
	public static final int STATUS_RANK_NO_NEED_RANKED = 4;

	public static final int STATUS_SYNC_PROCESS_FINISHED = 2;
	public static final int STATUS_SYNC_PROCESS_ERROR = 3;
	public static final int STATUS_SYNC_INVALID = 4;

	public static final int STATUS_SYNC_PROCESS_PARENT_CHILD_FINISHED = 6;
	public static final int STATUS_SYNC_PROCESS_PARENT_CHILD_ERROR = 7;
	public static final int STATUS_SYNC_PARENT_CHILD_INVALID = 8;

	public static final String MD5_SPLIT = "	";
	
	public static final int RESOURCE_CATEGORY_NATIONAL_KEYWORD = 1;
	public static final int RESOURCE_CATEGORY_GEO_KEYWORD = 2;
	public static final int RESOURCE_CATEGORY_NATIONAL_GEO_KEYWORD = 3;

	private Integer id;
	private Integer operationType;
	private Integer userId;
	private Integer resourceInfoId;
	private Integer ownDomainId;
	private String resourceMain;
	private String resourceSubordinate;
	private String resourceAdditional;
	private Integer resourceCategory;
	private String resource_md5;
	private Integer createDate;
	private Integer status;
	private Date processDate;
	private String errorMessage;
	private Integer statusRank;
	private Date processDateRank;
	private String errorMessageRank;
	//https://www.wrike.com/open.htm?id=446810629
	private Integer statusSync;
	private Date processDateSync;
	private String errorMessageSync;

	private String resourceSearchengines;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getOperationType() {
		return operationType;
	}

	public void setOperationType(Integer operationType) {
		this.operationType = operationType;
	}
	
	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Integer getResourceInfoId() {
		return resourceInfoId;
	}

	public void setResourceInfoId(Integer resourceInfoId) {
		this.resourceInfoId = resourceInfoId;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getResourceMain() {
		return resourceMain;
	}

	public void setResourceMain(String resourceMain) {
		this.resourceMain = resourceMain;
	}

	public String getResourceSubordinate() {
		return resourceSubordinate;
	}

	public void setResourceSubordinate(String resourceSubordinate) {
		this.resourceSubordinate = resourceSubordinate;
	}
	
	public String getResourceAdditional() {
		return resourceAdditional;
	}

	public void setResourceAdditional(String resourceAdditional) {
		this.resourceAdditional = resourceAdditional;
	}

	public Integer getResourceCategory() {
		return resourceCategory;
	}

	public void setResourceCategory(Integer resourceCategory) {
		this.resourceCategory = resourceCategory;
	}

	public String getResource_md5() {
		return resource_md5;
	}

	public void setResource_md5(String resource_md5) {
		this.resource_md5 = resource_md5;
	}
	
	public Integer getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Integer createDate) {
		this.createDate = createDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getProcessDate() {
		return processDate;
	}

	public void setProcessDate(Date processDate) {
		this.processDate = processDate;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public Integer getStatusRank() {
		return statusRank;
	}

	public void setStatusRank(Integer statusRank) {
		this.statusRank = statusRank;
	}

	public Date getProcessDateRank() {
		return processDateRank;
	}

	public void setProcessDateRank(Date processDateRank) {
		this.processDateRank = processDateRank;
	}

	public String getErrorMessageRank() {
		return errorMessageRank;
	}

	public void setErrorMessageRank(String errorMessageRank) {
		this.errorMessageRank = errorMessageRank;
	}

	public Integer getStatusSync() {
		return statusSync;
	}

	public void setStatusSync(Integer statusSync) {
		this.statusSync = statusSync;
	}

	public Date getProcessDateSync() {
		return processDateSync;
	}

	public void setProcessDateSync(Date processDateSync) {
		this.processDateSync = processDateSync;
	}

	public String getErrorMessageSync() {
		return errorMessageSync;
	}

	public void setErrorMessageSync(String errorMessageSync) {
		this.errorMessageSync = errorMessageSync;
	}

	public String getResourceSearchengines() {
		return resourceSearchengines;
	}

	public void setResourceSearchengines(String resourceSearchengines) {
		this.resourceSearchengines = resourceSearchengines;
	}

	/**
	 * to keep detail data unique
	 * @param valueMain
	 * @param valueSubordinate
	 * @return
	 */
	public static String calculateMd5(String valueMain, String valueSubordinate) {
		String input = valueMain;
		if (StringUtils.isNotBlank(valueSubordinate)) {
			input = input + MD5_SPLIT + valueSubordinate;
		}
		return FormatUtils.hexDigest(input.getBytes());
	}
	
	public static String calculateMd5(String valueMain, String valueSubordinate, String engines, Integer date) {
		String input = valueMain;
		if (StringUtils.isNotBlank(valueSubordinate)) {
			input = input + MD5_SPLIT + valueSubordinate;
		}
		if (StringUtils.isNotBlank(engines)) {
			input = input + MD5_SPLIT + engines;
		}
		if (date > 0) {
			input = input + MD5_SPLIT + date;
		}
		return FormatUtils.hexDigest(input.getBytes());
	}
	
	public static String calculateMd5(String valueMain, String valueSubordinate, String engines) {
		String input = valueMain;
		if (StringUtils.isNotBlank(valueSubordinate)) {
			input = input + MD5_SPLIT + valueSubordinate;
		}
		if (StringUtils.isNotBlank(engines)) {
			input = input + MD5_SPLIT + engines;
		}
		return FormatUtils.hexDigest(input.getBytes());
	}

}