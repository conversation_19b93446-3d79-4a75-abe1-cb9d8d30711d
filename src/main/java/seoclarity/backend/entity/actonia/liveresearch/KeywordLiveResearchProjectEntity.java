package seoclarity.backend.entity.actonia.liveresearch;

import lombok.ToString;

import java.util.Date;

@ToString
public class KeywordLiveResearchProjectEntity {

    public static final int ENABLED = 1;
    public static final int DISABLED = 0;

	public static final int STATUS_PENDING = 0;
	public static final int STATUS_PROCESSING = 1;
	public static final int STATUS_CRAWL_COMPLETED = 2;
	public static final int STATUS_SEND_ERROR = 3;
	public static final int STATUS_UPLOAD_COMPLETED = 4;

    public static final int RETRIEVESV_STATUS_NOT_STARTED = 0;
    public static final int RETRIEVESV_STATUS_PROCESSING = 1;
    public static final int RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR = 2;
    public static final int RETRIEVESV_STATUS_ERROR = 3;
    public static final int RETRIEVESV_STATUS_NO_NEED_RETRIEVE_SV = 4;
    public static final int RETRIEVESV_STATUS_SKIP_NO_VALID_ADWORDS_GEO_ID = 5;

    public static final int RANK_UPLOAD_STATUS_NOT_STARTED = 0;
    public static final int RANK_UPLOAD_STATUS_PROCESSING = 1;
    public static final int RANK_UPLOAD_STATUS_COMPLETED_WITHOUT_ERROR = 2;
    public static final int RANK_UPLOAD_STATUS_ERROR = 3;

    private int id;
    private int enabled;
    private int ownDomainId;
    private String projectName;
    private int createDate;
    private int createUserId;
    private String countryCode;
    private String languageCode;
    private int searchEngineId;
    private int languageId;
    private String customizePrefix;
    private int status;
    private int svRetrieveFlg;
    private int svRetrieveStatus;
    private String sqsName;
	private Date sendToSQSStartTime;
    private Date sendToSQSEndTime;
    private Integer messagesInSQS;
    private Integer messagesInFlight;
    private Date crawlCompleteTime;
	private Integer startedEC2Instances;
    private Integer startedEC2Times;
    private Date createdAt;
    private Date updatedAt;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getEnabled() {
		return enabled;
	}

	public void setEnabled(int enabled) {
		this.enabled = enabled;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public int getCreateDate() {
		return createDate;
	}

	public void setCreateDate(int createDate) {
		this.createDate = createDate;
	}

	public int getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(int createUserId) {
		this.createUserId = createUserId;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getLanguageCode() {
		return languageCode;
	}

	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}

	public int getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(int searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	public int getLanguageId() {
		return languageId;
	}

	public void setLanguageId(int languageId) {
		this.languageId = languageId;
	}

	public String getCustomizePrefix() {
		return customizePrefix;
	}

	public void setCustomizePrefix(String customizePrefix) {
		this.customizePrefix = customizePrefix;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public int getSvRetrieveFlg() {
		return svRetrieveFlg;
	}

	public void setSvRetrieveFlg(int svRetrieveFlg) {
		this.svRetrieveFlg = svRetrieveFlg;
	}

	public int getSvRetrieveStatus() {
		return svRetrieveStatus;
	}

	public void setSvRetrieveStatus(int svRetrieveStatus) {
		this.svRetrieveStatus = svRetrieveStatus;
	}

	public String getSqsName() {
		return sqsName;
	}

	public void setSqsName(String sqsName) {
		this.sqsName = sqsName;
	}

	public Date getSendToSQSStartTime() {
		return sendToSQSStartTime;
	}

	public void setSendToSQSStartTime(Date sendToSQSStartTime) {
		this.sendToSQSStartTime = sendToSQSStartTime;
	}

	public Date getSendToSQSEndTime() {
		return sendToSQSEndTime;
	}

	public void setSendToSQSEndTime(Date sendToSQSEndTime) {
		this.sendToSQSEndTime = sendToSQSEndTime;
	}

	public Integer getMessagesInSQS() {
		return messagesInSQS;
	}

	public void setMessagesInSQS(Integer messagesInSQS) {
		this.messagesInSQS = messagesInSQS;
	}

	public Integer getMessagesInFlight() {
		return messagesInFlight;
	}

	public void setMessagesInFlight(Integer messagesInFlight) {
		this.messagesInFlight = messagesInFlight;
	}
	
	public Date getCrawlCompleteTime() {
		return crawlCompleteTime;
	}

	public void setCrawlCompleteTime(Date crawlCompleteTime) {
		this.crawlCompleteTime = crawlCompleteTime;
	}

	public Integer getStartedEC2Instances() {
		return startedEC2Instances;
	}

	public void setStartedEC2Instances(Integer startedEC2Instances) {
		this.startedEC2Instances = startedEC2Instances;
	}

	public Integer getStartedEC2Times() {
		return startedEC2Times;
	}

	public void setStartedEC2Times(Integer startedEC2Times) {
		this.startedEC2Times = startedEC2Times;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
}