package seoclarity.backend.entity.actonia.liveresearch;

import java.util.Date;

public class KeywordLiveResearchDetailEntity {
	
    public static final int SVRETRIEVE_STATUS_NOT_STARTED = 0;
    public static final int SVRETRIEVE_STATUS_PROCESSING = 1;
    public static final int SVRETRIEVE_STATUS_RETRIEVED_SUCCESSFULLY = 2;
    public static final int SVRETRIEVE_STATUS_RETRIEVED_ERROR = 3;
    public static final int SVRETRIEVE_STATUS_EXIST_IN_RANKCHECK = 4;
    
    public static final int KEYWORD_STATUS_PENDING = 0;
    public static final int KEYWORD_STATUS_PROCESSING = 1;
    public static final int KEYWORD_STATUS_COMPLETED = 2;
    
    public static final int SENT_TO_RANK_SQS_YES = 1;
    public static final int SENT_TO_RANK_SQS_NO = 0;
    
    private int id;
    private int projectId;
    private int searchEngineId;
    private int languageId;
    private String keywordName;
    private String clarityDBKeywordHash;
    private Integer suggestCount;
    private Integer keywordStatus;
	private Integer svRetrieveStatus;
    private Date createdAt;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getProjectId() {
		return projectId;
	}

	public void setProjectId(int projectId) {
		this.projectId = projectId;
	}

	public int getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(int searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	public int getLanguageId() {
		return languageId;
	}

	public void setLanguageId(int languageId) {
		this.languageId = languageId;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public String getClarityDBKeywordHash() {
		return clarityDBKeywordHash;
	}

	public void setClarityDBKeywordHash(String clarityDBKeywordHash) {
		this.clarityDBKeywordHash = clarityDBKeywordHash;
	}

	public Integer getSuggestCount() {
		return suggestCount;
	}

	public void setSuggestCount(Integer suggestCount) {
		this.suggestCount = suggestCount;
	}

	public Integer getKeywordStatus() {
		return keywordStatus;
	}

	public void setKeywordStatus(Integer keywordStatus) {
		this.keywordStatus = keywordStatus;
	}

	public Integer getSvRetrieveStatus() {
		return svRetrieveStatus;
	}

	public void setSvRetrieveStatus(Integer svRetrieveStatus) {
		this.svRetrieveStatus = svRetrieveStatus;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
}