package seoclarity.backend.entity.actonia;

public class SearchvolRankcheckidEntity {

	private Long keywordRankcheckId;
	private Integer ownDomainId;
	private Long avgSearchVolume;
	private Integer searchVolumneDate;
	private Integer locationId;
	
	
	public Long getKeywordRankcheckId() {
		return keywordRankcheckId;
	}
	public void setKeywordRankcheckId(Long keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}
	public Integer getOwnDomainId() {
		return ownDomainId;
	}
	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	public Long getAvgSearchVolume() {
		return avgSearchVolume;
	}
	public void setAvgSearchVolume(Long avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume;
	}
	public Integer getSearchVolumneDate() {
		return searchVolumneDate;
	}
	public void setSearchVolumneDate(Integer searchVolumneDate) {
		this.searchVolumneDate = searchVolumneDate;
	}

	public Integer getLocationId() {
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}
}
