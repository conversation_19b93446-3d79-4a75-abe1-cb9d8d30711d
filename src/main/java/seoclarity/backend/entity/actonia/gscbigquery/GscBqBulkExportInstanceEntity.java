package seoclarity.backend.entity.actonia.gscbigquery;

import java.util.Date;

public class GscBqBulkExportInstanceEntity {

    public static final int EXTRACT_STATUS_PENDING = 0;
    public static final int EXTRACT_STATUS_RUNNING = 1;
    public static final int EXTRACT_STATUS_COMPLETE = 2;
    public static final int EXTRACT_STATUS_ERROR = 3;

    public static final int UPLOAD_STATUS_PENDING = 0;
    public static final int UPLOAD_STATUS_RUNNING = 1;
    public static final int UPLOAD_STATUS_COMPLETE = 2;
    public static final int UPLOAD_STATUS_ERROR = 3;

    private Integer id;
    private Integer domainId;
    private String bqNamespace;
    private String bqDataDate;
    private String bqEpochVersion;
    private Date bqPublishRime;
    private Integer extractStatus;
    private Integer uploadStatus;
    private String s3FullPathFileName;
    private Date createdDate;
    private Date updateDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDomainId() {
        return domainId;
    }

    public void setDomainId(Integer domainId) {
        this.domainId = domainId;
    }

    public String getBqNamespace() {
        return bqNamespace;
    }

    public void setBqNamespace(String bqNamespace) {
        this.bqNamespace = bqNamespace;
    }

    public String getBqDataDate() {
        return bqDataDate;
    }

    public void setBqDataDate(String bqDataDate) {
        this.bqDataDate = bqDataDate;
    }

    public String getBqEpochVersion() {
        return bqEpochVersion;
    }

    public void setBqEpochVersion(String bqEpochVersion) {
        this.bqEpochVersion = bqEpochVersion;
    }

    public Date getBqPublishRime() {
        return bqPublishRime;
    }

    public void setBqPublishRime(Date bqPublishRime) {
        this.bqPublishRime = bqPublishRime;
    }

    public Integer getExtractStatus() {
        return extractStatus;
    }

    public void setExtractStatus(Integer extractStatus) {
        this.extractStatus = extractStatus;
    }

    public Integer getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(Integer uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public String getS3FullPathFileName() {
        return s3FullPathFileName;
    }

    public void setS3FullPathFileName(String s3FullPathFileName) {
        this.s3FullPathFileName = s3FullPathFileName;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
