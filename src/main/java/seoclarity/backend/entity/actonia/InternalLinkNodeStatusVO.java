package seoclarity.backend.entity.actonia;

public class InternalLinkNodeStatusVO {

	/**
	 * {
		  "speed": 1.101250373468565,
		  "total_time": "2:09:45.695442",
		  "domain_id_i": "8701",
		  "starting_url_crawl_status": [],
		  "jobId": "f9b9ae8dc1bdc19ff17acfd2a674329b",
		  "crawl_request_log_id_i": "124013",
		  "scrapy_info": {
		    "PageLinkItem": 296548,
		    "page_link_item_count": 1854
		  }
		}
	 */
	
	private Integer domain_id_i;
	
	private String crawl_request_log_id_i;
	
	private ScrapyInfo scrapy_info;

	public Integer getDomain_id_i() {
		return domain_id_i;
	}

	public void setDomain_id_i(Integer domain_id_i) {
		this.domain_id_i = domain_id_i;
	}

	public String getCrawl_request_log_id_i() {
		return crawl_request_log_id_i;
	}

	public void setCrawl_request_log_id_i(String crawl_request_log_id_i) {
		this.crawl_request_log_id_i = crawl_request_log_id_i;
	}

	public ScrapyInfo getScrapy_info() {
		return scrapy_info;
	}

	public void setScrapy_info(ScrapyInfo scrapy_info) {
		this.scrapy_info = scrapy_info;
	}
	
	
	public class ScrapyInfo{
		
		private Long PageLinkItem;

		private Long page_link_item_count;

		public Long getPageLinkItem() {
			return PageLinkItem;
		}

		public void setPageLinkItem(Long PageLinkItem) {
			this.PageLinkItem = PageLinkItem;
		}

		public Long getPage_link_item_count() {
			return page_link_item_count;
		}

		public void setPage_link_item_count(Long page_link_item_count) {
			this.page_link_item_count = page_link_item_count;
		}
		
	}
}

