package seoclarity.backend.entity.actonia;

import java.util.Date;

public class TagParentChildRelEntity {
	
	public static final int PAGE_TAG = 1;
    public static final int KEYWORD_TAG = 2;
	
	private long id;
	private int ownDomainId;
	private int tagType;
	private int childTagId;
	private int parentTagId;
	private int createUser;
	private Date createDate;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public int getTagType() {
		return tagType;
	}

	public void setTagType(int tagType) {
		this.tagType = tagType;
	}

	public int getChildTagId() {
		return childTagId;
	}

	public void setChildTagId(int childTagId) {
		this.childTagId = childTagId;
	}

	public int getParentTagId() {
		return parentTagId;
	}

	public void setParentTagId(int parentTagId) {
		this.parentTagId = parentTagId;
	}

	public int getCreateUser() {
		return createUser;
	}

	public void setCreateUser(int createUser) {
		this.createUser = createUser;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
}