/**
 * 
 */
package seoclarity.backend.entity.actonia;

import java.util.Date;

public class AutorunDetailEntity {
	
	public static final Integer STATUS_NEW = 0;
	public static final Integer STATUS_PROICESSING = 1;
	public static final Integer STATUS_FINISH_WITHOUT_ERROR = 2;
	public static final Integer STATUS_FINISH_WITH_ERROR = 3;

	private Integer id;
	private Integer autorunInfoId;
	private Integer ownDomainId;
	private Integer profileId;
	private Integer status;
	private Date createDate;
	private Date processEndDate;
	private Integer summaryFrequency;
	private Integer startDate;
	private Integer endDate;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getProcessEndDate() {
		return processEndDate;
	}

	public void setProcessEndDate(Date processEndDate) {
		this.processEndDate = processEndDate;
	}

	public Integer getAutorunInfoId() {
		return autorunInfoId;
	}

	public void setAutorunInfoId(Integer autorunInfoId) {
		this.autorunInfoId = autorunInfoId;
	}

	public Integer getProfileId() {
		return profileId;
	}

	public void setProfileId(Integer profileId) {
		this.profileId = profileId;
	}

	public Integer getSummaryFrequency() {
		return summaryFrequency;
	}

	public void setSummaryFrequency(Integer summaryFrequency) {
		this.summaryFrequency = summaryFrequency;
	}

	public Integer getStartDate() {
		return startDate;
	}

	public void setStartDate(Integer startDate) {
		this.startDate = startDate;
	}

	public Integer getEndDate() {
		return endDate;
	}

	public void setEndDate(Integer endDate) {
		this.endDate = endDate;
	}
	
	

}
