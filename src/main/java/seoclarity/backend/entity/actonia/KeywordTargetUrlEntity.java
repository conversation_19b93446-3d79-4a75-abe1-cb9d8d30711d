package seoclarity.backend.entity.actonia;

import org.apache.commons.lang.StringEscapeUtils;

import java.net.URLDecoder;

public class KeywordTargetUrlEntity {

	public static final int ADD_BY_AUTOMAIC_ASSOCIATIONS = 10;

	public static final int USAGE_NO = 0;

	public static final int USAGE_YES = 1;

	private Integer id;

	private Long keywordId;

	private Long targetUrlId;

	private String keywordName;

	private Integer usage;

	private String url;
	
	//scott - https://www.wrike.com/open.htm?id=302753041
	private String keywordHash;
	private String urlHash;
	private Integer ownDomainId;
	private String urlMurmur3Hash;
	private String customUrlMurmur3Hash;
	
	public String getUrlMurmur3Hash() {
		return urlMurmur3Hash;
	}

	public void setUrlMurmur3Hash(String urlMurmur3Hash) {
		this.urlMurmur3Hash = urlMurmur3Hash;
	}

	public String getCustomUrlMurmur3Hash() {
		return customUrlMurmur3Hash;
	}

	public void setCustomUrlMurmur3Hash(String customUrlMurmur3Hash) {
		this.customUrlMurmur3Hash = customUrlMurmur3Hash;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Long getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(Long keywordId) {
		this.keywordId = keywordId;
	}

	public Long getTargetUrlId() {
		return targetUrlId;
	}

	public void setTargetUrlId(Long targetUrlId) {
		this.targetUrlId = targetUrlId;
	}

	public String getKeywordHash() {
		return keywordHash;
	}

	public void setKeywordHash(String keywordHash) {
		this.keywordHash = keywordHash;
	}

	public String getUrlHash() {
		return urlHash;
	}

	public void setUrlHash(String urlHash) {
		this.urlHash = urlHash;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getKeywordForHtml() {
		try {
			return StringEscapeUtils.escapeHtml(URLDecoder.decode(this.keywordName, "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public Integer getUsage() {
		return usage;
	}

	public void setUsage(Integer usage) {
		this.usage = usage;
	}

	@Override
	public String toString() {
		return "KeywordTargetUrlEntity{" + "id=" + id + ", keywordId=" + keywordId + ", targetUrlId=" + targetUrlId + ", keywordName='" + keywordName + '\''
		        + ", usage=" + usage + '}';
	}
}
