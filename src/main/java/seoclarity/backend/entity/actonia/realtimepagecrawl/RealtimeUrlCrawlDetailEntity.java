package seoclarity.backend.entity.actonia.realtimepagecrawl;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigInteger;
import java.util.Date;

@Data
@EqualsAndHashCode
public class RealtimeUrlCrawlDetailEntity {


    public static final int SEND_NOT_SEND = 0;
    public static final int SEND_TO_SQS = 1;

    private Integer id;
    private Integer ownDomainId;
    private Integer crawlInstanceId; // RealtimeUrlCrawlInstance.id
    private String url;
    private String urlMurmur3Hash; // murmurHash3_64(url)
    private Integer sendStatus; // 0: not sent, 1: sent to SQS
    private Integer uploadStatus; // 0: not upload, 1: uploaded to CDB
    private Date createdAt;
}
