package seoclarity.backend.entity.actonia.extract;

import java.util.Date;

public class ExtractScriptDetailEntity {

	public static final int STATUS_PROCESSING = 1;
	public static final int STATUS_SUCCESS = 2;
	public static final int STATUS_FAILURE = 3;
	public static final int STATUS_TRANSPORT_FAILURE = 4;
	public static final int SKIP_TO_RUN = 5;

	private int id;
	private int extractScriptId;
	private int extractInstanceId;
	private Integer targetDate;
	private int status;
	private String fatalError;
	private String extraError;
	private String serverHostname;
	private String serverPath;
	private String destServerHostname;
	private String outputFile;
	private String additionalDestinationServerHostname;
	private String additionalOutputFile;
	private int outputDataCount;
	private int outputFileSizeKB;
	private int elapsedSeconds;
	private Date startedTime;
	private Date endedTime;
	private Date createdAt;

	private Boolean isExpectedProcessHour;
	
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public int getExtractScriptId() {
		return extractScriptId;
	}
	public void setExtractScriptId(int extractScriptId) {
		this.extractScriptId = extractScriptId;
	}
	public int getExtractInstanceId() {
		return extractInstanceId;
	}
	public void setExtractInstanceId(int extractInstanceId) {
		this.extractInstanceId = extractInstanceId;
	}
	public Integer getTargetDate() {
		return targetDate;
	}
	public void setTargetDate(Integer targetDate) {
		this.targetDate = targetDate;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public String getFatalError() {
		return fatalError;
	}
	public void setFatalError(String fatalError) {
		this.fatalError = fatalError;
	}
	public String getExtraError() {
		return extraError;
	}
	public void setExtraError(String extraError) {
		this.extraError = extraError;
	}
	public String getServerHostname() {
		return serverHostname;
	}
	public void setServerHostname(String serverHostname) {
		this.serverHostname = serverHostname;
	}
	public String getServerPath() {
		return serverPath;
	}
	public void setServerPath(String serverPath) {
		this.serverPath = serverPath;
	}
	public String getDestServerHostname() {
		return destServerHostname;
	}
	public void setDestServerHostname(String destServerHostname) {
		this.destServerHostname = destServerHostname;
	}
	public String getOutputFile() {
		return outputFile;
	}
	public void setOutputFile(String outputFile) {
		this.outputFile = outputFile;
	}
	public String getAdditionalDestinationServerHostname() {
		return additionalDestinationServerHostname;
	}
	public void setAdditionalDestinationServerHostname(String additionalDestinationServerHostname) {
		this.additionalDestinationServerHostname = additionalDestinationServerHostname;
	}
	public String getAdditionalOutputFile() {
		return additionalOutputFile;
	}
	public void setAdditionalOutputFile(String additionalOutputFile) {
		this.additionalOutputFile = additionalOutputFile;
	}
	public int getOutputDataCount() {
		return outputDataCount;
	}
	public void setOutputDataCount(int outputDataCount) {
		this.outputDataCount = outputDataCount;
	}
	public int getOutputFileSizeKB() {
		return outputFileSizeKB;
	}
	public void setOutputFileSizeKB(int outputFileSizeKB) {
		this.outputFileSizeKB = outputFileSizeKB;
	}
	public int getElapsedSeconds() {
		return elapsedSeconds;
	}
	public void setElapsedSeconds(int elapsedSeconds) {
		this.elapsedSeconds = elapsedSeconds;
	}
	public Date getStartedTime() {
		return startedTime;
	}
	public void setStartedTime(Date startedTime) {
		this.startedTime = startedTime;
	}
	public Date getEndedTime() {
		return endedTime;
	}
	public void setEndedTime(Date endedTime) {
		this.endedTime = endedTime;
	}
	public Date getCreatedAt() {
		return createdAt;
	}
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}


	public Boolean getIsExpectedProcessHour() {
		return isExpectedProcessHour;
	}

	public void setIsExpectedProcessHour(Boolean expectedProcessHour) {
		isExpectedProcessHour = expectedProcessHour;
	}
}
