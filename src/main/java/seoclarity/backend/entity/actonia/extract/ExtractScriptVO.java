package seoclarity.backend.entity.actonia.extract;

public class ExtractScriptVO {

    private Integer extractScriptId;
    private Integer extractInstanceId;
    private String projectName;
    private String fullQulifiedClass;
    private int category;
    private int ownDomainId;
    private String country;
    private int tagId;
    private int rankType;
    private String device;


    public Integer getExtractScriptId() {
        return extractScriptId;
    }

    public void setExtractScriptId(Integer extractScriptId) {
        this.extractScriptId = extractScriptId;
    }

    public Integer getExtractInstanceId() {
        return extractInstanceId;
    }

    public void setExtractInstanceId(Integer extractInstanceId) {
        this.extractInstanceId = extractInstanceId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getFullQulifiedClass() {
        return fullQulifiedClass;
    }

    public void setFullQulifiedClass(String fullQulifiedClass) {
        this.fullQulifiedClass = fullQulifiedClass;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public int getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(int ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public int getTagId() {
        return tagId;
    }

    public void setTagId(int tagId) {
        this.tagId = tagId;
    }

    public int getRankType() {
        return rankType;
    }

    public void setRankType(int rankType) {
        this.rankType = rankType;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }
}
