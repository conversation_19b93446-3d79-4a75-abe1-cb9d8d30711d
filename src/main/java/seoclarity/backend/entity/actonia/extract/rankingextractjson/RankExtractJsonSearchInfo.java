package seoclarity.backend.entity.actonia.extract.rankingextractjson;

import java.util.List;

public class RankExtractJsonSearchInfo {

    private Float cpc;
    private Long searchVolume;
    private Integer trueDemand;
    private String googleRecommend;
    // https://www.wrike.com/open.htm?id=1611506956
    // disable totalResults
    private Long totalResults;
    private String keyword;
    private String location;

    private List<String> serpFilterButtonText;
    private List<String> serpFilterKeywords;

    private String keywordDateAdded;
    private List<String> keywordTags;
    private List<String> tagHierarchy;
    private List<String> preferredPages;

    private Double estdTraffic;
    private Float shareOfVoice;
    private Float shareOfMarket;

    private List<String> relatedSearches;

    public Float getCpc() {
        return cpc;
    }

    public void setCpc(Float cpc) {
        this.cpc = cpc;
    }

    public Long getSearchVolume() {
        return searchVolume;
    }

    public void setSearchVolume(Long searchVolume) {
        this.searchVolume = searchVolume;
    }

    public Integer getTrueDemand() {
        return trueDemand;
    }

    public void setTrueDemand(Integer trueDemand) {
        this.trueDemand = trueDemand;
    }

    public String getGoogleRecommend() {
        return googleRecommend;
    }

    public void setGoogleRecommend(String googleRecommend) {
        this.googleRecommend = googleRecommend;
    }

    // https://www.wrike.com/open.htm?id=1611506956
//    public Long getTotalResults() {
//        return totalResults;
//    }
//
//    public void setTotalResults(Long totalResults) {
//        this.totalResults = totalResults;
//    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<String> getSerpFilterButtonText() {
        return serpFilterButtonText;
    }

    public void setSerpFilterButtonText(List<String> serpFilterButtonText) {
        this.serpFilterButtonText = serpFilterButtonText;
    }

    public List<String> getSerpFilterKeywords() {
        return serpFilterKeywords;
    }

    public void setSerpFilterKeywords(List<String> serpFilterKeywords) {
        this.serpFilterKeywords = serpFilterKeywords;
    }

    public String getKeywordDateAdded() {
        return keywordDateAdded;
    }

    public void setKeywordDateAdded(String keywordDateAdded) {
        this.keywordDateAdded = keywordDateAdded;
    }

    public List<String> getKeywordTags() {
        return keywordTags;
    }

    public void setKeywordTags(List<String> keywordTags) {
        this.keywordTags = keywordTags;
    }

    public List<String> getTagHierarchy() {
        return tagHierarchy;
    }

    public void setTagHierarchy(List<String> tagHierarchy) {
        this.tagHierarchy = tagHierarchy;
    }

    public List<String> getPreferredPages() {
        return preferredPages;
    }

    public void setPreferredPages(List<String> preferredPages) {
        this.preferredPages = preferredPages;
    }

    public Double getEstdTraffic() {
        return estdTraffic;
    }

    public void setEstdTraffic(Double estdTraffic) {
        this.estdTraffic = estdTraffic;
    }

    public Float getShareOfVoice() {
        return shareOfVoice;
    }

    public void setShareOfVoice(Float shareOfVoice) {
        this.shareOfVoice = shareOfVoice;
    }

    public Float getShareOfMarket() {
        return shareOfMarket;
    }

    public void setShareOfMarket(Float shareOfMarket) {
        this.shareOfMarket = shareOfMarket;
    }

    public List<String> getRelatedSearches() {
        return relatedSearches;
    }

    public void setRelatedSearches(List<String> relatedSearches) {
        this.relatedSearches = relatedSearches;
    }
}
