package seoclarity.backend.entity.actonia.extract.rankingextractjson;

import java.util.List;

public class RankExtractJsonRankings {

    private String url;
    private String type;
    private Integer webRank;
    private Integer trueRank;

    private Integer visualRank;
    private Integer pixelDepth;
    private RankExtractJsonUrlInformation urlInformation;
    private List<RankExtractJsonSubRanks> subRanks;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getWebRank() {
        return webRank;
    }

    public void setWebRank(Integer webRank) {
        this.webRank = webRank;
    }

    public Integer getTrueRank() {
        return trueRank;
    }

    public void setTrueRank(Integer trueRank) {
        this.trueRank = trueRank;
    }

    public RankExtractJsonUrlInformation getUrlInformation() {
        return urlInformation;
    }

    public void setUrlInformation(RankExtractJsonUrlInformation urlInformation) {
        this.urlInformation = urlInformation;
    }

    public List<RankExtractJsonSubRanks> getSubRanks() {
        return subRanks;
    }

    public void setSubRanks(List<RankExtractJsonSubRanks> subRanks) {
        this.subRanks = subRanks;
    }

    public Integer getVisualRank() {
        return visualRank;
    }

    public void setVisualRank(Integer visualRank) {
        this.visualRank = visualRank;
    }

    public Integer getPixelDepth() {
        return pixelDepth;
    }

    public void setPixelDepth(Integer pixelDepth) {
        this.pixelDepth = pixelDepth;
    }
}
