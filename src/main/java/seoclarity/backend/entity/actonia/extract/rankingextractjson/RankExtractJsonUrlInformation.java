package seoclarity.backend.entity.actonia.extract.rankingextractjson;

public class RankExtractJsonUrlInformation {

    private String title;
    private String meta;
    private String price;
    private String rating;
    private String couponFlag;

    private String priceFlag;
    private String ratingFlag;


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getRating() {
        return rating;
    }

    public void setRating(String rating) {
        this.rating = rating;
    }

    public String getCouponFlag() {
        return couponFlag;
    }

    public void setCouponFlag(String couponFlag) {
        this.couponFlag = couponFlag;
    }

    public String getPriceFlag() {
        return priceFlag;
    }

    public void setPriceFlag(String priceFlag) {
        this.priceFlag = priceFlag;
    }

    public String getRatingFlag() {
        return ratingFlag;
    }

    public void setRatingFlag(String ratingFlag) {
        this.ratingFlag = ratingFlag;
    }
}
