package seoclarity.backend.entity.actonia.extract;

import java.util.Date;

public class ExtractScriptConfigEntity {

	public static final Integer ENABLED = 1;
	public static final Integer DISABLED = 0;

	public static final Integer CATEGORY_RANK = 1;
	public static final Integer CATEGORY_ANALYTICS_DATA = 2;

	public static final Integer SPECIAL_CATEGORY_DOMAIN_LEVEL = 1;
	public static final Integer SPECIAL_CATEGORY_COUNTRY_LEVEL = 2;
	public static final Integer SPECIAL_CATEGORY_TAG_LEVEL = 3;
	public static final Integer SPECIAL_CATEGORY_TARGET_DOMAIN_LEVEL = 4;

	public static final Integer FREQUENCY_DAILY = 1;
	public static final Integer FREQUENCY_WEEKLY = 7;
	public static final Integer FREQUENCY_BI_WEEKLY = 14;
	public static final Integer FREQUENCY_MONTHLY = 30;
	public static final Integer FREQUENCY_OTHER = 99;

	private int id;
	private int enabled;
	private String scriptDisplayName;
	private String projectName;
	private String fullQulifiedClass;
	private int category;
	private String specialCategory;
	private int defaultFrequency;
	private int defaultStartDay;
	private int groupId;
	private int defaultStartProcessHour;
	private int defaultExpectedProcessHour;
	private String buildFileName;
	private String targetNamePrefix;
	private String mailTitle;
	private String mailTo;
	private String mailCc;
	private String remark;
	private Date createdAt;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getEnabled() {
		return enabled;
	}

	public void setEnabled(int enabled) {
		this.enabled = enabled;
	}

	public String getScriptDisplayName() {
		return scriptDisplayName;
	}

	public void setScriptDisplayName(String scriptDisplayName) {
		this.scriptDisplayName = scriptDisplayName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getFullQulifiedClass() {
		return fullQulifiedClass;
	}

	public void setFullQulifiedClass(String fullQulifiedClass) {
		this.fullQulifiedClass = fullQulifiedClass;
	}

	public int getCategory() {
		return category;
	}

	public void setCategory(int category) {
		this.category = category;
	}

	public String getSpecialCategory() {
		return specialCategory;
	}

	public void setSpecialCategory(String specialCategory) {
		this.specialCategory = specialCategory;
	}

	public int getGroupId() {
		return groupId;
	}

	public void setGroupId(int groupId) {
		this.groupId = groupId;
	}

	public String getBuildFileName() {
		return buildFileName;
	}

	public void setBuildFileName(String buildFileName) {
		this.buildFileName = buildFileName;
	}

	public String getTargetNamePrefix() {
		return targetNamePrefix;
	}

	public void setTargetNamePrefix(String targetNamePrefix) {
		this.targetNamePrefix = targetNamePrefix;
	}

	public String getMailTitle() {
		return mailTitle;
	}

	public void setMailTitle(String mailTitle) {
		this.mailTitle = mailTitle;
	}

	public String getMailTo() {
		return mailTo;
	}

	public void setMailTo(String mailTo) {
		this.mailTo = mailTo;
	}

	public String getMailCc() {
		return mailCc;
	}

	public void setMailCc(String mailCc) {
		this.mailCc = mailCc;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public int getDefaultFrequency() {
		return defaultFrequency;
	}

	public void setDefaultFrequency(int defaultFrequency) {
		this.defaultFrequency = defaultFrequency;
	}

	public int getDefaultStartDay() {
		return defaultStartDay;
	}

	public void setDefaultStartDay(int defaultStartDay) {
		this.defaultStartDay = defaultStartDay;
	}

	public int getDefaultStartProcessHour() {
		return defaultStartProcessHour;
	}

	public void setDefaultStartProcessHour(int defaultStartProcessHour) {
		this.defaultStartProcessHour = defaultStartProcessHour;
	}

	public int getDefaultExpectedProcessHour() {
		return defaultExpectedProcessHour;
	}

	public void setDefaultExpectedProcessHour(int defaultExpectedProcessHour) {
		this.defaultExpectedProcessHour = defaultExpectedProcessHour;
	}
}
