package seoclarity.backend.entity.actonia;

/**
 * 
 * com.actonia.saas.model.TKeyword.java
 *
 * @version $Revision:$
 *          $Author:$
 */
public class KeywordRankSerpEntity implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private Integer id;
	private Integer ownDomainId;
	private Integer engineId;
	private Integer languageId;
	private String device;
	private Integer cityId;
	private String keywordName;
	private String rankDate;
	private String rankJson;
	
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getOwnDomainId() {
		return ownDomainId;
	}
	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	public Integer getEngineId() {
		return engineId;
	}
	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}
	public Integer getLanguageId() {
		return languageId;
	}
	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}
	public String getDevice() {
		return device;
	}
	public void setDevice(String device) {
		this.device = device;
	}
	public Integer getCityId() {
		return cityId;
	}
	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}
	public String getKeywordName() {
		return keywordName;
	}
	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}
	public String getRankDate() {
		return rankDate;
	}
	public void setRankDate(String rankDate) {
		this.rankDate = rankDate;
	}
	public String getRankJson() {
		return rankJson;
	}
	public void setRankJson(String rankJson) {
		this.rankJson = rankJson;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	

	
}
