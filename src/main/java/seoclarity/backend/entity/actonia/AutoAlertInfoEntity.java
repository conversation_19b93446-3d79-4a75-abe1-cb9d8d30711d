package seoclarity.backend.entity.actonia;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * https://www.wrike.com/open.htm?id=521118052
 * 
 * <AUTHOR>
 * @date 2020-09-15
 * @path seoclarity.backend.entity.actonia.AutoAlertInfoEntity
 * 
 */
public class AutoAlertInfoEntity {
	public static final int ENABLED_TYPE = 1;
	public static final int DISABLED_TYPE = 0;
	
	public static final int ALERTTYPE_RANK_POSITION_CHANGE = 1001;
	public static final int ALERTTYPE_COMPARE_DOMAIN_RANKING = 1002;
	public static final int ALERTTYPE_PREFERED_PAGE_RANKING = 1101;
	// https://www.wrike.com/open.htm?id=831237636
	public static final int ALERTTYPE_SERP_FEATURE_CHANGE = 1003;
	
	// https://www.wrike.com/open.htm?id=875559635
	// 1) For a specific domain, if ranking url no longer shows or starts showing url feature (rating, faq, stock, price, event), then trigger alert
	// new/lost
	public static final int ALERTTYPE_URL_SERP_FEATURE_RANKING_CHANGE = 1004;
	
	// 2) For a specific domain, if count of ranking urls that show a url feature (Such as rating, faq, stock, price or event) increases or decreases, trigger alert
	// increase/decline
	public static final int ALERTTYPE_URL_SERP_FEATURE_CHANGE = 1005;
	// 3) If the count of keywords showing (rating/fAQ/STOCK/PRICE) anywhere in the top 10 declines (meaning that no url in the top 10 for that keyword has the url level feature), then trigger an alert
	// We need to check the keyword level flag saved for url serp feature in top 10 to identify if the count of keywords where any of the top 10 ranking urls had the serp feature and that reduced or gained
	// increase/decline
	public static final int ALERTTYPE_URL_SERP_FEATURE_CHANGE_KEYWORD_LEVEL_TOP10 = 1006;

	// https://www.wrike.com/open.htm?id=1272800011
	public static final int ALERTTYPE_RANK_POSITION_RANGE_PERCENT_COMPARE = 1007;
	
	public static final Map<String, String> DEFAULT_RULE_SUBJECT_MAP = new HashMap<>();
	static {
		DEFAULT_RULE_SUBJECT_MAP.put(String.valueOf(ALERTTYPE_RANK_POSITION_CHANGE), "Rank Position Change");
		DEFAULT_RULE_SUBJECT_MAP.put(String.valueOf(ALERTTYPE_COMPARE_DOMAIN_RANKING), "Competitor Outranked Me");
		DEFAULT_RULE_SUBJECT_MAP.put(String.valueOf(ALERTTYPE_PREFERED_PAGE_RANKING), "Preferred Page Ranking Change");
		DEFAULT_RULE_SUBJECT_MAP.put(String.valueOf(ALERTTYPE_SERP_FEATURE_CHANGE), "SERP Feature Change");
		DEFAULT_RULE_SUBJECT_MAP.put(String.valueOf(ALERTTYPE_URL_SERP_FEATURE_CHANGE), "URL SERP Feature Change");
		DEFAULT_RULE_SUBJECT_MAP.put(String.valueOf(ALERTTYPE_URL_SERP_FEATURE_CHANGE_KEYWORD_LEVEL_TOP10), "URL SERP Feature In Top10 Keyword Change");
		DEFAULT_RULE_SUBJECT_MAP.put(String.valueOf(ALERTTYPE_RANK_POSITION_RANGE_PERCENT_COMPARE), "Rank Position Range Percent Compare");
	}
	
	private int id;
	private int enabled;
	private int alertType;
	private int frequenceType;
	private int frequenceValue;
	private int ownDomainId;
	private String alertJson;
	private String uiJson;
	private int createUserId;
	private String subject;
	private String sendTo;
	private Date createDate;
	private String alertName;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getEnabled() {
		return enabled;
	}

	public void setEnabled(int enabled) {
		this.enabled = enabled;
	}

	public int getAlertType() {
		return alertType;
	}

	public void setAlertType(int alertType) {
		this.alertType = alertType;
	}

	public int getFrequenceType() {
		return frequenceType;
	}

	public void setFrequenceType(int frequenceType) {
		this.frequenceType = frequenceType;
	}

	public int getFrequenceValue() {
		return frequenceValue;
	}

	public void setFrequenceValue(int frequenceValue) {
		this.frequenceValue = frequenceValue;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getAlertJson() {
		return alertJson;
	}

	public void setAlertJson(String alertJson) {
		this.alertJson = alertJson;
	}

	public int getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(int createUserId) {
		this.createUserId = createUserId;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getSendTo() {
		return sendTo;
	}

	public void setSendTo(String sendTo) {
		this.sendTo = sendTo;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getAlertName() {
		return alertName;
	}

	public void setAlertName(String alertName) {
		this.alertName = alertName;
	}

	public String getUiJson() {
		return uiJson;
	}

	public void setUiJson(String uiJson) {
		this.uiJson = uiJson;
	}

}
