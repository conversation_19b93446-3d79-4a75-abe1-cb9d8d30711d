package seoclarity.backend.entity.actonia;

import java.util.Date;

public class CdbTrackedKeywordEntity {
	
	public static final int KEYWORD_TYPE_MANAGED = 1;
	public static final int KEYWORD_TYPE_GEO = 2;
	
	public static final int DEFAULT_LOCATION_ID = 0;
	public static final int GROUPTAG_ID_FOR_DOMAIN = 0;
	
	public static final int TAG_STATUS_REMOVED = 0;
	public static final int TAG_STATUS_ACTIVE = 1;
	
	private int id;
	private int ownDomainId;
	private int keywordType;
	private long keywordId;
	private long keywordRankcheckId;
	private String cdbKeywordHash;
	private int locationId;
	private int groupTagId;
	private int tagStatus;
	private Date createDate;

	private String tagName;
	private String rawKeywordName;

	private Integer kwCnt;
	private Long minRCId;
	private Long maxRCId;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	
	public int getKeywordType() {
		return keywordType;
	}

	public void setKeywordType(int keywordType) {
		this.keywordType = keywordType;
	}

	public long getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(long keywordId) {
		this.keywordId = keywordId;
	}

	public long getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(long keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public String getCbdKeywordHash() {
		return cdbKeywordHash;
	}

	public void setCbdKeywordHash(String cbdKeywordHash) {
		this.cdbKeywordHash = cbdKeywordHash;
	}

	public int getLocationId() {
		return locationId;
	}

	public void setLocationId(int locationId) {
		this.locationId = locationId;
	}

	public int getGroupTagId() {
		return groupTagId;
	}

	public void setGroupTagId(int groupTagId) {
		this.groupTagId = groupTagId;
	}

	public int getTagStatus() {
		return tagStatus;
	}

	public void setTagStatus(int tagStatus) {
		this.tagStatus = tagStatus;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public Integer getKwCnt() {
		return kwCnt;
	}

	public void setKwCnt(Integer kwCnt) {
		this.kwCnt = kwCnt;
	}

	public Long getMinRCId() {
		return minRCId;
	}

	public void setMinRCId(Long minRCId) {
		this.minRCId = minRCId;
	}

	public Long getMaxRCId() {
		return maxRCId;
	}

	public void setMaxRCId(Long maxRCId) {
		this.maxRCId = maxRCId;
	}

	public String getRawKeywordName() {
		return rawKeywordName;
	}

	public void setRawKeywordName(String rawKeywordName) {
		this.rawKeywordName = rawKeywordName;
	}
}