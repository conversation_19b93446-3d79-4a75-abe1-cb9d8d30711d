package seoclarity.backend.entity.actonia;

import java.util.Date;

public class ClarityDBUploadLogEntity {
	
	public static final Integer UPLOAD_TYPE_GA = 1;
	public static final Integer UPLOAD_TYPE_SERP = 2;
	public static final Integer UPLOAD_TYPE_PAGESPEED = 3;
	public static final Integer UPLOAD_TYPE_GSC = 4;
	public static final Integer UPLOAD_TYPE_INTERNAL_LINK = 5;
	public static final Integer UPLOAD_TYPE_INTERNAL_LINK_SUMMARY = 6;
	public static final Integer UPLOAD_TYPE_GA_V4 = 8;
	public static final Integer UPLOAD_TYPE_SITE_MAP = 10;
	public static final Integer UPLOAD_TYPE_LINKS_GA = 11;
	public static final Integer UPLOAD_TYPE_MIX_TRAFFIC_GA3 = 12;
	public static final Integer UPLOAD_TYPE_MIX_TRAFFIC_GA4 = 13;
	public static final Integer UPLOAD_TYPE_INTERNAL_LINK_TEST = 15;
	public static final Integer UPLOAD_TYPE_INTERNAL_LINK_SUMMARY_V2 = 16;
	public static final Integer UPLOAD_TYPE_INTERNAL_LINK_SUMMARY_NEW_CLUSTER = 17;
	public static final Integer UPLOAD_TYPE_EMBEDDING_OPENAI_VECTORDB = 19;
	public static final Integer UPLOAD_TYPE_INTERNAL_LINK_PAGE_RANK_SUMMARY = 20;
	public static final Integer UPLOAD_TYPE_GA_V4_FRESH = 21;
	public static final Integer UPLOAD_TYPE_MIX_TRAFFIC_GA4_FRESH = 22;
	public static final Integer UPLOAD_TYPE_GA4_BIGQUERY = 23;
	public static final Integer UPLOAD_TYPE_MIX_TRAFFIC_GA4_BIGQUERY = 24;
	public static final Integer UPLOAD_TYPE_TIKTOK_RANKING = 25;
	
	public static final Integer UPLOAD_TYPE_GA_V4_HISTORICAL = 201;
	public static final Integer UPLOAD_TYPE_MIX_TRAFFIC_GA4_HISTORICAL = 202;

	public static final Integer TMP_TABLE_STATUS_NOT_APPLY = 0;
	public static final Integer TMP_TABLE_STATUS_PROCESSING = 1;
	public static final Integer TMP_TABLE_STATUS_SUCCESS = 2;
	public static final Integer TMP_TABLE_STATUS_FAILURE = 3;
	public static final Integer TMP_TABLE_STATUS_SUCCESS_MOVE_FILE_FAIL = 4;
	public static final Integer TMP_TABLE_STATUS_FINISHED_WITH_ERROR = 5;

	public static final Integer FINAL_TABLE_STATUS_NEW = 0;
	public static final Integer FINAL_TABLE_STATUS_PROCESSING = 1;
	public static final Integer FINAL_TABLE_STATUS_SUCCESS = 2;
	public static final Integer FINAL_TABLE_STATUS_FAILURE = 3;

	private Integer id;
	private Integer uploadType;
	private String serverIp;
	private String databaseName;
	private String tmpTableName;
	private String finalTableName;
	private Integer tmpTableUploadStatus;
	private Integer finalTableUploadStatus;
	private String inputFileName;
	private Integer tmpTableUploadDailyRows;
	private Integer tmpTableUploadReprocessRows;
	private Date tmpTableUploadStartTime;
	private Date tmpTableUploadEndTime;
	private Integer tmpTableUploadSeconds;
	private Integer finalTableUploadRows;
	private Date finalTableUploadStartTime;
	private Date finalTableUploadEndTime;
	private Integer finalTableUploadSeconds;
	private Integer prevFinalTableRows;
	private Integer currFinalTableRows;
	private Date creatDate;
	private String errorMsg;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getServerIp() {
		return serverIp;
	}

	public void setServerIp(String serverIp) {
		this.serverIp = serverIp;
	}

	public String getDatabaseName() {
		return databaseName;
	}

	public void setDatabaseName(String databaseName) {
		this.databaseName = databaseName;
	}

	public String getTmpTableName() {
		return tmpTableName;
	}

	public void setTmpTableName(String tmpTableName) {
		this.tmpTableName = tmpTableName;
	}

	public String getFinalTableName() {
		return finalTableName;
	}

	public void setFinalTableName(String finalTableName) {
		this.finalTableName = finalTableName;
	}

	public Integer getTmpTableUploadStatus() {
		return tmpTableUploadStatus;
	}

	public void setTmpTableUploadStatus(Integer tmpTableUploadStatus) {
		this.tmpTableUploadStatus = tmpTableUploadStatus;
	}

	public Integer getFinalTableUploadStatus() {
		return finalTableUploadStatus;
	}

	public void setFinalTableUploadStatus(Integer finalTableUploadStatus) {
		this.finalTableUploadStatus = finalTableUploadStatus;
	}

	public String getInputFileName() {
		return inputFileName;
	}

	public void setInputFileName(String inputFileName) {
		this.inputFileName = inputFileName;
	}

	public Integer getTmpTableUploadDailyRows() {
		return tmpTableUploadDailyRows;
	}

	public void setTmpTableUploadDailyRows(Integer tmpTableUploadDailyRows) {
		this.tmpTableUploadDailyRows = tmpTableUploadDailyRows;
	}

	public Integer getTmpTableUploadReprocessRows() {
		return tmpTableUploadReprocessRows;
	}

	public void setTmpTableUploadReprocessRows(
			Integer tmpTableUploadReprocessRows) {
		this.tmpTableUploadReprocessRows = tmpTableUploadReprocessRows;
	}

	public Date getTmpTableUploadStartTime() {
		return tmpTableUploadStartTime;
	}

	public void setTmpTableUploadStartTime(Date tmpTableUploadStartTime) {
		this.tmpTableUploadStartTime = tmpTableUploadStartTime;
	}

	public Date getTmpTableUploadEndTime() {
		return tmpTableUploadEndTime;
	}

	public void setTmpTableUploadEndTime(Date tmpTableUploadEndTime) {
		this.tmpTableUploadEndTime = tmpTableUploadEndTime;
	}

	public Integer getTmpTableUploadSeconds() {
		return tmpTableUploadSeconds;
	}

	public void setTmpTableUploadSeconds(Integer tmpTableUploadSeconds) {
		this.tmpTableUploadSeconds = tmpTableUploadSeconds;
	}

	public Integer getFinalTableUploadRows() {
		return finalTableUploadRows;
	}

	public void setFinalTableUploadRows(Integer finalTableUploadRows) {
		this.finalTableUploadRows = finalTableUploadRows;
	}

	public Date getFinalTableUploadStartTime() {
		return finalTableUploadStartTime;
	}

	public void setFinalTableUploadStartTime(Date finalTableUploadStartTime) {
		this.finalTableUploadStartTime = finalTableUploadStartTime;
	}

	public Date getFinalTableUploadEndTime() {
		return finalTableUploadEndTime;
	}

	public void setFinalTableUploadEndTime(Date finalTableUploadEndTime) {
		this.finalTableUploadEndTime = finalTableUploadEndTime;
	}

	public Integer getFinalTableUploadSeconds() {
		return finalTableUploadSeconds;
	}

	public void setFinalTableUploadSeconds(Integer finalTableUploadSeconds) {
		this.finalTableUploadSeconds = finalTableUploadSeconds;
	}

	public Integer getPrevFinalTableRows() {
		return prevFinalTableRows;
	}

	public void setPrevFinalTableRows(Integer prevFinalTableRows) {
		this.prevFinalTableRows = prevFinalTableRows;
	}

	public Integer getCurrFinalTableRows() {
		return currFinalTableRows;
	}

	public void setCurrFinalTableRows(Integer currFinalTableRows) {
		this.currFinalTableRows = currFinalTableRows;
	}

	public Date getCreatDate() {
		return creatDate;
	}

	public void setCreatDate(Date creatDate) {
		this.creatDate = creatDate;
	}

	public Integer getUploadType() {
		return uploadType;
	}

	public void setUploadType(Integer uploadType) {
		this.uploadType = uploadType;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	
}
