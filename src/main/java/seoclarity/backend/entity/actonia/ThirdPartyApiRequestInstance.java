package seoclarity.backend.entity.actonia;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/7 09:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThirdPartyApiRequestInstance {

    public static final Integer NEWLY_CREATED = 0;
    public static final Integer PROCESSING = 1;
    public static final Integer PROCESS_SUCCESS = 2;
    public static final Integer PROCESS_ERROR = 3;
    public static final Integer NO_DATA = 4;
    public static final Integer AUTHENTICATION_ERROR = 5;
    public static final Integer MAX_ERROR_MESSAGE_LENGTH = 512;

    private Integer id;

    /**
     * type
     */
    private Integer processType;

    /**
     * user unique
     */
    private Integer ownDomainId;

    /**
     * yesterday
     */
    private Integer targetDate;

    /**
     * today
     */
    private Integer processDate;

    /**
     * version number
     */
    private Integer rerunNo;

    /**
     * 0: newly created, 1: processing, 2: process success, 3: process error, 4: no data, 5: authentication error
     */
    private Integer status;

    /**
     * start processing date
     */
    private LocalDateTime processStartDate;

    /**
     * end processing date
     */
    private LocalDateTime processEndDate;

    /**
     * total count
     */
    private Integer resultCount;

    /**
     * add count
     */
    private Integer loadCount;

    /**
     * error message
     */
    private String errorMsg;

    private LocalDateTime createDate;

}
