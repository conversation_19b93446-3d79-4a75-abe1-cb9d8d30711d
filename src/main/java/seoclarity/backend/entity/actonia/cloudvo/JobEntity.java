package seoclarity.backend.entity.actonia.cloudvo;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter @Setter
public class JobEntity {

	private String jobTitle;
	private String jobCompany;
	private String jobLocation;
	private String jobSource;
	private String jobPostTime;
	private String jobType;
	private String jobId;
	private String jobPrimaryJobLink;
	private List<String> jobAdditionalJobLinks;
	private JSONObject applyOnList;

	//https://www.wrike.com/open.htm?id=1067872068
	private String jsTitle;
	private String jsCompany;
	private String jsId;
	private String companyId;
	private String companyCity;
	private String companyState;
	private String minSalary;
	private String maxSalary;
	private String salaryUnit;
	private String currencyCode;
	private String lrad;

	private String tmpFc;
	private String tmpFcv;
	private String tmpCallId;
	private String tmpFcId;

	private JSONArray ratingArray;
	private List<String> hourlyPay;
	private JSONArray salaryArray;

//	private Element baseElement;

}
