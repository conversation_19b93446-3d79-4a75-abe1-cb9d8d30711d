package seoclarity.backend.entity.actonia.cloudvo;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter @Setter
public class ThingsToKnow {

    public String label;
    public List<String> links;

    public void addLink(String link) {
        if(links == null) {
            links = new ArrayList<>(2);
        }
        if(StrUtil.isBlank(link) || StrUtil.startWith(link, "/") || links.contains(link)) {
            return;
        }
        links.add(link);
    }

}
