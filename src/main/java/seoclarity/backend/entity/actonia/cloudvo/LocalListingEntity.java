package seoclarity.backend.entity.actonia.cloudvo;

public class LocalListingEntity {
	private String name;
	private String address;
	private String phone;
	private String url;
	private Integer imgCnt;
	private String category;

	private String rating;
	private String reviewCnt;

	private String ludocids;
	private String website;
	private Boolean adsFlg;

	public String getLudocids() {
		return ludocids;
	}

	public void setLudocids(String ludocids) {
		this.ludocids = ludocids;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public String getReviewCnt() {
		return reviewCnt;
	}

	public void setReviewCnt(String reviewCnt) {
		this.reviewCnt = reviewCnt;
	}

	public Integer getImgCnt() {
		return imgCnt;
	}

	public void setImgCnt(Integer imgCnt) {
		this.imgCnt = imgCnt;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getWebsite() {
		return website;
	}

	public void setWebsite(String website) {
		this.website = website;
	}

	public Boolean getAdsFlg() {
		return adsFlg;
	}

	public void setAdsFlg(Boolean adsFlg) {
		this.adsFlg = adsFlg;
	}

	@Override
	public String toString() {
		return "LocalListingEntity [name=" + name + ", address=" + address + ", phone=" + phone + ", category="
				+ category + ", rating=" + rating + ", reviewCnt=" + reviewCnt + ", imgCnt=" + imgCnt + ", url=" + url + ", website=" + website+ ", adsFlg=" + adsFlg
				+ "]";
	}
}
