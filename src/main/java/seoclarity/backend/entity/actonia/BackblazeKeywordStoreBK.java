package seoclarity.backend.entity.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.BackblazeKeywordStoreBKDAO;

import javax.annotation.Resource;
import java.util.Date;

public class BackblazeKeywordStoreBK {

    private Date rankDate;
    private Integer keywordType;
    private int engineId;
    private Integer languageId;
    private String device;
    private Integer cityQueryName;
    private String keywordName;
    private String rawKeywordName;

    public Date getRankDate() {
        return rankDate;
    }

    public void setRankDate(Date rankDate) {
        this.rankDate = rankDate;
    }

    public Integer getKeywordType() {
        return keywordType;
    }

    public void setKeywordType(Integer keywordType) {
        this.keywordType = keywordType;
    }

    public int getEngineId() {
        return engineId;
    }

    public void setEngineId(int engineId) {
        this.engineId = engineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public Integer getCityQueryName() {
        return cityQueryName;
    }

    public void setCityQueryName(Integer cityQueryName) {
        this.cityQueryName = cityQueryName;
    }

    public String getKeywordName() {
        return keywordName;
    }

    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    public String getRawKeywordName() {
        return rawKeywordName;
    }

    public void setRawKeywordName(String rawKeywordName) {
        this.rawKeywordName = rawKeywordName;
    }
}
