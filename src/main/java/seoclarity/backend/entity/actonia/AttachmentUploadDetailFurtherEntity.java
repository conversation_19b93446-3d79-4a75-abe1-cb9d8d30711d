package seoclarity.backend.entity.actonia;

import java.util.Date;

public class AttachmentUploadDetailFurtherEntity {
    public static final int ST_NO_ATTACHMENT = AttachmentUploadDetailEntity.ST_NO_ATTACHMENT;
    public static final int ST_SERVER_ERROR = AttachmentUploadDetailEntity.ST_SERVER_ERROR;
    public static final int ST_FINISHED_SUCCESSFUL = AttachmentUploadDetailEntity.ST_FINISHED_SUCCESSFUL;

    private int id;
    private int attachmentId;
    private int uploadDetailId;
    private Date logDate;
    private Date downloadTime;
	private int status;
    private String fileName; 
    private Date actualSendDate;
    private String attachmentName;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getAttachmentId() {
		return attachmentId;
	}

	public void setAttachmentId(int attachmentId) {
		this.attachmentId = attachmentId;
	}

	public int getUploadDetailId() {
		return uploadDetailId;
	}

	public void setUploadDetailId(int uploadDetailId) {
		this.uploadDetailId = uploadDetailId;
	}

	public Date getLogDate() {
		return logDate;
	}

	public void setLogDate(Date logDate) {
		this.logDate = logDate;
	}

    public Date getDownloadTime() {
		return downloadTime;
	}

	public void setDownloadTime(Date downloadTime) {
		this.downloadTime = downloadTime;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public Date getActualSendDate() {
		return actualSendDate;
	}

	public void setActualSendDate(Date actualSendDate) {
		this.actualSendDate = actualSendDate;
	}
	
	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}
}