package seoclarity.backend.entity.actonia;

import java.util.Date;
import java.util.Objects;

public class CdbParentChildPageRelEntity {

    private Integer id;
    private Integer ownDomainId;
    private String childUrlMurmur3Hash;
    private String childCustomUrlMurmur3Hash;
    private String parentUrlMurmur3Hash;
    private String parentCustomUrlMurmur3Hash;
    private Date createDate;

    private long detailId;
    private long childUrlId;
    private long parentUrlId;
    private String childUrl;
    private String parentUrl;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public String getChildUrlMurmur3Hash() {
        return childUrlMurmur3Hash;
    }

    public void setChildUrlMurmur3Hash(String childUrlMurmur3Hash) {
        this.childUrlMurmur3Hash = childUrlMurmur3Hash;
    }

    public String getChildCustomUrlMurmur3Hash() {
        return childCustomUrlMurmur3Hash;
    }

    public void setChildCustomUrlMurmur3Hash(String childCustomUrlMurmur3Hash) {
        this.childCustomUrlMurmur3Hash = childCustomUrlMurmur3Hash;
    }

    public String getParentUrlMurmur3Hash() {
        return parentUrlMurmur3Hash;
    }

    public void setParentUrlMurmur3Hash(String parentUrlMurmur3Hash) {
        this.parentUrlMurmur3Hash = parentUrlMurmur3Hash;
    }

    public String getParentCustomUrlMurmur3Hash() {
        return parentCustomUrlMurmur3Hash;
    }

    public void setParentCustomUrlMurmur3Hash(String parentCustomUrlMurmur3Hash) {
        this.parentCustomUrlMurmur3Hash = parentCustomUrlMurmur3Hash;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public long getDetailId() {
        return detailId;
    }

    public void setDetailId(long detailId) {
        this.detailId = detailId;
    }

    public long getChildUrlId() {
        return childUrlId;
    }

    public void setChildUrlId(long childUrlId) {
        this.childUrlId = childUrlId;
    }

    public long getParentUrlId() {
        return parentUrlId;
    }

    public void setParentUrlId(long parentUrlId) {
        this.parentUrlId = parentUrlId;
    }

    public String getChildUrl() {
        return childUrl;
    }

    public void setChildUrl(String childUrl) {
        this.childUrl = childUrl;
    }

    public String getParentUrl() {
        return parentUrl;
    }

    public void setParentUrl(String parentUrl) {
        this.parentUrl = parentUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CdbParentChildPageRelEntity)) return false;
        CdbParentChildPageRelEntity that = (CdbParentChildPageRelEntity) o;
        return Objects.equals(ownDomainId, that.ownDomainId) &&
                Objects.equals(childCustomUrlMurmur3Hash, that.childCustomUrlMurmur3Hash) &&
                Objects.equals(parentCustomUrlMurmur3Hash, that.parentCustomUrlMurmur3Hash);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ownDomainId, childCustomUrlMurmur3Hash, parentCustomUrlMurmur3Hash);
    }


    @Override
    public String toString() {
        return "CdbParentChildPageRelEntity{" +
                "id=" + id +
                ", ownDomainId=" + ownDomainId +
                ", childUrlMurmur3Hash='" + childUrlMurmur3Hash + '\'' +
                ", childCustomUrlMurmur3Hash='" + childCustomUrlMurmur3Hash + '\'' +
                ", parentUrlMurmur3Hash='" + parentUrlMurmur3Hash + '\'' +
                ", parentCustomUrlMurmur3Hash='" + parentCustomUrlMurmur3Hash + '\'' +
                ", createDate=" + createDate +
                ", detailId=" + detailId +
                ", childUrlId=" + childUrlId +
                ", parentUrlId=" + parentUrlId +
                ", childUrl='" + childUrl + '\'' +
                ", parentUrl='" + parentUrl + '\'' +
                '}';
    }
}
