package seoclarity.backend.entity.actonia;

import java.util.Date;

public class ApiBillingInstanceEntity {

	private long id;
	private Integer billingInfoId;
	private Date requestTime;
	private Integer requestDate;
	private String sourceIp;
	private String accessToken;
	private Integer responseCode;
	private String errMsg;
	private Integer resultCount;
	private Integer responseLength;
	private String responseInfo;
	private Integer ownDomainId;
	private Integer createUserId;
	private String httpMethod;
	private String uniqueRequestId;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Integer getBillingInfoId() {
		return billingInfoId;
	}

	public void setBillingInfoId(Integer billingInfoId) {
		this.billingInfoId = billingInfoId;
	}

	public Date getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	public Integer getRequestDate() {
		return requestDate;
	}

	public void setRequestDate(Integer requestDate) {
		this.requestDate = requestDate;
	}

	public String getSourceIp() {
		return sourceIp;
	}

	public void setSourceIp(String sourceIp) {
		this.sourceIp = sourceIp;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public Integer getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(Integer responseCode) {
		this.responseCode = responseCode;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public Integer getResultCount() {
		return resultCount;
	}

	public void setResultCount(Integer resultCount) {
		this.resultCount = resultCount;
	}

	public Integer getResponseLength() {
		return responseLength;
	}

	public void setResponseLength(Integer responseLength) {
		this.responseLength = responseLength;
	}

	public String getResponseInfo() {
		return responseInfo;
	}

	public void setResponseInfo(String responseInfo) {
		this.responseInfo = responseInfo;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}

	public String getHttpMethod() {
		return httpMethod;
	}

	public void setHttpMethod(String httpMethod) {
		this.httpMethod = httpMethod;
	}

	public String getUniqueRequestId() {
		return uniqueRequestId;
	}

	public void setUniqueRequestId(String uniqueRequestId) {
		this.uniqueRequestId = uniqueRequestId;
	}


}
