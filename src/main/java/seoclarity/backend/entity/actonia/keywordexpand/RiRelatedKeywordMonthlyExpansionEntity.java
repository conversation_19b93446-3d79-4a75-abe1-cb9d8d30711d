package seoclarity.backend.entity.actonia.keywordexpand;

import java.util.Date;

public class RiRelatedKeywordMonthlyExpansionEntity {

    public static final int STATUS_START = 0;
    public static final int STATUS_CLEANUP = 11;
    public static final int STATUS_LOADING_STAGE = 21;
    public static final int STATUS_DEDUPING_STAGE = 22;
    public static final int STATUS_EXCLUDING_RESERVED = 23;
    public static final int STATUS_LOADING_TO64 = 31;
    public static final int STATUS_COLLECT_COMPLETED = 32;
    public static final int STATUS_SENDING_SQS = 41;
    public static final int STATUS_SENT_SQS_COMPLETED = 41;

    private Integer id;

    private Integer rankMonth;

    private Integer rankDate;

    private String countryType;

    private String device;

    // 11: cleaning up keyword, 21: loading into stage, 22: deduping stage, 23: excluding reserved, 31: loading candidate to db64, 32: collect completed, 41: sending to SQS, 42 sent to SQS completed
    private int status;

    private Integer riKeywordCount;

    private Integer nonRGKeywordCount;

    private Integer keywordCountToRank;

    private Long startVirtualRankcheckId;

    private Long endVirtualRankcheckId;

    private Integer sentToQCount;

    private Date createDate;

    public RiRelatedKeywordMonthlyExpansionEntity() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRankMonth() {
        return rankMonth;
    }

    public void setRankMonth(Integer rankMonth) {
        this.rankMonth = rankMonth;
    }

    public Integer getRankDate() {
        return rankDate;
    }

    public void setRankDate(Integer rankDate) {
        this.rankDate = rankDate;
    }

    public String getCountryType() {
        return countryType;
    }

    public void setCountryType(String countryType) {
        this.countryType = countryType;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public Integer getRiKeywordCount() {
        return riKeywordCount;
    }

    public void setRiKeywordCount(Integer riKeywordCount) {
        this.riKeywordCount = riKeywordCount;
    }

    public Integer getNonRGKeywordCount() {
        return nonRGKeywordCount;
    }

    public void setNonRGKeywordCount(Integer nonRGKeywordCount) {
        this.nonRGKeywordCount = nonRGKeywordCount;
    }

    public Integer getKeywordCountToRank() {
        return keywordCountToRank;
    }

    public void setKeywordCountToRank(Integer keywordCountToRank) {
        this.keywordCountToRank = keywordCountToRank;
    }

    public Long getStartVirtualRankcheckId() {
        return startVirtualRankcheckId;
    }

    public void setStartVirtualRankcheckId(Long startVirtualRankcheckId) {
        this.startVirtualRankcheckId = startVirtualRankcheckId;
    }

    public Long getEndVirtualRankcheckId() {
        return endVirtualRankcheckId;
    }

    public void setEndVirtualRankcheckId(Long endVirtualRankcheckId) {
        this.endVirtualRankcheckId = endVirtualRankcheckId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getSentToQCount() {
        return sentToQCount;
    }

    public void setSentToQCount(Integer sentToQCount) {
        this.sentToQCount = sentToQCount;
    }
}
