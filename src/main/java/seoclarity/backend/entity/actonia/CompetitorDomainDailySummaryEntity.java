package seoclarity.backend.entity.actonia;

import java.util.Date;

public class CompetitorDomainDailySummaryEntity {
    private int id;
    private int domainId;
    private Integer pageNum;
    private Date trackDate;
    private Integer ownDomainId;
    
    private String domain; // competitor domain
    
    private Integer total_keyword;
    
    private Integer search_volume_rank1;
    private Integer search_volume_rank2;
    private Integer search_volume_rank3;
    private Integer search_volume_rank4;
    private Integer search_volume_rank5;
    private Integer search_volume_rank6;
    private Integer search_volume_rank7;
    private Integer search_volume_rank8;
    private Integer search_volume_rank9;
    private Integer search_volume_rank10;

    private Integer keyword_top1_count;
    private Integer keyword_top3_count;
    private Integer keyword_top5_count;
    private Integer keyword_top30_count;
    private Integer keyword_top10_count;
    private Integer keyword_top50_count;
    private Integer keyword_top100_count;

    private Integer count_keyword_rank1;
    private Integer count_keyword_rank2;
    private Integer count_keyword_rank3;
    private Integer count_keyword_rank4;
    private Integer count_keyword_rank5;
    private Integer count_keyword_rank6;
    private Integer count_keyword_rank7;
    private Integer count_keyword_rank8;
    private Integer count_keyword_rank9;
    private Integer count_keyword_rank10;
    
    private Integer rank_in_page1;
    private Integer rank_in_page2;
    private Integer rank_in_page3;
    private Integer rank_in_page4;
    private Integer rank_in_page5;
    
    private Integer keyword_top10_entrances;
    
    private Float wtd_avg_rank;
    private Float avg_rank;

    public Integer getRank_in_page1() {
        return rank_in_page1;
    }

    public void setRank_in_page1(Integer rankInPage1) {
        rank_in_page1 = rankInPage1;
    }

    public Integer getRank_in_page2() {
        return rank_in_page2;
    }

    public void setRank_in_page2(Integer rankInPage2) {
        rank_in_page2 = rankInPage2;
    }

    public Integer getRank_in_page3() {
        return rank_in_page3;
    }

    public void setRank_in_page3(Integer rankInPage3) {
        rank_in_page3 = rankInPage3;
    }

    public Integer getRank_in_page4() {
        return rank_in_page4;
    }

    public void setRank_in_page4(Integer rankInPage4) {
        rank_in_page4 = rankInPage4;
    }

    public Integer getRank_in_page5() {
        return rank_in_page5;
    }

    public void setRank_in_page5(Integer rankInPage5) {
        rank_in_page5 = rankInPage5;
    }

    public Integer getKeyword_top5_count() {
        return keyword_top5_count;
    }

    public void setKeyword_top5_count(Integer keywordTop5Count) {
        keyword_top5_count = keywordTop5Count;
    }

    public Integer getSearch_volume_rank1() {
        return search_volume_rank1;
    }

    public void setSearch_volume_rank1(Integer searchVolumeRank1) {
        search_volume_rank1 = searchVolumeRank1;
    }

    public Integer getSearch_volume_rank2() {
        return search_volume_rank2;
    }

    public void setSearch_volume_rank2(Integer searchVolumeRank2) {
        search_volume_rank2 = searchVolumeRank2;
    }

    public Integer getSearch_volume_rank3() {
        return search_volume_rank3;
    }

    public void setSearch_volume_rank3(Integer searchVolumeRank3) {
        search_volume_rank3 = searchVolumeRank3;
    }

    public Integer getSearch_volume_rank4() {
        return search_volume_rank4;
    }

    public void setSearch_volume_rank4(Integer searchVolumeRank4) {
        search_volume_rank4 = searchVolumeRank4;
    }

    public Integer getSearch_volume_rank5() {
        return search_volume_rank5;
    }

    public void setSearch_volume_rank5(Integer searchVolumeRank5) {
        search_volume_rank5 = searchVolumeRank5;
    }

    public Integer getSearch_volume_rank6() {
        return search_volume_rank6;
    }

    public void setSearch_volume_rank6(Integer searchVolumeRank6) {
        search_volume_rank6 = searchVolumeRank6;
    }

    public Integer getSearch_volume_rank7() {
        return search_volume_rank7;
    }

    public void setSearch_volume_rank7(Integer searchVolumeRank7) {
        search_volume_rank7 = searchVolumeRank7;
    }

    public Integer getSearch_volume_rank8() {
        return search_volume_rank8;
    }

    public void setSearch_volume_rank8(Integer searchVolumeRank8) {
        search_volume_rank8 = searchVolumeRank8;
    }

    public Integer getSearch_volume_rank9() {
        return search_volume_rank9;
    }

    public void setSearch_volume_rank9(Integer searchVolumeRank9) {
        search_volume_rank9 = searchVolumeRank9;
    }

    public Integer getSearch_volume_rank10() {
        return search_volume_rank10;
    }

    public void setSearch_volume_rank10(Integer searchVolumeRank10) {
        search_volume_rank10 = searchVolumeRank10;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getDomainId() {
        return domainId;
    }

    public void setDomainId(int domainId) {
        this.domainId = domainId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Date getTrackDate() {
        return trackDate;
    }

    public void setTrackDate(Date trackDate) {
        this.trackDate = trackDate;
    }

    public Integer getKeyword_top1_count() {
        return keyword_top1_count;
    }

    public void setKeyword_top1_count(Integer keywordTop1Count) {
        keyword_top1_count = keywordTop1Count;
    }

    public Integer getKeyword_top3_count() {
        return keyword_top3_count;
    }

    public void setKeyword_top3_count(Integer keywordTop3Count) {
        keyword_top3_count = keywordTop3Count;
    }

    public Integer getKeyword_top30_count() {
        return keyword_top30_count;
    }

    public void setKeyword_top30_count(Integer keywordTop30Count) {
        keyword_top30_count = keywordTop30Count;
    }

    public Integer getKeyword_top10_count() {
        return keyword_top10_count;
    }

    public void setKeyword_top10_count(Integer keywordTop10Count) {
        keyword_top10_count = keywordTop10Count;
    }

    public Integer getKeyword_top50_count() {
        return keyword_top50_count;
    }

    public void setKeyword_top50_count(Integer keywordTop50Count) {
        keyword_top50_count = keywordTop50Count;
    }

    public Integer getKeyword_top100_count() {
        return keyword_top100_count;
    }

    public void setKeyword_top100_count(Integer keywordTop100Count) {
        keyword_top100_count = keywordTop100Count;
    }

    public Integer getCount_keyword_rank1() {
        return count_keyword_rank1;
    }

    public void setCount_keyword_rank1(Integer countKeywordRank1) {
        count_keyword_rank1 = countKeywordRank1;
    }

    public Integer getCount_keyword_rank2() {
        return count_keyword_rank2;
    }

    public void setCount_keyword_rank2(Integer countKeywordRank2) {
        count_keyword_rank2 = countKeywordRank2;
    }

    public Integer getCount_keyword_rank3() {
        return count_keyword_rank3;
    }

    public void setCount_keyword_rank3(Integer countKeywordRank3) {
        count_keyword_rank3 = countKeywordRank3;
    }

    public Integer getCount_keyword_rank4() {
        return count_keyword_rank4;
    }

    public void setCount_keyword_rank4(Integer countKeywordRank4) {
        count_keyword_rank4 = countKeywordRank4;
    }

    public Integer getCount_keyword_rank5() {
        return count_keyword_rank5;
    }

    public void setCount_keyword_rank5(Integer countKeywordRank5) {
        count_keyword_rank5 = countKeywordRank5;
    }

    public Integer getCount_keyword_rank6() {
        return count_keyword_rank6;
    }

    public void setCount_keyword_rank6(Integer countKeywordRank6) {
        count_keyword_rank6 = countKeywordRank6;
    }

    public Integer getCount_keyword_rank7() {
        return count_keyword_rank7;
    }

    public void setCount_keyword_rank7(Integer countKeywordRank7) {
        count_keyword_rank7 = countKeywordRank7;
    }

    public Integer getCount_keyword_rank8() {
        return count_keyword_rank8;
    }

    public void setCount_keyword_rank8(Integer countKeywordRank8) {
        count_keyword_rank8 = countKeywordRank8;
    }

    public Integer getCount_keyword_rank9() {
        return count_keyword_rank9;
    }

    public void setCount_keyword_rank9(Integer countKeywordRank9) {
        count_keyword_rank9 = countKeywordRank9;
    }

    public Integer getCount_keyword_rank10() {
        return count_keyword_rank10;
    }

    public void setCount_keyword_rank10(Integer countKeywordRank10) {
        count_keyword_rank10 = countKeywordRank10;
    }

	public Integer getTotal_keyword() {
		return total_keyword;
	}

	public void setTotal_keyword(Integer total_keyword) {
		this.total_keyword = total_keyword;
	}

	public Float getWtd_avg_rank() {
		return wtd_avg_rank;
	}

	public void setWtd_avg_rank(Float wtd_avg_rank) {
		this.wtd_avg_rank = wtd_avg_rank;
	}

	public Float getAvg_rank() {
		return avg_rank;
	}

	public void setAvg_rank(Float avg_rank) {
		this.avg_rank = avg_rank;
	}
	
	// https://www.wrike.com/open.htm?id=42916837
	// by floyd
	public long getRankPositionTotalSearchVolume() {
		long total = 0l;
		if (search_volume_rank1 != null) {
			total += (long)search_volume_rank1;
		}
		if (search_volume_rank2 != null) {
			total += (long)search_volume_rank2;
		}
		if (search_volume_rank3 != null) {
			total += (long)search_volume_rank3;
		}
		if (search_volume_rank4 != null) {
			total += (long)search_volume_rank4;
		}
		if (search_volume_rank5 != null) {
			total += (long)search_volume_rank5;
		}
		if (search_volume_rank6 != null) {
			total += (long)search_volume_rank6;
		}
		if (search_volume_rank7 != null) {
			total += (long)search_volume_rank7;
		}
		if (search_volume_rank8 != null) {
			total += (long)search_volume_rank8;
		}
		if (search_volume_rank9 != null) {
			total += (long)search_volume_rank9;
		}
		if (search_volume_rank10 != null) {
			total += (long)search_volume_rank10;
		}
		return total;
	}
	
	public Integer getKeyword_top10_entrances() {
		return keyword_top10_entrances;
	}

	public void setKeyword_top10_entrances(Integer keyword_top10_entrances) {
		this.keyword_top10_entrances = keyword_top10_entrances;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

}
