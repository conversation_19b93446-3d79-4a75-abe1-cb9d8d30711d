package seoclarity.backend.entity.actonia.hreflang;

import java.util.Date;

public class HrefLangUrlEntity {
	
	private int id;
	private int ownDomainId;
	private int clusterId;
	private int hreflangCodeId;
	private String url;
	private String browserMurmur3hash;
	private String urlMurmur3Hash;
	private int createUserId;
	private Integer updateUserId;
	private Date updateDate;
	private Date createDate;
	
	private Integer cnt;
    private Integer minId;
    private Integer maxId;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public int getClusterId() {
		return clusterId;
	}

	public void setClusterId(int clusterId) {
		this.clusterId = clusterId;
	}

	public int getHreflangCodeId() {
		return hreflangCodeId;
	}

	public void setHreflangCodeId(int hreflangCodeId) {
		this.hreflangCodeId = hreflangCodeId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getBrowserMurmur3hash() {
		return browserMurmur3hash;
	}

	public void setBrowserMurmur3hash(String browserMurmur3hash) {
		this.browserMurmur3hash = browserMurmur3hash;
	}

	public String getUrlMurmur3Hash() {
		return urlMurmur3Hash;
	}

	public void setUrlMurmur3Hash(String urlMurmur3Hash) {
		this.urlMurmur3Hash = urlMurmur3Hash;
	}

	public int getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(int createUserId) {
		this.createUserId = createUserId;
	}

	public Integer getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	public Integer getCnt() {
		return cnt;
	}

	public void setCnt(Integer cnt) {
		this.cnt = cnt;
	}

	public Integer getMinId() {
		return minId;
	}

	public void setMinId(Integer minId) {
		this.minId = minId;
	}

	public Integer getMaxId() {
		return maxId;
	}

	public void setMaxId(Integer maxId) {
		this.maxId = maxId;
	}
}