package seoclarity.backend.entity.actonia.hreflang;

import java.util.Date;

public class HrefLangUrlCrawlEntity {
	
	public static final int CEAWL_STATUS_SENT_TO_OPENSEARCH = 1;
	public static final int CEAWL_STATUS_CRAWL_COMPLETED = 2;
	public static final int CEAWL_STATUS_CRAWL_ERROR = 3;
	public static final int CEAWL_STATUS_INVALID_URL = 4;

	private int id;
	private int ownDomainId;
	private Date crawlDate;
	private int urlId;
	private int crawlStatus;
	private Integer responseCode;
	private Integer indexable;
	private Date createDate;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Date getCrawlDate() {
		return crawlDate;
	}

	public void setCrawlDate(Date crawlDate) {
		this.crawlDate = crawlDate;
	}

	public int getUrlId() {
		return urlId;
	}

	public void setUrlId(int urlId) {
		this.urlId = urlId;
	}

	public int getCrawlStatus() {
		return crawlStatus;
	}

	public void setCrawlStatus(int crawlStatus) {
		this.crawlStatus = crawlStatus;
	}

	public Integer getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(Integer responseCode) {
		this.responseCode = responseCode;
	}

	public Integer getIndexable() {
		return indexable;
	}

	public void setIndexable(Integer indexable) {
		this.indexable = indexable;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
}