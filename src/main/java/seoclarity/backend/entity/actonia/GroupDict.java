/**
 * 
 */
package seoclarity.backend.entity.actonia;

import java.beans.Transient;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

/**
 * com.actonia.saas.model.GroupDict.java
 * 
 * https://www.wrike.com/open.htm?id=127337716
 *
 * <AUTHOR>
 *
 * @version $Revision:$ $Author:$
 */
public class GroupDict {
    
    public static final String GROUP_UNASSIGN = "Unassigned";
    public static final String GROUP_UNASSIGN_SHORT = "-";
    public static final int GROUP_UNASSIGN_ID = 0;
    
	public static final int TAG_TYPE_TARGET_URL = 1;
	public static final int TAG_TYPE_KEYWORD = 2;
	public static final int TAG_TYPE_TARGET_PAGE = 9;
	
	private Integer id;
	private String name;
	private Integer ownDomainId;
	private Date createDate;
	private Date updateDate;

	private List<DyfGroupDictItemVO> items;
	
	private Boolean hasMoreAttr;
	
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	@Transient
	public List<DyfGroupDictItemVO> getItems() {
		return items;
	}

	public void setItems(List<DyfGroupDictItemVO> items) {
		this.items = items;
	}

	@Transient
	public void updateItem(DyfGroupDictItemVO itemVO) {
		if (items == null) {
			items = new ArrayList<DyfGroupDictItemVO>();
		}
		
		//Cee - https://www.wrike.com/open.htm?id=142941936
		//not check Group-Id, ClarityDB will not return it 
		for (DyfGroupDictItemVO attribute : items) {
            if (attribute.getTagId() == itemVO.getTagId()) {
                attribute.setCount(itemVO.getCount());
            }
        }
	}
	
	@Transient
	public void addItems(Collection<DyfGroupDictItemVO> tagList) {
		if (items == null) {
			items = new ArrayList<DyfGroupDictItemVO>();
		}
		
		if (tagList != null) {
			items.addAll(tagList);
		}
	}
	
    @Transient
    public Boolean getHasMoreAttr() {
        return hasMoreAttr;
    }

    public void setHasMoreAttr(Boolean hasMoreAttr) {
        this.hasMoreAttr = hasMoreAttr;
    }

	public static String verifyGroupName(String groupName) throws Exception {
	    groupName = StringUtils.trim(groupName);
	    if (StringUtils.isBlank(groupName)) {
	        throw new Exception("Invalid Name : " + groupName);
	    }
	    
	    if (StringUtils.equalsIgnoreCase(groupName, GROUP_UNASSIGN)) {
	        throw new Exception("Invalid Name : " + groupName);
	    }
	    
	    if (StringUtils.equalsIgnoreCase(groupName, GROUP_UNASSIGN_SHORT)) {
            throw new Exception("Invalid Name : " + groupName);
        }
	    
	    return groupName;
	}

}
