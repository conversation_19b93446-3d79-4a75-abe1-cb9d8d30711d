package seoclarity.backend.entity.actonia;

import java.util.Date;

public class VirtualSearchEngineRelEntity {
	
	public static final int ENABLED = 1;

	private int id;
	private int enabled;
	private String searchEngine;
	private int virtualSearchEngineId;
	private int rankcheckSearchEngineId;
	private Date createDate;
	
	public int getId() {
		return id;
	}
	
	public void setId(int id) {
		this.id = id;
	}
	
	public int getEnabled() {
		return enabled;
	}
	
	public void setEnabled(int enabled) {
		this.enabled = enabled;
	}
	
	public String getSearchEngine() {
		return searchEngine;
	}
	
	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}
	
	public int getVirtualSearchEngineId() {
		return virtualSearchEngineId;
	}
	
	public void setVirtualSearchEngineId(int virtualSearchEngineId) {
		this.virtualSearchEngineId = virtualSearchEngineId;
	}
	
	public int getRankcheckSearchEngineId() {
		return rankcheckSearchEngineId;
	}
	
	public void setRankcheckSearchEngineId(int rankcheckSearchEngineId) {
		this.rankcheckSearchEngineId = rankcheckSearchEngineId;
	}
	
	public Date getCreateDate() {
		return createDate;
	}
	
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
}