package seoclarity.backend.entity.actonia;

import java.util.Date;

public class VedMasterEngineFileInfoEntity {
    //id int NOT NULL AUTO_INCREMENT,
    //  vedTypeList varchar(1024) NOT NULL,
    //  vedTypeMurmurHash bigint unsigned NOT NULL COMMENT 'murmurHash3_64(vedTypeList)',
    //  feature varchar(128) NOT NULL,
    //  domain varchar(128) NOT NULL,
    //  url varchar(512) NOT NULL,
    //  device tinyint(1) NOT NULL COMMENT 'dm: desktop + mobile, d: desktop, m: mobile',
    //  rankType varchar(2000) NOT NULL,
    //  parseLogic tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: NONE(Skip This), 1: ONLY true rank, 2: ONLY web rank, 3: true rank + web rank',
    //  errorHandle varchar(10) NOT NULL COMMENT 'WARN, ERROR',
    //  description varchar(256) DEFAULT NULL,
    //  createDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    //  updateDate datetime DEFAULT NULL,
    //  PRIMARY KEY (id)
    //,

    private int id;
    private String vedTypeList;
    private String vedTypeMurmurHash;
    private String feature;
    private String subFeature;
    private String domain;
    private String url;
    private String device;
    private String rankType;
    private String parseLogic;
    private String errorHandle;
    private String description;
    private Date createDate;
    private Date updateDate;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getVedTypeList() {
        return vedTypeList;
    }

    public void setVedTypeList(String vedTypeList) {
        this.vedTypeList = vedTypeList;
    }

    public String getVedTypeMurmurHash() {
        return vedTypeMurmurHash;
    }

    public void setVedTypeMurmurHash(String vedTypeMurmurHash) {
        this.vedTypeMurmurHash = vedTypeMurmurHash;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getRankType() {
        return rankType;
    }

    public void setRankType(String rankType) {
        this.rankType = rankType;
    }

    public String getParseLogic() {
        return parseLogic;
    }

    public void setParseLogic(String parseLogic) {
        this.parseLogic = parseLogic;
    }

    public String getErrorHandle() {
        return errorHandle;
    }

    public void setErrorHandle(String errorHandle) {
        this.errorHandle = errorHandle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getSubFeature() {
        return subFeature;
    }

    public void setSubFeature(String subFeature) {
        this.subFeature = subFeature;
    }
}
