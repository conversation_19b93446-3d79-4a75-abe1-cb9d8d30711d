package seoclarity.backend.entity.actonia;

import lombok.Data;
import scala.util.parsing.combinator.testing.Str;
@Data
public class InstantDownloadInstanceEntity {
  private Integer id;
  private Integer ownDomainId;
  private String funcName;
  private String paramHash;
  private String parameter;
  private String processStatus;
  private String filePath;
  private Integer resultCount;
  private Integer createdDate;
  private Integer createdAt;

}
