package seoclarity.backend.entity.actonia;

public class TargetUrlCrawlAdditionalEntity {

	private Integer id;
	private Integer domainId;
	private Integer selectorType;
	private String selector;
	private Integer urlSelectorType;
	private String urlSelector;
	private String friendlyName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public Integer getSelectorType() {
		return selectorType;
	}

	public void setSelectorType(Integer selectorType) {
		this.selectorType = selectorType;
	}

	public String getSelector() {
		return selector;
	}

	public void setSelector(String selector) {
		this.selector = selector;
	}

	public Integer getUrlSelectorType() {
		return urlSelectorType;
	}

	public void setUrlSelectorType(Integer urlSelectorType) {
		this.urlSelectorType = urlSelectorType;
	}

	public String getUrlSelector() {
		return urlSelector;
	}

	public void setUrlSelector(String urlSelector) {
		this.urlSelector = urlSelector;
	}

	public String getFriendlyName() {
		return friendlyName;
	}

	public void setFriendlyName(String friendlyName) {
		this.friendlyName = friendlyName;
	}

}
