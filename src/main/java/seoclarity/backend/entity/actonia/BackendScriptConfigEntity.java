package seoclarity.backend.entity.actonia;

public class BackendScriptConfigEntity {
	
	public static final int BACKEND_SCRIPT_IEE_SUMMARY = 1;
	
	public static final int BACKEND_SCRIPT_OWN_DOMAIN_RANK_DATA_SUMMARY = 2;
	
	public static final int BACKEND_SCRIPT_HIERARCHY_SUMMARY = 3;

	public static final int BACKEND_SCRIPT_KEYWORD_MONTHLY_SUMMARY = 5;
	
	public static final int BACKEND_SCRIPT_PAGE_WTD_AVG_RANK_SUMMARY = 6;

	public static final int BACKEND_SCRIPT_GA_DAILY = 7;

	public static final int BACKEND_SCRIPT_BOT_DAILY = 8;
	
	private int id;
	
	private int enable;
	
	private String name;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getEnable() {
		return enable;
	}

	public void setEnable(int enable) {
		this.enable = enable;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	
}
