package seoclarity.backend.entity.actonia.adhoc;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AdHocJobCrawlerEntity {

    private String keyword;

    private String queryDate;

    private Integer searchVolume;

    private Integer trueDemand;

//    private String emptyHtml;

//    private String cityId;

    private String cityName;

    private List<JobEntityVO> jobEntityVOs;






    @Data
    public class JobEntityVO {

        private String jobTitle;
        private String jobCompany;
        private String jobLocation;
        private String jobSource;
        private String jobPostTime;
        private String jobType;
        private String jobPrimaryJobLink;
        private Map<String,String> applyOnList;
        private String jsCompany;
        private String companyId;
        private String companyCity;
        private String companyState;
        private String minSalary;
        private String maxSalary;
        private String salaryUnit;
        private String currencyCode;
        private List<RatingEntity> ratingArray;
        private List<SalaryEntity> salaryArray;


        @Data
        public class RatingEntity {
            private String link;
//            private String rating;
//            private String reviews;
            private String source;
            private String rank;
            private String starRating;
            private String reviewCount;

        }

        @Data
       public class SalaryEntity {
            private String url;
            private String title;
            private String meta;
            private String salary;
            private String salaryUnit;
            private String source;
            private String rank;
        }
    }
}