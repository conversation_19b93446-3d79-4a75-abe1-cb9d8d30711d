package seoclarity.backend.entity.actonia.adhoc;

import java.util.Date;

public class AutoAdhocRankKeywordEntity {

    public static final int SVRETRIEVE_STATUS_NOT_STARTED = 0;
    public static final int SVRETRIEVE_STATUS_PROCESSING = 1;
    public static final int SVRETRIEVE_STATUS_RETRIEVED_SUCCESSFULLY = 2;
    public static final int SVRETRIEVE_STATUS_RETRIEVED_ERROR = 3;
    public static final int SVRETRIEVE_STATUS_EXIST_IN_RANKCHECK = 4;

    private Long id;
    private Integer projectId;
    private Integer projectDetailId;
    private Integer ownDomainId;
    private Integer searchEngineId;
    private Integer languageId;
    private Long keywordId;
    private Integer createDate;
    private Date createdAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public Integer getProjectDetailId() {
        return projectDetailId;
    }

    public void setProjectDetailId(Integer projectDetailId) {
        this.projectDetailId = projectDetailId;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getSearchEngineId() {
        return searchEngineId;
    }

    public void setSearchEngineId(Integer searchEngineId) {
        this.searchEngineId = searchEngineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public Long getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(Long keywordId) {
        this.keywordId = keywordId;
    }

    public Integer getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Integer createDate) {
        this.createDate = createDate;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
