package seoclarity.backend.entity.actonia.adhoc;

import java.util.Date;

public class AdhocRankSvProjectRelEntity {

    private Integer id;
    private Integer ownDomainId;
    private Integer parentProjectId;
    private Integer childProjectId;
    private Long batchNo;
    private Date createDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getParentProjectId() {
        return parentProjectId;
    }

    public void setParentProjectId(Integer parentProjectId) {
        this.parentProjectId = parentProjectId;
    }

    public Integer getChildProjectId() {
        return childProjectId;
    }

    public void setChildProjectId(Integer childProjectId) {
        this.childProjectId = childProjectId;
    }

    public Long getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(Long batchNo) {
        this.batchNo = batchNo;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
