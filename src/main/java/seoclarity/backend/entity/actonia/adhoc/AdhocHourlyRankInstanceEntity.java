package seoclarity.backend.entity.actonia.adhoc;

import java.util.Date;

public class AdhocHourlyRankInstanceEntity {

    public static final int STATUS_NOT_STARTED = 0;
    public static final int STATUS_STARTED = 1;
    public static final int STATUS_COMPLETED = 2;
    public static final int STATUS_FAILED = 3;

    public static final int EXTRACT_RANK_STSTUS_NOT_STARTED = 0;
    public static final int EXTRACT_RANK_STSTUS_PROCESSING = 1;
    public static final int EXTRACT_RANK_STSTUS_COMPLETED_WITHOUT_ERROR = 2;
    public static final int EXTRACT_RANK_STSTUS_ERROR = 3;

    private Integer id;
    private Integer projectId;
    private Integer startDay;
    private Integer startHour;
    private Date ec2StartTime;
    private Integer status;
    private String sqsInfo;
    private Date createdAt;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public Integer getStartDay() {
        return startDay;
    }

    public void setStartDay(Integer startDay) {
        this.startDay = startDay;
    }

    public Integer getStartHour() {
        return startHour;
    }

    public void setStartHour(Integer startHour) {
        this.startHour = startHour;
    }

    public Date getEc2StartTime() {
        return ec2StartTime;
    }

    public void setEc2StartTime(Date ec2StartTime) {
        this.ec2StartTime = ec2StartTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSqsInfo() {
        return sqsInfo;
    }

    public void setSqsInfo(String sqsInfo) {
        this.sqsInfo = sqsInfo;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
