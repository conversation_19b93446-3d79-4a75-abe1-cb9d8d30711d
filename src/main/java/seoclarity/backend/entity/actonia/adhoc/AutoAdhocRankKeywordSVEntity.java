package seoclarity.backend.entity.actonia.adhoc;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AutoAdhocRankKeywordSVEntity {

    public static final int IS_FROM_RANK_CHECK = 1;
    public static final int NOT_FROM_RANK_CHECK = 0;

    public static final int SV_RETRIEVE_STATUS_NOT_STARTED = 0;
    public static final int SV_RETRIEVE_STATUS_PROCESSING = 1;
    public static final int SV_RETRIEVE_STATUS_SUCCESSFULLY = 2;
    public static final int SV_RETRIEVE_STATUS_ERROR = 3;

    public static final int IS_REJECTED_YES = 1;
    public static final int IS_REJECTED_NO = 0;

    private Long id;
    private Integer projectId;
    private Integer svEngineId; //99
    private Integer searchEngineId;
    private Integer languageId;
    private String keywordName;
    private String clarityDBKeywordHash;
    private Integer cityId;
    private Integer isRejected;
    private Integer isSVFromRankcheck;
    private Integer rankcheckKeywordId;
    private Integer rankcheckAdwordsId;
    private Integer svUpdateDate;
    private Integer svRetrieveStatus;
    private Date svRetrieveTime;

    private Integer avgSV;
    private Integer monthlySV1;
    private Integer monthlySV2;
    private Integer monthlySV3;
    private Integer monthlySV4;
    private Integer monthlySV5;
    private Integer monthlySV6;
    private Integer monthlySV7;
    private Integer monthlySV8;
    private Integer monthlySV9;
    private Integer monthlySV10;
    private Integer monthlySV11;
    private Integer monthlySV12;

    private Double competition;
    private Double cpc;
    private String category;
    private Date createdAt;

    private Integer criteriaId;
    private String canonicalName;

    private String svRetrieveTaskId;
    private String svRetrieveBatchNo;
    private Integer svRetrieveCount;

    private String cityName;
    private String lat;
    private String lng;

    private Integer startMonth;
    private Integer endMonth;
    private String monthlySVJson;

    private String monthListStr;
    private String svListStr;
    private List<Integer> monthList;
    private List<Integer> svList;

    private Integer retrieveType;

    public String getSvRetrieveTaskId() {
        return svRetrieveTaskId;
    }

    public void setSvRetrieveTaskId(String svRetrieveTaskId) {
        this.svRetrieveTaskId = svRetrieveTaskId;
    }

    public String getSvRetrieveBatchNo() {
        return svRetrieveBatchNo;
    }

    public void setSvRetrieveBatchNo(String svRetrieveBatchNo) {
        this.svRetrieveBatchNo = svRetrieveBatchNo;
    }

    public Integer getSvRetrieveCount() {
        return svRetrieveCount;
    }

    public void setSvRetrieveCount(Integer svRetrieveCount) {
        this.svRetrieveCount = svRetrieveCount;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSvEngineId() {
        return svEngineId;
    }

    public void setSvEngineId(Integer svEngineId) {
        this.svEngineId = svEngineId;
    }

    public Integer getSearchEngineId() {
        return searchEngineId;
    }

    public void setSearchEngineId(Integer searchEngineId) {
        this.searchEngineId = searchEngineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getKeywordName() {
        return keywordName;
    }

    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    public String getClarityDBKeywordHash() {
        return clarityDBKeywordHash;
    }

    public void setClarityDBKeywordHash(String clarityDBKeywordHash) {
        this.clarityDBKeywordHash = clarityDBKeywordHash;
    }

    public Integer getIsSVFromRankcheck() {
        return isSVFromRankcheck;
    }

    public void setIsSVFromRankcheck(Integer isSVFromRankcheck) {
        this.isSVFromRankcheck = isSVFromRankcheck;
    }

    public Integer getRankcheckKeywordId() {
        return rankcheckKeywordId;
    }

    public void setRankcheckKeywordId(Integer rankcheckKeywordId) {
        this.rankcheckKeywordId = rankcheckKeywordId;
    }

    public Integer getRankcheckAdwordsId() {
        return rankcheckAdwordsId;
    }

    public void setRankcheckAdwordsId(Integer rankcheckAdwordsId) {
        this.rankcheckAdwordsId = rankcheckAdwordsId;
    }

    public Integer getSvUpdateDate() {
        return svUpdateDate;
    }

    public void setSvUpdateDate(Integer svUpdateDate) {
        this.svUpdateDate = svUpdateDate;
    }

    public Integer getSvRetrieveStatus() {
        return svRetrieveStatus;
    }

    public void setSvRetrieveStatus(Integer svRetrieveStatus) {
        this.svRetrieveStatus = svRetrieveStatus;
    }

    public Date getSvRetrieveTime() {
        return svRetrieveTime;
    }

    public void setSvRetrieveTime(Date svRetrieveTime) {
        this.svRetrieveTime = svRetrieveTime;
    }

    public Integer getAvgSV() {
        return avgSV;
    }

    public void setAvgSV(Integer avgSV) {
        this.avgSV = avgSV;
    }

    public Integer getMonthlySV1() {
        return monthlySV1;
    }

    public void setMonthlySV1(Integer monthlySV1) {
        this.monthlySV1 = monthlySV1;
    }

    public Integer getMonthlySV2() {
        return monthlySV2;
    }

    public void setMonthlySV2(Integer monthlySV2) {
        this.monthlySV2 = monthlySV2;
    }

    public Integer getMonthlySV3() {
        return monthlySV3;
    }

    public void setMonthlySV3(Integer monthlySV3) {
        this.monthlySV3 = monthlySV3;
    }

    public Integer getMonthlySV4() {
        return monthlySV4;
    }

    public void setMonthlySV4(Integer monthlySV4) {
        this.monthlySV4 = monthlySV4;
    }

    public Integer getMonthlySV5() {
        return monthlySV5;
    }

    public void setMonthlySV5(Integer monthlySV5) {
        this.monthlySV5 = monthlySV5;
    }

    public Integer getMonthlySV6() {
        return monthlySV6;
    }

    public void setMonthlySV6(Integer monthlySV6) {
        this.monthlySV6 = monthlySV6;
    }

    public Integer getMonthlySV7() {
        return monthlySV7;
    }

    public void setMonthlySV7(Integer monthlySV7) {
        this.monthlySV7 = monthlySV7;
    }

    public Integer getMonthlySV8() {
        return monthlySV8;
    }

    public void setMonthlySV8(Integer monthlySV8) {
        this.monthlySV8 = monthlySV8;
    }

    public Integer getMonthlySV9() {
        return monthlySV9;
    }

    public void setMonthlySV9(Integer monthlySV9) {
        this.monthlySV9 = monthlySV9;
    }

    public Integer getMonthlySV10() {
        return monthlySV10;
    }

    public void setMonthlySV10(Integer monthlySV10) {
        this.monthlySV10 = monthlySV10;
    }

    public Integer getMonthlySV11() {
        return monthlySV11;
    }

    public void setMonthlySV11(Integer monthlySV11) {
        this.monthlySV11 = monthlySV11;
    }

    public Integer getMonthlySV12() {
        return monthlySV12;
    }

    public void setMonthlySV12(Integer monthlySV12) {
        this.monthlySV12 = monthlySV12;
    }

    public Double getCompetition() {
        return competition;
    }

    public void setCompetition(Double competition) {
        this.competition = competition;
    }

    public Double getCpc() {
        return cpc;
    }

    public void setCpc(Double cpc) {
        this.cpc = cpc;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Integer getCriteriaId() {
        return criteriaId;
    }

    public void setCriteriaId(Integer criteriaId) {
        this.criteriaId = criteriaId;
    }

    public String getCanonicalName() {
        return canonicalName;
    }

    public void setCanonicalName(String canonicalName) {
        this.canonicalName = canonicalName;
    }

    public Integer getIsRejected() {
        return isRejected;
    }

    public void setIsRejected(Integer isRejected) {
        this.isRejected = isRejected;
    }

    public List<Integer> getCategoryList() {
        if (StringUtils.isBlank(category)) {
            return null;
        } else {
            String[] list = StringUtils.split(category, ',');
            List<Integer> res = new ArrayList<Integer>();
            for (String val : list) {
                if (StringUtils.isNotBlank(val)) {
                    try {
                        res.add(Integer.valueOf(val));
                    } catch (Exception e) {
                        System.out.println("Parse int val failed. category:" + category + ", val:" + val);
                        e.printStackTrace();
                    }
                }
            }
            return res;
        }
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public Integer getStartMonth() {
        return startMonth;
    }

    public void setStartMonth(Integer startMonth) {
        this.startMonth = startMonth;
    }

    public Integer getEndMonth() {
        return endMonth;
    }

    public void setEndMonth(Integer endMonth) {
        this.endMonth = endMonth;
    }

    public String getMonthlySVJson() {
        return monthlySVJson;
    }

    public void setMonthlySVJson(String monthlySVJson) {
        this.monthlySVJson = monthlySVJson;
    }

    public List<Integer> getMonthList() {
        if (StringUtils.isNotBlank(this.monthListStr)) {
            Type type = new TypeToken<List<Integer>>() {
            }.getType();
            List<Integer> list = new Gson().fromJson(this.monthListStr, type);
            return this.svList = list;
        } else {
            return null;
        }
    }

    public void setMonthList(List<Integer> monthList) {
        this.monthList = monthList;
    }

    public List<Integer> getSvList() {
        if (StringUtils.isNotBlank(this.svListStr)) {
            Type type = new TypeToken<List<Integer>>() {
            }.getType();
            List<Integer> list = new Gson().fromJson(this.svListStr, type);
            return this.svList = list;
        } else {
            return null;
        }
    }

    public void setSvList(List<Integer> svList) {
        this.svList = svList;
    }

    public String getMonthListStr() {
        return monthListStr;
    }

    public void setMonthListStr(String monthListStr) {
        this.monthListStr = monthListStr;
    }

    public String getSvListStr() {
        return svListStr;
    }

    public void setSvListStr(String svListStr) {
        this.svListStr = svListStr;
    }

    public Integer getRetrieveType() {
        return retrieveType;
    }

    public void setRetrieveType(Integer retrieveType) {
        this.retrieveType = retrieveType;
    }
}
