package seoclarity.backend.entity.actonia.adhoc;

public class AutoAdhocSvRetrieveCtrlEntity {

    public static final int GOOGLE_ADS_API = 1;
    public static final int KEYWORD_TOOL_IO = 2;
    public static final int DATA_FOR_SEO = 3;

    private Integer id;
    private Integer apiType;
    private Integer retrieveDate;
    private Integer retrievedCount;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRetrieveDate() {
        return retrieveDate;
    }

    public void setRetrieveDate(Integer retrieveDate) {
        this.retrieveDate = retrieveDate;
    }

    public Integer getRetrievedCount() {
        return retrievedCount;
    }

    public void setRetrievedCount(Integer retrievedCount) {
        this.retrievedCount = retrievedCount;
    }

    public Integer getApiType() {
        return apiType;
    }

    public void setApiType(Integer apiType) {
        this.apiType = apiType;
    }
}
