package seoclarity.backend.entity.actonia.adhoc;

import java.util.Date;

public class AutoAdhocRankDetailEntity {

    public static final int ENABLED = 1;
    public static final int DISABLED = 0;

    public static final int RERUN_FIRST_RUN = 0;
    public static final int RERUN_RE_RUN = 1;

    public static final int CREATE_USER_ID_BACKEND_INSERT = 7;

    public static final int RANK_STATUS_NOT_STARTED = 0;
    public static final int RANK_STATUS_PROCESSING = 1;
    public static final int RANK_STATUS_COMPLETEED_WITHOUT_ERROR = 2;
    public static final int RANK_STATUS_ERROR = 3;

    public static final int RETRIEVESV_STATUS_NOT_STARTED = 0;
    public static final int RETRIEVESV_STATUS_PROCESSING = 1;
    public static final int RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR = 2;
    public static final int RETRIEVESV_STATUS_ERROR = 3;

    private Integer id;
    private Integer enabled;
    private Integer projectId;
    private Integer rankDate;
    private Integer isRerun;
    private Integer createUserId;
    private String rankQueueName;
    private Date sendToRankQueueStartTime;
    private Date sendToRankQueueEndTime;
    private Integer rankStatus;
    private Integer rankErrorMsg;
    private String retrieveSVQueueName;
    private Date sendToSVQueueStartTime;
    private Date sendToSVQueueEndTime;
    private Integer retrieveSVStatus;
    private String retrieveSVErrorMsg;
    private Date createdAt;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public Integer getRankDate() {
        return rankDate;
    }

    public void setRankDate(Integer rankDate) {
        this.rankDate = rankDate;
    }

    public Integer getIsRerun() {
        return isRerun;
    }

    public void setIsRerun(Integer isRerun) {
        this.isRerun = isRerun;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public String getRankQueueName() {
        return rankQueueName;
    }

    public void setRankQueueName(String rankQueueName) {
        this.rankQueueName = rankQueueName;
    }

    public Date getSendToRankQueueStartTime() {
        return sendToRankQueueStartTime;
    }

    public void setSendToRankQueueStartTime(Date sendToRankQueueStartTime) {
        this.sendToRankQueueStartTime = sendToRankQueueStartTime;
    }

    public Date getSendToRankQueueEndTime() {
        return sendToRankQueueEndTime;
    }

    public void setSendToRankQueueEndTime(Date sendToRankQueueEndTime) {
        this.sendToRankQueueEndTime = sendToRankQueueEndTime;
    }

    public Integer getRankStatus() {
        return rankStatus;
    }

    public void setRankStatus(Integer rankStatus) {
        this.rankStatus = rankStatus;
    }

    public Integer getRankErrorMsg() {
        return rankErrorMsg;
    }

    public void setRankErrorMsg(Integer rankErrorMsg) {
        this.rankErrorMsg = rankErrorMsg;
    }

    public String getRetrieveSVQueueName() {
        return retrieveSVQueueName;
    }

    public void setRetrieveSVQueueName(String retrieveSVQueueName) {
        this.retrieveSVQueueName = retrieveSVQueueName;
    }

    public Date getSendToSVQueueStartTime() {
        return sendToSVQueueStartTime;
    }

    public void setSendToSVQueueStartTime(Date sendToSVQueueStartTime) {
        this.sendToSVQueueStartTime = sendToSVQueueStartTime;
    }

    public Date getSendToSVQueueEndTime() {
        return sendToSVQueueEndTime;
    }

    public void setSendToSVQueueEndTime(Date sendToSVQueueEndTime) {
        this.sendToSVQueueEndTime = sendToSVQueueEndTime;
    }

    public Integer getRetrieveSVStatus() {
        return retrieveSVStatus;
    }

    public void setRetrieveSVStatus(Integer retrieveSVStatus) {
        this.retrieveSVStatus = retrieveSVStatus;
    }

    public String getRetrieveSVErrorMsg() {
        return retrieveSVErrorMsg;
    }

    public void setRetrieveSVErrorMsg(String retrieveSVErrorMsg) {
        this.retrieveSVErrorMsg = retrieveSVErrorMsg;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
