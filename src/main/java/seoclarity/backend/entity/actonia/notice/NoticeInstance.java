package seoclarity.backend.entity.actonia.notice;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NoticeInstance {
    private Long id;
    private Integer ownDomainId;
    private String category;
    private Integer dataType;
    private Integer subId;
    private Integer trackDate;
    private Integer status;
    private Integer messageId;
    private Integer authorId;
    private String missingReason;
    private Date updateDate;
    private Date createDate;
}
