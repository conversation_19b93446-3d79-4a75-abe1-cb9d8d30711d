package seoclarity.backend.entity.actonia;

import java.util.Date;

public class ApiTaskInstanceEntity {

	public static final int STATUS_CREATED = 0; // 0: newly created
	public static final int STATUS_PROCESSING = 1; // 1: processing
	public static final int STATUS_SUCCESS = 2; // 2:process successfully
	public static final int STATUS_ERR0R = 3; // 3:process error
	public static final int STATUS_INVALID = 4; // 3:invalid resources
	
	private Integer id;
	private Integer taskInfoId;
	private String taskInstanceName;
	private Integer ownDomainId;
	private Integer searchEngineId;
	private Integer languageId;
	private Integer cityId;
	private String paramHash;
	private String taskId;
	private Integer elementType;
	private String requestIp;
	private Integer createUserId;
	private Integer status;
	private Integer paramElementCount;
	private Integer uniqueElementCount;
	private Integer successElementCount;
	private Integer failureElementCount;
	private Date processStartTime;
	private Date processEndTime;
	private String ftpFullPathFilename;
	private Long outputFileSize;
	private Integer downloadTimes;
	private Date lastDownloadTime;
	private Integer createDate;
	private Date createdAt;
	private Date updateDate;
	
	private String errorMessage;
	private String errorTrace;
	private String errorMsgId;

	// https://www.wrike.com/open.htm?id=1057201829
	private Integer userId;

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public String getErrorTrace() {
		return errorTrace;
	}

	public void setErrorTrace(String errorTrace) {
		this.errorTrace = errorTrace;
	}

	public String getErrorMsgId() {
		return errorMsgId;
	}

	public void setErrorMsgId(String errorMsgId) {
		this.errorMsgId = errorMsgId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getTaskInfoId() {
		return taskInfoId;
	}

	public void setTaskInfoId(Integer taskInfoId) {
		this.taskInfoId = taskInfoId;
	}

	public String getTaskInstanceName() {
		return taskInstanceName;
	}

	public void setTaskInstanceName(String taskInstanceName) {
		this.taskInstanceName = taskInstanceName;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(Integer searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public String getParamHash() {
		return paramHash;
	}

	public void setParamHash(String paramHash) {
		this.paramHash = paramHash;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public Integer getElementType() {
		return elementType;
	}

	public void setElementType(Integer elementType) {
		this.elementType = elementType;
	}

	public String getRequestIp() {
		return requestIp;
	}

	public void setRequestIp(String requestIp) {
		this.requestIp = requestIp;
	}

	public Integer getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getParamElementCount() {
		return paramElementCount;
	}

	public void setParamElementCount(Integer paramElementCount) {
		this.paramElementCount = paramElementCount;
	}

	public Integer getUniqueElementCount() {
		return uniqueElementCount;
	}

	public void setUniqueElementCount(Integer uniqueElementCount) {
		this.uniqueElementCount = uniqueElementCount;
	}

	public Integer getSuccessElementCount() {
		return successElementCount;
	}

	public void setSuccessElementCount(Integer successElementCount) {
		this.successElementCount = successElementCount;
	}

	public Integer getFailureElementCount() {
		return failureElementCount;
	}

	public void setFailureElementCount(Integer failureElementCount) {
		this.failureElementCount = failureElementCount;
	}

	public Date getProcessStartTime() {
		return processStartTime;
	}

	public void setProcessStartTime(Date processStartTime) {
		this.processStartTime = processStartTime;
	}

	public Date getProcessEndTime() {
		return processEndTime;
	}

	public void setProcessEndTime(Date processEndTime) {
		this.processEndTime = processEndTime;
	}

	public String getFtpFullPathFilename() {
		return ftpFullPathFilename;
	}

	public void setFtpFullPathFilename(String ftpFullPathFilename) {
		this.ftpFullPathFilename = ftpFullPathFilename;
	}

	public Long getOutputFileSize() {
		return outputFileSize;
	}

	public void setOutputFileSize(Long outputFileSize) {
		this.outputFileSize = outputFileSize;
	}

	public Integer getDownloadTimes() {
		return downloadTimes;
	}

	public void setDownloadTimes(Integer downloadTimes) {
		this.downloadTimes = downloadTimes;
	}

	public Date getLastDownloadTime() {
		return lastDownloadTime;
	}

	public void setLastDownloadTime(Date lastDownloadTime) {
		this.lastDownloadTime = lastDownloadTime;
	}

	public Integer getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Integer createDate) {
		this.createDate = createDate;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
}