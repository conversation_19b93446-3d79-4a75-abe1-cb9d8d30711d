/**
 * 
 */
package seoclarity.backend.entity.actonia;

import java.util.Date;

public class ResourceDeleteInfoEntity {
	
	public static final int DISABLED = 0;
	public static final int ENABLED = 1;
	
	public static final int STATUS_NEWLY_CREATED = 0;
	public static final int STATUS_PROCESSING = 1;
	public static final int STATUS_PROCESS_FINISHED_WITHOUT_ERROR = 2;
	public static final int STATUS_PROCESS_FINISHED_WITH_ERROR = 3;
	
	private Integer id;
	private Integer enabled;
	private Integer ownDomainId;
	private Integer userId;
	private Date createDate;
	private Integer status;
	private Date processDate;
	private Date endDate;
	private Integer statusRankcheck;
	private Date processDateRankcheck;
	private Date endDateRankcheck;
	private Integer seq;
	private String serverInfo;
	private int detailCount;
	private int resourceType;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Date getProcessDate() {
		return processDate;
	}

	public void setProcessDate(Date processDate) {
		this.processDate = processDate;
	}
	
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	public Integer getStatusRankcheck() {
		return statusRankcheck;
	}

	public void setStatusRankcheck(Integer statusRankcheck) {
		this.statusRankcheck = statusRankcheck;
	}

	public Date getProcessDateRankcheck() {
		return processDateRankcheck;
	}

	public void setProcessDateRankcheck(Date processDateRankcheck) {
		this.processDateRankcheck = processDateRankcheck;
	}

	public Date getEndDateRankcheck() {
		return endDateRankcheck;
	}

	public void setEndDateRankcheck(Date endDateRankcheck) {
		this.endDateRankcheck = endDateRankcheck;
	}
	
	public Integer getSeq() {
		return seq;
	}

	public void setSeq(Integer seq) {
		this.seq = seq;
	}

	public String getServerInfo() {
		return serverInfo;
	}

	public void setServerInfo(String serverInfo) {
		this.serverInfo = serverInfo;
	}
	
	public int getDetailCount() {
		return detailCount;
	}

	public void setDetailCount(int detailCount) {
		this.detailCount = detailCount;
	}
	
	public int getResourceType() {
		return resourceType;
	}

	public void setResourceType(int resourceType) {
		this.resourceType = resourceType;
	}
}