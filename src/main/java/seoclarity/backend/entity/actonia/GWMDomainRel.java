package seoclarity.backend.entity.actonia;

public class GWMDomainRel {
	
	public static final int DATA_SOURCE_GSC = 0;
	
	private Integer id;
	private Integer ownDomainId;
	private String gwmDomainName;
	private String gwmCountryCode;
	private Integer defaultDomain;
	private Integer dataSource;
	
	private Integer isAuthenticationIssue;
	private String formattedProfileName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getGwmDomainName() {
		return gwmDomainName;
	}

	public void setGwmDomainName(String gwmDomainName) {
		this.gwmDomainName = gwmDomainName;
	}

	public String getGwmCountryCode() {
		return gwmCountryCode;
	}

	public void setGwmCountryCode(String gwmCountryCode) {
		this.gwmCountryCode = gwmCountryCode;
	}

	public Integer getDefaultDomain() {
		return defaultDomain;
	}

	public void setDefaultDomain(Integer defaultDomain) {
		this.defaultDomain = defaultDomain;
	}

	public Integer getDataSource() {
		return dataSource;
	}

	public void setDataSource(Integer dataSource) {
		this.dataSource = dataSource;
	}
	
	public Integer getIsAuthenticationIssue() {
		return isAuthenticationIssue;
	}

	public void setIsAuthenticationIssue(Integer isAuthenticationIssue) {
		this.isAuthenticationIssue = isAuthenticationIssue;
	}
	
	public String getFormattedProfileName() {
		return formattedProfileName;
	}

	public void setFormattedProfileName(String formattedProfileName) {
		this.formattedProfileName = formattedProfileName;
	}
}
