package seoclarity.backend.entity.actonia.tiktok;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TiktokKeyword {

    @Id
    private Long id;

    private String country;

    private String industry;

    private Integer crawlWeek;

    private Integer crawlDate;

    private String keyword;

    private String keywordMurmur3hash;

    private Integer comments;

    private Float cost;

    private Float cpa;

    private Float ctr;

    private Float cvr;

    private Double impression;

    private Integer likes;

    private Float sixSecondRate;

    private Integer post;

    private Float postChange;

    private Integer share;

    private Date createDate;

}
