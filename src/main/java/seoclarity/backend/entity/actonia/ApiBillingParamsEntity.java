package seoclarity.backend.entity.actonia;

public class ApiBillingParamsEntity {

	private long id;
	private long billingInstanceId;
	private String queryString;
	private String requestBody;
	private String userAgent;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getBillingInstanceId() {
		return billingInstanceId;
	}

	public void setBillingInstanceId(long billingInstanceId) {
		this.billingInstanceId = billingInstanceId;
	}

	public String getQueryString() {
		return queryString;
	}

	public void setQueryString(String queryString) {
		this.queryString = queryString;
	}

	public String getRequestBody() {
		return requestBody;
	}

	public void setRequestBody(String requestBody) {
		this.requestBody = requestBody;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

}
