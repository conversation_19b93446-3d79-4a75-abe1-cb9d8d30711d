package seoclarity.backend.entity.actonia;

import lombok.Data;
import java.util.Date;

@Data
public class GroupRelationshipFilterEntity implements Cloneable {

	public static int STATUS_ACTIVE = 1;

	private int groupId;
	private int ownDomainId;
	private String filter;
	private Date createDate;
	private Integer status;
	private Integer type;

	private String domainName;

	public GroupRelationshipFilterEntity clone() throws CloneNotSupportedException {
		return (GroupRelationshipFilterEntity) super.clone();
	}
}