package seoclarity.backend.entity.actonia.openai;

import java.util.Date;

public class OpenAiLoggingEntity {

    private Integer id;
    private Integer openAIActionId;
    private Integer promptId;
    private Integer ownDomainId;
    private String variableJson;
    private String responseJson;
    private Date createDate;
    private Integer userId;
    private Integer totalTokens;
    private String detailInfo;

    private String formatJson;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOpenAIActionId() {
        return openAIActionId;
    }

    public void setOpenAIActionId(Integer openAIActionId) {
        this.openAIActionId = openAIActionId;
    }

    public Integer getPromptId() {
        return promptId;
    }

    public void setPromptId(Integer promptId) {
        this.promptId = promptId;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public String getVariableJson() {
        return variableJson;
    }

    public void setVariableJson(String variableJson) {
        this.variableJson = variableJson;
    }

    public String getResponseJson() {
        return responseJson;
    }

    public void setResponseJson(String responseJson) {
        this.responseJson = responseJson;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getTotalTokens() {
        return totalTokens;
    }

    public void setTotalTokens(Integer totalTokens) {
        this.totalTokens = totalTokens;
    }

    public String getDetailInfo() {
        return detailInfo;
    }

    public void setDetailInfo(String detailInfo) {
        this.detailInfo = detailInfo;
    }

    public String getFormatJson() {
        return formatJson;
    }

    public void setFormatJson(String formatJson) {
        this.formatJson = formatJson;
    }
}
