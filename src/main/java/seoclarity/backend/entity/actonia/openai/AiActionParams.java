package seoclarity.backend.entity.actonia.openai;

import seoclarity.backend.entity.actonia.AttachmentUploadDetailEntity;

import java.util.List;
import java.util.Map;

public class AiActionParams {

    public static final String DATA_TYPE_SITE_AUDIT = "siteAudit";
    public static final String DATA_TYPE_MANAGED_URL = "managedUrl";

    private String funcCode;
    private String oid;
    private String contentLevel;
    private Boolean domainLevel;
    private String tagId;
    private String targetTagName;
    private String keywordBaseOn;
    private String engineLanguage;
    private Long projectId;
    private Integer crawlId;
    private List<String> fields;

    private String dataType;
    private List<SiteClarityFiltersVO> filters;
    private Map<String, String> urlIdKeywordList;

    private String filtersStr;

    public String getFuncCode() {
        return funcCode;
    }

    public void setFuncCode(String funcCode) {
        this.funcCode = funcCode;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getContentLevel() {
        return contentLevel;
    }

    public void setContentLevel(String contentLevel) {
        this.contentLevel = contentLevel;
    }

    public Boolean getDomainLevel() {
        return domainLevel;
    }

    public void setDomainLevel(Boolean domainLevel) {
        this.domainLevel = domainLevel;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getTargetTagName() {
        return targetTagName;
    }

    public void setTargetTagName(String targetTagName) {
        this.targetTagName = targetTagName;
    }

    public String getKeywordBaseOn() {
        return keywordBaseOn;
    }

    public void setKeywordBaseOn(String keywordBaseOn) {
        this.keywordBaseOn = keywordBaseOn;
    }

    public String getEngineLanguage() {
        return engineLanguage;
    }

    public void setEngineLanguage(String engineLanguage) {
        this.engineLanguage = engineLanguage;
    }


    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Integer getCrawlId() {
        return crawlId;
    }

    public void setCrawlId(Integer crawlId) {
        this.crawlId = crawlId;
    }

    public List<String> getFields() {
        return fields;
    }

    public void setFields(List<String> fields) {
        this.fields = fields;
    }

    public List<SiteClarityFiltersVO> getFilters() {
        return filters;
    }

    public void setFilters(List<SiteClarityFiltersVO> filters) {
        this.filters = filters;
    }

    public String getFiltersStr() {
        return filtersStr;
    }

    public void setFiltersStr(String filtersStr) {
        this.filtersStr = filtersStr;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Map<String, String> getUrlIdKeywordList() {
        return urlIdKeywordList;
    }

    public void setUrlIdKeywordList(Map<String, String> urlIdKeywordList) {
        this.urlIdKeywordList = urlIdKeywordList;
    }
}
