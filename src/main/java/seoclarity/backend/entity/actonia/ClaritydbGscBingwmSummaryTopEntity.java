package seoclarity.backend.entity.actonia;

import java.util.Date;

public class ClaritydbGscBingwmSummaryTopEntity {

    private Integer id;
    private Integer ownDomainId;
    private Integer relId;
    private Integer logDate;
    private Float clicks;
    private Float impressions;
    private Date createDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getRelId() {
        return relId;
    }

    public void setRelId(Integer relId) {
        this.relId = relId;
    }

    public Integer getLogDate() {
        return logDate;
    }

    public void setLogDate(Integer logDate) {
        this.logDate = logDate;
    }

    public Float getClicks() {
        return clicks;
    }

    public void setClicks(Float clicks) {
        this.clicks = clicks;
    }

    public Float getImpressions() {
        return impressions;
    }

    public void setImpressions(Float impressions) {
        this.impressions = impressions;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}
