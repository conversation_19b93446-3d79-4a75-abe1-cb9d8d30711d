package seoclarity.backend.entity.actonia.geo;

import java.util.Date;

public class KeywordGeoMappingEntity {

    private Long id;
    private Integer ownDomainId;
    private Integer engineId;
    private Integer languageId;
    private String device;
    private Long keywordId;
    private Long keywordRankcheckId;
    private Integer cityId;
    private Integer createDate;
    private Integer avgCitySearchVolume;
    private Integer queuebaseAddInfoId;
    private Date createAt;

    private String keywordName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getEngineId() {
        return engineId;
    }

    public void setEngineId(Integer engineId) {
        this.engineId = engineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public Long getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(Long keywordId) {
        this.keywordId = keywordId;
    }

    public Long getKeywordRankcheckId() {
        return keywordRankcheckId;
    }

    public void setKeywordRankcheckId(Long keywordRankcheckId) {
        this.keywordRankcheckId = keywordRankcheckId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Integer createDate) {
        this.createDate = createDate;
    }

    public Integer getAvgCitySearchVolume() {
        return avgCitySearchVolume;
    }

    public void setAvgCitySearchVolume(Integer avgCitySearchVolume) {
        this.avgCitySearchVolume = avgCitySearchVolume;
    }

    public Integer getQueuebaseAddInfoId() {
        return queuebaseAddInfoId;
    }

    public void setQueuebaseAddInfoId(Integer queuebaseAddInfoId) {
        this.queuebaseAddInfoId = queuebaseAddInfoId;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public String getKeywordName() {
        return keywordName;
    }

    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }
}
