/**
 *
 */
package seoclarity.backend.entity.actonia;

import java.util.Date;

public class DomainCompanyEntity {

    private Integer OID;
    private String domain;
    private String SEType;
    private String SE;
    private Integer kwCnt;

    private Integer id;
    private String KwChange;
    private Integer totolKeywordInDomain;

    public Integer getOID() {
        return OID;
    }

    public void setOID(Integer OID) {
        this.OID = OID;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getSEType() {
        return SEType;
    }

    public void setSEType(String SEType) {
        this.SEType = SEType;
    }

    public String getSE() {
        return SE;
    }

    public void setSE(String SE) {
        this.SE = SE;
    }

    public Integer getKwCnt() {
        return kwCnt;
    }

    public void setKwCnt(Integer kwCnt) {
        this.kwCnt = kwCnt;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public String getKwChange() {
        return KwChange;
    }

    public void setKwChange(String kwChange) {
        KwChange = kwChange;
    }

    public Integer getTotolKeywordInDomain() {
        return totolKeywordInDomain;
    }

    public void setTotolKeywordInDomain(Integer totolKeywordInDomain) {
        this.totolKeywordInDomain = totolKeywordInDomain;
    }
}
