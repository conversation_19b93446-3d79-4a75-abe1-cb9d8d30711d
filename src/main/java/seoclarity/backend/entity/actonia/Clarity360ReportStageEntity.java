package seoclarity.backend.entity.actonia;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Clarity360ReportStageEntity {
	
	//0: not started, 1: processing, 2: process completed, 3: process error
	public static final Integer STATUS_NOT_START = 0;
	public static final Integer STATUS_PROCESSING = 1;
	public static final Integer STATUS_COMPLETE = 2;
	public static final Integer STATUS_ERROR = 3;
	
	//1.SiteHealth, 2.GSC, 3.GA, 4.Bo<PERSON>, 5.SiteMap, 6.RI
	public static final Integer STEP_SITEHEALTH = 1;
	public static final Integer STEP_GSC = 2;
	public static final Integer STEP_GA = 3;
	public static final Integer STEP_BOT = 4;
	public static final Integer STEP_SITEMAP = 5;
	public static final Integer STEP_RI = 6;
	public static final Integer STEP_RG = 7;
	public static final Integer STEP_INTERNAL_LINK = 8;
	public static final Integer STEP_BACKLINK = 9;
	public static final Integer STEP_CUSTOM_DATA_SOURCE = 10;
	public static final Integer STEP_BOT_BING = 11;
	public static final Integer STEP_BOT_OPENAI = 12;
	public static final Integer STEP_BOT_PERPLEXITY = 13;
	public static final Integer STEP_BING_SEARCH_ANALYSIS = 14;
	public static final Integer STEP_URL_INSPECTION = 15;
	
	public static final Integer STEP_OPT_SERVER1 = 16;
	public static final Integer STEP_OPT_SERVER2 = 17;
	public static final Integer STEP_OPT_SERVER3 = 18;
	public static final Integer STEP_OPT_SERVER4 = 19;
	public static final Integer STEP_OPT_SERVER5 = 20;
	public static final Integer STEP_OPT_SERVER1_URL_HASH = 21;
	public static final Integer STEP_OPT_SERVER2_URL_HASH = 22;
	public static final Integer STEP_OPT_SERVER3_URL_HASH = 23;
	public static final Integer STEP_OPT_SERVER4_URL_HASH = 24;
	public static final Integer STEP_OPT_SERVER5_URL_HASH = 25;
	
	public static final Integer STEP_LOAD_INTO_FINAL = 30;
	public static final Integer STEP_LOAD_INTO_FINAL_NORMALIZATION = 31;
	public static List<Integer> STEP_LIST = new ArrayList<>();
	static {
		STEP_LIST.add(STEP_SITEHEALTH);
		STEP_LIST.add(STEP_GSC);
		STEP_LIST.add(STEP_GA);
		STEP_LIST.add(STEP_BOT);
		STEP_LIST.add(STEP_SITEMAP);
		STEP_LIST.add(STEP_RI);
		STEP_LIST.add(STEP_RG);
		STEP_LIST.add(STEP_INTERNAL_LINK);
		STEP_LIST.add(STEP_BACKLINK);
		STEP_LIST.add(STEP_CUSTOM_DATA_SOURCE);
		
		STEP_LIST.add(STEP_BOT_BING);
		STEP_LIST.add(STEP_BOT_OPENAI);
		STEP_LIST.add(STEP_BOT_PERPLEXITY);
		STEP_LIST.add(STEP_BING_SEARCH_ANALYSIS);
		STEP_LIST.add(STEP_URL_INSPECTION);
		
		STEP_LIST.add(STEP_OPT_SERVER1);
		STEP_LIST.add(STEP_OPT_SERVER2);
		STEP_LIST.add(STEP_OPT_SERVER3);
		STEP_LIST.add(STEP_OPT_SERVER4);
		STEP_LIST.add(STEP_OPT_SERVER5);
	}
	

	private Integer id;
	private Integer reportId;
	private Integer dataSource;
	private Integer status;
	private Date processStartTime;
	private Date processEndTime;
	private Integer sourceDataCount;
	private Integer targetDataCount;
	private String errorMessage;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getReportId() {
		return reportId;
	}

	public void setReportId(Integer reportId) {
		this.reportId = reportId;
	}

	public Integer getDataSource() {
		return dataSource;
	}

	public void setDataSource(Integer dataSource) {
		this.dataSource = dataSource;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getProcessStartTime() {
		return processStartTime;
	}

	public void setProcessStartTime(Date processStartTime) {
		this.processStartTime = processStartTime;
	}

	public Date getProcessEndTime() {
		return processEndTime;
	}

	public void setProcessEndTime(Date processEndTime) {
		this.processEndTime = processEndTime;
	}

	public Integer getSourceDataCount() {
		return sourceDataCount;
	}

	public void setSourceDataCount(Integer sourceDataCount) {
		this.sourceDataCount = sourceDataCount;
	}

	public Integer getTargetDataCount() {
		return targetDataCount;
	}

	public void setTargetDataCount(Integer targetDataCount) {
		this.targetDataCount = targetDataCount;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

}
