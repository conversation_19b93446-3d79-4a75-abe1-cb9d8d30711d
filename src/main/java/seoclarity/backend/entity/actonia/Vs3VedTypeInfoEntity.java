package seoclarity.backend.entity.actonia;

import java.util.Date;
import java.util.List;

public class Vs3VedTypeInfoEntity {

    private String version;
    private String device;
    private List<String> vedTypeArray;
    private String typeMainKey;
    private String imageFileName;
    private List<String> vedAttrKey;
    private List<String> vedAttrValue;
    private List<String> vedRawAttrKey;
    private List<String> vedRawAttrValue;
    private Date createDate;
    private Integer engineId;
    private Integer languageId;
    private String keywordName;
    private String vedType;
    private String typeMain;
    private String vedTypeMurmurHash;
    private String imageFileNameMurmurHash;

    private Integer type; //https://www.wrike.com/open.htm?id=1261783314

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public List<String> getVedTypeArray() {
        return vedTypeArray;
    }

    public void setVedTypeArray(List<String> vedTypeArray) {
        this.vedTypeArray = vedTypeArray;
    }

    public String getImageFileName() {
        return imageFileName;
    }

    public void setImageFileName(String imageFileName) {
        this.imageFileName = imageFileName;
    }

    public List<String> getVedAttrKey() {
        return vedAttrKey;
    }

    public void setVedAttrKey(List<String> vedAttrKey) {
        this.vedAttrKey = vedAttrKey;
    }

    public List<String> getVedAttrValue() {
        return vedAttrValue;
    }

    public void setVedAttrValue(List<String> vedAttrValue) {
        this.vedAttrValue = vedAttrValue;
    }

    public List<String> getVedRawAttrKey() {
        return vedRawAttrKey;
    }

    public void setVedRawAttrKey(List<String> vedRawAttrKey) {
        this.vedRawAttrKey = vedRawAttrKey;
    }

    public List<String> getVedRawAttrValue() {
        return vedRawAttrValue;
    }

    public void setVedRawAttrValue(List<String> vedRawAttrValue) {
        this.vedRawAttrValue = vedRawAttrValue;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getEngineId() {
        return engineId;
    }

    public void setEngineId(Integer engineId) {
        this.engineId = engineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getKeywordName() {
        return keywordName;
    }

    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    public String getVedType() {
        return vedType;
    }

    public void setVedType(String vedType) {
        this.vedType = vedType;
    }


    public String getTypeMain() {
        return typeMain;
    }

    public void setTypeMain(String typeMain) {
        this.typeMain = typeMain;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeMainKey() {
        return typeMainKey;
    }

    public void setTypeMainKey(String typeMainKey) {
        this.typeMainKey = typeMainKey;
    }

    public String getVedTypeMurmurHash() {
        return vedTypeMurmurHash;
    }

    public void setVedTypeMurmurHash(String vedTypeMurmurHash) {
        this.vedTypeMurmurHash = vedTypeMurmurHash;
    }

    public String getImageFileNameMurmurHash() {
        return imageFileNameMurmurHash;
    }
    public void setImageFileNameMurmurHash(String imageFileNameMurmurHash) {
        this.imageFileNameMurmurHash = imageFileNameMurmurHash;
    }
}
