package seoclarity.backend.entity.actonia;

import java.util.Date;

public class ClaritydbVersioning {
	
	public static final Integer DATATYPE_GSC = 1;
	public static final Integer DATATYPE_GA = 2;
	public static final Integer DATATYPE_BOT = 3;
	public static final Integer DATATYPE_PAGESPEED = 4;
	public static final Integer DATATYPE_INTERNAL_LINK = 5;
	public static final Integer DATATYPE_PPC = 6;
	public static final Integer DATATYPE_GA_OMNITURE = 8;
	
	private int id;

	// 1:GSC, 2:GA, 3:BOT, 4:PAGESPEED
	private int dataType;

	private int ownDomainId;

	private Date logDate;
	
	private long urlId;
	
	private int versioning;
	
	private Date createDate;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getDataType() {
		return dataType;
	}

	public void setDataType(int dataType) {
		this.dataType = dataType;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Date getLogDate() {
		return logDate;
	}

	public void setLogDate(Date logDate) {
		this.logDate = logDate;
	}

	public long getUrlId() {
		return urlId;
	}

	public void setUrlId(long urlId) {
		this.urlId = urlId;
	}

	public int getVersioning() {
		return versioning;
	}

	public void setVersioning(int versioning) {
		this.versioning = versioning;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

}
