package seoclarity.backend.entity.actonia;

import org.apache.commons.lang.StringUtils;


public class SolrInfoEntity {
	public static final Integer MONTHLY_RANKING_SOLR = 1;
	// https://www.wrike.com/open.htm?id=53485380
	public static final Integer MONTHLY_RANKING_MOBILE_SOLR = 2;
	public static final Integer SEARCH_VOLUME_SOLR = 4;
	public static final Integer CONTENT_IDEA_SOLR = 3;

	//Cee - https://www.wrike.com/open.htm?id=92556628
	public static final String COUNTRY_US = "US";
	public static final String COUNTRY_INTL = "INTL";
	
	public enum SolrType {
		MonthlyRank(1), 
		MonthlyRankMobile(2), 
		ContentIdea(3), 
		SearchVolume(4), 
		MonthlyMetrics(5), 
		MonthlyMetricsMobile(6),
		MonthlyCompetitor(7), 
		MonthlyCompetitorMobile(8),
		MonthlyRankES(10),
		MonthlyMetricsES(11),
		MonthlyCompetitorES(12),
		UniversalRankES(15),
		MonthlyLocallistingES(16),
		MonthlyMetricsTrendES(17);
		
		private final int value;

		SolrType(int value) {
			this.value = value;
		}

		public int getValue() {
			return this.value;
		}
	}
	
	private String countryCode;
	private String solrUrl;
	private Integer solrType;
	private Integer updateDate;
	private Integer solrMonth;
	private String ipList;
	private int connectFailCount;

	public Integer getSolrMonth() {
		return solrMonth;
	}

	public void setSolrMonth(Integer solrMonth) {
		this.solrMonth = solrMonth;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getSolrUrl() {
		return solrUrl;
	}

	public void setSolrUrl(String solrUrl) {
		this.solrUrl = solrUrl;
	}

	public Integer getSolrType() {
		return solrType;
	}

	public void setSolrType(Integer solrType) {
		this.solrType = solrType;
	}

	public Integer getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Integer updateDate) {
		this.updateDate = updateDate;
	}

	public String getIpList() {
		return ipList;
	}

	public void setIpList(String ipList) {
		this.ipList = ipList;
	}

	public int getConnectFailCount() {
		return connectFailCount;
	}

	public void setConnectFailCount(int connectFailCount) {
		this.connectFailCount = connectFailCount;
	}

	@Override
	public String toString() {
		return " SolrInfoEntity [countryCode=" + countryCode + ", solrUrl=" + solrUrl + ", solrType=" + solrType + ", updateDate=" + updateDate
				+ ", solrMonth=" + solrMonth + ", ipList=" + ipList + ", connectFailCount=" + connectFailCount + "]";
	}
	
	//Cee - https://www.wrike.com/open.htm?id=92556628
	public static String getCountryCode(String country, boolean isMobile, SolrType solrType) {
		if (solrType != SolrType.MonthlyRankES && 
				solrType != SolrType.MonthlyMetricsES && 
				solrType != SolrType.UniversalRankES &&
				solrType != SolrType.MonthlyCompetitorES  &&
				solrType != SolrType.MonthlyLocallistingES && 
				solrType != SolrType.MonthlyMetricsTrendES) {
			
			return country;
		}
		
		if (!StringUtils.equalsIgnoreCase(COUNTRY_US, country)) {
			country = COUNTRY_INTL;
		} else {
			country = COUNTRY_US;
		}
		
		if (isMobile) {
			return country + "_M";
		} else {
			return country + "_D";
		}
	}
	
}
