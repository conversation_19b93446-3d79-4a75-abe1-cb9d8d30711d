package seoclarity.backend.entity.actonia;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@EqualsAndHashCode
@NoArgsConstructor
public class ResourceProcessInstanceEntity {
    public static final int PROCESS_TYPE_ERROR_PAGE = 1001;
    public static final int PROCESS_TYPE_3XX_SUMMARY = 1002;

    public static final int STATUS_CREATED = 0;
    public static final int STATUS_PROCESSING = 1;
    public static final int STATUS_SUCCESS = 2;
    public static final int STATUS_ERROR = 3;


    private int id;
    private int processType;
    private int ownDomainId;
    private int userId;
    private String resourceId;
    private String resourceSubId;
    private int status;
    private Date processStartDate;
    private Date processEndDate;
    private String errorMsg;
    private Date createDate;
}
