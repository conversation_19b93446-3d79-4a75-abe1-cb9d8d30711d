package seoclarity.backend.entity.actonia.pixelcrawl;

import java.util.Date;

public class PixelCrawlInstanceEntity {

    public static final int STATUS_NOT_STARTED = 0;
    public static final int STATUS_PROCESSING = 1;
    public static final int STATUS_CRAWL_SUCCESS = 2;
    public static final int STATUS_CRAWL_FAILED = 3;

    public static int FREQ_DAILY_US_DESKTOP = 1; // us desktop
    public static int FREQ_DAILY_US_MOBILE = 2; // us mobile
    public static int FREQ_DAILY_INTL_DESKTOP = 3; // intl desktop
    public static int FREQ_DAILY_INTL_MOBILE = 4; // intl mobile
    public static int FREQ_WEEKLY = 7;
    public static int FREQ_WEEKLY_OLD_CRAWL_V2 = 77;

    private int id;
    private int rankDate;
    private int frequence;
    private String device;
    private String sqsName;
    private int status;
    private int totalKeywordCount;
    private Date createDate;
    private Date updateDate;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getRankDate() {
        return rankDate;
    }

    public void setRankDate(int rankDate) {
        this.rankDate = rankDate;
    }

    public int getFrequence() {
        return frequence;
    }

    public void setFrequence(int frequence) {
        this.frequence = frequence;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getSqsName() {
        return sqsName;
    }

    public void setSqsName(String sqsName) {
        this.sqsName = sqsName;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getTotalKeywordCount() {
        return totalKeywordCount;
    }

    public void setTotalKeywordCount(int totalKeywordCount) {
        this.totalKeywordCount = totalKeywordCount;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
