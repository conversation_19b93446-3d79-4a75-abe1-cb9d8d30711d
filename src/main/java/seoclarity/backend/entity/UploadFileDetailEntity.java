package seoclarity.backend.entity;

import java.util.Date;

public class UploadFileDetailEntity {
    public static final Integer UPLOAD_TYPE_BOT = 1;
    private Integer id;
    private Integer uploadType;
    private Integer ownDomainId;
    private String fileName;
    private Integer fileNameDay;
    private Long zipFileSize;
    private Long unzippedFileSize;
    private Integer dataCount;
    private Integer loadedCount;
    private Date createDate;

    public UploadFileDetailEntity() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUploadType() {
        return uploadType;
    }

    public void setUploadType(Integer uploadType) {
        this.uploadType = uploadType;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getFileNameDay() {
        return fileNameDay;
    }

    public void setFileNameDay(Integer fileNameDay) {
        this.fileNameDay = fileNameDay;
    }

    public Long getZipFileSize() {
        return zipFileSize;
    }

    public void setZipFileSize(Long zipFileSize) {
        this.zipFileSize = zipFileSize;
    }

    public Long getUnzippedFileSize() {
        return unzippedFileSize;
    }

    public void setUnzippedFileSize(Long unzippedFileSize) {
        this.unzippedFileSize = unzippedFileSize;
    }

    public Integer getDataCount() {
        return dataCount;
    }

    public void setDataCount(Integer dataCount) {
        this.dataCount = dataCount;
    }

    public Integer getLoadedCount() {
        return loadedCount;
    }

    public void setLoadedCount(Integer loadedCount) {
        this.loadedCount = loadedCount;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Override
    public String toString() {
        return "UploadFileDetail{" +
                "id=" + id +
                ", uploadType=" + uploadType +
                ", ownDomainId=" + ownDomainId +
                ", fileName='" + fileName + '\'' +
                ", fileNameDay=" + fileNameDay +
                ", zipFileSize=" + zipFileSize +
                ", unzippedFileSize=" + unzippedFileSize +
                ", dataCount=" + dataCount +
                ", loadedCount=" + loadedCount +
                ", createDate=" + createDate +
                '}';
    }
}
