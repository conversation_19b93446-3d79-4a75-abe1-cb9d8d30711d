package seoclarity.backend.entity.clickhouse.gscclicksteam;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class EstdSearchVolumeEntity {

    private Integer engineId;
    private Integer languageId;
    private Integer locationId;
    private Long keywordRankcheckId;
    private String keywordName;
    private String keywordHash;
    private Long estdSearchVolume;
    private Date createDate;
    private Integer sign;
    private Integer versioning;

}
