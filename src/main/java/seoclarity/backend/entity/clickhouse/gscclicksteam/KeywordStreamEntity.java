package seoclarity.backend.entity.clickhouse.gscclicksteam;

import java.util.Date;
import java.util.List;

public class KeywordStreamEntity {

    private Integer month;
    private Integer weekIndex;
    private String weekStartDate;
    private String weekEndDate;
    private String countryCd;
    private String keywordName;
    private String keywordHash;
    private Integer engineId;
    private Integer languageId;
    private List<String> stream;
    private List<String> word;
    private List<String> keywordVariationOneword;
    private List<String> keywordVariationNgram;
    private Date createDate;
    private Integer sign;
    private Float avgSearchVolume;
    private List<Integer> svAttrIntKey;
    private List<Integer> svAttrIntValue;

    public String getCountryCd() {
        return countryCd;
    }

    public void setCountryCd(String countryCd) {
        this.countryCd = countryCd;
    }

    public String getKeywordName() {
        return keywordName;
    }

    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    public String getKeywordHash() {
        return keywordHash;
    }

    public void setKeywordHash(String keywordHash) {
        this.keywordHash = keywordHash;
    }

    public Integer getEngineId() {
        return engineId;
    }

    public void setEngineId(Integer engineId) {
        this.engineId = engineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public List<String> getStream() {
        return stream;
    }

    public void setStream(List<String> stream) {
        this.stream = stream;
    }

    public List<String> getWord() {
        return word;
    }

    public void setWord(List<String> word) {
        this.word = word;
    }

    public List<String> getKeywordVariationOneword() {
        return keywordVariationOneword;
    }

    public void setKeywordVariationOneword(List<String> keywordVariationOneword) {
        this.keywordVariationOneword = keywordVariationOneword;
    }

    public List<String> getKeywordVariationNgram() {
        return keywordVariationNgram;
    }

    public void setKeywordVariationNgram(List<String> keywordVariationNgram) {
        this.keywordVariationNgram = keywordVariationNgram;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getSign() {
        return sign;
    }

    public void setSign(Integer sign) {
        this.sign = sign;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getWeekIndex() {
        return weekIndex;
    }

    public void setWeekIndex(Integer weekIndex) {
        this.weekIndex = weekIndex;
    }

    public String getWeekStartDate() {
        return weekStartDate;
    }

    public void setWeekStartDate(String weekStartDate) {
        this.weekStartDate = weekStartDate;
    }

    public String getWeekEndDate() {
        return weekEndDate;
    }

    public void setWeekEndDate(String weekEndDate) {
        this.weekEndDate = weekEndDate;
    }

    public Float getAvgSearchVolume() {
        return avgSearchVolume;
    }

    public void setAvgSearchVolume(Float avgSearchVolume) {
        this.avgSearchVolume = avgSearchVolume;
    }

    public List<Integer> getSvAttrIntKey() {
        return svAttrIntKey;
    }

    public void setSvAttrIntKey(List<Integer> svAttrIntKey) {
        this.svAttrIntKey = svAttrIntKey;
    }

    public List<Integer> getSvAttrIntValue() {
        return svAttrIntValue;
    }

    public void setSvAttrIntValue(List<Integer> svAttrIntValue) {
        this.svAttrIntValue = svAttrIntValue;
    }
}
