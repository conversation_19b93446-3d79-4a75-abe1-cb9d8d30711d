package seoclarity.backend.entity.clickhouse;

import java.util.Date;

public class AttrGrouptagRelEntity {
	private Date logDate;
	private Integer sign;
	private Integer groupId;
	private Integer tagId;
	private Integer ownDomainId;
	public Date getLogDate() {
		return logDate;
	}
	public void setLogDate(Date logDate) {
		this.logDate = logDate;
	}
	public Integer getSign() {
		return sign;
	}
	public void setSign(Integer sign) {
		this.sign = sign;
	}
	public Integer getGroupId() {
		return groupId;
	}
	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}
	public Integer getTagId() {
		return tagId;
	}
	public void setTagId(Integer tagId) {
		this.tagId = tagId;
	}
	public Integer getOwnDomainId() {
		return ownDomainId;
	}
	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	@Override
	public String toString() {
		return "AttrGrouptagRelEntity [logDate=" + logDate + ", sign=" + sign
				+ ", groupId=" + groupId + ", tagId=" + tagId
				+ ", ownDomainId=" + ownDomainId + "]";
	}
	
	
	
	
}
