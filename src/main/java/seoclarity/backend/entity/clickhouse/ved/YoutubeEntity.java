package seoclarity.backend.entity.clickhouse.ved;

import lombok.Getter;
import lombok.Setter;
import ru.yandex.clickhouse.ClickHouseArray;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class YoutubeEntity {
    private String videoId;
    private String youtubeTitle;
    private List<String> thumbnail;
    private String author;
    private String publishedTime;
    private String videolength;
    private String rankType;
    private String browseId;
    private String descriptionSnippet ;
    private List<String> badges;
    private String verifyStatus;
    private String videoCount;
    private String subscriberCount;
    private String subRank;
    private List<String> adsWebsite;
    private List<String> adsLink;
    private Integer ownDomainId;
    private Integer keywordRankcheckId;
    private Date rankingDate;
    private Integer engineId;
    private Integer languageId;
    private Integer locationId;
    private Integer searchVol;
    private double cpc;
    private String keyword;
    private Integer countOfSearch;
    private String canonicalBaseUrl;
    private String canonicalBaseUrlType;
    private String canonicalBaseUrlName;
    private String channelId;
    private String playlistId;
    private String websiteText;
    private String adUrl;
    private String generalId;
    private Integer hrd;
    private String viewCount;
    private Integer sign;
    private Integer trueRank;
    private List<String> attrsKey;
    private List<String> attrsvalue;
    private Integer hrrd;

    private String videoTitle;
    private String ownerText;
    private String rankDateStr;

    private ClickHouseArray videoTag;
}
