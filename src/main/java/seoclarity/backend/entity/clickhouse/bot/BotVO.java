package seoclarity.backend.entity.clickhouse.bot;

import java.util.Date;

/**
 * This entity is copied from Project "actoniaSubServer"
 * 
 * <AUTHOR>
 * @date 2017-03-23 
 * seoclarity.backend.entity.clickhouse.bot.BotVO
 */
public class BotVO {

	private int ownDomainId;
	private String uri;
	private String crawlIp;
	private int crawlDate;
	private int searchEngineId;
	private int responseCode;
	private String userAgentStr;
	private Date timestamp;

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public String getCrawlIp() {
		return crawlIp;
	}

	public void setCrawlIp(String crawlIp) {
		this.crawlIp = crawlIp;
	}

	public int getCrawlDate() {
		return crawlDate;
	}

	public void setCrawlDate(int crawlDate) {
		this.crawlDate = crawlDate;
	}

	public int getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(int searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	public int getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(int responseCode) {
		this.responseCode = responseCode;
	}

	public String getUserAgentStr() {
		return userAgentStr;
	}

	public void setUserAgentStr(String userAgentStr) {
		this.userAgentStr = userAgentStr;
	}

	public Date getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Date timestamp) {
		this.timestamp = timestamp;
	}
}