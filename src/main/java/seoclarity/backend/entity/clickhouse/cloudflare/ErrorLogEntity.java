package seoclarity.backend.entity.clickhouse.cloudflare;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ErrorLogEntity {
    private Integer ownDomainId;
    private String logDate;
    private String dateTimestamp;
    private String hash;
    private String messageType;
    private String errorType;
    private String url;
    private String urlHash;
    private String urlMurmur3Hash;
    private String errorMessage;
}
