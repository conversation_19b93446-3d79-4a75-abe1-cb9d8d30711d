package seoclarity.backend.entity.clickhouse.gsc;

import java.util.Date;

public class GscBqUrlEntity {

    private String dataDate;
    private Integer ownDomainId;
    private Integer versioning;
    private String device;
    private String countryCd;
    private String siteUrl;
    private String siteUrlHash;
    private String url;
    private String query;
    private String queryHash;
    private Integer isAnonymizedQuery;
    private String searchType;
    private Integer isAnonymizedDiscover;
    private Integer isAmpTopStories;
    private Integer isAmpBlueLink;
    private Integer isJobListing;
    private Integer isJobDetails;
    private Integer isTpfQa;
    private Integer isTpfFaq;
    private Integer isTpfHowto;
    private Integer isWeblite;
    private Integer isAction;
    private Integer isEventsListing;
    private Integer isEventsDetails;
    private Integer isSearchAppearanceAndroidApp;
    private Integer isAmpStory;
    private Integer isAmpImageResult;
    private Integer isVideo;
    private Integer isOrganicShopping;
    private Integer isReviewSnippet;
    private Integer isSpecialAnnouncement;
    private Integer isRecipeFeature;
    private Integer isRecipeRichSnippet;
    private Integer isSubscribedContent;
    private Integer isPageExperience;
    private Integer isPracticeProblems;
    private Integer isMathSolvers;
    private Integer isTranslatedResult;
    private Integer isEduQAndA;
    private Float impressions;
    private Float clicks;
    private Float sumPosition;
    private Float avgPosition;

    public String getDataDate() {
        return dataDate;
    }

    public void setDataDate(String dataDate) {
        this.dataDate = dataDate;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getVersioning() {
        return versioning;
    }

    public void setVersioning(Integer versioning) {
        this.versioning = versioning;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getCountryCd() {
        return countryCd;
    }

    public void setCountryCd(String countryCd) {
        this.countryCd = countryCd;
    }

    public String getSiteUrl() {
        return siteUrl;
    }

    public void setSiteUrl(String siteUrl) {
        this.siteUrl = siteUrl;
    }

    public String getSiteUrlHash() {
        return siteUrlHash;
    }

    public void setSiteUrlHash(String siteUrlHash) {
        this.siteUrlHash = siteUrlHash;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public Integer getIsAnonymizedQuery() {
        return isAnonymizedQuery;
    }

    public void setIsAnonymizedQuery(Integer isAnonymizedQuery) {
        this.isAnonymizedQuery = isAnonymizedQuery;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public Integer getIsAnonymizedDiscover() {
        return isAnonymizedDiscover;
    }

    public void setIsAnonymizedDiscover(Integer isAnonymizedDiscover) {
        this.isAnonymizedDiscover = isAnonymizedDiscover;
    }

    public Integer getIsAmpTopStories() {
        return isAmpTopStories;
    }

    public void setIsAmpTopStories(Integer isAmpTopStories) {
        this.isAmpTopStories = isAmpTopStories;
    }

    public Integer getIsAmpBlueLink() {
        return isAmpBlueLink;
    }

    public void setIsAmpBlueLink(Integer isAmpBlueLink) {
        this.isAmpBlueLink = isAmpBlueLink;
    }

    public Integer getIsJobListing() {
        return isJobListing;
    }

    public void setIsJobListing(Integer isJobListing) {
        this.isJobListing = isJobListing;
    }

    public Integer getIsJobDetails() {
        return isJobDetails;
    }

    public void setIsJobDetails(Integer isJobDetails) {
        this.isJobDetails = isJobDetails;
    }

    public Integer getIsTpfQa() {
        return isTpfQa;
    }

    public void setIsTpfQa(Integer isTpfQa) {
        this.isTpfQa = isTpfQa;
    }

    public Integer getIsTpfFaq() {
        return isTpfFaq;
    }

    public void setIsTpfFaq(Integer isTpfFaq) {
        this.isTpfFaq = isTpfFaq;
    }

    public Integer getIsTpfHowto() {
        return isTpfHowto;
    }

    public void setIsTpfHowto(Integer isTpfHowto) {
        this.isTpfHowto = isTpfHowto;
    }

    public Integer getIsWeblite() {
        return isWeblite;
    }

    public void setIsWeblite(Integer isWeblite) {
        this.isWeblite = isWeblite;
    }

    public Integer getIsAction() {
        return isAction;
    }

    public void setIsAction(Integer isAction) {
        this.isAction = isAction;
    }

    public Integer getIsEventsListing() {
        return isEventsListing;
    }

    public void setIsEventsListing(Integer isEventsListing) {
        this.isEventsListing = isEventsListing;
    }

    public Integer getIsEventsDetails() {
        return isEventsDetails;
    }

    public void setIsEventsDetails(Integer isEventsDetails) {
        this.isEventsDetails = isEventsDetails;
    }

    public Integer getIsSearchAppearanceAndroidApp() {
        return isSearchAppearanceAndroidApp;
    }

    public void setIsSearchAppearanceAndroidApp(Integer isSearchAppearanceAndroidApp) {
        this.isSearchAppearanceAndroidApp = isSearchAppearanceAndroidApp;
    }

    public Integer getIsAmpStory() {
        return isAmpStory;
    }

    public void setIsAmpStory(Integer isAmpStory) {
        this.isAmpStory = isAmpStory;
    }

    public Integer getIsAmpImageResult() {
        return isAmpImageResult;
    }

    public void setIsAmpImageResult(Integer isAmpImageResult) {
        this.isAmpImageResult = isAmpImageResult;
    }

    public Integer getIsVideo() {
        return isVideo;
    }

    public void setIsVideo(Integer isVideo) {
        this.isVideo = isVideo;
    }

    public Integer getIsOrganicShopping() {
        return isOrganicShopping;
    }

    public void setIsOrganicShopping(Integer isOrganicShopping) {
        this.isOrganicShopping = isOrganicShopping;
    }

    public Integer getIsReviewSnippet() {
        return isReviewSnippet;
    }

    public void setIsReviewSnippet(Integer isReviewSnippet) {
        this.isReviewSnippet = isReviewSnippet;
    }

    public Integer getIsSpecialAnnouncement() {
        return isSpecialAnnouncement;
    }

    public void setIsSpecialAnnouncement(Integer isSpecialAnnouncement) {
        this.isSpecialAnnouncement = isSpecialAnnouncement;
    }

    public Integer getIsRecipeFeature() {
        return isRecipeFeature;
    }

    public void setIsRecipeFeature(Integer isRecipeFeature) {
        this.isRecipeFeature = isRecipeFeature;
    }

    public Integer getIsRecipeRichSnippet() {
        return isRecipeRichSnippet;
    }

    public void setIsRecipeRichSnippet(Integer isRecipeRichSnippet) {
        this.isRecipeRichSnippet = isRecipeRichSnippet;
    }

    public Integer getIsSubscribedContent() {
        return isSubscribedContent;
    }

    public void setIsSubscribedContent(Integer isSubscribedContent) {
        this.isSubscribedContent = isSubscribedContent;
    }

    public Integer getIsPageExperience() {
        return isPageExperience;
    }

    public void setIsPageExperience(Integer isPageExperience) {
        this.isPageExperience = isPageExperience;
    }

    public Integer getIsPracticeProblems() {
        return isPracticeProblems;
    }

    public void setIsPracticeProblems(Integer isPracticeProblems) {
        this.isPracticeProblems = isPracticeProblems;
    }

    public Integer getIsMathSolvers() {
        return isMathSolvers;
    }

    public void setIsMathSolvers(Integer isMathSolvers) {
        this.isMathSolvers = isMathSolvers;
    }

    public Integer getIsTranslatedResult() {
        return isTranslatedResult;
    }

    public void setIsTranslatedResult(Integer isTranslatedResult) {
        this.isTranslatedResult = isTranslatedResult;
    }

    public Integer getIsEduQAndA() {
        return isEduQAndA;
    }

    public void setIsEduQAndA(Integer isEduQAndA) {
        this.isEduQAndA = isEduQAndA;
    }

    public Float getImpressions() {
        return impressions;
    }

    public void setImpressions(Float impressions) {
        this.impressions = impressions;
    }

    public Float getClicks() {
        return clicks;
    }

    public void setClicks(Float clicks) {
        this.clicks = clicks;
    }

    public Float getSumPosition() {
        return sumPosition;
    }

    public void setSumPosition(Float sumPosition) {
        this.sumPosition = sumPosition;
    }

    public Float getAvgPosition() {
        return avgPosition;
    }

    public void setAvgPosition(Float avgPosition) {
        this.avgPosition = avgPosition;
    }

    public String getQueryHash() {
        return queryHash;
    }

    public void setQueryHash(String queryHash) {
        this.queryHash = queryHash;
    }
}
