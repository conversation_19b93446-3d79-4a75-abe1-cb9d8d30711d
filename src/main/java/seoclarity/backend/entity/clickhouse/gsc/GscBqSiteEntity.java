package seoclarity.backend.entity.clickhouse.gsc;

import java.util.Date;

public class GscBqSiteEntity {

    private String dataDate;
    private Integer ownDomainId;
    private Integer versioning;
    private String device;
    private String countryCd;
    private String siteUrl;
    private String siteUrlHash;
    private String query;
    private String queryHash;
    private Integer isAnonymizedQuery;
    private String searchType;
    private Float impressions;
    private Float clicks;
    private Float sumTopPosition;
    private Float avgPosition;

    public String getDataDate() {
        return dataDate;
    }

    public void setDataDate(String dataDate) {
        this.dataDate = dataDate;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Integer getVersioning() {
        return versioning;
    }

    public void setVersioning(Integer versioning) {
        this.versioning = versioning;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getCountryCd() {
        return countryCd;
    }

    public void setCountryCd(String countryCd) {
        this.countryCd = countryCd;
    }

    public String getSiteUrl() {
        return siteUrl;
    }

    public void setSiteUrl(String siteUrl) {
        this.siteUrl = siteUrl;
    }

    public String getSiteUrlHash() {
        return siteUrlHash;
    }

    public void setSiteUrlHash(String siteUrlHash) {
        this.siteUrlHash = siteUrlHash;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public Integer getIsAnonymizedQuery() {
        return isAnonymizedQuery;
    }

    public void setIsAnonymizedQuery(Integer isAnonymizedQuery) {
        this.isAnonymizedQuery = isAnonymizedQuery;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public Float getImpressions() {
        return impressions;
    }

    public void setImpressions(Float impressions) {
        this.impressions = impressions;
    }

    public Float getClicks() {
        return clicks;
    }

    public void setClicks(Float clicks) {
        this.clicks = clicks;
    }

    public Float getSumTopPosition() {
        return sumTopPosition;
    }

    public void setSumTopPosition(Float sumTopPosition) {
        this.sumTopPosition = sumTopPosition;
    }

    public Float getAvgPosition() {
        return avgPosition;
    }

    public void setAvgPosition(Float avgPosition) {
        this.avgPosition = avgPosition;
    }

    public String getQueryHash() {
        return queryHash;
    }

    public void setQueryHash(String queryHash) {
        this.queryHash = queryHash;
    }
}
