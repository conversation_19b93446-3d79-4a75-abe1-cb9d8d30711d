package seoclarity.backend.entity.clickhouse.tiktok;

public class TiktokDetailEntity {
	
	/**
	 *  `keyword_name` String,
  `own_domain_id` UInt16,
  `keyword_rankcheck_id` UInt32,
  `engine_id` UInt8,
  `language_id` UInt8,
  `location_id` UInt32,
  `ranking_date` Date,
  `author_unique_id` String,
  `author_nickname` String,
  `author_status` String,
  `video_desc` String,
  `video_id` String ,
  `video_duration` UInt32 ,
  `video_stats` String ,
  `video_uri` String
  `attrstr.key` Array(String),
  `attrstr.value` Array(String),
  `attrint.key` Array(String),
  `attrint.value` Array(UInt32),
  `sign` Int8,
  `versioning` UInt8 DEFAULT CAST(1, 'UInt8'),
  `frequency` UInt8 DEFAULT CAST(1, 'UInt8'),
  `create_time` DateTime DEFAULT formatDateTime(now(), '%F %T')
	 */
	
	private String keywordName;
	private Integer ownDomainId;
	private Integer keywordRankcheckId;
	private Integer engineId;
	private Integer languageId;
	private Integer locationId;
	private Integer rank;
	private String rankingDate;
	private String authorUniqueId;
	private String authorNickname;
	private String authorStatus;
	private String videoDesc;
	private String videoId;
	private Integer videoDuration;
	private String videoStats;
	private String videoUri;
	private Integer frequency;
	private Integer relId;
	private String device;
	
	public String getKeywordName() {
		return keywordName;
	}
	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}
	public Integer getOwnDomainId() {
		return ownDomainId;
	}
	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	public Integer getKeywordRankcheckId() {
		return keywordRankcheckId;
	}
	public void setKeywordRankcheckId(Integer keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}
	public Integer getEngineId() {
		return engineId;
	}
	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}
	public Integer getLanguageId() {
		return languageId;
	}
	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}
	public Integer getLocationId() {
		return locationId;
	}
	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}
	public String getRankingDate() {
		return rankingDate;
	}
	public void setRankingDate(String rankingDate) {
		this.rankingDate = rankingDate;
	}
	public String getAuthorUniqueId() {
		return authorUniqueId;
	}
	public void setAuthorUniqueId(String authorUniqueId) {
		this.authorUniqueId = authorUniqueId;
	}
	public String getAuthorNickname() {
		return authorNickname;
	}
	public void setAuthorNickname(String authorNickname) {
		this.authorNickname = authorNickname;
	}
	public String getAuthorStatus() {
		return authorStatus;
	}
	public void setAuthorStatus(String authorStatus) {
		this.authorStatus = authorStatus;
	}
	public String getVideoDesc() {
		return videoDesc;
	}
	public void setVideoDesc(String videoDesc) {
		this.videoDesc = videoDesc;
	}
	public String getVideoId() {
		return videoId;
	}
	public void setVideoId(String videoId) {
		this.videoId = videoId;
	}
	public Integer getVideoDuration() {
		return videoDuration;
	}
	public void setVideoDuration(Integer videoDuration) {
		this.videoDuration = videoDuration;
	}
	public String getVideoStats() {
		return videoStats;
	}
	public void setVideoStats(String videoStats) {
		this.videoStats = videoStats;
	}
	public String getVideoUri() {
		return videoUri;
	}
	public void setVideoUri(String videoUri) {
		this.videoUri = videoUri;
	}
	public Integer getFrequency() {
		return frequency;
	}
	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}
	public Integer getRank() {
		return rank;
	}
	public void setRank(Integer rank) {
		this.rank = rank;
	}
	public Integer getRelId() {
		return relId;
	}
	public void setRelId(Integer relId) {
		this.relId = relId;
	}
	public String getDevice() {
		return device;
	}
	public void setDevice(String device) {
		this.device = device;
	}
	

}
