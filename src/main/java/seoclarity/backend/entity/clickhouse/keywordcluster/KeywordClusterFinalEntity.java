package seoclarity.backend.entity.clickhouse.keywordcluster;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class KeywordClusterFinalEntity {
    private String rankingDate;
    private Integer ownDomainId;
    private Integer projectId;
    private List<String> keywordArray;
    private List<BigInteger> keywordHashArray;
    private Date createTime;
}
