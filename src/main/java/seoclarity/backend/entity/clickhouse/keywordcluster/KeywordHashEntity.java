package seoclarity.backend.entity.clickhouse.keywordcluster;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class KeywordHashEntity {
    private String rankingDate;
    private Integer ownDomainId;
    private Integer projectId;
    private String keywordName;
    private String keywordHash;
    private List<String> urlArray;
    private List<String> urlSHA512Hash;
    private Integer sign;
    private Date createTime;
}
