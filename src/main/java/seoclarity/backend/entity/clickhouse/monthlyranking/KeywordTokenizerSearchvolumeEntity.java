package seoclarity.backend.entity.clickhouse.monthlyranking;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-09-13
 *       seoclarity.backend.entity.clickhouse.monthlyranking.KeywordTokenizerEntity
 *       https://www.wrike.com/open.htm?id=270587561
 */
public class KeywordTokenizerSearchvolumeEntity {

	private String streamValue;
	private Long keywordRankcheckId;
	private String keywordName;
	private String decodedKeywordName;
	private List<String> word;
	private List<String> stream;
	private Integer locationId;
	private Integer engineId;
	private Integer languageId;
	private Integer sign = 1;

	private Integer versioning = 0;
	private int avgSearchVolume;
	private int monthlySearchVolume1;
	private int monthlySearchVolume2;
	private int monthlySearchVolume3;
	private int monthlySearchVolume4;
	private int monthlySearchVolume5;
	private int monthlySearchVolume6;
	private int monthlySearchVolume7;
	private int monthlySearchVolume8;
	private int monthlySearchVolume9;
	private int monthlySearchVolume10;
	private int monthlySearchVolume11;
	private int monthlySearchVolume12;
	private float cpc;

	private List<String> keywordVariationOneword;
	private List<String> keywordVariationNgram;
	
	private List<Integer> category;
	
	private List<Integer> monthlySvAttrKeys;
	private List<Integer> monthlySvAttrValues;

	private Boolean hasSv;

	public Long getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(Long keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public List<String> getWord() {
		return word;
	}

	public String[] getWordArray() {
		if (word == null || word.size() == 0) {
			return new String[] {};
		} else {
			return word.toArray(new String[word.size()]);
		}
	}

	public void setWord(List<String> word) {
		this.word = word;
	}

	public List<String> getStream() {
		return stream;
	}

	public String[] getStreamArray() {
		if (stream == null || stream.size() == 0) {
			return new String[] {};
		} else {
			return stream.toArray(new String[stream.size()]);
		}
	}

	public void setStream(List<String> stream) {
		this.stream = stream;
	}

	public Integer getLocationId() {
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}

	public Integer getEngineId() {
		return engineId;
	}

	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public Integer getSign() {
		return sign;
	}

	public void setSign(Integer sign) {
		this.sign = sign;
	}

	public Integer getVersioning() {
		return versioning;
	}

	public void setVersioning(Integer versioning) {
		this.versioning = versioning;
	}

	public Integer getAvgSearchVolume() {
		return avgSearchVolume;
	}

	public void setAvgSearchVolume(Integer avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume == null ? 0 : avgSearchVolume;
	}

	public Integer getMonthlySearchVolume1() {
		return monthlySearchVolume1;
	}

	public void setMonthlySearchVolume1(Integer monthlySearchVolume1) {
		this.monthlySearchVolume1 = monthlySearchVolume1 == null ? 0 : monthlySearchVolume1;
	}

	public Integer getMonthlySearchVolume2() {
		return monthlySearchVolume2;
	}

	public void setMonthlySearchVolume2(Integer monthlySearchVolume2) {
		this.monthlySearchVolume2 = monthlySearchVolume2 == null ? 0 : monthlySearchVolume2;
	}

	public Integer getMonthlySearchVolume3() {
		return monthlySearchVolume3;
	}

	public void setMonthlySearchVolume3(Integer monthlySearchVolume3) {
		this.monthlySearchVolume3 = monthlySearchVolume3 == null ? 0 : monthlySearchVolume3;
	}

	public Integer getMonthlySearchVolume4() {
		return monthlySearchVolume4;
	}

	public void setMonthlySearchVolume4(Integer monthlySearchVolume4) {
		this.monthlySearchVolume4 = monthlySearchVolume4 == null ? 0 : monthlySearchVolume4;
	}

	public Integer getMonthlySearchVolume5() {
		return monthlySearchVolume5;
	}

	public void setMonthlySearchVolume5(Integer monthlySearchVolume5) {
		this.monthlySearchVolume5 = monthlySearchVolume5 == null ? 0 : monthlySearchVolume5;
	}

	public Integer getMonthlySearchVolume6() {
		return monthlySearchVolume6;
	}

	public void setMonthlySearchVolume6(Integer monthlySearchVolume6) {
		this.monthlySearchVolume6 = monthlySearchVolume6 == null ? 0 : monthlySearchVolume6;
	}

	public Integer getMonthlySearchVolume7() {
		return monthlySearchVolume7;
	}

	public void setMonthlySearchVolume7(Integer monthlySearchVolume7) {
		this.monthlySearchVolume7 = monthlySearchVolume7 == null ? 0 : monthlySearchVolume7;
	}

	public Integer getMonthlySearchVolume8() {
		return monthlySearchVolume8;
	}

	public void setMonthlySearchVolume8(Integer monthlySearchVolume8) {
		this.monthlySearchVolume8 = monthlySearchVolume8 == null ? 0 : monthlySearchVolume8;
	}

	public Integer getMonthlySearchVolume9() {
		return monthlySearchVolume9;
	}

	public void setMonthlySearchVolume9(Integer monthlySearchVolume9) {
		this.monthlySearchVolume9 = monthlySearchVolume9 == null ? 0 : monthlySearchVolume9;
	}

	public Integer getMonthlySearchVolume10() {
		return monthlySearchVolume10;
	}

	public void setMonthlySearchVolume10(Integer monthlySearchVolume10) {
		this.monthlySearchVolume10 = monthlySearchVolume10 == null ? 0 : monthlySearchVolume10;
	}

	public Integer getMonthlySearchVolume11() {
		return monthlySearchVolume11;
	}

	public void setMonthlySearchVolume11(Integer monthlySearchVolume11) {
		this.monthlySearchVolume11 = monthlySearchVolume11 == null ? 0 : monthlySearchVolume11;
	}

	public Integer getMonthlySearchVolume12() {
		return monthlySearchVolume12;
	}

	public void setMonthlySearchVolume12(Integer monthlySearchVolume12) {
		this.monthlySearchVolume12 = monthlySearchVolume12 == null ? 0 : monthlySearchVolume12;
	}

	public Float getCpc() {
		return cpc;
	}

	public void setCpc(Float cpc) {
		this.cpc = cpc == null ? 0 : cpc;
	}

	public List<String> getKeywordVariationOneword() {
		return keywordVariationOneword;
	}

	public String[] getKeywordVariationOnewordArray() {
		if (keywordVariationOneword == null || keywordVariationOneword.size() == 0) {
			return new String[] {};
		} else {
			return keywordVariationOneword.toArray(new String[keywordVariationOneword.size()]);
		}
	}
	
	public void setKeywordVariationOneword(List<String> keywordVariationOneword) {
		this.keywordVariationOneword = keywordVariationOneword;
	}

	public List<String> getKeywordVariationNgram() {
		return keywordVariationNgram;
	}
	
	public String[] getKeywordVariationNgramArray() {
		if (keywordVariationNgram == null || keywordVariationNgram.size() == 0) {
			return new String[] {};
		} else {
			return keywordVariationNgram.toArray(new String[keywordVariationNgram.size()]);
		}
	}

	public void setKeywordVariationNgram(List<String> keywordVariationNgram) {
		this.keywordVariationNgram = keywordVariationNgram;
	}

	public List<Integer> getCategory() {
		return category;
	}
	
	public Integer[] getCategoryArray() {
		if (category == null || category.size() == 0) {
			return new Integer[] {};
		} else {
			return category.toArray(new Integer[category.size()]);
		}
	}

	public void setCategory(List<Integer> category) {
		this.category = category;
	}
	
	

	public Integer[] getMonthlySvAttrKeys() {
		if (monthlySvAttrKeys == null || monthlySvAttrKeys.size() == 0) {
			return new Integer[] {};
		}
		return monthlySvAttrKeys.toArray(new Integer[monthlySvAttrKeys.size()]);
	}

	public void setMonthlySvAttrKeys(List<Integer> monthlySvAttrKeys) {
		this.monthlySvAttrKeys = monthlySvAttrKeys;
	}

	public Integer[] getMonthlySvAttrValues() {
		if (monthlySvAttrValues == null || monthlySvAttrValues.size() == 0) {
			return new Integer[] {};
		}
		return monthlySvAttrValues.toArray(new Integer[monthlySvAttrValues.size()]);
	}

	public void setMonthlySvAttrValues(List<Integer> monthlySvAttrValues) {
		this.monthlySvAttrValues = monthlySvAttrValues;
	}

	@Override
	public String toString() {
		return "KeywordTokenizerSearchvolumeEntity{" +
				"keywordRankcheckId=" + keywordRankcheckId +
				", keywordName='" + keywordName + '\'' +
				", word=" + word +
				", stream=" + stream +
				", locationId=" + locationId +
				", engineId=" + engineId +
				", languageId=" + languageId +
				", sign=" + sign +
				", versioning=" + versioning +
				", avgSearchVolume=" + avgSearchVolume +
				", monthlySearchVolume1=" + monthlySearchVolume1 +
				", monthlySearchVolume2=" + monthlySearchVolume2 +
				", monthlySearchVolume3=" + monthlySearchVolume3 +
				", monthlySearchVolume4=" + monthlySearchVolume4 +
				", monthlySearchVolume5=" + monthlySearchVolume5 +
				", monthlySearchVolume6=" + monthlySearchVolume6 +
				", monthlySearchVolume7=" + monthlySearchVolume7 +
				", monthlySearchVolume8=" + monthlySearchVolume8 +
				", monthlySearchVolume9=" + monthlySearchVolume9 +
				", monthlySearchVolume10=" + monthlySearchVolume10 +
				", monthlySearchVolume11=" + monthlySearchVolume11 +
				", monthlySearchVolume12=" + monthlySearchVolume12 +
				", cpc=" + cpc +
				", keywordVariationOneword=" + keywordVariationOneword +
				", keywordVariationNgram=" + keywordVariationNgram +
				", category=" + category +
				'}';
	}

	public String getStreamValue() {
		return streamValue;
	}

	public void setStreamValue(String streamValue) {
		this.streamValue = streamValue;
	}

	public String getDecodedKeywordName() {
		return decodedKeywordName;
	}

	public void setDecodedKeywordName(String decodedKeywordName) {
		this.decodedKeywordName = decodedKeywordName;
	}

	public Boolean getHasSv() {
		if (hasSv == null) {
			hasSv = avgSearchVolume >= 0;
		}
		return hasSv;
	}

	public void setHasSv(Boolean hasSv) {
		this.hasSv = hasSv;
	}
}
