package seoclarity.backend.entity.clickhouse.monthlyranking;

import lombok.Getter;
import lombok.Setter;
import ru.yandex.clickhouse.ClickHouseArray;

import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-09-05 14:11
 **/
@Getter @Setter
public class MonthlyVideoSearchEntity {

    private Integer keywordRankcheckId;
    private String keywordName;
    private Integer ownDomainId;
    private String publisher;
    private Integer engineId;
    private Integer languageId;
    private Integer locationId;
    private Date rankingDate;
    private String domainReverse;
    private String rootDomainReverse;
    private Long avgSearchVolume;
    private Double cpc;
    private String uri;
    private String url;
    private Integer protocol;
    private Integer rank;
    private Integer subRank;
    private Integer hrd;
    private Integer hrrd;
    private Integer sign;
//    private ClickHouseArray arrayKey;
//    private ClickHouseArray arrayValues;
    private String[] arrayKey;
    private String[] arrayValues;
//    private List<String> arrayKey;
//    private List<String> arrayValues;

}
