package seoclarity.backend.entity.clickhouse;

import java.beans.Transient;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.util.Date;

import org.apache.commons.lang.StringEscapeUtils;

import seoclarity.backend.utils.FormatUtils;

public class KeywordStatGaEntity {
	public static final int DATA_FROM_DESKTOP = 1;
	public static final int DATA_FROM_MOBILE = 10;
	private Long id;
	private Integer entrances;
	private Date trackDate;
	private Date trafficDate;
	private Integer ownDomainId;
	private Integer sumEntrances;
	private Integer bingEntrances;
	private Integer pageviews;
	private Integer exits;
	private Integer bounces;
	private String medium;
	private String source;
	
	private String hostName;
	private Integer version = 1;
	private String crawlDate;
	private String sessionDuration;
	private String timeOnPage;
	
	private String keyword;

	private String targeturl;

	private Integer keywordCount;

	private Integer urlCount;

	private Integer startOfMonthDomainRank;

	private Integer endOfMonthDomainRank;

	private Integer estdSearchVolume;

	private Integer year;

	private Integer month;

	private Integer day;

	private Integer weekInYear;

	private String keywordmd5_32;
	private BigInteger keywordmd5_64;

	private String urlmd5_32;
	private BigInteger urlmd5_64;

	private Integer dataFrom;
	
	private String device;
	
	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public Integer getDataFrom() {
		return dataFrom;
	}

	public void setDataFrom(Integer dataFrom) {
		this.dataFrom = dataFrom;
	}

	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public Integer getDay() {
		return day;
	}

	public void setDay(Integer day) {
		this.day = day;
	}

	public Integer getWeekInYear() {
		return weekInYear;
	}

	public void setWeekInYear(Integer weekInYear) {
		this.weekInYear = weekInYear;
	}

	public String getKeywordmd5_32() {
		return keywordmd5_32;
	}

	public void setKeywordmd5_32(String keywordmd5_32) {
		this.keywordmd5_32 = keywordmd5_32;
	}

	public String getUrlmd5_32() {
		return urlmd5_32;
	}

	public void setUrlmd5_32(String urlmd5_32) {
		this.urlmd5_32 = urlmd5_32;
	}

	public BigInteger getKeywordmd5_64() {
		return keywordmd5_64;
	}

	public void setKeywordmd5_64(BigInteger keywordmd5_64) {
		this.keywordmd5_64 = keywordmd5_64;
	}

	public BigInteger getUrlmd5_64() {
		return urlmd5_64;
	}

	public void setUrlmd5_64(BigInteger urlmd5_64) {
		this.urlmd5_64 = urlmd5_64;
	}

	@Transient
	public Integer getStartOfMonthDomainRank() {
		return startOfMonthDomainRank;
	}

	public void setStartOfMonthDomainRank(Integer startOfMonthDomainRank) {
		this.startOfMonthDomainRank = startOfMonthDomainRank;
	}

	@Transient
	public Integer getEndOfMonthDomainRank() {
		return endOfMonthDomainRank;
	}

	public void setEndOfMonthDomainRank(Integer endOfMonthDomainRank) {
		this.endOfMonthDomainRank = endOfMonthDomainRank;
	}

	@Transient
	public Integer getEstdSearchVolume() {
		return estdSearchVolume;
	}

	public void setEstdSearchVolume(Integer estdSearchVolume) {
		this.estdSearchVolume = estdSearchVolume;
	}

	public String getCtrForEmailReport() {
		if (estdSearchVolume == null || estdSearchVolume.intValue() == 0) {
			return "-";
		}
		return FormatUtils.formatPercentNumber((float) sumEntrances / (double) estdSearchVolume);
	}

	public Integer getBingEntrances() {
		return bingEntrances;
	}

	public void setBingEntrances(Integer bingEntrances) {
		this.bingEntrances = bingEntrances;
	}

	public Integer getUrlCount() {
		return urlCount;
	}

	public void setUrlCount(Integer urlCount) {
		this.urlCount = urlCount;
	}

	public Integer getKeywordCount() {
		return keywordCount;
	}

	public void setKeywordCount(Integer keywordCount) {
		this.keywordCount = keywordCount;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getTargeturl() {
		return targeturl;
	}

	public void setTargeturl(String targeturl) {
		this.targeturl = targeturl;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getEntrances() {
		if (entrances == null) {
			return 0;
		}
		return entrances;
	}

	public void setEntrances(Integer entrances) {
		this.entrances = entrances;
	}

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public Date getTrafficDate() {
		return trafficDate;
	}

	public void setTrafficDate(Date trafficDate) {
		this.trafficDate = trafficDate;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getSumEntrances() {
		return sumEntrances;
	}

	public void setSumEntrances(Integer sumEntrances) {
		this.sumEntrances = sumEntrances;
	}

	public Integer getPageviews() {
		if (pageviews == null) {
			return 0;
		}
		return pageviews;
	}

	public void setPageviews(Integer pageviews) {
		this.pageviews = pageviews;
	}

	public Integer getExits() {
		if (exits == null) {
			return 0;
		}
		return exits;
	}

	public void setExits(Integer exits) {
		this.exits = exits;
	}

	public Integer getBounces() {
		if (bounces == null) {
			return 0;
		}
		return bounces;
	}

	public void setBounces(Integer bounces) {
		this.bounces = bounces;
	}

	public String getMedium() {
		return medium;
	}

	public void setMedium(String medium) {
		this.medium = medium;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getKeywordForHtml() {
		try {
			return StringEscapeUtils.escapeHtml(URLDecoder.decode(keyword, "UTF-8"));
		} catch (Exception e) {
			System.out.println(keyword);
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public String toString() {
		return "KeywordStatGaEntity [id=" + id + ", entrances=" + entrances + ", trackDate=" + FormatUtils.formatDate(trackDate) + ", trafficDate="
				+ FormatUtils.formatDate(trafficDate) + ", ownDomainId=" + ownDomainId + ", sumEntrances="
				+ sumEntrances + ", bingEntrances=" + bingEntrances + ", pageviews=" + pageviews + ", exits=" + exits
				+ ", bounces=" + bounces + ", medium=" + medium + ", source=" + source + ", keyword=" + keyword
				+ ", targeturl=" + targeturl + ", keywordCount=" + keywordCount + ", urlCount=" + urlCount + ", month="
				+ month + ", startOfMonthDomainRank=" + startOfMonthDomainRank + ", endOfMonthDomainRank="
				+ endOfMonthDomainRank + ", estdSearchVolume=" + estdSearchVolume + "]";
	}

	public String getHostName() {
		return hostName;
	}

	public void setHostName(String hostName) {
		this.hostName = hostName;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public String getCrawlDate() {
		return crawlDate;
	}

	public void setCrawlDate(String crawlDate) {
		this.crawlDate = crawlDate;
	}

	public String getSessionDuration() {
		return sessionDuration;
	}

	public void setSessionDuration(String sessionDuration) {
		this.sessionDuration = sessionDuration;
	}

	public String getTimeOnPage() {
		return timeOnPage;
	}

	public void setTimeOnPage(String timeOnPage) {
		this.timeOnPage = timeOnPage;
	}
}
