package seoclarity.backend.entity.clickhouse;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-09-21 16:05
 **/
@Getter
@Setter
public class PixelHeightEntityV3 {

	private Date rankingDate;
	private Integer firstPageSize;
	private Integer ownDomainId;
	private Integer engineId;
	private Long searchVol;
	private Integer languageId;
	private Integer locationId;
	private String rootDomainReverse;
	private String domainReverse;
	private Integer keywordRankcheckId;
	private String keywordName;

	// https://www.wrike.com/open.htm?id=957695729
	private Integer webRank;
	private Integer visualRank;
	private Integer trueRank;

	private String label;
	private String url;
	private Integer height;
	private Integer fold;
	private Integer foldRank;
	private Integer offsetBottom;
	private Integer offsetTop;
	private Integer type;
	private Integer hrd;
	private Integer hrrd;
	private String device;
	private Date createDate;
	private String vedRawJson;
	private String vedAttName;
	private String vedAttValue;
	private List<String> vedTypeArray;
	private Integer orgFps;
	private Integer pixelFps;

	private Integer appbarTop;
	private Integer totalOffsetTop;

	public Date getRankingDate() {
		return rankingDate;
	}

	public void setRankingDate(Date rankingDate) {
		this.rankingDate = rankingDate;
	}

	public Integer getFirstPageSize() {
		return firstPageSize;
	}

	public void setFirstPageSize(Integer firstPageSize) {
		this.firstPageSize = firstPageSize;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getEngineId() {
		return engineId;
	}

	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}

	public Long getSearchVol() {
		return searchVol;
	}

	public void setSearchVol(Long searchVol) {
		this.searchVol = searchVol;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public Integer getLocationId() {
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}

	public String getRootDomainReverse() {
		return rootDomainReverse;
	}

	public void setRootDomainReverse(String rootDomainReverse) {
		this.rootDomainReverse = rootDomainReverse;
	}

	public String getDomainReverse() {
		return domainReverse;
	}

	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}

	public Integer getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(Integer keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getHeight() {
		return height;
	}

	public void setHeight(Integer height) {
		this.height = height;
	}

	public Integer getFold() {
		return fold;
	}

	public void setFold(Integer fold) {
		this.fold = fold;
	}

	public Integer getFoldRank() {
		return foldRank;
	}

	public void setFoldRank(Integer foldRank) {
		this.foldRank = foldRank;
	}

	public Integer getOffsetBottom() {
		return offsetBottom;
	}

	public void setOffsetBottom(Integer offsetBottom) {
		this.offsetBottom = offsetBottom;
	}

	public Integer getOffsetTop() {
		return offsetTop;
	}

	public void setOffsetTop(Integer offsetTop) {
		this.offsetTop = offsetTop;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getHrd() {
		return hrd;
	}

	public void setHrd(Integer hrd) {
		this.hrd = hrd;
	}

	public Integer getHrrd() {
		return hrrd;
	}

	public void setHrrd(Integer hrrd) {
		this.hrrd = hrrd;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getVedRawJson() {
		return vedRawJson;
	}

	public void setVedRawJson(String vedRawJson) {
		this.vedRawJson = vedRawJson;
	}

	public String getVedAttName() {
		return vedAttName;
	}

	public void setVedAttName(String vedAttName) {
		this.vedAttName = vedAttName;
	}

	public String getVedAttValue() {
		return vedAttValue;
	}

	public void setVedAttValue(String vedAttValue) {
		this.vedAttValue = vedAttValue;
	}

	public List<String> getVedTypeArray() {
		return vedTypeArray;
	}

	public void setVedTypeArray(List<String> vedTypeArray) {
		this.vedTypeArray = vedTypeArray;
	}

	public Integer getOrgFps() {
		return orgFps;
	}

	public void setOrgFps(Integer orgFps) {
		this.orgFps = orgFps;
	}

	public Integer getPixelFps() {
		return pixelFps;
	}

	public void setPixelFps(Integer pixelFps) {
		this.pixelFps = pixelFps;
	}

	public Integer getAppbarTop() {
		return appbarTop;
	}

	public void setAppbarTop(Integer appbarTop) {
		this.appbarTop = appbarTop;
	}

	public Integer getTotalOffsetTop() {
		return totalOffsetTop;
	}

	public void setTotalOffsetTop(Integer totalOffsetTop) {
		this.totalOffsetTop = totalOffsetTop;
	}

	public Integer getWebRank() {
		return webRank;
	}

	public void setWebRank(Integer webRank) {
		this.webRank = webRank;
	}

	public Integer getVisualRank() {
		return visualRank;
	}

	public void setVisualRank(Integer visualRank) {
		this.visualRank = visualRank;
	}

	public Integer getTrueRank() {
		return trueRank;
	}

	public void setTrueRank(Integer trueRank) {
		this.trueRank = trueRank;
	}

}