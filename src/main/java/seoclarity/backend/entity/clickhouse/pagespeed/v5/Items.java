package seoclarity.backend.entity.clickhouse.pagespeed.v5;


public class Items {

    private Integer totalBytes;
    private Double wastedMs;
    private String url;

    private Integer transferSize;
    private Double blockingTime;
    private Double mainThreadTime;
//    private ItemEntity entity;

    private Double cacheLifetimeMs;

    private Double wastedPercent;
    private Double wastedBytes;
    private Double cacheHitProbability;

//    private String value;
    private String statistic;
    private Element element;

    private String group;
    private Double duration;
    private String groupLabel;

    private Integer requestCount;
    private String resourceType;
    private String label;
    private String size;

    private Integer statusCode;

    public Integer getTotalBytes() {
        return totalBytes;
    }

    public void setTotalBytes(Integer totalBytes) {
        this.totalBytes = totalBytes;
    }

    public Double getWastedMs() {
        return wastedMs;
    }

    public void setWastedMs(Double wastedMs) {
        this.wastedMs = wastedMs;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getTransferSize() {
        return transferSize;
    }

    public void setTransferSize(Integer transferSize) {
        this.transferSize = transferSize;
    }

    public Double getBlockingTime() {
        return blockingTime;
    }

    public void setBlockingTime(Double blockingTime) {
        this.blockingTime = blockingTime;
    }

    public Double getMainThreadTime() {
        return mainThreadTime;
    }

    public void setMainThreadTime(Double mainThreadTime) {
        this.mainThreadTime = mainThreadTime;
    }

//    public ItemEntity getEntity() {
//        return entity;
//    }
//
//    public void setEntity(ItemEntity entity) {
//        this.entity = entity;
//    }

    public Double getWastedPercent() {
        return wastedPercent;
    }

    public void setWastedPercent(Double wastedPercent) {
        this.wastedPercent = wastedPercent;
    }

    public Double getCacheLifetimeMs() {
        return cacheLifetimeMs;
    }

    public void setCacheLifetimeMs(Double cacheLifetimeMs) {
        this.cacheLifetimeMs = cacheLifetimeMs;
    }

    public Double getWastedBytes() {
        return wastedBytes;
    }

    public void setWastedBytes(Double wastedBytes) {
        this.wastedBytes = wastedBytes;
    }

    public Double getCacheHitProbability() {
        return cacheHitProbability;
    }

    public void setCacheHitProbability(Double cacheHitProbability) {
        this.cacheHitProbability = cacheHitProbability;
    }

//    public String getValue() {
//        return value;
//    }
//
//    public void setValue(String value) {
//        this.value = value;
//    }

    public String getStatistic() {
        return statistic;
    }

    public void setStatistic(String statistic) {
        this.statistic = statistic;
    }

    public Element getElement() {
        return element;
    }

    public void setElement(Element element) {
        this.element = element;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public String getGroupLabel() {
        return groupLabel;
    }

    public void setGroupLabel(String groupLabel) {
        this.groupLabel = groupLabel;
    }

    public Integer getRequestCount() {
        return requestCount;
    }

    public void setRequestCount(Integer requestCount) {
        this.requestCount = requestCount;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }
}
