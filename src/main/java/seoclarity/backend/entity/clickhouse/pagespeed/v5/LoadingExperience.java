package seoclarity.backend.entity.clickhouse.pagespeed.v5;

public class LoadingExperience {

    private String id;

    private String overall_category;

    private String initial_url;

    private Metrics metrics;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOverall_category() {
        return overall_category;
    }

    public void setOverall_category(String overall_category) {
        this.overall_category = overall_category;
    }

    public String getInitial_url() {
        return initial_url;
    }

    public void setInitial_url(String initial_url) {
        this.initial_url = initial_url;
    }

    public Metrics getMetrics() {
        return metrics;
    }

    public void setMetrics(Metrics metrics) {
        this.metrics = metrics;
    }
}
