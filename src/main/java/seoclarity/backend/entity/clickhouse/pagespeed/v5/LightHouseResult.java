package seoclarity.backend.entity.clickhouse.pagespeed.v5;


import com.google.gson.Gson;

import java.util.Map;

public class LightHouseResult {

    private Gson gson = new Gson();

    private String requestedUrl;
    private String finalUrl;
    private String lighthouseVersion;
    private String userAgent;
    private String fetchTime;
    private Categories categories;

    private Map<String, Object> audits;

    private Audits auditsObjects;

    public String getRequestedUrl() {
        return requestedUrl;
    }

    public void setRequestedUrl(String requestedUrl) {
        this.requestedUrl = requestedUrl;
    }

    public String getFinalUrl() {
        return finalUrl;
    }

    public void setFinalUrl(String finalUrl) {
        this.finalUrl = finalUrl;
    }

    public String getLighthouseVersion() {
        return lighthouseVersion;
    }

    public void setLighthouseVersion(String lighthouseVersion) {
        this.lighthouseVersion = lighthouseVersion;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getFetchTime() {
        return fetchTime;
    }

    public void setFetchTime(String fetchTime) {
        this.fetchTime = fetchTime;
    }

    public Map<String, Object> getAudits() {
        return audits;
    }

    public void setAudits(Map<String, Object> audits) {
        this.audits = audits;
    }

    public Audits getAuditsObjects() {
        Audits audits = new Audits();
        audits.setSpeedIndex(getSpeedIndex());
        audits.setEstimatedInputLatency(getEstimatedInputLatency());
        audits.setMainthreadWorkBreakdown(getMainthreadWorkBreakdown());
        audits.setUsesOptimizedImages(getUsesOptimizedImages());
        audits.setInteractive(getInteractive());
        audits.setUsesRelPreconnect(getUsesRelPreconnect());
        audits.setUsesRelPreload(getUsesRelPreload());
        audits.setMaxPotentialFid(getMaxPotentialFid());
        audits.setOffscreenImages(getOffscreenImages());
        audits.setUsesLongCacheTtl(getUsesLongCacheTtl());
        audits.setFirstCpuIdle(getFirstCpuIdle());
        audits.setUsesWebpImages(getUsesWebpImages());
        audits.setDomSize(getDomSize());
        audits.setFirstMeaningfulPaint(getFirstMeaningfulPaint());
        audits.setFirstContentfulPaint(getFirstContentfulPaint());
        audits.setCumulativeLayoutShift(getCumulativeLayoutShift());
        audits.setRedirects(getRedirects());
        audits.setUsesResponsiveImages(getUsesResponsiveImages());
        audits.setUnusedCssRules(getUnusedCssRules());
        audits.setTotalBlockingTime(getTotalBlockingTime());
        audits.setTotalByteWeight(getTotalByteWeight());
        audits.setUnminifiedCss(getUnminifiedCss());
        audits.setRenderBlockingResources(getRenderBlockingResources());
        audits.setUnusedJavascript(getUnusedJavascript());
        audits.setLargestContentfulPaint(getLargestContentfulPaint());
        audits.setUnminifiedJavascript(getUnminifiedJavascript());
        audits.setUsesTextCompression(getUsesTextCompression());
        audits.setBootupTime(getBootupTime());
        audits.setEfficientAnimatedContent(getEfficientAnimatedContent());
        audits.setFontDisplay(getFontDisplay());
        audits.setServerResponseTime(getServerResponseTime());
        return audits;
    }


    public AuditsCommonProperty getSpeedIndex() {
        return new Gson().fromJson(new Gson().toJson(audits.get("speed-index")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getEstimatedInputLatency() {
        return new Gson().fromJson(new Gson().toJson(audits.get("estimated-input-latency")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getMainthreadWorkBreakdown() {
        return new Gson().fromJson(new Gson().toJson(audits.get("mainthread-work-breakdown")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUsesOptimizedImages() {
        return new Gson().fromJson(new Gson().toJson(audits.get("uses-optimized-images")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getInteractive() {
        return new Gson().fromJson(new Gson().toJson(audits.get("interactive")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUsesRelPreconnect() {
        return new Gson().fromJson(new Gson().toJson(audits.get("uses-rel-preconnect")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUsesRelPreload() {
        return new Gson().fromJson(new Gson().toJson(audits.get("uses-rel-preload")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getMaxPotentialFid() {
        return new Gson().fromJson(new Gson().toJson(audits.get("max-potential-fid")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getOffscreenImages() {
        return new Gson().fromJson(new Gson().toJson(audits.get("offscreen-images")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUsesLongCacheTtl() {
        return new Gson().fromJson(new Gson().toJson(audits.get("uses-long-cache-ttl")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getFirstCpuIdle() {
        return new Gson().fromJson(new Gson().toJson(audits.get("first-cpu-idle")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUsesWebpImages() {
        return new Gson().fromJson(new Gson().toJson(audits.get("uses-webp-images")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getDomSize() {
        return new Gson().fromJson(new Gson().toJson(audits.get("dom-size")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getFirstMeaningfulPaint() {
        return new Gson().fromJson(new Gson().toJson(audits.get("first-meaningful-paint")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getFirstContentfulPaint() {
        return new Gson().fromJson(new Gson().toJson(audits.get("first-contentful-paint")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getCumulativeLayoutShift() {
        return new Gson().fromJson(new Gson().toJson(audits.get("cumulative-layout-shift")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getRedirects() {
        return new Gson().fromJson(new Gson().toJson(audits.get("redirects")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUsesResponsiveImages() {
        return new Gson().fromJson(new Gson().toJson(audits.get("uses-responsive-images")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUnusedCssRules() {
        return new Gson().fromJson(new Gson().toJson(audits.get("unused-css-rules")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getTotalBlockingTime() {
        return new Gson().fromJson(new Gson().toJson(audits.get("total-blocking-time")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getTotalByteWeight() {
        return new Gson().fromJson(new Gson().toJson(audits.get("total-byte-weight")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUnminifiedCss() {
        return new Gson().fromJson(new Gson().toJson(audits.get("unminified-css")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getRenderBlockingResources() {
        return new Gson().fromJson(new Gson().toJson(audits.get("render-blocking-resources")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUnusedJavascript() {
        return new Gson().fromJson(new Gson().toJson(audits.get("unused-javascript")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getLargestContentfulPaint() {
        return new Gson().fromJson(new Gson().toJson(audits.get("largest-contentful-paint")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUnminifiedJavascript() {
        return new Gson().fromJson(new Gson().toJson(audits.get("unminified-javascript")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getUsesTextCompression() {
        return new Gson().fromJson(new Gson().toJson(audits.get("uses-text-compression")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getBootupTime() {
        return new Gson().fromJson(new Gson().toJson(audits.get("bootup-time")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getEfficientAnimatedContent() {
        return new Gson().fromJson(new Gson().toJson(audits.get("efficient-animated-content")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getFontDisplay() {
        return new Gson().fromJson(new Gson().toJson(audits.get("font-display")), AuditsCommonProperty.class);
    }

    public AuditsCommonProperty getServerResponseTime() {
        return new Gson().fromJson(new Gson().toJson(audits.get("server-response-time")), AuditsCommonProperty.class);
    }

    public String getSpeedIndexJson() {
        return new Gson().toJson(audits.get("speed-index"));
    }

    public String getEstimatedInputLatencyJson() {
        return new Gson().toJson(audits.get("estimated-input-latency"));
    }

    public String getMainthreadWorkBreakdownJson() {
        return new Gson().toJson(audits.get("mainthread-work-breakdown"));
    }

    public String getUsesOptimizedImagesJson() {
        return new Gson().toJson(audits.get("uses-optimized-images"));
    }

    public String getInteractiveJson() {
        return new Gson().toJson(audits.get("interactive"));
    }

    public String getUsesRelPreconnectJson() {
        return new Gson().toJson(audits.get("uses-rel-preconnect"));
    }

    public String getUsesRelPreloadJson() {
        return new Gson().toJson(audits.get("uses-rel-preload"));
    }

    public String getMaxPotentialFidJson() {
        return new Gson().toJson(audits.get("max-potential-fid"));
    }

    public String getOffscreenImagesJson() {
        return new Gson().toJson(audits.get("offscreen-images"));
    }

    public String getUsesLongCacheTtlJson() {
        return new Gson().toJson(audits.get("uses-long-cache-ttl"));
    }

    public String getFirstCpuIdleJson() {
        return new Gson().toJson(audits.get("first-cpu-idle"));
    }

    public String getUsesWebpImagesJson() {
        return new Gson().toJson(audits.get("uses-webp-images"));
    }

    public String getDomSizeJson() {
        return new Gson().toJson(audits.get("dom-size"));
    }

    public String getFirstMeaningfulPaintJson() {
        return new Gson().toJson(audits.get("first-meaningful-paint"));
    }

    public String getFirstContentfulPaintJson() {
        return new Gson().toJson(audits.get("first-contentful-paint"));
    }

    public String getCumulativeLayoutShiftJson() {
        return new Gson().toJson(audits.get("cumulative-layout-shift"));
    }

    public String getRedirectsJson() {
        return new Gson().toJson(audits.get("redirects"));
    }

    public String getUsesResponsiveImagesJson() {
        return new Gson().toJson(audits.get("uses-responsive-images"));
    }

    public String getUnusedCssRulesJson() {
        return new Gson().toJson(audits.get("unused-css-rules"));
    }

    public String getTotalBlockingTimeJson() {
        return new Gson().toJson(audits.get("total-blocking-time"));
    }

    public String getTotalByteWeightJson() {
        return new Gson().toJson(audits.get("total-byte-weight"));
    }

    public String getUnminifiedCssJson() {
        return new Gson().toJson(audits.get("unminified-css"));
    }

    public String getRenderBlockingResourcesJson() {
        return new Gson().toJson(audits.get("render-blocking-resources"));
    }

    public String getUnusedJavascriptJson() {
        return new Gson().toJson(audits.get("unused-javascript"));
    }

    public String getLargestContentfulPaintJson() {
        return new Gson().toJson(audits.get("largest-contentful-paint"));
    }

    public String getUnminifiedJavascriptJson() {
        return new Gson().toJson(audits.get("unminified-javascript"));
    }

    public String getUsesTextCompressionJson() {
        return new Gson().toJson(audits.get("uses-text-compression"));
    }

    public String getBootupTimeJson() {
        return new Gson().toJson(audits.get("bootup-time"));
    }

    public String getFontDisplayJson() {
        return new Gson().toJson(audits.get("font-display"));
    }

    public String getServerResponseTimeJson() {
        return new Gson().toJson(audits.get("server-response-time"));
    }

    public String getEfficientAnimatedContentJson() {
        return new Gson().toJson(audits.get("efficient-animated-content"));
    }

    public Integer getResponseCode() {
        Integer responseCode = null;
        Items[] items = this.getAuditsObjects().getNetworkRequests().getDetails().getItems();
        if (items == null) {
            return responseCode;
        } else {
            for (int i = 0; i < items.length; i++) {
                if (items[i].getResourceType() != null && items[i].getResourceType().equalsIgnoreCase("Document")) {
                    responseCode = items[i].getStatusCode();
                    return responseCode;
                }
            }
        }
        return responseCode;
    }

    public Categories getCategories() {
        return categories;
    }

    public void setCategories(Categories categories) {
        this.categories = categories;
    }
}
