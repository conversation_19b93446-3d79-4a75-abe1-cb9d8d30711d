package seoclarity.backend.entity.clickhouse.pagespeed.v5;

public class Audits {

    private AuditsCommonProperty firstContentfulPaint;
    private AuditsCommonProperty speedIndex;
    private AuditsCommonProperty interactive;
    private AuditsCommonProperty firstMeaningfulPaint;
    private AuditsCommonProperty firstCpuIdle;
    private AuditsCommonProperty maxPotentialFid;
    private AuditsCommonProperty renderBlockingResources;
    private AuditsCommonProperty unusedCssRules;
    private AuditsCommonProperty thirdPartySummary;
    private AuditsCommonProperty usesLongCacheTtl;
    private AuditsCommonProperty domSize;
    private AuditsCommonProperty mainthreadWorkBreakdown;
    private AuditsCommonProperty criticalRequestChains;
    private AuditsCommonProperty resourceSummary;
    private AuditsCommonProperty networkRequests;
    private AuditsCommonProperty estimatedInputLatency;
    private AuditsCommonProperty totalBlockingTime;
    private AuditsCommonProperty usesResponsiveImages;

    private AuditsCommonProperty usesOptimizedImages;
    private AuditsCommonProperty usesRelPreconnect;
    private AuditsCommonProperty usesRelPreload;
    private AuditsCommonProperty offscreenImages;
    private AuditsCommonProperty usesWebpImages;
    private AuditsCommonProperty cumulativeLayoutShift;
    private AuditsCommonProperty redirects;
    private AuditsCommonProperty totalByteWeight;
    private AuditsCommonProperty unminifiedCss;
    private AuditsCommonProperty unusedJavascript;
    private AuditsCommonProperty largestContentfulPaint;
    private AuditsCommonProperty unminifiedJavascript;
    private AuditsCommonProperty usesTextCompression;
    private AuditsCommonProperty bootupTime;
    private AuditsCommonProperty efficientAnimatedContent;

    private AuditsCommonProperty serverResponseTime;
    private AuditsCommonProperty fontDisplay;

//    private AuditsCommonProperty unminified_css_json;
//    private AuditsCommonProperty unminified_javascript_json;
//    private AuditsCommonProperty uses_optimized_images_json;
//    private AuditsCommonProperty efficient_animated_content_json;
//    private AuditsCommonProperty uses_webp_images_json;
//    private AuditsCommonProperty uses_text_compression_json;
//    private AuditsCommonProperty uses_rel_preconnect_json;
//    private AuditsCommonProperty time_to_first_byte_json;
//    private AuditsCommonProperty uses_rel_preload_json;


    private Categories categories;

    public AuditsCommonProperty getFirstContentfulPaint() {
        return firstContentfulPaint;
    }

    public void setFirstContentfulPaint(AuditsCommonProperty firstContentfulPaint) {
        this.firstContentfulPaint = firstContentfulPaint;
    }

    public AuditsCommonProperty getSpeedIndex() {
        return speedIndex;
    }

    public void setSpeedIndex(AuditsCommonProperty speedIndex) {
        this.speedIndex = speedIndex;
    }

    public AuditsCommonProperty getInteractive() {
        return interactive;
    }

    public void setInteractive(AuditsCommonProperty interactive) {
        this.interactive = interactive;
    }

    public AuditsCommonProperty getFirstMeaningfulPaint() {
        return firstMeaningfulPaint;
    }

    public void setFirstMeaningfulPaint(AuditsCommonProperty firstMeaningfulPaint) {
        this.firstMeaningfulPaint = firstMeaningfulPaint;
    }

    public AuditsCommonProperty getFirstCpuIdle() {
        return firstCpuIdle;
    }

    public void setFirstCpuIdle(AuditsCommonProperty firstCpuIdle) {
        this.firstCpuIdle = firstCpuIdle;
    }

    public AuditsCommonProperty getMaxPotentialFid() {
        return maxPotentialFid;
    }

    public void setMaxPotentialFid(AuditsCommonProperty maxPotentialFid) {
        this.maxPotentialFid = maxPotentialFid;
    }

    public Categories getCategories() {
        return categories;
    }

    public void setCategories(Categories categories) {
        this.categories = categories;
    }

    public AuditsCommonProperty getRenderBlockingResources() {
        return renderBlockingResources;
    }

    public void setRenderBlockingResources(AuditsCommonProperty renderBlockingResources) {
        this.renderBlockingResources = renderBlockingResources;
    }

    public AuditsCommonProperty getUnusedCssRules() {
        return unusedCssRules;
    }

    public void setUnusedCssRules(AuditsCommonProperty unusedCssRules) {
        this.unusedCssRules = unusedCssRules;
    }

    public AuditsCommonProperty getThirdPartySummary() {
        return thirdPartySummary;
    }

    public void setThirdPartySummary(AuditsCommonProperty thirdPartySummary) {
        this.thirdPartySummary = thirdPartySummary;
    }

    public AuditsCommonProperty getUsesLongCacheTtl() {
        return usesLongCacheTtl;
    }

    public void setUsesLongCacheTtl(AuditsCommonProperty usesLongCacheTtl) {
        this.usesLongCacheTtl = usesLongCacheTtl;
    }

    public AuditsCommonProperty getDomSize() {
        return domSize;
    }

    public void setDomSize(AuditsCommonProperty domSize) {
        this.domSize = domSize;
    }

    public AuditsCommonProperty getMainthreadWorkBreakdown() {
        return mainthreadWorkBreakdown;
    }

    public void setMainthreadWorkBreakdown(AuditsCommonProperty mainthreadWorkBreakdown) {
        this.mainthreadWorkBreakdown = mainthreadWorkBreakdown;
    }

    public AuditsCommonProperty getCriticalRequestChains() {
        return criticalRequestChains;
    }

    public void setCriticalRequestChains(AuditsCommonProperty criticalRequestChains) {
        this.criticalRequestChains = criticalRequestChains;
    }

    public AuditsCommonProperty getResourceSummary() {
        return resourceSummary;
    }

    public void setResourceSummary(AuditsCommonProperty resourceSummary) {
        this.resourceSummary = resourceSummary;
    }

    public AuditsCommonProperty getNetworkRequests() {
        return networkRequests;
    }

    public void setNetworkRequests(AuditsCommonProperty networkRequests) {
        this.networkRequests = networkRequests;
    }

    public AuditsCommonProperty getEstimatedInputLatency() {
        return estimatedInputLatency;
    }

    public void setEstimatedInputLatency(AuditsCommonProperty estimatedInputLatency) {
        this.estimatedInputLatency = estimatedInputLatency;
    }

    public AuditsCommonProperty getTotalBlockingTime() {
        return totalBlockingTime;
    }

    public void setTotalBlockingTime(AuditsCommonProperty totalBlockingTime) {
        this.totalBlockingTime = totalBlockingTime;
    }

    public AuditsCommonProperty getUsesResponsiveImages() {
        return usesResponsiveImages;
    }

    public void setUsesResponsiveImages(AuditsCommonProperty usesResponsiveImages) {
        this.usesResponsiveImages = usesResponsiveImages;
    }

    public AuditsCommonProperty getUsesOptimizedImages() {
        return usesOptimizedImages;
    }

    public void setUsesOptimizedImages(AuditsCommonProperty usesOptimizedImages) {
        this.usesOptimizedImages = usesOptimizedImages;
    }

    public AuditsCommonProperty getUsesRelPreconnect() {
        return usesRelPreconnect;
    }

    public void setUsesRelPreconnect(AuditsCommonProperty usesRelPreconnect) {
        this.usesRelPreconnect = usesRelPreconnect;
    }

    public AuditsCommonProperty getUsesRelPreload() {
        return usesRelPreload;
    }

    public void setUsesRelPreload(AuditsCommonProperty usesRelPreload) {
        this.usesRelPreload = usesRelPreload;
    }

    public AuditsCommonProperty getOffscreenImages() {
        return offscreenImages;
    }

    public void setOffscreenImages(AuditsCommonProperty offscreenImages) {
        this.offscreenImages = offscreenImages;
    }

    public AuditsCommonProperty getUsesWebpImages() {
        return usesWebpImages;
    }

    public void setUsesWebpImages(AuditsCommonProperty usesWebpImages) {
        this.usesWebpImages = usesWebpImages;
    }

    public AuditsCommonProperty getCumulativeLayoutShift() {
        return cumulativeLayoutShift;
    }

    public void setCumulativeLayoutShift(AuditsCommonProperty cumulativeLayoutShift) {
        this.cumulativeLayoutShift = cumulativeLayoutShift;
    }

    public AuditsCommonProperty getRedirects() {
        return redirects;
    }

    public void setRedirects(AuditsCommonProperty redirects) {
        this.redirects = redirects;
    }

    public AuditsCommonProperty getTotalByteWeight() {
        return totalByteWeight;
    }

    public void setTotalByteWeight(AuditsCommonProperty totalByteWeight) {
        this.totalByteWeight = totalByteWeight;
    }

    public AuditsCommonProperty getUnminifiedCss() {
        return unminifiedCss;
    }

    public void setUnminifiedCss(AuditsCommonProperty unminifiedCss) {
        this.unminifiedCss = unminifiedCss;
    }

    public AuditsCommonProperty getUnusedJavascript() {
        return unusedJavascript;
    }

    public void setUnusedJavascript(AuditsCommonProperty unusedJavascript) {
        this.unusedJavascript = unusedJavascript;
    }

    public AuditsCommonProperty getLargestContentfulPaint() {
        return largestContentfulPaint;
    }

    public void setLargestContentfulPaint(AuditsCommonProperty largestContentfulPaint) {
        this.largestContentfulPaint = largestContentfulPaint;
    }

    public AuditsCommonProperty getUnminifiedJavascript() {
        return unminifiedJavascript;
    }

    public void setUnminifiedJavascript(AuditsCommonProperty unminifiedJavascript) {
        this.unminifiedJavascript = unminifiedJavascript;
    }

    public AuditsCommonProperty getUsesTextCompression() {
        return usesTextCompression;
    }

    public void setUsesTextCompression(AuditsCommonProperty usesTextCompression) {
        this.usesTextCompression = usesTextCompression;
    }

    public AuditsCommonProperty getBootupTime() {
        return bootupTime;
    }

    public void setBootupTime(AuditsCommonProperty bootupTime) {
        this.bootupTime = bootupTime;
    }

    public AuditsCommonProperty getEfficientAnimatedContent() {
        return efficientAnimatedContent;
    }

    public void setEfficientAnimatedContent(AuditsCommonProperty efficientAnimatedContent) {
        this.efficientAnimatedContent = efficientAnimatedContent;
    }

    public AuditsCommonProperty getServerResponseTime() {
        return serverResponseTime;
    }

    public void setServerResponseTime(AuditsCommonProperty serverResponseTime) {
        this.serverResponseTime = serverResponseTime;
    }

    public AuditsCommonProperty getFontDisplay() {
        return fontDisplay;
    }

    public void setFontDisplay(AuditsCommonProperty fontDisplay) {
        this.fontDisplay = fontDisplay;
    }
}
