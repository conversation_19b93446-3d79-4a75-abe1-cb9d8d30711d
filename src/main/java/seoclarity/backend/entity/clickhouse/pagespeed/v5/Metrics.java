package seoclarity.backend.entity.clickhouse.pagespeed.v5;

public class Metrics {

    private FirstContentFulPaintMs FIRST_CONTENTFUL_PAINT_MS;

    private FirstInputDelayMs FIRST_INPUT_DELAY_MS;

    public FirstContentFulPaintMs getFIRST_CONTENTFUL_PAINT_MS() {
        return FIRST_CONTENTFUL_PAINT_MS;
    }

    public void setFIRST_CONTENTFUL_PAINT_MS(FirstContentFulPaintMs FIRST_CONTENTFUL_PAINT_MS) {
        this.FIRST_CONTENTFUL_PAINT_MS = FIRST_CONTENTFUL_PAINT_MS;
    }

    public FirstInputDelayMs getFIRST_INPUT_DELAY_MS() {
        return FIRST_INPUT_DELAY_MS;
    }

    public void setFIRST_INPUT_DELAY_MS(FirstInputDelayMs FIRST_INPUT_DELAY_MS) {
        this.FIRST_INPUT_DELAY_MS = FIRST_INPUT_DELAY_MS;
    }
}
