package seoclarity.backend.entity.clickhouse.pagespeed.v5;

public class AuditsCommonProperty {

    private String id;
    private String title;
    private String description;
    private Float score;
    private String scoreDisplayMode;
    private String displayValue;
    private Double numericValue;

    private Details details;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getScoreDisplayMode() {
        return scoreDisplayMode;
    }

    public void setScoreDisplayMode(String scoreDisplayMode) {
        this.scoreDisplayMode = scoreDisplayMode;
    }

    public String getDisplayValue() {
        return displayValue;
    }

    public void setDisplayValue(String displayValue) {
        this.displayValue = displayValue;
    }

    public Details getDetails() {
        return details;
    }

    public void setDetails(Details details) {
        this.details = details;
    }

    public Float getScore() {
        return score;
    }

    public void setScore(Float score) {
        this.score = score;
    }

    public Double getNumericValue() {
        return numericValue;
    }

    public void setNumericValue(Double numericValue) {
        this.numericValue = numericValue;
    }
}
