package seoclarity.backend.entity.clickhouse.pagespeed.v5;


import java.util.Map;

public class PageSpeedV5ResponseEntity {

    private String id;

    private Map<Object,Object> loadingExperience;

    private Map<Object,Object> originLoadingExperience;

    private LightHouseResult lighthouseResult;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Map<Object, Object> getLoadingExperience() {
        return loadingExperience;
    }

    public void setLoadingExperience(Map<Object, Object> loadingExperience) {
        this.loadingExperience = loadingExperience;
    }

    public Map<Object, Object> getOriginLoadingExperience() {
        return originLoadingExperience;
    }

    public void setOriginLoadingExperience(Map<Object, Object> originLoadingExperience) {
        this.originLoadingExperience = originLoadingExperience;
    }

    public LightHouseResult getLighthouseResult() {
        return lighthouseResult;
    }

    public void setLighthouseResult(LightHouseResult lighthouseResult) {
        this.lighthouseResult = lighthouseResult;
    }
}
