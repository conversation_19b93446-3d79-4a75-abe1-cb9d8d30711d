package seoclarity.backend.entity.clickhouse.pagespeed.v5;

import com.google.gson.Gson;

public class Test {

    public static void main(String[] args){

        String json = "{\n" +
                "\"captchaResult\": \"CAPTCHA_NOT_NEEDED\",\n" +
                "\"kind\": \"pagespeedonline#result\",\n" +
                "\"id\": \"https://www.seoclarity.net/\",\n" +
                "\"lighthouseResult\": {\n" +
                "\"audits\": {\n" +
                "\"time-to-first-byte\": {\n" +
                "\"id\": \"time-to-first-byte\",\n" +
                "\"title\": \"Server response times are low (TTFB)\",\n" +
                "\"description\": \"Time To First Byte identifies the time at which your server sends a response. [Learn more](https://web.dev/time-to-first-byte).\",\n" +
                "\"score\": 1,\n" +
                "\"scoreDisplayMode\": \"binary\",\n" +
                "\"displayValue\": \"Root document took 510 ms\",\n" +
                "\"details\": {\n" +
                "\"overallSavingsMs\": -90.17700000000002,\n" +
                "\"headings\": [],\n" +
                "\"type\": \"opportunity\",\n" +
                "\"items\": []\n" +
                "},\n" +
                "\"numericValue\": 509.823\n" +
                "},\n" +
                "\"estimated-input-latency\": {\n" +
                "\"id\": \"estimated-input-latency\",\n" +
                "\"title\": \"Estimated Input Latency\",\n" +
                "\"description\": \"Estimated Input Latency is an estimate of how long your app takes to respond to user input, in milliseconds, during the busiest 5s window of page load. If your latency is higher than 50 ms, users may perceive your app as laggy. [Learn more](https://web.dev/estimated-input-latency).\",\n" +
                "\"score\": 0.99,\n" +
                "\"scoreDisplayMode\": \"numeric\",\n" +
                "\"displayValue\": \"40 ms\",\n" +
                "\"numericValue\": 39.906666666666666\n" +
                "}\n" +
                "}\n" +
                "}\n" +
                "}";
        Gson gson = new Gson();
        PageSpeedV5ResponseEntity googleJson = gson.fromJson(json, PageSpeedV5ResponseEntity.class);
//        System.out.println("===lightHouseResult:" + new Gson().toJson(pageSpeedV5ResultEntity));


        System.out.println("===lightHouseResult:" + googleJson.getLighthouseResult().getEstimatedInputLatencyJson());

//        String json1 = "{\n" +
//                "\"captchaResult\": \"CAPTCHA_NOT_NEEDED\",\n" +
//                "\"kind\": \"pagespeedonline#result\",\n" +
//                "\"id\": \"https://searchengineland.com/\",\n" +
//                "\"responseCode\": 200,\n" +
//                "\"loadingExperience\": {\n" +
//                "\"id\": \"https://searchengineland.com/\",\n" +
//                "\"metrics\": {\n" +
//                "\"FIRST_CONTENTFUL_PAINT_MS\": {},\n" +
//                "\"DOM_CONTENT_LOADED_EVENT_FIRED_MS\": {}\n" +
//                "},\n" +
//                "\"overall_category\": \"AVERAGE\",\n" +
//                "\"initial_url\": \"https://searchengineland.com/\"\n" +
//                "}\n" +
//                "}";
//        UrlResultEntity resultJsonEntity = gson.fromJson(json1, UrlResultEntity.class);
//        System.out.println("===resultJsonEntity:" + new Gson().toJson(resultJsonEntity));

    }

}
