package seoclarity.backend.entity.clickhouse.pagespeed.v5;

public class Request {

    private Double startTime;
    private Integer transferSize;
    private String url;
    private Double responseReceivedTime;
    private Double endTime;

    public Double getStartTime() {
        return startTime;
    }

    public void setStartTime(Double startTime) {
        this.startTime = startTime;
    }

    public Integer getTransferSize() {
        return transferSize;
    }

    public void setTransferSize(Integer transferSize) {
        this.transferSize = transferSize;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Double getResponseReceivedTime() {
        return responseReceivedTime;
    }

    public void setResponseReceivedTime(Double responseReceivedTime) {
        this.responseReceivedTime = responseReceivedTime;
    }

    public Double getEndTime() {
        return endTime;
    }

    public void setEndTime(Double endTime) {
        this.endTime = endTime;
    }
}
