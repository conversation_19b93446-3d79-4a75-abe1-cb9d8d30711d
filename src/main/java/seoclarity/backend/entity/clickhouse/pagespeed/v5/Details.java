package seoclarity.backend.entity.clickhouse.pagespeed.v5;

import java.util.Map;

public class Details {

    private Double overallSavingsMs;

    private String type;

    private Headings[] headings;

    private Items[] items;

    private Summary summary;

    private Map<String, Object> chains;

    public Double getOverallSavingsMs() {
        return overallSavingsMs;
    }

    public void setOverallSavingsMs(Double overallSavingsMs) {
        this.overallSavingsMs = overallSavingsMs;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Headings[] getHeadings() {
        return headings;
    }

    public void setHeadings(Headings[] headings) {
        this.headings = headings;
    }

    public Items[] getItems() {
        return items;
    }

    public void setItems(Items[] items) {
        this.items = items;
    }

    public Summary getSummary() {
        return summary;
    }

    public void setSummary(Summary summary) {
        this.summary = summary;
    }

    public Map<String, Object> getChains() {
        return chains;
    }

    public void setChains(Map<String, Object> chains) {
        this.chains = chains;
    }
}
