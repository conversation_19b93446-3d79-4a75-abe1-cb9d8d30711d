package seoclarity.backend.entity.clickhouse.prod;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import ru.yandex.clickhouse.ClickHouseArray;

import java.util.Date;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class DisSiteHealth3xx {
    private Integer domainId;
    private Integer crawlRequestId;
    private Date crawlRequestDate;
    private String url;
    private String[] h1;
    private String indexFlg;
    private Integer indexable;
    private String title;
    private String description;
    private String robots;
    private Integer status;
    private String robotsContents;
    private String canonical;
    private String redirectedUrl;
    private String redirectedAnchorText;

    private String finalUrl;
    private String[] finalH1;
    private String finalIndexFlg;
    private Integer finalIndexable;
    private String finalTitle;
    private String finalDescription;
    private String finalRobots;
    private Integer finalStatus;
    private String finalCanonical;
}
