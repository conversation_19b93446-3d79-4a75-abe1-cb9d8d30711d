package seoclarity.backend.entity.clickhouse.ri;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-10-17
 * @path seoclarity.backend.entity.clickhouse.ri.RankIntelligenceQueryForClarityDBVO
 * 
 */
public class RankIntelligenceQueryForClarityDBVO extends BaseClarityDBVO{
	public static final int TOP_COMPETITOR_COUNT_DEF = 5;
	public static final int PAGE_SIZE_DEF = 10;
	public static final int PAGE_SIZE_DOWNLOAD = 1000000;
	
	public static final int QUERY_TYPE_TOP_PAGES = 1;
	public static final int QUERY_TYPE_TOP_FOLDERS = 2;
	public static final int QUERY_TYPE_TOP_SUBDOMAIN = 3;
	
	public static final String QUERY_UNIVERSAL_RANK_TYPE_ANSWERBOX = "answerbox";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_IMAGE = "img";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_JOB = "job";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_JOB_OWN_DOMAIN = "job_owndomain";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_KNOWLEDGE = "knog";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_NEWS = "news";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_PPC = "ppc";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_PEOPLEALSOASK = "peoplealsoask";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_SHOPPING = "pla";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_VIDEO = "video";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_APP = "app";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_FAQ = "faq";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_LOCALLISTING = "ll";
	public static final String QUERY_UNIVERSAL_RANK_TYPE_AMP = "amp";
	
	public static final String[] QUERY_UNIVERSAL_RANK_TYPE_LIST = new String[] {
			QUERY_UNIVERSAL_RANK_TYPE_ANSWERBOX,
			QUERY_UNIVERSAL_RANK_TYPE_IMAGE,
			QUERY_UNIVERSAL_RANK_TYPE_JOB,
			QUERY_UNIVERSAL_RANK_TYPE_NEWS,
			QUERY_UNIVERSAL_RANK_TYPE_PPC,
			QUERY_UNIVERSAL_RANK_TYPE_PEOPLEALSOASK,
			QUERY_UNIVERSAL_RANK_TYPE_SHOPPING,
			QUERY_UNIVERSAL_RANK_TYPE_VIDEO,
			QUERY_UNIVERSAL_RANK_TYPE_APP,
			QUERY_UNIVERSAL_RANK_TYPE_FAQ,
			QUERY_UNIVERSAL_RANK_TYPE_LOCALLISTING,
			QUERY_UNIVERSAL_RANK_TYPE_JOB_OWN_DOMAIN,
			QUERY_UNIVERSAL_RANK_TYPE_AMP
	};

	public static final List<Integer> SERP_DAILY_DOMAINS = Arrays.asList(new Integer[] {
			7628, 7605, 8405,8406,8407,8408,8409,8410,8411,8412,
			// red ventrues domains
			8826, 8879, 9013, 9014, 9016, 9017, 9018, 9031, 9032, 9033,
			9034, 9035, 9036, 9037, 9038, 9039, 9040, 9041, 9042, 9043,
			9044, 9045, 9046, 9089, 9090, 9091, 9092, 9093, 9094, 9095,
			9096, 9097, 9098, 9107, 9108, 9109, 9110, 9111, 9112, 9113,
			9114, 9115, 9116, 9117, 9118, 9119, 9120, 9121, 9122, 9123,
			9124, 9125, 9126, 9127, 9128, 9129, 9130, 9131, 9132, 9133,
			9134, 9135, 9136, 9137, 9138, 9139, 9140, 9141, 9142, 9143,
			9144, 9145, 9146, 9147, 9148, 9149, 9150, 9151, 9152, 9153,
			9154, 9155, 9156, 9157, 9158, 9159, 9160, 9161, 9162, 9163,
			9164, 9165, 9166, 9167, 9168, 9169, 9170, 9171, 9172, 9173,
			9174, 9175, 9176, 9177, 9178, 9179, 9180, 9181, 9182, 9183,
			9184, 9185, 9186, 9260, 9261, 9315, 9345, 9346, 9397, 9398,
			9399, 9409
	});

	private int ownDomainId;
	private String[] tagIdList;
	private String[] excludeTagIdList;
	private String[] subTagIdList;
	// https://www.wrike.com/open.htm?id=355493748
	private String[] locationTagList;
	private String[] locationExcludeTagList;
	
	private String[] domainList;
	private String[] indexList;
	private String[] avoidDateList;
	private String[] ctrList;
	private String locationIdList; // 111,222,333...
	private String[] competitorGroups;
	private Map<String, String> competitorGroupsMap;
	private Map<String, String> competitorGroupsIdNameMap;
	// for tag view trend summary
	private String[] queryTagTrendList;
	// for customer trend summary
	private String[] customerRankRangeList;
	private String[] customerPageRangeList; 
	// for new/lost/increase/decrease compare
	private String compareType;
	private String domainType;
	private String topQueryType;
	// 1/2/3
	private String queryType;
	private int engine;
	private int language;
	private int engineId;
	private int languageId;
	// 1-1-d,1-1-m,255-1-d
	private String[] engineLanguageList;

	private Integer fromIndex;
	private Integer toIndex;

	private Integer countInTop = 0;
	private Integer countSvInTop = 0;
	private Integer topCompetitorCountInt;
	// for kp top competitor domains sort
	// rank/searchVolume:Xrank
	private String sortMetricsForTopCompetitor;

	private boolean uniqueKeywords;
	private boolean broadMatch;
	private boolean trueRank;
	private boolean mobile;
	private boolean customSearchVol;
	private boolean include101;
	// filter answer box
	private boolean filterAnswer;
	private boolean downLoadAll;
	private boolean weeklyRankFrequency;

	private String ownDomainName;
	private String queryName;
	private String[] queryKeywords;
	// for top folders query
	private String[] subFolders;
	private String[] subDomainList;
	private String[] urlList;
	private List<String[]> competitorDomainList;
	private String competitorName;
	
	private String[] multiDomainIds;
	private String[] multiDomainNames;
	private String[] multiDomainLocationList;
	private String[] multiDomainKeywordTags;
	
	private String queryFolder;
	private String queryUrlHash;
	private String querySubDomain;
	private String index;

	private String[] brandList;
	private String[] domainSetBrandList;
	
	private String[] trendSummaryTypes;
	
	private boolean enableTranslation;

	private boolean kwAddedToday;
	private boolean kwDeledToday;
	
	// for adhoc 
	private String projectId;
	
	// for page
	private int size = PAGE_SIZE_DEF;
	private int offset = 0;
	private String sort;

	// left filter
	private Map<String, String> filterMap;
	// column filter
	private Map<String, String> nvFilterMap;
	
	/**
	 * NULL : do nothing, 
	 * TRUE : ONLY processes for google domains and group by type, 
	 * FALSE :  should exclude google domains from the result
	 */
	private Boolean isOnlyForGoogleCompetitor;

	private boolean isSpecialParamDomain = false;
	
	// use this for universal rank type detail table
	private String queryUniversalRankType;
	
	// for traffic potential
	private String rankImprovingEstimateType;
	private String rankImprovingEstimateValue;
	private String currencySymbolStr;

	/**
	 * move the tag filter to the outermost  in sql
	 */
	private boolean isUseOutterTagFilter = false;

	// https://www.wrike.com/open.htm?id=555333123
	// use for keyword ranking export
	private List<Map<String, String>> dateRangeTableMapList;

	public RankIntelligenceQueryForClarityDBVO() {
	}

	public boolean isUseOutterTagFilter() {
		return isUseOutterTagFilter;
	}

	public void setUseOutterTagFilter(boolean useOutterTagFilter) {
		isUseOutterTagFilter = useOutterTagFilter;
	}

	public String[] getTagIdList() {
		return tagIdList;
	}

	public void setTagIdList(String[] tagIdList) {
		this.tagIdList = tagIdList;
	}

	public String[] getExcludeTagIdList() {
		return excludeTagIdList;
	}

	public void setExcludeTagIdList(String[] excludeTagIdList) {
		this.excludeTagIdList = excludeTagIdList;
	}

	public String[] getSubTagIdList() {
		return subTagIdList;
	}

	public void setSubTagIdList(String[] subTagIdList) {
		this.subTagIdList = subTagIdList;
	}

	public String[] getDomainList() {
		return domainList;
	}

	public void setDomainList(String[] domainList) {
		this.domainList = domainList;
	}

	public String[] getIndexList() {
		return indexList;
	}

	public void setIndexList(String[] indexList) {
		this.indexList = indexList;
	}

	public String[] getAvoidDateList() {
		return avoidDateList;
	}

	public void setAvoidDateList(String[] avoidDateList) {
		this.avoidDateList = avoidDateList;
	}

	public String[] getCtrList() {
		return ctrList;
	}

	public void setCtrList(String[] ctrList) {
		this.ctrList = ctrList;
	}

	public int getEngine() {
		return engine;
	}

	public void setEngine(int engine) {
		this.engine = engine;
	}

	public int getLanguage() {
		return language;
	}

	public void setLanguage(int language) {
		this.language = language;
	}

	public Integer getFromIndex() {
		if (indexList == null || indexList.length == 0) {
			return fromIndex;
		} else {
			return Integer.valueOf(indexList[0]);
		}
	}

	public Integer getToIndex() {
		if (indexList == null || indexList.length == 0) {
			return this.toIndex;
		} else {
			return (indexList.length > 1 ? Integer.valueOf(indexList[1]) : Integer.valueOf(indexList[0]));
		}
	}

	public Integer getCountInTop() {
		return countInTop;
	}

	public void setCountInTop(Integer countInTop) {
		this.countInTop = countInTop;
	}

	public Integer getCountSvInTop() {
		return countSvInTop;
	}

	public void setCountSvInTop(Integer countSvInTop) {
		this.countSvInTop = countSvInTop;
	}

	public Integer getTopCompetitorCountInt() {
		return topCompetitorCountInt;
	}

	public void setTopCompetitorCountInt(Integer topCompetitorCountInt) {
		this.topCompetitorCountInt = topCompetitorCountInt;
	}

	public boolean isUniqueKeywords() {
		return uniqueKeywords;
	}

	public void setUniqueKeywords(boolean uniqueKeywords) {
		this.uniqueKeywords = uniqueKeywords;
	}

	public boolean isBroadMatch() {
		return broadMatch;
	}

	public void setBroadMatch(boolean broadMatch) {
		this.broadMatch = broadMatch;
	}

	public boolean isTrueRank() {
		return trueRank;
	}

	public void setTrueRank(boolean isTrueRank) {
		this.trueRank = isTrueRank;
	}

	public boolean isMobile() {
		return mobile;
	}

	public void setMobile(boolean mobile) {
		this.mobile = mobile;
	}

	public boolean isCustomSearchVol() {
		return customSearchVol;
	}

	public void setCustomSearchVol(boolean isCustomSearchVol) {
		this.customSearchVol = isCustomSearchVol;
	}

	public boolean isInclude101() {
		return include101;
	}

	public void setInclude101(boolean isInclude101) {
		this.include101 = isInclude101;
	}

	public boolean isFilterAnswer() {
		return filterAnswer;
	}

	public void setFilterAnswer(boolean isFilterAnswer) {
		this.filterAnswer = isFilterAnswer;
	}

	public boolean isDownLoadAll() {
		return downLoadAll;
	}

	public void setDownLoadAll(boolean isDownLoadAll) {
		this.downLoadAll = isDownLoadAll;
	}

	public boolean isWeeklyRankFrequency() {
		return weeklyRankFrequency;
	}

	public void setWeeklyRankFrequency(boolean isWeeklyRankFrequency) {
		this.weeklyRankFrequency = isWeeklyRankFrequency;
	}

	public String[] getQueryKeywords() {
		return queryKeywords;
	}

	public void setQueryKeywords(String[] queryKeywords) {
		this.queryKeywords = queryKeywords;
	}

	public String getDomainType() {
		return domainType;
	}

	public void setDomainType(String domainType) {
		this.domainType = domainType;
	}

	public String[] getSubFolders() {
		return subFolders;
	}

	public void setSubFolders(String[] subFolders) {
		this.subFolders = subFolders;
	}

	public int getSize() {
		if (isDownLoadAll()) {
			return PAGE_SIZE_DOWNLOAD;
		}
		return size;
	}

	public void setSize(int size) {
		this.size = size;
	}

	public int getOffset() {
		if (isDownLoadAll()) {
			return 0;
		}
		return offset;
	}

	public void setOffset(int offset) {
		this.offset = offset;
	}

	public String getSort() {
		return sort;
	}

	public void setSort(String sort) {
		this.sort = sort;
	}

	public Map<String, String> getFilterMap() {
		return filterMap;
	}

	public void setFilterMap(Map<String, String> filterMap) {
		this.filterMap = filterMap;
	}

	public Map<String, String> getNvFilterMap() {
		return nvFilterMap;
	}

	public void setNvFilterMap(Map<String, String> nvFilterMap) {
		this.nvFilterMap = nvFilterMap;
	}

	public String getLocationIdList() {
		return locationIdList;
	}

	public void setLocationIdList(String locationIdList) {
		this.locationIdList = locationIdList;
	}
	
	public String[] getCustomerRankRangeList() {
		return customerRankRangeList;
	}

	public void setCustomerRankRangeList(String[] customerRankRangeList) {
		this.customerRankRangeList = customerRankRangeList;
	}

	public String[] getCustomerPageRangeList() {
		return customerPageRangeList;
	}

	public void setCustomerPageRangeList(String[] customerPageRangeList) {
		this.customerPageRangeList = customerPageRangeList;
	}

	public String getCompareType() {
		return compareType;
	}

	public void setCompareType(String compareType) {
		this.compareType = compareType;
	}

	public String getTopQueryType() {
		return topQueryType;
	}

	public void setTopQueryType(String topQueryType) {
		this.topQueryType = topQueryType;
	}

	public List<String[]> getCompetitorDomainList() {
		return competitorDomainList;
	}

	public void setCompetitorDomainList(List<String[]> competitorDomainList) {
		this.competitorDomainList = competitorDomainList;
	}

	public String getQueryName() {
		return queryName;
	}

	public void setQueryName(String queryName) {
		this.queryName = queryName;
	}

	public String[] getCompetitorGroups() {
		return competitorGroups;
	}

	public void setCompetitorGroups(String[] competitorGroups) {
		this.competitorGroups = competitorGroups;
	}

	public String getOwnDomainName() {
		return ownDomainName;
	}

	public void setOwnDomainName(String ownDomainName) {
		this.ownDomainName = ownDomainName;
	}

	public String[] getUrlList() {
		return urlList;
	}

	public void setUrlList(String[] urlList) {
		this.urlList = urlList;
	}

	public String getCompetitorName() {
		return competitorName;
	}

	public void setCompetitorName(String competitorName) {
		this.competitorName = competitorName;
	}

	public String getQueryType() {
		return queryType;
	}

	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}

	public Map<String, String> getCompetitorGroupsMap() {
		return competitorGroupsMap;
	}

	public void setCompetitorGroupsMap(Map<String, String> competitorGroupsMap) {
		this.competitorGroupsMap = competitorGroupsMap;
	}

	public Map<String, String> getCompetitorGroupsIdNameMap() {
		return competitorGroupsIdNameMap;
	}

	public void setCompetitorGroupsIdNameMap(Map<String, String> competitorGroupsIdNameMap) {
		this.competitorGroupsIdNameMap = competitorGroupsIdNameMap;
	}

	public String getSortMetricsForTopCompetitor() {
		return sortMetricsForTopCompetitor;
	}

	public void setSortMetricsForTopCompetitor(String sortMetricsForTopCompetitor) {
		this.sortMetricsForTopCompetitor = sortMetricsForTopCompetitor;
	}

	public Boolean getIsOnlyForGoogleCompetitor() {
		return isOnlyForGoogleCompetitor;
	}

	public void setIsOnlyForGoogleCompetitor(Boolean isOnlyForGoogleCompetitor) {
		this.isOnlyForGoogleCompetitor = isOnlyForGoogleCompetitor;
	}

	public String getQueryFolder() {
		return queryFolder;
	}

	public void setQueryFolder(String queryFolder) {
		this.queryFolder = queryFolder;
	}

	public String getQueryUrlHash() {
		return queryUrlHash;
	}

	public void setQueryUrlHash(String queryUrlHash) {
		this.queryUrlHash = queryUrlHash;
	}

	public String getQuerySubDomain() {
		return querySubDomain;
	}

	public void setQuerySubDomain(String querySubDomain) {
		this.querySubDomain = querySubDomain;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public String[] getSubDomainList() {
		return subDomainList;
	}

	public void setSubDomainList(String[] subDomainList) {
		this.subDomainList = subDomainList;
	}

	public String[] getQueryTagTrendList() {
		return queryTagTrendList;
	}

	public void setQueryTagTrendList(String[] queryTagTrendList) {
		this.queryTagTrendList = queryTagTrendList;
	}

	public String[] getLocationExcludeTagList() {
		return locationExcludeTagList;
	}

	public void setLocationExcludeTagList(String[] locationExcludeTagList) {
		this.locationExcludeTagList = locationExcludeTagList;
	}

	public String[] getLocationTagList() {
		return locationTagList;
	}

	public void setLocationTagList(String[] locationTagList) {
		this.locationTagList = locationTagList;
	}

	public String[] getEngineLanguageList() {
		return engineLanguageList;
	}

	public void setEngineLanguageList(String[] engineLanguageList) {
		this.engineLanguageList = engineLanguageList;
	}

	public String[] getMultiDomainIds() {
		return multiDomainIds;
	}

	public void setMultiDomainIds(String[] multiDomainIds) {
		this.multiDomainIds = multiDomainIds;
	}

	public String[] getMultiDomainNames() {
		return multiDomainNames;
	}

	public void setMultiDomainNames(String[] multiDomainNames) {
		this.multiDomainNames = multiDomainNames;
	}

	public String[] getMultiDomainLocationList() {
		return multiDomainLocationList;
	}

	public void setMultiDomainLocationList(String[] multiDomainLocationList) {
		this.multiDomainLocationList = multiDomainLocationList;
	}

	public String[] getMultiDomainKeywordTags() {
		return multiDomainKeywordTags;
	}

	public void setMultiDomainKeywordTags(String[] multiDomainKeywordTags) {
		this.multiDomainKeywordTags = multiDomainKeywordTags;
	}

	public boolean isSpecialParamDomain() {
		return isSpecialParamDomain;
	}

	public void setSpecialParamDomain(boolean isSpecialParamDomain) {
		this.isSpecialParamDomain = isSpecialParamDomain;
	}

	public String getQueryUniversalRankType() {
		return queryUniversalRankType;
	}

	public void setQueryUniversalRankType(String queryUniversalRankType) {
		this.queryUniversalRankType = queryUniversalRankType;
	}

	public String[] getBrandList() {
		return brandList;
	}

	public void setBrandList(String[] brandList) {
		this.brandList = brandList;
	}

	public String[] getDomainSetBrandList() {
		return domainSetBrandList;
	}

	public void setDomainSetBrandList(String[] domainSetBrandList) {
		this.domainSetBrandList = domainSetBrandList;
	}

	public String[] getTrendSummaryTypes() {
		return trendSummaryTypes;
	}

	public void setTrendSummaryTypes(String[] trendSummaryTypes) {
		this.trendSummaryTypes = trendSummaryTypes;
	}

	public boolean isEnableTranslation() {
		return enableTranslation;
	}

	public void setEnableTranslation(boolean isEnableTranslation) {
		this.enableTranslation = isEnableTranslation;
	}

	public String getRankImprovingEstimateType() {
		return rankImprovingEstimateType;
	}

	public void setRankImprovingEstimateType(String rankImprovingEstimateType) {
		this.rankImprovingEstimateType = rankImprovingEstimateType;
	}

	public String getRankImprovingEstimateValue() {
		return rankImprovingEstimateValue;
	}

	public void setRankImprovingEstimateValue(String rankImprovingEstimateValue) {
		this.rankImprovingEstimateValue = rankImprovingEstimateValue;
	}

	public String getCurrencySymbolStr() {
		return currencySymbolStr;
	}

	public void setCurrencySymbolStr(String currencySymbolStr) {
		this.currencySymbolStr = currencySymbolStr;
	}

	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public int getEngineId() {
		if (engine != 0) {
			return engine;
		}
		return engineId;
	}

	public void setEngineId(int engineId) {
		this.engineId = engineId;
		this.engine = engineId;
	}

	public int getLanguageId() {
		if (language != 0) {
			return language;
		}
		return languageId;
	}

	public void setLanguageId(int languageId) {
		this.languageId = languageId;
		this.language = languageId;
	}

	public int getOwnDomainId() {
		if (this.getDomainId() != null && this.getDomainId() != 0) {
			return this.getDomainId();
		}
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
		this.setDomainId(ownDomainId);
	}

	public boolean isKwDeledToday() {
		return kwDeledToday;
	}

	public void setKwDeledToday(boolean kwDeledToday) {
		this.kwDeledToday = kwDeledToday;
	}

	public boolean isKwAddedToday() {
		return kwAddedToday;
	}

	public void setKwAddedToday(boolean kwAddedToday) {
		this.kwAddedToday = kwAddedToday;
	}

	public List<Map<String, String>> getDateRangeTableMapList() {
		return dateRangeTableMapList;
	}

	public void setDateRangeTableMapList(List<Map<String, String>> dateRangeTableMapList) {
		this.dateRangeTableMapList = dateRangeTableMapList;
	}
}
