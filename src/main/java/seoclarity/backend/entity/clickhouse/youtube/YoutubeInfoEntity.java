package seoclarity.backend.entity.clickhouse.youtube;

public class YoutubeInfoEntity {
	
	private String keywordName;
	private Integer ownDomainId;
	private Integer engineId;
	private Integer languageId;
	private Integer keywordRankcheckId;
	private String rankingDate;
	private Integer frequency;
	private Integer relId;
	private String device;
	
	private String[] attrstrkey; // Array(String)
	private String[] attrstrvalue; // Array(String)
	private String[] attrintkey; // Array(String)
	private Integer[] attrintvalue; // Array(UInt32)
	
	public String getKeywordName() {
		return keywordName;
	}
	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}
	public Integer getOwnDomainId() {
		return ownDomainId;
	}
	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	public Integer getEngineId() {
		return engineId;
	}
	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}
	public Integer getLanguageId() {
		return languageId;
	}
	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}
	public Integer getKeywordRankcheckId() {
		return keywordRankcheckId;
	}
	public void setKeywordRankcheckId(Integer keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}
	public String getRankingDate() {
		return rankingDate;
	}
	public void setRankingDate(String rankingDate) {
		this.rankingDate = rankingDate;
	}
	public Integer getFrequency() {
		return frequency;
	}
	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}
	public Integer getRelId() {
		return relId;
	}
	public void setRelId(Integer relId) {
		this.relId = relId;
	}
	public String getDevice() {
		return device;
	}
	public void setDevice(String device) {
		this.device = device;
	}
	public String[] getAttrstrkey() {
		return attrstrkey;
	}
	public void setAttrstrkey(String[] attrstrkey) {
		this.attrstrkey = attrstrkey;
	}
	public String[] getAttrstrvalue() {
		return attrstrvalue;
	}
	public void setAttrstrvalue(String[] attrstrvalue) {
		this.attrstrvalue = attrstrvalue;
	}
	public String[] getAttrintkey() {
		return attrintkey;
	}
	public void setAttrintkey(String[] attrintkey) {
		this.attrintkey = attrintkey;
	}
	public Integer[] getAttrintvalue() {
		return attrintvalue;
	}
	public void setAttrintvalue(Integer[] attrintvalue) {
		this.attrintvalue = attrintvalue;
	}
	
	

}
