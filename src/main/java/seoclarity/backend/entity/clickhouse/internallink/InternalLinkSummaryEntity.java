package seoclarity.backend.entity.clickhouse.internallink;

import java.util.Date;

public class InternalLinkSummaryEntity {
	
	
	private Integer domainId;
	private Date today;
	private Long crawlRequestLogId;
	private Long crawlDateLong;
	private Integer partitionNum;
	private Integer sourceLinkNofollow;
	private Integer destLinkNofollow;
	private String sourceUrl;
	private Long sourceUrlHash;
	private String sourceDomain;
	private Integer sourceRespCode;
	private String destUrl;
	private Long destinationUrlHash;
	private String destDomain;
	private Integer destRespCode;
	private Long outboundCnt;
	private String anchorText;
	private Long anchorTextHash;
	private Integer popularity;
	private String analyzedUrls;
	private String canonicalType;
	private boolean canonicalFlg;
	private String pageRobotsMetaIndex;
	private String pageRobotsMetaDollow;
	private String pageRobotsMetaArchive;
	private String titleMd5;
	private String title;
	private String canonical;
	private Integer analyzedUrlFlgs;
	private Integer titleFlg;
	private String canonicalString;
	private String sourceFolderLevel1;
	private Long sourceFolderLevel1Hash;
	private Long sourceFolderLevel2;
	private Long sourceFolderLevel2Hash;
	private Long descFolderLevel1;
	private Long descFolderLevel1Hash;
	private Long descFolderLevel2;
	private Long descFolderLevel2Hash;
	
	
	public Integer getDomainId() {
		return domainId;
	}
	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}
	public Date getToday() {
		return today;
	}
	public void setToday(Date today) {
		this.today = today;
	}
	public Long getCrawlRequestLogId() {
		return crawlRequestLogId;
	}
	public void setCrawlRequestLogId(Long crawlRequestLogId) {
		this.crawlRequestLogId = crawlRequestLogId;
	}
	public Long getCrawlDateLong() {
		return crawlDateLong;
	}
	public void setCrawlDateLong(Long crawlDateLong) {
		this.crawlDateLong = crawlDateLong;
	}
	public Integer getPartitionNum() {
		return partitionNum;
	}
	public void setPartitionNum(Integer partitionNum) {
		this.partitionNum = partitionNum;
	}
	public Integer getSourceLinkNofollow() {
		return sourceLinkNofollow;
	}
	public void setSourceLinkNofollow(Integer sourceLinkNofollow) {
		this.sourceLinkNofollow = sourceLinkNofollow;
	}
	public Integer getDestLinkNofollow() {
		return destLinkNofollow;
	}
	public void setDestLinkNofollow(Integer destLinkNofollow) {
		this.destLinkNofollow = destLinkNofollow;
	}
	public String getSourceUrl() {
		return sourceUrl;
	}
	public void setSourceUrl(String sourceUrl) {
		this.sourceUrl = sourceUrl;
	}
	public Long getSourceUrlHash() {
		return sourceUrlHash;
	}
	public void setSourceUrlHash(Long sourceUrlHash) {
		this.sourceUrlHash = sourceUrlHash;
	}
	public String getSourceDomain() {
		return sourceDomain;
	}
	public void setSourceDomain(String sourceDomain) {
		this.sourceDomain = sourceDomain;
	}
	public Integer getSourceRespCode() {
		return sourceRespCode;
	}
	public void setSourceRespCode(Integer sourceRespCode) {
		this.sourceRespCode = sourceRespCode;
	}
	public String getDestUrl() {
		return destUrl;
	}
	public void setDestUrl(String destUrl) {
		this.destUrl = destUrl;
	}
	public Long getDestinationUrlHash() {
		return destinationUrlHash;
	}
	public void setDestinationUrlHash(Long destinationUrlHash) {
		this.destinationUrlHash = destinationUrlHash;
	}
	public String getDestDomain() {
		return destDomain;
	}
	public void setDestDomain(String destDomain) {
		this.destDomain = destDomain;
	}
	public Integer getDestRespCode() {
		return destRespCode;
	}
	public void setDestRespCode(Integer destRespCode) {
		this.destRespCode = destRespCode;
	}
	public Long getOutboundCnt() {
		return outboundCnt;
	}
	public void setOutboundCnt(Long outboundCnt) {
		this.outboundCnt = outboundCnt;
	}
	public String getAnchorText() {
		return anchorText;
	}
	public void setAnchorText(String anchorText) {
		this.anchorText = anchorText;
	}
	public Long getAnchorTextHash() {
		return anchorTextHash;
	}
	public void setAnchorTextHash(Long anchorTextHash) {
		this.anchorTextHash = anchorTextHash;
	}
	public Integer getPopularity() {
		return popularity;
	}
	public void setPopularity(Integer popularity) {
		this.popularity = popularity;
	}
	public String getAnalyzedUrls() {
		return analyzedUrls;
	}
	public void setAnalyzedUrls(String analyzedUrls) {
		this.analyzedUrls = analyzedUrls;
	}
	public String getCanonicalType() {
		return canonicalType;
	}
	public void setCanonicalType(String canonicalType) {
		this.canonicalType = canonicalType;
	}
	public boolean isCanonicalFlg() {
		return canonicalFlg;
	}
	public void setCanonicalFlg(boolean canonicalFlg) {
		this.canonicalFlg = canonicalFlg;
	}
	public String getPageRobotsMetaIndex() {
		return pageRobotsMetaIndex;
	}
	public void setPageRobotsMetaIndex(String pageRobotsMetaIndex) {
		this.pageRobotsMetaIndex = pageRobotsMetaIndex;
	}
	public String getPageRobotsMetaDollow() {
		return pageRobotsMetaDollow;
	}
	public void setPageRobotsMetaDollow(String pageRobotsMetaDollow) {
		this.pageRobotsMetaDollow = pageRobotsMetaDollow;
	}
	public String getPageRobotsMetaArchive() {
		return pageRobotsMetaArchive;
	}
	public void setPageRobotsMetaArchive(String pageRobotsMetaArchive) {
		this.pageRobotsMetaArchive = pageRobotsMetaArchive;
	}
	public String getTitleMd5() {
		return titleMd5;
	}
	public void setTitleMd5(String titleMd5) {
		this.titleMd5 = titleMd5;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getCanonical() {
		return canonical;
	}
	public void setCanonical(String canonical) {
		this.canonical = canonical;
	}
	public Integer getAnalyzedUrlFlgs() {
		return analyzedUrlFlgs;
	}
	public void setAnalyzedUrlFlgs(Integer analyzedUrlFlgs) {
		this.analyzedUrlFlgs = analyzedUrlFlgs;
	}
	public Integer getTitleFlg() {
		return titleFlg;
	}
	public void setTitleFlg(Integer titleFlg) {
		this.titleFlg = titleFlg;
	}
	public String getCanonicalString() {
		return canonicalString;
	}
	public void setCanonicalString(String canonicalString) {
		this.canonicalString = canonicalString;
	}
	public String getSourceFolderLevel1() {
		return sourceFolderLevel1;
	}
	public void setSourceFolderLevel1(String sourceFolderLevel1) {
		this.sourceFolderLevel1 = sourceFolderLevel1;
	}
	public Long getSourceFolderLevel1Hash() {
		return sourceFolderLevel1Hash;
	}
	public void setSourceFolderLevel1Hash(Long sourceFolderLevel1Hash) {
		this.sourceFolderLevel1Hash = sourceFolderLevel1Hash;
	}
	public Long getSourceFolderLevel2() {
		return sourceFolderLevel2;
	}
	public void setSourceFolderLevel2(Long sourceFolderLevel2) {
		this.sourceFolderLevel2 = sourceFolderLevel2;
	}
	public Long getSourceFolderLevel2Hash() {
		return sourceFolderLevel2Hash;
	}
	public void setSourceFolderLevel2Hash(Long sourceFolderLevel2Hash) {
		this.sourceFolderLevel2Hash = sourceFolderLevel2Hash;
	}
	public Long getDescFolderLevel1() {
		return descFolderLevel1;
	}
	public void setDescFolderLevel1(Long descFolderLevel1) {
		this.descFolderLevel1 = descFolderLevel1;
	}
	public Long getDescFolderLevel1Hash() {
		return descFolderLevel1Hash;
	}
	public void setDescFolderLevel1Hash(Long descFolderLevel1Hash) {
		this.descFolderLevel1Hash = descFolderLevel1Hash;
	}
	public Long getDescFolderLevel2() {
		return descFolderLevel2;
	}
	public void setDescFolderLevel2(Long descFolderLevel2) {
		this.descFolderLevel2 = descFolderLevel2;
	}
	public Long getDescFolderLevel2Hash() {
		return descFolderLevel2Hash;
	}
	public void setDescFolderLevel2Hash(Long descFolderLevel2Hash) {
		this.descFolderLevel2Hash = descFolderLevel2Hash;
	}
	
	
	
	
}
