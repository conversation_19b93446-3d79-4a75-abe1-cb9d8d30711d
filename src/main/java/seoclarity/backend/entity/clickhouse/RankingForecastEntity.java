package seoclarity.backend.entity.clickhouse;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-07-06 21:23
 **/
@Getter @Setter
public class RankingForecastEntity {

    private String keywordName;
    private Integer ownDomainId;
    private Integer keywordRankcheckId;
    private Integer engineId;
    private Integer languageId;
    private Integer locationId;
    private String device;
    private Date rankingDate;
    private Double score;
    private Integer sign;
    private Integer versioning;

}
