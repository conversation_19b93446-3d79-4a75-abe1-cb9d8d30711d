package seoclarity.backend.entity.clickhouse;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-09-21 16:05
 **/
@Getter @Setter
public class PixelHeightEntity {

    private Date rankingDate;
    private Integer firstPageSize;
    private Integer ownDomainId;
    private Integer engineId;
    private Integer searchVol;
    private Integer languageId;
    private Integer locationId;
    private String rootDomainReverse;
    private String domainReverse;
    private Integer keywordRankcheckId;
    private String keywordName;
    private Integer rank;
    private String label;
    private String url;
    private Integer height;
    private Integer fold;
    private Integer foldRank;
    private Integer offsetBottom;
    private Integer offsetTop;
    private Integer type;
    private Integer hrd;
    private Integer hrrd;
    private String device;
    private Date createDate;


}
