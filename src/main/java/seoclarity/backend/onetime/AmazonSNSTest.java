package seoclarity.backend.onetime;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sns.AmazonSNS;
import com.amazonaws.services.sns.AmazonSNSClient;
import com.amazonaws.services.sns.model.CreateTopicResult;
import com.amazonaws.services.sns.model.PublishRequest;
import com.amazonaws.services.sns.model.PublishResult;

import java.util.Properties;

public class AmazonSNSTest {

    public static void main(String[] args) throws Exception{
        AmazonSNSTest ins = new AmazonSNSTest();
        //ins.test1();
        ins.test2();


    }

    private void test1() {
        AmazonSNSClient client = Region.getRegion(Regions.US_EAST_1).createClient(AmazonSNSClient.class, null, null);
        CreateTopicResult createTopic = client.createTopic("arn:aws:sns:us-east-1:397485469449:general_msg:06ab7135-3ed8-4d8a-bbdf-addad3c0077a");
        createTopic.getTopicArn();
        final PublishRequest publishRequest = new PublishRequest(createTopic.getTopicArn(), "Test message");
        PublishResult result = client.publish(publishRequest);
        System.out.println(result);
        System.out.println("OK");
    }

    private void test2() throws Exception{

        AWSCredentials credentials = new BasicAWSCredentials("********************","v7Qts+qswF9qDojbKZQ1BofRbWcQLJu8CLM+eXrf");

//        AmazonS3 s3client = new AmazonS3Client(credentials);

        AmazonSNS sns = new AmazonSNSClient(credentials);
        sns.setEndpoint("https://sns.us-east-1.amazonaws.com");

        String msg = "If you receive this message, publishing a message to an Amazon SNS topic works.";
        String topicArn = "arn:aws:sns:us-east-1:397485469449:general_msg:06ab7135-3ed8-4d8a-bbdf-addad3c0077a";
        final PublishRequest publishRequest = new PublishRequest(topicArn, msg);
        final PublishResult publishResponse = sns.publish(publishRequest);
        System.out.println("MessageId: " + publishResponse.getMessageId());
    }

}
