package seoclarity.backend.onetime;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.dao.actonia.RgFrequencyConfigEntityDao;
import seoclarity.backend.entity.RGFrequencyConfigEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.text.ParseException;
import java.time.*;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * ewain
 * 20220811
 * https://www.wrike.com/open.htm?id=946026188
 *
 * nohup /bin/mvn -f /home/<USER>/source/ewain/dev-clarity-backend-scripts/pom.xml exec:java -Dexec.mainClass="seoclarity.backend.onetime.PopulateVirtualDataToRGFreqConfigTable" -Dexec.args="" -Dexec.cleanupDaemonThreads=false >> log/PopulateVirtualDataToRGFreqConfigTable-01.log 2>&1 &
 */
public class PopulateVirtualDataToRGFreqConfigTable {

    private RgFrequencyConfigEntityDao rgFrequencyConfigEntityDao;

    public PopulateVirtualDataToRGFreqConfigTable() {
        rgFrequencyConfigEntityDao = SpringBeanFactory.getBean("rgFrequencyConfigEntityDao");
    }

    public static void main(String[] args) {
        PopulateVirtualDataToRGFreqConfigTable tool = new PopulateVirtualDataToRGFreqConfigTable();
        tool.processUpdateMonthlyViewDate();
    }

    private void processUpdateMonthlyViewDate() {
        int updateCnt = 0;
        List<RGFrequencyConfigEntity> rgFrequencyConfigEntityList = rgFrequencyConfigEntityDao.getAll();
        for (RGFrequencyConfigEntity rgFrequencyConfigEntity : rgFrequencyConfigEntityList) {
            int id = rgFrequencyConfigEntity.getId();
            int frequency = rgFrequencyConfigEntity.getFrequency();
            int monthlyViewDate = rgFrequencyConfigEntity.getMonthlyViewDate();
            int rankDateI = rgFrequencyConfigEntity.getRankDate();
            int fromDate = rgFrequencyConfigEntity.getFromDate();
            if (frequency >= 60) {
                if (monthlyViewDate == 0) {
                    Date rankDate;
                    try {
                        rankDate = DateUtils.parseDate(String.valueOf(rankDateI), "yyyyMMdd");
                    } catch (ParseException e) {
                        e.printStackTrace();
                        return;
                    }
                    Calendar instance = Calendar.getInstance();
                    instance.setTime(rankDate);
                    int dayOfM = instance.get(Calendar.DAY_OF_MONTH);
                    if (dayOfM == 1) {
                        System.out.println("need update freq: " + frequency + ", rankDate: " + rankDateI + ", fromDate: " + fromDate);
                        int i = rgFrequencyConfigEntityDao.updateMonthlyViewDateById(id, fromDate);
                        updateCnt += i;
                    }
                }
            }
        }

        System.out.println("updateCnt: " + updateCnt);
    }

    private void process() {
        Date sDate = null;
        Date eDate = null;
        try {
            sDate = DateUtils.parseDate("2016-01-01", "yyyy-MM-dd");
            eDate = DateUtils.parseDate("2036-12-31", "yyyy-MM-dd");
        } catch (ParseException e) {
            e.printStackTrace();
        }

        List<RGFrequencyConfigEntity> rgFrequencyConfigEntityList = new ArrayList<>();

        while (true) {
            LocalDate localDate = sDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            RGFrequencyConfigEntity rgFrequencyConfigEntityDaily = buildDailyData(sDate, localDate);
            rgFrequencyConfigEntityList.add(rgFrequencyConfigEntityDaily);

            RGFrequencyConfigEntity rgFrequencyConfigEntityWeekly = buildWeeklyData(sDate, localDate);
            rgFrequencyConfigEntityList.add(rgFrequencyConfigEntityWeekly);

            RGFrequencyConfigEntity rgFrequencyConfigEntityMonthly = buildMonthlyData(sDate, localDate);
            rgFrequencyConfigEntityList.add(rgFrequencyConfigEntityMonthly);

            RGFrequencyConfigEntity rgFrequencyConfigEntityBiMonthly = buildBiMonthlyData(sDate, localDate);
            rgFrequencyConfigEntityList.add(rgFrequencyConfigEntityBiMonthly);

            RGFrequencyConfigEntity rgFrequencyConfigEntityQuarter = buildQuarterData(sDate, localDate);
            rgFrequencyConfigEntityList.add(rgFrequencyConfigEntityQuarter);

            sDate = DateUtils.addDays(sDate, 1);
            if (sDate.compareTo(eDate) > 0) {
                break;
            }
        }

        rgFrequencyConfigEntityDao.insertBatch(rgFrequencyConfigEntityList);
    }
    private RGFrequencyConfigEntity constructEntity(int rankDate, int frequency, int fromDate, int toDate, int year, int month,
                                                    int yearIndex, int dailyViewDate, int weeklyViewDate, int monthlyViewDate) {
        RGFrequencyConfigEntity rgFrequencyConfigEntity = new RGFrequencyConfigEntity();
        rgFrequencyConfigEntity.setRankDate(rankDate);
        rgFrequencyConfigEntity.setFrequency(frequency);
        rgFrequencyConfigEntity.setFromDate(fromDate);
        rgFrequencyConfigEntity.setToDate(toDate);
        rgFrequencyConfigEntity.setYear(year);
        rgFrequencyConfigEntity.setMonth(month);
        rgFrequencyConfigEntity.setYearIndex(yearIndex);
        rgFrequencyConfigEntity.setDailyViewDate(dailyViewDate);
        rgFrequencyConfigEntity.setWeeklyViewDate(weeklyViewDate);
        rgFrequencyConfigEntity.setMonthlyViewDate(monthlyViewDate);
        rgFrequencyConfigEntity.setCreateDate(new Date());
        return rgFrequencyConfigEntity;
    }

    private RGFrequencyConfigEntity buildDailyData(Date sDate, LocalDate localDate) {
        int rankDate = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
        int frequency = 1;
        int fromDate = rankDate;
        int toDate = rankDate;
        int year = Integer.parseInt(DateFormatUtils.format(sDate, "yyyy"));
        int month = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMM"));
        int yearIndex = localDate.getDayOfYear();

        int dailyV = fromDate;
        int weeklyV = 0;
        while (true) {
            Calendar instance = Calendar.getInstance();
            instance.setTime(sDate);
            if (instance.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                weeklyV = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
                break;
            }else {
                sDate = DateUtils.addDays(sDate, -1);
            }
        }

        if (weeklyV != rankDate) {
            weeklyV = 0;
        }

        Calendar instance = Calendar.getInstance();
        instance.setTime(sDate);
        int actualMaximum = instance.getActualMaximum(Calendar.DAY_OF_MONTH);
        instance.set(Calendar.DAY_OF_MONTH, actualMaximum);
        int monthlyV = Integer.parseInt(DateFormatUtils.format(instance.getTime(), "yyyyMMdd"));

        if (monthlyV != rankDate) {
            monthlyV = 0;
        }

        return constructEntity(rankDate, frequency, fromDate, toDate, year, month, yearIndex, dailyV, weeklyV, monthlyV);
    }

    private RGFrequencyConfigEntity buildWeeklyData(Date sDate, LocalDate localDate) {
        int value = localDate.getDayOfWeek().getValue();
        int minusValue;
        if (value == 7) {
            minusValue = 0;
        } else {
            minusValue = value;
        }

        int rankDate = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
        int frequency = 7;
        int fromDate = Integer.parseInt(DateFormatUtils.format(DateUtils.addDays(sDate, -minusValue), "yyyyMMdd"));
        int toDate = Integer.parseInt(DateFormatUtils.format(DateUtils.addDays(sDate, 7-minusValue-1), "yyyyMMdd"));
        int year = Integer.parseInt(String.valueOf(fromDate).substring(0, 4));
        int month = Integer.parseInt(String.valueOf(fromDate).substring(0, 6));

        LocalDate fromLocalDate = DateUtils.addDays(sDate, -minusValue).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        int yearIndex = fromLocalDate.get(WeekFields.ISO.weekOfWeekBasedYear());

        int dailyV = fromDate;

        int weeklyV = 0;
        while (true) {
            Calendar instance = Calendar.getInstance();
            instance.setTime(sDate);
            if (instance.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                weeklyV = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
                break;
            }else {
                sDate = DateUtils.addDays(sDate, -1);
            }
        }

        if (weeklyV != rankDate) {
            weeklyV = 0;
        }

        int monthlyV = 0;
        Calendar instance = Calendar.getInstance();
        instance.setTime(sDate);
        int actualMaximum = instance.getActualMaximum(Calendar.DAY_OF_MONTH);
        instance.set(Calendar.DAY_OF_MONTH, actualMaximum);
        Date time = instance.getTime();

        while (true) {
            Calendar instance2 = Calendar.getInstance();
            instance2.setTime(time);
            if (instance2.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                monthlyV = Integer.parseInt(DateFormatUtils.format(time, "yyyyMMdd"));
                break;
            }else {
                time = DateUtils.addDays(time, -1);
            }
        }

        if (monthlyV != rankDate) {
            monthlyV = 0;
        }

        return constructEntity(rankDate, frequency, fromDate, toDate, year, month, yearIndex, dailyV, weeklyV, monthlyV);
    }

    private RGFrequencyConfigEntity buildMonthlyData(Date sDate, LocalDate localDate) {

        int rankDate = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
        int frequency = 30;

        int currYear = localDate.getYear();
        int currMonth = localDate.getMonth().getValue();

        Date nextMonthFirstDate;
        if (currMonth < 12) {
            nextMonthFirstDate = new org.joda.time.LocalDate(currYear, currMonth + 1, 1).toDate();
        }else {
            nextMonthFirstDate = new org.joda.time.LocalDate(currYear + 1, 1, 1).toDate();
        }

        int fromDate = Integer.parseInt(DateFormatUtils.format(new org.joda.time.LocalDate(currYear, currMonth, 1).toDate(), "yyyyMMdd"));
        int toDate = Integer.parseInt(DateFormatUtils.format(DateUtils.addDays(nextMonthFirstDate, -1), "yyyyMMdd"));
        int year = Integer.parseInt(String.valueOf(fromDate).substring(0, 4));
        int month = Integer.parseInt(String.valueOf(fromDate).substring(0, 6));
        int yearIndex = localDate.getMonth().getValue();


        int dailyV = fromDate;
        int weeklyV = 0;
        while (true) {
            Calendar instance = Calendar.getInstance();
            instance.setTime(sDate);
            if (instance.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                weeklyV = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
                break;
            }else {
                sDate = DateUtils.addDays(sDate, -1);
            }
        }

        if (weeklyV != rankDate) {
            weeklyV = 0;
        }else {
            weeklyV = fromDate;
        }

        int monthlyV = 0;
        if (rankDate == fromDate) {
            monthlyV = fromDate;
        } else {
            monthlyV = 0;
        }

        return constructEntity(rankDate, frequency, fromDate, toDate, year, month, yearIndex, dailyV, weeklyV, monthlyV);
    }


    private RGFrequencyConfigEntity buildBiMonthlyData(Date sDate, LocalDate localDate) {

        int rankDate = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
        int frequency = 60;

        int currYear = localDate.getYear();
        int currMonth = localDate.getMonth().getValue();

        boolean endFlg = false;
        if (currMonth%2 == 0) {
            endFlg = true;
        }

        Date start;
        Date nextMonthFirstDate;
        int yearIndex;
        if (endFlg) {
            start = new org.joda.time.LocalDate(currYear, currMonth - 1, 1).toDate();
            if (currMonth < 12) {
                nextMonthFirstDate = new org.joda.time.LocalDate(currYear, currMonth + 1, 1).toDate();
            }else {
                nextMonthFirstDate = new org.joda.time.LocalDate(currYear + 1, 1, 1).toDate();
            }
            yearIndex = currMonth/2;
        } else {
            start = new org.joda.time.LocalDate(currYear, currMonth, 1).toDate();
            int i = currMonth + 2;
            if (i - 12 > 0) {
                nextMonthFirstDate = new org.joda.time.LocalDate(currYear + 1, i - 12, 1).toDate();
            } else {
                nextMonthFirstDate = new org.joda.time.LocalDate(currYear, currMonth + 2, 1).toDate();
            }

            yearIndex = currMonth/2 + 1;
        }

        int fromDate = Integer.parseInt(DateFormatUtils.format(start, "yyyyMMdd"));
        int toDate = Integer.parseInt(DateFormatUtils.format(DateUtils.addDays(nextMonthFirstDate, -1), "yyyyMMdd"));
        int year = Integer.parseInt(String.valueOf(fromDate).substring(0, 4));
        int month = Integer.parseInt(String.valueOf(fromDate).substring(0, 6));

        int dailyV = fromDate;
        int weeklyV = 0;
        while (true) {
            Calendar instance = Calendar.getInstance();
            instance.setTime(sDate);
            if (instance.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                weeklyV = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
                break;
            }else {
                sDate = DateUtils.addDays(sDate, -1);
            }
        }

        if (weeklyV != rankDate) {
            weeklyV = 0;
        }else {
            weeklyV = fromDate;
        }

        int monthlyV = 0;
        if (rankDate == fromDate) {
            monthlyV = fromDate;
        } else {
            monthlyV = 0;
        }

        return constructEntity(rankDate, frequency, fromDate, toDate, year, month, yearIndex, dailyV, weeklyV, monthlyV);

    }

    private RGFrequencyConfigEntity buildQuarterData(Date sDate, LocalDate localDate) {

        int rankDate = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
        int frequency = 90;

        int currYear = localDate.getYear();
        int currMonth = localDate.getMonth().getValue();

        boolean endFlg = false;
        if (currMonth%3 == 0) {
            endFlg = true;
        }

        Date start;
        Date nextMonthFirstDate;
        int yearIndex;
        if (endFlg) {
            start = new org.joda.time.LocalDate(currYear, currMonth - 2, 1).toDate();
            if (currMonth < 12) {
                nextMonthFirstDate = new org.joda.time.LocalDate(currYear, currMonth + 1, 1).toDate();
            }else {
                nextMonthFirstDate = new org.joda.time.LocalDate(currYear + 1, 1, 1).toDate();
            }
            yearIndex = currMonth/3;
        } else {
            if (currMonth%3 > 1) {
                start = new org.joda.time.LocalDate(currYear, currMonth - 1, 1).toDate();
                int i = currMonth + 2;
                if (i - 12 > 0) {
                    nextMonthFirstDate = new org.joda.time.LocalDate(currYear + 1, i - 12, 1).toDate();
                } else {
                    nextMonthFirstDate = new org.joda.time.LocalDate(currYear, currMonth + 2, 1).toDate();
                }
            } else {
                start = new org.joda.time.LocalDate(currYear, currMonth, 1).toDate();
                int i = currMonth + 3;
                if (i - 12 > 0) {
                    nextMonthFirstDate = new org.joda.time.LocalDate(currYear + 1, i - 12, 1).toDate();
                } else {
                    nextMonthFirstDate = new org.joda.time.LocalDate(currYear, i, 1).toDate();
                }
            }
            yearIndex = currMonth/3 + 1;
        }

        int fromDate = Integer.parseInt(DateFormatUtils.format(start, "yyyyMMdd"));
        int toDate = Integer.parseInt(DateFormatUtils.format(DateUtils.addDays(nextMonthFirstDate, -1), "yyyyMMdd"));
        int year = Integer.parseInt(String.valueOf(fromDate).substring(0, 4));
        int month = Integer.parseInt(String.valueOf(fromDate).substring(0, 6));

        int dailyV = fromDate;
        int weeklyV = 0;
        while (true) {
            Calendar instance = Calendar.getInstance();
            instance.setTime(sDate);
            if (instance.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                weeklyV = Integer.parseInt(DateFormatUtils.format(sDate, "yyyyMMdd"));
                break;
            }else {
                sDate = DateUtils.addDays(sDate, -1);
            }
        }

        if (weeklyV != rankDate) {
            weeklyV = 0;
        }else {
            weeklyV = fromDate;
        }

        int monthlyV = 0;
        if (rankDate == fromDate) {
            monthlyV = fromDate;
        } else {
            monthlyV = 0;
        }


        return constructEntity(rankDate, frequency, fromDate, toDate, year, month, yearIndex, dailyV, weeklyV, monthlyV);
    }

}
