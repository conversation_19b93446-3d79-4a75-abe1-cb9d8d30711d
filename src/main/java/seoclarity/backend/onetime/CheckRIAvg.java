package seoclarity.backend.onetime;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.RIDailyRankingService;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @date 2020-11-10
 * @path seoclarity.backend.onetime.CheckRIAvg
 * 
 */
public class CheckRIAvg {
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private RIDailyRankingService service;
	
	static int[] oids = new int[] {
			4739,9692,551,4744,4745,5069,4736,4748,4752,4763,4762,4731,4730,4761,4754,7079,4747,552,4729,4732,8443,4746,4735,4743,4753,4749,561,555,4751,4750,553,4756,5070,9180,9454,9178,4733,562,4755,9285,4728,4348,556,4764,9284,7196,6373,4769,1901,4742
	};
	public static void main(String[] args) {
		CheckRIAvg INS = new CheckRIAvg();
		INS.process();
	}
	
	private void process() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		service = new RIDailyRankingService(true);
		
		for (int oid : oids) {
			OwnDomainEntity domain = ownDomainEntityDAO.getById(oid);
			int engine = ScKeywordRankManager.getSearchEngineId(domain);
			int language = ScKeywordRankManager.getSearchLanguageId(domain);
			
			String sql = createSql(engine, language, oid, StringUtils.removeStart(domain.getDomain(), "www."));
			List<Map<String, Object>> list = service.queryForAll(sql);
			
			for (Map<String, Object> map : list) {
				String ranking_date = map.get("ranking_date").toString();
				String avgRankWith101 = map.get("avgRankWith101").toString();
				String avgRankWithout101 = map.get("avgRankWithout101").toString();
				String cntWith101 = map.get("cntWith101").toString();
				String cntWithout101 = map.get("cntWithout101").toString();
				
				System.out.println(oid + "," + ranking_date  + "," + cntWith101 + "," + cntWithout101 + "," + avgRankWith101 + "," + avgRankWithout101);
			}
		}
		
	}
	
	private String createSql(int engine, int language, int oid, String domainName) {
		StringBuffer sql = new StringBuffer();
		String rootDomain = ClarityDBUtils.getRootDomain(domainName);
		
		String infoT = "d_ranking_info_" + ((engine == 1 && language == 1) ? "us" : "intl") + "_202011";
		String detailT = "d_ranking_detail_" + ((engine == 1 && language == 1) ? "us" : "intl") + "_202011";
		
		sql.append(" SELECT                                                                                                                                             ");
		sql.append("     ranking_date,                                                                                                                                  ");
		sql.append("     round(avg(rank), 2) AS avgRankWith101,                                                                                                         ");
		sql.append("     round(sum(if(rank < 101, rank, 0)) / sum(if(rank < 101, 1, 0)), 2) AS avgRankWithout101,                                                       ");
		sql.append("     count() AS cntWith101,                                                                                                                         ");
		sql.append("     sum(if(rank < 101, 1, 0)) AS cntWithout101                                                                                                     ");
		sql.append(" FROM                                                                                                                                               ");
		sql.append(" (                                                                                                                                                  ");
		sql.append("     SELECT                                                                                                                                         ");
		sql.append("         ranking_date,                                                                                                                              ");
		sql.append("         engine_id,                                                                                                                                 ");
		sql.append("         language_id,                                                                                                                               ");
		sql.append("         keyword_rankcheck_id,                                                                                                                      ");
		sql.append("         if(web_rank = 0, 101, web_rank) AS rank                                                                                                    ");
		sql.append("     FROM                                                                                                                                           ");
		sql.append("     (                                                                                                                                              ");
		sql.append("         SELECT                                                                                                                                     ");
		sql.append("             engine_id,                                                                                                                             ");
		sql.append("             language_id,                                                                                                                           ");
		sql.append("             keyword_rankcheck_id,                                                                                                                  ");
		sql.append("             ranking_date                                                                                                                           ");
		sql.append("         FROM " + infoT + "                                                                                                            ");
		sql.append("         WHERE (own_domain_id = " + oid + ") AND (location_id = 0)                                                                                         ");
		sql.append("         AND engine_id = " + engine + " AND language_id = " + language + " ");
		sql.append("         AND ranking_date >= '2020-11-07' ");
		sql.append("         AND ((engine_id, language_id, keyword_rankcheck_id) GLOBAL IN                                                                              ");
		sql.append("         (                                                                                                                                          ");
		sql.append("             SELECT                                                                                                                                 ");
		sql.append("                 engine_id,                                                                                                                         ");
		sql.append("                 language_id,                                                                                                                       ");
		sql.append("                 keyword_rankcheck_id                                                                                                               ");
		sql.append("             FROM " + infoT + "                                                                                                        ");
		sql.append("             WHERE (own_domain_id = " + oid + ") AND (location_id = 0) AND (ranking_date = '2020-11-09')                                                   ");
		sql.append("             AND engine_id = " + engine + " AND language_id = " + language + " ");
		sql.append("         ))                                                                                                                                         ");
		sql.append("     )                                                                                                                                              ");
		sql.append("     ANY LEFT JOIN                                                                                                                                  ");
		sql.append("     (                                                                                                                                              ");
		sql.append("         SELECT                                                                                                                                     ");
		sql.append("             engine_id,                                                                                                                             ");
		sql.append("             language_id,                                                                                                                           ");
		sql.append("             keyword_rankcheck_id,                                                                                                                  ");
		sql.append("             web_rank,                                                                                                                              ");
		sql.append("             ranking_date                                                                                                                           ");
		sql.append("         FROM " + detailT + "                                                                                                          ");
		sql.append("         WHERE (own_domain_id = " + oid + ") AND (location_id = 0) AND (web_rank > 0)             ");
		if (rootDomain.equals(domainName)) {
			sql.append(" and root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "' and hrrd = 1                                               ");
		} else {
			sql.append(" and root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "'  and domain_reverse = '" + StringUtils.reverseDelimited(domainName, '.') + "' and hrd = 1                                               ");
		}
		sql.append("         AND engine_id = " + engine + " AND language_id = " + language + " ");
		sql.append("         AND ranking_date >= '2020-11-07' ");
		sql.append("         AND ((engine_id, language_id, keyword_rankcheck_id) GLOBAL IN                                                                              ");
		sql.append("         (                                                                                                                                          ");
		sql.append("             SELECT                                                                                                                                 ");
		sql.append("                 engine_id,                                                                                                                         ");
		sql.append("                 language_id,                                                                                                                       ");
		sql.append("                 keyword_rankcheck_id                                                                                                               ");
		sql.append("             FROM d_ranking_info_intl_202011                                                                                                        ");
		sql.append("             WHERE (own_domain_id = " + oid + ") AND (location_id = 0) AND (ranking_date = '2020-11-09')                                                   ");
		sql.append("         ))                                                                                                                                         ");
		sql.append("     ) USING (engine_id, language_id, keyword_rankcheck_id, ranking_date)                                                                           ");
		sql.append(" )                                                                                                                                                  ");
		sql.append(" GROUP BY ranking_date                                                                                                                              ");

		return sql.toString();
	}

}
