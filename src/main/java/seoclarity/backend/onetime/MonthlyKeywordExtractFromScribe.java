package seoclarity.backend.onetime;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.summary.BaseScribeSummary;

import java.io.*;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-09-28 9:53
 *
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.MonthlyKeywordExtractFromScribe" -Dexec.cleanupDaemonThreads=false -Dexec.args="/dat/scribeKeyword/ 2018-09 1#1 1"
 **/
@CommonsLog
public class MonthlyKeywordExtractFromScribe extends BaseScribeSummary {
    private Gson gson = new Gson();
    private CSVPrinter csvPrinter;

    public void close() throws IOException {
        csvPrinter.flush();
        csvPrinter.close();
    }

    public MonthlyKeywordExtractFromScribe() throws IOException {
        CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',').withHeader("keyword", "recommend", "engine", "language", "searchVol");
        csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter("/home/<USER>/keywordEx/keywordEx.csv")), csvFormat);
    }

    public static void main(String[] args) throws IOException {
        baseLocation = args[0];
        processDateString = args[1];
        String engineLanguageString = args[2];
        threadCount = NumberUtils.toInt(args[3], 1);
        String[] engines = engineLanguageString.split(",");
        if (ArrayUtils.isEmpty(engines)) {
            log.error("Empty engines!!!");
            return;
        }
        MonthlyKeywordExtractFromScribe monthlyKeywordExtractFromScribe = new MonthlyKeywordExtractFromScribe();
        monthlyKeywordExtractFromScribe.start(engines);
        monthlyKeywordExtractFromScribe.close();

    }

    @Override
    protected String getScribeFolder(int engine, int language) {
        return String.format("monthly_keyword_expansion_commoncrawl_keywordRank_%d_%d", engine, language);
    }

    @Override
    protected void processFile(File file, int engineId, int languageId) {
        log.info("Process file : "+file.getAbsolutePath()+" Start.");
        List<String> lines;
        try {
            lines = IOUtils.readLines(new FileInputStream(file));
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }
        if (CollectionUtils.isEmpty(lines)) {
            log.info("Empty file, exit.");
            return;
        }
        log.info("total count : "+lines.size());
        for (String line : lines) {
            KeywordRankVO keywordRankVO = gson.fromJson(line, KeywordRankVO.class);
            if (keywordRankVO == null) {
                log.error("vo is empty :" + line);
                continue;
            }
            try {
                csvPrinter.printRecord(
                        keywordRankVO.getKeyword(),
                        StringUtils.isBlank(keywordRankVO.getGoogleRecommend()) ? "-" : keywordRankVO.getGoogleRecommend(),
                        engineId,
                        languageId,
                        keywordRankVO.getSearchVol()
                );
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
