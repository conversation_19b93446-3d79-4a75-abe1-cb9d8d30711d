package seoclarity.backend.onetime.dummydata;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.utils.SpringBeanFactory;

public class UploadBwmDataFor4661Onetime {
	
	private GscBaseDao gscBaseDao;

	public UploadBwmDataFor4661Onetime() {
		// TODO Auto-generated constructor stub
		
		gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
	}

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		UploadBwmDataFor4661Onetime uploadBwmDataFor4661Onetime = new UploadBwmDataFor4661Onetime();
		uploadBwmDataFor4661Onetime.process("/home/<USER>/bwm_keyword.txt", 2);
		uploadBwmDataFor4661Onetime.process("/home/<USER>/bwm_url.txt", 3);
	}
	
	private void process(String filePath, Integer type) {
		
		
		try {
    		String content = "";
    		String day = "";
    		String date = "";
    		List<String[]> uploadList = new ArrayList<String[]>();
			BufferedReader bf = new BufferedReader(new FileReader(filePath));
			while (content != null) {
				content = bf.readLine();
				
				if (StringUtils.startsWith(content, "Date")) {
					System.out.println("=== skip header");
					continue;
				}

				if (content == null) {
					break;
				}
				
				String[] array = StringUtils.split(content, "\t");
				
				if (array == null) {
					System.out.println("==array is empty!");
					continue;
				}
				
				if (array.length != 6) {
					System.out.println("==length:" + array.length);
					continue;
				}
				
	        	day = StringUtils.split(array[0], "-")[0];
	        	date = "2025-02-" + (day.length() == 1 ? "0" : "") + day;
	        	
	        	array[0] = date;
	        	
	        	array[1] = StringUtils.replace(StringUtils.replace(array[1], "\"", ""), ",", "");
	        	array[2] = StringUtils.replace(StringUtils.replace(array[2], "\"", ""), ",", "");
	        	array[3] = StringUtils.replace(StringUtils.replace(array[3], "\"", ""), ",", "");
	        	array[5] = StringUtils.replace(StringUtils.replace(array[5], "\"", ""), ",", "");
	        	Float avgPos = NumberUtils.toFloat(array[5]);
	        	if (avgPos <= 1) {
	        		array[5] = "1";
				} else {
					int rank =  (int) NumberUtils.toFloat(array[5]);
					array[5] = String.valueOf(rank);
				}
	        	
				uploadList.add(array);
				
			}
			
			bf.close();
			
			System.out.println("uploadList size:" + uploadList);
			
			//2:keyword  3:url
			gscBaseDao.insertForBatchBwm(uploadList, "dummy_data_4661_20250415_scott", type, 4661, 13943);
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	

}
