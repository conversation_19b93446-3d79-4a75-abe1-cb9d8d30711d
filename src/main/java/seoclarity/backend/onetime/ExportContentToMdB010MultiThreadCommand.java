package seoclarity.backend.onetime;

import seoclarity.backend.dao.actoniamonitor.ContentIdeaUploadMonitorDAO;
import seoclarity.backend.dao.actoniamonitor.ContentIdeaUploadTitleKeywordMonitorDAO;
import seoclarity.backend.dao.actoniamonitor.ContentIdeaUploadTitleUrlMonitorDAO;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.utils.SpringBeanFactory;

public class ExportContentToMdB010MultiThreadCommand extends BaseThreadCommand {
	
	private String ip;
	
	private String version;
	
	private Integer type;
	
	private String[] commandData;
	
	private ContentIdeaUploadMonitorDAO contentIdeaUploadMonitorDAO;
	private ContentIdeaUploadTitleKeywordMonitorDAO contentIdeaUploadTitleKeywordMonitorDAO;
	private ContentIdeaUploadTitleUrlMonitorDAO contentIdeaUploadTitleUrlMonitorDAO;
	
	
	public ExportContentToMdB010MultiThreadCommand(String ip, String[] commandData, Integer type,
			 String version) {
		
		this.ip = ip;
		this.commandData = commandData;
		this.version = version;
		this.type = type;
		contentIdeaUploadMonitorDAO = SpringBeanFactory.getBean("contentIdeaUploadMonitorDAO");
		contentIdeaUploadTitleKeywordMonitorDAO = SpringBeanFactory.getBean("contentIdeaUploadTitleKeywordMonitorDAO");
		contentIdeaUploadTitleUrlMonitorDAO = SpringBeanFactory.getBean("contentIdeaUploadTitleUrlMonitorDAO");
		
	}
	
	private void loadToMdB010() throws Exception {
		if (type == 1) {
			contentIdeaUploadMonitorDAO.insertForBatch(commandData, version);
		} else if (type == 2) {
			contentIdeaUploadTitleKeywordMonitorDAO.insertForBatch(commandData, version);
		} else if (type == 3) {
			contentIdeaUploadTitleUrlMonitorDAO.insertForBatch(commandData, version);
		}

	}

	@Override
	protected void execute() throws Exception {
		long a = System.currentTimeMillis();
		System.out.println("Start command IP: " + ip);
		
		loadToMdB010();
		
		long b = System.currentTimeMillis();
		System.out.println("End command IP: " + ip + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
		CacheModleFactory.getInstance().setAliveIpAddress(ip);
		
		
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
		
	}

}
