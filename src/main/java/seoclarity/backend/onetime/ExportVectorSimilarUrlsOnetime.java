package seoclarity.backend.onetime;

import org.apache.commons.io.FileUtils;
import seoclarity.backend.dao.clickhouse.clarity360.lweb01.Clarity360Lweb01DAO;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * ewain202307
 */
// mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.ExportVectorSimilarUrlsOnetime" -Dexec.args="" -Dexec.cleanupDaemonThreads=false
public class ExportVectorSimilarUrlsOnetime {

    private Clarity360Lweb01DAO clarity360Lweb01DAO;
    public ExportVectorSimilarUrlsOnetime(){
        clarity360Lweb01DAO = SpringBeanFactory.getBean("clarity360Lweb01DAO");
    }

    public static void main(String[] args) {
        ExportVectorSimilarUrlsOnetime exportVectorSimilarUrlsOnetime = new ExportVectorSimilarUrlsOnetime();
        exportVectorSimilarUrlsOnetime.process();
    }

    private void process() {
        File top_6_similar_urls_and_dist = new File("/home/<USER>/top_6_similar_urls_and_dist.txt");
        File top_6_similar_urls = new File("/home/<USER>/top_6_similar_urls.txt");
        List<String> urls = clarity360Lweb01DAO.getUrls();
        int cnt =0;
        for (String url : urls) {
            cnt++;
            List<Map<String, Object>> urlAndDist = clarity360Lweb01DAO.getUrlAndDist(url);
            StringBuilder urlA = new StringBuilder("[");
            StringBuilder distA = new StringBuilder("[");
            for (int i = 0; i < urlAndDist.size(); i++) {
                Map<String, Object> stringObjectMap = urlAndDist.get(i);
                String newUrl = stringObjectMap.get("url").toString();
                Object dist = stringObjectMap.get("dist");
                float distVal = Float.parseFloat(dist.toString());
                if (i != 5) {
                    urlA.append("'").append(newUrl).append("',");
                    distA.append(distVal).append(",");
                } else {
                    urlA.append("'").append(newUrl).append("'");
                    distA.append(distVal);
                }
            }
            urlA.append("]");
            distA.append("]");
            String res = urlA + "\t" + distA;
            try {
                FileUtils.write(top_6_similar_urls_and_dist, url+"\t"+res+"\n", "UTF-8", true);
                FileUtils.write(top_6_similar_urls, urlA+"\n", "UTF-8", true);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        System.out.println("cnt: "+ cnt);
    }
}
