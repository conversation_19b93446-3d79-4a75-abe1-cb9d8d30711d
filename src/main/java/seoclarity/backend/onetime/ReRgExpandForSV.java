package seoclarity.backend.onetime;

import seoclarity.backend.upload.Utils.WriteUtils;
import seoclarity.backend.utils.CSVParser;
import java.io.*;
import java.net.URLDecoder;
import java.util.LinkedList;
import java.util.List;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.onetime
 * @author: cil
 * @date: 2021-07-20 17:55
 **/
public class ReRgExpandForSV {
    static final String OLD_RESULT = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/rgExpandForSV_20210719_result.txt";
    static final String NEW_RESULT = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/rgExpandForSV_20210720_result.txt";
    static final String FOR_SVFILE = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/us_rgExpandForSV_0720.txt";
    static final   CSVParser parser = new CSVParser(',');
    public static void main(String[] args) {
        List<String> strings = readForSVFile(FOR_SVFILE);


        List<String> old = readFile(OLD_RESULT);
        String head = old.get(0);
        old.remove(0);

        WriteUtils.write(NEW_RESULT,head);
        for (String line:
             old) {
            try {
                String[] parseLine = parser.parseLine(line);
                if (parseLine.length>=3 ){
                    if (strings .contains(parseLine[2]))
                        WriteUtils.write(NEW_RESULT,line);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }







    }
    private static List<String> readForSVFile(String path) {
        List<String> dataList = new LinkedList<>();

        File fin = new File(path);
        BufferedReader br = null;
        try {
            FileInputStream fis = new FileInputStream(fin);
            //Construct BufferedReader from InputStreamReader
            br = new BufferedReader(new InputStreamReader(fis));
            String line;
            while ((line = br.readLine()) != null) {
                String[] strings = parser.parseLine(line);
                dataList.add(URLDecoder.decode(strings[0], "UTF-8"));

            }
            return dataList;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;


    }




    private static List<String> readFile(String path) {
        List<String> dataList = new LinkedList<>();

        File fin = new File(path);
        BufferedReader br = null;
        try {
            FileInputStream fis = new FileInputStream(fin);
            //Construct BufferedReader from InputStreamReader
            br = new BufferedReader(new InputStreamReader(fis));
            String line;
            while ((line = br.readLine()) != null) {

                dataList.add(line);

            }
            return dataList;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;


    }



}
