package seoclarity.backend.onetime;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.poc02.Poc02ClarityDBEntityDAO;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.utils.SpringBeanFactory;

// mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.Poc02ShaHashTest" -Dexec.args=""
public class Poc02ShaHashTest {
	
	private Poc02ClarityDBEntityDAO poc02ClarityDBEntityDAO;
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	

	public Poc02ShaHashTest() {
		poc02ClarityDBEntityDAO = SpringBeanFactory.getBean("poc02ClarityDBEntityDAO");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
	}

	public static void main(String[] args) {
		
		Poc02ShaHashTest Poc02ShaHashTest = new Poc02ShaHashTest();
		
//		if (args != null && args.length >= 0) {
//			
//		}
		Poc02ShaHashTest.process();
	}
	
	private static final String tableName = "sha_hash_kwd";
	
	private void process() {
////		Map<String, List<ClickHouseArray>> resultMap = new HashMap<>();
//		
//		List<CLRankingDetailEntity> keywordUrlList = clDailyRankingEntityDao.getKeywordUrlForPocTestV1(1, 1, false, "2024-01-20", 256);
////		for(CLRankingDetailEntity clRankingDetailEntity : keywordUrlList) {
////			clRankingDetailEntity.getKeywordName();
////			clRankingDetailEntity.getDateArray();
////			String keywordName = clRankingDetailEntity.getKeywordName();
////			
////			List<String> hash512List = poc02ClarityDBEntityDAO.getHashByFunctionName("hex(SHA512(x))", clRankingDetailEntity.getDateArray());
////			List<String> hash1List = poc02ClarityDBEntityDAO.getHashByFunctionName("hex(SHA1(x))", clRankingDetailEntity.getDateArray());
////			List<String> hash224List = poc02ClarityDBEntityDAO.getHashByFunctionName("hex(SHA224(x))", clRankingDetailEntity.getDateArray());
////			
////			List<ClickHouseArray> resultList = new ArrayList<>();
////			resultList.add(new ClickHouseArray(ClickHouseDataType.String, hash1List.stream().toArray(String[]::new)));
////			resultList.add(new ClickHouseArray(ClickHouseDataType.String, hash224List.stream().toArray(String[]::new)));
////			resultList.add(new ClickHouseArray(ClickHouseDataType.String, hash512List.stream().toArray(String[]::new)));
////			
////			
////			resultMap.put(keywordName, resultList);
////		}
//		System.out.println("resultMap size:" + keywordUrlList.size());
		
		List<CLRankingDetailEntity> keywordUrlList = new ArrayList<>();
		try {
			BufferedReader in = new BufferedReader(new FileReader("/home/<USER>/svwith10.txt"));
			String str;
			int num = 1;
			while ((str = in.readLine()) != null) {
				System.out.println("processing on :" + num);
				long startTime = System.currentTimeMillis();
				num++;
				String[] arrays = StringUtils.split(str, "\t");
				if (arrays.length != 2) {
					System.out.println("ERROR: " + str);
					continue;
				} else {
					CLRankingDetailEntity CLRankingDetailEntity = new CLRankingDetailEntity();
					CLRankingDetailEntity.setKeywordName(arrays[0]);
					CLRankingDetailEntity.setUrl(arrays[1]);
					
					CLRankingDetailEntity.setTrueRank(num);
					keywordUrlList.add(CLRankingDetailEntity);
				}

				if (num % 10000 == 0) {
					System.out.println("processing on line :" + num);
				}
				long endTimeo = System.currentTimeMillis();
				int elapsedSecondos = (int) (endTimeo - startTime);
				System.out.println("cost: " + elapsedSecondos + " sec");

			}
			in.close();
			System.out.println("resultMap size:" + keywordUrlList.size());
			
			poc02ClarityDBEntityDAO.insertForBatch(keywordUrlList, tableName);
			
		} catch (Exception e) {
			// TODO: handle exception
		}
		 
		
		
	}

}
