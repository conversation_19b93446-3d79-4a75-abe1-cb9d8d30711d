package seoclarity.backend.onetime;

import com.alibaba.fastjson.JSONArray;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class TopicExplorerSv {

    private static final char[] ILLEGAL_CHARACTERS = new char[]{
        '¡', '¢', '£', '¤', '¥', '¦', '§', '¨', '©', 'ª', '«', '¬', '®', '¯', '–', '—',
                '‘', '’', '‚', '“', '”', '„', '†', '‡', '•', '…', '‰', '€', '™', '°', '±',
                '²', '³', '´', 'µ', '¶', '·', '¸', '¹', 'º', '»', '¼', '½', '¾', '¿', '�', '×'};
    // FormatUtils.containIllegalCharacter(keywordName)

    private static final String[] SPLIT_ARR = new String[]{"-", "/", ",", "+", ".", "'", "#", "&", "`", "?", "(", ")", "[", "]", "\\"};
    private static final Pattern pattern = Pattern.compile("-?\\d+(\\.\\d+)?");
    private static final List<String> SPLIT_LIST = Arrays.asList(SPLIT_ARR);
    private static final int CDB_EX_RETRY_CNT = 2;

    private Map<String, String> skwMap;
    private String errorFilePath;

    private static final Gson gson = new Gson();

    private static String outputFilePath;
    private static final String UNION = " UNION ALL ";
    private GscClickSteamDAO gscClickSteamDAO;
    private int cnt;
    private int totalSize = 0;

    public TopicExplorerSv() {
        gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
        skwMap = new HashMap<>();
    }

    public static void main(String[] args) {
        if (args == null || args.length < 2) {
            System.out.println("params error");
            return;
        }
        String filePath = args[0];
        outputFilePath = args[1];
        new TopicExplorerSv().startProcess(filePath);
    }

    private File initOutputFile() {
        File file = new File(outputFilePath + "/result_" + System.currentTimeMillis() + ".txt");
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            FileUtils.writeLines(file, "UTF-8", Collections.singleton("rawKeyword,keyword_name,avg_search_volume"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        File errorFile = new File(outputFilePath + "/error_" + System.currentTimeMillis() + ".txt");
        if (!errorFile.exists()) {
            try {
                errorFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        errorFilePath = errorFile.getAbsolutePath();
        System.out.println("==errorKwPath:" + errorFilePath);

        return file;
    }

    private void outputRes(List<String> res, File outputFile) {
        if (res == null || res.isEmpty()) {
            return;
        }
        totalSize += res.size();
        try {
            FileUtils.writeLines(outputFile, "UTF-8", res, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void startProcess(String filePath) {
        File outputFile = initOutputFile();
        Map<String, List<String>> kwMap = getKwPArrMap(filePath);
        if (kwMap.isEmpty()) {
            return;
        }

        int index = 0;
        StringBuilder subSql = new StringBuilder();
        List<String> kwListBatch = new ArrayList<>();
        for (String sourceKw : kwMap.keySet()) {
            if (index >= 5) {
                outputRes(queryRes(subSql.toString(), kwListBatch), outputFile);
                index = 0;
                subSql.setLength(0);
                kwListBatch.clear();
            }
            if (index > 0) {
                subSql.append(UNION);
            }
            kwListBatch.add(skwMap.get(sourceKw));
            subSql.append("select '").append(sourceKw).append("' as rawKeyword, keyword_name, round(avg_search_volume,0) as avg_search_volume from gsc_clickstream.dis_keyword_summary_annual_big_v2 where country_cd='usa' ");
            List<String> kwList = kwMap.get(sourceKw);
            for (String kw : kwList) {
                subSql.append(" and match(keyword_name, '").append(kw).append("') ");
            }
            subSql.append(" and impressions>=500 order by impressions desc limit 300 ");
            index++;
        }
        outputRes(queryRes(subSql.toString(), kwListBatch), outputFile);
        System.out.println("===queryTime:" + cnt + ", exportSize:" + totalSize);
    }

    private Map<String, List<String>> getKwPArrMap(String filePath) {
        Map<String, List<String>> resultMap = new TreeMap<>();
        List<String> sourceLines = null;
        try {
            sourceLines = FileUtils.readLines(new File(filePath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (sourceLines == null || sourceLines.isEmpty()) {
            return null;
        }
        int totalKw = sourceLines.size();
        int skipSourceKw = 0;
        for (String sourceKw : sourceLines) {
            if (StringUtils.isBlank(sourceKw) || sourceKw.length() == 1) {
                skipSourceKw++;
                System.out.println("===skipSourceKw: "+sourceKw);
                continue;
            }
            String tmpSourceKw = sourceKw;
            List<String> kwList = new ArrayList<>();
            String lowerSourceKw = sourceKw.trim().toLowerCase();

            if (FormatUtils.containIllegalCharacter(lowerSourceKw)) {
                for (char illegalCharacter : ILLEGAL_CHARACTERS) {
                    if (lowerSourceKw.contains(illegalCharacter + "")) {
                        lowerSourceKw = lowerSourceKw.replaceAll(illegalCharacter + "", "");
                    }
                }
            }

            for (String splitStr : SPLIT_LIST) {
                if (StringUtils.containsIgnoreCase(lowerSourceKw, splitStr)) {
                    lowerSourceKw = StringUtils.replace(lowerSourceKw, splitStr, " ");
                }
            }

            String[] split1 = lowerSourceKw.split(" ");
            for (String kw : split1) {
                if (isSkip(kw)) {
                    continue;
                }
                kw = kw.trim();
                if (kwList.contains(kw)) {
                    continue;
                }
                kwList.add(kw);
            }
            sourceKw = checkSpecialChar(sourceKw);
            if (StringUtils.containsIgnoreCase(sourceKw, "'")) {
                int i = sourceKw.indexOf("'");
                String newSourceKw = sourceKw.substring(0, i) + "\\" + sourceKw.substring(i);
                resultMap.put(newSourceKw, kwList);
                skwMap.put(newSourceKw, tmpSourceKw);
            } else {
                resultMap.put(sourceKw, kwList);
                skwMap.put(sourceKw, tmpSourceKw);
            }
        }
        System.out.println("totalKw:" + totalKw + ", skipSourceKw:" + skipSourceKw + ", resultMap size:" + resultMap.size() + ", skwMap size:" + skwMap.size());
        return resultMap;
    }

    private String checkSpecialChar(String str) {
        if (StringUtils.containsIgnoreCase(str, "\\")) {
            return StringUtils.replace(str, "\\", "\\\\");
        } else {
            return str;
        }
    }

    private List<String> queryRes(String sql, List<String> kwList) {
        try {
            if (StringUtils.isBlank(sql)) {
                return null;
            }
            cnt++;
            String finalSql = "select * from (" + sql + ") order by rawKeyword, avg_search_volume";
//            System.out.println("finalSql:" + finalSql + ";");
            long start = System.currentTimeMillis();
            List list = null;
            try {
                list = getCdbResWithRetry(finalSql, kwList);
            } catch (Exception e) {
                list = null;
                System.out.println("===queryError sql:" + finalSql);
                e.printStackTrace();
            }
            long useTime = System.currentTimeMillis() - start;
            System.out.println("===useTime:" + (useTime / 1000) + "s");
            if (list == null || list.isEmpty()) {
                return null;
            }
            System.out.println("====currentSie:" + list.size());
            String json = gson.toJson(list);
            List<Map> maps = JSONArray.parseArray(json, Map.class);
            return maps.stream().map(map -> {
                String rawKeyword = map.get("rawKeyword").toString();
                String keywordName = map.get("keyword_name").toString();
                String demand = map.get("avg_search_volume").toString();
                demand = new BigDecimal(demand).setScale(0, RoundingMode.HALF_UP).toString();
                return rawKeyword + "\t" + keywordName + "\t" + demand;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            System.out.println("===genOutputResError:" + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    private List getCdbResWithRetry(String sql, List<String> kwList) {
        List res = null;
        int currentRetryCount = 0;
        while (true) {
            try {
                res = gscClickSteamDAO.queryForListFromClarityDB(sql);
                break;
            } catch (Exception e) {
                currentRetryCount++;
                System.out.println("====queryError retryCnt:" + currentRetryCount + " errorMsg:" + e.getMessage());
                if (currentRetryCount > CDB_EX_RETRY_CNT) {
                    System.out.println("==queryCdbErrAfterRetry retryCnt:" + currentRetryCount + " sql:" + sql);
                    e.printStackTrace();
                    try {
                        FileUtils.writeLines(new File(errorFilePath), "UTF-8", kwList, true);
                    } catch (IOException ex) {
                        System.out.println("====outputErrorkw:" + StringUtils.join(kwList, ","));
                        ex.printStackTrace();
                    }
                    throw new RuntimeException(e);
                }
                try {
                    TimeUnit.MINUTES.sleep(currentRetryCount * 2L);
                } catch (InterruptedException ex) {
                    ex.printStackTrace();
                }
            }
        }
        return res;
    }

    private boolean isNumeric(String str) {
        return pattern.matcher(str).matches();
    }

    private boolean isSkip(String str) {
        boolean skip = false;
        if (StringUtils.isBlank(str)) {
            skip = true;
        }
        String trim = str.trim();
        if (isNumeric(trim)) {
            skip = true;
        }

        if (trim.length() == 1) {
            skip = true;
        }
        return skip;
    }


}
