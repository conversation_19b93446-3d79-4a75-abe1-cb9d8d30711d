package seoclarity.backend.onetime;

import org.apache.commons.io.FileUtils;
import seoclarity.backend.dao.actonia.Utf8MbxTest1Dao;
import seoclarity.backend.dao.actonia.Utf8MbxTest2Dao;
import seoclarity.backend.entity.actonia.Utf8MbxTestEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.List;

// mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.TestCharsetOnetime" -Dexec.cleanupDaemonThreads=false -Dexec.args=""
public class TestCharsetOnetime {

    public Utf8MbxTest1Dao utf8MbxTest1Dao;
    public Utf8MbxTest2Dao utf8MbxTest2Dao;

    public static final String FILE_NAME1 = "/home/<USER>/charsetTest/table1.txt";
    public static final String FILE_NAME2 = "/home/<USER>/charsetTest/table2.txt";

    public static final String FILE_NAME3 = "/home/<USER>/charsetTest/table1_2.txt";
    public static final String FILE_NAME4 = "/home/<USER>/charsetTest/table2_2.txt";

    public static final String FILE_NAME5 = "/home/<USER>/charsetTest/table1_3.txt";
    public static final String FILE_NAME6 = "/home/<USER>/charsetTest/table2_4.txt";


    public TestCharsetOnetime() {
        utf8MbxTest1Dao = SpringBeanFactory.getBean("utf8MbxTest1Dao");
        utf8MbxTest2Dao = SpringBeanFactory.getBean("utf8MbxTest2Dao");
    }

    public static void main(String[] args) {
        new TestCharsetOnetime().process();
    }

    private void process() {

        //select
        try {
            List<Utf8MbxTestEntity> all1 = utf8MbxTest1Dao.getAll();
            System.out.println("all1: "+all1.toString());
            FileUtils.write(new File(FILE_NAME1), all1.toString(), "UTF-8");
            List<Utf8MbxTestEntity> all2 = utf8MbxTest2Dao.getAll();
            System.out.println("all2: "+all2.toString());
            FileUtils.write(new File(FILE_NAME2), all2.toString(), "UTF-8");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        //insert1
        Utf8MbxTestEntity Utf8MbxTestEntity1_1 = new Utf8MbxTestEntity();
        Utf8MbxTestEntity1_1.setEmoji("a☏b001");
        Utf8MbxTestEntity1_1.setColMb3("b☏a001");
        utf8MbxTest1Dao.insert(Utf8MbxTestEntity1_1);
        Utf8MbxTestEntity Utf8MbxTestEntity1_2 = new Utf8MbxTestEntity();
        Utf8MbxTestEntity1_2.setEmoji("a☏b002");
        Utf8MbxTestEntity1_2.setColMb3("b☏a002");
        utf8MbxTest2Dao.insert(Utf8MbxTestEntity1_2);


        //select
        try {
            List<Utf8MbxTestEntity> all1 = utf8MbxTest1Dao.getAll();
            System.out.println("all1_2: "+all1.toString());
            FileUtils.write(new File(FILE_NAME3), all1.toString(), "UTF-8");
            List<Utf8MbxTestEntity> all2 = utf8MbxTest2Dao.getAll();
            System.out.println("all2_2: "+all2.toString());
            FileUtils.write(new File(FILE_NAME4), all2.toString(), "UTF-8");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        //insert2
        Utf8MbxTestEntity Utf8MbxTestEntity2_1 = new Utf8MbxTestEntity();
        Utf8MbxTestEntity2_1.setEmoji("a\uD83D\uDE0Bb001");
        Utf8MbxTestEntity2_1.setColMb3("b☏a001");
        utf8MbxTest1Dao.insert(Utf8MbxTestEntity2_1);
        Utf8MbxTestEntity Utf8MbxTestEntity2_2 = new Utf8MbxTestEntity();
        Utf8MbxTestEntity2_2.setEmoji("a\uD83D\uDE0Bb002");
        Utf8MbxTestEntity2_2.setColMb3("b☏a002");
        utf8MbxTest2Dao.insert(Utf8MbxTestEntity2_2);


        //select
        try {
            List<Utf8MbxTestEntity> all1 = utf8MbxTest1Dao.getAll();
            System.out.println("all1_2: "+all1.toString());
            FileUtils.write(new File(FILE_NAME5), all1.toString(), "UTF-8");
            List<Utf8MbxTestEntity> all2 = utf8MbxTest2Dao.getAll();
            System.out.println("all2_2: "+all2.toString());
            FileUtils.write(new File(FILE_NAME6), all2.toString(), "UTF-8");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }



    }
}
