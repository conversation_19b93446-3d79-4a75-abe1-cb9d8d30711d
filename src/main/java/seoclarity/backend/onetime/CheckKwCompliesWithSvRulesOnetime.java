package seoclarity.backend.onetime;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class CheckKwCompliesWithSvRulesOnetime {

    public static void main(String[] args) {
        CheckKwCompliesWithSvRulesOnetime tool = new CheckKwCompliesWithSvRulesOnetime();
        tool.process();
    }
    private void process() {
        int svEqualOrGreaterThan10Cnt = 0;
        int svLessThan10Cnt = 0;
        String fileName = "E:\\work\\sync-bd\\BaiduSyncdisk\\seoClarity\\ticket\\20230824 RG expansion upload\\202324 upload rgkw\\expansion_9_10_resultSv.txt";
        List<String> lines = null;
        try {
            lines = FileUtils.readLines(new File(fileName), "UTF-8");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        for (String line : lines) {
            String[] split = line.split("\t");
            /**
             * [root@scripts15 groupSvResult2023-08-28]# head -10 expansion_1_1_resultSv.txt
             * 1 bedroom house kits    2840    50      1.2     100.0   202307=30       202306=40       202305=70       202304=110      202303=30       202302=40       202301=40       202212=30     202211=40       202210=40       202209=50    202208=70       202207=50       202206=30       202205=40       202204=50       202203=40       202202=90       202201=70     202112=40       202111=40       202110=50       202109=50       202108=30
             * 1 coat vs 2 coats paint 2840    210     -       0.0     202307=210      202306=170      202305=140      202304=210      202303=210      202302=140      202301=170      202212=140    202211=170      202210=260      202209=320    202208=210      202207=260      202206=260      202205=210      202204=210      202203=170      202202=90       202201=90     202112=90       202111=90       202110=110      202109=110      202108=70
             */
            if (split.length >= 3) {
                if (split[2].equals("-")) {
                    svLessThan10Cnt ++;
                } else {
                    int sv = Integer.parseInt(split[2]);
                    if (sv>=10) {
                        svEqualOrGreaterThan10Cnt ++;
                    }else {
                        svLessThan10Cnt ++;
                    }
                }
            } else {
                svLessThan10Cnt ++;
            }
        }

        System.out.println("svEqualOrGreaterThan10Cnt: " + svEqualOrGreaterThan10Cnt + "\nsvLessThan10Cnt: " + svLessThan10Cnt);
    }
}
