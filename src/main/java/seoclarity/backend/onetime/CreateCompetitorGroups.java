package seoclarity.backend.onetime;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import seoclarity.backend.dao.actonia.CompetitorGroupsEntityDAO;
import seoclarity.backend.dao.actonia.CompetitorGroupsRelEntityDAO;
import seoclarity.backend.entity.actonia.CompetitorGroupsEntity;
import seoclarity.backend.entity.actonia.CompetitorGroupsRelEntity;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public class CreateCompetitorGroups { // https://www.wrike.com/open.htm?id=1254709015
	
	private static final String IN_FILE_NAME = "/home/<USER>/blogspotCompGroupsNov2023v1.xlsx"; // TODO
	
	private static final int OID = 8422; // TODO
	private static final int USER_ID = 214;
	
	private CompetitorGroupsEntityDAO competitorGroupsEntityDAO;
	private CompetitorGroupsRelEntityDAO competitorGroupsRelEntityDAO;
	
	public CreateCompetitorGroups() {
		competitorGroupsEntityDAO = SpringBeanFactory.getBean("competitorGroupsEntityDAO");
		competitorGroupsRelEntityDAO = SpringBeanFactory.getBean("competitorGroupsRelEntityDAO");
	}

	public static void main(String[] args) throws Exception {
		new CreateCompetitorGroups().createCompetitorGroups();	
	}
	
	private void createCompetitorGroups() throws Exception {
		List<String[]> list = getListFromExcel(0, 1); // TODO
		System.out.println("======InFileName:" + IN_FILE_NAME + " OID:" + OID + " cnt:" + list.size());
		
		for (int k = 0; k < list.size(); k++) {
			String[] arr = list.get(k);
			String competitorDomain = arr[0];
			String groupName = arr[1];
			if (StringUtils.isEmpty(competitorDomain) || StringUtils.isEmpty(groupName)) {
				System.out.println(" ==SkipAbnormalRow:" + k + " competitor:" + competitorDomain + " groupName:" + groupName);
				continue;
			}
			
			String domainReverse = StringUtils.reverseDelimited(competitorDomain, '.');
			String rootDomainReverse = StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(competitorDomain), '.');
			System.out.println(" ==AddCompetitorDomain:" + k + " groupName:" + groupName + " competitor:" + competitorDomain + " domainReverse:" + domainReverse + 
				" rootDomainReverse:" + rootDomainReverse);
			int competitorGroupId = 0;
			CompetitorGroupsEntity groupEntity = competitorGroupsEntityDAO.getByUniqueKey(OID, groupName);
			if (groupEntity == null) {
				CompetitorGroupsEntity newGroupEntity = new CompetitorGroupsEntity();
				newGroupEntity.setOwnDomainId(OID);
				newGroupEntity.setGroupName(groupName);
				newGroupEntity.setCreateUser(USER_ID);
				newGroupEntity.setCreateDate(new Date());
				competitorGroupId = competitorGroupsEntityDAO.insert(newGroupEntity); // TODO
				System.out.println(" ##InsCompetitorGroups id:" + competitorGroupId + " OID:" + OID + " groupName:" + groupName);
			} else {
				competitorGroupId = groupEntity.getId();
			}
			
			CompetitorGroupsRelEntity relEntity = competitorGroupsRelEntityDAO.getByUniqueKey(OID, competitorDomain);
			if (relEntity == null) {
				CompetitorGroupsRelEntity newRelEntity = new CompetitorGroupsRelEntity();
				newRelEntity.setOwnDomainId(OID);
				newRelEntity.setCompetitorDomain(competitorDomain);
				newRelEntity.setCompetitorGroupId(competitorGroupId);
				newRelEntity.setDomainReverse(domainReverse);
				newRelEntity.setRootDomainReverse(rootDomainReverse);
				newRelEntity.setCreateUser(USER_ID);
				newRelEntity.setCreateDate(new Date());
				int relId = competitorGroupsRelEntityDAO.insert(newRelEntity); // TODO
				System.out.println(" ##InsCompetitorGroupRel id:" + relId + " OID:" + OID + " competitorDomain:" + competitorDomain);
			} else {
				System.out.println(" CompetitorGroupsRelExist id:" + relEntity.getId() + " OID:" + OID + " competitorDomain:" + relEntity.getCompetitorDomain());
			}			
		}		
	}

	private List<String[]> getListFromExcel(int sheetNo, int startRow) throws Exception {
		XSSFWorkbook wb = new XSSFWorkbook(IN_FILE_NAME);
		XSSFSheet sheet = wb.getSheetAt(sheetNo); // TODO
		int lastRow = sheet.getLastRowNum();
		System.out.println("======InFileName:" + IN_FILE_NAME + " startRow:" + startRow + " lastRow:" + lastRow);
		List<String[]> list = new ArrayList<String[]>();
		for (int i = startRow; i <= lastRow; i++) {
			XSSFRow row = sheet.getRow(i);
			if (row != null && row.getPhysicalNumberOfCells() >= 2) { // TODO
				String competitor = row.getCell(0).getStringCellValue().trim();
				String competitorGroup = row.getCell(1).getStringCellValue().trim();
				list.add(new String[] { competitor, competitorGroup});
				System.out.println(" ====Row:" + i + " competitor:" + competitor + " group:" + competitorGroup);
			}			
		}
		return list;
	}
}