package seoclarity.backend.onetime;

import org.apache.commons.io.FileUtils;
import seoclarity.backend.keywordexpand.utils.ExportDataUtil;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1299816919
 */
public class GroupKeyword {

    private static final String CH_DB_RG_URL = "http://*************:8123";
    private static final String CH_DB_RG = "default";
    private static final String USER = "default";
    private static final String PSW = "clarity99!";

    public static void main(String[] args) throws Exception {
        new GroupKeyword().process();
    }

    private void test() {
        String owner = "allergies and post nasal drip and cough,allergies and post nasal drip cough,allergies post nasal drip remedies,antihistamine and post nasal drip,best allergy medicine for runny nose and post nasal drip,best antihistamine for post nasal drip cough,best otc for post nasal drip and cough,best over the counter post nasal drip medication,best treatment for chronic post nasal drip,can antihistamines help post nasal drip,causes of post nasal drip in throat,chronic post nasal drip natural remedies,chronic post nasal drip throat clearing,cough from post nasal drip relief,coughing up phlegm post nasal drip,excessive mucus post nasal drip,get rid of cough from post nasal drip,get rid of post nasal drip and cough,get rid of post nasal drip mucus,get rid post nasal drip home remedies,home remedies for post nasal drip and cough,home remedies for post nasal drip treatment,how do i get rid of a post nasal drip,how do i get rid of post nasal drip in my throat,how to clear post nasal drip fast,how to get rid of mucus in throat from post nasal drip,how to get rid of phlegm from post nasal drip,how to get rid of post nasal drip for good,how to get rid of post nasal drip phlegm,how to remove post nasal drip from throat,how to stop post nasal drip and sore throat,is it post nasal drip,medication for post nasal drip over the counter,medicine for runny nose and post nasal drip,meds for post nasal drip and cough,nasal drip cough home remedy,nasal spray cause post nasal drip,natural remedies for post nasal drip mucus,natural ways to cure post nasal drip,neti pot post nasal drip cough,nighttime post nasal drip medicine,post nasal drip and coughing up mucus,post nasal drip and mucus in throat,post nasal drip and phlegm in throat,post nasal drip and sore throat remedies,post nasal drip and throat,post nasal drip cure natural remedies,post nasal drip cure treatment,post nasal drip cures at home,post nasal drip drops,post nasal drip dry cough treatment,post nasal drip relief remedies,post nasal drip symptoms and treatment,post nasal drip syndrome treatment,post nasal drip treatment home remedies,reason of post nasal drip,reduce mucus post nasal drip,reduce post nasal drip phlegm,remedies for post nasal drip and sore throat,remedy for cough due to post nasal drip,signs of post nasal drip cough,sinus drops post nasal drip,sinus post nasal drip natural remedies,sinus post nasal drip treatment,sore throat and post nasal drip remedies,sore throat from post nasal drip allergies,treatment of chronic post nasal drip,what can you do for a post nasal drip,what causes post nasal drip and cough,what is a post nasal drip and symptoms,what is a post nasal drip cough like,what is post nasal drip and what causes it,what is post nasal drip cough like,what is post nasal drip sore throat,why do we get post nasal drip,why does post nasal drip make throat sore,will post nasal drip make you cough";
        String sample = "allergies and post nasal drip and cough,allergies and post nasal drip cough,allergies post nasal drip remedies,best treatment for chronic post nasal drip,causes of post nasal drip in throat,cough from post nasal drip relief,get rid of cough from post nasal drip,get rid of post nasal drip and cough,get rid of post nasal drip mucus,get rid post nasal drip home remedies,home remedies for post nasal drip and cough,home remedies for post nasal drip treatment,how do i get rid of a post nasal drip,how do i get rid of post nasal drip in my throat,how to clear post nasal drip fast,how to get rid of phlegm from post nasal drip,how to get rid of post nasal drip for good,how to get rid of post nasal drip phlegm,how to remove post nasal drip from throat,how to stop post nasal drip and sore throat,is it post nasal drip,meds for post nasal drip and cough,nasal drip cough home remedy,post nasal drip and coughing up mucus,post nasal drip and mucus in throat,post nasal drip and phlegm in throat,post nasal drip and throat,post nasal drip relief remedies,post nasal drip syndrome treatment,post nasal drip treatment home remedies,reason of post nasal drip,reduce mucus post nasal drip,reduce post nasal drip phlegm,remedies for post nasal drip and sore throat,remedy for cough due to post nasal drip,signs of post nasal drip cough,sinus post nasal drip natural remedies,sinus post nasal drip treatment,sore throat and post nasal drip remedies,sore throat from post nasal drip allergies,treatment of chronic post nasal drip,what can you do for a post nasal drip,what causes post nasal drip and cough,what is a post nasal drip and symptoms,what is a post nasal drip cough like,what is post nasal drip and what causes it,what is post nasal drip cough like,what is post nasal drip sore throat,will post nasal drip make you cough";
        List<String> sampleList = Arrays.asList(sample.split(","));
        List<String> ownerList = Arrays.asList(owner.split(","));
        for (String keyword : ownerList) {
            if (!sampleList.contains(keyword)) {
                System.out.println(keyword);
            }
        }
        System.out.println("========================");
        for (String keyword : sampleList) {
            if (!ownerList.contains(keyword)) {
                System.out.println(keyword);
            }
        }
    }

    private void process() throws Exception {
        List<String> sourceList = FileUtils.readLines(new File("/Users/<USER>/Desktop/group_keyword2_we.txt"));
        mergerList1(sourceList);
    }

    private void mergerList(List<String> sourceList) throws Exception {
        List<List<String>> resultList = new ArrayList<>();
        for (String sourceKw : sourceList) {
            resultList.add(Arrays.asList(sourceKw.split(",")));
        }
        System.out.println("===sourceSize:" + resultList.size());
        List<String> mergedList = new ArrayList<>();
        for (int i = 0; i < resultList.size(); i++) {
            List<String> rowKwList = new ArrayList<>(resultList.get(i));
            List<String> keywordsToAdd = new ArrayList<>();
            for (String keyword : rowKwList) {
                for (int j = 0; j < resultList.size(); j++) {
                    if (i == j) {
                        continue;
                    }
                    List<String> rowKwList2 = resultList.get(j);
                    if (rowKwList2.contains(keyword)) {
                        keywordsToAdd.addAll(rowKwList2);
                    }
                }
            }
            rowKwList.addAll(keywordsToAdd);
            String collect = rowKwList.stream().distinct().sorted().collect(Collectors.joining(","));
            mergedList.add(collect);
        }
        List<String> collect = mergedList.stream().sorted().distinct().collect(Collectors.toList());
        int beforeSize = mergedList.size();
        removeSubsets(collect);
        int afterSize = collect.size();
        System.out.println("====mergerSize:" + afterSize + " beforeSize:" + beforeSize);
        FileUtils.writeLines(new File("/Users/<USER>/Desktop/group_keyword2_we.txt"), collect);
    }

    private void mergerList1(List<String> sourceList) throws Exception {
        List<List<String>> resultList = new ArrayList<>();
        for (String sourceKw : sourceList) {
            resultList.add(Arrays.asList(sourceKw.split(",")));
        }
        resultList.sort(Comparator.comparingInt(List::size));

        List<String> outputList = new ArrayList<>();
        for (int i = 0; i < resultList.size(); i++) {
            List<String> strings = resultList.get(i);
            String collect = strings.stream().collect(Collectors.joining(","));
            outputList.add(collect);
        }
        FileUtils.writeLines(new File("/Users/<USER>/Desktop/group_keyword_sort.txt"), outputList);

    }

    // bk001
    /*private void process() throws Exception {
        List<String> sourceList = FileUtils.readLines(new File("/Users/<USER>/Desktop/group_keyword_source.txt"));
        List<List<String>> resultList = new ArrayList<>();
        for (String sourceKw : sourceList) {
            resultList.add(Arrays.asList(sourceKw.split(",")));
        }
        System.out.println("===sourceSize:" + resultList.size());
        List<String> mergedList = new ArrayList<>();
        Set<Integer> indexSet = new HashSet<>();
        for (int i = 0; i < resultList.size(); i++) {
            if (indexSet.contains(i)) {
                continue;
            }
            List<String> rowKwList = new ArrayList<>(resultList.get(i));
            List<String> keywordsToAdd = new ArrayList<>();
            for (String keyword : rowKwList) {
                for (int j = 0; j < resultList.size(); j++) {
                    if (i == j) {
                        continue;
                    }
                    List<String> rowKwList2 = resultList.get(j);
                    if (rowKwList2.contains(keyword)) {
                        indexSet.add(j);
                        keywordsToAdd.addAll(rowKwList2);
                    }
                }
            }
            rowKwList.addAll(keywordsToAdd);
            String collect = rowKwList.stream().distinct().sorted().collect(Collectors.joining(","));
            mergedList.add(collect);
        }
        List<String> collect = mergedList.stream().sorted().distinct().collect(Collectors.toList());
        int beforeSize = mergedList.size();
        removeSubsets(collect);
        int afterSize = collect.size();
        System.out.println("====mergerSize:" + afterSize + " beforeSize:" + beforeSize);
        FileUtils.writeLines(new File("/Users/<USER>/Desktop/group_keyword2_we.txt"), mergedList);
        System.out.println("===indexSet:" + indexSet.size());
    }*/

    private void removeSubsets(List<String> dataList) {
        Iterator<String> iterator = dataList.iterator();

        while (iterator.hasNext()) {
            String current = iterator.next();
            List<String> currentList = Arrays.asList(current.split(","));

            // 检查当前元素是否是其他元素的子集
            for (String other : dataList) {
                if (!current.equals(other)) {
                    List<String> otherList = Arrays.asList(other.split(","));
                    if (isSubset(currentList, otherList)) {
                        iterator.remove();
                        break;
                    }
                }
            }
        }
    }

    private boolean isSubset(List<String> subset, List<String> superset) {
        return superset.containsAll(subset);
    }

    private void exportCdbToFile() {
        String sql = "select * from sc_test_sha_hash_kwd_v2_stage1";
        try {
            ExportDataUtil.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, sql, "/home/<USER>/source/radeL/bot_project/tmp_file/group_keyword.txt", true, false, new String[]{""});
            System.out.println("====expoert ok");
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }
}
