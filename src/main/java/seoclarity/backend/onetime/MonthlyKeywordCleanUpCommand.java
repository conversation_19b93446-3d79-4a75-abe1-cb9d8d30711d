package seoclarity.backend.onetime;

import java.io.File;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.utils.SpringBeanFactory;

public class MonthlyKeywordCleanUpCommand extends BaseThreadCommand {
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;

	private List<SeoClarityKeywordEntity> keywordList;

	private String ip;

	private Integer keywordEngine;

	private Integer keywordLanguage;

	private File outputFile;

	public MonthlyKeywordCleanUpCommand(String ip, List<SeoClarityKeywordEntity> keywordList, Integer keywordEngine,
			Integer keywordLanguage, File outputFile) {
		super();

		this.keywordList = keywordList;
		this.ip = ip;
		this.keywordEngine = keywordEngine;
		this.keywordLanguage = keywordLanguage;
		this.outputFile = outputFile;

		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory
				.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
	}

	/**
	 * For Testing
	 */
	public static void main(String[] args) throws Exception {
		MonthlyKeywordCleanUpCommand m = new MonthlyKeywordCleanUpCommand(null, null, null, null, null);

		SeoClarityKeywordEntity keywordProperty = null;

		List<SeoClarityKeywordEntity> keyList = new ArrayList<SeoClarityKeywordEntity>();

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("autozone'");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("autozone # ");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("autozone.");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("autozone/");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("comcast'");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("g -lm");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home depot'");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home depot'");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home depot.");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home depot.");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home depot]");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home depot]");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home. depot");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home. depot");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home.depot");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("home.depot");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("Keyword");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("lowes'");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("lowes/");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("macy's");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("netflix -whited00r_tested.ipa");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("southwest airlines");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("t - mobile");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("t-.mobile");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("verizon'");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("verizon wireless");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("verizon wireless'");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("verizon wireless.");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("verizon. wireless");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("verizon.wireless");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("verizon/wireless");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("wells fargo   ");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("zillow'");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("zillow.");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("zillow/");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("zillow://");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("zillow]");
		keyList.add(keywordProperty);

		keywordProperty = new SeoClarityKeywordEntity();
		keywordProperty.setKeywordText("well seems_good site:www.baidu.com finish_test");
		keyList.add(keywordProperty);

		for (int i = 0; i < keyList.size(); i++) {
			keywordProperty = keyList.get(i);
//			m.process(keywordProperty);
		}
	}

//	private void process(SeoClarityKeywordEntity keywordProperty) throws Exception {
//		String originalText = URLDecoder.decode(keywordProperty.getKeywordText(), "utf-8");
//		String alterText = originalText;
//		int tmp = 0;
//		do {
//			Map<String, String> processInfo = MonthlyKeywordCleanUpCommand.cleanupKeyowrds(alterText);
//			if (StringUtils.equalsIgnoreCase(processInfo.get("needReprocess"), "false")) {
//				break;
//			}
//			alterText = processInfo.get("canonicalkeyword");
//			tmp++;
//			if (tmp > 100)
//				System.out.println("----------------------ERROR Orig Text:" + originalText
//						+ "----------------------------");
//		} while (true);
//
//		String encodeAlterText = URLEncoder.encode(alterText, "utf-8");
//
//		if (StringUtils.isNotBlank(keywordProperty.getKeywordText()) && StringUtils.isNotBlank(encodeAlterText)
//				&& !StringUtils.equalsIgnoreCase(encodeAlterText, keywordProperty.getKeywordText())) {
//			String log = "BEFORE:" + keywordProperty.getKeywordText() + "!_!" + originalText + "====AFTER:"
//					+ encodeAlterText + "!_!" + alterText;
//			System.out.println(log);
//
//			if (outputFile != null) {
//				FileUtils.writeStringToFile(outputFile, log + "\n\r", true);
//			}
//
//			SeoClarityKeywordEntity keywordEntity = seoClarityKeywordEntityDAO.getKeywordEntityByName(encodeAlterText);
//			if (keywordEntity == null) {
//				System.out.println("!!!error!!! , keyword not exits:" + encodeAlterText);
//
//				Integer kId = seoClarityKeywordEntityDAO.insert(encodeAlterText);
//				SeoClarityKeywordMonthlySearchEngineRelationEntity sRelation = new SeoClarityKeywordMonthlySearchEngineRelationEntity();
//				sRelation.setCreateDate(NumberUtils.toInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
//				sRelation.setKeywordId(kId);
//				sRelation.setSearchEngineId(keywordEngine);
//				sRelation.setSearchLanguageId(keywordLanguage);
//				seoClarityKeywordMonthlySearchEngineRelationEntityDAO.insert(sRelation);
//
//				
//			} else {
//				SeoClarityKeywordMonthlySearchEngineRelationEntity searchEngineRelationEntity = seoClarityKeywordMonthlySearchEngineRelationEntityDAO
//						.checkExist(keywordEntity.getId(), keywordEngine, keywordLanguage);
//				if (searchEngineRelationEntity != null) {
//					System.out.println("relationship already exists! kid:" + keywordEntity.getId());
//				} else {
//					SeoClarityKeywordMonthlySearchEngineRelationEntity sRelation = new SeoClarityKeywordMonthlySearchEngineRelationEntity();
//					sRelation.setCreateDate(NumberUtils.toInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
//					sRelation.setKeywordId(keywordEntity.getId());
//					sRelation.setSearchEngineId(keywordEngine);
//					sRelation.setSearchLanguageId(keywordLanguage);
//					int relId = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.insert(sRelation);
//					System.out.println("creating new relationships,kId: " + keywordEntity.getId() + " ,new relId:"
//							+ relId);
//				}
//			}
//			System.out.println("delete old relationships!!!, deleted Kid:" + keywordProperty.getId());
////			seoClarityKeywordMonthlySearchEngineRelationEntityDAO.deleteRelationship(keywordProperty.getId(),
////					keywordEngine, keywordLanguage);
//		}
//	}

	@Override
	protected void execute() throws Exception {
		long a = System.currentTimeMillis();
		for (SeoClarityKeywordEntity keywordProperty : keywordList) {
			try {
//				process(keywordProperty);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		CacheModleFactory.getInstance().setAliveIpAddress(ip);
		long b = System.currentTimeMillis();
		System.out.println("End command IP: " + ip + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}

	public static Map<String, String> cleanupKeyowrds(String keywordStr) {
		Map<String, String> returnValues = new HashMap<String, String>();
		returnValues.put("needReprocess", "false");
		returnValues.put("canonicalkeyword", keywordStr);

		if (StringUtils.endsWith(keywordStr, ".")) {
			keywordStr = StringUtils.removeEndIgnoreCase(keywordStr, ".");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.endsWith(keywordStr, "'")) {
			keywordStr = StringUtils.removeEndIgnoreCase(keywordStr, "'");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.endsWith(keywordStr, "\"")) {
			keywordStr = StringUtils.removeEndIgnoreCase(keywordStr, "\"");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.startsWith(keywordStr, "\"")) {
			keywordStr = StringUtils.removeStart(keywordStr, "\"");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.endsWith(keywordStr, "/")) {
			keywordStr = StringUtils.removeEndIgnoreCase(keywordStr, "/");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.startsWith(keywordStr, "[")) {
			keywordStr = StringUtils.removeStart(keywordStr, "[");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.endsWith(keywordStr, "]")) {
			keywordStr = StringUtils.removeEndIgnoreCase(keywordStr, "]");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		
		// https://www.wrike.com/open.htm?id=43538027
		if (StringUtils.startsWith(keywordStr, ",")) {
			keywordStr = StringUtils.removeStart(keywordStr, ",");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.endsWith(keywordStr, ",")) {
			keywordStr = StringUtils.removeEndIgnoreCase(keywordStr, ",");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		// deal with blank string at the end of keyword
		if (StringUtils.endsWith(keywordStr, " ")) {
			keywordStr = StringUtils.removeEndIgnoreCase(keywordStr, " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.containsIgnoreCase(keywordStr, "site:")) {
			// keywordStr = StringUtils.replace(keywordStr, "site:", "");
			// returnValues.put("needReprocess", "true");
			// returnValues.put("canonicalkeyword", keywordStr);
			keywordStr = StringUtils.substringBeforeLast(keywordStr, "site:");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.containsIgnoreCase(keywordStr, "-whited00r_tested.ipa")) {
			keywordStr = StringUtils.replace(keywordStr, "-whited00r_tested.ipa", "");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.containsIgnoreCase(keywordStr, "-install-global-extension")) {
			keywordStr = StringUtils.replace(keywordStr, "-install-global-extension", "");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.containsIgnoreCase(keywordStr, "]") || StringUtils.containsIgnoreCase(keywordStr, "[")) {
			keywordStr = StringUtils.replace(keywordStr, "]", "");
			keywordStr = StringUtils.replace(keywordStr, "[", "");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.containsIgnoreCase(keywordStr, "\\")) {
			keywordStr = StringUtils.replace(keywordStr, "\\", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		if (StringUtils.containsIgnoreCase(keywordStr, "/")) {
			keywordStr = StringUtils.replace(keywordStr, "/", " ");
			returnValues.put("needReprocess", "true");

			returnValues.put("canonicalkeyword", keywordStr);
		}

		// '#' --> ''
		if (StringUtils.containsIgnoreCase(keywordStr, "#")) {
			keywordStr = StringUtils.replace(keywordStr, "#", "");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		// '//' --> ''
		if (StringUtils.containsIgnoreCase(keywordStr, "//")) {
			keywordStr = StringUtils.replace(keywordStr, "//", "");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		// '.' --> ' '
		if (StringUtils.containsIgnoreCase(keywordStr, ".")) {
			keywordStr = StringUtils.replace(keywordStr, ".", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		// ':' --> ' '
		if (StringUtils.containsIgnoreCase(keywordStr, ":")) {
			keywordStr = StringUtils.replace(keywordStr, ":", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		
		// '-' --> ' '
		if (StringUtils.containsIgnoreCase(keywordStr, "-")) {
			keywordStr = StringUtils.replace(keywordStr, "-", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}

		// ''' --> ' '
//		if (StringUtils.containsIgnoreCase(keywordStr, "'")) {
//			keywordStr = StringUtils.replace(keywordStr, "'", " ");
//			returnValues.put("needReprocess", "true");
//			returnValues.put("canonicalkeyword", keywordStr);
//		}

		// retrim string of blank more than two to a single one
		if (StringUtils.containsIgnoreCase(keywordStr, "  ")) {
			keywordStr = StringUtils.replace(keywordStr, "  ", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		
		// ';' --> ' '
		if (StringUtils.containsIgnoreCase(keywordStr, ";")) {
			keywordStr = StringUtils.replace(keywordStr, ";", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		
		if (StringUtils.containsIgnoreCase(keywordStr, ",")) {
			keywordStr = StringUtils.replace(keywordStr, ",", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "@")) {
			keywordStr = StringUtils.replace(keywordStr, "@", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "(")) {
			keywordStr = StringUtils.replace(keywordStr, "(", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, ")")) {
			keywordStr = StringUtils.replace(keywordStr, ")", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "\"")) {
			keywordStr = StringUtils.replace(keywordStr, "\"", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "=")) {
			keywordStr = StringUtils.replace(keywordStr, "=", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "!")) {
			keywordStr = StringUtils.replace(keywordStr, "!", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "©")) {
			keywordStr = StringUtils.replace(keywordStr, "©", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "*")) {
			keywordStr = StringUtils.replace(keywordStr, "*", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "~")) {
			keywordStr = StringUtils.replace(keywordStr, "~", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "<")) {
			keywordStr = StringUtils.replace(keywordStr, "<", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, ">")) {
			keywordStr = StringUtils.replace(keywordStr, ">", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "_")) {
			keywordStr = StringUtils.replace(keywordStr, "_", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		if (StringUtils.containsIgnoreCase(keywordStr, "®")) {
			keywordStr = StringUtils.replace(keywordStr, "®", " ");
			returnValues.put("needReprocess", "true");
			returnValues.put("canonicalkeyword", keywordStr);
		}
		keywordStr = StringUtils.trim(keywordStr.replaceAll("\\s+", " ").trim());
		returnValues.put("canonicalkeyword", keywordStr);

		return returnValues;
	}
}
