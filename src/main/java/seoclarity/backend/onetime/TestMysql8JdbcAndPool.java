package seoclarity.backend.onetime;

import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityCityEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.util.Arrays;
import java.util.List;

// mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.TestMysql8JdbcAndPool" -Dexec.cleanupDaemonThreads=false -Dexec.args=""
public class TestMysql8JdbcAndPool {
    private KeywordEntityDAO keywordEntityDAO;
    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;

    public TestMysql8JdbcAndPool() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
    }

    public static void main(String[] args) {
        TestMysql8JdbcAndPool tool = new TestMysql8JdbcAndPool();
        tool.process();
    }

    private void process() {
        for (int i = 0; i < 600; i++) {
            System.out.println("=========================== i " + i);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            List<SeoClarityKeywordEntity> keywordById = seoClarityKeywordEntityDAO.getKeywordByIdList(Arrays.asList(26));
            System.out.println("keywordById: "+keywordById);
            KeywordEntity keyword = keywordEntityDAO.getById(12063L, 15);
            System.out.println("keyword: "+keyword);
        }
    }
}
