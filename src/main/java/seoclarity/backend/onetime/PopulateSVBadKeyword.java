package seoclarity.backend.onetime;

import java.net.URLDecoder;
import java.util.List;
import org.apache.commons.lang.StringEscapeUtils;
import seoclarity.backend.dao.rankcheck.SvBadKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.utils.SpringBeanFactory;

public class PopulateSVBadKeyword {

	private final SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private final SvBadKeywordEntityDAO svBadKeywordEntityDAO;
	
	public PopulateSVBadKeyword() {
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		svBadKeywordEntityDAO = SpringBeanFactory.getBean("svBadKeywordEntityDAO");
	}
	
	public static void main(String[] args) throws Exception {
		new PopulateSVBadKeyword().populate();
	}
	
	private void populate() throws Exception {
		int fromId = 0; // TODO
		int currMaxId = 192554643; // TODO 2022/08/01
		int STEP_CNT = 10000;

		int totalKWCnt = 0;
		int decodeErrCnt = 0;
		int badKWCnt = 0;
		while (fromId <= currMaxId) {
			int toId = fromId + STEP_CNT;
			List<SeoClarityKeywordEntity> kwList = seoClarityKeywordEntityDAO.getListByIdRange(fromId, toId);
			if (kwList != null && kwList.size() > 0) {
				System.out.println(" ==ProcessingIdRange:" + fromId + "-" + toId + " kws:" + kwList.size());
				totalKWCnt += kwList.size();
			} else {
				System.out.println(" ==ProcessingIdRange:" + fromId + "-" + toId + " NoKW");
			}
			
			for (SeoClarityKeywordEntity keywordEntity : kwList) {
				int rcKWId = keywordEntity.getId();
				String encodedKW = keywordEntity.getKeywordText();
				String decodedKWName = null;
				try {
					decodedKWName = StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(encodedKW, "UTF-8")));
				} catch (Exception exp) {
					decodeErrCnt++;
					System.out.println(" ==DecodeError id:" + rcKWId + " KW:" + encodedKW);
					exp.printStackTrace();
				}
				if (decodedKWName != null) {
					if (seoclarity.backend.utils.FormatUtils.checkKeyword(decodedKWName) == null) {
						badKWCnt++;
		    			System.out.println(" ==FoundBadKW id:" + rcKWId + " KW:" + decodedKWName);
		    			
		    			try {
		    				svBadKeywordEntityDAO.insert(rcKWId, decodedKWName);
						} catch (Exception exp) {
							decodeErrCnt++;
							System.out.println(" ==InsError id:" + rcKWId + " KW:" + encodedKW);
							exp.printStackTrace();
							svBadKeywordEntityDAO.insert(rcKWId, "");
						}
		    		}
				}
			}
			fromId = toId;
		}
		System.out.println("===========totalKWCnt:" + totalKWCnt + " decodeErrCnt:" + decodeErrCnt + " badKWCnt:" + badKWCnt);
	}
}