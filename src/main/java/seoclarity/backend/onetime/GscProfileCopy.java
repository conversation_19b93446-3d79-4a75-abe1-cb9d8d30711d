package seoclarity.backend.onetime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.GwmDomainRelDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.actonia.GWMDomainRel;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

// https://www.wrike.com/open.htm?id=1336960685
public class GscProfileCopy {

    private static final int DEFAULT_DATA_SOURCE = 0;

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private GwmDomainRelDAO gwmDomainRelDAO;
    private String sourceFilePath;
    private String outputFilePath;
    private boolean isTest = true;


    public GscProfileCopy() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        gwmDomainRelDAO = SpringBeanFactory.getBean("gwmDomainRelDAO");
    }

    public static void main(String[] args) {
        new GscProfileCopy().process(args);
    }

    private void initParam(String[] args) {
        isTest = Boolean.parseBoolean(args[0]);
        sourceFilePath = args[1];
        outputFilePath = args[2];
        System.out.println("===paramInfo isTest:" + isTest + " sourcePath:" + sourceFilePath + " outputPath:" + outputFilePath);
    }

    private void process(String[] args) {
        initParam(args);
        List<ProfileInfo> profileInfoList = readFile(sourceFilePath);
        List<Integer> oidList = profileInfoList.stream().map(ProfileInfo::getOid).distinct().collect(Collectors.toList());
        Map<Integer, String> oidPCodeMap = new HashMap<>();
        List<List<Integer>> lists = CollectionSplitUtils.splitCollectionBySize(oidList, 100);
        for (int i = 0; i < lists.size(); i++) {
            List<Integer> list = lists.get(i);
            List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainByIdList(list);
            domainList.forEach(domain -> oidPCodeMap.put(domain.getId(), domain.getSearchEngineCountry()));
        }
        List<GWMDomainRel> gwmDomainRels = new ArrayList<>();
        profileInfoList.forEach(profileInfo -> {
            String countryCode = oidPCodeMap.get(profileInfo.getOid());
            if (StringUtils.isBlank(countryCode)) {
                return;
            }
            GWMDomainRel gwmDomainRel = new GWMDomainRel();
            gwmDomainRel.setOwnDomainId(profileInfo.getOid());
            gwmDomainRel.setGwmDomainName(profileInfo.getProfileName());
            gwmDomainRel.setGwmCountryCode(countryCode);
            gwmDomainRel.setDataSource(DEFAULT_DATA_SOURCE);
            gwmDomainRels.add(gwmDomainRel);
        });
        System.out.println("===uniqueDomain:" + oidList.size() + " oidPCodeMap:" + oidPCodeMap.size());
        List<GWMDomainRel> insertList = new ArrayList<>();
        List<GWMDomainRel> exsitsList = new ArrayList<>();
        for (GWMDomainRel gwmDomainRel : gwmDomainRels) {
            List<GWMDomainRel> exsitsProfileList = gwmDomainRelDAO.getByDomainId(gwmDomainRel.getOwnDomainId(), gwmDomainRel.getDataSource(),
                    gwmDomainRel.getGwmCountryCode(), new HashSet<>(Collections.singletonList(gwmDomainRel.getGwmDomainName())));
            if (exsitsProfileList != null && !exsitsProfileList.isEmpty()) {
                exsitsProfileList.stream().filter(exsitsProfile -> StringUtils.equalsIgnoreCase(exsitsProfile.getGwmDomainName(), gwmDomainRel.getGwmDomainName())).peek(exsitsList::add);
            } else {
                insertList.add(gwmDomainRel);
            }
        }

        List<String> outputList = new ArrayList<>();
        if (!exsitsList.isEmpty()) {
            outputList.add("domainId\tprofileName");
            exsitsList.forEach(exsits -> outputList.add(exsits.getOwnDomainId() + "\t" + exsits.getGwmDomainName()));
            try {
                FileUtils.writeLines(new File(outputFilePath), exsitsList);
            } catch (IOException e) {
                System.out.println("===outputError list:" + outputList);
                e.printStackTrace();
            }
        }

        if (!isTest) {
            if (!insertList.isEmpty()) {
                gwmDomainRelDAO.insertData(insertList);
            }
        }

        System.out.println("===endInfo sourceList:" + profileInfoList.size() + " domainList:" + oidList.size() + " countryCodeMap:" + oidPCodeMap.size() + " insertSize:" + insertList.size() + " exsitsSize:" + exsitsList.size() + " ouputSize:" + outputList.size());
    }

    private List<ProfileInfo> readFile(String filePath) {
        List<ProfileInfo> profileInfoList = new ArrayList<>();
        try {
            CSVParser csvParser = CSVFormat.DEFAULT.withDelimiter(',').withIgnoreEmptyLines(true).withFirstRecordAsHeader().parse(new FileReader(filePath));
            List<CSVRecord> records = csvParser.getRecords();
            for (CSVRecord record : records) {
                String[] split = record.get(0).split("-");
                int oid = Integer.parseInt(split[split.length - 1].trim());
                String profileName = record.get(1).trim();
                profileInfoList.add(new ProfileInfo(oid, profileName));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("sourceListSize:" + profileInfoList.size());
        return profileInfoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode
    private static class ProfileInfo {
        private int oid;
        private String profileName;
    }

}
