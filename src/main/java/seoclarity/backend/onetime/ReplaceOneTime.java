package seoclarity.backend.onetime;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jetty.util.StringUtil;
import seoclarity.backend.dao.clickhouse.RGRankingDetail202107DAO1;
import seoclarity.backend.dao.clickhouse.RGRankingDetail202107DAO2;
import seoclarity.backend.dao.clickhouse.RGRankingDetail202107DAO3;
import seoclarity.backend.dao.clickhouse.RGRankingDetail202107DAO4;
import seoclarity.backend.upload.Utils.WriteUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.onetime
 * @author: cil
 * @date: 2021-08-13 13:57
 **/
public class ReplaceOneTime {
    private RGRankingDetail202107DAO1 rgRankingDetail202107DAO1;
    private RGRankingDetail202107DAO2 rgRankingDetail202107DAO2;
    private RGRankingDetail202107DAO3 rgRankingDetail202107DAO3;
    private RGRankingDetail202107DAO4 rgRankingDetail202107DAO4;

    public ReplaceOneTime() {
        rgRankingDetail202107DAO1 = SpringBeanFactory.getBean("RGRankingDetail202107DAO1");
        rgRankingDetail202107DAO2 = SpringBeanFactory.getBean("RGRankingDetail202107DAO2");
        rgRankingDetail202107DAO3 = SpringBeanFactory.getBean("RGRankingDetail202107DAO3");
        rgRankingDetail202107DAO4 = SpringBeanFactory.getBean("RGRankingDetail202107DAO4");
    }

    public static void main(String[] args) {
        ReplaceOneTime ins = new ReplaceOneTime();
        String path = "/home/<USER>/unique-keywords-final.txt";
        String result_path = "/home/<USER>/unique-keywords-result" + args[0] + ".txt";
//        String toPath = "/home/<USER>/unique-keywords-final_replace.txt";

        List<String> stringList = read(path, Integer.valueOf(args[0]));
        if ("1".equals(args[0]))
            stringList.remove(0);
//        WriteUtils.write(toPath,stringList);


        System.out.println("fileSize:" + stringList.size());
        int flag = 0;
        while (flag < stringList.size()) {
            try {


                List<String> list = stringList.subList(flag, stringList.size() > flag + 100 ? flag + 100 : stringList.size() - 1);

                System.out.println("listSize:" + list.size() + " index:" + flag);
                flag += 100;
                long millis = System.currentTimeMillis();
                List<Map<String, Object>> maps = new LinkedList<>();
                switch (args[0]) {
                    case "1":
                        maps = ins.rgRankingDetail202107DAO1.selectData(list);
                        break;
                    case "2":
                        maps = ins.rgRankingDetail202107DAO2.selectData(list);
                        break;
                    case "3":
                        maps = ins.rgRankingDetail202107DAO3.selectData(list);
                        break;
                    case "4":
                        maps = ins.rgRankingDetail202107DAO4.selectData(list);
                        break;
                }


                System.out.println("mapsSize:" + maps.size() + " useTime:" + (System.currentTimeMillis() - millis) / 1000 + "s");
                for (Map<String, Object> map : maps) {

                    String[] groupUniqArrays = (String[]) map.get("kList");
                    StringBuilder stringBuilder = new StringBuilder();
                    if (groupUniqArrays != null && groupUniqArrays.length > 0) {
                        stringBuilder.append("\"").append(StringUtils.join(groupUniqArrays, ",")).append("\"");

                    } else {
                        stringBuilder.append("");
                    }
                    WriteUtils.write(result_path, (String) map.get("keyword_name"), stringBuilder.toString());
                }
                list.clear();
                list = null;
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }

    }


    private static List<String> read(String path, int i) {
        List<String> dataList = new LinkedList<>();

        File fin = new File(path);
        BufferedReader br = null;
        try {
            FileInputStream fis = new FileInputStream(fin);
            //Construct BufferedReader from InputStreamReader
            br = new BufferedReader(new InputStreamReader(fis));
            String line;

            while ((line = br.readLine()) != null) {
                if (i < 4) {
                    i++;
                    continue;
                }
                i = 1;

                dataList.add(replaceString(line).replace("'", "\\'"));
            }
            return dataList;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    private static String replaceString(String str) {
        if (StringUtil.isEmpty(str))
            return "";
        if (str.startsWith("\"")) {

            return replaceString(str.substring(1));
        }
        if (str.endsWith("\"")) {
            return replaceString(str.substring(0, str.length() - 1));
        }
        return str;
    }

}
