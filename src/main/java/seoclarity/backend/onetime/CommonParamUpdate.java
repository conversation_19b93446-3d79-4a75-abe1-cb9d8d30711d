package seoclarity.backend.onetime;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;

@CommonsLog
public class CommonParamUpdate {

    private CommonParamDAO commonParamDAO;

    public CommonParamUpdate() {
        commonParamDAO =  SpringBeanFactory.getBean("commonParamDAO");
    }


    private void process(){

        int domainId = 7605;
        String functionName = "rankIntgc";
        String title = "Markets";
        List<CommonParamEntity> commonParamEntityList = commonParamDAO.getParamListByTitleReg(domainId, functionName, title);
        if(CollectionUtils.isEmpty(commonParamEntityList) || commonParamEntityList.size() != 19){
            log.info("====ErrorcommonParamEntityList:" + commonParamEntityList.size());
            return;
        }

        for(CommonParamEntity commonParamEntity: commonParamEntityList){
            String paramJson = commonParamEntity.getParamJson();
            Map map = new Gson().fromJson(paramJson, Map.class);
            map.get("location");
            log.info("********* title:" + commonParamEntity.getTitle() + " ,paramJson:" + paramJson);

//            log.info("********* uiJson:" + commonParamEntity.getUiJson());

        }
    }

    private static void getGeoSet(){
        Map<String, Set<Integer>> roverGeoIdMap = new HashMap<>();
        File geoMapFile = new File("/home/<USER>/geo_mapping_20240909.txt");
        Map<String, Integer> geoMap = new HashMap<>();
        try {
            List<String> lineList = FileUtils.readLines(geoMapFile, "utf-8");
            for(String line: lineList){
                String[] arr = line.split("\t");
                String geoName = arr[0];
                String geoId = arr[1];
                geoMap.put(geoName, Integer.parseInt(geoId));
            }

        }catch (Exception e){
            e.printStackTrace();
        }
        log.info("=======geoMap:" + geoMap.size());

        File roverGeoFile = new File("/home/<USER>/roverUS_GEOfilters090524.csv");
        Map<String, Set<String>> roverGeoMap = new HashMap<>();
        try {

            CSVFormat csvFormat = CSVFormat.DEFAULT.withFirstRecordAsHeader().withSkipHeaderRecord();
            CSVParser csvParser = CSVParser.parse(roverGeoFile, StandardCharsets.UTF_8, csvFormat);
            Iterator<CSVRecord> iterator = csvParser.iterator();
            while (iterator.hasNext()) {
                CSVRecord csvRecord = iterator.next();
                String geoName = csvRecord.get("GEO");
                if(StringUtils.isBlank(geoName)){
                    continue;
                }
                for(int i=1;i<csvRecord.size();i++) {
                    String title1 = csvRecord.get("Saved Filter" + i);
                    if (roverGeoMap.get(title1) == null) {
                        Set set1 = new HashSet();
                        set1.add(geoName);
                        roverGeoMap.put(title1, set1);
                    } else {
                        Set set1 = roverGeoMap.get(title1);
                        set1.add(geoName);
                        roverGeoMap.put(title1, set1);
                    }
                }

            }

            log.info("=======roverGeoMapSize:" + roverGeoMap.size());

            for(String key: roverGeoMap.keySet()){
                Set<String> geoSet = roverGeoMap.get(key);
                for(String geoName: geoSet){
                    if(geoMap.get(geoName) != null){
                        Integer geoId = geoMap.get(geoName);
                        if(roverGeoIdMap.get(key) == null){
                            Set set = new HashSet();
                            set.add(geoId);
                            roverGeoIdMap.put(key, set);
                        }else {
                            Set set = roverGeoIdMap.get(key);
                            set.add(geoId);
                            roverGeoIdMap.put(key, set);
                        }
                    }
                }
                log.info("======key:" + key );
            }

            Set<Integer> geoIdSet = roverGeoIdMap.get("401 - 450 Markets");
            geoIdSet.add(305542);
            roverGeoIdMap.put("401 - 450 Markets", geoIdSet);

        }catch (Exception e){
            e.printStackTrace();
        }

        log.info("====roverGeoIdMap:" + new Gson().toJson(roverGeoIdMap));
        for (String key: roverGeoIdMap.keySet()){
            String geoIdStr = "\"";
            Set<Integer> getIdSet = roverGeoIdMap.get(key);
            geoIdStr += StringUtils.join(getIdSet, "\",\"");
            geoIdStr += "\"";
            log.info("================key:" + key + " geoIdStr : " + geoIdStr);
        }


    }

    public static void main(String[] args){
        getGeoSet();

        CommonParamUpdate commonParamUpdate = new CommonParamUpdate();
        commonParamUpdate.process();
    }
}
