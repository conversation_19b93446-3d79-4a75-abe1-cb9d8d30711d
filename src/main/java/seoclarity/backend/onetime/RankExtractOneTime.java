package seoclarity.backend.onetime;

import com.google.common.net.InternetDomainName;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.export.ExportTopXRankData;
import seoclarity.backend.service.*;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

@CommonsLog
public class RankExtractOneTime {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static final String KEY_SPLIT = "#_#";
    private static String LOC = "/home/<USER>/";

    private static Date processDate = new Date();
    private static String[] domainIdList;
    private static int domainId;
    private static boolean extractTag = false;
    private static String device;
    private static int topX;

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private CommonDataService commonDataService;
    private ScriptDeployInfoEntityDAO scriptDeployInfoEntityDAO;
    private KeywordEntityDAO keywordEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;

    public RankExtractOneTime() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        commonDataService = SpringBeanFactory.getBean("commonDataService");
        scriptDeployInfoEntityDAO = SpringBeanFactory.getBean("scriptDeployInfoEntityDAO");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
    }

    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();

    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(765, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
        SPECIAL_DOMAIN_SERVER_MAP.put(2047, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
    }

    public static final List<Integer> EXTRACT_SECONDARY_DOMAIN_LIST = Arrays.asList();


    private void processForDomain(int ownDomainId) {

        log.info("********************** process domain " + ownDomainId + ",processDate: " + processDate + " *******************");

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);
        }catch (Exception e){
            e.printStackTrace();
        }


        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);

        boolean isMobile = device.equalsIgnoreCase("mobile") ? true :false;
        processExtract(ownDomainEntity, isMobile);


    }

    private void processExtract(OwnDomainEntity ownDomainEntity, boolean isMobile) {

        int ownDomainId = ownDomainEntity.getId();
        int processingDate = FormatUtils.formatDateToYyyyMmDd(processDate);

        String fileName = getFileName(ownDomainEntity, processingDate, isMobile);

        File localFolder = new File(LOC + ownDomainId);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }

//        String localFilePath = LOC + "8386" + File.separator;
        String localFilePath = LOC + ownDomainId + File.separator;
        String remoteFilePath = localFilePath;

        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        try {

            processFile(localFile, ownDomainEntity, isMobile, localFileName, remoteFilePath);


        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, boolean isMobile, String localFilePath, String remoteFilePath) throws Exception {

        int ownDomainId = ownDomainEntity.getId();
        addHeadersForExactFile(localFile, ownDomainEntity, processDate);//todo

        List<String> dataList = getDataFromDB(ownDomainEntity, isMobile);
        FileUtils.writeLines(localFile, dataList, true);

        int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
        if (SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId) != null) {
            serverType = SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId);
        }


        copyFileToRemoteServer(serverType, ownDomainId, localFilePath, remoteFilePath);
    }

    private String getFileName(OwnDomainEntity ownDomainEntity, int processingDate, boolean isMobile) {

        String device = isMobile ? "mobile" : "desktop";
        String engineName = isMobile ? "Google Mobile" : "Google Desktop";
        String fileName = ownDomainEntity.getDomain() +"_" + ownDomainEntity.getId() + "_" + engineName + "_" + processingDate + "_" + topX + "_RankExtract_"  + device + ".txt";
        return fileName;
    }



    private void addHeadersForExactFile(File outFile, OwnDomainEntity ownDomainEntity, Date processDate) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Ranking URL").append(SPLIT);
        header.append("Rank").append(SPLIT);
        header.append("URL Type").append(SPLIT);
        header.append("Search Volume");

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity, boolean isMobile) throws Exception{

        List<String> extractLines = new ArrayList<String>();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);
        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);


        List<CLRankingDetailEntity> dataList = new ArrayList<>();

        int locationId = 0;
        int rank = 30;

        ExtractQueryVO extractQueryVO = new ExtractQueryVO();
        extractQueryVO.setDomainId(domainId);
        extractQueryVO.setEngineId(engineId);
        extractQueryVO.setLanguageId(languageId);
        extractQueryVO.setDevice(device);
        extractQueryVO.setRankDate(rankingDate);
        extractQueryVO.setRank(topX);
        extractQueryVO.setLocationId(locationId);

        dataList = clDailyRankingEntityDao.exportTopXKeywords(ownDomainId, engineId, languageId, locationId, rankingDate, isMobile, topX);
//        dataList = clColdDailyRankingEntityDao.exportTopXKeywords(
//                ownDomainId, engineId, languageId, locationId, rankingDate, isMobile, topX);

        if(EXTRACT_SECONDARY_DOMAIN_LIST.contains(ownDomainId)){
            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
            if(CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)){
                for(DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList){
                    engineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    languageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    boolean secondaryIsMobile = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? false : true;
                    if(secondaryIsMobile != isMobile){
                        log.info("===secondary not same device isMobile: " + isMobile + ",secondaryIsMobile:" + secondaryIsMobile);
                        continue;
                    }
                    log.info("===process secondary engine :" + engineId + ",languageId: " + languageId + ",isMobile:" + secondaryIsMobile + ",domain:" + ownDomainId);
                    List<CLRankingDetailEntity> secondaryDataList = new ArrayList<>();
                    if (ownDomainId == 8682) {
                        locationId = 0;
                        rank = 100;
                        secondaryDataList = clDailyRankingEntityDao.exportTopXKeywords(
                                ownDomainId, engineId, languageId, locationId, rankingDate, secondaryIsMobile, rank);
                    }
                    log.info("===secondaryDataList size:" + secondaryDataList.size());
                    dataList.addAll(secondaryDataList);
                }
            }

        }

        List<String> keywordNameList = new ArrayList<>();
        Map<String, String[]> tagMap = new HashMap<>();
        Map<String, CLRankingDetailEntity> keywordMap = new HashMap<>();

        log.info("===dataList size: " + dataList.size());

        if(extractTag){
            //get tag
            for (CLRankingDetailEntity detail : dataList) {
                try {

                    keywordNameList.add(URLEncoder.encode(detail.getKeywordName(), "utf-8").toLowerCase());
                    if (keywordNameList.size() >= 100) {
                        //tag
                        List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeywordNameList(domainId, keywordNameList);
                        if (CollectionUtils.isNotEmpty(tagList)) {
                            for (GroupTagEntity groupTagEntity : tagList) {
                                String[] tagNameArray = groupTagEntity.getTagName().split(TAG_SPLIT);
                                String decodeKw = URLDecoder.decode(groupTagEntity.getKeywordName(), "utf-8").toLowerCase();
                                tagMap.put(decodeKw, tagNameArray);
                            }
                        }
                        keywordNameList.clear();
                    }
                    String key = detail.getKeywordName() + TAG_SPLIT + detail.getRank();
                    keywordMap.put(key, detail);

                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
            }

            if(CollectionUtils.isNotEmpty(keywordNameList)){
                List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeywordNameList(domainId, keywordNameList);
                if (CollectionUtils.isNotEmpty(tagList)) {
                    for (GroupTagEntity groupTagEntity : tagList) {
                        String[] tagNameArray = groupTagEntity.getTagName().split(TAG_SPLIT);
                        String decodeKw = URLDecoder.decode(groupTagEntity.getKeywordName(), "utf-8").toLowerCase();
                        tagMap.put(decodeKw, tagNameArray);
                    }
                }
            }

            for(String key : keywordMap.keySet()){

                String kw = key.split(TAG_SPLIT)[0];
                CLRankingDetailEntity detail = keywordMap.get(key);

                String[] tags = tagMap.get(kw);
                if(tags != null && tags.length > 0){
                    for(String tag: tags){
                        extractLines.add(appendData(detail, ownDomainId, tag));
                    }
                }else {
                    extractLines.add(appendData(detail, ownDomainId, null));
                }
            }
        }else {
            for (CLRankingDetailEntity detail : dataList) {
                extractLines.add(appendData(detail, ownDomainId, null));
            }
        }

        return extractLines;
    }

    public static String appendData(CLRankingDetailEntity clRankingDetailEntity, int domainId, String tagName) {
        StringBuffer line = new StringBuffer();

        line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);

        if(StringUtils.isBlank(clRankingDetailEntity.getUrl())){
            line.append("-").append(SPLIT);
            line.append("-").append(SPLIT);
            line.append("-").append(SPLIT);
            line.append("-");
        }else {
            line.append(ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
//            if (clRankingDetailEntity.getWebRank().equals(0)) {
//                line.append("101").append(SPLIT);
//            } else {
//                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
//            }
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
            line.append(clRankingDetailEntity.getAvgSearchVolume());
        }


        return line.toString();
    }

    private void copyFileToRemoteServer(int serverType, int domainId, String localFilePath, String remoteFilePath) throws Exception{//todo

        int getServerInfoDomainId = domainId;
        if (domainId == 2047) {
            getServerInfoDomainId = 765;
        }

        boolean success = serverAuthenticationInfoService.copyFileToRemoteServer(serverType, getServerInfoDomainId, localFilePath, remoteFilePath, null);

    }

    private static String getRootDomain(String fullDomain) {
        String domainName = null;
        try {
            domainName = StringUtils.reverseDelimited(fullDomain, '.');
            return StringUtils.reverseDelimited(InternetDomainName.from(domainName).topPrivateDomain().toString(), '.');
        } catch (Exception e) {
            try {
                if (StringUtils.startsWithIgnoreCase(domainName, "www.")) {
                    return StringUtils.reverseDelimited(StringUtils.removeStartIgnoreCase(domainName, "www."), '.');
                } else {
                    return StringUtils.reverseDelimited(domainName, '.');
                }
            } catch (Exception ex) {
            }
        }
        return null;
    }


    public static void main(String[] args) {


        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainIdList = args[0].split(",");
            } else {
                domainId = Integer.parseInt(args[0]);
            }

        }

        topX = Integer.parseInt(args[1]);
        device = args[2];

        RankExtractOneTime rankExtractOneTime = new RankExtractOneTime();

        if (domainIdList != null && domainIdList.length > 0) {

            for (String processingDomainId : domainIdList) {

                if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
                    Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
                    Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
                    while (sDate.compareTo(eDate) <= 0) {
                        processDate = sDate;
                        rankExtractOneTime.processForDomain(Integer.parseInt(processingDomainId));
                        sDate = DateUtils.addDays(sDate, 1);
                    }

                } else if(args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")){
                    processDate = FormatUtils.toDate(args[3], FormatUtils.DATE_PATTERN_2);
                    rankExtractOneTime.processForDomain(Integer.parseInt(processingDomainId));
                } else {
                    processDate = DateUtils.addDays(new Date(), -1);
                    rankExtractOneTime.processForDomain(Integer.parseInt(processingDomainId));
                }
            }

        } else {

            if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
                Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
                while (sDate.compareTo(eDate) <= 0) {
                    processDate = sDate;
                    rankExtractOneTime.processForDomain(domainId);
                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else if(args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")){
                processDate = FormatUtils.toDate(args[3], FormatUtils.DATE_PATTERN_2);
                rankExtractOneTime.processForDomain(domainId);
            } else {
                processDate = DateUtils.addDays(new Date(), -1);
                rankExtractOneTime.processForDomain(domainId);
            }

        }

        }


}
