package seoclarity.backend.onetime;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.RIDailyRankingService;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @date 2020-11-09
 * @path seoclarity.backend.onetime.CheckRILoad
 * 
 */
public class CheckRILoad {
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private RIDailyRankingService service;
	
	public static void main(String[] args) {
		CheckRILoad ins = new CheckRILoad();
		ins.process();
	}
	
	private void process() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		service = new RIDailyRankingService(true);
		
		List<OwnDomainEntity> domains = ownDomainEntityDAO.getByIdAsc();
		
		for (OwnDomainEntity domain : domains) {
			int engine = ScKeywordRankManager.getSearchEngineId(domain);
			int language = ScKeywordRankManager.getSearchLanguageId(domain);
			if (domain.isMobileDomain()) {
				continue;
			}
			if (Arrays.asList(new int[] {100, 255, 120, 150, 160, 165}).contains(engine)) {
				continue;
			}
			OwnDomainSettingEntity sett = ownDomainSettingEntityDAO.getSettingByDomainId(domain.getId());
			if (sett.getRegionId() != null && sett.getRegionId() > 0) {
				continue;
			}
			if (engine > 1 || language > 1) {
				
				check(engine, language, domain.getId(), domain.getName(), domain.isBroadMatch());
			}
		}
		
	}
	
	private void check(int engine, int language, int oid, String domainName, boolean isBroadMatch) {
		StringBuffer sql = new StringBuffer();
		
		String rootDomain = ClarityDBUtils.getRootDomain(domainName);
		
		sql.append(" select engine_id, language_id,                                                                    ");
		sql.append("     sum(if(true_rank = 1, cnt, 0)) as cnt1,                                                       ");
		sql.append("     sum(if(true_rank <= 3, cnt, 0)) as cnt3,                                                      ");
		sql.append("     sum(if(true_rank <= 5, cnt, 0)) as cnt5,                                                      ");
		sql.append("     sum(if(true_rank <= 10, cnt, 0)) as cnt10,                                                     ");
		sql.append("     sum(if(true_rank <= 20, cnt, 0)) as cnt20                                                     ");
		sql.append(" from (                                                                                            ");
		sql.append(" select                                                                                            ");
		sql.append("     engine_id, language_id, true_rank, count(distinct keyword_rankcheck_id, location_id) as cnt   ");
		sql.append(" from d_ranking_detail_intl_202011                                                                 ");
		sql.append(" where ranking_date = '2020-11-09' and location_id = 0  and own_domain_id = " + oid + "                                            ");
		sql.append(" and engine_id = " + engine + " and language_id = " + language + " ");
		if (rootDomain.equals(domainName)) {
			sql.append(" and root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "' and hrrd = 1                                               ");
		} else {
			sql.append(" and root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "'  and domain_reverse = '" + StringUtils.reverseDelimited(domainName, '.') + "' and hrd = 1                                               ");
		}
		sql.append(" and true_rank <= 20                                                                                ");
		sql.append(" group by engine_id, language_id, true_rank                                                        ");
		sql.append(" ) group by engine_id, language_id                                                                 ");
		
		List list1 = service.queryForAll(sql.toString());
		
		StringBuffer sql2 = new StringBuffer();
		sql2.append(" select engine_id, language_id,                                                                    ");
		sql2.append("     sum(if(true_rank = 1, cnt, 0)) as cnt1,                                                       ");
		sql2.append("     sum(if(true_rank <= 3, cnt, 0)) as cnt3,                                                      ");
		sql2.append("     sum(if(true_rank <= 5, cnt, 0)) as cnt5,                                                      ");
		sql2.append("     sum(if(true_rank <= 10, cnt, 0)) as cnt10,                                                     ");
		sql2.append("     sum(if(true_rank <= 20, cnt, 0)) as cnt20                                                     ");
		sql2.append(" from (                                                                                           ");
		sql2.append(" select                                                                                           ");
		sql2.append("     engine_id, language_id, true_rank, count(distinct keyword_rankcheck_id, location_id) as cnt  ");
		sql2.append(" from d_ranking_detail_intl_202011                                                                ");
		sql2.append(" where ranking_date = '2020-11-08' and location_id = 0  and own_domain_id = " + oid + "                                          ");
		sql.append(" and engine_id = " + engine + " and language_id = " + language + " ");
		if (rootDomain.equals(domainName)) {
			sql2.append(" and root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "' and hrrd = 1                                               ");
		} else {
			sql2.append(" and root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "'  and domain_reverse = '" + StringUtils.reverseDelimited(domainName, '.') + "' and hrd = 1                                               ");
		}
		sql2.append(" and true_rank <= 20                                                                              ");
		sql2.append(" and (engine_id, language_id, keyword_rankcheck_id) global in (                                   ");
		sql2.append("     select engine_id, language_id, keyword_rankcheck_id                                          ");
		sql2.append("     from d_ranking_info_intl_202011                                                              ");
		sql2.append("     where ranking_date = '2020-11-09' and location_id = 0    and own_domain_id = " + oid + "                                    ");
		sql2.append(" )                                                                                                ");
		sql2.append(" group by engine_id, language_id, true_rank                                                       ");
		sql2.append(" ) group by engine_id, language_id                                                                ");
		
		List list2 = service.queryForAll(sql2.toString());
		
		StringBuffer sql3 = new StringBuffer();
		sql3.append(" select engine_id, language_id,                                                                    ");
		sql3.append("     sum(if(true_rank = 1, cnt, 0)) as cnt1,                                                       ");
		sql3.append("     sum(if(true_rank <= 3, cnt, 0)) as cnt3,                                                      ");
		sql3.append("     sum(if(true_rank <= 5, cnt, 0)) as cnt5,                                                      ");
		sql3.append("     sum(if(true_rank <= 10, cnt, 0)) as cnt10,                                                     ");
		sql3.append("     sum(if(true_rank <= 20, cnt, 0)) as cnt20                                                     ");
		sql3.append(" from (                                                                                           ");
		sql3.append(" select                                                                                           ");
		sql3.append("     engine_id, language_id, true_rank, count(distinct keyword_rankcheck_id, location_id) as cnt  ");
		sql3.append(" from d_ranking_detail_intl_202011                                                                ");
		sql3.append(" where ranking_date = '2020-11-07' and location_id = 0  and own_domain_id = " + oid + "                                          ");
		sql.append(" and engine_id = " + engine + " and language_id = " + language + " ");
		if (rootDomain.equals(domainName)) {
		    sql3.append(" and root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "' and hrrd = 1                                               ");
		} else {
		    sql3.append(" and root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "'  and domain_reverse = '" + StringUtils.reverseDelimited(domainName, '.') + "' and hrd = 1                                               ");
		}
		sql3.append(" and true_rank <= 20                                                                              ");
		sql3.append(" and (engine_id, language_id, keyword_rankcheck_id) global in (                                   ");
		sql3.append("     select engine_id, language_id, keyword_rankcheck_id                                          ");
		sql3.append("     from d_ranking_info_intl_202011                                                              ");
		sql3.append("     where ranking_date = '2020-11-09' and location_id = 0    and own_domain_id = " + oid + "                                    ");
		sql3.append(" )                                                                                                ");
		sql3.append(" group by engine_id, language_id, true_rank                                                       ");
		sql3.append(" ) group by engine_id, language_id                                                                ");

		List list3 = service.queryForAll(sql3.toString());
		
		if (list1.size() > 0 && list2.size() > 0 && list3.size() > 0) {
		
		
		Map<String, Object> map = (Map<String, Object>) list1.get(0);
		String cnt1 = map.get("cnt1").toString();
		String cnt3 = map.get("cnt3").toString();
		String cnt5 = map.get("cnt5").toString();
		String cnt10 = map.get("cnt10").toString();
		String cnt20 = map.get("cnt20").toString();
		
		Map<String, Object> map2 = (Map<String, Object>) list2.get(0);
		String cnt21 = map2.get("cnt1").toString();
		String cnt23 = map2.get("cnt3").toString();
		String cnt25 = map2.get("cnt5").toString();
		String cnt210 = map2.get("cnt10").toString();
		String cnt220 = map2.get("cnt20").toString();
		
		Map<String, Object> map3 = (Map<String, Object>) list3.get(0);
		String cnt31 = map3.get("cnt1").toString();
		String cnt33 = map3.get("cnt3").toString();
		String cnt35 = map3.get("cnt5").toString();
		String cnt310 = map3.get("cnt10").toString();
		String cnt320 = map3.get("cnt20").toString();
		
		System.out.println("OID:" + oid  + ", engine:" + engine + ", language:" + language + ", ");
		System.out.println("cnt31:" + cnt31 + ", cnt21:" + cnt21 + ", chg:" + (Integer.valueOf(cnt21) - Integer.valueOf(cnt31)) + ", cnt1:" + cnt1 + ", chg:" + (Integer.valueOf(cnt1) - Integer.valueOf(cnt21)) );
		System.out.println("cnt33:" + cnt33 + ", cnt23:" + cnt23 + ", chg:" + (Integer.valueOf(cnt23) - Integer.valueOf(cnt33)) + ", cnt3:" + cnt3 + ", chg:" + (Integer.valueOf(cnt3) - Integer.valueOf(cnt23)) );
		System.out.println("cnt35:" + cnt35 + ", cnt25:" + cnt25+ ", chg:" + (Integer.valueOf(cnt25) - Integer.valueOf(cnt35)) + ", cnt5:" + cnt5 + ", chg:" + (Integer.valueOf(cnt5) - Integer.valueOf(cnt25)) );
		System.out.println("cnt310:" + cnt310 + ", cnt210:" + cnt210+ ", chg:" + (Integer.valueOf(cnt210) - Integer.valueOf(cnt310)) + ", cnt10:" + cnt10 + ", chg:" + (Integer.valueOf(cnt10) - Integer.valueOf(cnt210)) );
		System.out.println("cnt320:" + cnt320 + ", cnt220:" + cnt220+ ", chg:" + (Integer.valueOf(cnt220) - Integer.valueOf(cnt320)) + ", cnt20:" + cnt20 + ", chg:" + (Integer.valueOf(cnt20) - Integer.valueOf(cnt220)) );
		
		
		StringBuffer app = new StringBuffer();
		app.append(oid + "," + engine + "," + language + "," + domainName + "," + cnt31 + "," + cnt21 + "," + cnt1);
		app.append("," + cnt33 + "," + cnt23 + "," + cnt3);
		app.append("," + cnt35 + "," + cnt25 + "," + cnt5);
		app.append("," + cnt310 + "," + cnt210 + "," + cnt10);
		app.append("," + cnt320 + "," + cnt220 + "," + cnt20 + "\n");
		
		// oid,engine,language,domain,
		// cnt1_07,cnt1_08,cnt1_09,
		// cnt3_07,cnt3_08,cnt3_09,
		// cnt5_07,cnt5_08,cnt5_09,
		// cnt10_07,cnt10_08,cnt10_09,
		// cnt20_07,cnt20_08,cnt20_09,
		try {
			FileUtils.write(new File("/home/<USER>/xxx.csv"), app.toString(), "UTF-8", true);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		System.out.println("==============================================");
		}
	}

}
