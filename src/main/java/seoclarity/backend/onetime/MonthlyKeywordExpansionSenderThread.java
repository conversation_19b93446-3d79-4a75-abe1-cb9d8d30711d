package seoclarity.backend.onetime;

import com.amazonaws.services.sqs.AmazonSQS;
import com.google.gson.Gson;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class MonthlyKeywordExpansionSenderThread implements Runnable {

	private AmazonSQS amazonSQS;

	private String queryUrl;

	private List<KeywordProperty> keywords;

	public MonthlyKeywordExpansionSenderThread(AmazonSQS amazonSQS, String queryUrl, List<KeywordProperty> keywords) {
		super();
		this.amazonSQS = amazonSQS;
		this.queryUrl = queryUrl;
		this.keywords = keywords;
	}

	@Override
	public void run() {
		Map<String, String> messages = new HashMap<>();
		for (KeywordProperty keywordProperty : keywords) {
			try {
				if (messages.size() == 10) {
					SQSUtils.sendBatchMessageToQueue(amazonSQS, queryUrl, messages);
					messages = new HashMap<>();
				}
				messages.put(UUID.randomUUID().toString().replaceAll("-", ""), new Gson().toJson(keywordProperty));
				Thread.sleep(10);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

        if (messages.size() != 0 && messages.size() <= 10) {
            SQSUtils.sendBatchMessageToQueue(amazonSQS, queryUrl, messages);
        } else {
            // This should be impossilbe
            System.out.println("impossilbe: more than 0 or 10 left " + messages.size());
        }

	}

}
