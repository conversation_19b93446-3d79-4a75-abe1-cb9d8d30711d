package seoclarity.backend.onetime;

import com.google.gson.Gson;
import lombok.Data;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.clarity360.ReportCustomFileDao;
import seoclarity.backend.dao.clickhouse.clarity360.UrlAttributesDao;
import seoclarity.backend.entity.actonia.clarity360.ReportCustomFileEntity;
import seoclarity.backend.entity.clarity360.UrlAttributes;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=1235661278
 */
public class UrlAttributeUpload {

    private static final String IN_FILE_PATH = "/home/<USER>/source/radeL/urlUploadCustmorFile/tmp_file/9688_20231024_1703_yw203CvL8i_10w.csv";
    private static final String SPLIT_ATTR = "@@@";
    private static final char SPLIT_COL = '|';

    private static final int BATCH_SAVE_SIZE = 500;
    private static final int domainId = 9688;
    private Integer fileId = 26;


    CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(SPLIT_COL).withIgnoreEmptyLines(true);
    private UrlAttributesDao urlAttributesDao;
    private ReportCustomFileDao reportCustomFileDao;

    public UrlAttributeUpload() {
        urlAttributesDao = SpringBeanFactory.getBean("urlAttributesDao");
        reportCustomFileDao = SpringBeanFactory.getBean("reportCustomFileDao");
    }

    public static void main(String[] args) throws Exception {
        UrlAttributeUpload upload = new UrlAttributeUpload();
        upload.process();
    }

    private void insertReportCustomFile() {
        ReportCustomFileEntity reportCustomFile = new ReportCustomFileEntity();
        reportCustomFile.setEnabled(0);
        reportCustomFile.setOwnDomainId(domainId);
        reportCustomFile.setStorageType(1);
        reportCustomFile.setUploadStatus(1);
        reportCustomFile.setSourceFilename("out.csv");
        reportCustomFile.setFriendlyName("9688 custom file");
        reportCustomFile.setFtpFullPathFilename("/home/<USER>/public_html/360_custom_file/9688_20231024_1703_yw203CvL8i_10w.csv");
        reportCustomFile.setCreateUserId(214);
        reportCustomFile.setCreatedAt(new Date());
        fileId = reportCustomFileDao.insert(reportCustomFile);
        System.out.println("=====fileId:" + fileId);
    }

    private void process() {
//        insertReportCustomFile();
        CSVParser csvParser;
        List<CSVRecord> records;
        try {
            csvParser = csvFormat.parse(new FileReader(IN_FILE_PATH));
            records = csvParser.getRecords();
        } catch (IOException e) {
            System.out.println("=====parseCsvError");
            e.printStackTrace();
            return;
        }
        if (records.isEmpty()) {
            System.out.println("=====fileEmpty");
            return;
        }
        List<String> attrList = getAttrList(records);
        if (attrList.isEmpty()) {
            System.out.println("=====fileDontHaveAttr attrList is empty.");
            return;
        }
        loadIntoDB(records, attrList);
    }

    private List<String> getAttrList(List<CSVRecord> records) {
        System.out.println("====parseAttrListStart====");
        List<String> attrList = new ArrayList<>();
        for (int i = 0; i < records.size(); i++) {
            CSVRecord record = records.get(i);
            if (record.size() != 3 || StringUtils.isBlank(record.get(2))) {// skip no attribute url
                continue;
            }
            String[] split = record.get(2).split(SPLIT_ATTR);
            for (int j = 0; j < split.length; j++) { // get url attribute
                String attr = split[j].trim();
                if (StringUtils.isBlank(attr)) {
                    continue;
                }
                if (attrList.contains(attr)) {
                    continue;
                }
                attrList.add(attr);
            }
        }
        System.out.println("====parseAttrListEnd attrList:" + attrList.size());
        return attrList;
    }

    private void loadIntoDB(List<CSVRecord> records, List<String> attrList) {
        List<UrlAttributes> cdbList = new ArrayList<>();
        int skipCount = 0;
        int insertCount = 0;
        for (int i = 0; i < records.size(); i++) {
            CSVRecord record = records.get(i);
            if (record.size() != 3 || StringUtils.isBlank(record.get(2))) {// skip no attribute url
                skipCount++;
                continue;
            }
            UrlAttributes cdb = new UrlAttributes();
            for (int j = 0; j < record.size(); j++) {
                String data = record.get(j).trim();
                switch (j) {
                    case 0: cdb.setUrl(data); break;
                    case 1: cdb.setUrlMurmurHash(data); break;
                    case 2: {
                        List<String> valueList = initValueList(attrList.size());
                        String[] split = data.split(SPLIT_ATTR);
                        for (String tmpAttr : split) {
                            if (StringUtils.isEmpty(tmpAttr)) {
                                continue;
                            }
                            String attr = tmpAttr.trim();
                            if (!attrList.contains(attr)) {
                                continue;
                            }
                            valueList.set(attrList.indexOf(attr), "1");
                        }
                        cdb.setAttrKeys(attrList);
                        cdb.setAttrValues(valueList);
                        break;
                    }
                    default:
                        System.out.println("=====errorDataFormat");
                }
            }
            cdb.setDomainId(domainId);
            cdb.setFileId(fileId);
            cdbList.add(cdb);
            if (cdbList.size() >= BATCH_SAVE_SIZE) {
                insertCount += cdbList.size();
                urlAttributesDao.insertBatch(cdbList);
                cdbList.clear();
            }
        }
        if (!cdbList.isEmpty()) {
            insertCount += cdbList.size();
            urlAttributesDao.insertBatch(cdbList);
            cdbList.clear();
        }
//        reportCustomFileDao.updateStatusByIdAndOid(ReportCustomFileEntity.STATUS_LOADING_SUCCESS, domainId, fileId);
        System.out.println("====endInfo totalCount:" + records.size() + " skipCount:" + skipCount + " insertCount:" + insertCount);
    }

    private List<String> initValueList(int size) {
        List<String> valueList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            valueList.add("0");
        }
        return valueList;
    }
}
