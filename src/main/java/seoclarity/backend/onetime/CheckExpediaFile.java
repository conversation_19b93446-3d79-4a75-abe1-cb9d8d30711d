package seoclarity.backend.onetime;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.bot.BotDirectoryDao;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.summary.ClarityDBBotDailyUploadThreadMain;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class CheckExpediaFile {

    private String baseFilePath;

    private static Gson gson = new Gson();

    Map<String, Integer> fileNamePDomainIdMap = new HashMap<>();

    private BotDirectoryDao botDirectoryDao;

    public CheckExpediaFile() {
        botDirectoryDao = SpringBeanFactory.getBean("botDirectoryDao");
    }

    public static void main(String[] args) throws Exception {

        new CheckExpediaFile().process(args);
    }

    private void process(String[] args) throws Exception {
        if (args != null) {
            if (args.length > 0) {
                baseFilePath = args[0];
            }
        }
        File parentFile = new File(baseFilePath);
        File[] files = parentFile.listFiles();
        if (files == null) {
            return;
        }

        Set<String> ualist1 = new HashSet<>();
        Set<String> ualist2 = new HashSet<>();
        Set<String> ualist3 = new HashSet<>();

        Set<String> urllist1 = new HashSet<>();
        Set<String> urllist2 = new HashSet<>();
        Set<String> urllist3 = new HashSet<>();


        fileNamePDomainIdMap.put("www.expedia.com", 4765);
        fileNamePDomainIdMap.put("www.expedia.co.uk", 4739);
        fileNamePDomainIdMap.put("www.wotif.com", 10666);

        int expediaTotalCnt = 0;
        int expediaSkipCnt = 0;
        int expediaSkipUaCnt = 0;
        int expediaCnt = 0;

        int expediaUkTotalCnt = 0;
        int expediaUkSkipCnt = 0;
        int expediaUkSkipUaCnt = 0;
        int expediaUkCnt = 0;

        int wotifTotalCnt = 0;
        int wotifSkipCnt = 0;
        int wotifSkipUaCnt = 0;
        int wotifCnt = 0;

        int processFileCnt = 0;
        for (File file : files) {
            processFileCnt++;
            String fileName = file.getName();

            List<String> lines = FileUtils.readLines(file);
            for (int i = 0, length = lines.size(); i < length; i++) {
                BotJsonVO botJsonVO;
                if (StrUtil.startWith(lines.get(i), "[")) {
                    JSONArray jsonArray = JSONUtil.parseArray(lines.get(i));
                    for (JSONObject jsonObject : jsonArray.jsonIter()) {
                        botJsonVO = jsonObject.toBean(BotJsonVO.class);
                        String userAgent = botJsonVO.getData().getUserAgentStr();

                        if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                            expediaTotalCnt++;
                        } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                            expediaUkTotalCnt++;
                        }
                        else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                            wotifTotalCnt++;
                        }

                        //https://www.wrike.com/open.htm?id=*********
                        int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
                        if (uaGroupId < 0) {
                            if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                                if (ualist1.size() <= 10) {
                                    ualist1.add(userAgent);
                                }
                                expediaSkipUaCnt++;
                            } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                                if (ualist2.size() <= 10) {
                                    ualist2.add(userAgent);
                                }
                                expediaUkSkipUaCnt++;
                            }
                            else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                                if (ualist3.size() <= 10) {
                                    ualist3.add(userAgent);
                                }
                                wotifSkipUaCnt++;
                            }
                            continue;
                        }
                        //https://www.wrike.com/open.htm?id=1135983604
                        String uri = botJsonVO.getData().getReqPath();
                        if (UrlFilterUtil.shouldSkipUrl(uri)) {
                            if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                                if (urllist1.size() <= 10) {
                                    urllist1.add(uri);
                                }
                                expediaSkipCnt++;
                            } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                                if (urllist2.size() <= 10) {
                                    urllist2.add(uri);
                                }
                                expediaUkSkipCnt++;
                            }
                            else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                                if (urllist3.size() <= 10) {
                                    urllist3.add(uri);
                                }
                                wotifSkipCnt++;
                            }
                            continue;
                        }
                        botJsonVO.getData().setUaGroupId(uaGroupId);
                        BotJsonVO.BotDataVO dataVO = botJsonVO.getData();
                        int domainId = getIdByFileName(fileName);
                        String userAgentKey = domainId +"!_!"+ dataVO.getUserAgentStr();
                        Integer agentId = null;
                        if (CommonDataService.getDirectoryEntities().containsKey(userAgentKey)) {
                            agentId = CommonDataService.getDirectoryEntities().get(userAgentKey);
                        } else {
                            agentId = botDirectoryDao.getUserAgentIdByUserAgent(dataVO.getUserAgentStr(), domainId);
                        }
                        if (agentId == null) {
                            if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                                expediaSkipUaCnt++;
                            } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                                expediaUkSkipUaCnt++;
                            }
                            else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                                wotifSkipUaCnt++;
                            }
                            continue;
                        }

                        if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                            expediaCnt++;
                        } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                            expediaUkCnt++;
                        }
                        else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                            wotifCnt++;
                        }
                    }
                } else {
                    botJsonVO = gson.fromJson(lines.get(i), BotJsonVO.class);
                    String userAgent = botJsonVO.getData().getUserAgentStr();

                    if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                        expediaTotalCnt++;
                    } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                        expediaUkTotalCnt++;
                    }
                    else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                        wotifTotalCnt++;
                    }

                    //https://www.wrike.com/open.htm?id=*********
                    int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
                    if (uaGroupId < 0) {
                        if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                            if (ualist1.size() <= 10) {
                                ualist1.add(userAgent);
                            }
                            expediaSkipUaCnt++;
                        } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                            if (ualist2.size() <= 10) {
                                ualist2.add(userAgent);
                            }
                            expediaUkSkipUaCnt++;
                        }
                        else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                            if (ualist3.size() <= 10) {
                                ualist3.add(userAgent);
                            }
                            wotifSkipUaCnt++;
                        }
                        continue;
                    }
                    // https://www.wrike.com/open.htm?id=1135983604
                    String uri = botJsonVO.getData().getReqPath();
                    if (UrlFilterUtil.shouldSkipUrl(uri)) {
                        if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                            if (urllist1.size() <= 10) {
                                urllist1.add(uri);
                            }
                            expediaSkipCnt++;
                        } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                            if (urllist2.size() <= 10) {
                                urllist2.add(uri);
                            }
                            expediaUkSkipCnt++;
                        }
                        else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                            if (urllist3.size() <= 10) {
                                urllist3.add(uri);
                            }
                            wotifSkipCnt++;
                        }
                        continue;
                    }

                    botJsonVO.getData().setUaGroupId(uaGroupId);
                    BotJsonVO.BotDataVO dataVO = botJsonVO.getData();
                    int domainId = getIdByFileName(fileName);
                    String userAgentKey = domainId +"!_!"+ dataVO.getUserAgentStr();
                    Integer agentId = null;
                    if (CommonDataService.getDirectoryEntities().containsKey(userAgentKey)) {
                        agentId = CommonDataService.getDirectoryEntities().get(userAgentKey);
                    } else {
                        agentId = botDirectoryDao.getUserAgentIdByUserAgent(dataVO.getUserAgentStr(), domainId);
                    }
                    if (agentId == null) {
                        if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                            expediaSkipUaCnt++;
                        } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                            expediaUkSkipUaCnt++;
                        }
                        else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                            wotifSkipUaCnt++;
                        }
                        continue;
                    }

                    if (StringUtils.containsIgnoreCase(fileName, "www.expedia.com")) {
                        expediaCnt++;
                    } else if (StringUtils.containsIgnoreCase(fileName, "www.expedia.co.uk")) {
                        expediaUkCnt++;
                    }
                    else if (StringUtils.containsIgnoreCase(fileName, "www.wotif.com")) {
                        wotifCnt++;
                    }
                }
            }
        }
        System.out.println("totalFileCnt:" + files.length + " processFileCnt:" + processFileCnt);
        System.out.println("expediaInfo totalCnt:" + expediaTotalCnt + " skipUaCnt:" + expediaSkipUaCnt + " skipUrlCnt:" + expediaSkipCnt + " dataCnt:" + expediaCnt);
        System.out.println("expediaUkInfo totalCnt:" + expediaUkTotalCnt + " skipUaCnt:" + expediaUkSkipUaCnt + " skipUrlCnt:" + expediaUkSkipCnt + " dataCnt:" + expediaUkCnt);
        System.out.println("wotifInfo totalCnt:" + wotifTotalCnt + " skipUaCnt:" + wotifSkipUaCnt + " skipUrlCnt:" + wotifSkipCnt + " dataCnt:" + wotifCnt);
        System.out.println("4765");
        for (String string : ualist1) {
            System.out.println(string);
        }
        System.out.println("---------------");
        for (String string : urllist1) {
            System.out.println(string);
        }

        System.out.println("4739");
        for (String string : ualist2) {
            System.out.println(string);
        }
        System.out.println("---------------");
        for (String string : urllist2) {
            System.out.println(string);
        }

        System.out.println("10666");
        for (String string : ualist3) {
            System.out.println(string);
        }
        System.out.println("---------------");
        for (String string : urllist3) {
            System.out.println(string);
        }
    }

    private int getIdByFileName(String filename) {
        for (String key : fileNamePDomainIdMap.keySet()) {
            if (StringUtils.containsIgnoreCase(filename, key)) {
                return fileNamePDomainIdMap.get(key);
            }
        }
        return -1;
    }
}
