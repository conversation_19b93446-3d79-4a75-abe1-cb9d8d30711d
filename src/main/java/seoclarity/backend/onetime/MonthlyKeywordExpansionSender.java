package seoclarity.backend.onetime;

import cn.hutool.core.collection.CollectionUtil;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.DeleteQueueRequest;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.MonthlyKeywordExpansionSender" -Dexec.cleanupDaemonThreads=false -Dexec.args="***************:1463 /home/<USER>/keywordEx/save/rankcheck MONTHLY_KEYWORD_EXPANSION false"
 */
@CommonsLog
public class MonthlyKeywordExpansionSender {

	private static int threadCount = 40;
	private ExecutorService newFixedThreadPool =
			new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), new BackendThreadFactory());

	private static String SQS_NAME;
	private static String scribeIpAndPort;
	private static String fileLoc;
	private static boolean appendtoQ = false;
	private AmazonSQS amazonSQS = null;

	private CSVFormat csvFormat = CSVFormat.DEFAULT.withSkipHeaderRecord().withFirstRecordAsHeader();

	private void processKeywords() {
		try {
			amazonSQS = SQSUtils.getAmazonSQS();
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
		
		File file = new File(fileLoc);
		if (file.isDirectory()) {
			for (File file1 : file.listFiles()) {
				try {
					processFile(file1);
				} catch (IOException e) {
					e.printStackTrace();
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		} else {
			try {
				processFile(file);
			} catch (IOException e) {
				e.printStackTrace();
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
		while (!newFixedThreadPool.isShutdown()) {
			try {
				Thread.sleep(10 * 1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			int aliveCount = ((ThreadPoolExecutor)newFixedThreadPool).getActiveCount();
			System.out.println("Thread aliveCount : "+aliveCount);
			if (aliveCount == 0) {
				newFixedThreadPool.shutdown();
			}
		}
	}

	private void processFile(File file) throws IOException, InterruptedException {
		String fileName = file.getName();
		String[] arr = StringUtils.split(fileName, "__");
		String country = arr[0].toUpperCase();

		String qName = SQS_NAME+"_"+country;
		log.info("Process file : "+file.getAbsolutePath()+"# "+qName);
		String queryUrl = SQSUtils.createQueue(qName, amazonSQS);

		if(!appendtoQ){
			amazonSQS.deleteQueue(new DeleteQueueRequest(queryUrl));
			Thread.sleep(120000);
			queryUrl = SQSUtils.createQueue(qName, amazonSQS);
		}
		Thread.sleep(10000);

		CSVParser csvParser = new CSVParser(new FileReader(file), csvFormat);
		List<CSVRecord> csvRecords = csvParser.getRecords();
		List<KeywordProperty> keywordProperties = new ArrayList<>(csvRecords.size());
		for (CSVRecord csvRecord : csvRecords) {
			int engineId = NumberUtils.toInt(csvRecord.get("EngineId"), -1);
			int languageId = NumberUtils.toInt(csvRecord.get("LanguageId"), -1);
			if (engineId <= 0 || languageId <= 0) {
				log.info("Error on engine + language :"+engineId+"#"+languageId);
				continue;
			}
			float cpc = NumberUtils.toFloat(csvRecord.get("CPC"), 0);
			long sv = NumberUtils.toLong(csvRecord.get("Avg SearchVol"), 0);
			KeywordProperty keywordProperty = new KeywordProperty();
			keywordProperty.setKeywordText(FormatUtils.encodeKeyword(csvRecord.get("Keyword")).toLowerCase());
			keywordProperty.setSearchEngine(engineId);
			keywordProperty.setSearchLanguage(languageId);
			keywordProperty.setSearchVol(sv);
			keywordProperty.setCpc(cpc);
			keywordProperty.setScribeIpAndPort(scribeIpAndPort);
			keywordProperties.add(keywordProperty);
		}
		process(keywordProperties, amazonSQS, queryUrl);
	}

	private void process(List<KeywordProperty> tKeywords, AmazonSQS amazonSQS, String queryUrl) {
		System.out.println("totalCount:" + tKeywords.size());
		List<List<KeywordProperty>> lists = CollectionUtil.split(tKeywords, 500);
		for (List<KeywordProperty> list : lists) {
			newFixedThreadPool.execute(new MonthlyKeywordExpansionSenderThread(amazonSQS, queryUrl, list));
		}
	}

	public static void main(String[] args) {
		if (args != null && args.length >= 3) {
			scribeIpAndPort = args[0];
			fileLoc = args[1];
			SQS_NAME = args[2];
			if(args.length >= 4) {
				appendtoQ = Boolean.parseBoolean(args[3]);
			}
		} else {
			System.out.println("Error Parameter.");
			return;
		}

		MonthlyKeywordExpansionSender monthlyKeywordExpansionSender = new MonthlyKeywordExpansionSender();
		System.out.println(SQS_NAME + " " + scribeIpAndPort + " filePath:"+fileLoc+" appendtoQ: "+appendtoQ);
		monthlyKeywordExpansionSender.processKeywords();
	}
}
