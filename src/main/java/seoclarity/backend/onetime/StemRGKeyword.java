package seoclarity.backend.onetime;

import java.io.File;
import java.util.*;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.entity.KeywordStreamSearchengineCountryMappingEntity;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

public class StemRGKeyword {

    private static final String SPLIT = "	";
    private static final String IN_FILE_FOLDR = "/home/<USER>/rg-file";

    ///home/<USER>/rg-file/20231018/KeywordExpansionasana.com_cleanup_ok_16_17_ES_es_desktop_2023-10-17.csv
    ///home/<USER>/rg-file/20231013/KeywordExpansionwww.ajg.com_cleanup_ok_2_5_AU_en_desktop_2023-10-13.csv
    private static final List<String> IN_FILE_LIST = Arrays.asList(
            "/home/<USER>/rg-file/20231018/KeywordExpansionasana.com_cleanup_ok_16_17_ES_es_desktop_2023-10-17.csv");

    private static Map<String, String> lanaguageMap = new HashMap<>();

    private static String[] STOP_WORDS_ARR = new String[]{    // updated on 11/08/2021, confirmed by Mitul
            "a", "a\\'s", "according", "accordingly", "across", "actually", "afterwards", "again", "against", "ain\\'t", "all", "allow", "allows", "almost", "along", "already", "also", "although", "always", "am", "among", "amongst", "an", "and", "another",
            "any", "anybody", "anyhow", "anyone", "anything", "anyway", "anyways", "anywhere", "appropriate", "are", "aren\\'t", "around", "as", "asking", "associated", "at", "awfully", "b", "be", "became", "because", "become", "becomes",
            "becoming", "been", "before", "beforehand", "behind", "being", "below", "beside", "besides", "better", "between", "both", "brief", "but", "by", "c", "c\\'mon", "c\\'s", "came", "can", "can\\'t", "cannot", "cant", "certain", "certainly", "clearly",
            "co", "com", "come", "comes", "concerning", "consequently", "consider", "considering", "contain", "containing", "contains", "corresponding", "could", "couldn\\'t", "currently", "d", "definitely", "described", "despite", "did", "didn\\'t",
            "different", "do", "does", "doesn\\'t", "doing", "don\\'t", "done", "down", "downwards", "during", "e", "each", "edu", "eg", "eight", "either", "else", "elsewhere", "enough", "entirely", "especially", "et", "etc", "even", "ever", "every", "everybody",
            "everyone", "everything", "everywhere", "ex", "exactly", "example", "except", "f", "fifth", "first", "five", "followed", "following", "follows", "for", "former", "formerly", "forth", "four", "from", "further", "furthermore", "g", "get", "gets", "getting",
            "given", "gives", "go", "goes", "going", "gone", "got", "gotten", "greetings", "h", "had", "hadn\\'t", "happens", "hardly", "has", "hasn\\'t", "have", "haven\\'t", "having", "he", "he\\'s", "hello", "hence", "her", "here", "here\\'s", "hereafter", "hereby", "herein",
            "hereupon", "herself", "hi", "himself", "hither", "hopefully", "howbeit", "however", "i", "i\\'d", "i\\'ll", "i\\'m", "i\\'ve", "ie", "if", "ignored", "immediate", "in", "inasmuch", "inc", "indicate", "indicated", "indicates", "inner", "insofar", "instead", "into",
            "inward", "is", "isn\\'t", "it", "it\\'d", "it\\'ll", "it\\'s", "its", "itself", "j", "just", "k", "keeps", "kept", "know", "known", "knows", "l", "last", "lately", "later", "latter", "latterly", "lest", "let", "let\\'s", "like", "liked", "likely", "little", "looking", "ltd", "m", "mainly",
            "many", "may", "maybe", "mean", "meanwhile", "merely", "might", "more", "moreover", "mostly", "my", "myself", "n", "namely", "nd", "nearly", "necessary", "neither", "never", "nevertheless", "next", "nine", "no", "nobody", "non", "none",
            "noone", "nor", "normally", "not", "nothing", "novel", "nowhere", "o", "obviously", "of", "off", "often", "oh", "ok", "okay", "old", "on", "once", "one", "ones", "only", "onto", "or", "other", "others", "otherwise", "ought", "our", "ours", "ourselves",
            "overall", "own", "p", "particular", "particularly", "per", "perhaps", "placed", "please", "plus", "possible", "presumably", "probably", "provides", "q", "que", "quite", "qv", "r", "rather", "rd", "re", "really", "reasonably", "regarding", "regardless",
            "regards", "relatively", "respectively", "right", "s", "said", "same", "saw", "say", "saying", "says", "second", "secondly", "see", "seeing", "seem", "seemed", "seeming", "seems", "seen", "selves", "sensible", "sent", "serious", "seriously", "seven",
            "several", "shall", "she", "should", "shouldn\\'t", "since", "six", "so", "some", "somebody", "somehow", "someone", "something", "sometime", "sometimes", "somewhat", "somewhere", "soon", "sorry", "specified", "specify", "specifying",
            "still", "sub", "such", "sup", "sure", "t", "t\\'s", "taken", "tell", "tends", "th", "than", "thank", "thanks", "thanx", "that", "that\\'s", "thats", "the", "their", "theirs", "them", "themselves", "then", "thence", "there", "there\\'s", "thereafter", "thereby",
            "therefore", "therein", "theres", "thereupon", "these", "they", "they\\'d", "they\\'ll", "they\\'re", "they\\'ve", "third", "this", "thorough", "thoroughly", "those", "though", "three", "through", "throughout", "thru", "thus", "to", "together", "too",
            "toward", "towards", "tried", "tries", "try", "trying", "twice", "two", "u", "un", "unfortunately", "unless", "unlikely", "until", "unto", "up", "upon", "us", "useful", "uses", "using", "usually", "uucp", "v", "value", "various", "very", "via", "viz", "w",
            "wants", "was", "wasn\\'t", "way", "we", "we\\'d", "we\\'ll", "we\\'re", "we\\'ve", "welcome", "went", "were", "weren\\'t", "what\\'s", "whatever", "whence", "whenever", "where\\'s", "whereafter", "whereas", "whereby", "wherein", "whereupon", "wherever",
            "whether", "while", "whither", "who\\'s", "whoever", "whom", "whose", "willing", "wish", "with", "within", "without", "won\\'t", "wonder", "would", "wouldn\\'t", "x", "y", "yes", "yet", "you", "you\\'d", "you\\'ll", "you\\'re", "you\\'ve", "your", "yours", "yourself",
            "yourselves", "z", "zero"};

    private static List<String> STOP_WORDS_LIST = Arrays.asList(STOP_WORDS_ARR);
    private static Set<String> STOP_WORDS_SET = new HashSet<String>(STOP_WORDS_LIST);

    private final KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;

    public StemRGKeyword() {
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");
    }

    public static void main(String[] args) throws Exception {
        StemRGKeyword stemRGKeyword = new StemRGKeyword();
        stemRGKeyword.initLanguageMap();
        stemRGKeyword.process();
    }


    private void initLanguageMap() {
		lanaguageMap.put("1_1", "English");
		lanaguageMap.put("24_25", "English");
		lanaguageMap.put("6_8", "English");
        lanaguageMap.put("2_5", "English");
		lanaguageMap.put("17_18", "Dutch");
        lanaguageMap.put("16_17", "Spanish");
    }

    private void process() throws Exception {

        // KeywordExpansionwww.ajg.com_cleanup_ok_1_1_US_en_desktop_2023-10-07.csv
        for (String infileFullPath : IN_FILE_LIST) {
            String infileName = StringUtils.removeStart(infileFullPath, IN_FILE_FOLDR);
            String[] fileInfoSplit = infileName.split("_");
            String fileNamePrefix = fileInfoSplit[0];
            String fileNameSuffix = fileInfoSplit[8];
            String device = fileInfoSplit[7];
            int engineId = Integer.parseInt(fileInfoSplit[3]);
            int languageId = Integer.parseInt(fileInfoSplit[4]);

            String language = lanaguageMap.get(engineId + "_" + languageId);
            String OUTPUT_FILE = IN_FILE_FOLDR + fileNamePrefix + "_wordStem_ok_" + language + "_" + engineId + "_" + languageId + "_" + device + "_" + fileNameSuffix;

            if (true) {
                System.out.println("OUTPUT_FILE: ------- " + OUTPUT_FILE + ", language: " + language);
            }

            List<String> kwList = FileUtils.readLines(new File(infileFullPath), "UTF-8");
            List<String> resultList = new ArrayList<String>();
            for (int idx = 0; idx < kwList.size(); idx++) {
                String line = kwList.get(idx);
                String[] arr = line.split(SPLIT);
                engineId = Integer.parseInt(arr[0]);
                languageId = Integer.parseInt(arr[1]);
                String kwName = arr[2].trim();
				String impressions = arr[3].trim();

                List<String> streamList1 = SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(kwName.toLowerCase(), language); // TODO
                Collections.sort(streamList1);

                // TODO It is ok to include stop word as expand just to find "a b" and "b a"
                List<String> streamList = new ArrayList<String>();
                boolean hasStopWord = false;
                for (String stream : streamList1) {
                    if (STOP_WORDS_SET.contains(stream)) {
                        hasStopWord = true;
                        System.out.println(" ==SkipStopWord idx:" + idx + " stream:" + stream);
                    } else {
                        streamList.add(stream);
                    }
                }
                if (hasStopWord) {
                    System.out.println(" ==AfterDelStopWord idx:" + idx + " kw:" + kwName + " stream:" + streamList1 + "->" + streamList);
                }

                StringBuffer streamBuff = new StringBuffer();
                streamBuff.append("[");
                for (int k = 0; k < streamList.size(); k++) {
                    if (k > 0) {
                        streamBuff.append(",");
                    }
                    String stream = streamList.get(k).trim();
                    if (stream.contains("'")) {
                        stream = stream.replaceAll("'", ""); // TODO
                        System.out.println(" ==RemoveSingleQuote:" + " stream:" + streamList.get(k) + "->" + stream);
                    }
                    if (stream.contains("	")) {
                        stream = stream.replaceAll("	", ""); // TODO
                        System.out.println(" ==RemoveTab:" + " stream:" + streamList.get(k) + "->" + stream);
                    }
                    streamBuff.append("'").append(stream).append("'");
                }
                streamBuff.append("]");
                System.out.println(" ==GotStream idx:" + idx + " KW:" + kwName + "->" + streamBuff.toString());

                StringBuffer sb = new StringBuffer();
                // TODO
                sb.append(engineId).append(SPLIT);
                sb.append(languageId).append(SPLIT);
                sb.append(kwName).append(SPLIT);
				sb.append(impressions).append(SPLIT);
                sb.append(streamBuff.toString());
                resultList.add(sb.toString());

                if (resultList.size() >= 200) {
                    FileUtils.writeLines(new File(OUTPUT_FILE), "UTF-8", resultList, true);
                    resultList = new ArrayList<String>();
                }
            }

            if (resultList.size() > 0) {
                FileUtils.writeLines(new File(OUTPUT_FILE), "UTF-8", resultList, true);
            }
            System.out.println(" ======kwList:" + kwList.size() + "->" + resultList.size());
        }


    }
}