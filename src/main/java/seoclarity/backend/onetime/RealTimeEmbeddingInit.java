package seoclarity.backend.onetime;

import org.apache.commons.io.FileUtils;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.dao.actonia.EmbeddingTrimRuleDAO;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.util.List;

public class RealTimeEmbeddingInit {

    private CrawlRequestLogDAO crawlRequestLogDAO;
    private EmbeddingTrimRuleDAO embeddingTrimRuleDAO;

    public RealTimeEmbeddingInit() {
        crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
        embeddingTrimRuleDAO = SpringBeanFactory.getBean("embeddingTrimRuleDAO");
    }

    public static void main(String[] args) throws Exception {
        System.out.println("===start init real time embedding...");
        new RealTimeEmbeddingInit().process(args);
        System.out.println("===end init real time embedding...");
    }

    private void process(String[] args) throws Exception {
        String paramFilePath = args[0];
        int processType = 0;
        if (args.length > 1) {
            processType = Integer.parseInt(args[1]);
        }
        List<String> dataList = FileUtils.readLines(new File(paramFilePath));
        String ruleSql = dataList.get(0);
        String crawlLogUpdateSql = dataList.get(1);
        if (processType == 0) {
            embeddingTrimRuleDAO.insertBySql(ruleSql);
            crawlRequestLogDAO.updateEmbeddingCrawl(crawlLogUpdateSql);
        } else if (processType == 1) {
            embeddingTrimRuleDAO.insertBySql(ruleSql);
        } else if (processType == 2) {
            crawlRequestLogDAO.updateEmbeddingCrawl(crawlLogUpdateSql);
        }

    }
}
