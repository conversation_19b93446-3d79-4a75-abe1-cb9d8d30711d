package seoclarity.backend.onetime;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;


import seoclarity.backend.dao.actonia.GroupDictDAO;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.actonia.GroupDict;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.AddTagGroupRel" -Dexec.args=""
public class AddTagGroupRel {

	private OwnDomainEntityDAO ownDomainEntityDAO;
	private GroupDictDAO groupDictDAO;
	private GroupTagEntityDAO groupTagEntityDAO;
	
	public AddTagGroupRel() {
		groupDictDAO = SpringBeanFactory.getBean("groupDictDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		
	}

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		AddTagGroupRel addTagGroupRel = new AddTagGroupRel();
		addTagGroupRel.process();
	}
	
	private static List<String> groupNameList = new ArrayList<String>();
	static {
		groupNameList.add("Materials 1");
		groupNameList.add("Designer");
		groupNameList.add("Color");
		groupNameList.add("Event");
		groupNameList.add("Patterns/Texture");
		groupNameList.add("Product Type");
		groupNameList.add("Size");
		groupNameList.add("Style");
	}
	
	private static Integer OID = 9099;
	
	private void process() {
		
		Map<String, Integer> groupNameIdMap = new HashMap<String, Integer>();
		
		for(String groupName : groupNameList) {
			GroupDict groupDictV2 = groupDictDAO.findByNameAndOid(groupName, OID, GroupDict.TAG_TYPE_KEYWORD);
			if (groupDictV2 != null && groupDictV2.getId() > 0) {
				groupNameIdMap.put(groupName, groupDictV2.getId());
				System.out.println("Group id:" + groupDictV2.getId() + ", groupDictName:" + groupName);
			} else {
				//else add new group
				groupDictDAO.insert(groupName, OID, GroupDict.TAG_TYPE_KEYWORD);
				groupDictV2 = groupDictDAO.findByNameAndOid(groupName, OID, GroupDict.TAG_TYPE_KEYWORD);
				groupNameIdMap.put(groupName, groupDictV2.getId());
				System.out.println("Group id:" + groupDictV2.getId() + ", groupDictName:" + groupName);
			}
		}
		
		String content = "";
		Integer num = 1;
		String groupName = "";
		String tagName = "";
		Integer groupId = 0;
		Integer tagId = 0;
		GroupTagEntity groupTagEntity;
		
		List<Integer[]> tagGroupArrayList = new ArrayList<>();
		
		try {
			BufferedReader bf = new BufferedReader(new FileReader("/home/<USER>/group_tag_rel.txt"));
			while (content != null) {
				content = bf.readLine();

				if (content == null) {
					break;
				}
				num++;
				if (num % 100 == 0) {
					
					System.out.println("processing on line : " + num);
				}

				String[] arrays = StringUtils.split(content, "\t");
				if (ArrayUtils.isEmpty(arrays) || arrays.length < 2) {
					System.out.println("======= skip++++ content is not correct:" + content);
					continue;
				}

				groupName = arrays[0];
				tagName = arrays[1];

				groupId = groupNameIdMap.get(groupName);
				groupTagEntity = groupTagEntityDAO.getGroupTagEntity(OID, tagName, GroupTagEntity.TAG_TYPE_KEYWORD);
				if (groupTagEntity == null ||  groupTagEntity.getId() == 0) {
					System.out.println("Tag: " + tagName + " is not found!!");
					continue;
				}
				
				if (groupTagEntity.getGroupId() > 0) {
					System.out.println("Tag: " + tagName + " groupId exist!!");
					continue;
				}
				
				tagId = groupTagEntity.getId();
				
				tagGroupArrayList.add(new Integer[] {tagId, groupId});
			}
			
			System.out.println(" tagGroupArrayList size:" + tagGroupArrayList.size());
		
			for(Integer[] array : tagGroupArrayList) {
				
				System.out.println("tagId:" + array[0] + ", gid:" + array[1]);
			}
			
			groupTagEntityDAO.updateTagGroupId(tagGroupArrayList, OID);
			
			bf.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}

}
