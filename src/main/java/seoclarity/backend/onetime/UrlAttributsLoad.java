package seoclarity.backend.onetime;

import com.google.gson.Gson;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.io.FileUtils;
import seoclarity.backend.dao.clickhouse.clarity360.UrlAttributesJsonDao;
import seoclarity.backend.dao.clickhouse.clarity360.UrlAttributesFlatDao;
import seoclarity.backend.entity.clarity360.UrlAttributesFlatEntity;
import seoclarity.backend.entity.clarity360.UrlAttributesJsonEntity;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * https://www.wrike.com/open.htm?id=1199598577
 */
public class UrlAttributsLoad {

    private static final String DEFAULT_FILE_PATH = "/Users/<USER>/Desktop/URL-Attributs.txt";
    private static final int BATCH_INSERT = 20000;
    private static final Gson gson = new Gson();

    private static String filePath;
    private UrlAttributesFlatDao urlAttributesFlatDao;
    private UrlAttributesJsonDao urlAttributesJsonDao;

    public UrlAttributsLoad() {
        urlAttributesFlatDao = SpringBeanFactory.getBean("urlAttributesFlatDao");
        urlAttributesJsonDao = SpringBeanFactory.getBean("urlAttributesJsonDao");
    }

    public static void main(String[] args) {
        if (args != null) {
            filePath = args[0].trim();
        } else {
            filePath = DEFAULT_FILE_PATH;
        }
        System.out.println("===filePath: " + filePath);
        UrlAttributsLoad urlAttributsLoad = new UrlAttributsLoad();
        System.out.println("===start===");
        urlAttributsLoad.process();
        System.out.println("===end===");
    }

    private void process() {
        int loopCount = 0;
        int flatCount = 0;
        int jsonCount = 0;
        List<String> rows = null;
        List<UrlAttributesFlatEntity> urlFlatList = new ArrayList<>();
        List<UrlAttributesJsonEntity> urlJsonList = new ArrayList<>();
        try {
            rows = FileUtils.readLines(new File(filePath), "UTF-8");
        } catch (IOException e) {
            System.out.println("Error reading file");
            e.printStackTrace();
        }
        if (rows == null || rows.size() == 0) {
            System.out.println("====read empty file====");
            return;
        }
        List<String> headerList = getHeaderList(rows.get(0));
        for (int i = 1; i < rows.size(); i++) {
            loopCount++;
            UrlAttributesFlatEntity flat = new UrlAttributesFlatEntity();
            UrlAttributesJsonEntity json = new UrlAttributesJsonEntity();
            List<String> valueList = new ArrayList<>();
            SiteInfo siteInfo = new SiteInfo();

            String row = rows.get(i);
            String[] split = row.split("\t");
            String url = split[0].trim();
            String site_name = split[1].trim();
            String siteid = split[2].trim();
            String locale = split[3].trim();
            String lob = split[4].trim();
            String pageType = split[5].trim();
            String pageSubType = split[6].trim();
            String geoType = split[7].trim();
            String index = split[8].trim();
            String raw_score = split[9].trim();

            valueList.add(url);
            valueList.add(site_name);
            valueList.add(siteid);
            valueList.add(locale);
            valueList.add(lob);
            valueList.add(pageType);
            valueList.add(pageSubType);
            valueList.add(geoType);
            valueList.add(index);
            valueList.add(raw_score);

            String urlMurmur3Hash = MurmurHashUtils.getMurmurHash3_64(url);

            flat.setUrl(url);
            flat.setUrlMurmurHash(urlMurmur3Hash.toString());
            flat.setAttrKeys(headerList);
            flat.setAttrValues(valueList);
            urlFlatList.add(flat);

            siteInfo.setSite_name(site_name);
            siteInfo.setSiteid(siteid);
            siteInfo.setLocale(locale);
            siteInfo.setLob(lob);
            siteInfo.setPageType(pageType);
            siteInfo.setPageSubType(pageSubType);
            siteInfo.setGeoType(geoType);
            siteInfo.setIndex(index);
            siteInfo.setRaw_score(raw_score);

            json.setUrl(url);
            json.setUrlMurmurHash(urlMurmur3Hash.toString());
            json.setJson(gson.toJson(siteInfo));
            urlJsonList.add(json);

            if (urlFlatList.size() == BATCH_INSERT) {
                flatCount += urlFlatList.size();
                urlAttributesFlatDao.insertBatch(urlFlatList);
                urlFlatList.clear();
            }

            if (urlJsonList.size() == BATCH_INSERT) {
                jsonCount += urlJsonList.size();
                urlAttributesJsonDao.insertBatch(urlJsonList);
                urlJsonList.clear();
            }
        }

        if (urlFlatList.size() > 0) {
            flatCount += urlFlatList.size();
            urlAttributesFlatDao.insertBatch(urlFlatList);
        }

        if (urlJsonList.size() > 0) {
            jsonCount += urlJsonList.size();
            urlAttributesJsonDao.insertBatch(urlJsonList);
        }
        System.out.println("totalRows:" + rows.size() + " loopCount:" + loopCount + " flatInsertCount:" + flatCount + " jsonInsertCount:" + jsonCount);
    }

    private List<String> getHeaderList(String headerRow) {
        System.out.println("headerRow: " + headerRow);
        String[] split = headerRow.split("\t");
        return new ArrayList<>(Arrays.asList(split));
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode
    public static class SiteInfo {
        private String site_name;
        private String siteid;
        private String locale;
        private String lob;
        private String pageType;
        private String pageSubType;
        private String geoType;
        private String index;
        private String raw_score;
    }
}
