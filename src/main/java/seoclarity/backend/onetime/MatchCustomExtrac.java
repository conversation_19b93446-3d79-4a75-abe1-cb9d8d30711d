package seoclarity.backend.onetime;

import lombok.Data;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.dao.clickhouse.monthlyranking.LwebMonthlyRankingDao;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class MatchCustomExtrac {

    private static final String parentPath = "/home/<USER>/source/radeL/bot_project/tmp_file/";

    private int oid = 7529;
    private String queryDate = "2023-12-01";
    private String filePath = "/Users/<USER>/Desktop/7529.txt";
    private boolean isTest = true;
    private int processType;

    private LwebMonthlyRankingDao rgDao;
    private GscBaseDao gscBaseDao;


    public MatchCustomExtrac() {
        rgDao = SpringBeanFactory.getBean("lwebMonthlyRankingDao");
        gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
    }

    public static void main(String[] args) throws Exception {
        new MatchCustomExtrac().process(args);
    }

    private void initParam(String[] args) {
        if (args != null) {
            if (args.length > 0) {
                isTest = Boolean.parseBoolean(args[0]);
            }
            if (args.length > 1) {
                filePath = args[1];
            }
            if (args.length > 2) {
                oid = Integer.parseInt(args[2]);
            }
            if (args.length > 3) {
                queryDate = args[3];
            }
            if (args.length > 4) {
                processType = Integer.parseInt(args[4]);
            }
        }
    }

    private void process(String[] args) throws Exception {
        initParam(args);
        File file = new File(filePath);
        List<String> sourceList = FileUtils.readLines(file);
        Map<Integer, String> rowNumPUrlMap = new HashMap<>();
        List<String> rgSqlList = new ArrayList<>();
        List<String> gscSqlList = new ArrayList<>();
        for (int i = 0; i < sourceList.size(); i++) {
            String source = sourceList.get(i);
            String[] split = source.split("\t");
            String url = split[0].trim();
            String content = split[1].trim();
            if (StringUtils.equalsIgnoreCase(content, "['[]']")) {
                continue;
            }
            int rowNum = i + 1;

            content = content.replaceAll("\\['\\[\"", "");
            content = content.replaceAll("\"\\]'\\]", "");
            content = content.replaceAll("\\\\\\\\\"", "\"");
            content = content.replaceAll("\\\\\\\\/", "/");
            content = content.replaceAll("\\\\\\\\<", "<");
            content = content.replaceAll("\\\\\\\\>", ">");

            String text = Jsoup.clean(content, Whitelist.none());

            text = text.replaceAll("\\\\", "\\\\\\\\");
            text = text.replaceAll("\"", "\\\\\\\\\\\\\\\\\\\\\"");
            text = text.replaceAll("\\\\\'", "\\\'");

            String rgSql = genRGSql(text, "d_ranking_info_202401_us", "d_ranking_detail_202401_us", "com.macys", rowNum);
            rgSqlList.add(rgSql);
            String gscSql = genGSCSql(text, rowNum);
            gscSqlList.add(gscSql);

            rowNumPUrlMap.put(rowNum, url);
        }
        System.out.println("===info sourceTotal:" + sourceList.size() + " resMapSize:" + rowNumPUrlMap.size());


        if (processType == 1) {
            List<String> outPutList = new ArrayList<>();
            File outFile = new File(parentPath + oid + "_" + System.currentTimeMillis() + ".txt");
            FileUtils.writeLines(outFile, Collections.singletonList("keyword(s) matched\tURL with custom content\tavg search volume\trank\tranking url"));
            List<List<String>> rgSubList = splitList(rgSqlList, 5);
            String sql = "";
            int errorCnt = 0;
            int successCnt = 0;
            int totalCnt = rgSubList.size();

            for (List<String> subList : rgSubList) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < subList.size(); i++) {
                    sb.append(subList.get(i));
                    sb.append(" union all ");
                }
                sql = sb.substring(0, sb.lastIndexOf(" union all "));
                List<Map<String, Object>> mapList = null;
                try {
                    mapList = rgDao.executeSql(sql, null);
                } catch (Exception e) {
                    e.printStackTrace();
                    errorCnt++;
                    System.out.println("===errorSql:" + sql);
                }
                if (mapList == null || mapList.isEmpty()) {
                    continue;
                }
                successCnt++;
                Map<Integer, List<RgEntity>> resultList = mapList.stream().map(map -> {
                    String rowNum = map.get("rowNum").toString();
                    String keywordName = map.get("keywordName").toString();
                    String url = map.get("url").toString();
                    String avgSearchVolume = map.get("avgSearchVolume").toString();
                    String trueRank = map.get("trueRank").toString();

                    RgEntity rgEntity = new RgEntity();
                    rgEntity.setRowNum(Integer.parseInt(rowNum));
                    rgEntity.setKeywordName(keywordName);
                    rgEntity.setUrl(url);
                    rgEntity.setAvgSearchVolume(avgSearchVolume);
                    rgEntity.setTrueRank(trueRank);
                    return rgEntity;
                }).collect(Collectors.groupingBy(RgEntity::getRowNum, Collectors.toList()));
                for (int rowNum : resultList.keySet()) {
                    String customUrl = rowNumPUrlMap.get(rowNum);
                    List<RgEntity> rgEntityList = resultList.get(rowNum);
                    for (RgEntity rgEntity : rgEntityList) {
                        String output = rgEntity.getKeywordName() + "\t" + customUrl + "\t" + rgEntity.getAvgSearchVolume() + "\t" + rgEntity.getTrueRank() + "\t" + rgEntity.getUrl();
                        outPutList.add(output);
                    }
                }
                FileUtils.writeLines(outFile, outPutList, true);
                outPutList.clear();
                TimeUnit.MILLISECONDS.sleep(200);
            }
            System.out.println("===sqlInfo successCnt:" + successCnt + " errorCnt:" + errorCnt + " totalCnt:" + totalCnt);
        }

        if (processType == 2) {
            List<String> outPutList = new ArrayList<>();
            File outFile = new File(parentPath + oid + "_GSC_" + System.currentTimeMillis() + ".txt");
            FileUtils.writeLines(outFile, Collections.singletonList("keyword(s) matched\tURL with custom content\timpressions\tclicks\taverage positition\thighest ranking url"));
            List<List<String>> gscSubList = splitList(gscSqlList, 5);
            String sql = "";
            int errorCnt = 0;
            int successCnt = 0;
            int totalCnt = gscSubList.size();

            for (List<String> subList : gscSubList) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < subList.size(); i++) {
                    sb.append(subList.get(i));
                    sb.append(" union all ");
                }
                sql = sb.substring(0, sb.lastIndexOf(" union all "));
                List<Map<String, Object>> mapList = null;
                try {
                    mapList = gscBaseDao.executeSql(sql, null);
                } catch (Exception e) {
                    e.printStackTrace();
                    errorCnt++;
                    System.out.println("===errorSql:" + sql);
                }
                if (mapList == null || mapList.isEmpty()) {
                    continue;
                }
                successCnt++;
                Map<Integer, List<GscEntity>> resultList = mapList.stream().map(map -> {
                    String rowNum = map.get("rowNum").toString();
                    String keywordName = map.get("keywordName").toString();
                    String impressions = map.get("impressions").toString();
                    String clicks = map.get("clicks").toString();
                    String position = map.get("position").toString();
                    String url = map.get("url").toString();

                    GscEntity gscEntity = new GscEntity();
                    gscEntity.setRowNum(Integer.parseInt(rowNum));
                    gscEntity.setKeywordName(keywordName);
                    gscEntity.setUrl(url);
                    gscEntity.setImpressions(impressions);
                    gscEntity.setClicks(clicks);
                    gscEntity.setPosition(position);
                    return gscEntity;
                }).collect(Collectors.groupingBy(GscEntity::getRowNum, Collectors.toList()));
                for (int rowNum : resultList.keySet()) {
                    String customUrl = rowNumPUrlMap.get(rowNum);
                    List<GscEntity> rgEntityList = resultList.get(rowNum);
                    Map<String, List<GscEntity>> kwGroup = rgEntityList.stream().collect(Collectors.groupingBy(GscEntity::getKeywordName));
                    for (String keywordName : kwGroup.keySet()) {
                        List<GscEntity> gscEntityList = kwGroup.get(keywordName);
                        Collections.sort(gscEntityList, Comparator.comparing(GscEntity::getImpressions).reversed());
                        GscEntity gscEntity = gscEntityList.get(0);
                        String output = keywordName + "\t" + customUrl + "\t" + gscEntity.getImpressions() + "\t" + gscEntity.getClicks() + "\t" + gscEntity.getPosition() + "\t" + gscEntity.getUrl();
                        outPutList.add(output);
                    }
                }
                FileUtils.writeLines(outFile, outPutList, true);
                outPutList.clear();
                TimeUnit.MILLISECONDS.sleep(200);
            }
            System.out.println("===sqlInfo successCnt:" + successCnt + " errorCnt:" + errorCnt + " totalCnt:" + totalCnt);
        }
    }

    private String genRGSql(String content, String infoTable, String detailTable, String reverseDomain, int rowNum) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ").append(rowNum).append(" as rowNum, keyword_name as keywordName, url, avg_search_volume as avgSearchVolume, true_rank as trueRank ");
        sb.append("from ( ");
        sb.append("select keyword_rankcheck_id, keyword_name ");
        sb.append("from ").append(infoTable).append(" ");
        sb.append("where hasSubstr(tokens(lower('\"");
        sb.append(content);
        sb.append("\"')) ,tokens(keyword_name)) =1) ");
        sb.append("inner join ( ");
        sb.append("select keyword_rankcheck_id, url, avg_search_volume, true_rank ");
        sb.append("from ").append(detailTable).append(" ");
        sb.append("where root_domain_reverse ='").append(reverseDomain).append("' and hrrd=1 ");
        sb.append(")using (keyword_rankcheck_id)");
        return sb.toString();
    }

    private String genGSCSql(String content, int rowNum) {
        StringBuilder sb = new StringBuilder();
        sb.append("select ").append(rowNum).append(" as rowNum, any(keyword_name) as keywordName, sum(impressions) as impressions, sum(clicks) as clicks, avg(position) as position, url FROM gsc_daily_insert where hasSubstr(tokens(lower('\"");
        sb.append(content);
        sb.append("\"')), tokens(keyword_name)) =1 and (own_domain_id = ").append(oid).append(") AND (log_date >= '").append(queryDate).append("') and keyword_name!='' and (type = 1)");
        sb.append("group by keyword_hash, url");
        return sb.toString();
    }

    public <T> List<List<T>> splitList(List<T> originalList, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        int fromIndex = 0;

        while (fromIndex < originalList.size()) {
            int toIndex = Math.min(fromIndex + batchSize, originalList.size());
            batches.add(originalList.subList(fromIndex, toIndex));
            fromIndex = toIndex;
        }

        return batches;
    }

    @Data
    public static class RgEntity {
        private int rowNum;
        private String keywordName;
        private String impressions;
        private String url;
        private String avgSearchVolume;
        private String trueRank;
    }

    @Data
    public static class GscEntity {
        private int rowNum;
        private String keywordName;
        private String impressions;
        private String clicks;
        private String position;
        private String url;
    }
}
