package seoclarity.backend.onetime;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-12-03 14:20
 **/
public class Get12MonthSv {

    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;
    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;

    public Get12MonthSv() {
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
    }

    private void process(String filePath, int engineId, int languageId) throws IOException {
        CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("Keyword","Avg Search Volume","Oct 2018 Rank","CPC","Estimated Monthly Traffic","URL").withSkipHeaderRecord();
        CSVParser csvParser = new CSVParser(new FileReader(filePath), csvFormat);
        CSVFormat outCsvFormat = CSVFormat.DEFAULT.withHeader("Keyword","Avg Search Volume","CPC", "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December");
        CSVPrinter csvPrinter = new CSVPrinter(new FileWriter(filePath+"_withSv"), outCsvFormat);
        List<CSVRecord> csvRecordList = csvParser.getRecords();
        for (CSVRecord csvRecord : csvRecordList) {
            String kName = FormatUtils.encodeKeyword(csvRecord.get("Keyword"));
            SeoClarityKeywordEntity clarityKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(kName);
            if (null == clarityKeywordEntity || clarityKeywordEntity.getId() == 0) {
                System.out.println("can't find kName : "+kName);
                continue;
            }
            KeywordAdwordsEntity adwordsEntity = keywordAdwordsEntityDAO.getById(engineId,languageId,0, clarityKeywordEntity.getId());
            if (null == adwordsEntity) {
                System.out.println("can't find sv : "+kName+" kId : "+clarityKeywordEntity.getId());
                continue;
            }
            csvPrinter.printRecord(csvRecord.get("Keyword"),
                    csvRecord.get("Avg Search Volume"),
                    csvRecord.get("CPC"),
                    adwordsEntity.getMonthlySearchVolume1(),
                    adwordsEntity.getMonthlySearchVolume2(),
                    adwordsEntity.getMonthlySearchVolume3(),
                    adwordsEntity.getMonthlySearchVolume4(),
                    adwordsEntity.getMonthlySearchVolume5(),
                    adwordsEntity.getMonthlySearchVolume6(),
                    adwordsEntity.getMonthlySearchVolume7(),
                    adwordsEntity.getMonthlySearchVolume8(),
                    adwordsEntity.getMonthlySearchVolume9(),
                    adwordsEntity.getMonthlySearchVolume10(),
                    adwordsEntity.getMonthlySearchVolume11(),
                    adwordsEntity.getMonthlySearchVolume12());

        }
        csvPrinter.flush();
        csvPrinter.close();

    }

    public static void main(String[] args) throws IOException {
        Get12MonthSv get12MonthSv = new Get12MonthSv();
        get12MonthSv.process("/home/<USER>/hsn_com.csv", 99, 1);
    }

}
