package seoclarity.backend.onetime;

import org.apache.commons.lang.StringUtils;
import seoclarity.backend.upload.Utils.WriteUtils;

import java.io.*;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.onetime
 * @author: cil
 * @date: 2021-07-21 13:28
 **/
public class GetCountryFromFile {

    static final String READ_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/domain_ideas.csv";

    static final String WRITE_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/country.txt";



    public static void main(String[] args) {
        Set<String> ukList = readFile(READ_PATH);
        for (String str:
        ukList ) {
            WriteUtils.write(WRITE_PATH,str);

        }
    }
    private static Set<String> readFile(String path) {
        Set<String> dataList = new HashSet<>();

        File fin = new File(path);
        BufferedReader br = null;
        try {
            FileInputStream fis = new FileInputStream(fin);
            //Construct BufferedReader from InputStreamReader
            br = new BufferedReader(new InputStreamReader(fis));
            String line;
            while ((line = br.readLine()) != null) {
                String[] split = line.split("\t");
                if (split.length < 3)
                    continue;
                dataList.add(split[1] + "\t" + split[2]);

            }
            return dataList;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;


    }

}
