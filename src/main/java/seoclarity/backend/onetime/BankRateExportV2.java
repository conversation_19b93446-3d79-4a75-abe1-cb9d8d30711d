package seoclarity.backend.onetime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.clickhouse.pixelheight.PixelDetailDao;
import seoclarity.backend.entity.clickhouse.PixelDetailEntity;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desktop:
 * nohup /usr/bin/mvn -f /home/<USER>/source/rade/clarity-backend-scripts/pom.xml exec:java -Dexec.mainClass="seoclarity.backend.onetime.BankRateExportV2" -Dexec.cleanupDaemonThreads=false -Dexec.args="false /home/<USER>/source/hao/master/clarity-backend-scripts/files/bankRateFiles/kwFiles/d_9112_kw.txt 9112 2024-06-16 /home/<USER>/source/hao/master/clarity-backend-scripts/files/bankRateFiles/outputFiles/9112_0616_desktop_trueRank.csv - 0 com.bankrate.www 1"  >> ./log/dExport_trank_0616_01.log 2>&1 &
 * mobile:
 * nohup /usr/bin/mvn -f /home/<USER>/source/rade/clarity-backend-scripts/pom.xml exec:java -Dexec.mainClass="seoclarity.backend.onetime.BankRateExportV2" -Dexec.cleanupDaemonThreads=false -Dexec.args="false /home/<USER>/source/hao/master/clarity-backend-scripts/files/bankRateFiles/kwFiles/m_9112_kw.txt 9112 2024-06-16 /home/<USER>/source/hao/master/clarity-backend-scripts/files/bankRateFiles/outputFiles/9112_0616_mobile_trueRank.csv - 1 com.bankrate.www 1"  >> ./log/mExport_trank_0616_01.log 2>&1 &
 */
public class BankRateExportV2 {


    private static final String DEFAULT_NULL = "n/a";
    private static final String DEFAULT_EMPTY = "";
    private static final String DEFAULT_RANK = "100+";
    private static final String SPLIT = "\t";
    private static final int BATCH_SIZE = 500;

    private static final List<String> pdRankCheckList = Arrays.asList("162119", "197648", "156857", "106859");
    private static final List<String> ttkRankCheckList = Collections.singletonList("129300");
    private static final List<String> paaRankCheckList1 = Collections.singletonList("10041");
    private static final List<String> paaRankCheckList2 = Collections.singletonList("37335");
    private static final List<String> discussRankList = Collections.singletonList("121292");

    private boolean isTest = true;
    private String sourceFilePath;
    private int domainId;
    private String rankingDate;
    private String outputFilePath;
    private String exitsFilePath;
    private int device; // 0 desktop 1 mobile
    private String domain; // com.bankrate.www
    private int rankingType = 0; // note: only for url rank -> 0:visualRank 1:trueRank 2: webRank

    private PixelDetailDao pixelDetailDao;

    public BankRateExportV2() {
        pixelDetailDao = SpringBeanFactory.getBean("pixelDetailDao");
    }


    public static void main(String[] args) {
        new BankRateExportV2().process(args);
    }

    private void process(String[] args) {
        initParam(args);
        if (isTest) {
            checkFileKw();
        } else {
            exportBankRate();
        }
    }

    /*private void exportExpedia() {
        File sourceFile = new File(sourceFilePath);
        if (!sourceFile.exists()) {
            System.out.println("file not exist:" + sourceFilePath);
            return;
        }
        List<String> sourceFileLines = null;
        try {
            sourceFileLines = FileUtils.readLines(sourceFile);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (sourceFileLines == null || sourceFileLines.isEmpty()) {
            System.out.println("file empty:" + sourceFilePath);
            return;
        }
        List<Integer> kwIdList = new ArrayList<>();
        Map<Integer, String> idPKwMap = new HashMap<>();
        for (String sourceFileLine : sourceFileLines) {
            String[] split = sourceFileLine.split(SPLIT);
            int kwId = Integer.parseInt(split[0].trim());
            kwIdList.add(kwId);
            idPKwMap.put(kwId, split[1].trim());
        }
        List<List<Integer>> lists = CollectionSplitUtils.splitCollectionBySize(kwIdList, BATCH_SIZE);
        List<ExportInfo> exportInfoList = new ArrayList<>();
        for (List<Integer> subKwIdList : lists) {
            List<PixelDetailEntity> kwInfoBatch = pixelDetailDao.getKwInfoBatch(domainId, rankingDate, subKwIdList, device);
            if (kwInfoBatch == null || kwInfoBatch.isEmpty()) {
                continue;
            }
            Map<Integer, List<PixelDetailEntity>> idPDetailMap = kwInfoBatch.stream().collect(Collectors.groupingBy(PixelDetailEntity::getKeywordRankcheckId));
            for (Map.Entry<Integer, List<PixelDetailEntity>> entry : idPDetailMap.entrySet()) {
                Integer kwId = entry.getKey();
                List<PixelDetailEntity> detailList = entry.getValue();
                ExportInfo exportInfo = getExportInfo(idPKwMap.get(kwId), detailList);
                exportInfoList.add(exportInfo);
            }
            if (exportInfoList.size() >= 100) {
                exportResult(exportInfoList);
                exportInfoList.clear();
            }
        }
        if (!exportInfoList.isEmpty()) {
            exportResult(exportInfoList);
        }
    }*/

    private void exportBankRate() {
        File sourceFile = new File(sourceFilePath);
        if (!sourceFile.exists()) {
            System.out.println("file not exist:" + sourceFilePath);
            return;
        }
        List<String> sourceFileLines = null;
        try {
            sourceFileLines = FileUtils.readLines(sourceFile);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (sourceFileLines == null || sourceFileLines.isEmpty()) {
            System.out.println("file empty:" + sourceFilePath);
            return;
        }
        List<Integer> kwIdList = new ArrayList<>();
        Map<Integer, String> idPKwMap = new HashMap<>();
        for (String sourceFileLine : sourceFileLines) {
            String[] split = sourceFileLine.split(SPLIT);
            int kwId = Integer.parseInt(split[0].trim());
            kwIdList.add(kwId);
            idPKwMap.put(kwId, split[1].trim());
        }
        List<List<Integer>> lists = CollectionSplitUtils.splitCollectionBySize(kwIdList, BATCH_SIZE);
        List<ExportInfo> exportInfoList = new ArrayList<>();
        for (List<Integer> subKwIdList : lists) {
            List<PixelDetailEntity> kwInfoBatch = pixelDetailDao.getKwInfoBatch(domainId, rankingDate, subKwIdList, device);
            if (kwInfoBatch == null || kwInfoBatch.isEmpty()) {
                continue;
            }
            Map<Integer, List<PixelDetailEntity>> idPDetailMap = kwInfoBatch.stream().collect(Collectors.groupingBy(PixelDetailEntity::getKeywordRankcheckId));

            List<PixelDetailEntity> dafCntBatch = pixelDetailDao.getKwDafCntBatch(domainId, rankingDate, subKwIdList, device);
            Map<Integer, Integer> idPDafCntMap = dafCntBatch.stream().collect(Collectors.toMap(PixelDetailEntity::getKeywordRankcheckId, PixelDetailEntity::getDafCnt, (var3, var4) -> var3));

            for (Map.Entry<Integer, List<PixelDetailEntity>> entry : idPDetailMap.entrySet()) {
                Integer kwId = entry.getKey();
                List<PixelDetailEntity> detailList = entry.getValue();
                ExportInfo exportInfo = getExportInfo(idPKwMap.get(kwId), detailList);
                if (idPDafCntMap.containsKey(kwId)) {
                    exportInfo.setDafCnt(idPDafCntMap.get(kwId).toString());
                } else {
                    exportInfo.setDafCnt(DEFAULT_EMPTY);
                }
                exportInfoList.add(exportInfo);
            }
            if (exportInfoList.size() >= 100) {
                exportResult(exportInfoList);
                exportInfoList.clear();
            }
        }
        if (!exportInfoList.isEmpty()) {
            exportResult(exportInfoList);
        }
    }

    private void exportResult(List<ExportInfo> exportInfoList) {
        List<String> outputList = exportInfoList.stream().map(info -> info.getKeywordName() + SPLIT + info.getSearchVolume() + SPLIT + info.getDomainRank() + SPLIT + info.getUrl() + SPLIT + info.getPdRank() + SPLIT + info.getDafCnt()).collect(Collectors.toList());
        try {
            FileUtils.writeLines(new File(outputFilePath), outputList, true);
        } catch (IOException e) {
            System.out.println("===outputError:" + outputList);
            e.printStackTrace();
        }
    }

    private ExportInfo getExportInfo(String keywordName, List<PixelDetailEntity> detailList) {
        ExportInfo result = new ExportInfo();
        result.setKeywordName(keywordName);
        long avgSearchVolume = detailList.get(0).getAvgSearchVolume();
        avgSearchVolume = SvRound(avgSearchVolume);
        result.setSearchVolume(avgSearchVolume);

        // 处理url rank
        List<PixelDetailEntity> urlRankInfoList = null;
        if (rankingType == 0) {
            urlRankInfoList = detailList.stream().filter(detail -> StringUtils.isNotEmpty(detail.getDomainReverse()) && StringUtils.equalsIgnoreCase(domain, detail.getDomainReverse()) && detail.getType() == 1).sorted((Comparator.comparing(PixelDetailEntity::getVisualRank))).collect(Collectors.toList());
        } else if (rankingType == 1) {
            urlRankInfoList = detailList.stream().filter(detail -> StringUtils.isNotEmpty(detail.getDomainReverse()) && StringUtils.equalsIgnoreCase(domain, detail.getDomainReverse()) && detail.getType() == 1).sorted((Comparator.comparing(PixelDetailEntity::getTrueRank))).collect(Collectors.toList());
        } else {
            urlRankInfoList = detailList.stream().filter(detail -> StringUtils.isNotEmpty(detail.getDomainReverse()) && StringUtils.equalsIgnoreCase(domain, detail.getDomainReverse()) && detail.getType() == 1).sorted((Comparator.comparing(PixelDetailEntity::getWebRank))).collect(Collectors.toList());
        }

        if (urlRankInfoList.isEmpty()) {
            result.setUrl(DEFAULT_EMPTY);
            result.setDomainRank(DEFAULT_RANK);
        } else {
            PixelDetailEntity urlRankInfo = urlRankInfoList.get(0);
            result.setUrl(urlRankInfo.getUrl());
            result.setDomainRank(String.valueOf(urlRankInfo.getVisualRank()));
        }

        List<PixelDetailEntity> pdRankInfoList = new ArrayList<>();

        for (PixelDetailEntity pixelDetailEntity : detailList) {
            List<String> typeFullArray = pixelDetailEntity.getTypeMainList();
            if (typeFullArray != null && !typeFullArray.isEmpty()) {
                if (listsAreEqual(discussRankList, typeFullArray)) {
                    pdRankInfoList.add(pixelDetailEntity);
                }
            }
        }

        if (pdRankInfoList.isEmpty()) {
            result.setPdRank(DEFAULT_EMPTY);
        } else {
            PixelDetailEntity pixelDetailEntity = pdRankInfoList.stream().sorted((Comparator.comparing(PixelDetailEntity::getVisualRank))).collect(Collectors.toList()).get(0);
            result.setPdRank(String.valueOf(pixelDetailEntity.getVisualRank()));
        }

        return result;
    }

    /*private ExportInfo getExportInfo(String keywordName, List<PixelDetailEntity> detailList) {
        ExportInfo result = new ExportInfo();
        result.setKeywordName(keywordName);
        long avgSearchVolume = detailList.get(0).getAvgSearchVolume();
        avgSearchVolume = SvRound(avgSearchVolume);
        result.setSearchVolume(avgSearchVolume);

        // 处理url rank
        List<PixelDetailEntity> urlRankInfoList = detailList.stream().filter(detail -> StringUtils.isNotEmpty(detail.getDomainReverse()) && DOMAIN.equals(detail.getDomainReverse()) && detail.getType() == 1).sorted((Comparator.comparing(PixelDetailEntity::getVisualRank))).collect(Collectors.toList());
        if (urlRankInfoList.isEmpty()) {
            result.setUrl(DEFAULT_NULL);
            result.setDomainRank(DEFAULT_NULL);
        } else {
            PixelDetailEntity urlRankInfo = urlRankInfoList.get(0);
            result.setUrl(urlRankInfo.getUrl());
            result.setDomainRank(String.valueOf(urlRankInfo.getVisualRank()));
        }

        List<PixelDetailEntity> pdRankInfoList = new ArrayList<>();
        List<PixelDetailEntity> paaRankInfoList = new ArrayList<>();
        List<PixelDetailEntity> ttkRankInfoList = new ArrayList<>();

        for (PixelDetailEntity pixelDetailEntity : detailList) {
            List<String> typeFullArray = pixelDetailEntity.getTypeFullList();
            if (typeFullArray != null && !typeFullArray.isEmpty()) {
                if (listsAreEqual(pdRankCheckList, typeFullArray)) {
                    pdRankInfoList.add(pixelDetailEntity);
                    continue;
                }
            }

            List<String> typeMainArray = pixelDetailEntity.getTypeMainList();
            if (typeMainArray != null && !typeMainArray.isEmpty()) {
                if (listsAreEqual(paaRankCheckList1, typeMainArray)) {
                    paaRankInfoList.add(pixelDetailEntity);
                } else if (listsAreEqual(paaRankCheckList2, typeMainArray)) {
                    paaRankInfoList.add(pixelDetailEntity);
                } else if (listsAreEqual(ttkRankCheckList, typeMainArray)) {
                    ttkRankInfoList.add(pixelDetailEntity);
                }
            }
        }

        if (pdRankInfoList.isEmpty()) {
            result.setPdRank(DEFAULT_NULL);
        } else {
            PixelDetailEntity pixelDetailEntity = pdRankInfoList.stream().sorted((Comparator.comparing(PixelDetailEntity::getVisualRank))).collect(Collectors.toList()).get(0);
            result.setPdRank(String.valueOf(pixelDetailEntity.getVisualRank()));
        }

        if (paaRankInfoList.isEmpty()) {
            result.setPaaRank(DEFAULT_NULL);
        } else {
            PixelDetailEntity pixelDetailEntity = paaRankInfoList.stream().sorted((Comparator.comparing(PixelDetailEntity::getVisualRank))).collect(Collectors.toList()).get(0);
            result.setPaaRank(String.valueOf(pixelDetailEntity.getVisualRank()));
        }

        if (ttkRankInfoList.isEmpty()) {
            result.setTtkRank(DEFAULT_NULL);
        } else {
            PixelDetailEntity pixelDetailEntity = ttkRankInfoList.stream().sorted((Comparator.comparing(PixelDetailEntity::getVisualRank))).collect(Collectors.toList()).get(0);
            result.setTtkRank(String.valueOf(pixelDetailEntity.getVisualRank()));
        }

        return result;
    }*/

    private void checkFileKw() {
        File sourceFile = new File(sourceFilePath);
        if (!sourceFile.exists()) {
            System.out.println("file not exist:" + sourceFilePath);
            return;
        }
        List<String> sourceFileLines = null;
        try {
            sourceFileLines = FileUtils.readLines(sourceFile);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (sourceFileLines == null || sourceFileLines.isEmpty()) {
            System.out.println("file empty:" + sourceFilePath);
            return;
        }
        List<Integer> kwIdList = new ArrayList<>();
        Map<Integer, String> idPKwMap = new HashMap<>();
        for (String sourceFileLine : sourceFileLines) {
            String[] split = sourceFileLine.split(SPLIT);
            int kwId = Integer.parseInt(split[0].trim());
            kwIdList.add(kwId);
            idPKwMap.put(kwId, split[1].trim());
        }

        List<String> outputList = new ArrayList<>();
        List<String> exitsOutputList = new ArrayList<>();
        int notExistCnt = 0;
        int existCnt = 0;
        List<List<Integer>> lists = CollectionSplitUtils.splitCollectionBySize(kwIdList, BATCH_SIZE);
        for (List<Integer> subKwIdList : lists) {
            List<PixelDetailEntity> detaiList = pixelDetailDao.checkExistBatch(domainId, rankingDate, subKwIdList);
            if (detaiList == null || detaiList.isEmpty()) {
                for (Integer kwId : subKwIdList) {
                    outputList.add(kwId + SPLIT + idPKwMap.get(kwId));
                }
                notExistCnt = outputNotExitsKw(outputList, notExistCnt);
            } else {
                existCnt += detaiList.size();
                Set<Integer> existKwSet = detaiList.stream().map(PixelDetailEntity::getKeywordRankcheckId).collect(Collectors.toSet());
                for (Integer kwId : subKwIdList) {
                    if (!existKwSet.contains(kwId)) {
                        outputList.add(kwId + SPLIT + idPKwMap.get(kwId));
                    } else {
                        exitsOutputList.add(kwId + SPLIT + idPKwMap.get(kwId));
                    }
                }
                notExistCnt = outputNotExitsKw(outputList, notExistCnt);
                outputExitsKw(exitsOutputList, notExistCnt);
            }
        }
        notExistCnt = outputNotExitsKw(outputList, notExistCnt);
        System.out.println("===endInfo totalCnt:" + kwIdList.size() + " mapSize:" + idPKwMap.size() + " notExistCnt:" + notExistCnt + " existCnt:" + existCnt);
    }

    private int outputNotExitsKw(List<String> outputList, int notExistCnt) {
        if (!outputList.isEmpty()) {
            try {
                notExistCnt += outputList.size();
                FileUtils.writeLines(new File(outputFilePath), outputList, true);
                outputList.clear();
            } catch (IOException e) {
                System.out.println("===outputError outList:" + outputList);
                e.printStackTrace();
            }
        }
        return notExistCnt;
    }

    private int outputExitsKw(List<String> outputList, int notExistCnt) {
        if (!outputList.isEmpty()) {
            try {
                notExistCnt += outputList.size();
                FileUtils.writeLines(new File(exitsFilePath), outputList, true);
                outputList.clear();
            } catch (IOException e) {
                System.out.println("===outputError outList:" + outputList);
                e.printStackTrace();
            }
        }
        return notExistCnt;
    }

    private void initParam(String[] args) {
        if (args != null) {
            if (args.length >= 1) {
                isTest = Boolean.parseBoolean(args[0]);
            }
            if (args.length >= 2) {
                sourceFilePath = args[1];
            }
            if (args.length >= 3) {
                domainId = Integer.parseInt(args[2]);
            }
            if (args.length >= 4) {
                rankingDate = args[3];
            }
            if (args.length >= 5) {
                outputFilePath = args[4];
            }
            if (args.length >= 6) {
                exitsFilePath = args[5];
            }
            if (args.length >= 7) {
                device = Integer.parseInt(args[6]);
            }
            if (args.length >= 8) {
                domain = args[7];
            }
            if (args.length >= 9) {
                rankingType = Integer.parseInt(args[8]);
            }
        }
        try {
            FileUtils.writeLines(new File(outputFilePath), Collections.singletonList("Keyword" + SPLIT + "Search Volume" + SPLIT + "bankrate.com rank" + SPLIT + "Highest Ranking URLs" + SPLIT + "Discussions and Forums Rank" + SPLIT + "Discussion And Forums Count"));
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("==paramInfo isTest:" + isTest + " sourceFilePath:" + sourceFilePath + " domainId:" + domainId + " rankingDate:" + rankingDate + " outputFilePath:" + outputFilePath + " exitsFilePath:" + exitsFilePath + " device:" + device + " reverseDomain:" + domain + " rankingType:" + rankingType);
    }

    @Data
    @EqualsAndHashCode
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ExportInfo {
        private String keywordName;
        private double searchVolume;
        private String domainRank;
        private String url;
        private String pdRank;
        private String paaRank;
        private String ttkRank;
        private String dafCnt;
    }

    private static boolean listsAreEqual(List<?> list1, List<?> list2) {
        return list1.size() == list2.size() && new HashSet<>(list1).containsAll(list2);
    }

    private long SvRound(long sv) {
        if (sv == 0) {
            return 0;
        } else if (sv > 0 && sv <= 10) {
            return 10;
        } else if (sv > 10 && sv <= 100) {
            return Math.round((float) sv / 10) * 10L;
        } else {
            return Math.round((float) sv / 100) * 100L;
        }
    }
}
