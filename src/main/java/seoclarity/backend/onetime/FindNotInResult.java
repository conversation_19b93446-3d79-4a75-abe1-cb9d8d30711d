package seoclarity.backend.onetime;

import seoclarity.backend.upload.Utils.WriteUtils;
import seoclarity.backend.utils.CSVParser;

import java.io.*;
import java.net.URLDecoder;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.onetime
 * @author: cil
 * @date: 2021-07-23 13:42
 **/
public class FindNotInResult {
    private static final String RESULT_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/rgExpandForSV_20210721_result.txt";
    private static final String EXPAND_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/rgExpandForSV_20210721.txt";
    private static final String EXPAND_PATH_2 = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/rgExpandForSV_20210721_2.txt";
    private static final CSVParser parser = new CSVParser(',');

    public static void main(String[] args) {
        Set<String> strings = readForSVFile(EXPAND_PATH);
        System.out.println("SETCNT"+strings.size());
        List<String> old = readFile(RESULT_PATH);
        System.out.println("oldCNT"+old.size());

        old.remove(0);
        for (String line : old) {
            if (!strings.contains(line))
                WriteUtils.write(EXPAND_PATH_2, line);
        }


    }


    private static List<String> readFile(String path) {
        List<String> dataList = new LinkedList<>();

        File fin = new File(path);
        BufferedReader br = null;
        try {
            FileInputStream fis = new FileInputStream(fin);
            //Construct BufferedReader from InputStreamReader
            br = new BufferedReader(new InputStreamReader(fis));
            String line;
            while ((line = br.readLine()) != null) {
                String[] strings = parser.parseLine(line);
                dataList.add(strings[2]);
            }
            return dataList;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }


    private static Set<String> readForSVFile(String path) {
        Set<String> dataList = new HashSet<>();

        File fin = new File(path);
        BufferedReader br = null;
        try {
            FileInputStream fis = new FileInputStream(fin);
            //Construct BufferedReader from InputStreamReader
            br = new BufferedReader(new InputStreamReader(fis));
            String line;
            while ((line = br.readLine()) != null) {
                String[] strings = parser.parseLine(line);
                dataList.add(URLDecoder.decode(strings[0], "UTF-8"));

            }
            return dataList;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;

    }


}
