package seoclarity.backend.onetime.monthlyRanking;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.utils.CSVParser;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @date 2020-07-07
 * @path seoclarity.backend.onetime.monthlyRanking.DeleteDirtyKeywordsForMonthly
 * https://www.wrike.com/open.htm?id=531690686
 * delete keywords from monthly relation rank check and add to dirty keywords table
 */
public class DeleteDirtyKeywordsForMonthly {
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
	
	public DeleteDirtyKeywordsForMonthly() {
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
	}
	
	public static void main(String[] args) {
		DeleteDirtyKeywordsForMonthly ins = new DeleteDirtyKeywordsForMonthly();
		try {
//			ins.process(args[0]);
//			ins.processReAddDirty();
//			ins.processAddDirty(args[0]);
			ins.processAddDirtyKeywords(args[0], 1, 1);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * @param file: keyword file
	 */
	private void processAddDirtyKeywords(String file, int engine, int language) throws Exception{
		List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
		int total = 0;
		int processTotal = 0;
		int pageSize = 300;
		List<String> tempList = new ArrayList<>();
		for (String line : lines) {
			if (StringUtils.isBlank(line)) {
				continue;
			}
			total++;
			String str = StringUtils.trim(line).toLowerCase();
			str = URLEncoder.encode(str, "UTF-8").toLowerCase();
			tempList.add(str);
			if (tempList.size() >= pageSize) {
				// check keyword entity
				List<SeoClarityKeywordEntity> kwList = seoClarityKeywordEntityDAO.getByKeywords(tempList);
				processTotal += kwList.size();
				System.out.println("=====tempList:" + tempList.size() + ", kwList:" + kwList.size() + ", processed:" + total);
				if (kwList.size() != tempList.size()) {
					tempList.removeAll(kwList.stream().map(x -> StringUtils.lowerCase(x.getKeywordText())).collect(Collectors.toList()));
					if (tempList.size() > 0) {
						System.out.println("===can not get the rank checkid, kw:" + tempList);
					}
				}
				// add to dirty keyword list
				if (kwList.size() > 0) {
					List<Integer> kidList = kwList.stream().map(SeoClarityKeywordEntity::getId).collect(Collectors.toList());
					deleteFromMonthlyRelation(engine, language, kidList);
					addToDirtyKeywords(engine, language, kidList);
				}
				tempList.clear();
			}
		}
		
		if (tempList.size() > 0) {
			// check keyword entity
			List<SeoClarityKeywordEntity> kwList = seoClarityKeywordEntityDAO.getByKeywords(tempList);
			processTotal += kwList.size();
			System.out.println("=====tempList:" + tempList.size() + ", kwList:" + kwList.size() + ", processed:" + total);
			if (kwList.size() != tempList.size()) {
				tempList.removeAll(kwList.stream().map(x -> StringUtils.lowerCase(x.getKeywordText())).collect(Collectors.toList()));
				if (tempList.size() > 0) {
					System.out.println("===can not get the rank checkid, kw:" + tempList);
				}
			}
			// add to dirty keyword list
			if (kwList.size() > 0) {
				List<Integer> kidList = kwList.stream().map(SeoClarityKeywordEntity::getId).collect(Collectors.toList());
				deleteFromMonthlyRelation(engine, language, kidList);
				addToDirtyKeywords(engine, language, kidList);
			}
		}
		
		System.out.println("=================finished. total:" + total + ", processTotal:" + processTotal);
	}
	
	// "about samsung galaxy tab","[117667167,117667158]"
	private void processAddDirty(String file) throws Exception{
		int engine = 1;
		int language = 1;
		List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
		List<Integer> kidList = new ArrayList<Integer>();
		CSVParser parser = new CSVParser(',');
		int cnt = 0;
		for (String line : lines) {
			if (StringUtils.isBlank(line)) {
				continue;
			}
			String[] cols = parser.parseLine(line);
//			String kw = cols[0].toLowerCase().trim();
			String str = StringUtils.remove(StringUtils.trim(cols[1]), '[');
			str = StringUtils.remove(str, ']').trim();
			String[] kids = StringUtils.split(str, ',');
			for (String kid : kids) {
				kidList.add(Integer.valueOf(StringUtils.trim(kid)));
			}
			if (kidList.size() >= 300) {
				cnt += kidList.size();
				addToDirtyKeywords(engine, language, kidList);
				kidList.clear();
			}
		}
		if (kidList.size() > 0) {
			cnt += kidList.size();
			addToDirtyKeywords(engine, language, kidList);
			kidList.clear();
		}
		System.out.println("total:" + lines.size() + ", kidList:" + cnt);
	}
	
	private void processReAddDirty() {
		SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
		List<Map<String, Object>> list = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.checkDeletedMonthlyRelation(sf.format(new Date()));
		Map<String, List<Integer>> dirtyMap = new HashMap<String, List<Integer>>();
		for (Map<String, Object> map : list) {
			int engine = Integer.valueOf(map.get("search_engine_id").toString());
			int language = Integer.valueOf(map.get("search_language_id").toString());
			int kid = Integer.valueOf(map.get("keyword_id").toString());
			String key = engine + "-" + language;
			if (dirtyMap.containsKey(key)) {
				dirtyMap.get(key).add(Integer.valueOf(kid));
			} else {
				List<Integer> kidList = new ArrayList<Integer>();
				kidList.add(Integer.valueOf(kid));
				dirtyMap.put(key, kidList);
			}
		}
		
		for (String key : dirtyMap.keySet()) {
			int engine = Integer.valueOf(StringUtils.split(key, '-')[0]);
			int language = Integer.valueOf(StringUtils.split(key, '-')[1]);
			System.out.println("===key:" + key + ", size:" + (dirtyMap.get(key).size()));
			addToDirtyKeywords(engine, language, dirtyMap.get(key));
		}
	}
	
	private void process(String file) throws Exception{
		List<String> lines = FileUtils.readLines(new File(file), "UTF-8");
		
		Map<String, List<Integer>> dirtyMap = new HashMap<String, List<Integer>>();
		List<Integer> tempList = new ArrayList<Integer>();
		int cnt = 0;
		for (String line : lines) {
			if (StringUtils.isBlank(line)) {
				continue;
			}
			String[] cols = StringUtils.split(line, ',');
			int engine = Integer.valueOf(cols[0]);
			int language = Integer.valueOf(cols[1]);
			String kid = cols[2];
			tempList.add(Integer.valueOf(kid));
			String key = engine + "-" + language;
			if (dirtyMap.containsKey(key)) {
				dirtyMap.get(key).add(Integer.valueOf(kid));
			} else {
				List<Integer> kidList = new ArrayList<Integer>();
				kidList.add(Integer.valueOf(kid));
				dirtyMap.put(key, kidList);
			}
			if (tempList.size() >= 200) {
				List<SeoClarityKeywordEntity> kwList = seoClarityKeywordEntityDAO.getKeywordByIdList(tempList);
				cnt += kwList.size();
				tempList.clear();
				for (SeoClarityKeywordEntity kw : kwList) {
					System.out.println("Should del kw:" + kw.getKeywordText());
				}
			}
		}
		
		if (tempList.size() > 0) {
			List<SeoClarityKeywordEntity> kwList = seoClarityKeywordEntityDAO.getKeywordByIdList(tempList);
			cnt += kwList.size();
			tempList.clear();
			for (SeoClarityKeywordEntity kw : kwList) {
				System.out.println("Should del kw:" + kw.getKeywordText());
			}
		}
		
		System.out.println("========================");
		System.out.println("total:" + lines.size() + ", cnt:" + cnt);
		for (String key : dirtyMap.keySet()) {
			int engine = Integer.valueOf(StringUtils.split(key, '-')[0]);
			int language = Integer.valueOf(StringUtils.split(key, '-')[1]);
			System.out.println("===key:" + key + ", size:" + (dirtyMap.get(key).size()));
			deleteFromMonthlyRelation(dirtyMap.get(key));
			addToDirtyKeywords(engine, language, dirtyMap.get(key));
		}
		
	}
	
	private void deleteFromMonthlyRelation(List<Integer> kidList) {
		int cnt = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.delByKidList(kidList);
		System.out.println("Delete kidList:" + kidList.size() + ", execute:" + cnt);
	} 
	
	private void deleteFromMonthlyRelation(int engine, int language, List<Integer> kidList) {
		int cnt = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.delByKidList(engine, language, kidList);
		System.out.println("Delete from monthly relation, engine:" + engine + ", language:" + language +", kidList:" + kidList.size() + ", execute:" + cnt);
	} 
	
	private void addToDirtyKeywords(int engine, int language, List<Integer> kidList) {
		List<Integer> exists = seoClarityKeywordEntityDAO.checkExistsInDirtyKeywords(engine, language, kidList);
		if (exists != null && exists.size() > 0) {
			kidList.removeAll(exists);
		}
		if (kidList.size() > 0) {
			seoClarityKeywordEntityDAO.insertToDirtyKeywords(engine, language, kidList);
		}
		System.out.println("Add to dirty keywords, engine:" + engine + ", language:" + language + ", kidList:" +kidList.size() + ", exists:" + exists.size());
	}

}
