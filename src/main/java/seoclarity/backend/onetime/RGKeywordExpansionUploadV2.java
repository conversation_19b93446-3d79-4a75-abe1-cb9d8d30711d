package seoclarity.backend.onetime;

import seoclarity.backend.dao.actonia.KeywordSuggestDAO;
import seoclarity.backend.entity.actonia.ContentJsonPOJO;
import seoclarity.backend.upload.RGKeywordExpansionV2;
import seoclarity.backend.upload.RGKeywordExpansionV3;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.*;

/**
 * @program: backend
 * @description: https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/723128339
 * @packagename: seoclarity.backend.onetime
 * @author: cil
 * @date: 2021-07-15 11:03
 **/
public class RGKeywordExpansionUploadV2 {

    //FILE_PATH
    private static final String FILE_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/domain_ideas.csv";
    //    private static final String READ_FILE_PATH = "/Users/<USER>/Downloads/ngram-result-final.txt";
    private static final String US = "us";
    private static final String JP = "jp";
    private static final String UK = "uk";

    public static void main(String[] args) {
        Date date = new Date();
        System.out.println("startTime:" + date);
        RGKeywordExpansionUploadV2 ins = new RGKeywordExpansionUploadV2();
        ins.process();
        System.out.println("endTime:" + new Date() + " useTime:" + (date.getTime() - new Date().getTime()) / 1000 + "s");

    }

    private void process() {
        RGKeywordExpansionV3 rg = new RGKeywordExpansionV3();


        int num = 0;
        //all
        List<ContentJsonPOJO> JsonPOJOList = readFile(FILE_PATH);
        System.out.println("JsonPOJOList:" + JsonPOJOList.size());
        rg.doSomething(JsonPOJOList, num);
    }


    private static List<ContentJsonPOJO> readFile(String path) {
        List<ContentJsonPOJO> dataList = new LinkedList<>();

        File fin = new File(path);
        BufferedReader br = null;
        try {
            FileInputStream fis = new FileInputStream(fin);
            //Construct BufferedReader from InputStreamReader
            br = new BufferedReader(new InputStreamReader(fis));
            String line;
            while ((line = br.readLine()) != null) {

                String[] split = line.split("\t");


                if (split.length < 4)
                    continue;
                if (    !("CA".equals(split[1]) && "Canada".equals(split[2])) &&
                        !("UK".equals(split[1]) && "United Kingdom".equals(split[2])) &&
                        !("JP".equals(split[1]) && "Japan".equals(split[2]))&&
                        !("DE".equals(split[1]) && "Germany".equals(split[2]))&&
                        !("IN".equals(split[1]) && "India".equals(split[2]))&&
                        !("FR".equals(split[1]) && "France".equals(split[2]))&&
                        !("US".equals(split[1]) && "United States".equals(split[2]))) {
                    ContentJsonPOJO contentJsonPOJO = new ContentJsonPOJO();
                    contentJsonPOJO.setCountry(split[1]);
                    contentJsonPOJO.setSearch(split[3]);
                    dataList.add(contentJsonPOJO);
                }
            }
            return dataList;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;


    }

    private static List<ContentJsonPOJO> stringArrToJsonPOJO(List<String> countryAndSearchText, String country) {
        Set<ContentJsonPOJO> jsonPOJOset = new HashSet<>();
        for (String jsonStr : countryAndSearchText) {
            ContentJsonPOJO contentJsonPOJO = new ContentJsonPOJO();
            contentJsonPOJO.setCountry(country);
            contentJsonPOJO.setSearch(jsonStr);
            jsonPOJOset.add(contentJsonPOJO);

        }

        List<ContentJsonPOJO> jsonPOJOList = new LinkedList<>(jsonPOJOset);

        Collections.sort(jsonPOJOList);
        return jsonPOJOList;
    }


}
