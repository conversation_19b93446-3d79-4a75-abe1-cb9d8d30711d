package seoclarity.backend.onetime.RI;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.rankcheck.RcKeywordDomainRelEntityDAO;
import seoclarity.backend.dao.rankcheck.RcKeywordSeRelEntityDAO;
import seoclarity.backend.entity.rankcheck.RcKeywordDomainRelEntity;
import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

//
public class CheckOverlappedKeywordsFor13579 {

    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private RcKeywordSeRelEntityDAO rcKeywordSeRelEntityDAO;

    public CheckOverlappedKeywordsFor13579() {
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        rcKeywordSeRelEntityDAO = SpringBeanFactory.getBean("rcKeywordSeRelEntityDAO");
    }

    public static void main(String[] args) {
        new CheckOverlappedKeywordsFor13579().process();
    }

    private void process() {
        Date startDate = null;
        Date endDate = null;
        try {
            startDate = DateUtils.parseDate("2024-07-01", "yyyy-MM-dd"); // TODO: 2025/1/17
            endDate = DateUtils.parseDate("2025-01-16", "yyyy-MM-dd");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        File fileAllEngine = new File("/home/<USER>/13579/all_engine_20240117.txt");
        File fileSpecialEngine = new File("/home/<USER>/13579/special_engine_20240117.txt");

        int domainId = 13579;

        List<RcKeywordSeRelEntity> rcKeywordSeRelEntities = rcKeywordSeRelEntityDAO.selectEntityListByDomainId(domainId);
        Map<String, List<Integer>> engineKwMap = rcKeywordSeRelEntities.stream().collect(Collectors.groupingBy(var -> var.getSearchEngineId() + "!_!" + var.getLanguageId() + "!_!" + var.getDevice() + "!_!", Collectors.mapping(RcKeywordSeRelEntity::getKeywordId, Collectors.toList())));

        List<String> aeF = new ArrayList<>();
        List<String> seF = new ArrayList<>();

        Date trafficDate = endDate;
        while (true) {

            int totalKwCnt = 0;
            int existKwCnt = 0;

            String rankingDate = DateFormatUtils.format(trafficDate, "yyyy-MM-dd");

            System.out.println("=== Start process date: " + rankingDate);

            for (Map.Entry<String, List<Integer>> stringListEntry : engineKwMap.entrySet()) {
                String key = stringListEntry.getKey();
                List<Integer> kwIds = stringListEntry.getValue();
                String[] split = key.split("!_!");

                int engineId = Integer.parseInt(split[0]);
                int language = Integer.parseInt(split[1]);
                String device = split[2];

                List<Integer> existKwIds = clDailyRankingEntityDao.ckeckKidExistsInInfoTable(engineId, language, device.equalsIgnoreCase("m"), 0, rankingDate, kwIds.toArray(new Integer[0]));

                int tlCnt = kwIds.size();
                int exCnt = existKwIds.size();

                System.out.println("     === Process engine: " + engineId + "-" + language + "-" + device + ", tl size: " + kwIds.size() + ", ek szie: " + existKwIds.size());

                seF.add(rankingDate + "\t" + engineId + "\t" + language + "\t" + device + "\t" + tlCnt + "\t" + exCnt);

                totalKwCnt+=tlCnt;
                existKwCnt+=exCnt;
            }

            aeF.add(rankingDate + "\t" + totalKwCnt + "\t" + existKwCnt);

            trafficDate = DateUtils.addDays(trafficDate, -1);

            if (trafficDate.compareTo(startDate) < 0) {
                break;
            }
        }

        try {
            FileUtils.writeLines(fileAllEngine, "UTF-8", aeF, true);
            FileUtils.writeLines(fileSpecialEngine, "UTF-8", seF, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
