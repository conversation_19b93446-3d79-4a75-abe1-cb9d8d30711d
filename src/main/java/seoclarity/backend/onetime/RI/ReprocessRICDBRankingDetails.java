package seoclarity.backend.onetime.RI;

import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.text.csv.*;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.RankTypeManager;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.*;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-02-02
 * @path seoclarity.backend.onetime.RI.ReprocessRICDBRankingDetails
 *
 * https://www.wrike.com/open.htm?id=1553887808
 * expport detail table to csv file day by day from db
 * summary web rank from csv file
 * export ranking data to new csv file
 * load new csv file to temp table
 *
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.RI.ReprocessRICDBRankingDetails" -Dexec.args="" -Dexec.cleanupDaemonThreads=false
 */
public class ReprocessRICDBRankingDetails {
    private static final String EXPORT_FOLDER = "/tmp/";
    private static final String START_DATE = "2024-11-01";
    private static final String END_DATE = "2024-12-17";
    private static final String FLIGHT_DOMAIN_REVERSE = "com.google.flights";
    private static final String DB_HOST = "***************";
    private static final List<String> ENGINE_LIST = Arrays.asList(
//            "1-1",
//            "2-5", "3-3", "4-7", "6-8", "8-9", "11-12", "14-15",
//            "15-16", "16-17", "17-18", "18-19", "20-21",
//            "23-24",
//            "24-25", "30-34", "31-127",
            "34-34", "35-35", "37-37"
//            "42-42", "45-46", "46-47", "61-62"
    );

    private static final CsvReadConfig config = CsvReadConfig.defaultConfig();
    private static final CsvWriteConfig wCfg = CsvWriteConfig.defaultConfig();
    public ReprocessRICDBRankingDetails() {
        config.setSkipEmptyRows(true);
        config.setTrimField(true);
        config.setFieldSeparator(',');

        wCfg.setAlwaysDelimitText(true);
    }
    /**
     * reprocess keywords info tables
     * test_fix_meo_flight_local_d_info_202411_202412_intl
     * test_fix_meo_flight_local_d_info_202411_202412_us
     * test_fix_meo_flight_local_m_info_202411_202412_intl
     * test_fix_meo_flight_local_m_info_202411_202412_us
     *
     * backup tables
     * test_fix_meo_flight_sky_local_d_ranking_detail_intl_202411_backup
     * test_fix_meo_flight_sky_local_d_ranking_detail_intl_202412_backup
     * test_fix_meo_flight_sky_local_d_ranking_detail_us_202411_backup
     * test_fix_meo_flight_sky_local_d_ranking_detail_us_202412_backup
     *
     * test_fix_meo_flight_sky_local_m_ranking_detail_intl_202411_backup
     * test_fix_meo_flight_sky_local_m_ranking_detail_intl_202412_backup
     * test_fix_meo_flight_sky_local_m_ranking_detail_us_202411_backup
     * test_fix_meo_flight_sky_local_m_ranking_detail_us_202412_backup
     * ---------------------------
     * new table
     *  test_fix_meo_flight_sky_local_d_ranking_detail_intl_202411_finished
     *  test_fix_meo_flight_sky_local_d_ranking_detail_intl_202412_finished
     *  test_fix_meo_flight_sky_local_d_ranking_detail_us_202411_finished
     *  test_fix_meo_flight_sky_local_d_ranking_detail_us_202412_finished
     *
     *  test_fix_meo_flight_sky_local_m_ranking_detail_intl_202411_finished
     *  test_fix_meo_flight_sky_local_m_ranking_detail_intl_202412_finished
     *  test_fix_meo_flight_sky_local_m_ranking_detail_us_202411_finished
     *  test_fix_meo_flight_sky_local_m_ranking_detail_us_202412_finished
     */

    public static void main(String[] args) throws Exception {
//        String url = "https://www.google.com/travel/flights?hl=ko";
//        int type = 1;
//        String[] domainUrlList = CommonUtils.splitString(url);
//        String domainName = domainUrlList[0];
//        String uriPattern = domainUrlList[1];
//        String newDomainReverse = RankTypeManager.convertToNewDomainName(domainName, type, uriPattern);
//
//
//        System.out.println(uriPattern);
//        System.out.println(newDomainReverse);


        ReprocessRICDBRankingDetails ins = new ReprocessRICDBRankingDetails();
        String specSDate = null;
        String specEDate = null;
        String device = null;
        String country = null;
        if (args.length > 0) {
            specSDate = args[0];
            specEDate = args[1];
        }
        if (args.length > 2) {
            device = args[2];
        }
        if (args.length > 3) {
            country = args[3];
        }
        if (StringUtils.equals(specSDate, "null")) {
            specSDate = null;
        }
        if (StringUtils.equals(specEDate, "null")) {
            specEDate = null;
        }
        if (StringUtils.equals(device, "null")) {
            device = null;
        }
        if (StringUtils.equals(country, "null")) {
            country = null;
        }
        ins.process(specSDate, specEDate, device, country);
    }

    private void process(String sDate, String eDate, String device, String country) throws Exception {
        // export ranking data day by day
        List<String> dateList = ClarityDBUtils.getTrendDateList(StringUtils.replace(START_DATE, "-", ""), StringUtils.replace(END_DATE, "-", ""), null);
        System.out.println("===dateList:" + dateList);
        for (String date : dateList) {
            if (StringUtils.isNotBlank(sDate) && StringUtils.isNotBlank(eDate)) {
                int dateIdx = Integer.parseInt(StringUtils.replace(date, "-", ""));
                int s = Integer.parseInt(StringUtils.replace(sDate, "-", ""));
                int e = Integer.parseInt(StringUtils.replace(eDate, "-", ""));
                if (dateIdx < s || dateIdx > e) {
                    continue;
                }
            }
            for (String engine : ENGINE_LIST) {
                if (StringUtils.equals(country, "us") && !StringUtils.equals(engine, "1-1")) {
                    continue;
                } else if (StringUtils.equals(country, "intl") && StringUtils.equals(engine, "1-1")) {
                    continue;
                }
                String[] engineArr = StringUtils.split(engine, "-");
                String engineId = engineArr[0];
                String languageId = engineArr[1];
                System.out.println("=========================engineId:" + engineId + ", languageId:" + languageId + ", date:" + date + "=======================================");
                // export ranking detail
                if (StringUtils.isBlank(device)) {
                    // geo
                    reprocessRanking(engineId, languageId, date, true, false);
                    reprocessRanking(engineId, languageId, date, true, true);
                    // national
                    reprocessRanking(engineId, languageId, date, false, false);
                    reprocessRanking(engineId, languageId, date, false, true);
                } else {
                    boolean isMobile = StringUtils.equalsIgnoreCase(device, "m");
                    reprocessRanking(engineId, languageId, date, true, isMobile);
                    reprocessRanking(engineId, languageId, date, false, isMobile);
                }
            }
        }
    }

    private void reprocessRanking(String engineId, String languageId, String date, boolean isGeo, boolean isMobile) {
        // export ranking detail
        String exportFile = exportRankingDetail(engineId, languageId, date, isGeo, isMobile);
        // check rows
        int rows = Integer.parseInt(StringUtils.trim(executeBashShell("wc -l < " + exportFile, false)));
        System.out.println("==========================");
        System.out.println("=========current process engineId:" + engineId + ", languageId:" + languageId + ", date:" + date + ", isGeo:" + isGeo + ", isMobile:" + isMobile
                + ", export file:" + exportFile + ", rows:" + rows);
        if (rows == 0) {
            System.out.println("==========================");
            new File(exportFile).delete();
            return;
        }
        String newFile = StringUtils.substringBeforeLast(exportFile, ".") + "_new";

        // loop reading
        int maxLine = 1000;
        int lineCnt = 0;
        long a = System.currentTimeMillis();
        try (BufferedReader br = new BufferedReader(new FileReader(new File(exportFile), "UTF-8").getReader())) {
            CsvParser p = new CsvParser(br, config);

            Map<String, List<List<String>>> keywordMap = new LinkedHashMap<>();
            while (p.hasNext()) {
                lineCnt++;
                List<String> row = p.next();
                String own_domain_id = row.get(1);
                String keyword_rankcheck_id = row.get(2);
                String location_id = row.get(5);
                String key = own_domain_id + "-" + keyword_rankcheck_id + "-" + location_id;
                if (keywordMap.containsKey(key)) {
                    List<List<String>> list = keywordMap.get(key);
                    list.add(row);
                } else {
                    List<List<String>> list = new ArrayList<>();
                    list.add(row);
                    keywordMap.put(key, list);
                }
                if (keywordMap.size() >= maxLine) {
                    reSummaryRank(keywordMap, key, newFile);
                }
            }

            if (keywordMap.size() > 0) {
                reSummaryRank(keywordMap, null, newFile);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        long b = System.currentTimeMillis();
        // load to temp table
        // check rows
        int newFileRows = Integer.parseInt(StringUtils.trim(executeBashShell("wc -l < " + newFile, false)));
        if (Math.abs(newFileRows - rows) <= 1 || newFileRows == rows) {
            String result = importTable(engineId, languageId, date, isGeo, isMobile, newFile);
            if (StringUtils.isNotBlank(StringUtils.trim(result))) {
                System.out.println("========reprocess failed, newFile:" + StringUtils.substringAfterLast(newFile, "/") + ", rows:" + rows + ", newFileRows:" + newFileRows
                        + ", lineCnt:" + lineCnt + ", cost:" + (b - a));
            } else {
                System.out.println("========reprocess complete, newFile:" + StringUtils.substringAfterLast(newFile, "/") + ", rows:" + rows + ", newFileRows:" + newFileRows
                        + ", lineCnt:" + lineCnt + ", cost:" + (b - a));
                new File(exportFile).delete();
                new File(newFile).delete();
            }
        } else {
            System.out.println("========reprocess wrong, export not match, engineId:" + engineId + ", languageId:" + languageId + ", date:" + date + ", isGeo:" + isGeo + ", isMobile:" + isMobile
                    + ", export file:" + exportFile + ", rows:" + rows + ", newFileRows:" + newFileRows + ", lineCnt:" + lineCnt + ", cost:" + (b - a));
        }
        System.out.println("==========================");
    }

    private void reSummaryRank(Map<String, List<List<String>>> keywordMap, String lastKey, String newFile) {
        List<List<String>> lastRows = new ArrayList<>(0);
        if (StringUtils.isNotBlank(lastKey)) {
            lastRows.addAll(keywordMap.get(lastKey));
            keywordMap.remove(lastKey);
        }

        // reprocess rank
        keywordMap.forEach(this::rankHandler);
        // export to file
        CsvWriter w = new CsvWriter(new File(newFile), Charset.forName("UTF-8"), true, wCfg);

        w.write(keywordMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        // file not finished, need add a \n
        w.writeLine();
        w.flush();
        w.close();
        keywordMap.clear();
        if (StringUtils.isNotBlank(lastKey)) {
            keywordMap.put(lastKey, lastRows);
        }
    }

    private void rankHandler(String key, List<List<String>> rows) {
        int webRankingCount = 0;
        // hrd
        Set<String> uniqueDomainSet = new HashSet<>();

        for (List<String> row : rows) {
            String domainReverse = row.get(6);
            int trueRank = Integer.parseInt(row.get(12));
//            int webRank = Integer.parseInt(row.get(13));
            int type = Integer.parseInt(row.get(14));
//            String hrd = row.get(17);
            String url = row.get(9);

            String newHrd = "";
            int newWebRank = 0;

            String[] domainUrlList = CommonUtils.splitString(url);
            String domainName = domainUrlList[0];
            String uriPattern = domainUrlList[1];
            String newDomainReverse = RankTypeManager.convertToNewDomainName(domainName, type, uriPattern);
            boolean isUniversalRank = isUniversalUrl(url, type, null);
            if (!isUniversalRank) {
                newWebRank = trueRank - webRankingCount;
            } else {
                webRankingCount++;
            }

            // TODO
            if (StringUtils.equals(domainReverse, FLIGHT_DOMAIN_REVERSE) && StringUtils.equals(domainReverse, newDomainReverse) && type == 1) {
                System.out.println("====================row:" + row);
            }

            if (uniqueDomainSet.contains(newDomainReverse)) {
                newHrd = "0";
            } else {
                uniqueDomainSet.add(newDomainReverse);
                newHrd = "1";
            }

            // reset row
            row.set(6, newDomainReverse);
            row.set(13, String.valueOf(newWebRank));
            row.set(17, newHrd);
        }
    }

    private String exportRankingDetail(String engineId, String languageId, String date, boolean isGeo, boolean isMobile) {
        String backupTable = getBackupTable(Integer.parseInt(engineId), Integer.parseInt(languageId), isMobile, date, false);
        String exportFile = EXPORT_FOLDER + backupTable + "_" + engineId + "-" + languageId + "_" + (isGeo ? "geo" : "nal") + "_" + (isMobile ? "m" : "d") + "_" + date + ".csv";
        File f = new File(exportFile);
        if (f.exists()) {
            f.delete();
        }
        String locationFilter = isGeo ? " AND location_id > 0 " : " AND location_id = 0 ";
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM " + backupTable);
        sql.append(" WHERE ranking_date = '" + date + "' ");
        sql.append(" AND engine_id = " + engineId + " AND language_id = " + languageId + " ");
        sql.append(locationFilter);
        sql.append(" ORDER BY own_domain_id, keyword_rankcheck_id, location_id, true_rank format CSV");

        String shellStr = "clickhouse-client -h " + DB_HOST + " --password clarity99! -m -n --database seo_daily_ranking --query=\"" + sql.toString() + "\" >> " + exportFile;
        executeBashShell(shellStr, true);
        return exportFile;
    }

    private String importTable(String engineId, String languageId, String date, boolean isGeo, boolean isMobile, String newFile) {
        String newTable = getBackupTable(Integer.parseInt(engineId), Integer.parseInt(languageId), isMobile, date, true);
        String shellStr = "cat " + newFile + "|clickhouse-client -h " + DB_HOST + " --password clarity99! --database seo_daily_ranking -m -n  --query=\"insert into " + newTable + " FORMAT CSV\"";
        return executeBashShell(shellStr, true);
    }

    private static String getBackupTable(int engine, int language, boolean isMobile, String rankingDate, boolean isNew) {
        String prefix = "test_fix_meo_flight_sky_local_";
        String device = isMobile ? "m_ranking_detail_" : "d_ranking_detail_";
        String suffix = engine == 1 && language == 1 ? "us_" : "intl_";
        String month = StringUtils.replace(rankingDate, "-", "").substring(0, 6);
        String verSuffix = isNew ? "_finished" : "_backup";
        return prefix + device + suffix + month + verSuffix;
    }

    private static String executeBashShell(String shellStr, boolean isShowLog) {
        List<String> infoList = new ArrayList<>();
        infoList.add("shellStr:" + shellStr);

        ProcessBuilder processBuilder = new ProcessBuilder("/bin/bash", "-c", shellStr);
        processBuilder.redirectErrorStream(true);
        long s = System.currentTimeMillis();
        String response = "";
        BufferedReader reader = null;
        try {
            Process process = processBuilder.start();
            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            int exitCode = process.waitFor();
            infoList.add("Exit Code: " + exitCode);
            infoList.add("Output: " + output.toString());

            response = output.toString();

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        long e = System.currentTimeMillis();
        infoList.add("EXECUTE FINISHED. Total time: " + (e - s) / 1000 + "s");
        if (isShowLog) {
            infoList.forEach(System.out::println);
        }
        return response;
    }

    public static boolean isUniversalUrl(String url, int urlType, Map<String, Boolean> flagMap) {
        if (RankTypeManager.isWebRank(url, urlType, flagMap)) {
            return false;
        }
        return true;
    }

    private static String getRootDomain(String fullDomain) {
        String domainName = null;
        try {
            domainName = StringUtils.reverseDelimited(fullDomain, '.');
            return StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(domainName), '.');
        } catch (Exception e) {
            try {
                if (StringUtils.startsWithIgnoreCase(domainName, "www.")) {
                    return StringUtils.reverseDelimited(StringUtils.removeStartIgnoreCase(domainName, "www."), '.');
                } else {
                    return StringUtils.reverseDelimited(domainName, '.');
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return null;
    }
}
