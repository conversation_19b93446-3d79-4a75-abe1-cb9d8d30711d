package seoclarity.backend.onetime.RI;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.clickhouse.RIDailyRankingService;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.utils.RankTypeManager;

/**
 * <AUTHOR>
 * @date 2021-03-11
 * @path seoclarity.backend.onetime.RI.CheckRIRankingFilesDiffFromDB
 * compare keyword ranking json files with DB
 */
public class CheckRIRankingFilesDiffFromDB {
	private static boolean isMobile = false;
	private SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
	private SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
	private RIDailyRankingService riDailyRankingService;
	
	public CheckRIRankingFilesDiffFromDB() {
		riDailyRankingService = new RIDailyRankingService(true);
	}
	
	public static void main(String[] args) {
		String folder = args[0];
		String prefix = args[1];
		if (StringUtils.containsIgnoreCase(prefix, "mobile")) {
			isMobile = true;
		}
		CheckRIRankingFilesDiffFromDB ins = new CheckRIRankingFilesDiffFromDB();
		File[] files = new File(folder).listFiles();
		for (File file : files) {
			if (file.isDirectory() && StringUtils.startsWith(file.getName(), prefix)) {
				File[] subFile = new File(file.getAbsolutePath()).listFiles();
				List<File> fileList = new ArrayList<>();
				for (File f : subFile) {
					fileList.add(f);
				}
				try {
					ins.processCheck(fileList, prefix, file.getName());
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	private void processCheck(List<File> files, String prefix, String folder) throws Exception{
		Gson gson = new Gson();
		List<String> totalList = new ArrayList<>();
		for (File file : files) {
			String[] arr = StringUtils.split(StringUtils.substringAfter(file.getName(), prefix), "_");
			int engine = Integer.valueOf(arr[0]);
			int language = Integer.valueOf(StringUtils.substringBefore(arr[1], "-"));
			
			List<String> lines = FileUtils.readLines(file, "UTF-8");
			Map<String, List<KeywordRankVO>> kwMap = new HashedMap<>();
			for (String line : lines) {
				if (StringUtils.isBlank(line)) {
					continue;
				}
				KeywordRankVO vo = gson.fromJson(line, KeywordRankVO.class);
				Integer date = vo.getSendToQDate();
				if (kwMap.containsKey(date.toString())) {
					kwMap.get(date.toString()).add(vo);
				} else {
					List<KeywordRankVO> list = new ArrayList<>();
					list.add(vo);
					kwMap.put(date.toString(), list);
				}
			}
			System.out.println("===file:" + file.getName() + ", engine:" + engine + ", language:" + language + ", lines:" + lines.size());
			
			// check difference between file and db
			for (String date : kwMap.keySet()) {
//				List<String> wrongList = createCheckSqlForDifferentUrlCnt(kwMap.get(date), Integer.valueOf(date), engine, language, isMobile);
				List<String> wrongList = createCheckForWrongTop1Url(kwMap.get(date), Integer.valueOf(date), engine, language, isMobile);
				totalList.addAll(wrongList);
				System.out.println("wrong list:" + wrongList.size() + ", unique size:" + new HashSet<String>(wrongList).size());
			}
		}
		System.out.println("=============folder:" + folder + ", isMobile:" + isMobile + ", total:" + totalList.size() + ", unique:" + (new HashSet<>(totalList)).size());
	}
	
	private List<String>createCheckForWrongTop1Url(List<KeywordRankVO>kwList, int queryDate, int engine, int language, boolean isMobile) throws Exception{
		Map<String, KeywordRankVO> kwFpsMap = new HashMap<String, KeywordRankVO>();
		
		Set<String> domains = new HashSet<>();
		List<Integer> kidList = new ArrayList<Integer>();
		for (KeywordRankVO vo : kwList) {
			int kid = vo.getId();
			int fps = vo.getKeywordRankEntityVOs() == null ? 0 : vo.getKeywordRankEntityVOs().size();
			kwFpsMap.put(String.valueOf(kid), vo);
			
			domains.add(vo.getDomainList().get(0));
			kidList.add(kid);
		}
		StringBuffer sql = new StringBuffer();
		String date = yyyy_MM_dd.format(yyyyMMdd.parse(String.valueOf(queryDate)));
		
		String tableName = "%device%_ranking_detail_%country%_%month%";
		tableName = StringUtils.replace(tableName, "%device%", isMobile ? "m" : "d");
		tableName = StringUtils.replace(tableName, "%country%", (engine == 1 && language == 1) ? "us" : "intl");
		tableName = StringUtils.replace(tableName, "%month%", StringUtils.substring(String.valueOf(queryDate), 0, 6));
		
		sql.append(" select keyword_rankcheck_id, url                                                       ");
		sql.append(" from " + tableName + "                                                      ");
		sql.append(" where own_domain_id IN (" + StringUtils.join(domains, ",") + ") and engine_id = " + engine + " and language_id = " + language + " and location_id = 0 ");
		sql.append(" and keyword_rankcheck_id in (" + StringUtils.join(kidList, ",") + ") and web_rank = 1        and ranking_date = '" + date + "'  ");
		
		List<String> wrongKidList = new ArrayList<>();
		
		List<Map<String, Object>> results = riDailyRankingService.queryForAll(sql.toString());
		
		for (Map<String, Object> map : results) {
			String keyword_rankcheck_id = map.get("keyword_rankcheck_id").toString();
			String url = map.get("url").toString();
			
			KeywordRankVO vo = kwFpsMap.get(keyword_rankcheck_id);
			KeywordRankEntityVO rankVo = null;
			for (KeywordRankEntityVO rank : vo.getKeywordRankEntityVOs()) {
				if (StringUtils.contains(rank.getLandingPage(), ".google.")) {
					continue;
				} else {
					rankVo = rank;
					break;
				}
			}
			// get different url
//			if ((rankVo.getSubRankVOs() != null && rankVo.getSubRankVOs().size() > 0) || !StringUtils.equalsIgnoreCase(url, rankVo.getLandingPage())) {
			String newParsedUrl = rankVo.getLandingPage();
			if (StringUtils.contains(newParsedUrl, "#")) {
				newParsedUrl = StringUtils.substringBefore(newParsedUrl, "#");
			}
			if (!StringUtils.equalsIgnoreCase(url, newParsedUrl)) {
				System.out.println("=== get wrong kw, queryDate:" + queryDate + ", engine:" + engine + ", language:" + language 
						+ ", kid:" + keyword_rankcheck_id + ", kw:" + vo.getKeywordForHtml() + ", top1url:" + url + ", newParsed:" + rankVo.getLandingPage() 
						+ ", newParsedSubrank:" + ((rankVo.getSubRankVOs() != null && rankVo.getSubRankVOs().size() > 0) ? rankVo.getSubRankVOs().size() : 0));
				wrongKidList.add(keyword_rankcheck_id);
			}
		}
		
		return wrongKidList;
	}
	
	private List<String> createCheckSqlForDifferentUrlCnt(List<KeywordRankVO>kwList, int queryDate, int engine, int language, boolean isMobile) throws Exception{
		Map<String, KeywordRankVO> kwFpsMap = new HashMap<String, KeywordRankVO>();
		
		Set<String> domains = new HashSet<>();
		List<Integer> kidList = new ArrayList<Integer>();
		for (KeywordRankVO vo : kwList) {
			int kid = vo.getId();
			int fps = vo.getKeywordRankEntityVOs() == null ? 0 : vo.getKeywordRankEntityVOs().size();
			kwFpsMap.put(String.valueOf(kid), vo);
			
			domains.add(vo.getDomainList().get(0));
			kidList.add(kid);
		}
		StringBuffer sql = new StringBuffer();
		String date = yyyy_MM_dd.format(yyyyMMdd.parse(String.valueOf(queryDate)));
		
		String tableName = "%device%_ranking_detail_%country%_%month%";
		tableName = StringUtils.replace(tableName, "%device%", isMobile ? "m" : "d");
		tableName = StringUtils.replace(tableName, "%country%", (engine == 1 && language == 1) ? "us" : "intl");
		tableName = StringUtils.replace(tableName, "%month%", StringUtils.substring(String.valueOf(queryDate), 0, 6));
		
		sql.append(" select keyword_rankcheck_id, own_domain_id, count() as cnt                                          ");
		sql.append(" from " + tableName + "                                                                     ");
		sql.append(" where location_id = 0 and ranking_date = '" + date + "' and own_domain_id in (" + StringUtils.join(domains, ",") + ") and keyword_rankcheck_id in (" + StringUtils.join(kidList, ",") + ")  ");
		sql.append(" group by keyword_rankcheck_id, own_domain_id                                                        ");
		
		List<String> wrongKidList = new ArrayList<>();
		List<Map<String, Object>> results = riDailyRankingService.queryForAll(sql.toString());
		for (Map<String, Object> map : results) {
			String keyword_rankcheck_id = map.get("keyword_rankcheck_id").toString();
			String cnt = map.get("cnt").toString();
			KeywordRankVO vo = kwFpsMap.get(keyword_rankcheck_id);
			int fps = getRankingCount(vo);
			if (fps != Integer.valueOf(cnt)) {
				wrongKidList.add(keyword_rankcheck_id);
				System.out.println("====wrong kw, engine:" + engine + ", language:" + language + ", queryDate:" + queryDate 
						+ ", kid:" + keyword_rankcheck_id + ", kw:" + vo.getKeywordForHtml() + ", new fps:" + fps + ", db cnt:" + cnt + ", domains:" + vo.getDomainList());
			}
		}
		return wrongKidList;
	}
	
	private int getRankingCount(KeywordRankVO vo) {
		int cnt = 0;
		if (vo.getKeywordRankEntityVOs() != null) {
			for (KeywordRankEntityVO svo : vo.getKeywordRankEntityVOs()) {
				if (!StringUtils.startsWithIgnoreCase(svo.getLandingPage(), "ftp:")) {
					cnt++;
				}
			}
		}
		return cnt;
	}
	
}
