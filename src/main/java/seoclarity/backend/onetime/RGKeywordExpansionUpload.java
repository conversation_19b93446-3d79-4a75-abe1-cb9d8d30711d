package seoclarity.backend.onetime;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.KeywordSuggestDAO;
import seoclarity.backend.entity.actonia.ContentJsonPOJO;
import seoclarity.backend.entity.actonia.KeywordSuggest;
import seoclarity.backend.upload.RGKeywordExpansionV2;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.*;

/**
 * @program: backend
 * @description: https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/723128339
 * @packagename: seoclarity.backend.onetime
 * @author: cil
 * @date: 2021-07-15 11:03
 **/
public class RGKeywordExpansionUpload {
    //todo write read address
    private static final String READ_FILE_PATH = "/home/<USER>/analytics/backend/clarity-backend-scripts/log/ngram-result-final.txt";
//    private static final String READ_FILE_PATH = "/Users/<USER>/Downloads/ngram-result-final.txt";
    private static final String JP = "jp";
    private static final String US = "us";
    private static final String UK = "uk";
    private KeywordSuggestDAO keywordSuggestDAO;

    //jp oid is 8730
    private static final int JP_OID = 8730;

    public RGKeywordExpansionUpload() {
        keywordSuggestDAO = SpringBeanFactory.getBean("keywordSuggestDAO");
    }

    public static void main(String[] args) {
        System.out.println("startTime :" + new Date());
        RGKeywordExpansionUpload ins = new RGKeywordExpansionUpload();
        ins.process();

    }

    private void process() {
        RGKeywordExpansionV2 rg = new RGKeywordExpansionV2();
        //read file (uk/us)
        Map<String, List<String[]>> stringListMap = readFile(READ_FILE_PATH);
        int num = 0;
        //us
        List<ContentJsonPOJO> usJsonPOJOList = stringArrToJsonPOJO(stringListMap.get(US), US);
        rg.doSomething(usJsonPOJOList, num);

        //uk
        List<ContentJsonPOJO> ukJsonPOJOList = stringArrToJsonPOJO(stringListMap.get(UK), UK);
        rg.doSomething(ukJsonPOJOList, num);

        //jp
        List<String> jpKeywordSuggests = selectByOid(JP_OID);
        List<ContentJsonPOJO> jpJsonPOJOList = stringToJsonPOJO(jpKeywordSuggests, JP);
        ;
        rg.doSomething(jpJsonPOJOList, num);
    }

    private List<String> selectByOid(int oid) {
        return keywordSuggestDAO.selectByOid(oid);
    }

    private static Map<String, List<String[]>> readFile(String readPath) {
        Map countryMap = new HashMap(2);
        List<String[]> usList = new LinkedList<>();
        List<String[]> ukList = new LinkedList<>();

        File fin = new File(readPath);
        BufferedReader br = null;
        try {
            FileInputStream fis = new FileInputStream(fin);
            //Construct BufferedReader from InputStreamReader
            br = new BufferedReader(new InputStreamReader(fis));

            String line;
            while ((line = br.readLine()) != null) {
                String[] split = line.split("\t");
                if (split.length<2)
                    continue;
                if (JP.equals(split[1]))
                    continue;
                if (UK.equals(split[1]))
                    ukList.add(Arrays.copyOfRange(split, 0,2));
                if (US.equals(split[1]))
                    usList.add(Arrays.copyOfRange(split, 0,2));

            }
            countryMap.put(US, usList);
            countryMap.put(UK, ukList);
            return countryMap;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;


    }

    private static List<ContentJsonPOJO> stringToJsonPOJO(List<String> countryAndSearchText, String country) {
        Set<ContentJsonPOJO> jsonPOJOset = new HashSet<>();
        for (String jsonStr : countryAndSearchText) {
            ContentJsonPOJO contentJsonPOJO = new ContentJsonPOJO();
            contentJsonPOJO.setCountry(country);
            contentJsonPOJO.setSearch(jsonStr);
            jsonPOJOset.add(contentJsonPOJO);

        }

        List<ContentJsonPOJO> jsonPOJOList = new LinkedList<>(jsonPOJOset);
        Collections.sort(jsonPOJOList);
        return jsonPOJOList;
    }

    private static List<ContentJsonPOJO> stringArrToJsonPOJO(List<String[]> countryAndSearchText, String country) {
        Set<ContentJsonPOJO> jsonPOJOset = new HashSet<>();
        for (String[] jsonStr : countryAndSearchText) {
            ContentJsonPOJO contentJsonPOJO = new ContentJsonPOJO();
            contentJsonPOJO.setCountry(country);
            contentJsonPOJO.setSearch(jsonStr[0]);
            jsonPOJOset.add(contentJsonPOJO);

        }

        List<ContentJsonPOJO> jsonPOJOList = new LinkedList<>(jsonPOJOset);

        Collections.sort(jsonPOJOList);
        return jsonPOJOList;
    }


}
