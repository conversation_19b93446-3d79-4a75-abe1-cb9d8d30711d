package seoclarity.backend.onetime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import seoclarity.backend.dao.clickhouse.ga.GaClarityDBEntityDAO;
import seoclarity.backend.dao.clickhouse.gagsc.GaGscClarityDBEntityDAO;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.clickhouse.ga.GoogleAnalyticsEntity;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.MergeGaGscDataOnetime" -Dexec.args=""
public class MergeGaGscDataOnetime {
	
	
	private GaClarityDBEntityDAO gaClarityDBEntityDAO;
	private GscBaseDao gscBaseDao;
	private GaGscClarityDBEntityDAO gaGscClarityDBEntityDAO;

	public MergeGaGscDataOnetime() {
		gaClarityDBEntityDAO = SpringBeanFactory.getBean("gaClarityDBEntityDAO");
		gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
		gaGscClarityDBEntityDAO = SpringBeanFactory.getBean("gaGscClarityDBEntityDAO");
	}

	public static void main(String[] args) {
		MergeGaGscDataOnetime mergeGaGscDataOnetime = new MergeGaGscDataOnetime();
		mergeGaGscDataOnetime.process();
	}
	


	
	private static Integer processDomainId = 256;
	private static Integer processDomainRelId = 2500;
	private static String startDate = "2022-01-01";
	private static String endDate = "2022-03-20";
	
	private void process() {
		
		List<GoogleAnalyticsEntity> gaList = gaClarityDBEntityDAO.getExtractData(processDomainId, startDate, endDate);
		if (gaList != null && gaList.size() > 0) {
			System.out.println(" gaList size:" + gaList.size() + ", OID:" + processDomainId + ", sd:" + startDate + ", ed:" + endDate);
		}
		
		System.out.println(" load GA into GaGscDB!");
		gaGscClarityDBEntityDAO.insertForBatchGa(gaList);
//		
		
		List<GscEntity> gscList = gscBaseDao.getExtractData(processDomainId, processDomainRelId, startDate, endDate);
		if (gscList != null && gscList.size() > 0) {
			System.out.println(" gscList size:" + gscList.size()  + ", OID:" + processDomainId + ", sd:" + startDate + ", ed:" + endDate);
		}
		
		System.out.println(" load Gsc into GaGscDB!");
		gaGscClarityDBEntityDAO.insertForBatchGsc(gscList);
		
	}
}
