package seoclarity.backend.onetime.conductor.entity;

import lombok.Getter;
import lombok.Setter;

import java.sql.Date;

/**
 * <AUTHOR>
 * @date 2020-04-23
 * @path seoclarity.actonia_competitor_summary.entity.dailyRanking.DailyRankingSubrankEntity
 * 
 */
public class DailyRankingSubrankEntity {
	private String keywordName; 
	private int ownDomainId; 
	private int keywordRankcheckId; 
	private int engineId; 
	private int languageId; 
	private int locationId; 

	private String rankingDate; 
	private String domainReverse; 
	private String rootDomainReverse; 
	private long avgSearchVolume; 
	private String uri; 
	private String url; 
	private int protocol; 
	private int subRank; 
	private int rank; 
	private int rankingType; 
	private int urlType; 
	private String[] attrskey; 
	private String[] attrsvalue; 
	private int sign = 1; 
	private int versioning;
	public String getKeywordName() {
		return keywordName;
	}
	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}
	public int getOwnDomainId() {
		return ownDomainId;
	}
	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	public int getKeywordRankcheckId() {
		return keywordRankcheckId;
	}
	public void setKeywordRankcheckId(int keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}
	public int getEngineId() {
		return engineId;
	}
	public void setEngineId(int engineId) {
		this.engineId = engineId;
	}
	public int getLanguageId() {
		return languageId;
	}
	public void setLanguageId(int languageId) {
		this.languageId = languageId;
	}
	public int getLocationId() {
		return locationId;
	}
	public void setLocationId(int locationId) {
		this.locationId = locationId;
	}
	public String getRankingDate() {
		return rankingDate;
	}
	public void setRankingDate(String rankingDate) {
		this.rankingDate = rankingDate;
	}
	public String getDomainReverse() {
		return domainReverse;
	}
	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}
	public String getRootDomainReverse() {
		return rootDomainReverse;
	}
	public void setRootDomainReverse(String rootDomainReverse) {
		this.rootDomainReverse = rootDomainReverse;
	}
	public long getAvgSearchVolume() {
		return avgSearchVolume;
	}
	public void setAvgSearchVolume(long avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume;
	}
	public String getUri() {
		return uri;
	}
	public void setUri(String uri) {
		this.uri = uri;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public int getProtocol() {
		return protocol;
	}
	public void setProtocol(int protocol) {
		this.protocol = protocol;
	}
	public int getSubRank() {
		return subRank;
	}
	public void setSubRank(int subRank) {
		this.subRank = subRank;
	}
	public int getRank() {
		return rank;
	}
	public void setRank(int rank) {
		this.rank = rank;
	}
	public int getRankingType() {
		return rankingType;
	}
	public void setRankingType(int rankingType) {
		this.rankingType = rankingType;
	}
	public int getUrlType() {
		return urlType;
	}
	public void setUrlType(int urlType) {
		this.urlType = urlType;
	}
	public String[] getAttrskey() {
		return attrskey;
	}
	public void setAttrskey(String[] attrskey) {
		this.attrskey = attrskey;
	}
	public String[] getAttrsvalue() {
		return attrsvalue;
	}
	public void setAttrsvalue(String[] attrsvalue) {
		this.attrsvalue = attrsvalue;
	}
	public int getSign() {
		return sign;
	}
	public void setSign(int sign) {
		this.sign = sign;
	}
	public int getVersioning() {
		return versioning;
	}
	public void setVersioning(int versioning) {
		this.versioning = versioning;
	}

}
