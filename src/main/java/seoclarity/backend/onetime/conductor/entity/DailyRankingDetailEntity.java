package seoclarity.backend.onetime.conductor.entity;

import lombok.Getter;
import lombok.Setter;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-04-23
 * @path seoclarity.actonia_competitor_summary.entity.dailyRanking.DailyRankingDetailEntity
 * 
 */
public class DailyRankingDetailEntity {
	private String keywordName; 
	private int ownDomainId; 
	private int keywordRankcheckId; 
	private int engineId; 
	private int languageId; 
	private int locationId; 

	private String domainReverse; 
	private String rootDomainReverse; 
	private String uri; 
	private String url; 
	private long urlhash; 
	private int protocol; 
	private int trueRank; 
	private int webRank; 
	private int type; 
	private long avgSearchVolume; 
	private float cpc; 
	private int hrd; 
	private int hrrd; 
	private String rankingDate; 
	private int rating; 
	private long wtdVolTr; 
	private long estTrafficTr; 
	private long wtdVolWr; 
	private long estTrafficWr; 
	private String folderLevel1; 
	private long folderLevel1Hash; 
	private String folderLevel2; 
	private long folderLevel2Hash; 
	private String folderLevel3; 
	private long folderLevel3Hash; 
	private String[] attrskey; 
	private String[] attrsvalue; 
	private int sign = 1; 
	private int versioning = 0; 
	private String label; 
	private String meta; 
	private int lableLen; 
	private int metaLen; 
	private long labelHash; 
	private long metaHash; 
	private String frequentlyOccurringDomain; 
	private int frequentlyOccurringDomainCount;
	private String device;

	private List<DailyRankingSubrankEntity> subRankList = new ArrayList<>();

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public int getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(int keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public int getEngineId() {
		return engineId;
	}

	public void setEngineId(int engineId) {
		this.engineId = engineId;
	}

	public int getLanguageId() {
		return languageId;
	}

	public void setLanguageId(int languageId) {
		this.languageId = languageId;
	}

	public int getLocationId() {
		return locationId;
	}

	public void setLocationId(int locationId) {
		this.locationId = locationId;
	}

	public String getDomainReverse() {
		return domainReverse;
	}

	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}

	public String getRootDomainReverse() {
		return rootDomainReverse;
	}

	public void setRootDomainReverse(String rootDomainReverse) {
		this.rootDomainReverse = rootDomainReverse;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public long getUrlhash() {
		return urlhash;
	}

	public void setUrlhash(long urlhash) {
		this.urlhash = urlhash;
	}

	public int getProtocol() {
		return protocol;
	}

	public void setProtocol(int protocol) {
		this.protocol = protocol;
	}

	public int getTrueRank() {
		return trueRank;
	}

	public void setTrueRank(int trueRank) {
		this.trueRank = trueRank;
	}

	public int getWebRank() {
		return webRank;
	}

	public void setWebRank(int webRank) {
		this.webRank = webRank;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public long getAvgSearchVolume() {
		return avgSearchVolume;
	}

	public void setAvgSearchVolume(long avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume;
	}

	public float getCpc() {
		return cpc;
	}

	public void setCpc(float cpc) {
		this.cpc = cpc;
	}

	public int getHrd() {
		return hrd;
	}

	public void setHrd(int hrd) {
		this.hrd = hrd;
	}

	public int getHrrd() {
		return hrrd;
	}

	public void setHrrd(int hrrd) {
		this.hrrd = hrrd;
	}

	public String getRankingDate() {
		return rankingDate;
	}

	public void setRankingDate(String rankingDate) {
		this.rankingDate = rankingDate;
	}

	public int getRating() {
		return rating;
	}

	public void setRating(int rating) {
		this.rating = rating;
	}

	public long getWtdVolTr() {
		return wtdVolTr;
	}

	public void setWtdVolTr(long wtdVolTr) {
		this.wtdVolTr = wtdVolTr;
	}

	public long getEstTrafficTr() {
		return estTrafficTr;
	}

	public void setEstTrafficTr(long estTrafficTr) {
		this.estTrafficTr = estTrafficTr;
	}

	public long getWtdVolWr() {
		return wtdVolWr;
	}

	public void setWtdVolWr(long wtdVolWr) {
		this.wtdVolWr = wtdVolWr;
	}

	public long getEstTrafficWr() {
		return estTrafficWr;
	}

	public void setEstTrafficWr(long estTrafficWr) {
		this.estTrafficWr = estTrafficWr;
	}

	public String getFolderLevel1() {
		return folderLevel1;
	}

	public void setFolderLevel1(String folderLevel1) {
		this.folderLevel1 = folderLevel1;
	}

	public long getFolderLevel1Hash() {
		return folderLevel1Hash;
	}

	public void setFolderLevel1Hash(long folderLevel1Hash) {
		this.folderLevel1Hash = folderLevel1Hash;
	}

	public String getFolderLevel2() {
		return folderLevel2;
	}

	public void setFolderLevel2(String folderLevel2) {
		this.folderLevel2 = folderLevel2;
	}

	public long getFolderLevel2Hash() {
		return folderLevel2Hash;
	}

	public void setFolderLevel2Hash(long folderLevel2Hash) {
		this.folderLevel2Hash = folderLevel2Hash;
	}

	public String getFolderLevel3() {
		return folderLevel3;
	}

	public void setFolderLevel3(String folderLevel3) {
		this.folderLevel3 = folderLevel3;
	}

	public long getFolderLevel3Hash() {
		return folderLevel3Hash;
	}

	public void setFolderLevel3Hash(long folderLevel3Hash) {
		this.folderLevel3Hash = folderLevel3Hash;
	}

	public String[] getAttrskey() {
		return attrskey;
	}

	public void setAttrskey(String[] attrskey) {
		this.attrskey = attrskey;
	}

	public String[] getAttrsvalue() {
		return attrsvalue;
	}

	public void setAttrsvalue(String[] attrsvalue) {
		this.attrsvalue = attrsvalue;
	}

	public int getSign() {
		return sign;
	}

	public void setSign(int sign) {
		this.sign = sign;
	}

	public int getVersioning() {
		return versioning;
	}

	public void setVersioning(int versioning) {
		this.versioning = versioning;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getMeta() {
		return meta;
	}

	public void setMeta(String meta) {
		this.meta = meta;
	}

	public int getLableLen() {
		return lableLen;
	}

	public void setLableLen(int lableLen) {
		this.lableLen = lableLen;
	}

	public int getMetaLen() {
		return metaLen;
	}

	public void setMetaLen(int metaLen) {
		this.metaLen = metaLen;
	}

	public long getLabelHash() {
		return labelHash;
	}

	public void setLabelHash(long labelHash) {
		this.labelHash = labelHash;
	}

	public long getMetaHash() {
		return metaHash;
	}

	public void setMetaHash(long metaHash) {
		this.metaHash = metaHash;
	}

	public String getFrequentlyOccurringDomain() {
		return frequentlyOccurringDomain;
	}

	public void setFrequentlyOccurringDomain(String frequentlyOccurringDomain) {
		this.frequentlyOccurringDomain = frequentlyOccurringDomain;
	}

	public int getFrequentlyOccurringDomainCount() {
		return frequentlyOccurringDomainCount;
	}

	public void setFrequentlyOccurringDomainCount(int frequentlyOccurringDomainCount) {
		this.frequentlyOccurringDomainCount = frequentlyOccurringDomainCount;
	}

	public List<DailyRankingSubrankEntity> getSubRankList() {
		return subRankList;
	}

	public void setSubRankList(List<DailyRankingSubrankEntity> subRankList) {
		this.subRankList = subRankList;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}


}
