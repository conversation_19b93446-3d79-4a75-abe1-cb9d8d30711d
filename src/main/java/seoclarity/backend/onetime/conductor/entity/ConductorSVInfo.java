package seoclarity.backend.onetime.conductor.entity;

public class ConductorSVInfo {

	/**
	 *  {"volumeItems":[{"volume":1830000,"month":9,"year":2024},{"volume":1220000,"month":8,"year":2024},
	 *  {"volume":1220000,"month":7,"year":2024},{"volume":1000000,"month":6,"year":2024},{"volume":1220000,"month":5,"year":2024},
	 *  {"volume":1500000,"month":4,"year":2024},{"volume":5000000,"month":3,"year":2024},{"volume":3350000,"month":2,"year":2024},
	 *  {"volume":3350000,"month":1,"year":2024},{"volume":5000000,"month":12,"year":2023},{"volume":3350000,"month":11,"year":2023},
	 *  {"volume":6120000,"month":10,"year":2023}],"averageVolume":2740000,"trackedSearchId":36570446}
	 */
	
	private Long averageVolume;
	
	private Integer trackedSearchId;
	
	
	public Long getAverageVolume() {
		return averageVolume;
	}
	public void setAverageVolume(Long averageVolume) {
		this.averageVolume = averageVolume;
	}
	
	public Integer getTrackedSearchId() {
		return trackedSearchId;
	}
	public void setTrackedSearchId(Integer trackedSearchId) {
		this.trackedSearchId = trackedSearchId;
	}
	
	
}
