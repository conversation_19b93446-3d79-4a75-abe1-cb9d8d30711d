package seoclarity.backend.onetime.conductor;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.google.gson.Gson;

import seoclarity.backend.clarity360.exception.TaskException;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.rankcheck.RcKeywordSeRelEntityDAO;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.onetime.conductor.entity.ConductorKeywordInfo;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

public class AddKeywordConductor {

    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;

    public AddKeywordConductor() {
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    public static void main(String[] args) {
        AddKeywordConductor in = new AddKeywordConductor();
        for (String account : desktopAccountInfos) {
                String aId = account.split("_")[0];
                in.process(aId);
        }
    }

    public static Map<Integer, Integer> locaitonMap = new HashMap<Integer, Integer>();
    public static Map<Integer, Integer> accIdDomainIdMap = new HashMap<Integer, Integer>();
    public static Map<Integer, String> domainNameMap = new HashMap<Integer, String>();

    private static final String LOCAL = "/home/<USER>/conductor";
    private static String KEYWORD_FILE_NAME_TEMPLATE = "KBB_KEYWORD_%s.txt";
    
        private static List<String> desktopAccountInfos = new ArrayList<String>();
    static {
//        desktopAccountInfos.add("16088_79964");
//        desktopAccountInfos.add("19564_96403");
//        desktopAccountInfos.add("20282_100859");
    	desktopAccountInfos.add("16088_32038");
    
        locaitonMap.put(3, 0);
        locaitonMap.put(1179, 310630);
        locaitonMap.put(1300, 310646);
        locaitonMap.put(1301, 310583);
        locaitonMap.put(1302, 310627);
        locaitonMap.put(1303, 320362);
        locaitonMap.put(1304, 322967);
        locaitonMap.put(1305, 320155);
        locaitonMap.put(1306, 320342);
        locaitonMap.put(1307, 310577);
        locaitonMap.put(1308, 310548);
        locaitonMap.put(1309, 310594);
        locaitonMap.put(1310, 310558);
        locaitonMap.put(1311, 320324);
        locaitonMap.put(1312, 320169);
        locaitonMap.put(1313, 320207);
        locaitonMap.put(1314, 310566);
        locaitonMap.put(1315, 320369);
        locaitonMap.put(1316, 320221);
        locaitonMap.put(1317, 310576);
        locaitonMap.put(1318, 320137);
        locaitonMap.put(1319, 320343);
        locaitonMap.put(1320, 320127);
        locaitonMap.put(1321, 320121);
        locaitonMap.put(1322, 320277);
        locaitonMap.put(1323, 320322);
        locaitonMap.put(1324, 320179);
        locaitonMap.put(1325, 310567);
        locaitonMap.put(1326, 323356);
        locaitonMap.put(1327, 320403);
        locaitonMap.put(1328, 320405);
        locaitonMap.put(1329, 320182);
        locaitonMap.put(1330, 320214);
        locaitonMap.put(1331, 320323);
        locaitonMap.put(1333, 320146);
        locaitonMap.put(1334, 310589);
        locaitonMap.put(1335, 310555);
        locaitonMap.put(1336, 310622);
        locaitonMap.put(1337, 310564);
        locaitonMap.put(1338, 310604);
        locaitonMap.put(1339, 320409);
        locaitonMap.put(1340, 310579);
        locaitonMap.put(1341, 310568);
        locaitonMap.put(1342, 320186);
        locaitonMap.put(1344, 320227);
        locaitonMap.put(1345, 321748);
        locaitonMap.put(1347, 320175);
        locaitonMap.put(1348, 310561);
        locaitonMap.put(1349, 310601);
        locaitonMap.put(1350, 320140);
        locaitonMap.put(1351, 320263);
        locaitonMap.put(1352, 320258);
        locaitonMap.put(1353, 310553);
        locaitonMap.put(1354, 310615);
        locaitonMap.put(1356, 310575);
        locaitonMap.put(1357, 310645);
        locaitonMap.put(1358, 320272);
        locaitonMap.put(1359, 310590);
        locaitonMap.put(1360, 320326);
        locaitonMap.put(1376, 309088);
        locaitonMap.put(1377, 320378);
        locaitonMap.put(1378, 310585);
        locaitonMap.put(1379, 320273);
        locaitonMap.put(1380, 310643);
        locaitonMap.put(1381, 320352);
        locaitonMap.put(1382, 320350);
        locaitonMap.put(1383, 310584);
        locaitonMap.put(1384, 323373);
        locaitonMap.put(1385, 320924);
        locaitonMap.put(1386, 310581);
        locaitonMap.put(1414, 323374);
        locaitonMap.put(1415, 323363);
        locaitonMap.put(1416, 310562);
        locaitonMap.put(22476, 320176);
        locaitonMap.put(22536, 323376);
        locaitonMap.put(22537, 323377);
        locaitonMap.put(22538, 323378);
        locaitonMap.put(22539, 323379);
        locaitonMap.put(22540, 323380);
        locaitonMap.put(22541, 323381);
        locaitonMap.put(22542, 323382);
        locaitonMap.put(22543, 323383);
        locaitonMap.put(22544, 323384);
        locaitonMap.put(22545, 323385);
        locaitonMap.put(22546, 320854);
        locaitonMap.put(22547, 320190);
        locaitonMap.put(22548, 323386);
        locaitonMap.put(22549, 323387);
        locaitonMap.put(22550, 323388);
        locaitonMap.put(22551, 323389);
        locaitonMap.put(22552, 323390);
        locaitonMap.put(22553, 323391);
        locaitonMap.put(22554, 323392);
        locaitonMap.put(22555, 323393);
        locaitonMap.put(22556, 320856);
        locaitonMap.put(22557, 323395);
        locaitonMap.put(22558, 323367);
        locaitonMap.put(22559, 323397);
        locaitonMap.put(22560, 320857);
        locaitonMap.put(22561, 323398);
        locaitonMap.put(22562, 320293);
        locaitonMap.put(22563, 323399);
        locaitonMap.put(22564, 323400);
        locaitonMap.put(22565, 323401);
        locaitonMap.put(22566, 320860);
        locaitonMap.put(22567, 323402);
        locaitonMap.put(22568, 323403);
        locaitonMap.put(22569, 310608);
        locaitonMap.put(22570, 323404);
        locaitonMap.put(22571, 323405);
        locaitonMap.put(22572, 321761);
        locaitonMap.put(22573, 323406);
        locaitonMap.put(22574, 320863);
        locaitonMap.put(22575, 323407);
        locaitonMap.put(22576, 323408);
        locaitonMap.put(22577, 323409);
        locaitonMap.put(22578, 310629);
        locaitonMap.put(22579, 320290);
        locaitonMap.put(22580, 320859);
        locaitonMap.put(22581, 323410);
        locaitonMap.put(22582, 323411);
        locaitonMap.put(22583, 320866);
        locaitonMap.put(22584, 323413);
        locaitonMap.put(22585, 320868);
        locaitonMap.put(22586, 323414);
        locaitonMap.put(22587, 323415);
        locaitonMap.put(22588, 320125);
        locaitonMap.put(22589, 323416);
        locaitonMap.put(22590, 320402);
        locaitonMap.put(22591, 323417);
        locaitonMap.put(22592, 323418);
        locaitonMap.put(22593, 323419);
        locaitonMap.put(22594, 323420);
        locaitonMap.put(22595, 323421);
        locaitonMap.put(22596, 323422);
        locaitonMap.put(22597, 323423);
        locaitonMap.put(22598, 320867);
        locaitonMap.put(22599, 323424);
        locaitonMap.put(22600, 323425);
        locaitonMap.put(22601, 323426);
        locaitonMap.put(22602, 323427);
        locaitonMap.put(22603, 323428);
        locaitonMap.put(22604, 310603);
        locaitonMap.put(22605, 323429);
        locaitonMap.put(22606, 323430);
        locaitonMap.put(22607, 323431);
        locaitonMap.put(22608, 323432);
        locaitonMap.put(22609, 323433);
        locaitonMap.put(22610, 323434);
        locaitonMap.put(22611, 320879);
        locaitonMap.put(22612, 323436);
        locaitonMap.put(22613, 323437);
        locaitonMap.put(22614, 310404);
        locaitonMap.put(22615, 323439);
        locaitonMap.put(22616, 323440);
        locaitonMap.put(22617, 320877);
        locaitonMap.put(22618, 323442);
        locaitonMap.put(22619, 320301);
        locaitonMap.put(22620, 323443);
        locaitonMap.put(22621, 323444);
        locaitonMap.put(22622, 323445);
        locaitonMap.put(22623, 323446);
        locaitonMap.put(22624, 310582);
        locaitonMap.put(22625, 323447);
        locaitonMap.put(22626, 323448);
        locaitonMap.put(22627, 309366);
        locaitonMap.put(22628, 323449);
        locaitonMap.put(22629, 323450);
        locaitonMap.put(22630, 323451);
        locaitonMap.put(22631, 323452);
        locaitonMap.put(22632, 323453);
        locaitonMap.put(22633, 310620);
        locaitonMap.put(22634, 323454);
        locaitonMap.put(22635, 323455);
        locaitonMap.put(22636, 323456);
        locaitonMap.put(22637, 323457);
        locaitonMap.put(22638, 323458);
        locaitonMap.put(22639, 310610);
        locaitonMap.put(22640, 323460);
        locaitonMap.put(22641, 323461);
        locaitonMap.put(22642, 323462);
        locaitonMap.put(22643, 320282);
        locaitonMap.put(22644, 323463);
        locaitonMap.put(22645, 323464);
        locaitonMap.put(22646, 323465);
        locaitonMap.put(22647, 323466);
        locaitonMap.put(22648, 323467);
        locaitonMap.put(22649, 320883);
        locaitonMap.put(22650, 323468);
        locaitonMap.put(22651, 323469);
        locaitonMap.put(22652, 323470);
        locaitonMap.put(22653, 320267);
        locaitonMap.put(22654, 323471);
        locaitonMap.put(22655, 320884);
        locaitonMap.put(22656, 310633);
        locaitonMap.put(22657, 323472);
        locaitonMap.put(22658, 310588);
        locaitonMap.put(22659, 323473);
        locaitonMap.put(22660, 323474);
        locaitonMap.put(22661, 320886);
        locaitonMap.put(22662, 323475);
        locaitonMap.put(22663, 320183);
        locaitonMap.put(22664, 320887);
        locaitonMap.put(22665, 323476);
        locaitonMap.put(22666, 320297);
        locaitonMap.put(22667, 323477);
        locaitonMap.put(22668, 323478);
        locaitonMap.put(22669, 320889);
        locaitonMap.put(22670, 320232);
        locaitonMap.put(22671, 323480);
        locaitonMap.put(22672, 323481);
        locaitonMap.put(22673, 323482);
        locaitonMap.put(22674, 320318);
        locaitonMap.put(22675, 323483);
        locaitonMap.put(22676, 323484);
        locaitonMap.put(22677, 323485);
        locaitonMap.put(22678, 323486);
        locaitonMap.put(22679, 320890);
        locaitonMap.put(22680, 323487);
        locaitonMap.put(22681, 323488);
        locaitonMap.put(22682, 323489);
        locaitonMap.put(22683, 323490);
        locaitonMap.put(22684, 320230);
        locaitonMap.put(22685, 320891);
        locaitonMap.put(22686, 323491);
        locaitonMap.put(22687, 310570);
        locaitonMap.put(22688, 323492);
        locaitonMap.put(22689, 323493);
        locaitonMap.put(22690, 323494);
        locaitonMap.put(22691, 320224);
        locaitonMap.put(22692, 323495);
        locaitonMap.put(22693, 323496);
        locaitonMap.put(22694, 323497);
        locaitonMap.put(22695, 323498);
        locaitonMap.put(22696, 323499);
        locaitonMap.put(22697, 323500);
        locaitonMap.put(22698, 323501);
        locaitonMap.put(22699, 323502);
        locaitonMap.put(22700, 320897);
        locaitonMap.put(22701, 320896);
        locaitonMap.put(22702, 310596);
        locaitonMap.put(22703, 323503);
        locaitonMap.put(22704, 323504);
        locaitonMap.put(22705, 310580);
        locaitonMap.put(22706, 323505);
        locaitonMap.put(22707, 310626);
        locaitonMap.put(22708, 323506);
        locaitonMap.put(22709, 323507);
        locaitonMap.put(22710, 323508);
        locaitonMap.put(22711, 323509);
        locaitonMap.put(22712, 323510);
        locaitonMap.put(22713, 323511);
        locaitonMap.put(22714, 323512);
        locaitonMap.put(22715, 323513);
        locaitonMap.put(22716, 323514);
        locaitonMap.put(22717, 323515);
        locaitonMap.put(22718, 323516);
        locaitonMap.put(22719, 323517);
        locaitonMap.put(22720, 323518);
        locaitonMap.put(22721, 323519);
        locaitonMap.put(22722, 323520);
        locaitonMap.put(22723, 323521);
        locaitonMap.put(22724, 323522);
        locaitonMap.put(22725, 320898);
        locaitonMap.put(22726, 323523);
        locaitonMap.put(22727, 323524);
        locaitonMap.put(22728, 323525);
        locaitonMap.put(22729, 323526);
        locaitonMap.put(22730, 320215);
        locaitonMap.put(22731, 323528);
        locaitonMap.put(22732, 323529);
        locaitonMap.put(22733, 323530);
        locaitonMap.put(22734, 323531);
        locaitonMap.put(22735, 320204);
        locaitonMap.put(22736, 323532);
        locaitonMap.put(22737, 323533);
        locaitonMap.put(22738, 323534);
        locaitonMap.put(22739, 323535);
        locaitonMap.put(22740, 323536);
        locaitonMap.put(22741, 320119);
        locaitonMap.put(22742, 323537);
        locaitonMap.put(22743, 323538);
        locaitonMap.put(22744, 323539);
        locaitonMap.put(22745, 323540);
        locaitonMap.put(22746, 323541);
        locaitonMap.put(22747, 323542);
        locaitonMap.put(22748, 323543);
        locaitonMap.put(22749, 320901);
        locaitonMap.put(22750, 323544);
        locaitonMap.put(22751, 323545);
        locaitonMap.put(22752, 323546);
        locaitonMap.put(22753, 323547);
        locaitonMap.put(22754, 323548);
        locaitonMap.put(22755, 310652);
        locaitonMap.put(22756, 323550);
        locaitonMap.put(22757, 323551);
        locaitonMap.put(22758, 323552);
        locaitonMap.put(22759, 320899);
        locaitonMap.put(22760, 320404);
        locaitonMap.put(22761, 323554);
        locaitonMap.put(22762, 323555);
        locaitonMap.put(22763, 320152);
        locaitonMap.put(22764, 323556);
        locaitonMap.put(22765, 323557);
        locaitonMap.put(22766, 323558);
        locaitonMap.put(22767, 320410);
        locaitonMap.put(22768, 323559);
        locaitonMap.put(22769, 323560);
        locaitonMap.put(22770, 323561);
        locaitonMap.put(22771, 320903);
        locaitonMap.put(22772, 323562);
        locaitonMap.put(22773, 323563);
        locaitonMap.put(22774, 320904);
        locaitonMap.put(22775, 323565);
        locaitonMap.put(22776, 323566);
        locaitonMap.put(22777, 323567);
        locaitonMap.put(22778, 323568);
        locaitonMap.put(22779, 320164);
        locaitonMap.put(22780, 308990);
        locaitonMap.put(22781, 320288);
        locaitonMap.put(22782, 323569);
        locaitonMap.put(22783, 323570);
        locaitonMap.put(22784, 310642);
        locaitonMap.put(22785, 323571);
        locaitonMap.put(22786, 323572);
        locaitonMap.put(22787, 323573);
        locaitonMap.put(22788, 323574);
        locaitonMap.put(22789, 320908);
        locaitonMap.put(22790, 320910);
        locaitonMap.put(22791, 320909);
        locaitonMap.put(22792, 320911);
        locaitonMap.put(22793, 323575);
        locaitonMap.put(22794, 323576);
        locaitonMap.put(22795, 323577);
        locaitonMap.put(22796, 323578);
        locaitonMap.put(22797, 323579);
        locaitonMap.put(22798, 323580);
        locaitonMap.put(22799, 323581);
        locaitonMap.put(22800, 310660);
        locaitonMap.put(22801, 323582);
        locaitonMap.put(22802, 320225);
        locaitonMap.put(22803, 323583);
        locaitonMap.put(22804, 323584);
        locaitonMap.put(22805, 323585);
        locaitonMap.put(22806, 323586);
        locaitonMap.put(22807, 323587);
        locaitonMap.put(22808, 320912);
        locaitonMap.put(22809, 323588);
        locaitonMap.put(22810, 323589);
        locaitonMap.put(22811, 323590);
        locaitonMap.put(22812, 323591);
        locaitonMap.put(22813, 323592);
        locaitonMap.put(22814, 323593);
        locaitonMap.put(22815, 320914);
        locaitonMap.put(22816, 323594);
        locaitonMap.put(22817, 320913);
        locaitonMap.put(22818, 310616);
        locaitonMap.put(22819, 323596);
        locaitonMap.put(22820, 323597);
        locaitonMap.put(22821, 323598);
        locaitonMap.put(22822, 323599);
        locaitonMap.put(22823, 323600);
        locaitonMap.put(22824, 323601);
        locaitonMap.put(22825, 320873);
        locaitonMap.put(22826, 310617);
        locaitonMap.put(22827, 323602);
        locaitonMap.put(22828, 323603);
        locaitonMap.put(22829, 323604);
        locaitonMap.put(22830, 320917);
        locaitonMap.put(22831, 323606);
        locaitonMap.put(22832, 323607);
        locaitonMap.put(22833, 323608);
        locaitonMap.put(22834, 323609);
        locaitonMap.put(22835, 310591);
        locaitonMap.put(22836, 323610);
        locaitonMap.put(22837, 311075);
        locaitonMap.put(22838, 320919);
        locaitonMap.put(22839, 323611);
        locaitonMap.put(22840, 320330);
        locaitonMap.put(22841, 310640);
        locaitonMap.put(22842, 323612);
        locaitonMap.put(22843, 323613);
        locaitonMap.put(22844, 320229);
        locaitonMap.put(22845, 308656);
        locaitonMap.put(22846, 310650);
        locaitonMap.put(22847, 320922);
        locaitonMap.put(22848, 323614);
        locaitonMap.put(22849, 323615);
        locaitonMap.put(22850, 323616);
        locaitonMap.put(22851, 323617);
        locaitonMap.put(22852, 323618);
        locaitonMap.put(22853, 323619);
        locaitonMap.put(22854, 323620);
        locaitonMap.put(22855, 320379);
        locaitonMap.put(22856, 323621);
        locaitonMap.put(22857, 323622);
        locaitonMap.put(22858, 323623);
        locaitonMap.put(22859, 323624);
        locaitonMap.put(22860, 323625);
        locaitonMap.put(22861, 323626);
        locaitonMap.put(22862, 323627);
        locaitonMap.put(22863, 323628);
        locaitonMap.put(22864, 320382);
        locaitonMap.put(22865, 323629);
        locaitonMap.put(22866, 323630);
        locaitonMap.put(22867, 323631);
        locaitonMap.put(22868, 310578);
        locaitonMap.put(22869, 323632);
        locaitonMap.put(22870, 323633);
        locaitonMap.put(22871, 320157);
        locaitonMap.put(22872, 310606);
        locaitonMap.put(22873, 323365);
        locaitonMap.put(22874, 323636);
        locaitonMap.put(22875, 323637);
        locaitonMap.put(22876, 323638);
        locaitonMap.put(22877, 323639);
        locaitonMap.put(22878, 320925);
        locaitonMap.put(22879, 323640);
        locaitonMap.put(22880, 320926);
        locaitonMap.put(22881, 320265);
        locaitonMap.put(22882, 323641);
        locaitonMap.put(22883, 323362);
        locaitonMap.put(22884, 323643);
        locaitonMap.put(22885, 323644);
        locaitonMap.put(22886, 323645);
        locaitonMap.put(22887, 323646);
        locaitonMap.put(22888, 323647);
        locaitonMap.put(22889, 323648);
        locaitonMap.put(22890, 323649);
        locaitonMap.put(22891, 310574);
        locaitonMap.put(22892, 323650);
        locaitonMap.put(22893, 323651);
        locaitonMap.put(22894, 323652);
        locaitonMap.put(22895, 320250);
        locaitonMap.put(22896, 320143);
        locaitonMap.put(22897, 323653);
        locaitonMap.put(22898, 323654);
        locaitonMap.put(22899, 323655);
        locaitonMap.put(22900, 323656);
        locaitonMap.put(22901, 323657);
        locaitonMap.put(22902, 320932);
        locaitonMap.put(22903, 320363);
        locaitonMap.put(22904, 323659);
        locaitonMap.put(22905, 320928);
        locaitonMap.put(22906, 323660);
        locaitonMap.put(22907, 323661);
        locaitonMap.put(22908, 320314);
        locaitonMap.put(22909, 323662);
        locaitonMap.put(22910, 320241);
        locaitonMap.put(22911, 323663);
        locaitonMap.put(22912, 323664);
        locaitonMap.put(22913, 323665);
        locaitonMap.put(22914, 323666);
        locaitonMap.put(22915, 320289);
        locaitonMap.put(22916, 323667);
        locaitonMap.put(22917, 320340);
        locaitonMap.put(22918, 323366);
        locaitonMap.put(22919, 323669);
        locaitonMap.put(22920, 320411);
        locaitonMap.put(22921, 323670);
        locaitonMap.put(22922, 323671);
        locaitonMap.put(22923, 323672);
        locaitonMap.put(22924, 323673);
        locaitonMap.put(22925, 323674);
        locaitonMap.put(22926, 323675);
        locaitonMap.put(22927, 323676);
        locaitonMap.put(22928, 321743);
        locaitonMap.put(22929, 321739);
        locaitonMap.put(22930, 310598);
        locaitonMap.put(22931, 310602);
        locaitonMap.put(22932, 323677);
        locaitonMap.put(2341, 310571);
        locaitonMap.put(2342, 308679);
        locaitonMap.put(2346, 310319);
        locaitonMap.put(2347, 323680);
        locaitonMap.put(2350, 310320);
        locaitonMap.put(2352, 310658);
        locaitonMap.put(2355, 320234);
        locaitonMap.put(2360, 320921);
        locaitonMap.put(2367, 320349);
        locaitonMap.put(2619, 320358);
        locaitonMap.put(2620, 320203);
        locaitonMap.put(2622, 310586);
        locaitonMap.put(2625, 320865);
        locaitonMap.put(2626, 310597);
        locaitonMap.put(2628, 310624);
        locaitonMap.put(2630, 323682);
        locaitonMap.put(2631, 310592);
        locaitonMap.put(2632, 323683);
        locaitonMap.put(2633, 323684);
        locaitonMap.put(2636, 320248);
        locaitonMap.put(2637, 320881);
        locaitonMap.put(2639, 310097);
        locaitonMap.put(2640, 320882);
        locaitonMap.put(2641, 320244);
        locaitonMap.put(2642, 320880);
        locaitonMap.put(2644, 323685);
        locaitonMap.put(2645, 323686);
        locaitonMap.put(2648, 323687);
        locaitonMap.put(2649, 320892);
        locaitonMap.put(2650, 323688);
        locaitonMap.put(2651, 323689);
        locaitonMap.put(2652, 310649);
        locaitonMap.put(2653, 320895);
        locaitonMap.put(2654, 323690);
        locaitonMap.put(2655, 323691);
        locaitonMap.put(2656, 320274);
        locaitonMap.put(2657, 323692);
        locaitonMap.put(2659, 320210);
        locaitonMap.put(2660, 320139);
        locaitonMap.put(2661, 320325);
        locaitonMap.put(2663, 323693);
        locaitonMap.put(2664, 323694);
        locaitonMap.put(2665, 323695);
        locaitonMap.put(2666, 323696);
        locaitonMap.put(2667, 320918);
        locaitonMap.put(2669, 320915);
        locaitonMap.put(2674, 323369);
        locaitonMap.put(2675, 320131);
        locaitonMap.put(2676, 323698);
        locaitonMap.put(2677, 320218);
        locaitonMap.put(2678, 323699);
        locaitonMap.put(2679, 321755);
        locaitonMap.put(2680, 320153);
        locaitonMap.put(2682, 323700);
        locaitonMap.put(2683, 320927);
        locaitonMap.put(2684, 323701);
        locaitonMap.put(2745, 323702);
        locaitonMap.put(2746, 321757);
        locaitonMap.put(2748, 310628);
        locaitonMap.put(2749, 323354);
        locaitonMap.put(2750, 323703);
        locaitonMap.put(2751, 323704);
        locaitonMap.put(2755, 310654);
        locaitonMap.put(2756, 323705);
        locaitonMap.put(2758, 323706);
        locaitonMap.put(2759, 323707);
        locaitonMap.put(2760, 323708);
        locaitonMap.put(391, 310572);
        locaitonMap.put(392, 310551);
        locaitonMap.put(393, 308994);
        locaitonMap.put(394, 310549);
        locaitonMap.put(395, 310560);
        locaitonMap.put(397, 308988);
        locaitonMap.put(399, 308962);
        locaitonMap.put(400, 308987);
        locaitonMap.put(401, 308995);
        locaitonMap.put(402, 308991);
        locaitonMap.put(403, 308989);
        locaitonMap.put(404, 308993);
        locaitonMap.put(405, 310550);
        locaitonMap.put(406, 310552);
        locaitonMap.put(407, 310557);
        locaitonMap.put(408, 308992);
        locaitonMap.put(409, 323709);
        locaitonMap.put(410, 308986);
        locaitonMap.put(795, 310599);
        locaitonMap.put(796, 310559);
        locaitonMap.put(797, 323710);
        locaitonMap.put(798, 309060);
        locaitonMap.put(799, 308996);
        locaitonMap.put(800, 310565);
        locaitonMap.put(822, 310556);

    
//      accIdDomainIdMap.put(16088, 13649);
//      accIdDomainIdMap.put(19564, 13651);
//      accIdDomainIdMap.put(20282, 13653);
//      accIdDomainIdMap.put(20840, 13648);
    

//        accIdDomainIdMap.put(16088, 13475);
//        accIdDomainIdMap.put(19564, 13650);
//        accIdDomainIdMap.put(20282, 13652);
    	accIdDomainIdMap.put(16088, 13649);
    
    	domainNameMap.put(13649, "argos.co.uk");
//        domainNameMap.put(13649, "argos.co.uk");
//        domainNameMap.put(13651, "tuclothing.sainsburys.co.uk");
//        domainNameMap.put(13653, "habitat.co.uk");
//        domainNameMap.put(13648, "sainsburys.co.uk");
    
    }
    private void process(String accId) {
    
        List<String[]> desktopKeywordList = new ArrayList<String[]>();
        List<String[]> mobileKeywordList = new ArrayList<String[]>();
        List<String[]> desktopGeoKeywordList = new ArrayList<String[]>();
        List<String[]> mobileGeoKeywordList = new ArrayList<String[]>();
       
        String filePath = LOCAL + "/" + String.format(KEYWORD_FILE_NAME_TEMPLATE, accId);
    
        File keywordFile = new File(filePath);
        if (keywordFile == null || !keywordFile.isFile()) {
                        throw new TaskException("File not found!" + filePath);
                }
    
        try {
            String content = "";
            StringBuffer json = new StringBuffer();
            BufferedReader bf = new BufferedReader(new FileReader(keywordFile));
            while (content != null) {
                content = bf.readLine();

                if (content == null) {
                        break;
                }

                json.append(content);

            }
            bf.close();

            ConductorKeywordInfo[] keywordArray = new Gson().fromJson(json.toString(), ConductorKeywordInfo[].class);

            Integer locationId = 0;
            for(ConductorKeywordInfo conductorKeywordInfo : keywordArray) {
                if (conductorKeywordInfo.getIsActive()) {

                    locationId = locaitonMap.get(conductorKeywordInfo.getLocationId());
                    
                    if (locationId == null) {
                    	System.out.println(" location not found! " + conductorKeywordInfo.getLocationId());
						continue;
					}

                    if (locationId == 0) {
                        if (StringUtils.equalsIgnoreCase(conductorKeywordInfo.getDeviceId(), "1")) {

                                desktopKeywordList.add(new String[] {conductorKeywordInfo.getQueryPhrase()});
                        } else if (StringUtils.equalsIgnoreCase(conductorKeywordInfo.getDeviceId(), "2")) {

                                mobileKeywordList.add(new String[] {conductorKeywordInfo.getQueryPhrase()});
                        }
                    } else {
                        if (StringUtils.equalsIgnoreCase(conductorKeywordInfo.getDeviceId(), "1")) {

                                desktopGeoKeywordList.add(new String[] {conductorKeywordInfo.getQueryPhrase(), locationId + ""});
                        } else if (StringUtils.equalsIgnoreCase(conductorKeywordInfo.getDeviceId(), "2")) {

                                mobileGeoKeywordList.add(new String[] {conductorKeywordInfo.getQueryPhrase(), locationId + ""});
                        }
                    }
                }
            }

            System.out.println("desktopKeywordList:" + desktopKeywordList.size());
            System.out.println("mobileKeywordList:" + mobileKeywordList.size());
            System.out.println("desktopGeoKeywordList:" + desktopGeoKeywordList.size());
            System.out.println("mobileGeoKeywordList:" + mobileGeoKeywordList.size());

        } catch (Exception e) {
                e.printStackTrace();
        }

        if (CollectionUtils.isNotEmpty(desktopKeywordList)) {
                getQBaseTast(desktopKeywordList, accIdDomainIdMap.get(NumberUtils.toInt(accId)), "6-8-d", ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD);
                }
    
        if (CollectionUtils.isNotEmpty(mobileKeywordList)) {
                getQBaseTast(mobileKeywordList, accIdDomainIdMap.get(NumberUtils.toInt(accId)), "6-8-m", ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD);
        
        }
    
        if (CollectionUtils.isNotEmpty(desktopGeoKeywordList)) {
                getQBaseTast(desktopGeoKeywordList, accIdDomainIdMap.get(NumberUtils.toInt(accId)), "6-8-d", ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO);
    
        }
    
        if (CollectionUtils.isNotEmpty(mobileGeoKeywordList)) {
                getQBaseTast(mobileGeoKeywordList, accIdDomainIdMap.get(NumberUtils.toInt(accId)), "6-8-m", ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO);
    
        }
    
    }
    
    /**
     * we need set engine value to info and detail!
     * info:
     *      engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
     * detail:
     *      resourceMain: keywordName (raw keyword name,  please don't encode!!)
     *      resourceSubordinate: tagName (optional)
     *      resourceAdditional: cityId
     *      resource_searchengines: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
    public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO = 10142;
     */

    private void getQBaseTast(List<String[]> keywordIdList, int oid, String se, Integer operationType) {
    
        if (oid == 0 || CollectionUtils.isEmpty(keywordIdList)) {
                        return;
                }
    
        int detailCnt = 10000;
        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        Date rbiDate = new Date();
//        String se = "6-8-d,217-1-m";

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(oid);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setEngineLanguageDevice(se);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (String keyword[] : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(oid);
            rbd.setResourceMain(keyword[0]);
            rbd.setResourceMd5(Md5Util.Md5(String.valueOf(oid) + keyword + se + operationType + (keyword.length >=2 ? keyword[1] : "") + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
            rbd.setResourceSearchengines(se);
            if (operationType.intValue() == ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO) {
                rbd.setResourceAdditional(keyword[1]);
                        }
            resourceBatchDetailEntityList.add(rbd);

            if (resourceBatchDetailEntityList.size() >= detailCnt) {
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
                resourceBatchDetailEntityList = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }

//        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());

    }

}