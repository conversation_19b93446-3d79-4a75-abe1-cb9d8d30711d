package seoclarity.backend.onetime.conductor;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;

public class ConductorDownloader {

    private static String KEY = "s8tzgb6ys5n1k5pxd9869lcq859qi9l2xye4rm1w";
    private static String SCR = "ar3od2huwkpf4442ovhx90ydbytucuxvyv7nccqi";
//    private static String KEY = "sg5bu2yij3tra23282yhwv94xoev1a521furgrny";
//    private static String SCR = "v3x2ykvs83mlu5dbm6ow0vqul2cwvucaflvtjedw";
//    private static String ACID = "6926";
//    private static String PropertyID = "1707";
    //accountId_propertyId
    private static List<String> desktopAccountInfos = new ArrayList<String>();
//    private static List<String> mobileAccountInfos = Arrays.asList("15662_76981");//"15776_77897"
    private static int START_TIME_ID = 708;
    private static int END_TIME_ID = 814;
    private static int desktop = 1;
    private static int mobile = 2;
    private static String DOMAIN = "https://api.conductor.com/";
    private static String query = "?apiKey="+KEY+"&sig=%s";
    private static String daily_query = "?reportingDuration=DAY&apiKey="+KEY+"&sig=%s";
    private static String basePathD = "KBB_7500_desktop";
    private static String basePathM = "KBB_7500_mobile";
    
    static {
    	desktopAccountInfos.add("16088_79964");
    	desktopAccountInfos.add("19564_96403");
    	desktopAccountInfos.add("20282_100859");
    	desktopAccountInfos.add("20840_105287");
    }
    
    
    public static Map<Integer, Integer> locaitonMap = new HashMap<Integer, Integer>();
    public static Map<Integer, Integer> accIdDomainIdMap = new HashMap<Integer, Integer>();
    
    private static String RANKING_FILE_NAME_TEMPLATE = "KBB_RANK_%s_5_%s.txt";
    private static String SV_FILE_NAME_TEMPLATE = "KBB_SV_%s_5_%s.txt";
    private static String KEYWORD_FILE_NAME_TEMPLATE = "KBB_KEYWORD_%s.txt";
    
    private static final String LOCAL = "/home/<USER>/conductor";
    
    static{
    	
    	locaitonMap.put(3, 0);
    	locaitonMap.put(399, 308962);
    	locaitonMap.put(795, 310599);
    	
    	accIdDomainIdMap.put(16088, 13649);
    	accIdDomainIdMap.put(19564, 13651);
    	accIdDomainIdMap.put(20282, 13653);
    	accIdDomainIdMap.put(20840, 13648);
    	
    }
    
    
    

    /**
     * 第一步找到客户所有的keywords
     * 1) :accountId
     * 2) :webPropertyId
     */
    private static String STEP1_API_PATH = "v3/accounts/%s/web-properties/%s/tracked-searches";
    /**
     * 第二步找到客户所有的keywords的每个日期的Rank
     * 1) :accountId
     * 2) :webPropertyId
     * 3) :rankSourceId
     * 4) :timePeriodId
     */
    private static String STEP2_API_PATH = "v3/%s/web-properties/%s/rank-sources/%s/tp/%s/serp-items";
    
    //https://api.conductor.com/v3/20840/web-properties/105287/rank-sources/5/tp/CURRENT/search-volumes?apiKey=s8tzgb6ys5n1k5pxd9869lcq859qi9l2xye4rm1w&sig=d1b496a1e333a25ac4df61b211547957
    private static String STEP2_SV_API_PATH = "v3/%s/web-properties/%s/rank-sources/%s/tp/%s/search-volumes";


    public static void main(String[] args) throws IOException {

//        System.out.println(generateSignature(KEY, SCR, Math.round(System.currentTimeMillis() / 1000.0)));
//        File file = new File("/home/<USER>/"+basePathD+"/keyword_list.txt");
//        String path = DOMAIN+String.format(STEP1_API_PATH, "15663", "76977")+String.format(query, generateSignature(KEY, SCR, Math.round(System.currentTimeMillis() / 1000.0)));
//        HttpResponse request = HttpUtil.createGet(path).executeAsync();
//        FileUtil.writeString(request.body(), file, Charset.forName("utf-8"));
//
//        File file2 = new File("/home/<USER>/"+basePathM+"/keyword_list.txt");
//        String path2 = DOMAIN+String.format(STEP1_API_PATH, "15662", "76981")+String.format(query, generateSignature(KEY, SCR, Math.round(System.currentTimeMillis() / 1000.0)));
//        HttpResponse request2 = HttpUtil.createGet(path2).executeAsync();
//        FileUtil.writeString(request2.body(), file2, Charset.forName("utf-8"));



//        Set<String> trackedSearchIds = new HashSet<>();
//        Gson gson = new Gson();
//
//        //step1 finished
//        String keywordsJson = IoUtil.read(new FileInputStream(file), Charset.forName("utf-8"));
//        List<Map<String, Object>> mapList = gson.fromJson(keywordsJson, List.class);
//
//        for (Map<String, Object> map : mapList) {
//            trackedSearchIds.add(map.get("trackedSearchId").toString());
//        }
//
//        trackedSearchIds.forEach(s -> System.out.println(s));
//        while (START_TIME_ID <= END_TIME_ID) {
////            sendRequest(END_TIME_ID, desktop);
//            sendRequest(END_TIME_ID, mobile);
//            END_TIME_ID--;
//        }
        
    	//weekly
//        while (START_TIME_ID <= END_TIME_ID) {
    		//rank
////            sendRequest(START_TIME_ID, desktop);
    	
    		//sv
//        	sendSVRequest(END_TIME_ID, mobile);
//        	END_TIME_ID--;
//        }

    	System.out.println(generateSignature(KEY, SCR, Math.round(System.currentTimeMillis() / 1000.0)));
    	
    	//daily
//    	Date startDate = FormatUtils.toDate("20230305", "yyyyMMdd");
//    	Date endDate = FormatUtils.toDate("********", "yyyyMMdd");
//    	
//    	Date currentDate = startDate;
//        while (currentDate.before(endDate)) {
//            
//        	System.out.println("===processDate:" + FormatUtils.formatDate(currentDate, "yyyy-MM-dd"));
//        	
//        	sendRequestDaily(FormatUtils.formatDateToYyyyMmDd(currentDate));
//        	
//            currentDate = DateUtils.addDays(currentDate, +1);
//        }
    	
    }

    public static void sendRequest(int timeId, int rankSourceId) {

        for (String account : desktopAccountInfos) {
            String aId = account.split("_")[0];
            String pId = account.split("_")[1];
            
            File file = new File("/home/<USER>/conductor/KBB_RANK_"+aId+"_5_"+timeId+".txt");
            
            if (file != null && file.isFile()) {
            	System.out.println("SKIP acc:" + aId + ", timeId:" + timeId);
				continue;
			}
            
            String path = DOMAIN+String.format(STEP2_API_PATH, aId, pId, 5, timeId)+String.format(query, generateSignature(KEY, SCR, Math.round(System.currentTimeMillis() / 1000.0)));
            System.out.println("sending request : "+path);
            HttpResponse httpResponse = HttpUtil.createGet(path).execute();
            String body = httpResponse.body();
            if (httpResponse.getStatus() != 200) {
                System.out.println("Error : "+httpResponse.getStatus());
                System.out.println(body);
                return;
            }
            //TODO
            FileUtil.writeString(body, file, Charset.forName("utf-8"));
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }
    
    public static void sendSVRequest(int timeId, int rankSourceId) {

        for (String account : desktopAccountInfos) {
            String aId = account.split("_")[0];
            String pId = account.split("_")[1];
            
            File file = new File("/home/<USER>/conductor/KBB_SV_"+aId+"_5_"+timeId+".txt");
            
            if (file != null && file.isFile()) {
            	System.out.println("SKIP acc:" + aId + ", timeId:" + timeId);
				continue;
			}
            
            String path = DOMAIN+String.format(STEP2_SV_API_PATH, aId, pId, 5, timeId)+String.format(query, generateSignature(KEY, SCR, Math.round(System.currentTimeMillis() / 1000.0)));
            System.out.println("sending request : "+path);
            HttpResponse httpResponse = HttpUtil.createGet(path).execute();
            String body = httpResponse.body();
            if (httpResponse.getStatus() != 200) {
                System.out.println("Error : "+httpResponse.getStatus());
                System.out.println(body);
                return;
            }
            //TODO
            FileUtil.writeString(body, file, Charset.forName("utf-8"));
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }
    
    public static void sendRequestDaily(int timeId) {

        for (String account : desktopAccountInfos) {
            String aId = account.split("_")[0];
            String pId = account.split("_")[1];
            
            File file = new File("/home/<USER>/conductor/KBB_RANK_"+aId+"_5_"+timeId+".txt");
            
            if (file != null && file.isFile()) {
            	System.out.println("SKIP acc:" + aId + ", timeId:" + timeId);
				continue;
			}
            
            String path = DOMAIN+String.format(STEP2_API_PATH, aId, pId, 5, timeId)+String.format(daily_query, generateSignature(KEY, SCR, Math.round(System.currentTimeMillis() / 1000.0)));
            System.out.println("sending request : "+path);
            HttpResponse httpResponse = HttpUtil.createGet(path).execute();
            String body = httpResponse.body();
            if (httpResponse.getStatus() != 200) {
                System.out.println("Error : "+httpResponse.getStatus());
                System.out.println(body);
                return;
            }
            //TODO
            FileUtil.writeString(body, file, Charset.forName("utf-8"));
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

    }

    static String generateSignature(final String apiKey, final String sharedSecret, final long reqEpochSec) {
        final String stringToHash = apiKey + sharedSecret + reqEpochSec;
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        final byte[] digestBytes = md.digest(stringToHash.getBytes());
        // It avoids the dependence on commons.codec.binary, which could have classpath issues
        final StringBuffer sb = new StringBuffer();
        for (int i = 0; i < digestBytes.length; i++) {
            sb.append(Integer.toString((digestBytes[i] & 0xff) + 0x100, 16).substring(1));
        }
        final String md5 = sb.toString();
        return md5;
    }
}
