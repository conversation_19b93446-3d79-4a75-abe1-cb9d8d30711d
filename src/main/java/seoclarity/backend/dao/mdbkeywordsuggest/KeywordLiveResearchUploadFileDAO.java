package seoclarity.backend.dao.mdbkeywordsuggest;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.googlesuggest.KeywordLiveResearchUploadFileEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class KeywordLiveResearchUploadFileDAO extends MDBBaseJdbcSupport<KeywordLiveResearchUploadFileEntity>{

    @Override
    public String getTableName() {
        return "keyword_live_search_upload_files";
    }

    public List<KeywordLiveResearchUploadFileEntity> queryLastExecFile() {
        StringBuilder sbd = new StringBuilder();
        sbd.append("select * from " + getTableName() + " order by uploadTime desc limit 1");
        return this.findBySql(sbd.toString()) ;
    }

    public int insert(KeywordLiveResearchUploadFileEntity entity) {
        Map<String, Object> values = new HashMap<>();
        values.put("uploadTime", entity.getUploadTime());
        values.put("fileName", entity.getFileName());
        values.put("lineNo", entity.getLineNo());
        values.put("createDate", entity.getCreateDate());
        return this.insert(values);
    }

}
