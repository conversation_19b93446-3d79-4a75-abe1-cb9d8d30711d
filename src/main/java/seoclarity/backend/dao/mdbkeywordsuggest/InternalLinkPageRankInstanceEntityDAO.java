package seoclarity.backend.dao.mdbkeywordsuggest;

import java.util.List;

import org.springframework.stereotype.Repository;

import seoclarity.backend.dao.actonia.ActoniaMdbBaseJdbcSupport;
import seoclarity.backend.entity.actonia.InternalLinkPageRankInstanceEntity;

@Repository
public class InternalLinkPageRankInstanceEntityDAO extends ActoniaMdbBaseJdbcSupport<InternalLinkPageRankInstanceEntity>{

    @Override
    public String getTableName() {
        return "internal_link_page_rank_instance";
    }

    public InternalLinkPageRankInstanceEntity getByCrawlRequestId(Integer ownDomainId, Integer crawlRequestLogId) {
        StringBuilder sbd = new StringBuilder();
        sbd.append("select * from " + getTableName() + " where crawlRequestLogId = ? and ownDomainId = ? limit 1");
        return this.findObject(sbd.toString(), crawlRequestLogId, ownDomainId) ;
    }
    
    
    public List<InternalLinkPageRankInstanceEntity> getSummaryList() {
        StringBuilder sbd = new StringBuilder();
        sbd.append("select * from " + getTableName() + " where summaryStatus = ? and status = ? ");
        return this.findBySql(sbd.toString(), InternalLinkPageRankInstanceEntity.SUMMARY_STATUS_NOT_STARTED, InternalLinkPageRankInstanceEntity.STATUS_SUCCESS) ;
    }
    
    
    public void updateStatusById(Integer ownDomainId, Integer id, Integer status) {
    	StringBuilder sql = new StringBuilder();
    	sql.append(" update " + getTableName() + " set summaryStatus = ? where id = ? and ownDomainId = ? limit 1");
    	System.out.println(sql.toString());
    	System.out.println("status:" + status);
    	System.out.println("id:" + id);
    	System.out.println("ownDomainId:" + ownDomainId);
        this.executeUpdate(sql.toString(), status, id, ownDomainId);
    }

    public void insert(Integer ownDomainId, Integer crawlRequestLogId, Integer count, String companyName) {
    	
    	String sql = "insert ignore into " + getTableName() + "(ownDomainId, crawlRequestLogId, summaryDataCount, userId, enabled,  status, mutualStatus, dedicatedCompany)VALUES(?,?,?,?,?,?,?,?)";
        this.executeUpdate(sql, ownDomainId, crawlRequestLogId, count, 214, 1, 0, 0, companyName);
    }

}
