package seoclarity.backend.dao;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.ApiTaskParamsEntity;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
@Repository
public class ApiTaskParamsEntityDAO extends ActoniaBaseJdbcSupport<ApiTaskParamsEntity> {
    public String getTableName() {
        return "api_task_params";
    }
    public int insert(ApiTaskParamsEntity apiTaskParamsEntity) {
        String sql = "insert into " + getTableName() + " (taskInstanceId,accessToken,requestBody ,translatedParams,translatedJsonParams) values (?, ?, ?,?, ?) ";

        return this.executeUpdate(sql, apiTaskParamsEntity.getTaskInstanceId(), apiTaskParamsEntity.getAccessToken(), apiTaskParamsEntity.getRequestBody(),
                apiTaskParamsEntity.getTranslatedParams(), apiTaskParamsEntity.getTranslatedJsonParams());
    }

    public ApiTaskParamsEntity getApiTaskParamsByTaskName(int taskId) {
        String sql = "select * from "+getTableName()+" where taskInstanceId=?";
        return findObject(sql, taskId);
    }

    public int updateTransParam(ApiTaskParamsEntity apiTaskParamsEntity, int instantId) {
        String sql = "update " + getTableName() + " set translatedParams=?,translatedJsonParams=? where taskInstanceId=? ";
        return this.executeUpdate(sql,
                apiTaskParamsEntity.getTranslatedParams(), apiTaskParamsEntity.getTranslatedJsonParams(), instantId);
    }

}
