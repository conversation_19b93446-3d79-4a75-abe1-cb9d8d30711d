/**
 *
 */
package seoclarity.backend.dao.actoniamonitor;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class ActoniaMonitorBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="actoniaMonitorDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
