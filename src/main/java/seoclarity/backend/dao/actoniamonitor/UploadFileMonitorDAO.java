package seoclarity.backend.dao.actoniamonitor;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actoniamonitor.UploadFileMonitorEntity;

@Repository
public class UploadFileMonitorDAO  extends ActoniaMonitorBaseJdbcSupport<UploadFileMonitorEntity> {

    @Override
    public String getTableName() {
        return "upload_file_monitor";
    }

    public void insert(UploadFileMonitorEntity entity) {
        StringBuffer sql = new StringBuffer();
        sql.append(" insert into " + getTableName());
        sql.append(" (uploadType, frequency, fileName, version) ");
        sql.append(" values(?,?,?,?)");

        this.executeUpdate(sql.toString(),
                entity.getUploadType(),
                entity.getFrequency(),
                entity.getFileName(),
                entity.getVersion()
        );
    }

    public UploadFileMonitorEntity getUploadFileByUniqueKey(int uploadType, String fileName, int version, int frequency){
        String sql = " select * from " + getTableName() + " where uploadType = ? and fileName=? and version=? and frequency=? ";
        return findObject(sql, uploadType, fileName, version, frequency);
    }


    public void updateStatus(int uploadType, String fileName, int version, int uploadStatus, int frequency) {
        String sql = "update " + getTableName() + " set uploadStatus=? where uploadType = ? and fileName=? and version=? and frequency=? ";
        executeUpdate(sql, uploadStatus, uploadType, fileName, version, frequency);
    }

}
