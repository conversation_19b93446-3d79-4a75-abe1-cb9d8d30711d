package seoclarity.backend.dao.actoniamonitor;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.dao.actonia.ActoniaMdbMonitorDailyBaseJdbcSupport;
import seoclarity.backend.entity.actoniamonitor.ContentIdeaUploadMonitorEntity;

@Repository
public class ContentIdeaUploadTitleUrlMonitorDAO extends ActoniaMdbMonitorDailyBaseJdbcSupport<ContentIdeaUploadMonitorEntity>{

	@Override
	public String getTableName() {
		return "content_idea_upload_title_url_monitor";
	}
	
	public String getTableName(String version) {
		return "content_idea_upload_title_url_monitor" + (StringUtils.isBlank(version) ? "" : ("_v" + version));
	}
	
	public void insert(String hashId, String version) {
		
		StringBuffer sql = new StringBuffer();
		sql.append(" insert into ").append(getTableName(version));
		sql.append(" values (?)");

		executeUpdate(sql.toString(), hashId);
	}
	
	public int[] insertForBatch(List<String> needInsert, String version) {
        String sql = "insert ignore into " + getTableName(version) + "(hash_md5) " +
                "values(?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (String str : needInsert) {
            Object[] values = new Object[]{str};
            batch.add(values);
        }
        return executeBatch(sql, batch);
    }
	
	public int[] insertForBatch(String[] needInsert, String version) {
        String sql = "insert ignore into " + getTableName(version) + "(hash_md5) " +
                "values(?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (String str : needInsert) {
            Object[] values = new Object[]{str};
            batch.add(values);
        }
        return executeBatch(sql, batch);
    }
	
	public boolean checkExisted(String hashId, String version) {
		
		String sql = "select * from " + getTableName(version) + " where hash_md5 = ?";
		
		List<ContentIdeaUploadMonitorEntity> result = this.findBySql(sql, hashId);
		
		return result!=null && result.size() > 0;
	}
	
	public List<ContentIdeaUploadMonitorEntity> getByPage(int pageNum, int pageSize) {
		
        String sql = "select * from " + getTableName() + " limit ?, ?";
		
		List<ContentIdeaUploadMonitorEntity> result = this.findBySql(sql, pageNum, pageSize);
		
		return result;
		
	}

}
