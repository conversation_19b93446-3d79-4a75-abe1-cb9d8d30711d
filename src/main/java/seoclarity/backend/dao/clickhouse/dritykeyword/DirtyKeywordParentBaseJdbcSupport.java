package seoclarity.backend.dao.clickhouse.dritykeyword;

import org.apache.poi.ss.formula.functions.T;
import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

public class DirtyKeywordParentBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name="clarityDBDirtyKeywordParentAndChildDataSource")
    private DataSource dataSource;

    @Override
    public String getTableName() {
        return null;
    }

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }
}
