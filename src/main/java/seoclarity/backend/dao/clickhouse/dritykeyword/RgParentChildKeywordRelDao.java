package seoclarity.backend.dao.clickhouse.dritykeyword;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.dirtykeyword.RgParentChildKeywordRelEntity;
import seoclarity.backend.entity.rankcheck.KeywordMonthlyRecommend;

import java.util.ArrayList;
import java.util.List;

@Repository
public class RgParentChildKeywordRelDao extends DirtyKeywordParentBaseJdbcSupport<RgParentChildKeywordRelEntity> {

    @Override
    public String getTableName() {
        return "local_rg_parent_child_keyword_rel";
    }

    public void createTable(String tempTableName) {
        String sql = "create table " + tempTableName + " as " + getTableName() + " ";
        System.out.println("sql:" + sql);
        this.executeUpdate(sql);
    }

    public void dropTable() {
        String sql = "drop table IF EXISTS " + getTableName() + " ";
        this.executeUpdate(sql);
    }

    public void dropTable(String tempTableName) {
        String sql = "drop table IF EXISTS " + tempTableName + " ";
        this.executeUpdate(sql);
    }

    public void renameTable(String oldTableName){
        String sql = "rename table " + oldTableName + " to " + getTableName() + " ";
        System.out.println(sql);
        this.executeUpdate(sql);
    }

    public void insertBatch(List<RgParentChildKeywordRelEntity> insertList, String tempTableName) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("insert into ").append(tempTableName)
                .append(" (engineId, languageId, childKeywordName, childKeywordStream, parentKeywordName)")
                .append(" values(?,?,?,?,?) ");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (RgParentChildKeywordRelEntity recommend : insertList) {
            Object[] values = new Object[]{recommend.getEngineId(), recommend.getLanguageId(), recommend.getChildKeywordName(), recommend.getChildKeywordStream(), recommend.getParentKeywordName()};
            batch.add(values);
        }
        this.executeBatch(sqlBuilder.toString(), batch);
    }
}
