/**
 *
 */
package seoclarity.backend.dao.clickhouse.cloudflare;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class ClCloudflareJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDBCloudflareDateSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
