package seoclarity.backend.dao.clickhouse.bot;

import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.bot.UrlDetailEntity;
import seoclarity.backend.utils.FormatUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 2017/2/27.
 */
@Repository
public class UrlDetailDao extends ClBotBaseJdbcSupport<UrlDetailEntity> {

    @Override
    public String getTableName() {
        return "url_detail";
    }

    public void insertForBatch(List<UrlDetailEntity> insertData) {
        String sql = "INSERT INTO "+getTableName()
                +" (own_domain_id, last_seen, url, sign) VALUES (?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (UrlDetailEntity entity : insertData) {
            Object[] values = new Object[]{
                    entity.getOwnDomainId(),
                    FormatUtils.formatDate(entity.getLastSeen(), "yyyy-MM-dd"),
                    entity.getUrl(),
                    entity.getSign()
            };
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    /**
     *
     * @param ownDomainId
     * @return
     */
    public List<UrlDetailEntity> getAllByDomainId(int ownDomainId) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * from ");
        sql.append(getTableName());
        sql.append(" where own_domain_id = ? ");

        return findBySql(sql.toString(), ownDomainId);

    }

    /**
     *
     * @param ownDomainId
     * @param processDate the first date query
     * @return
     */
    public List<Map<String, Object>> getLastSeenSummary(int ownDomainId, Date processDate) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        sql.append(" sum(if(lseen = toDate('%1$s'), 1, 0)) AS crawled1Days, ");
        sql.append(" sum(if((toDate('%1$s') - 1) = lseen, 1, 0)) AS crawled2Days, ");
        sql.append(" sum(if((toDate('%1$s') - 2) = lseen, 1, 0)) AS crawled3Days, ");
        sql.append(" sum(if((toDate('%1$s') - 3) = lseen, 1, 0)) AS crawled4Days, ");
        sql.append(" sum(if((toDate('%1$s') - 4) = lseen, 1, 0)) AS crawled5Days, ");
        sql.append(" sum(if((toDate('%1$s') - 5) = lseen, 1, 0)) AS crawled6Days, ");
        sql.append(" sum(if((toDate('%1$s') - 6) = lseen, 1, 0)) AS crawled7Days, ");
        sql.append(" sum(if(((toDate('%1$s') - 14) <= lseen) AND ((toDate('%1$s') - 6) > lseen), 1, 0)) AS crawled15Days, ");
        sql.append(" sum(if(((toDate('%1$s') - 30) <= lseen) AND ((toDate('%1$s') - 14) > lseen), 1, 0)) AS crawled30Days, ");
        sql.append(" sum(if(((toDate('%1$s') - 60) <= lseen) AND ((toDate('%1$s') - 30) > lseen), 1, 0)) AS crawled60Days, ");
        sql.append(" sum(if((toDate('%1$s') - 60) > lseen, 1, 0)) AS crawledMoreThan60Days ");
        sql.append(" FROM ( ");
        sql.append("    SELECT ");
        sql.append("      urlhash, ");
        sql.append("      max(last_seen) AS lseen ");
        sql.append("    FROM url_detail ");
        sql.append("    WHERE own_domain_id = ? ");
        sql.append("    GROUP BY urlhash ");
        sql.append(" ) ");

        String date = DateFormatUtils.format(processDate, "yyyy-MM-dd");

        String query = String.format(sql.toString(), date);

        System.out.println(query);

        return queryForMapList(query, ownDomainId);

    }

}
