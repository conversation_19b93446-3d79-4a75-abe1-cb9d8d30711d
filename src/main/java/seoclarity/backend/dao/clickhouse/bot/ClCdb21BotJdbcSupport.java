/**
 *
 */
package seoclarity.backend.dao.clickhouse.bot;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;


public abstract class ClCdb21BotJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDBCdb21botDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	

}
