/**
 *
 */
package seoclarity.backend.dao.clickhouse.bot;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class ClBotBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDBBotUploadDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}

}
