package seoclarity.backend.dao.clickhouse.suggestkeyword;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.adhoc.AdhocSearchvolumeEntity;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.entity.googlesuggest.KeywordResearchEntity;
import seoclarity.backend.entity.googlesuggest.KeywordResearchSVEntity;

import java.util.ArrayList;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Repository
public class KeywordResearchSVClarityDBDAO extends KeywordResearchBaseJdbcSupport<KeywordResearchSVEntity> {

    @Override
    public String getTableName() {
        //return "local_keyword_research_sv_ewaintest";
        return "dis_keyword_research_sv";
    }

    public List<String> getKeywordListByProject(int projectId){
        String sql = " select keyword_name from " + getTableName() + " where project_id = ? ";
        return queryForStringList(sql, projectId);
    }

    public void insertBatch(List<KeywordResearchSVEntity> list) {
        String sql = "insert into " + getTableName()
                + " (own_domain_id,project_id,engine_id,language_id,location_id,keyword_name,source_keyword,"
                + " avg_search_volume, "
                + "monthly_search_volume1,"
                + "monthly_search_volume2,"
                + "monthly_search_volume3,"
                + "monthly_search_volume4,"
                + "monthly_search_volume5,"
                + "monthly_search_volume6,"
                + "monthly_search_volume7,"
                + "monthly_search_volume8,"
                + "monthly_search_volume9,"
                + "monthly_search_volume10,"
                + "monthly_search_volume11,"
                + "monthly_search_volume12,"
                + "cpc,category,word,stream,"
                + "keyword_variation_oneword,"
                + "keyword_variation_ngram,"
                + "sequence_no,versioning,svFromRankcheck,sign"
                + ") "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordResearchSVEntity entity : list) {
            Object[] values = new Object[] {
                    entity.getOwnDomainId(),
                    entity.getProjectId(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getLocationId(),
                    entity.getKeywordName(),
                    entity.getSourceKeyword(),
                    entity.getAvgSearchVolume(),
                    entity.getMonthlySearchVolume1(),
                    entity.getMonthlySearchVolume2(),
                    entity.getMonthlySearchVolume3(),
                    entity.getMonthlySearchVolume4(),
                    entity.getMonthlySearchVolume5(),
                    entity.getMonthlySearchVolume6(),
                    entity.getMonthlySearchVolume7(),
                    entity.getMonthlySearchVolume8(),
                    entity.getMonthlySearchVolume9(),
                    entity.getMonthlySearchVolume10(),
                    entity.getMonthlySearchVolume11(),
                    entity.getMonthlySearchVolume12(),
                    entity.getCpc(),
                    entity.getCategory(),
                    entity.getWord(),
                    entity.getStream(),
                    entity.getKeywordVariationOneword(),
                    entity.getKeywordVariationNgram(),
                    entity.getSequenceNo(),
                    entity.getVersioning(),
                    entity.getSvFromRankcheck(),
                    entity.getSign(),
            };
            batch.add(values);
        }

        int[] count = this.executeBatch(sql, batch);
    }

    public List<KeywordResearchSVEntity> getByPidEngineLanguageDomainLocationKWName(int projectId, int engineId, int languageId, int ownDomainId, int locationId, String sourceKeyword, List<String> keywordNameList) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where own_domain_id = ? and project_id = ? and engine_id = ? and language_id = ? and location_id = ? and source_keyword = ? ");
        sbd.append(" and keyword_name in ('").append(StringUtils.join(keywordNameList,"','")).append("')");
        return findBySql(sbd.toString(), ownDomainId, projectId, engineId, languageId, locationId, sourceKeyword);
    }
}
