package seoclarity.backend.dao.clickhouse;

import org.jsoup.helper.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.clickhouse
 * @author: cil
 * @date: 2021-08-13 14:49
 **/@Repository
public class RGRankingDetail202107DAO2 extends ThgCkRgBaseJdbcSupport2{


    @Override
    public String getTableName() {
        return "local_rg_ranking_detail_202107_usV2";
    }


    public List<Map<String, Object>> selectData(List<String> data){
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(
                "select keyword_name,kList from ( " +
                        "SELECT" +
                        "    keyword_name," +
                        "    groupUniqArray(kwName) as kList " +
                        "FROM" +
                        "(" +
                        "    SELECT DISTINCT" +
                        "        keyword_name," +
                        "        kwName," +
                        "        arrayIntersect(urlHashArray1, urlHashArray2) AS urlHashArray" +
                        "    FROM" +
                        "    (" +
                        "        SELECT" +
                        "            keyword_name AS kwName," +
                        "            urlHash AS urlHashArray1" +
                        "        FROM default.local_rg_ranking_detail_202107_usV4" +
                        "    )" +
                        "    ," +
                        "    (" +
                        "        SELECT" +
                        "            keyword_name," +
                        "            urlHash AS urlHashArray2" +
                        "        FROM default.local_rg_ranking_detail_202107_usV4" +
                        "        WHERE keyword_name IN ('" ).append(StringUtil.join(data,"','")).append( "')" +
                "    )" +
                ")" +
                "WHERE length(urlHashArray) >= 9 " +
                "GROUP BY keyword_name" +
                ") where length(kList) >1 " +
                "SETTINGS max_bytes_before_external_group_by = 20000000000"
        );

        return this.queryForMapList(stringBuilder.toString());

    }


}
