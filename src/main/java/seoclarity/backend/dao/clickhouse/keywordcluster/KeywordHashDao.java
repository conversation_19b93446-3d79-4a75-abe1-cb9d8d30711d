package seoclarity.backend.dao.clickhouse.keywordcluster;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.keywordcluster.KeywordClusterGroupEntity;
import seoclarity.backend.entity.clickhouse.keywordcluster.KeywordHashEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Repository
public class KeywordHashDao extends ClKeywordClusterJdbcSupport<KeywordHashEntity> {
    @Override
    public String getTableName() {
        return "dis_keyword_hash";
    }

    public void batchAdd(List<KeywordHashEntity> groupList) {
        String sql = "INSERT INTO " + getTableName() + " (ranking_date, own_domain_id, project_id, keyword_name, urlArray, sign) " +
                "VALUES (?, ?, ?, ?, ?, ?)";
        List<Object[]> batch = new ArrayList<>();
        for (KeywordHashEntity groupEntity : groupList) {
            Object[] obj = new Object[]{
                    groupEntity.getRankingDate(),
                    groupEntity.getOwnDomainId(),
                    groupEntity.getProjectId(),
                    groupEntity.getKeywordName(),
                    groupEntity.getUrlArray(),
                    groupEntity.getSign()
            };
            batch.add(obj);
        }
        autoRetryBatchInsert(sql, batch);
    }
}
