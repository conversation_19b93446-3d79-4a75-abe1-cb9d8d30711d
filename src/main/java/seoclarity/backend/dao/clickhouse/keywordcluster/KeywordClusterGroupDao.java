package seoclarity.backend.dao.clickhouse.keywordcluster;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.keywordcluster.KeywordClusterGroupEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Repository
public class KeywordClusterGroupDao extends ClKeywordClusterJdbcSupport<KeywordClusterGroupEntity> {

    @Override
    public String getTableName() {
        return "dis_adhoc_keyword_cluster_group";
    }

    public void batchAdd(List<KeywordClusterGroupEntity> groupList) {
        String sql = "INSERT INTO dis_adhoc_keyword_cluster_group (own_domain_id, project_id, engine_id, language_id, location_id, keyword_name, keyword_array) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";
        List<Object[]> batch = new ArrayList<>();
        for (KeywordClusterGroupEntity groupEntity : groupList) {
            Object[] obj = new Object[]{
                    groupEntity.getOwnDomainId(),
                    groupEntity.getProjectId(),
                    groupEntity.getEngineId(),
                    groupEntity.getLanguageId(),
                    groupEntity.getLocationId(),
                    groupEntity.getKeywordName(),
                    groupEntity.getKeywordArray()
            };
            batch.add(obj);
        }
        autoRetryBatchInsert(sql, batch);
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
