package seoclarity.backend.dao.clickhouse.keywordcluster;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.keywordcluster.KeywordClusterFinalEntity;
import seoclarity.backend.entity.clickhouse.keywordcluster.KeywordClusterStage1Entity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class KeywordClusterFinalDao extends ClKeywordClusterJdbcSupport<KeywordClusterFinalEntity> {
    @Override
    public String getTableName() {
        return "dis_keyword_clustering_final";
    }

    public void batchAdd(List<KeywordClusterFinalEntity> groupList) {
        String sql = "INSERT INTO " + getTableName() + " (ranking_date, own_domain_id, project_id,keywordArray,keywordHashArray) " +
                "VALUES (?, ?, ?, ?, ?)";
        List<Object[]> batch = new ArrayList<>();
        for (KeywordClusterFinalEntity groupEntity : groupList) {
            Object[] obj = new Object[]{
                    groupEntity.getRankingDate(),
                    groupEntity.getOwnDomainId(),
                    groupEntity.getProjectId(),
                    groupEntity.getKeywordArray(),
                    groupEntity.getKeywordHashArray()
            };
            batch.add(obj);
        }
        autoRetryBatchInsert(sql, batch);
    }
}
