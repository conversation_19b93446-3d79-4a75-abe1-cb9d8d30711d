package seoclarity.backend.dao.clickhouse.openai;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.CICentralBaseJdbcSupport;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.cityhash.CityHashUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Repository
public class PageClarityDAO extends CICentralBaseJdbcSupport<String> {

	@Override
	public String getTableName() {
		return null;
	}
	

	public List<Map<String, Object>> getTitleMetaByUrlIdList(int domainId, List<Long> urlIdList, String infoTable,
															   String startQueryDate, String endQueryDate, boolean enabledDiffSES){
		String sql = " SELECT url_Id,url, title, description AS meta, primaryTargetKeyword ";
		sql += " 	FROM ( ";
		sql += " 		SELECT url_Id,url, keywordNames AS primaryTargetKeyword, urlMurmur3Hash, customUrlMurmur3Hash, responseCode, title, description ";
		sql += " 		FROM ( ";
		sql += " 			SELECT url_Id,url, customUrlMurmur3Hash, urlMurmur3Hash, keywordNames ";
		sql += " 			FROM ( ";
		sql += " 				SELECT url_Id,url, urlMurmur3Hash, customUrlMurmur3Hash ";
		sql += " 				FROM ( ";
		sql += " 					SELECT any(urlId) as url_Id,any(url) AS url, any(urlMurmur3Hash) AS urlMurmur3Hash, customUrlMurmur3Hash ";
		sql += " 					FROM page_clarity.dis_page_clarity ";
		sql += " 					WHERE ownDomainId = " + domainId;
		sql += " 					and urlId in (" + StringUtils.join(urlIdList, ",") + " ) ";
//		if(groupTagId != null){
//			sql += " 					AND urlId GLOBAL IN ( ";
//			sql += " 						SELECT resource_id from (";
//			sql += " 							SELECT resource_id, group_tag_id, own_domain_id ";
//			sql += " 							FROM page_clarity.dis_group_tag_rel ";
//			sql += " 							WHERE own_domain_id=" + domainId + " AND group_tag_id=" + groupTagId + " AND (resource_type = 1) ";
//			sql += " 							GROUP BY resource_id, group_tag_id, own_domain_id ";
//			sql += " 							HAVING sum(sign) > 0 ";
//			sql += " 						) GROUP BY resource_id HAVING COUNT() = 1 ";
//			sql += " 					) ";
//		}
		sql += "				GROUP BY customUrlMurmur3Hash, type, status ";
		sql += " 				HAVING (sum(sign) > 0) AND ((type = 1) AND (status = 1)) AND (lower(url) NOT LIKE '%/robots.txt') ";
		sql += " 			)) ANY LEFT JOIN (";
		sql += "				SELECT customUrlMurmur3Hash, MAX(keywordName) AS keywordNames from ( ";
		sql += "					SELECT keyword_hash, custom_url_murmur3_hash AS customUrlMurmur3Hash ";
		sql += "					FROM daily_ranking.dis_keyword_page_rel ";
		sql += "					WHERE own_domain_id = " + domainId;
		sql += "					GROUP BY rel_id, own_domain_id, keyword_id, page_id, keyword_hash, custom_url_murmur3_hash HAVING SUM(sign) = 1 ";
		sql += "				) ANY LEFT JOIN ( ";
		sql += "					SELECT any(keyword_name) AS keywordName, URLHash(keywordName) AS keyword_hash ";
		sql += "					FROM " + infoTable;
		sql += "					WHERE (1 = 1) AND ((ranking_date >= '" + startQueryDate + "') AND (ranking_date <= '" + endQueryDate + "')) ";
		if(enabledDiffSES){
			sql += "					AND (dictGetUInt32('file_dic_tracked_keyword_with_ses', 'own_domain_id', (toUInt32(" + domainId + "), toUInt32(engine_id), toUInt32(language_id), toString('d'), toUInt64(URLHash(keyword_name)), toUInt32(location_id))) > 0)";
		}
		sql += "					GROUP BY keyword_rankcheck_id ";
		sql += "				) USING (keyword_hash) ";
		sql += "				GROUP BY customUrlMurmur3Hash ";
		sql += "		) USING (customUrlMurmur3Hash) ";
		sql += "	) ANY LEFT JOIN (";
		sql += "		SELECT url_murmur_hash, url_murmur_hash AS urlMurmur3Hash, response_code AS responseCode, title, description, 1 AS isCrawledFlg ";
		sql += "		FROM seo_daily_ranking.dis_target_url_html_daily ";
		sql += "		WHERE domain_id = " + domainId + " AND (daily_data_creation_date = '" + endQueryDate + "') ";
		sql += "	) USING (urlMurmur3Hash)";
		sql += " ) WHERE responseCode = '200' ";
//		sql += " ORDER BY primaryTargetKeyword DESC";
//		sql += " LIMIT 0, 10 ";
		System.out.println("=========getTitleMetaByDomainOrTag sql:" +sql);
		return queryForMapList(sql);
	}

	/**
	 *
	 * @param domainId
	 * @param keywordHashList
	 * @param detailTable
	 * @param engineId
	 * @param languageId
	 * @param rankingDate
	 * @param type:1.title;2.meta
	 * @return
	 */
	public List<Map<String, Object>> getTitleMetaByKeywordList(int domainId, List<String> keywordHashList, String detailTable,
															   int engineId, int languageId, String rankingDate, int type){
		String sql = " SELECT keyword_name,URLHash(keyword_name) as keywordHash,any(label) as label,any(meta) as meta from (";
		sql += " select keyword_name,label,meta";
		sql += " from " + detailTable;
		sql += " where own_domain_id = " + domainId + " and engine_id=" + engineId + " and language_id=" + languageId + "";
		sql += " and ranking_date='" + rankingDate + "' AND (web_rank > 0) AND (root_domain_reverse != 'com.google') ";
		sql += " and URLHash(keyword_name) in (" + StringUtils.join(keywordHashList, ",") + ")";
//		for(int i=0;i<keywordList.size();i++){
//			sql += "URLHash(keyword_name) = URLHash('" + keywordList.get(i) + "')";
//			if(i<keywordList.size() -1){
//				sql += " or ";
//			}
//		}
		if(type == 1){
			sql += " and label!= ''";
		}else if (type == 2){
			sql += " and meta!= ''";
		}
		sql += " order by keyword_name asc,web_rank asc";
		sql += " ) group by keyword_name";
		System.out.println("=========getTitleMetaByKeywordList sql:" +sql);
		return queryForMapList(sql);
	}


	public List<Map<String, Object>> getTopXRankingUrlsWithWebRank(List<String> queryKeywords, int topX, int engine, int language, String tableName) {
		StringBuilder sql = new StringBuilder();
		List<String> formatKeywordList = new ArrayList<>();
		for (String keyword: queryKeywords) {
			formatKeywordList.add(formatKeyword(keyword));
		}

		sql.append(" SELECT DISTINCT url ");
		sql.append(" FROM ");
		sql.append(" ( ");
		sql.append("     SELECT arraySlice(arrayFilter((i, x) -> (x IN (1, 9) and i not like '%.google.%'), rankings.url, rankings.type), 1, " + topX + ") AS urls ");
		sql.append("     FROM " + tableName + " ");
		sql.append("     WHERE (engine_id = " + engine + ") AND (language_id = " + language + ") AND (location_id = 0) ");
		sql.append("     AND (keyword_name IN ('" + StringUtils.join(formatKeywordList, "','") + "')) ");
		sql.append(" ) ");
		sql.append(" ARRAY JOIN urls AS url ");

		System.out.println("==========getTopXRankingUrlsWithWebRank:" + sql);
		return queryForMapList(sql.toString());
	}

	public List<Map<String, Object>> getEstTrafficTrKeyword(String monthlyDetailTable, int engineId, int languageId, String svDic, List<String> urlList){
		String sql = " SELECT keywordName,decodeURLComponent(url) AS url from (";
		sql += " select any(keyword_name) as keywordName,url from ( ";
//		String sql = " select keyword_name, decodeURLComponent(url) AS url from (";
		sql += " 	SELECT keyword_name, keyword_rankcheck_id, web_rank AS true_rank, ";
		sql += svDic + " AS avg_search_volume, ";
		sql += " 	round(cpc + 0.0001, 2) AS cpc, ";
		sql += " 	round(((caseWithExpression(web_rank, 1, 0.193, 2, 0.114, 3, 0.077, 4, 0.052, 5, 0.041, 6, 0.033, 7, 0.026, 8, 0.021, 9, 0.02, 10, 0.022, 0) * dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id))) * if(hrd = 1, 1., 0.2)) + 0.01) AS est_traffic_tr, ";
		sql += " 	round((est_traffic_tr * cpc) + 0.0001, 2) AS acquisition_cost, ";
		sql += " 	url,label AS title,type  ";
		sql += " FROM " + monthlyDetailTable;
		sql += " where engine_id=" + engineId + " and language_id=" + languageId + "";
		sql += " AND dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(" + engineId + "), toUInt64(" + languageId + "),toUInt64(keyword_rankcheck_id)))  = 0 ";
		sql += " AND (web_rank != 0) AND (location_id = 0) ";
		sql += " AND ( ";
		List<String> domainSqlList = urlList.stream().map(url -> {
			StringBuilder domainSql = new StringBuilder();
			String domainName = ClarityDBUtils.getDomain(url);
			String rootDomain = ClarityDBUtils.getRootDomain(domainName);
			domainSql.append(" ( root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "' ");
			domainSql.append(" AND domain_reverse = '" + StringUtils.reverseDelimited(domainName, '.') + "' ");
			if(url.contains("'")){
				url = url.replaceAll("'", "\\\\'");
			}
			domainSql.append(" AND URLHash(lower(decodeURLComponent(url))) = URLHash(decodeURLComponent('" + url + "'))) ");
			return domainSql.toString();
		}).collect(Collectors.toList());
		sql += StringUtils.join(domainSqlList, " OR ");
		sql +=  ") ";
		sql += " ORDER BY est_traffic_tr DESC ";
//		sql += " limit 0,10";
		sql += " )t ";
		sql += " group by url";
		sql += " ) ";
		System.out.println("=========getEstTrafficTrKeyword sql:" +sql);
		return queryForMapList(sql);
	}

	private static String formatKeyword(String keywordName) {
		String queryName = new String(keywordName).toLowerCase();
		if (StringUtils.contains(queryName, "\\")) {
			queryName = StringUtils.replace(queryName, "\\", "\\\\");
			System.out.println("Convert special charater, queryName:" + keywordName + "->" + queryName);
		} else if (StringUtils.contains(queryName, "'")) {
			queryName = StringUtils.replace(queryName, "'", "\\'");
			System.out.println("Convert special charater, queryName:" + keywordName + "->" + queryName);
		}
		return queryName;
	}

	public List<Map<String, Object>> getTopCompetitorRankingUrlTitlesForKeyword(List<String> keywordList, List<String> urlList, String tableName, int engineId, int languageId) {

		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT ");
		sql.append("     url, ");
		sql.append("     keyword_name, ");
		sql.append("     label, ");
		sql.append("     meta ");
		sql.append(" FROM " + tableName);
		sql.append(" WHERE engine_id=" + engineId + " and language_id=" + languageId + "");
		sql.append(" AND dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(" + engineId + "), toUInt64(" + languageId + "),toUInt64(keyword_rankcheck_id)))  = 0 ");
		sql.append(" AND URLHash(keyword_name) IN (" + keywordList.stream().map(CityHashUtil::getCityHash64ForString).collect(Collectors.joining(",")) + ") ");

		sql.append(" AND ( ");
		List<String> domainSqlList = urlList.stream().map(url -> {
			StringBuilder domainSql = new StringBuilder();
			String domainName = ClarityDBUtils.getDomain(url);
			String rootDomain = ClarityDBUtils.getRootDomain(domainName);
			domainSql.append(" ( root_domain_reverse = '" + StringUtils.reverseDelimited(rootDomain, '.') + "' ");
			if (StringUtils.equals(domainName, rootDomain)) {
				domainSql.append(" AND domain_reverse = '" + StringUtils.reverseDelimited(domainName, '.') + "' ");
			}
			domainSql.append(" AND urlhash = " + CityHashUtil.getUrlHashForString(StringUtils.lowerCase(url)) + ") ");
			return domainSql.toString();
		}).collect(Collectors.toList());
		sql.append(StringUtils.join(domainSqlList, " OR "));
		sql.append(" ) ");

		System.out.println("==========getTopCompetitorRankingUrlTitlesForKeyword:" + sql);
		return queryForMapList(sql.toString());
	}

	public List<Map<String, Object>> getUrlResponseCodeByDomainId(int domainId, Date date) {
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append("SELECT ")
				.append(" url_murmur_hash AS urlMurmur3Hash ")
				.append(" ,title,description as meta")
				.append(" FROM seo_daily_ranking.dis_target_url_html_daily")
				.append(" WHERE response_code = '200' ")
				.append(" AND domain_id = ? ")
				.append(" AND daily_data_creation_date = ? ");
		String sql = sqlBuilder.toString();
		System.out.println("==========getUrlResponseCodeByDomainId:" + sql);
		return queryForMapList(sql, domainId, FormatUtils.formatDate(date, FormatUtils.DATE_PATTERN_2));
	}

	public List<Map<String, Object>> getUrlResponseCodeByDomainId(int domainId, Date date, List<String> murmur3HashList) {
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append("SELECT ")
				.append(" url_murmur_hash AS urlMurmur3Hash ")
				.append(" ,title,description as meta")
				.append(" FROM seo_daily_ranking.dis_target_url_html_daily")
				.append(" WHERE response_code = '200' ")
				.append(" AND domain_id = ? ")
				.append(" AND daily_data_creation_date = ? ")
				.append(" AND url_murmur_hash in (" + StringUtils.join(murmur3HashList, ",")).append(") ");
		String sql = sqlBuilder.toString();
		System.out.println("==========getUrlResponseCodeByDomainId:" + sql);
		return queryForMapList(sql, domainId, FormatUtils.formatDate(date, FormatUtils.DATE_PATTERN_2));
	}



}
