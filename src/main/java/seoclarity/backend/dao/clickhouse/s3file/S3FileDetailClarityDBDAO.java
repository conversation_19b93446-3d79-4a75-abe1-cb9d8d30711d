package seoclarity.backend.dao.clickhouse.s3file;

import org.springframework.stereotype.Repository;
import seoclarity.backend.tools.transferrankdatafroms3tob2.onetime.LoadDailyRankingKeyFromS3ToClarityDB;

import java.util.ArrayList;
import java.util.List;

@Repository
public class S3FileDetailClarityDBDAO extends S3FileDetailBaseJdbcSupport<LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo> {

    @Override
    public String getTableName() {
        return "local_s3_file_detailV2";
    }

    @Deprecated
    public int[] insertBatch(List<LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo> s3FileDetailVos) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" insert into ").append(getTableName());
        sbd.append(" (rank_date, folder, file_name) ");
        sbd.append(" values ");
        sbd.append( "(?,?,?)");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo entity : s3FileDetailVos) {
            Object[] values = new Object[]{
                    entity.getRankDate(),
                    entity.getFolder(),
                    entity.getFileName()
            };
            batch.add(values);
        }
        return this.executeBatch(sbd.toString(), batch);
    }

    public int[] insertBatchForGlacier(List<LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo> s3FileDetailVos) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" insert into default.local_s3_file_detail_gla ");
        sbd.append(" (rank_date, folder, file_name) ");
        sbd.append(" values ");
        sbd.append( "(?,?,?)");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo entity : s3FileDetailVos) {
            Object[] values = new Object[]{
                    entity.getRankDate(),
                    entity.getFolder(),
                    entity.getFileName()
            };
            batch.add(values);
        }
        return this.executeBatch(sbd.toString(), batch);
    }

    public List<LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo> getList(int rankingDate, int pageNum, int pageSize){
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where rank_date = ?");
        sbd.append(" order by rank_date , folder, file_name ");
        sbd.append(" limit ? , ? ");
        return this.findBySql(sbd.toString(), rankingDate, (pageNum * pageSize), pageSize);
    }

    public List<LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo> getOverDateList(int rankingDate){
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where rank_date >= ?");
        sbd.append(" order by rank_date , folder, file_name ");
        return this.findBySql(sbd.toString(), rankingDate);
    }

    public List<LoadDailyRankingKeyFromS3ToClarityDB.S3FileDetailVo> getList(int maxRankDate){
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where rank_date <= ?");
        return this.findBySql(sbd.toString(), maxRankDate);
    }

    public List<String> getAllOrderRankingDateList(){
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select distinct(rank_date) from ").append(getTableName());
        sbd.append(" order by rank_date ASC");
        return this.queryForStringList(sbd.toString());
    }

}
