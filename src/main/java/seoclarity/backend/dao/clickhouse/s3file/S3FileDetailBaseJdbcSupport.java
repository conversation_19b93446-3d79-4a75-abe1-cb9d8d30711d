package seoclarity.backend.dao.clickhouse.s3file;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

public abstract class S3FileDetailBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name="clarityDBS3FileDetailDateSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }
}
