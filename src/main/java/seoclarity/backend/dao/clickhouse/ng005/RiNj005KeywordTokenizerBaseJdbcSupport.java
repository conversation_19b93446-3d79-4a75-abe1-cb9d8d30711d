package seoclarity.backend.dao.clickhouse.ng005;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.clickhouse.kp207
 * @author: cil
 * @date: 2021-06-11 18:22
 **/
public abstract class RiNj005KeywordTokenizerBaseJdbcSupport <T> extends BaseJdbcSupport<T> {

    @Resource(name="clarityDBKeywordDataSourceCdbRiNj005")
    private DataSource dataSource;

    public DataSource getDataSource() {
        return this.dataSource;
    }

    public void createManagedSearchvolumeTable(String newTableName ,String oldTableName){

        String sql = "CREATE TABLE keyword_searchvolume."+ newTableName+" as keyword_searchvolume." + oldTableName+ "  " +
                "ENGINE = CollapsingMergeTree(create_date, (engine_id, language_id, location_id, keyword_rankcheck_id, key), 8192, sign)";
        System.out.println(sql);
        this.executeUpdate(sql);

    }

    public void createDisManagedSearchvolumeTable(String oldTableName,String newTableName,String localTable){

        String sql = "CREATE TABLE keyword_searchvolume."+newTableName+" as keyword_searchvolume."+oldTableName  +
                " ENGINE = Distributed('ranking_shard', 'keyword_searchvolume', '" + localTable + "', cityHash64(keyword_rankcheck_id))";
        System.out.println(sql);
        this.executeUpdate(sql);

    }

    public void renameTable(String oldTableName,String newTableName){
        String sql = "rename table " +oldTableName + " to "+newTableName+";";
        System.out.println(sql);
        this.executeUpdate(sql);

    }

    public void dropTable(String oldTableName){
        String sql = "drop table " + oldTableName;
        System.out.println(sql);
        this.executeUpdate(sql);
    }

}
