package seoclarity.backend.dao.clickhouse;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.clickhouse.AttrGrouptagRelEntity;

@Repository
public class AttrGrouptagRelDao extends ClBaseJdbcSupport<AttrGrouptagRelEntity> {

	public String getTableName() {
		return "attr_grouptag_rel";
	}


    public void insertForBatch(List<AttrGrouptagRelEntity> insertData) {
        String sql = "INSERT INTO attr_grouptag_rel (own_domain_id,tag_id,group_id) VALUES (?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (AttrGrouptagRelEntity enitity : insertData) {
            Object[] values = new Object[]{enitity.getOwnDomainId(), enitity.getTagId(),enitity.getGroupId()};
            batch.add(values);
        }
        executeBatch(sql, batch);
    }
    
}
