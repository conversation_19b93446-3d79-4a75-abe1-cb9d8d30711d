package seoclarity.backend.dao.clickhouse.prodclarity;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.prod.DisCrawlStatsEntity;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDocEntity;

@Repository("disCrawlStatsDao")
public class DisCrawlStatsDao extends ProdClarityJdbcSupport<DisCrawlStatsEntity> {

    @Override
    public String getTableName() {
        return "dis_crawl_stats";
    }


    public boolean checkIsEndOfCrawl(String domainId, int crawlLogId) {
        String sql = "select count(*) from " + getTableName() + " where domain_id_i = ? and crawl_request_id = ? ";
        Integer cnt = this.queryForInt(sql, domainId, crawlLogId);
        return cnt > 0;
    }

}