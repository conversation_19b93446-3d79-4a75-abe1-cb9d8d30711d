package seoclarity.backend.dao.clickhouse.prodclarity;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDocEntity;

import java.util.ArrayList;
import java.util.List;

@Repository("disSiteCrawlDocDao")
public class DisSiteCrawlDocDao extends ProdClarity3JdbcSupport<DisSiteCrawlDocEntity> {

    @Override
    public String getTableName() {
        return "dis_site_crawl_doc";
    }

    public boolean checkIsExists(int domainId, int crawlLogId) {
        String sql = "select count(*) from " + getTableName() + " where domain_id = ? and crawl_request_id = ? ";
        Integer cnt = this.queryForInt(sql, domainId, crawlLogId);
        return cnt > 0;
    }

    public String getLastDataRequestTime(Integer ownDomainId, Integer crawlRequestLogId) {
        String sql = " select max(create_time) as create_time from  " + getTableName() + " where domain_id = ? and crawl_request_id = ? ";
        return queryForString(sql, ownDomainId, crawlRequestLogId);
    }

    public List<DisSiteCrawlDocEntity> getDocBaseInfo(int ownDomainId, int crawlRequestLogId, int mod, List<String> dateList) {
        StringBuilder stringBuilder = new StringBuilder();
        /*stringBuilder.append(" select t2.url_murmur_hash, linkMurmurHash, t2.url, t2.domainId, t2.crawlRequestId, t2.crawlRequestDate, t2.h1, t2.indexFlg, t2.indexable, t2.title, t2.description, t2.robots, t2.status, t2.robotsContents, t2.canonical, arrayElement(t2.page_link_destination_anchortext, indexOf(t2.page_link_destination_url_murmur_hash, linkMurmurHash)) as redirectAnchorText, arrayElement(t2.page_link_destination_url, indexOf(t2.page_link_destination_url_murmur_hash, linkMurmurHash)) as redirectFinalUrl ");
        stringBuilder.append(" from (select page_link_destination_url_murmur_hash, page_link_destination_anchortext, page_link_destination_url, url_murmur_hash, url, domain_id as domainId, crawl_request_id as crawlRequestId, crawl_request_date as crawlRequestDate, h1, index_flg as indexFlg, indexable, title, description, robots, status, robots_contents as robotsContents, canonical   ");
        stringBuilder.append(" from dis_site_crawl_doc WHERE domain_id= ? ");
        stringBuilder.append(" AND crawl_request_id= ? ");
        if (dateList != null && !dateList.isEmpty()) {
            stringBuilder.append(" and crawl_request_date in( '").append(StringUtils.join(dateList, "','")).append("') ");
        }
        stringBuilder.append(" ) t2 ANY JOIN  ( ");
        stringBuilder.append(" select url_murmur_hash, linkMurmurHash from ( select url_murmur_hash,linkMurmurHash from ( ");
        stringBuilder.append(" select url_murmur_hash, linkMurmurHash from dis_site_crawl_doc ARRAY JOIN page_link_destination_url_murmur_hash as linkMurmurHash ");
        stringBuilder.append(" WHERE domain_id= ? AND crawl_request_id= ? and crawl_request_date in( '").append(StringUtils.join(dateList, "','")).append("' ");
        stringBuilder.append(" ) ) where linkMurmurHash % 1000 = ?) in1 ");
        stringBuilder.append(" inner join (select linkMurmurHash from (select linkMurmurHash from dis_sitehealth_non200 ARRAY JOIN url_murmur_hash_arr as linkMurmurHash ");
        stringBuilder.append(" WHERE domain_id= ? AND crawl_request_id= ? AND status >= 300 and status <= 399  ");
        stringBuilder.append(" ) where linkMurmurHash % 1000 = ?) in2 using (linkMurmurHash) ) t1 using(url_murmur_hash) ");
        stringBuilder.append(" SETTINGS max_bytes_before_external_group_by=10000000000,max_bytes_before_external_sort=10000000000  ");*/
        stringBuilder.append(" select linkMurmurHash, url, crawl_request_date as crawlRequestDate, h1, index_flg as indexFlg, indexable, title, description, robots, status, robots_contents as robotsContents, canonical, destUrl as redirectFinalUrl, anchorText as redirectAnchorText, destRespCode ");
        stringBuilder.append(" FROM ( ");
        stringBuilder.append(" select redirect_final_url,url_murmur_hash, url, crawl_request_date, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical  ");
        stringBuilder.append(" from dis_site_crawl_doc WHERE domain_id=? and crawl_request_id=? and url_murmur_hash % 1000 = ? and crawl_request_date in('").append(StringUtils.join(dateList, "','")).append("') ");
        stringBuilder.append(" ) doc  ");
        stringBuilder.append(" ANY INNER JOIN ( ");
        stringBuilder.append(" select distinct sourceUrlMurmurHash, destinationUrlMurmurHash as linkMurmurHash, destUrl, anchorText, destRespCode from actonia_internal_link.ref_internal_link_sampled_view_final   ");
        stringBuilder.append(" where domainId = ? and crawlRequestLogId = ? and sourceUrlMurmurHash % 1000 = ? and destRespCode >= 300 and destRespCode <= 399 ");
        stringBuilder.append(" ) il on doc.url_murmur_hash=il.sourceUrlMurmurHash  ");
        stringBuilder.append(" SETTINGS max_bytes_before_external_group_by=10000000000,max_bytes_before_external_sort=10000000000 ");

        return findBySql(stringBuilder.toString(), ownDomainId, crawlRequestLogId, mod, ownDomainId, crawlRequestLogId, mod);
    }

    public List<DisSiteCrawlDocEntity> getRedirectInfo(int ownDomainId, int crawlRequestLogId, String murmurHashStr) {
//        String sql = "select url, url_murmur_hash, h1, index_flg, indexable, title, description, robots, status,canonical from dis_site_crawl_doc where domain_id = ? and crawl_request_id = ? and url_murmur_hash global in (" + murmurHashStr + ") ";
        String sql = "select a.url, b.sourceHash as urlMurmurHash, a.h1, a.index_flg as indexFlg, a.indexable, a.title, a.description, a.robots, a.status, a.canonical from dis_site_crawl_doc a global inner join (select url_murmur_hash as sourceHash, murmurHash3_64(redirect_final_url) as targetHash from dis_site_crawl_doc where domain_id = ? and crawl_request_id = ? and url_murmur_hash global in (" + murmurHashStr + ")) b on a.url_murmur_hash = b.targetHash where domain_id = ? and crawl_request_id = ? ";
        return findBySql(sql, ownDomainId, crawlRequestLogId, ownDomainId, crawlRequestLogId);
    }

    public List<String> getDateList(int domainId, int crawlRequestLogId) {
        String sql = "select distinct toString(crawl_request_date) from dis_site_crawl_doc where domain_id=? and crawl_request_id=?";
        return queryForStringList(sql, domainId, crawlRequestLogId);
    }

    public void insertTmpTable(int domainId, int crawlRequestId, int mod, List<String> dateList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" insert into dis_sitehealth_intermediate_3xx (domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, final_url, redirected_url_status) ");
        stringBuilder.append(" select baseInfo.domain_id, baseInfo.crawl_request_id, baseInfo.crawl_request_date, baseInfo.url, baseInfo.h1, baseInfo.index_flg, baseInfo.indexable, baseInfo.title, baseInfo.description, baseInfo.robots, baseInfo.status, baseInfo.robots_contents, baseInfo.canonical, baseInfo.redirected_url, baseInfo.redirected_anchor_text, finalInfo.redirect_final_url, baseInfo.destRespCode ");
        stringBuilder.append(" from ( ");
        stringBuilder.append(" select url_murmur_hash, redirect_final_url from dis_site_crawl_doc where domain_id= ? and crawl_request_id = ?  ");
        stringBuilder.append(" and crawl_request_date in('").append(StringUtils.join(dateList, "','")).append("') ");
        stringBuilder.append(" ) finalInfo ");
        stringBuilder.append(" INNER JOIN ( ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, destUrl as redirected_url, anchorText as redirected_anchor_text, linkMurmurHash, destRespCode ");
        stringBuilder.append(" FROM ( ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date,url_murmur_hash, url, crawl_request_date, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical  ");
        stringBuilder.append(" from dis_site_crawl_doc WHERE domain_id= ? and crawl_request_id= ? and url_murmur_hash % 1000 = ? ");
        stringBuilder.append(" and crawl_request_date in('").append(StringUtils.join(dateList, "','")).append("') ");
        stringBuilder.append(" ) doc  ");
        stringBuilder.append(" INNER JOIN ( ");
        stringBuilder.append(" select distinct sourceUrlMurmurHash, destinationUrlMurmurHash as linkMurmurHash, destUrl, anchorText, destRespCode from actonia_internal_link.ref_internal_link_sampled_view_final   ");
        stringBuilder.append(" where domainId = ? and crawlRequestLogId = ? and sourceUrlMurmurHash % 1000 = ?  ");
        stringBuilder.append(" and destRespCode >= 300 and destRespCode <= 399 ");
        stringBuilder.append(" ) il on doc.url_murmur_hash=il.sourceUrlMurmurHash ");
        stringBuilder.append(" ) baseInfo on finalInfo.url_murmur_hash = baseInfo.linkMurmurHash ");
        stringBuilder.append(" SETTINGS max_bytes_before_external_group_by=10000000000,max_bytes_before_external_sort=10000000000 ");
        this.executeUpdate(stringBuilder.toString(), domainId, crawlRequestId, domainId, crawlRequestId, mod, domainId, crawlRequestId, mod);
    }

    public void insertTmpTableNoMod(int domainId, int crawlRequestId, List<String> dateList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" insert into dis_sitehealth_intermediate_3xx (domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, final_url, redirected_url_status) ");
        stringBuilder.append(" select baseInfo.domain_id, baseInfo.crawl_request_id, baseInfo.crawl_request_date, baseInfo.url, baseInfo.h1, baseInfo.index_flg, baseInfo.indexable, baseInfo.title, baseInfo.description, baseInfo.robots, baseInfo.status, baseInfo.robots_contents, baseInfo.canonical, baseInfo.redirected_url, baseInfo.redirected_anchor_text, finalInfo.redirect_final_url, baseInfo.destRespCode ");
        stringBuilder.append(" from ( ");
        stringBuilder.append(" select url_murmur_hash, redirect_final_url from dis_site_crawl_doc where domain_id= ? and crawl_request_id = ?  ");
        stringBuilder.append(" and crawl_request_date in('").append(StringUtils.join(dateList, "','")).append("') ");
        stringBuilder.append(" ) finalInfo ");
        stringBuilder.append(" INNER JOIN ( ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, destUrl as redirected_url, anchorText as redirected_anchor_text, linkMurmurHash, destRespCode ");
        stringBuilder.append(" FROM ( ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date,url_murmur_hash, url, crawl_request_date, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical  ");
        stringBuilder.append(" from dis_site_crawl_doc WHERE domain_id= ? and crawl_request_id= ? ");
        stringBuilder.append(" and crawl_request_date in('").append(StringUtils.join(dateList, "','")).append("') ");
        stringBuilder.append(" ) doc  ");
        stringBuilder.append(" INNER JOIN ( ");
        stringBuilder.append(" select distinct sourceUrlMurmurHash, destinationUrlMurmurHash as linkMurmurHash, destUrl, anchorText, destRespCode from actonia_internal_link.ref_internal_link_sampled_view_final   ");
        stringBuilder.append(" where domainId = ? and crawlRequestLogId = ? ");
        stringBuilder.append(" and destRespCode >= 300 and destRespCode <= 399 ");
        stringBuilder.append(" ) il on doc.url_murmur_hash=il.sourceUrlMurmurHash ");
        stringBuilder.append(" ) baseInfo on finalInfo.url_murmur_hash = baseInfo.linkMurmurHash ");
        stringBuilder.append(" SETTINGS max_bytes_before_external_group_by=10000000000,max_bytes_before_external_sort=10000000000 ");
        this.executeUpdate(stringBuilder.toString(), domainId, crawlRequestId, domainId, crawlRequestId, domainId, crawlRequestId);
    }

    public void insert3xxBatch(int domainId, int crawlRequestId, int crawlIdMod,int mod, List<String> dateList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" insert into dis_sitehealth_3xx (domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, final_url, final_h1, final_index_flg, final_indexable, final_title, final_description, final_robots, final_status, final_canonical, redirected_url_status, final_robot_contents) ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, finalUrl, finalH1, finalIndexFlg, fianlIndexable, finalTitle, finalDescription, finalRobots, finalStatus, finalCanonical, redirected_url_status, finalRobotContents ");
        stringBuilder.append(" from ( ");
        stringBuilder.append(" select url as finalUrl, url_murmur_hash as finalUrlHash,  h1 as finalH1, index_flg as finalIndexFlg, indexable as fianlIndexable, title as finalTitle, description as finalDescription, robots as finalRobots, status as finalStatus, canonical as finalCanonical, robots_contents as finalRobotContents  ");
        stringBuilder.append(" from dis_site_crawl_doc ");
        stringBuilder.append(" where domain_id = ? and crawl_request_id = ?  ");
        stringBuilder.append(" and crawl_request_date in('").append(StringUtils.join(dateList, "','")).append("') ");
        stringBuilder.append(" ) finalInfo ");
        stringBuilder.append(" INNER JOIN ( ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, final_url_murmur_hash, redirected_url_status   ");
        stringBuilder.append(" from dis_sitehealth_intermediate_3xx  ");
        stringBuilder.append(" where domain_id= ? and crawl_request_id = ? and crawl_request_id_mod = ? and final_url_murmur_hash % 1000 = ? and final_url_murmur_hash_mod = ? ");
        stringBuilder.append(" ) tmpInfo on tmpInfo.final_url_murmur_hash = finalInfo.finalUrlHash ");
        stringBuilder.append(" SETTINGS max_bytes_before_external_group_by=10000000000,max_bytes_before_external_sort=10000000000 ");
        this.executeUpdate(stringBuilder.toString(), domainId, crawlRequestId, domainId, crawlRequestId, crawlIdMod, mod, mod);
    }

    public void insert3xxBatchNoMod(int domainId, int crawlRequestId, int crawlIdMod, List<String> dateList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" insert into dis_sitehealth_3xx (domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, final_url, final_h1, final_index_flg, final_indexable, final_title, final_description, final_robots, final_status, final_canonical, redirected_url_status, final_robot_contents) ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, finalUrl, finalH1, finalIndexFlg, fianlIndexable, finalTitle, finalDescription, finalRobots, finalStatus, finalCanonical, redirected_url_status, finalRobotContents ");
        stringBuilder.append(" from ( ");
        stringBuilder.append(" select url as finalUrl, url_murmur_hash as finalUrlHash,  h1 as finalH1, index_flg as finalIndexFlg, indexable as fianlIndexable, title as finalTitle, description as finalDescription, robots as finalRobots, status as finalStatus, canonical as finalCanonical, robots_contents as finalRobotContents  ");
        stringBuilder.append(" from dis_site_crawl_doc ");
        stringBuilder.append(" where domain_id = ? and crawl_request_id = ?  ");
        stringBuilder.append(" and crawl_request_date in('").append(StringUtils.join(dateList, "','")).append("') ");
        stringBuilder.append(" ) finalInfo ");
        stringBuilder.append(" INNER JOIN ( ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, final_url_murmur_hash, redirected_url_status   ");
        stringBuilder.append(" from dis_sitehealth_intermediate_3xx  ");
        stringBuilder.append(" where domain_id= ? and crawl_request_id = ? and crawl_request_id_mod = ? ");
        stringBuilder.append(" ) tmpInfo on tmpInfo.final_url_murmur_hash = finalInfo.finalUrlHash ");
        stringBuilder.append(" SETTINGS max_bytes_before_external_group_by=10000000000,max_bytes_before_external_sort=10000000000 ");
        this.executeUpdate(stringBuilder.toString(), domainId, crawlRequestId, domainId, crawlRequestId, crawlIdMod);
    }

    public void insert3xxBatchBigData(int domainId, int crawlRequestId, int crawlIdMod, int mod, int subMod, List<String> dateList) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" insert into dis_sitehealth_3xx (domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, final_url, final_h1, final_index_flg, final_indexable, final_title, final_description, final_robots, final_status, final_canonical, redirected_url_status, final_robot_contents) ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, finalUrl, finalH1, finalIndexFlg, fianlIndexable, finalTitle, finalDescription, finalRobots, finalStatus, finalCanonical, redirected_url_status, finalRobotContents ");
        stringBuilder.append(" from ( ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, finalUrl, finalH1, finalIndexFlg, fianlIndexable, finalTitle, finalDescription, finalRobots, finalStatus, finalCanonical, redirected_url_status, final_url_murmur_hash, finalRobotContents ");
        stringBuilder.append(" from ( ");
        stringBuilder.append(" select url as finalUrl, url_murmur_hash as finalUrlHash,  h1 as finalH1, index_flg as finalIndexFlg, indexable as fianlIndexable, title as finalTitle, description as finalDescription, robots as finalRobots, status as finalStatus, canonical as finalCanonical, robots_contents as finalRobotContents  ");
        stringBuilder.append(" from dis_site_crawl_doc  ");
        stringBuilder.append(" where domain_id= ? and crawl_request_id= ?  ");
        stringBuilder.append(" and crawl_request_date in('").append(StringUtils.join(dateList, "','")).append("') ");
        stringBuilder.append(" ) finalInfo ");
        stringBuilder.append(" INNER JOIN ( ");
        stringBuilder.append(" select domain_id, crawl_request_id, crawl_request_date, url, h1, index_flg, indexable, title, description, robots, status, robots_contents, canonical, redirected_url, redirected_anchor_text, final_url_murmur_hash, redirected_url_status ");
        stringBuilder.append(" from dis_sitehealth_intermediate_3xx  ");
        stringBuilder.append(" where domain_id= ? and crawl_request_id= ? and crawl_request_id_mod = ? and final_url_murmur_hash % 1000 = ? and final_url_murmur_hash_mod = ? and canonical_hash % 100 = ? and canonical_hash_mod = ? ");
        stringBuilder.append(" ) tmpInfo on tmpInfo.final_url_murmur_hash = finalInfo.finalUrlHash ");
        stringBuilder.append(" ) outerTable ");
        stringBuilder.append(" where (outerTable.final_url_murmur_hash, murmurHash3_64(outerTable.url), redirected_anchor_text)  global not in ( ");
        stringBuilder.append(" select final_url_murmur_hash, url_murmur_hash, redirected_anchor_text  ");
        stringBuilder.append(" from dis_sitehealth_3xx  ");
        stringBuilder.append(" where domain_id= ? and crawl_request_id= ? and final_url_murmur_hash % 1000 = ? ");
        stringBuilder.append(" ) ");
        stringBuilder.append(" SETTINGS max_bytes_before_external_group_by=10000000000,max_bytes_before_external_sort=10000000000 ");
        this.executeUpdate(stringBuilder.toString(), domainId, crawlRequestId, domainId, crawlRequestId, crawlIdMod, mod, mod, subMod, subMod, domainId, crawlRequestId, mod);
    }

    public int getCrawlDataCount(int ownDomainId, int crawlRequestId) {
        String sql = " select count(*) from dis_site_crawl_doc where domain_id= ? and crawl_request_id= ? ";
        return this.queryForInt(sql, ownDomainId, crawlRequestId);
    }

    public int getInternalLinkCount(int ownDomainId, int crawlRequestId) {
        String sql = "select count() from actonia_internal_link.ref_internal_link_sampled_view_final where domainId = ? and crawlRequestLogId = ? and destRespCode >= 300 and destRespCode <= 399";
        return this.queryForInt(sql, ownDomainId, crawlRequestId);
    }

    public List<String> isExistsInDb(int ownDomainId, int crawlRequestId, List<String> urls) {
        List<Object> args = new ArrayList<>();
        args.add(ownDomainId);
        args.add(crawlRequestId);

        StringBuilder sql = new StringBuilder();
        sql.append(" select url from dis_site_crawl_doc where domain_id= ? and crawl_request_id= ? and url_murmur_hash in ( ");
        for (String url : urls) {
            sql.append("murmurHash3_64(?),");
            args.add(url);
        }
        sql.setLength(sql.length() - 1);
        sql.append(")");

        return this.queryForStringList(sql.toString(), args.toArray());
    }

    public int isExistsInDb(int ownDomainId, int crawlRequestId, String url) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select count() from dis_site_crawl_doc where domain_id= ? and crawl_request_id= ? and url_murmur_hash = murmurHash3_64(?) ");

        return this.queryForInt(sql.toString(), ownDomainId, crawlRequestId, url);
    }
}