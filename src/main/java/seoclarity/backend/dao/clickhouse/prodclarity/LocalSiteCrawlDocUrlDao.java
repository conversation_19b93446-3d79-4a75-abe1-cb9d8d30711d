package seoclarity.backend.dao.clickhouse.prodclarity;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.prod.LocalSiteCrawlDocUrlEntity;

import java.util.ArrayList;
import java.util.List;
@Repository
public class LocalSiteCrawlDocUrlDao extends ProdClarityJdbcSupport<LocalSiteCrawlDocUrlEntity> {

    @Override
    public String getTableName() {
        return null;
    }

    public void batchInsert(List<LocalSiteCrawlDocUrlEntity> localSiteCrawlDocUrlEntityList) {
        String sql = "insert into local_site_crawl_doc_url(domain_id ,crawl_request_id,crawl_request_date,url,url_hash,url_murmur_hash,";
        sql += " page_link_destination_url,page_link_destination_url_hash ,page_link_destination_url_murmur_hash)";
        sql += " values (?,?,?,?,?,?,?,?,?) ";
        /*
         domain_id ,    crawl_request_id , crawl_request_date ,url , url_hash ,url_murmur_hash ,page_link_destination_url,page_link_destination_url_hash ,page_link_destination_url_murmur_hash
        *
        * */
        List<Object[]> batch = new ArrayList<>();
        for (LocalSiteCrawlDocUrlEntity entity : localSiteCrawlDocUrlEntityList) {
            Object[] values = new Object[]{
                    entity.getDomainId(), entity.getCrawlRequestId(), entity.getCrawlRequestDate(), entity.getUrl(),
                    entity.getUrlHash(), entity.getUrlMurmurHash(), entity.getPageLinkDestinationUrl(), entity.getPageLinkDestinationUrlHash(),
                    entity.getPageLinkDestinationUrlMurmurHash()
            };
            batch.add(values);
        }
        this.executeBatch(sql, batch);
    }

}
