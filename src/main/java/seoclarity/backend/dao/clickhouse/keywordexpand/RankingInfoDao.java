package seoclarity.backend.dao.clickhouse.keywordexpand;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.dritykeyword.DirtyKeywordParentBaseJdbcSupport;
import seoclarity.backend.entity.clickhouse.keywordexpand.RankingInfoEntity;

import java.util.List;

@Repository("rankingInfoDao")
public class RankingInfoDao extends DirtyKeywordParentBaseJdbcSupport<RankingInfoEntity> {

    @Override
    public String getTableName() {
        return "";
    }

    public String getTableName(int dateMonthInt, int engineId, int languageId, String device) {
        String tableName = "";
        tableName = device + "_ranking_info_" + dateMonthInt;
        if (engineId == 1 && languageId == 1) {
            tableName += "_us";
        } else {
            tableName += "_intl";
        }
        return tableName;
    }

    public int getKwCntByIds(int expansionId, int dateMonthInt, int engineId, int languageId, String device, List<Integer> kwIdList) {
        String tableName = getTableName(dateMonthInt, engineId, languageId, device);
        if (StringUtils.isEmpty(tableName)) {
            System.out.println("====tableNameEmpty dateMonthInt:" + dateMonthInt + " engineId:" + engineId + " languageId:" + languageId + " device:" + device + " kwIdList:" + kwIdList);
            return 0;
        }
        String sql = "select count() from " + tableName + " where engine_id = ? and language_id = ? " + " and keyword_id in (" + StringUtils.join(kwIdList, ",") + ") ";
        return this.queryForInt(sql, engineId, languageId);
    }



}
