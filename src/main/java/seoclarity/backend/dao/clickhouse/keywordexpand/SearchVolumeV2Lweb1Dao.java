package seoclarity.backend.dao.clickhouse.keywordexpand;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.keywordexpand.SearchVolumeV2Entity;
import seoclarity.backend.entity.clickhouse.monthlyranking.KeywordTokenizerSearchvolumeEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository("searchVolumeV2Lweb1Dao")
public class SearchVolumeV2Lweb1Dao extends ClKeywordSv1JdbcSupport<SearchVolumeV2Entity> {

    @Override
    public String getTableName() {
        return "dis_searchvolume_v2";
    }

    public List<Integer> checkKeywordExists(int engine, int language, List<Integer> keywordIdList) {
        String sql = "select keyword_rankcheck_id from " + getTableName() + " where location_id = 0 and engine_id = ? and language_id = ? and keyword_rankcheck_id in (" + StringUtils.join(keywordIdList, ',') + ")";
        return this.queryForIntegerList(sql, engine, language);
    }

    public int getLastRefreshMonth() {
        String sql = "select lastRefreshMonth,count() cnt from dis_searchvolume_v2 group by lastRefreshMonth order by cnt desc";
        List list = this.queryForMapList(sql);
        int lastRefreshMonth = 0;
        if (list != null && !list.isEmpty()) {
            Map obj = (Map) list.get(0);
            lastRefreshMonth = Integer.parseInt(obj.get("lastRefreshMonth").toString());
        }
        return lastRefreshMonth;
    }

    public void insertBatch(List<SearchVolumeV2Entity> list, int lastRefreshMonth) {
        String sql = "insert into " + getTableName()
                + " (keyword_rankcheck_id, keyword_name, word, stream, location_id, engine_id, language_id, sign, versioning, "
                + " avg_search_volume, "
                + "monthly_search_volume1,"
                + "monthly_search_volume2,"
                + "monthly_search_volume3,"
                + "monthly_search_volume4,"
                + "monthly_search_volume5,"
                + "monthly_search_volume6,"
                + "monthly_search_volume7,"
                + "monthly_search_volume8,"
                + "monthly_search_volume9,"
                + "monthly_search_volume10,"
                + "monthly_search_volume11,"
                + "monthly_search_volume12,"
                + "cpc,"
                + "keyword_variation_oneword,"
                + "keyword_variation_ngram,"
                + "monthlySvAttr.key,"
                + "monthlySvAttr.value,"
                + "category,"
                + "lastRefreshMonth,"
                + "hasSv,"
                + "true_demand,"
                + "trueDemandAttr.key,"
                + "trueDemandAttr.value"
                + ") "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (SearchVolumeV2Entity entity : list) {
            Object[] values = new Object[] {
                    entity.getKeywordRankcheckId(),
                    entity.getKeywordName(),
                    entity.getWord(),
                    entity.getStream(),
                    entity.getLocationId(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getSign(),
                    entity.getVersioning(),
                    entity.getAvgSearchVolume(),
                    entity.getMonthlySearchVolume1(),
                    entity.getMonthlySearchVolume2(),
                    entity.getMonthlySearchVolume3(),
                    entity.getMonthlySearchVolume4(),
                    entity.getMonthlySearchVolume5(),
                    entity.getMonthlySearchVolume6(),
                    entity.getMonthlySearchVolume7(),
                    entity.getMonthlySearchVolume8(),
                    entity.getMonthlySearchVolume9(),
                    entity.getMonthlySearchVolume10(),
                    entity.getMonthlySearchVolume11(),
                    entity.getMonthlySearchVolume12(),
                    entity.getCpc(),
                    entity.getKeywordVariationOneword(),
                    entity.getKeywordVariationNgram(),
                    entity.getMonthlySvAttrKeys(),
                    entity.getMonthlySvAttrValues(),
                    entity.getCategory(),
                    lastRefreshMonth,
                    entity.getHasSv() ? 1 : 0,
                    entity.getTrueDemand(),
                    entity.getTrueDemandAttrKeys(),
                    entity.getTrueDemandAttrValues()
            };
            batch.add(values);
        }

        int[] count = this.executeBatch(sql, batch);
    }
}
