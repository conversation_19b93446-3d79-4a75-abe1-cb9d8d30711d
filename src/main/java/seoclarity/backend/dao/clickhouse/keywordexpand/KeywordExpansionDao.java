package seoclarity.backend.dao.clickhouse.keywordexpand;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.keywordexpand.RelatedKeywordExpansionEntity;
import seoclarity.backend.keywordexpand.utils.ExportDataUtil;
import seoclarity.backend.utils.DateUtils;
import seoclarity.backend.utils.FormatUtils;

import java.util.Date;
import java.util.List;

@Repository
public class KeywordExpansionDao extends ClKeywordExpandJdbcSupport<RelatedKeywordExpansionEntity> {

    private static final String DATABASE = "monthly_ranking";
    private static final String DATABASE_URL = "http://23.105.174.58:8123";
    private static final String USER = "default";
    private static final String PSW = "clarity99!";

    @Override
    public String getTableName() {
        return "dis_related_keyword_expansion";
    }

    public int getDataCountByRandDate(Date rankDate, int engineId, int languageId) {
        String sql = "select count(*) from " + DATABASE + "." + getTableName() + " where ranking_date = ? and engine_id = ? and language_id = ?";
        return queryForInt(sql, FormatUtils.formatDate(rankDate, FormatUtils.DATE_PATTERN_2), engineId, languageId);
    }

    public void insertByExpandStage(Date date, int hashModn, int countryType, int engineId, int languageId) {
        String dateStr = FormatUtils.formatDate(date, "yyyy-MM-dd");
        this.executeUpdate(getInsertByExpandStageExecuteSql(countryType), dateStr, hashModn, engineId, languageId, hashModn, dateStr, hashModn, engineId, languageId);
    }

    private String getInsertByExpandStageExecuteSql(int countryType) {
        StringBuffer query = new StringBuffer();
        query.append("INSERT INTO " + DATABASE + ".").append(getTableName()).append(" (ranking_date, engine_id, language_id, location_id, stream_hash, keyword_name) ");
        query.append("SELECT ranking_date, engine_id, language_id, location_id, stream_hash, keyword_name ");
        query.append("FROM ( ");
        query.append("    SELECT * ");
        query.append("    FROM monthly_ranking.dis_related_keyword_expand_stage2 ");
        query.append("    WHERE ranking_date = ? AND keyword_hash % 100 = ? and engine_id = ? and language_id = ? ");
        query.append(") t1 ");
        query.append("LEFT JOIN ( ");
        query.append("    SELECT URLHash(LOWER(keyword_name)) AS keyword_hash ");
        if (countryType == 0) {
            query.append("    FROM monthly_ranking.d_ranking_info_us ");
        } else {
            query.append("    FROM monthly_ranking.d_ranking_info_intl ");
        }
        query.append("    WHERE keyword_hash % 100 = ? AND URLHash(LOWER(keyword_name)) GLOBAL IN ( ");
        query.append("        SELECT DISTINCT keyword_hash ");
        query.append("        FROM monthly_ranking.dis_related_keyword_expand_stage2 ");
        query.append("        WHERE ranking_date = ? AND keyword_hash % 100 = ? and engine_id = ? and language_id = ? ");
        query.append("    ) ");
        query.append(") t2 ON t1.keyword_hash = t2.keyword_hash ");
        query.append("WHERE t2.keyword_hash = 0");
        return query.toString();
    }

    public Long getExpansionListToFile(Date date, String fileName, int engineId, int languageId) throws Exception {
        String rankingDate = FormatUtils.formatDate(DateUtils.getLastSundayOfPreviousMonth(date), FormatUtils.DATE_PATTERN_2);
        System.out.println("====execGetExpansionListByDateParam:" + rankingDate);
        String sql = "SELECT CONCAT( toString(ranking_date), '!_!', toString(engine_id), '!_!', toString(language_id), '!_!', toString(location_id), '!_!', keyword_name ) AS keyword from " + DATABASE + "." + getTableName() + " where ranking_date = '" + rankingDate + "' and engine_id = " + engineId + " and language_id = " + languageId;
        System.out.println("====execGetExpansionListByDateSql:" + sql);
        return ExportDataUtil.httpExportFromClarityDB(DATABASE_URL, DATABASE, USER, PSW, sql, fileName, false, false, new String[]{""});
    }

}
