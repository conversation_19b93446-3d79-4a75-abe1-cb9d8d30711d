package seoclarity.backend.dao.clickhouse.ved;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.ved.VedTypeInfoEntity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class VedTypeInfoDAO extends PixelVedBaseJdbcSupport<VedTypeInfoEntity> {

    @Override
    public String getTableName() {
        return "local_vs3_ved_type_info";
    }


    public void insertBatch(List<VedTypeInfoEntity> vedTypeInfoEntityList) {

        String sql = "INSERT INTO " + getTableName();
        sql += " (version, device, image_file_name,engine_id,language_id,type_main_key,type)" +
                "VALUES (?,?,?,?,?,?,?)";

        List<Object[]> batch = new ArrayList<>();
        for (VedTypeInfoEntity entity : vedTypeInfoEntityList) {
            Object[] values = new Object[]{
                    entity.getVersion(),
                    entity.getDevice(),
                    entity.getImageFileName(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getTypeMainKey(),
                    entity.getType()
            };

            batch.add(values);
        }
        System.out.println("===insert batch size:" + batch.size());
        executeBatch(sql, batch);
    }


    public List<VedTypeInfoEntity> getTableListByVersion() {
        String sql = " select arrayStringConcat(type_main_key, '-') as vedType ,any(image_file_name) as imageFileName " +
                "from "+getTableName() + " where device = 'mobile' and version = '20230201'  and create_date = '2023-02-05' group by vedType ";
        return this.findBySql(sql);
    }
}
