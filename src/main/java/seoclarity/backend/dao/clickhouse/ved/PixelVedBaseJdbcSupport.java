package seoclarity.backend.dao.clickhouse.ved;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;


public abstract class PixelVedBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name="pixelVedClarityDateSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }

}
