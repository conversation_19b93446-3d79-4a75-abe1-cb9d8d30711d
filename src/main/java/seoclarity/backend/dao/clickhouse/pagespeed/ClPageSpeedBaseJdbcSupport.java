package seoclarity.backend.dao.clickhouse.pagespeed;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @create 2018-03-07 9:35
 **/
public abstract class ClPageSpeedBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name="clarityDBPageSpeedDataSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }

}
