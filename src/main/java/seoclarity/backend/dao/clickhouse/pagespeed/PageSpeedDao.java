package seoclarity.backend.dao.clickhouse.pagespeed;

import org.springframework.stereotype.Repository;
import scala.Int;
import seoclarity.backend.entity.clickhouse.pagespeed.PageSpeedEntity;
import seoclarity.backend.utils.FormatUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-03-07 10:01
 **/
@Repository
public class PageSpeedDao extends ClPageSpeedBaseJdbcSupport<PageSpeedEntity> {

    @Override
    public String getTableName() {
        return "page_speed_dis_v4";
//        return "local_page_speedtest0613";
    }

    public String getOneTimeTableName() {
        return "testlocal_page_speed_v4";
    }

    public String getWeeklyTableName() {
        return "page_speed_dis_v4_weekly";
    }

    public void insertBatch(List<PageSpeedEntity> speedEntityList, boolean isSuccess, boolean isTest) {

        String countSql = "select count() from ";
        if (isTest) {
            countSql += getOneTimeTableName();
        } else {
            countSql += getTableName();
        }
        Integer startCount = queryForInteger(countSql);
        System.out.println("===startCount:" + startCount);

        String today = FormatUtils.formatDate(new Date(), "yyyy-MM-dd");

        String sql = "INSERT INTO ";
        if (isTest) {
            sql += getOneTimeTableName();
        } else {
            sql += getTableName();
        }

        List<Object[]> batch = new ArrayList<>();
        if (isSuccess) {
            sql += " (crawl_date,crawl_month, own_domain_id, device, o_url, r_url, redfirect_flg, response_code, score" +
                    ", first_contentful_paint_ms, first_contentful_paint_ms_fast, first_contentful_paint_ms_moderate,  first_contentful_paint_ms_low" +
                    ", first_input_delay_ms, first_input_delay_ms_fast, first_input_delay_ms_moderate,  first_input_delay_ms_low" +
                    ", origin_first_contentful_paint_ms, origin_first_contentful_paint_ms_fast, origin_first_contentful_paint_ms_moderate,  origin_first_contentful_paint_ms_low" +
                    ", origin_first_input_delay_ms, origin_first_input_delay_ms_fast, origin_first_input_delay_ms_moderate,  origin_first_input_delay_ms_low" +
                    ", first_contentful_paint_description, first_contentful_paint_score, first_contentful_paint_display_value,  first_contentful_paint_numeric_value_ms" +
                    ", speed_index_description, speed_index_score, speed_index_display_value,  speed_index_numeric_value_ms" +
                    ", interactive_description, interactive_score, interactive_display_value,  interactive_numeric_value_ms" +
                    ", first_meaningful_paint_description, first_meaningful_paint_score, first_meaningful_paint_display_value,  first_meaningful_paint_numeric_value_ms" +
                    ", first_cpu_idle_description, first_cpu_idle_score, first_cpu_idle_display_value,  first_cpu_idle_numeric_value_ms" +
                    ", max_potential_fid_description, max_potential_fid_score, max_potential_fid_display_value,  max_potential_fid_numeric_value_ms" +
                    ", estimated_input_latency_json, total_blocking_time_json, render_blocking_resources_json,  uses_responsive_images_json" +
                    ", offscreen_images_json, unminified_css_json, unminified_javascript_json,  unused_css_rules_json" +
                    ", uses_optimized_images_json, efficient_animated_content_json, uses_webp_images_json,  uses_text_compression_json" +
                    ", uses_rel_preconnect_json, time_to_first_byte_json, redirects_json,  uses_rel_preload_json" +
                    ", attrstr.key, attrstr.value" +
                    ",version" +
                    ")" +
                    "VALUES (" +
                    " ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? " +
                    ")";
            for (PageSpeedEntity entity : speedEntityList) {
                Object[] values = new Object[]{
                        entity.getSendDate() == null ? today : FormatUtils.formatDate(entity.getSendDate(), FormatUtils.DATE_PATTERN_2),
                        entity.getCrawlMonth(),
                        entity.getOwnDomainId(),
//                    entity.getUrlId(),
                        entity.getDeviceIntType(),
                        entity.getoUrl(),
                        entity.getrUrl(),
                        entity.getRedfirectFlg(),
                        entity.getResponseCode(),
                        entity.getScore(),
                        entity.getFirstContentfulPaintMs(), entity.getFirstContentfulPaintMsFast(), entity.getFirstContentfulPaintMsModerate(), entity.getFirstContentfulPaintMsLow(),
                        entity.getFirstInputDelayMs(), entity.getFirstInputDelayMsFast(), entity.getFirstInputDelayMsModerate(), entity.getFirstInputDelayMsLow(),
                        entity.getOriginFirstContentfulPaintMs(), entity.getOriginFirstContentfulPaintMsFast(), entity.getOriginFirstContentfulPaintMsModerate(), entity.getOriginFirstContentfulPaintMsLow(),
                        entity.getOriginFirstInputDelayMs(), entity.getOriginFirstInputDelayMsFast(), entity.getOriginFirstInputDelayMsModerate(), entity.getOriginFirstInputDelayMsLow(),
                        entity.getFirstContentfulPaintDescription(), entity.getFirstContentfulPaintScore(), entity.getFirstContentfulPaintDisplayValue(), entity.getFirstContentfulPaintNumericValueMs(),
                        entity.getSpeedIndexDescription(), entity.getSpeedIndexScore(), entity.getSpeedIndexDisplayValue(), entity.getSpeedIndexNumericValueMs(),
                        entity.getInteractiveDescription(), entity.getInteractiveScore(), entity.getInteractiveDisplayValue(), entity.getInteractiveNumericValueMs(),
                        entity.getFirstMeaningfulPaintDescription(), entity.getFirstMeaningfulPaintScore(), entity.getFirstMeaningfulPaintDisplayValue(), entity.getFirstMeaningfulPaintNumericValueMs(),
                        entity.getFirstCpuIdleDescription(), entity.getFirstCpuIdleScore(), entity.getFirstCpuIdleDisplayValue(), entity.getFirstCpuIdleNumericValueMs(),
                        entity.getMaxPotentialFidDescription(), entity.getMaxPotentialFidScore(), entity.getMaxPotentialFidDisplayValue(), entity.getMaxPotentialFidNumericValueMs(),
                        entity.getEstimatedInputLatencyJson(), entity.getTotalBlockingTimeJson(), entity.getRenderBlockingResourcesJson(),
                        entity.getUsesResponsiveImagesJson(), entity.getOffscreenImagesJson(), entity.getUnminifiedCssJson(),
                        entity.getUnminifiedJavascriptJson(), entity.getUnusedCssRulesJson(), entity.getUsesOptimizedImagesJson(),
                        entity.getEfficientAnimatedContentJson(), entity.getUsesWebpImagesJson(), entity.getUsesTextCompressionJson(),
                        entity.getUsesRelPreconnectJson(), entity.getTimeToFirstByteJson(), entity.getRedirectsJson(),
                        entity.getUsesRelPreloadJson(),
                        entity.getRuleResultskey(),
                        entity.getRuleResultsvalue(),
//                        entity.getRawJson(),
                        entity.getVersion()
                };
//            if(entity.getPrioritizeVisibleContent() == null){
//                System.out.println(" ###################### " + JSON.toJSONString(entity));
//            }
//            System.out.println("===getPrioritizeVisibleContent:" + entity.getPrioritizeVisibleContent());
//            System.out.println("===getEnableGzipCompression:" + entity.getEnableGzipCompression());
//            System.out.println("===getLeverageBrowserCaching:" + entity.getLeverageBrowserCaching());
//            System.out.println("===getMainResourceServerResponseTime:" + entity.getMainResourceServerResponseTime());
//            System.out.println("===getMinifyCss:" + entity.getMinifyCss());
//            System.out.println("===getMinifyHtml:" + entity.getMinifyHtml());
//            System.out.println(new Gson().toJson(values));

                batch.add(values);
            }
        } else {
            sql += " (crawl_date,crawl_month, own_domain_id, device, o_url, r_url, redfirect_flg, response_code" +
                    " ,version )" +
                    " VALUES ( ?,?,?,?,?,?,?,?,?)";
            for (PageSpeedEntity entity : speedEntityList) {
                Object[] values = new Object[]{
                        entity.getSendDate() == null ? today : FormatUtils.formatDate(entity.getSendDate(), FormatUtils.DATE_PATTERN_2),
                        entity.getCrawlMonth(),
                        entity.getOwnDomainId(),
//                    entity.getUrlId(),
                        entity.getDeviceIntType(),
                        entity.getoUrl(),
                        entity.getrUrl(),
                        entity.getRedfirectFlg(),
                        entity.getResponseCode(),
//                        entity.getRawJson(),
                        entity.getVersion()
                };
                batch.add(values);
            }
        }


        System.out.println("===insert batch size:" + batch.size());
        Long startTime = System.currentTimeMillis();
        executeBatch(sql, batch);
        System.out.println("===耗时:" + (System.currentTimeMillis() - startTime) / 1000 + " s");
        String endCountSql = "select count() from ";
        if (isTest) {
            endCountSql += getOneTimeTableName();
        } else {
            endCountSql += getTableName();
        }
        Integer endCount = queryForInteger(endCountSql);
        System.out.println("===endCount:" + endCount);
    }

    public Integer getRows() {
        String sql = "select count() from " + getTableName() + "";
        return queryForInt(sql);
    }

    public List<Integer> getDomainList(int crawlMonth, int device) {
        String sql = " select own_domain_id from " + getTableName();
        sql += " where crawl_month = ? and device = ? ";
        sql += " group by own_domain_id order by own_domain_id ";
        return queryForIntegerList(sql, crawlMonth, device);
    }

    public List<String> getDuplicateUrl(int domainId, int crawlMonth, int device) {
        String sql = " select o_url ";
        sql += " from " + getTableName();
        sql += " where own_domain_id = ? and crawl_month = ? and device = ? ";
        sql += " group by crawl_month,o_url,device having count()> 1 ";
        return queryForStringList(sql, domainId, crawlMonth, device);
    }

    public PageSpeedEntity getByUrl(int domainId, int crawlMonth, int device, String url) {
        String sql = " select *, attrstr.key as ruleResultskey , attrstr.value as ruleResultsvalue from " + getTableName();
        sql += " where own_domain_id = ? and crawl_month = ? and device = ? ";
        sql += " and o_url = ? ";
        sql += " limit 1";
        return findObject(sql, domainId, crawlMonth, device, url);
    }

    public List<PageSpeedEntity> getByDomain(int domainId, int crawlMonth, int device, Long startId, int pageSize) {
        String sql = " select *, attrstr.key as ruleResultskey , attrstr.value as ruleResultsvalue from " + getTableName();
        sql += " where own_domain_id = ? and crawl_month = ? and device = ? ";
        sql += " and url_id > ? ";
        sql += " order by url_id limit ? ";
        System.out.println("===SQL getByDomain: " + sql + ",domainId: " + domainId + ",crawlMonth:" + crawlMonth + ",device: " + device + ",startId:" + startId + ",pageSize: " + pageSize);
        return findBySql(sql, domainId, crawlMonth, device, startId, pageSize);
    }


    public void insertBatchV3(List<PageSpeedEntity> speedEntityList, boolean isSuccess, boolean isTest, boolean isWeekly) {

//        String countSql = "select count() from ";
//        if (isTest) {
//            countSql += getOneTimeTableName();
//        } else {
//            countSql += getTableName();
//        }
//        Integer startCount = queryForInteger(countSql);
//        System.out.println("===startCount:" + startCount);

        String today = FormatUtils.formatDate(new Date(), "yyyy-MM-dd");

        String sql = "INSERT INTO ";
        if (isTest) {
            sql += getOneTimeTableName();
        } else if (isWeekly) {
            sql += getWeeklyTableName();
        } else {
            sql += getTableName();
        }

        List<Object[]> batch = new ArrayList<>();
        if (isSuccess) {
            sql += " (crawl_date,crawl_month, own_domain_id, device, o_url, r_url, redfirect_flg, response_code, score" +
                    ", speed_index_score,estimated_input_latency_score,mainthread_work_breakdown_score" +
                    ",uses_optimized_images_score,interactive_score,uses_rel_preconnect_score,uses_rel_preload_score" +
                    ",max_potential_fid_score,offscreen_images_score,uses_long_cache_ttl_score,first_cpu_idle_score" +
                    ",uses_webp_images_score,dom_size_score,first_meaningful_paint_score,first_contentful_paint_score" +
                    ",cumulative_layout_shift_score,redirects_score,uses_responsive_images_score,unused_css_rules_score" +
                    ",total_blocking_time_score,total_byte_weight_score,unminified_css_score" +
                    ",render_blocking_resources_score,unused_javascript_score" +
                    ",largest_contentful_paint_score,unminified_javascript_score,uses_text_compression_score" +
                    ",bootup_time_score,efficient_animated_content_score,server_response_time_score,font_display_score" +
                    ",speed_index_display_value" +
                    ",estimated_input_latency_display_value,mainthread_work_breakdown_display_value" +
                    ",uses_optimized_images_display_value,interactive_display_value,uses_rel_preconnect_display_value" +
                    ",uses_rel_preload_display_value,max_potential_fid_display_value,offscreen_images_display_value" +
                    ",uses_long_cache_ttl_display_value,first_cpu_idle_display_value,uses_webp_images_display_value" +
                    ",dom_size_display_value,first_meaningful_paint_display_value,first_contentful_paint_display_value" +
                    ",cumulative_layout_shift_display_value,redirects_display_value,uses_responsive_images_display_value" +
                    ",unused_css_rules_display_value,total_blocking_time_display_value,total_byte_weight_display_value" +
                    ",unminified_css_display_value,render_blocking_resources_display_value,unused_javascript_display_value" +
                    ",largest_contentful_paint_display_value,unminified_javascript_display_value" +
                    ",uses_text_compression_display_value,bootup_time_display_value,efficient_animated_content_display_value" +
                    ",server_response_time_display_value,font_display_display_value" +
                    ",speed_index_numeric_value,estimated_input_latency_numeric_value,mainthread_work_breakdown_numeric_value" +
                    ",uses_optimized_images_numeric_value,interactive_numeric_value,uses_rel_preconnect_numeric_value" +
                    ",uses_rel_preload_numeric_value,max_potential_fid_numeric_value,offscreen_images_numeric_value" +
                    ",uses_long_cache_ttl_numeric_value,first_cpu_idle_numeric_value,uses_webp_images_numeric_value" +
                    ",dom_size_numeric_value,first_meaningful_paint_numeric_value,first_contentful_paint_numeric_value" +
                    ",cumulative_layout_shift_numeric_value,redirects_numeric_value,uses_responsive_images_numeric_value" +
                    ",unused_css_rules_numeric_value,total_blocking_time_numeric_value,total_byte_weight_numeric_value" +
                    ",unminified_css_numeric_value,render_blocking_resources_numeric_value,unused_javascript_numeric_value" +
                    ",largest_contentful_paint_numeric_value,unminified_javascript_numeric_value" +
                    ",uses_text_compression_numeric_value,bootup_time_numeric_value,efficient_animated_content_numeric_value" +
                    ",server_response_time_numeric_value,font_display_numeric_value" +
                    ",speed_index_json,estimated_input_latency_json,mainthread_work_breakdown_json,uses_optimized_images_json" +
                    ",interactive_json,uses_rel_preconnect_json,uses_rel_preload_json,max_potential_fid_json" +
                    ",offscreen_images_json,uses_long_cache_ttl_json,first_cpu_idle_json,uses_webp_images_json,dom_size_json" +
                    ",first_meaningful_paint_json,first_contentful_paint_json,cumulative_layout_shift_json,redirects_json" +
                    ",uses_responsive_images_json,unused_css_rules_json,total_blocking_time_json,total_byte_weight_json" +
                    ",unminified_css_json,render_blocking_resources_json,unused_javascript_json,largest_contentful_paint_json" +
                    ",unminified_javascript_json,uses_text_compression_json,bootup_time_json,efficient_animated_content_json " +
                    ",server_response_time_json,font_display_json" +
                    ",interaction_to_next_paint_json,origin_interaction_to_next_paint_json" +
                    ",loading_experience_json,origin_loading_experience_json" +
                    ", attrstr.key, attrstr.value" +
                    ",upload_date,version" +
                    ")" +
                    "VALUES (" +
                    " ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?, " +
                    " ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? " +
                    ")";
            for (PageSpeedEntity pageSpeedEntity : speedEntityList) {
                Object[] values = new Object[]{
                        pageSpeedEntity.getSendDate() == null ? today : FormatUtils.formatDate(pageSpeedEntity.getSendDate(), FormatUtils.DATE_PATTERN_2),
                        pageSpeedEntity.getCrawlMonth(),
                        pageSpeedEntity.getOwnDomainId(),
//                    pageSpeedEntity.getUrlId(),
                        pageSpeedEntity.getDeviceIntType(),
                        pageSpeedEntity.getoUrl(),
                        pageSpeedEntity.getrUrl(),
                        pageSpeedEntity.getRedfirectFlg(),
                        pageSpeedEntity.getResponseCode(),
                        pageSpeedEntity.getScore(),

                        pageSpeedEntity.getSpeedIndexScore(),
                        pageSpeedEntity.getEstimatedInputLatencyScore(),
                        pageSpeedEntity.getMainthreadWorkBreakdownScore(),
                        pageSpeedEntity.getUsesOptimizedImagesScore(),
                        pageSpeedEntity.getInteractiveScore(),
                        pageSpeedEntity.getUsesRelPreconnectScore(),
                        pageSpeedEntity.getUsesRelPreloadScore(),
                        pageSpeedEntity.getMaxPotentialFidScore(),
                        pageSpeedEntity.getOffscreenImagesScore(),
                        pageSpeedEntity.getUsesLongCacheTtlScore(),
                        pageSpeedEntity.getFirstCpuIdleScore(),
                        pageSpeedEntity.getUsesWebpImagesScore(),
                        pageSpeedEntity.getDomSizeScore(),
                        pageSpeedEntity.getFirstMeaningfulPaintScore(),
                        pageSpeedEntity.getFirstContentfulPaintScore(),
                        pageSpeedEntity.getCumulativeLayoutShiftScore(),
                        pageSpeedEntity.getRedirectsScore(),
                        pageSpeedEntity.getUsesResponsiveImagesScore(),
                        pageSpeedEntity.getUnusedCssRulesScore(),
                        pageSpeedEntity.getTotalBlockingTimeScore(),
                        pageSpeedEntity.getTotalByteWeightScore(),
                        pageSpeedEntity.getUnminifiedCssScore(),
                        pageSpeedEntity.getRenderBlockingResourcesScore(),
                        pageSpeedEntity.getUnusedJavascriptScore(),
                        pageSpeedEntity.getLargestContentfulPaintScore(),
                        pageSpeedEntity.getUnminifiedJavascriptScore(),
                        pageSpeedEntity.getUsesTextCompressionScore(),
                        pageSpeedEntity.getBootupTimeScore(),
                        pageSpeedEntity.getEfficientAnimatedContentScore(),
                        pageSpeedEntity.getServerResponseTimeScore(),
                        pageSpeedEntity.getFontDisplayScore(),


                        pageSpeedEntity.getSpeedIndexDisplayValue(),
                        pageSpeedEntity.getEstimatedInputLatencyDisplayValue(),
                        pageSpeedEntity.getMainthreadWorkBreakdownDisplayValue(),
                        pageSpeedEntity.getUsesOptimizedImagesDisplayValue(),
                        pageSpeedEntity.getInteractiveDisplayValue(),
                        pageSpeedEntity.getUsesRelPreconnectDisplayValue(),
                        pageSpeedEntity.getUsesRelPreloadDisplayValue(),
                        pageSpeedEntity.getMaxPotentialFidDisplayValue(),
                        pageSpeedEntity.getOffscreenImagesDisplayValue(),
                        pageSpeedEntity.getUsesLongCacheTtlDisplayValue(),
                        pageSpeedEntity.getFirstCpuIdleDisplayValue(),
                        pageSpeedEntity.getUsesWebpImagesDisplayValue(),
                        pageSpeedEntity.getDomSizeDisplayValue(),
                        pageSpeedEntity.getFirstMeaningfulPaintDisplayValue(),
                        pageSpeedEntity.getFirstContentfulPaintDisplayValue(),
                        pageSpeedEntity.getCumulativeLayoutShiftDisplayValue(),
                        pageSpeedEntity.getRedirectsDisplayValue(),
                        pageSpeedEntity.getUsesResponsiveImagesDisplayValue(),
                        pageSpeedEntity.getUnusedCssRulesDisplayValue(),
                        pageSpeedEntity.getTotalBlockingTimeDisplayValue(),
                        pageSpeedEntity.getTotalByteWeightDisplayValue(),
                        pageSpeedEntity.getUnminifiedCssDisplayValue(),
                        pageSpeedEntity.getRenderBlockingResourcesDisplayValue(),
                        pageSpeedEntity.getUnusedJavascriptDisplayValue(),
                        pageSpeedEntity.getLargestContentfulPaintDisplayValue(),
                        pageSpeedEntity.getUnminifiedJavascriptDisplayValue(),
                        pageSpeedEntity.getUsesTextCompressionDisplayValue(),
                        pageSpeedEntity.getBootupTimeDisplayValue(),
                        pageSpeedEntity.getEfficientAnimatedContentDisplayValue(),
                        pageSpeedEntity.getServerResponseTimeDisplayValue(),
                        pageSpeedEntity.getFontDisplayDisplayValue(),

                        pageSpeedEntity.getSpeedIndexNumericValue(),
                        pageSpeedEntity.getEstimatedInputLatencyNumericValue(),
                        pageSpeedEntity.getMainthreadWorkBreakdownNumericValue(),
                        pageSpeedEntity.getUsesOptimizedImagesNumericValue(),
                        pageSpeedEntity.getInteractiveNumericValue(),
                        pageSpeedEntity.getUsesRelPreconnectNumericValue(),
                        pageSpeedEntity.getUsesRelPreloadNumericValue(),
                        pageSpeedEntity.getMaxPotentialFidNumericValue(),
                        pageSpeedEntity.getOffscreenImagesNumericValue(),
                        pageSpeedEntity.getUsesLongCacheTtlNumericValue(),
                        pageSpeedEntity.getFirstCpuIdleNumericValue(),
                        pageSpeedEntity.getUsesWebpImagesNumericValue(),
                        pageSpeedEntity.getDomSizeNumericValue(),
                        pageSpeedEntity.getFirstMeaningfulPaintNumericValue(),
                        pageSpeedEntity.getFirstContentfulPaintNumericValue(),
                        pageSpeedEntity.getCumulativeLayoutShiftNumericValue(),
                        pageSpeedEntity.getRedirectsNumericValue(),
                        pageSpeedEntity.getUsesResponsiveImagesNumericValue(),
                        pageSpeedEntity.getUnusedCssRulesNumericValue(),
                        pageSpeedEntity.getTotalBlockingTimeNumericValue(),
                        pageSpeedEntity.getTotalByteWeightNumericValue(),
                        pageSpeedEntity.getUnminifiedCssNumericValue(),
                        pageSpeedEntity.getRenderBlockingResourcesNumericValue(),
                        pageSpeedEntity.getUnusedJavascriptNumericValue(),
                        pageSpeedEntity.getLargestContentfulPaintNumericValue(),
                        pageSpeedEntity.getUnminifiedJavascriptNumericValue(),
                        pageSpeedEntity.getUsesTextCompressionNumericValue(),
                        pageSpeedEntity.getBootupTimeNumericValue(),
                        pageSpeedEntity.getEfficientAnimatedContentNumericValue(),
                        pageSpeedEntity.getServerResponseTimeNumericValue(),
                        pageSpeedEntity.getFontDisplayNumericValue(),

                        pageSpeedEntity.getSpeedIndexJson(),
                        pageSpeedEntity.getEstimatedInputLatencyJson(),
                        pageSpeedEntity.getMainthreadWorkBreakdownJson(),
                        pageSpeedEntity.getUsesOptimizedImagesJson(),
                        pageSpeedEntity.getInteractiveJson(),
                        pageSpeedEntity.getUsesRelPreconnectJson(),
                        pageSpeedEntity.getUsesRelPreloadJson(),
                        pageSpeedEntity.getMaxPotentialFidJson(),
                        pageSpeedEntity.getOffscreenImagesJson(),
                        pageSpeedEntity.getUsesLongCacheTtlJson(),
                        pageSpeedEntity.getFirstCpuIdleJson(),
                        pageSpeedEntity.getUsesWebpImagesJson(),
                        pageSpeedEntity.getDomSizeJson(),
                        pageSpeedEntity.getFirstMeaningfulPaintJson(),
                        pageSpeedEntity.getFirstContentfulPaintJson(),
                        pageSpeedEntity.getCumulativeLayoutShiftJson(),
                        pageSpeedEntity.getRedirectsJson(),
                        pageSpeedEntity.getUsesResponsiveImagesJson(),
                        pageSpeedEntity.getUnusedCssRulesJson(),
                        pageSpeedEntity.getTotalBlockingTimeJson(),
                        pageSpeedEntity.getTotalByteWeightJson(),
                        pageSpeedEntity.getUnminifiedCssJson(),
                        pageSpeedEntity.getRenderBlockingResourcesJson(),
                        pageSpeedEntity.getUnusedJavascriptJson(),
                        pageSpeedEntity.getLargestContentfulPaintJson(),
                        pageSpeedEntity.getUnminifiedJavascriptJson(),
                        pageSpeedEntity.getUsesTextCompressionJson(),
                        pageSpeedEntity.getBootupTimeJson(),
                        pageSpeedEntity.getEfficientAnimatedContentJson(),
                        pageSpeedEntity.getServerResponseTimeJson(),
                        pageSpeedEntity.getFontDisplayJson(),
                        pageSpeedEntity.getInteractionToNextPaintJson(),
                        pageSpeedEntity.getOriginInteractionToNextPaintJson(),
                        pageSpeedEntity.getLoadingExperienceJson(),
                        pageSpeedEntity.getOriginLoadingExperienceJson(),

                        pageSpeedEntity.getRuleResultskey(),
                        pageSpeedEntity.getRuleResultsvalue(),
//                        pageSpeedEntity.getRawJson(),
                        pageSpeedEntity.getUploadDate() == null ? today : pageSpeedEntity.getUploadDate(),
                        pageSpeedEntity.getVersion()
                };
//            if(entity.getPrioritizeVisibleContent() == null){
//                System.out.println(" ###################### " + JSON.toJSONString(entity));
//            }
//            System.out.println("===getPrioritizeVisibleContent:" + entity.getPrioritizeVisibleContent());
//            System.out.println("===getEnableGzipCompression:" + entity.getEnableGzipCompression());
//            System.out.println("===getLeverageBrowserCaching:" + entity.getLeverageBrowserCaching());
//            System.out.println("===getMainResourceServerResponseTime:" + entity.getMainResourceServerResponseTime());
//            System.out.println("===getMinifyCss:" + entity.getMinifyCss());
//            System.out.println("===getMinifyHtml:" + entity.getMinifyHtml());
//            System.out.println(new Gson().toJson(values));

                batch.add(values);
            }
        } else {
            sql += " (crawl_date,crawl_month, own_domain_id, device, o_url, r_url, redfirect_flg, response_code" +
                    " ,upload_date,version )" +
                    " VALUES ( ?,?,?,?,?,?,?,?,?,?)";
            for (PageSpeedEntity entity : speedEntityList) {
                Object[] values = new Object[]{
                        entity.getSendDate() == null ? today : FormatUtils.formatDate(entity.getSendDate(), FormatUtils.DATE_PATTERN_2),
                        entity.getCrawlMonth(),
                        entity.getOwnDomainId(),
//                    entity.getUrlId(),
                        entity.getDeviceIntType(),
                        entity.getoUrl(),
                        entity.getrUrl(),
                        entity.getRedfirectFlg(),
                        entity.getResponseCode(),
//                        entity.getRawJson(),
                        entity.getUploadDate() == null ? today : entity.getUploadDate(),
                        entity.getVersion()
                };
                batch.add(values);
            }
        }


        System.out.println("===insert batch size:" + batch.size());
        Long startTime = System.currentTimeMillis();
        executeBatch(sql, batch);
        System.out.println("===耗时:" + (System.currentTimeMillis() - startTime) / 1000 + " s");
//        String endCountSql = "select count() from ";
//        if (isTest) {
//            endCountSql += getOneTimeTableName();
//        } else {
//            endCountSql += getTableName();
//        }
//        Integer endCount = queryForInteger(endCountSql);
//        System.out.println("===endCount:" + endCount);
    }

    public void insertBatchV3Test(List<PageSpeedEntity> speedEntityList, boolean isSuccess, boolean isTest, boolean isWeekly) {

//        String countSql = "select count() from ";
//        if (isTest) {
//            countSql += getOneTimeTableName();
//        } else {
//            countSql += getTableName();
//        }
//        Integer startCount = queryForInteger(countSql);
//        System.out.println("===startCount:" + startCount);

        String today = FormatUtils.formatDate(new Date(), "yyyy-MM-dd");

        String sql = "INSERT INTO ";
        if (isTest) {
            sql += getOneTimeTableName();
        } else if (isWeekly) {
            sql += getWeeklyTableName();
        } else {
            sql += getTableName();
        }

        List<Object[]> batch = new ArrayList<>();
        if (isSuccess) {
            sql += " (crawl_date,crawl_month, own_domain_id, device, o_url, r_url, redfirect_flg, response_code, score" +
                    ", speed_index_score,estimated_input_latency_score,mainthread_work_breakdown_score" +
                    ",uses_optimized_images_score,interactive_score,uses_rel_preconnect_score,uses_rel_preload_score" +
                    ",max_potential_fid_score,offscreen_images_score,uses_long_cache_ttl_score,first_cpu_idle_score" +
                    ",uses_webp_images_score,dom_size_score,first_meaningful_paint_score,first_contentful_paint_score" +
                    ",cumulative_layout_shift_score,redirects_score,uses_responsive_images_score,unused_css_rules_score" +
                    ",total_blocking_time_score,total_byte_weight_score,unminified_css_score" +
                    ",render_blocking_resources_score,unused_javascript_score" +
                    ",largest_contentful_paint_score,unminified_javascript_score,uses_text_compression_score" +
                    ",bootup_time_score,efficient_animated_content_score,server_response_time_score,font_display_score" +
                    ",speed_index_display_value" +
                    ",estimated_input_latency_display_value,mainthread_work_breakdown_display_value" +
                    ",uses_optimized_images_display_value,interactive_display_value,uses_rel_preconnect_display_value" +
                    ",uses_rel_preload_display_value,max_potential_fid_display_value,offscreen_images_display_value" +
                    ",uses_long_cache_ttl_display_value,first_cpu_idle_display_value,uses_webp_images_display_value" +
                    ",dom_size_display_value,first_meaningful_paint_display_value,first_contentful_paint_display_value" +
                    ",cumulative_layout_shift_display_value,redirects_display_value,uses_responsive_images_display_value" +
                    ",unused_css_rules_display_value,total_blocking_time_display_value,total_byte_weight_display_value" +
                    ",unminified_css_display_value,render_blocking_resources_display_value,unused_javascript_display_value" +
                    ",largest_contentful_paint_display_value,unminified_javascript_display_value" +
                    ",uses_text_compression_display_value,bootup_time_display_value,efficient_animated_content_display_value" +
                    ",server_response_time_display_value,font_display_display_value" +
                    ",speed_index_numeric_value,estimated_input_latency_numeric_value,mainthread_work_breakdown_numeric_value" +
                    ",uses_optimized_images_numeric_value,interactive_numeric_value,uses_rel_preconnect_numeric_value" +
                    ",uses_rel_preload_numeric_value,max_potential_fid_numeric_value,offscreen_images_numeric_value" +
                    ",uses_long_cache_ttl_numeric_value,first_cpu_idle_numeric_value,uses_webp_images_numeric_value" +
                    ",dom_size_numeric_value,first_meaningful_paint_numeric_value,first_contentful_paint_numeric_value" +
                    ",cumulative_layout_shift_numeric_value,redirects_numeric_value,uses_responsive_images_numeric_value" +
                    ",unused_css_rules_numeric_value,total_blocking_time_numeric_value,total_byte_weight_numeric_value" +
                    ",unminified_css_numeric_value,render_blocking_resources_numeric_value,unused_javascript_numeric_value" +
                    ",largest_contentful_paint_numeric_value,unminified_javascript_numeric_value" +
                    ",uses_text_compression_numeric_value,bootup_time_numeric_value,efficient_animated_content_numeric_value" +
                    ",server_response_time_numeric_value,font_display_numeric_value" +
                    ",speed_index_json,estimated_input_latency_json,mainthread_work_breakdown_json,uses_optimized_images_json" +
                    ",interactive_json,uses_rel_preconnect_json,uses_rel_preload_json,max_potential_fid_json" +
                    ",offscreen_images_json,uses_long_cache_ttl_json,first_cpu_idle_json,uses_webp_images_json,dom_size_json" +
                    ",first_meaningful_paint_json,first_contentful_paint_json,cumulative_layout_shift_json,redirects_json" +
                    ",uses_responsive_images_json,unused_css_rules_json,total_blocking_time_json,total_byte_weight_json" +
                    ",unminified_css_json,render_blocking_resources_json,unused_javascript_json,largest_contentful_paint_json" +
                    ",unminified_javascript_json,uses_text_compression_json,bootup_time_json,efficient_animated_content_json " +
                    ",server_response_time_json,font_display_json" +
                    ",interaction_to_next_paint_json,origin_interaction_to_next_paint_json" +
                    ",loading_experience_json,origin_loading_experience_json" +
                    ", attrstr.key, attrstr.value" +
                    ",upload_date,version" +
                    ")" +
                    "VALUES (" +
                    " ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?, " +
                    " ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? " +
                    ")";
            for (PageSpeedEntity pageSpeedEntity : speedEntityList) {
                Object[] values = new Object[]{
                        pageSpeedEntity.getSendDate() == null ? today : FormatUtils.formatDate(pageSpeedEntity.getSendDate(), FormatUtils.DATE_PATTERN_2),
                        pageSpeedEntity.getCrawlMonth(),
                        pageSpeedEntity.getOwnDomainId(),
//                    pageSpeedEntity.getUrlId(),
                        pageSpeedEntity.getDeviceIntType(),
                        pageSpeedEntity.getoUrl(),
                        pageSpeedEntity.getrUrl(),
                        pageSpeedEntity.getRedfirectFlg(),
                        pageSpeedEntity.getResponseCode(),
                        pageSpeedEntity.getScore(),

                        pageSpeedEntity.getSpeedIndexScore(),
                        pageSpeedEntity.getEstimatedInputLatencyScore(),
                        pageSpeedEntity.getMainthreadWorkBreakdownScore(),
                        pageSpeedEntity.getUsesOptimizedImagesScore(),
                        pageSpeedEntity.getInteractiveScore(),
                        pageSpeedEntity.getUsesRelPreconnectScore(),
                        pageSpeedEntity.getUsesRelPreloadScore(),
                        pageSpeedEntity.getMaxPotentialFidScore(),
                        pageSpeedEntity.getOffscreenImagesScore(),
                        pageSpeedEntity.getUsesLongCacheTtlScore(),
                        pageSpeedEntity.getFirstCpuIdleScore(),
                        pageSpeedEntity.getUsesWebpImagesScore(),
                        pageSpeedEntity.getDomSizeScore(),
                        pageSpeedEntity.getFirstMeaningfulPaintScore(),
                        pageSpeedEntity.getFirstContentfulPaintScore(),
                        pageSpeedEntity.getCumulativeLayoutShiftScore(),
                        pageSpeedEntity.getRedirectsScore(),
                        pageSpeedEntity.getUsesResponsiveImagesScore(),
                        pageSpeedEntity.getUnusedCssRulesScore(),
                        pageSpeedEntity.getTotalBlockingTimeScore(),
                        pageSpeedEntity.getTotalByteWeightScore(),
                        pageSpeedEntity.getUnminifiedCssScore(),
                        pageSpeedEntity.getRenderBlockingResourcesScore(),
                        pageSpeedEntity.getUnusedJavascriptScore(),
                        pageSpeedEntity.getLargestContentfulPaintScore(),
                        pageSpeedEntity.getUnminifiedJavascriptScore(),
                        pageSpeedEntity.getUsesTextCompressionScore(),
                        pageSpeedEntity.getBootupTimeScore(),
                        pageSpeedEntity.getEfficientAnimatedContentScore(),
                        pageSpeedEntity.getServerResponseTimeScore(),
                        pageSpeedEntity.getFontDisplayScore(),


                        pageSpeedEntity.getSpeedIndexDisplayValue(),
                        pageSpeedEntity.getEstimatedInputLatencyDisplayValue(),
                        pageSpeedEntity.getMainthreadWorkBreakdownDisplayValue(),
                        pageSpeedEntity.getUsesOptimizedImagesDisplayValue(),
                        pageSpeedEntity.getInteractiveDisplayValue(),
                        pageSpeedEntity.getUsesRelPreconnectDisplayValue(),
                        pageSpeedEntity.getUsesRelPreloadDisplayValue(),
                        pageSpeedEntity.getMaxPotentialFidDisplayValue(),
                        pageSpeedEntity.getOffscreenImagesDisplayValue(),
                        pageSpeedEntity.getUsesLongCacheTtlDisplayValue(),
                        pageSpeedEntity.getFirstCpuIdleDisplayValue(),
                        pageSpeedEntity.getUsesWebpImagesDisplayValue(),
                        pageSpeedEntity.getDomSizeDisplayValue(),
                        pageSpeedEntity.getFirstMeaningfulPaintDisplayValue(),
                        pageSpeedEntity.getFirstContentfulPaintDisplayValue(),
                        pageSpeedEntity.getCumulativeLayoutShiftDisplayValue(),
                        pageSpeedEntity.getRedirectsDisplayValue(),
                        pageSpeedEntity.getUsesResponsiveImagesDisplayValue(),
                        pageSpeedEntity.getUnusedCssRulesDisplayValue(),
                        pageSpeedEntity.getTotalBlockingTimeDisplayValue(),
                        pageSpeedEntity.getTotalByteWeightDisplayValue(),
                        pageSpeedEntity.getUnminifiedCssDisplayValue(),
                        pageSpeedEntity.getRenderBlockingResourcesDisplayValue(),
                        pageSpeedEntity.getUnusedJavascriptDisplayValue(),
                        pageSpeedEntity.getLargestContentfulPaintDisplayValue(),
                        pageSpeedEntity.getUnminifiedJavascriptDisplayValue(),
                        pageSpeedEntity.getUsesTextCompressionDisplayValue(),
                        pageSpeedEntity.getBootupTimeDisplayValue(),
                        pageSpeedEntity.getEfficientAnimatedContentDisplayValue(),
                        pageSpeedEntity.getServerResponseTimeDisplayValue(),
                        pageSpeedEntity.getFontDisplayDisplayValue(),

                        pageSpeedEntity.getSpeedIndexNumericValue(),
                        pageSpeedEntity.getEstimatedInputLatencyNumericValue(),
                        pageSpeedEntity.getMainthreadWorkBreakdownNumericValue(),
                        pageSpeedEntity.getUsesOptimizedImagesNumericValue(),
                        pageSpeedEntity.getInteractiveNumericValue(),
                        pageSpeedEntity.getUsesRelPreconnectNumericValue(),
                        pageSpeedEntity.getUsesRelPreloadNumericValue(),
                        pageSpeedEntity.getMaxPotentialFidNumericValue(),
                        pageSpeedEntity.getOffscreenImagesNumericValue(),
                        pageSpeedEntity.getUsesLongCacheTtlNumericValue(),
                        pageSpeedEntity.getFirstCpuIdleNumericValue(),
                        pageSpeedEntity.getUsesWebpImagesNumericValue(),
                        pageSpeedEntity.getDomSizeNumericValue(),
                        pageSpeedEntity.getFirstMeaningfulPaintNumericValue(),
                        pageSpeedEntity.getFirstContentfulPaintNumericValue(),
                        pageSpeedEntity.getCumulativeLayoutShiftNumericValue(),
                        pageSpeedEntity.getRedirectsNumericValue(),
                        pageSpeedEntity.getUsesResponsiveImagesNumericValue(),
                        pageSpeedEntity.getUnusedCssRulesNumericValue(),
                        pageSpeedEntity.getTotalBlockingTimeNumericValue(),
                        pageSpeedEntity.getTotalByteWeightNumericValue(),
                        pageSpeedEntity.getUnminifiedCssNumericValue(),
                        pageSpeedEntity.getRenderBlockingResourcesNumericValue(),
                        pageSpeedEntity.getUnusedJavascriptNumericValue(),
                        pageSpeedEntity.getLargestContentfulPaintNumericValue(),
                        pageSpeedEntity.getUnminifiedJavascriptNumericValue(),
                        pageSpeedEntity.getUsesTextCompressionNumericValue(),
                        pageSpeedEntity.getBootupTimeNumericValue(),
                        pageSpeedEntity.getEfficientAnimatedContentNumericValue(),
                        pageSpeedEntity.getServerResponseTimeNumericValue(),
                        pageSpeedEntity.getFontDisplayNumericValue(),

                        pageSpeedEntity.getSpeedIndexJson(),
                        pageSpeedEntity.getEstimatedInputLatencyJson(),
                        pageSpeedEntity.getMainthreadWorkBreakdownJson(),
                        pageSpeedEntity.getUsesOptimizedImagesJson(),
                        pageSpeedEntity.getInteractiveJson(),
                        pageSpeedEntity.getUsesRelPreconnectJson(),
                        pageSpeedEntity.getUsesRelPreloadJson(),
                        pageSpeedEntity.getMaxPotentialFidJson(),
                        pageSpeedEntity.getOffscreenImagesJson(),
                        pageSpeedEntity.getUsesLongCacheTtlJson(),
                        pageSpeedEntity.getFirstCpuIdleJson(),
                        pageSpeedEntity.getUsesWebpImagesJson(),
                        pageSpeedEntity.getDomSizeJson(),
                        pageSpeedEntity.getFirstMeaningfulPaintJson(),
                        pageSpeedEntity.getFirstContentfulPaintJson(),
                        pageSpeedEntity.getCumulativeLayoutShiftJson(),
                        pageSpeedEntity.getRedirectsJson(),
                        pageSpeedEntity.getUsesResponsiveImagesJson(),
                        pageSpeedEntity.getUnusedCssRulesJson(),
                        pageSpeedEntity.getTotalBlockingTimeJson(),
                        pageSpeedEntity.getTotalByteWeightJson(),
                        pageSpeedEntity.getUnminifiedCssJson(),
                        pageSpeedEntity.getRenderBlockingResourcesJson(),
                        pageSpeedEntity.getUnusedJavascriptJson(),
                        pageSpeedEntity.getLargestContentfulPaintJson(),
                        pageSpeedEntity.getUnminifiedJavascriptJson(),
                        pageSpeedEntity.getUsesTextCompressionJson(),
                        pageSpeedEntity.getBootupTimeJson(),
                        pageSpeedEntity.getEfficientAnimatedContentJson(),
                        pageSpeedEntity.getServerResponseTimeJson(),
                        pageSpeedEntity.getFontDisplayJson(),
                        pageSpeedEntity.getInteractionToNextPaintJson(),
                        pageSpeedEntity.getOriginInteractionToNextPaintJson(),
                        pageSpeedEntity.getLoadingExperienceJson(),
                        pageSpeedEntity.getOriginLoadingExperienceJson(),

                        pageSpeedEntity.getRuleResultskey(),
                        pageSpeedEntity.getRuleResultsvalue(),
//                        pageSpeedEntity.getRawJson(),
                        pageSpeedEntity.getUploadDate() == null ? today : pageSpeedEntity.getUploadDate(),
                        pageSpeedEntity.getVersion()
                };
//            if(entity.getPrioritizeVisibleContent() == null){
//                System.out.println(" ###################### " + JSON.toJSONString(entity));
//            }
//            System.out.println("===getPrioritizeVisibleContent:" + entity.getPrioritizeVisibleContent());
//            System.out.println("===getEnableGzipCompression:" + entity.getEnableGzipCompression());
//            System.out.println("===getLeverageBrowserCaching:" + entity.getLeverageBrowserCaching());
//            System.out.println("===getMainResourceServerResponseTime:" + entity.getMainResourceServerResponseTime());
//            System.out.println("===getMinifyCss:" + entity.getMinifyCss());
//            System.out.println("===getMinifyHtml:" + entity.getMinifyHtml());
//            System.out.println(new Gson().toJson(values));

                batch.add(values);
            }
        } else {
            sql += " (crawl_date,crawl_month, own_domain_id, device, o_url, r_url, redfirect_flg, response_code" +
                    " ,upload_date,version )" +
                    " VALUES ( ?,?,?,?,?,?,?,?,?,?)";
            for (PageSpeedEntity entity : speedEntityList) {
                Object[] values = new Object[]{
                        entity.getSendDate() == null ? today : FormatUtils.formatDate(entity.getSendDate(), FormatUtils.DATE_PATTERN_2),
                        entity.getCrawlMonth(),
                        entity.getOwnDomainId(),
//                    entity.getUrlId(),
                        entity.getDeviceIntType(),
                        entity.getoUrl(),
                        entity.getrUrl(),
                        entity.getRedfirectFlg(),
                        entity.getResponseCode(),
//                        entity.getRawJson(),
                        entity.getUploadDate() == null ? today : entity.getUploadDate(),
                        entity.getVersion()
                };
                batch.add(values);
            }
        }


        System.out.println("===insert batch size:" + batch.size());
        Long startTime = System.currentTimeMillis();
        executeBatch(sql, batch);
        System.out.println("===耗时:" + (System.currentTimeMillis() - startTime) / 1000 + " s");
//        String endCountSql = "select count() from ";
//        if (isTest) {
//            endCountSql += getOneTimeTableName();
//        } else {
//            endCountSql += getTableName();
//        }
//        Integer endCount = queryForInteger(endCountSql);
//        System.out.println("===endCount:" + endCount);
    }

    public List<Integer> getUniqueDomainListByMonth(int crawlMonth) {
        String sql = " select distinct own_domain_id from page_speed_dis_v2 where crawl_month = " + crawlMonth + " order by own_domain_id ";
        return queryForIntegerList(sql);
    }

    public List<PageSpeedEntity> getByDomainId(String crawlDate, int crawlMonth, int domainId, int device) {
        String sql = " select crawl_date,crawl_month,own_domain_id,device,o_url,r_url,redfirect_flg,response_code,score,raw_json,upload_date,version";
        sql += " from page_speed_dis_v2";
        sql += " where crawl_date = ? and crawl_month = ? and own_domain_id = ? and device = ? ";
        return findBySql(sql, crawlDate, crawlMonth, domainId, device);
    }

    public List<Long> getUrlsMessing(int domianId, int device, String ids, Integer crawl_month) {
        String sql = "select url_id from " + getTableName() +
                " where own_domain_id = ? and crawl_month = ? and device = ?  and url_id in( " + ids + ")";

        return this.queryForLongList(sql, domianId, crawl_month, device);
    }

    public List<String> getUrlsMessingV4(int device, Integer crawl_month, int domainId) {
        String sql = "select o_url from " + getTableName() +
                " where  crawl_month = ? and device = ?  and own_domain_id = ?";

        return this.queryForStringList(sql, crawl_month, device, domainId);
    }

}
