package seoclarity.backend.dao.clickhouse;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;

import java.util.*;

@Repository
public class ClDailySeoAdsEntityDao extends ClAdsBaseJdbcSupport<CLRankingDetailEntity> {
    public List<Map<String,Object>> queryListMap (String sql){

        return this.queryForMapList(sql);
    }
    public String getTableName() {
        return "ads_detail";
    }

    public List<Map<String, Object>> queryForList(String sql) {
        return this.getJdbcTemplate().queryForList(sql);
    }

    public List<Map<String, Object>> queryDataForAgodaPPC(List<String> keywordNameList, OwnDomainEntity domainEntity, int engine, int language, Date rankDate){
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select keyword_name,own_domain_id,display_url,rank,avg_search_volume ");
        sbd.append(" from ").append(getTableName());
        sbd.append(" where keyword_name in ('").append(StringUtils.join(keywordNameList, "','")).append("')");
        sbd.append(" and own_domain_id = ").append(domainEntity.getId()).append(" and engine_id = ").append(engine).append(" and language_id = ").append(language);
        sbd.append(" and ranking_date = '").append(DateFormatUtils.format(rankDate, "yyyy-MM-dd")).append("'");
        return this.getJdbcTemplate().queryForList(sbd.toString());
    }
}
