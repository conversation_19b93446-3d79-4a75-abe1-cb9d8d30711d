package seoclarity.backend.dao.clickhouse.googlesuggest;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.BaseJdbcSupport;
import seoclarity.backend.entity.actonia.KeywordSuggest;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Date;
import java.util.List;

@Repository("googleKeywordSuggestClarityDBDAO")
public class GoogleKeywordSuggestClarityDBDAO extends BaseJdbcSupport<KeywordSuggest> {

    @Resource(name="googleKeywordSuggestDataSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }
    @Override
    public String getTableName() {
        return "dis_keyword_suggest";
    }


    public List<String> getDistinctSeedKeyword(String trafficDate) {
        String sql = " select seed_keyword from " + getTableName() + " where log_date = ? ";
        return queryForStringList(sql, trafficDate);
    }

    public List<KeywordSuggest> getList(String trafficDate, List<String> seedKeywordList) {
        String sql = " select * from " + getTableName() + " where log_date = ? and seed_keyword in ('" + StringUtils.join(seedKeywordList, "','") + "') ";
        return findBySql(sql, trafficDate);
    }
}
