package seoclarity.backend.dao.clickhouse.monthlyranking;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.monthlyranking.KeywordTokenizerSearchvolumeEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Repository
public class GoogleSuggestKeywordUploadDAO extends GoogleSuggestKeywordUploadBaseJdbcSupport<KeywordTokenizerSearchvolumeEntity>{

	@Override
	public String getTableName() {
		return "dis_google_suggest";
	}

	public List<String>  checkKeywordExists(int engine, int language, int locationId, List<String> keywordNameList, String tableName) {
		String sql = "select keyword_name from " + tableName + " where  engine_id = ? and language_id = ? and  location_id = ? and keyword_name in ('" + StringUtils.join(keywordNameList, "','") + "')";
		//System.out.println("************** sql:" + sql + " **************");
		return this.queryForStringList(sql, engine, language, locationId);
	}

	public int[] insertBatchForUnRankcheck(List<KeywordTokenizerSearchvolumeEntity> list, String tableName) {
		String sql = "insert into " + tableName
			+ " (stream_value, keyword_name, word, stream, location_id, engine_id, language_id, sign, "
			+ " avg_search_volume, "
			+ " cpc,"
			+ " keyword_variation_oneword,"
			+ " keyword_variation_ngram,"
			+ " category"
			+ ") "
			+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?) ";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (KeywordTokenizerSearchvolumeEntity entity : list) {
			Object[] values = new Object[] {
					entity.getStream(),
					entity.getKeywordName(),
					entity.getWordArray(),
					entity.getStreamArray(),
					entity.getLocationId(),
					entity.getEngineId(),
					entity.getLanguageId(),
					entity.getSign(),
					entity.getAvgSearchVolume(),
					entity.getCpc(),
					entity.getKeywordVariationOnewordArray(),
					entity.getKeywordVariationNgramArray(),
					entity.getCategoryArray()
			};
			batch.add(values);
		}

		return this.executeBatch(sql, batch);
	}
}
