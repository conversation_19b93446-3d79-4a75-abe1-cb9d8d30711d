package seoclarity.backend.dao.clickhouse.monthlyranking;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.dao.clickhouse.CICentralBaseJdbcSupport;
import seoclarity.backend.entity.clickhouse.monthlyranking.KeywordTokenizerSearchvolumeEntity;

/**
 * <AUTHOR>
 * @date 2021-08-04
 * @path seoclarity.backend.dao.clickhouse.monthlyranking.CentralKeywordTokenizerDAO
 * 
 */
@Repository
public class CentralKeywordTokenizerDAO extends CICentralBaseJdbcSupport<String> {

	@Override
	public java.lang.String getTableName() {
		return null;
	}
	
	public void insertBatch(List<KeywordTokenizerSearchvolumeEntity> list, String tableName) {
		String sql = "insert into " + tableName
			+ " (keyword_name, stream) "
			+ " values (?,?) ";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (KeywordTokenizerSearchvolumeEntity entity : list) {
			Object[] values = new Object[] {
					entity.getKeywordName(),
					entity.getKeywordVariationOnewordArray()
			};
			batch.add(values);
		}

		int[] count = this.executeBatch(sql, batch);
	}

	public List<Map<String, Object>> get7465Extract(String logDate, boolean isBrand){
		String sql = " SELECT SUM(impressions) AS impressionsSum,SUM(clicks) AS clicksSum,uniqHLL12(keyword_hash) AS keywordCount ";
		sql += " 	FROM ( ";
		sql += " 		select keyword_url_hash, clicks,impressions,position,device,keyword_hash,urlhash ";
		sql += " 		from actonia_gsc.gsc_all_in_one ";
		sql += " 		where own_domain_id = 7465 and log_date = '" + logDate + "' ";
		sql += " 		AND rel_id IN (4821,7170) AND type = 1 ";
		sql += " 		AND versioning = dictGetUInt16('claritydbVersionGSC', 'versioning', (1, toUInt32(own_domain_id), toDate(log_date), toUInt64(0))) ";
		if(isBrand){
			sql += " 		AND URLHash(lower(keyword_name)) GLOBAL IN (";
		}else {
			sql += " 		AND URLHash(lower(keyword_name)) GLOBAL NOT IN (";
		}
		sql += " 			SELECT cbd_keyword_hash from( ";
		sql += " 				SELECT grouptag_id, own_domain_id, location_id, grouptag_status, cbd_keyword_hash ";
		sql += "				FROM seo_daily_ranking.cdb_tracked_keyword ";
		sql += " 				WHERE (own_domain_id = 7465) AND (grouptag_id IN (1355942)) AND (grouptag_status = 1) AND (keyword_type = 1) ";
		sql += " 				GROUP BY grouptag_id, own_domain_id, location_id, grouptag_status, cbd_keyword_hash HAVING sum(sign) > 0 ";
		sql += "		)) ";
		sql += "		AND (lower(url) not like '%/blog%') AND (lower(decodeURLComponent(url)) not like '%/blog%') ";
		sql += "		AND (length(attrs.key) = 0) ";
		if(isBrand){
			sql += "		AND (lower(url) <> 'https://learning.linkedin.com/content-library') ";
			sql += "		AND (lower(decodeURLComponent(url)) <> 'https://learning.linkedin.com/content-library') ";
		}else {
			sql += " and  not match(keyword_name, 'linked in|xvideo|x vidios|^xvideos$|site:|^49ers$|linkedin|linkedin|linkdin|andy cohen|ssi|https://www.linkedin.com|billy beane|linkeidn|likedin|^quit$|social selling|linkin|sal khan|linekdn|linkedn|linedin|linked|photoshop cc 2019|linkein|san francisco|black panther|linekdin|xvids|linkd|lindekin|inkedin|xvideis|x videos|xxxvidios|lonkedin|linkelin|xvidos|xxvidios|lin|link|li|allen blue|index| לינקדאין|ךןמל|λινκεδιν|lin|li|linekdin|recruiterlite|linedin|linkedin|linked in|recruiter lite|recruiter light|likedin|linked|lts|لینکدین|lı|লিঙ্ক|l i|lnked|линкедин|حلول|дштлувшт|inkedin|дшт|υοθτθβε|in|לינקדין|লিনকেদিন|λινκ|xvodeos|xbideos|xvudeos|xvifeos|x.vidios|xvideio|xcideos|xvidei|xx vídeos|x vide|xivideos|xvdios|zvideos|xvideios|x video s|x vids|xvides|xvedeos|xvide|xvidros|xvieos|xvvideos|x vedeos|xxxvídeos') ";
		}

		sql += "	)";
		System.out.println("=========get7465Extract sql:" +sql);
		return queryForMapList(sql);
	}

	public String getDomainByUrl(String url){
		String sql = " SELECT domain('" + url + "') ";
		return queryForString(sql);
	}

	public List<String> getTop3CompetitorDomain(int engineId, int languageId, String domainReverse, String rootDomainReverse, String device){
		String tableDevice = "d_";
//		if(!device.equalsIgnoreCase("desktop")){
//			tableDevice = "m_";
//		}
		String rankcheckIdTableName = tableDevice + "rankcheckid_ranking_detail_202309_us";
		String detailTableName = tableDevice + "ranking_detail_202310_us";
		if(engineId != 1 && languageId != 1){
			rankcheckIdTableName = tableDevice + "rankcheckid_ranking_detail_202309_intl";
			detailTableName = tableDevice + "ranking_detail_202310_intl";
		}
		String sql = " select domain from ( ";
		sql += " SELECT domain_reverse AS domain ,count() AS cnt FROM monthly_ranking." + rankcheckIdTableName;
		sql += " WHERE engine_id = " + engineId + " AND language_id = " + languageId + " AND (true_rank <= 10)  ";
		sql += " AND keyword_rankcheck_id IN ( ";
		sql += " SELECT keyword_rankcheck_id AS id FROM monthly_ranking." + detailTableName + " AS x ";
		sql += " WHERE engine_id = " + engineId + " AND language_id =" + languageId + " AND (location_id = 0) AND (true_rank <= 50) ";
		sql += " AND (root_domain_reverse = '" + rootDomainReverse + "') AND (domain_reverse='"+ domainReverse + "') ";
		sql += " AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(" + engineId + "), toUInt64(" + languageId + "), toUInt64(keyword_rankcheck_id))) = 0) ";
		sql += " ) ";
		sql += " AND (domain NOT LIKE '%.google%') AND (domain NOT LIKE '%.yahoo') AND (domain NOT LIKE '%.bing') AND (domain NOT LIKE '%.baidu') ";
		sql += " AND (domain NOT LIKE '%.yandex') AND (domain NOT LIKE '%.naver') GROUP BY domain ORDER BY cnt DESC LIMIT 50 ";
		sql += " )t ";
		sql += " SETTINGS max_bytes_before_external_group_by = 20000000000, max_bytes_before_external_sort = 10000000000,distributed_aggregation_memory_efficient = 1, distributed_product_mode = 'local'";
		System.out.println("====getTop3CompetitorDomain:" + sql);
		return queryForStringList(sql);
	}

}
