package seoclarity.backend.dao.clickhouse.monthlyranking;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.CICentralBaseJdbcSupport;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;

import java.util.List;

@Repository
public class AioContentDAO extends CICentralBaseJdbcSupport<String> {

    @Override
    public java.lang.String getTableName() {
        return null;
    }

    public List<CLRankingDetailEntity> getRIAIOContent(ExtractQueryVO extractQueryVO){

        StringBuffer sql = new StringBuffer();
        sql.append("  select distinct keyword_name,keyword_rankcheck_id,aio_content   ");
        sql.append(" from aio_db.dis_ri_aio_content ");
        sql.append("    WHERE own_domain_id in (").append(StringUtils.join(extractQueryVO.getDomainIdList(), ",")).append(") ");
        sql.append("    AND engine_id =  ").append(extractQueryVO.getEngineId());
        sql.append("    AND language_id =  ").append(extractQueryVO.getLanguageId());
        sql.append("    AND ranking_date = '").append(extractQueryVO.getRankDate()).append("' ");
        sql.append("    AND device = '").append(extractQueryVO.getDevice()).append("' ");
        sql.append("    AND frequency = '").append(extractQueryVO.getFrequency()).append("' ");
        if (!extractQueryVO.getExtractAllLocation()) {
            if (extractQueryVO.getExtractGeo() == null || !extractQueryVO.getExtractGeo()) {
                sql.append(" AND location_id =  ").append(extractQueryVO.getLocationId());
            } else {
                sql.append(" AND location_id != 0 ");
            }
        }
        sql.append(" order by keyword_rankcheck_id" );
        sql.append(" limit " + (extractQueryVO.getPageNum() * extractQueryVO.getPageSize()) + " , " + extractQueryVO.getPageSize());
        System.out.println(" ====getRIAIOContentSQL: " + sql.toString());
        return findBySql(sql.toString(), CLRankingDetailEntity.class);
    }

}
