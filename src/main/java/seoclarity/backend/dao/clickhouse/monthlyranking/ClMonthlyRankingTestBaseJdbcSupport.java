package seoclarity.backend.dao.clickhouse.monthlyranking;

import seoclarity.backend.dao.BaseJdbcSupport;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.sql.DataSource;


public abstract class ClMonthlyRankingTestBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name="clarityDBMonthlyTestDataSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }
    
    public List<Map<String, Object>> getSql(String sql) {
    	return this.queryForMapList(sql);
    }

}
