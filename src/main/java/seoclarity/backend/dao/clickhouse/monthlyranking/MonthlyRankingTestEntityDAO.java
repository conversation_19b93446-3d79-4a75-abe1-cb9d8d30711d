package seoclarity.backend.dao.clickhouse.monthlyranking;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyRankingEntity;

/**
 * <AUTHOR>
 * @date 2019-09-03
 * @path seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingTestEntityDAO
 * 
 */
@Repository
public class MonthlyRankingTestEntityDAO extends ClMonthlyRankingTestBaseJdbcSupport<MonthlyRankingEntity> {

	@Override
	public String getTableName() {
		return null;
	}
	
	public List<String> getAllKeywords() {
		String sql = "select distinct keyword_rankcheck_id from test_local_d_amazon_201906_us order by keyword_rankcheck_id";
		return this.queryForStringList(sql);
	}
	
	public List<Map<String, Object>> executeSql(String sql) {
		return this.queryForMapList(sql);
	}

}
