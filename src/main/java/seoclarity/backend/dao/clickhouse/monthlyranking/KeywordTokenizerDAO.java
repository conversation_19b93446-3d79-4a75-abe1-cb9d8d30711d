package seoclarity.backend.dao.clickhouse.monthlyranking;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.clickhouse.monthlyranking.KeywordTokenizerSearchvolumeEntity;

/**
 * <AUTHOR>
 * @date 2018-09-13
 * seoclarity.backend.dao.clickhouse.monthlyranking.KeywordTokenizerDAO
 * https://www.wrike.com/open.htm?id=270587561
 */
@Repository
public class KeywordTokenizerDAO extends CIKeywordTokenizerBaseJdbcSupport<KeywordTokenizerSearchvolumeEntity>{
	private String tableNameValue;

	@Override
	public String getTableName() {
		if (StringUtils.isNotBlank(tableNameValue)) {
			return tableNameValue;
		}
//		return "temp_newly_added_local_searchvolume";
//		return "dis_searchvolume_v2";
//		return "local_test_searchvolume_v2_new_add";
//		return "local_searchvolume_v2_newtmp2_20210830 ";
		return "local_searchvolume_v2_20240927";
	}

	public void setTableName(String tableName) {
		this.tableNameValue = tableName;
	}
	
	public List<String>  checkKeywordExists(int engine, int language, int locationId, Integer[] keywordIdList, String... tableName) {
		String queryTable = (tableName == null || tableName.length == 0 || StringUtils.isBlank(tableName[0])) ? getTableName() : tableName[0];
		String sql = "select keyword_rankcheck_id from " + queryTable + " where  engine_id = ? and language_id = ? and  location_id = ? and keyword_rankcheck_id in (" + StringUtils.join(keywordIdList, ',') + ")";
		return this.queryForStringList(sql, engine, language, locationId);
	}
	
	public List<String>  checkKeywordExistsForInertUnRankcheckTable(int engine, int language, int locationId, Integer[] keywordIdList, String tableName) {
		String sql = "select keyword_rankcheck_id from " + tableName + " where  engine_id = ? and language_id = ? and  location_id = ? and keyword_rankcheck_id in (" + StringUtils.join(keywordIdList, ',') + ")";
		return this.queryForStringList(sql, engine, language, locationId);
	}

	public List<String>  checkKeywordExists(int engine, int language, int locationId, List<String> keywordNameList, String tableName) {
		String sql = "select keyword_name from " + tableName + " where  engine_id = ? and language_id = ? and  location_id = ? and keyword_name in ('" + StringUtils.join(keywordNameList, "','") + "')";
		//System.out.println("************** sql:" + sql + " **************");
		return this.queryForStringList(sql, engine, language, locationId);
	}

	public void insertKeywordCategory(String tableName, List<Object[]> values) {
		String sql = "insert into " + tableName + " (id, category, current_category, parent_id) values (?,?,?,?)";
		List<Object[]> batch = new ArrayList<Object[]>();
		batch.addAll(values);
		int[] count = this.executeBatch(sql, batch);
	}
	
	public void insertBatch(List<KeywordTokenizerSearchvolumeEntity> list) {
		String sql = "insert into " + getTableName() 
			+ " (keyword_rankcheck_id, keyword_name, word, stream, location_id, engine_id, language_id, sign, versioning, "
			+ " avg_search_volume, "
			+ "monthly_search_volume1,"
			+ "monthly_search_volume2,"
			+ "monthly_search_volume3,"
			+ "monthly_search_volume4,"
			+ "monthly_search_volume5,"
			+ "monthly_search_volume6,"
			+ "monthly_search_volume7,"
			+ "monthly_search_volume8,"
			+ "monthly_search_volume9,"
			+ "monthly_search_volume10,"
			+ "monthly_search_volume11,"
			+ "monthly_search_volume12,"
			+ "cpc,"
			+ "keyword_variation_oneword,"
			+ "keyword_variation_ngram,"
			+ "category"
			+ ") "
			+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (KeywordTokenizerSearchvolumeEntity entity : list) {
			Object[] values = new Object[] {
					entity.getKeywordRankcheckId(),
					entity.getKeywordName(),
					entity.getWordArray(),
					entity.getStreamArray(),
					entity.getLocationId(),
					entity.getEngineId(),
					entity.getLanguageId(),
					entity.getSign(),
					entity.getVersioning(),
					entity.getAvgSearchVolume(),
					entity.getMonthlySearchVolume1(),
					entity.getMonthlySearchVolume2(),
					entity.getMonthlySearchVolume3(),
					entity.getMonthlySearchVolume4(),
					entity.getMonthlySearchVolume5(),
					entity.getMonthlySearchVolume6(),
					entity.getMonthlySearchVolume7(),
					entity.getMonthlySearchVolume8(),
					entity.getMonthlySearchVolume9(),
					entity.getMonthlySearchVolume10(),
					entity.getMonthlySearchVolume11(),
					entity.getMonthlySearchVolume12(),
					entity.getCpc(),
					entity.getKeywordVariationOnewordArray(),
					entity.getKeywordVariationNgramArray(),
					entity.getCategoryArray(),
			};
			batch.add(values);
		}

		int[] count = this.executeBatch(sql, batch);
	}
	
	public void insertBatch(List<KeywordTokenizerSearchvolumeEntity> list, int lastRefreshMonth) {
		String sql = "insert into " + getTableName() 
			+ " (keyword_rankcheck_id, keyword_name, word, stream, location_id, engine_id, language_id, sign, versioning, "
			+ " avg_search_volume, "
			+ "monthly_search_volume1,"
			+ "monthly_search_volume2,"
			+ "monthly_search_volume3,"
			+ "monthly_search_volume4,"
			+ "monthly_search_volume5,"
			+ "monthly_search_volume6,"
			+ "monthly_search_volume7,"
			+ "monthly_search_volume8,"
			+ "monthly_search_volume9,"
			+ "monthly_search_volume10,"
			+ "monthly_search_volume11,"
			+ "monthly_search_volume12,"
			+ "cpc,"
			+ "keyword_variation_oneword,"
			+ "keyword_variation_ngram,"
			+ "monthlySvAttr.key,"
			+ "monthlySvAttr.value,"
			+ "category,"
			+ "lastRefreshMonth,"
			+ "hasSv"
			+ ") "
			+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (KeywordTokenizerSearchvolumeEntity entity : list) {
			Object[] values = new Object[] {
					entity.getKeywordRankcheckId(),
					entity.getKeywordName(),
					entity.getWordArray(),
					entity.getStreamArray(),
					entity.getLocationId(),
					entity.getEngineId(),
					entity.getLanguageId(),
					entity.getSign(),
					entity.getVersioning(),
					entity.getAvgSearchVolume(),
					entity.getMonthlySearchVolume1(),
					entity.getMonthlySearchVolume2(),
					entity.getMonthlySearchVolume3(),
					entity.getMonthlySearchVolume4(),
					entity.getMonthlySearchVolume5(),
					entity.getMonthlySearchVolume6(),
					entity.getMonthlySearchVolume7(),
					entity.getMonthlySearchVolume8(),
					entity.getMonthlySearchVolume9(),
					entity.getMonthlySearchVolume10(),
					entity.getMonthlySearchVolume11(),
					entity.getMonthlySearchVolume12(),
					entity.getCpc(),
					entity.getKeywordVariationOnewordArray(),
					entity.getKeywordVariationNgramArray(),
					entity.getMonthlySvAttrKeys(),
					entity.getMonthlySvAttrValues(),
					entity.getCategoryArray(),
					lastRefreshMonth,
					entity.getHasSv() ? 1 : 0
			};
			batch.add(values);
		}

		int[] count = this.executeBatch(sql, batch);
	}

	public int[] insertBatchForUnRankcheck(List<KeywordTokenizerSearchvolumeEntity> list, String tableName) {
		String sql = "insert into " + tableName
			+ " (stream_value, keyword_name, word, stream, location_id, engine_id, language_id, sign, "
			+ " avg_search_volume, "
			+ " cpc,"
			+ " keyword_variation_oneword,"
			+ " keyword_variation_ngram,"
			+ " category"
			+ ") "
			+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?) ";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (KeywordTokenizerSearchvolumeEntity entity : list) {
			Object[] values = new Object[] {
					entity.getStream(),
					entity.getKeywordName(),
					entity.getWordArray(),
					entity.getStreamArray(),
					entity.getLocationId(),
					entity.getEngineId(),
					entity.getLanguageId(),
					entity.getSign(),
					entity.getAvgSearchVolume(),
					entity.getCpc(),
					entity.getKeywordVariationOnewordArray(),
					entity.getKeywordVariationNgramArray(),
					entity.getCategoryArray()
			};
			batch.add(values);
		}

		return this.executeBatch(sql, batch);
	}
	
	public void insertBatchForUnRankcheckWithEmptySv(List<KeywordTokenizerSearchvolumeEntity> list, String tableName) {
		String sql = "insert into " + tableName
			+ " (keyword_rankcheck_id, keyword_name, word, stream, location_id, engine_id, language_id, sign, versioning, "
			+ " avg_search_volume, "
			+ "monthly_search_volume1,"
			+ "monthly_search_volume2,"
			+ "monthly_search_volume3,"
			+ "monthly_search_volume4,"
			+ "monthly_search_volume5,"
			+ "monthly_search_volume6,"
			+ "monthly_search_volume7,"
			+ "monthly_search_volume8,"
			+ "monthly_search_volume9,"
			+ "monthly_search_volume10,"
			+ "monthly_search_volume11,"
			+ "monthly_search_volume12,"
			+ "cpc,"
			+ "keyword_variation_oneword,"
			+ "keyword_variation_ngram,"
			+ "category"
			+ ") "
			+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (KeywordTokenizerSearchvolumeEntity entity : list) {
			Object[] values = new Object[] {
					entity.getKeywordRankcheckId(),
					entity.getKeywordName(),
					entity.getWordArray(),
					entity.getStreamArray(),
					entity.getLocationId(),
					entity.getEngineId(),
					entity.getLanguageId(),
					entity.getSign(),
					entity.getVersioning(),
					entity.getAvgSearchVolume(),
					entity.getMonthlySearchVolume1(),
					entity.getMonthlySearchVolume2(),
					entity.getMonthlySearchVolume3(),
					entity.getMonthlySearchVolume4(),
					entity.getMonthlySearchVolume5(),
					entity.getMonthlySearchVolume6(),
					entity.getMonthlySearchVolume7(),
					entity.getMonthlySearchVolume8(),
					entity.getMonthlySearchVolume9(),
					entity.getMonthlySearchVolume10(),
					entity.getMonthlySearchVolume11(),
					entity.getMonthlySearchVolume12(),
					entity.getCpc(),
					entity.getKeywordVariationOnewordArray(),
					entity.getKeywordVariationNgramArray(),
					entity.getCategoryArray()
			};
			batch.add(values);
		}

		int[] count = this.executeBatch(sql, batch);
	}
	
	public List<KeywordTokenizerSearchvolumeEntity> getAllByPageFromOld(int engine, int language, long minId, int pageSize) {
		String sql= "select * from " + this.getTableName() + " where engine_id = ? and language_id = ? and keyword_rankcheck_id > ? order by keyword_rankcheck_id limit ? ";
		return this.findBySql(sql, engine, language, minId, pageSize);
	}
	
    
    public List<Map<String, Object>> getAllCategory() {
        String sql = "select * from keyword_categorey";
        return this.queryForMapList(sql);
    }

    public void insertBatchForUnRankcheckV2(List<KeywordTokenizerSearchvolumeEntity> list, String tableName) {
		String sql = "insert into " + tableName
			+ " (stream_value, keyword_name, word, stream, location_id, engine_id, language_id,"
			+ " avg_search_volume, "
			+ "cpc,"
			+ "keyword_variation_oneword,"
			+ "keyword_variation_ngram,"
			+ "category"
			+ ") "
			+ " values (?,?,?,?,?,?,?,?,?,?,?,?) ";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (KeywordTokenizerSearchvolumeEntity entity : list) {
			Object[] values = new Object[] {
					entity.getStreamValue(),
					entity.getKeywordName(),
					entity.getWordArray(),
					entity.getStreamArray(),
					entity.getLocationId(),
					entity.getEngineId(),
					entity.getLanguageId(),
					entity.getAvgSearchVolume(),
					entity.getCpc(),
					entity.getKeywordVariationOnewordArray(),
					entity.getKeywordVariationNgramArray(),
					entity.getCategoryArray()
			};
			batch.add(values);
		}

		int[] count = this.executeBatch(sql, batch);
	}

	public int[] insertBatch(List<Object[]> batch, String tableName, String sql) {
//		String sql = "insert into " + tableName
//				+ " (stream_value, keyword_name, word, stream, location_id, engine_id, language_id,"
//				+ " avg_search_volume, "
//				+ "cpc,"
//				+ "keyword_variation_oneword,"
//				+ "keyword_variation_ngram,"
//				+ "category"
//				+ ") "
//				+ " values (?,?,?,?,?,?,?,?,?,?,?,?) ";
//		List<Object[]> batch = new ArrayList<Object[]>();
//		for (KeywordTokenizerSearchvolumeEntity entity : list) {
//			Object[] values = new Object[] {
//					entity.getStreamValue(),
//					entity.getKeywordName(),
//					entity.getWordArray(),
//					entity.getStreamArray(),
//					entity.getLocationId(),
//					entity.getEngineId(),
//					entity.getLanguageId(),
//					entity.getAvgSearchVolume(),
//					entity.getCpc(),
//					entity.getKeywordVariationOnewordArray(),
//					entity.getKeywordVariationNgramArray(),
//					entity.getCategoryArray()
//			};
//			batch.add(values);
//		}
		return this.executeBatch(sql, batch);
	}
}
