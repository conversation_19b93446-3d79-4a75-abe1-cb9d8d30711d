package seoclarity.backend.dao.clickhouse.youtube;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

public abstract class ClYoutubeRankingBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
    DataSouceType type;

    @Resource(name="clarityDBYoutubeRankingDataSourceCdbRi307")
    protected DataSource dataSource;

    @Resource(name="clarityDBYoutubeRankingDataSourceCdbRi07")
    protected DataSource dataSource2;

    @Resource(name="clarityDBNj114YoutubeDateSource")
    protected DataSource dataSource3;

    @Override
    public DataSource getDataSource() {
        return null;
    }
}
