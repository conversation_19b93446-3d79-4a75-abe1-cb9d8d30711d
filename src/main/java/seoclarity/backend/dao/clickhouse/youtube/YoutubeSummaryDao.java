package seoclarity.backend.dao.clickhouse.youtube;

import cn.hutool.json.JSONUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import scala.Int;
import seoclarity.backend.entity.clickhouse.ved.YoutubeEntity;
import seoclarity.backend.utils.FormatUtils;

import javax.sql.DataSource;
import java.util.*;

@Repository
public class YoutubeSummaryDao extends ClYoutubeRankingBaseJdbcSupport<YoutubeEntity> {

    @Override
    public DataSource getDataSource() {
        try {
            if (type == null) {
                type = DataSouceType.CdbRi307;
            }
            if (type.name().equalsIgnoreCase(DataSouceType.CdbRi307.name())) {
                return dataSource;
            } else if (type.name().equalsIgnoreCase(DataSouceType.CdbRi114.name())) {
                return dataSource3;
            } else {
                return null;
            }
        } catch (Exception e) {
            System.out.println("type=========="+type);
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String getTableName() {
        return null;
    }

    public void changeDataSource(DataSouceType dataSouceType) {
        this.type = dataSouceType;
    }

    public Integer checkExistBatchV2(Integer engineId, Integer languageId, Integer ownDomainId, Integer locationId, Date rankingDate, Integer id) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct keyword_rankcheck_id from d_youtube_ranking_info                                       ");
        sql.append(" where own_domain_id=? and engine_id=? and language_id=? and location_id=? and ranking_date=? and       ");
        sql.append(" keyword_rankcheck_id =                                                                              ");
        sql.append( id + "                                               ");
        return this.queryForInteger(sql.toString(), ownDomainId, engineId, languageId, locationId, FormatUtils.formatDate(rankingDate, FormatUtils.DATE_PATTERN_2));
    }

    public Integer checkDetailExistBatchV2(Integer engineId, Integer languageId, Integer ownDomainId, Integer locationId, Date rankingDate, Integer id, int trueRank) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct keyword_rankcheck_id from d_youtube_ranking_detail                                       ");
        sql.append(" where own_domain_id=? and engine_id= ? and language_id= ? and location_id= ? and ranking_date= ?");
        sql.append(" and keyword_rankcheck_id = ? and true_rank = ? ");
        return this.queryForInteger(sql.toString(), ownDomainId, engineId, languageId, locationId, FormatUtils.formatDate(rankingDate, FormatUtils.DATE_PATTERN_2), id, trueRank);
    }


    public void insertYoutubeRankingInfo(YoutubeEntity youtubeEntity) {
        StringBuilder sql = new StringBuilder();
        sql.append(" insert into d_youtube_ranking_info                                                                         ");
        sql.append(" (keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id                    ");
        sql.append(" , ranking_date, avg_search_volume, cpc, count_of_search, sign)                                             ");
        sql.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?)                                                                             ");

        Object[] value = new Object[] {
                StringUtils.lowerCase(youtubeEntity.getKeyword()),
                youtubeEntity.getOwnDomainId(),
                youtubeEntity.getKeywordRankcheckId(),
                youtubeEntity.getEngineId(),
                youtubeEntity.getLanguageId(),
                youtubeEntity.getLocationId(),
                FormatUtils.formatDate(youtubeEntity.getRankingDate(), FormatUtils.DATE_PATTERN_2),
                youtubeEntity.getSearchVol(),
                youtubeEntity.getCpc(),
                youtubeEntity.getCountOfSearch(),
                youtubeEntity.getSign()
        };

        System.out.println(JSONUtil.toJsonStr(value));
        this.executeUpdate(sql.toString(), value);
    }


    public void insertYoutubeRankingInfoDetail(List<YoutubeEntity> youtubeEntities) {
        StringBuilder sql = new StringBuilder();

        sql.append(" insert into d_youtube_ranking_detail                                                                                               ");
        sql.append(" (keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id                                            ");
        sql.append(" , canonical_base_url, canonical_base_url_type, canonical_base_url_name, length_text, video_id,                                     ");
        sql.append(" channel_id, playlist_id, general_id, video_title, description_snippet, owner_text, badges, published_time, view_count,             ");
        sql.append(" rank_type_str, verify_status, subscriber_count_text, video_count_text, browse_id, website_text, ad_url,                            ");
        sql.append(" attrs.key, attrs.value, sign, true_rank, avg_search_volume, cpc, hrd, hrrd, ranking_date)                                          ");
        sql.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)                                                   ");


        List<Object[]> values = new ArrayList<>();
        int i = 1;
        for (YoutubeEntity youtubeEntity : youtubeEntities) {
            List<String> keyList = new ArrayList<>();
            List<String> valueList = new ArrayList<>();
            Integer hrrd = 0;
            youtubeEntity.setTrueRank(i);
            youtubeEntity.setHrrd(hrrd);
            youtubeEntity.setAttrsKey(keyList);
            youtubeEntity.setAttrsvalue(valueList);
            Object[] value = new Object[] {
                    StringUtils.lowerCase(youtubeEntity.getKeyword()),
                    youtubeEntity.getOwnDomainId(),
                    youtubeEntity.getKeywordRankcheckId(),
                    youtubeEntity.getEngineId(),
                    youtubeEntity.getLanguageId(),
                    youtubeEntity.getLocationId(),
                    youtubeEntity.getCanonicalBaseUrl(),
                    youtubeEntity.getCanonicalBaseUrlType(),
                    youtubeEntity.getCanonicalBaseUrlName(),
                    youtubeEntity.getVideolength(),
                    youtubeEntity.getVideoId(),
                    youtubeEntity.getChannelId(),
                    youtubeEntity.getPlaylistId(),
                    youtubeEntity.getGeneralId(),
                    youtubeEntity.getYoutubeTitle(),
                    youtubeEntity.getDescriptionSnippet(),
                    youtubeEntity.getAuthor(),
                    youtubeEntity.getBadges(),
                    youtubeEntity.getPublishedTime(),
                    youtubeEntity.getViewCount(),
                    youtubeEntity.getRankType(),
                    youtubeEntity.getVerifyStatus(),
                    youtubeEntity.getSubscriberCount(),
                    youtubeEntity.getVideoCount(),
                    youtubeEntity.getBrowseId(),
                    youtubeEntity.getWebsiteText(),
                    youtubeEntity.getAdUrl(),
                    youtubeEntity.getAttrsKey(),
                    youtubeEntity.getAttrsvalue(),
                    youtubeEntity.getSign(),
                    youtubeEntity.getTrueRank(),
                    youtubeEntity.getSearchVol(),
                    youtubeEntity.getCpc(),
                    youtubeEntity.getHrd(),
                    youtubeEntity.getHrrd(),
                    FormatUtils.formatDate(youtubeEntity.getRankingDate(), FormatUtils.DATE_PATTERN_2)
            };

//            Integer existId = checkDetailExistBatchV2(youtubeEntity.getEngineId(), youtubeEntity.getLanguageId(), youtubeEntity.getOwnDomainId(), youtubeEntity.getLocationId(), youtubeEntity.getRankingDate(), youtubeEntity.getKeywordRankcheckId(), i);
//            if(existId == null) {
//                values.add(value);
//                System.out.println(JSONUtil.toJsonStr(value));
                this.executeUpdate(sql.toString(), value);
//            } else {
//                System.out.println("Exist Detail = "+youtubeEntity.getOwnDomainId()+" - "+youtubeEntity.getKeywordRankcheckId()+" - "+i);
//            }
            i++;
        }

//        if(values.size() > 0) {
//            System.out.println(JSONUtil.toJsonStr(values.get(0)));
//            this.executeBatch(sql.toString(), values);
//        }
    }

    public List<Map<String,Object>> getYoutubeIndoAsc(String dateStr, List<Integer> domainList) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select                                                                 ");
        sql.append(" keyword_name AS Keyword_Name, 'US' as Country, 'EN' as Language,       ");
        sql.append(" ranking_date as Date,                                                  ");
        sql.append(" avg_search_volume as Search_Volume,                                    ");
        sql.append(" cpc as CPC,                                                            ");
        sql.append(" true_rank as Rank,                                                     ");
        sql.append(" replaceAll(video_title, '\\n', '') as Title,                           ");
        sql.append(" published_time as Published_Time,                                      ");
        sql.append(" rank_type_str as Type,                                                 ");
        sql.append(" verify_status as Verified,                                             ");
        sql.append(" length_text as Video_Length,                                           ");
        sql.append(" subscriber_count_text as Subscriber_Count,                             ");
        sql.append(" replaceAll(description_snippet, '\\n', '') as Description_Snippet,     ");
        sql.append(" owner_text as Author,                                                  ");
        sql.append(" badges as Video_Tag,                                                   ");
        sql.append(" view_count as View_Count,                                              ");
        sql.append(" video_count_text as Video_Count,                                       ");
        sql.append(" general_id as Id,                                                      ");
        sql.append(" browse_id as browseId,                                                 ");
        sql.append(" website_text as AD_Text,                                               ");
        sql.append(" ad_url as AD_Url,                                                      ");
        sql.append(" canonical_base_url as Canonical_Base_Url,                              ");
        sql.append(" canonical_base_url_type as Canonical_Base_Url_Type,                    ");
        sql.append(" canonical_base_url_name as Canonical_Base_Url_Nname                    ");
        sql.append(" FROM d_youtube_ranking_detail                                          ");
        sql.append(" where ranking_date = ? and own_domain_id in (" + StringUtils.join(domainList, ',') + ") order by keyword_name, true_rank asc            ");
        return queryForMapList(sql.toString(), dateStr);
    }

    public List<YoutubeEntity> getYoutubeDetail(int ownDomainId, String dateStr) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select keyword_name as keyword, length_text as videolength, video_id, channel_id, playlist_id, general_id, video_title, description_snippet,");
        sql.append(" owner_text, badges as videoTag, published_time, view_count, rank_type_str as rankType, verify_status, subscriber_count_text as subscriberCount,");
        sql.append(" video_count_text as videoCount, true_rank, ranking_date as rankDateStr FROM d_youtube_ranking_detail where own_domain_id = ? and ranking_date = ? ");
        System.out.println("==SQL:" + sql.toString());
        return findBySql(sql.toString(), ownDomainId, dateStr);
    }

    public List<YoutubeEntity> getExtractYoutubeDetail(int ownDomainId, int engineId, int languageId, String dateStr,
                                                       List<Long> rcIdList, List<String> videoIdList) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select keyword_name as keyword,ranking_date,keyword_rankcheck_id,true_rank,video_id ");
        sql.append(" from d_youtube_ranking_detail ");
        sql.append(" where own_domain_id = ? and engine_id= ? and language_id=? and location_id=0 and ranking_date = ? ");
        sql.append(" and keyword_rankcheck_id in (" + StringUtils.join(rcIdList, ",")).append(") ");
        sql.append(" and video_id in ('" + StringUtils.join(videoIdList, "','")).append("') ");
        System.out.println("==SQL:" + sql.toString());
        return findBySql(sql.toString(), ownDomainId, engineId, languageId, dateStr);
    }


}