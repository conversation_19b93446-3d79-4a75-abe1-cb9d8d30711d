package seoclarity.backend.dao.clickhouse.siteclarity;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.upload.siteclarity.claritydb.ClarityDBHandler;
import seoclarity.backend.upload.siteclarity.kafka.FilterCrawlDateVo;
import seoclarity.backend.upload.siteclarity.vendor.Constants;
import seoclarity.backend.upload.siteclarity.vendor.DocumentBatch;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Repository
public class ClSiteClarityDao extends ClSiteClarityJdbcSupport<Map> {

    @Override
    public String getTableName() {
        return null;
    }

    public void batchInsert(DocumentBatch documentBatch) {
        List<String> fields = documentBatch.getFields();
        String insertSQL = String.format(Constants.SQL_INSERT_QUERY_TEMPLATE,
                documentBatch.getTable(), String.join(Constants.DEFAULT_DELIMITER, fields), new ClarityDBHandler().buildMarkers(documentBatch.getFields()));
        List<Map<String, Object>> batchData = documentBatch.getRecords();

        List<Object[]> batch = new ArrayList<>();
        for (Map<String, Object> record : batchData) {
            Object[] objects = new Object[fields.size()];
            for (int i = 0; i < fields.size(); i++) {
                String field = fields.get(i);
                if (!record.containsKey(field)) {
                    logger.info("Missing field value " + field);
                }
                Object value = record.get(field);
                if (field.equals(Constants.ROBOTS_CONTENTS_X_TAG)) {
                    value = Constants.ZERO;
                }
                if (field.equals("crawl_request_date")) {
                    try {
                        java.util.Date dateObj = new SimpleDateFormat("yyyy-MM-dd").parse(value.toString());
                        value = new Date(dateObj.getTime());
                    } catch (Exception e) {
                        logger.error("Failed to set crawl_request_date | value " + value);
                        value = new Date(0);
                    }
                }
                objects[i] = value;
            }
            batch.add(objects);
        }
        executeBatch(insertSQL, batch);
    }

}
