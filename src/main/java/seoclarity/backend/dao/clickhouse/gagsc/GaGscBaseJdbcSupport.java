/**
 *
 */
package seoclarity.backend.dao.clickhouse.gagsc;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class GaGscBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDBGaGscDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
