package seoclarity.backend.dao.clickhouse.clarity360;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.clarity360.lweb01.Clarity360LwebBaseJdbcSupport01;
import seoclarity.backend.entity.actonia.bot.BotGroup;
import seoclarity.backend.entity.clarity360.UrlAttributesFlatEntity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class UrlAttributesFlatDao extends Clarity360LwebBaseJdbcSupport01<UrlAttributesFlatEntity> {
    @Override
    public String getTableName() {
        return "url_attributes_flat";
    }

    public void insertBatch(List<UrlAttributesFlatEntity> urlFlatList) {
        String sql = "insert into " + getTableName() + " (url_murmur_hash, url, attr.keys, attr.values) values (?, ?, ?, ?) ";
        List<Object[]> batch = new ArrayList<>();
        for (UrlAttributesFlatEntity entity : urlFlatList) {
            Object[] values = new Object[] {
                    entity.getUrlMurmurHash(),
                    entity.getUrl(),
                    entity.getAttrKeys(),
                    entity.getAttrValues()
            };
            batch.add(values);
        }
        this.executeBatch(sql, batch);
    }
}
