package seoclarity.backend.dao.clickhouse.clarity360.lweb01;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.summary.Clarity360Entity;

@Repository
public class Clarity360Lweb01DAO extends Clarity360LwebBaseJdbcSupport01<Clarity360Entity> {


    @Override
    public String getTableName() {
        return "local_360_table_v3";
    }

    public void optimizeTable(String month){
        String sql = "optimize table clarity360." + getTableName() + " partition " + month + " final";
        System.out.println(sql);
        this.executeUpdate(sql);
    }

    
    public Boolean isDataExistByOidAndMonth(Integer domainId, String month) {
        String sql = " select count(1)  " +
                " from dis_clarity360_v2 where domain_id=? and year_month=? " ;
        return queryForInteger(sql, domainId, month) > 0;
    }
    
    public String getMaxMonth(Integer domainId) {
        String sql = " select max(year_month)  " +
                " from dis_clarity360_v2 where domain_id=? " ;
        return queryForString(sql, domainId);
    }
    
    public void optimizeTable(String tempTableName, String targetDate){
        String sql = "optimize table clarity360." + tempTableName + " partition '" + targetDate + "' final";
        System.out.println(sql);
        this.executeUpdate(sql);
    }
    

    // vector_db.dis_vector_table
    public List<String> getUrls() {
        String sql = "select url from vector_db.dis_vector_table where crawl_request_id= 9946428";
        return this.queryForStringList(sql);
    }

    public List<Map<String, Object>> getUrlAndDist(String url) {
        String sql = "" +
                "SELECT " +
                "    url, " +
                "    dist " +
                "FROM vector_db.dis_vector_table " +
                "WHERE crawl_request_id = 9946428 " +
                "ORDER BY L2Distance(title_embedding, ( " +
                "        SELECT title_embedding " +
                "        FROM vector_db.dis_vector_table " +
                "        WHERE (crawl_request_id = 9946428) AND (url = ?) " +
                "    )) AS dist ASC " +
                "LIMIT 6";
        return this.queryForMapList(sql, url);
    }
}
