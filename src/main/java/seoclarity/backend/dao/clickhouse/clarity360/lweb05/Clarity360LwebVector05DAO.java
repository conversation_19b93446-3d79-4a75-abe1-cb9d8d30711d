package seoclarity.backend.dao.clickhouse.clarity360.lweb05;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;

import seoclarity.backend.summary.Clarity360Entity;
import seoclarity.backend.vectordb.EmbeddingEntity;

@Repository
public class Clarity360LwebVector05DAO extends Clarity360LwebVectorBaseJdbcSupport05<Clarity360Entity> {


    @Override
    public String getTableName() {
        return "local_vector_table";
    }
    
    


	public void insertBatch(List<EmbeddingEntity> insertData, String tableName) {
        String sql = "INSERT INTO " + tableName
                + " (crawl_request_id, crawl_request_date, domain_id, url,"
                + " title, meta, h1, h2, full_text, custom_data_1, custom_data_2, custom_data_3, custom_data_4, custom_data_5, "
                + " title_embedding, meta_embedding, full_text_embedding, h1_embedding, h2_embedding, custom_data_1_embedding,  "
                + " custom_data_2_embedding, custom_data_3_embedding, custom_data_4_embedding, custom_data_5_embedding"
                + ") VALUES (?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (EmbeddingEntity entity : insertData) {
            Object[] values = new Object[]{
                    entity.getCrawlRequestId(),
                    entity.getCrawlRequestDate(),
                    entity.getDomainId(),
                    entity.getUrl(),
                    entity.getTitle(),
                    
                    entity.getMeta(),
                    entity.getH1(),
                    entity.getH2(),
                    entity.getFullText(),
                    entity.getCustomData1(),
                    
                    entity.getCustomData2(),
                    entity.getCustomData3(),
                    entity.getCustomData4(),
                    entity.getCustomData5(),
                    entity.getTitleEmbedding(),
                    
                    entity.getMetaEmbedding(),
                    entity.getH1Embedding(),
                    entity.getH2Embedding(),
                    entity.getFullTextEmbedding(),
                    entity.getCustomData1Embedding(),
                    
                    entity.getCustomData2Embedding(),
                    entity.getCustomData3Embedding(),
                    entity.getCustomData4Embedding(),
                    entity.getCustomData5Embedding()
            };

//            if (i++ < 5) {
//                System.out.println(new Gson().toJson(values));
//            }

            batch.add(values);
        }
        executeBatch(sql, batch);
    }

	

	public void insertBatchOnetimeScript(List<EmbeddingEntity> insertData, String tableName) {
        String sql = "INSERT INTO " + tableName
                + " (url, title, meta, title_embedding, meta_embedding) VALUES (?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (EmbeddingEntity entity : insertData) {
            Object[] values = new Object[]{
                    entity.getUrl(),
                    entity.getTitle(),
                    entity.getMeta(),
                    entity.getTitleEmbedding(),
                    entity.getMetaEmbedding()
            };

//            if (i++ < 5) {
//                System.out.println(new Gson().toJson(values));
//            }

            batch.add(values);
        }
        executeBatch(sql, batch);
    }
}
