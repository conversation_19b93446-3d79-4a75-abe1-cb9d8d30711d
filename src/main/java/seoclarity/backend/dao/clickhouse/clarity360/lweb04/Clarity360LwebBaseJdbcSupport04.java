package seoclarity.backend.dao.clickhouse.clarity360.lweb04;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * 
 * <AUTHOR>
 *
 * @param <T>
 */
public abstract class Clarity360LwebBaseJdbcSupport04<T> extends BaseJdbcSupport<T> {
    @Resource(name="clarityDB360SummaryDataSourceCdb004")
    private DataSource dataSource;

    public DataSource getDataSource() {
        return this.dataSource;
    }


}
