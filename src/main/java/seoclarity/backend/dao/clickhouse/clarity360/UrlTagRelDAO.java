package seoclarity.backend.dao.clickhouse.clarity360;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360LwebBaseJdbcSupport05;
import seoclarity.backend.entity.clarity360.UrlTagRelEntity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class UrlTagRelDAO extends Clarity360LwebBaseJdbcSupport05<UrlTagRelEntity> {


    @Override
    public String getTableName() {
        return "dis_url_tag_rel";
    }

	public void batchAdd(List<UrlTagRelEntity> groupList) {
		String sql = "INSERT INTO " + getTableName() + " (domain_id, file_id, url, tag, parent_tag, is_tag_has_child) " +
				"VALUES (?, ?, ?, ?, ?, ?)";
		List<Object[]> batch = new ArrayList<>();
		for (UrlTagRelEntity  groupEntity : groupList) {
			Object[] obj = new Object[]{
					groupEntity.getDomainId(),
					groupEntity.getFileId(),
					groupEntity.getUrl(),
					groupEntity.getTag(),
					groupEntity.getParentTag(),
					groupEntity.getIsTagHasChild()
			};
			batch.add(obj);
		}
		executeBatch(sql, batch);
	}
}
