package seoclarity.backend.dao.clickhouse.clarity360.lweb03;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * 
 * <AUTHOR>
 *
 * @param <T>
 */
public abstract class Clarity360LwebBaseJdbcSupport03<T> extends BaseJdbcSupport<T> {
    @Resource(name="clarityDB360SummaryDataSourceCdb003")
    private DataSource dataSource;

    public DataSource getDataSource() {
        return this.dataSource;
    }


}
