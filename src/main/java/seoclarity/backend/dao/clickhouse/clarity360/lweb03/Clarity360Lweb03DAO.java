package seoclarity.backend.dao.clickhouse.clarity360.lweb03;

import org.springframework.stereotype.Repository;

import seoclarity.backend.summary.Clarity360Entity;

@Repository
public class Clarity360Lweb03DAO extends Clarity360LwebBaseJdbcSupport03<Clarity360Entity> {


    @Override
    public String getTableName() {
        return "local_360_table_v3";
    }

    public void optimizeTable(String month){
        String sql = "optimize table clarity360." + getTableName() + " partition " + month + " final";
        System.out.println(sql);
        this.executeUpdate(sql);
    }
    
    public void optimizeTable(String tempTableName, String targetDate){
        String sql = "optimize table clarity360." + tempTableName + " partition '" + targetDate + "' final";
        System.out.println(sql);
        this.executeUpdate(sql);
    }

}
