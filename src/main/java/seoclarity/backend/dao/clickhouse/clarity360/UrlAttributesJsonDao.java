package seoclarity.backend.dao.clickhouse.clarity360;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.clarity360.lweb01.Clarity360LwebBaseJdbcSupport01;
import seoclarity.backend.entity.clarity360.UrlAttributesFlatEntity;
import seoclarity.backend.entity.clarity360.UrlAttributesJsonEntity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class UrlAttributesJsonDao extends Clarity360LwebBaseJdbcSupport01<UrlAttributesJsonEntity> {
    @Override
    public String getTableName() {
        return "url_attributes_json";
    }

    public void insertBatch(List<UrlAttributesJsonEntity> urlJsonList) {
        String sql = "insert into " + getTableName() + " (url_murmur_hash, url, json) values (?, ?, ?) ";
        List<Object[]> batch = new ArrayList<>();
        for (UrlAttributesJsonEntity entity : urlJsonList) {
            Object[] values = new Object[] {
                    entity.getUrlMurmurHash(),
                    entity.getUrl(),
                    entity.getJson()
            };
            batch.add(values);
        }
        this.executeBatch(sql, batch);
    }
}
