package seoclarity.backend.dao.clickhouse.socialengine;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.clickhouse.amazon.AmazonAdsEntity;
import seoclarity.backend.entity.clickhouse.amazon.AmazonDetailEntity;
import seoclarity.backend.entity.clickhouse.amazon.AmazonInfoEntity;

@Repository
public class AmazonRankingColdEntityDAO extends SocialEngineColdBaseJdbcSupport<AmazonDetailEntity> {

    public String getTableName() {
        return null;
    }

    public void insertForBatchAmazonInfo(List<AmazonInfoEntity> insertData, String tableName) {
    	
    	String sql = "INSERT INTO "+ tableName + "(device, keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, ranking_date, "
    			+ " attrstr.key, attrstr.value, attrint.key, attrint.value) "
    			+ "VALUES (?,?,?,?,?, ?,?,?,?,?, ?)";
    	
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (AmazonInfoEntity entity : insertData) {
            Object[] values;
			values = new Object[] {
				entity.getDevice(),
				entity.getKeywordName(),
				entity.getOwnDomainId(),
				entity.getKeywordRankcheckId(),
				entity.getEngineId(),
				
				entity.getLanguageId(),
				entity.getRankingDate(),
				entity.getAttrstrkey(),
				entity.getAttrstrvalue(),
				entity.getAttrintkey(),
				
				entity.getAttrintvalue()
			};
			
			if(i++ < 3) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);

            
        }
        executeBatch(sql, batch);
    }
    
    public void insertForBatchAmazonDetail(List<AmazonDetailEntity> insertData, String tableName) {
    	
    	String sql = "INSERT INTO "+ tableName + "(device, keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, "
    			+ "ranking_date, rank, type, asin, image_url, url, title, rating, rate_number, prime_flag, price, history_price, "
    			+ "additionalInfo, attrstr.key, attrstr.value, attrint.key, attrint.value) "
    			+ "VALUES (?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?)";
    	
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (AmazonDetailEntity entity : insertData) {
            Object[] values;
			values = new Object[] {
				entity.getDevice(),
				entity.getKeywordName(),
				entity.getOwnDomainId(),
				entity.getKeywordRankcheckId(),
				entity.getEngineId(),
				
				entity.getLanguageId(),
				entity.getRankingDate(),
				entity.getRank(),
				entity.getType(),
				entity.getAsin(),
				
				entity.getImageUrl(),
				entity.getUrl(),
				entity.getTitle(),
				entity.getRating(),
				entity.getRateNumber(),
				
				entity.getPrimeFlag(),
				entity.getNowPrice(),
				entity.getHistoryPrice(),
				entity.getAdditionalInfo(),
				entity.getAttrstrkey(),
				
				entity.getAttrstrvalue(),
				entity.getAttrintkey(),
				entity.getAttrintvalue()
				
			};
			
			if(i++ < 3) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);

            
        }
        executeBatch(sql, batch);
    }
    
   public void insertForBatchAmazonAds(List<AmazonAdsEntity> insertData, String tableName) {
    	
    	String sql = "INSERT INTO "+ tableName + "(device, keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, ranking_date, "
    			+ "rank, title, link, image, asin, sponsored_flag, prime_flag, fresh_flag, rating, rate_number, price, history_price) "
    			+ "VALUES (?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?)";
    	
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (AmazonAdsEntity entity : insertData) {
            Object[] values;
			values = new Object[] {
				entity.getDevice(),
				entity.getKeywordName(),
				entity.getOwnDomainId(),
				entity.getKeywordRankcheckId(),
				entity.getEngineId(),
				
				entity.getLanguageId(),
				entity.getRankingDate(),
				entity.getRank(),
				entity.getTitle(),
				entity.getLink(),
				
				entity.getImage(),
				entity.getAsin(),
				entity.getAdsFlag(),
				entity.getPrimeFlag(),
				entity.getFreshFlag(),
				
				entity.getRating(),
				entity.getRateNumber(),
				entity.getPrice(),
				entity.getHistoryPrice()
			};
			
			if(i++ < 3) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);

            
        }
        executeBatch(sql, batch);
    }
    
}
