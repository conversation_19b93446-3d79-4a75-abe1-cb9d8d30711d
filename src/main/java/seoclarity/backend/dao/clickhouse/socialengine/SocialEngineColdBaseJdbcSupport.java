/**
 *
 */
package seoclarity.backend.dao.clickhouse.socialengine;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class SocialEngineColdBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="socialEngineColdBaseJdbcSupport")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
