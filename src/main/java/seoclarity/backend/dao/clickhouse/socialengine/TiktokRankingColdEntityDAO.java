package seoclarity.backend.dao.clickhouse.socialengine;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.entity.clickhouse.tiktok.TiktokDetailEntity;
import seoclarity.backend.entity.clickhouse.tiktok.TiktokInfoEntity;

@Repository
public class TiktokRankingColdEntityDAO extends SocialEngineColdBaseJdbcSupport<TiktokDetailEntity> {

    public String getTableName() {
        return null;
    }

    public void insertForBatchTiktokInfo(List<TiktokInfoEntity> insertData, String tableName) {
    	
    	String sql = "INSERT INTO "+ tableName + "(keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id, ranking_date, "
    			+ " frequency, rel_id, device) "
    			+ "VALUES (?,?,?,?,?, ?,?,?,?,?)";
    	
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (TiktokInfoEntity entity : insertData) {
            Object[] values;
			values = new Object[] {
				entity.getKeywordName(),
				entity.getOwnDomainId(),
				entity.getKeywordRankcheckId(),
				entity.getEngineId(),
				entity.getLanguageId(),
				
				entity.getLocationId(),
				entity.getRankingDate(),
				entity.getFrequency(),
				entity.getRelId(),
				entity.getDevice(),
			};
			
			if(i++ < 3) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);

            
        }
        executeBatch(sql, batch);
    }
    
    public void insertForBatchTiktokDetail(List<TiktokDetailEntity> insertData, String tableName) {
    	
    	String sql = "INSERT INTO "+ tableName + "(keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id, ranking_date, rank, "
    			+ "author_unique_id, author_nickname, author_status, video_desc, video_id, video_uri, video_duration, video_stats, frequency, rel_id, device) "
    			+ "VALUES (?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?)";
    	
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (TiktokDetailEntity entity : insertData) {
            Object[] values;
			values = new Object[] {
				entity.getKeywordName(),
				entity.getOwnDomainId(),
				entity.getKeywordRankcheckId(),
				entity.getEngineId(),
				entity.getLanguageId(),
				
				entity.getLocationId(),
				entity.getRankingDate(),
				entity.getRank(),
				entity.getAuthorUniqueId(),
				entity.getAuthorNickname(),
				
				entity.getAuthorStatus(),
				entity.getVideoDesc(),
				entity.getVideoId(),
				entity.getVideoUri(),
				entity.getVideoDuration(),
				
				entity.getVideoStats(),
				entity.getFrequency(),
				entity.getRelId(),
				entity.getDevice(),
				
			};
			
			if(i++ < 3) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);

            
        }
        executeBatch(sql, batch);
    }
    
    public List<TiktokDetailEntity> getUniqKeyForMonitor(String tableName, String rankingDate) {

        StringBuilder sql = new StringBuilder();
        sql.append("  SELECT  ");
        sql.append("   own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id, device  ");
        sql.append("  from " + tableName);
        sql.append("  WHERE ranking_date = '" + rankingDate + "'  ");
        sql.append("  group by own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id, device  ");
        return findBySql(sql.toString());
    }
    
}
