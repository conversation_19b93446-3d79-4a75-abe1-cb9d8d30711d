package seoclarity.backend.dao.clickhouse;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.utils.SpringBeanFactory;

/**
 * <AUTHOR>
 * @date 2020-04-01
 * @path seoclarity.backend.dao.clickhouse.RIDailyRankingService
 * 
 */
public class RIDailyRankingService {
	public static final String TABLE_INFO_TABLE = "ranking_info";
	public static final String TABLE_DETAIL_TYPE = "ranking_detail";
	public static final String TABLE_SUBRANK_TYPE = "ranking_subrank";
	private static final String DATABASE_NAME = "seo_daily_ranking";
	private boolean isHot = true;

	private ClDailyRankingEntityDao clDailyRankingEntityDao;
//	private ClColdDailyRankingEntityDao clColdDailyRankingEntityDao;

	public RIDailyRankingService(boolean isHot) {
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
	}

	public static String getTable(String tableType, int engine, int language, boolean isMobile, boolean isAllInOne) {
		String tableName = "";
		String prefix = isMobile ? "m_" : "d_";
		if (isAllInOne) {
			prefix = "all_in_one_" + prefix;
			tableName = prefix + StringUtils.removeStart(tableType, "ranking_");
		} else {
			tableName = prefix + tableType;
		}
		if (engine == 1 && language == 1) {
			tableName = tableName + "_us";
		} else {
			tableName = tableName + "_intl";
		}
		return DATABASE_NAME + "." + tableName;
	}
	
	
	public List<String> queryForList(String sql) {
		try {
			return clDailyRankingEntityDao.queryForList(sql);
		} catch (Exception e) {
			System.out.println("SQL FAILED, sql:" + sql);
			e.printStackTrace();
			return null;
		}
	}
	
	public List<Map<String, Object>> queryForAll(String sql) {
		return clDailyRankingEntityDao.queryForAll(sql);
	}
	

}
