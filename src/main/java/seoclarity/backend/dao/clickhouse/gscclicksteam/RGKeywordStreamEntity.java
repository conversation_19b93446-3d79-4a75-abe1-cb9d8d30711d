package seoclarity.backend.dao.clickhouse.gscclicksteam;

import java.util.Date;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.clickhouse.gscclicksteam.defaultDB
 * @author: cil
 * @date: 2021-07-14 18:09
 **/
public class RGKeywordStreamEntity {
    private String keywordName;
    private String key;
    private Float avgSearchVolme;
    private Integer dayOfCount;
    private Integer sign;
    private Date createDate;
    private String searchText;
    //upcase
    private String countryCode;
    //Hao --https://www.wrike.com/open.htm?id=1097358460
    private String trueDemand;
    private int inRG;
    public String getKeywordName() {
        return keywordName;
    }

    public void setKeywordName(String keywordName) {
        this.keywordName = keywordName;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Float getAvgSearchVolme() {
        return avgSearchVolme;
    }

    public void setAvgSearchVolme(Float avgSearchVolme) {
        this.avgSearchVolme = avgSearchVolme;
    }

    public Integer getDayOfCount() {
        return dayOfCount;
    }

    public void setDayOfCount(Integer dayOfCount) {
        this.dayOfCount = dayOfCount;
    }

    public Integer getSign() {
        return sign;
    }

    public void setSign(Integer sign) {
        this.sign = sign;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getSearchText() {
        return searchText;
    }

    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTrueDemand() {
        return trueDemand;
    }

    public void setTrueDemand(String trueDemand) {
        this.trueDemand = trueDemand;
    }

    public int getInRG() {
        return inRG;
    }

    public void setInRG(int inRG) {
        this.inRG = inRG;
    }
}
