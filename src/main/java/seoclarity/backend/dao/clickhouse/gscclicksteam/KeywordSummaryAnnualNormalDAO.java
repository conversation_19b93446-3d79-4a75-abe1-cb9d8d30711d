package seoclarity.backend.dao.clickhouse.gscclicksteam;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.ContentJsonPOJO;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;
import seoclarity.backend.upload.Utils.StringUtilsTool;

import java.util.List;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.clickhouse.gscclicksteam
 * @author: cil
 * @date: 2021-07-08 15:12
 **/
@Repository
public class KeywordSummaryAnnualNormalDAO extends ClGscClickSteamBaseJdbcSupport<KeywordStreamEntity> {
    @Override
    public String getTableName() {
        return "dis_keyword_summary_annual_normal";
    }

    public List<KeywordStreamEntity> getKeywordNameAndAvgSearchVolume(ContentJsonPOJO contentJsonPOJO) {
        StringBuffer sqlBuffer = new StringBuffer("SELECT distinct keyword_name as keywordName,avg_search_volume as avgSearchVolume " +
                "from gsc_clickstream.dis_keyword_summary_annual_normal " +
                "where country_cd= ? and count_of_days>1 and keyword_hash global " +
                "in ( select distinct keyword_hash from gsc_clickstream.dis_keyword_stream where country_cd= ?  " +
                "AND length(stream) < 10 ");
        for (String word : contentJsonPOJO.getWord()) {
            sqlBuffer.append("AND (has(stream, '").append(StringUtilsTool.replaceSingleQuote(word)).append("')) ");
        }
        sqlBuffer.append(
                " AND NOT ( match(keyword_name,'.*[\\\\[\\\\]\\\\(\\\\){}_?!,:;~\"=！，。【】（）——“”’@&\\'<>$%\\*\\\\^\\+￼`]+.*') " +
                        " or (match(keyword_name,'.*[¡¢£¤¥¦§¨©ª«¬®¯–—‘’‚“”„†‡•…‰€™°±²³´µ¶·¸¹º»¼½¾¿]+.*')))  " +
                        ") order by avg_search_volume desc limit 1000 SETTINGS max_bytes_before_external_group_by=10000000000, " +
                        "max_bytes_before_external_sort=10000000000;");
        String sql = sqlBuffer.toString();
        System.out.println(sql);
        return findBySql(sql, contentJsonPOJO.getCountryCode(), contentJsonPOJO.getCountryCode());
    }

}
