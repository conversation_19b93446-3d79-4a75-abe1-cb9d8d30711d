package seoclarity.backend.dao.clickhouse.gscclicksteam;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordGscClickstreamSummaryEntity;

import java.util.List;

@Repository
public class KeywordGscClickstreamSummaryDAO extends ClGscClickSteamBaseJdbcSupport<KeywordGscClickstreamSummaryEntity> {

    @Override
    public String getTableName() {
        return "dis_keyword_gsc_clickstream_summary";
    }

    public List<KeywordGscClickstreamSummaryEntity> getKeywordGscClickstreamSummaryEntityList(String countryCd, String ... args) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select keyword_name as keywordName from ").append(getTableName());
        sbd.append(" where 1 = 1");
        if (args.length < 2) {
            return null;
        }
        for (String arg : args) {
            sbd.append(" and keyword_name like '%").append(arg).append("%'");
        }
        sbd.append(" and country_cd = ?");
        sbd.append(" order by avg_search_volume desc limit 100 ");
        System.out.println("sql: " + sbd.toString());
        return this.findBySql(sbd.toString(), countryCd);
    }

}
