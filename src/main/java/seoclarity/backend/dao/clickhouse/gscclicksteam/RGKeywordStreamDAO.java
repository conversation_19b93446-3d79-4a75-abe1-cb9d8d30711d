package seoclarity.backend.dao.clickhouse.gscclicksteam;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.gscclicksteam.ClGscClickDefaultBaseJdbcSupport;
import seoclarity.backend.dao.clickhouse.gscclicksteam.RGKeywordStreamEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.utils.FormatUtils;

import java.util.*;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.clickhouse.gscclicksteam.defaultDB
 * @author: cil
 * @date: 2021-07-21 09:54
 **/
@Repository
public class RGKeywordStreamDAO extends ClGscClickDefaultBaseJdbcSupport<RGKeywordStreamEntity> {
    @Override
    public String getTableName() {
        return "local_rg_keyword_stream";
    }



    public List<Integer> findKeyHash(String countryCd) {
        String sql = "select distinct key_hash_mod from " +
                "default.local_rg_keyword_stream where country_cd= ? order by key_hash_mod;";
        return this.queryForIntegerList(sql,countryCd);
    }


    public List<RGKeywordStreamEntity> findByKeyHash(int keyHash,String countryCd) {
        String sql = "select keyword_name as keywordName,key,searchText,avg_search_volme as avgSearchVolme ," +
                " day_of_count as dayOfCount,country_cd as countryCode" +
                " from default.local_rg_keyword_stream where country_cd= ? and key_hash_mod= ? order by key;";
        return this.findBySql(sql,countryCd, keyHash);
    }

    public void insertKeywordStreamForBatch(Set<RGKeywordStreamEntity> insertData) {
        String sql = "INSERT INTO local_rg_keyword_stream "
                + " (keyword_name,searchText,key,country_cd) "
                + " VALUES (?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (RGKeywordStreamEntity entity : insertData) {
            Object[] values = new Object[]{
                    entity.getKeywordName(),
                    entity.getSearchText(),
                    entity.getKey(),
                    entity.getCountryCode()

            };

            batch.add(values);
        }
        executeBatch(sql, batch);
    }


    public String createTableByDatetime(String tableName) {
        String sql = "create table default." + tableName + "(keyword_name String, rankcheck_keyword_id UInt32, createDate Date default today() ) "
                + "   ENGINE = MergeTree() ORDER BY (keyword_name) SETTINGS index_granularity=8192; ";
        System.out.println("sql:" + sql);
        this.executeUpdate(sql);
        return tableName;
    }

    public void insertKeyword(List<SeoClarityKeywordEntity> insertData,String table) {
        String sql = "INSERT INTO  default."+table
                + " (keyword_name,rankcheck_keyword_id) "
                + " VALUES (?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (SeoClarityKeywordEntity entity : insertData) {
            Object[] values = new Object[]{
                    entity.getKeywordText(),
                    entity.getId()
            };

            batch.add(values);
        }
        executeBatch(sql, batch);
    }

}
