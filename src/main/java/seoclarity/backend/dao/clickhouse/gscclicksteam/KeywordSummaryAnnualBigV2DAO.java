package seoclarity.backend.dao.clickhouse.gscclicksteam;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;

import java.util.List;

@Repository
public class KeywordSummaryAnnualBigV2DAO extends ClGscClickSteamBaseJdbcSupport<KeywordStreamEntity>{
    @Override
    public String getTableName() {
        return "dis_keyword_summary_annual_big_v2";
    }

    public List<String> getDistinctCountryCode() {
        String sql = "select distinct country_cd from gsc_clickstream.dis_keyword_summary_annual_big_v2 order by country_cd";
        return queryForStringList(sql);
    }

    public String getQueryParam(String keywordHash, String murmur3Hash, String countryCode) {
        return "( keyword_hash_mod = (" + keywordHash + "%2000) and country_cd = '" + countryCode + "' and keyword_hash = " + keywordHash + " and keyword_murmurhash = " + murmur3Hash + " )";
    }

    public List<KeywordStreamEntity> getByParamList(List<String> paramsInSummaryBigList) {
        StringBuffer sbf = new StringBuffer();
        sbf.append(
                "   select " +
                        "           keyword_name, " +
                        "           keyword_hash, " +
                        "           country_cd, " +
                        "           round(avg_search_volume, 0) as avg_search_volume, " +
                        "           svAttrInt.key as svAttrIntKey, " +
                        "           svAttrInt.value as svAttrIntValue" +
                        "   from " +
                        "           dis_keyword_summary_annual_big_v2 " +
                        "   where "
        );

        for (int i = 0; i < paramsInSummaryBigList.size(); i++) {
            String param = paramsInSummaryBigList.get(i);
            if (i == 0) {
                sbf.append(param);
            } else {
                sbf.append(" or ").append(param);
            }
        }

        sbf.append(" SETTINGS max_bytes_before_external_group_by=15000000000,max_bytes_before_external_sort=15000000000 ");
        String sql = sbf.toString();
        return findBySql(sql);
    }
}
