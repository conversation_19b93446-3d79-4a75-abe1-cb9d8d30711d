/**
 *
 */
package seoclarity.backend.dao.clickhouse.gscclicksteam;

import seoclarity.backend.dao.BaseJdbcSupport;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class ClGscClickDefaultBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDB01DefaultDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}



}
