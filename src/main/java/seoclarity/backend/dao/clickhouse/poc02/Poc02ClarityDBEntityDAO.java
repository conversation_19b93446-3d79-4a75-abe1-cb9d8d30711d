package seoclarity.backend.dao.clickhouse.poc02;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import ru.yandex.clickhouse.ClickHouseArray;
import ru.yandex.clickhouse.domain.ClickHouseDataType;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.poc02TestHashEntity;

/**
 * 
 * <AUTHOR>
 * @date 2020-11-19
 * com.actonia.subserver.dao.clarityDB.poc02TestHashEntity
 *
 */
@Repository
public class Poc02ClarityDBEntityDAO extends Poc02BaseJdbcSupport<poc02TestHashEntity>{
	private static final String[] columns = new String[] {
			 "kName", 
			 "hash_SHA1", 
			 "hash_SHA224",
			 "hash_SHA512",
	};

	@Deprecated
	public String getTableName() {
		return "sha_hash_kwd";
	}
	
	
	 public void insertForBatch(Map<String, List<ClickHouseArray>> resultMap, String tableName) {

	        String sql = "INSERT INTO " + tableName
	                + " (" + StringUtils.join(columns, ",") + ") VALUES (?,?,?,?)";
	        List<Object[]> batch = new ArrayList<Object[]>();
	        int i = 0;
	        List<ClickHouseArray> hashList = new ArrayList<>();
	        for (String keyword : resultMap.keySet()) {
	        	hashList = resultMap.get(keyword);
	        	
	        	if (hashList == null || hashList.size() != 3) {
	        		System.out.println("HashList size incorrect!");
					continue;
				}
	            Object[] values = new Object[]{
	            		keyword,
	            		hashList.get(0),
	            		hashList.get(1),
	            		hashList.get(2),
	            };

	            if (i++ < 5) {
	                System.out.println(new Gson().toJson(values));
	            }

	            batch.add(values);
	        }
	        executeBatch(sql, batch, new int[] {ClickHouseDataType.String.getSqlType(), ClickHouseDataType.Array.getSqlType(), ClickHouseDataType.Array.getSqlType()});
	    }
	 
	 
	 public void insertForBatch(List<CLRankingDetailEntity> resultList, String tableName) {

	        String sql = "INSERT INTO sc_test_sha_hash_kwd "
	                + " (kName, urlArray, rank) VALUES (?,?,?)";
	        List<Object[]> batch = new ArrayList<Object[]>();
	        int i = 0;
	        for (CLRankingDetailEntity cLRankingDetailEntity : resultList) {
	            Object[] values = new Object[]{
	            		cLRankingDetailEntity.getKeywordName(),
	            		cLRankingDetailEntity.getUrl(),
	            		cLRankingDetailEntity.getTrueRank(),
	            };

	            if (i++ < 5) {
	                System.out.println(new Gson().toJson(values));
	            }

	            batch.add(values);
	        }
	        executeBatch(sql, batch);
	    }

	public List<String> getHashByFunctionName(String func, String[] dateArray) {
		
		String text = "['" + StringUtils.join(dateArray, "','") + "']";
		
		StringBuffer sql = new StringBuffer();
//		sql.append(" SELECT arrayMap(x -> URLHash(x), " + text + ")   ");
		sql.append(" SELECT arrayMap(x -> " + func + ", " + text + ")   ");
		
		System.out.println(sql.toString());
		
		return this.queryForStringList(sql.toString());
	}
	
	
}
