package seoclarity.backend.dao.clickhouse.monthlyvideoranking;

import cn.hutool.core.util.ArrayUtil;
import com.github.housepower.jdbc.statement.ClickHousePreparedInsertStatement;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import ru.yandex.clickhouse.ClickHouseArray;
import ru.yandex.clickhouse.domain.ClickHouseDataType;
import seoclarity.backend.dao.clickhouse.youtube.DataSouceType;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyRankingEntity;
import seoclarity.backend.entity.clickhouse.monthlyranking.MonthlyVideoSearchEntity;
import seoclarity.backend.utils.FormatUtils;

import javax.sql.DataSource;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Repository
public class MonthlyVideoRankingDao extends ClMonthlyVideoRankingBaseJdbcSupport<MonthlyRankingEntity> {

    public enum DATA_SOURCE_TYPE {
        main, backup
    }

    @Override
    public DataSource getDataSource() {
        try {
            if (type == null) {
                type = DATA_SOURCE_TYPE.main;
            }
            if (type == DATA_SOURCE_TYPE.main) {
                return dataSource;
            } else if (type == DATA_SOURCE_TYPE.backup) {
                return dataSource2;
            } else {
                return null;
            }
        } catch (Exception e) {
            System.out.println("type=========="+type);
            e.printStackTrace();
            return null;
        }
    }

    public void changeDataSource(DATA_SOURCE_TYPE dataSourceType) {
        this.type = dataSourceType;
    }

    @Override
    public String getTableName() {
        return "d_video_search_us";
    }

    public void insertVideoSearchV2(List<MonthlyVideoSearchEntity> videoSearchEntities) throws SQLException {

        StringBuilder sql = new StringBuilder();
        sql.append(" insert into d_video_search_us ");
        sql.append(" ( keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id, ranking_date ");
        sql.append(" , domain_reverse, root_domain_reverse, avg_search_volume, cpc, uri, url, protocol, rank, sub_rank ");
        sql.append(" , hrd, hrrd, sign, publisher, attrs.key, attrs.value ) ");
        sql.append("  VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? )");

        List<Object[]> values = new ArrayList<>();

        for (MonthlyVideoSearchEntity videoSearchEntity : videoSearchEntities) {
            int index = 1;
            Object[] value = new Object[] {
                    StringUtils.lowerCase(videoSearchEntity.getKeywordName()),
                    videoSearchEntity.getOwnDomainId(),
                    videoSearchEntity.getKeywordRankcheckId(),
                    videoSearchEntity.getEngineId(),
                    videoSearchEntity.getLanguageId(),
                    videoSearchEntity.getLocationId(),
                    FormatUtils.formatDate(videoSearchEntity.getRankingDate(), FormatUtils.DATE_PATTERN_2),
                    videoSearchEntity.getDomainReverse(),
                    videoSearchEntity.getRootDomainReverse(),
                    videoSearchEntity.getAvgSearchVolume(),
                    videoSearchEntity.getCpc(),
                    videoSearchEntity.getUri(),
                    videoSearchEntity.getUrl(),
                    videoSearchEntity.getProtocol(),
                    videoSearchEntity.getRank(),
                    videoSearchEntity.getSubRank(),
                    videoSearchEntity.getHrd(),
                    videoSearchEntity.getHrrd(),
                    videoSearchEntity.getSign(),
                    videoSearchEntity.getPublisher(),
                    videoSearchEntity.getArrayKey(),
                    videoSearchEntity.getArrayValues()
//                    new ClickHouseArray(ClickHouseDataType.String, videoSearchEntity.getArrayKey()),
//                    new ClickHouseArray(ClickHouseDataType.String, videoSearchEntity.getArrayValues())
            };
            values.add(value);
        }

        this.executeBatch(sql.toString(), values);

    }

    public PreparedStatement getStatement() throws SQLException {

        StringBuilder sql = new StringBuilder();
        sql.append(" insert into d_video_search_us ");
        sql.append(" ( keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id, ranking_date ");
        sql.append(" , domain_reverse, root_domain_reverse, avg_search_volume, cpc, uri, url, protocol, rank, sub_rank ");
        sql.append(" , hrd, hrrd, sign, publisher, attrs.key, attrs.value ) ");
        sql.append("  VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? )");

        return this.getConnection().prepareStatement(sql.toString());
    }

    public void insertVideoSearch(PreparedStatement preparedStatement, List<MonthlyVideoSearchEntity> videoSearchEntities) throws SQLException {

//        List<Object[]> values = new ArrayList<>();
        for (MonthlyVideoSearchEntity videoSearchEntity : videoSearchEntities) {
            int index = 1;
//            Object[] value = new Object[] {
//                    StringUtils.lowerCase(videoSearchEntity.getKeywordName()),
//                    videoSearchEntity.getOwnDomainId(),
//                    videoSearchEntity.getKeywordRankcheckId(),
//                    videoSearchEntity.getEngineId(),
//                    videoSearchEntity.getLanguageId(),
//                    videoSearchEntity.getLocationId(),
//                    FormatUtils.formatDate(videoSearchEntity.getRankingDate(), FormatUtils.DATE_PATTERN_2),
//                    videoSearchEntity.getDomainReverse(),
//                    videoSearchEntity.getRootDomainReverse(),
//                    videoSearchEntity.getAvgSearchVolume(),
//                    videoSearchEntity.getCpc(),
//                    videoSearchEntity.getUri(),
//                    videoSearchEntity.getUrl(),
//                    videoSearchEntity.getProtocol(),
//                    videoSearchEntity.getRank(),
//                    videoSearchEntity.getSubRank(),
//                    videoSearchEntity.getHrd(),
//                    videoSearchEntity.getHrrd(),
//                    videoSearchEntity.getSign(),
//                    videoSearchEntity.getPublisher(),
//                    videoSearchEntity.getArrayKey(),
//                    videoSearchEntity.getArrayValues()
//            };
//            values.add(value);
            preparedStatement.setString(index++, StringUtils.lowerCase(videoSearchEntity.getKeywordName()));
            preparedStatement.setInt(index++, videoSearchEntity.getOwnDomainId());
            preparedStatement.setInt(index++, videoSearchEntity.getKeywordRankcheckId());
            preparedStatement.setInt(index++, videoSearchEntity.getEngineId());
            preparedStatement.setInt(index++, videoSearchEntity.getLanguageId());
            preparedStatement.setInt(index++, videoSearchEntity.getLocationId());
            preparedStatement.setDate(index++, videoSearchEntity.getRankingDate());
            preparedStatement.setString(index++, videoSearchEntity.getDomainReverse());
            preparedStatement.setString(index++, videoSearchEntity.getRootDomainReverse());
            preparedStatement.setLong(index++, videoSearchEntity.getAvgSearchVolume());
            preparedStatement.setDouble(index++, videoSearchEntity.getCpc());
            preparedStatement.setString(index++, videoSearchEntity.getUri());
            preparedStatement.setString(index++, videoSearchEntity.getUrl());
            preparedStatement.setInt(index++, videoSearchEntity.getProtocol());
            preparedStatement.setInt(index++, videoSearchEntity.getRank());
            preparedStatement.setInt(index++, videoSearchEntity.getSubRank());
            preparedStatement.setInt(index++, videoSearchEntity.getHrd());
            preparedStatement.setInt(index++, videoSearchEntity.getHrrd());
            preparedStatement.setInt(index++, videoSearchEntity.getSign());
            preparedStatement.setString(index++, videoSearchEntity.getPublisher());
            preparedStatement.setArray(index++, new ClickHouseArray(ClickHouseDataType.String, videoSearchEntity.getArrayKey()));
            preparedStatement.setArray(index++, new ClickHouseArray(ClickHouseDataType.String, videoSearchEntity.getArrayValues()));
            preparedStatement.addBatch();
        }

//        for (Object[] value : values) {
//            logger.info(new Gson().toJson(value));
//            break;
//        }

//        preparedStatement.closeOnCompletion();
    }

}
