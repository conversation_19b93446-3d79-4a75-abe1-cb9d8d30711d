/**
 *
 */
package seoclarity.backend.dao.clickhouse.ga;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class GaBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDBGaDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
