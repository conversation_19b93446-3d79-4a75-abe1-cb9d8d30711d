package seoclarity.backend.dao.clickhouse.ga;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.clarity360.vo.GaVO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360Lweb05DAO;
import seoclarity.backend.entity.clickhouse.ga.GoogleAnalyticsEntity;
import seoclarity.backend.export.vo.ClarityDBTypesFilterVO;
import seoclarity.backend.export.vo.PaginationColFilter;
import seoclarity.backend.utils.ClarityDBUtils;


@Repository
public class Ga001ClarityDBEntityDAO extends Ga001BaseJdbcSupport<GoogleAnalyticsEntity>{

	@Override
	public String getTableName() {
		// TODO Auto-generated method stub
		return null;
	}
	
	public static String getTable(Integer dateSourceType, Integer ownDomainId, GaVO gaVO, List<Integer> GA_GA4_DOMAIN_MAP, List<Integer> GA_GA4_BQ_DOMAIN_MAP) {
		// https://www.wrike.com/open.htm?id=454976547
		// for special data type like 214, it means old sourceType = 2, now sourceType = 1, we should merge 2 and 1 data, use the last value 4 as the sourceType
		dateSourceType = dateSourceType < 10 ? dateSourceType : dateSourceType % 10;
		List<String> tableList = new ArrayList<>(1);
		if (dateSourceType == GoogleAnalyticsEntity.DATA_SOURCE_TYPE_OMNITURE) {
			tableList.add("dis_adobe");
		} else if (dateSourceType == GoogleAnalyticsEntity.DATA_SOURCE_TYPE_FILE_UPLOAD) {
			tableList.add("dis_upload");
		} else if (dateSourceType == GoogleAnalyticsEntity.DATA_SOURCE_TYPE_FILE_GA_ALL) {
			tableList.add("dis_ga");
			tableList.add("dis_analytics_ga4");
//				tableList.add("dis_adobe");
			tableList.add("dis_upload");
		} else if (dateSourceType == GoogleAnalyticsEntity.DATA_SOURCE_TYPE_OMNITURE_GA_ALL) {
			tableList.add("dis_ga");
			tableList.add("dis_analytics_ga4");
			tableList.add("dis_adobe");
		} else {
			tableList.add("dis_ga");
			// tableList.add("dis_analytics_ga4");
		}
		
		tableList.addAll(ClarityDBUtils.getExtendedTablesForGA(ownDomainId, gaVO.getStartDate(), gaVO.getEndDate(), GA_GA4_DOMAIN_MAP, GA_GA4_BQ_DOMAIN_MAP));

		String dataBase = "actonia_site_analytics";
		if (tableList.size() == 1) {
			return dataBase + "." + tableList.get(0);
		} else {
			return "merge(" + dataBase + ",'^" + StringUtils.join(tableList, "$|^") + "$')";
		}
	}
	
	public Integer getPartitionNum(Integer ownDomainId) {
		
		if (ownDomainId != null) {
			return ownDomainId % 25;
		}
		return null;
	}
	
	public static Integer DATA_SOURCE_TYPE_OMNITURE = 2;
	
	public Integer getTotalCount(Integer dateSourceType, Integer ownDomainId, GaVO gaVO, 
			String targetDate, Integer reportId, List<Integer> GA_GA4_DOMAIN_MAP, List<Integer> GA_GA4_BQ_DOMAIN_MAP, 
			Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO, Integer protocol) throws Exception {
    	
		if (gaVO == null || dateSourceType == null) {
			
			System.out.println("gaVO is Empty!");
			return 0;
		}
		
		dateSourceType = dateSourceType < 10 ? dateSourceType : dateSourceType % 10;
		String tableName = getTable(dateSourceType, ownDomainId, gaVO, GA_GA4_DOMAIN_MAP, GA_GA4_BQ_DOMAIN_MAP);
		
    	StringBuffer sql = new StringBuffer();
        sql.append(" select count() ");
        sql.append(" from (   ");
        sql.append(" select url_murmur_hash ");
        sql.append(" FROM " + tableName + " ");
    	sql.append(" WHERE domain_id = " + ownDomainId + " AND log_date>='" + gaVO.getStartDate() + "' AND log_date<='" + gaVO.getEndDate() + "' and profile_id =  " + gaVO.getProfileId());
    	
    	String url = "lower(concat('" + (protocol == 0 ? "http://" : "https://") + "', host_name, uri)) ";
    	if (gaVO.getLeftNaviFilters() != null && gaVO.getLeftNaviFilters().length > 0) {
            for (PaginationColFilter paginationColFilter : gaVO.getLeftNaviFilters()) {
                if ("url".equalsIgnoreCase(paginationColFilter.getColname())) {
                	sql.append(ClarityDBUtils.generateColumnFilterSql(paginationColFilter, url, Types.VARCHAR, ClarityDBUtils.OPERATOR_AND, true, true));
                }
            }
        }
    	
    	if (ArrayUtils.isNotEmpty(gaVO.getEngines())) {
    		
    		String engineFilterSql = "";
    		
    		for(String source : gaVO.getEngines()) {
    			
    			if (StringUtils.isNotBlank(source)) {
    				engineFilterSql += " source = '" + source + "' or";
				}
    		}
    		
    		if (StringUtils.isNotBlank(engineFilterSql) && StringUtils.endsWith(engineFilterSql, "or")) {
    			engineFilterSql = StringUtils.removeEnd(engineFilterSql, "or");
    			
    			sql.append(" AND (").append(engineFilterSql).append(")");
			}
		}
    	
    		
		if (StringUtils.isNotBlank(gaVO.getAnalyticsGroupFilter())) {
    		System.out.println(" adding filter for group : " + gaVO.getAnalyticsGroupFilter());
			String filter = gaVO.getAnalyticsGroupFilter();
    		if (StringUtils.isNotBlank(filter)) {
				sql.append(filter);
			}
			System.out.println("=======applyGroupFilter:" + filter);
		}
		
    	if (urlTagId != null && urlTagId > 0) {
    		sql.append(" AND (dictGetUInt64('file_dic_rg_managed_uri', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), URLHash(lowerUTF8(uri)))) > 0) ");
		}
    	
    		
		url = "concat('" + (protocol == 0 ? "http://" : "https://") + "', host_name, uri) ";
		
		String contentFilterSql = ClarityDBUtils.getCommonTypesFilter(gaVO, "urlNameNeedReplace");
		
		sql.append(StringUtils.replace(contentFilterSql, "urlNameNeedReplace", url));

    	String versionSql = "";
    	if (dateSourceType != null && dateSourceType == DATA_SOURCE_TYPE_OMNITURE) {
    		versionSql = " AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (8, toUInt32(domain_id), log_date, toUInt64(0))) ";
		} else {
			versionSql = " AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0))) ";
		}
    	sql.append(versionSql);
    	sql.append(" GROUP BY  url_murmur_hash  ");
        
        sql.append(" ) ");
        sql.append(" SETTINGS max_bytes_before_external_group_by=20000000000, max_bytes_before_external_sort=10000000000, distributed_aggregation_memory_efficient=1 ");

        System.out.println("GA Totalcount : " + sql.toString());
		return this.queryForInteger(sql.toString());
	}
	
	public void gaClarity360Summary(Integer dateSourceType, Integer ownDomainId, GaVO gaVO, 
			String targetDate, Integer reportId, List<Integer> GA_GA4_DOMAIN_MAP, List<Integer> GA_GA4_BQ_DOMAIN_MAP, 
			Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO, Integer protocol, String urlHashColumn, Boolean isForUrlHash, Integer partNum, Integer num, Integer clusterNum) {
		
		if (gaVO == null) {
			
			System.out.println("gaVO is Empty!");
			return;
		}
		
    	int domainId = ownDomainId;
    	int partitionNum = getPartitionNum(ownDomainId);

		if(gaVO.getAnalyticsGroupId() != null && gaVO.getAnalyticsGroupId() > 0) {
			domainId = gaVO.getAnalyticsGroupId();
			partitionNum = gaVO.getAnalyticsGroupId() % 25;
		}
		
		if (dateSourceType == null) {
			System.out.println("dateSourceType not found, dateSourceType:" + dateSourceType);
			return ;
		}
		
		dateSourceType = dateSourceType < 10 ? dateSourceType : dateSourceType % 10;
		
		
		String tableName = getTable(dateSourceType, ownDomainId, gaVO, GA_GA4_DOMAIN_MAP, GA_GA4_BQ_DOMAIN_MAP);
    	StringBuffer sql = new StringBuffer();
    	sql.append(" insert into clarity360." + Clarity360Lweb05DAO.getDisTableNameByType(isForUrlHash, clusterNum) + " ( domain_id,  url,  url_murmur_hash, `sa_entrances`, `sa_pageviews`, `sa_exits`, `sa_bounces`, ");
    	sql.append(" `sa_session_duration`, `sa_time_on_page`, `sa_transactions`, `sa_item_revenue`, ");
    	sql.append(" `sa_goal1completions`, `sa_goal2completions`,  ");
    	sql.append(" `sa_goal3completions`, `sa_goal4completions`, `sa_goal5completions`, `sa_goal6completions`, `sa_goal7completions`, `sa_goal8completions`, ");
    	sql.append(" `sa_goal9completions`, `sa_goal10completions`, `sa_goal11completions`, `sa_goal12completions`, `sa_goal13completions`, `sa_goal14completions`, ");
    	sql.append(" `sa_goal15completions`, `sa_goal16completions`, `sa_goal17completions`, `sa_goal18completions`, `sa_goal19completions`, `sa_goal20completions`, ");
    	sql.append(" `sa_goal1Value`, `sa_goal2Value`, `sa_goal3Value`, `sa_goal4Value`, `sa_goal5Value`, `sa_goal6Value`, `sa_goal7Value`, `sa_goal8Value`, ");
    	sql.append(" `sa_goal9Value`, `sa_goal10Value`, `sa_goal11Value`, `sa_goal12Value`, `sa_goal13Value`, `sa_goal14Value`, `sa_goal15Value`, `sa_goal16Value`, ");
    	sql.append(" `sa_goal17Value`, `sa_goal18Value`, `sa_goal19Value`, `sa_goal20Value`,    ");
    	
    	sql.append(" `goal21completions`, `goal22completions`,  ");
    	sql.append(" `goal23completions`, `goal24completions`, `goal25completions`, `goal26completions`, `goal27completions`, `goal28completions`, ");
    	sql.append(" `goal29completions`, `goal30completions`, `goal31completions`, `goal32completions`, `goal33completions`, `goal34completions`, ");
    	sql.append(" `goal35completions`, `goal36completions`, `goal37completions`, `goal38completions`, `goal39completions`, `goal40completions`, ");
    	sql.append(" `goal21Value`, `goal22Value`, `goal23Value`, `goal24Value`, `goal25Value`, `goal26Value`, `goal27Value`, `goal28Value`, ");
    	sql.append(" `goal29Value`, `goal30Value`, `goal31Value`, `goal32Value`, `goal33Value`, `goal34Value`, `goal35Value`, `goal36Value`, ");
    	sql.append(" `goal37Value`, `goal38Value`, `goal39Value`, `goal40Value`,    ");
    	
    	sql.append(" sources,  target_date,  process_date, siteanalytics_numofdays, report_id, folder_level_1, folder_level_2, folder_level_3 ) ");
    	sql.append(" SELECT  ?,  any(url),  " + urlHashColumn + " as hash,  sum(entrances) AS sa_entrances,  sum(pageviews) AS sa_pageviews,  sum(exits) AS sa_exits,  ");
    	sql.append(" sum(bounces) AS sa_bounces,  sum(session_duration) AS sa_session_duration,  sum(time_on_page) AS sa_time_on_page,  sum(transactions) AS sa_transactions,  ");
    	sql.append(" sum(item_revenue) AS sa_item_revenue,   ");
    	sql.append(" sum(goal1completions) AS sa_goal1completions,  sum(goal2completions) AS sa_goal2completions,  ");
    	sql.append(" sum(goal3completions) AS sa_goal3completions,  sum(goal4completions) AS sa_goal4completions,  sum(goal5completions) AS sa_goal5completions,  ");
    	sql.append(" sum(goal6completions) AS sa_goal6completions,  sum(goal7completions) AS sa_goal7completions,  sum(goal8completions) AS sa_goal8completions,  ");
    	sql.append(" sum(goal9completions) AS sa_goal9completions,  sum(goal10completions) AS sa_goal10completions,  sum(goal11completions) AS sa_goal11completions,  ");
    	sql.append(" sum(goal12completions) AS sa_goal12completions,  sum(goal13completions) AS sa_goal13completions,  sum(goal14completions) AS sa_goal14completions,  ");
    	sql.append(" sum(goal15completions) AS sa_goal15completions,  sum(goal16completions) AS sa_goal16completions,  sum(goal17completions) AS sa_goal17completions,  ");
    	sql.append(" sum(goal18completions) AS sa_goal18completions,  sum(goal19completions) AS sa_goal19completions,  sum(goal20completions) AS sa_goal20completions,  ");
    	sql.append(" sum(goal1Value) AS sa_goal1Value,  sum(goal2Value) AS sa_goal2Value,  sum(goal3Value) AS sa_goal3Value,  sum(goal4Value) AS sa_goal4Value,  ");
    	sql.append(" sum(goal5Value) AS sa_goal5Value,  sum(goal6Value) AS sa_goal6Value,  sum(goal7Value) AS sa_goal7Value,  sum(goal8Value) AS sa_goal8Value,  ");
    	sql.append(" sum(goal9Value) AS sa_goal9Value,  sum(goal10Value) AS sa_goal10Value,  sum(goal11Value) AS sa_goal11Value,  sum(goal12Value) AS sa_goal12Value,  ");
    	sql.append(" sum(goal13Value) AS sa_goal13Value,  sum(goal14Value) AS sa_goal14Value,  sum(goal15Value) AS sa_goal15Value,  sum(goal16Value) AS sa_goal16Value,  ");
    	sql.append(" sum(goal17Value) AS sa_goal17Value,  sum(goal18Value) AS sa_goal18Value,  sum(goal19Value) AS sa_goal19Value,  sum(goal20Value) AS sa_goal20Value,  ");
    	
    	sql.append(" sum(goal21completions) AS sa_goal21completions,  sum(goal22completions) AS sa_goal22completions,  ");
    	sql.append(" sum(goal23completions) AS sa_goal23completions,  sum(goal24completions) AS sa_goal24completions,  sum(goal25completions) AS sa_goal25completions,  ");
    	sql.append(" sum(goal26completions) AS sa_goal26completions,  sum(goal27completions) AS sa_goal27completions,  sum(goal28completions) AS sa_goal28completions,  ");
    	sql.append(" sum(goal29completions) AS sa_goal29completions,  sum(goal30completions) AS sa_goal30completions,  sum(goal31completions) AS sa_goal31completions,  ");
    	sql.append(" sum(goal32completions) AS sa_goal32completions,  sum(goal33completions) AS sa_goal33completions,  sum(goal34completions) AS sa_goal34completions,  ");
    	sql.append(" sum(goal35completions) AS sa_goal35completions,  sum(goal36completions) AS sa_goal36completions,  sum(goal37completions) AS sa_goal37completions,  ");
    	sql.append(" sum(goal38completions) AS sa_goal38completions,  sum(goal39completions) AS sa_goal39completions,  sum(goal40completions) AS sa_goal40completions,  ");
    	sql.append(" sum(goal21Value) AS sa_goal21Value,  sum(goal22Value) AS sa_goal22Value,  sum(goal23Value) AS sa_goal23Value,  sum(goal24Value) AS sa_goal24Value,  ");
    	sql.append(" sum(goal25Value) AS sa_goal25Value,  sum(goal26Value) AS sa_goal26Value,  sum(goal27Value) AS sa_goal27Value,  sum(goal28Value) AS sa_goal28Value,  ");
    	sql.append(" sum(goal29Value) AS sa_goal29Value,  sum(goal30Value) AS sa_goal30Value,  sum(goal31Value) AS sa_goal31Value,  sum(goal32Value) AS sa_goal32Value,  ");
    	sql.append(" sum(goal33Value) AS sa_goal33Value,  sum(goal34Value) AS sa_goal34Value,  sum(goal35Value) AS sa_goal35Value,  sum(goal36Value) AS sa_goal36Value,  ");
    	sql.append(" sum(goal37Value) AS sa_goal37Value,  sum(goal38Value) AS sa_goal38Value,  sum(goal39Value) AS sa_goal39Value,  sum(goal40Value) AS sa_goal40Value,  ");
    	
    	sql.append(" ['traffic'], ?, toDate(now()), count(distinct log_date), " + reportId + ", ");
    	sql.append(" if(endsWith(URLPathHierarchy(cutQueryString(any(url)))[1], '/'), replaceAll(URLPathHierarchy(cutQueryString(any(url)))[1], '/', ''), '') AS folder1, ");
    	sql.append(" if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[2], length(URLPathHierarchy(cutQueryString(any(url)))[1]), length(URLPathHierarchy(cutQueryString(any(url)))[2])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[2], length(URLPathHierarchy(cutQueryString(any(url)))[1]), length(URLPathHierarchy(cutQueryString(any(url)))[2])), ''), '/', ''), '') AS folder2, ");
    	sql.append(" if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[3], length(URLPathHierarchy(cutQueryString(any(url)))[2]), length(URLPathHierarchy(cutQueryString(any(url)))[3])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[3], length(URLPathHierarchy(cutQueryString(any(url)))[2]), length(URLPathHierarchy(cutQueryString(any(url)))[3])), ''), '/', ''), '') AS folder3 ");
    	sql.append(" FROM " + tableName + " ");
    	sql.append(" WHERE domain_id = ? AND log_date>='" + gaVO.getStartDate() + "' AND log_date<='" + gaVO.getEndDate() + "' and profile_id = " + gaVO.getProfileId());
    	
    	if (partNum != 1) {
    		sql.append(" and ((hash % " + partNum + ") = " + num +")");
		}
    	
//		sql.append(" and hash global not in (select url_murmur_hash from clarity360." + Clarity360Lweb05DAO.getDisTableNameByType(isForUrlHash) + " where report_id = " + reportId + " and has(sources, 'traffic')  and  ((url_murmur_hash % " + partNum + ") = " + num +")) ");
    	
    	
    	String url = "lower(concat('" + (protocol == 0 ? "http://" : "https://") + "', host_name, uri)) ";
    	if (gaVO.getLeftNaviFilters() != null && gaVO.getLeftNaviFilters().length > 0) {
            for (PaginationColFilter paginationColFilter : gaVO.getLeftNaviFilters()) {
                if ("url".equalsIgnoreCase(paginationColFilter.getColname())) {
                	sql.append(ClarityDBUtils.generateColumnFilterSql(paginationColFilter, url, Types.VARCHAR, ClarityDBUtils.OPERATOR_AND, true, true));
                }
            }
        }
    	
    	if (ArrayUtils.isNotEmpty(gaVO.getEngines())) {
    		
    		String engineFilterSql = "";
    		
    		for(String source : gaVO.getEngines()) {
    			
    			if (StringUtils.isNotBlank(source)) {
    				engineFilterSql += " source = '" + source + "' or";
				}
    		}
    		
    		if (StringUtils.isNotBlank(engineFilterSql) && StringUtils.endsWith(engineFilterSql, "or")) {
    			engineFilterSql = StringUtils.removeEnd(engineFilterSql, "or");
    			
    			sql.append(" AND (").append(engineFilterSql).append(")");
			}
		}
    	
    		
		if (StringUtils.isNotBlank(gaVO.getAnalyticsGroupFilter())) {
    		System.out.println(" adding filter for group : " + gaVO.getAnalyticsGroupFilter());
			String filter = gaVO.getAnalyticsGroupFilter();
//        		if (!StringUtils.equals(uri, uriColNameVal)) {
//        			filter = paramVO.getAnalyticsGroupFilter();
//        			String[] replaceArray = new String[]{"(uri)", "(uri,", "(uri ", " uri ", "uri,", "uri=", "uri ",};
//        			for (String col : replaceArray) {
//        				if (StringUtils.contains(filter, col)) {
//        					String replacement = StringUtils.replace(col, uriColNameVal, uri);
//        					filter = StringUtils.replace(filter, col, replacement);
//						}
//					}
//        			if (filterForChildOnly) {
//						filter = StringUtils.replace(filter, "host_name,", getCommonParentUrlHostNameColumn(null, getProtocol(paramVO)) + ",");
//					}
//				}
    		if (StringUtils.isNotBlank(filter)) {
				sql.append(filter);
			}
			System.out.println("=======applyGroupFilter:" + filter);
		}
		
    	if (urlTagId != null && urlTagId > 0) {
    		sql.append(" AND (dictGetUInt64('file_dic_rg_managed_uri', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), URLHash(lowerUTF8(uri)))) > 0) ");
		}
    	
    		
		url = "concat('" + (protocol == 0 ? "http://" : "https://") + "', host_name, uri) ";
		
		String contentFilterSql = ClarityDBUtils.getCommonTypesFilter(gaVO, "urlNameNeedReplace");
		
		sql.append(StringUtils.replace(contentFilterSql, "urlNameNeedReplace", url));

    	String versionSql = "";
    	if (dateSourceType != null && dateSourceType == DATA_SOURCE_TYPE_OMNITURE) {
    		versionSql = " AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (8, toUInt32(domain_id), log_date, toUInt64(0))) ";
		} else {
			versionSql = " AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0))) ";
		}
    	sql.append(versionSql);
    	sql.append(" GROUP BY  domain_id,  " + urlHashColumn);
    	
    	System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), ownDomainId, targetDate, domainId);
    }

	public GoogleAnalyticsEntity getHasGaDate(Integer dateSourceType,Integer domainId,Integer googleAnalyticsVersion, String last3MonthDays) {
		String tableName = getTable(dateSourceType,googleAnalyticsVersion);
		StringBuffer sql = new StringBuffer();
		sql.append(" select keyword_text from ");
		sql.append(tableName);
		sql.append(" where domain_id = ? and log_date >= ? ");
		sql.append(" limit 1 ");
		System.out.println("===###queryGA : domain : " + domainId + "dateSourceType:  " + dateSourceType + " , googleAnalyticsVersion :" + googleAnalyticsVersion + " ,tableName : " + tableName);
		System.out.println(" sql : " + sql.toString());
		return this.findObject(sql.toString(), domainId,last3MonthDays);
	}


	public static String getTable(Integer dateSourceType,Integer googleAnalyticsVersion) {
		// https://www.wrike.com/open.htm?id=454976547
		// for special data type like 214, it means old sourceType = 2, now sourceType = 1, we should merge 2 and 1 data, use the last value 4 as the sourceType
		dateSourceType = dateSourceType < 10 ? dateSourceType : dateSourceType % 10;
		List<String> tableList = new ArrayList<>(1);
		if (dateSourceType == GoogleAnalyticsEntity.DATA_SOURCE_TYPE_OMNITURE) {
			tableList.add("dis_adobe");
		} else if (dateSourceType == GoogleAnalyticsEntity.DATA_SOURCE_TYPE_FILE_UPLOAD) {
			tableList.add("dis_upload");
		} else if (dateSourceType == GoogleAnalyticsEntity.DATA_SOURCE_TYPE_FILE_GA_ALL) {
			tableList.add("dis_ga");
			tableList.add("dis_analytics_ga4");
//				tableList.add("dis_adobe");
			tableList.add("dis_upload");
		} else if (dateSourceType == GoogleAnalyticsEntity.DATA_SOURCE_TYPE_OMNITURE_GA_ALL) {
			tableList.add("dis_ga");
			tableList.add("dis_analytics_ga4");
			tableList.add("dis_adobe");
		} else {
			if(null == googleAnalyticsVersion || googleAnalyticsVersion == 0){
				tableList.add("dis_ga");
			} else if (googleAnalyticsVersion == 3 ){
				tableList.add("dis_ga");
			}else if (googleAnalyticsVersion == 4){
				tableList.add("dis_analytics_ga4");
			}else {
				tableList.add("dis_ga");
			}

			// tableList.add("dis_analytics_ga4");
		}


		String dataBase = "actonia_site_analytics";
		if (tableList.size() == 1) {
			return dataBase + "." + tableList.get(0);
		} else {
			return "merge(" + dataBase + ",'^" + StringUtils.join(tableList, "$|^") + "$')";
		}
	}
}
