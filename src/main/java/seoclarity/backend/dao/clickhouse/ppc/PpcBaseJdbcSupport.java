/**
 *
 */
package seoclarity.backend.dao.clickhouse.ppc;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class PpcBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDBPpcDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
