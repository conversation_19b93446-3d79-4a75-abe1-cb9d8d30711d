package seoclarity.backend.dao.clickhouse.ppc;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.clickhouse.GoogleAdwordsPpcEntity;

/**
 * 
 * <AUTHOR>
 * @date 2020-11-19
 * com.actonia.subserver.dao.clarityDB.PpcClusterbClarityDBEntityDAO
 *
 */
@Repository
public class PpcClusterbClarityDBEntityDAO extends PpcClusterbBaseJdbcSupport<GoogleAdwordsPpcEntity>{
	private static final String[] columns = new String[] {
			 "domain_id", 
			 "log_date", 
			 "keyword_text", 
			 "host_name", 
			 "uri", 
			 "url", 
			 "folder_level1", 
			 "folder_level2", 
			 "folder_level3", 
			 "data_type", 
			 "channel", 
			 "ad_group_name", 
			 "ad_distribution_network", 
			 "campaign", 
			 "match_type", 
			 "match_query", 
			 "keyword_status", 
			 "ad_group_status", 
			 "transactions", 
			 "impressions", 
			 "clicks", 
			 "cost", 
			 "cpm", 
			 "ctr", 
			 "cpc", 
			 "max_cpc", 
			 "quality_score", 
			 "first_page_cpc", 
			 "avg_position", 
			 "itemRevenue", 
			 "conversionRate", 
			 "goal1value", 
			 "goal2value", 
			 "goal3value", 
			 "goal4value", 
			 "goal5value", 
			 "goal6value", 
			 "goal7value", 
			 "goal8value", 
			 "goal9value", 
			 "goal10value", 
			 "goal1completions", 
			 "goal2completions", 
			 "goal3completions", 
			 "goal4completions", 
			 "goal5completions", 
			 "goal6completions", 
			 "goal7completions", 
			 "goal8completions", 
			 "goal9completions", 
			 "goal10completions", 
			 "version", 
			 "attrs.key", 
			 "attrs.value",
	};

	@Deprecated
	public String getTableName() {
		return null;
	}
	
	
	public String getInsertSql(String tableName) {
		String sql = "INSERT INTO " + tableName + "(" + StringUtils.join((columns), ",") + ") FORMAT TabSeparated";
		return sql;
	}
	
    public void batchCopy(String currentDate, String copyDate, Integer domainId, Integer version) {
        String sql = "INSERT INTO google_adwords_ppc(domain_id,log_date,keyword_text,host_name,uri,url,folder_level1,folder_level2,folder_level3,"
        		+ "data_type,channel,ad_group_name,ad_distribution_network,campaign,match_type,match_query,keyword_status,ad_group_status,"
        		+ "transactions,impressions,clicks,cost,cpm,ctr,cpc,max_cpc,quality_score,first_page_cpc,avg_position,itemRevenue,conversionRate,"
        		+ "goal1value,goal2value,goal3value,goal4value,goal5value,goal6value,goal7value,goal8value,goal9value,goal10value,goal1completions,"
        		+ "goal2completions,goal3completions,goal4completions,goal5completions,goal6completions,goal7completions,goal8completions,"
        		+ "goal9completions,goal10completions,version,attrs.key,attrs.value) "
        		+ "select domain_id,'" + currentDate + "',keyword_text,host_name,uri,url,folder_level1,folder_level2,folder_level3,"
        		+ "data_type,channel,ad_group_name,ad_distribution_network,campaign,match_type,match_query,keyword_status,ad_group_status,"
        		+ "transactions,impressions,clicks,cost,cpm,ctr,cpc,max_cpc,quality_score,first_page_cpc,avg_position,itemRevenue,conversionRate,"
        		+ "goal1value,goal2value,goal3value,goal4value,goal5value,goal6value,goal7value,goal8value,goal9value,goal10value,goal1completions,"
        		+ "goal2completions,goal3completions,goal4completions,goal5completions,goal6completions,goal7completions,goal8completions,goal9completions,"
        		+ "goal10completions," + version + ",attrs.key,attrs.value"
        		+ " from google_adwords_ppc WHERE (domain_id = ?) AND (log_date = '" + copyDate + "')";
        
        System.out.println(sql);
        executeUpdate(sql, domainId);
    }
	
}
