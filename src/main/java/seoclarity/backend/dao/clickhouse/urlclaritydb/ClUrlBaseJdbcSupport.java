package seoclarity.backend.dao.clickhouse.urlclaritydb;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @create 2019-09-19
 **/
public abstract class ClUrlBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name="clarityDBCrawlUrlDataSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }

}
