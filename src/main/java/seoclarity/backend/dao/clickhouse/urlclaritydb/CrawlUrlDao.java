package seoclarity.backend.dao.clickhouse.urlclaritydb;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.clickhouse.crawlurl.TargetUrlHtmlDaily;

/**
 * <AUTHOR>
 * @create 2019-09-19
 **/
@Repository
public class CrawlUrlDao extends ClUrlBaseJdbcSupport<TargetUrlHtmlDaily> {

    @Override
    public String getTableName() {
        return "dis_target_url_html";
    }


    public List<TargetUrlHtmlDaily> getDataByUrl(int domainId, List<String> urlList, String dailyDataCreationDate) {

        String sql = " select title ,description,lower_case_url_hash,url,url_murmur_hash,response_code from " + getTableName();
        sql += " where domain_id = " + domainId + " and daily_data_creation_date = '" + dailyDataCreationDate + "' and url_murmur_hash in( ";
        for (String url : urlList) {
            sql += "murmurHash3_64('" + url + "'),";
        }
        sql = sql.substring(0, sql.length() - 1);
        sql += ")";
//        sql += " group by title ,description,url,url_murmur_hash,response_code ";

        System.out.println("===SQL getDataByUrl: " + sql);
        return findBySql(sql);
    }


    public TargetUrlHtmlDaily getLastCustomDatByUrl(int domainId, String url) {
        if (StringUtils.contains(url, "'")) {
            url = StringUtils.replace(url, "'", "\\'");
        }

        StringBuffer sql = new StringBuffer();
        sql.append("SELECT custom_data from ");
        sql.append(getTableName());
        sql.append(" WHERE (domain_id = " + domainId + ") AND (url = '" + url + "') ");
        sql.append(" ORDER BY crawl_timestamp DESC ");
        sql.append(" LIMIT 1 ");
//        sql += " group by title ,description,url,url_murmur_hash,response_code ";

        System.out.println("===SQL getDataByUrl: " + sql);
        List<TargetUrlHtmlDaily> resultList = findBySql(sql.toString());
        if (resultList != null && resultList.size() == 1) {
            return resultList.get(0);
        }

        return null;
    }

    public List<TargetUrlHtmlDaily> getCustomDataByDate(int domainId, String processDate, String contentType) {

        StringBuffer sql = new StringBuffer();
        sql.append("SELECT distinct url, custom_data  ");
        sql.append(" from dis_target_url_custom_data ");
        sql.append(" WHERE (domain_id = " + domainId + ") AND (daily_data_creation_date = '" + processDate + "') ");
        sql.append(" AND (match(url, '" + contentType + "')  " +
                "  OR match(decodeURLComponent(url), '" + contentType + "')" +
                "  ) ");
        System.out.println("===SQL getCustomDataByDate: " + sql);
        List<TargetUrlHtmlDaily> resultList = findBySql(sql.toString());
        return resultList;
    }

    public List<TargetUrlHtmlDaily> getAllCustomDataByDate(int domainId, String processDate) {

        StringBuffer sql = new StringBuffer();
        sql.append("SELECT distinct url, custom_data  ");
        sql.append(" from dis_target_url_custom_data ");
        sql.append(" WHERE (domain_id = " + domainId + ") AND (daily_data_creation_date = '" + processDate + "') ");
        System.out.println("===SQL getCustomDataByDate: " + sql);
        List<TargetUrlHtmlDaily> resultList = findBySql(sql.toString());
        return resultList;
    }
    
    public List<TargetUrlHtmlDaily> getAllCustomDataByDateMatchfoundTrue(int domainId, String processDate) {

        StringBuffer sql = new StringBuffer();
        sql.append("SELECT distinct url, custom_data  ");
        sql.append(" from dis_target_url_custom_data ");
        sql.append(" WHERE (domain_id = " + domainId + ") AND (daily_data_creation_date = '" + processDate + "') and (custom_data LIKE '%\"match_found\":true%') ");
        System.out.println("===SQL getCustomDataByDate: " + sql);
        List<TargetUrlHtmlDaily> resultList = findBySql(sql.toString());
        return resultList;
    }

    public TargetUrlHtmlDaily getCustomDataByUrl(int domainId, String url) {

        StringBuffer sql = new StringBuffer();
        sql.append("SELECT distinct url, custom_data  ");
        sql.append(" from dis_target_url_custom_data ");
        sql.append(" WHERE (domain_id = " + domainId + ") AND (url = '" + url + "') ORDER BY daily_data_creation_date desc limit 1 ");
        System.out.println("===SQL getCustomDataByUrl: " + sql);
        TargetUrlHtmlDaily result = findObject(sql.toString());
        return result;
    }

    public List<TargetUrlHtmlDaily> getCustomDataByDate(int domainId, String processDate) {

        StringBuffer sql = new StringBuffer();
        sql.append("SELECT distinct url, custom_data  ");
        sql.append(" from dis_target_url_custom_data ");
        sql.append(" WHERE (domain_id = " + domainId + ")  AND (daily_data_creation_date = '" + processDate + "') ");
        System.out.println("===SQL getCustomDataByDate: " + sql);
        List<TargetUrlHtmlDaily> resultList = findBySql(sql.toString());
        return resultList;
    }

    public List<TargetUrlHtmlDaily> getDataByUrl(int domainId, List<String> urlList, List<String> urlHashList) {

        String sql = " select distinct lower_case_url_hash,url_murmur_hash,track_date, domain_id, response_code, title, description ";
        sql += " from ( ";
        sql += "    select lower_case_url_hash,url_murmur_hash,url, track_date, domain_id, response_code, title, description  ";
        sql += " from " + getTableName();
        sql += " where domain_id = " + domainId;
        sql += " and response_code != '301' ";//https://www.wrike.com/open.htm?id=1149658449
        sql += " and lower_case_url_hash in( " + StringUtils.join(urlHashList, ",") + ") ";
        sql += " and url_murmur_hash in( ";
        for (String url : urlList) {
            sql += "murmurHash3_64('" + url + "'),";
        }
        sql = sql.substring(0, sql.length() - 1);
        sql += " ) )as t1 all inner join ( ";
        sql += " select lower_case_url_hash,url_murmur_hash, max(track_date) track_date ";
        sql += " from " + getTableName();
        sql += " where domain_id = " + domainId;
        sql += " and lower_case_url_hash in( " + StringUtils.join(urlHashList, ",") + ") ";
        sql += " and url_murmur_hash in( ";
        for (String url : urlList) {
            sql += "murmurHash3_64('" + url + "'),";
        }
        sql = sql.substring(0, sql.length() - 1);
        sql += " ) ";
        sql += " group by lower_case_url_hash,url_murmur_hash  ";
        sql += " ) t2 using (lower_case_url_hash,url_murmur_hash,track_date) ";
        sql += " ";
//        sql += " group by title ,description,url,url_murmur_hash,response_code ";

        System.out.println("===SQL getDataByUrl: " + sql);
        return findBySql(sql);
    }

    public TargetUrlHtmlDaily getDataByUrlHash(int domainId, String url, boolean isDecode) {

        String sql = " select title ,description,lower_case_url_hash,response_code from " + getTableName();
        sql += " where domain_id = " + domainId;
        if(isDecode){
            sql += " and lower_case_url_hash = URLHash(lower(decodeURLComponent('" + url + "'))) ";
        }else {
            sql += " and lower_case_url_hash = URLHash(lower('" + url + "')) ";
        }
//        sql += " and daily_data_creation_date = '" + dailyDataCreationDate + "' ";
        sql += " and response_code != '301' ";//https://www.wrike.com/open.htm?id=1149658449
        sql += " order by track_date desc limit 1 ";
        System.out.println("===SQL getDataByUrlHash: " + sql);
        return findObject(sql);
    }

    public List<Integer> getDomainIdListByStatusCode(String processDate, Integer respCode) {

        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT domain_id ");
        sql.append(" FROM dis_target_url_html_daily ");
        sql.append(" WHERE (track_date = '" + processDate + "') AND (response_code = '" + respCode + "') ");
        sql.append(" GROUP BY domain_id ");
        System.out.println("===SQL getDomainIdListByStatusCode: " + sql);
        List<Integer> resultList = queryForIntegerList(sql.toString());
        return resultList;
    }

    public List<String> getUrlByOid(int domainId, String processDate) {

        StringBuffer sql = new StringBuffer();
        sql.append("SELECT distinct url  ");
        sql.append(" from dis_target_url_html_daily ");
        sql.append(" WHERE (domain_id = " + domainId + ")  AND (track_date = '" + processDate + "') limit 10 ");
        System.out.println("===SQL getCustomDataByDate: " + sql);
        List<String> resultList = queryForStringList(sql.toString());
        return resultList;
    }


    public List<TargetUrlHtmlDaily> getDataByUrlList(int domainId, String trackDate, List<String> urlHashList) {

        String sql = " select url,url_murmur_hash,track_date, domain_id, response_code, title, description ";
        sql += " from dis_target_url_html_daily ";
        sql += " where domain_id = " + domainId + " and daily_data_creation_date='" + trackDate + "' ";
        sql += " and response_code = '200' ";
        sql += " and url_murmur_hash in( ";
        for (String url : urlHashList) {
            sql += "'" + url + "',";
        }
        sql = sql.substring(0, sql.length() - 1);
        sql += " ) ";
        sql += " ";
        System.out.println("===SQL getDataByUrlList: " + sql);
        return findBySql(sql);
    }

    public List<TargetUrlHtmlDaily> getCustomDataByUrlList(int domainId, String processDate, List<String> urlHashList) {

        String sql = " SELECT url, url_murmur_hash, custom_data from dis_target_url_custom_data ";
        sql += " where domain_id = " + domainId + " and daily_data_creation_date='" + processDate + "' ";
        sql += " and url_murmur_hash in( ";
        for (String url : urlHashList) {
            sql += "'" + url + "',";
        }
        sql = sql.substring(0, sql.length() - 1);
        sql += " ) ";
        sql += " ";
        System.out.println("===SQL getCustomDataByUrlList: " + sql);
        return findBySql(sql);
    }

}
