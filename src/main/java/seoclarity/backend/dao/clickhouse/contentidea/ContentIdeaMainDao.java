package seoclarity.backend.dao.clickhouse.contentidea;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.clickhouse.contentidea.ContentIdeaEntity;

@Repository
public class ContentIdeaMainDao extends ClContentIdeaBaseJdbcSupport<ContentIdeaEntity> {

	@Override
	public String getTableName() {
		return "actonia_content_idea";
	}
	
	public String getTableName(String version) {
		return "actonia_content_idea" + (StringUtils.isBlank(version) ? "" : ("_v" + version));
	}

	public void insertForBatch(List<ContentIdeaEntity> insertData) {
        String sql = "INSERT INTO "+ getTableName()
                +" (title, domain_reverse, root_domain_reverse, url, folder_level1, folder_level2, " +
                "uri, hash_id, source_type, found_date, engine_id, language_id, one_word, one_word_count) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (ContentIdeaEntity entity : insertData) {
            Object[] values = new Object[] {
                    entity.getTitle(),
                    entity.getDomainReverse(),
                    entity.getRootDomainReverse(),
                    entity.getUrl(),
                    entity.getFolder_level1(),
                    entity.getFolder_level2(),
                    entity.getUri(),
                    entity.getHashId(),
                    entity.getSourceType(),
                    entity.getFoundDate(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getOneWord(),
                    entity.getOneWordCount()
            };

            if(i++ < 10) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);
        }
        executeBatch(sql, batch);
    }
	 
	public List<ContentIdeaEntity> getUniqueHashIdList(String version, Integer pageNum, Integer pageSize){
		String sql = " select hash_id from  " + getTableName(version) + " order by hash_id limit ?, ? ";
		
		return this.findBySql(sql, (pageNum * pageSize), pageSize);
	}
	
}
