package seoclarity.backend.dao.clickhouse.contentidea;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.ContentIdeaKeywordListEntity;
import seoclarity.backend.entity.clickhouse.contentidea.keywordListEntity;

@Repository
public class KeywordListDao extends ClContentIdeaBaseJdbcSupport<ContentIdeaKeywordListEntity>{

	@Override
	public String getTableName() {
		return "actonia_content_idea_keywordlist_insert";
	}
	
	public String getTableName(String version) {
		return "local_actonia_content_idea_keywordlist" + (StringUtils.isBlank(version) ? "" : ("_v" + version));
	}
	
	public void insertForBatch(List<keywordListEntity> insertData) {
        String sql = "INSERT INTO "+ getTableName()
                +" (hash_id, keyowrdList) VALUES (?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (keywordListEntity entity : insertData) {
            Object[] values = new Object[] {
                    entity.getHashId(),
                    entity.getKeywordArrayStr()
            };
            System.out.println(entity.getHashId());
            System.out.println(entity.getKeywordArrayStr());
            if(i++ < 10) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);
        }
        executeBatch(sql, batch);
    }

	
	public List<ContentIdeaKeywordListEntity> getUniqueTitleKeywordList(String version, Integer pageNum, Integer pageSize){
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT ");
		sql.append("     title, ");
		sql.append("     engine_id, ");
		sql.append("     language_id, ");
		sql.append("     keyowrdList ");
		sql.append(" FROM ");
		sql.append(" ( ");
		sql.append("     SELECT ");
		sql.append("         title, ");
		sql.append("         engine_id, ");
		sql.append("         language_id, ");
		sql.append("         hash_id ");
		sql.append("     FROM actonia_content_idea.actonia_content_idea_v5 ");
		sql.append("     ORDER BY hash_id, engine_id, language_id  ");
		sql.append("     LIMIT ? , ?  ");
		sql.append(" ) ");
		sql.append(" ANY LEFT JOIN ");
		sql.append(" ( ");
		sql.append("     SELECT ");
		sql.append("         hash_id, ");
		sql.append("         keyowrdList ");
		sql.append("     FROM actonia_content_idea.actonia_content_idea_keywordlist_insert_v5 ");
		sql.append("     WHERE hash_id GLOBAL IN ");
		sql.append("     ( ");
		sql.append("         SELECT title ");
		sql.append("         FROM actonia_content_idea.actonia_content_idea_v5 ");
		sql.append("         ORDER BY hash_id, engine_id, language_id  ");
		sql.append("         LIMIT ? , ?  ");
		sql.append("     ) ");
		sql.append(" ) USING (hash_id) ");
		
		return this.findBySql(sql.toString(), (pageNum * pageSize), pageSize, (pageNum * pageSize), pageSize);
	}
	
	
}
