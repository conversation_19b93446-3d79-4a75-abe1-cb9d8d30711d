package seoclarity.backend.dao.clickhouse.contentidea;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;

public abstract class ClContentIdeaBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

	@Resource(name="clarityDBContentIdeaDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}

}
