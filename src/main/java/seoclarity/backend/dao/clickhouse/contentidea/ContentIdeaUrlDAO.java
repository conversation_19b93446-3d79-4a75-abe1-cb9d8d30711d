package seoclarity.backend.dao.clickhouse.contentidea;

import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.dao.BaseJdbcSupport;
import seoclarity.backend.entity.ContentIdeaKeywordListEntity;
import seoclarity.backend.entity.ContentIdeaUrlListEntity;

@Repository
public class ContentIdeaUrlDAO extends ClContentIdeaBaseJdbcSupport<ContentIdeaUrlListEntity>{

	@Override
	public String getTableName() {
		return "actonia_content_idea_title_url_v5";
	}
	
	public String getTableName(String version) {
		return "actonia_content_idea_title_url" + (StringUtils.isBlank(version) ? "" : ("_v" + version));
	}
	
	
	private List<ContentIdeaUrlListEntity> getUniqueHashIdList(String version, Integer pageNum, Integer pageSize){
		String sql = " select hash_id from  " + getTableName(version) + " order by hash_id limit ?, ? ";
		
		return this.findBySql(sql, (pageNum * pageSize), pageSize);
	}

	public List<ContentIdeaUrlListEntity> getUniqueTitleUrlList(String version, int pageNum, Integer pageSize) {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT ");
		sql.append("     title, ");
		sql.append("     engine_id, ");
		sql.append("     language_id, ");
		sql.append("     url ");
		sql.append(" FROM ");
		sql.append(" ( ");
		sql.append("     SELECT ");
		sql.append("         title, ");
		sql.append("         engine_id, ");
		sql.append("         language_id, ");
		sql.append("         hash_id ");
		sql.append("     FROM actonia_content_idea.actonia_content_idea_v5 ");
		sql.append("     ORDER BY hash_id, engine_id, language_id ");
		sql.append("     LIMIT ? , ?  ");
		sql.append(" ) ");
		sql.append(" ANY LEFT JOIN ");
		sql.append(" ( ");
		sql.append("     SELECT ");
		sql.append("         hash_id, ");
		sql.append("         url ");
		sql.append("     FROM actonia_content_idea.actonia_content_idea_title_url_v5 ");
		sql.append("     WHERE hash_id GLOBAL IN ");
		sql.append("     ( ");
		sql.append("         SELECT hash_id ");
		sql.append("         FROM actonia_content_idea.actonia_content_idea_v5 ");
		sql.append("         ORDER BY hash_id, engine_id, language_id ");
		sql.append("         LIMIT ? , ?  ");
		sql.append("     ) ");
		sql.append(" ) USING (hash_id) ");
		
		return this.findBySql(sql.toString(), (pageNum * pageSize), pageSize, (pageNum * pageSize), pageSize);
	}
	
}
