package seoclarity.backend.dao.clickhouse.contentidea;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;

public abstract class ClContentIdeaBaseJdbcSupportServer3<T> extends BaseJdbcSupport<T> {

	@Resource(name="clarityDBContentIdeaSever3DataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}

}
