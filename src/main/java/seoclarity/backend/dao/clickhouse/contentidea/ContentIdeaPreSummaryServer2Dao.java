package seoclarity.backend.dao.clickhouse.contentidea;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.clickhouse.contentidea.ContentIdeaSummaryEntity;
import seoclarity.backend.utils.FormatUtils;

@Repository
public class ContentIdeaPreSummaryServer2Dao extends ClContentIdeaBaseJdbcSupportServer2<ContentIdeaSummaryEntity> implements ContentIdeaImpl {

	@Override
	public String getTableName() {
		return "actonia_content_idea_summary";
	}
	
	public void insertForBatch(List<ContentIdeaSummaryEntity> insertData) {
        String sql = "INSERT INTO "+ getTableName()
                +" (title, url, url_title, uri, hash_id, found_date, engine_id, language_id, domain_reverse, "
                + "root_domain_reverse, keyword_count, url_count, avg_search_volume, one_word, "
                + "one_word_count, stemmer_word, ngram_word) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (ContentIdeaSummaryEntity entity : insertData) {
            Object[] values = new Object[] {
                    entity.getTitle(),
                    entity.getUrl(),
                    entity.getUrl_title(),
                    entity.getUri(),
                    entity.getHash_id(),
                    entity.getFound_date(),
                    entity.getEngine_id(),
                    entity.getLanguage_id(),
                    entity.getDomain_reverse(),
                    entity.getRoot_domain_reverse(),
                    entity.getKeyword_count(),
                    entity.getUrl_count(),
                    entity.getAvg_search_volume(),
                    entity.getOne_word(),
                    entity.getOne_word_count(),
                    entity.getStemmer_word(),
                    entity.getNgram_word()
            };

            if(i++ < 10) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);
        }
        executeBatch(sql, batch);
    }
	
	public List<String> getTableListV2(String tableName) {
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForStringList(sql);
	}
	
    public String getTempSummaryTableName(Date date){
		return "local_actonia_content_idea_summary_" + FormatUtils.formatDate(date, "yyyyMMddHH");
	}
	
	public String createTable(Date date) throws Exception{
		String tableName = getTempSummaryTableName(date);
		String sql = "create table " + tableName + " as local_internal_link_view_v5_template "
				+ "  ENGINE = MergeTree(today, (crawl_request_log_id_i, domain_id_i, crawl_date_long), 8192) ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public static final String[] headers = new String[]{
		"title", "url", "url_title", "uri", "hash_id", "found_date", "engine_id", "language_id", 
		"domain_reverse", "root_domain_reverse", "keyword_count", "url_count", "avg_search_volume", 
		"one_word", "one_word_count", "stemmer_word", "ngram_word"
	};
	
    
	public List<String> getTableList(Date date) {
		String tableName = getTempSummaryTableName(date);
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForStringList(sql);
	}
	
    
	public Integer getTotalCount(Date date) {
		String tableName = getTempSummaryTableName(date);
		String sql = " select count() from " + tableName + "";
		System.out.println("sql:" + sql);
		return this.queryForInteger(sql);
	}
	
	public String dropTable(String tableName) throws Exception{
		String sql = "drop table " + tableName;
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String renameTable(String tableName, String oldTableName) {
		String sql = "rename table " + oldTableName + " to " + tableName;
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String getTableList(String tableName) {
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForString(sql);
	}
	
	public String createInsertDistributeTable(String tableName, String templateTableName, String localTableName){
		String sql = "create table " + tableName + " as " + templateTableName
				+ " ENGINE = Distributed('internal-link', 'actonia_content_idea', '" + localTableName + "', rand()) ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String createLocalTable(String tableName, String templateTableName) throws Exception{
		
		this.executeUpdate(" set allow_experimental_data_skipping_indices = 1 ");
		
		String sql = "create table " + tableName + " as " + templateTableName
				+ " ENGINE = MergeTree() PARTITION BY toYYYYMM(found_date) ORDER BY (engine_id, language_id) SETTINGS index_granularity = 4096 ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	 
}
