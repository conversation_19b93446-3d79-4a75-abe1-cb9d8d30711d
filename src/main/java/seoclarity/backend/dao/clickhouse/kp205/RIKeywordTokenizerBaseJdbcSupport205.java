package seoclarity.backend.dao.clickhouse.kp205;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.clickhouse.kp206
 * @author: cil
 * @date: 2021-07-02 10:43
 **/
public abstract class RIKeywordTokenizerBaseJdbcSupport205<T> extends BaseJdbcSupport<T> {
    @Resource(name="clarityDBKeywordDataSourceCdbRi205")
    private DataSource dataSource;

    public DataSource getDataSource() {
        return this.dataSource;
    }


}
