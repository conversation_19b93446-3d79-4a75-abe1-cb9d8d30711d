package seoclarity.backend.dao.clickhouse.rankingforecast;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.RankingForecastEntity;
import seoclarity.backend.utils.FormatUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-07-08 20:22
 **/
@Repository
@CommonsLog
public class RankingForecastDao extends ClRankingForecastBaseJdbcSupport<RankingForecastEntity> {

    private Gson gson = new Gson();

    @Override
    public String getTableName() {
        return "dis_ranking_forecast";
    }

    public void inserBatch(List<RankingForecastEntity> forecastEntities) {
        if (CollectionUtils.isEmpty(forecastEntities)) {
            log.error("forecastEntities is EMPTY, please check.");
            return;
        }
        String sql = "INSERT INTO "+getTableName()
                +" (keyword_name, own_domain_id, keyword_rankcheck_id, engine_id, language_id, location_id, device" +
                ",ranking_date,score,sign,versioning" +
                ") VALUES (?,?,?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<>();
        for (RankingForecastEntity entity : forecastEntities) {
            Object[] values = new Object[]{
                    entity.getKeywordName(),
                    entity.getOwnDomainId(),
                    entity.getKeywordRankcheckId(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getLocationId(),
                    entity.getDevice(),
                    FormatUtils.formatDate(entity.getRankingDate(), FormatUtils.DATE_PATTERN_2),
                    entity.getScore(),
                    entity.getSign(),
                    entity.getVersioning()
            };
            batch.add(values);
        }

        log.info(gson.toJson(batch.get(0)));

        autoRetryBatchInsert(sql, batch);
    }

    public boolean domainExistData(int domainId, Date rankDate) {
        String sql = "select count() from "+getTableName()+" where own_domain_id = ? and ranking_date = ?";
        List<Integer> result = queryForIntegerList(sql, domainId, FormatUtils.formatDate(rankDate, FormatUtils.DATE_PATTERN_2));
        if (CollectionUtils.isNotEmpty(result) && result.get(0) != null && result.get(0) > 0) {
            return true;
        }
        return false;
    }

}
