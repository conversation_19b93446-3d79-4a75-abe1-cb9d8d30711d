package seoclarity.backend.dao.clickhouse.ng007;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.clickhouse.gscclicksteam.EstdSearchVolumeEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.DisManagedSearchvolume;

@Repository
public class DisManagedSearchvolumeNj007DAO extends RiNj007KeywordTokenizerBaseJdbcSupport<DisManagedSearchvolume> {

    @Override
    public String getTableName() {
        return "dis_managed_searchvolume";
    }
//    public String getTableName() {
//        return "tttt";
//    }
//    public String getTableName() {
//            return "dis_managed_searchvolumeV2";
//    }


    public int[] insertBatchByName(List<DisManagedSearchvolume> list,String tableName) {
        System.out.println(Thread.currentThread().getName() + "will be inserte ========== " + list.size());
        String sql = "insert into " + tableName
                + " (keyword_rankcheck_id," +
                "  keyword_name," +
                "  word," +
                "  stream," +
                "  location_id," +
                "  engine_id," +
                "  language_id," +
                "  sign," +
                "  versioning," +
                "  avg_search_volume," +
                "  monthly_search_volume1," +
                "  monthly_search_volume2," +
                "  monthly_search_volume3," +
                "  monthly_search_volume4," +
                "  monthly_search_volume5," +
                "  monthly_search_volume6," +
                "  monthly_search_volume7," +
                "  monthly_search_volume8," +
                "  monthly_search_volume9," +
                "  monthly_search_volume10," +
                "  monthly_search_volume11," +
                "  monthly_search_volume12," +
                "  cpc," +
                "  monthlySvAttr.key," +
                "  monthlySvAttr.value," +
                "  keyword_variation_oneword," +
                "  keyword_variation_ngram," +
                "  category," +
                "  lastRefreshMonth," +
                "  has_sv) "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (DisManagedSearchvolume entity : list) {
            Object[] values = new Object[]{
                    entity.getKeyword_rankcheck_id(),
                    entity.getKeyword_name(),
                    entity.getWord(),
                    entity.getStream(),
                    entity.getLocation_id(),
                    entity.getEngine_id(),
                    entity.getLanguage_id(),
                    entity.getSign(),
                    entity.getVersioning(),
                    entity.getAvg_search_volume(),
                    entity.getMonthly_search_volume1(),
                    entity.getMonthly_search_volume2(),
                    entity.getMonthly_search_volume3(),
                    entity.getMonthly_search_volume4(),
                    entity.getMonthly_search_volume5(),
                    entity.getMonthly_search_volume6(),
                    entity.getMonthly_search_volume7(),
                    entity.getMonthly_search_volume8(),
                    entity.getMonthly_search_volume9(),
                    entity.getMonthly_search_volume10(),
                    entity.getMonthly_search_volume11(),
                    entity.getMonthly_search_volume12(),
                    entity.getCpc(),
                    entity.getMonthlySvAttr_key(),
                    entity.getMonthlySvAttr_value(),
                    entity.getKeyword_variation_oneword(),
                    entity.getKeyword_variation_ngram(),
                    entity.getCategory(),
                    entity.getLastRefreshMonth(),
                    entity.getHas_sv()
            };
            batch.add(values);
        }
        int[] count = this.executeBatch(sql, batch);
        return count;
    }
    
    public int[] insertBatchByNameV2(List<DisManagedSearchvolume> list,String tableName) {
        System.out.println(Thread.currentThread().getName() + "will be inserte ========== " + list.size());
        String sql = "insert into " + tableName
                + " (keyword_rankcheck_id," +
                "  keyword_name," +
                "  word," +
                "  stream," +
                "  location_id," +
                "  engine_id," +
                "  language_id," +
                "  sign," +
                "  versioning," +
                "  avg_search_volume," +
                "  monthly_search_volume1," +
                "  monthly_search_volume2," +
                "  monthly_search_volume3," +
                "  monthly_search_volume4," +
                "  monthly_search_volume5," +
                "  monthly_search_volume6," +
                "  monthly_search_volume7," +
                "  monthly_search_volume8," +
                "  monthly_search_volume9," +
                "  monthly_search_volume10," +
                "  monthly_search_volume11," +
                "  monthly_search_volume12," +
                "  cpc," +
                "  monthlySvAttr.key," +
                "  monthlySvAttr.value," +
                "  true_demand," +
                "  trueDemandAttr.key," +
                "  trueDemandAttr.value," +
                "  keyword_variation_oneword," +
                "  keyword_variation_ngram," +
                "  category," +
                "  lastRefreshMonth," +
                "  has_sv) "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        
        int num = 0;
        List<Object[]> batch = new ArrayList<>();
        for (DisManagedSearchvolume entity : list) {
        	
        	num++;
            Object[] values = new Object[]{
                    entity.getKeyword_rankcheck_id(),
                    entity.getKeyword_name(),
                    entity.getWord(),
                    entity.getStream(),
                    entity.getLocation_id(),
                    entity.getEngine_id(),
                    entity.getLanguage_id(),
                    entity.getSign(),
                    entity.getVersioning(),
                    entity.getAvg_search_volume(),
                    entity.getMonthly_search_volume1(),
                    entity.getMonthly_search_volume2(),
                    entity.getMonthly_search_volume3(),
                    entity.getMonthly_search_volume4(),
                    entity.getMonthly_search_volume5(),
                    entity.getMonthly_search_volume6(),
                    entity.getMonthly_search_volume7(),
                    entity.getMonthly_search_volume8(),
                    entity.getMonthly_search_volume9(),
                    entity.getMonthly_search_volume10(),
                    entity.getMonthly_search_volume11(),
                    entity.getMonthly_search_volume12(),
                    entity.getCpc(),
                    entity.getMonthlySvAttr_key(),
                    entity.getMonthlySvAttr_value(),
                    entity.getTrue_demand(),
                    entity.getTrueDemandAttr_key(),
                    entity.getTrueDemandAttr_value(),
                    entity.getKeyword_variation_oneword(),
                    entity.getKeyword_variation_ngram(),
                    entity.getCategory(),
                    entity.getLastRefreshMonth(),
                    entity.getHas_sv()
                   
            };
            
            if (num <= 10) {
				System.out.println(new Gson().toJson(entity));
			}
            batch.add(values);
        }
        int[] count = this.executeBatch(sql, batch);
        return count;
    }

    public int[] insertBatch(List<DisManagedSearchvolume> list) {
        System.out.println(Thread.currentThread().getName() + "will be inserte ========== " + list.size());
        String sql = "insert into " + getTableName()
                + " (keyword_rankcheck_id," +
                "  keyword_name," +
                "  word," +
                "  stream," +
                "  location_id," +
                "  engine_id," +
                "  language_id," +
                "  sign," +
                "  versioning," +
                "  avg_search_volume," +
                "  monthly_search_volume1," +
                "  monthly_search_volume2," +
                "  monthly_search_volume3," +
                "  monthly_search_volume4," +
                "  monthly_search_volume5," +
                "  monthly_search_volume6," +
                "  monthly_search_volume7," +
                "  monthly_search_volume8," +
                "  monthly_search_volume9," +
                "  monthly_search_volume10," +
                "  monthly_search_volume11," +
                "  monthly_search_volume12," +
                "  cpc," +
                "  monthlySvAttr.key," +
                "  monthlySvAttr.value," +
                "  keyword_variation_oneword," +
                "  keyword_variation_ngram," +
                "  category," +
                "  lastRefreshMonth," +
                "  has_sv) "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (DisManagedSearchvolume entity : list) {
            Object[] values = new Object[]{
                    entity.getKeyword_rankcheck_id(),
                    entity.getKeyword_name(),
                    entity.getWord(),
                    entity.getStream(),
                    entity.getLocation_id(),
                    entity.getEngine_id(),
                    entity.getLanguage_id(),
                    entity.getSign(),
                    entity.getVersioning(),
                    entity.getAvg_search_volume(),
                    entity.getMonthly_search_volume1(),
                    entity.getMonthly_search_volume2(),
                    entity.getMonthly_search_volume3(),
                    entity.getMonthly_search_volume4(),
                    entity.getMonthly_search_volume5(),
                    entity.getMonthly_search_volume6(),
                    entity.getMonthly_search_volume7(),
                    entity.getMonthly_search_volume8(),
                    entity.getMonthly_search_volume9(),
                    entity.getMonthly_search_volume10(),
                    entity.getMonthly_search_volume11(),
                    entity.getMonthly_search_volume12(),
                    entity.getCpc(),
                    entity.getMonthlySvAttr_key(),
                    entity.getMonthlySvAttr_value(),
                    entity.getKeyword_variation_oneword(),
                    entity.getKeyword_variation_ngram(),
                    entity.getCategory(),
                    entity.getLastRefreshMonth(),
                    entity.getHas_sv()
            };
            batch.add(values);
        }
        int[] count = this.executeBatch(sql, batch);
        return count;
    }
    
    public int[] insertBatchV2(List<DisManagedSearchvolume> list) {
        System.out.println(Thread.currentThread().getName() + "will be inserte ========== " + list.size());
        String sql = "insert into " + getTableName()
                + " (keyword_rankcheck_id," +
                "  keyword_name," +
                "  word," +
                "  stream," +
                "  location_id," +
                "  engine_id," +
                "  language_id," +
                "  sign," +
                "  versioning," +
                "  avg_search_volume," +
                "  monthly_search_volume1," +
                "  monthly_search_volume2," +
                "  monthly_search_volume3," +
                "  monthly_search_volume4," +
                "  monthly_search_volume5," +
                "  monthly_search_volume6," +
                "  monthly_search_volume7," +
                "  monthly_search_volume8," +
                "  monthly_search_volume9," +
                "  monthly_search_volume10," +
                "  monthly_search_volume11," +
                "  monthly_search_volume12," +
                "  cpc," +
                "  monthlySvAttr.key," +
                "  monthlySvAttr.value," +
                "  true_demand," +
                "  trueDemandAttr.key," +
                "  trueDemandAttr.value," +
                "  keyword_variation_oneword," +
                "  keyword_variation_ngram," +
                "  category," +
                "  lastRefreshMonth," +
                "  has_sv) "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (DisManagedSearchvolume entity : list) {
            Object[] values = new Object[]{
                    entity.getKeyword_rankcheck_id(),
                    entity.getKeyword_name(),
                    entity.getWord(),
                    entity.getStream(),
                    entity.getLocation_id(),
                    entity.getEngine_id(),
                    entity.getLanguage_id(),
                    entity.getSign(),
                    entity.getVersioning(),
                    entity.getAvg_search_volume(),
                    entity.getMonthly_search_volume1(),
                    entity.getMonthly_search_volume2(),
                    entity.getMonthly_search_volume3(),
                    entity.getMonthly_search_volume4(),
                    entity.getMonthly_search_volume5(),
                    entity.getMonthly_search_volume6(),
                    entity.getMonthly_search_volume7(),
                    entity.getMonthly_search_volume8(),
                    entity.getMonthly_search_volume9(),
                    entity.getMonthly_search_volume10(),
                    entity.getMonthly_search_volume11(),
                    entity.getMonthly_search_volume12(),
                    entity.getCpc(),
                    entity.getMonthlySvAttr_key(),
                    entity.getMonthlySvAttr_value(),
                    entity.getTrue_demand(),
                    entity.getTrueDemandAttr_key(),
                    entity.getTrueDemandAttr_value(),
                    entity.getKeyword_variation_oneword(),
                    entity.getKeyword_variation_ngram(),
                    entity.getCategory(),
                    entity.getLastRefreshMonth(),
                    entity.getHas_sv()
            };
            batch.add(values);
        }
        int[] count = this.executeBatch(sql, batch);
        return count;
    }



    public List<DisManagedSearchvolume> queryExist(Map<String, HashSet<Integer>> keywordAdwordsExpandedMap) {
        StringBuffer sqlbf = new StringBuffer("select distinct engine_id,language_id, location_id,keyword_rankcheck_id from ").append(getTableName()).append(" where ");
        keywordAdwordsExpandedMap.forEach((k, v) -> {
            String[] split = k.split("-");
            sqlbf.append("(engine_id = ").append(split[0])
                    .append(" and language_id = ").append(split[1])
                    .append(" and location_id = ").append(split[2])
                    .append(" and keyword_rankcheck_id in (").append(
                    StringUtils.join(v, ",")).append(")) or ");
        });

        String sql = sqlbf.substring(0, sqlbf.length() - 3);
//        System.out.println("distinct sql = " + sql);
        return this.findBySql(sql);
    }
    public List<Integer> getDistinctId(Map<String, HashSet<Integer>> keywordAdwordsExpandedMap) {
        StringBuffer sqlbf = new StringBuffer("select keyword_rankcheck_id from ").append(getTableName()).append(" where ");
        keywordAdwordsExpandedMap.forEach((k, v) -> {
            String[] split = k.split("-");
            sqlbf.append("(engine_id = ").append(split[0])
                    .append(" and language_id = ").append(split[1])
                    .append(" and location_id = ").append(split[2])
                    .append(" and keyword_rankcheck_id in (").append(
                    StringUtils.join(v, ",")).append(")) or ");
        });

        String sql = sqlbf.substring(0, sqlbf.length() - 3);
//        System.out.println("distinct sql = " + sql);
        return queryForIntegerList(sql);
    }


    public List<String> checkExistKeywordList(Integer engineId, Integer languageId, Integer locationId, List<Long> rankCheckIdList){
        String sql = " SELECT keyword_name from dis_estd_searchvolume ";
        sql += " where keyword_rankcheck_id in (" + StringUtils.join(rankCheckIdList , ",") + ") ";
        sql += " and engine_id= ? and language_id= ? and location_id= ? ";
        return queryForStringList(sql, engineId, languageId, locationId);
    }


    public void batchInsert(List<EstdSearchVolumeEntity> estdSearchVolumeList){
        String sql = "insert into dis_estd_searchvolume(engine_id,language_id,location_id,keyword_rankcheck_id,keyword_name ";
        sql += " ,estd_search_volume,sign,versioning)";
        sql += " values (?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (EstdSearchVolumeEntity entity : estdSearchVolumeList) {
            Object[] values = new Object[]{
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getLocationId(),
                    entity.getKeywordRankcheckId(),
                    entity.getKeywordName(),
                    entity.getEstdSearchVolume(),
                    entity.getSign(),
                    entity.getVersioning()

            };
            batch.add(values);
        }
        this.executeBatch(sql, batch);
    }
    
    public Integer queryForCnt(String date) {
    	
    	String sql = "select count() from dis_managed_searchvolume where create_date = '" + date + "' ";
    	return queryForInt(sql);
    }

}
