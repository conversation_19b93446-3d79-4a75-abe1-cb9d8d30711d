package seoclarity.backend.dao.clickhouse.kp206;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.clickhouse.kp206
 * @author: cil
 * @date: 2021-07-02 10:43
 **/
public abstract class RIKeywordTokenizerBaseJdbcSupport206 <T> extends BaseJdbcSupport<T> {
    @Resource(name="clarityDBKeywordDataSourceCdbRi206")
    private DataSource dataSource;

    public DataSource getDataSource() {
        return this.dataSource;
    }


}
