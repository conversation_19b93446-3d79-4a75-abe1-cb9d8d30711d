package seoclarity.backend.dao.clickhouse.kp206;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.kp207.RIKeywordTokenizerBaseJdbcSupport;
import seoclarity.backend.entity.rankcheck.retrievesv.DisManagedSearchvolume;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.clickhouse.kp207
 * @author: cil
 * @date: 2021-06-11 18:24
 **/
@Repository
public class ManagedSearchvolume206DAO extends RIKeywordTokenizerBaseJdbcSupport206<DisManagedSearchvolume> {


    @Override
    public String getTableName() {
        return "local_managed_searchvolume";
    }

    public void createManagedSearchvolumeTable(String newTableName,String oldTableName){
        String sql = "CREATE TABLE keyword_searchvolume."+ newTableName+" as keyword_searchvolume." + oldTableName+ "  " +
            //"ENGINE = CollapsingMergeTree(create_date, (engine_id, language_id, location_id, keyword_rankcheck_id, key), 8192, sign)";
            "ENGINE = CollapsingMergeTree(sign) PARTITION BY create_date ORDER BY(engine_id, language_id, location_id, keyword_rankcheck_id, key) SETTINGS index_granularity=8192";
        System.out.println(sql);
        this.executeUpdate(sql);
    }

    public void renameTable(String oldTableName,String newTableName){
        String sql = "rename table " +oldTableName + " to "+newTableName+";";
        System.out.println(sql);
        this.executeUpdate(sql);


    }
    public void dropTable(String oldTableName){
        String sql = "drop table " + oldTableName;
        System.out.println(sql);
        this.executeUpdate(sql);
    }

}
