/**
 *
 */
package seoclarity.backend.dao.clickhouse.internallink;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class ClInternalLinkNewClusterBaseJdbcSupportServer1<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDBInternalLinkNewClusterDataSourceServer1")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}

}
