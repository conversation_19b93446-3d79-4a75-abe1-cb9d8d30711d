package seoclarity.backend.dao.clickhouse.internallink;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.clickhouse.internallink.InternalLinkClarityDBEntity;
import seoclarity.backend.entity.clickhouse.internallink.ProcessListVO;
import seoclarity.backend.utils.FormatUtils;

/**
 * Created by <PERSON> on 2017/5/11.
 */
@Repository
public class InternalLinkNewClusterServer3Dao extends ClInternalLinkNewClusterBaseJdbcSupportServer3<InternalLinkClarityDBEntity> implements InternalLinkImpl{

    @Override
    public String getTableName() {
        return "distributed_internal_link_view";
    }

    public void insertForBatch(List<InternalLinkClarityDBEntity> insertData) {
        insertForBatch(insertData, getTableName());
    }
    
    public String getTempSummaryTableName(Date date){
		return "local_internal_link_view_v5_" + FormatUtils.formatDate(date, "yyyyMMddHH");
	}
    
	public String createTable(Date date) throws Exception{
		String tableName = getTempSummaryTableName(date);
		String sql = "create table " + tableName + " as local_internal_link_view_v5_template "
				+ "  ENGINE = MergeTree" + 
				" PARTITION BY crawl_request_log_mod" + 
				" PRIMARY KEY (domain_id_i, crawl_request_log_id_i)" + 
				" ORDER BY (domain_id_i, crawl_request_log_id_i, destination_url_murmur_hash, source_url_murmur_hash, anchor_text_hash)" + 
				" SETTINGS index_granularity = 8192 ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public List<String> getTableListV2(String tableName) {
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForStringList(sql);
	}
	
	public static final String[] headers = new String[]{
		"link_nofollow", "domain_id_i", "analyzed_url_s", "source_domain", "canonical_type",
		"canonical_flg", "anchor_text", "destination_domain", "destination_root_domain", "crawl_date_long",
		"source_url", "page_robots_meta_index", "page_robots_meta_follow", "page_robots_meta_archive", "destination_url",
		"title_md5", "popularity", "title", "canonical", "source_root_domain",
		"analyzed_url_flg_s", "title_flg", "crawl_request_log_id_i", "canonical_string", "destination_folder_level_1",
		"destination_folder_level_2", "source_folder_level_1", "source_folder_level_2", "today", "source_url_response_code",
		"sourceurl_anchortext_hash", "destinationurl_anchortext_hash"
	};

    public void insertForBatch(List<InternalLinkClarityDBEntity> insertData, String tableName) {
    	
    	String sql = "INSERT INTO "+ tableName + "(link_nofollow, domain_id_i, analyzed_url_s, source_domain, canonical_type,"
				+ "canonical_flg, anchor_text, destination_domain, destination_root_domain, crawl_date_long,"
				+ "source_url, page_robots_meta_index, page_robots_meta_follow, page_robots_meta_archive, destination_url,"
				+ "title_md5, popularity, title, canonical, source_root_domain,"
				+ "analyzed_url_flg_s, title_flg, crawl_request_log_id_i, canonical_string, destination_folder_level_1,"
				+ "destination_folder_level_2, source_folder_level_1, source_folder_level_2, today, source_url_response_code,"
				+ "sourceurl_anchortext_hash, destinationurl_anchortext_hash) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    	
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (InternalLinkClarityDBEntity entity : insertData) {
            Object[] values;
			values = new Object[] {
					entity.isLink_nofollow() ? 1 : 0,
			        entity.getDomain_id_i(),
			        entity.getAnalyzed_url_s(),
			        entity.getSource_domain(),
			        entity.getCanonical_type(),
			        entity.isCanonical_flg() ? 1 : 0,
			        FormatUtils.decoderString(entity.getAnchor_text(), true, insertData.size()),
			        entity.getDestination_domain(),
			        entity.getDestination_root_domain(),
			        entity.getCrawl_date_long(),
			        FormatUtils.decoderString(entity.getSource_url(), true, insertData.size()),
			        entity.getPage_robots_meta_index(),
			        entity.getPage_robots_meta_follow(),
			        entity.getPage_robots_meta_archive(),
			        FormatUtils.decoderString(entity.getDestination_url(), true, insertData.size()),
			        entity.getTitle_md5(),
			        entity.getPopularity(),
			        FormatUtils.decoderString(entity.getTitle(), true, insertData.size()),
			        entity.getCanonical(),
			        entity.getSource_root_domain(),
			        entity.isAnalyzed_url_flg_s() ? 1 : 0,
			        entity.isTitle_flg() ? 1 : 0,
			        entity.getCrawl_request_log_id_i(),
			        entity.getCanonical_string(),
			        entity.getDestination_folder_level_1(),
			        entity.getDestination_folder_level_2(),
			        entity.getSource_folder_level_1(),
			        entity.getSource_folder_level_2(),
			        entity.getToday(),			
			        entity.getSource_url_response_code(),			
			        (entity.getSourceurl_anchortext_hash() == null ? 0 : entity.getSourceurl_anchortext_hash()),			
			        (entity.getDestinationurl_anchortext_hash() == null ? 0 : entity.getDestinationurl_anchortext_hash())
			};
			
			if(i++ < 10) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);

            
        }
        executeBatch(sql, batch);
    }

    
    
	public List<String> getTableList(Date date) {
		String tableName = getTempSummaryTableName(date);
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForStringList(sql);
	}
	
    
	public Integer getTotalCount(Date date) {
		String tableName = getTempSummaryTableName(date);
		String sql = " select count() from " + tableName + "";
		System.out.println("sql:" + sql);
		return this.queryForInteger(sql);
	}
	
	
	private static final String DIS_TABLE_NAME = "dis_internal_link_sampled_view_final";
	
	
	public List<ProcessListVO> getSummaryList() {
		String sql = " select crawl_request_log_id_i, domain_id_i, cnt from (select crawl_request_log_id_i, domain_id_i,count() as cnt from distributed_internal_link_view group by crawl_request_log_id_i, domain_id_i) " + 
				" where crawl_request_log_id_i not in ( select crawlRequestLogId from " + DIS_TABLE_NAME + " group by crawlRequestLogId  )";
		System.out.println("sql:" + sql);
		
		return this.findBySql(sql, ProcessListVO.class);
	}
	
	private final static int MAX_RESULT_COUNT = 1000000;
	
	
	public String dropTable(String tableName) throws Exception{
		String sql = "drop table " + tableName;
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String renameTable(String tableName, String oldTableName) throws Exception{
		String sql = "rename table " + oldTableName + " to " + tableName;
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String getTableList(String tableName) {
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForString(sql);
	}
	
	public String createInsertDistributeTable(String tableName, String templateTableName, String localTableName) throws Exception{
		String sql = "create table " + tableName + " as " + templateTableName
				+ " ENGINE = Distributed('cdb_shard', 'actonia_internal_link', '" + localTableName + "', rand()) ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String createLocalTable(String tableName, String templateTableName) throws Exception{
		String sql = "create table " + tableName + " as " + templateTableName
				+ " ENGINE = MergeTree PARTITION BY (crawlRequestLogMod) ORDER BY (crawlRequestLogId, domainId, sourceUrlHash) SAMPLE BY sourceUrlHash SETTINGS index_granularity = 8192 ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
    
    public void summaryTopLevelByDestationUrl(int ownDomainId, int crawlerId) {
    	StringBuffer sql = new StringBuffer();
    	
    	sql.append("  insert into actonia_internal_link.distributed_internal_link_merge(today, domainId, crawlRequestLogId, ");
    	sql.append("              crawlDateLong, url, countInbound, countAnchorText ) ");
    	sql.append("  select now(), any(domain_id_i) as domainId, any(crawl_request_log_id_i) as crawlRequestLogId,  ");
    	sql.append("         any(crawl_date_long) as crawlDateLong, any(destination_url) as destUrl, count(distinct source_url_hash) as countInbound, ");
    	sql.append("         count(distinct anchor_text_hash) as countAnchorText  ");
    	sql.append("          ");
    	sql.append("  from actonia_internal_link.local_internal_link_view_v5 ");
    	sql.append("  WHERE domain_id_i = ? and crawl_request_log_id_i = ? ");
    	sql.append("  group by destination_url_hash  ");
    	sql.append("  SETTINGS max_threads = 1, max_bytes_before_external_group_by = 22000000000 ");
    	
    	System.out.println("SQL: " + sql.toString());
    	this.executeUpdate(sql.toString(), ownDomainId, crawlerId);
    }

	@Override
	public List<ProcessListVO> getCountByPopularity(int ownDomainId, int crawlerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<Integer> getPopularityList(int ownDomainId, int crawlerId) {
		// TODO Auto-generated method stub
		return null;
	}
	
    
}
