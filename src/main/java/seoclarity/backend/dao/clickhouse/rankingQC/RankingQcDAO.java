package seoclarity.backend.dao.clickhouse.rankingQC;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021-05-24
 * @path seoclarity.backend.dao.clickhouse.rankingQC.RankingQcDAO
 * 
 * 
┌─name─────────────────┬─type───┬
│ keyword_name         │ String │
│ keyword_rankcheck_id │ UInt32 │
│ engine_id            │ UInt8  │
│ language_id          │ UInt8  │
│ location_id          │ UInt32 │
│ ranking_date         │ Date   │
│ file_name            │ String │
│ device               │ UInt8  │
│ fps                  │ UInt8  │
│ sign                 │ Int8   │
└──────────────────────┴────────┴
 */
@Repository
public class RankingQcDAO extends RankingQCBaseJDBCSupport<Object> {
	public static final int DEVICE_DESKTOP= 1;
	public static final int DEVICE_MOBILE = 2;
	public static final String TABLE_PREFIX = "daily_qc_";
	
	public static String getTableName(String dateStr) {
		String month = StringUtils.substring(StringUtils.remove(dateStr, "-"), 0, 6);
		return TABLE_PREFIX + month;
	}
	
	/**
	 * 
	 * @param dateStr yyyy-MM-dd
	 */
	public List<Map<String, Object>> checkExists(String dateStr, int engine, int langauge, boolean isMobile, int locationId, List<String> kidSets) {
		int device = isMobile ?  DEVICE_MOBILE : DEVICE_DESKTOP;
		String sql = "select keyword_rankcheck_id, location_id from " + getTableName(dateStr) + " where engine_id = ? " 
				+ " and language_id = ? and location_id = ? and ranking_date = ? and device = ?  ";
		if (kidSets.size() == 1) {
			sql += " and  keyword_rankcheck_id = ? ";
			return this.queryForMapList(sql, engine, langauge, locationId, dateStr, device, Integer.valueOf(kidSets.get(0)));
		} else {
			sql += " and  keyword_rankcheck_id IN (" + StringUtils.join(kidSets, ",") + ") ";
			return this.queryForMapList(sql, engine, langauge, locationId, dateStr, device);
		}
	}

}
