package seoclarity.backend.dao.clickhouse.rankingQC;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;

/**
 * <AUTHOR>
 * @date 2021-05-24
 * @path seoclarity.backend.dao.clickhouse.rankingQC.RankingQCBaseJDBCSupport
 * 
 */
public class RankingQCBaseJDBCSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="clarityDBDailyRankingQCDataSource")
    private DataSource dataSource;

	@Override
	public String getTableName() {
		return null;
	}

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}

}
