package seoclarity.backend.dao.clickhouse.adhoc;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.adhoc.AdhocSearchvolumeEntity;

import java.util.ArrayList;
import java.util.List;


@Repository
public class AdhocSearchvolumeDAO extends ClAdhocSvBaseJdbcSupport<AdhocSearchvolumeEntity> {

    @Override
    public String getTableName() {
        return "dis_adhoc_searchvolume";
    }

    public void insertBatch(List<AdhocSearchvolumeEntity> list) {
        String sql = "insert into " + getTableName()
                + " (project_id,sequence_no,request_date,own_domain_id,retrieve_date,keyword_rankcheck_id, keyword_name, word, stream, location_id, engine_id, language_id, sign, versioning, "
                + " avg_search_volume, "
                + "monthly_search_volume1,"
                + "monthly_search_volume2,"
                + "monthly_search_volume3,"
                + "monthly_search_volume4,"
                + "monthly_search_volume5,"
                + "monthly_search_volume6,"
                + "monthly_search_volume7,"
                + "monthly_search_volume8,"
                + "monthly_search_volume9,"
                + "monthly_search_volume10,"
                + "monthly_search_volume11,"
                + "monthly_search_volume12,"
                + "cpc,"
                + "keyword_variation_oneword,"
                + "keyword_variation_ngram,"
                + "category,"
                + "monthlySvAttr.key,"
                + "monthlySvAttr.value"
                + ") "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (AdhocSearchvolumeEntity entity : list) {
            Object[] values = new Object[]{
                    entity.getProjectId(),
                    entity.getSequenceNo(),
                    entity.getRequestDate(),
                    entity.getOwnDomainId(),
                    entity.getRetrieveDate(),
                    entity.getKeywordRankcheckId(),
                    entity.getKeywordName(),
                    entity.getWordArray(),
                    entity.getStreamArray(),
                    entity.getLocationId(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getSign(),
                    entity.getVersioning(),
                    entity.getAvgSearchVolume(),
                    entity.getMonthlySearchVolume1(),
                    entity.getMonthlySearchVolume2(),
                    entity.getMonthlySearchVolume3(),
                    entity.getMonthlySearchVolume4(),
                    entity.getMonthlySearchVolume5(),
                    entity.getMonthlySearchVolume6(),
                    entity.getMonthlySearchVolume7(),
                    entity.getMonthlySearchVolume8(),
                    entity.getMonthlySearchVolume9(),
                    entity.getMonthlySearchVolume10(),
                    entity.getMonthlySearchVolume11(),
                    entity.getMonthlySearchVolume12(),
                    entity.getCpc(),
                    entity.getKeywordVariationOnewordArray(),
                    entity.getKeywordVariationNgramArray(),
                    entity.getCategoryArray(),
                    entity.getMonthlySvAttrKey(),
                    entity.getMonthlySvAttrValue()
            };
            batch.add(values);
        }

        int[] count = this.executeBatch(sql, batch);
    }

    public void insertBatchForNullSv(List<AdhocSearchvolumeEntity> list) {
        String sql = "insert into " + getTableName()
                + " (project_id,sequence_no,request_date,own_domain_id,retrieve_date,keyword_rankcheck_id, keyword_name, word, stream, location_id, engine_id, language_id, sign, versioning, "
                + "keyword_variation_oneword,"
                + "keyword_variation_ngram,"
                + "category,"
                + "monthlySvAttr.key,"
                + "monthlySvAttr.value"
                + ") "
        + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (AdhocSearchvolumeEntity entity : list) {
            Object[] values = new Object[]{
                    entity.getProjectId(),
                    entity.getSequenceNo(),
                    entity.getRequestDate(),
                    entity.getOwnDomainId(),
                    entity.getRetrieveDate(),
                    entity.getKeywordRankcheckId(),
                    entity.getKeywordName(),
                    entity.getWordArray(),
                    entity.getStreamArray(),
                    entity.getLocationId(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getSign(),
                    entity.getVersioning(),
                    entity.getKeywordVariationOnewordArray(),
                    entity.getKeywordVariationNgramArray(),
                    entity.getCategoryArray(),
                    entity.getMonthlySvAttrKey(),
                    entity.getMonthlySvAttrValue()
            };
            batch.add(values);
        }

        int[] count = this.executeBatch(sql, batch);
    }

    public void insertBatchForNullCpc(List<AdhocSearchvolumeEntity> list) {
        String sql = "insert into " + getTableName()
                + " (project_id,sequence_no,request_date,own_domain_id,retrieve_date,keyword_rankcheck_id, keyword_name, word, stream, location_id, engine_id, language_id, sign, versioning, "
                + " avg_search_volume, "
                + "monthly_search_volume1,"
                + "monthly_search_volume2,"
                + "monthly_search_volume3,"
                + "monthly_search_volume4,"
                + "monthly_search_volume5,"
                + "monthly_search_volume6,"
                + "monthly_search_volume7,"
                + "monthly_search_volume8,"
                + "monthly_search_volume9,"
                + "monthly_search_volume10,"
                + "monthly_search_volume11,"
                + "monthly_search_volume12,"
                + "keyword_variation_oneword,"
                + "keyword_variation_ngram,"
                + "category,"
                + "monthlySvAttr.key,"
                + "monthlySvAttr.value"
                + ") "
                + " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (AdhocSearchvolumeEntity entity : list) {
            Object[] values = new Object[]{
                    entity.getProjectId(),
                    entity.getSequenceNo(),
                    entity.getRequestDate(),
                    entity.getOwnDomainId(),
                    entity.getRetrieveDate(),
                    entity.getKeywordRankcheckId(),
                    entity.getKeywordName(),
                    entity.getWordArray(),
                    entity.getStreamArray(),
                    entity.getLocationId(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getSign(),
                    entity.getVersioning(),
                    entity.getAvgSearchVolume(),
                    entity.getMonthlySearchVolume1(),
                    entity.getMonthlySearchVolume2(),
                    entity.getMonthlySearchVolume3(),
                    entity.getMonthlySearchVolume4(),
                    entity.getMonthlySearchVolume5(),
                    entity.getMonthlySearchVolume6(),
                    entity.getMonthlySearchVolume7(),
                    entity.getMonthlySearchVolume8(),
                    entity.getMonthlySearchVolume9(),
                    entity.getMonthlySearchVolume10(),
                    entity.getMonthlySearchVolume11(),
                    entity.getMonthlySearchVolume12(),
                    entity.getKeywordVariationOnewordArray(),
                    entity.getKeywordVariationNgramArray(),
                    entity.getCategoryArray(),
                    entity.getMonthlySvAttrKey(),
                    entity.getMonthlySvAttrValue()
            };
            batch.add(values);
        }

        int[] count = this.executeBatch(sql, batch);
    }

    public List<AdhocSearchvolumeEntity> getSvListByKeywordHashList(int projectId, int cityId, List<String> keywordHashList){
        String sql = " select keyword_name,keyword_hash,word,stream,avg_search_volume,location_id," +
                "monthly_search_volume1," +
                "monthly_search_volume2," +
                "monthly_search_volume3," +
                "monthly_search_volume4," +
                "monthly_search_volume5," +
                "monthly_search_volume6," +
                "monthly_search_volume7," +
                "monthly_search_volume8," +
                "monthly_search_volume9," +
                "monthly_search_volume10," +
                "monthly_search_volume11," +
                "monthly_search_volume12," +
                "cpc," +
                "category," +
                "keyword_variation_oneword as keywordVariationOneword," +
                "keyword_variation_ngram as keywordVariationNgram," +
                "monthlySvAttr.key as monthlySvAttrKey," +
                "monthlySvAttr.value as monthlySvAttrValue" +
                " from " + getTableName() + " where project_id = ? and location_id = ? " +
                "and keyword_hash in (" + StringUtils.join(keywordHashList, ",") + ")";
        System.out.println("=====SQLgetSvListByKeywordHashList:" + sql);
        return findBySql(sql, projectId, cityId);
    }

}
