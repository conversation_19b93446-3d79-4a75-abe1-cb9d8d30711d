package seoclarity.backend.dao.clickhouse.adhoc;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @create 2019-05-20
 **/
public abstract class ClAdhocBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name="clarityDBAdhocDataSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }

}
