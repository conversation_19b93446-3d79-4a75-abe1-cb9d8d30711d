package seoclarity.backend.dao.clickhouse.ri;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.clickhouse.ClBaseJdbcSupport;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;

import java.util.List;
import java.util.Map;

@Repository
public class ClarityDBRIDao extends ClarityDBRIJdbcSupport<CLRankingDetailEntity> {
    @Override
    public String getTableName() {
        return null;
    }
    public List<Map<String,Object>> queryListMap (String sql){

        return this.queryForMapList(sql);
    }

    public List<Map<String, Object>> executeSql(String sql, List<Object> params) {
        if (params != null && params.size() > 0) {
            return this.queryForMapList(sql, params.toArray(new Object[params.size()]));
        } else {
            return this.queryForMapList(sql);
        }
    }

}
