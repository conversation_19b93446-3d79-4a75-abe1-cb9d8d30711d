package seoclarity.backend.dao.clickhouse;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2021-08-04
 * @path seoclarity.backend.dao.clickhouse.CICentralBaseJdbcSupport
 * 
 */
public abstract class ThgCkRgBaseJdbcSupport2<T> extends BaseJdbcSupport<T> {

	@Resource(name="thgCkRgDao2")
    private DataSource dataSource;

	public DataSource getDataSource() {
		return this.dataSource;
	}
}
