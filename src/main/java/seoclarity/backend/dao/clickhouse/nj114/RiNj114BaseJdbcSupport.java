package seoclarity.backend.dao.clickhouse.nj114;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

public abstract class RiNj114BaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name="clarityDBNj114KeywordSearchVolumeDateSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return this.dataSource;
    }

}
