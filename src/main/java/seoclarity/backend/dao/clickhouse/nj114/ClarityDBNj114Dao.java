package seoclarity.backend.dao.clickhouse.nj114;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.EstdSearchVolumeEntity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class ClarityDBNj114Dao extends RiNj114BaseJdbcSupport<CLRankingDetailEntity> {

    @Override
    public String getTableName() {
        return null;
    }

    public void batchInsert(List<EstdSearchVolumeEntity> estdSearchVolumeList){
        String sql = "insert into dis_estd_searchvolume(engine_id,language_id,location_id,keyword_rankcheck_id,keyword_name ";
        sql += " ,estd_search_volume,sign,versioning)";
        sql += " values (?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (EstdSearchVolumeEntity entity : estdSearchVolumeList) {
            Object[] values = new Object[]{
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getLocationId(),
                    entity.getKeywordRankcheckId(),
                    entity.getKeywordName(),
                    entity.getEstdSearchVolume(),
                    entity.getSign(),
                    entity.getVersioning()

            };
            batch.add(values);
        }
        autoRetryBatchInsert(sql, batch);
    }

}
