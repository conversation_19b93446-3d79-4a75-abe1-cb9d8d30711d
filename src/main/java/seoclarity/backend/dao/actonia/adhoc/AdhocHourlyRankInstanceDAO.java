package seoclarity.backend.dao.actonia.adhoc;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.adhoc.AdhocHourlyRankInstanceEntity;

import java.util.List;

@Repository
public class AdhocHourlyRankInstanceDAO extends ActoniaBaseJdbcSupport<AdhocHourlyRankInstanceEntity> {
    @Override
    public String getTableName() {
        return "adhoc_hourly_rank_instance";
    }

    public List<AdhocHourlyRankInstanceEntity> getCompletedTask() {
        StringBuilder sbf = new StringBuilder();
        sbf.append(" select id,projectId,startDay,startHour,status from " + getTableName());
        // and  startHour <> HOUR(NOW()), upload could be delay, so extract should not run for current hour.
        // requirment from Alps
        sbf.append(" where status = 2 and (extractStatus is null or extractStatus in(0,3)) ");
        sbf.append(" and startHour = HOUR(DATE_SUB(NOW(), INTERVAL 1 HOUR)) and  startDay = DATE_FORMAT(NOW(), '%Y%m%d') ");
        String sql = sbf.toString();
        System.out.println("=====getCompletedTask: " + sql);
        return findBySql(sql);
    }

    public List<AdhocHourlyRankInstanceEntity> getCompulsoryTaskByDelayHour(int delayHour) {
        StringBuilder sbf = new StringBuilder();
        sbf.append(" select id,projectId,startDay,startHour,status from " + getTableName());
        sbf.append(" where (extractStatus is null or extractStatus in(0,3)) and startHour < HOUR(NOW()) ");
        sbf.append(" and startHour >= HOUR(DATE_SUB(NOW(), INTERVAL " + delayHour + " HOUR)) and  startDay = DATE_FORMAT(NOW(), '%Y%m%d') ");
        String sql = sbf.toString();
        System.out.println("=====getCompulsoryTaskByDelayHour: " + sql);
        return findBySql(sql);
    }

    public void updateExtractStatus(int id, int extractStatus) {
        String sql = "update adhoc_hourly_rank_instance set extractStatus=? where id=?";
        executeUpdate(sql, extractStatus, id);
    }

}
