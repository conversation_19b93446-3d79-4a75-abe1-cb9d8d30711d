package seoclarity.backend.dao.actonia.adhoc;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankDetailEntity;

@Repository
public class AutoAdhocRankDetailEntityDAO extends ActoniaBaseJdbcSupport<AutoAdhocRankDetailEntity> {
    @Override
    public String getTableName() {
        return "auto_adhoc_rank_detail";
    }

    public AutoAdhocRankDetailEntity getByProjectId(int projectId){

        String sql = " select * from " + getTableName() + " where projectId = ? limit 1 ";

        return findObject(sql,projectId);
    }

}
