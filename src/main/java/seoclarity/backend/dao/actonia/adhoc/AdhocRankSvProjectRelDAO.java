package seoclarity.backend.dao.actonia.adhoc;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.adhoc.AdhocRankSvProjectRelEntity;

import java.util.List;

@Repository
public class AdhocRankSvProjectRelDAO extends ActoniaBaseJdbcSupport<AdhocRankSvProjectRelEntity> {
    @Override
    public String getTableName() {
        return "adhoc_rank_sv_project_rel";
    }

    public List<Integer> getParentProjectId(Integer childProjectId) {
        String sql = "SELECT parentProjectId FROM " + getTableName() + " WHERE childProjectId = ?";
        return queryForIntegerList(sql, childProjectId);
    }

    public List<Integer> getNotFinishedParentSvProjectList(int childProjectId, int parentProjectId) {
        String sql = "SELECT parentProjectId FROM " + getTableName() + " t1";
        sql += " join auto_adhoc_rank_project t2 on t1.parentProjectId = t2.id";
        sql += " WHERE childProjectId = " + childProjectId + " and parentProjectId != " + parentProjectId + " and t2.status != 1003 ";
        System.out.println("===SQLgetNotFinishedParentSvProjectList:" +sql);
        return queryForIntegerList(sql);
    }

}
