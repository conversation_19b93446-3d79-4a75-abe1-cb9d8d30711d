/**
 *
 */
package seoclarity.backend.dao.actonia;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class ActoniaBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="actoniaDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
