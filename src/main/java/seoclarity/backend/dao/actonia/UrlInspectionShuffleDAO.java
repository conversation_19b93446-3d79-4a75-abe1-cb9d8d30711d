package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.UrlInspectionShuffleEntity;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class UrlInspectionShuffleDAO extends ActoniaBaseJdbcSupport<UrlInspectionShuffleEntity> {

    @Override
    public String getTableName() {
        return "url_inspection_shuffle";
    }

    public void insertData(List<UrlInspectionShuffleEntity> urlInspectionShuffleList) {
        String sql = "insert ignore into " + getTableName() + " (frequency, periodStartDate, sendDate, dataSource, url," + 
        	" urlMurmur3Hash, ownDomainId, profileId, profileList, sendStatus, createDate) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (UrlInspectionShuffleEntity entity : urlInspectionShuffleList) {
            batch.add(new Object[] { entity.getFrequency(), entity.getPeriodStartDate(), entity.getSendDate(), entity.getDataSource(), entity.getUrl(),
            	entity.getUrlMurmur3Hash(), entity.getOwnDomainId(), entity.getProfileId(), entity.getProfileList(), entity.getSendStatus(), new Date()
            });
        }
        this.executeBatch(sql, batch);
    }
    
    public int getUrlCount(int dataSource, Date periodStartDate) {
    	StringBuffer sqlBuff = new StringBuffer();
    	sqlBuff.append("select count(*) from ").append(getTableName()).append(" where dataSource=? and periodStartDate=?");
    	return this.queryForInteger(sqlBuff.toString(), dataSource, periodStartDate);
    }
    
    public List<UrlInspectionShuffleEntity> getUrlList(int dataSource, int frequency, Date periodStartDate, int sendDate, int pageSize) {
        StringBuffer sqlBuff = new StringBuffer();
        sqlBuff.append("select uis.periodStartDate, uis.dataSource, uis.url, uis.urlMurmur3Hash, uis.ownDomainId, uis.profileId, uis.profileList, uis.sendStatus,");
        sqlBuff.append("  rel.gwm_domain_name, (case when (rel.gwm_domain_name like 'http://%' or rel.gwm_domain_name like 'https://%')");
        sqlBuff.append("   and rel.gwm_domain_name not like '%/' then concat(rel.gwm_domain_name, '/') else rel.gwm_domain_name end) formattedProfileName,");
        sqlBuff.append("  ge.gsc_refresh_token, gop.clientId, gop.encryptSecret FROM ").append(getTableName()).append(" uis ");
        sqlBuff.append(" join t_own_domain_setting tods on uis.ownDomainId=tods.own_domain_id ");
        sqlBuff.append(" join gsc_entity ge on ge.gsc_account=tods.gwt_account ");
        sqlBuff.append(" join google_oauth_project gop on ge.oauth_id=gop.id ");
        sqlBuff.append(" join gwm_domain_rel rel on uis.ownDomainId=rel.own_domain_id and uis.profileId=rel.id and uis.dataSource=rel.data_source ");
        sqlBuff.append(" where uis.dataSource=? and uis.frequency=? and uis.periodStartDate=? and uis.sendDate=? and uis.sendStatus in(0,1,3)");
        sqlBuff.append("  and ge.oauth_id>0 order by uis.urlMurmur3Hash limit ").append(pageSize);;
        return this.findBySql(sqlBuff.toString(), dataSource, frequency, periodStartDate, sendDate);
    }
    
    public void updateSendStatus(int dataSource, int frequency, Date periodStartDate, int sendDate, List<String> urlMurmur3HashList) {
    	StringBuffer sqlBuff = new StringBuffer();
    	sqlBuff.append("update ").append(getTableName()).append(" set sendStatus=?, sendTime=NOW() ");
    	sqlBuff.append(" where dataSource=? and frequency=? and periodStartDate=? and sendDate=? and sendStatus IN(0,1,3) ");
    	sqlBuff.append(" and urlMurmur3Hash IN (").append(StringUtils.join(urlMurmur3HashList, ",")).append(")");
 		this.executeUpdate(sqlBuff.toString(), UrlInspectionShuffleEntity.SEND_STATUS_SENT_SUCCESS, dataSource, frequency, periodStartDate, sendDate);
     }
}