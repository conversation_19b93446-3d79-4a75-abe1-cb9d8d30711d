package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.RankIndexParamEntity;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class RankIndexParamEntityDAO extends ActoniaBaseJdbcSupport<RankIndexParamEntity> {

	@Override
	public String getTableName() {
		return "rank_index_param_entity";
	}

	// map key = rank position
	// map value = click thru rate
	public Map<Integer, BigDecimal> getRankClickThruRateMap(int domainId, int groupTagId) {
		Map<Integer, BigDecimal> rankClickThruRateMap = new HashMap<Integer, BigDecimal>();
		List<RankIndexParamEntity> rankIndexParamEntityList = null;
		Integer rankPosition = null;
		Float parameterValueFloat = null;
		BigDecimal clickThruRate = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select ");
		stringBuilder.append("     * ");
		stringBuilder.append(" from ");
		stringBuilder.append("     rank_index_param_entity");
		stringBuilder.append(" where ");
		stringBuilder.append("     own_domain_id = ? ");
		stringBuilder.append(" and grouptag_id = ?");
		String sqlString = stringBuilder.toString();
		rankIndexParamEntityList = findBySql(sqlString, domainId, groupTagId);

		// when client domain has not specified CTR percents in the setting page, retrieve default values from domain 0
		if (rankIndexParamEntityList == null || rankIndexParamEntityList.isEmpty()) {
			rankIndexParamEntityList = findBySql(sqlString, 0, 0);
		}

		if (rankIndexParamEntityList != null && !rankIndexParamEntityList.isEmpty()) {
			for (RankIndexParamEntity rankIndexParamEntity : rankIndexParamEntityList) {
				rankPosition = rankIndexParamEntity.getRankPosition();
				parameterValueFloat = rankIndexParamEntity.getParamValue();
				if (rankPosition != null && parameterValueFloat != null) {
					clickThruRate = new BigDecimal(parameterValueFloat);
					rankClickThruRateMap.put(rankPosition, clickThruRate);
				}
			}
		}
		// when default values from domain 0 cannot be retrieved, use the hardcoded default values
		else {
			rankClickThruRateMap.put(1, new BigDecimal(30));
			rankClickThruRateMap.put(2, new BigDecimal(24));
			rankClickThruRateMap.put(3, new BigDecimal(15));
			rankClickThruRateMap.put(4, new BigDecimal(10));
			rankClickThruRateMap.put(5, new BigDecimal(8));
			rankClickThruRateMap.put(6, new BigDecimal(5));
			rankClickThruRateMap.put(7, new BigDecimal(4));
			rankClickThruRateMap.put(8, new BigDecimal(3));
			rankClickThruRateMap.put(9, new BigDecimal(1));
			rankClickThruRateMap.put(10, new BigDecimal(0));
		}
		return rankClickThruRateMap;
	}
	
	
	public List<RankIndexParamEntity> getRankIndexParams(int ownDomainId, int groupTagId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from rank_index_param_entity");
		sql.append(" where own_domain_id=? and grouptag_id = ?");
		sql.append(" order by rank_position");
		System.out.println(" ===================getRankIndexParams===============");
		System.out.println( sql.toString());
		return findBySql(sql.toString(), ownDomainId, groupTagId);
	}

	public List<RankIndexParamEntity> getRankIndexParamsByIds(List<Integer> domainIdList) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from rank_index_param_entity");
		sql.append(" where own_domain_id in (").append(StringUtils.join(domainIdList, ",")).append(") ");
		sql.append(" order by rank_position");
		System.out.println("==========getRankIndexParams: " + sql + " param: " + domainIdList.toString());
		return findBySql(sql.toString());
	}
}
