package seoclarity.backend.dao.actonia;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.KeywordRankSerpEntity;

@Repository("keywordRankSerpDAO")
public class KeywordRankSerpDAO extends ActoniaBaseJdbcSupport<KeywordRankSerpEntity> {

    @Override
    public String getTableName() {
        return "keyword_rank_serp";
    }

    public boolean checkexists(KeywordRankSerpEntity keywordRankSerpEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from ").append(getTableName());
		sql.append(" where engineId=? and languageId=? and device=? and keywordName=? and rankDate = ?");
		KeywordRankSerpEntity dbRecord = findObject(sql.toString(), 
				keywordRankSerpEntity.getEngineId(),
				keywordRankSerpEntity.getLanguageId(), 
				keywordRankSerpEntity.getDevice(), 
				keywordRankSerpEntity.getKeywordName(),
				keywordRankSerpEntity.getRankDate());

		if (dbRecord != null && dbRecord.getId() != null) {
			return true;
		}
		return false;
	}

	public void insert(KeywordRankSerpEntity keywordRankSerpEntity) {
		
		StringBuffer sql = new StringBuffer();
		sql.append(" insert into ").append(getTableName());
		sql.append(" (engineId, languageId, device, keywordName, rankDate, rankJson)VALUES(?,?,?,?,?,?)");
		
		this.executeUpdate(sql.toString(), 
				keywordRankSerpEntity.getEngineId(),
				keywordRankSerpEntity.getLanguageId(),
				keywordRankSerpEntity.getDevice(),
				keywordRankSerpEntity.getKeywordName(),
				keywordRankSerpEntity.getRankDate(),
				keywordRankSerpEntity.getRankJson());
		
	}

}