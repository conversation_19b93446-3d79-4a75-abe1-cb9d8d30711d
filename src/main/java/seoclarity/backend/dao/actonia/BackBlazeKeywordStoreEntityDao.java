package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.BackBlazeKeywordStoreEntity;
import seoclarity.backend.entity.BackBlazeSourceFileS3Entity;

import java.util.*;

@Repository
public class BackBlazeKeywordStoreEntityDao extends ActoniaBaseJdbcSupport<BackBlazeKeywordStoreEntity>{
    @Override
    public String getTableName() {
        return "backblaze_keyword_store";
    }

    public void insertBatchIgnoreDup(List<BackBlazeKeywordStoreEntity> entities) {
        String sql = "insert ignore into " + getTableName() + " (" +
                "rankDate, " +
                "keywordType ," +
                "engineId ," +
                "languageId ," +
                "device ," +
                "cityQueryName ," +
                "keywordName  ," +
                "rawKeywordName  ," +
                "cdbKeywordHash  ," +
                "cdbKeywordMurmur3hash  ," +
                "cdbEnocdeKeywordHash  ," +
                "metaFileId  ," +
                "fileName  ," +
                "alternativeName   ," +
                "renamed    ," +
                "sourceFileId  " +
                ")" +
                " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (BackBlazeKeywordStoreEntity entity : entities) {
            Object[] values = new Object[]{
                    entity.getRankDate(),
                    entity.getKeywordType(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getDevice(),
                    entity.getCityQueryName(),
                    entity.getKeywordName(),
                    entity.getRawKeywordName(),
                    entity.getCdbKeywordHash(),
                    entity.getCdbKeywordMurmur3hash(),
                    entity.getCdbEnocdeKeywordHash(),
                    entity.getMetaFileId(),
                    entity.getFileName(),
                    entity.getAlternativeName(),
                    entity.getRenamed(),
                    entity.getSourceFileId()
            };
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    /**
     * 插入cdbKeywordHash会报错, 因为spring3+会将数据库中的bigint类型自动映射为long,而实体类中定义的 hash值是20位的数字
     * @param entity
     * @return
     */
    public Integer insert(BackBlazeKeywordStoreEntity entity) {
        Map<String, Object> val = new HashMap<>();
        val.put("rankDate", entity.getRankDate());
        val.put("keywordType", entity.getKeywordType());
        val.put("engineId", entity.getEngineId());
        val.put("languageId", entity.getLanguageId());
        val.put("device", entity.getDevice());
        val.put("cityQueryName", entity.getCityQueryName());
        val.put("keywordName", entity.getKeywordName());
        val.put("rawKeywordName", entity.getRawKeywordName());
        val.put("cdbKeywordHash", entity.getCdbKeywordHash());
        val.put("cdbKeywordMurmur3hash", entity.getCdbKeywordMurmur3hash());
        val.put("cdbEnocdeKeywordHash", entity.getCdbEnocdeKeywordHash());
        val.put("metaFileId", entity.getMetaFileId());
        val.put("fileName", entity.getFileName());
        val.put("alternativeName ", entity.getAlternativeName());
        val.put("renamed ", entity.getRenamed());
        val.put("sourceFileId", entity.getSourceFileId());
        val.put("createDate", new Date());
        return this.insert(val);
    }

    public List<BackBlazeKeywordStoreEntity> getListForUpdateKeywordType(int pageNum, int pageSize) {
        String sql = "select bmfo.fullPathFolder,bks.* from backblaze_keyword_store bks " +
                "left join backblaze_meta_file bmf on bks.metaFileId = bmf.id " +
                "left join backblaze_meta_folder bmfo on bmfo.id = bmf.metaFolderId " +
                //5773103: max id for old data
                "where bks.id <= 5773103 " +
                "order by bks.id " +
                "limit ? , ? ";
        return this.findBySql(sql, (pageNum * pageSize), pageSize);
    }


    public List<BackBlazeKeywordStoreEntity> getListForReCheckByUniqueKey(int rankDate, int keywordType, int engineId, int languageId,
                                                                          String device, String cityQueryName,
                                                                          List<String> cdbKeywordHashList) {
        StringBuffer sbf = new StringBuffer();
        sbf.append(" select * from backblaze_keyword_store where rankDate = ? and keywordType = ? and engineId = ? and languageId = ? and device = ? and cityQueryName = ? ");
        sbf.append(" and cdbKeywordHash in ('").append(StringUtils.join(cdbKeywordHashList, "','")).append("')");
//
//        String sql = "select * from backblaze_keyword_store where rankDate = "+rankDate+" and keywordType = "+keywordType+" and engineId = "+engineId
//                +" and languageId = "+languageId+" and device = '"+device+"' and cityQueryName = '"+cityQueryName
//                +"' and cdbKeywordHash in ('" + StringUtils.join(cdbKeywordHashList, "','") +"')";
//
//        if (flag) {
//            System.out.println("SQL: " + sql);
//        }
        return this.findBySql(sbf.toString(), rankDate, keywordType, engineId, languageId, device, cityQueryName);
    }


    public List<BackBlazeKeywordStoreEntity> getListForUpdateSourceFileId() {
        String sql = "select * from backblaze_keyword_store where sourceFileId = 0 ";
        return this.findBySql(sql);
    }

    public void updateKeywordTypeById(long id, int currentKeywordType, int expectKeywordType) {
        StringBuffer sbf = new StringBuffer();
        sbf.append(" update ").append(getTableName()).append(" set keywordType = ? ");
        sbf.append(" where id = ? and keywordType = ? ");
        this.executeUpdate(sbf.toString(), expectKeywordType, id, currentKeywordType);
    }

    public void updateSourceFileIdById(long id, long sourceFileId) {
        String sql = " update " + getTableName() + " set sourceFileId = ? where id = ? ";
        this.executeUpdate(sql, sourceFileId, id);
    }

    public void updateFileNameById(long id, String fileName) {
        String sql = " update " + getTableName() + " set fileName = ? where id = ? ";
        this.executeUpdate(sql, fileName, id);
    }

    public List<String> getFileNames(String fileId) {
        StringBuilder sql = new StringBuilder();
        sql.append("select keywordName from backblaze_keyword_store where metaFileId = ").append(fileId);
        System.out.println( sql.toString());
        return this.queryForStringList(sql.toString());
    }

    public List<Map<String, Object>> getZip(String date) {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        sql.append(" 	t1.keywordType,");
        sql.append(" 	t1.SE,");
        sql.append(" 	t1.KW,");
        sql.append(" 	t1.fileName,");
        sql.append(" 	t1.zipFile,");
        sql.append(" 	zipFileName,");
        sql.append(" 	substring_index(path, 'serp-html/', - 1) as foder,");
        sql.append(" 	t1.srcS3File,");
        sql.append(" 	t1.metaFileId,");
        sql.append(" 	count(t2.metaFileId) cntInZip ");
        sql.append(" FROM");
        sql.append(" 	(");
        sql.append(" 		SELECT");
        sql.append(" 			kw.id,");
        sql.append(" 			keywordType,");
        sql.append(" 			concat( ");
        sql.append(" 				engineId,");
        sql.append(" 				'_',");
        sql.append(" 				languageId,");
        sql.append(" 				'_',");
        sql.append(" 				device,");
        sql.append(" 				'_',");
        sql.append(" 				ifNull(cityQueryName, '0')");
        sql.append(" 			) SE,");
        sql.append(" 			keywordName KW,");
        sql.append(" 			concat(");
        sql.append(" 				fileName,");
        sql.append(" 				'(renamed:',");
        sql.append(" 				renamed,");
        sql.append(" 				' alternativeName:',");
        sql.append(" 				ifNull(alternativeName, ''),");
        sql.append(" 				')'");
        sql.append(" 			) fileName,");
        sql.append(" 			metaFileId,");
        sql.append(" 			concat(");
        sql.append(" 				mfolder.fullPathFolder,");
        sql.append(" 				'',");
        sql.append(" 				zipFileName");
        sql.append(" 			) zipFile,");
        sql.append(" 			zipFileName,");
        sql.append(" 			mfolder.fullPathFolder path,");
        sql.append(" 			concat(");
        sql.append(" 				srcS3.fullPathFolder,");
        sql.append(" 				'',");
        sql.append(" 				sourceFileName,");
        sql.append(" 				'(size:',");
        sql.append(" 				sourceFileSize,");
        sql.append(" 				')'");
        sql.append(" 			) srcS3File,");
        sql.append(" 			processStatus ");
        sql.append(" 		FROM");
        sql.append(" 			(");
        sql.append(" 				SELECT DISTINCT");
        sql.append(" 					id");
        sql.append(" 				FROM");
        sql.append(" 					(");
        sql.append(" 						SELECT");
        sql.append(" 							min(id) AS id");
        sql.append(" 						FROM");
        sql.append(" 							backblaze_keyword_store");
        sql.append(" 						WHERE");
        sql.append(" 							rankDate = ").append(date);
        sql.append(" 						UNION ALL");
        sql.append(" 							SELECT");
        sql.append(" 								max(id)");
        sql.append(" 							FROM");
        sql.append(" 								backblaze_keyword_store");
        sql.append(" 							WHERE");
        sql.append(" 								rankDate = ").append(date);
        sql.append(" 							UNION ALL");
        sql.append(" 								SELECT");
        sql.append(" 									maxId");
        sql.append(" 								FROM");
        sql.append(" 									(");
        sql.append(" 										SELECT");
        sql.append(" 											keywordType,");
        sql.append(" 											max(id) maxId");
        sql.append(" 										FROM");
        sql.append(" 											backblaze_keyword_store");
        sql.append(" 										WHERE ");
        sql.append(" 											rankDate = ").append(date);
        sql.append(" 										GROUP BY");
        sql.append(" 											keywordType");
        sql.append(" 									) m");
        sql.append(" 								UNION ALL");
        sql.append(" 									(");
        sql.append(" 										SELECT");
        sql.append(" 											id");
        sql.append(" 										FROM");
        sql.append(" 											backblaze_keyword_store");
        sql.append(" 										WHERE");
        sql.append(" 											rankDate = ").append(date);
        sql.append(" 										AND keywordType != 1");
        sql.append(" 										LIMIT 1");
        sql.append(" 									)");
        sql.append(" 					) t");
        sql.append(" 				LIMIT 8");
        sql.append(" 			) tmp");
        sql.append(" 		JOIN backblaze_keyword_store kw ON tmp.id = kw.id");
        sql.append(" 		JOIN backblaze_meta_file mf ON kw.metaFileId = mf.id");
        sql.append(" 		JOIN backblaze_meta_folder mfolder ON mf.metaFolderId = mfolder.id");
        sql.append(" 		JOIN backblaze_source_file_s3 srcS3 ON kw.sourceFileId = srcS3.id");
        sql.append(" 	) t1");
        sql.append(" JOIN backblaze_keyword_store t2 ON t1.metaFileId = t2.metaFileId");
        sql.append(" GROUP BY");
        sql.append(" 	t1.keywordType,");
        sql.append(" 	t1.SE,");
        sql.append(" 	t1.KW,");
        sql.append(" 	t1.fileName,");
        sql.append(" 	t1.zipFile,");
        sql.append(" 	t1.srcS3File,");
        sql.append(" 	t1.metaFileId");
        sql.append(" order by cntInZip desc  ");

        System.out.println( "sql : " + sql.toString());
        return this.queryForMapList(sql.toString());
    }

    public boolean checkData(String date, String engine, String language, String device, String keywordName,String kw) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from ").append(getTableName());
        sql.append(" where (keywordName = '").append(keywordName).append("'");
        sql.append(" or keywordName = '").append(kw).append("')");
        sql.append(" and engineId = ").append(engine);
        sql.append(" and languageId = ").append(language);
        sql.append(" and device = '").append(device).append("' ");
        sql.append(" and rankDate = ").append(date);
        System.out.println(sql.toString());
        return this.findBySql(sql.toString()).size()>0;
    }
}
