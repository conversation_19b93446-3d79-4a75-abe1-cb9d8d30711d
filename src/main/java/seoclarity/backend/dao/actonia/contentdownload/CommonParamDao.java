package seoclarity.backend.dao.actonia.contentdownload;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.CommonParamEntity;

import java.util.List;

@Repository
public class CommonParamDao extends ActoniaBaseJdbcSupport<CommonParamEntity> {

    @Override
    public String getTableName() {
        return "common_param";
    }

    public List<CommonParamEntity> getParamJsonByDomainId(int domainId, String funName) {
        String sql = "select title, paramJson from " + getTableName() + " where ownDomainId= ? and funcName= ?";
        return findBySql(sql, domainId, funName);
    }

    public CommonParamEntity getParamJsonById(int id) {
        String sql = "select title, paramJson from " + getTableName() + " where id= ?";
        return findObject(sql, id);
    }

}
