/**
 * 
 */
package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.SiaProjectEntity;
import seoclarity.backend.entity.SiaUrlDetailEntity;
import seoclarity.backend.utils.cityhash.CityHashUtil;

@Repository
public class SiaUrlDetailEntityDAO extends ActoniaBaseJdbcSupport<SiaUrlDetailEntity> {

	@Override
	public String getTableName() {
		return "sia_url_detail";
	}
	

    public void batchInsert(List<String> urlList, Integer ownDomainId, Integer projectId) {
        String sql = "insert ignore into " + getTableName()
                + "(projectId, ownDomainId, url, urlMurmur3Hash, status, sendTime, uploadTime) " +
                "values (?,?,?,?,?,  ?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        
        int num = 0;
        for (String url : urlList) {
        	num ++;
        	
        	if (num <= 5) {
				System.out.println("url:" + url);
			}
        	
            batch.add(new Object[]{
        		projectId,
        		ownDomainId,
        		url,
                CityHashUtil.getCityHash64ForBigInteger(url),
                SiaUrlDetailEntity.STATUS_NOT_START,
                new Date(),
                new Date()
            });
        }
        this.executeBatch(sql, batch);
    }
    
	public List<SiaUrlDetailEntity> getDetailUrlList(Integer projectId, Integer start, Integer pageSize) {
		String sql = "select projectId, ownDomainId, url, urlMurmur3Hash, status from " + getTableName() 
			+ " where projectId = ? and (status in (?, ?) or status is null) order by id ASC limit ?, ? ";
		
		return findBySql(sql, projectId, 
				SiaUrlDetailEntity.STATUS_NOT_START, 
				SiaUrlDetailEntity.STATUS_SEND_ERROR,
				start, pageSize);
	}
	

	
}
