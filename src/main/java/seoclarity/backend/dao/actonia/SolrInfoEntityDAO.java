package seoclarity.backend.dao.actonia;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.SolrInfoEntity;
import seoclarity.backend.entity.actonia.SolrInfoEntity.SolrType;

@Repository
public class SolrInfoEntityDAO extends ActoniaBaseJdbcSupport<SolrInfoEntity> {

	@Override
	public String getTableName() {
		return null;
	}

	//Leo - 
	public SolrInfoEntity getSolrInfoByCountry(String country, SolrType solrType) {
		
		StringBuilder sql = new StringBuilder();
		sql.append(" select * ");
		sql.append(" from solr_info ");
		sql.append(" where country_code = ? ");
		sql.append(" and solr_type = ? ");
		sql.append(" order by solr_month desc ");
		sql.append(" limit 1 ");
		
		return this.findObject(sql.toString(), country, solrType.getValue());
	}
	
	public List<SolrInfoEntity> getAllAvailableSolrServerViaType(int solrType) {
		String sql = " select * from solr_info where solr_type = ?";
		return findBySql(sql, solrType);
	}

	public Map<String, String> getAllAvailableSolrServerViaTypeReturnAsMap(int solrType) {
		List<SolrInfoEntity> availableSolrs = getAllAvailableSolrServerViaType(solrType);
		Map<String, String> countryCdAndSolrUrl = new HashMap<String, String>();

		Map<String, Integer> latestCoreForEachCountry = new HashMap<String, Integer>();

		if (availableSolrs != null) {
			for (SolrInfoEntity solrInfoEntity : availableSolrs) {

				if (latestCoreForEachCountry.containsKey(solrInfoEntity.getCountryCode().toLowerCase())) {
					int currentMaxMonth = latestCoreForEachCountry.get(solrInfoEntity.getCountryCode().toLowerCase());
					if (currentMaxMonth < solrInfoEntity.getSolrMonth()) {
						latestCoreForEachCountry.put(solrInfoEntity.getCountryCode().toLowerCase(), solrInfoEntity.getSolrMonth());
						countryCdAndSolrUrl.put(solrInfoEntity.getCountryCode().toLowerCase(), solrInfoEntity.getSolrUrl());
					}
				} else {
					countryCdAndSolrUrl.put(solrInfoEntity.getCountryCode().toLowerCase(), solrInfoEntity.getSolrUrl());
					latestCoreForEachCountry.put(solrInfoEntity.getCountryCode().toLowerCase(),
							solrInfoEntity.getSolrMonth());
				}

			}
		}
		return countryCdAndSolrUrl;
	}
	
    //https://www.wrike.com/open.htm?id=52384759
    //by floyd
	public Map<String, SolrInfoEntity> getAllAvailableSolrServerViaTypeReturnAsMapV2(int solrType) {
		List<SolrInfoEntity> availableSolrs = getAllAvailableSolrServerViaType(solrType);
		Map<String, SolrInfoEntity> countryCdAndSolrUrl = new HashMap<String, SolrInfoEntity>();

		Map<String, Integer> latestCoreForEachCountry = new HashMap<String, Integer>();

		if (availableSolrs != null) {
			for (SolrInfoEntity solrInfoEntity : availableSolrs) {

				if (latestCoreForEachCountry.containsKey(solrInfoEntity.getCountryCode().toLowerCase())) {
					int currentMaxMonth = latestCoreForEachCountry.get(solrInfoEntity.getCountryCode().toLowerCase());
					if (currentMaxMonth < solrInfoEntity.getSolrMonth()) {
						latestCoreForEachCountry.put(solrInfoEntity.getCountryCode().toLowerCase(), solrInfoEntity.getSolrMonth());
						countryCdAndSolrUrl.put(solrInfoEntity.getCountryCode().toLowerCase(), solrInfoEntity);
					}
				} else {
					countryCdAndSolrUrl.put(solrInfoEntity.getCountryCode().toLowerCase(), solrInfoEntity);
					latestCoreForEachCountry.put(solrInfoEntity.getCountryCode().toLowerCase(),
							solrInfoEntity.getSolrMonth());
				}

			}
		}
		return countryCdAndSolrUrl;
	}
	
	
	public String getLatestMonth(String countryCode, String solrType) {
		String sql = " select solr_month from solr_info where solr_type = ? and country_code = ? order by solr_month desc limit 1";
		return queryForString(sql, solrType, countryCode);
	}
	
	public List<SolrInfoEntity> getLatestList(int solrType) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    solr_info_outer.* ");
		stringBuilder.append("from ");
		stringBuilder.append("	  solr_info solr_info_outer ");
		stringBuilder.append("where ");
		stringBuilder.append("    solr_info_outer.solr_type = ? ");
		stringBuilder.append("and solr_info_outer.solr_month =  ");
		stringBuilder.append("( ");
		stringBuilder.append("	select ");
		stringBuilder.append("		max(solr_info_inner.solr_month) ");
		stringBuilder.append("	from ");
		stringBuilder.append("      solr_info solr_info_inner ");
		stringBuilder.append("	where ");
		stringBuilder.append("      solr_info_inner.country_code = solr_info_outer.country_code ");
		stringBuilder.append("	and solr_info_inner.solr_type = solr_info_outer.solr_type ");
		stringBuilder.append(") ");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, solrType);
	}
	
	public SolrInfoEntity getCountryLatest(int solrType, String searchEngineCountryCode) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    solr_info_outer.* ");
		stringBuilder.append("from ");
		stringBuilder.append("	  solr_info solr_info_outer ");
		stringBuilder.append("where ");
		stringBuilder.append("    solr_info_outer.solr_type = ? ");
		stringBuilder.append("and solr_info_outer.country_code = ? ");
		stringBuilder.append("and solr_info_outer.solr_month =  ");
		stringBuilder.append("( ");
		stringBuilder.append("	select ");
		stringBuilder.append("		max(solr_info_inner.solr_month) ");
		stringBuilder.append("	from ");
		stringBuilder.append("      solr_info solr_info_inner ");
		stringBuilder.append("	where ");
		stringBuilder.append("      solr_info_inner.country_code = solr_info_outer.country_code ");
		stringBuilder.append("	and solr_info_inner.solr_type = solr_info_outer.solr_type ");
		stringBuilder.append(") ");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, solrType, searchEngineCountryCode);
		
	}
}
