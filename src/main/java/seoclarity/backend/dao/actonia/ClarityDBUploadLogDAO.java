package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;

@Repository
public class ClarityDBUploadLogDAO extends ActoniaMdbBaseJdbcSupport<ClarityDBUploadLogEntity> {

	@Override
	public String getTableName() {
		return "claritydb_upload_log";
	}

	//find if there is processing the upload files.
	public List<ClarityDBUploadLogEntity> getProcessingRecords(Integer uploadType) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from " + getTableName() + " where tmpTableUploadStatus = ? and uploadType = ? ");
		sql.append(" order by id asc");
		return findBySql(sql.toString(), ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING, uploadType);
	}
	
	public List<ClarityDBUploadLogEntity> getProcessingRecords(List<Integer> uploadTypeList) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from " + getTableName() + " where tmpTableUploadStatus = ? and uploadType in (" + getIntegerListQueryParam(uploadTypeList) + ") ");
		sql.append(" order by id asc");
		return findBySql(sql.toString(), ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);
	}
	
	
	//TODO
	public ClarityDBUploadLogEntity getByFileName(String date, String fileName) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from " + getTableName() + " where creatDate = ? and inputFileName = ? ");
		sql.append(" order by id asc");
		return findObject(sql.toString(), date, fileName);
	}
	
	
	public void updateTmpTableStatus(
			Integer status, 
			long tmpTableUploadDailyRows, 
			Integer tmpTableUploadSeconds, 
			Integer logId) {
        String sql = "update " + getTableName() + " set tmpTableUploadStatus = ?, tmpTableUploadEndTime=?, tmpTableUploadDailyRows=?, tmpTableUploadSeconds=?  where id = ?";
        executeUpdate(sql, status, new Date(), tmpTableUploadDailyRows, tmpTableUploadSeconds, logId);
    }
	
	public void updateTmpTableStatus(
			Integer status, 
			Integer logId) {
        String sql = "update " + getTableName() + " set tmpTableUploadStatus = ?, tmpTableUploadEndTime=? where id = ?";
        executeUpdate(sql, status, new Date(), logId);
    }
	 
	public void updateForPageSpeed(ClarityDBUploadLogEntity pagespeedUploadLogEntity) {

		List paramList = new ArrayList();

		StringBuffer sql = new StringBuffer();
		sql.append(" update " + getTableName() + " set finalTableUploadStatus = ? ");
		sql.append(" ,finalTableUploadRows = ? ");
		sql.append(" ,finalTableUploadEndTime = ? ");
		sql.append(" ,finalTableUploadSeconds = ? ");
		sql.append(" ,currFinalTableRows = ? ");

		paramList.add(pagespeedUploadLogEntity.getFinalTableUploadStatus());
		paramList.add(pagespeedUploadLogEntity.getFinalTableUploadRows());
		paramList.add(pagespeedUploadLogEntity.getFinalTableUploadEndTime());
		paramList.add(pagespeedUploadLogEntity.getFinalTableUploadSeconds());
		paramList.add(pagespeedUploadLogEntity.getCurrFinalTableRows());

		if(StringUtils.isNotBlank(pagespeedUploadLogEntity.getErrorMsg())){
			sql.append(" ,errorMsg = ? ");
			paramList.add(pagespeedUploadLogEntity.getErrorMsg());
		}
		sql.append(" where id = ? ");
		paramList.add(pagespeedUploadLogEntity.getId());

		executeUpdate(sql.toString(), paramList.toArray());
	}	
	 
	public Integer insert(ClarityDBUploadLogEntity clarityDBUploadLogEntity) {
	    Map<String, Object> val = new HashMap<String, Object>();
	    val.put("serverIp", clarityDBUploadLogEntity.getServerIp());
	    val.put("uploadType", clarityDBUploadLogEntity.getUploadType());
	    val.put("databaseName", clarityDBUploadLogEntity.getDatabaseName());
	    val.put("tmpTableName", clarityDBUploadLogEntity.getTmpTableName());
	    val.put("finalTableName", clarityDBUploadLogEntity.getFinalTableName());
	    val.put("tmpTableUploadStatus", clarityDBUploadLogEntity.getTmpTableUploadStatus());
	    val.put("finalTableUploadStatus", clarityDBUploadLogEntity.getFinalTableUploadStatus());
	    val.put("inputFileName", clarityDBUploadLogEntity.getInputFileName());
	    val.put("tmpTableUploadDailyRows", clarityDBUploadLogEntity.getTmpTableUploadDailyRows());
	    val.put("tmpTableUploadReprocessRows", clarityDBUploadLogEntity.getTmpTableUploadReprocessRows());
	    val.put("tmpTableUploadStartTime", clarityDBUploadLogEntity.getTmpTableUploadStartTime());
	    val.put("tmpTableUploadEndTime", clarityDBUploadLogEntity.getTmpTableUploadEndTime());
	    val.put("tmpTableUploadSeconds", clarityDBUploadLogEntity.getTmpTableUploadSeconds());
	    val.put("finalTableUploadRows", clarityDBUploadLogEntity.getFinalTableUploadRows());
	    val.put("finalTableUploadStartTime", clarityDBUploadLogEntity.getFinalTableUploadStartTime());
	    val.put("finalTableUploadEndTime", clarityDBUploadLogEntity.getFinalTableUploadEndTime());
	    val.put("finalTableUploadSeconds", clarityDBUploadLogEntity.getFinalTableUploadSeconds());
	    val.put("prevFinalTableRows", clarityDBUploadLogEntity.getPrevFinalTableRows());
	    val.put("currFinalTableRows", clarityDBUploadLogEntity.getCurrFinalTableRows());
	    val.put("creatDate", new Date());
	    return this.insert(val);
	}

	public void deleteById(Integer id) {
		String sql = " delete from " + getTableName() + " where id = " + id + " limit 1";
		System.out.println(sql);
		this.executeUpdate(sql);
	}

}
