/**
 *
 */
package seoclarity.backend.dao.actonia;

import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.AgencyInfoEntity;
import seoclarity.backend.entity.actonia.AioKeywordWithoutSubrankEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.contentidea.ContentIdeaEntity;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class AioKeywordWithoutSubrankDAO extends ActoniaBaseJdbcSupport<AioKeywordWithoutSubrankEntity> {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    @Override
    public String getTableName() {
        return "aio_keyword_without_subrank";
    }


    public void insertBitch(List<CLRankingDetailEntity> list, Integer engineId, Integer languageId, boolean isMobile) {
        String sql = "INSERT INTO " + getTableName()
                + " (rankType, engineId, languageId, device, rankDate, aioKeywordCount ,aioKeywordNoSubrankCount,createDate ) "
                + " VALUES (?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (CLRankingDetailEntity entity : list) {
            Integer rankingDate = Integer.parseInt(entity.getRankingDate().replaceAll("-",""));
            System.out.println(rankingDate);
            Object[] values = new Object[]{
                    "RI",
                    engineId,
                    languageId,
                    isMobile ? "m" : "d",
                    rankingDate,
                    entity.getTotalCnt(),
                    entity.getNoSubrankCnt(),
                    new Date()
            };

            if (i++ < 10) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);
        }
        executeBatch(sql, batch);
    }
}
