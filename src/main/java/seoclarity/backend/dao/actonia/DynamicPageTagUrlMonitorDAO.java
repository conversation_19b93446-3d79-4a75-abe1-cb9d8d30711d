package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.DynamicPageTagUrlMonitorEntity;
import seoclarity.backend.utils.Md5Util;

@Repository
public final class DynamicPageTagUrlMonitorDAO extends ActoniaBaseJdbcSupport<DynamicPageTagUrlMonitorEntity> {

    @Override
    public String getTableName() {
        return "dynamic_page_tag_monitor";
    }
    
    public void truncate() {
    	String sql = " truncate table dynamic_page_tag_monitor";
    	
    	this.executeUpdate(sql);
    }
    
    public int[] insertForBatch(List<String> needInsert, Integer ownDomainId, Integer engineId, Integer languageId) {
        String sql = "insert ignore into " + getTableName() + "(own_domain_id, hash_md5) " +
                "values(?, ?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (String str : needInsert) {
            Object[] values = new Object[]{ownDomainId, Md5Util.Md5(str + "-" + ownDomainId + "-" + engineId + "-" + languageId)};
            batch.add(values);
        }
        return executeBatch(sql, batch);
    }

}
