package seoclarity.backend.dao.actonia.hreflang;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.hreflang.HrefLangUrlCrawlEntity;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class HrefLangUrlCrawlDAO extends ActoniaBaseJdbcSupport<HrefLangUrlCrawlEntity> {

    @Override
    public String getTableName() {
        return "hreflang_url_crawl";
    }
    
    public void insertData(List<HrefLangUrlCrawlEntity> urlInspectionShuffleList) {
        String sql = "insert ignore into " + getTableName() + " (ownDomainId, crawlDate, urlId, crawlStatus) values (?, ?, ?, ?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (HrefLangUrlCrawlEntity entity : urlInspectionShuffleList) {
            batch.add(new Object[] { entity.getOwnDomainId(), entity.getCrawlDate(), entity.getUrlId(), entity.getCrawlStatus() });
        }
        this.executeBatch(sql, batch);
    }
    
    public int getSentCount(Date crawlDate) {
        StringBuffer sql = new StringBuffer();
        sql.append("select count(*) from ").append(getTableName()).append(" where crawlDate=? ");
        return queryForInt(sql.toString(), crawlDate);
    }
}