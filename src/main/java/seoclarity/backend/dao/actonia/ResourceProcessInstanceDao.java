package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.ResourceProcessInstanceEntity;

import java.util.Date;
import java.util.List;

@Repository("resourceProcessInstanceDao")
public class ResourceProcessInstanceDao extends ActoniaBaseJdbcSupport<ResourceProcessInstanceEntity>{
    @Override
    public String getTableName() {
        return "resource_process_instance";
    }

    public int insert(ResourceProcessInstanceEntity resourceProcessInstanceEntity) {
        String sql = "INSERT INTO " + getTableName() + " (processType,ownDomainId,resourceId,status) VALUES(?,?,?,?)";
        return executeUpdate(sql, resourceProcessInstanceEntity.getProcessType(),
                resourceProcessInstanceEntity.getOwnDomainId(),
                resourceProcessInstanceEntity.getResourceId(),
                resourceProcessInstanceEntity.getStatus()
        );
    }

    public void updateStatusAndStartDateById(int id, int status, Date startDate) {
        String sql = "UPDATE " + getTableName() + " SET status = ?, processStartDate = ? WHERE id = ?";
        executeUpdate(sql, status, startDate, id);
    }

    public void updateErrorMsgById(int id, String msg) {
        String sql = "UPDATE " + getTableName() + " SET errorMsg = ? WHERE id = ?";
        executeUpdate(sql, msg, id);
    }

    public void updateStatusAndEndDateById(int id, int status, Date endDate) {
        String sql = "UPDATE " + getTableName() + " SET status = ?, processEndDate = ? WHERE id = ?";
        executeUpdate(sql, status, endDate, id);
    }

    public List<ResourceProcessInstanceEntity> getListByProcessTypeAndStatus(int processType, int status) {
        String sql = "SELECT * FROM " + getTableName() + " WHERE processType = ? and status = ? ";
        return findBySql(sql, processType, status);
    }

    public ResourceProcessInstanceEntity getEntityByProcessTypeAndStatus(int processType, int ownDomainId, String resourceId, int status) {
        String sql = "SELECT * FROM " + getTableName() + " WHERE processType = ? and ownDomainId = ? and resourceId = ? and status = ? ";
        return findObject(sql, processType, ownDomainId, resourceId, status);
    }

    public void deleteById(int id) {
        String sql = "DELETE FROM " + getTableName() + " WHERE id = ?";
        executeUpdate(sql, id);
    }

    public ResourceProcessInstanceEntity getEntityByProcessType(int processType, int ownDomainId, String resourceId) {
        String sql = "SELECT * FROM " + getTableName() + " WHERE processType = ? and ownDomainId = ? and resourceId = ? ";
        return findObject(sql, processType, ownDomainId, resourceId);
    }
}
