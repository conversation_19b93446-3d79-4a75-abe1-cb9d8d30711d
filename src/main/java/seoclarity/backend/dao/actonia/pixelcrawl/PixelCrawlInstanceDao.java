package seoclarity.backend.dao.actonia.pixelcrawl;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.pixelcrawl.PixelCrawlInstanceEntity;

@Repository
public class PixelCrawlInstanceDao extends ActoniaBaseJdbcSupport<PixelCrawlInstanceEntity> {
    @Override
    public String getTableName() {
        return "pixel_crawl_instance";
    }

    public PixelCrawlInstanceEntity getByUniqueKey(int rankDate, int frequency, String device) {
        String sql = " select * from " + getTableName() + " where rankDate = ? and frequence = ? and device = ?";
        return findObject(sql, rankDate, frequency, device);
    }

    public void insert(PixelCrawlInstanceEntity entity) {
        String sql = "INSERT INTO " + getTableName() +
                " (rankDate, frequence, device, sqsName, status, totalKeywordCount, createDate) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?)";
        executeUpdate(sql, entity.getRankDate(), entity.getFrequence(), entity.getDevice(), entity.getSqsName(), entity.getStatus(), entity.getTotalKeywordCount(), entity.getCreateDate());
    }

    public void updateStatus(int status, int id) {
        String sql = "UPDATE " + getTableName() + " SET status = ? WHERE id = ?";
        executeUpdate(sql, status, id);
    }
}
