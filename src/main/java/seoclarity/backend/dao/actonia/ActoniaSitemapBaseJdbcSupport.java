/**
 *
 */
package seoclarity.backend.dao.actonia;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class ActoniaSitemapBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="siteMapDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
