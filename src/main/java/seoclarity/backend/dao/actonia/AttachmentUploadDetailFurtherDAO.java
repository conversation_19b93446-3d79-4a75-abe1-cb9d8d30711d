package seoclarity.backend.dao.actonia;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.AttachmentUploadDetailFurtherEntity;
@Repository
public class AttachmentUploadDetailFurtherDAO extends ActoniaBaseJdbcSupport<AttachmentUploadDetailFurtherEntity> {

	public String getTableName() {
		return "attachment_upload_detail_further";
	}//attachment_upload_detail_further_test20220515

	public void insert(AttachmentUploadDetailFurtherEntity uploadDetailFurtherEntity) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("attachment_id", uploadDetailFurtherEntity.getAttachmentId());
		map.put("upload_detail_id", uploadDetailFurtherEntity.getUploadDetailId());
		map.put("log_date", uploadDetailFurtherEntity.getLogDate());
		map.put("download_time", new Date());
		map.put("status", uploadDetailFurtherEntity.getStatus());
		map.put("file_name", uploadDetailFurtherEntity.getFileName());
		map.put("actual_send_date", uploadDetailFurtherEntity.getActualSendDate());
		map.put("attachment_name", uploadDetailFurtherEntity.getAttachmentName());
		insert(map);
	}

	public List<AttachmentUploadDetailFurtherEntity> getAttachmentUploadDetailFurtherToday(int attchmentId, 
			int uploadDetailId, String todayDateString) {
		StringBuilder sql = new StringBuilder("select * from ");
		sql.append(this.getTableName());
		sql.append(" where attachment_id=? and upload_detail_id=? and log_date=date(?) ");
		return findBySql(sql.toString(), attchmentId, uploadDetailId, todayDateString);
	}
}