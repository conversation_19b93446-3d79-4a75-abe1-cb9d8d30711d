package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.VirtualSearchEngineRelEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class VirtualSearchEngineRelDAO extends ActoniaBaseJdbcSupport<VirtualSearchEngineRelEntity> {

	@Override
	public String getTableName() {
		return "virtual_search_engine_rel";
	}

	public List<VirtualSearchEngineRelEntity> getEnabledList() {
		String sql = "select * from " + getTableName() + " where enabled = ?";
		return this.findBySql(sql.toString(), VirtualSearchEngineRelEntity.ENABLED);
	}

	public Map<Integer, VirtualSearchEngineRelEntity> getRelationMap() {
		String sql = "select * from " + getTableName() + " where enabled = ?";
        List<VirtualSearchEngineRelEntity> result = findBySql(sql, VirtualSearchEngineRelEntity.ENABLED);
        Map<Integer, VirtualSearchEngineRelEntity> resultMap = new HashMap<Integer, VirtualSearchEngineRelEntity>();
        if (result != null) {
            for (VirtualSearchEngineRelEntity relEntity : result) {
            	resultMap.put(relEntity.getVirtualSearchEngineId(), relEntity);
            }
        }
        return resultMap;
	}
}