package seoclarity.backend.dao.actonia;

import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.CompetitorGroupsEntity;

@Repository
public class CompetitorGroupsEntityDAO extends ActoniaBaseJdbcSupport<CompetitorGroupsEntity> {

	@Override
	public String getTableName() {
		return "competitor_groups";
	}
	
	public int insert(CompetitorGroupsEntity groupEntity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("ownDomainId", groupEntity.getOwnDomainId());
		values.put("groupName", groupEntity.getGroupName());
		values.put("color", groupEntity.getColor());
		values.put("createUser", groupEntity.getCreateUser());
		values.put("createDate", groupEntity.getCreateDate());	
		return this.insert(values);
	}
	
    public CompetitorGroupsEntity getByUniqueKey(int ownDomainId, String groupName) {
        String sql = "select * from "+ getTableName() + " where ownDomainId=? and groupName=?";
        return findObject(sql, ownDomainId, groupName);
    }
}