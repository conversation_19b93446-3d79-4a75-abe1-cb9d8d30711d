package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.KeywordTranslationEntity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class KeywordTranslationEntityDAO extends ActoniaBaseJdbcSupport<KeywordTranslationEntity> {

	@Override
	public String getTableName() {
		return "keyword_translate";
	}

	public int[] insertBatchIgnoreDup(List<KeywordTranslationEntity> KeywordTranslationEntityList) {
		StringBuilder sbd = new StringBuilder();
		sbd.append(" insert ignore into ").append(getTableName());
		sbd.append(" (ownDomainId,engineId,languageId,keywordId,translateString,lastUpdateDate) values(?,?,?,?,?,?) ");
		List<Object[]> batch = new ArrayList<>();
		for (KeywordTranslationEntity entity : KeywordTranslationEntityList) {
			batch.add(new Object[]{
					entity.getOwnDomainId(),
					entity.getEngineId(),
					entity.getLanguageId(),
					entity.getKeywordId(),
					entity.getTranslateString(),
					entity.getLastUpdateDate()
			});
		}
		return this.executeBatch(sbd.toString(), batch);
	}

}