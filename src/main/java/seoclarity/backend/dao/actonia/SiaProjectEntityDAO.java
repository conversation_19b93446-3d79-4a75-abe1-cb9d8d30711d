/**
 * 
 */
package seoclarity.backend.dao.actonia;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.SiaProjectEntity;

@Repository
public class SiaProjectEntityDAO extends ActoniaBaseJdbcSupport<SiaProjectEntity> {

	@Override
	public String getTableName() {
		return "sia_project";
	}
	
	public List<SiaProjectEntity> getProjectNeedProcess() {
		String sql = "select * from " + getTableName() + " where status in (?, ?) and source is not null order by id ASC ";
		return findBySql(sql, SiaProjectEntity.STATUS_NEW, SiaProjectEntity.STATUS_COLLECT_DETAIL_ERROR);
	}
	
	public List<SiaProjectEntity> getProjectNeedProcess(Integer projectId) {
		String sql = "select * from " + getTableName() + " where id = ? and status in (?, ?) and source is not null order by id ASC ";
		return findBySql(sql, projectId, SiaProjectEntity.STATUS_NEW, SiaProjectEntity.STATUS_COLLECT_DETAIL_ERROR);
	}
	
	public void updateProjectStatus(Integer projectId, Integer status) {
		String sql = " update " + getTableName() + " set status = ?, updateTime = ? where id = ? limit 1 ";
		this.executeUpdate(sql, status, new Date(), projectId);
	}
	
	public List<SiaProjectEntity> getProjectNeedSend() {
		String sql = "select * from " + getTableName() + " where status = ? order by id ASC ";
		return findBySql(sql, SiaProjectEntity.STATUS_COLLECT_DETAIL_COMLETED, SiaProjectEntity.STATUS_COLLECT_DETAIL_ERROR);
	}
	
}
