package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.ThirdPartyApiRequestInstance;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/9 09:56
 */
@Repository
public class ThirdPartyApiRequestInstanceDAO extends ActoniaBaseJdbcSupport<ThirdPartyApiRequestInstance> {

    @Override
    public String getTableName() {
        return "third_party_api_request_instance";
    }

    public ThirdPartyApiRequestInstance getByProcessTypeAndOwnDomainIdAndTargetDate(Integer processType, Integer ownDomainId, Integer targetDate) {
        String sql = "select processType, ownDomainId, targetDate, processDate, rerunNo, status, processStartDate, processEndDate, resultCount, loadCount, errorMsg, createDate " +
                "from " + getTableName() + " where processType = ? and ownDomainId = ? and targetDate = ? order by rerunNo desc limit 1";
        return findObject(sql, processType, ownDomainId, targetDate);
    }

    public List<Integer> getOwnDomainIdByCreateDate(String startTime, String endTime) {
        String sql = "select distinct ownDomainId from " + getTableName() + " where createDate between ? and ?";
        return queryForIntegerList(sql, startTime, endTime);
    }

    public int insert(ThirdPartyApiRequestInstance thirdPartyApiRequestInstance) {
        String sql = "insert into " + getTableName() +
                " (processType, ownDomainId, targetDate, processDate, rerunNo, status, processStartDate, processEndDate, resultCount, loadCount, errorMsg) " +
                "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
        return this.executeUpdate(sql, thirdPartyApiRequestInstance.getProcessType(),
                thirdPartyApiRequestInstance.getOwnDomainId(),
                thirdPartyApiRequestInstance.getTargetDate(),
                thirdPartyApiRequestInstance.getProcessDate(),
                thirdPartyApiRequestInstance.getRerunNo(),
                thirdPartyApiRequestInstance.getStatus(),
                thirdPartyApiRequestInstance.getProcessStartDate(),
                thirdPartyApiRequestInstance.getProcessEndDate(),
                thirdPartyApiRequestInstance.getResultCount(),
                thirdPartyApiRequestInstance.getLoadCount(),
                thirdPartyApiRequestInstance.getErrorMsg());
    }

}
