package seoclarity.backend.dao.actonia.clarity360;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.clarity360.ReportCustomFileEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ReportCustomFileDao extends ActoniaBaseJdbcSupport<ReportCustomFileEntity> {
    @Override
    public String getTableName() {
        return "clarity_360_report_custom_file";
    }

    public List<ReportCustomFileEntity> getNeedProcessFileList() {
        String sql = "select * from " + getTableName() + " where enabled = ? and uploadStatus in(?,?) ";
        return findBySql(sql, ReportCustomFileEntity.ENABLED, ReportCustomFileEntity.STATUS_NEW_CREATE, ReportCustomFileEntity.STATUS_LOADING_ERROR);
    }

    public void updateStatusByIdAndOid(int status, int domainId, int id) {
        String sql = "update " + getTableName() + " set uploadStatus = ? where ownDomainId = ? and id = ?";
        this.executeUpdate(sql, status, domainId, id);
    }

    public void updateFtpFileName(String fileName, String ftpFullPathFilename, int domainId, int id) { // use for s3
        String sql = "update " + getTableName() + " set sourceFilename = ?, ftpReserveFilename = ? where ownDomainId = ? and id = ?";
        this.executeUpdate(sql, fileName, ftpFullPathFilename, domainId, id);
    }

    public void updateFtpFileName(String ftpFullPathFilename, int domainId, int id) { // use for own ftp
        String sql = "update " + getTableName() + " set ftpReserveFilename = ? where ownDomainId = ? and id = ?";
        this.executeUpdate(sql, ftpFullPathFilename, domainId, id);
    }

    public ReportCustomFileEntity getReportFileById(int id) {
        String sql = "select * from " + getTableName() + " where id = ?";
        return findObject(sql, id);
    }

    public List<ReportCustomFileEntity> getReportFileByIdList(List<Integer> idList) {
        String id = StringUtils.join(idList, ",");
        String sql = "select * from " + getTableName() + " where id in (" + id + ")";
        return findBySql(sql);
    }

    public int insert(ReportCustomFileEntity entity) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("enabled", entity.getEnabled());
        values.put("ownDomainId", entity.getOwnDomainId());
        values.put("storageType", entity.getStorageType());
        values.put("uploadStatus", entity.getUploadStatus());
        values.put("sourceFilename", entity.getSourceFilename());
        values.put("friendlyName", entity.getFriendlyName());
        values.put("ftpFullPathFilename", entity.getFtpFullPathFilename());
        values.put("createUserId", entity.getCreateUserId());
        values.put("createdAt", entity.getCreatedAt());
        return insert(values);
    }
}
