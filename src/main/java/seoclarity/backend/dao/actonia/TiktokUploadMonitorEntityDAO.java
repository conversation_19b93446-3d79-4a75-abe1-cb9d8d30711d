package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.cronjob.tiktok.entity.TiktokUploadInfoEntity;
import seoclarity.backend.cronjob.tiktok.entity.TiktokUploadMonitorEntity;
import seoclarity.backend.entity.actonia.SeoRankingMonitorEntity;

/**
 * seoclarity.backend.dao.actonia.TiktokUploadMonitorEntity
 */
@Repository
public class TiktokUploadMonitorEntityDAO extends ActoniaMdbMonitorDailyBaseJdbcSupport<TiktokUploadMonitorEntity>{

	@Override
	public String getTableName() {
		return "supplemental_upload_monitor_w_template";
	}
	
	public boolean checkTable(String tableName) {
		String sql = "show tables like '" + tableName + "'";
		List<String> tableList = queryForStringList(sql);
		if (tableList == null || tableList.size() == 0) {
			return false;
		}
		return true;
	}
	
	public void createTable(String descTab) {
		StringBuffer sql = new StringBuffer();
		sql.append(" create table ").append(descTab).append(" like supplemental_upload_monitor_w_template ");

		executeUpdate(sql.toString());
	}
	
	public void dropTable(String descTab) {
		StringBuffer sql = new StringBuffer();
		sql.append(" drop table ").append(descTab);

		executeUpdate(sql.toString());
	}
	
	public void deleteDataByEngineId(String tableName, Integer engineId, Integer languageId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" delete from ").append(tableName).append(" where engineId = ?  and  languageId = ? ");

		executeUpdate(sql.toString(), engineId, languageId);
	}
	
	/**
		private Integer keywordType;
		private Integer ownDomainId;
		private Integer engineId;
		private Integer languageId;
		private String device;
		private Integer cityId;
		private Integer keywordRankcheckId;
		private Integer frequence;
	 * @return
	 */
	
	
	public int[] insertIgnore(String tableName, Collection<String> uniqeKeyList) {
		StringBuffer sql = new StringBuffer();
		sql.append(" insert ignore into ").append(tableName).append("(keywordRankcheckId, engineId, languageId, cityId, frequence, keywordType, ownDomainId, device)");
		sql.append(" values (?,?,?,?,?, ?,?,?)");
		
		List<Object[]> batchData = new ArrayList<Object[]>();
		for (String uniqueKey : uniqeKeyList) {
			
			String[] keys = StringUtils.split(uniqueKey, "-");
			
			batchData.add(new Object[] {
				keys[0], //keywordRankcheckId
				keys[1], //engineId 
				keys[2], //languageId
				keys[3], //cityId
				keys[4], //frequence
				TiktokUploadMonitorEntity.KEYWORD_TYPE_NATIONAL, //keywordType, only support national
				keys[5], //ownDomainId
				keys[6]  //device
			});
		}
		
		return this.executeBatch(sql.toString(), batchData);
	}
	
//	public void delete(String tableName, Integer kid, int engine, int language, int locationId, int frequency, int type, int ownDomainId) {
//		StringBuffer sql = new StringBuffer();
//		sql.append(" delete from ").append(tableName);
//		sql.append(" where keyword_rankcheck_id =? and engine =? and language = ? and location_id = ? and frequence = ? and type= ? and own_domain_id = ?");
//		executeUpdate(sql.toString(), kid, engine, language, locationId, frequency, type, ownDomainId);
//	}
//	
//	public void delete(String tableName, Integer kid, int engine, int language, int locationId, int frequency, int type) {
//		StringBuffer sql = new StringBuffer();
//		sql.append(" delete from ").append(tableName);
//		sql.append(" where keyword_rankcheck_id =? and engine =? and language = ? and location_id = ? and frequence = ? and type= ? ");
//		executeUpdate(sql.toString(), kid, engine, language, locationId, frequency, type);
//	}
//	
//	public void delete(String tableName, Collection<String> kidList, int engine, int language, int locationId, int frequency, int type) {
//		StringBuffer sql = new StringBuffer();
//		sql.append(" delete from ").append(tableName);
//		sql.append(" where keyword_rankcheck_id in (" + StringUtils.join(kidList, ',') + ") and engine =? and language = ? and location_id = ? and frequence = ? and type= ? ");
//		executeUpdate(sql.toString(), engine, language, locationId, frequency, type);
//	}
	
	
}
