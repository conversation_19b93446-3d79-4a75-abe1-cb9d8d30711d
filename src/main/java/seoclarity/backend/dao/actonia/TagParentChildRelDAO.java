package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.TagParentChildRelEntity;
import java.util.*;

@Repository
public class TagParentChildRelDAO extends ActoniaBaseJdbcSupport<TagParentChildRelEntity> {

    @Override
    public String getTableName() {
        return "tag_parent_child_rel";
    }
    
    public List<TagParentChildRelEntity> getTagEntityByType(int ownDomainId, int tagType) {
        String sql = "select id, childTagId, parentTagId from " + getTableName() + " where ownDomainId=? and tagType=?";
        return findBySql(sql, ownDomainId, tagType);
    }

    public List<Integer> getLastChildTagIdByType(int ownDomainId, int tagType) {
        String sql = " SELECT t1.childTagId  from " + getTableName() + " t1 ";
        sql += " LEFT JOIN tag_parent_child_rel t2 ON t1.childTagId = t2.parentTagId";
        sql += " WHERE t1.ownDomainId =? and t1.tagType=? and t2.parentTagId IS NULL;";
        sql += "";
        return queryForIntegerList(sql, ownDomainId, tagType);
    }

}