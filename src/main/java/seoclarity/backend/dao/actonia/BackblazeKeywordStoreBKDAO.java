package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import scala.Int;
import seoclarity.backend.entity.s3tob2.B2FileInfoVo;

@Repository
public class BackblazeKeywordStoreBKDAO extends ActoniaBaseJdbcSupport<B2FileInfoVo> {

    @Override
    public String getTableName() {
        return "backblaze_keyword_store_bk20220608";
    }

    public Integer checkKwInb2(Integer date, Integer keywordType, Integer engineId, Integer languageId,
                               String device, String cityQueryName, String cdbKeywordHash, String cdbKeywordMurmur3hash, String cdbEnocdeKeywordHash) {
        String sql = "select id from backblaze_keyword_store_bk20220608 where rankDate=" + date + " and keywordType=" + keywordType +
                "  and engineId=" + engineId + " and languageId=" + languageId + " and device='" + device + "' and cityQueryName='" + cityQueryName +"'"+
                "  and cdbKeywordHash=" + cdbKeywordHash + " and cdbKeywordMurmur3hash=" + cdbKeywordMurmur3hash +
                "  and cdbEnocdeKeywordHash=" + cdbEnocdeKeywordHash;
//        System.out.println(sql);
        return this.queryForInteger(sql);
    }

//
//    public Integer checkKwInb2(Integer date, Integer keywordType, Integer engineId, Integer languageId, String device,
//                               String cityQueryName, String cdbKeywordHash, String cdbKeywordMurmur3hash, String cdbEnocdeKeywordHash) {
////        String sql = "select * from backblaze_keyword_store_bk20220608 where rankDate=? and keywordType=?" +
////                "  and engineId=? and languageId=? and device='"+device+"' and cityQueryName=?" +
////                "  and cdbKeywordHash=? and cdbKeywordMurmur3hash=?" +
////                "  and cdbEnocdeKeywordHash=?";
//
//        String sql = "select * from backblaze_keyword_store_bk20220608 where rankDate=" + date + " and keywordType=" + keywordType +
//                "  and engineId=" + engineId + " and languageId=" + languageId + " and device='" + device + "' and cityQueryName=" + cityQueryName +
//                "  and cdbKeywordHash=" + cdbKeywordHash + " and cdbKeywordMurmur3hash=" + cdbKeywordMurmur3hash +
//                "  and cdbEnocdeKeywordHash=" + cdbEnocdeKeywordHash;
//        System.out.println(sql);
//        return this.queryForInteger(sql);
//    }
}
