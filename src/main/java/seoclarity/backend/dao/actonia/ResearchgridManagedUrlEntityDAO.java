package seoclarity.backend.dao.actonia;


import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.ResearchgridManagedUrlEntity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class ResearchgridManagedUrlEntityDAO extends ActoniaBaseJdbcSupport<ResearchgridManagedUrlEntity>{

    @Override
    public String getTableName() {
//        return "researchgrid_managed_url_wilber_20181112";
        return "researchgrid_managed_url";
    }

    public List<ResearchgridManagedUrlEntity> getUrls(long startId , int limitCount) {
        String sql = " select target_url_id , t2.url as targetUrl from ( ";
        sql += " select target_url_id from " + getTableName() + "  ";
        sql += " where target_url_id > ? ";
        sql += " and (research_grid_uri_hash is null or research_grid_uri_hash = 0) ";
        sql += " group by target_url_id ";
        sql += " order by target_url_id  ";
        sql += " )t1 ";
        sql += " inner join t_target_url t2 on t1.target_url_id = t2.id ";
        sql += " order by target_url_id ";
        sql += " limit ? ";

        System.out.println(" ==== getUrls : " + sql.toString() + ",startId : " + startId + ",limitCount: " + limitCount);

        return findBySql(sql, startId, limitCount);
    }

    public List<ResearchgridManagedUrlEntity> getNotExistInTargetUrlUrls(int limitCount) {
        String sql = " select target_url_id , target_url from " + getTableName() + "  ";
        sql += " where (research_grid_uri_hash is null or research_grid_uri_hash = 0) ";
        sql += " limit ? ";

        return findBySql(sql, limitCount);
    }

    public void updateUriHashCodeForBatch(List<ResearchgridManagedUrlEntity> researchgridManagedUrl) {

        StringBuffer sql = new StringBuffer();
        sql.append(" update  " + getTableName() + " set research_grid_uri_hash = ? ");
        sql.append(" where target_url_id  = ? " );

        List<Object[]> batch = new ArrayList<Object[]>();
        for (ResearchgridManagedUrlEntity managedUrlEntity : researchgridManagedUrl) {
            Object[] values = new Object[] {
                    managedUrlEntity.getResearchGridUriHash(),
                    managedUrlEntity.getTargetUrlId(),

            };
            batch.add(values);
        }

        System.out.println(" ==== updateUriHashCodeForBatch : " + sql.toString());

        this.executeBatch(sql.toString(), batch);
    }

}
