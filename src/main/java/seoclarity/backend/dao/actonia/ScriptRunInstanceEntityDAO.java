package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.dao.BaseJdbcSupport;
import seoclarity.backend.entity.actonia.ScriptRunInstanceEntity;

@Repository
public class ScriptRunInstanceEntityDAO extends ActoniaBaseJdbcSupport<ScriptRunInstanceEntity> {

	@Override
	public String getTableName() {
		return "script_run_instance";
	}
	
	public ScriptRunInstanceEntity getById(int id) {
		String sql = "select * from " + getTableName() + " where id=?";
		return findObject(sql, id);
	}
	
	public int insert(ScriptRunInstanceEntity entity) {
		Date now = new Date();
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("deployInfoId", entity.getDeployInfoId());
		values.put("processDate", entity.getProcessDate());
		values.put("started_at", now);
		values.put("status", entity.getStatus());
		values.put("serverIp", entity.getServerIp());
		values.put("serverPath", entity.getServerPath());
		values.put("createdAt", now);
		
		return this.insert(values);
	}
	
	public int insertFullProperties(ScriptRunInstanceEntity entity) {
		Date now = new Date();
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("deployInfoId", entity.getDeployInfoId());
		values.put("processDate", entity.getProcessDate());
		values.put("ownDomainId", entity.getOwnDomainId());
		values.put("started_at", now);
		values.put("status", entity.getStatus());
		values.put("serverIp", entity.getServerIp());
		values.put("serverPath", entity.getServerPath());
		values.put("createdAt", now);
		values.put("device", entity.getDevice());
		values.put("tagId", entity.getTagId());
		values.put("frequency", entity.getFrequency());
		values.put("country", entity.getCountry());
		
		return this.insert(values);
	}
	
	public void updateRunInfo(ScriptRunInstanceEntity entity) {
		StringBuffer sb = new StringBuffer();
		sb.append("update ").append(getTableName());
		sb.append(" set ended_at = now(), status = ?, fatalError = ?, extraError = ?, ownDomainId = ?, destinationServerIp = ?, outputFile = ?, ");
		sb.append(" additionalDestinationServerIp = ?, additionalOutputFile = ?, outputFileSizeKB = ?, outputDataCount = ?, skipDataCount = ?, ");
		sb.append(" errorDataCount = ?, elapsedSeconds = ? where id = ?");
		
		this.executeUpdate(sb.toString(), entity.getStatus(), entity.getFatalError(), entity.getExtraError(), entity.getOwnDomainId(), 
			entity.getDestinationServerIp(), entity.getOutputFile(), entity.getAdditionalDestinationServerIp(), entity.getAdditionalOutputFile(),
			entity.getOutputFileSizeKB(), entity.getOutputDataCount(), entity.getSkipDataCount(), entity.getErrorDataCount(), 
			entity.getElapsedSeconds(), entity.getId());
	}

	//keep old version method
	public ScriptRunInstanceEntity checkExist(ScriptRunInstanceEntity entity) {
		String sql = "select * from " + getTableName() + " where deployInfoId=? and processDate=? and ownDomainId=? and device=? and country=? and tagId=? and frequency=?";
		return findObject(sql, entity.getDeployInfoId(), entity.getProcessDate(), entity.getOwnDomainId(), entity.getDevice(), entity.getCountry(), entity.getTagId(), entity.getFrequency());
	}
	//new version method
	public ScriptRunInstanceEntity checkExist(ScriptRunInstanceEntity entity, boolean needTargetDomain) {
		StringBuilder sql = new StringBuilder();
		sql.append("select * from ");
		sql.append(getTableName());
		sql.append(" where deployInfoId=? and processDate=? and ownDomainId=? and device=? and country=? and tagId=? and frequency=?");

		ArrayList<Object> paramList = new ArrayList<Object>();
		paramList.add(entity.getDeployInfoId());
		paramList.add(entity.getProcessDate());
		paramList.add(entity.getOwnDomainId());
		paramList.add(entity.getDevice());
		paramList.add(entity.getCountry());
		paramList.add(entity.getTagId());
		paramList.add(entity.getFrequency());


		if (needTargetDomain) {
			sql.append(" and targetDomain=?");
			paramList.add(entity.getTargetDomain());
		}

		return findObject(sql.toString(), paramList.toArray());

	}


}