package seoclarity.backend.dao.actonia;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.dao.BaseJdbcSupport;
import seoclarity.backend.entity.actonia.ResourceDeleteDetailEntity;
import seoclarity.backend.entity.actonia.ResourceDeleteInfoEntity;

@Repository
public class ResourceDeleteInfoEntityDAO extends ActoniaBaseJdbcSupport<ResourceDeleteInfoEntity> {

    /*
      * (non-Javadoc)
      *
      * @see com.actonia.subserver.dao.BaseJdbcSupport#getTableName()
      */
    @Override
    public String getTableName() {
        return "resource_delete_info";
    }
    
    public ResourceDeleteInfoEntity getById(int id) {
        String sql = " select * from resource_delete_info where id=? ";
        return this.findObject(sql, id);
    }
    
    public List<ResourceDeleteInfoEntity> getDeleInfoList(int domainId, Date startDate, Date endDate, int status, int type) {
	    StringBuffer sql = new StringBuffer();
	    sql.append(" select distinct rdi.*  from resource_delete_info rdi ");
	    sql.append(" join resource_delete_detail rdd on rdi.id=rdd.delete_info_id  ");
	    sql.append(" where rdi.own_domain_id = ? and rdd.resource_type = ?  and rdi.status > ? and rdi.process_date >= ? and rdi.process_date <= ?  order by rdi.id ");
	    return this.findBySql(sql.toString(), domainId, type, status, startDate, endDate);
    }
    
    public List<ResourceDeleteInfoEntity> getDeleteInfoList() {
        StringBuffer sql = new StringBuffer();
        sql.append(" select distinct rdi.* from resource_delete_info rdi join resource_delete_detail rdd on rdi.id=rdd.delete_info_id  ");
        sql.append(" where rdi.enabled=1 and (rdi.status=? or rdi.status=?) and rdd.resource_type>=? and rdd.resource_type<=? order by rdi.id ");

        return this.findBySql(sql.toString(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, 
        		ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR, 
        		ResourceDeleteDetailEntity.RESOURCE_TYPE_DELETE_MIN_ID, ResourceDeleteDetailEntity.RESOURCE_TYPE_DELETE_MAX_ID);
    }
    
    public List<ResourceDeleteInfoEntity> getDeleteInfoCandidateList(Set<Integer> execDomains, Set<Integer> notExecDomains) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select info.*, derived.resource_type as resourceType, derived.cnt as detailCount from resource_delete_info info join ");
        sql.append(" (select rdi.id as id,rdd.resource_type as resource_type,count(*) as cnt from resource_delete_info rdi join resource_delete_detail rdd ");
        sql.append("  on rdi.id=rdd.delete_info_id where rdi.enabled=1 and (rdi.status=? or rdi.status=?) and rdd.resource_type>=? and rdd.resource_type<=? ");
        sql.append("  group by rdi.id,rdd.resource_type) derived on info.id=derived.id where 1=1 ");
        
    	if (execDomains != null && execDomains.size() > 0) {
    		sql.append(" and info.own_domain_id in (").append(getQueryParamByIntList(execDomains)).append(") ");
    	}
    	if (notExecDomains != null && notExecDomains.size() > 0) {
    		sql.append(" and info.own_domain_id not in (").append(getQueryParamByIntList(notExecDomains)).append(") ");
    	}
    	sql.append(" order by info.id");

        return this.findBySql(sql.toString(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, 
        		ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR, 
        		ResourceDeleteDetailEntity.RESOURCE_TYPE_DELETE_MIN_ID, ResourceDeleteDetailEntity.RESOURCE_TYPE_DELETE_MAX_ID);
    }
    
    public List<ResourceDeleteInfoEntity> getDeleteInfoList(Set<Integer> notExecDomains) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select distinct rdi.* from resource_delete_info rdi join resource_delete_detail rdd on rdi.id=rdd.delete_info_id  ");
        sql.append(" where rdi.enabled=1 and (rdi.status=? or rdi.status=?) and rdd.resource_type>=? and rdd.resource_type<=? ");
        if (notExecDomains != null && notExecDomains.size() > 0) {
        	sql.append(" and rdi.own_domain_id not in ( ");
        	sql.append(getQueryParamByIntList(notExecDomains));
        	sql.append(" ) ");
        }
        sql.append("  order by rdi.id  ");

        return this.findBySql(sql.toString(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, 
        		ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR, 
        		ResourceDeleteDetailEntity.RESOURCE_TYPE_DELETE_MIN_ID, ResourceDeleteDetailEntity.RESOURCE_TYPE_DELETE_MAX_ID);
    }
    
    public List<ResourceDeleteInfoEntity> getNotCompletedList(int ownDomainId, Date fromDate, Date toDate) {
    	StringBuffer sql = new StringBuffer();
        sql.append(" select distinct info.* from resource_delete_info info join( ");
        sql.append("  select rdi.id as id,count(*) as cnt from resource_delete_info rdi join resource_delete_detail rdd ");
        sql.append("  on rdi.id=rdd.delete_info_id where rdi.enabled=1 and rdi.own_domain_id = ? and rdi.status != ? ");
        sql.append("  and rdi.create_date>? and rdi.create_date<? and rdd.resource_type>=? and rdd.resource_type<=? ");
        sql.append(" group by rdi.id) derived on info.id=derived.id where derived.cnt>0 order by info.id ");
        
        return this.findBySql(sql.toString(), ownDomainId, ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, 
        		fromDate, toDate, ResourceDeleteDetailEntity.RESOURCE_TYPE_DELETE_MIN_ID,
        		ResourceDeleteDetailEntity.RESOURCE_TYPE_DELETE_MAX_ID);
    }


    public ResourceDeleteInfoEntity getLastNotCompletedDeleteInfoForContentGuardUrl(int ownDomainId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select distinct info.* from resource_delete_info info join ");
        sql.append(" ( select rdi.id as id,count(*) as cnt from resource_delete_info rdi join resource_delete_detail rdd on rdi.id=rdd.delete_info_id ");
        sql.append(" where rdi.enabled = ? and rdi.own_domain_id = ? and rdi.status != ? and rdd.resource_type = ? group by rdi.id) derived ");
        sql.append(" on info.id = derived.id where derived.cnt > 0 order by info.id desc limit 1 ");
        return this.findObject(sql.toString(), ResourceDeleteInfoEntity.ENABLED, ownDomainId, ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, ResourceDeleteDetailEntity.OPERATION_TYPE_CONTENT_GUARD_URL);
    }

    
    public List<ResourceDeleteInfoEntity> getDesyncRankcheckInfoList(int desyncType) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select distinct rdi.* from resource_delete_info rdi join resource_delete_detail rdd on rdi.id=rdd.delete_info_id  ");
        sql.append(" where rdi.enabled=1 and (rdi.status = ? or rdi.status = ?) and rdd.resource_type = ? order by rdi.id ");
        
        return this.findBySql(sql.toString(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, 
        		ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR, desyncType);
    }
    
    private String getQueryParamByIntList(Set<Integer> intList) {
		if (intList == null || intList.size() == 0) {
			return null;
		}
		StringBuilder intStr = new StringBuilder();
		for (Integer i : intList) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}
    
    public List<ResourceDeleteInfoEntity> getDesyncRankcheckInfoList(int desyncType, Set<Integer> notExecDomains) {
	        StringBuffer sql = new StringBuffer();
	        sql.append(" select distinct rdi.* from resource_delete_info rdi join resource_delete_detail rdd on rdi.id=rdd.delete_info_id  ");
	        sql.append(" where rdi.enabled=1 and (rdi.status = ? or rdi.status = ?) and rdd.resource_type = ? ");
	        if (notExecDomains != null && notExecDomains.size() > 0) {
	        	sql.append(" and rdi.own_domain_id not in ( ");
	        	sql.append(getQueryParamByIntList(notExecDomains));
	        	sql.append(" ) ");
	        }
	        sql.append(" order by rdi.id ");
	        
	        return this.findBySql(sql.toString(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, 
	        		ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR, desyncType);
    }
    
    public List<ResourceDeleteInfoEntity> getNotCompletedDesyncRankcheckList(int ownDomainId, 
    		Date fromDate, Date toDate, int resourceType) {
    	StringBuffer sql = new StringBuffer();
        sql.append(" select distinct rdi.* from resource_delete_info rdi join resource_delete_detail rdd ");
        sql.append(" on rdi.id=rdd.delete_info_id where rdi.enabled=1 and rdi.own_domain_id=? and rdi.status!=? ");
        sql.append(" and rdi.create_date>? and rdi.create_date<? and rdd.resource_type=? ");
        
        return this.findBySql(sql.toString(), ownDomainId, ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, 
        		fromDate, toDate, resourceType);
    }
    
    public List<ResourceDeleteInfoEntity> getDeleteInfoRankcheckList() {
        StringBuffer sql = new StringBuffer();
        sql.append(" select distinct rdi.* from resource_delete_info rdi join resource_delete_detail rdd on rdi.id=rdd.delete_info_id ");
        sql.append(" where rdi.enabled=1 and (rdd.resource_type=? or rdd.resource_type=? or rdd.resource_type=?) and rdi.status=? and (rdi.status_rankcheck=? or ");
        sql.append(" (rdi.status_rankcheck=? and (rdd.status_rankcheck is null or rdd.status_rankcheck!=?))) ");
//        sql.append(" and rdi.own_domain_id = 7741 ");
        sql.append(" order by rdi.id ");
        return this.findBySql(sql.toString(), 
        		ResourceDeleteDetailEntity.RESOURCE_TYPE_KEYWORD, 
        		ResourceDeleteDetailEntity.RESOURCE_TYPE_KEYWORD_CITY_REL,
        		ResourceDeleteDetailEntity.RESOURCE_TYPE_CITY,
        		ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR,
        		ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED);
    }
    
    public List<ResourceDeleteInfoEntity> getDeleteInfoByResourceType(int delInfoId, Integer[] resourceTypeList) {
    	StringBuffer sql = new StringBuffer();
    	 sql.append(" select distinct rdi.* from resource_delete_info rdi join resource_delete_detail rdd on rdi.id=rdd.delete_info_id ");
    	 sql.append(" where rdi.enabled=1 and (rdd.resource_type in (" + StringUtils.join(resourceTypeList, ',') + ")) and (rdi.status=? or  rdi.status=?) and (rdd.status is null or rdd.status = ? ) ");
    	 sql.append(" and rdi.id = ? order by rdi.id");
    	 return this.findBySql(sql.toString(), 
    			 ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR, 
    			 ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED,
    			 ResourceDeleteDetailEntity.STATUS_PROCESS_ERROR,
    			 delInfoId);
    }

    public void updateStatusBeforeProcess(int id, int status, Date processDate) {
        String sql = "update resource_delete_info set status=?, process_date=? where id=? ";
        executeUpdate(sql, status, processDate, id);
    }
    
    public boolean updateStatusBeforeProcess(int id, int status, Date processDate, String serverInfo, int currentStatus) {
    	StringBuffer sql = new StringBuffer();
    	sql.append(" update resource_delete_info set status=?, process_date=?, seq=IFNULL(seq,0)+1, server_info=? ");
    	sql.append(" where id=? and status=? limit 1 ");
    	return executeUpdate(sql.toString(), status, processDate, serverInfo, id, currentStatus) > 0;
    }
    
    public void updateStatusAfterProcess(int id, int status, Date endDate) {
        String sql = "update resource_delete_info set status=?, end_date=? where id=? ";
        executeUpdate(sql, status, endDate, id);
    }
    
    public void updateRankcheckStatusBeforeProcess(int id, int status, Date processDate) {
        String sql = "update resource_delete_info set status_rankcheck=?, process_date_rankcheck=? where id=? ";
        executeUpdate(sql, status, processDate, id);
    }
    
    public void updateRankcheckStatusAfterProcess(int id, int status, Date endDate) {
        String sql = "update resource_delete_info set status_rankcheck=?, end_date_rankcheck=? where id=? ";
        executeUpdate(sql, status, endDate, id);
    }
    
	public int insert(ResourceDeleteInfoEntity deleteInfoEntity) {
		Map<String, Object> val = new HashMap<String, Object>();
		val.put("enabled", deleteInfoEntity.getEnabled());
		val.put("own_domain_id", deleteInfoEntity.getOwnDomainId());
		val.put("user_id", deleteInfoEntity.getUserId());
		val.put("create_date", deleteInfoEntity.getCreateDate());
		val.put("status", deleteInfoEntity.getStatus());
		val.put("status_rankcheck", deleteInfoEntity.getStatusRankcheck());
		
		return insert(val);
	}
	
	public int deleteDetail(int oid, int infoId) {
		String sql = "delete from resource_delete_detail where delete_info_id = ? and own_domain_id = ? ";
		return this.executeUpdate(sql, infoId, oid);
	}

    public List<ResourceDeleteInfoEntity> getDeleteInfoForList(){
        StringBuffer sql = new StringBuffer();
        sql.append(" select distinct rdi.* from resource_delete_info rdi join resource_delete_detail rdd on rdi.id=rdd.delete_info_id where rdd.resource_type = ? and (rdi.status = ? or rdi.status = ?) order by create_date");
        return findBySql(sql.toString(), ResourceDeleteDetailEntity.RESOURCE_TYPE_PARENT_CHILD_URL, ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR);
    }

    public void updateStatusForPauseRanking(Integer id, int status, Date startDate) {
        String sql = "update resource_delete_info set status=?, end_date=?, process_date=? where id=? ";
        executeUpdate(sql, status, startDate, startDate, id);
    }

    public int updateInfoListByDomainIdAndStatus(Integer ownDomainId, Date date, int status) {
        String sql = "update resource_add_info set status=?, end_date=?, process_date=? where own_domain_id=? and status !=?";
        return executeUpdate(sql, status, date, date, ownDomainId, status);
    }
    
    public Integer insert(int ownDomainId, int userId) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("own_domain_id", ownDomainId);
		values.put("user_id", userId);
		
		values.put("enabled", ResourceDeleteInfoEntity.ENABLED);
		values.put("create_date", new Date());
		values.put("status", ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED);
		values.put("status_rankcheck", ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED);
		
		int newId = this.insert(values);
		
		if (newId < 0) {
			return null;
		}
		return newId;
	}
}