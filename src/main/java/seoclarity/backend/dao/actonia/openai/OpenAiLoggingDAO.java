package seoclarity.backend.dao.actonia.openai;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.openai.OpenAiLoggingEntity;

import java.util.*;

@Repository
public class OpenAiLoggingDAO extends ActoniaBaseJdbcSupport<OpenAiLoggingEntity> {
    @Override
    public String getTableName() {
        return "open_ai_logging";
    }

    public void insert(OpenAiLoggingEntity entity) {
        String sql = "INSERT INTO " + getTableName() + " (`openAIActionId`, `promptId`, `ownDomainId`, `variableJson`, `responseJson`, `createDate`, `userId`, `totalTokens`, `detailInfo`) values(?,?,?,?,?,?,?,?,?)";
        List<Object[]> params = new ArrayList<Object[]>();
        params.add(new Object[] {
                entity.getOpenAIActionId(), entity.getPromptId(), entity.getOwnDomainId(), entity.getVariableJson(), entity.getResponseJson(),
                new Date(), entity.getUserId(), entity.getTotalTokens(), entity.getDetailInfo()
        });
        this.executeBatch(sql, params);
    }

    public List<OpenAiLoggingEntity> getActionLogByActionId(int openAIActionId){
        String sql = " select openAIActionId,t1.promptId,t1.ownDomainId,t2.responseJson,detailInfo,json_extract(detailInfo, '$') as formatJson  ";
        sql += " from open_ai_action t1 join open_ai_logging t2 on t1.id = t2.openAIActionId ";
        sql += " where t1.id = ? and t1.processCount > 0 ";
        return findBySql(sql, openAIActionId);
    }

}
