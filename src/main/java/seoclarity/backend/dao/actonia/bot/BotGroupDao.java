package seoclarity.backend.dao.actonia.bot;

import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.bot.BotDirectoryEntity;
import seoclarity.backend.entity.actonia.bot.BotGroup;
import seoclarity.backend.upload.GoogleKGSearchApiMain;
import seoclarity.backend.utils.FormatUtils;

import java.util.*;

/**
 * Created by Leo on 2017/2/27.
 */
@Repository
public class BotGroupDao extends ActoniaBotBaseJdbcSupport<BotGroup> {

    private Gson gson = new Gson();

    @Override
    public String getTableName() {
        return "bot_group";
    }

    public BotGroup getGroupByDomainAndGroupName(int ownDomainId, String groupName) {
        String sql = "select * from " + getTableName() + " where ownDomainId = ? and name = ?";
        return findObject(sql, ownDomainId, groupName);
    }

    public List<BotGroup> getAllGroupByDomain(int ownDomainId) {
        String sql = "select * from " +getTableName()+ " where ownDomainId = ?";
        return findBySql(sql, ownDomainId);
    }

    public int[] insertBatch(List<BotGroup> groups) {
        String sql = "insert into " + getTableName() + " (" +
                "name, " +
                "ownDomainId, " +
                "defaultFlg, " +
                "updateUser, " +
                "updateDate) " +
                " values (?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (BotGroup entity : groups) {
            Object[] values = new Object[] {
                    entity.getName(),
                    entity.getOwnDomainId(),
                    entity.getDefaultFlg(),
                    entity.getUpdateUser(),
                    entity.getUpdateDate()
            };
            batch.add(values);
        }
        return this.executeBatch(sql, batch);
    }

    public long insertForEntity(BotGroup group) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("name", group.getName());
        params.put("ownDomainId", group.getOwnDomainId());
        params.put("defaultFlg", group.getDefaultFlg());
        params.put("updateUser", group.getUpdateUser());
        params.put("updateDate", new Date());
        return insertForLongId(params);
    }
}
