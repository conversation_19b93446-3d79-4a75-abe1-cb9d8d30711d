package seoclarity.backend.dao.actonia.extract;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.extract.RankQcStateEntity;
import seoclarity.backend.utils.FormatUtils;

@Repository
public class RankQcStateDAO extends ActoniaBaseJdbcSupport<RankQcStateEntity> {

    @Override
    public String getTableName() {
        return "rank_qc_state";
    }

    public Integer getRankQcStatus(RankQcStateEntity rankQcStateEntity) {

        StringBuilder sql = new StringBuilder();
        sql.append(" select status ");
        sql.append(" from ").append(getTableName());
        sql.append(" where rankDate = ? and ownDomainId = ? and engineId = ? and languageId = ? and device = ? and rankType = ? ");
        sql.append(" and frequence = ? ");

        System.out.println("===sql getRankQcStatus:" + sql.toString() + ",rankDate:" + rankQcStateEntity.getRankDate()
                + ",ownDomainId:" + rankQcStateEntity.getOwnDomainId() + ",engineId:" + rankQcStateEntity.getEngineId()
                + ",languageId:" + rankQcStateEntity.getLanguageId() + ",device:" + rankQcStateEntity.getDevice() + ",rankType:" + rankQcStateEntity.getRankType()
                + ",frequence:" + rankQcStateEntity.getFrequence());

        return queryForInteger(sql.toString(), rankQcStateEntity.getRankDate(), rankQcStateEntity.getOwnDomainId(), rankQcStateEntity.getEngineId(),
                rankQcStateEntity.getLanguageId(), rankQcStateEntity.getDevice(), rankQcStateEntity.getRankType(), rankQcStateEntity.getFrequence());

    }
    
    
    public Boolean isAllDomainPassedQcByDomainIdList(List<Integer> ownDomainIdList, Integer rankType, 
    		Integer frequence, String device, Date rankDate, Integer engineId, Integer langageId) {
    	
    	if (CollectionUtils.isEmpty(ownDomainIdList)) {
			return false;
		}
    	
    	String ownDomainIdStr = getIntegerListQueryParam(ownDomainIdList);

        StringBuilder sql = new StringBuilder();
        sql.append(" select count(1) ");
        sql.append(" from ").append(getTableName());
        sql.append(" where rankDate = ? and ownDomainId in (" + ownDomainIdStr + ") and device = ? and rankType = ? and engineId = ? and languageId = ? ");
        sql.append(" and frequence = ? and status not in (" + RankQcStateEntity.STATUS_OK + ", " + RankQcStateEntity.STATUS_NO_MANAGED_KEYWORDS + ") ");

        System.out.println("===sql getRankQcStatus: " + sql.toString() + " " 
        		+ FormatUtils.formatDateToYyyyMmDd(rankDate) + " - "  + engineId + " - "  + langageId + " - " 
        		+ device + " - " + rankType + " - " + frequence);
        
        Integer cnt = queryForInteger(sql.toString(), FormatUtils.formatDateToYyyyMmDd(rankDate), 
        		device, rankType, engineId, langageId, frequence);

        return (cnt == 0);

    }
    
    
    public Boolean isAllDomainPassedQcByDomainId(Integer ownDomainId, Integer rankType, 
    		Integer frequence, String device, Date rankDate, Integer engineId, Integer langageId) {
    	

        StringBuilder sql = new StringBuilder();
        sql.append(" select count(1) ");
        sql.append(" from ").append(getTableName());
        sql.append(" where rankDate = ? and ownDomainId = " + ownDomainId + " and device = ? and rankType = ? and engineId = ? and languageId = ? ");
        sql.append(" and frequence = ? and status not in (" + RankQcStateEntity.STATUS_OK + ", " + RankQcStateEntity.STATUS_NO_MANAGED_KEYWORDS + ") ");

        System.out.println("===sql getRankQcStatus: " + sql.toString() + " " 
        		+ rankDate + " - "  + engineId + " - "  + langageId + " - " 
        		+ device + " - " + rankType + " - " + frequence);
        
        Integer cnt = queryForInteger(sql.toString(), rankDate, 
        		device, rankType, engineId, langageId, frequence);

        return (cnt == 0);

    }

}
