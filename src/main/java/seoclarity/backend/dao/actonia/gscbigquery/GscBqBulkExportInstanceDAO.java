package seoclarity.backend.dao.actonia.gscbigquery;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.gscbigquery.GscBqBulkExportInstanceEntity;

import java.util.*;

@Repository
public class GscBqBulkExportInstanceDAO extends ActoniaBaseJdbcSupport<GscBqBulkExportInstanceEntity> {


    @Override
    public String getTableName() {
        return "gsc_bq_bulkexport_instance";
    }

    public String getLastDataDateByDomainId(int domainId){
        String sql = " select max(bqDataDate) from " + getTableName();
        sql += " where domainId = ? and uploadStatus = ? ";
        return queryForString(sql, domainId, GscBqBulkExportInstanceEntity.UPLOAD_STATUS_COMPLETE);
    }

    public String getMaxExtractByDomainId(int domainId, String bqNamespace){
        String sql = " select max(bqDataDate) from " + getTableName();
        sql += " where domainId = ? and extractStatus = ? and bqNamespace = ? ";
        return queryForString(sql, domainId, GscBqBulkExportInstanceEntity.EXTRACT_STATUS_COMPLETE, bqNamespace);
    }

    public int insert(GscBqBulkExportInstanceEntity entity) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("domainId", entity.getDomainId());
        values.put("bqNamespace", entity.getBqNamespace());
        values.put("bqDataDate", entity.getBqDataDate());
        values.put("bqEpochVersion", entity.getBqEpochVersion());
        values.put("bqPublishRime", entity.getBqPublishRime());
        values.put("extractStatus", entity.getExtractStatus());
        values.put("uploadStatus", entity.getUploadStatus());
        values.put("createdDate", new Date());
        return insert(values);
    }

    public void bulkInsert(List<GscBqBulkExportInstanceEntity> entityList) {
        String sql = "insert into " + getTableName()+ " (domainId,bqNamespace,bqDataDate,bqEpochVersion,bqPublishRime,extractStatus,uploadStatus) " +
                "values(?,?,?,?,?,?,?)";
        List<Object[]> params = new ArrayList<Object[]>();
        for (GscBqBulkExportInstanceEntity entity : entityList) {
            params.add(new Object[] {
                    entity.getDomainId(),
                    entity.getBqNamespace(),
                    entity.getBqDataDate(),
                    entity.getBqEpochVersion(),
                    entity.getBqEpochVersion(),
                    entity.getBqPublishRime(),
                    entity.getExtractStatus(),
                    entity.getUploadStatus()
            });
        }
        params.add(new Object[] {});
        this.executeBatch(sql, params);
    }

    public GscBqBulkExportInstanceEntity getInstanceById(int id){
        String sql = " select * from " + getTableName() + " where id = ? ";
        return findObject(sql, id);
    }

    public void updateExtractStatus(int id, int status, String s3FullPathFileName){
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set extractStatus = ? , updateDate = ? ");
        if(StringUtils.isNotBlank(s3FullPathFileName)){
            sql.append(" ,s3FullPathFileName='").append(s3FullPathFileName).append("' ");
        }
        sql.append(" where id =?  ");
        executeUpdate(sql.toString(), status, new Date(), id);
    }

    public void updateUploadStatus(int id, int status){
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set uploadStatus = ? , updateDate = ? where id =? ");
        executeUpdate(sql.toString(), status, new Date(), id);
    }

    public GscBqBulkExportInstanceEntity getInstanceByDate(int domainId, String bqNamespace, String bqDataDate){
        String sql = " select * from " + getTableName() + " where domainId = ? and bqNamespace= ? and bqDataDate = ?";
        return findObject(sql, domainId, bqNamespace, bqDataDate);
    }

    public List<GscBqBulkExportInstanceEntity> getAllInstanceByDomainId(int domainId, String dataDate){
        String sql = " select * from " + getTableName();
        sql += " where domainId = ? ";
        if(StringUtils.isNotBlank(dataDate)){
            sql += " and bqDataDate = '" + dataDate + "' ";
        }
        sql += " order by bqDataDate ";
        return findBySql(sql, domainId);
    }

}
