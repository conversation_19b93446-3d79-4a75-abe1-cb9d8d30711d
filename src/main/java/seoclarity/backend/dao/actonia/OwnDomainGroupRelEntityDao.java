package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainGroupRelEntity;

import java.util.List;

@Repository
public class OwnDomainGroupRelEntityDao extends ActoniaBaseJdbcSupport<OwnDomainGroupRelEntity> {

    @Override
    public String getTableName() {
        return "owndomain_group_rel ";
    }

    public List<OwnDomainGroupRelEntity> checkBelongGroup(int domainId) {
        String sql = "SELECT * FROM " + getTableName() + "  WHERE ownDomainId = ? ";
        return findBySql(sql, domainId);
    }

}
