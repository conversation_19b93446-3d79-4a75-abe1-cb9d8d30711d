package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickstreamUploadLogEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class GscClickstreamUploadLogDAO extends ActoniaMdbBaseJdbcSupport<GscClickstreamUploadLogEntity>{

    @Override
    public String getTableName() {
        return "gsc_clickstream_upload_log";
    }

    public void insert(GscClickstreamUploadLogEntity entity) {
        String sql = " insert into " + getTableName();
        sql += " (dataType,month,weekIndexInMonth,weekStartDate,weekEndDate,status,uploadStartTime)  values (?,?,?,?,?,?,?) ";
        this.executeUpdate(sql,
                entity.getDataType(), entity.getMonth(), entity.getWeekIndexInMonth(), entity.getWeekStartDate(),
                entity.getWeekEndDate(), entity.getStatus(), entity.getUploadStartTime());
    }

    public GscClickstreamUploadLogEntity getByUniqueKey(int dataType, int month, int weekIndexInMonth){
        String sql = " select * from " + getTableName() + " where dataType=? and month=? and weekIndexInMonth=? ";
        return findObject(sql, dataType, month, weekIndexInMonth);
    }

    public GscClickstreamUploadLogEntity getByStartDayEndDay(int dataType, int month, int weekStartDate, int weekEndDate){
        String sql = " select * from " + getTableName() + " where dataType=? and month=? and weekStartDate=? and weekEndDate=? ";
        return findObject(sql, dataType, month, weekStartDate, weekEndDate);
    }

    public void updateById(long id, int uploadStatus, Date uploadEndTime, long uploadRows, String uploadErrorMsg) {

        List paramList = new ArrayList();

        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set status = ? ");
        sql.append(" ,uploadEndTime = ? ");
        sql.append(" ,uploadRows = ? ");
        paramList.add(uploadStatus);
        paramList.add(uploadEndTime);
        paramList.add(uploadRows);

        if(StringUtils.isNotBlank(uploadErrorMsg)){
            sql.append(" ,errorMsg = ? ");
            paramList.add(uploadErrorMsg);
        }
        sql.append(" where id = ? ");
        paramList.add(id);

        executeUpdate(sql.toString(), paramList.toArray());
    }

    public Integer getMaxWeekIndexInMonth(int dataType, int month){
        String sql = " select max(weekIndexInMonth) from " + getTableName();
        sql += " where dataType=? and month=? ";
        return queryForInteger(sql, dataType, month);
    }

    public void updateMonthDataFullyUploaded(long id, int monthDataFullyUploaded) {
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set monthDataFullyUploaded = ? ");
        sql.append(" where id = ? ");
        executeUpdate(sql.toString(), monthDataFullyUploaded, id);
    }

    public GscClickstreamUploadLogEntity getNeedUploadWeeklyLog(int dataType, int status){
        String sql = " select * from " + getTableName() + " where dataType=? and status=? order by id limit 1 ";
        return findObject(sql, dataType, status);
    }

    public void updateWeeklyStreamById(long id, int uploadStatus,Date uploadStartTime, Date uploadEndTime, long uploadRows, String uploadErrorMsg) {

        List paramList = new ArrayList();

        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set status = ? ");
        sql.append(" ,streamStartTime = ? ");
        sql.append(" ,streamEndTime = ? ");
        sql.append(" ,streamCount = ? ");
        paramList.add(uploadStatus);
        paramList.add(uploadStartTime);
        paramList.add(uploadEndTime);
        paramList.add(uploadRows);

        if(StringUtils.isNotBlank(uploadErrorMsg)){
            sql.append(" ,errorMsg = ? ");
            paramList.add(uploadErrorMsg);
        }
        sql.append(" where id = ? ");
        paramList.add(id);

        executeUpdate(sql.toString(), paramList.toArray());
    }

}
