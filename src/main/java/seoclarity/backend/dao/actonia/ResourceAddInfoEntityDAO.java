package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.ResourceAddDetailEntity;
import seoclarity.backend.entity.actonia.ResourceAddInfoEntity;
import seoclarity.backend.entity.actonia.ResourceDeleteDetailEntity;
import seoclarity.backend.entity.actonia.ResourceDeleteInfoEntity;

@Repository
public class ResourceAddInfoEntityDAO extends ActoniaBaseJdbcSupport<ResourceAddInfoEntity> {

    /*
      * (non-Javadoc)
      *
      * @see com.actonia.subserver.dao.BaseJdbcSupport#getTableName()
      */
    @Override
    public String getTableName() {
        return "resource_add_info";
    }

    public List<ResourceAddInfoEntity> getProcessingInfoList(Date startDate) {
        String sql = " select own_domain_id, group_concat(distinct operation_type) as operationTypes from resource_add_info where create_date>=? and status=1 group by own_domain_id";
        return this.findBySql(sql, startDate);
    }

	public int insert(ResourceAddInfoEntity addInfoEntity) {
		Map<String, Object> val = new HashMap<String, Object>();
		val.put("operation_type", addInfoEntity.getOperationType());
		val.put("own_domain_id", addInfoEntity.getOwnDomainId());
		val.put("user_id", addInfoEntity.getUserId());
		val.put("create_date", addInfoEntity.getCreateDate());
		val.put("status", addInfoEntity.getStatus());

		return insert(val);
	}

    public ResourceAddInfoEntity getById(int id) {
        String sql = " select * from resource_add_info where id=? ";
        return this.findObject(sql, id);
    }

	public boolean updateStatus(int infoId, int ownDomainId, int operationType, int status) {
		StringBuffer sql = new StringBuffer();

		sql.append(" UPDATE ").append(getTableName());
		sql.append(" SET ");
		sql.append(" 	 status = ? ");
		sql.append(" WHERE ");
		sql.append(" 	id = ? ");
		sql.append(" 	AND own_domain_id = ? ");
		sql.append(" 	AND operation_type = ? ");
		sql.append(" LIMIT 1 ");

		List args = new ArrayList();
		args.add(status);
		args.add(infoId);
		args.add(ownDomainId);
		args.add(operationType);

		return executeUpdate(sql.toString(), args.toArray()) > 0;
	}

    public List<ResourceAddInfoEntity> getAddInfoList(int detailCountThreshold, Set<Integer> domainNotExec, int limitDetails) {
	    List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();

	    	StringBuffer sqlCommon = new StringBuffer();
	    	sqlCommon.append(" select info.*, derived.cnt as detailCount from resource_add_info info join( ");
	    	sqlCommon.append("  select rai.id as id,count(*) as cnt from resource_add_info rai join resource_add_detail rad ");
	    	sqlCommon.append("  on rai.id=rad.resource_info_id where rai.operation_type>=? and rai.operation_type<=? and (rai.status=? or rai.status=?) ");
	    	sqlCommon.append("  and (rad.status is null or rad.status!=? and rad.status!=?) group by rai.id having count(1) <= ?) derived on info.id=derived.id ");

	    	if (domainNotExec!=null && domainNotExec.size()>0) {
	    		sqlCommon.append(" where info.own_domain_id not in ( ");
	    		sqlCommon.append(getQueryParamByIntList(domainNotExec));
	    		sqlCommon.append(" ) ");
	    	}

	    	StringBuffer sql = new StringBuffer();
	    	sql.append(sqlCommon.toString()).append(" order by derived.id ");
	    	List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(),
	    			ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID,
	        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
	        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID, limitDetails);
	    	for (ResourceAddInfoEntity entity : addInfoList) {
				if (!resultAddInfoList.contains(entity)) {
					resultAddInfoList.add(entity);
				}
			}

	    	return resultAddInfoList;
    }

    // old
    public List<ResourceAddInfoEntity> getAddInfoList(int detailCountThreshold, Set<Integer> domainNotExec) {
    	List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();

    	StringBuffer sqlCommon = new StringBuffer();
    	sqlCommon.append(" select info.*, derived.cnt as detailCount from resource_add_info info join( ");
    	sqlCommon.append("  select rai.id as id,count(*) as cnt from resource_add_info rai join resource_add_detail rad ");
    	sqlCommon.append("  on rai.id=rad.resource_info_id where rai.operation_type>=? and rai.operation_type<=? and (rai.status=? or rai.status=?) ");
    	sqlCommon.append("  and (rad.status is null or rad.status!=? and rad.status!=?) group by rai.id) derived on info.id=derived.id ");

    	if (domainNotExec!=null && domainNotExec.size()>0) {
    		sqlCommon.append(" where info.own_domain_id not in ( ");
    		sqlCommon.append(getQueryParamByIntList(domainNotExec));
    		sqlCommon.append(" ) ");
    	}

    	StringBuffer sql = new StringBuffer();
    	/*sql.append(sqlCommon.toString()).append(" where derived.cnt>0 and derived.cnt<=? order by derived.cnt,derived.id ");
    	List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(), 
    			ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID, 
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID, 
        		detailCountThreshold);
    	if (addInfoList != null && addInfoList.size() > 0) {
    		resultAddInfoList.addAll(addInfoList);
    	}
    	sql = new StringBuffer();
    	sql.append(sqlCommon.toString()).append(" where derived.cnt>? order by derived.id ");
    	List<ResourceAddInfoEntity> addInfoList2 = this.findBySql(sql.toString(), 
    			ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID, 
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID, 
        		detailCountThreshold);
    	if (addInfoList2 != null && addInfoList2.size() > 0) {
    		for (ResourceAddInfoEntity entity : addInfoList2) {
    			if (!resultAddInfoList.contains(entity)) {
    				resultAddInfoList.add(entity);
    			}
    		}
    	}*/

    	sql.append(sqlCommon.toString()).append(" order by derived.id ");
    	List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(),
    			ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID,
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID);
    	for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}

    	return resultAddInfoList;
    }

    public List<ResourceAddInfoEntity> getAddInfoList(int detailSizeThreshold, Set<Integer> execDomains, Set<Integer> domainNotExec,
    		Integer[] processType, Integer[] notProcessType) {
    	List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();
    	StringBuffer sqlCommon = new StringBuffer();
    	sqlCommon.append(" select info.*, derived.cnt as detailCount from resource_add_info info join( ");
    	sqlCommon.append("  select rai.id as id,count(*) as cnt from resource_add_info rai join resource_add_detail rad ");
    	sqlCommon.append("  on rai.id=rad.resource_info_id where rai.operation_type>=? and rai.operation_type<=? and (rai.status=? or rai.status=?) ");
    	sqlCommon.append("  and (rad.status is null or rad.status!=? and rad.status!=?) group by rai.id) derived on info.id=derived.id ");
    	sqlCommon.append(" where derived.cnt<=? ");

    	if (execDomains != null && execDomains.size() > 0) {
    		sqlCommon.append(" and info.own_domain_id in (").append(getQueryParamByIntList(execDomains)).append(") ");
    	}
    	if (domainNotExec != null && domainNotExec.size() > 0) {
    		sqlCommon.append(" and info.own_domain_id not in (").append(getQueryParamByIntList(domainNotExec)).append(") ");
    	}

    	// by Meo
    	if (notProcessType != null && notProcessType.length > 0) {
    		sqlCommon.append(" and info.operation_type not in (" + (StringUtils.join(notProcessType, ',')) + ") ");
    	}
    	if (processType != null && processType.length > 0) {
    		sqlCommon.append(" and info.operation_type in (" + (StringUtils.join(processType, ',')) + ") ");
    	}

    	StringBuffer sql = new StringBuffer();
    	sql.append(sqlCommon.toString()).append(" order by derived.id ");
    	List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(),
    			ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID,
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID, detailSizeThreshold);
    	for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}

    	return resultAddInfoList;
    }


    //https://www.wrike.com/open.htm?id=411853590
    public List<ResourceAddInfoEntity> getAddInfoListForHighRankUrl(Set<Integer> execDomains, Set<Integer> domainNotExec, int operationType) {
    	List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();
    	StringBuffer sqlCommon = new StringBuffer();
    	sqlCommon.append(" select info.*, derived.cnt as detailCount from resource_add_info info join( ");
    	sqlCommon.append("  select rai.id as id,count(*) as cnt from resource_add_info rai join resource_add_detail rad ");
    	sqlCommon.append("  on rai.id=rad.resource_info_id where rai.operation_type=? and (rai.status=? or rai.status=?) ");
    	sqlCommon.append("  and (rad.status is null or rad.status!=? and rad.status!=?) group by rai.id) derived on info.id=derived.id ");
    	sqlCommon.append(" where 1=1 ");

    	if (execDomains != null && execDomains.size() > 0) {
    		sqlCommon.append(" and info.own_domain_id in (").append(getQueryParamByIntList(execDomains)).append(") ");
    	}
    	if (domainNotExec != null && domainNotExec.size() > 0) {
    		sqlCommon.append(" and info.own_domain_id not in (").append(getQueryParamByIntList(domainNotExec)).append(") ");
    	}

    	StringBuffer sql = new StringBuffer();
    	sql.append(sqlCommon.toString()).append(" order by info.own_domain_id,info.user_id,info.id ");
    	List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(),
    			operationType,
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID);
    	for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}

    	return resultAddInfoList;
    }


	public List<ResourceAddInfoEntity> getAddInfoListForContentGuardUrl(Set<Integer> execDomains, Set<Integer> domainNotExec, int operationType) {
		List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();
		StringBuffer sqlCommon = new StringBuffer();
		sqlCommon.append(" select info.*, derived.cnt as detailCount from resource_add_info info join( ");
		sqlCommon.append("  select rai.id as id,count(*) as cnt from resource_add_info rai join resource_add_detail rad ");
		sqlCommon.append("  on rai.id=rad.resource_info_id where rai.operation_type=? and (rai.status=? or rai.status=?) ");
		sqlCommon.append("  and (rad.status is null or rad.status!=? and rad.status!=?) group by rai.id) derived on info.id=derived.id ");
		sqlCommon.append(" where 1=1 ");

		if (execDomains != null && execDomains.size() > 0) {
			sqlCommon.append(" and info.own_domain_id in (").append(getQueryParamByIntList(execDomains)).append(") ");
		}
		if (domainNotExec != null && domainNotExec.size() > 0) {
			sqlCommon.append(" and info.own_domain_id not in (").append(getQueryParamByIntList(domainNotExec)).append(") ");
		}

		StringBuffer sql = new StringBuffer();
		sql.append(sqlCommon.toString()).append(" order by info.own_domain_id,info.user_id,info.id ");
		List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(),
				operationType,
				ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
				ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID);
		for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}
		return resultAddInfoList;
	}



    public List<ResourceAddInfoEntity> getAddInfoList(int detailCountThreshold, Set<Integer> domainNotExec, Integer[] processType, Integer[] notProcessType) {
    	List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();

    	StringBuffer sqlCommon = new StringBuffer();
    	sqlCommon.append(" select info.*, derived.cnt as detailCount from resource_add_info info join( ");
    	sqlCommon.append("  select rai.id as id,count(*) as cnt from resource_add_info rai join resource_add_detail rad ");
    	sqlCommon.append("  on rai.id=rad.resource_info_id where rai.operation_type>=? and rai.operation_type<=? and (rai.status=? or rai.status=?) ");
    	sqlCommon.append("  and (rad.status is null or rad.status!=? and rad.status!=?) group by rai.id) derived on info.id=derived.id ");
    	sqlCommon.append(" where 1=1 ");

    	if (domainNotExec!=null && domainNotExec.size()>0) {
    		sqlCommon.append(" and info.own_domain_id not in ( ");
    		sqlCommon.append(getQueryParamByIntList(domainNotExec));
    		sqlCommon.append(" ) ");
    	}

    	// by Meo
    	if (notProcessType != null && notProcessType.length > 0) {
    		sqlCommon.append(" and info.operation_type not in (" + (StringUtils.join(notProcessType, ',')) + ") ");
    	}
    	if (processType != null && processType.length > 0) {
    		sqlCommon.append(" and info.operation_type in (" + (StringUtils.join(processType, ',')) + ") ");
    	}

    	StringBuffer sql = new StringBuffer();
    	/*sql.append(sqlCommon.toString()).append(" where derived.cnt>0 and derived.cnt<=? order by derived.cnt,derived.id ");
    	List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(), 
    			ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID, 
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID, 
        		detailCountThreshold);
    	if (addInfoList != null && addInfoList.size() > 0) {
    		resultAddInfoList.addAll(addInfoList);
    	}
    	sql = new StringBuffer();
    	sql.append(sqlCommon.toString()).append(" where derived.cnt>? order by derived.id ");
    	List<ResourceAddInfoEntity> addInfoList2 = this.findBySql(sql.toString(), 
    			ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID, 
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID, 
        		detailCountThreshold);
    	if (addInfoList2 != null && addInfoList2.size() > 0) {
    		for (ResourceAddInfoEntity entity : addInfoList2) {
    			if (!resultAddInfoList.contains(entity)) {
    				resultAddInfoList.add(entity);
    			}
    		}
    	}*/

    	sql.append(sqlCommon.toString()).append(" order by derived.id ");
    	List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(),
    			ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID,
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
        		ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID);
    	for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}

    	return resultAddInfoList;
    }

	private String getQueryParamByIntList(Set<Integer> intList) {
		if (intList == null || intList.size() == 0) {
			return null;
		}
		StringBuilder intStr = new StringBuilder();
		for (Integer i : intList) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");

		return intParams;
	}

    public List<ResourceAddInfoEntity> getNotCompletedList(int ownDomainId, Date fromDate, Date toDate) {
    	StringBuffer sql = new StringBuffer();
        sql.append(" select info.* from resource_add_info info join( ");
        sql.append("  select rai.id as id,count(*) as cnt from resource_add_info rai join resource_add_detail rad ");
        sql.append("  on rai.id=rad.resource_info_id where rai.own_domain_id = ? and rai.operation_type>=? and rai.operation_type<=? ");
        sql.append("  and rai.status != ? and rai.create_date > ? and rai.create_date < ?  group by rai.id) derived ");
        sql.append(" on info.id=derived.id where derived.cnt>0 order by info.id ");

        return this.findBySql(sql.toString(), ownDomainId, ResourceAddInfoEntity.OPERATION_TYPE_ADD_MIN_ID,
        		ResourceAddInfoEntity.OPERATION_TYPE_ADD_MAX_ID, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, fromDate, toDate);
    }

    public List<ResourceAddInfoEntity> getSyncRankcheckInfoList(int operationType) {
        String sql = " select * from resource_add_info where operation_type=? and (status=? or status=?) order by id asc ";

        return this.findBySql(sql, operationType,
        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR);
    }

    public List<ResourceAddInfoEntity> getSyncRankcheckInfoList(int operationType, Set<Integer> notExecDomains) {
	        StringBuffer sql = new StringBuffer();
	        sql.append(" select * from resource_add_info where operation_type=? and (status=? or status=?) ");
	        if (notExecDomains != null && notExecDomains.size() > 0) {
	        	sql.append(" and own_domain_id not in (");
	        	sql.append(getQueryParamByIntList(notExecDomains));
	        	sql.append(" ) ");
	        }
	        sql.append(" order by id asc ");
	        return this.findBySql(sql.toString(), operationType,
	        		ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR);
	    }

    public List<ResourceAddInfoEntity> getNotCompletedSyncRankcheckList(int ownDomainId, int syncType, Date fromDate, Date toDate) {
    	StringBuffer sql = new StringBuffer();
        sql.append(" select * from resource_add_info where own_domain_id = ? and operation_type=? ");
        sql.append(" and status != ? and create_date > ? and create_date < ? ");

        return this.findBySql(sql.toString(), ownDomainId, syncType,
        		ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, fromDate, toDate);
    }

    public boolean updateStatusBeforeProcess(int id, int status, Date processDate, String serverInfo, int currentStatus) {
    	StringBuffer sql = new StringBuffer();
    	sql.append(" update resource_add_info set status=?, process_date=?, seq=IFNULL(seq,0)+1, server_info=? ");
    	sql.append(" where id=? and status=? limit 1 ");
    	return executeUpdate(sql.toString(), status, processDate, serverInfo, id, currentStatus) > 0;
    }

    public List<ResourceAddInfoEntity> getOtherProcessingListByDomain(int id, int ownDomainId, Date processDate) {
    	Date fromDate = DateUtils.addDays(new Date(), -5);
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from resource_add_info where id!=? ");
        sql.append(" and own_domain_id=? and status=? and process_date>=? and process_date<? ");

        return findBySql(sql.toString(), id, ownDomainId, ResourceAddInfoEntity.STATUS_PROCESSING, fromDate, processDate);
    }

    public boolean returnToQueue(int id, int currentStatus) {
    	StringBuffer sql = new StringBuffer();
    	sql.append(" update resource_add_info set status=?, process_date=NULL, seq=seq-1, server_info=NULL ");
    	sql.append(" where id=? and status=? limit 1 ");

    	int updateResult = executeUpdate(sql.toString(), ResourceAddInfoEntity.STATUS_NEWLY_CREATED, id, currentStatus);
    	return updateResult > 0;
    }

    public void updateStatusAfterProcess(int id, int status, Date endDate) {
        String sql = "update resource_add_info set status=?, end_date=? where id=? ";
        executeUpdate(sql, status, endDate, id);
    }

    // https://www.wrike.com/open.htm?id=25444734
 	// by Harry
 	public List<Map> queryNewlyAddedKeywordCount(Date sDate, Date eDate) {
 		// https://www.wrike.com/open.htm?id=20881127
 		StringBuffer sql = new StringBuffer();

 		sql.append(" select ");
 		sql.append(" 	total.own_domain_id as ownDomainId, ");
 		sql.append(" 	total.totalCount as totalKeywords, ");
 		sql.append(" 	added.addedCount as totalAdded, ");
 		sql.append(" 	processedRankCheck.processedRankCount as totalProcessedRank, ");
 		sql.append(" 	rankFailed.rankFailedCount as totalRankFailed ");
 		sql.append(" from  ");

 		sql.append(" ( ");
		sql.append(" select ");
		sql.append(" 	rai.own_domain_id as own_domain_id, ");
		sql.append(" 	count(distinct rai.own_domain_id, rad.resource_main ) as totalCount ");
		sql.append(" from  ");
		sql.append(" 	resource_add_info rai ");
		sql.append(" join ");
		sql.append(" 	resource_add_detail rad ");
		sql.append(" on  ");
		sql.append(" 	rai.id = rad.resource_info_id ");
		sql.append(" where  ");
		sql.append(" 		rai.create_date >= ? ");
		sql.append(" 	and rai.create_date < ? ");
		sql.append(" 	and operation_type >= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MIN_ID);
		sql.append(" 	and operation_type <= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MAX_ID);
 		sql.append(" group by ");
		sql.append(" 	rai.own_domain_id");
		sql.append(" ) total ");

 		sql.append(" left join ");

 		sql.append(" ( ");
 		sql.append(" select ");
 		sql.append(" 	rai.own_domain_id as own_domain_id, ");
 		sql.append(" 	count(distinct rai.own_domain_id, rad.resource_main ) as addedCount ");
 		sql.append(" from  ");
 		sql.append(" 	resource_add_info rai ");
 		sql.append(" join ");
 		sql.append(" 	resource_add_detail rad ");
 		sql.append(" on  ");
 		sql.append(" 	rai.id = rad.resource_info_id ");
 		sql.append(" where  ");
 		sql.append(" 		rai.create_date >= ? ");
 		sql.append("	and rai.create_date < ? ");
 		sql.append("	and rad.status in ( "+ ResourceAddDetailEntity.STATUS_PROCESS_FINISHED +"," + ResourceAddDetailEntity.STATUS_INVALID + ") ");
		sql.append(" 	and operation_type >= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MIN_ID);
		sql.append(" 	and operation_type <= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MAX_ID);
 		sql.append(" group by ");
 		sql.append(" 	rai.own_domain_id");
 		sql.append(" ) added ");

 		sql.append(" on  ");
 		sql.append("		total.own_domain_id = added.own_domain_id   ");

 		sql.append(" left join ");

 		sql.append(" ( ");
 		sql.append(" select ");
 		sql.append(" 	rai.own_domain_id as own_domain_id, ");
 		sql.append(" 	count(distinct rai.own_domain_id, rad.resource_main ) as processedRankCount ");
 		sql.append(" from  ");
 		sql.append(" 	resource_add_info rai ");
 		sql.append(" join ");
 		sql.append(" 	resource_add_detail rad ");
 		sql.append(" on  ");
 		sql.append(" 	rai.id = rad.resource_info_id ");
 		sql.append(" where  ");
 		sql.append(" 		rai.create_date >= ? ");
 		sql.append(" 	and rai.create_date < ? ");
 		sql.append(" 	and rad.status_rank in ( " + ResourceAddDetailEntity.STATUS_RANK_FINISHED + "," + ResourceAddDetailEntity.STATUS_RANK_NO_NEED_RANKED + ") ");
		sql.append(" 	and operation_type >= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MIN_ID);
		sql.append(" 	and operation_type <= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MAX_ID);
 		sql.append(" group by ");
 		sql.append(" 	rai.own_domain_id");
 		sql.append(" ) processedRankCheck ");

 		sql.append(" on  ");
 		sql.append(" 		total.own_domain_id = processedRankCheck.own_domain_id   ");

 		sql.append(" left join ");

 		sql.append(" ( ");
 		sql.append(" select ");
 		sql.append(" 	rai.own_domain_id as own_domain_id, ");
 		sql.append(" 	count(distinct rai.own_domain_id, rad.resource_main ) as rankFailedCount ");
 		sql.append(" from  ");
 		sql.append(" 	resource_add_info rai ");
 		sql.append(" join ");
 		sql.append(" 	resource_add_detail rad ");
 		sql.append(" on  ");
 		sql.append(" 	rai.id = rad.resource_info_id ");
 		sql.append(" where  ");
 		sql.append(" 		rai.create_date >= ? ");
 		sql.append(" 	and rai.create_date < ? ");
 		sql.append(" 	and rad.status_rank = " + ResourceAddDetailEntity.STATUS_RANK_ERROR + " ");
		sql.append(" 	and operation_type >= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MIN_ID);
		sql.append(" 	and operation_type <= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MAX_ID);
 		sql.append(" group by ");
 		sql.append(" 	rai.own_domain_id");
 		sql.append(" ) rankFailed ");

 		sql.append(" on  ");
 		sql.append(" 		total.own_domain_id = rankFailed.own_domain_id   ");

 		sql.append(" order by ");
 		sql.append(" 	total.own_domain_id");

 		return this.queryForMapList(sql.toString(), sDate, eDate, sDate, eDate, sDate, eDate, sDate, eDate );
 	}

 	public List<Map> queryNewlyAddedKeywordDetail(Date sDate, Date eDate){
 		// https://www.wrike.com/open.htm?id=20881127
 		StringBuffer sql = new StringBuffer();

		sql.append(" select ");
		sql.append(" 	distinct rai.own_domain_id, rad.resource_main");
		sql.append(" from  ");
		sql.append(" 	resource_add_info rai ");
		sql.append(" join ");
		sql.append(" 	resource_add_detail rad ");
		sql.append(" on  ");
		sql.append(" 	rai.id = rad.resource_info_id ");
		sql.append(" where  ");
		sql.append(" 		rai.create_date >= ? ");
		sql.append(" 	and rai.create_date < ? ");
		sql.append(" 	and operation_type >= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MIN_ID);
		sql.append(" 	and operation_type <= ").append(ResourceAddInfoEntity.OPERATION_TYPE_ADD_KEYWORD_MAX_ID);
 		sql.append(" order by ");
		sql.append(" 	rai.own_domain_id");

		return this.queryForMapList(sql.toString(), sDate, eDate);
 	}

 	public List<ResourceAddInfoEntity> getResourceAddInfoList(int oid, int operationType, int userId, Date startDate) {
 		String sql = "select  *  from resource_add_info where create_date >= ? and operation_type = ? and user_id = ? and own_domain_id = ? ";
 		return this.findBySql(sql, startDate, operationType, userId, oid);
 	}

 	public List<ResourceAddInfoEntity> getResourceAddInfoList(int oid, int operationType, int status, Date startDate, Date endDate) {
 		String sql = "select  *  from resource_add_info where create_date >= ? and  create_date <= ? and status = ? and operation_type = ? and own_domain_id = ? ";
 		sql += " and process_date is not null and end_date is not null  and server_info is not null";
 		return this.findBySql(sql, startDate, endDate, status, operationType, oid);
 	}

 	public int deleteDetails(int oid, int infoId) {
 		String sql = "delete from resource_add_detail where own_domain_id = ? and resource_info_id = ? ";
 		return this.executeUpdate(sql, oid, infoId);
 	}

	public List<ResourceAddInfoEntity> getAddInfoListForListType(Set<Integer> execDomains, Set<Integer> domainNotExec,
													  Integer[] processType, Integer[] notProcessType) {

		List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();
		StringBuffer sqlCommon = new StringBuffer();

		sqlCommon.append(" 	SELECT rai.id as id, rai.operation_type as operationType,rai.own_domain_id,rai.user_id, ");
		sqlCommon.append("  	rai.status,rai.process_date,rai.end_date,rai.create_date	");
		sqlCommon.append("  FROM " + getTableName() + " rai");
		sqlCommon.append("  JOIN resource_add_detail rad ON rai.id = rad.resource_info_id ");
		sqlCommon.append("  WHERE 1=1 ");
		sqlCommon.append("  and rai.operation_type >= ? AND rai.operation_type <= ? ");
		sqlCommon.append("  AND (rai.status = ? OR rai.status = ?) ");
		sqlCommon.append("  AND (rad.status is NULL or rad.status != ? AND rad.status != ? ) ");

		if (execDomains != null && execDomains.size() > 0) {
			sqlCommon.append(" and rai.own_domain_id in (").append(getQueryParamByIntList(execDomains)).append(") ");
		}
		if (domainNotExec != null && domainNotExec.size() > 0) {
			sqlCommon.append(" and rai.own_domain_id not in (").append(getQueryParamByIntList(domainNotExec)).append(") ");
		}

		if (notProcessType != null && notProcessType.length > 0) {
			sqlCommon.append(" and rai.operation_type not in (" + (StringUtils.join(notProcessType, ',')) + ") ");
		}
		if (processType != null && processType.length > 0) {
			sqlCommon.append(" and rai.operation_type in (" + (StringUtils.join(processType, ',')) + ") ");
		}

		sqlCommon.append("  UNION ALL ");
		sqlCommon.append("  SELECT rdi.id as id , rdd.resource_type as operationType , rdi.own_domain_id,rdi.user_id , ");
		sqlCommon.append("  	rdi.status ,rdi.process_date ,rdi.end_date,rdi.create_date	");
		sqlCommon.append("  FROM resource_delete_info rdi ");
		sqlCommon.append("  JOIN resource_delete_detail rdd ON rdi.id = rdd.delete_info_id ");
		sqlCommon.append("  WHERE rdi.enabled = ? ");
		sqlCommon.append("   AND rdd.resource_type >= ? AND rdd.resource_type <= ?  ");
		sqlCommon.append("   AND (rdi. STATUS = ? OR rdi. STATUS = ?) ");
		sqlCommon.append("   AND (rdd. STATUS IS NULL OR rdd. STATUS != ? AND rdd. STATUS != ? ) ");

		if (execDomains != null && execDomains.size() > 0) {
			sqlCommon.append(" and rdi.own_domain_id in (").append(getQueryParamByIntList(execDomains)).append(") ");
		}
		if (domainNotExec != null && domainNotExec.size() > 0) {
			sqlCommon.append(" and rdi.own_domain_id not in (").append(getQueryParamByIntList(domainNotExec)).append(") ");
		}

		if (notProcessType != null && notProcessType.length > 0) {
			sqlCommon.append(" and rdd.resource_type not in (" + (StringUtils.join(notProcessType, ',')) + ") ");
		}
		if (processType != null && processType.length > 0) {
			sqlCommon.append(" and rdd.resource_type in (" + (StringUtils.join(processType, ',')) + ") ");
		}

		StringBuffer sql = new StringBuffer();
		sql.append(sqlCommon.toString()).append(" order by create_date ASC ");
		List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(),
				ResourceAddInfoEntity.OPERATION_TYPE_LIST_KEYWORD, ResourceAddInfoEntity.OPERATION_TYPE_LIST_KEYWORD_AND_TARGET_URL,
				ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
				ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID,
				ResourceDeleteInfoEntity.ENABLED,
				ResourceDeleteDetailEntity.RESOURCE_TYPE_LIST_KEYWORD,ResourceDeleteDetailEntity.RESOURCE_TYPE_LIST_TARGET_URL,
				ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
				ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID);

		System.out.println("====SQL getAddInfoListForListType : " + sql.toString());

		for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}

		return resultAddInfoList;
	}

	public List<ResourceAddInfoEntity> getAddInfoListForTrafficMixDataType(Set<Integer> execDomains, Set<Integer> domainNotExec,
																 Integer[] processType, Integer[] notProcessType) {

		List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();
		StringBuffer sqlCommon = new StringBuffer();

		sqlCommon.append(" 	SELECT rai.id as id, rai.operation_type as operationType,rai.own_domain_id,rai.user_id, ");
		sqlCommon.append("  	rai.status,rai.process_date,rai.end_date,rai.create_date	");
		sqlCommon.append("  FROM " + getTableName() + " rai");
		sqlCommon.append("  JOIN resource_add_detail rad ON rai.id = rad.resource_info_id ");
		sqlCommon.append("  WHERE 1=1 ");
		sqlCommon.append("  and rai.operation_type = ? ");
		sqlCommon.append("  AND (rai.status = ? OR rai.status = ?) ");
		sqlCommon.append("  AND (rad.status is NULL or rad.status != ? AND rad.status != ? ) ");

		if (execDomains != null && execDomains.size() > 0) {
			sqlCommon.append(" and rai.own_domain_id in (").append(getQueryParamByIntList(execDomains)).append(") ");
		}
		if (domainNotExec != null && domainNotExec.size() > 0) {
			sqlCommon.append(" and rai.own_domain_id not in (").append(getQueryParamByIntList(domainNotExec)).append(") ");
		}

		if (notProcessType != null && notProcessType.length > 0) {
			sqlCommon.append(" and rai.operation_type not in (" + (StringUtils.join(notProcessType, ',')) + ") ");
		}
		if (processType != null && processType.length > 0) {
			sqlCommon.append(" and rai.operation_type in (" + (StringUtils.join(processType, ',')) + ") ");
		}

		sqlCommon.append("  UNION ALL ");
		sqlCommon.append("  SELECT rdi.id as id , rdd.resource_type as operationType , rdi.own_domain_id,rdi.user_id , ");
		sqlCommon.append("  	rdi.status ,rdi.process_date ,rdi.end_date,rdi.create_date	");
		sqlCommon.append("  FROM resource_delete_info rdi ");
		sqlCommon.append("  JOIN resource_delete_detail rdd ON rdi.id = rdd.delete_info_id ");
		sqlCommon.append("  WHERE rdi.enabled = ? ");
		sqlCommon.append("   AND rdd.resource_type = ?  ");
		sqlCommon.append("   AND (rdi. STATUS = ? OR rdi. STATUS = ?) ");
		sqlCommon.append("   AND (rdd. STATUS IS NULL OR rdd. STATUS != ? AND rdd. STATUS != ? ) ");

		if (execDomains != null && execDomains.size() > 0) {
			sqlCommon.append(" and rdi.own_domain_id in (").append(getQueryParamByIntList(execDomains)).append(") ");
		}
		if (domainNotExec != null && domainNotExec.size() > 0) {
			sqlCommon.append(" and rdi.own_domain_id not in (").append(getQueryParamByIntList(domainNotExec)).append(") ");
		}

		if (notProcessType != null && notProcessType.length > 0) {
			sqlCommon.append(" and rdd.resource_type not in (" + (StringUtils.join(notProcessType, ',')) + ") ");
		}
		if (processType != null && processType.length > 0) {
			sqlCommon.append(" and rdd.resource_type in (" + (StringUtils.join(processType, ',')) + ") ");
		}

		StringBuffer sql = new StringBuffer();
		sql.append(sqlCommon.toString()).append(" order by create_date ASC ");
		List<ResourceAddInfoEntity> addInfoList = this.findBySql(sql.toString(),
				ResourceAddInfoEntity.OPERATION_TYPE_TRAFFIC_MIX_DATA_ADD,
				ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
				ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID,
				ResourceDeleteInfoEntity.ENABLED,
				ResourceDeleteDetailEntity.RESOURCE_TYPE_TRAFFIC_MIX_DATA_DELETE,
				ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR,
				ResourceDeleteDetailEntity.STATUS_PROCESS_FINISHED, ResourceDeleteDetailEntity.STATUS_INVALID);

		System.out.println("====SQL getAddInfo for TrafficMix : " + sql.toString());

		for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}

		return resultAddInfoList;
	}

	public List<ResourceAddInfoEntity> getAddCount(String startDate,Date endDate, Set<Integer> categoryList){

		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT t1.id, count(1) as detailCount ");
		sql.append(" from resource_add_info t1  ");
		sql.append(" INNER JOIN resource_add_detail t2 ON t1.id = t2.resource_info_id ");
		sql.append(" WHERE t1.create_date >= ?  and t1.create_date < ? ");
		sql.append(" and t1.operation_type in (");
		StringUtils.join(categoryList, ",");
		sql.append(" ) ");
		sql.append(" GROUP BY t1.id ");
		sql.append(" order by t1.create_date ");


		return findBySql(sql.toString(), startDate, endDate);

	}


	public Integer insert(int ownDomainId, int userId, int operationType) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("own_domain_id", ownDomainId);
		values.put("user_id", userId);
		values.put("operation_type", operationType);

		//Cee - https://www.wrike.com/open.htm?id=288841793
//		values.put("create_date", FormatUtils.getChicagoCurrentTime());
		values.put("create_date", new Date());
		values.put("status", ResourceAddInfoEntity.STATUS_NEWLY_CREATED);

		int newId = this.insert(values);

		logger.info(ownDomainId + ", " + userId + ", " + operationType);

		if (newId < 0) {
			return null;
		}
		return newId;
	}

	public List<ResourceAddInfoEntity> getAddInfoList(){
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from resource_add_info where operation_type = ? and (status = ? or status = ?) order by create_date");
		return findBySql(sql.toString(), ResourceAddInfoEntity.OPERATION_TYPE_ADD_PARENT_URL_CHILD_URL,ResourceAddInfoEntity.STATUS_NEWLY_CREATED, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR);
	}

	public Integer insertSchedule(int ownDomainId, int userId, int operationType) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("own_domain_id", ownDomainId);
		values.put("user_id", userId);
		values.put("operation_type", operationType);

		//Cee - https://www.wrike.com/open.htm?id=288841793
//		values.put("create_date", FormatUtils.getChicagoCurrentTime());
		values.put("create_date", new Date());
		values.put("status", ResourceAddInfoEntity.STATUS_SCHEDULED);

		int newId = this.insert(values);

		logger.info(ownDomainId + ", " + userId + ", " + operationType);

		if (newId < 0) {
			return null;
		}
		return newId;
	}

	public List<ResourceAddInfoEntity> getSyncParentRankCheckInfoList(Set<Integer> processType, Set<Integer> domainList, int diffDate) {
		List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();
		StringBuffer sqlCommon = new StringBuffer();

		sqlCommon.append(" 	SELECT rai.id as id, rai.operation_type as operationType,rai.own_domain_id,rai.user_id, ");
		sqlCommon.append("  	rai.status,rai.process_date,rai.end_date,rai.create_date,rai.status_sync	");
		sqlCommon.append("  FROM " + getTableName() + " rai ");
		sqlCommon.append("  WHERE 1=1 ");
		sqlCommon.append("  and rai.operation_type in (" + (StringUtils.join(processType, ',')) + ") ");
		sqlCommon.append("  and rai.own_domain_id in (" + (StringUtils.join(domainList, ',')) + ") ");
//		sqlCommon.append("  AND rai.own_domain_id = 7741 ");//todo test
		sqlCommon.append("  AND rai.status = ? ");
		sqlCommon.append("  AND (rai.status_sync = ? or rai.status_sync = ? ) ");
		sqlCommon.append("  AND datediff(now(), end_date) <= ? ");
		sqlCommon.append("  order by rai.id ");

		System.out.println("===SQL getSyncParentRankCheckInfoList:"+ sqlCommon.toString());

		List<ResourceAddInfoEntity> addInfoList = this.findBySql(sqlCommon.toString(),
				ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR,
				ResourceAddInfoEntity.STATUS_SYNC_PROCESS_FINISHED_WITHOUT_ERROR,
				ResourceAddInfoEntity.STATUS_SYNC_PROCESSING_PARENT_CHILD_WITH_ERROR,
				diffDate);
		for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}

		return resultAddInfoList;
	}

	public boolean updateSyncStatusBeforeProcess(int id, int status, int currentStatus) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update resource_add_info set status_sync=? ");
		sql.append(" where id=? and status=? limit 1 ");
		return executeUpdate(sql.toString(), status, id, currentStatus) > 0;
	}

	public boolean updateSyncStatusForParentChild(int id, int syncStatus, int currentStatus) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update resource_add_info set status_sync=? ");
		sql.append(" where id=? and status_sync=? limit 1 ");
		return executeUpdate(sql.toString(), syncStatus, id, currentStatus) > 0;
	}

	public void updateSyncStatusAfterProcess(int id, int status) {
		String sql = "update resource_add_info set status_sync=? where id=? ";
		executeUpdate(sql, status, id);
	}

	public List<ResourceAddInfoEntity> getDiffKSResourceList(Set<Integer> processType, List<Integer> domainList, int diffDate) {
		List<ResourceAddInfoEntity> resultAddInfoList = new ArrayList<ResourceAddInfoEntity>();
		StringBuffer sqlCommon = new StringBuffer();

		sqlCommon.append(" 	SELECT rai.id as id, rai.operation_type as operationType,rai.own_domain_id,rai.user_id, ");
		sqlCommon.append("  	rai.status,rai.process_date,rai.end_date,rai.create_date	");
		sqlCommon.append("  FROM " + getTableName() + " rai ");
		sqlCommon.append("  WHERE ");
		sqlCommon.append("  rai.operation_type in (" + (StringUtils.join(processType, ',')) + ") ");
		sqlCommon.append("  and rai.own_domain_id in (" + (StringUtils.join(domainList, ',')) + ") ");
//		sqlCommon.append("  AND rai.own_domain_id = 7741 ");//todo test
		sqlCommon.append("  AND rai.status = ? ");
		sqlCommon.append("  AND (rai.status_sync IS NULL OR rai.status_sync = ? OR rai.status_sync = ?) ");
		sqlCommon.append("  AND datediff(now(), end_date) <= ? ");
		sqlCommon.append("  order by rai.id ");

		System.out.println("===SQL getDiffKSResourceList:"+ sqlCommon.toString());

		List<ResourceAddInfoEntity> addInfoList = this.findBySql(sqlCommon.toString(),
				ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR,
				ResourceAddInfoEntity.STATUS_SYNC_NEWLY_CREATED,ResourceAddInfoEntity.STATUS_SYNC_PROCESS_FINISHED_WITH_ERROR,
				diffDate);
		for (ResourceAddInfoEntity entity : addInfoList) {
			if (!resultAddInfoList.contains(entity)) {
				resultAddInfoList.add(entity);
			}
		}

		return resultAddInfoList;
	}

	public boolean updateStatusById(int id, int status) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update resource_add_info set status=? ");
		sql.append(" where id=? limit 1 ");
		return executeUpdate(sql.toString(), status, id) > 0;
	}

	public void updateStatusAnServerInfoForPauseRanking(int id, int status, Date startDate, String serverInfo) {
		String sql = "update resource_add_info set status=?, server_info =?, end_date=?, process_date=? where id=? ";
		executeUpdate(sql, status, serverInfo, startDate, startDate, id);
	}

	public int updateInfoListByDomainIdAndStatus(int ownDomainId, Date date, int status) {
		String sql = "update resource_add_info set status=?, end_date=?, process_date=? where own_domain_id=? and status !=?";
		return executeUpdate(sql, status, date, date, ownDomainId, status);
	}

}