package seoclarity.backend.dao.actonia.liveresearch;


import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.liveresearch.KeywordLiveResearchProjectEntity;

import java.util.List;
import java.util.Set;

@Repository
public class KeywordLiveResearchProjectEntityDAO extends ActoniaBaseJdbcSupport<KeywordLiveResearchProjectEntity> {

    @Override
    public String getTableName() {
        return "keyword_live_research_project";
    }
    
    public List<KeywordLiveResearchProjectEntity> getNeedRetrieveSvProject() {
        String sql = "select * from " + getTableName();
        sql += " where status = ? and svRetrieveFlg = ? and svRetrieveStatus != ? ";
        return findBySql(sql, KeywordLiveResearchProjectEntity.STATUS_UPLOAD_COMPLETED,
                KeywordLiveResearchProjectEntity.ENABLED, KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR);
    }
    
    public List<KeywordLiveResearchProjectEntity> getNeedRetrieveAdwordsSuggestProject() {
        String sql = "select * from " + getTableName();
        sql += " where adwordSuggestStatus != ? ";
        return findBySql(sql, KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR);
    }
    
    public List<KeywordLiveResearchProjectEntity> getProjectById(Integer projectId) {
        String sql = "select * from " + getTableName();
        sql += " where id = ?  ";
        return findBySql(sql, projectId);
    }


    public List<KeywordLiveResearchProjectEntity> getAllCrawlCompletedDataList(){
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from " + getTableName());
        sbd.append(" where enabled = ? and status = ?");
        return findBySql(sbd.toString(), KeywordLiveResearchProjectEntity.ENABLED, KeywordLiveResearchProjectEntity.STATUS_CRAWL_COMPLETED);
    }

    public int updateStatusById(int id, int uploadStatus, int crawlStatus) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" update " + getTableName());
        sbd.append(" set status = ? where id = ? and status = ?");
        return executeUpdate(sbd.toString(), uploadStatus, id, crawlStatus);
    }

    public int updateSvStatusByIdList(Set<Integer> list, int svRetrieveStatus) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" update " + getTableName());
        sbd.append(" set svRetrieveStatus = ? where id in (" + StringUtils.join(list, ",") + ")");
        return executeUpdate(sbd.toString(), svRetrieveStatus);
    }

    public int updateSvStatusById(int id, int svRetrieveStatus) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" update " + getTableName());
        sbd.append(" set svRetrieveStatus = ? where id = ? ");
        return executeUpdate(sbd.toString(), svRetrieveStatus, id);
    }
    
    public int updateAdwordSuggestStatusById(int id, int svRetrieveStatus) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" update " + getTableName());
        sbd.append(" set adwordSuggestStatus = ? where id = ? ");
        return executeUpdate(sbd.toString(), svRetrieveStatus, id);
    }

}