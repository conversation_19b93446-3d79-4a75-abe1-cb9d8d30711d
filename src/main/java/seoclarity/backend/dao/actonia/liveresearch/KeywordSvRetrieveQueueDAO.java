package seoclarity.backend.dao.actonia.liveresearch;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankKeywordSVEntity;
import seoclarity.backend.entity.actonia.liveresearch.KeywordSvRetrieveQueueEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class KeywordSvRetrieveQueueDAO extends ActoniaBaseJdbcSupport<KeywordSvRetrieveQueueEntity> {

    @Override
    public String getTableName() {
        return "keyword_sv_retrieve_queue";
    }

    public void batchInsert(List<KeywordSvRetrieveQueueEntity> keywordSvRetrieveQueueList) {
        String sql = "insert into " + getTableName()
                + "(keywordType,ownDomainId,projectId, searchEngineId, languageId, keywordName, clarityDBKeywordHash,sourceKeyword,svRetrieveStatus )" +
                "values (?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordSvRetrieveQueueEntity entity : keywordSvRetrieveQueueList) {
            batch.add(new Object[]{
                    entity.getKeywordType(),
                    entity.getOwnDomainId(),
                    entity.getProjectId(),
                    entity.getSearchEngineId(),
                    entity.getLanguageId(),
                    entity.getKeywordName(),
                    entity.getClarityDBKeywordHash(),
                    entity.getSourceKeyword(),
                    entity.getSvRetrieveStatus(),
            });
        }
        this.executeBatch(sql, batch);
    }

    public List<KeywordSvRetrieveQueueEntity> getNeedRetrieveSvByStatus(int projectId, List<Integer> status) {
        String sql = "select keywordType,ownDomainId,projectId,searchEngineId,languageId,keywordName,clarityDBKeywordHash ";
        sql += " ,GROUP_CONCAT(sourceKeyword separator '!_!') as sourceKeywordGroup ";
        sql += " from " + getTableName();
        sql += " where keywordType = ? and projectId = ? and svRetrieveStatus in (" + StringUtils.join(status, ",") + ")";
        sql += " group by clarityDBKeywordHash";
        return findBySql(sql, KeywordSvRetrieveQueueEntity.KEYWORD_TYPE_LIVE_RESEARCH, projectId);
    }

    
    public List<KeywordSvRetrieveQueueEntity> getNeedRetrieveSvByStatusAndKeywordType(int projectId, List<Integer> status, Integer keywordType) {
    	String sql = "select * from " + getTableName();
    	sql += " where keywordType = ? and projectId = ? and svRetrieveStatus in (" + StringUtils.join(status, ",") + " )";
    	return findBySql(sql, keywordType, projectId);
    }
    
    public void updateSv(Integer svRetrieveStatus, Date svRetrieveTime, String svRetrieveBatchNo, String svRetrieveTaskId,
                         int projectId, int engineId, int languageId, List<String> keywordHashList){
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set svRetrieveStatus = ?, svRetrieveTime = ?, svRetrieveTaskId = ?, svRetrieveBatchNo = ? " +
                "where keywordType = ? and projectId = ? and searchEngineId=? and languageId=? ");
        sql.append(" and clarityDBKeywordHash in (" + StringUtils.join(keywordHashList, ",") + ") ");
        executeUpdate(sql.toString(), svRetrieveStatus, svRetrieveTime,svRetrieveTaskId, svRetrieveBatchNo,
                KeywordSvRetrieveQueueEntity.KEYWORD_TYPE_LIVE_RESEARCH, projectId, engineId, languageId);

    }
    

    public void updateSv(Integer svRetrieveStatus, Date svRetrieveTime, String svRetrieveBatchNo, String svRetrieveTaskId, Long id){
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set svRetrieveStatus = ?, svRetrieveTime = ?, svRetrieveTaskId = ?, svRetrieveBatchNo = ? " +
                "where id =  ? ");
        
        System.out.println(sql);
        executeUpdate(sql.toString(), svRetrieveStatus, svRetrieveTime,svRetrieveTaskId, svRetrieveBatchNo, id);

    }

    public List<KeywordSvRetrieveQueueEntity> getNotUpdateSVList(int projectId){

        String sql = " select keywordType,ownDomainId,projectId,searchEngineId,languageId,keywordName,clarityDBKeywordHash,svRetrieveTaskId ";
        sql += " ,GROUP_CONCAT(sourceKeyword separator '!_!') as sourceKeywordGroup ";
        sql += " from " + getTableName();
        sql += " where svRetrieveStatus = " + AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_PROCESSING;
        sql += " and (svRetrieveTaskId is not null or svRetrieveTaskId != '')";
        sql += " and projectId = " + projectId;
        sql += " and keywordType = " + KeywordSvRetrieveQueueEntity.KEYWORD_TYPE_LIVE_RESEARCH;
        sql += " group by clarityDBKeywordHash";
        sql += " order by id";

        return findBySql(sql);
    }
    
    public List<KeywordSvRetrieveQueueEntity> getByTaskIdAndProjectId(Integer projectId, String svRetrieveTaskId){

        String sql = " select * from " + getTableName();
        sql += " where svRetrieveTaskId = '" + svRetrieveTaskId + "'";
        sql += " and projectId = " + projectId;

        return findBySql(sql);
    }
    
    
    
    public List<KeywordSvRetrieveQueueEntity> getNotUpdateSVListByKeywordType(Integer projectId, Integer keywordType){

        String sql = " select * ";
        sql += " from " + getTableName();
        sql += " where svRetrieveStatus = " + AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_PROCESSING;
        sql += " and (svRetrieveTaskId is not null or svRetrieveTaskId != '')";
        sql += " and projectId = " + projectId;
        sql += " and keywordType = " + keywordType;

        System.out.println(sql);
        return findBySql(sql);
    }
    
    public void updateStatusByTaskIdAndProjectId(Integer projectid, String taskId, Integer status, Integer keywordType) {

        StringBuffer sql = new StringBuffer();
        sql.append(" update  " + getTableName() + " set svRetrieveStatus = ? where svRetrieveTaskId = ? and  projectId = ? and keywordType = ?  ");

        this.executeUpdate(sql.toString(), status, taskId, projectid, keywordType);
    }

    public void updateSvForBatch(List<KeywordSvRetrieveQueueEntity> keywordSvRetrieveQueueEntityList) {

        StringBuffer sql = new StringBuffer();
        sql.append(" update  " + getTableName() + " set svUpdateDate= ?,svRetrieveTime = ?, svRetrieveStatus = ?  " +
                " where keywordType = ? and projectId=? and searchEngineId=? and languageId = ? and clarityDBKeywordHash=?   ");

        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordSvRetrieveQueueEntity keywordSvRetrieveQueueEntity : keywordSvRetrieveQueueEntityList) {
            Object[] values = new Object[] {
                    keywordSvRetrieveQueueEntity.getSvUpdateDate(),
                    keywordSvRetrieveQueueEntity.getSvRetrieveTime(),
                    keywordSvRetrieveQueueEntity.getSvRetrieveStatus(),
                    keywordSvRetrieveQueueEntity.getKeywordType(),
                    keywordSvRetrieveQueueEntity.getProjectId(),
                    keywordSvRetrieveQueueEntity.getSearchEngineId(),
                    keywordSvRetrieveQueueEntity.getLanguageId(),
                    keywordSvRetrieveQueueEntity.getClarityDBKeywordHash()

            };
            batch.add(values);

//            System.out.println("====svRetrieveStatus:" + keywordSvRetrieveQueueEntity.getSvRetrieveStatus() +",searchEngineId : " + keywordSvRetrieveQueueEntity.getSearchEngineId() + ",languageId: "
//                    + keywordSvRetrieveQueueEntity.getLanguageId()
//             + ",clarityDBKeywordHash: " + keywordSvRetrieveQueueEntity.getClarityDBKeywordHash()
//                    + ",projectId:" + keywordSvRetrieveQueueEntity.getProjectId() + ",KeywordType:" + keywordSvRetrieveQueueEntity.getKeywordType());
        }
        System.out.println("===SQL updateSvForBatch : " + sql.toString() + ",:batch size:" + batch.size());

        this.executeBatch(sql.toString(), batch);
    }

    public Integer getUnRetrieveCount(int keywordType, int projectId){
        String sql = " select count(1) from " + getTableName();
        sql += " where keywordType = ? and projectId = ? and svRetrieveStatus != " + KeywordSvRetrieveQueueEntity.SV_RETRIEVE_STATUS_SUCCESSFULLY;
        return queryForInteger(sql, keywordType, projectId);
    }

}
