package seoclarity.backend.dao.actonia.liveresearch;


import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.liveresearch.KeywordLiveResearchDetailEntity;
import seoclarity.backend.entity.actonia.liveresearch.KeywordSvRetrieveQueueEntity;

import java.util.List;

@Repository
public class KeywordLiveResearchDetailEntityDAO extends ActoniaBaseJdbcSupport<KeywordLiveResearchDetailEntity> {

    @Override
    public String getTableName() {
        return "keyword_live_research_project_detail";
    }
    
    public Integer getKeywordCountForProject(int projectId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select count(1) from " + getTableName()).append(" where projectId = ?");
        return queryForInteger(sql.toString(), projectId);
    }
    
    public List<KeywordLiveResearchDetailEntity> getNotSentKeywords(int projectId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select id, projectId, searchEngineId, languageId, keywordName, clarityDBKeywordHash from ").append(getTableName());
		sql.append(" where projectId = ? and keywordStatus = ? order by id ");
		return findBySql(sql.toString(), projectId, KeywordLiveResearchDetailEntity.KEYWORD_STATUS_PENDING);
    }
    
    public boolean isProjectFinished(int projectId) {
    	StringBuffer sql = new StringBuffer();
    	sql.append(" select * from   ").append(getTableName());
    	sql.append(" where projectId = ?  and adwordSuggestStatus != ? ");
    	List<KeywordLiveResearchDetailEntity> list = findBySql(sql.toString(), projectId, KeywordLiveResearchDetailEntity.KEYWORD_STATUS_COMPLETED);
    	if (list != null && list.size() > 0) {
			return false;
		}
    	return true;
    }
    
    public List<KeywordLiveResearchDetailEntity> getNotSentKeywordsForAdwordsSuggest(int projectId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select id, projectId, searchEngineId, languageId, keywordName from ").append(getTableName());
		sql.append(" where projectId = ? and adwordSuggestStatus = ? order by id ");
		return findBySql(sql.toString(), projectId, KeywordLiveResearchDetailEntity.KEYWORD_STATUS_PENDING);
    }
    
    
    public void updateKeywordsStatus(KeywordLiveResearchDetailEntity liveResearchDetailEntity) {
    	StringBuffer sql = new StringBuffer();
    	sql.append("update ").append(getTableName()).append(" set isSentToRankSQS=? where id=?");
        executeUpdate(sql.toString(), KeywordLiveResearchDetailEntity.KEYWORD_STATUS_PROCESSING, liveResearchDetailEntity.getId());
    }
    
    public boolean updateKeywordsAdwordsStatus(List<KeywordSvRetrieveQueueEntity> keywordSvRetrieveQueueEntityList, Integer status){
		
    	StringBuffer sql = new StringBuffer();
    	sql.append("update ").append(getTableName()).append(" set adwordSuggestStatus=? where projectId=? and searchEngineId=? ");
    	sql.append(" and languageId=? and keywordName=? ");
		getJdbcTemplate().batchUpdate(sql.toString(), 
			new BatchPreparedStatementSetter() {
		
				@Override
				public int getBatchSize() {
					return keywordSvRetrieveQueueEntityList == null ? 0 : keywordSvRetrieveQueueEntityList.size();
				}
		
				@Override
				public void setValues(PreparedStatement ps, int i) throws SQLException {
					ps.setInt(1, status);
					ps.setInt(2, keywordSvRetrieveQueueEntityList.get(i).getProjectId());
					ps.setInt(3, keywordSvRetrieveQueueEntityList.get(i).getSearchEngineId());
					ps.setInt(4, keywordSvRetrieveQueueEntityList.get(i).getLanguageId());
					ps.setString(5, keywordSvRetrieveQueueEntityList.get(i).getKeywordName());
				}
				
			});
			
		return true;
	}
    
    
    public void updateKeywordsAdwordsStatusById(String keywordName, 
    		Integer projectId, Integer engineId, Integer languageId, Integer status){
		
    	StringBuffer sql = new StringBuffer();
    	sql.append("update ").append(getTableName()).append(" set adwordSuggestStatus=? where keywordName=? and projectId=? "
    			+ " and searchEngineId = ? and languageId = ?  ");
		this.executeUpdate(sql.toString(), status, keywordName, projectId, engineId, languageId);
	}
    
    public List<String> getKeywordListByProject(int projectId){
        String sql = " select keywordName from " + getTableName() + " where projectId = ?";
        return queryForStringList(sql, projectId);
    }

    public int getSuggestCount(int projectId, int searchEngineId, int languageId, String keywordName){
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select suggestCount from ").append(getTableName());
        sbd.append(" where projectId = ? and searchEngineId = ? and  languageId = ? and  keywordName = ? ");
        Integer cnt = queryForInteger(sbd.toString(), projectId, searchEngineId, languageId, keywordName);
        return cnt == null ? 0 : cnt.intValue();
    }

    public int updateSuggestCount(int projectId, int searchEngineId, int languageId, String keywordName, int cnt) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" update ").append(getTableName()).append(" set suggestCount = ?");
        sbd.append(" where projectId = ? and searchEngineId = ? and languageId = ? and keywordName = ? ");
        return executeUpdate(sbd.toString(), cnt, projectId, searchEngineId, languageId, keywordName);
    }

}