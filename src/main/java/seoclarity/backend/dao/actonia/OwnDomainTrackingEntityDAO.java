package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.OwnDomainTrackingEntity;

@Repository
public class OwnDomainTrackingEntityDAO extends ActoniaBaseJdbcSupport<OwnDomainTrackingEntity> {

	@Override
	public String getTableName() {
		return "t_own_domain_tracking";
	}

	public OwnDomainTrackingEntity getSettingByDomainId(int domainId) {
		String sql = "select * from t_own_domain_tracking where ownDomainId=?";
		return findObject(sql, domainId);
	}

	public void batchinsertIgnore(List<Integer> ownDomainIdList) {
		
		StringBuffer sql = new StringBuffer();
        sql.append(" insert ignore into " + getTableName() + " (ownDomainId) ");
        sql.append(" values(?)");

		List<Object[]> batch = new ArrayList<Object[]>();
		for (Integer ownDomainId : ownDomainIdList) {
			Object[] values = new Object[] {ownDomainId};
			batch.add(values);
		}
		this.executeBatch(sql.toString(), batch);
        
    }
	
	
	public List<OwnDomainTrackingEntity> getAllActiveList(){
		StringBuffer sql = new StringBuffer();
		sql.append("select odt.* from  " + getTableName() + " odt");
		sql.append(" join t_own_domain od on od.id = odt.ownDomainId  ");
		sql.append(" where od.status = ? ");
		
		return this.findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}
	
	public void updateGscLatestFinalDateByOid(Map<Integer, String> OidDateMap) {
		if (OidDateMap == null || OidDateMap.keySet().size() == 0) {
			return ;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("update  " + getTableName()).append(" set gscLatestFinalDate = ? where ownDomainId = ? ");
		List<Object[]> batch = new ArrayList<Object[]>();
		for (Integer ownDomainId : OidDateMap.keySet()) {

			if (StringUtils.isBlank(OidDateMap.get(ownDomainId))) {
				System.out.println(" OID:" + ownDomainId +", date is not correct! " + OidDateMap.get(ownDomainId));
				continue;
			}

			Object[] values = new Object[]{OidDateMap.get(ownDomainId) , ownDomainId};
			batch.add(values);
		}
		executeBatch(sql.toString(), batch);
	}
	
	public void updateGscLatestDateByOid(Map<Integer, String> OidDateMap) {
		if (OidDateMap == null || OidDateMap.keySet().size() == 0) {
			return ;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("update  " + getTableName()).append(" set gscLatestFreshDate = ? where ownDomainId = ? ");
		List<Object[]> batch = new ArrayList<Object[]>();
		for (Integer ownDomainId : OidDateMap.keySet()) {

			if (StringUtils.isBlank(OidDateMap.get(ownDomainId))) {
				System.out.println(" OID:" + ownDomainId +", date is not correct! " + OidDateMap.get(ownDomainId));
				continue;
			}

			Object[] values = new Object[]{OidDateMap.get(ownDomainId) , ownDomainId};
			batch.add(values);
		}
		executeBatch(sql.toString(), batch);
	}


	public void updateGscLatestAndFinalDateByOid(Map<Integer, String[]> OidDateMap) {
		if (OidDateMap == null || OidDateMap.keySet().size() == 0) {
			return ;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("update  " + getTableName()).append(" set gscLatestFreshDate = ?, gscLatestFinalDate = ? where ownDomainId = ? ");
		List<Object[]> batch = new ArrayList<Object[]>();
		for (Integer ownDomainId : OidDateMap.keySet()) {

			if (ArrayUtils.isEmpty(OidDateMap.get(ownDomainId)) || OidDateMap.get(ownDomainId).length != 2) {
				System.out.println(" OID:" + ownDomainId +", date is not correct! " + OidDateMap.get(ownDomainId));
				continue;
			}

			Object[] values = new Object[]{OidDateMap.get(ownDomainId)[0], OidDateMap.get(ownDomainId)[1] , ownDomainId};
			batch.add(values);
		}
		executeBatch(sql.toString(), batch);
	}

    public List<OwnDomainTrackingEntity> getSharedProfileLatestDate() {
		StringBuffer sql = new StringBuffer();
		sql.append("select refer_domain_id as ownDomainId, max(gscLatestFinalDate) as gscLatestFinalDate, max(gscLatestFreshDate) as gscLatestFreshDate from gsc_multi_domain_profile mul ");
		sql.append("left join t_own_domain_tracking odt ");
		sql.append("on mul.base_domain_id = odt.ownDomainId ");
		sql.append("group by refer_domain_id ");

		return findBySql(sql.toString());
    }
	
}
