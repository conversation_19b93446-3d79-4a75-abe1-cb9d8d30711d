package seoclarity.backend.dao.actonia;

import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.ApiBillingInstanceEntity;
import seoclarity.backend.entity.actonia.CloudWatchResultVO;

@Repository
public class ApiBillingInstanceEntityDAO extends ActoniaBaseJdbcSupport<ApiBillingInstanceEntity> {

	public String getTableName() {
		return "api_billing_instance";
	}

	
	public void insert(Map<String, Integer> infoMap, CloudWatchResultVO cloudWatchResultVO) {
		
		String sql = "insert into " + getTableName() + "("
				+ "billingInfoId, requestTime, requestDate, sourceIp, accessToken, "
				+ "responseCode, errMsg, resultCount, responseLength, responseInfo, "
				+ "ownDomainId, createUserId, httpMethod, uniqueRequestId "
				+ ") VALUES (?,?,?,?,?, ?,?,?,?,?, ?,?,?,?)";
		 this.executeUpdate(sql, infoMap.get(cloudWatchResultVO.getApi_id()),
	    		 cloudWatchResultVO.getRequestTimeStr(),
	    		 cloudWatchResultVO.getRequestDate(),
	    		 cloudWatchResultVO.getSource_ip(),
	    		 cloudWatchResultVO.getAccess_token(),
	    		 cloudWatchResultVO.getStatus(),
	    		 cloudWatchResultVO.getErr_msg(),
	    		 cloudWatchResultVO.getResult_cnt(),
	    		 cloudWatchResultVO.getResponseLength(),
	    		 cloudWatchResultVO.getResponseInfo(),
	    		 0,//ownDomainId
	    		 0,//createUserId
	    		 cloudWatchResultVO.getHttp_method(),
	    		 cloudWatchResultVO.getRequest_id());
	}


}
