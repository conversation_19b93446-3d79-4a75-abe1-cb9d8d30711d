package seoclarity.backend.dao.actonia;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.GWMDomainRel;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.sender.pagespeed.SendPageSpeedTargetUrlsSQS;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static seoclarity.backend.entity.actonia.OwnDomainSettingEntity.PAGE_SPEED_FREQUENCY_WEEKLY;
import static seoclarity.backend.entity.actonia.OwnDomainSettingEntity.PAGE_SPEED_SETTING_ENABLED_PAGE_TAG;

@Repository
public class OwnDomainEntityDAO extends ActoniaBaseJdbcSupport<OwnDomainEntity> {


	@Override
	public String getTableName() {
		return "t_own_domain";
	}

	public List<Integer> getDomainIdsByFunctionId(int functionId) {
		String sql = "select resourceId " +
				"from function_relation " +
				"where resourceType = 2 " +
				"and funcId = ? and value = 'true' " +
				"union select rel.ownDomainId " +
				"from owndomain_group_rel rel " +
				"join function_relation frel on frel.resourceType = 1 and frel.resourceId = rel.groupId " +
				"where frel.value = 'true'" +
				"and frel.funcId = ?";
		return queryForIntegerList(sql, functionId, functionId);
	}

	public List<OwnDomainEntity> queryForAllUs() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where search_engine_country ='US' and `status` = ?");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}

	public OwnDomainEntity getOwnDomainEntityById(int ownDomainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where id = ?");
		return findObject(sql.toString(), ownDomainId);
	}

	public List<OwnDomainEntity> getAllDomain() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select domain ,search_engine_country,id from t_own_domain where `status` = ? and search_engine_country != 'US' group by search_engine_country,domain");
		return findBySql(sql.toString(),OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> getByIdDesc() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? order by id desc ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> getByIdAsc() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? order by id ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}

	public OwnDomainEntity getOwnDomainEntityByOwnDomainId(int ownDomainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where id = ? and `status` = ? ");
		return findObject(sql.toString(), ownDomainId, OwnDomainEntity.STATE_ACTIVE);
	}

	public OwnDomainEntity getById(int ownDomainId) {
		return getOwnDomainEntityByOwnDomainId(ownDomainId);
	}

	public OwnDomainEntity getOwnDomainEntityByOwnDomainName(String domainName) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where domain =? and `status` = ? ");
		return findObject(sql.toString(), domainName, OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> getDomainListBasedCompanyName(String companyName) {
		StringBuffer sql = new StringBuffer();
		sql.append("select t.* from t_own_domain t left join t_own_domain_setting s on t.id = s.own_domain_id ");
		sql.append("where t.`status` =1 and s.company_name like ? order by t.id  ");

		return this.findBySql(sql.toString(), "%" + companyName + "%");
	}

	public List<OwnDomainEntity> getOwnDomainListByOwnDomainName(String domainName) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where domain =? and `status` = ? ");
		return findBySql(sql.toString(), domainName, OwnDomainEntity.STATE_ACTIVE);
	}

	public List<Map<String, Object>> getDomainCountryInfo(List<Integer> domainIdList) {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT ");
		sql.append(" tod.id, ");
		sql.append(" tod.domain, ");
		sql.append(" countryQueryName, ");
		sql.append(" countryDisplayName ");
		sql.append(" FROM t_own_domain_setting AS tods ");
		sql.append(" INNER JOIN t_own_domain AS tod ON tods.own_domain_id = tod.id ");
		sql.append(" INNER JOIN engine_country_language_mapping AS mapp ");
		sql.append(" ON (tod.search_engine_country = mapp.countryQueryName) ");
		sql.append(" AND (tod.search_engine = mapp.engineQueryName) ");
		sql.append(" AND (tod.language = mapp.languageQueryName) AND (tod.rank_from = mapp.rankFrom) ");
		sql.append(" WHERE (tod.status = 1) AND (tod.id in (" + StringUtils.join(domainIdList, ',') + ")) ");

		return this.queryForMapList(sql.toString());
	}

	public List<Map> getDistinctCountryAndLanguageByCompanyNameList(List<String> companyNameList, boolean isMobile) {
		StringBuffer sql = new StringBuffer();
		sql.append("select t.search_engine_country, t.language, t.search_engine from t_own_domain t left join t_own_domain_setting s on t.id = s.own_domain_id ");
		sql.append("where t.`status` = 1 and (s.company_name like '%");
		sql.append(StringUtils.join(companyNameList, "%' or s.company_name like '%"));
		sql.append("%') " );
		//https://www.wrike.com/open.htm?id=*********
		sql.append(" and not (pause_ranking_flg=1 and pause_ranking_status=2) ");
//		if(isMobile) {
//			sql.append("and (t.mobile_domain_flg = 1 or s.enable_moblie = 1) ");
//		} else {
//			sql.append("and ((t.mobile_domain_flg = 0 or t.mobile_domain_flg is null) or (s.enable_moblie = 0 or s.enable_moblie is null)) ");
//		}
		sql.append(" group by t.search_engine_country, t.language, t.search_engine ");

		return this.queryForMapList(sql.toString());
	}

	public List<Integer> getDistinctDomainIdByCompanyNameList(List<String> companyNameList, boolean isMobile, String country, String language, String engine) {
		StringBuffer sql = new StringBuffer();
		sql.append("select t.id from t_own_domain t left join t_own_domain_setting s on t.id = s.own_domain_id ");
		sql.append("where t.`status` = 1 and (s.company_name like '%");
		sql.append(StringUtils.join(companyNameList, "%' or s.company_name like '%"));
		sql.append("%') " );
		//https://www.wrike.com/open.htm?id=*********
		sql.append(" and not (pause_ranking_flg=1 and pause_ranking_status=2) ");
//		if(isMobile) {
//			sql.append("and (t.mobile_domain_flg = 1 or s.enable_moblie = 1) ");
//		} else {
//			sql.append("and ((t.mobile_domain_flg = 0 or t.mobile_domain_flg is null) or (s.enable_moblie = 0 or s.enable_moblie is null)) ");
//		}
		sql.append(" and t.search_engine_country = ? ");
		sql.append(" and t.language = ? ");
		sql.append(" and t.search_engine = ? ");

		return this.queryForIntegerList(sql.toString(), country, language, engine);
	}

	public OwnDomainEntity getDomainListBasedCompanyNameAndDomainId(String companyName, Integer ownDomainId) {
		StringBuffer sql = new StringBuffer();
		sql.append("select t.* from t_own_domain t left join t_own_domain_setting s on t.id = s.own_domain_id ");
		sql.append("where t.id = ? and t.`status` =1 and s.company_name like ? order by t.id limit 1 ");

		return this.findObject(sql.toString(), ownDomainId, "%" + companyName + "%");
	}


	public List<OwnDomainEntity> queryForAll() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}

	public Map<Integer, OwnDomainEntity> getOwnDomainEntityMap() {
		List<OwnDomainEntity> result = queryForAll();

		Map<Integer, OwnDomainEntity> resultMap = new HashMap<Integer, OwnDomainEntity>();
		if (result != null) {
			for (OwnDomainEntity entity : result) {
				resultMap.put(entity.getId(), entity);
			}
		}

		return resultMap;
	}

	public List<OwnDomainEntity> getWeeklyPageSpeedDomainList(List<Integer> execDomainList, int device){
		String sql = " SELECT distinct t1.id,pagespeed_client_apikey from " + getTableName() + " t1";
		sql += " INNER JOIN t_own_domain_setting t2 on t1.id = t2.own_domain_id ";
		sql += " where t1.`status` = ? and pagespeed_frequency = ? and pagespeed_client_apikey is not null and pagespeed_setting = ? ";
		if(CollectionUtils.isNotEmpty(execDomainList)){
			sql += " and t1.id in (" + StringUtils.join(execDomainList, ",") + ") ";
		}
		if(device == SendPageSpeedTargetUrlsSQS.DEVICE_MOBILE){
			sql += " and t2.pagespeed_device in (" + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_MOBILE + "," + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_ALL + ")";
		}else if(device == SendPageSpeedTargetUrlsSQS.DEVICE_DESKTOP){
			sql += " and t2.pagespeed_device in (" + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_DESKTOP + "," + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_ALL + ")";
		}
		System.out.println("===getWeeklyPageSpeedDomainList:" + sql);
		return findBySql(sql, OwnDomainEntity.STATE_ACTIVE, PAGE_SPEED_FREQUENCY_WEEKLY, PAGE_SPEED_SETTING_ENABLED_PAGE_TAG);
	}

	public List<OwnDomainEntity> getGa4Domain() {
		String sql = " select id from t_own_domain where google_analytics_version  = ? and status = ? ";
		return findBySql(sql.toString(), OwnDomainEntity.GOOGLE_ANALYTICS_VER4, OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> getGSCProcessingDomain(List<Integer> sharedProfileList, Integer dataSource) {
		StringBuffer sql = new StringBuffer();

		String sharedProfileStr = getIntegerListQueryParam(sharedProfileList);

		sql.append(" SELECT  t1.id, t3.gwm_domain_name   ");
		sql.append(" from  t_own_domain t1  ");
		sql.append(" JOIN t_own_domain_setting t2 ON t1.id = t2.own_domain_id ");
		if (StringUtils.isNotBlank(StringUtils.trim(sharedProfileStr))) {
			sql.append(" JOIN (select * from  gwm_domain_rel  where id not in (" + sharedProfileStr + ")) t3 ON t1.id = t3.own_domain_id ");
		} else {
			sql.append(" JOIN gwm_domain_rel t3 ON t1.id = t3.own_domain_id ");

		}
		sql.append(" WHERE t1.status = ? and t2.gwt_account IS NOT NULL AND t2.gwt_account != '' AND t3.data_source  = ? ");
		sql.append(" GROUP BY t1.id");
//	        sql.append(" limit 5 ");

		logger.info("=== getGSCProcessingDomain: " + sql.toString());

		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE, dataSource);
	}

	public List<OwnDomainEntity> getDomainByIdList(List<Integer> domainIdList) {
		String sql = "select * from t_own_domain where id in (" + StringUtils.join(domainIdList, ",") + ") and status = ?";
		System.out.println("===== getDomainByIdList: " + sql);
		return findBySql(sql, OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> getHasRecurringSiteAudit(Set<Integer> domainIds,String month) {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT COUNT(1) as cnt , own_domain_id as id ");
		sql.append(" from crawl_request_log ");
		sql.append(" where own_domain_id  in (0");
		for (Integer domainId : domainIds){
			sql.append(",");
			sql.append(domainId);
		}
		sql.append("  ) ");
		if (StringUtils.isNotBlank(month)){
			sql.append(" AND crawl_request_date > ").append(Integer.parseInt(month));
		}
		sql.append(" group by own_domain_id ");
		return findBySql(sql.toString());
	}

	public List<OwnDomainEntity> getTrafficByIdList(Set<Integer> domainIds) {
		//select traffic_type,count(*) from t_own_domain tod join t_own_domain_setting tods on tods.own_domain_id=tod.id where tod.status=1 group by traffic_type;
		StringBuffer sql = new StringBuffer();
		sql.append(" select tods.traffic_type as trafficType, tod.google_analytics_version as googleAnalyticsVersion,tod.id ");
		sql.append(" from t_own_domain tod join t_own_domain_setting tods on tods.own_domain_id=tod.id ");
		sql.append(" where tod.status=1 ");
		sql.append(" and tod.id  in (0");
		for (Integer domainId : domainIds){
			sql.append(",");
			sql.append(domainId);
		}
		sql.append("  ) ");
		return findBySql(sql.toString());
	}
}
