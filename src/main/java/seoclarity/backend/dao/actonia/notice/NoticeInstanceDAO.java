package seoclarity.backend.dao.actonia.notice;


import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.notice.NoticeInstance;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class NoticeInstanceDAO extends ActoniaBaseJdbcSupport<NoticeInstance> {

    private static final String TABLE_NAME = "notice_instance";
    private static final String COL_ID = "id";
    private static final String COL_OWN_DOMAIN_ID = "ownDomainId";
    private static final String COL_CATEGORY = "category";
    private static final String COL_DATA_TYPE = "dataType";
    private static final String COL_SUB_ID = "subId";
    private static final String COL_TRACK_DATE = "trackDate";
    private static final String COL_STATUS = "status";
    private static final String COL_MESSAGE_ID = "messageId";
    private static final String COL_AUTHOR_ID = "authorId";
    private static final String COL_MISSING_REASON = "missingReason";
    private static final String COL_UPDATE_DATE = "updateDate";
    private static final String COL_CREATE_DATE = "createDate";

    public void save(NoticeInstance noticeInstance) {
        String sql = "INSERT INTO " + TABLE_NAME + " (" + COL_OWN_DOMAIN_ID + ", " + COL_CATEGORY + ", " + COL_DATA_TYPE + ", "
                + COL_SUB_ID + ", " + COL_TRACK_DATE + ", " + COL_STATUS + ", " + COL_MESSAGE_ID + ", " + COL_AUTHOR_ID + ", "
                + COL_UPDATE_DATE + ", " + COL_CREATE_DATE + ") "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        super.executeUpdate(sql, noticeInstance.getOwnDomainId(), noticeInstance.getCategory(), noticeInstance.getDataType(),
                noticeInstance.getSubId(), noticeInstance.getTrackDate(), noticeInstance.getStatus(), noticeInstance.getMessageId(),
                noticeInstance.getAuthorId(), noticeInstance.getUpdateDate(), noticeInstance.getCreateDate());
    }

    public NoticeInstance findById(Long id) {
        String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + COL_ID + " = ?";
        return super.findObject(sql, id);
    }


    public void update(NoticeInstance noticeInstance) {
        String sql = "UPDATE " + TABLE_NAME + " SET " + COL_OWN_DOMAIN_ID + " = ?, " + COL_CATEGORY + " = ?, " + COL_DATA_TYPE + " = ?, "
                + COL_SUB_ID + " = ?, " + COL_TRACK_DATE + " = ?, " + COL_STATUS + " = ?, " + COL_MESSAGE_ID + " = ?, "
                + COL_AUTHOR_ID + " = ?, " + COL_UPDATE_DATE + " = ?, " + COL_CREATE_DATE + " = ? WHERE " + COL_ID + " = ?";

        super.executeUpdate(sql, noticeInstance.getOwnDomainId(), noticeInstance.getCategory(), noticeInstance.getDataType(),
                noticeInstance.getSubId(), noticeInstance.getTrackDate(), noticeInstance.getStatus(), noticeInstance.getMessageId(),
                noticeInstance.getAuthorId(), noticeInstance.getUpdateDate(), noticeInstance.getCreateDate(), noticeInstance.getId());
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public List<NoticeInstance> queryNoticeInstanceByBetweenTrackDateAndInDomainIds(String oldestLogDate, String newestLogDate, String domainIds, String category) {
//        String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + COL_STATUS + " > 2 AND " + COL_TRACK_DATE + ">= ? AND " + COL_TRACK_DATE + "<= ? AND " + COL_OWN_DOMAIN_ID + " IN (" + domainIds + ")";
        String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + COL_TRACK_DATE + ">= ? AND " + COL_TRACK_DATE + "<= ? AND " + COL_OWN_DOMAIN_ID + " IN (" + domainIds + ")" + " AND " + COL_CATEGORY + " = ?";
        System.out.println("=====noticeInstanceExistSql: " + sql);
        return super.findBySql(sql, oldestLogDate, newestLogDate, category);
    }

    public List<NoticeInstance> queryNoticeInstanceByBetweenTrackDateAndInProfileIds(String oldestLogDate, String newestLogDate, String profileIds, String category) {
//        String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + COL_STATUS + " > 2 AND " + COL_TRACK_DATE + ">= ? AND " + COL_TRACK_DATE + "<= ? AND " + COL_OWN_DOMAIN_ID + " IN (" + domainIds + ")";
        String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + COL_TRACK_DATE + ">= ? AND " + COL_TRACK_DATE + "<= ? AND " + COL_SUB_ID + " IN (" + profileIds + ")" + " AND " + COL_CATEGORY + " = ? ";
        System.out.println("=====noticeInstanceExistSql: " + sql);
        return super.findBySql(sql, oldestLogDate, newestLogDate, category);
    }

    public void batchInsert(List<NoticeInstance> missingDateNoticeInstances) {
        String sql = "INSERT INTO " + TABLE_NAME + " (" + COL_OWN_DOMAIN_ID + ", " + COL_CATEGORY + ", " + COL_DATA_TYPE + ", "
                + COL_SUB_ID + ", " + COL_TRACK_DATE + ", " + COL_STATUS + ", " + COL_MESSAGE_ID + ", " + COL_AUTHOR_ID + ", "
                + COL_CREATE_DATE + ", " + COL_MISSING_REASON + ") "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        final List<Object[]> batchData = missingDateNoticeInstances.stream().map(noticeInstance -> new Object[]{noticeInstance.getOwnDomainId(), noticeInstance.getCategory(), noticeInstance.getDataType(), noticeInstance.getSubId(),
                noticeInstance.getTrackDate(), noticeInstance.getStatus(), noticeInstance.getMessageId(), noticeInstance.getAuthorId(),
                noticeInstance.getCreateDate(), noticeInstance.getMissingReason()}).collect(Collectors.toList());
        super.executeBatch(sql, batchData);
    }

    public List<NoticeInstance> queryNoticeInstanceByDomainIdAndTrackDate(Integer domainId, Integer oldestLogDate, String category) {
        String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + COL_OWN_DOMAIN_ID + " = ? AND " + COL_TRACK_DATE + " >= ? AND " + COL_CATEGORY + " = ?";
        return super.findBySql(sql, domainId, oldestLogDate, category);
    }

    public List<NoticeInstance> queryNoticeInstanceByProfileIdAndTrackDate(Integer profileId, Integer oldestLogDate, String category, Integer type) {
        String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + COL_SUB_ID + " = ? AND " + COL_TRACK_DATE + " >= ? AND " + COL_CATEGORY + " = ? AND " + COL_DATA_TYPE + " = ?";
        return super.findBySql(sql, profileId, oldestLogDate, category, type);
    }

    public void batchUpdate(List<NoticeInstance> needToUpdateClosedStatus) {
        // batch update status and updateDate
        String sql = "UPDATE " + TABLE_NAME + " SET " + COL_STATUS + " = ?, " + COL_UPDATE_DATE + " = ? WHERE " + COL_ID + " = ?";
        final List<Object[]> batchData = needToUpdateClosedStatus.stream().map(noticeInstance -> new Object[]{noticeInstance.getStatus(), noticeInstance.getUpdateDate(), noticeInstance.getId()}).collect(Collectors.toList());
        super.executeBatch(sql, batchData);
    }

    public int queryTodayHaveData(String currentDate) {
        String sql = "SELECT COUNT(" + COL_ID + ") FROM " + TABLE_NAME + " WHERE " + COL_CREATE_DATE + " >= ?";
        return super.queryForInt(sql, currentDate);
    }

    public long queryTodayMinId(String currentDate) {
        String sql = "SELECT MIN(" + COL_ID + ") FROM " + TABLE_NAME + " WHERE " + COL_CREATE_DATE + " >= ?";;
        return super.queryForLong(sql, currentDate);
    }

    public long queryMaxId() {
        String sql = "SELECT MAX(" + COL_ID + ") FROM " + TABLE_NAME;
        return super.queryForLong(sql);
    }

    public int queryNewAddCountByCategory(long id, String category, int status) {
        String sql = "SELECT COUNT(" + COL_ID + ") as num FROM " + TABLE_NAME + " WHERE " + COL_ID + " > ? AND " + COL_CATEGORY + " = ?" + " AND " + COL_STATUS + " = ?";
        return super.queryForInt(sql, id, category, status);
    }

    public List<NoticeInstance> queryNeedSentEmailNoticeInstance(long maxId, int status, String category) {
        String sql = "select * from " + TABLE_NAME + " where "
                + COL_OWN_DOMAIN_ID + " in (select distinct " + COL_OWN_DOMAIN_ID + " from " + TABLE_NAME + " where " + COL_ID + " > ? and " + COL_STATUS + " = ? and " + COL_CATEGORY + " = ?) and "
                + COL_STATUS + " = ? and " + COL_CATEGORY + " = ?";
        return super.findBySql(sql, maxId, status, category, status, category);
    }

    public void batchUpdateStatusById(String idStr, int status) {
        // batch update status and updateDate
        String sql = "UPDATE " + TABLE_NAME + " SET " + COL_STATUS + " = ?, " + COL_UPDATE_DATE + " = ?  WHERE " + COL_ID + " in (" + idStr + ")";
        this.executeUpdate(sql, status, new Date());
    }
    
    public NoticeInstance findLastInstanceByCategoryAndOid(int status, String category, Integer ownDomainId) {
        String sql = "select * from " + TABLE_NAME + " where ownDomainId = ? and category = ? and status = ? order by id desc limit 1";
        return this.findObject(sql, ownDomainId, category, status);
    }

    public List<NoticeInstance> queryNoticeByStatusAndCategory(Integer status, String category, int logDateStart, int logDateEnd, List<Integer> domainList, boolean isOnlyToday) {
        String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + COL_STATUS + " = ? AND " + COL_CATEGORY + " = ? AND " + COL_TRACK_DATE + " >= ? AND " + COL_TRACK_DATE + " <= ? AND " + COL_OWN_DOMAIN_ID + " in (" + StringUtils.join(domainList, ",") + ")";
        if (isOnlyToday) {
            sql += " AND DATE(" + COL_CREATE_DATE + ") = CURDATE()";
        }
        sql += " and dataType = 1 "; // https://www.wrike.com/open.htm?id=1574948306
        return super.findBySql(sql, status, category, logDateStart, logDateEnd);
    }
}

