package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.VedMasterEngineFileInfoEntity;
import seoclarity.backend.entity.actonia.Vs3VedTypeInfoEntity;

import java.util.ArrayList;
import java.util.List;

@Repository("vedMasterEngineFileInfoDAO")
public class VedMasterEngineFileInfoDAO extends ActoniaBaseJdbcSupport<VedMasterEngineFileInfoEntity> {


    @Override
    public String getTableName() {
        return "ved_master_engine_file_info ";
    }

    public String updateRows(VedMasterEngineFileInfoEntity entity) {

        StringBuilder sql = new StringBuilder();
        sql.append(" update ").append(getTableName());
        sql.append(" set ");
        sql.append(" updateDate = ? ");
        if (StringUtils.isNotBlank(entity.getFeature())) {
            sql.append(" ,feature =  '").append(entity.getFeature()).append("' ");
        }
        if (StringUtils.isNotBlank(entity.getSubFeature())) {
            sql.append(" ,sub_feature =  '").append(entity.getSubFeature()).append("' ");
        }
        if (StringUtils.isNotBlank(entity.getDomain())) {
            sql.append(" ,domain = '").append(entity.getDomain()).append("' ");
        }
        if (StringUtils.isNotBlank(entity.getUrl())) {
            sql.append(" ,url = '").append(entity.getUrl()).append("' ");
        }

        sql.append(" ,device = ? ");

        if (StringUtils.isNotBlank(entity.getRankType())) {
            sql.append(" ,rankType = '").append(entity.getRankType()).append("' ");
        }
        if (StringUtils.isNotBlank(entity.getParseLogic())) {
            sql.append(" ,parseLogic = ").append(entity.getParseLogic());
        }
        if (StringUtils.isNotBlank(entity.getErrorHandle())) {
            sql.append(" ,errorHandle = ").append(entity.getErrorHandle());
        }
        if (StringUtils.isNotBlank(entity.getDescription())) {
            sql.append(" ,description = '").append(entity.getDescription()).append("' ");
        }

        sql.append(" where id = ? ");
        System.out.println("===###updateRows  : " + sql.toString() + " , " + entity.getUpdateDate() + ", Device : " + entity.getDevice() + " , : id :" + entity.getId());
        return this.executeUpdate(sql.toString(), entity.getUpdateDate(), entity.getDevice(), entity.getId()) > 0 ? "true" : "false";
    }

    public List<VedMasterEngineFileInfoEntity> getInfo(String vedTypList, String device) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from " + getTableName() + " where vedTypeList = ? and device = ? ");
        System.out.println("===###getInfo ===============");
        System.out.println(sql.toString());
        return this.findBySql(sql.toString(), vedTypList, device);

    }

    public String addNew(VedMasterEngineFileInfoEntity entity) {
        StringBuilder sql = new StringBuilder();
        sql.append(" insert into ").append(getTableName());
        sql.append("(feature,sub_feature,domain,url,rankType,parseLogic,errorHandle,description,createDate,vedTypeList,vedTypeMurmurHash,device )");
        sql.append(" values(?,?,?,?,?,?,?,?,?,?,?,?) ");
        return executeUpdate(sql.toString(), entity.getFeature(), entity.getSubFeature(), entity.getDomain(), entity.getUrl(), entity.getRankType(), entity.getParseLogic(), entity.getErrorHandle(), entity.getDescription(), entity.getCreateDate(), entity.getVedTypeList(), entity.getVedTypeMurmurHash(), entity.getDevice()) > 0 ? "true" : "false";
    }

    public List<VedMasterEngineFileInfoEntity> getAll2Device() {
        String sql = "select * from " + getTableName() + " where device = 'dm'";
        return this.findBySql(sql.toString());
    }

    public VedMasterEngineFileInfoEntity getByTypeAndDevice(String type, String device) {
        String sql = "select * from " + getTableName() + " where   vedTypeList = ? and device = ?";
        return this.findObject(sql, type, device);
    }

    public void delete(int id) {
        String sql = "delete from " + getTableName() + " where id = ?";
        executeUpdate(sql, id);
    }

    public List<VedMasterEngineFileInfoEntity> getList() {
        String sql = "select vedTypeList , device ,rankType  from ved_master_engine_file_info";
        return this.findBySql(sql.toString());

    }

    public void insertBatch(List<VedMasterEngineFileInfoEntity> vedTypeInfoEntityList) {

        String sql = "INSERT INTO " + getTableName();
        sql += " (feature,sub_feature,domain,url,rankType,parseLogic,errorHandle,description,createDate,vedTypeList,vedTypeMurmurHash,device )" +
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";

        List<Object[]> batch = new ArrayList<>();
        for (VedMasterEngineFileInfoEntity entity : vedTypeInfoEntityList) {
            Object[] values = new Object[]{
                    entity.getFeature(),
                    entity.getSubFeature(),
                    entity.getDomain(),
                    entity.getUrl(),
                    entity.getRankType(),
                    entity.getParseLogic(),
                    entity.getErrorHandle(),
                    entity.getDescription(),
                    entity.getCreateDate(),
                    entity.getVedTypeList(),
                    entity.getVedTypeMurmurHash(),
                    entity.getDevice()
            };

            batch.add(values);
        }
        System.out.println("===insert batch size:" + batch.size());
        executeBatch(sql, batch);
    }

    public List<VedMasterEngineFileInfoEntity> getByVedType(String vedTypList) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from " + getTableName() + " where vedTypeList = ? ");
        return this.findBySql(sql.toString(), vedTypList);

    }

}
