package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.FileUploadConfigEntity;

/**
 * <AUTHOR>
 * @date 2024/12/16 11:18
 */
@Repository
public class FileUploadConfigDAO extends ActoniaBaseJdbcSupport<FileUploadConfigEntity> {

    @Override
    public String getTableName() {
        return "file_upload_config";
    }

    public FileUploadConfigEntity getByOwnDomainIdAndType(Integer ownDomainId, int type) {
        String sql = "select id, enabled, full_qulified_class from file_upload_config where own_domain_id = ? and type = ?;";
        return findObject(sql, ownDomainId, type);
    }

    public void updateEnableById(int id, int enabled) {
    	String sql = "update file_upload_config set enabled = ? where id = ?;";
    	executeUpdate(sql, enabled, id);
    }

}
