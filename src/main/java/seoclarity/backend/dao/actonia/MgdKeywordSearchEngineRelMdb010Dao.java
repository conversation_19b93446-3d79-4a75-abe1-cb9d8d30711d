package seoclarity.backend.dao.actonia;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.MgdKeywordSearchEngineRelEntity;
import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;
import seoclarity.backend.utils.FormatUtils;

/**
 * <AUTHOR>
 * @date 2021-05-24
 * @path seoclarity.backend.dao.actonia.MgdKeywordSearchEngineRelMdb010Dao
 * 
 */
@Repository
public class MgdKeywordSearchEngineRelMdb010Dao extends ActoniaMdbMonitorDailyBaseJdbcSupport<MgdKeywordSearchEngineRelEntity> {

	@Override
    public String getTableName() {
        return "mgd_keyword_search_engine_rel";
    }
	

	public List<Map<String, Object>> checkByDomainEngineLanguageDeviceOwnDomainId(Integer ownDomainId, String date, Integer engineId, 
			Integer languageId, boolean isMobile, Integer rankType, Integer frequence) {
		
		String yesterday = FormatUtils.formatDate(DateUtils.addDays(FormatUtils.toDate(date, "yyyyMMdd"), -2), "yyyyMMdd");
		
		String monitorTableName = "daily_ranking_imp_monitor_col_" + date;
		String deviceStr = isMobile ? "m" : "d";
		Integer deviceInt = isMobile ? 2 : 1;
		
		StringBuilder stringBuilder = new StringBuilder();
		
		/**
		 * select * from actonia.mgd_keyword_search_engine_rel
where ownDomainId = 4748  and engineId = 3 and languageId = 3 and tagId = 0 and device = 'm'
 and cityId = 0 and frequence = 1 and keywordRankcheckId not in (select distinct keyword_rankcheck_id from daily_ranking_imp_monitor_col_20201006
 where engine = 3 and language = 3 and location_id = 0 and frequence = 1 and type = 2 and own_domain_id = 4748  )
		 */
		
		stringBuilder.append("  select keywordRankcheckId, cityId from actonia.cdb_keyword_search_engine_rel "); 
		stringBuilder.append("   where ownDomainId = " + ownDomainId + "  and searchEngineId = " + engineId + " and languageId = " + languageId + "  and device = '" + deviceStr + "' "); 
		stringBuilder.append("   and createDate <= ").append(yesterday);
		if (rankType == MgdKeywordSearchEngineRelEntity.GEO_LEVELL) {
			stringBuilder.append("  and cityId > 0    ");
			stringBuilder.append("  and keywordType = " + RcKeywordSeRelEntity.KEYWORD_TYPE_GEO + "    ");
		} else {
			stringBuilder.append("  and cityId = 0    ");
			stringBuilder.append("  and keywordType = " + RcKeywordSeRelEntity.KEYWORD_TYPE_NATIONAL + "    ");
		}
		
		if (frequence != null && frequence > 0) {
			stringBuilder.append("   and frequency = " + frequence); 
		}
		
		stringBuilder.append("   and keywordRankcheckId not in (select distinct keyword_rankcheck_id from " + monitorTableName + "  "); 
		stringBuilder.append("   where engine = " + engineId + " and language = " + languageId); 
		
		if (rankType == MgdKeywordSearchEngineRelEntity.GEO_LEVELL) {
			stringBuilder.append("  and location_id > 0    ");
		} else {
			stringBuilder.append("  and location_id = 0    ");
		}
		
		if (frequence != null && frequence > 0) {
			stringBuilder.append("   and frequence = " + frequence); 
		}
		stringBuilder.append(" and type = " + deviceInt + " and own_domain_id = " + ownDomainId + "  ) ");
		String sqlString = stringBuilder.toString();
//		System.out.println("sql:" + sqlString);
		
		return this.queryForMapList(sqlString);
	}
	
}
