package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.ApiBillingInfoEntity;

@Repository
public class ApiBillingInfoEntityDAO extends ActoniaBaseJdbcSupport<ApiBillingInfoEntity> {

	public String getTableName() {
		return "api_billing_info";
	}
	public ApiBillingInfoEntity getByApiId(String apiId, String resourcePath) {
		String sql = "select * from "+getTableName()+" where apiId = ? and resourcePath = ?";
		return findObject(sql, apiId, resourcePath);
	}
	
	public List<ApiBillingInfoEntity>  getApiTaskInfos() {
		String sql = "select * from " + getTableName();
		return findBySql(sql);
	}
	
	public void batchInsertByApiIdAndPath(Map<String, String> resultMap) {
		
		String sql = "insert ignore into " + getTableName() + "(apiId, apiName, apiDescription, resourcePath) VALUES (?,?,?,?)";
		
		List<Object[]> batch = new ArrayList<Object[]>();
		 for (String key : resultMap.keySet()) {
		     batch.add(new Object[]{
	    		 key,
	    		 key,
	    		 "",
	    		 resultMap.get(key)
		     });
		 }
		 this.executeBatch(sql, batch);
	}


}
