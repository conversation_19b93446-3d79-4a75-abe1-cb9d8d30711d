/**
 *
 */
package seoclarity.backend.dao.actonia;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.AutorunInfoEntity;

@Repository
public class AutoRunInfoEntityDAO extends ActoniaBaseJdbcSupport<AutorunInfoEntity> {

	@Override
	public String getTableName() {
		return "autorun_info";
	}
	
	
	public void updateStatusForSender(Integer category, Integer ownDomainId, Integer status, Date processEndDate) {
		String sql = "update " + getTableName() + " set status = ?, processEndDate = ? where  category = ? and ownDomainId = ?  limit 1 ";
		executeUpdate(sql, status, processEndDate, category, ownDomainId);
	}
	
	public int insert(AutorunInfoEntity autoRunInfoEntity) {

		Map<String, Object> values = new HashMap<String, Object>();
		values.put("category", autoRunInfoEntity.getCategory());
		values.put("ownDomainId", autoRunInfoEntity.getOwnDomainId());
		values.put("fromDate", autoRunInfoEntity.getFromDate());
		values.put("toDate", autoRunInfoEntity.getToDate());
		values.put("status", autoRunInfoEntity.getStatus());
		values.put("createUserId", autoRunInfoEntity.getCreateUserId());
		values.put("createDate", new Date());

		return this.insert(values);
	}

	public void updateStatus() {
		String sql = " update " + getTableName() + " set status = 2 where id = 11046 and category = 20 ";
		this.executeUpdate(sql);
	}

	public boolean checkTask(Integer domainId, Integer categary) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select count(*) ");
		sql.append(" from ");
		sql.append(getTableName());
		sql.append(" where ownDomainId = ").append(domainId);
		sql.append(" and category = ").append(categary);
		sql.append(" and status != 2 ");
		System.out.println(sql.toString());
		return this.queryForInteger(sql.toString()) > 0;
	}

	public int addTask(Integer domainId, Integer userid, Integer category) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("ownDomainId", domainId);
		values.put("status", 0);
		values.put("fromDate", 0);
		values.put("toDate", 0);
		values.put("createUserId", userid);
		values.put("createDate", new Date());
		values.put("category", category);
		return this.insert(values);
	}

}