package seoclarity.backend.dao.actonia;


import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.GroupRelationshipFilterEntity;

import java.util.List;

@Repository
public class GroupRelationshipFilterEntityDAO extends ActoniaBaseJdbcSupport<GroupRelationshipFilterEntity> {

	@Override
	public String getTableName() {
		return "`group_relationship_filter`";
	}

	public GroupRelationshipFilterEntity getByGroupIdDomainId(int groupId, int domainId, int type, int adobeAnalyticsFlg) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     group_id = ?");
		stringBuilder.append(" and own_domain_id = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and adobe_analytics_flg = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, groupId, domainId, type, adobeAnalyticsFlg);
	}

	public GroupRelationshipFilterEntity getByDomainIdStatus(int domainId, int status) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and status = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, domainId, status);
	}

	public GroupRelationshipFilterEntity getByDomainIdStatus(int domainId, int status, int type, int adobeAnalyticsFlg) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and status = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and adobe_analytics_flg = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, domainId, status, type, adobeAnalyticsFlg);
	}

	public GroupRelationshipFilterEntity getByGroupIdDomainIdStatus(int groupId, int domainId, int status, int type, int adobeAnalyticsFlg) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     group_id = ?");
		stringBuilder.append(" and own_domain_id = ?");
		stringBuilder.append(" and status = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and adobe_analytics_flg = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, groupId, domainId, status, type, adobeAnalyticsFlg);
	}

	public List<GroupRelationshipFilterEntity> getListByStatus(int status, int type, int adobeAnalyticsFlg) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     status = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and adobe_analytics_flg = ?");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, status, type, adobeAnalyticsFlg);
	}

	public List<GroupRelationshipFilterEntity> getListByGroupIdStatus(int groupId, int status, int type, int adobeAnalyticsFlg) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     group_id = ?");
		stringBuilder.append(" and status = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and adobe_analytics_flg = ?");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, groupId, status, type, adobeAnalyticsFlg);
	}

	public List<GroupRelationshipFilterEntity> getListStartEndGroupIdStatus(int startGroupId, int endGroupId, int status, int type, int adobeAnalyticsFlg) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     group_id >= ?");
		stringBuilder.append(" and group_id <= ?");
		stringBuilder.append(" and status = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and adobe_analytics_flg = ?");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, startGroupId, endGroupId, status, type, adobeAnalyticsFlg);
	}

}