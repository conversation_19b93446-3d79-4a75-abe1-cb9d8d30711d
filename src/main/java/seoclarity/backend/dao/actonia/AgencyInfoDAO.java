/**
 * 
 */
package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.AgencyInfoEntity;

/**
 * com.actonia.subserver.dao.AgencyInfoDAO.java
 * 
 * https://www.wrike.com/open.htm?id=59034165
 *
 * <AUTHOR>
 *
 * @version $Revision:$
 *          $Author:$
 */
@Repository
public class AgencyInfoDAO extends ActoniaBaseJdbcSupport<AgencyInfoEntity> {

	@Override
	public String getTableName() {
		return "agency_info";
	}
	
	public AgencyInfoEntity getByCompanyName(String companyName) {
		if (StringUtils.isBlank(companyName)) {
			companyName = AgencyInfoEntity.DEFAULT_COMPANY_NAME;
		}
		
		StringBuilder sql = new StringBuilder();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where company_name = ? ");
		
		return this.findObject(sql.toString(), companyName);
	}
	
	public AgencyInfoEntity getByWebsiteDomain(String websiteDomain) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where website_domain like ? ");
		
		return this.findObject(sql.toString(), "%" + websiteDomain + "%");
	}
	
	public AgencyInfoEntity getDefault() {
		return getByCompanyName(AgencyInfoEntity.DEFAULT_COMPANY_NAME);
	}

}
