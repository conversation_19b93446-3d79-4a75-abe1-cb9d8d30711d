package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.GoogleTrendIndexEntity;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-04-03 11:48
 **/
@Repository
public class GoogleTrendIndexDAO extends ActoniaBaseJdbcSupport<GoogleTrendIndexEntity> {

    @Override
    public String getTableName() {
        return "google_trend_data";
    }


    public String getLastData(String keywordName, String country, Integer frequency) {
        String sql = "select jsonData from " + getTableName() + " where keywordName=? and geo = ? and dataRange = ? order by rankDate desc limit 1";
        return queryForString(sql, keywordName, country, frequency);
    }

    public Integer delete(String keywordName, Integer rankingDate, String country, Integer frequency) {
        String sql = "delete from " + getTableName() + " where keywordName=? and rankDate=? and geo = ? and dataRange = ? ";
        return executeUpdate(sql, keywordName, rankingDate, country, frequency);
    }

    public int insert(GoogleTrendIndexEntity entity) {
        Map<String, Object> values = new HashMap<>();
        values.put("keywordName", entity.getKeywordName());
        values.put("geo", entity.getGeo());
        values.put("rankDate", entity.getRankDate());
        values.put("jsonData", entity.getJsonData());
        values.put("createDate", entity.getCreateDate());
        values.put("dataRange", entity.getDataRange());

        return this.insert(values);
    }

}
