/**
 *
 */
package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.AutorunInstanceEntity;

@Repository
public class AutoRunInstanceTestEntityDAO extends ActoniaBaseJdbcSupport<AutorunInstanceEntity> {

	@Override
	public String getTableName() {
		return "autorun_intance_test";
	}
	
	public void batchInsert(Collection<AutorunInstanceEntity> list) {
		String sql = "insert into " + getTableName() + " ( ";
		
		sql += " autorunDetailId  ,";	
		sql += " autorunInfoId	  ,";
		sql += " category		  ,";
		sql += " processType	  ,";	
		sql += " ownDomainId	  ,";	
		
		sql += " profileId		  ,";
		sql += " logDate		  ,";	
		sql += " status			  ,";
		sql += " sendDate		  ,";
		sql += " uploadDate		  ,";
		
		sql += " uploadCount	  ,";	
		sql += " version		  ,";	
		sql += " error_msg	   ) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (AutorunInstanceEntity entity : list) {
			batch.add(new Object[] {
					entity.getAutorunDetailId(),
					entity.getAutorunInfoId(),
					entity.getCategory(),
					entity.getProcessType(),
					entity.getOwnDomainId(),

					entity.getProfileId(),
					entity.getLogDate(),
					entity.getStatus(),
					entity.getSendDate(),
					entity.getUploadDate(),

					entity.getUploadCount(),
					entity.getVersion(),
					entity.getErrorMsg()
			});
		}
		this.executeBatch(sql, batch);
	}
	
	public void insert(AutorunInstanceEntity autorunInstanceEntity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("autorunDetailId", autorunInstanceEntity.getAutorunDetailId());
		values.put("autorunInfoId", autorunInstanceEntity.getAutorunInfoId());
		values.put("category", autorunInstanceEntity.getCategory());
		values.put("processType", autorunInstanceEntity.getProcessType());
		values.put("ownDomainId", autorunInstanceEntity.getOwnDomainId());
		values.put("profileId", autorunInstanceEntity.getProfileId());
		values.put("logDate", autorunInstanceEntity.getLogDate());
		values.put("status", autorunInstanceEntity.getStatus());
		values.put("sendDate", autorunInstanceEntity.getSendDate());
		values.put("uploadDate", autorunInstanceEntity.getUploadDate());
		values.put("uploadCount", autorunInstanceEntity.getUploadCount());
		values.put("version", autorunInstanceEntity.getVersion());
		values.put("error_msg", autorunInstanceEntity.getErrorMsg());
		values.put("createDate", autorunInstanceEntity.getCreateDate());

		insert(values);
	}
	
	
	public void updateSendDateForSender(Integer category, Integer ownDomainId, Integer profileId, Integer logDate, Date sendDate) {
		String sql = "update " + getTableName() + " set sendDate = ? where category = ?  and ownDomainId = ?  and profileId = ? and logDate = ? limit 1 ";
		executeUpdate(sql, sendDate, category, ownDomainId, profileId, logDate);
	}

	public boolean updateUploadInfoForUploader(Integer category, Integer ownDomainId, Integer profileId, Integer logDate, Integer version, Integer status, Date uploadDate, Integer uploadCount) {
		String sql = "update " + getTableName() + " set status = ?, sendDate = ?, uploadCount = ? where category = ?  and ownDomainId = ?  and profileId = ? and logDate = ? and version = ? and status = ? limit 1 ";
		
		System.out.println(sql + " " + category + "-" + ownDomainId + "-" + profileId + "-" + logDate + "-" + version + "-" + uploadDate + "-" + uploadCount + "-" + AutorunInstanceEntity.STATUS_PROICESSING);
		return executeUpdate(sql, status, uploadDate, uploadCount, category, ownDomainId, profileId, logDate, version, AutorunInstanceEntity.STATUS_PROICESSING) > 0;
		
	}

}