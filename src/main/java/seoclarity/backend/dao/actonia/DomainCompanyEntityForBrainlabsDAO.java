/**
 *
 */
package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.AutorunInfoEntity;
import seoclarity.backend.entity.actonia.DomainCompanyEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class DomainCompanyEntityForBrainlabsDAO extends ActoniaBaseJdbcSupport<DomainCompanyEntity> {

	@Override
	public String getTableName() {
		return "autorun_info";
	}


	public List<DomainCompanyEntity> getDomainCompany(String companyName,Integer sDate,Integer eDate) {
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT ");
		sql.append("     OID, ");
		sql.append("     domain, ");
		sql.append("     SEType, ");
		sql.append("     concat(SE, '(', country, '_', language, ')(', if(device = 'd', 'Desktop', 'Mobile'), ')') AS SE, ");
		sql.append("     kwCnt, ");
		sql.append("     (CASE WHEN ((addedKW > 0) OR (removedKW > 0)) THEN  concat('', if(addedKW > 0, concat(' Added:', addedKW), ''), if(removedKW > 0, concat(' Deleted:', removedKW), '')) ELSE '-' end ) AS KwChange");
		sql.append(" FROM ");
		sql.append(" (");
		sql.append("     SELECT ");
		sql.append("         OID, ");
		sql.append("         domain, ");
		sql.append("         SEType, ");
		sql.append("         SE, ");
		sql.append("         country, ");
		sql.append("         language, ");
		sql.append("         t1.device, ");
		sql.append("         t1.engineId, ");
		sql.append("         t1.languageId, ");
		sql.append("         count(*), ");
		sql.append("         count(distinct cdb.keywordRankcheckId, cdb.cityId) AS kwCnt, ");
		sql.append("         kwType, ");
		sql.append("         sortType, ");
		sql.append("         addedKW, ");
		sql.append("         removedKW");
		sql.append("     FROM ");
		sql.append("     (");
		sql.append("         SELECT ");
		sql.append("             tod.id AS OID, ");
		sql.append("             domain, ");
		sql.append("             'National(Primary)' AS SEType, ");
		sql.append("             search_engine AS SE, ");
		sql.append("             search_engine_country AS country, ");
		sql.append("             language, ");
		sql.append(" 			(CASE WHEN mobile_domain_flg = 1 THEN 'm' ELSE 'd' end) as device,");
		sql.append("             engineId, ");
		sql.append("             languageId, ");
		sql.append("             1 AS kwType, ");
		sql.append("             1 AS sortType");
		sql.append("         FROM t_own_domain AS tod");
		sql.append("         INNER JOIN t_own_domain_setting AS tods ON tod.id = tods.own_domain_id");
		sql.append("         INNER JOIN engine_country_language_mapping AS mapp ON (tod.search_engine_country = mapp.countryQueryName) AND (tod.search_engine = mapp.engineQueryName) AND (tod.language = mapp.languageQueryName) AND (tod.rank_from = mapp.rankFrom)");
		sql.append("         WHERE (tod.status = 1) AND (tods.company_name LIKE '%" + companyName + "%')");
		sql.append("         UNION ALL");
		sql.append("         SELECT ");
		sql.append("             tod.id, ");
		sql.append("             domain, ");
		sql.append("             'National(Secondary)' AS SEType, ");
		sql.append("             engineQueryName AS SE, ");
		sql.append("             countryQueryName AS country, ");
		sql.append("             languageQueryName AS language, ");
		sql.append("             rel.device, ");
		sql.append("             search_engine_id, ");
		sql.append("             search_engine_languageid, ");
		sql.append("             1 AS kwType, ");
		sql.append("             2 AS sortType");
		sql.append("         FROM t_own_domain AS tod");
		sql.append("         INNER JOIN t_own_domain_setting AS tods ON tod.id = tods.own_domain_id");
		sql.append("         INNER JOIN domain_search_engine_rel AS rel ON rel.own_domain_id = tod.id");
		sql.append("         INNER JOIN engine_country_language_mapping AS mapp ON (rel.search_engine_id = mapp.engineId) AND (rel.search_engine_languageid = mapp.languageId) AND (tod.rank_from = mapp.rankFrom)");
		sql.append("         WHERE (tod.status = 1) AND (rel.search_engine_id != 999) AND (tods.company_name LIKE '%" + companyName + "%') AND (mapp.enabled = 1)");
		sql.append("         UNION ALL");
		sql.append("         SELECT ");
		sql.append("             tod.id, ");
		sql.append("             domain, ");
		sql.append("             'National(Secondary)' AS SEType, ");
		sql.append("             engineQueryName AS SE, ");
		sql.append("             countryQueryName AS country, ");
		sql.append("             languageQueryName AS language, ");
		sql.append("             rel.device, ");
		sql.append("             mapp.engineId, ");
		sql.append("             mapp.languageId, ");
		sql.append("             1 AS kwType, ");
		sql.append("             2 AS sortType");
		sql.append("         FROM t_own_domain AS tod");
		sql.append("         INNER JOIN t_own_domain_setting AS tods ON tod.id = tods.own_domain_id");
		sql.append("         INNER JOIN engine_country_language_mapping AS mapp ON (tod.search_engine_country = mapp.countryQueryName) AND (tod.search_engine = mapp.engineQueryName) AND (tod.language = mapp.languageQueryName) AND (tod.rank_from = mapp.rankFrom)");
		sql.append("         INNER JOIN domain_search_engine_rel AS rel ON rel.own_domain_id = tod.id");
		sql.append("         WHERE (tod.status = 1) AND (rel.search_engine_id = 999) AND (tods.company_name LIKE '%" + companyName + "%') AND (mapp.enabled = 1)");
		sql.append("         UNION ALL");
		sql.append("         SELECT ");
		sql.append("             tod.id, ");
		sql.append("             domain, ");
		sql.append("             'Geo' AS SEType, ");
		sql.append("             search_engine AS engine, ");
		sql.append("             search_engine_country AS country, ");
		sql.append("             language, ");
		sql.append("             'd' AS device, ");
		sql.append("             engineId, ");
		sql.append("             languageId, ");
		sql.append("             2 AS kwType, ");
		sql.append("             3 AS sortType");
		sql.append("         FROM t_own_domain AS tod");
		sql.append("         INNER JOIN t_own_domain_setting AS tods ON tod.id = tods.own_domain_id");
		sql.append("         INNER JOIN engine_country_language_mapping AS mapp ON (tod.search_engine_country = mapp.countryQueryName) AND (tod.search_engine = mapp.engineQueryName) AND (tod.language = mapp.languageQueryName) AND (tod.rank_from = mapp.rankFrom)");
		sql.append("         WHERE (tod.status = 1) AND(tods.company_name LIKE '%" + companyName + "%') AND (enable_city_rank = 1)");
		sql.append("         UNION ALL");
		sql.append("         SELECT ");
		sql.append("             tod.id, ");
		sql.append("             domain, ");
		sql.append("             'Geo' AS SEType, ");
		sql.append("             search_engine AS engine, ");
		sql.append("             search_engine_country AS country, ");
		sql.append("             language, ");
		sql.append("             'm' AS device, ");
		sql.append("             engineId, ");
		sql.append("             languageId, ");
		sql.append("             2 AS kwType, ");
		sql.append("             3 AS sortType");
		sql.append("         FROM t_own_domain AS tod");
		sql.append("         INNER JOIN t_own_domain_setting AS tods ON tod.id = tods.own_domain_id");
		sql.append("         INNER JOIN engine_country_language_mapping AS mapp ON (tod.search_engine_country = mapp.countryQueryName) AND (tod.search_engine = mapp.engineQueryName) AND (tod.language = mapp.languageQueryName) AND (tod.rank_from = mapp.rankFrom)");
		sql.append("         WHERE (tod.status = 1) AND (tods.company_name LIKE '%" + companyName + "%') AND (enable_geo_mobile = 1)");
		sql.append("     ) AS t1");
		sql.append("     LEFT JOIN cdb_keyword_search_engine_rel AS cdb ON (t1.OID = cdb.ownDomainId) AND (t1.engineId = cdb.searchEngineId) AND (t1.languageId = cdb.languageId) AND (t1.device = cdb.device) AND (t1.kwType = cdb.keywordType)");
		sql.append("     LEFT JOIN ");
		sql.append("     (");
		sql.append("         SELECT ");
		sql.append("             ownDomainId, ");
		sql.append("             keywordType, ");
		sql.append("             searchEngineId, ");
		sql.append("             languageId, ");
		sql.append("             device, ");
		sql.append("             count(distinct keywordRankcheckId, cityId) AS addedKW");
		sql.append("         FROM cdb_keyword_search_engine_rel_log AS log1");
		sql.append("         INNER JOIN t_own_domain AS tod ON log1.ownDomainId = tod.id");
		sql.append("         INNER JOIN t_own_domain_setting AS tods ON tod.id = tods.own_domain_id");
		sql.append("         WHERE (tod.status = 1) AND (tods.company_name LIKE '%" + companyName + "%') AND (log1.operationType = 1) ");
		//AND log1.logDate >= 20240102 AND log1.logDate < 20240109
		sql.append(" AND log1.logDate >= ").append(sDate);
		sql.append(" AND log1.logDate < ").append(eDate);
		sql.append("         GROUP BY ");
		sql.append("             ownDomainId, ");
		sql.append("             keywordType, ");
		sql.append("             searchEngineId, ");
		sql.append("             languageId, ");
		sql.append("             device");
		sql.append("     ) AS logA ON (t1.OID = logA.ownDomainId) AND (t1.kwType = logA.keywordType) AND (t1.engineId = logA.searchEngineId) AND (t1.languageId = logA.languageId) AND (t1.device = logA.device)");
		sql.append("     LEFT JOIN ");
		sql.append("     (");
		sql.append("         SELECT ");
		sql.append("             ownDomainId, ");
		sql.append("             keywordType, ");
		sql.append("             searchEngineId, ");
		sql.append("             languageId, ");
		sql.append("             device, ");
		sql.append("             count(distinct keywordRankcheckId, cityId) AS removedKW");
		sql.append("         FROM cdb_keyword_search_engine_rel_log AS log1");
		sql.append("         INNER JOIN t_own_domain AS tod ON log1.ownDomainId = tod.id");
		sql.append("         INNER JOIN t_own_domain_setting AS tods ON tod.id = tods.own_domain_id");
		sql.append("         WHERE (tod.status = 1) AND (tods.company_name LIKE '%" + companyName + "%') AND (log1.operationType = 3) ");
		sql.append(" AND log1.logDate >= ").append(sDate);
		sql.append(" AND log1.logDate < ").append(eDate);
		sql.append("         GROUP BY ");
		sql.append("             ownDomainId, ");
		sql.append("             keywordType, ");
		sql.append("             searchEngineId, ");
		sql.append("             languageId, ");
		sql.append("             device");
		sql.append("     ) AS logD ON (t1.OID = logD.ownDomainId) AND (t1.kwType = logD.keywordType) AND (t1.engineId = logD.searchEngineId) AND (t1.languageId = logD.languageId) AND (t1.device = logD.device)");
		sql.append("     GROUP BY ");
		sql.append("         OID, ");
		sql.append("         domain, ");
		sql.append("         SEType, ");
		sql.append("         SE, ");
		sql.append("         country, ");
		sql.append("         language, ");
		sql.append("         t1.device, ");
		sql.append("         t1.engineId, ");
		sql.append("         t1.languageId");
		sql.append(" ) AS z");
		sql.append(" ORDER BY ");
		sql.append("     OID ASC, ");
		sql.append("     sortType ASC, ");
		sql.append("     SE ASC");
		System.out.println("=============================== getDomainCompany ==============================");
		System.out.println(sql.toString());
		return this.findBySql(sql.toString());
	}

}