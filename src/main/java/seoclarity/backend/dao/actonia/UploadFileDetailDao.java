package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.UploadFileDetailEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class UploadFileDetailDao extends ActoniaBaseJdbcSupport<UploadFileDetailEntity>{

    @Override
    public String getTableName() {
        return "upload_file_detail";
    }

    public UploadFileDetailEntity getUploadFileDetailByUniqueKey(UploadFileDetailEntity fileDetailEntity) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select id, uploadType, ownDomainId, fileName, fileNameDay, zipFileSize, unzippedFileSize, dataCount, loadedCount, createDate from ");
        sql.append(getTableName());
        sql.append(" where uploadType = ? and ownDomainId = ? and fileName = ? and fileNameDay = ?");
        return findObject(sql.toString(), fileDetailEntity.getUploadType(), fileDetailEntity.getOwnDomainId(), fileDetailEntity.getFileName(), fileDetailEntity.getFileNameDay());
    }

    public int insert(UploadFileDetailEntity fileDetailEntity) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("uploadType", fileDetailEntity.getUploadType());
        values.put("ownDomainId", fileDetailEntity.getOwnDomainId());
        values.put("fileName", fileDetailEntity.getFileName());
        values.put("fileNameDay", fileDetailEntity.getFileNameDay());
        values.put("zipFileSize", fileDetailEntity.getZipFileSize());
        values.put("unzippedFileSize", fileDetailEntity.getUnzippedFileSize());
        values.put("dataCount", fileDetailEntity.getDataCount());
        values.put("loadedCount", fileDetailEntity.getLoadedCount());
        values.put("createDate", fileDetailEntity.getCreateDate());
        return this.insert(values);
    }

    public void updateZippedFileSize(int id, UploadFileDetailEntity fileDetailEntity) {
        StringBuilder sql = new StringBuilder();
        sql.append(" update ").append(getTableName()).append(" set zipFileSize = ? ");
        sql.append(" where id = ? and uploadType = ? and ownDomainId = ? and fileName = ?");
        this.executeUpdate(sql.toString(), fileDetailEntity.getZipFileSize(), id, fileDetailEntity.getUploadType(), fileDetailEntity.getOwnDomainId(), fileDetailEntity.getFileName());
    }

    public void updateUnzippedFileSize(int id, UploadFileDetailEntity fileDetailEntity) {
        StringBuilder sql = new StringBuilder();
        sql.append(" update ").append(getTableName()).append(" set unzippedFileSize = ? ");
        sql.append(" where id = ? and uploadType = ? and ownDomainId = ? and fileName = ? and fileNameDay = ? ");
        this.executeUpdate(sql.toString(), fileDetailEntity.getUnzippedFileSize(), id, fileDetailEntity.getUploadType(),
                fileDetailEntity.getOwnDomainId(), fileDetailEntity.getFileName(), fileDetailEntity.getFileNameDay());
    }

    public void updateDataCount(int id, UploadFileDetailEntity fileDetailEntity) {
        StringBuilder sql = new StringBuilder();
        sql.append(" update ").append(getTableName()).append(" set dataCount = ? ");
        sql.append(" where id = ? and uploadType = ? and ownDomainId = ? and fileName = ? and fileNameDay = ? ");
        this.executeUpdate(sql.toString(), fileDetailEntity.getDataCount(), id, fileDetailEntity.getUploadType(),
                fileDetailEntity.getOwnDomainId(), fileDetailEntity.getFileName(), fileDetailEntity.getFileNameDay());
    }

    public void updateLoadedCount(int id, UploadFileDetailEntity fileDetailEntity) {
        StringBuilder sql = new StringBuilder();
        sql.append(" update ").append(getTableName()).append(" set loadedCount = ? ");
        sql.append(" where id = ? and uploadType = ? and ownDomainId = ? and fileName = ? and fileNameDay = ? ");
        this.executeUpdate(sql.toString(), fileDetailEntity.getLoadedCount(), id, fileDetailEntity.getUploadType(),
                fileDetailEntity.getOwnDomainId(), fileDetailEntity.getFileName(), fileDetailEntity.getFileNameDay());
    }

    public List<String> getFileNameDaysByDates(String dates, int domainId) {
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct fileNameDay from ").append(getTableName()).append(" where fileNameDay in (").append(dates).append(") ").append(" and ownDomainId = ? ");
        System.out.println("===getFileNameDaysByDates:" + sql.toString() + " domainId: " + domainId);
        return queryForStringList(sql.toString(), domainId);
    }
}
