package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.CdbTrackedKeywordEntity;
import seoclarity.backend.entity.actonia.GroupTagEntity;

import java.util.List;
import java.util.Set;

@Repository
public class CdbTrackedKeywordEntityDAO extends ActoniaBaseJdbcSupport<CdbTrackedKeywordEntity>{

	@Override
	public String getTableName() {
		return "cdb_tracked_keyword";
	}

	public List<CdbTrackedKeywordEntity> getCdbListByDomainId(int domainId){
		String sql = " select keywordId,keywordRankcheckId,locationId,groupTagId,t2.tag_name from " + getTableName() + " t1 ";
		sql += " left join t_group_tag t2 on t1.groupTagId = t2.id ";
		sql += " where t1.tagStatus = " + CdbTrackedKeywordEntity.TAG_STATUS_ACTIVE;
		sql += " and ownDomainId = " + domainId;
		System.out.println("===SQL getCdbListByDomainId:" + sql);
		return findBySql(sql);
	}

	public CdbTrackedKeywordEntity getKeywordCountByDomainId(int domainId, long startId, int pageSize){
		String sql = " select count(*) as kwCnt,min(keywordRankcheckId) minRCId,max(keywordRankcheckId) maxRCId from (";
		sql += " select keywordRankcheckId from " + getTableName();
		sql += " where keywordType = " + CdbTrackedKeywordEntity.KEYWORD_TYPE_MANAGED;
		sql += " and groupTagId = " + CdbTrackedKeywordEntity.GROUPTAG_ID_FOR_DOMAIN;
		sql += " and locationId = " + CdbTrackedKeywordEntity.DEFAULT_LOCATION_ID;
		sql += " and ownDomainId = " + domainId;
		sql += " and keywordRankcheckId > ? ";
		sql += " order by keywordRankcheckId limit ? )t ";
		System.out.println("===SQL getKeywordCountByDomainId:" + sql + ",startId:" + startId + ",pageSize:" + pageSize);
		return findObject(sql, startId, pageSize);
	}

	@Deprecated
	public List<CdbTrackedKeywordEntity> getCdbListFor7733(){
		String sql = "select tk.raw_keyword_name as rawKeywordName, tag.tag_name as tagName from cdb_tracked_keyword_log log  " +
				"left join t_keyword tk on log.keywordId  = tk.id and log.ownDomainId = tk.own_domain_id " +
				"left join t_group_tag tag on tag.id = log.groupTagId and tag.domain_id = log.ownDomainId " +
				" where ownDomainId = 7733 and logDate = 20221223 and operationType =3 and groupTagId > 0 ";

		return findBySql(sql);
	}

	public List<CdbTrackedKeywordEntity> getTagByKeyword(String kw, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select ctk.id,ctk.ownDomainId,ctk.keywordType," );
		sql.append(	"ctk.keywordId,ctk.keywordRankcheckId,ctk.locationId,ctk.groupTagId,tag.tag_name,tk.keyword_name ");
		sql.append(" from ").append(getTableName()).append("  ctk  ");
		sql.append(" left join t_keyword  tk on ctk.ownDomainId = tk.own_domain_id  and ctk.keywordId = tk.id ");
		sql.append(" left join t_group_tag tag on ctk.groupTagId= tag.id  ");
		sql.append(" where ctk.ownDomainId  = ? ").append(" and tag.tag_name = ? ");
		sql.append(" order by groupTagId desc , locationId desc , keywordId ");
		return this.findBySql(sql.toString(),domainId,kw);
	}

	public List<GroupTagEntity> getTagKeywordsByEngineLanguage(int domainId, int tagId, int engineId, int languageId, String device){
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT distinct t3.id,t3.raw_keyword_name " );
		sql.append(" from cdb_tracked_keyword t1 ");
		sql.append(" join keyword_search_engine_rel t2 on t1.keywordId = t2.keywordId and t1.ownDomainId = t2.ownDomainId  ");
		sql.append(" and t1.keywordType = t2.keywordType and t1.locationId = t2.cityId  ");
		sql.append(" join t_keyword t3 on t1.ownDomainId = t3.own_domain_id and t1.keywordId = t3.id ");
		sql.append(" where t1.ownDomainId = ? and t1.groupTagId = ? and t2.searchEngineId = ? and t2.languageId = ? and t2.device = ? ");
		return findBySql(sql.toString(), GroupTagEntity.class, domainId, tagId, engineId, languageId, device);
	}

	public List<CdbTrackedKeywordEntity> getHasRIData(Set<Integer> domainIds) {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT COUNT(1) as kwCnt , own_domain_id as ownDomainId ");
		sql.append(" from crawl_request_log ");
		sql.append(" where own_domain_id  in (0");
		for (Integer domainId : domainIds){
			sql.append(",");
			sql.append(domainId);
		}
		sql.append("  ) ");
		sql.append(" group by own_domain_id ");
		return findBySql(sql.toString());
	}


}