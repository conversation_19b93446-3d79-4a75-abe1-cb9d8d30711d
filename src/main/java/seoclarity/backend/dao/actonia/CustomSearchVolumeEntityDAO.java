package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.CustomSearchVolumeEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordAdwordsEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class CustomSearchVolumeEntityDAO extends ActoniaBaseJdbcSupport<CustomSearchVolumeEntity>{
    @Override
    public String getTableName() {
        return "custom_searchvolume";
    }

    public List<CustomSearchVolumeEntity> getListByDomainId(int domainId) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where ownDomainId = ? ");
        return findBySql(sbd.toString(), domainId);
    }

    public List<CustomSearchVolumeEntity> getExistList(int domainId, int engineId, int languageId, int keywordType, int dataType, int locationId) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where ownDomainId = ? ");
        sbd.append(" and engineId = ? ");
        sbd.append(" and languageId = ? ");
        sbd.append(" and keywordType = ? ");
        sbd.append(" and dataType = ? ");
        sbd.append(" and locationId = ? ");
        return findBySql(sbd.toString(), domainId, engineId, languageId, keywordType, dataType, locationId);
    }

    public List<CustomSearchVolumeEntity> checkExistByUniqueKey(int keywordType, int domainId, int engineId, int languageId, List<? extends Number> keywordIds, int locationId) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where keywordType = ? ");
        sbd.append(" and ownDomainId = ? ");
        sbd.append(" and engineId = ? ");
        sbd.append(" and languageId = ? ");
        sbd.append(" and locationId = ? ");
        sbd.append(" and keywordRankcheckId in( ").append(StringUtils.join(keywordIds,",")).append(" )");
        return this.findBySql(sbd.toString(), keywordType, domainId, engineId, languageId, locationId);
    }

    public int[] batchInsertIgnore(List<CustomSearchVolumeEntity> list) {
        String sql = "insert ignore into " + getTableName()
                + "(ownDomainId,keywordType,engineId,languageId,keywordRankcheckId,locationId,dataType,avgSearchVolume,searchVolumeDate,createDate) " +
                "values (?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (CustomSearchVolumeEntity entity : list) {
            batch.add(new Object[]{
                    entity.getOwnDomainId(),
                    entity.getKeywordType(),
                    entity.getEngineId(),
                    entity.getLanguageId(),
                    entity.getKeywordRankcheckId(),
                    entity.getLocationId(),
                    entity.getDataType(),
                    entity.getAvgSearchVolume(),
                    entity.getSearchVolumneDate(),
                    new Date()
            });
        }
        return this.executeBatch(sql, batch);
    }

    public void updateAvgSearchVolumeByUniqueKey(long newAvgSearchVolume, int ownDomainId, int keywordType, int engineId, int languageId, int keywordRankcheckId, int locationId, int searchVolumeDate) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" update ").append(getTableName());
        sbd.append(" set avgSearchVolume = ?, ");
        sbd.append(" searchVolumeDate = ? ");
        sbd.append(" where ownDomainId = ? ");
        sbd.append(" and  keywordType = ? ");
        sbd.append(" and engineId = ? ");
        sbd.append(" and languageId = ? ");
        sbd.append(" and keywordRankcheckId = ? ");
        sbd.append(" and locationId = ? ");
        System.out.println("sbd" + sbd.toString() + " param:" + newAvgSearchVolume + "," + ownDomainId + "," + keywordType + "," + engineId + "," + languageId + "," + keywordRankcheckId + "," + locationId + "," + searchVolumeDate);
        executeUpdate(sbd.toString(), newAvgSearchVolume, searchVolumeDate, ownDomainId, keywordType, engineId, languageId, keywordRankcheckId, locationId);
    }

    public void batchUpdateSv(List<CustomSearchVolumeEntity> list) {
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName());
        sql.append(" set avgSearchVolume=?, searchVolumeDate=?, createDate=? ");
        sql.append(" where id = ? ");
        Object[] values = null;
        List<Object[]> batch = new ArrayList<Object[]>();
        for (CustomSearchVolumeEntity entity : list) {
            values = new Object[]{
                    entity.getAvgSearchVolume(),
                    entity.getSearchVolumneDate(),
                    entity.getCreateDate(),
                    entity.getId()
            };
            batch.add(values);
        }
        this.executeBatch(sql.toString(), batch);
    }

    public List<CustomSearchVolumeEntity> getSvByOidAndKwIdList(List<Integer> kwIdList, int oid) {
        String sbd = " select * from " + getTableName() + " where ownDomainId = ? and keywordRankcheckId in( " + StringUtils.join(kwIdList, ",") + " )";
        return findBySql(sbd, oid);
    }
}
