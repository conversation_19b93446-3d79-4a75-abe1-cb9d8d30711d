package seoclarity.backend.dao.actonia.qvc;

import lombok.extern.apachecommons.CommonsLog;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.QvcProductEntity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@CommonsLog
@Repository
public class QvcProductDAO extends ActoniaBaseJdbcSupport<QvcProductEntity> {

    @Override
    public String getTableName() {
        return "qvc_product";
    }

    public Integer getCountByDate(Integer stockDate, Integer oId) {
        String sql = " select count(1) from " + getTableName() + " where stock_date = ? and own_domain_id = ? ";
        return queryForInteger(sql, stockDate, oId);
    }

    public void batchInsert(Collection<QvcProductEntity> list) {
        String sql = "insert into " + getTableName() + " ( ";
        sql += " own_domain_id  ,";
        sql += " stock_date	  ,";
        sql += " p_id		  ,";
        sql += " f_id	  ,";
        sql += " title	  ,";
        sql += " description		  ,";
        sql += " google_product_category		  ,";
        sql += " product_type			  ,";
        sql += " link		  ,";
        sql += " image_link		  ,";
        sql += " `condition`		  ,";
        sql += " availability		  ,";
        sql += " price		  ,";
        sql += " sale_price		  ,";
        sql += " price_unit		  ,";
        sql += " sale_price_effective_date		  ,";
        sql += " brand		  ,";
        sql += " gtin		  ,";
        sql += " mpn		  ,";
        sql += " item_group_id		  ,";
        sql += " color		  ,";
        sql += " material		  ,";
        sql += " pattern		  ,";
        sql += " size		  ,";
        sql += " gender		  ,";
        sql += " age_group		  ,";
        sql += " tax		  ,";
        sql += " shipping		  ,";
        sql += " shipping_weight		  ,";
        sql += " excluded_destination		  ,";
        sql += " unit_pricing_base_measure		  ,";
        sql += " unit_pricing_measure		  ,";
        sql += " adwords_redirect		  ,";
        sql += " custom_label_0		  ,";
        sql += " custom_label_1		  ,";
        sql += " custom_label_2		  ,";
        sql += " custom_label_3		  ,";
        sql += " promotion_id		  ,";
        sql += " product_type_2		  ,";
        sql += " link_template		  ,";
        sql += " createDate	   ) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (QvcProductEntity entity : list) {
            batch.add(new Object[]{
                    entity.getOwnDomainId(),
                    entity.getStockDate(),
                    entity.getpId(),
                    entity.getfId(),
                    entity.getTitle(),
                    entity.getDescription(),
                    entity.getGoogleProductCategory(),
                    entity.getProductType(),
                    entity.getLink(),
                    entity.getImageLink(),
                    entity.getCondition(),
                    entity.getAvailability(),
                    entity.getPrice(),
                    entity.getSalePrice(),
                    entity.getPriceUnit(),
                    entity.getSalePriceEffectiveDate(),
                    entity.getBrand(),
                    entity.getGtin(),
                    entity.getMpn(),
                    entity.getItemGroupId(),
                    entity.getColor(),
                    entity.getMaterial(),
                    entity.getPattern(),
                    entity.getSize(),
                    entity.getGender(),
                    entity.getAgeGroup(),
                    entity.getTax(),
                    entity.getShipping(),
                    entity.getShippingWeight(),
                    entity.getExcludedDestination(),
                    entity.getUnitPricingBaseMeasure(),
                    entity.getUnitPricingMeasure(),
                    entity.getAdwordsRedirect(),
                    entity.getCustomLabel0(),
                    entity.getCustomLabel1(),
                    entity.getCustomLabel2(),
                    entity.getCustomLabel3(),
                    entity.getPromotionId(),
                    entity.getProductType2(),
                    entity.getLinkTemplate(),
                    entity.getCreateDate()

            });
        }
        this.executeBatch(sql, batch);
    }

    public List<String> getOutOfStockPidList(int domainId, int yesterday, int processDay) {
        String sql = " SELECT distinct t1.p_id from( ";
        sql += " SELECT p_id FROM qvc_product qp1";
        sql += "    where  qp1.own_domain_id = ? and qp1.stock_date >= ? and  qp1.stock_date < ? ";
        sql += "    group by p_id having count(distinct stock_date) >= 3 ";
        sql += " )t1 LEFT JOIN (";
        sql += "    SELECT distinct p_id FROM qvc_product qp2 where qp2.own_domain_id = ? and qp2.stock_date = ? ) t2 ";
        sql += "        on t1.p_id = t2.p_id ";
        sql += " where t2.p_id is null";
        log.info("==getOutOfStockPidList: " + sql + ",domainId:" + domainId + ",yesterday:" + yesterday + ",processDay:" + processDay);
        return queryForStringList(sql, domainId, yesterday, processDay, domainId, processDay);
    }

    public Integer getCountByPid(Integer stockDate, Integer oId, String pid) {
        String sql = " select count(1) from " + getTableName() + " where stock_date = ? and own_domain_id = ? and p_id = ? ";
        return queryForInteger(sql, stockDate, oId, pid);
    }

}
