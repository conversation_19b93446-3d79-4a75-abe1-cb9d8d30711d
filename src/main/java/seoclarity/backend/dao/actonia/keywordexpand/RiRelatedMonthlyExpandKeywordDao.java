package seoclarity.backend.dao.actonia.keywordexpand;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.keywordexpand.RiRelatedMonthlyExpandKeyword;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class RiRelatedMonthlyExpandKeywordDao extends ActoniaBaseJdbcSupport<RiRelatedMonthlyExpandKeyword> {

    @Override
    public String getTableName() {
        return "ri_related_monthly_expand_keyword";
    }

    public void insertBatch(List<RiRelatedMonthlyExpandKeyword> expandKeywordList) {
        String sql = "INSERT IGNORE INTO " + getTableName() + " (rankDate, countryType, engineId, languageId, keywordName, cdbKeywordMurmur3hash, sendFlg, uploadFlg) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        List<Object[]> batch = new ArrayList<>();
        for (RiRelatedMonthlyExpandKeyword expandKeyword : expandKeywordList) {
            Object[] obj = new Object[]{
                    expandKeyword.getRankDate(),
                    expandKeyword.getCountryType(),
                    expandKeyword.getEngineId(),
                    expandKeyword.getLanguageId(),
                    expandKeyword.getKeywordName(),
                    expandKeyword.getCdbKeywordMurmur3hash(),
                    0,
                    0
            };
            batch.add(obj);
        }
        autoRetryBatchInsert(sql, batch);
    }

    public List<RiRelatedMonthlyExpandKeyword> getExpandKeywordListByRankDate(int rankDate, String countryType) {
        String sql = "select id, CAST(cdbKeywordMurmur3hash AS CHAR) as cdbKeywordMurmur3hash from ri_related_monthly_expand_keyword where rankDate = ? and countryType = ? order by id";
        return findBySql(sql, rankDate, countryType);
    }

    public void updateRankDateByStartId(int rankDate,long startId) {
        String sql = "update " + getTableName() + " set rankDate = ? where id >= ?";
        executeUpdate(sql, rankDate, startId);
    }
}
