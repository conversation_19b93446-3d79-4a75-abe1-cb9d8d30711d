package seoclarity.backend.dao.actonia.gabigquery;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.gabigquery.GaBqBulkExportInstanceEntity;
import seoclarity.backend.entity.actonia.gscbigquery.GscBqBulkExportInstanceEntity;

@Repository
public class GaBqBulkExportInstanceDAO extends ActoniaBaseJdbcSupport<GaBqBulkExportInstanceEntity> {


    @Override
    public String getTableName() {
        return "ga_bq_bulkexport_instance ";
    }
    
    public Integer getInstanceIdByDomainIdAndDate(int domainId, String tableName, String processDate, Integer trafficType){
        String sql = " select id from " + getTableName();
        sql += " where domainId = ?  and bqNamespace = ? and bqDataDate  = ? and trafficType = ? limit 1";
        
        System.out.println(sql);
        
        System.out.println(domainId + "-" + tableName  +  "-" +  processDate +  "-"  + trafficType);
        return queryForInteger(sql, domainId, 
        		tableName, 
        		processDate, 
        		trafficType);
    }

    public int insert(GaBqBulkExportInstanceEntity entity) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("domainId", entity.getDomainId());
        values.put("bqNamespace", entity.getBqNamespace());
        values.put("bqDataDate", entity.getBqDataDate());
        values.put("extractStatus", entity.getExtractStatus());
        values.put("uploadStatus", entity.getUploadStatus());
        values.put("trafficType", entity.getTrafficType());
        values.put("createdDate", new Date());
        return insert(values);
    }

    public void bulkInsert(List<GaBqBulkExportInstanceEntity> entityList) {
        String sql = "insert into " + getTableName()+ " (domainId,bqNamespace,bqDataDate,extractStatus,uploadStatus,trafficType) " +
                "values(?,?,?,?,?,?)";
        List<Object[]> params = new ArrayList<Object[]>();
        for (GaBqBulkExportInstanceEntity entity : entityList) {
            params.add(new Object[] {
                    entity.getDomainId(),
                    entity.getBqNamespace(),
                    entity.getBqDataDate(),
                    entity.getExtractStatus(),
                    entity.getUploadStatus(),
                    entity.getTrafficType()
            });
        }
        params.add(new Object[] {});
        this.executeBatch(sql, params);
    }

    public GaBqBulkExportInstanceEntity getInstanceById(int id){
        String sql = " select * from " + getTableName() + " where id = ? ";
        return findObject(sql, id);
    }

    public void updateExtractStatus(int id, int status, String s3FullPathFileName){
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set extractStatus = ? , updateDate = ? ");
        if(StringUtils.isNotBlank(s3FullPathFileName)){
            sql.append(" ,s3FullPathFileName='").append(s3FullPathFileName).append("' ");
        }
        sql.append(" where id =?  ");
        executeUpdate(sql.toString(), status, new Date(), id);
    }

    public void updateUploadStatus(int id, int status){
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set uploadStatus = ? , updateDate = ? where id =? ");
        executeUpdate(sql.toString(), status, new Date(), id);
    }

    public GaBqBulkExportInstanceEntity getInstanceByDate(int domainId, String bqNamespace, String bqDataDate, Integer trafficType){
        String sql = " select * from " + getTableName() + " where domainId = ? and bqNamespace= ? and bqDataDate = ? and trafficType = ?";
        return findObject(sql, domainId, bqNamespace, bqDataDate, trafficType);
    }
    
    public GaBqBulkExportInstanceEntity getInstanceByDate(int domainId, String bqDataDate, Integer trafficType){
        String sql = " select * from " + getTableName() + " where domainId = ? and bqDataDate = ? and trafficType = ?";
        return findObject(sql, domainId, bqDataDate, trafficType);
    }

    public List<GaBqBulkExportInstanceEntity> getAllInstanceByDomainId(int domainId, String dataDate){
        String sql = " select * from " + getTableName();
        sql += " where domainId = ? ";
        if(StringUtils.isNotBlank(dataDate)){
            sql += " and bqDataDate = '" + dataDate + "' ";
        }
        sql += " order by bqDataDate ";
        return findBySql(sql, domainId);
    }

}
