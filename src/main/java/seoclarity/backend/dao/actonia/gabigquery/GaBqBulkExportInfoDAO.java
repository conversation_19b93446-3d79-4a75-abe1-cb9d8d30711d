package seoclarity.backend.dao.actonia.gabigquery;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.gabigquery.GaBqBulkexportInfoEntity;
import seoclarity.backend.entity.actonia.gscbigquery.BulkExportInfoEntity;

import java.util.Date;
import java.util.List;

@Repository
public class GaBqBulkExportInfoDAO extends ActoniaBaseJdbcSupport<GaBqBulkexportInfoEntity> {


    @Override
    public String getTableName() {
        return "ga_bq_bulkexport_info";
    }

    public List<GaBqBulkexportInfoEntity> getBulkExportInfoList(){
        String sql = " select * ";
        sql += " from " + getTableName() + " where enabled = ? ";
        return findBySql(sql, BulkExportInfoEntity.ENABLED);
    }

    public GaBqBulkexportInfoEntity getInfoByDomainId(int domainId){
        String sql = " select * " +
                " from " + getTableName() + " where enabled = ? and domainId = ? ";
        return findObject(sql, BulkExportInfoEntity.ENABLED, domainId);
    }

    public void updateErrorStatus(int id, String errorMessage, String errorTrace){
        StringBuffer sql = new StringBuffer();
        sql.append(" update " + getTableName() + " set status = ? , errorMessage = ?, errorTrace = ? , updateDate = ? where id =? ");
        executeUpdate(sql.toString(), BulkExportInfoEntity.STATUS_ACCESS_ERROR, errorMessage, errorTrace, new Date(), id);

    }

}
