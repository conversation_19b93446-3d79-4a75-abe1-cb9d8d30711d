package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.GWMDomainRel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public class GwmDomainRelDAO extends ActoniaBaseJdbcSupport<GWMDomainRel> {

    @Override
    public String getTableName() {
        return "gwm_domain_rel";
    }

    public String getProfileById(int id) {
        String sql = "select gwm_domain_name from gwm_domain_rel where id = ? ";
        return queryForString(sql, id);
    }

    public String getProfileByRelId(int relId) {
        String sql = "select gwm_domain_name from gwm_domain_rel_log where rel_id = ? and operation_type = 1 order by created_at limit 1";
        return queryForString(sql, relId);
    }

    public List<Integer> getProfilesByDomainId(int domainId) {
        String sql = "select id from " + getTableName() + " where own_domain_id = ? and data_source = 0 ";
        return queryForIntegerList(sql, domainId);
    }

    public List<GWMDomainRel> getByDomainId(Integer id, int dataSource, String countryCode, Set<String> urls) {
        String sql = " select *  FROM " + getTableName() + " WHERE own_domain_id = ? and data_source = ? and gwm_country_code = ? and gwm_domain_name in('" + StringUtils.join(urls, "','") + "') ";
        return this.findBySql(sql, id, dataSource, countryCode);
    }

    public void insertData(List<GWMDomainRel> gwmDomainRels) {

        String sql = "insert into " + getTableName() + " (own_domain_id, gwm_domain_name, data_source, gwm_country_code, create_date, default_domain"
                + ") values (?, ?, ?, ?, ?, ?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (GWMDomainRel entity : gwmDomainRels) {
            batch.add(new Object[]{
                    entity.getOwnDomainId(),
                    entity.getGwmDomainName(),
                    entity.getDataSource(),
                    entity.getGwmCountryCode(),
                    new Date(),
                    null
            });
        }
        this.executeBatch(sql, batch);
    }
    
    public List<GWMDomainRel> getProfileList(int dataSource, String retrieveErrCategory, int authenticateFromDate) {
    	StringBuffer sqlBuff = new StringBuffer();
    	sqlBuff.append("select t1.own_domain_id, t1.relId as id, gwm_domain_name, formattedProfileName, if(err.subId is not null, 1, 0) as isAuthenticationIssue");
    	sqlBuff.append(" FROM (select rel.own_domain_id, rel.id relId, gwm_domain_name, ");
    	sqlBuff.append("  (case when (gwm_domain_name like 'http://%' or gwm_domain_name like 'https://%') and gwm_domain_name not like '%/' ");
    	sqlBuff.append("   then concat(gwm_domain_name, '/') else gwm_domain_name end) formattedProfileName ");
    	sqlBuff.append("  from ").append(getTableName()).append(" rel join t_own_domain tod on rel.own_domain_id=tod.id ");
    	sqlBuff.append("  join t_own_domain_setting tods on tod.id=tods.own_domain_id ");
    	sqlBuff.append("  join gsc_entity gsc on tods.gwt_account=gsc.gsc_account ");
    	sqlBuff.append("  left join gsc_multi_domain_profile shr on rel.own_domain_id=shr.refer_domain_id and rel.id=shr.refer_profile_id ");
    	sqlBuff.append("  where tod.status=1 and ifNull(tods.disable_url_inspect_retrieval, 0)!=1 and ifNull(tods.gwt_account, '')!='' ");
    	sqlBuff.append("   and rel.data_source=? and shr.id is null ) t1");
    	sqlBuff.append(" left join (select distinct ownDomainId,subId from analytics_retrieve_error where category=? and isAuthenticationIssue=1 ");
    	sqlBuff.append("  and trackDate>=?) err on t1.own_domain_id=err.ownDomainId and t1.relId=err.subId ");
    	sqlBuff.append(" order by formattedProfileName,t1.own_domain_id desc, t1.relId");
        return this.findBySql(sqlBuff.toString(), dataSource, retrieveErrCategory, authenticateFromDate);
    }
}
