package seoclarity.backend.dao.actonia;


import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.GroupRelationshipEntity;

import java.util.List;

@Repository
public class GroupRelationshipEntityDAO extends ActoniaBaseJdbcSupport<GroupRelationshipEntity> {

	@Override
	public String getTableName() {
		return "`group_relationship`";
	}

	public GroupRelationshipEntity get(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("  own_domain_id = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, domainId);
	}

	public GroupRelationshipEntity get(int groupId, int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     group_id = ?");
		stringBuilder.append(" and own_domain_id = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, groupId, domainId);
	}

	public GroupRelationshipEntity get(int groupId, int domainId, int status) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     group_id = ?");
		stringBuilder.append(" and own_domain_id = ?");
		stringBuilder.append(" and status = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, groupId, domainId, status);
	}

	public List<GroupRelationshipEntity> getList(int groupId, int status) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     group_id = ?");
		stringBuilder.append(" and status = ?");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, groupId, status);
	}

	public List<GroupRelationshipEntity> getList(int startGroupId, int endGroupId, int status) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     group_id >= ?");
		stringBuilder.append(" and group_id <= ?");
		stringBuilder.append(" and status = ?");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, startGroupId, endGroupId, status);
	}

}