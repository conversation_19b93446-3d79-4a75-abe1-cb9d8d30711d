package seoclarity.backend.dao.actonia;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.MonthlySenderInfoEntity;

@Deprecated
@Repository
public class MonthlySenderInfoEntityDAO extends ActoniaBaseJdbcSupport<MonthlySenderInfoEntity> {

	@Override
	public String getTableName() {
		return "monthly_sender_info";
	}
	
	public List<MonthlySenderInfoEntity> getAllMonthlyInfoList() {
		String sql = "select * from monthly_sender_info order by id ";
		return this.findBySql(sql);
	}
	
	public MonthlySenderInfoEntity getByEngineAndLanguage(int searchEngineId, int searchLanguageId) {
		
		StringBuffer sql = new StringBuffer();
		
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where search_engine_id = ? ");
		sql.append(" and search_language_id = ? ");
		
		return findObject(sql.toString(), searchEngineId, searchLanguageId);
		
	}

	public List<MonthlySenderInfoEntity> getByCountryName(String countryName) {
		String sql = "select * from monthly_sender_info where country_name=? ";
		return this.findBySql(sql, countryName);
	}

	public List<Map<String,String>> getConnect(String sql) {
		return this.queryForMapList(sql);
	}
}
