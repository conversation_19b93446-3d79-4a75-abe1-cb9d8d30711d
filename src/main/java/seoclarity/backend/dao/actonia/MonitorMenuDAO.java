
package seoclarity.backend.dao.actonia;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.MonitorMenu;

import java.util.List;

@Repository("monitorMenuDAO")
public class MonitorMenuDAO extends ActoniaBaseJdbcSupport<MonitorMenu> {

	@Override
	public String getTableName() {
		return "monitor_menu";
	}

	public List<MonitorMenu> queryAllMenu(){
		String sql = " select * from " + getTableName();
		sql += " where enabled = " + MonitorMenu.ENABLED;
		return findBySql(sql);
	}

	public void updateSort(MonitorMenu monitorMenu) {
		String sql = "update " + getTableName() + " set displayOrder = " + monitorMenu.getDisplayOrder() + " where id = " + monitorMenu.getId();
	this.executeUpdate(sql);
	}
}
