package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;

import java.util.List;
import java.util.Set;


@Repository
public class GscSummaryTopDAO extends ActoniaBaseJdbcSupport<GscEntity> {

    public String getTableName() {
        return "claritydb_gsc_summary_top ";
    }

    public GscEntity extractTotalByDomainMonth(int domainId, String startDateString, String endDateString){
        String sql = " select own_domain_id,sum(clicks) as clicks ,sum(impressions) as impressions,  ";
        sql += " round( sum(clicks)/sum(impressions)*100,2) as ctr, round(avg(position),2) as position ";
        sql += " from claritydb_gsc_summary_top ";
        sql += " where own_domain_id = " + domainId;
        sql += " and log_date>= "  + startDateString + " and log_date<= " + endDateString;
        System.out.println("===SQL:" + sql);
        return findObject(sql);
    }

    public List<GscEntity> getGSCData(Set<Integer> domainIds, int date) {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT COUNT(clicks) as clicksCnt , COUNT(impressions) as impCnt,own_domain_id ");
        sql.append(" from ").append(getTableName());
        sql.append(" where own_domain_id  in (0");
        for (Integer domainId : domainIds){
            sql.append(",");
            sql.append(domainId);
        }
        sql.append("  ) ");
        sql.append(" and log_date >=").append(date);
        sql.append(" group by own_domain_id ");
        return findBySql(sql.toString());
    }
}
