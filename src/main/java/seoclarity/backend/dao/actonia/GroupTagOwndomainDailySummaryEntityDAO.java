package seoclarity.backend.dao.actonia;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.GroupTagOwndomainDailySummaryEntity;

/**
 * <AUTHOR>
 * @date 2020-04-01
 * @path seoclarity.backend.dao.actonia.GroupTagOwndomainDailySummaryEntityDAO
 * 
 */
@Repository
public class GroupTagOwndomainDailySummaryEntityDAO extends ActoniaBaseJdbcSupport<GroupTagOwndomainDailySummaryEntity> {

	@Override
	public String getTableName() {
		return null;
	}
	
	public GroupTagOwndomainDailySummaryEntity getRankByOwnDomainIdAndGroupTagIdAndLogDate(int domainId, int tagId, Date logDate) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select id, ");
		sql.append(" total_keyword as totalKeyword, ");
		sql.append(" keyword_count_rank_top3 as keywordCountRankTop3, ");
		sql.append(" keyword_count_rank_top10 as keywordCountRankTop10, ");
		sql.append(" keyword_count_rank_top30 as keywordCountRankTop30, ");
		sql.append(" keyword_count_rank_top50 as keywordCountRankTop50, ");
		sql.append(" keyword_count_rank_top100 as keywordCountRankTop100, ");
		
		sql.append(" rank_in_page1 as rankInPage1, ");
		sql.append(" rank_in_page2 as rankInPage2, ");
		sql.append(" rank_in_page3 as rankInPage3, ");
		sql.append(" rank_in_page4 as rankInPage4, ");
		sql.append(" rank_in_page5 as rankInPage5, ");
		
		sql.append(" count_keyword_rank1 as countKeywordRank1, ");
		sql.append(" count_keyword_rank2 as countKeywordRank2, ");
		sql.append(" count_keyword_rank3 as countKeywordRank3, ");
		sql.append(" count_keyword_rank4 as countKeywordRank4, ");
		sql.append(" count_keyword_rank5 as countKeywordRank5, ");
		sql.append(" count_keyword_rank6 as countKeywordRank6, ");
		sql.append(" count_keyword_rank7 as countKeywordRank7, ");
		sql.append(" count_keyword_rank8 as countKeywordRank8, ");
		sql.append(" count_keyword_rank9 as countKeywordRank9, ");
		sql.append(" count_keyword_rank10 as countKeywordRank10, ");
		sql.append(" wtd_avg_rank, avg_rank, avg_search_volume_total ");
		
		sql.append("  from group_tag_owndomain_daily_summary_entity ");
		sql.append(" where own_domain_id=? and group_tag_id=? and log_date=? ");
		return findObject(sql.toString(), domainId, tagId, DateUtils.truncate(logDate, Calendar.DAY_OF_MONTH));
	}

}
