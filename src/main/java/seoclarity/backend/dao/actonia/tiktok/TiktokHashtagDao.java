package seoclarity.backend.dao.actonia.tiktok;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.tiktok.TiktokHashtagEntity;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public class TiktokHashtagDao extends ActoniaBaseJdbcSupport<TiktokHashtagEntity> {

    private static final String TABLE_NAME = "tiktok_hashtag";
    private static final String ID = "id";
    private static final String COUNTRY = "country";
    private static final String INDUSTRY = "industry";
    private static final String CRAWL_WEEK = "crawlWeek";
    private static final String CRAWL_DATE = "crawlDate";
    private static final String HASHTAG_ID = "hashtagId";
    private static final String HASHTAG_NAME = "hashtagName";
    private static final String IS_PROMOTED = "isPromoted";
    private static final String PERIOD = "period";
    private static final String TRENDING_TYPE = "trendingType";
    private static final String TREND = "trend";
    private static final String PUBLISH_CNT = "publishCnt";
    private static final String VIDEO_VIEWS = "videoViews";
    private static final String RANK_POS = "rankPos";
    private static final String RANK_DIFF = "rankDiff";
    private static final String RANK_DIFF_TYPE = "rankDiffType";
    private static final String CREATE_DATE = "createDate";

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public void batchInsert(List<TiktokHashtagEntity> tikTokHashtags) {
        final String sql = "insert into " + getTableName() + "("
                + COUNTRY + "," + INDUSTRY + "," + CRAWL_WEEK + "," + CRAWL_DATE
                + "," + HASHTAG_ID + "," + HASHTAG_NAME + "," + IS_PROMOTED
                + "," + PERIOD + "," + TRENDING_TYPE + "," + TREND
                + "," + PUBLISH_CNT + "," + VIDEO_VIEWS + "," + RANK_POS
                + "," + RANK_DIFF + "," + RANK_DIFF_TYPE + ") values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        // convert tikTokHashtags to List<Object[]>
        List<Object[]> args = tikTokHashtags.stream().map(tikTokHashtag -> new Object[]{
                tikTokHashtag.getCountry(),
                tikTokHashtag.getIndustry(),
                tikTokHashtag.getCrawlWeek(),
                tikTokHashtag.getCrawlDate(),
                tikTokHashtag.getHashtagId(),
                tikTokHashtag.getHashtagName(),
                tikTokHashtag.getIsPromoted(),
                tikTokHashtag.getPeriod(),
                tikTokHashtag.getTrendingType(),
                tikTokHashtag.getTrend(),
                tikTokHashtag.getPublishCnt(),
                tikTokHashtag.getVideoViews(),
                tikTokHashtag.getRankPos(),
                tikTokHashtag.getRankDiff(),
                tikTokHashtag.getRankDiffType()
        }).collect(Collectors.toList());
        super.autoRetryBatchInsert(sql, args);
    }
}
