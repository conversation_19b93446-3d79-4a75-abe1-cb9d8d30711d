package seoclarity.backend.dao.actonia;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.clarity360.SemanticSimilarityProject;

@Repository("semanticSimilarityProjectDAO")
public class SemanticSimilarityProjectDAO extends ActoniaBaseJdbcSupport<SemanticSimilarityProject> {

	public String getTableName() {
		return "t_semantic_similarity_project";
	}
	
	public List<SemanticSimilarityProject> getProjectList() {
		String sql = "select * from " + getTableName() + " where status = ? ";
		return findBySql(sql, SemanticSimilarityProject.STATUS_CREATE);
	}	
	

	public void updateStartProcess(Integer projectId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update ").append(getTableName()).append(" set status = ?, process_start_time = ?  where id = ? ");
		
		this.executeUpdate(sql.toString(), SemanticSimilarityProject.STATUS_PROCESSING, new Date(), projectId);
	}
	
	public void updateProcessDone(Integer projectId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update ").append(getTableName()).append(" set status = ?, process_end_time = ?  where id = ? ");
		
		this.executeUpdate(sql.toString(), SemanticSimilarityProject.STATUS_DONE, new Date(), projectId);
	}
}
