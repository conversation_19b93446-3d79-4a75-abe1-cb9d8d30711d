package seoclarity.backend.dao.actonia.list;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.list.CdbListEntity;
import seoclarity.backend.utils.FormatUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Repository
public class CdbListEntityDAO extends ActoniaBaseJdbcSupport<CdbListEntity> {


    @Override
    public String getTableName() {
        return "cdb_list";
    }

    public long insert(CdbListEntity entity) {

        Map<String, Object> values = new HashMap<String, Object>();
        values.put("enabled", entity.getEnabled());
        values.put("name", entity.getName());
        values.put("ownDomainId", entity.getOwnDomainId());
        values.put("listType", entity.getListType());
        values.put("createUser", entity.getCreateUser());
        values.put("createDate", FormatUtils.formatDateToYyyyMmDd(new Date()));

        return insertForLongId(values);
    }

    public CdbListEntity getListByName(int listType, int ownDomainId, String name){

        String sql = " select * from " + getTableName() + " where listType = ? and ownDomainId = ? and name = ? ";

        return findObject(sql,listType,ownDomainId,name);
    }

    public int deleteById(long id) {

        String sql = " delete from " + getTableName() + " where id = ? ";

        return executeUpdate(sql, id);
    }

}
