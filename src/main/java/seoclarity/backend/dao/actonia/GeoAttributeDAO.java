/**
 * 
 */
package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.GeoAttributeEntity;
import seoclarity.backend.entity.actonia.GroupDict;

@Repository
public class GeoAttributeDAO extends ActoniaBaseJdbcSupport<GeoAttributeEntity> {

	@Override
	public String getTableName() {
		return "dynamic_page_tag_geo_attributes";
	}
	
	/*
	 * 	
	private Integer id;
	private String dataType;
	private Long geoId;
	private String geoName;
	private String neighborhoodName;
	private String cityName;
	private String multiCityVicinityName;
	private String provinceStateName;
	private String countryName;
	private String continentName;
	private String superRegionName;
	private String geoTypeName;
	private String hotelType;
	private String brand;
	private String parentChain;
	private Float starRating;
	private String vrFlag;
	private String singleMultiUnitIndicator;
	 */
	public Map<String, Object> findFirstByDataTypeAndGeoid(String dataType, String geoId) {
		String sql = "select geo_name, neighborhood_name, city_name, multi_city_vicinity_name, province_state_name, "
				+ "    country_name, continent_name, super_region_name, geo_type_name, hotel_type, brand, parent_chain, "
				+ "    star_rating, vr_flag, single_multi_unit_indicator "
				+ " from dynamic_page_tag_geo_attributes where data_type = ? and geo_id = ? limit 1";
		
		System.out.println("sql : " + sql);
		System.out.println("dataType : " + dataType + ", geoId : " + geoId);
		List<Map<String, Object>> results = getJdbcTemplate().queryForList(sql.toString(), dataType, geoId);
		
		return CollectionUtils.isNotEmpty(results) ? results.get(0) : null;
	}
	
	 public void batchInsertHOTEL(Collection<GeoAttributeEntity> list) {
        String sql = "insert into " + getTableName() + " (data_type, geo_id, geo_name, neighborhood_name, city_name, multi_city_vicinity_name,"
        		+ " province_state_name, country_name, continent_name, super_region_name, geo_type_name, hotel_type, "
        		+ "brand, parent_chain, star_rating, vr_flag, single_multi_unit_indicator"
        		+ ") values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (GeoAttributeEntity entity : list) {
            batch.add(new Object[]{
                    entity.getDataType(),
                    entity.getGeoId(),
                    entity.getGeoName(),
                    entity.getNeighborhoodName(),
                    entity.getCityName(),

                    entity.getMultiCityVicinityName(),
                    entity.getProvinceStateName(),
                    entity.getCountryName(),
                    entity.getContinentName(),
                    entity.getSuperRegionName(),

                    entity.getGeoTypeName(),
                    entity.getHotelType(),
                    entity.getBrand(),
                    entity.getParentChain(),
                    entity.getStarRating(),
                    
                    entity.getVrFlag(),
                    entity.getSingleMultiUnitIndicator(),
            });
        }
        this.executeBatch(sql, batch);
    }
	
	 public void batchInsertGAIA(Collection<GeoAttributeEntity> list) {
        String sql = "insert into " + getTableName() + " (data_type, geo_id, geo_name, neighborhood_name, city_name, "
        		+ "multi_city_vicinity_name, province_state_name, country_name, continent_name, super_region_name, geo_type_name"
        		+ ") values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (GeoAttributeEntity entity : list) {
            batch.add(new Object[]{
                    entity.getDataType(),
                    entity.getGeoId(),
                    entity.getGeoName(),
                    entity.getNeighborhoodName(),
                    entity.getCityName(),

                    entity.getMultiCityVicinityName(),
                    entity.getProvinceStateName(),
                    entity.getCountryName(),
                    entity.getContinentName(),
                    entity.getSuperRegionName(),

                    entity.getGeoTypeName(),
            });
        }
        this.executeBatch(sql, batch);
    }
	
}
