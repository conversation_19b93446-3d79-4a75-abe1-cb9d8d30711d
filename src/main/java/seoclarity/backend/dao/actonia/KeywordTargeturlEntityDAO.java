package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.KeywordTargetUrlEntity;

import java.util.*;

@Repository
public class KeywordTargeturlEntityDAO extends ActoniaBaseJdbcSupport<KeywordTargetUrlEntity> {

	@Override
	public String getTableName() {
		return "t_keyword_targeturl";
	}



	public List<KeywordTargetUrlEntity> getKeywordNameByTargetUrlId(int domainId, List<Long> targetUrlIdList) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select k.id as keywordId, k.keyword_name, k.raw_keyword_name,u.id as targetUrlId,r.keyword_hash,r.url_hash ");
		sql.append(" from t_target_url u ");
		sql.append(" join t_keyword_targeturl r on r.target_url_id = u.id ");
		sql.append(" join t_keyword k on r.keyword_id = k.id and k.own_domain_id = ? ");
		sql.append(" where u.own_domain_id = ? ");
		sql.append(" and u.id in (" + StringUtils.join(targetUrlIdList, ",")).append(") ");
//		sql.append(" group by u.id ");
		sql.append(" LIMIT 10");
		System.out.println("===getKeywordNameByTargetUrlId:" + sql.toString());
		return findBySql(sql.toString(), domainId, domainId);
	}

	public List<KeywordTargetUrlEntity> getByUrlId(long targetUrlId) {
		String sql = "select * from t_keyword_targeturl where target_url_id = ? ";
		return findBySql(sql, targetUrlId);
	}

}