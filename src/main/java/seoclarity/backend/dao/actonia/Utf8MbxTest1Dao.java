package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.Utf8MbxTestEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class Utf8MbxTest1Dao extends ActoniaBaseJdbcSupport<Utf8MbxTestEntity>{

    @Override
    public String getTableName() {
        return "testutf8mb4_1";
    }

    public List<Utf8MbxTestEntity> getAll() {
        String sql = "select * from " + getTableName();
        return findBySql(sql);
    }

    public void insert(Utf8MbxTestEntity utf8MbxTestEntity) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("emoji", utf8MbxTestEntity.getEmoji());
        values.put("colMb3", utf8MbxTestEntity.getColMb3());
        this.insert(values);
    }
}
