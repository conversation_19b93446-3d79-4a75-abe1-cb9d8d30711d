package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.KeywordSuggest;
import seoclarity.backend.entity.actonia.OwnDomainEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class KeywordSuggestDAO extends ActoniaBaseJdbcSupport<KeywordSuggest> {

    @Override
    public String getTableName() {
        return "keyword_suggest";
    }

    public Long findFirstId(Date logDate) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select min(id) from " + this.getTableName())
                .append(" where log_date = ? ");
        return queryForLong(sql.toString(), logDate);
    }

    public List<KeywordSuggest> getKeywordSuggestListByLogDate(Date logDateStart, Date logDateEnd, Long startId, Integer BATCH_SIZE) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from " + this.getTableName() + " where 1=1 ");
        sql.append(" and id>=? ");
        sql.append(" and log_date>=? and log_date<=? ");
        sql.append(" order by id asc ");
        sql.append(" limit ?");
        List<KeywordSuggest> result = findBySql(sql.toString(), startId, logDateStart, logDateEnd, BATCH_SIZE);
        return result != null ? result : new ArrayList<KeywordSuggest>();
    }

    public List<KeywordSuggest> getKeywordSuggestListByLogDate(Date logDateStart, Date logDateEnd, Integer startId, Integer BATCH_SIZE, String beginID, String endID) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from " + this.getTableName() + " where 1=1 ");
        sql.append(" and id>=? and id <= ?");
        sql.append(" and id>=? ");
        sql.append(" and log_date>=? and log_date<=? ");
        sql.append(" order by id asc ");
        sql.append(" limit ?");
        List<KeywordSuggest> result = findBySql(sql.toString(), beginID, endID, startId, logDateStart, logDateEnd, BATCH_SIZE);
        return result != null ? result : new ArrayList<KeywordSuggest>();
    }
    public List<String> selectByOid(Integer oid) {
        StringBuffer sql = new StringBuffer();
        sql.append("select suggest_text as suggestText " +
                "  from " + this.getTableName());
        sql.append(" where own_domain_id = ").append(oid).append(
                "  order by own_domain_id ");
        List<String> result = queryForStringList(sql.toString());
        return result ;
    }

    public List<KeywordSuggest> selectByOid(List<Integer> oid) {
        StringBuffer sql = new StringBuffer();
        sql.append("select suggest_text as suggestText ,country_code as countryCode," +
                " own_domain_id as ownDomainId from " + this.getTableName());
        sql.append(" where own_domain_id in (").append(StringUtils.join(oid,",")).append(
        " ) order by own_domain_id ");
        List<KeywordSuggest> result = findBySql(sql.toString());
        return result ;
    }

    public List<KeywordSuggest> getKeywordByDomainList(List<Integer> domainIdList, List<Integer> engineIdList, List<Integer> languageIdList) {
        StringBuilder sql = new StringBuilder();
        sql.append("select distinct engineId as searchEngineId, ");
        sql.append("languageId as languageId, ks.suggest_text as suggestText ");
        sql.append("from keyword_suggest ks ");
        sql.append("join t_own_domain tod on ks.own_domain_id=tod.id ");
        sql.append("join engine_country_language_mapping mapp on tod.search_engine_country=mapp.countryQueryName ");
        sql.append("and tod.search_engine=mapp.engineQueryName and tod.language=mapp.languageQueryName ");
        sql.append("and tod.rank_from=mapp.rankFrom ");
        sql.append("where tod.status = ? ");
        sql.append("and ks.own_domain_id in(").append(StringUtils.join(domainIdList, ",")).append(") ");
        sql.append("and mapp.engineId in(").append(StringUtils.join(engineIdList, ",")).append(") ");
        sql.append("and mapp.languageId in(").append(StringUtils.join(languageIdList, ",")).append(") ");
        return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
    }
}
