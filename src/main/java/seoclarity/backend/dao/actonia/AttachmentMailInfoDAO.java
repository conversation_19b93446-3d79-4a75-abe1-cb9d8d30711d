package seoclarity.backend.dao.actonia;


import java.util.List;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.AttachmentMailInfoEntity;
@Repository
public class AttachmentMailInfoDAO extends ActoniaBaseJdbcSupport<AttachmentMailInfoEntity> {

    public String getTableName() {
        return "attachment_mail_info";
    }

    public List<AttachmentMailInfoEntity> getAllMailInfo() {
        String sql = "select * from " + this.getTableName();
        return findBySql(sql);
    }
    
    public AttachmentMailInfoEntity getByAttachmentId(int attachmentId) {
    	StringBuffer sql = new StringBuffer();
        sql.append(" select ami.* from attachment_infos ai join "+getTableName()+" ami on ai.mail_info_id=ami.id");
        sql.append("  where ai.enabled=1 and ai.id=? ");// attachment_infos_test20220515

        return this.findObject(sql.toString(), attachmentId);
    }
}