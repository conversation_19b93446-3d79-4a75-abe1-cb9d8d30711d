package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import seoclarity.backend.entity.actonia.RankQcStateEntity;

@Repository
public class RankQcStateEntityDAO extends ActoniaBaseJdbcSupport<RankQcStateEntity> {

    public String getTableName() {
        return "rank_qc_state";
    }

	 public List<Integer> getNgDomainId(int rankDate, List<Integer> ownDomainIdList, Integer engineId,
			 Integer languageId, Integer frequence) {

	        StringBuffer sql = new StringBuffer();
	        /**
	         * 	sql.append(" (rankDate, ownDomainId, engineId, languageId,  ");
				sql.append(" device, rankType, frequence, keywordCount, ");
				sql.append(" rankedCount, status, startProcessDate, serverIp, createdAt ) ");
					private Integer engineId;
	private Integer languageId;
	private String device;
	private Integer rankType;
	         */
	        
	        if(CollectionUtils.isEmpty(ownDomainIdList) || ownDomainIdList.size() == 0) {
	        	return new ArrayList<Integer>();
	        }
	        
	        String ownDomainIdStr = getIntegerListQueryParam(ownDomainIdList);

	        sql.append(" SELECT   ");
	        sql.append("      ownDomainId ");
	        sql.append("        from   " + getTableName() + " ");
	        sql.append("        WHERE rankDate= ? ");
	        sql.append("    and status in (" + RankQcStateEntity.STATUS_PROCESSING + ", " + RankQcStateEntity.STATUS_ERROR + ", " + RankQcStateEntity.STATUS_MISSING_DATA + ", " + RankQcStateEntity.STATUS_NO_RANK_DATA + ")");
	        sql.append("    and ownDomainId in (" + ownDomainIdStr + ")");
	        sql.append("    and engineId = " + engineId);
	        sql.append("    and languageId = " + languageId);
	        sql.append("    and frequence = " + frequence);
//	        sql.append("    and device = '" + device + "' ");
//	        sql.append("    and rankType = " + rankType);
	        
	        System.out.println(sql.toString());

	        List<Integer> result = this.queryForIntegerList(sql.toString(), rankDate) ;
	        return result;
	    }



}
