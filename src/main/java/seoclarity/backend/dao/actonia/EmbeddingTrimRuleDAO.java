package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.EmbeddingTrimRuleEntity;

@Repository("embeddingTrimRuleDAO")
public class EmbeddingTrimRuleDAO extends ActoniaBaseJdbcSupport<EmbeddingTrimRuleEntity> {
    @Override
    public String getTableName() {
        return "embedding_trim_rules";
    }

    public void insertBySql(String sql) {
        System.out.println("===insertBySql:" + sql);
        this.executeUpdate(sql);
    }
}
