/**
 *
 */
package seoclarity.backend.dao;

import java.math.BigInteger;
import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.sql.DataSource;

import org.apache.commons.lang.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.support.DaoSupport;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.jdbc.support.SQLExceptionTranslator;

import seoclarity.backend.utils.GenericsUtils;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class BaseJdbcSupport<T> extends DaoSupport {

	private SimpleJdbcInsert simpleJdbcInsert;
	
//	@Resource(name="actoniaJdbcTemplate")
//    private JdbcTemplate jdbcTemplate;

	private String primaryKeyName;
	
	protected Class<T> entityClass;

	public BaseJdbcSupport() {
//		super();
        entityClass = GenericsUtils.getSuperClassGenricType(getClass());
	}

    protected Class<T> getEntityClass() {
        return entityClass;
    }

	public String getPrimaryKeyName() {
		if (StringUtils.isEmpty(primaryKeyName))
			primaryKeyName = "id";
		return primaryKeyName;
	}

	protected void initTemplateConfig() {
		simpleJdbcInsert = new SimpleJdbcInsert(getJdbcTemplate()).withTableName(getTableName())
				.usingGeneratedKeyColumns(getPrimaryKeyName());
	}

	/**
	 * @param parameters
	 * @return the new record's id, -1 if some error
	 */
	public int insert(Map<String, Object> parameters) {
		Number newId = simpleJdbcInsert.executeAndReturnKey(parameters);
		return newId == null ? -1 : newId.intValue();
	}
	public void insertWithOutKey(Map<String, Object> parameters) {
		simpleJdbcInsert.execute(parameters);
	}

	public long insertForLongId(Map<String, Object> parameters) {
		Number newId = simpleJdbcInsert.executeAndReturnKey(parameters);
		return newId == null ? -1 : newId.longValue();
	}

	public int insert(Map<String, Object> parameters, String tableName) {
		simpleJdbcInsert = new SimpleJdbcInsert(getJdbcTemplate()).withTableName(tableName)
				.usingGeneratedKeyColumns(getPrimaryKeyName());
		Number newId = simpleJdbcInsert.executeAndReturnKey(parameters);

		return newId == null ? -1 : newId.intValue();
	}

	public void insertWithoutAutoIncrementalKey(Map<String, Object> parameters) {
		simpleJdbcInsert.execute(parameters);
	}

	/**
	 * find first object by name = value
	 *
	 * @param name
	 * @param value
	 * @return null if not found
	 */
	protected final T findFirstBy(String name, Object value) {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT * FROM ").append(getTableName());
		sql.append(" WHERE ").append(name).append(" = ? ");
		sql.append(" LIMIT 1");

		List<T> result = getJdbcTemplate().query(sql.toString(),
				new BeanPropertyRowMapper<T>(entityClass), value);

		if (result != null && result.size() > 0) {
			return (T) result.get(0);
		}
		return null;
	}

	protected final List<T> findBySql(String sql, Object... args) {
		return getJdbcTemplate().query(sql, new BeanPropertyRowMapper<T>(entityClass),
				args);
	}

	protected final List findBySql(String sql,Class entityClass, Object... args) {
		return getJdbcTemplate().query(sql, new BeanPropertyRowMapper<T>(entityClass),
				args);
	}
	
	protected final T findObject(String sql, Object... args) {
		List<T> result = getJdbcTemplate().query(sql,
				new BeanPropertyRowMapper<T>(entityClass), args);
		if (result != null && result.size() > 0) {
			return (T) result.get(0);
		}
		return null;
	}

	protected String queryForString(String sql, Object... args) throws DataAccessException {
		String result = this.getJdbcTemplate().queryForObject(sql, args, String.class);
		return (result != null ? result : null);
	}

	protected List<String> queryForStringList(String sql, Object... args) throws DataAccessException {
		List result = this.getJdbcTemplate().queryForList(sql, args, String.class);
		return (result != null ? result : null);
	}

	protected List<Object[]> queryForObjectArrayList(String sql, Object... args) throws DataAccessException {
		List result = this.getJdbcTemplate().queryForList(sql, args, Object[].class);
		return result;
	}

	protected List queryForMapList(String sql, Object... args) throws DataAccessException {
		List result = this.getJdbcTemplate().queryForList(sql, args);
		return result;
	}

	protected List<Integer> queryForIntegerList(String sql, Object... args) throws DataAccessException {
		List result = this.getJdbcTemplate().queryForList(sql, args, Integer.class);
		return (result != null ? result : null);
	}

	protected Integer queryForInteger(String sql, Object... args) throws DataAccessException {
		List list = this.getJdbcTemplate().queryForList(sql, args, Integer.class);
		if (list != null && list.size() > 0) {
			Integer result = (Integer) list.get(0);
			return (result != null ? result.intValue() : null);
		}
		return null;
	}

	protected Date queryForDate(String sql, Object... args) throws DataAccessException {
		Date result = (Date) this.getJdbcTemplate().queryForObject(sql, args, Date.class);
		return (result != null ? result : null);
	}

	protected final int executeUpdate(String sql, Object... args) {
		return getJdbcTemplate().update(sql, args);
	}

	/**
	 * 1. pageNum start from 1 2. sql can't content limit
	 *
	 * @param sql
	 * @param pageSize
	 * @return
	 */
	protected List<T> queryPageForList(String sql, int pageSize, Object... args) {
		sql += " limit " + pageSize;
		return getJdbcTemplate().query(sql, new BeanPropertyRowMapper<T>(entityClass),
				args);
	}

	protected int[] executeBatch(String sql, List<Object[]> batchData) {
		int[] updateCounts = getJdbcTemplate().batchUpdate(sql, batchData);
		return updateCounts;

	}
	
	protected int[] executeBatch(String sql, List<Object[]> batchData, int[] argTypes) {
		int[] updateCounts = getJdbcTemplate().batchUpdate(sql, batchData, argTypes);
		return updateCounts;

	}

	protected Integer queryForInt(String sql, Object... args) throws DataAccessException {
//		getJdbcTemplate().queryForObject(sql, Integer.class, args);
		return this.getJdbcTemplate().queryForObject(sql, Integer.class, args);
	}

	protected Long queryForLong(String sql, Object... args) throws DataAccessException {
		List result = this.getJdbcTemplate().queryForList(sql, args, Long.class);
		if (result != null && result.size() > 0) {
			return (Long)result.get(0);
		}
		return null;
	}

	protected List<Long> queryForLongList(String sql, Object... args) throws DataAccessException {
		List result = this.getJdbcTemplate().queryForList(sql, args, Long.class);
		return (result != null ? result : null);
	}

	protected Integer queryForIntWithDate(String sql) throws DataAccessException {
//		return this.getJdbcTemplate().queryForInt(sql);
		return this.getJdbcTemplate().queryForObject(sql, Integer.class);
	}

	public abstract String getTableName();

	protected BigInteger queryForBigInteger(String sql, Object... args) throws DataAccessException {
		BigInteger result = (BigInteger) this.getJdbcTemplate().queryForObject(sql, args, BigInteger.class);
		return (result != null ? result : null);
	}

	protected String getIntegerListQueryParam(List<Integer> intList) {
		if (intList == null || intList.isEmpty()) {
			return "";
		}
		StringBuilder intStr = new StringBuilder();
		for (Integer i : intList) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");

		return intParams;
	}
	
	
	protected String getStringListQueryParam(List<String> intList) {
		if (intList == null || intList.isEmpty()) {
			return "";
		}
		String intParams = StringUtils.removeEndIgnoreCase(StringUtils.join(intList, ",").toString(), ",");

		return intParams;
	}
	
	protected String getIntegerListQueryParam(Set<String> intList) {
		if (intList == null || intList.isEmpty()) {
			return "";
		}
		StringBuilder intStr = new StringBuilder();
		for (String i : intList) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");

		return intParams;
	}

	/**
	 * Create a JdbcTemplate for the given DataSource.
	 * Only invoked if populating the DAO with a DataSource reference!
	 * <p>Can be overridden in subclasses to provide a JdbcTemplate instance
	 * with different configuration, or a custom JdbcTemplate subclass.
	 * @param dataSource the JDBC DataSource to create a JdbcTemplate for
	 * @return the new JdbcTemplate instance
	 * @see #setDataSource
	 */
	protected JdbcTemplate createJdbcTemplate(DataSource dataSource) {
		return new JdbcTemplate(dataSource);
	}

	/**
	 * Return the JDBC DataSource used by this DAO.
	 */
	public abstract DataSource getDataSource();

	/**
	 * Return the JdbcTemplate for this DAO,
	 * pre-initialized with the DataSource or set explicitly.
	 */
	public final JdbcTemplate getJdbcTemplate() {
		
	  return createJdbcTemplate(getDataSource());
	}

	/**
	 * Initialize the template-based configuration of this DAO.
	 * Called after a new JdbcTemplate has been set, either directly
	 * or through a DataSource.
	 * <p>This implementation is empty. Subclasses may override this
	 * to configure further objects based on the JdbcTemplate.
	 * @see #getJdbcTemplate()
	 */
//	protected void initTemplateConfig() {
//	}

	@Override
	protected void checkDaoConfig() {
		if (this.getDataSource() == null) {
			throw new IllegalArgumentException("'dataSource' is required");
		}
		initTemplateConfig();
	}


	/**
	 * Return the SQLExceptionTranslator of this DAO's JdbcTemplate,
	 * for translating SQLExceptions in custom JDBC access code.
	 * @see org.springframework.jdbc.core.JdbcTemplate#getExceptionTranslator()
	 */
	protected final SQLExceptionTranslator getExceptionTranslator() {
		return getJdbcTemplate().getExceptionTranslator();
	}

	/**
	 * Get a JDBC Connection, either from the current transaction or a new one.
	 * @return the JDBC Connection
	 * @throws CannotGetJdbcConnectionException if the attempt to get a Connection failed
	 * @see org.springframework.jdbc.datasource.DataSourceUtils#getConnection(javax.sql.DataSource)
	 */
	protected final Connection getConnection() throws CannotGetJdbcConnectionException {
		return DataSourceUtils.getConnection(getDataSource());
	}

	/**
	 * Close the given JDBC Connection, created via this DAO's DataSource,
	 * if it isn't bound to the thread.
	 * @param con Connection to close
	 * @see org.springframework.jdbc.datasource.DataSourceUtils#releaseConnection
	 */
	protected final void releaseConnection(Connection con) {
		DataSourceUtils.releaseConnection(con, getDataSource());
	}

	protected void autoRetryBatchInsert(String sql, List<Object[]> batch) {
		int retryCount = 1;
		int maxRetryCount = 10;
		while (true) {
			try {
				executeBatch(sql, batch);
				break;
			} catch (Exception e) {
				e.printStackTrace();
				if ((StringUtils.containsIgnoreCase(e.getMessage(), "Connection timed out") || StringUtils.containsIgnoreCase(e.getMessage(), "failed to respond") || StringUtils.containsIgnoreCase(e.getMessage(), "Broken pipe")) &&  retryCount <= maxRetryCount) {
					retryCount++;
					System.out.println("Upload data error, will try after 5 seconds. error time : "+retryCount);
					try {
						Thread.sleep(5 * 1000);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				} else {
					throw e;
				}
			}
		}
	}

	protected int[] executeBatch(List<String> sqlList) {
		return getJdbcTemplate().batchUpdate(sqlList.toArray(new String[0]));
	}

}
