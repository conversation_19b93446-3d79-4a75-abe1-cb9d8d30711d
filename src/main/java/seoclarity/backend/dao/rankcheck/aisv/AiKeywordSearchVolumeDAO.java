package seoclarity.backend.dao.rankcheck.aisv;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.rankcheck.RankCheckBaseJdbcSupport;
import seoclarity.backend.entity.rankcheck.aisv.AiKeywordSearchVolumeEntity;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Repository
public class AiKeywordSearchVolumeDAO extends RankCheckBaseJdbcSupport<AiKeywordSearchVolumeEntity> {

    @Override
    public String getTableName() {
        return "ai_keyword_search_volume";
    }

    public int insert(AiKeywordSearchVolumeEntity entity) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("languageId", entity.getLanguageId());
        values.put("keywordId", entity.getKeywordId());
        values.put("sv", entity.getSv());
        values.put("createDate", new Date());
        return this.insert(values);
    }

}
