package seoclarity.backend.dao.rankcheck.retrievesv;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.rankcheck.RankCheckBaseJdbcSupport;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsExpandedEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsRawEntity;
import seoclarity.backend.utils.FormatUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;


@Repository
public class KeywordAdwordsExpandedDAO extends RankCheckBaseJdbcSupport<KeywordAdwordsExpandedEntity> {

    @Override
    public String getTableName() {
        return "keyword_adwords_expanded";
    }

    public void insertForBatch(List<KeywordAdwordsExpandedEntity> insertData) {
        String sql = "INSERT ignore INTO keyword_adwords_expanded";
        sql += "(search_engine_id,language_id,keyword_id,city_id,month,avg_monthly_search_volume,monthly_search_volume,cost_per_click,monthly_relationship_create_date) ";
        sql += " VALUES (?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordAdwordsExpandedEntity enitity : insertData) {
            Object[] values = new Object[]{
                    enitity.getSearchEngineId(),
                    enitity.getLanguageId(),
                    enitity.getKeywordId(),
                    enitity.getCityId(),
                    enitity.getMonth(),
                    enitity.getAvgMonthlySearchVolume(),
                    enitity.getMonthlySearchVolume(),
                    enitity.getCostPerClick(),
                    enitity.getMonthlyRelationshipCreateDate()
            };
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    public void insertRefreshTableForBatch(List<KeywordAdwordsExpandedEntity> insertData) {
        String sql = "INSERT ignore INTO keyword_adwords_expandedNew ";
        sql += "(search_engine_id,language_id,keyword_id,city_id,month,avg_monthly_search_volume,monthly_search_volume,cost_per_click,monthly_relationship_create_date) ";
        sql += " VALUES (?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordAdwordsExpandedEntity enitity : insertData) {
            Object[] values = new Object[]{
                    enitity.getSearchEngineId(),
                    enitity.getLanguageId(),
                    enitity.getKeywordId(),
                    enitity.getCityId(),
                    enitity.getMonth(),
                    enitity.getAvgMonthlySearchVolume(),
                    enitity.getMonthlySearchVolume(),
                    enitity.getCostPerClick(),
                    enitity.getMonthlyRelationshipCreateDate()
            };
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    public void insertForBatchForTest(List<KeywordAdwordsExpandedEntity> insertData) {
        String setbinglogSql = " SET sql_log_bin = 0 ";
        this.executeUpdate(setbinglogSql);
        String sql = "INSERT ignore INTO keyword_adwords_expandedNew";
        sql += "(search_engine_id,language_id,keyword_id,city_id,month,avg_monthly_search_volume,monthly_search_volume,cost_per_click,monthly_relationship_create_date) ";
        sql += " VALUES (?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordAdwordsExpandedEntity enitity : insertData) {
            Object[] values = new Object[]{
                    enitity.getSearchEngineId(),
                    enitity.getLanguageId(),
                    enitity.getKeywordId(),
                    enitity.getCityId(),
                    enitity.getMonth(),
                    enitity.getAvgMonthlySearchVolume(),
                    enitity.getMonthlySearchVolume(),
                    enitity.getCostPerClick(),
                    enitity.getMonthlyRelationshipCreateDate()
            };
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    public void batchUpdateMonthlyRelCreateDate(List<KeywordAdwordsExpandedEntity> list) {
        StringBuffer sql = new StringBuffer();
        sql.append("update ").append(getTableName()).append(" set monthly_relationship_create_date=? ");
        sql.append(" where keyword_id = ? and search_engine_id=? and language_id= ? and city_id = ? ");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordAdwordsExpandedEntity entity : list) {
            Object[] values = new Object[]{
                    entity.getMonthlyRelationshipCreateDate(),
                    entity.getKeywordId(),
                    entity.getSearchEngineId(),
                    entity.getLanguageId(),
                    entity.getCityId()
            };
            batch.add(values);
        }
        executeBatch(sql.toString(), batch);
    }

    public void batchUpdateExpandSv(List<KeywordAdwordsExpandedEntity> list) {
        StringBuffer sql = new StringBuffer();
        sql.append("update ").append(getTableName()).append(" set avg_monthly_search_volume=?,monthly_search_volume=?,cost_per_click=? ");
        sql.append(" where search_engine_id=? and language_id= ? and keyword_id = ? and city_id = ? and month =? ");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordAdwordsExpandedEntity entity : list) {
            Object[] values = new Object[]{
                    entity.getAvgMonthlySearchVolume(),
                    entity.getMonthlySearchVolume(),
                    entity.getCostPerClick(),
                    entity.getSearchEngineId(),
                    entity.getLanguageId(),
                    entity.getKeywordId(),
                    entity.getCityId(),
                    entity.getMonth()
            };
            batch.add(values);
        }
        executeBatch(sql.toString(), batch);
    }

    public List<KeywordAdwordsExpandedEntity> getExistExpandSv(int engineId, int languageId, boolean isCity, Collection keywordIdList){
        int startMonth = Integer.parseInt(FormatUtils.formatDate(DateUtils.addMonths(new Date(), -49), "yyyyMM"));
        String sql = " select search_engine_id,language_id,keyword_id,city_id,month,avg_monthly_search_volume,monthly_search_volume,cost_per_click from " + getTableName();
        sql += " force index (uidx_engine_language_keyword_city_month) ";
        sql += " where search_engine_id=? and language_id = ? ";
        sql += " and keyword_id in(" + StringUtils.join(keywordIdList, ",") + ") ";
        if(isCity){
            sql += " and city_id >0 ";
        }else {
            sql += " and city_id =0 ";
        }
        sql += " and month>= " + startMonth;
        System.out.println("==getExistExpandSv:" + sql + ",engineId:" + engineId + ",languageId:" + languageId);
        return findBySql(sql, engineId, languageId);
    }

    public List<KeywordAdwordsExpandedEntity> getExistExpandSvForCopy(int engineId, int languageId, boolean isCity, List<Integer> keywordIdList, int startMonth){
        String sql = " select search_engine_id,language_id,keyword_id,city_id,month,avg_monthly_search_volume," +
                "monthly_search_volume,cost_per_click,monthly_relationship_create_date from " + getTableName();
        sql += " force index (uidx_engine_language_keyword_city_month) ";
        sql += " where search_engine_id=? and language_id = ? ";
        sql += " and keyword_id in(" + StringUtils.join(keywordIdList, ",") + ") ";
        if(isCity){
            sql += " and city_id >0 ";
        }else {
            sql += " and city_id =0 ";
        }
        sql += " and month>= ? ";
        sql += " order by month desc ";
        System.out.println("==getExistExpandSv:" + sql + ",engineId:" + engineId + ",languageId:" + languageId + ",startMonth:" + startMonth);
        return findBySql(sql, engineId, languageId, startMonth);
    }


    public void insertForBatchByRaw(List<KeywordAdwordsRawEntity> insertData) {
        String sql = "INSERT ignore INTO keyword_adwords_expanded";
        sql += "(search_engine_id,language_id,keyword_id,city_id,month,avg_monthly_search_volume,monthly_search_volume,cost_per_click,monthly_relationship_create_date) ";
        sql += " VALUES (?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordAdwordsRawEntity entity : insertData) {
            Object[] values = new Object[]{
                    entity.getSearchEngineId(),
                    entity.getLanguageId(),
                    entity.getKeywordId(),
                    entity.getCityId(),
                    entity.getMonth(),
                    entity.getAvgMonthlySearchVolume(),
                    entity.getSv(),
                    entity.getCostPerClick(),
                    entity.getMonthlyRelationshipCreateDate()
            };
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    public void batchUpdateExpandSvByRaw(List<KeywordAdwordsRawEntity> list) {
        StringBuffer sql = new StringBuffer();
        sql.append("update ").append(getTableName()).append(" set avg_monthly_search_volume=?,monthly_search_volume=?,cost_per_click=? ");
        sql.append(" where search_engine_id=? and language_id= ? and keyword_id = ? and city_id = ? and month =? ");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordAdwordsRawEntity entity : list) {
            Object[] values = new Object[]{
                    entity.getAvgMonthlySearchVolume(),
                    entity.getSv(),
                    entity.getCostPerClick(),
                    entity.getSearchEngineId(),
                    entity.getLanguageId(),
                    entity.getKeywordId(),
                    entity.getCityId(),
                    entity.getMonth()
            };
            batch.add(values);
        }
        executeBatch(sql.toString(), batch);
    }

    public List<KeywordAdwordsExpandedEntity> getExistExpandSvFromNew(int engineId, int languageId, int cityId, int KeywordId){
        String sql = " select search_engine_id,language_id,keyword_id,city_id,month,avg_monthly_search_volume,monthly_search_volume,cost_per_click " +
                " from keyword_adwords_expandedNew ";
        sql += " force index (uidx_engine_language_keyword_city_month) ";
        sql += " where search_engine_id=? and language_id = ? ";
        sql += " and keyword_id = ? and city_id = ? ";
        System.out.println("==getExistExpandSvFromNew:" + sql + ",engineId:" + engineId + ",languageId:" + languageId + ",KeywordId:" + KeywordId);
        return findBySql(sql, engineId, languageId, KeywordId, cityId);
    }
    public int deleteByKeywordLanguageCity(int keywordId, int languageId, int cityId) {
        String sql = "delete from keyword_adwords_expandedNew where search_engine_id=99 and keyword_id=? and language_id = ? and city_id=? ";
        return this.executeUpdate(sql, keywordId, languageId, cityId);
    }

}
