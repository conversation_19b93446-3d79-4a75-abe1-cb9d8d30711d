package seoclarity.backend.dao.rankcheck.retrievesv;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.rankcheck.RankCheckBaseJdbcSupport;
import seoclarity.backend.entity.rankcheck.retrievesv.SvRetrieveStateEntity;

import java.util.ArrayList;
import java.util.List;


@Repository
public class SvRetrieveStateDAO extends RankCheckBaseJdbcSupport<SvRetrieveStateEntity> {

    @Override
    public String getTableName() {
        return "sv_retreive_state";
    }

    public void insertForBatch(List<SvRetrieveStateEntity> insertData) {
        String sql = "INSERT ignore INTO " + getTableName();
        sql += "(refreshDate,keywordType,languageId,keywordId,cityId,status) ";
        sql += " VALUES (?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (SvRetrieveStateEntity enitity : insertData) {
            Object[] values = new Object[]{
                    enitity.getRefreshDate(),
                    enitity.getKeywordType(),
                    enitity.getLanguageId(),
                    enitity.getKeywordId(),
                    enitity.getCityId(),
                    enitity.getStatus()
            };
            batch.add(values);
        }
        executeBatch(sql, batch);
    }


}
