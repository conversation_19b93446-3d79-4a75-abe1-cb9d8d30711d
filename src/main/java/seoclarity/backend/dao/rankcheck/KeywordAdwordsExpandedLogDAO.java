package seoclarity.backend.dao.rankcheck;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsExpandedLogEntity;

/**
 * @program: backend
 * @description: KeywordAdwordsExpandedLogEntityDAO
 * @packagename: seoclarity.backend.dao.rankcheck
 * @author: cil
 * @date: 2021-06-21 15:27
 **/@Repository
public class KeywordAdwordsExpandedLogDAO extends RankCheckBaseJdbcSupport<KeywordAdwordsExpandedLogEntity> {

    @Override
    public String getTableName() {
        return "keyword_adwords_expanded_log";
    }

    public Long getMaxLogId() {
        StringBuilder sql = new StringBuilder();
        sql.append("select max(id) from keyword_adwords_expanded_log");
        return queryForLong(sql.toString());
    }





}
