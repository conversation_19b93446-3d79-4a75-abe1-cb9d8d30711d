package seoclarity.backend.dao.rankcheck;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.AdwordsGeoIdTableEntity;

import java.util.ArrayList;
import java.util.List;

@Repository
public class AdwordsGeoIdDAO extends RankCheckBaseJdbcSupport<AdwordsGeoIdTableEntity> {

    @Override
    public String getTableName() {
        return "adwords_geo_id_table";
    }
    
    /**
     * 	private Integer id;
		private Integer criteriaId;
		private String name;
		private String canonicalName;
		private Integer parentId;
		private String countryCode;
		private String targetType;
		private String status;
		private Date createDate;
     * @param insertData
     */
    public void insertForBatch(List<AdwordsGeoIdTableEntity> insertData) {
        String sql = "insert into " + getTableName() + "(criteriaId, name, canonicalName, parentId, countryCode, targetType, status) values(?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (AdwordsGeoIdTableEntity adwordsGeoIdTableEntity : insertData) {
            Object[] values = new Object[]{adwordsGeoIdTableEntity.getCriteriaId(), adwordsGeoIdTableEntity.getName(), adwordsGeoIdTableEntity.getCanonicalName(), adwordsGeoIdTableEntity.getParentId(), 
            		adwordsGeoIdTableEntity.getCountryCode(), adwordsGeoIdTableEntity.getTargetType(), adwordsGeoIdTableEntity.getStatus()};
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    
    
    public List<AdwordsGeoIdTableEntity> findAdwordsEntitiesByMetrics(String targetType, String countryCode, String name){
    	
    	if (StringUtils.contains(name, "'")) {
    		name = StringUtils.replace(name, "'", "\\'");
		}
    	
    	String sql = "select * from " + getTableName() + " where targetType = ? and countryCode = ? and (name =  '" + name + "' or name like  '" + name + " %' )";
		return findBySql(sql, targetType, countryCode);
    }
    
    
    
    public List<AdwordsGeoIdTableEntity> findAdwordsEntitiesByMetricsLevel2(String targetType, String countryCode, String name, String canonicalName){
    	
    	if (StringUtils.contains(name, "'")) {
    		name = StringUtils.replace(name, "'", "\\'");
		}
    	
    	if (StringUtils.contains(canonicalName, "'")) {
    		canonicalName = StringUtils.replace(canonicalName, "'", "\\'");
		}
    	
    	String sql = "select * from " + getTableName() + " where targetType = ? and countryCode = ? and (name =  '" + name + "' or name like  '" + name + " %' ) and canonicalName like '%" + canonicalName + "%'";
		return findBySql(sql, targetType, countryCode);
    }
    
    public List<AdwordsGeoIdTableEntity> findAdwordsEntities(String countryCode, String name, String stateName, String cityNameLevel1, String cityNameLevel2){
    	
    	if (StringUtils.isBlank(stateName)) {
			System.out.println("=========== stateName is empty!!!!");
			return null;
    	}
    	
    	if (StringUtils.contains(name, "'")) {
    		name = StringUtils.replace(name, "'", "\\'");
		}
    	
    	if (StringUtils.contains(cityNameLevel1, "'")) {
    		cityNameLevel1 = StringUtils.replace(cityNameLevel1, "'", "\\'");
		}
    	
    	if (StringUtils.contains(cityNameLevel2, "'")) {
    		cityNameLevel2 = StringUtils.replace(cityNameLevel2, "'", "\\'");
		}
    	
    	String sql = "select * from " + getTableName() + " where countryCode = ? and targetType in('City', 'Country', 'County' , 'State') and ((name = ? and canonicalName like '" + name + "," + stateName + ",%') or ( name = ? and canonicalName like '" + cityNameLevel1 + "," + stateName + ",%' ) or ( name = ? and canonicalName like '" + cityNameLevel2 + "," + stateName + ",%' )) ";
		
    	System.out.println(sql + " " + countryCode + "_" + name + "_" + cityNameLevel1 + "_" + cityNameLevel2);
    	return findBySql(sql, countryCode, name, cityNameLevel1, cityNameLevel2);
    }
    
    public List<AdwordsGeoIdTableEntity> findAdwordsEntitiesStep2(String countryCode, String name, String stateName){
    	
    	if (StringUtils.isBlank(stateName)) {
			System.out.println("=========== stateName is empty!!!!");
			return null;
    	}
    	
    	if (StringUtils.contains(name, "'")) {
    		name = StringUtils.replace(name, "'", "\\'");
		}
    	
    	String sql = "select * from " + getTableName() + " where countryCode = ? and targetType in('City', 'Country', 'County' , 'State') and ((name = ? and canonicalName like '" + name + "," + stateName + ",%') )  ";
		
    	System.out.println(sql + " " + countryCode + "_" + name );
    	return findBySql(sql, countryCode, name);
    }


	public AdwordsGeoIdTableEntity findCriteriaByCountryCode(String countryCode){

    	String sql = " select countryCode,criteriaId,canonicalName from " + getTableName() + " where  countryCode =?  and Status = 'Active' and parentId=0";
		if(countryCode.equalsIgnoreCase("TW")){
			sql += " and targetType = 'Region' ";//taiwan
		}else if(countryCode.equalsIgnoreCase("PR")){//https://www.wrike.com/open.htm?id=1325376618
			sql += " and targetType = 'Region' ";//taiwan
		}else {
			sql += " and targetType = 'country' ";
		}

    	return findObject(sql,countryCode);
	}

	public AdwordsGeoIdTableEntity findByCountry(String countryCode){

		String sql = "select * from adwords_geo_id_table where targetType='Country' and countryCode = ? ;  ";


		return findObject(sql,countryCode);
	}
   
}
