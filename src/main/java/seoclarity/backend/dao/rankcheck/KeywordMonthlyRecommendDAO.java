package seoclarity.backend.dao.rankcheck;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.rankcheck.KeywordMonthlyRecommend;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class KeywordMonthlyRecommendDAO extends RankCheckBaseJdbcSupport<KeywordMonthlyRecommend> {

    @Override
    public String getTableName() {
        return "keyword_monthly_recommend";
    }

    public void insert(KeywordMonthlyRecommend keywordMonthlyRecommend) {

        StringBuilder sql = new StringBuilder();

        sql.append("insert into ").append(getTableName()).append(" (search_engine_id, search_language_id, rankcheck_id) values (?, ?, ?)");

        this.executeUpdate(sql.toString(), keywordMonthlyRecommend.getSearchEngineId(), keywordMonthlyRecommend.getSearchLanguageId(), keywordMonthlyRecommend.getRankcheckId());

    }

    public List<KeywordMonthlyRecommend> getByRankCheckNotIn(List<Integer> rankCheckIdList) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from " + this.getTableName());
        sql.append(" where rankCheck_id in ( ").append(this.getIntegerListQueryParam(rankCheckIdList)).append(" ) ");
        List<KeywordMonthlyRecommend> result = findBySql(sql.toString());
        return result != null ? result : new ArrayList<KeywordMonthlyRecommend>();
    }

    public List<Integer> checkExists(List<Integer> kidList, int engine, int language) {
        if (kidList.isEmpty()) {
            return new ArrayList<>();
        }
        String sql = "select rankcheck_id from " + getTableName() + " where search_engine_id = ? and search_language_id = ? and rankcheck_id in (" + StringUtils.join(kidList, ',') + ") ";
        return this.queryForIntegerList(sql, engine, language);
    }

    public void insertBatch(List<KeywordMonthlyRecommend> recommendList) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("insert into ").append(getTableName())
                .append(" (search_engine_id, search_language_id, rankcheck_id, child_keyword_hash, child_keyword_stream, parent_keyword_rankcheck_id, parent_keyword_hash, create_date)")
                .append(" values(?,?,?,?,?,?,?,?)");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordMonthlyRecommend recommend : recommendList) {
            Object[] values = new Object[]{recommend.getSearchEngineId(), recommend.getSearchLanguageId(), recommend.getRankcheckId(), recommend.getChildKeywordHash(), recommend.getChildKeywordStream(), recommend.getParentKeywordRankcheckId(), recommend.getParentKeywordHash(), recommend.getCreateDate()};
            batch.add(values);
        }
        executeBatch(sqlBuilder.toString(), batch);
    }

    public void updateBatch(List<KeywordMonthlyRecommend> recommendList) {
        String sql = "update " + getTableName() + " set child_keyword_hash = ?, child_keyword_stream = ?, parent_keyword_rankcheck_id = ?, parent_keyword_hash = ?" +
                " where search_engine_id = ? and search_language_id = ? and rankcheck_id = ?";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordMonthlyRecommend recommend : recommendList) {
            Object[] values = new Object[]{recommend.getChildKeywordHash(), recommend.getChildKeywordStream(), recommend.getParentKeywordRankcheckId(), recommend.getParentKeywordHash(), recommend.getSearchEngineId(), recommend.getSearchLanguageId(), recommend.getRankcheckId()};
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    public void batchUpdateSeAndLangIdById(List<KeywordMonthlyRecommend> recommendList) {
        String sql = "update " + getTableName() + " set search_engine_id = ?, search_language_id = ? where id = ?";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordMonthlyRecommend recommend : recommendList) {
            Object[] values = new Object[]{recommend.getSearchEngineId(), recommend.getSearchLanguageId(), recommend.getId()};
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    public List<KeywordMonthlyRecommend> checkExists(int engine, int language, Collection<String> encodeKeywordNameList) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select rankcheck_id,keyword_text  ");
        sbd.append(" from ").append(getTableName()).append(" as kmr ");
        sbd.append(" join keyword_entity ke on kmr.rankcheck_id=ke.id ");
        sbd.append(" where search_engine_id = ? ");
        sbd.append(" and search_language_id =  ? ");
        sbd.append(" and keyword_text in ('").append(StringUtils.join(encodeKeywordNameList, "','")).append("')");
        return this.findBySql(sbd.toString(), engine, language);
    }

    public List<KeywordMonthlyRecommend> getAllParentKw(boolean isTest) {
        String sql = "select a.*, b.keyword_text as childKeywordName, c.keyword_text as parentKeywordName from keyword_monthly_recommend a join keyword_entity b on a.rankcheck_id = b.id join keyword_entity c on a.parent_keyword_rankcheck_id = c.id where a.parent_keyword_rankcheck_id != 0 and a.parent_keyword_rankcheck_id is not null ";
        if (isTest) {
            sql += " limit 10";
        }
        System.out.println("=====getAllParentKw:" + sql);
        return this.findBySql(sql);
    }

    public List<KeywordMonthlyRecommend> getRankCheckIdAndSeId(List<Integer> rankCheckIdList, int engine, int language) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from " + this.getTableName());
        sql.append(" where rankCheck_id in ( ").append(this.getIntegerListQueryParam(rankCheckIdList)).append(" ) ");
        sql.append(" and search_engine_id = ? ");
        sql.append(" and search_language_id = ? ");
        List<KeywordMonthlyRecommend> result = findBySql(sql.toString(), engine, language);
        return result != null ? result : new ArrayList<KeywordMonthlyRecommend>();
    }

    public int delete(List<Integer> idList, int engineId, int languageId) {
        String sql = "delete from " + this.getTableName() + " where rankcheck_id in (" + this.getIntegerListQueryParam(idList) + ")" + " and search_engine_id = ? and search_language_id = ?";
        return executeUpdate(sql, engineId, languageId);
    }

    public int deleteByIdList(List<Integer> idList) {
        String sql = "delete from " + this.getTableName() + " where id in (" + this.getIntegerListQueryParam(idList) + ")";
        return executeUpdate(sql);
    }

    public List<KeywordMonthlyRecommend> getHashIsZeroKwList(int limit) {
        String sql = "select * from " + this.getTableName() + " where child_keyword_hash = 0 ";
        if (limit > 0) {
            sql += " limit " + limit;
        }
        return this.findBySql(sql);
    }

    public void updateHashBatch(List<KeywordMonthlyRecommend> recommendList) {
        String sql = "update " + getTableName() + " set child_keyword_hash = ? " + " where id = ?";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (KeywordMonthlyRecommend recommend : recommendList) {
            Object[] values = new Object[]{recommend.getChildKeywordHash(), recommend.getId()};
            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    public Map<Integer, Integer> getRecommendByRankcheckIdAndEngineAndLanguage(List<Integer> kidList, int engine, int language) {
        if (kidList.isEmpty()) {
            System.out.println("===queryParam KwId is empty ");
            return null;
        }
        String sql = "select id, rankcheck_id as rankcheckId from " + getTableName() + " where search_engine_id = ? and search_language_id = ? and rankcheck_id in (" + StringUtils.join(kidList, ',') + ") ";
        List<KeywordMonthlyRecommend> recommendList = this.findBySql(sql, engine, language);
        if (recommendList == null || recommendList.isEmpty()) {
            System.out.println("===getRecommendByRankcheckIdAndEngineAndLanguage is empty kwId:" + kidList);
            return null;
        }
        return recommendList.stream().collect(Collectors.toMap(KeywordMonthlyRecommend::getRankcheckId, KeywordMonthlyRecommend::getId, (a, b) -> a));
    }
}

