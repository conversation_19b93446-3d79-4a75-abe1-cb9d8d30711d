package seoclarity.backend.dao.rankcheck;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.rankcheck.RgRealtimeExpansionEntity;
import seoclarity.backend.entity.rankcheck.RgRealtimeExpansionSeedKeywordEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository("realtimeExpansionSeedKeywordEntityDAO")
public class RgRealtimeExpansionSeedKeywordEntityDAO extends RankCheckBaseJdbcSupport<RgRealtimeExpansionSeedKeywordEntity> {
    @Override
    public String getTableName() {
        return "rg_realtime_expansion_seed_keyword";
    }

    public void batchInsert(List<RgRealtimeExpansionSeedKeywordEntity> realtimeExpansionSeedKwList) {
        String sql = "insert ignore into " + getTableName() + " (expansionId, seedKeyword, createdAt) values (?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (RgRealtimeExpansionSeedKeywordEntity entity : realtimeExpansionSeedKwList) {
            batch.add(new Object[]{
                    entity.getExpansionId(),
                    entity.getSeedKeyword(),
                    entity.getCreatedAt()
            });
        }
        this.executeBatch(sql, batch);
    }

    public int insert(RgRealtimeExpansionSeedKeywordEntity entity) {
        Map<String, Object> paramMap = new HashMap<>();

        paramMap.put("expansionId", entity.getExpansionId());
        paramMap.put("seedKeyword", entity.getSeedKeyword());
        paramMap.put("createdAt", entity.getCreatedAt());

        long longId = this.insertForLongId(paramMap);
        int id = 0;
        if (longId >= Integer.MIN_VALUE && longId <= Integer.MAX_VALUE) {
            id = (int) longId;
        }
        return id;
    }

    public List<RgRealtimeExpansionSeedKeywordEntity> getSeedKwListByKeywordList(int expansionId) {
        String sql = "select * from " + getTableName() + " where expansionId = ? ";
        return this.findBySql(sql, expansionId);
    }
}
