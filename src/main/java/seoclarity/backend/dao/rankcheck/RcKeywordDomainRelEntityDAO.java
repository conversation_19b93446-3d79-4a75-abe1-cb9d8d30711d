package seoclarity.backend.dao.rankcheck;


import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.rankcheck.RcKeywordDomainRelEntity;

import java.util.List;

@Repository
public class RcKeywordDomainRelEntityDAO extends RankCheckBaseJdbcSupport<RcKeywordDomainRelEntity> {
    @Override
    public String getTableName() {
        return "rc_keyword_domain_rel";
    }

    public List<Long> getKeywordListByDomain(int keywordType, int engineId, int languageId, int domainId, String device,
                                             long startId, int pageSize){

        String sql = " select t1.keywordId from rc_keyword_se_rel t1 ";
        sql += " join rc_keyword_domain_rel t2 on t1.id = t2.keywordSeRelId";
        sql += " where t1.keywordType = " + keywordType + " and searchEngineId = " + engineId + " and languageId = " + languageId;
        sql += " and t2.ownDomainId = " + domainId + " and device = '" + device + "' ";
        sql += " and t1.keywordId > " + startId;
        sql += " order by t1.keywordId limit " + pageSize;
        System.out.println("===getKeywordListByDomainSQL:" + sql);
        return queryForLongList(sql);
    }

}