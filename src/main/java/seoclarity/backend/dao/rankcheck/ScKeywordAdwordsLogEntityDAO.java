package seoclarity.backend.dao.rankcheck;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.rankcheck.ScKeywordAdwordsLogEntity;

import java.util.List;

@Repository
public class Sc<PERSON><PERSON>wordAdwordsLogEntityDAO extends RankCheckBaseJdbcSupport<ScKeywordAdwordsLogEntity> {

	@Override
	public String getTableName() {
		return "keyword_adwords_log";
	}

	public List<ScKeywordAdwordsLogEntity> getKeywordsFromMaxId(long maxId, int pageSize) {
		String sql = " select t1.id as logId,t1.operation_type,t2.* from keyword_adwords_log t1 ";
		sql += " inner join keyword_city_adwords_data_entity t2 on t1.adwords_id = t2.id ";
		sql += " where t1.id > ? order by t1.id ";
		return queryPageForList(sql, pageSize, maxId);
	}

}
