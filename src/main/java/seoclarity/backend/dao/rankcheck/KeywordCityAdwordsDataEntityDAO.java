/**
 * 
 */
package seoclarity.backend.dao.rankcheck;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.rankcheck.KeywordCityAdwordsDataEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordAdwordsEntity;

/**
 * com.actonia.subserver.dao.KeywordAdwordsDataEntityDAO.java
 *
 * @version $Revision: 7773 $
 *          $Author: floyd@SHINETECHCHINA $
 */
// https://www.wrike.com/open.htm?id=39597430
// by floyd
@Repository
public class KeywordCityAdwordsDataEntityDAO extends RankCheckBaseJdbcSupport<KeywordCityAdwordsDataEntity> {

	@Override
	public String getTableName() {
		return "keyword_city_adwords_data_entity";
	}

	public Integer getAvgSearchVolume(int keywordId, int languageId, int searchEngineId,int cityId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select avg_monthly_search_volume from keyword_city_adwords_data_entity ");
		sql.append("  where keyword_id = ?  and search_engine_id = ?  and language_id = ? and city_id = ? and avg_monthly_search_volume>=0");
		sql.append(" order by monthly_search_date desc limit 1");

		return this.queryForInteger(sql.toString(), keywordId, searchEngineId, languageId, cityId);
	}
	
	@SuppressWarnings("unchecked")
	public List<Map<String, Object>> getAvgSearchVolumeAndCPC(int keywordId, int languageId, int searchEngineId,int cityId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select keyword_id, avg_monthly_search_volume, cost_per_click from keyword_city_adwords_data_entity ");
		sql.append("  where keyword_id = ?  and search_engine_id = ?  and language_id = ? and city_id = ? and avg_monthly_search_volume>=0");
		sql.append(" order by monthly_search_date desc limit 1");
		
		return this.queryForMapList(sql.toString(), keywordId, searchEngineId, languageId, cityId);
	}
	
	public List<Map<String, Object>> getAvgSearchVolumeAndCPC(int keywordId, int languageId, int searchEngineId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select keyword_id, avg_monthly_search_volume, cost_per_click from keyword_city_adwords_data_entity ");
		sql.append("  where keyword_id = ?  and search_engine_id = ?  and language_id = ? and city_id = 0 and avg_monthly_search_volume>=0");
		sql.append(" order by monthly_search_date desc limit 1");
		
		return this.queryForMapList(sql.toString(), keywordId, searchEngineId, languageId);
	}

	public Integer getAvgSearchVolume(int keywordId, int languageId, int searchEngineId,int cityId, Date queryDate) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select avg_monthly_search_volume from keyword_city_adwords_data_entity ");
		sql.append("  where keyword_id = ?  and search_engine_id = ?  and language_id = ? and city_id = ? and avg_monthly_search_volume>=0");
		sql.append(" and Date(monthly_search_date) <= Date(?) ");
		sql.append(" order by monthly_search_date desc limit 1");

		return this.queryForInteger(sql.toString(), keywordId, searchEngineId, languageId, queryDate);
	}

	public KeywordCityAdwordsDataEntity getExistEntitiesByInfos(KeywordCityAdwordsDataEntity dataEntity){
		StringBuffer sql = new StringBuffer();
		sql.append(" Select * from keyword_city_adwords_data_entity  ");
		sql.append(" where keyword_id = ? ");
		sql.append(" and city_id = ? ");
		sql.append(" and language_id = ?" );
		sql.append(" and search_engine_id = ? ");
		sql.append(" and date_format(monthly_search_date, '%Y%m') = date_format(?, '%Y%m')  ");
		return findObject(sql.toString(), dataEntity.getKeywordId(), dataEntity.getCityId(), dataEntity.getLanguageId(),
				dataEntity.getEngineId(), dataEntity.getMonthlySearchDate());
	}

	public List<KeywordCityAdwordsDataEntity> getExistEntitiesByInfos(SeoClarityKeywordAdwordsEntity dataEntity){
		StringBuffer sql = new StringBuffer();
		sql.append(" Select * from keyword_city_adwords_data_entity  ");
		sql.append(" where keyword_id = ? ");
		sql.append(" and city_id = ? ");
		sql.append(" and language_id = ?" );
		sql.append(" and search_engine_id = ? ");
		return findBySql(sql.toString(), dataEntity.getKeywordId(), dataEntity.getCityId(), dataEntity.getLanguageId(), dataEntity.getSearchEngineId());
	}

	public void insert(KeywordCityAdwordsDataEntity enitity){
		String sql = "insert into " + getTableName() +
				"(keyword_id,city_id,monthly_search_volume,monthly_search_date,avg_monthly_search_volume,language_id,search_engine_id,upload_info_id,cost_per_click) " +
				"values(?,?,?,?,?,?,?,?,?)";
		executeUpdate(sql, enitity.getKeywordId(), enitity.getCityId(), enitity.getMonthlySearchVolume(), enitity.getMonthlySearchDate(),
				enitity.getAvgMonthlySearchVolume(), enitity.getLanguageId(),
				enitity.getEngineId(), enitity.getUploadInfoId(), enitity.getCostPerClick());
	}

	public void insertForBatch(List<KeywordCityAdwordsDataEntity> insertData) {
		String sql = "INSERT INTO " + getTableName();
		sql += "(keyword_id,city_id,avg_monthly_search_volume,monthly_search_volume,monthly_search_date,language_id,search_engine_id,upload_info_id,cost_per_click) ";
		sql += " VALUES (?,?,?,?,?,?,?,?,?)";
		List<Object[]> batch = new ArrayList<Object[]>();
		for (KeywordCityAdwordsDataEntity enitity : insertData) {
			Object[] values = new Object[]{
					enitity.getKeywordId(),
					enitity.getCityId(),
					enitity.getAvgMonthlySearchVolume(),
					enitity.getMonthlySearchDate(),
					enitity.getMonthlySearchVolume(),
					enitity.getLanguageId(),
					enitity.getEngineId(),
					enitity.getUploadInfoId(),
					enitity.getCostPerClick()
			};
			batch.add(values);
		}
		executeBatch(sql, batch);
	}

	public void updateEntity(KeywordCityAdwordsDataEntity dataEntity){
		StringBuffer sql = new StringBuffer();
		sql.append(" update keyword_city_adwords_data_entity set avg_monthly_search_volume = ? ");
		sql.append("  , monthly_search_volume = ? ");
		sql.append("  , cost_per_click =? ,upload_info_id=?");
		sql.append(" where id=?" );
		executeUpdate(sql.toString(), dataEntity.getAvgMonthlySearchVolume(), dataEntity.getMonthlySearchVolume(),
				dataEntity.getCostPerClick(), dataEntity.getUploadInfoId(), dataEntity.getId());
	}

	public List<KeywordCityAdwordsDataEntity> getCityKeywordAdwords(int keywordId, int searchEngineId, int languageId, int cityId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from keyword_city_adwords_data_entity ");
		sql.append("  where keyword_id = ?  and search_engine_id = ?  and language_id = ? and city_id = ? ");
		return findBySql(sql.toString(), keywordId, searchEngineId, languageId, cityId);
	}

	public List<KeywordCityAdwordsDataEntity> getCityKeywordAdwordsLatest(int keywordId, int searchEngineId, int languageId, int cityId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from keyword_city_adwords_data_entity ");
		sql.append("  where keyword_id = ?  and search_engine_id = ?  and language_id = ? and city_id = ? order by monthly_search_date desc limit 1");
		return findBySql(sql.toString(), keywordId, searchEngineId, languageId, cityId);
	}

	public int deleteCityAdwords(int keywordId, int searchEngineId, int languageId, int cityId){
		String sql = " delete from keyword_city_adwords_data_entity where keyword_id = " + keywordId + " and city_id=" + cityId
				+ " and search_engine_id = " + searchEngineId + " and language_id = " + languageId;
		System.out.println("====deleteCityAdwords:" + sql);
		return executeUpdate(sql);
	}

}