package seoclarity.backend.dao.rankcheck;

import java.util.*;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.rankcheck.KeywordAdwordsEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;


@Repository
public class SeoClarityKeywordMonthlySearchEngineRelationEntityDAO extends RankCheckBaseJdbcSupport<SeoClarityKeywordMonthlySearchEngineRelationEntity>{

	@Override
	public String getTableName() {
		return "keyword_monthly_search_engine_relation_entity";
	}
	
    public int insert(SeoClarityKeywordMonthlySearchEngineRelationEntity sRelation) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("keyword_id", sRelation.getKeywordId());
        values.put("search_language_id", sRelation.getSearchLanguageId());
        values.put("search_engine_id", sRelation.getSearchEngineId());
        values.put("create_date", sRelation.getCreateDate());
        values.put("frequency", sRelation.getFrequency());
        return insert(values);
    }

    public int[] insertForBatch(List<SeoClarityKeywordMonthlySearchEngineRelationEntity> list) {
        StringBuffer sql = new StringBuffer();
        sql.append("insert ignore into keyword_monthly_search_engine_relation_entity (keyword_id, search_language_id, search_engine_id, device, create_date, frequency)");
        sql.append(" values (? ,?, ?, ?, ?, ?) ");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (SeoClarityKeywordMonthlySearchEngineRelationEntity entity : list) {
            Object[] values = new Object[]{
                    entity.getKeywordId(),
                    entity.getSearchLanguageId(),
                    entity.getSearchEngineId(),
                    entity.getDevice(),
                    entity.getCreateDate(),
                    entity.getFrequency()};
            batch.add(values);
        }
        return executeBatch(sql.toString(), batch);
    }

    public SeoClarityKeywordMonthlySearchEngineRelationEntity checkExist(int keywordId, int sLanguageId, int sEngineId) {
        String sql = "select * from keyword_monthly_search_engine_relation_entity where keyword_id=? and search_language_id=? and search_engine_id=?";
        return findObject(sql, keywordId, sLanguageId, sEngineId);
    }

    public List<Integer> checkExist(int engineId, int languageId, List<Integer> kidList) {
        String sql = "select keyword_id from keyword_monthly_search_engine_relation_entity where search_engine_id = ? and search_language_id = ? and keyword_id in (" + StringUtils.join(kidList, ',') + ")";
        List<Integer> result = this.queryForIntegerList(sql, engineId, languageId);
        return result != null ? result : new ArrayList<>();
    }

    public List<SeoClarityKeywordMonthlySearchEngineRelationEntity> checkExistIdAndDateAtEntity(int engineId, int languageId, String device, List<Integer> kidList) {
        String sql = "select id, keyword_id, create_date from keyword_monthly_search_engine_relation_entity where search_engine_id = ? and search_language_id = ? and device = ? and keyword_id in (" + StringUtils.join(kidList, ',') + ")";
        return findBySql(sql, engineId, languageId, device);
    }

    public SeoClarityKeywordMonthlySearchEngineRelationEntity checkExist_rank100(String keyword_text, int sLanguageId, int sEngineId) {
        String sql = "select keyword_id,keyword_text from keyword_monthly_search_engine_relation_entity rel join keyword_entity ke on rel.keyword_id=ke.id" +
                " where search_engine_id=? and search_language_id=? and keyword_text=?";

        return findObject(sql, sLanguageId, sEngineId,keyword_text);
    }

    public int delByKidList(List<Integer> kidList) {
    	String sql = "delete from " + getTableName() + " where keyword_id in (" + StringUtils.join(kidList, ',') + ") ";
    	return this.executeUpdate(sql);
    }
    
    public int delByKidList(int engine, int language, List<Integer> kidList) {
    	String sql = "delete from " + getTableName() + " where keyword_id in (" + StringUtils.join(kidList, ',') + ") and search_engine_id = ? and search_language_id = ? ";
    	return this.executeUpdate(sql, engine, language);
    }
    
    public List<Map<String, Object>> checkDeletedMonthlyRelation(String dateStr) {
    	String sql = "select search_engine_id, search_language_id, keyword_id from keyword_monthly_search_engine_relation_log where operationType = 3 and logDate > '" + dateStr  + "' ";
    	return this.queryForMapList(sql);
    }

    public List<SeoClarityKeywordMonthlySearchEngineRelationEntity> getNeedUpdateKeywordAdwords(int startId, int endId){
	    String sql = " select distinct t1.keyword_id,t1.search_language_id,t1.create_date from keyword_monthly_search_engine_relation_entity t1 ";
        sql += " inner join keyword_adwords_expanded t2 on t1.keyword_id = t2.keyword_id   ";
        sql += " and t1.search_language_id = t2.language_id ";
        sql += " where t1.keyword_id >= ? and t1.keyword_id <? and t2.monthly_relationship_create_date is null ";
        sql += "";
	    return findBySql(sql,startId, endId);
    }

    public List<SeoClarityKeywordMonthlySearchEngineRelationEntity> getKeywordAdwordsListByPage(int engineId, int languageId, long startId, int pageSize, String joinTableName){
        if(StringUtils.isBlank(joinTableName)){
            joinTableName = "keyword_adwords";
        }
        String sql = " select t1.id,t1.keyword_id,t1.search_language_id,t1.create_date,t2.avg_monthly_search_volume,t2.cost_per_click ";
        sql += " ,t2.monthly_search_volume1,t2.monthly_search_volume2,t2.monthly_search_volume3,t2.monthly_search_volume4 ";
        sql += " ,t2.monthly_search_volume5,t2.monthly_search_volume6,t2.monthly_search_volume7,t2.monthly_search_volume8 ";
        sql += " ,t2.monthly_search_volume9,t2.monthly_search_volume10,t2.monthly_search_volume11,t2.monthly_search_volume12 ";
        if(joinTableName.equals("keyword_adwords")){
            sql += " ,t2.start_month,t2.end_month";
        }
        sql += " from keyword_monthly_search_engine_relation_entity t1 ";
        sql += " inner join " + joinTableName + " t2 on t1.keyword_id = t2.keyword_id   ";
        sql += " and t1.search_language_id = t2.language_id ";
        sql += " where t1.search_engine_id = ? and t1.search_language_id = ? and t1.id > ? ";
        sql += " order by t1.id limit ? ";
        return findBySql(sql, engineId, languageId, startId, pageSize);
    }


    public List<Integer> checkExistsInDirtyKeywords(int engine, int language, List<Integer> kidList) {
        String sql = "select keyword_id from keyword_monthly_search_engine_relation_entity where search_engine_id = ? and search_language_id = ? and keyword_id in (" + StringUtils.join(kidList, ',') + ")";
        return this.queryForIntegerList(sql, engine, language);
    }

    public List<SeoClarityKeywordMonthlySearchEngineRelationEntity> checkExist(int engine, int language, String device, Collection<String> encodeKeywordNameList) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select keyword_id, keyword_text ");
        sbd.append(" from ").append(getTableName()).append(" as rel ");
        sbd.append(" join keyword_entity as ke on rel.keyword_id=ke.id ");
        sbd.append(" where search_engine_id = ? ");
        sbd.append(" and search_language_id =  ? ");
        sbd.append(" and device =  ? ");
        sbd.append(" and keyword_text in ('").append(StringUtils.join(encodeKeywordNameList, "','")).append("')");
        return this.findBySql(sbd.toString(), engine, language, device);
    }


    public List<SeoClarityKeywordMonthlySearchEngineRelationEntity> getLimit(String limitSql,long lastId) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select rel.keyword_id from keyword_monthly_search_engine_relation_entity rel left join keyword_monthly_recommend d\n" +
                " on rel.search_engine_id=d.search_engine_id and rel.search_language_id=d.search_language_id\n" +
                " and rel.keyword_id=d.rankcheck_id where rel.search_engine_id=1 and rel.search_language_id=1 and keyword_id > "+lastId+ " and d.id is null \n" +
                "order by keyword_id ");
        sbd.append(limitSql );
        System.out.println("=====getLimit"+ sbd.toString());
        return this.findBySql(sbd.toString());
    }

    public List<SeoClarityKeywordMonthlySearchEngineRelationEntity> getBySeAndKwIdList(int engine, int language, String device, List<Integer> kidList) {
        String sql = "select * from " + getTableName() + " where search_engine_id = ? and search_language_id = ? and device =  ? and keyword_id in (" + StringUtils.join(kidList, ',') + ")";
        return this.findBySql(sql, engine, language, device);
    }

    public void batchUpdateCreateDate(List<SeoClarityKeywordMonthlySearchEngineRelationEntity> list) {
        String sql = "update " + this.getTableName() + " set create_date = ? where id = ? ";
        Object[] values = null;
        List<Object[]> batch = new ArrayList<Object[]>();
        for (SeoClarityKeywordMonthlySearchEngineRelationEntity entity : list) {
            values = new Object[]{entity.getCreateDate(), entity.getId()};
            batch.add(values);
        }
        this.executeBatch(sql, batch);
    }
}
