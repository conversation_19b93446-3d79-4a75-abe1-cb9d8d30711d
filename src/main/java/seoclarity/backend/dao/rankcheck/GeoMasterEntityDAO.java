/**
 *
 */
package seoclarity.backend.dao.rankcheck;


import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.rankcheck.GeoMasterEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class GeoMasterEntityDAO extends RankCheckBaseJdbcSupport<GeoMasterEntity> {

    @Override
    public String getTableName() {
        return "geo_master";
    }

    public int insert(String place_id, String city_name, String short_name, String lat, String lng, String line) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("place_id", place_id);
        values.put("city_name", city_name);
        values.put("country_code", short_name);
        values.put("lat", lat);
        values.put("lng", lng);
        values.put("detail_json", line);
        return this.insert(values);
    }

    public int insert(String place_id, String city_name, String short_name, String lat, String lng, String detail_json, String place_type) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("place_id", place_id);
        values.put("city_name", city_name);
        values.put("place_type", place_type);
        values.put("country_code", short_name);
        values.put("lat", lat);
        values.put("lng", lng);
        values.put("detail_json", detail_json);
        return this.insert(values);
    }

    public int insertWithType(String placeId, String cityName, String countryCode, String lat, String lng, String detailJson, String placeType, Integer type) {
        Map<String, Object> values = new HashMap<String, Object>();
        values.put("place_id", placeId);
        values.put("city_name", cityName);
        values.put("place_type", placeType);
        values.put("country_code", countryCode);
        values.put("lat", lat);
        values.put("lng", lng);
        values.put("detail_json", detailJson);
        values.put("type", type);
        return this.insert(values);
    }

    public List<GeoMasterEntity> getAllGeoList() {
        StringBuffer sqlBuff = new StringBuffer();
        sqlBuff.append(" select ce.id as oldCityId, ce.city_name as oldCityName, ce.query_name as oldQueryName, ce.state_id as oldStateId, ce.type as oldType,");
        sqlBuff.append(" ce.country_code as oldCountryCode, ce.uule as oldUule, ce.lat as oldLat, ce.lng as oldLng, ce.adwords_criteria_id as oldAdwordsCriteriaId,");
        sqlBuff.append(" ce.city_name_for_zipcode as oldCityNameForZipcode, mapp.id mappingId, gm.id as id, gm.city_name, gm.place_id, gm.place_type, gm.state_id,");
        sqlBuff.append(" gm.type, gm.country_code, gm.lat, gm.lng, gm.adwords_criteria_id from city_entity ce join geo_mapping mapp on ce.id=mapp.old_city_id ");
        sqlBuff.append(" join ").append(getTableName()).append(" gm on mapp.geo_id=gm.id ");
        sqlBuff.append(" UNION ALL select ce.id, ce.city_name, ce.query_name, ce.state_id, ce.type, ce.country_code, ce.uule, ce.lat, ce.lng, ce.adwords_criteria_id,");
        sqlBuff.append(" ce.city_name_for_zipcode, null as mappingId, null as id, null as city_name, null as place_id, null as place_type, null as state_id,");
        sqlBuff.append(" null as type, null as country_code, null as lat, null as lng, null as adwords_criteria_id from city_entity ce LEFT join geo_mapping mapp ");
        sqlBuff.append(" on ce.id=mapp.old_city_id where mapp.id is null ");
        sqlBuff.append(" UNION ALL select null as oldCityId, null as oldCityName, null as oldQueryName, null as oldStateId, null as oldType, null as oldCountryCode,");
        sqlBuff.append(" null as oldUule, null as oldLat, null as oldLng, null as oldAdwordsCriteriaId, null as oldCityNameForZipcode, null as mappingId,");
        sqlBuff.append(" gm.id, gm.city_name, gm.place_id, gm.place_type, gm.state_id, gm.type, gm.country_code, gm.lat, gm.lng, gm.adwords_criteria_id ");
        sqlBuff.append(" from ").append(getTableName()).append(" gm LEFT join geo_mapping mapp on gm.id=mapp.geo_id where mapp.id is null");
        return findBySql(sqlBuff.toString());
    }

    public GeoMasterEntity getCityById(int cityId) {
        String sql = "select * from " + getTableName() + " where id = ?";
        return findObject(sql, cityId);
    }

    public GeoMasterEntity getByPlaceId(String placeId) {
        String sql = "select * from " + getTableName() + " where place_id = ?";
        return findObject(sql, placeId);
    }

    public List<GeoMasterEntity> getAllGeoMaster() {
        StringBuffer sql = new StringBuffer();
        sql.append(" select id,city_name,place_id,country_code,lat,lng,adwords_criteria_id, detail_json from " + getTableName());
        return findBySql(sql.toString());
    }

    public boolean updateCityCriteriaIdById(int id, Integer adwords_criteria_id) {
        String sql = "update " + getTableName() + " set adwords_criteria_id = ? where id = ? limit 1";
        return executeUpdate(sql, adwords_criteria_id, id) > 0;
    }

    public void batchUpdateCityName(List<GeoMasterEntity> list) {
        StringBuffer sql = new StringBuffer();
        sql.append("update ").append(getTableName()).append(" set city_name=? where id=?");
        List<Object[]> batch = new ArrayList<Object[]>();
        for (GeoMasterEntity entity : list) {
            Object[] values = new Object[]{entity.getCityName(), entity.getId()};
            batch.add(values);
        }
        executeBatch(sql.toString(), batch);
    }

    public GeoMasterEntity getByOldCityId(int cityId) {
        String sql = "select t1.* from " + getTableName() + " t1 inner join geo_mapping t2 on t1.id = t2.geo_id where old_city_id = ?";
        return findObject(sql, cityId);
    }

    public GeoMasterEntity getCityByName(String cityname) {
        String sql = " select id,city_name from " + getTableName() + " where city_name = ? and country_code = 'US' limit 1";
        return this.findObject(sql, cityname);
    }
}