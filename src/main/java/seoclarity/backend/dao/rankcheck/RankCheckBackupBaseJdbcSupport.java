/**
 *
 */
package seoclarity.backend.dao.rankcheck;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class RankCheckBackupBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="rankCheckBackupDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
