package seoclarity.backend.dao.rankcheck;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.rankcheck.RcKeywordDomainRelEntity;

import java.util.List;

@Repository
public class AiKeywordDomainRelEntityDAO extends RankCheckBaseJdbcSupport<RcKeywordDomainRelEntity>{


    @Override
    public String getTableName() {
        return "ai_keyword_domain_rel";
    }

    public List<RcKeywordDomainRelEntity> getKeywordListByDomainList(List<Integer> domainIdList, Integer limitNo){

        String sql = " select distinct left(countryLanguageCd,2) countryCd,rel.keywordId,ke.rawKeywordName as keywordName  ";
        sql += " FROM ai_keyword_domain_rel domainRel ";
        sql += " JOIN ai_keyword_se_rel rel on domainRel.keywordSeRelId=rel.id  ";
        sql += " JOIN ai_search_engine_entity se on rel.searchEngineId=se.id  ";
        sql += " JOIN ai_language_entity le on rel.languageId=le.id ";
        sql += " JOIN ai_search_engine_prompt pt on domainRel.promptId=pt.id  ";
        sql += " JOIN ai_keyword_entity ke on rel.keywordId=ke.id ";
        sql += " WHERE domainRel.ownDomainId in(" + StringUtils.join(domainIdList, ',') + ") ";
        if(limitNo != null){
            sql += " limit " + limitNo;
        }
        System.out.println("===getKeywordListByDomainSQL:" + sql);
        return findBySql(sql);
    }

}
