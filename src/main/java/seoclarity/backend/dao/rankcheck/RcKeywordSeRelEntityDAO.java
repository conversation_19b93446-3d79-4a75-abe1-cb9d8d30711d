package seoclarity.backend.dao.rankcheck;

import java.util.List;

import org.springframework.stereotype.Repository;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.rankcheck
 * @author: cil
 * @date: 2021-06-10 13:42
 **/

import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;

@Repository
public class RcKeywordSeRelEntityDAO extends RankCheckBaseJdbcSupport<RcKeywordSeRelEntity> {
    @Override
    public String getTableName() {
        return "rc_keyword_se_rel";
    }


    /**
     * get 1000 RcKeywordSeRelEntity by id
     * @param id  not null
     * @return
     */
    public List<RcKeywordSeRelEntity> getKeywordEntityListById(Long id) {

        StringBuilder sql = new StringBuilder();
        sql.append("select ksr.* ,ka." + //todo ks.cloums
                "from " +
                "rc_keyword_se_rel ksr " +
                "left join " +
                "rc_keyword_domain_rel kdr " +
                "on ksr.keywordId =  kdr.keywordId " +
                "left join keyword_adwords ka " +
                "on kdr.keywordId = ka.keyword_id" +
                "where krd.id is not null"
        );
        sql.append(" and ksr.id > ").append(id);

        return this.queryPageForList(sql.toString(),1000);
    }
    
    
    
    public List<RcKeywordSeRelEntity> getEngineLanguageIdList() {
    	
    	StringBuilder sql = new StringBuilder();
    	sql.append("select searchEngineId,languageId from rc_keyword_se_rel ksr " + 
    			"    where  (ksr.searchEngineId<100 or (ksr.searchEngineId >200 and ksr.searchEngineId <255)) group by searchEngineId,languageId");
    	
    	return this.findBySql(sql.toString());
    }


    public List<RcKeywordSeRelEntity> getKeywords(int engineId, int languageId, String device, int cityId, int oid) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select DISTINCT ke.keyword_text as  keywordText ");
        sql.append(" FROM rc_keyword_se_rel rel ");
        sql.append(" JOIN rc_keyword_domain_rel domainRel ON rel.id = domainRel.keywordSeRelId ");
        sql.append(" JOIN keyword_entity ke ON rel.keywordId = ke.id ");
        sql.append(" where ownDomainId = ? and rel.searchEngineId = ? and rel.languageId = ? and rel.device = ? and rel.cityId = ? ");
        return this.findBySql(sql.toString(),oid,engineId,languageId,device,cityId);
    }

    public List<RcKeywordSeRelEntity> getUnionKeywords(int engineId, int languageId, int oid, int keywordType) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select id as keywordId, keyword_text as keywordText ");
        sql.append(" from keyword_entity where id in ( ");
        sql.append(" select distinct  rel.keywordId  ");
        sql.append(" from rc_keyword_se_rel rel ");
        sql.append(" join rc_keyword_domain_rel domainRel on rel.id=domainRel.keywordSeRelId ");
        sql.append(" where ownDomainId = ? and rel.searchEngineId = ? and rel.languageId = ? and rel.keywordType = ?) ");
        return this.findBySql(sql.toString(),oid,engineId,languageId, keywordType);
    }

    public List<RcKeywordSeRelEntity> getRelKeywords(int engineId, int languageId, int oid, int keywordType) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select distinct  rel.keywordId, rel.cityId  ");
        sql.append(" from rc_keyword_se_rel rel ");
        sql.append(" join rc_keyword_domain_rel domainRel on rel.id=domainRel.keywordSeRelId ");
        sql.append(" where ownDomainId = ? and rel.searchEngineId = ? and rel.languageId = ? and rel.keywordType = ? ");
        return this.findBySql(sql.toString(),oid,engineId,languageId, keywordType);
    }

    public List<RcKeywordSeRelEntity> selectEntityListByDomainId(int domainId) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select rd.keywordId as keywordId, rs.searchEngineId as searchEngineId, rs.languageId as languageId, rs.device as device, rs.cityId as cityId, " +
                "rd.frequency as frequency, rd.id as kdrId, rd.ownDomainId as ownDomainId, rd.createDate as createDate ");
        sbd.append(" from rc_keyword_domain_rel rd left join ").append(getTableName()).append(" rs on rs.id = rd.keywordSeRelId ");
        sbd.append(" where rd.ownDomainId =  ? ");
        return findBySql(sbd.toString(), domainId);
    }

}