package seoclarity.backend.dao.rankcheck;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.BaseJdbcSupport;
import seoclarity.backend.entity.rankcheck.RcKeywordSeRelEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsExpandedEntityNew;
import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.dao.rankcheck
 * @author: cil
 * @date: 2021-06-11 09:45
 **/
@Repository
public class KeywordAdwordsExpandedEntityNewDAO extends RankCheckBaseJdbcSupport<KeywordAdwordsExpandedEntityNew> {

    @Override
    public String getTableName() {
        return null;
    }

    public Long getMaxId() {
        String sql = " select max(id) from keyword_entity" ;
        return queryForLong(sql);
    }

    public Long getMinId() {
        String sql = " select min(id) from keyword_entity" ;
        return queryForLong(sql);
    }
    
    public List<KeywordAdwordsExpandedEntityNew> getAdwordsListByKeywordIdRange(int keywordType, long fromId, long toId, List<Integer> excludeSEList) {
        if (keywordType == RcKeywordSeRelEntity.KEYWORD_TYPE_NATIONAL) {
            StringBuffer sql = new StringBuffer("select kae.id as id, rel.searchEngineId, rel.languageId, ke.id as keywordId, ke.keyword_text as keyword_name, kae.month as month," +
                " ka.avg_monthly_search_volume as avgMonthlySearchVolume, ifnull(kae.monthly_search_volume,0) as monthlySearchVolume, ka.cost_per_click as costPerClick," +
                " rel.cityId as locationId, ka.category as category " +
                "from keyword_entity ke join " +
                "( select distinct ksr.keywordId,searchEngineId,languageId,cityId from rc_keyword_se_rel ksr join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId " +
                " join search_engine_entity se on ksr.searchEngineId=se.id where ksr.keywordType= ? and se.search_engine like '%google%' " +
                "  and ksr.searchEngineId NOT IN(" + StringUtils.join(excludeSEList, ",") + ") and se.search_engine_type='google' " +
                "  and ksr.keywordId > ? and ksr.keywordId <= ? ) rel on ke.id = rel.keywordId " +
                "left join keyword_adwords_expanded kae on kae.keyword_id=rel.keywordId and kae.search_engine_id= 99 and kae.language_id=rel.languageId and kae.city_id=rel.cityId " +
                "left join keyword_adwords ka on ka.keyword_id = rel.keywordId and ka.search_engine_id=99 and ka.language_id=rel.languageId and ka.city_id=0 " +
                "where ke.id > ? and ke.id <= ? order by ke.id,rel.searchEngineId,rel.languageId,kae.city_id,kae.month");
                return this.findBySql(sql.toString(), keywordType, fromId, toId, fromId, toId);
        } else if (keywordType == RcKeywordSeRelEntity.KEYWORD_TYPE_GEO) {
        	StringBuffer sql = new StringBuffer();
        	sql.append("select kae.id as id, rel.searchEngineId, rel.languageId,ke.id as keywordId, ke.keyword_text as keyword_name, kae.month as month,");
        	sql.append(" ka.avg_monthly_search_volume as avgMonthlySearchVolume, ifnull(kae.monthly_search_volume,0) as monthlySearchVolume,");
        	sql.append(" ka.cost_per_click as costPerClick, rel.cityId as locationId ");
        	sql.append(" from keyword_entity ke join ( select distinct ksr.keywordId,searchEngineId,languageId,cityId from rc_keyword_se_rel ksr ");
        	sql.append("  join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId join search_engine_entity se on ksr.searchEngineId=se.id ");
        	sql.append("  where ksr.keywordType=? and cityId>0 and se.search_engine like '%google%' and ksr.searchEngineId NOT IN(");
        	sql.append(StringUtils.join(excludeSEList, ",")).append(") and se.search_engine_type='google' ");
        	sql.append("  and ksr.keywordId > ? and ksr.keywordId <= ? ) rel on ke.id = rel.keywordId ");
        	sql.append(" left join keyword_adwords_expanded kae on kae.keyword_id=rel.keywordId and kae.search_engine_id=99 and kae.language_id=rel.languageId ");
        	sql.append("  and kae.city_id=rel.cityId ");
        	sql.append(" left join (select t2.search_engine_id, t2.language_id, t2.keyword_id, t2.city_id, t2.avg_monthly_search_volume, t2.cost_per_click from ");
        	sql.append("  (select keyword_id,city_id,search_engine_id,language_id,max(monthly_search_date) maxDate from keyword_city_adwords_data_entity");
        	sql.append("   where keyword_id>? and keyword_id<=? group by keyword_id,city_id,search_engine_id,language_id) t1 ");
        	sql.append("  join keyword_city_adwords_data_entity t2 on t1.search_engine_id=t2.search_engine_id and t1.language_id=t2.language_id"); 
        	sql.append("   and t1.keyword_id=t2.keyword_id and t1.city_id=t2.city_id and t1.maxDate=t2.monthly_search_date) ka ");  
        	sql.append("  on ka.search_engine_id=99 and ka.language_id=rel.languageId and ka.keyword_id=rel.keywordId and ka.city_id=rel.cityId");
        	sql.append(" where ke.id>? and ke.id<=? order by ke.id,rel.searchEngineId,rel.languageId,kae.city_id,kae.month");
        	return this.findBySql(sql.toString(), keywordType, fromId, toId, fromId, toId, fromId, toId);
        }
        return null;
    }
    
    public List<KeywordAdwordsExpandedEntityNew> getNewlyAddedAdwordsList(int keywordType, long minId, long maxId, List<Integer> excludeSEList) {
        StringBuffer sql = new StringBuffer();
        if (keywordType == RcKeywordSeRelEntity.KEYWORD_TYPE_NATIONAL) {
        	sql.append("select kae.id as id, rel.searchEngineId, rel.languageId, ke.id as keywordId, ke.keyword_text as keyword_name, kae.month as month,");
        	sql.append("  ka.avg_monthly_search_volume as avgMonthlySearchVolume, ifnull(kae.monthly_search_volume,0) as monthlySearchVolume,");
        	sql.append("  ka.cost_per_click as costPerClick, rel.cityId as locationId, ka.category as category ");
        	sql.append(" from keyword_entity ke join ( select distinct ksr.keywordId,searchEngineId,languageId,cityId from rc_keyword_se_rel ksr ");
        	sql.append("  join rc_keyword_domain_rel kdr on ksr.id=kdr.keywordSeRelId join search_engine_entity se on ksr.searchEngineId=se.id ");
        	sql.append("  join keyword_adwords_expanded_log log on log.search_engine_id=99 and ksr.languageId=log.language_id and ksr.keywordId=log.keyword_id and log.city_id=0 ");
        	sql.append("  where ksr.keywordType=? and se.search_engine_type='google' and ksr.searchEngineId NOT IN(" + StringUtils.join(excludeSEList, ",") + ") ");
        	sql.append("   and log.id>=? and log.id<? ) rel on ke.id=rel.keywordId ");
        	sql.append(" left join keyword_adwords_expanded kae on kae.keyword_id=rel.keywordId and kae.search_engine_id=99 and kae.language_id=rel.languageId ");
        	sql.append("  and kae.city_id=rel.cityId ");
        	sql.append(" left join keyword_adwords ka on ka.keyword_id=rel.keywordId and ka.search_engine_id=99 and ka.language_id=rel.languageId and ka.city_id=0 ");
        	sql.append(" where ke.id IN (select distinct keyword_id from keyword_adwords_expanded_log where id>=? and id<? and city_id=0) ");
        	sql.append(" order by ke.id,rel.searchEngineId,rel.languageId, kae.city_id");
        	return this.findBySql(sql.toString(), keywordType, minId, maxId, minId, maxId);
        } else if (keywordType == RcKeywordSeRelEntity.KEYWORD_TYPE_GEO) {
        	sql.append("select kae.id as id, rel.searchEngineId, rel.languageId, ke.id as keywordId, ke.keyword_text as keyword_name, kae.month as month,");
        	sql.append("  ka.avg_monthly_search_volume as avgMonthlySearchVolume, ifnull(kae.monthly_search_volume,0) as monthlySearchVolume,");
        	sql.append("  ka.cost_per_click as costPerClick, rel.cityId as locationId");
        	sql.append(" from keyword_entity ke join ( select distinct ksr.keywordId,searchEngineId,languageId,cityId from rc_keyword_se_rel ksr ");
        	sql.append("  join rc_keyword_domain_rel kdr on ksr.id=kdr.keywordSeRelId join search_engine_entity se on ksr.searchEngineId=se.id");
        	sql.append("  join keyword_adwords_expanded_log log on log.search_engine_id=99 and ksr.languageId=log.language_id and ksr.keywordId=log.keyword_id and ksr.cityId=log.city_id");
        	sql.append("  where ksr.keywordType=? and cityId>0 and se.search_engine_type='google' and ksr.searchEngineId NOT IN(" + StringUtils.join(excludeSEList, ",") + ") ");
        	sql.append("   and log.id>=? and log.id<?) rel on ke.id=rel.keywordId");
        	sql.append(" left join keyword_adwords_expanded kae on kae.keyword_id=rel.keywordId and kae.search_engine_id=99 and kae.language_id=rel.languageId ");
        	sql.append("  and kae.city_id=rel.cityId ");
        	sql.append(" left join (select t2.search_engine_id, t2.language_id, t2.keyword_id, t2.city_id, t2.avg_monthly_search_volume, t2.cost_per_click from ");
        	sql.append("  (select kca.keyword_id,kca.city_id,kca.search_engine_id,kca.language_id,max(kca.monthly_search_date) maxDate from ");
        	sql.append("   keyword_adwords_expanded_log log join keyword_city_adwords_data_entity kca on log.search_engine_id=99 and kca.search_engine_id=99 ");
        	sql.append("   and log.language_id=kca.language_id and log.keyword_id=kca.keyword_id and log.city_id=kca.city_id ");
        	sql.append("   where log.id>=? and log.id<? group by kca.keyword_id,kca.city_id,kca.search_engine_id,kca.language_id) t1");
        	sql.append("  left join keyword_city_adwords_data_entity t2 on t1.search_engine_id=t2.search_engine_id and t1.language_id=t2.language_id ");
        	sql.append("   and t1.keyword_id=t2.keyword_id and t1.city_id=t2.city_id and t1.maxDate=t2.monthly_search_date) ka ");
        	sql.append("  on ka.search_engine_id=99 and ka.language_id=rel.languageId and ka.keyword_id=rel.keywordId and ka.city_id=rel.cityId ");
        	sql.append(" where ke.id IN (select distinct keyword_id from keyword_adwords_expanded_log where id>=? and id<? and city_id>0) ");
        	sql.append(" order by ke.id,rel.searchEngineId,rel.languageId, kae.city_id,kae.month");
        	return this.findBySql(sql.toString(), keywordType, minId, maxId, minId, maxId, minId, maxId);
        }
        return null;
    }
    
    /**
     * 一次性拿30个keyword的数据
     * @param id
     * @return
     */
    public List<KeywordAdwordsExpandedEntityNew> select30Byid(int keywordType ,Long id) {
        long bigId = id + 200;
        StringBuffer sql = new StringBuffer("select kae.id as id,rel.searchEngineId,rel.languageId,ke.id as keywordId,ke.keyword_text as keyword_name,kae.month as month," +
                " ka.avg_monthly_search_volume as avgMonthlySearchVolume, ifnull(kae.monthly_search_volume,0) as monthlySearchVolume, ka.cost_per_click as costPerClick," +
                "  rel.cityId as locationId, ka.category as category " +
                "from keyword_entity ke join " +
                "(select distinct ksr.keywordId,searchEngineId,languageId,cityId from rc_keyword_se_rel ksr  join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId " +
                "  where ksr.keywordType= ? and (ksr.searchEngineId<100 or (ksr.searchEngineId >200 and ksr.searchEngineId <255)) and ksr.keywordId > ? and ksr.keywordId <= ?)  rel" +
                "  on ke.id = rel.keywordId " +
                "left join keyword_adwords_expanded kae on kae.keyword_id = rel.keywordId and kae.search_engine_id= 99 and kae.language_id=rel.languageId and kae.city_id = rel.cityId " +
                "left join keyword_adwords ka on ka.keyword_id = rel.keywordId and ka.search_engine_id = 99 and ka.language_id = rel.languageId and ka.city_id = 0 " +
                "where ke.id > ? and ke.id <= ? order by ke.id,rel.searchEngineId,rel.languageId, kae.city_id ;");
        return this.findBySql(sql.toString(), keywordType,id,bigId,id,bigId);
    }

    public List<KeywordAdwordsExpandedEntityNew> findToday(long minId, long maxId,int keywordType) {
        StringBuffer sql = new StringBuffer("select kae.id as id,rel.searchEngineId,rel.languageId,ke.id as keywordId,ke.keyword_text as keyword_name,kae.month as month," +
                " kae.avg_monthly_search_volume as avgMonthlySearchVolume, ifnull(kae.monthly_search_volume,0) as monthlySearchVolume, kae.cost_per_click as costPerClick," +
                "  rel.cityId as locationId, ka.category as category " +
                "from keyword_entity ke join " +
                "(select distinct ksr.keywordId,searchEngineId,languageId,cityId from rc_keyword_se_rel ksr  join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId " +
                "  where ksr.keywordType= ? and (ksr.searchEngineId<100 or (ksr.searchEngineId >200 and ksr.searchEngineId <255)) and ksr.keywordId in (select distinct keyword_id from keyword_adwords_expanded_log where id >= ? and id < ?) )  rel" +
                "  on ke.id = rel.keywordId " +
                "left join keyword_adwords_expanded kae on kae.keyword_id = rel.keywordId and kae.search_engine_id= 99 and kae.language_id=rel.languageId and kae.city_id = rel.cityId " +
                "left join keyword_adwords ka on ka.keyword_id = rel.keywordId and ka.search_engine_id = 99 and ka.language_id = rel.languageId and ka.city_id = 0 " +
                "where  ke.id in(select distinct keyword_id from keyword_adwords_expanded_log where id >= ? and id < ?)   order by ke.id,rel.searchEngineId,rel.languageId, kae.city_id ;");
        return this.findBySql(sql.toString(),keywordType,minId,maxId,minId,maxId);
    }
    
    public List<KeywordAdwordsExpandedEntityNew> findTodayV2(long minId, long maxId,int keywordType, Integer engineId, Integer languageId) {
    	StringBuffer sql = new StringBuffer("select kae.id as id,rel.searchEngineId,rel.languageId,ke.id as keywordId,ke.keyword_text as keyword_name,kae.month as month," +
    			" kae.avg_monthly_search_volume as avgMonthlySearchVolume, ifnull(kae.monthly_search_volume,0) as monthlySearchVolume, kae.cost_per_click as costPerClick," +
    			"  rel.cityId as locationId, ka.category as category " +
    			"from keyword_entity ke join " +
    			"(select distinct ksr.keywordId,searchEngineId,languageId,cityId from rc_keyword_se_rel ksr  join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId " +
    			"  where ksr.keywordType= ? and (ksr.searchEngineId = " + engineId + " and ksr.languageId = " + languageId + ") and ksr.keywordId in (select distinct keyword_id from keyword_adwords_expanded_log where id >= ? and id < ?) )  rel" +
    			"  on ke.id = rel.keywordId " +
    			"left join keyword_adwords_expanded kae on kae.keyword_id = rel.keywordId and kae.search_engine_id= 99 and kae.language_id=rel.languageId and kae.city_id = rel.cityId " +
    			"left join keyword_adwords ka on ka.keyword_id = rel.keywordId and ka.search_engine_id = 99 and ka.language_id = rel.languageId and ka.city_id = 0 " +
    			"where  ke.id in(select distinct keyword_id from keyword_adwords_expanded_log where id >= ? and id < ?)   order by ke.id,rel.searchEngineId,rel.languageId, kae.city_id ;");
    	return this.findBySql(sql.toString(),keywordType,minId,maxId,minId,maxId);
    }
    
	public List<KeywordAdwordsExpandedEntityNew> getListByKeywordIdRange(int keywordType ,Long id, int engineId, int languageId, int pageSize) {
        long bigId = id + pageSize;
        StringBuffer sql = new StringBuffer("select kae.id as id,rel.searchEngineId,rel.languageId,ke.id as keywordId,ke.keyword_text as keyword_name,kae.month as month," +
                " kae.avg_monthly_search_volume as avgMonthlySearchVolume, ifnull(kae.monthly_search_volume,0) as monthlySearchVolume, kae.cost_per_click as costPerClick," +
                "  rel.cityId as locationId, ka.category as category " +
                "from keyword_entity ke join " +
                "(select distinct ksr.keywordId,searchEngineId,languageId,cityId from rc_keyword_se_rel ksr  join rc_keyword_domain_rel kdr on ksr.id = kdr.keywordSeRelId " +
                "  where ksr.keywordType= ? and (ksr.searchEngineId = " + engineId + " and ksr.languageId  =  " + languageId + " ) and ksr.keywordId > ? and ksr.keywordId <= ?)  rel" +
                "  on ke.id = rel.keywordId " +
                "left join keyword_adwords_expanded kae on kae.keyword_id = rel.keywordId and kae.search_engine_id= 99 and kae.language_id=rel.languageId and kae.city_id = rel.cityId " +
                "left join keyword_adwords ka on ka.keyword_id = rel.keywordId and ka.search_engine_id = 99 and ka.language_id = rel.languageId and ka.city_id = 0 " +
                "where ke.id > ? and ke.id <= ? order by ke.id, kae.city_id ;");
        return this.findBySql(sql.toString(), keywordType,id,bigId,id,bigId);
    }
}
