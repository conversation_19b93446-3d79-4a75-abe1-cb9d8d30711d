package seoclarity.backend.cronjob.tiktok.entity;

import com.google.gson.annotations.SerializedName;
import java.util.List;

public class NimblewayApiResponse {
    @SerializedName("page_results")
    private int pageResults;
    private List<Post> posts;
    private Pagination pagination;
    
    public int getPageResults() {
		return pageResults;
	}

	public void setPageResults(int pageResults) {
		this.pageResults = pageResults;
	}

	public List<Post> getPosts() {
		return posts;
	}

	public void setPosts(List<Post> posts) {
		this.posts = posts;
	}

	public Pagination getPagination() {
		return pagination;
	}

	public void setPagination(Pagination pagination) {
		this.pagination = pagination;
	}

	public static class Post {
        @SerializedName("post_id")
        private String postId;
        private String desc;
        private Author author;
        private Statistics statistics;
        private Video video;
		public String getPostId() {
			return postId;
		}
		public void setPostId(String postId) {
			this.postId = postId;
		}
		public String getDesc() {
			return desc;
		}
		public void setDesc(String desc) {
			this.desc = desc;
		}
		public Author getAuthor() {
			return author;
		}
		public void setAuthor(Author author) {
			this.author = author;
		}
		public Statistics getStatistics() {
			return statistics;
		}
		public void setStatistics(Statistics statistics) {
			this.statistics = statistics;
		}
		public Video getVideo() {
			return video;
		}
		public void setVideo(Video video) {
			this.video = video;
		}
        
    }

    public static class Author {
        @SerializedName("follower_count")
        private int followerCount;
        @SerializedName("following_count")
        private int followingCount;
        private String nickname;
        private String uid;
        private String username;
		public int getFollowerCount() {
			return followerCount;
		}
		public void setFollowerCount(int followerCount) {
			this.followerCount = followerCount;
		}
		public int getFollowingCount() {
			return followingCount;
		}
		public void setFollowingCount(int followingCount) {
			this.followingCount = followingCount;
		}
		public String getNickname() {
			return nickname;
		}
		public void setNickname(String nickname) {
			this.nickname = nickname;
		}
		public String getUid() {
			return uid;
		}
		public void setUid(String uid) {
			this.uid = uid;
		}
		public String getUsername() {
			return username;
		}
		public void setUsername(String username) {
			this.username = username;
		}
        
        
    }

    public static class Statistics {
        @SerializedName("likes_count")
        private int likesCount;
        @SerializedName("collect_count")
        private int collectCount;
        @SerializedName("comment_count")
        private int commentCount;
        @SerializedName("download_count")
        private int downloadCount;
        @SerializedName("forward_count")
        private int forwardCount;
        @SerializedName("lose_comment_count")
        private int loseCommentCount;
        @SerializedName("lose_count")
        private int loseCount;
        @SerializedName("play_count")
        private int playCount;
        @SerializedName("repost_count")
        private int repostCount;
        @SerializedName("share_count")
        private int shareCount;
        @SerializedName("whatsapp_share_count")
        private int whatsappShareCount;
		public int getLikesCount() {
			return likesCount;
		}
		public void setLikesCount(int likesCount) {
			this.likesCount = likesCount;
		}
		public int getCollectCount() {
			return collectCount;
		}
		public void setCollectCount(int collectCount) {
			this.collectCount = collectCount;
		}
		public int getCommentCount() {
			return commentCount;
		}
		public void setCommentCount(int commentCount) {
			this.commentCount = commentCount;
		}
		public int getDownloadCount() {
			return downloadCount;
		}
		public void setDownloadCount(int downloadCount) {
			this.downloadCount = downloadCount;
		}
		public int getForwardCount() {
			return forwardCount;
		}
		public void setForwardCount(int forwardCount) {
			this.forwardCount = forwardCount;
		}
		public int getLoseCommentCount() {
			return loseCommentCount;
		}
		public void setLoseCommentCount(int loseCommentCount) {
			this.loseCommentCount = loseCommentCount;
		}
		public int getLoseCount() {
			return loseCount;
		}
		public void setLoseCount(int loseCount) {
			this.loseCount = loseCount;
		}
		public int getPlayCount() {
			return playCount;
		}
		public void setPlayCount(int playCount) {
			this.playCount = playCount;
		}
		public int getRepostCount() {
			return repostCount;
		}
		public void setRepostCount(int repostCount) {
			this.repostCount = repostCount;
		}
		public int getShareCount() {
			return shareCount;
		}
		public void setShareCount(int shareCount) {
			this.shareCount = shareCount;
		}
		public int getWhatsappShareCount() {
			return whatsappShareCount;
		}
		public void setWhatsappShareCount(int whatsappShareCount) {
			this.whatsappShareCount = whatsappShareCount;
		}
        
        
    }

    public static class Video {
        private int duration;

		public int getDuration() {
			return duration;
		}

		public void setDuration(int duration) {
			this.duration = duration;
		}
        
        
    }

    public static class User {
        private String id;

		public String getId() {
			return id;
		}

		public void setId(String id) {
			this.id = id;
		}
        
        
    }

    public static class Pagination {
        private String cursor;
        @SerializedName("hasMore")
        private boolean hasMore;
        @SerializedName("has_more")
        private boolean has_more;
		public String getCursor() {
			return cursor;
		}
		public void setCursor(String cursor) {
			this.cursor = cursor;
		}
		public boolean isHasMore() {
			return hasMore;
		}
		public void setHasMore(boolean hasMore) {
			this.hasMore = hasMore;
		}
		public boolean isHas_more() {
			return has_more;
		}
		public void setHas_more(boolean has_more) {
			this.has_more = has_more;
		}
        
        
    }
}

