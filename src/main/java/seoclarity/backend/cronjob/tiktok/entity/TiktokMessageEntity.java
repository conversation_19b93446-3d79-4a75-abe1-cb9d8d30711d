package seoclarity.backend.cronjob.tiktok.entity;

import java.util.List;

public class TiktokMessageEntity {

	private Integer rankcheckKeywordId;
	private Integer cityId;
	private Integer frequency;
	private String keywordName;
	private String countryCd;
	private String device;
	private String kfkTpc;
	
	//yyyyMMdd
	private String sendToQDate; 
	private Integer engineId;
	private Integer languageId;
	private List<Integer> domainList;
	private List<Integer> relIdList;
	
	
	public Integer getRankcheckKeywordId() {
		return rankcheckKeywordId;
	}
	public void setRankcheckKeywordId(Integer rankcheckKeywordId) {
		this.rankcheckKeywordId = rankcheckKeywordId;
	}
	public Integer getCityId() {
		return cityId;
	}
	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}
	public Integer getFrequency() {
		return frequency;
	}
	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}
	public String getKeywordName() {
		return keywordName;
	}
	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}
	public String getCountryCd() {
		return countryCd;
	}
	public void setCountryCd(String countryCd) {
		this.countryCd = countryCd;
	}
	public String getDevice() {
		return device;
	}
	public void setDevice(String device) {
		this.device = device;
	}
	public String getKfkTpc() {
		return kfkTpc;
	}
	public void setKfkTpc(String kfkTpc) {
		this.kfkTpc = kfkTpc;
	}
	public String getSendToQDate() {
		return sendToQDate;
	}
	public void setSendToQDate(String sendToQDate) {
		this.sendToQDate = sendToQDate;
	}
	public Integer getEngineId() {
		return engineId;
	}
	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}
	public Integer getLanguageId() {
		return languageId;
	}
	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}
	public List<Integer> getRelIdList() {
		return relIdList;
	}
	public void setRelIdList(List<Integer> relIdList) {
		this.relIdList = relIdList;
	}
	public List<Integer> getDomainList() {
		return domainList;
	}
	public void setDomainList(List<Integer> domainList) {
		this.domainList = domainList;
	}
	
	

}
