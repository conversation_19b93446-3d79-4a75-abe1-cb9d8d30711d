package seoclarity.backend.cronjob.tiktok.entity;

public class TiktokUploadDetailEntity {
		
		private Integer rank;
		private String desc;
		private String videoId;
		private Integer duration;
		private String authorId;
		private String authorUniqueId;
		private String authorNickname;
		private String uri;

		private String videoStats;
		
		private String authorStats;
		
		
		public String getUri() {
			return uri;
		}
		public void setUri(String uri) {
			this.uri = uri;
		}
		public String getVideoId() {
			return videoId;
		}
		public void setVideoId(String videoId) {
			this.videoId = videoId;
		}
		public Integer getRank() {
			return rank;
		}
		public void setRank(Integer rank) {
			this.rank = rank;
		}
		public String getDesc() {
			return desc;
		}
		public void setDesc(String desc) {
			this.desc = desc;
		}
		public Integer getDuration() {
			return duration;
		}
		public void setDuration(Integer duration) {
			this.duration = duration;
		}
		public String getAuthorId() {
			return authorId;
		}
		public void setAuthorId(String authorId) {
			this.authorId = authorId;
		}
		public String getAuthorUniqueId() {
			return authorUniqueId;
		}
		public void setAuthorUniqueId(String authorUniqueId) {
			this.authorUniqueId = authorUniqueId;
		}
		public String getAuthorNickname() {
			return authorNickname;
		}
		public void setAuthorNickname(String authorNickname) {
			this.authorNickname = authorNickname;
		}
		public String getVideoStats() {
			return videoStats;
		}
		public void setVideoStats(String videoStats) {
			this.videoStats = videoStats;
		}
		public String getAuthorStats() {
			return authorStats;
		}
		public void setAuthorStats(String authorStats) {
			this.authorStats = authorStats;
		}
		
		
		
		

}
