package seoclarity.backend.cronjob.tiktok.entity;

public class TiktokUploadMonitorEntity {

	/**
	 * CREATE TABLE actonia_monitor.tiktok_upload_monitor_w_template (
  ->  id int NOT NULL AUTO_INCREMENT,
 	  keywordType tinyint NOT NULL DEFAULT '1' COMMENT '1: national keyword 2: geo keyword',  ->  keywordType tinyint NOT NULL DEFAULT '1' COMMENT '1: national keyword 2: geo keyword',
  ->  ownDomainId int NOT NULL,
  ->  engineId smallint NOT NULL DEFAULT '114' COMMENT 'search engine id, Example:114',
  ->  languageId smallint NOT NULL COMMENT 'search language id, Example:8',
  ->  device varchar(1) NOT NULL DEFAULT 'd' COMMENT 'd: desktop, m: mobile',
  ->  cityId int NOT NULL DEFAULT '0' COMMENT '0: national, >0: location id',
  ->  keywordRankcheckId int NOT NULL,
  ->  frequence tinyint(1) NOT NULL DEFAULT '7' COMMENT '1:daily, 7:weekly, 30: monthly', 
  ->  createdDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ->  PRIMARY KEY (id),
  ->  UNIQUE KEY uidx_kwType_se_city_kwId_oid (keywordType,engineId,languageId,device,cityId,keywordRankcheckId,ownDomainId),
  ->  KEY idx_kwId_se_city_oid_kwType (keywordRankcheckId,engineId,languageId,device,cityId,ownDomainId,keywordType),
  ->  KEY idx_oid_se_kwId_city (ownDomainId,engineId,languageId,device,keywordRankcheckId,cityId)
  -> ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
	 */
	
	public static final Integer KEYWORD_TYPE_NATIONAL = 1;
	public static final String DEVICE_DESKTOP = "d";
	
	private Integer id;
	private Integer keywordType;
	private Integer ownDomainId;
	private Integer engineId;
	private Integer languageId;
	private String device;
	private Integer cityId;
	private Integer keywordRankcheckId;
	private Integer frequence;
	
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getKeywordType() {
		return keywordType;
	}
	public void setKeywordType(Integer keywordType) {
		this.keywordType = keywordType;
	}
	public Integer getOwnDomainId() {
		return ownDomainId;
	}
	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	public Integer getEngineId() {
		return engineId;
	}
	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}
	public Integer getLanguageId() {
		return languageId;
	}
	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}
	public String getDevice() {
		return device;
	}
	public void setDevice(String device) {
		this.device = device;
	}
	public Integer getCityId() {
		return cityId;
	}
	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}
	public Integer getKeywordRankcheckId() {
		return keywordRankcheckId;
	}
	public void setKeywordRankcheckId(Integer keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}
	public Integer getFrequence() {
		return frequence;
	}
	public void setFrequence(Integer frequence) {
		this.frequence = frequence;
	}
	
	

}
