package seoclarity.backend.cronjob.tiktok;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;

import cn.hutool.core.util.IdUtil;
import seoclarity.backend.cronjob.tiktok.entity.NimblewayApiResponse;
import seoclarity.backend.cronjob.tiktok.entity.NimblewayApiResponse.Post;
import seoclarity.backend.cronjob.tiktok.entity.TiktokMessageEntity;
import seoclarity.backend.cronjob.tiktok.entity.TiktokUploadDetailEntity;
import seoclarity.backend.cronjob.tiktok.entity.TiktokUploadInfoEntity;
import seoclarity.backend.cronjob.tiktok.utils.KafkaProducerUtil;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.amazon.SQSUtils;

public class NimblewayTiktokCommand extends BaseThreadCommand {
	
	private String ip;
	private String queryUrl;
	private AmazonSQS amazonSQS;
	private Message msg;
	private String token;
	
	public NimblewayTiktokCommand() {
		
	}
	
	public NimblewayTiktokCommand(String ipAddress, String queryUrl, AmazonSQS amazonSQS, Message msg, String token) {
		this.queryUrl = queryUrl;
		this.amazonSQS = amazonSQS;
		this.msg = msg;
		this.ip = ipAddress;
		this.token = token;
	}
	
	@Override
	protected void execute() throws Exception {
		long a = System.currentTimeMillis();
		System.out.println("SETIP:" + ip);
		try {
			process();
		} catch (Exception e) {
			e.printStackTrace();
		} 
		
		long b = System.currentTimeMillis();
		System.out.println("EndIP:" + ip + " time:" + (b - a) * 1.0 / 1000 + "s");
		CacheModleFactory.getInstance().setAliveIpAddress(ip); 
	}
	
	@Override
	protected void undo() throws Exception {
		
	}
	
	public static String API_PATH_TEMPLATE = "https://api.webit.live/api/v1/realtime/social/tiktok/v1/search/posts-by-keyword?keyword=%s&country_code=%s";
	
	private Gson gson = new Gson(); 
	
	private final static String TIKTOK_URI_TEMPLATE = "/@%s/video/%s";
	
	public final static String COUNTRY_US = "us";
	
	
	private void process() throws IOException {
		
		TiktokMessageEntity tiktokMessageEntity = null;
		try {
			tiktokMessageEntity = gson.fromJson(msg.getBody(), TiktokMessageEntity.class); 
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
		
		if (tiktokMessageEntity == null) {
			return;
		}
		
		System.out.println("==== processing on :" + tiktokMessageEntity.getKeywordName());
		
		String response = HttpRequestUtils.httpByMethod(String.format(API_PATH_TEMPLATE, FormatUtils.encodeKeyword(tiktokMessageEntity.getKeywordName()), 
				tiktokMessageEntity.getCountryCd()), "", token, "GET");
		
		NimblewayApiResponse nimblewayApiResponse = gson.fromJson(response, NimblewayApiResponse.class);
		Integer rank = 1;
		
		String rankingDate = convertDateFormat(tiktokMessageEntity.getSendToQDate());
		
		if (StringUtils.isBlank(rankingDate)) {
			System.out.println("===Skip incorrect date format, sendToQDate:" + tiktokMessageEntity.getSendToQDate());
			return;
		}
		
		TiktokUploadInfoEntity tiktokUploadInfoEntity = new TiktokUploadInfoEntity();
		tiktokUploadInfoEntity.setKeywordText(tiktokMessageEntity.getKeywordName());
		tiktokUploadInfoEntity.setKeywordRankcheckId(tiktokMessageEntity.getRankcheckKeywordId());
		tiktokUploadInfoEntity.setEngineId(tiktokMessageEntity.getEngineId());
		tiktokUploadInfoEntity.setLanguageId(tiktokMessageEntity.getLanguageId());
		tiktokUploadInfoEntity.setDomainIdList(tiktokMessageEntity.getDomainList());
		tiktokUploadInfoEntity.setSendToQueueDate(rankingDate);
		
		tiktokUploadInfoEntity.setRelIdList(tiktokMessageEntity.getRelIdList());
		tiktokUploadInfoEntity.setFrequency(tiktokMessageEntity.getFrequency());
		tiktokUploadInfoEntity.setDevice(tiktokMessageEntity.getDevice());
		

		List<TiktokUploadDetailEntity> detailList = new ArrayList<TiktokUploadDetailEntity>();
		
		if (nimblewayApiResponse != null && nimblewayApiResponse.getPosts() != null && nimblewayApiResponse.getPosts().size() >= 1) {
			
			for(Post post : nimblewayApiResponse.getPosts()) {
				TiktokUploadDetailEntity tiktokUploadDetailEntity = new TiktokUploadDetailEntity();
				
				tiktokUploadDetailEntity.setRank(rank++);
				tiktokUploadDetailEntity.setDesc(post.getDesc());
				tiktokUploadDetailEntity.setVideoId(post.getPostId());
				tiktokUploadDetailEntity.setDuration(post.getVideo().getDuration());
				tiktokUploadDetailEntity.setAuthorId(post.getAuthor().getUid());
				tiktokUploadDetailEntity.setAuthorUniqueId(post.getAuthor().getUsername());
				tiktokUploadDetailEntity.setAuthorNickname(post.getAuthor().getNickname());
				tiktokUploadDetailEntity.setUri(String.format(TIKTOK_URI_TEMPLATE, post.getAuthor().getUsername(), post.getPostId()));
				
				Map<String, Number> authorStatsMap = new HashMap<String, Number>();
				authorStatsMap.put("authorfollowingCount", post.getAuthor().getFollowingCount());
				authorStatsMap.put("authorFollowerCount", post.getAuthor().getFollowerCount());
				
				tiktokUploadDetailEntity.setAuthorStats(gson.toJson(authorStatsMap));
				
				Map<String, Integer> videoStatsMap = new HashMap<String, Integer>();
				videoStatsMap.put("diggCount", post.getStatistics().getLikesCount());
				videoStatsMap.put("shareCount", post.getStatistics().getShareCount());
				videoStatsMap.put("commentCount", post.getStatistics().getCommentCount());
				videoStatsMap.put("playCount", post.getStatistics().getPlayCount());
				videoStatsMap.put("collectCount", post.getStatistics().getCollectCount());
				
				tiktokUploadDetailEntity.setVideoStats(gson.toJson(videoStatsMap));
				detailList.add(tiktokUploadDetailEntity);
				
			}
		}
		tiktokUploadInfoEntity.setDetailList(detailList);
		
		String key =  tiktokMessageEntity.getEngineId() + "_" + tiktokMessageEntity.getLanguageId() + "_d" + "_"+ IdUtil.fastUUID();
		boolean kafkaStatus = KafkaProducerUtil.sendMessage(tiktokMessageEntity.getKfkTpc(), gson.toJson(tiktokUploadInfoEntity), key, tiktokMessageEntity.getKeywordName(), false); //crawlerProperties.isScribeEnable() == false
		System.out.println("tpc:" + tiktokMessageEntity.getKfkTpc() + ", KafkaStatus '" + tiktokMessageEntity.getKeywordName() + "': " + kafkaStatus);
		
		System.out.println("==Delete from Queue!");
		SQSUtils.deleteMessageFromQueue(amazonSQS, queryUrl, msg, "0");
	}
	
	//convert dateformat from yyyyMMdd to yyyy-MM-dd
    public static String convertDateFormat(String input) {
        if (input == null || input.length() != 8) {
            return null;
        }

        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd");
        inputFormat.setLenient(false);  // 严格校验

        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

        try {
            Date date = inputFormat.parse(input);
            return outputFormat.format(date);
        } catch (ParseException e) {
            // 输入格式不符合 yyyyMMdd
            return null;
        }
    }
}