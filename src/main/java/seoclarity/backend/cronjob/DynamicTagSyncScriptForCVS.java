package seoclarity.backend.cronjob;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.GroupDictDAO;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.GroupTagRelationEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.ResourceAddDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceAddInfoEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.urlclaritydb.CrawlUrlDao;
import seoclarity.backend.entity.ResultContent;
import seoclarity.backend.entity.actonia.GroupDict;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.ResourceAddDetailEntity;
import seoclarity.backend.entity.actonia.ResourceAddInfoEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.actonia.ResourceDeleteDetailEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.crawlurl.TargetUrlHtmlDaily;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.DynamicTagSyncScriptForCVS" -Dexec.args=""
 * <AUTHOR>
 *
 */
public class DynamicTagSyncScriptForCVS {
	
//	private ClColdDailyRankingEntityDao clColdDailyRankingEntityDao;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private CrawlUrlDao crawlUrlDao;
	private GroupTagEntityDAO groupTagEntityDAO;
	private GroupTagRelationEntityDAO groupTagRelationEntityDAO;
	private KeywordEntityDAO keywordEntityDAO;
	private ResourceAddInfoEntityDAO resourceAddInfoEntityDAO;
	private ResourceAddDetailEntityDAO resourceAddDetailEntityDAO;
//	private ResourceDeleteInfoEntityDAO resourceDeleteInfoEntityDAO;
//	private ResourceDeleteDetailEntityDAO resourceDeleteDetailEntityDAO;
	private GroupDictDAO groupDictDAO;
	private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
	private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	
	public DynamicTagSyncScriptForCVS() {
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
//		clColdDailyRankingEntityDao = SpringBeanFactory.getBean("clColdDailyRankingEntityDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		crawlUrlDao = SpringBeanFactory.getBean("crawlUrlDao");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		groupTagRelationEntityDAO = SpringBeanFactory.getBean("groupTagRelationEntityDAO");
		resourceAddInfoEntityDAO = SpringBeanFactory.getBean("resourceAddInfoEntityDAO");
		resourceAddDetailEntityDAO = SpringBeanFactory.getBean("resourceAddDetailEntityDAO");
//		resourceDeleteDetailEntityDAO = SpringBeanFactory.getBean("resourceDeleteDetailEntityDAO");
//		resourceDeleteInfoEntityDAO = SpringBeanFactory.getBean("resourceDeleteInfoEntityDAO");
		groupDictDAO = SpringBeanFactory.getBean("groupDictDAO");
		resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
		resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");

	}
	
	private static String processDate = FormatUtils.formatDate(FormatUtils.getYesterday(true), "yyyy-MM-dd");
//	private static Integer processOID = 10619;
	private static final int DEFAULF_USER = 214;
	private static final SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
	public static final String MD5_SPLIT = "	";
	public static boolean isCheckAddDetailExists = false;
	private static String ROOT_DOMAIN = "com.cvs";
	
	public static void main(String[] args) {
		DynamicTagSyncScriptForCVS dynamicTagSyncScriptForTarget = new DynamicTagSyncScriptForCVS();
		dynamicTagSyncScriptForTarget.process();
		
	}
	
	private static String COMPANY_NAME = "CVS Health Corporation";

	private static Integer pageSize = 10000;
	private static String TAG_PREFIX = " (dt)";
//	private static Map<String, Integer> groupDictNameIdMap = new HashMap<>();
	
	private void process() {
		
		List<OwnDomainSettingEntity> ownDomainSettingEntities = ownDomainSettingEntityDAO.getDomainByCompanyName(COMPANY_NAME);
		if (ownDomainSettingEntities == null || ownDomainSettingEntities.size() == 0) {
			System.out.println(" do not find any domain base on company :" + COMPANY_NAME);
			return ;
		}
		
		System.out.println("Total " + ownDomainSettingEntities.size() + " domains need to process");
		for(OwnDomainSettingEntity ownDomainSettingEntity : ownDomainSettingEntities) {
			processByDomain(ownDomainSettingEntity.getOwnDomainId());
		}
	}
	
	private void processByDomain(Integer processOID) {
		
		Date yesterday = FormatUtils.getYesterday(true);
		List<TargetUrlHtmlDaily> cacheList = crawlUrlDao.getCustomDataByDate(processOID, FormatUtils.formatDate(yesterday, "yyyy-MM-dd"));
		
		Map<String, TargetUrlHtmlDaily> cacheMap = new HashMap<>();
		
		cacheMap = cacheList.stream().collect(Collectors.toMap(TargetUrlHtmlDaily::getUrl, Function.identity()));
		
		System.out.println("cacheMap size:" + cacheMap.size());
		
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(processOID);
		
		if (ownDomainEntity == null ) {
			System.out.println(" ownDomainEntity is not found!!!");
			return ;
		}
		
		Integer pageNum = 0;
		int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        
        String resourceSearchEngine = engineId + "-" + languageId + "-m";
        System.out.println("resourceSearchEngine:" + resourceSearchEngine);
        
//        //delete keyword-tag relation
//        List<Integer> groupTagList = groupTagEntityDAO.getTagEntityForDynamicTags(processOID, GroupTagEntity.TAG_TYPE_KEYWORD, TAG_PREFIX);
//        
//        if (groupTagList.size() > 0) {
//			System.out.println("Truncate keyword from tag, tag size:" + groupTagList.size());
//			System.out.println(new Gson().toJson(groupTagList));
//			Integer deleteTaskInfoId = deleteKeywordTagRelation(processOID, DEFAULF_USER, groupTagList);
////        	Integer deleteTaskInfoId = deleteTag(processOID, groupTagList);
//        	
//        	if (deleteTaskInfoId == null || deleteTaskInfoId == 0) {
//        		System.out.println("deleteTaskInfoId is empty!!!");
//				return;
//			}
//			
//			while (true) {
//				ResourceDeleteInfoEntity resourceDeleteInfoEntity = resourceDeleteInfoEntityDAO.getById(deleteTaskInfoId);
//				if (resourceDeleteInfoEntity.getStatus() == ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR
//						|| resourceDeleteInfoEntity.getStatus() == ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITH_ERROR) {
//					System.out.println("!!!TASK finished, deleteTaskInfoId: " + deleteTaskInfoId);
//					break;
//				}
//				try {
//            		Thread.sleep(30 * 1000);
//            		System.out.println("===TASK still running, deleteTaskInfoId: " + deleteTaskInfoId);
//            	} catch (Exception exp) {
//            		exp.printStackTrace();
//            	}
//			}
//        	
//		}
        
		
        while (true) {
        	//Alps: only query for desktop and national
        	List<CLRankingDetailEntity> resultList = clDailyRankingEntityDao.getManagedKeywordListForTarget(
        			processOID, engineId, languageId, processDate,
    				true, ROOT_DOMAIN, pageNum, pageSize);
        	
			if (resultList == null || resultList.size() == 0) {
    			break;
    		}
			
			pageNum ++ ;
			System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize + ", size:" + resultList.size());
			
			/**
			 *	keyword_name
			 *	keyword_rankcheck_id
			 *	url
			 */
			int num = 1;
			
			List<String[]> relations = new ArrayList<>();
			List<String[]> keywordTagParentchildRelList = new ArrayList<>();
			
			for(CLRankingDetailEntity entity : resultList) {
				
				System.out.println("pricessing on :" + num++);
				
				try {
					TargetUrlHtmlDaily targetUrlHtmlDaily = cacheMap.get(entity.getUrl());
					if (targetUrlHtmlDaily != null && StringUtils.isNotBlank(targetUrlHtmlDaily.getCustomData())) {
						
						try {
							ResultContent[] results = new Gson().fromJson(targetUrlHtmlDaily.getCustomData(), ResultContent[].class);
							
							for(ResultContent result : results) {
								
								if (result == null || result.getLinks() == null || result.getLinks().length == 0) {
//									System.out.println("Content is empty!");
									continue;
								}
								

								String[] tagList = new String[result.getLinks().length - 1];
								
								// first level should be skip!
								for(int i = 1 ; i <= result.getLinks().length -1 ; i ++) {
									String content = result.getLinks()[i].getAnchor_text();
									System.out.println("origin tagStr:" + content);
									
									String tagString = StringUtils.removeEnd(StringUtils.removeEnd(StringEscapeUtils.unescapeXml(FormatUtils.ascii2native(content)), "\\u003e"), ">");
							        
									tagList[i-1] = tagString;
							        
								}
								
								System.out.println("TagList:" + new Gson().toJson(tagList));
								
								Integer groupDictId = null;
						        // if length=1 means tag name equals to group name
						        if (tagList.length == 1) {
						        	
						        	//step 1. create or get group id
						        	String groupDictName = parseTagName(tagList[0]);
						        	String tagName = "";
						        	if (StringUtils.isNotBlank(groupDictName)) {
						        		tagName = groupDictName = groupDictName + TAG_PREFIX;
						        		//get group id from cache
//						        		if (groupDictNameIdMap.get(groupDictName) != null && groupDictNameIdMap.get(groupDictName) > 0) {
//							        		groupDictId = groupDictNameIdMap.get(groupDictName);
//							        		System.out.println("Getting group from Cache, id:" + groupDictId + ", groupDictName:" + groupDictName);
//										} else {
											//query if group exist
											GroupDict groupDict = groupDictDAO.findByNameAndOid(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
											if (groupDict != null && groupDict.getId() > 0) {
												groupDictId = groupDict.getId();
												System.out.println("Found Exist Group, id:" + groupDictId + ", groupDictName:" + groupDictName);
											} else {
												//else add new group
												groupDictDAO.insert(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
												groupDict = groupDictDAO.findByNameAndOid(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
												groupDictId = groupDict.getId();
												System.out.println("adding Group, id:" + groupDictId + ", groupDictName:" + groupDictName);
											}
											//add to cache
//											groupDictNameIdMap.put(groupDictName, groupDictId);
//										}
									}
						        	
						        	//step 2. create or get tag and update group id
						        	Integer groupTagId = 0;
						        	GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(processOID, tagName, GroupTagEntity.TAG_TYPE_KEYWORD);
						        	if (groupTagEntity != null && groupTagEntity.getId() > 0) {
						        		groupTagId = groupTagEntity.getId();
						        		if (groupTagEntity.getGroupId() == null || groupTagEntity.getGroupId() == 0) {
						        			System.out.println("Updating tag group relation, tagId:" + groupTagEntity.getId() + ", groupId:" + groupDictId);
											groupTagEntityDAO.updateGroupId(groupTagEntity.getId(), groupDictId);
										}
									} else {
										System.out.println("INS tag , tagName:" + tagName + ", groupId:" + groupDictId);
										groupTagId = groupTagEntityDAO.insertForDynamicTag(processOID, GroupTagEntity.TAG_TYPE_KEYWORD, tagName, tagName, groupDictId);
									}
						        	
						        	if (groupTagId == 0) {
										System.out.println(" @@@ Group Tag Id IS NOT SETTED correctly!!!");
									} else {
										relations.add(new String[] {entity.getKeywordName(), groupTagId + ""});
										System.out.println("Relation:" + entity.getKeywordName() + ", " + groupTagId + "");
									}
						        			
								} else if (tagList.length > 1) {
									
									//step 1. create or get group id
						        	String groupDictName = parseTagName(tagList[0]);
						        	if (StringUtils.isNotBlank(groupDictName)) {
						        		groupDictName = groupDictName + TAG_PREFIX;
						        		//get group id from cache
//						        		if (groupDictNameIdMap.get(groupDictName) != null && groupDictNameIdMap.get(groupDictName) > 0) {
//							        		groupDictId = groupDictNameIdMap.get(groupDictName);
//							        		System.out.println("Getting group from Cache, id:" + groupDictId + ", groupDictName:" + groupDictName);
//										} else {
											//query if group exist
											GroupDict groupDict = groupDictDAO.findByNameAndOid(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
											if (groupDict != null && groupDict.getId() > 0) {
												groupDictId = groupDict.getId();
												System.out.println("Found Exist Group, id:" + groupDictId + ", groupDictName:" + groupDictName);
											} else {
												//else add new group
												groupDictDAO.insert(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
												groupDict = groupDictDAO.findByNameAndOid(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
												groupDictId = groupDict.getId();
												System.out.println("adding Group, id:" + groupDictId + ", groupDictName:" + groupDictName);
											}
											//add to cache
//											groupDictNameIdMap.put(groupDictName, groupDictId);
//										}
									}
						        	
									
									for(int i = 0 ; i <= tagList.length - 1 ; i++) {
										//step 2. create or get tag and update group id
							        	Integer groupTagId = 0;
							        	String tagName = getTagName(tagList, i + 1) + TAG_PREFIX;
							        	System.out.println("len " + i + ", tagN:" + tagName);
							        	String friendlyName = parseTagName(tagList[i]) + TAG_PREFIX;
							        	
							        	
							        	GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(processOID, tagName, GroupTagEntity.TAG_TYPE_KEYWORD);
							        	if (groupTagEntity != null && groupTagEntity.getId() > 0) {
							        		groupTagId = groupTagEntity.getId();
							        		if ((groupTagEntity.getGroupId() == null || groupTagEntity.getGroupId() == 0) && i == 0) {
												groupTagEntityDAO.updateGroupId(groupTagEntity.getId(), groupDictId);
												System.out.println("Update group tag, id:" + groupTagId + ", groupDictId:" + groupDictId + ", tagName:" + tagName);
											}
										} else {
											groupTagId = groupTagEntityDAO.insertForDynamicTag(processOID, GroupTagEntity.TAG_TYPE_KEYWORD, tagName, friendlyName, groupDictId);
											System.out.println("Adding group tag, id:" + groupTagId + ", groupDictId:" + groupDictId + ", tagName:" + tagName);
										}
							        	
							        	if (groupTagId == 0) {
											System.out.println(" @@@ Group Tag Id IS NOT SETTED correctly!!!");
										} else {
											relations.add(new String[] {entity.getKeywordName(), groupTagId + ""});
											//String[childTag, parentTag]
											if (i > 0) {
												String parentTagName = getTagName(tagList, i) + TAG_PREFIX;
												System.out.println("len " + i + ", tagN:" + tagName + ", parentTagName:" + parentTagName);
									        	keywordTagParentchildRelList.add(new String[] {tagName, parentTagName});
											}
											System.out.println("Relation:" + entity.getKeywordName() + ", " + groupTagId + "");
										}
							        }
								}
						        
							}
							
						} catch (Exception e) {
							e.printStackTrace();
						}
						
					} else {
						System.out.println("@@@ NOT found in dis_target_url_custom_data, url :" + entity.getUrl());
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				
			}
			
			
			
			if (relations.size() > 0) {
				System.out.println("Relation size : " + relations.size());
			}
			
			if (keywordTagParentchildRelList.size() > 0) {
				System.out.println("keywordTagParentchildRelList size : " + keywordTagParentchildRelList.size());
			}
			
			
			List<ResourceAddDetailEntity> addList = new ArrayList<ResourceAddDetailEntity>();
			for (String[] rel : relations) {
				String kw = rel[0];
				String tag = rel[1];
				if (StringUtils.isNotBlank(kw) && StringUtils.isNotBlank(tag)) {
	    			ResourceAddDetailEntity detail = new ResourceAddDetailEntity();
	    			detail.setResourceMain(kw);
	    			detail.setResourceSubordinate(tag);
	    			detail.setOwnDomainId(processOID);
	    			detail.setId(null);
					detail.setStatus(null);
					detail.setProcessDate(null);
					detail.setErrorMessage(null);
					detail.setStatusRank(null);
					detail.setErrorMessageRank(null);
					detail.setResourceSearchengines(resourceSearchEngine);
	    			addList.add(detail);
				}
			}
			if (relations.size() == 0) {
				System.out.println("no details... skip");
				return;
			}
			ResourceAddInfoEntity info =  addResourceAddInfo(processOID, ResourceAddInfoEntity.OPERATION_TYPE_KEYWORD_TAGID);
			resourceAddInfoEntityDAO.updateStatusAfterProcess(info.getId(), ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, new Date());
			addAddDetail(addList, info.getId());
			resourceAddInfoEntityDAO.updateStatusAfterProcess(info.getId(), ResourceAddInfoEntity.STATUS_NEWLY_CREATED, new Date());
			
			System.out.println("===== create queue task for tag parent child rel");
			int actionType = ResourceBatchInfoEntity.TYPE_ADD;
	        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_TAG_PARENT_CHILD_REL ;
        	try {
                ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
                resourceBatchInfoEntity.setActionType(actionType);
                resourceBatchInfoEntity.setOwnDomainId(processOID);
                resourceBatchInfoEntity.setOperationType(operationType);
                resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
                resourceBatchInfoEntity.setUserId(214);
                resourceBatchInfoEntity.setCreateDate(new Date());
                resourceBatchInfoEntity.setCustomFlag(2);
                resourceBatchInfoEntity.setStatusRankcheck(0);
                long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
                
                resourceBatchInfoEntityDAO.updateStatusAfterProcess(id, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, new Date());
                
                List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
                for (String[] rel : keywordTagParentchildRelList) {
                	
                	if (StringUtils.isBlank(rel[1]) || StringUtils.isBlank(rel[0])) {
						System.out.println("==== Add Queue detail Err, Tagid is not set correctly ");
                		continue;
					}
                	
                    ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
                    rbd.setInfoId(id);
                    rbd.setActionType(actionType);
                    rbd.setOwnDomainId(processOID);
                    rbd.setResourceMain(rel[0]);
                    rbd.setResourceSubordinate(rel[1]);
                    rbd.setResourceCategory(2);
                    rbd.setResourceMd5(Md5Util.Md5(rel[0] + rel[1] + FormatUtils.formatDate(new Date(), "yyyy-MM-dd") + ""));
                    rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
                    resourceBatchDetailEntityList.add(rbd);
                }
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("Qbs====>create new info, id: " + id + ", resourceBatchDetailEntityList size: " + resourceBatchDetailEntityList.size());
            
                resourceBatchInfoEntityDAO.updateStatusAfterProcess(id, ResourceAddInfoEntity.STATUS_NEWLY_CREATED, new Date());
        	} catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
	}
	
	private String getTagName(String[] tagList, Integer num) {
		
		StringBuffer sb = new StringBuffer();
		int cnt = 0;
		for(String tag : tagList) {
			cnt ++;
			if (cnt > num ) {
				break;
			}
			sb.append(parseTagName(tag) + ">");
			
		}
		
		return StringUtils.removeEnd(sb.toString(), ">");
	}
	
	
//	public Integer deleteTag(int oid, List<Integer> tags) {
//		List<ResourceDeleteDetailEntity> needProcessList = new ArrayList<ResourceDeleteDetailEntity>();
//		for (Integer tagId : tags) {
//			ResourceDeleteDetailEntity deleteDetailEntity = null;
//			if (tagId != 0) {
//				deleteDetailEntity = new ResourceDeleteDetailEntity();
//				deleteDetailEntity.setOwnDomainId(oid);
//				deleteDetailEntity.setResourceType(ResourceDeleteDetailEntity.RESOURCE_TYPE_TAG_BY_ID);
//				deleteDetailEntity.setResourceId(tagId);
//				
//				needProcessList.add(deleteDetailEntity);
//			} else {
//				System.out.println("Bad resource, tagId:" + tagId);
//			}
//		}
//		return this.batchAddDelete(needProcessList, oid, ResourceDeleteDetailEntity.RESOURCE_TYPE_TAG_BY_ID);
//	}
	
//	public Integer batchAddDelete(List<ResourceDeleteDetailEntity> delList, int oid, int resourceType) {
//		if (delList.size() > 0) {
//			try {
//				// add deleteInfo
//				ResourceDeleteInfoEntity deleteInfoEntity = addDeleteInfo(oid);
//				resourceDeleteInfoEntityDAO.updateRankcheckStatusAfterProcess(deleteInfoEntity.getId(), ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, null);
//				resourceDeleteInfoEntityDAO.updateRankcheckStatusAfterProcess(deleteInfoEntity.getId(), ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, null);
//				// add details
//				addDeleteDetail(delList, deleteInfoEntity.getId());
//				resourceDeleteInfoEntityDAO.updateRankcheckStatusAfterProcess(deleteInfoEntity.getId(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, null);
//				resourceDeleteInfoEntityDAO.updateRankcheckStatusAfterProcess(deleteInfoEntity.getId(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, null);
//				
//				return deleteInfoEntity.getId();
//			
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//		return null;
//	}
	
//	protected void addDeleteDetail(List<ResourceDeleteDetailEntity> deleteDetailList, int deleteInfoId) {
//		int createDate = Integer.valueOf(sf.format(new Date()));
//		int count = 0;
//		int exists = 0;
//		for (ResourceDeleteDetailEntity deleteDetailEntity : deleteDetailList) {
//			deleteDetailEntity.setDeleteInfoId(deleteInfoId);
//			deleteDetailEntity.setCreateDate(createDate);
//			try {
//				createMD5ForDeleteDetail(deleteDetailEntity);
//				try {
//					resourceDeleteDetailEntityDAO.insert(deleteDetailEntity);
//				} catch (Exception e) {
//					exists++;
//				}
//				count++;
//			} catch (Exception e){
//				System.out.println("insert error, deleteInfo id:" + deleteInfoId);
//				e.printStackTrace();
//			}
//		}
//		System.out.println("Create delete details, deleteInfoId:" + deleteInfoId + ", insert count:" + count + ", exists:" + exists);
//	}
	
	private void createMD5ForDeleteDetail(ResourceDeleteDetailEntity detailEntity) throws Exception{
		String md5 = calculateMd5(String.valueOf(detailEntity.getResourceId()), detailEntity.getResourceSubId() + "");
		if (!StringUtils.isEmpty(md5)) {
			detailEntity.setResource_md5(md5);
		} else {
			throw new Exception("MD5 not created...");
		}
	}
	
	
//	protected ResourceDeleteInfoEntity addDeleteInfo(int ownDomainId) {
//		ResourceDeleteInfoEntity deleteInfoEntity = new ResourceDeleteInfoEntity();
//		deleteInfoEntity.setEnabled(ResourceDeleteInfoEntity.ENABLED);
//		deleteInfoEntity.setOwnDomainId(ownDomainId);
//		deleteInfoEntity.setUserId(DEFAULF_USER);
//		deleteInfoEntity.setCreateDate(new Date());
//		deleteInfoEntity.setStatus(ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED);
//		deleteInfoEntity.setStatusRankcheck(0);
//		int id = resourceDeleteInfoEntityDAO.insert(deleteInfoEntity);
//		deleteInfoEntity.setId(id);
//		System.out.println("Create deleteInfo, id:" + id + ", OID:" + ownDomainId);
//		return deleteInfoEntity;
//	}
	
//	private Integer deleteKeywordTagRelation(Integer ownDomainId, Integer userId, List<Integer> tagIds) {
//		
//		Integer infoId = saveResourceDeleteInfo(ownDomainId, userId);
//		
//		System.out.println("Create queue base for truncate keyword from tag, task infoID : " + infoId);
//		
//		Long maxKeywordTagRelationId = groupTagRelationEntityDAO.getMaxKeywordTagRelationId(ownDomainId, GroupTagEntity.TAG_TYPE_KEYWORD);
//		for (Integer gtid : tagIds) {
//			saveResourceDeleteDetail(infoId, ownDomainId, ResourceDeleteDetailEntity.RESOURCE_TYPE_DISASSOCIATE_ALL_KEYWORD_FROM_TAG, 
//					maxKeywordTagRelationId, gtid);
//		}
//		return infoId;
//	}
	
	
//	private void saveResourceDeleteDetail(int infoId, int ownDomainId, int resourceType, Long resourceId, Number resourceSubId) {
//		try {
//			resourceDeleteDetailEntityDAO.insert(infoId, ownDomainId, resourceType, resourceId, resourceSubId, null);
//		} catch (DuplicateKeyException ex) {
//			ex.printStackTrace();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
	
//	private Integer saveResourceDeleteInfo(int ownDomainId, int userId) {
//		Integer infoId = resourceDeleteInfoEntityDAO.insert(ownDomainId, userId);
//		return infoId;
//	}
	
	protected void addAddDetail(List<ResourceAddDetailEntity> detailList, int infoId) {
		int createDate = Integer.valueOf(sf.format(new Date()));
		int count = 0;
		for (ResourceAddDetailEntity detail : detailList) {
			detail.setResourceInfoId(infoId);
			detail.setCreateDate(createDate);
			if (detail.getUserId() == null) {
				detail.setUserId(DEFAULF_USER);
			}
			try {
				if (StringUtils.isEmpty(detail.getResource_md5())) {
					String md5 = calculateMd5(String.valueOf(detail.getResourceMain()), detail.getResourceSubordinate() + "");
					detail.setResource_md5(md5);
				}
				if (isCheckAddDetailExists && isExistsDetail(detail.getOwnDomainId(), detail.getResource_md5(), detail.getCreateDate())) {
					System.out.println("Exists detail, infoId:" + detail.getResourceInfoId() + ", OID:" + detail.getOwnDomainId()
					+ ", resourceMain:" + detail.getResourceMain() 
					+ " resourceSub:" + detail.getResourceSubordinate() + ", OperationType" + detail.getOperationType() + ", md5:" + detail.getResource_md5() 
					+ ", createDate:" + detail.getCreateDate());
				} else {
					resourceAddDetailEntityDAO.insertForSpecialSE(detail);
				}
				count++;
			} catch (Exception e) {
				System.out.println("insert error, resourceAddInfoId:" + infoId + ", ERROR:" + e.getMessage());
//				e.printStackTrace();
			}
		}
		System.out.println("Create add details, infoId:" + infoId + ", insert count:" + count);
	}
	
	public static String calculateMd5(String valueMain, String valueSubordinate) {
		String input = valueMain;
		if (StringUtils.isNotBlank(valueSubordinate)) {
			input = input + MD5_SPLIT + valueSubordinate;
		}
		return FormatUtils.hexDigest(input.getBytes());
	}
	
	protected boolean isExistsDetail(int oid, String resourceMD5, int createDate) {
		ResourceAddDetailEntity detail = resourceAddDetailEntityDAO.checkExists(oid, resourceMD5, createDate);
		if (detail == null) {
			return false;
		}
		return true;
	}
	
	 public KeywordEntity checkKeyword(String name, int ownDomainId) throws Exception {
        KeywordEntity keywordEntity = null;
        String keywordName = formatKeywordForInsert(name.toLowerCase());
        try {
            if (!StringUtils.isEmpty(keywordName)) {
                keywordName = URLEncoder.encode(keywordName, "UTF-8");
            }
            keywordName = CommonDataService.formatEncodeKeywordForInsert(keywordName);
            if (!StringUtils.isEmpty(keywordName)) {
                keywordName = keywordName.toLowerCase();
                keywordName = StringUtils.removeStart(keywordName, "%EF%BB%BF".toLowerCase());
                keywordEntity = getKeywordByKeywordName(keywordName, ownDomainId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return keywordEntity;
    }
 
	public static String formatKeywordForInsert(String keywordStr) {
		if (StringUtils.isBlank(keywordStr)) {
			return null;
		}
		
		keywordStr = StringUtils.chomp(keywordStr);
		keywordStr = StringUtils.chomp(keywordStr);
		
		// remove invisible characters
		keywordStr = keywordStr.replaceAll("\\s", " ");
		
		// remove multi-space characters to one space
		keywordStr = keywordStr.replaceAll(" +", " ");
		
		// remove START and END space
		keywordStr = StringUtils.stripToEmpty(keywordStr);
		
		return keywordStr;
	}
 
    public KeywordEntity getKeywordByKeywordName(String keywordName, int ownDomainId) {
        KeywordEntity keywordEntity = keywordEntityDAO.findFirstByKeywordNameAndRanked(keywordName, ownDomainId);
        if (keywordEntity == null) {
            keywordEntity = keywordEntityDAO.findFirstByKeywordName(keywordName, ownDomainId);
        }
        return keywordEntity;
    }

    private String parseTagName(String tagName) {
    	if (StringUtils.contains(tagName, '\u200e')) {
    		String[] tagNameSubArray = StringUtils.split(tagName, '\u200e');
    		for(String tag : tagNameSubArray) {
	    	   if (!StringUtils.contains(tag, '\u200e')) {
	    		   tagName = tag;
	   				break;
	   			}
    		}
		}
    	
    	tagName = StringUtils.trim(tagName);
    	tagName = StringUtils.replace(tagName, "\t", "");
    	
    	return tagName;
    }
	
	public ResourceAddInfoEntity addResourceAddInfo(int ownDomainId, int operationType) {
		ResourceAddInfoEntity info = new ResourceAddInfoEntity();
		info.setOperationType(operationType);
		info.setOwnDomainId(ownDomainId);
		info.setUserId(DEFAULF_USER);
		info.setCreateDate(new Date());
		info.setStatus(ResourceAddInfoEntity.STATUS_NEWLY_CREATED);
		int id = resourceAddInfoEntityDAO.insert(info);
		info.setId(id);
		System.out.println("Create addInfo, id:" + id + ", OID:" + ownDomainId);
		return info;
	}
	
	
}