package seoclarity.backend.cronjob;

import java.util.ArrayList;
import java.util.List;

import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.UpdateTagNameFor9742" -Dexec.args=""
public class UpdateTagNameFor9742 {
	
	private GroupTagEntityDAO groupTagEntityDAO;
	
	private static List<Integer> tagList = new ArrayList<>();
	
	static {
		tagList.add(7384759);
		tagList.add(7384445);
		tagList.add(7384105);
		tagList.add(7384665);
		tagList.add(7384297);
		tagList.add(7384253);
		tagList.add(7384244);
		tagList.add(7384446);
		tagList.add(7384175);
		tagList.add(7384690);
		tagList.add(7384286);
		tagList.add(7384548);
		tagList.add(7383949);
		tagList.add(7384040);
		tagList.add(7384691);
		tagList.add(7384477);
		tagList.add(7384685);
		tagList.add(7384733);
		tagList.add(7383991);
		tagList.add(7384686);
		tagList.add(7383998);
		tagList.add(7384295);
		tagList.add(7384239);
		tagList.add(7384134);
		tagList.add(7384707);
		tagList.add(7384384);
		tagList.add(7384647);
		tagList.add(7384019);
		tagList.add(7384638);
		tagList.add(7384513);
		tagList.add(7384742);
		tagList.add(7384623);
		tagList.add(7384116);
		tagList.add(7384026);
		tagList.add(7383834);
		tagList.add(7384718);
		tagList.add(7383937);
		tagList.add(7384681);

	}
	
	public UpdateTagNameFor9742(){
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
	}
	
	public static void main(String[] args) {
		UpdateTagNameFor9742 updateTagNameFor9742 = new UpdateTagNameFor9742();
		updateTagNameFor9742.process();
	}
	
	private static Integer oid = 9742;
	
	private void process() {
		
		List<GroupTagEntity> dynamicTagList = groupTagEntityDAO.getDynamicTagList(oid);
		
		System.out.println("size:" + dynamicTagList.size());
		groupTagEntityDAO.updateTagName(tagList, dynamicTagList, oid);
		
	}
	
	

}












