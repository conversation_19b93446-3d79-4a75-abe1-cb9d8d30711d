package seoclarity.backend.cronjob.onetime;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;
//mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.onetime.DownloadFileFromSearchMetricsV2" -Dexec.args=""
public class DownloadFileFromSearchMetricsV2 {
	public static final int TRY_LIMIT = 3;
	public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	
	private static Map<Integer, String> domainMap = new HashMap<>();
	static {
//		domainMap.put(11545, "[{\"project_id\": 1520849, \"project_name\": \"New Profile UK (Reviewed)\", \"project_url\": \"skyscanner.net\", \"keyword_count\": 7241, \"engine_count\": 2, \"engines\": [ \"4\", \"1231\"]}]");
//		domainMap.put(11550, "[{\"project_id\": 1521490, \"project_name\": \"New Profile DE (Reviewed)\", \"project_url\": \"skyscanner.de\", \"keyword_count\": 6313, \"engine_count\": 2, \"engines\": [ \"1\", \"1229\"]}]");
		domainMap.put(11551, "[{\"project_id\": 1521518, \"project_name\": \"New Profile JP (Reviewed)\", \"project_url\": \"skyscanner.jp\", \"keyword_count\": 7358, \"engine_count\": 3, \"engines\": [ \"105\"]}]");
//		domainMap.put(11551, "[{\"project_id\": 1521518, \"project_name\": \"New Profile JP (Reviewed)\", \"project_url\": \"skyscanner.jp\", \"keyword_count\": 7358, \"engine_count\": 3, \"engines\": [ \"104\", \"105\", \"1305\"]}]");
//		domainMap.put(11552, "[{\"project_id\": 1521488, \"project_name\": \"New Profile RU (Reviewed)\", \"project_url\": \"skyscanner.ru\", \"keyword_count\": 5903, \"engine_count\": 3, \"engines\": [ \"22\", \"1297\"]}]");
//		domainMap.put(11553, "[{\"project_id\": 1521625, \"project_name\": \"New Profile AE (Reviewed)\", \"project_url\": \"skyscanner.ae\", \"keyword_count\": 5490, \"engine_count\": 2, \"engines\": [ \"174\", \"1343\"]}]");
//		domainMap.put(11546, "[{\"project_id\": 1520859, \"project_name\": \"New Profile US (Reviewed)\", \"project_url\": \"skyscanner.com\", \"keyword_count\": 6482, \"engine_count\": 2, \"engines\": [ \"29\", \"1227\"]}]");
//		domainMap.put(11548, "[{\"project_id\": 1520829, \"project_name\": \"New Profile AU (Reviewed)\", \"project_url\": \"skyscanner.com.au\", \"keyword_count\": 5769, \"engine_count\": 2, \"engines\": [ \"99\", \"1253\"]}]");
//		domainMap.put(11549, "[{\"project_id\": 1521497, \"project_name\": \"New Profile BR (Reviewed)\", \"project_url\": \"skyscanner.com.br\", \"keyword_count\": 4679, \"engine_count\": 2, \"engines\": [ \"66\", \"1337\"]}]");
//		domainMap.put(11554, "[{\"project_id\": 1521713, \"project_name\": \"New Profile ES (Reviewed)\", \"project_url\": \"skyscanner.es\", \"keyword_count\": 6521, \"engine_count\": 2, \"engines\": [ \"13\", \"1257\"]}]");

//		domainMap.put(11547, "[{\"project_id\": 1520830, \"project_name\": \"New Profile CA (Reviewed)\", \"project_url\": \"skyscanner.ca\", \"keyword_count\": 6796, \"engine_count\": 2, \"engines\": [\"96\", \"1382\"]}]");

		
//		domainMap.put(11555, "[{\"project_id\": 1521623, \"project_name\": \"New Profile ID (Reviewed)\", \"project_url\": \"skyscanner.co.id\", \"keyword_count\": 2528, \"engine_count\": 2, \"engines\": [ \"133\", \"1275\"]}]");
//		domainMap.put(11556, "[{\"project_id\": 1521618, \"project_name\": \"New Profile IN (Reviewed)\", \"project_url\": \"skyscanner.co.in\", \"keyword_count\": 6918, \"engine_count\": 2, \"engines\": [ \"117\", \"1380\"]}]");
//		domainMap.put(11557, "[{\"project_id\": 1521708, \"project_name\": \"New Profile IT (Reviewed)\", \"project_url\": \"skyscanner.it\", \"keyword_count\": 6857, \"engine_count\": 2, \"engines\": [ \"10\", \"1259\"]}]");
//		domainMap.put(11558, "[{\"project_id\": 1521709, \"project_name\": \"New Profile NL (Reviewed)\", \"project_url\": \"skyscanner.nl\", \"keyword_count\": 5854, \"engine_count\": 2, \"engines\": [ \"54\", \"1248\"]}]");
//		domainMap.put(11559, "[{\"project_id\": 1521662, \"project_name\": \"New Profile PL (Reviewed)\", \"project_url\": \"skyscanner.pl\", \"keyword_count\": 4299, \"engine_count\": 2, \"engines\": [ \"63\", \"1322\"]}]");
//		domainMap.put(11560, "[{\"project_id\": 1521656, \"project_name\": \"New Profile TR (Reviewed)\", \"project_url\": \"skyscanner.com.tr\", \"keyword_count\": 5704, \"engine_count\": 2, \"engines\": [ \"68\", \"1405\" ]}]");
//		domainMap.put(11561, "[{\"project_id\": 1521765, \"project_name\": \"New Profile FR (Reviewed)\", \"project_url\": \"skyscanner.fr\", \"keyword_count\": 6716, \"engine_count\": 2, \"engines\": [ \"7\", \"1233\"]}]");
//		domainMap.put(11562, "[{\"project_id\": 1521779, \"project_name\": \"New Profile MX (Reviewed)\", \"project_url\": \"skyscanner.com.mx\", \"keyword_count\": 6391, \"engine_count\": 2, \"engines\": [ \"51\", \"1319\"]}]");
//		domainMap.put(11563, "[{\"project_id\": 1521831, \"project_name\": \"New Profile KR (Reviewed)\", \"project_url\": \"skyscanner.co.kr\", \"keyword_count\": 1392, \"engine_count\": 2, \"engines\": [ \"134\", \"1370\"]}]");

	}
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
    
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	
	private OwnDomainEntityDAO ownDomainEntityDAO;
	
	public DownloadFileFromSearchMetricsV2() {
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
	}
	
	public static void main(String[] args) throws IOException {
		DownloadFileFromSearchMetricsV2 downloadFileFromSearchMetricsV2 = new DownloadFileFromSearchMetricsV2();
		
		for(Integer ownDomainId : domainMap.keySet()) {
			Response[] projectArrays = new Gson().fromJson(domainMap.get(ownDomainId), Response[].class);
			
			for(Response project : projectArrays) {
				
				downloadFileFromSearchMetricsV2.processProject(ownDomainId, project);
			}
		}
		
		
	}
	
    private void processProject(Integer ownDomainId, Response project) throws IOException {
    	
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
		
		if (ownDomainEntity == null ) {
			System.out.println(" ownDomainEntity is not found!!!");
			return ;
		}
		
		int searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int searchLanguageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        
       
        
        
    	System.out.println(" ## process project :" + project.getProject_id() + ", name :" + project.getProject_name() + ", domain :" + project.getProject_url());
    	System.out.println(" ## process searchEngineId :" + searchEngineId + ", searchLanguageId:" + searchLanguageId);
    	refreshAccessToken();
		for(Integer engineId : project.getEngines()) {
//        		Response project, Integer searchEngineId, 
//        		Integer searchLanguageId, Integer engineId, Integer ownDomainId, String ownDomainName
			if (engineId == 104) {
				//yahoo, JP_ja
				searchEngineId = 100;
			}
    		processEngine(project, searchEngineId, searchLanguageId, engineId, ownDomainId, project.getProject_url());
    	}
    	
    	
    }
    
    private static String token = "";
    
    private static String apiPath = "https://api.searchmetrics.com/v4/ProjectOrganicGetListRankingsS7.json?access_token=%s&project_id=%s&se_id=%d&url=%s&limit=%d&offset=%d";
    private static String rankApiPath = "https://api.searchmetrics.com/v4/ProjectOrganicGetListRankingsAnalysisS7.json?access_token=%s&project_id=%s&se_id=%d&keyword=%s&date=%d&limit=%d&offset=%d";
    
    private static Gson gson = new Gson();
    
    private static Integer pageSize = 250;
    
    private static final String secret = "eac9011d5c0a1bae706983b0bab503a08548e461";
    private static final String clientId = "e726987e57b0447a491737abc2e83e011b268da4";
    private static final String refreshTokenApi = "https://api.searchmetrics.com/v4/token";
    
    private static Integer refreshTime = NumberUtils.toInt(FormatUtils.formatDate(new Date(), "ddHHmm"));
    
	public static void refreshAccessToken() {
		
		Integer currentTime = NumberUtils.toInt(FormatUtils.formatDate(new Date(), "ddHHmm"));
		
		if (StringUtils.isNotBlank(token) && currentTime - refreshTime < 85) {
			return;
		}
		
		System.out.println("===== refreshing token :" + currentTime);
		
		/*
		 *  {
			    "access_token": "**********************************************************************",
			    "expires_in_sec": 3600,
			    "api_domain": "https://www.zohoapis.com",
			    "token_type": "Bearer",
			    "expires_in": 3600000
			}
		 */
		
		List<NameValuePair> paramMap = new ArrayList<>();
		
		NameValuePair nameValuePair = new BasicNameValuePair("grant_type", "client_credentials");
		paramMap.add(nameValuePair);
		
		String accountResp =  queryWebServiceFunctionPost(refreshTokenApi, null, clientId, secret, paramMap);
		
		try {
			
			System.out.println("accountResp:" + accountResp);
			
			Map<String, Object> map = gson.fromJson(accountResp, Map.class);
			token = map.get("access_token") != null ? map.get("access_token").toString() : "";
			System.out.println("Updated accessToken : " + token);
			
			refreshTime = NumberUtils.toInt(FormatUtils.formatDate(new Date(), "ddHHmm"));
		} catch (Exception e) {
			System.out.println("Err MSG :" + accountResp);
			e.printStackTrace();
			throw e;
		}
		
	}
	
	public static String queryWebServiceFunctionPost(
            String webServiceUrl, 
            String stringBody, 
            String userName, 
            String password,
            List<NameValuePair> paramMap) {
        
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        
    	String authentication = base64Encode(userName, password);
		if (StringUtils.isNotBlank(authentication)) {
			header.put("Authorization", "Basic " + authentication);
		} else {
			System.out.println("Unauthorization was found!!!");
			return "Unauthorization was found!!!";
		}
        
        System.out.println("API-POST: " + webServiceUrl);
        System.out.println("API-POST Body: " + stringBody);
        
        HttpPost post = postStringBody(webServiceUrl, header, stringBody, paramMap);
        
        return queryWebServiceByPost(post);
    }
	
	private static String queryWebServiceByPost(
	        HttpPost post) {

	    if (post == null) {
	        return "";
	    }
	
	    InputStream inputStream = null;
	    HttpResponse response = null;
	    HttpClient client = HttpClientBuilder.create().build();
	    try {
	        response = client.execute(post);
	
	        int responseCode = response.getStatusLine().getStatusCode();
	        System.out.println("API-POST resp code:" + responseCode);
	
	        inputStream = response.getEntity().getContent();
	        String html = IOUtils.toString(inputStream, "UTF-8");
	        if (StringUtils.isNotBlank(html)) {
	            return html;
	        }
	
	        if (responseCode == 404) {
	        	System.out.println("API-POST RespCode : " + responseCode + "; (No Data)");
	            return "";
	        }
	
	
	    } catch (Exception e) {
	        e.printStackTrace();
	        System.out.println("API-POST: " + e.getMessage());
	    }  finally {
	        if (inputStream != null) {
	            try {
	                inputStream.close();
	            } catch (IOException e) {
	                e.printStackTrace();
	            }
	        }
	        if (post != null) {
	            post.releaseConnection();
	        }
	        if (response != null) {
	            HttpClientUtils.closeQuietly(response);
	        }
	        if (client != null) {
	            HttpClientUtils.closeQuietly(client);
	        }
	    }
	
	    return "";
	}
	
	public static HttpPost postStringBody(String url, Map<String, String> header, String stringBody, List<NameValuePair> paramMap) {
        HttpPost post = new HttpPost(url);
        for (String key : header.keySet()) {
            post.addHeader(key, header.get(key));
        }
        
        try {
        	
        	if (StringUtils.isNotBlank(stringBody)) {
        		StringEntity str = new StringEntity(stringBody, "UTF-8");
  	            post.setEntity(str);
			} else if (CollectionUtils.isNotEmpty(paramMap)) {
				post.setEntity(new UrlEncodedFormEntity(paramMap,"UTF-8"));
			}
        	
        	System.out.println("param:" + gson.toJson(post.getEntity()));
    		
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return post;
    }
    
    private void processEngine(Response project, Integer searchEngineId, 
    		Integer searchLanguageId, Integer engineId, Integer ownDomainId, String ownDomainName) throws IOException {
    	
    	System.out.println(" ==== process searchmetrics engineId: " + engineId);
    	System.out.println(" ==== process searchEngineId: " + searchEngineId);
    	System.out.println(" ==== process searchLanguageId: " + searchLanguageId);
    	//&project_id=%s&se_id=%d&url=%s&limit=%d&offset=%d
    	
    	List<CLRankingDetailEntity> keywordDateList = clDailyRankingEntityDao.getKeywordInfo(ownDomainId, searchEngineId, searchLanguageId, 0, 
				"2021-10-03", "2022-11-06",
        		DownloadFileFromSearchMetricsV2Command.mobileEngineList.contains(engineId));
    	
    	Map<String, List<String>> keywordDateMap = new HashMap<>();
    	
    	List<String> dateList = new ArrayList<>();
    	
    	for(CLRankingDetailEntity cLRankingDetailEntity : keywordDateList) {
    		dateList = keywordDateMap.get(cLRankingDetailEntity.getKeywordName());
    		if (CollectionUtils.isEmpty(dateList)) {
				dateList = new ArrayList<>();
			}
    		
    		dateList.add(cLRankingDetailEntity.getRankingDate());
    		keywordDateMap.put(cLRankingDetailEntity.getKeywordName(), dateList);
    		
    	}
    	
    	System.out.println("keywordDateMap size: " + keywordDateMap.keySet().size());
    	
    	for(String keywordName : keywordDateMap.keySet()) {
    		System.out.println("keywordName:" + keywordName + ", " + new Gson().toJson(keywordDateMap.get(keywordName)));
    		
    		break;
    	}
    	List<RankResponse> keywordList = new ArrayList<>();
    	
    	Integer pageNum = 0;
    	
    	while (true) {
    		pageNum++;
    		
    		String apiFullPath = String.format(apiPath, token, project.getProject_id(), engineId, 
    				project.getProject_url(), pageSize, pageSize * (pageNum - 1));
    		
    		System.out.println("=== apiFullPath:" + apiFullPath);
        	String resp = getRespFromApi(apiFullPath);
        	
        	try {
        		MainResponse mainResponse = gson.fromJson(resp, MainResponse.class);
            	if (mainResponse == null || mainResponse.getResponse() == null) {
            		System.out.println("No Response from API");
            		System.out.println("!!! Resp:" + resp);
            		return ;
            	}
            	
            	for(RankResponse rankResponse : mainResponse.getResponse()) {
            		SeoClarityKeywordEntity seoClarityKeywordEntity = getRankCheckId(rankResponse.getKeyword());
            		if (seoClarityKeywordEntity == null) {
            			
            			System.out.println("skip+++++++++++++");
            			return;
            		}
            		Integer kid = seoClarityKeywordEntity.getId();
            		rankResponse.setKeywordRankCheckId(kid);
            		keywordList.add(rankResponse);
            	}
            	
            	if (mainResponse == null || mainResponse.getResponse() == null || mainResponse.getResponse().length < pageSize) {
        			break;
        		}
            	
            	Thread.sleep(5 * 1000);
    		} catch (Exception e) {
    			// TODO: handle exception
    		}
		}
    	
    	System.out.println("Total size:" + keywordList.size());
    	

    	
//    	for(RankResponse rankResponse : keywordList) {
//    		refreshAccessToken();
//    		String keyword = rankResponse.getKeyword();
//    		Integer startDate = rankResponse.getDate();
//    		
//    		System.out.println("==== process keyword: " + keyword);
//    		
//    		processByKeyword(ownDomainId, searchEngineId, searchLanguageId, ownDomainName, 
//    				pageNum, startDate, keyword, project, engineId);
//    		
//    	}
    	
    	try {
			threadPool.init();
			CommonUtils.initThreads(50);
			
			
			RankResponse[] rankResponseArray = keywordList.toArray(new RankResponse[keywordList.size()]);
			
			do {

				refreshAccessToken();
				String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
				if (ipAddress == null) {
					Thread.sleep(1 * 1000);
					continue;
				}

//				System.out.println(" GotIP:" + ipAddress);
				
				if (rankResponseArray == null || rankResponseArray.length == 0) {
					break;
				}
				
				RankResponse[] commandData = (RankResponse[]) ArrayUtils.subarray(rankResponseArray, 0, 1);
				rankResponseArray = (RankResponse[]) ArrayUtils.remove(rankResponseArray, 0);
				
				//String ip, Integer processDate, 
				//String keyword, Response project, Integer engineId
				
				List<String> keywordHistorical = keywordDateMap.get(commandData[0].getKeyword());
				
				if (keywordDateMap.get(commandData[0].getKeyword()) == null) {
					keywordHistorical = new ArrayList<>(); 
				}
				
				DownloadFileFromSearchMetricsV2Command crawCommand = getUpdateCommand(ipAddress, ownDomainId, 
						searchEngineId, searchLanguageId, ownDomainName, commandData[0], 
//						pageNum, project, engineId, new ArrayList<>());
						pageNum, project, engineId, keywordHistorical);
				try {
					threadPool.execute(crawCommand); 
				} catch (Exception e) {
					e.printStackTrace();
				}

			} while (true);
			
			do {
				try {
					Thread.sleep(5000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			} while (threadPool.getThreadPool().getActiveCount() > 0);
			
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
		}
    }
    
//    private void processByKeyword(Integer ownDomainId, Integer searchEngineId, Integer searchLanguageId, 
//    		String ownDomainName, Integer pageNum, Integer startDate, String keyword, Response project, Integer engineId) {
//    	List<Integer> dateList = getProcessDateList(startDate);
//    	
//    	SeoClarityKeywordEntity seoClarityKeywordEntity = getRankCheckId(keyword);
//		if (seoClarityKeywordEntity == null) {
//			
//			System.out.println("skip+++++++++++++");
//			return;
//		}
//		Integer kid = seoClarityKeywordEntity.getId();
//    	
//    	try {
//			threadPool.init();
//			CommonUtils.initThreads(36);
//			
//			System.out.println("dateList size : " + dateList.size());
//			
//			Integer[] dateArray = dateList.toArray(new Integer[dateList.size()]);
//			
//			do {
//
//				String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
//				if (ipAddress == null) {
//					Thread.sleep(1 * 1000);
//					continue;
//				}
//
////				System.out.println(" GotIP:" + ipAddress);
//				
//				if (dateArray == null || dateArray.length == 0) {
//					break;
//				}
//				
//				Integer[] commandData = (Integer[]) ArrayUtils.subarray(dateArray, 0, 1);
//				dateArray = (Integer[]) ArrayUtils.remove(dateArray, 0);
//				
//				//String ip, Integer processDate, 
//				//String keyword, Response project, Integer engineId
//				DownloadFileFromSearchMetricsV2Command crawCommand = getUpdateCommand(ipAddress, ownDomainId, 
//						searchEngineId, searchLanguageId, ownDomainName, commandData[0], 
//						pageNum, kid, keyword, project, engineId);
//				try {
//					threadPool.execute(crawCommand); 
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
//
//			} while (true);
//			
//			do {
//				try {
//					Thread.sleep(5000);
//				} catch (InterruptedException e) {
//					e.printStackTrace();
//				}
//			} while (threadPool.getThreadPool().getActiveCount() > 0);
//			
//		} catch (Exception e) {
//			e.printStackTrace();
//		} finally {
//			threadPool.destroy();
//		}
//    	
//    }
    
	private DownloadFileFromSearchMetricsV2Command getUpdateCommand(String ip, Integer ownDomainId, 
			Integer searchEngineId, Integer searchLanguageId, String ownDomainName, RankResponse rankResponse,
			Integer pageNum,  Response project, Integer engineId, List<String> existDateList) {	
		DownloadFileFromSearchMetricsV2Command crawlCommand = new DownloadFileFromSearchMetricsV2Command(
				ip, ownDomainId, searchEngineId, searchLanguageId, ownDomainName, 
				rankResponse, pageNum, project, engineId, token, existDateList);
		crawlCommand.setStatus(true);
		return crawlCommand;
	}
    
    
//    private static Date startDate = FormatUtils.toDate("2021-03-14", "yyyy-MM-dd");
//    
//    private List<Integer> getProcessDateList(Integer endDateInt) {
//    	
//    	List<Integer> dateList = new ArrayList<Integer>();
//    	
//    	Date eDate = startDate;
//    	Date sDate = FormatUtils.toDate(endDateInt + "", "yyyyMMdd");
//    	
//		while (sDate.compareTo(eDate) >= 0) {
//			
//			dateList.add(FormatUtils.formatDateToYyyyMmDd(sDate));
//
//			sDate = DateUtils.addDays(sDate, -7);
//		}
//		
//		return dateList;
//    }
	 
    
	public static String getRespFromApi(String apiUrl) {
		
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("Content-Type", "application/json");
		
		int tryCnt = 0;
		while(true) {
			tryCnt++;
			if (tryCnt > TRY_LIMIT) {
				break;
			}
			try {
				String result = HttpRequestUtils.simpleGet(apiUrl, null, 300000, headerMap);
				
				return result;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	public class Response {

		private Integer project_id;
		private String project_name;
		private String project_url;
		private Integer keyword_count;
		private Integer[] engines;

		public Integer getProject_id() {
			return project_id;
		}

		public void setProject_id(Integer project_id) {
			this.project_id = project_id;
		}

		public String getProject_name() {
			return project_name;
		}

		public void setProject_name(String project_name) {
			this.project_name = project_name;
		}

		public String getProject_url() {
			return project_url;
		}

		public void setProject_url(String project_url) {
			this.project_url = project_url;
		}

		public Integer[] getEngines() {
			return engines;
		}

		public void setEngines(Integer[] engines) {
			this.engines = engines;
		}

		public Integer getKeyword_count() {
			return keyword_count;
		}

		public void setKeyword_count(Integer keyword_count) {
			this.keyword_count = keyword_count;
		}
		
	}
	
	
	public class MainResponse {
		
		private RankResponse[] response;

		public RankResponse[] getResponse() {
			return response;
		}

		public void setResponse(RankResponse[] response) {
			this.response = response;
		}
	}
	
	public class RankResponse {

		private Integer keywordRankCheckId;
		private String keyword;
		private posEntity[] pos;
		private Integer date;

		public String getKeyword() {
			return keyword;
		}

		public void setKeyword(String keyword) {
			this.keyword = keyword;
		}

		public posEntity[] getPos() {
			return pos;
		}

		public void setPos(posEntity[] pos) {
			this.pos = pos;
		}

		public Integer getDate() {
			return date;
		}

		public void setDate(Integer date) {
			this.date = date;
		}

		public Integer getKeywordRankCheckId() {
			return keywordRankCheckId;
		}

		public void setKeywordRankCheckId(Integer keywordRankCheckId) {
			this.keywordRankCheckId = keywordRankCheckId;
		}
		
	}
	
	public class posEntity {
		
		private String[] tags;

		public String[] getTags() {
			return tags;
		}

		public void setTags(String[] tags) {
			this.tags = tags;
		}
		
	}
	
	public static String base64Encode(String userName, String password) {
		
		try {
			Base64 base64 = new Base64();
			String encodedText = base64.encodeToString((userName + ":" + password).getBytes());
			return encodedText;
		} catch (Exception e) {
			// TODO: handle exception
		}

		return "";
		
	}
	
	private SeoClarityKeywordEntity getRankCheckId(String keywordName) {
		
		if (StringUtils.isBlank(keywordName)) {
			
			System.out.println("keywordName is empty!!! keywordName:" + keywordName);
			return null;
		}
		
		keywordName = StringUtils.lowerCase(FormatUtils.encodeKeyword(keywordName));
		SeoClarityKeywordEntity keywordEntity = seoClarityKeywordEntityDAO.getByKeyword(keywordName);
		
		if(keywordEntity == null || keywordEntity.getId() <= 0) {
			seoClarityKeywordEntityDAO.insert(keywordName);
		} else {
			return keywordEntity;
		}
		
		SeoClarityKeywordEntity checkExistAfterInsert = seoClarityKeywordEntityDAO.getByKeyword(keywordName);
		
		System.out.println("=====inserted rank check:" + checkExistAfterInsert != null ? checkExistAfterInsert.getId() : null);
		
		return checkExistAfterInsert;
	}
	
	
}
