package seoclarity.backend.cronjob;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.actonia.TargetUrlCrawlAdditionalContentDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.urlclaritydb.CrawlUrlDao;
import seoclarity.backend.entity.ResultContent;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ResourceAddInfoEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.crawlurl.TargetUrlHtmlDaily;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * mvn exec:java
 * -Dexec.mainClass="seoclarity.backend.cronjob.DynamicTagSyncScriptFor9422"
 * -Dexec.args=""
 * 
 * <AUTHOR>
 *
 */
public class DynamicTagSyncScriptFor9422 {

//      private ClColdDailyRankingEntityDao clColdDailyRankingEntityDao;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private CrawlUrlDao crawlUrlDao;
	private GroupTagEntityDAO groupTagEntityDAO;
//      private ResourceDeleteInfoEntityDAO resourceDeleteInfoEntityDAO;
//      private ResourceDeleteDetailEntityDAO resourceDeleteDetailEntityDAO;	
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private TargetUrlCrawlAdditionalContentDAO targetUrlCrawlAdditionalContentDAO;

	private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
	private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;

	public DynamicTagSyncScriptFor9422() {
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
//              clColdDailyRankingEntityDao = SpringBeanFactory.getBean("clColdDailyRankingEntityDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		crawlUrlDao = SpringBeanFactory.getBean("crawlUrlDao");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
//              resourceDeleteDetailEntityDAO = SpringBeanFactory.getBean("resourceDeleteDetailEntityDAO");
//              resourceDeleteInfoEntityDAO = SpringBeanFactory.getBean("resourceDeleteInfoEntityDAO");
		targetUrlCrawlAdditionalContentDAO = SpringBeanFactory.getBean("targetUrlCrawlAdditionalContentDAO");
		resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
		resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
	}

	private static Integer processOID = 9422;
	private static final int DEFAULF_USER = 214;
	public static final String MD5_SPLIT = "        ";
	public static boolean isCheckAddDetailExists = false;
	private static String processDate = FormatUtils.formatDate(FormatUtils.getYesterday(true), "yyyy-MM-dd");

	public static void main(String[] args) {
		DynamicTagSyncScriptFor9422 dynamicTagSyncScriptForTarget = new DynamicTagSyncScriptFor9422();
		try {
			dynamicTagSyncScriptForTarget.process();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	private static Map<String, String> xpathMap = new HashMap<>();

	private static String TAG_PREFIX = " (dt)";
	private static String ROOT_DOMAIN_NAME = "ca.grainger";
//      private static Map<String, Integer> groupDictNameIdMap = new HashMap<>();
	private static Integer pageSize = 10000;

	private void process() throws IOException {

		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(processOID);

		if (ownDomainEntity == null) {
			System.out.println(" ownDomainEntity is not found!!!");
			return;
		}

		Map<String, TargetUrlHtmlDaily> cacheMap = new HashMap<>();
		try {
			List<TargetUrlHtmlDaily> targetUrlHtmlDailyList = crawlUrlDao.getAllCustomDataByDate(processOID,
					processDate);

			cacheMap = targetUrlHtmlDailyList.stream()
					.collect(Collectors.toMap(TargetUrlHtmlDaily::getUrl, Function.identity()));

		} catch (Exception e) {
			e.printStackTrace();
		}

		System.out.println("Cache size:" + cacheMap.size());
		System.out.println(new Gson()
				.toJson(cacheMap.get("https://www.grainger.ca/en/category/hand-soap/personal-care-products/c/27211")));

		Integer pageNum = 0;
		int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
		int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);

		String resourceSearchEngine = engineId + "-" + languageId + "-d";
		System.out.println("resourceSearchEngine:" + resourceSearchEngine);

		while (true) {
			// Alps: only query for desktop and national
			List<CLRankingDetailEntity> resultList = clDailyRankingEntityDao.getManagedUrlListFor9422Plp(processOID,
					engineId, languageId, processDate, false, ROOT_DOMAIN_NAME, pageNum, pageSize);

			if (resultList == null || resultList.size() == 0) {
				break;
			}

			pageNum++;
			System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize + ", size:" + resultList.size());

			/**
			 * keyword_name keyword_rankcheck_id url
			 */
			int num = 1;

			List<String[]> relations = new ArrayList<>();

			for (CLRankingDetailEntity entity : resultList) {
				boolean isProductUrl = false;
				boolean isCategoryUrl = false;

				if (StringUtils.contains(entity.getUri(), "/p/")) {
					isProductUrl = true;
				} else if (StringUtils.contains(entity.getUri(), "/c/")) {
					isCategoryUrl = true;
				} else {
					System.out.println("skip url which is not product and category!");
					continue;
				}

				System.out.println("url: " + entity.getUri());
				System.out.println("pricessing on :" + (num++) + ", isProductUrl:" + isProductUrl + ", isCategoryUrl:"
						+ isCategoryUrl);

				try {
					TargetUrlHtmlDaily targetUrlHtmlDaily = cacheMap.get(entity.getUri());

					if (targetUrlHtmlDaily != null && StringUtils.isNotBlank(targetUrlHtmlDaily.getCustomData())) {

						try {
							ResultContent[] results = new Gson().fromJson(targetUrlHtmlDaily.getCustomData(),
									ResultContent[].class);
							if (results != null && results.length > 0) {
								
								List<String> tagContentList = new ArrayList<String>();

								for (ResultContent result : results) {
									
									if(!StringUtils.startsWith(result.getSelector(), "//nav[@id=\"breadcrumb\"]/ul/li") 
											|| !StringUtils.endsWith(result.getSelector(), "//text()")) {
										System.out.println("skip selector :" + result.getSelector());
										continue;
									}

									if (result.getContent() == null || result.getContent().length == 0) {
										System.out.println(" skip empty content");
										continue;
									}

									String content = getContentFromArray(result.getContent());
									if (StringUtils.endsWith(content, "Close")) {
										content = StringUtils.removeEnd(content, "Close");
									}

									content = StringUtils.replace(content, "\n", "");
									content = StringUtils.replace(content, ",", "");
									content = StringUtils.replace(content, "&amp;", "&");
									content = StringUtils.trim(content);

									if (StringUtils.isBlank(content)) {
										continue;
									}

									tagContentList.add(content);
								}
								System.out.println("tagContentList.size():" + tagContentList.size());
								if (tagContentList != null && tagContentList.size() > 0) {
									for (int i = 0; i < tagContentList.size(); i++) {
//                                                                              if (isProductUrl && i == 0) {
//                                                                                      System.out.println(" skip first for product");
//                                                                                      continue;
//                                                                              }

										if (i >= 2) {
											System.out.println(" store only first 2 level for category/product");
											continue;
										}
										String tagName = tagContentList.get(i) + TAG_PREFIX;

										if (StringUtils.contains(tagName, "/")) {
											tagName = StringUtils.replace(tagName, "/", "-");
										}

//										fw.write(entity.getKeywordName() + "\t" + tagName + "\n");

										Integer groupTagId = 0;
										GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(processOID,
												tagName, GroupTagEntity.TAG_TYPE_KEYWORD);
										if (groupTagEntity != null && groupTagEntity.getId() > 0) {
											groupTagId = groupTagEntity.getId();
										} else {
											groupTagId = groupTagEntityDAO.insertForDynamicTag(processOID,
													GroupTagEntity.TAG_TYPE_KEYWORD, tagName, tagName, 0);
										}
//										        	
										if (groupTagId == 0) {
											System.out.println(" @@@ keyword Group Tag Id IS NOT SETTED correctly!!!");
										} else {
											relations.add(new String[] { entity.getKeywordName(), groupTagId + "" });
											System.out.println(
													"Relation:" + entity.getKeywordName() + ", " + groupTagId + "");
										}

									}

								}

							}

						} catch (Exception e) {
							e.printStackTrace();
						}

					} else {
						System.out.println("Url not found in cache:" + entity.getUri());
					}

				} catch (Exception e) {
					e.printStackTrace();
				}
			}

			if (relations.size() == 0) {
				System.out.println("no details... skip");
				continue;
			}

			System.out.println("Relation size : " + relations.size());

			System.out.println("===== create queue task for v2");

			int actionType = ResourceBatchInfoEntity.TYPE_ADD;
			int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_ID;
			try {
				ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
				resourceBatchInfoEntity.setActionType(actionType);
				resourceBatchInfoEntity.setOwnDomainId(ownDomainEntity.getId());
				resourceBatchInfoEntity.setOperationType(operationType);
				resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
				resourceBatchInfoEntity.setUserId(214);
				resourceBatchInfoEntity.setCreateDate(new Date());
				resourceBatchInfoEntity.setStatusRankcheck(0);
				resourceBatchInfoEntity.setEngineLanguageDevice(resourceSearchEngine);
				long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);

				resourceBatchInfoEntityDAO.updateStatusAfterProcess(id,
						ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, new Date());

				List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
				for (String[] rel : relations) {

					if (StringUtils.isBlank(rel[1]) || NumberUtils.toLong(rel[1]) == 0L) {
						System.out.println("==== Add Queue detail Err, Tagid is not set correctly ");
						continue;
					}

					ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
					rbd.setInfoId(id);
					rbd.setActionType(actionType);
					rbd.setOwnDomainId(ownDomainEntity.getId());
					rbd.setResourceMain(rel[0]);
					rbd.setResourceId(NumberUtils.toLong(rel[1]));
					rbd.setResourceSubordinate(rel[1]);
					rbd.setResourceSearchengines(resourceSearchEngine);
					rbd.setResourceMd5(
							Md5Util.Md5(rel[0] + rel[1] + FormatUtils.formatDate(new Date(), "yyyy-MM-dd") + ""));
					rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
					resourceBatchDetailEntityList.add(rbd);
				}
				resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
				System.out.println("Qbs====>create new info, id: " + id + ", resourceBatchDetailEntityList size: "
						+ resourceBatchDetailEntityList.size());

	            resourceBatchInfoEntityDAO.updateStatusAfterProcess(id, ResourceAddInfoEntity.STATUS_NEWLY_CREATED, new Date());
			} catch (NumberFormatException e) {
				e.printStackTrace();
			}
		}

	}

	public static String calculateMd5(String valueMain, String valueSubordinate) {
		String input = valueMain;
		if (StringUtils.isNotBlank(valueSubordinate)) {
			input = input + MD5_SPLIT + valueSubordinate;
		}
		return FormatUtils.hexDigest(input.getBytes());
	}

	private String parseTagName(String tagName) {
		if (StringUtils.contains(tagName, '\u200e')) {
			String[] tagNameSubArray = StringUtils.split(tagName, '\u200e');
			for (String tag : tagNameSubArray) {
				if (!StringUtils.contains(tag, '\u200e')) {
					tagName = tag;
					break;
				}
			}
		}

		tagName = StringUtils.trim(tagName);
		return tagName;
	}

	private String getContentFromArray(String[] strArray) {
		for (String content : strArray) {
			if (StringUtils.isNotBlank(content)) {
				return content;
			}
		}
		return null;
	}

}
