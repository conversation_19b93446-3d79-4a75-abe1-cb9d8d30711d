package seoclarity.backend.cronjob;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.CollectionUtils;

import seoclarity.backend.dao.actonia.AutoRunDetailEntityDAO;
import seoclarity.backend.dao.actonia.AutoRunInfoEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.entity.actonia.AutorunDetailEntity;
import seoclarity.backend.entity.actonia.AutorunInfoEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.GaAutoRunTrigger" -Dexec.args=""
 */
public class GaAutoRunTrigger {


    private AutoRunInfoEntityDAO autoRunInfoEntityDAO;

    private AutoRunDetailEntityDAO autoRunDetailEntityDAO;
    
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;


    public final static int adminUserId = 1703;

    public GaAutoRunTrigger() {
    	ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        autoRunInfoEntityDAO = SpringBeanFactory.getBean("autoRunInfoEntityDAO");
        autoRunDetailEntityDAO = SpringBeanFactory.getBean("autoRunDetailEntityDAO");
    }

    public static void main(String[] args) {

        GaAutoRunTrigger gscAutoRunTrigger = new GaAutoRunTrigger();

        gscAutoRunTrigger.process();

    }

    private void process() {

    	
        try {
            List<OwnDomainSettingEntity> ownDomainSettingEntities = ownDomainSettingEntityDAO.getGaNeedReprocess();
            
            if(CollectionUtils.isEmpty(ownDomainSettingEntities)){
                System.out.println(" no new ga reprocess task , exit !! ");
                return;
            }

            for (OwnDomainSettingEntity gwmDomainRelLog : ownDomainSettingEntities) {
            	
            	
            	
            	
            	 Date fromDate = DateUtils.addMonths(new Date(), -13);
                 int fromDateInt = NumberUtils.toInt(FormatUtils.formatDate(fromDate, FormatUtils.DATE_FORMAT_YYYYMMDD));
                 int toDateInt = NumberUtils.toInt(FormatUtils.formatDate(DateUtils.addDays(new Date(), -2), FormatUtils.DATE_FORMAT_YYYYMMDD));

                 int domainId = gwmDomainRelLog.getOwnDomainId();

                 //insert AutoRunInfo
                 int autoRunInfoId = insertAutoRunInfo(domainId, fromDateInt, toDateInt, AutorunInfoEntity.STATUS_NEW, adminUserId);
                 System.out.println(" insert autoRunInfo id: " + autoRunInfoId + " now : " + FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                 if (autoRunInfoId < 1) {
                     new RuntimeException("insert autoRunInfo fail !! ");
                 }

                 //insert AutoRunDetail
                 AutorunDetailEntity autoRunDetailEntity = new AutorunDetailEntity();
                 autoRunDetailEntity.setAutorunInfoId(autoRunInfoId);
                 autoRunDetailEntity.setOwnDomainId(domainId);
                 autoRunDetailEntity.setStatus(AutorunDetailEntity.STATUS_NEW);
                 autoRunDetailEntity.setProfileId(0);

                 int autoRunDetailId = autoRunDetailEntityDAO.insertAutoRunDetail(autoRunDetailEntity);
                 
                 ownDomainSettingEntityDAO.updateGaAuFlg(domainId);
                 System.out.println("Create auto-GA task, OID:" + domainId);
                 
                 if (autoRunDetailId < 1) {
                     new RuntimeException("insert autoRunDetail fail !! ");
                 }
            	
            }

           
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private int insertAutoRunInfo(Integer ownDomainId, Integer fromDateInt, Integer toDateInt, Integer status, Integer createUserId) {

        AutorunInfoEntity autoRunInfoEntity = new AutorunInfoEntity();
        autoRunInfoEntity.setCategory(AutorunInfoEntity.CATEGORY_GA);
        autoRunInfoEntity.setOwnDomainId(ownDomainId);
        autoRunInfoEntity.setFromDate(fromDateInt);
        autoRunInfoEntity.setToDate(toDateInt);
        autoRunInfoEntity.setStatus(status);
        autoRunInfoEntity.setCreateUserId(createUserId);

        int row = autoRunInfoEntityDAO.insert(autoRunInfoEntity);

        return row;
    }

}
