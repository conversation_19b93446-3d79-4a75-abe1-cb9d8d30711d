package seoclarity.backend.cronjob;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.ResultContent;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ResourceAddInfoEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.clickhouse.crawlurl.TargetUrlHtmlDaily;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.DynamicTagSyncScriptFor765" -Dexec.args=""
 * <AUTHOR>
 *
 */
public class DynamicTagSyncScriptFor765 {
	
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private GroupTagEntityDAO groupTagEntityDAO;
	
	private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
	private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
	
	public DynamicTagSyncScriptFor765() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
		resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
	}
	
	private static Integer processOID = 765;
	private static final int DEFAULF_USER = 214;
	public static final String MD5_SPLIT = "	";
	private static Integer pageSize = 2000;
	
	public static void main(String[] args) {
		DynamicTagSyncScriptFor765 dynamicTagSyncScriptForTarget = new DynamicTagSyncScriptFor765();
		dynamicTagSyncScriptForTarget.process();
		
	}
	
	private static String HREF_TAG_NAME = "ContainsHreflang (dt)";
	
	
	private void process() {
		
		Integer groupTagId = 0;
    	GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(processOID, HREF_TAG_NAME, GroupTagEntity.TAG_TYPE_TARGET_URL);
    	if (groupTagEntity != null && groupTagEntity.getId() > 0) {
    		groupTagId = groupTagEntity.getId();
		} else {
			System.out.println("INS tag , tagName:" + HREF_TAG_NAME);
			groupTagId = groupTagEntityDAO.insert(processOID, GroupTagEntity.TAG_TYPE_TARGET_URL, HREF_TAG_NAME, 0);
		}
		
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(processOID);
		
		if (ownDomainEntity == null ) {
			System.out.println(" ownDomainEntity is not found!!!");
			return ;
		}
		
		Integer pageNum = 0;
		boolean isMobile = false;
		List<String[]> relations = new ArrayList<>();
		try {
			
			while (true) {
	        	//Alps: only query for desktop and national
				List<String> urlList = clDailyRankingEntityDao.getManagedKeywordListForExpedia(
	        			ownDomainEntity.getId(), 1, 1, FormatUtils.formatDate(FormatUtils.getYesterday(true), "yyyy-MM-dd"),
	        			isMobile, ownDomainEntity.getName(), pageNum, pageSize, ownDomainEntity.isBroadMatch());
	        	
				if (urlList == null || urlList.size() == 0) {
	    			break;
	    		}
				
				pageNum ++ ;
				System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize + ", size:" + urlList.size());
				
				
				for(String url : urlList) {
					if (StringUtils.isNotBlank(url) && StringUtils.contains(url, "/category/") 
							&& !StringUtils.contains(url, "?attrs=") 
							&& !StringUtils.contains(url, "?brandName=") 
							&& !StringUtils.contains(url, "?attrs= Examples:") 
							&& !StringUtils.contains(url, "/brand/")) {
		        		relations.add(new String[] {url, groupTagId + ""});
		        		System.out.println("Relation:" + url + ", " + HREF_TAG_NAME + "");
					} else {
						System.out.println(" @@@ Not found href!!!");
					}
				}
				
				if (relations.size() > 0) {
					System.out.println("Relation size : " + relations.size());
				} else {
					continue;
				}
				
				/**
			     * resourceMain: url
			     * resourceId: tagId
			     */
				int actionType = ResourceBatchInfoEntity.TYPE_ADD;
				int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG_ID;
				
				ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
				resourceBatchInfoEntity.setActionType(actionType);
				resourceBatchInfoEntity.setOwnDomainId(processOID);
				resourceBatchInfoEntity.setOperationType(operationType);
				resourceBatchInfoEntity.setStatus(ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR);
				resourceBatchInfoEntity.setUserId(DEFAULF_USER);
				resourceBatchInfoEntity.setCreateDate(new Date());
				
				long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
				
				List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
				for (String[] rel : relations) {
					String url = rel[0];
					String tagId = rel[1];
					if (StringUtils.isNotBlank(url) && StringUtils.isNotBlank(tagId) && NumberUtils.toLong(tagId) > 0) {
						ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
				        rbd.setInfoId(id);
				        rbd.setActionType(actionType);
				        rbd.setOwnDomainId(processOID);
				        rbd.setResourceMain(url);
				        rbd.setResourceId(NumberUtils.toLong(tagId));
				        rbd.setResourceMd5(Md5Util.Md5(url + tagId + processOID + FormatUtils.formatDate(new Date(), "yyyy-MM-dd") + ""));
				        rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
				        resourceBatchDetailEntityList.add(rbd);
					} else {
						System.out.println("ERROR! url: " + url + ", tagId: " + tagId);
					}
				}
				if (relations.size() == 0) {
					System.out.println("no details... skip");
					return;
				}
			    
				resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
		        System.out.println("Qbs====>create new info, id: " + id + ", resourceBatchDetailEntityList size: " + resourceBatchDetailEntityList.size());
				
				resourceBatchInfoEntityDAO.updateStatusAfterProcess(id, ResourceAddInfoEntity.STATUS_NEWLY_CREATED, new Date());
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	public static String calculateMd5(String valueMain, String valueSubordinate) {
		String input = valueMain;
		if (StringUtils.isNotBlank(valueSubordinate)) {
			input = input + MD5_SPLIT + valueSubordinate;
		}
		return FormatUtils.hexDigest(input.getBytes());
	}
	
    private String parseTagName(String tagName) {
    	if (StringUtils.contains(tagName, '\u200e')) {
    		String[] tagNameSubArray = StringUtils.split(tagName, '\u200e');
    		for(String tag : tagNameSubArray) {
	    	   if (!StringUtils.contains(tag, '\u200e')) {
	    		   tagName = tag;
	   				break;
	   			}
    		}
		}
    	
    	tagName = StringUtils.trim(tagName);
    	return tagName;
    }
	
	
}
