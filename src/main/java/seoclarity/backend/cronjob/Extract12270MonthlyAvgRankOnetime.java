package seoclarity.backend.cronjob;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.Extract12270MonthlyAvgRankOnetime" -Dexec.args="true true"
public class Extract12270MonthlyAvgRankOnetime {
	
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private OwnDomainEntityDAO ownDomainEntityDAO;

	public Extract12270MonthlyAvgRankOnetime() {
		// TODO Auto-generated constructor stub
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}
	
	public static Map<Long, Map<String, Double>> keywordAvgRankMap = new HashMap<Long, Map<String,Double>>();
	
	private static Integer OID = 12270;
	
	public static List<String> processDateList = new ArrayList<String>();

	public static void main(String[] args) throws IOException {
		// TODO Auto-generated method stub
		Extract12270MonthlyAvgRankOnetime extract12270MonthlyAvgRankOnetime = new Extract12270MonthlyAvgRankOnetime();
		
		boolean isTrueRank = false;
		if (ArrayUtils.isNotEmpty(args) && args.length >= 1) {
			isTrueRank = BooleanUtils.toBoolean(args[0]);
		}
		
		boolean isMobile = false;
		if (ArrayUtils.isNotEmpty(args) && args.length >= 2) {
			isMobile = BooleanUtils.toBoolean(args[1]);
		}
		
		File file = new File("/home/<USER>/" + OID + "/" + OID + (isTrueRank ? "_true_rank" : "_web_rank") + "_" + (isMobile? "mobile" : "desktop") + ".csv");
		if(file != null && file.exists() && file.isFile()) {
			file.delete();
		}
		
		FileWriter writer = new FileWriter(file, true);
		
		for(int i = 0; i < 24; i ++) {
			
			String startDate = FormatUtils.formatDate(FormatUtils.getMonthFirstDay(DateUtils.addMonths(new Date(), (-1 * i - 1))), "yyyy-MM-dd");
			String endDate = FormatUtils.formatDate(FormatUtils.getMonthLastDay(DateUtils.addMonths(new Date(), (-1 * i - 1))), "yyyy-MM-dd");
			processDateList.add(startDate);
			
			System.out.println("==== processing on ；" + startDate);
			try {
				extract12270MonthlyAvgRankOnetime.process(startDate, endDate, isTrueRank, isMobile);
//				extract12270MonthlyAvgRankOnetime.process(FormatUtils.formatDate(FormatUtils.getMonthFirstDay(DateUtils.addMonths(new Date(), (-1 * i - 1))), "yyyy-MM-dd"), false);
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
			}
		}
		
		System.out.println("size: " + keywordAvgRankMap.size());
		
		List<String> headerList = new ArrayList<String>();
		
		headerList.add("Keyword Name");
		
		Collections.reverse(processDateList);
		for(String date : processDateList) {
			headerList.add("Avg Rank " + FormatUtils.formatDate(FormatUtils.toDate(date, "yyyy-MM-dd"), "MMM yyyy"));
		}
		writer.write(StringUtils.join(headerList, ",") + "\n");
		
		DecimalFormat df = new DecimalFormat("#");
		
		for(Long keywordRankcheckId : keywordAvgRankMap.keySet()) {
        	
        	Map<String, Double> monthlyDate = keywordAvgRankMap.get(keywordRankcheckId);
        	
        	writer.write(keywordIdNameMap.get(keywordRankcheckId));
        	
        	for(String date : processDateList) {
        		
        		Double avgRank = monthlyDate.get(date);
        		
        		if (avgRank == null) {
        			avgRank  = 102D;
				}
        		writer.write("," + df.format(avgRank));
        	}
        	
        	writer.write("\n");
        	writer.flush();
        }
		
		writer.close();
	}

	public static Map<Long, String> keywordIdNameMap = new HashMap<Long, String>();
	
	private void process(String startDate, String endDate, boolean isTrueRank, boolean isMobile) throws IOException {
		
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(OID);
		
		int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        
        List<CLRankingDetailEntity> result = clDailyRankingEntityDao.export12270ExtractOneTime(startDate, endDate, OID, engineId, 
        		languageId, isMobile, ownDomainEntity.getDomain(), isTrueRank);
        
        
        Map<String, Double> avgRankMap = null;
        
        if (CollectionUtils.isEmpty(result)) {
        	return;
		} else {
			for(CLRankingDetailEntity CLRankingDetailEntity : result) {
				
				keywordIdNameMap.put(CLRankingDetailEntity.getKeywordRankcheckId(), CLRankingDetailEntity.getKeywordName());
				
				avgRankMap = keywordAvgRankMap.get(CLRankingDetailEntity.getKeywordRankcheckId());
				if(MapUtils.isEmpty(avgRankMap)) {
					
					avgRankMap = new HashMap<String, Double>();
					avgRankMap.put(startDate, CLRankingDetailEntity.getAvgRank());
					
				} else {
					avgRankMap.put(startDate, CLRankingDetailEntity.getAvgRank());
				}
				
				keywordAvgRankMap.put(CLRankingDetailEntity.getKeywordRankcheckId(), avgRankMap);
				
	        }
		}
        
	}
	
}
