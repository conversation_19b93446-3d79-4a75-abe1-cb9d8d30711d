package seoclarity.backend.cronjob.youtube;

import java.util.List;

public class YoutubeUploadDetailEntity {
		
		private String videoId;
		private String publishedTime;
		private String videolength;
		private Integer trueRank;
		private Integer videoType;
		private String[] tags;
		private String authorUrl;
		private String videoUrl;
		private Integer authorType;
		private String viewCount;
		private String title;
		private String meta;
		private String authorName;
		private String authorUniqueId;
		private String rankType;
		private Boolean verifyFlag;
		private String subscriberCount;
		
		private List<YoutubeUploadSubrankEntity> subRankList;
		
		public String getVideoId() {
			return videoId;
		}
		public void setVideoId(String videoId) {
			this.videoId = videoId;
		}
		public String getPublishedTime() {
			return publishedTime;
		}
		public void setPublishedTime(String publishedTime) {
			this.publishedTime = publishedTime;
		}
		public String getVideolength() {
			return videolength;
		}
		public void setVideolength(String videolength) {
			this.videolength = videolength;
		}
		public Integer getTrueRank() {
			return trueRank;
		}
		public void setTrueRank(Integer trueRank) {
			this.trueRank = trueRank;
		}
		public Integer getVideoType() {
			return videoType;
		}
		public void setVideoType(Integer videoType) {
			this.videoType = videoType;
		}
		public String[] getTags() {
			return tags;
		}
		public void setTags(String[] tags) {
			this.tags = tags;
		}
		public String getAuthorUrl() {
			return authorUrl;
		}
		public void setAuthorUrl(String authorUrl) {
			this.authorUrl = authorUrl;
		}
		public String getVideoUrl() {
			return videoUrl;
		}
		public void setVideoUrl(String videoUrl) {
			this.videoUrl = videoUrl;
		}
		public Integer getAuthorType() {
			return authorType;
		}
		public void setAuthorType(Integer authorType) {
			this.authorType = authorType;
		}
		public String getViewCount() {
			return viewCount;
		}
		public void setViewCount(String viewCount) {
			this.viewCount = viewCount;
		}
		public String getTitle() {
			return title;
		}
		public void setTitle(String title) {
			this.title = title;
		}
		public String getMeta() {
			return meta;
		}
		public void setMeta(String meta) {
			this.meta = meta;
		}
		public String getAuthorName() {
			return authorName;
		}
		public void setAuthorName(String authorName) {
			this.authorName = authorName;
		}
		public String getAuthorUniqueId() {
			return authorUniqueId;
		}
		public void setAuthorUniqueId(String authorUniqueId) {
			this.authorUniqueId = authorUniqueId;
		}
		public String getRankType() {
			return rankType;
		}
		public void setRankType(String rankType) {
			this.rankType = rankType;
		}
		public Boolean getVerifyFlag() {
			if (verifyFlag == null) {
				return false;
			}
			
			return verifyFlag;
		}
		public void setVerifyFlag(Boolean verifyFlag) {
			this.verifyFlag = verifyFlag;
		}
		public List<YoutubeUploadSubrankEntity> getSubRankList() {
			return subRankList;
		}
		public void setSubRankList(List<YoutubeUploadSubrankEntity> subRankList) {
			this.subRankList = subRankList;
		}
		public String getSubscriberCount() {
			return subscriberCount;
		}
		public void setSubscriberCount(String subscriberCount) {
			this.subscriberCount = subscriberCount;
		}
		
		
}
