package seoclarity.backend.cronjob.youtube;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.TiktokUploadMonitorEntityDAO;
import seoclarity.backend.dao.clickhouse.socialengine.YoutubeRankingColdEntityDAO;
import seoclarity.backend.dao.clickhouse.socialengine.YoutubeRankingMasterEntityDAO;
import seoclarity.backend.entity.clickhouse.youtube.YoutubeDetailEntity;
import seoclarity.backend.entity.clickhouse.youtube.YoutubeInfoEntity;
import seoclarity.backend.entity.clickhouse.youtube.YoutubeSubrankEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * 
 * <AUTHOR> @date 
 * seoclarity.backend.cronjob.youtube.YoutubeUploader
 * upload to clarityDB
 */
public class YoutubeUploader {
	
	private static Gson gson = new Gson();
	public static final Log logger = LogFactory.getLog(YoutubeUploader.class);
	
	public static final String STATIC_BASE_HREF_LINK = "https://www.youtube.com";

	private static List<String> processingFileList = new ArrayList<String>();
	
	private YoutubeRankingColdEntityDAO youtubeRankingColdEntityDAO;
	private YoutubeRankingMasterEntityDAO youtubeRankingMasterEntityDAO;
	private TiktokUploadMonitorEntityDAO tiktokUploadMonitorEntityDAO;
	
	private static final String databaseName = "youtube_ranking";
//	dis_ranking_detail_2025
//	dis_ranking_info_2025  
	private static final String FINAL_TABLE_INFO_TEMPLATE = "dis_ranking_info_%s";
	
	//d_ranking_detail_us_2025
	private static final String FINAL_TABLE_DETAIL_TEMPLATE = "dis_ranking_detail_%s";
	private static final String FINAL_TABLE_SUBRANK_TEMPLATE = "dis_ranking_subrank_%s";
	
	private static String uploadInfoTable;
	private static String uploadDetailTable;
	private static String uploadSubrankTable;
	
    public static String storeDoneFilePath = "/home/<USER>/youtube/needUpload";
    public static String storeTempFilePath = "/home/<USER>/youtube/uploading/" + FormatUtils.formatDate(new Date(), "yyyyMMddHH") + "/";    
    public static String storeBKFilePath = "/home/<USER>/youtube/backUpFolder";
    
	private static String uploadDate;
	private static String uploadYear;
	
	private static final String MONITOR_TABLE_NAME_TEMPALTE = "supplemental_upload_monitor_w_%s";
	
	public YoutubeUploader() {
		youtubeRankingColdEntityDAO = SpringBeanFactory.getBean("youtubeRankingColdEntityDAO");
		youtubeRankingMasterEntityDAO = SpringBeanFactory.getBean("youtubeRankingMasterEntityDAO");
		tiktokUploadMonitorEntityDAO = SpringBeanFactory.getBean("tiktokUploadMonitorEntityDAO");
		
	}

	public static void main(String args[]) {
		
		if (args != null && args.length >= 1) {
			FormatUtils.getSundayOfLastWeek(new Date());
			try {
				Date rankingDate = FormatUtils.getSundayOfLastWeek(DateUtils.parseDate(args[0], new String[]{"yyyyMMdd"}));
				uploadDate = FormatUtils.formatDate(rankingDate, "yyyyMMdd");
				uploadYear = FormatUtils.formatDate(rankingDate, "yyyy") ;
	        } catch (ParseException e) {
	            logger.error("incorrect dateformat, should be yyyyMMdd，e.g 20250410");
	        }
			
		} else {
			Date rankingDate = FormatUtils.getSundayOfLastWeek(new Date());
			uploadDate  = FormatUtils.formatDate(rankingDate, "yyyyMMdd");
			uploadYear  = FormatUtils.formatDate(rankingDate, "yyyy");
		}
		
		uploadInfoTable = String.format(FINAL_TABLE_INFO_TEMPLATE, uploadYear);
		uploadDetailTable = String.format(FINAL_TABLE_DETAIL_TEMPLATE, uploadYear);
		uploadSubrankTable = String.format(FINAL_TABLE_SUBRANK_TEMPLATE, uploadYear);
		
		YoutubeUploader youtubeUploader = new YoutubeUploader();
		
		try {
			youtubeUploader.process();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void process() {
		
		// get file list
		moveFilesToProcessingFolder();
		try {
			
			if (CollectionUtils.isEmpty(processingFileList)) {
				logger.info("== No file need to upload!");
				return;
			}
			
			logger.info("processingFileList size:" + processingFileList.size());

			String monitorTable = String.format(MONITOR_TABLE_NAME_TEMPALTE, uploadDate);
			if (!tiktokUploadMonitorEntityDAO.checkTable(monitorTable)) {
				System.out.println("Not found monitor table, creating now! " + monitorTable);
				tiktokUploadMonitorEntityDAO.createTable(monitorTable);
			} else {
				tiktokUploadMonitorEntityDAO.deleteDataByEngineId(monitorTable, 130, 1);
			}
			
			File tmpFile;
			for (String youtubeRankingFile : processingFileList) {
				tmpFile = new File(youtubeRankingFile);
				
				if (tmpFile == null || !tmpFile.isFile()) {
					logger.info("File is not exist or is a folder : " + youtubeRankingFile);
					continue;
				}
				
				logger.info("=====processing file:" + tmpFile.getAbsolutePath());
				processFile(tmpFile, monitorTable);
			}
			
		} catch (Exception e) {
			logger.info("Insert table failed, table: " + uploadInfoTable + ", folder: "  + storeDoneFilePath);
			moveFilesBackProcessingFolder();
			e.printStackTrace();
			return ;
		}
		
		
		try {
			moveFileAndZip();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			deleteTempProcessingFolder();
		}
		
			
		
	}
	
	private void processFile(File file, String monitorTable) throws Exception {

		List<YoutubeInfoEntity> infoList = new ArrayList<>();
		List<YoutubeDetailEntity> detailList = new ArrayList<>();
		List<YoutubeSubrankEntity> subrankList = new ArrayList<>();
		
		YoutubeInfoEntity youtubeInfoEntity = new YoutubeInfoEntity();
		YoutubeDetailEntity youtubeDetailEntity = new YoutubeDetailEntity();
		YoutubeSubrankEntity youtubeSubrankEntity = new YoutubeSubrankEntity();
		
		List<String> uniqueList = new ArrayList<String>();
		List<YoutubeUploadInfoEntity> parsedEntityList = new ArrayList<YoutubeUploadInfoEntity>();
		try {

			BufferedReader bf = new BufferedReader(new FileReader(file));
			String content = "";
			
			Integer ownDomainId = 0;
			Integer relId = 0;
			String uniqKey = "";
			
//			keys[0], //keywordRankcheckId
//			keys[1], //engineId 
//			keys[2], //languageId
//			keys[3], //cityId
//			keys[4], //frequence
//			keys[5], //ownDomainId
//			keys[6]  //device
			
			while (content != null) {
				content = bf.readLine();
				if (content == null) {
					break;
				}
				
				try {
					YoutubeUploadInfoEntity youtubeUploadInfoEntity = gson.fromJson(content, YoutubeUploadInfoEntity.class);
					
					if (CollectionUtils.isEmpty(youtubeUploadInfoEntity.getDomainList()) || youtubeUploadInfoEntity.getDomainList().size() ==0) {
						logger.info("@@domainlist is empty, RCid:" + youtubeUploadInfoEntity.getId());
						continue;
					}
					
					for(Integer i = 0; i < youtubeUploadInfoEntity.getDomainList().size(); i++) {
						YoutubeUploadInfoEntity youtubeUploadClone = clone(youtubeUploadInfoEntity);
						ownDomainId = youtubeUploadInfoEntity.getDomainList().get(i);
						try {
							relId = youtubeUploadInfoEntity.getRelIdList().get(i);
						} catch (Exception e) {
							logger.info("@@relArray not matched idx, oidArray:" + youtubeUploadInfoEntity.getDomainList().size() + ", relArray:" + youtubeUploadInfoEntity.getRelIdList().size());
						}
						
						uniqKey = youtubeUploadInfoEntity.getId() + "-" + youtubeUploadInfoEntity.getSearchEngine() + "-" +
								youtubeUploadInfoEntity.getSearchLanguage() + "-" + youtubeUploadInfoEntity.getCityId() + "-" +
								youtubeUploadInfoEntity.getFrequency() + "-" + ownDomainId + "-" + youtubeUploadInfoEntity.getDevice();
						
						youtubeUploadClone.setOwnDomainId(ownDomainId);
						youtubeUploadClone.setRelId(relId);
						
						uniqueList.add(uniqKey);
						parsedEntityList.add(youtubeUploadClone);
					}
					
				} catch (Exception e) {
					logger.info("====== content can not be parse to entity, content: " + content);
					e.printStackTrace();
				}
			}
			
			bf.close();
			
			logger.info("Total check cnt:" + uniqueList.size());
			int[] results = tiktokUploadMonitorEntityDAO.insertIgnore(monitorTable, uniqueList);
			
			List<YoutubeUploadInfoEntity> passedEntityList = new ArrayList<YoutubeUploadInfoEntity>();
			
			for(Integer i = 0; i < results.length; i ++) {
				
				if (results[i] == 1) {
					passedEntityList.add(parsedEntityList.get(i));
				}
			}
			
			logger.info("passedEntityList:" + passedEntityList.size());
			
			String rankingDate = "";
			for(YoutubeUploadInfoEntity youtubeUploadInfoEntity : passedEntityList) {
				
				rankingDate = FormatUtils.formatDate(FormatUtils.toDate(youtubeUploadInfoEntity.getSendToQDate() + "", "yyyyMMdd"), "yyyy-MM-dd"); 
				
				youtubeInfoEntity = new YoutubeInfoEntity();
				
				youtubeInfoEntity.setEngineId(youtubeUploadInfoEntity.getSearchEngine());
				youtubeInfoEntity.setKeywordName(FormatUtils.decodeKeyword(youtubeUploadInfoEntity.getKeyword()));
				youtubeInfoEntity.setKeywordRankcheckId(youtubeUploadInfoEntity.getId());
				youtubeInfoEntity.setLanguageId(youtubeUploadInfoEntity.getSearchLanguage());
//				youtubeInfoEntity.setLocationId(youtubeUploadInfoEntity.getCityId());
				youtubeInfoEntity.setOwnDomainId(youtubeUploadInfoEntity.getOwnDomainId());
				youtubeInfoEntity.setRankingDate(rankingDate);
				youtubeInfoEntity.setDevice(youtubeUploadInfoEntity.getDevice());
				youtubeInfoEntity.setRelId(youtubeUploadInfoEntity.getRelId());
				youtubeInfoEntity.setFrequency(youtubeUploadInfoEntity.getFrequency());
				
				infoList.add(youtubeInfoEntity);
				for(YoutubeUploadDetailEntity youtubeUploadDetailEntity : youtubeUploadInfoEntity.getKeywordRankEntityVOs()) {
					youtubeDetailEntity = new YoutubeDetailEntity();
					
					youtubeDetailEntity.setDevice(youtubeUploadInfoEntity.getDevice());
					youtubeDetailEntity.setKeywordName(FormatUtils.decodeKeyword(youtubeUploadInfoEntity.getKeyword()));
					youtubeDetailEntity.setOwnDomainId(youtubeUploadInfoEntity.getOwnDomainId());
					youtubeDetailEntity.setKeywordRankcheckId(youtubeUploadInfoEntity.getId());
					youtubeDetailEntity.setEngineId(youtubeUploadInfoEntity.getSearchEngine());
					youtubeDetailEntity.setLanguageId(youtubeUploadInfoEntity.getSearchLanguage());
//					youtubeDetailEntity.setLocationId(youtubeUploadInfoEntity.getLocationId());
					youtubeDetailEntity.setRankingDate(rankingDate);
					youtubeDetailEntity.setRank(youtubeUploadDetailEntity.getTrueRank());
					youtubeDetailEntity.setType(youtubeUploadDetailEntity.getVideoType());
					youtubeDetailEntity.setVideoId(youtubeUploadDetailEntity.getVideoId());
					youtubeDetailEntity.setAuthorUrl(youtubeUploadDetailEntity.getAuthorUrl());
					youtubeDetailEntity.setVideoUrl(youtubeUploadDetailEntity.getVideoUrl());
					youtubeDetailEntity.setVerifyFlg(youtubeUploadDetailEntity.getVerifyFlag() ? "Y" : "N");
					youtubeDetailEntity.setTags(youtubeUploadDetailEntity.getTags());
					youtubeDetailEntity.setAuthorType(youtubeUploadDetailEntity.getAuthorType());
					youtubeDetailEntity.setViewCount(youtubeUploadDetailEntity.getViewCount());
					youtubeDetailEntity.setVideoTitle(youtubeUploadDetailEntity.getTitle());
					youtubeDetailEntity.setVideoMeta(youtubeUploadDetailEntity.getMeta());
					youtubeDetailEntity.setAuthorName(youtubeUploadDetailEntity.getAuthorName());
					youtubeDetailEntity.setAuthorUniqueId(youtubeUploadDetailEntity.getAuthorUniqueId());
					youtubeDetailEntity.setVideoLength(youtubeUploadDetailEntity.getVideolength());
					youtubeDetailEntity.setPublishTime(youtubeUploadDetailEntity.getPublishedTime());
					youtubeDetailEntity.setSubscriberCount(youtubeUploadDetailEntity.getSubscriberCount());
					youtubeDetailEntity.setRankType(youtubeUploadDetailEntity.getRankType());
					
					detailList.add(youtubeDetailEntity);
					
					if (!CollectionUtils.isEmpty(youtubeUploadDetailEntity.getSubRankList())) {
						for(YoutubeUploadSubrankEntity youtubeUploadSubrankEntity : youtubeUploadDetailEntity.getSubRankList()) {
							youtubeSubrankEntity = new YoutubeSubrankEntity();
							/**
							 * 	private String device;
								private String keywordName;
								private Integer ownDomainId;
								private Integer keywordRankcheckId;
								private Integer engineId;
								private Integer languageId;
								private String rankingDate;
								private Integer rank;
								private Integer subrank;
								private Integer type;
								private String videoId;
								private String videoUrl;
								private String viewCount;
								private String videoTitle;
								private String videoMeta;
								private String[] attrstrkey; // Array(String)
								private String[] attrstrvalue; // Array(String)
								private String[] attrintkey; // Array(String)
								private Integer[] attrintvalue; // Array(UInt32)
							 */
							youtubeSubrankEntity.setSubrank(youtubeUploadSubrankEntity.getSubRank());
							youtubeSubrankEntity.setRank(youtubeUploadSubrankEntity.getRank());
							youtubeSubrankEntity.setType(youtubeUploadSubrankEntity.getType());
							youtubeSubrankEntity.setVideoId(youtubeUploadSubrankEntity.getVideoId());
							if (!StringUtils.startsWith(youtubeUploadSubrankEntity.getHref(), STATIC_BASE_HREF_LINK)) {
								youtubeSubrankEntity.setVideoUrl(STATIC_BASE_HREF_LINK + youtubeUploadSubrankEntity.getHref());
							} else {
								youtubeSubrankEntity.setVideoUrl(youtubeUploadSubrankEntity.getHref());
							}
							
							youtubeSubrankEntity.setViewCount(youtubeUploadSubrankEntity.getViewCount());
							youtubeSubrankEntity.setVideoTitle(youtubeUploadSubrankEntity.getTitle());
							
							youtubeSubrankEntity.setEngineId(youtubeUploadInfoEntity.getSearchEngine());
							youtubeSubrankEntity.setKeywordName(FormatUtils.decodeKeyword(youtubeUploadInfoEntity.getKeyword()));
							youtubeSubrankEntity.setKeywordRankcheckId(youtubeUploadInfoEntity.getId());
							youtubeSubrankEntity.setLanguageId(youtubeUploadInfoEntity.getSearchLanguage());
							youtubeSubrankEntity.setOwnDomainId(youtubeUploadInfoEntity.getOwnDomainId());
							youtubeSubrankEntity.setRankingDate(rankingDate);
							youtubeSubrankEntity.setDevice(youtubeUploadInfoEntity.getDevice());
							
							subrankList.add(youtubeSubrankEntity);
						}
					}
				}
			}
			
			System.out.println("subrankList size:" + subrankList.size());
			
			try {
				logger.info("===Uploading to master info");
				youtubeRankingMasterEntityDAO.insertForBatchInfo(infoList, getInfoTableName());
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				logger.info("===Uploading to master detail");
				youtubeRankingMasterEntityDAO.insertForBatchDetail(detailList, getDetailTableName());
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				logger.info("===Uploading to master subrank");
				youtubeRankingMasterEntityDAO.insertForBatchSubrank(subrankList, getSubrankTableName());
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				logger.info("===Uploading to cold info");
				youtubeRankingColdEntityDAO.insertForBatchInfo(infoList, getInfoTableName());
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				logger.info("===Uploading to cold detail");
				youtubeRankingColdEntityDAO.insertForBatchDetail(detailList, getDetailTableName());
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				logger.info("===Uploading to master subrank");
				youtubeRankingColdEntityDAO.insertForBatchSubrank(subrankList, getSubrankTableName());
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
	}
	
	private YoutubeUploadInfoEntity clone(YoutubeUploadInfoEntity youtubeUploadInfoEntity) {
		YoutubeUploadInfoEntity youtubeUploadInfoEntityClone = new YoutubeUploadInfoEntity();
		
		youtubeUploadInfoEntityClone.setKeywordRankEntityVOs(youtubeUploadInfoEntity.getKeywordRankEntityVOs());
		youtubeUploadInfoEntityClone.setDevice(youtubeUploadInfoEntity.getDevice());
		youtubeUploadInfoEntityClone.setDomainList(youtubeUploadInfoEntity.getDomainList());
		youtubeUploadInfoEntityClone.setSearchEngine(youtubeUploadInfoEntity.getSearchEngine());
		youtubeUploadInfoEntityClone.setSearchLanguage(youtubeUploadInfoEntity.getSearchLanguage());
		
		youtubeUploadInfoEntityClone.setFrequency(youtubeUploadInfoEntity.getFrequency());
		youtubeUploadInfoEntityClone.setId(youtubeUploadInfoEntity.getId());
		youtubeUploadInfoEntityClone.setKeyword(youtubeUploadInfoEntity.getKeyword());
		youtubeUploadInfoEntityClone.setCityId(youtubeUploadInfoEntity.getCityId());
		youtubeUploadInfoEntityClone.setRelIdList(youtubeUploadInfoEntity.getRelIdList());
		
		youtubeUploadInfoEntityClone.setSendToQDate(youtubeUploadInfoEntity.getSendToQDate());
		
		return youtubeUploadInfoEntityClone;
	}

	public String getInfoTableName() {
		return databaseName + "." + uploadInfoTable;
	}
	
	public String getDetailTableName() {
		return databaseName + "." + uploadDetailTable;
	}
	
	public String getSubrankTableName() {
		return databaseName + "." + uploadSubrankTable;
	}
	
	private void moveFilesToProcessingFolder(){
		
		File targetFolder = new File(storeTempFilePath);
		if (targetFolder == null || !targetFolder.isDirectory()) {
			logger.info("Folder is not exist, mkdir: " + storeTempFilePath);
			targetFolder.mkdir();
		}
		
		File doneFolder = new File(storeDoneFilePath);
		if (doneFolder == null || !doneFolder.isDirectory()) {
			logger.info("Folder is not exist :" + doneFolder);
			return;
		}
		
		logger.info("====moving files to processing folder!! from " + storeDoneFilePath + " to " + storeTempFilePath);
		
		for (File file : doneFolder.listFiles()) {
			try {
				
				if (StringUtils.startsWith(file.getName(), "youtube_") && StringUtils.endsWith(file.getName(), ".txt") &&file.isFile()) {
					String[] keyArray = StringUtils.split(file.getName(), "_");
					String fileBelongDate = FormatUtils.formatDate(FormatUtils.getSundayOfLastWeek(FormatUtils.toDate(keyArray[1], "yyyyMMdd")), "yyyyMMdd");
					if (StringUtils.equals(fileBelongDate, uploadDate)) {
						FileUtils.moveFile(file, new File(storeTempFilePath + "/" + file.getName()));
						processingFileList.add(storeTempFilePath + "/" + file.getName());
					} else {
						logger.info("=== skip file:" + file.getName() + ", date not matched:" + fileBelongDate + "-" + uploadDate);
					}
				} else {
					logger.info("=== skip file:" + file.getName());
				}
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
	}
	
	private void moveFilesBackProcessingFolder(){
		
		File processingFolder = new File(storeTempFilePath);
		
		logger.info("====moving files back from processing folder !! from " + storeTempFilePath + " to " + storeDoneFilePath);
		
		for (File file : processingFolder.listFiles()) {
			try {
				FileUtils.moveFile(file, new File(storeDoneFilePath + "/" + file.getName()));
				processingFileList.add(storeDoneFilePath + "/" + file.getName());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		deleteTempProcessingFolder();
	}
	
	private void moveFileAndZip(){
		
		File bkFolder = new File(storeBKFilePath);
		if (bkFolder == null || !bkFolder.isDirectory()) {
			logger.info("BK folder is not exist, mkdir: " + storeBKFilePath);
			bkFolder.mkdir();
		}
		
		File tmpFile;
		File targetFile;
		
		for (String fileFullPath : processingFileList) {
			// zip file
			try {
				tmpFile = new File(fileFullPath);
				targetFile = new File(bkFolder + "/" + tmpFile.getName());
				
				FileUtils.moveFile(tmpFile, targetFile);
				
				logger.info("zipped file : " + targetFile.getAbsolutePath());
	            GZipUtil.zipFile(targetFile.getAbsolutePath());
	            
	            targetFile.delete();
	            logger.info("delete file : [" + fileFullPath + "]");
	        } catch (Exception e) {
	        	logger.info("delete file failed. file: [" + fileFullPath + "]");
	            e.printStackTrace();
	        }
			
		}
		
		deleteTempProcessingFolder();
	}
	
	private void deleteTempProcessingFolder(){
		//deleted the temp processing folder
		File tempFolder = new File(storeTempFilePath);
		
		if (tempFolder != null && tempFolder.isDirectory()) {
			tempFolder.delete();
		}
	}
	
}
