package seoclarity.backend.cronjob;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import ch.ethz.ssh2.SFTPv3Client;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.bean.FTPServerInfoBean;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.KeywordPortfolioReviewExtract" -Dexec.args=""
public class KeywordPortfolioReviewExtract {

	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	public KeywordPortfolioReviewExtract() {
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}
	
	private static List<Integer> ownDomainIdList = new ArrayList<Integer>();
	
	static {
		
		ownDomainIdList.add(11281);
		ownDomainIdList.add(11282);
		ownDomainIdList.add(11283);
		ownDomainIdList.add(11284);
		ownDomainIdList.add(11285);
		ownDomainIdList.add(11286);
		ownDomainIdList.add(11287);
		ownDomainIdList.add(11288);
		ownDomainIdList.add(11289);
		ownDomainIdList.add(11290);
		ownDomainIdList.add(11291);
		ownDomainIdList.add(11292);
		ownDomainIdList.add(11293);
		ownDomainIdList.add(11294);
		ownDomainIdList.add(11295);
		ownDomainIdList.add(11296);
		ownDomainIdList.add(11297);
		ownDomainIdList.add(11298);
		ownDomainIdList.add(11516);
		ownDomainIdList.add(11517);
		ownDomainIdList.add(11518);
		ownDomainIdList.add(11519);
		ownDomainIdList.add(8711);
		ownDomainIdList.add(8754);
		ownDomainIdList.add(8755);
		ownDomainIdList.add(8756);
		ownDomainIdList.add(8757);
		ownDomainIdList.add(8758);
		ownDomainIdList.add(8759);
		ownDomainIdList.add(8760);
		ownDomainIdList.add(8761);
		ownDomainIdList.add(8762);
		ownDomainIdList.add(8763);
		ownDomainIdList.add(8764);
		ownDomainIdList.add(8765);
		ownDomainIdList.add(9028);
		ownDomainIdList.add(9106);
		ownDomainIdList.add(9192);
		ownDomainIdList.add(9193);
		ownDomainIdList.add(9194);
		ownDomainIdList.add(9195);
		ownDomainIdList.add(9196);
		ownDomainIdList.add(9198);
		ownDomainIdList.add(9197);
		ownDomainIdList.add(9199);
		ownDomainIdList.add(9200);
		ownDomainIdList.add(9201);
		ownDomainIdList.add(9202);
		ownDomainIdList.add(9203);
		ownDomainIdList.add(9204);
		ownDomainIdList.add(9205);
		ownDomainIdList.add(9206);
		ownDomainIdList.add(9207);
		ownDomainIdList.add(9208);
		ownDomainIdList.add(9209);
		ownDomainIdList.add(9210);
		ownDomainIdList.add(9211);
		ownDomainIdList.add(9212);
		ownDomainIdList.add(9214);
		ownDomainIdList.add(9213);
		ownDomainIdList.add(9215);
		ownDomainIdList.add(9216);
		ownDomainIdList.add(9217);
		ownDomainIdList.add(9218);
		ownDomainIdList.add(9219);
		ownDomainIdList.add(9220);
		ownDomainIdList.add(9222);
		ownDomainIdList.add(9221);
		ownDomainIdList.add(9223);
		ownDomainIdList.add(9224);
		ownDomainIdList.add(9225);
		ownDomainIdList.add(9226);
		ownDomainIdList.add(9227);
		ownDomainIdList.add(9228);
		ownDomainIdList.add(9229);
		ownDomainIdList.add(9230);
		ownDomainIdList.add(9231);
		ownDomainIdList.add(9232);
		ownDomainIdList.add(9233);
		ownDomainIdList.add(9234);
		ownDomainIdList.add(9235);
		ownDomainIdList.add(9236);
		ownDomainIdList.add(9238);
		ownDomainIdList.add(9237);
		ownDomainIdList.add(9239);
		ownDomainIdList.add(9240);
		ownDomainIdList.add(9241);
		ownDomainIdList.add(9242);
		ownDomainIdList.add(9388);
		ownDomainIdList.add(9389);
		ownDomainIdList.add(9390);
		ownDomainIdList.add(9391);
		ownDomainIdList.add(9392);
		ownDomainIdList.add(9393);
		ownDomainIdList.add(9394);
		ownDomainIdList.add(9395);
		ownDomainIdList.add(9441);
		ownDomainIdList.add(9442);
		ownDomainIdList.add(9551);
		ownDomainIdList.add(9576);
		ownDomainIdList.add(9589);
		ownDomainIdList.add(9825);
		ownDomainIdList.add(9924);
		ownDomainIdList.add(9925);
		ownDomainIdList.add(9926);
		ownDomainIdList.add(10874);
		ownDomainIdList.add(11252);

	}

	public static void main(String[] args) throws IOException {
		// TODO Auto-generated method stub
		
		KeywordPortfolioReviewExtract keywordPortfolioReviewExtract = new KeywordPortfolioReviewExtract();
		keywordPortfolioReviewExtract.process();
	}

	
	private void process() throws IOException {
		for(Integer oid : ownDomainIdList) {
			
			System.out.println("Processing on OID: " + oid);
			processByDomain(oid);
		}
		
	}
	
	private static String rankingDate = FormatUtils.formatDate(FormatUtils.getYesterday(true), "yyyy-MM-dd");
	
	
	private void processByDomain(Integer ownDomainId) throws IOException {
		
		
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
		
		if (ownDomainEntity == null ) {
			System.out.println(" ownDomainEntity is not found!!!");
			return ;
		}
		
		File file = new File("/home/<USER>/KeywordPortfolioReview/KeywordPortfolioReview_" 
				+ StringUtils.replace(ownDomainEntity.getDomain(), "/", "_") + "_" + ownDomainId + "_desktop_" + rankingDate + ".csv");
		
		if (file != null && file.exists()) {
			System.out.println("File Already exist, delete now!");
			file.delete();
		}
		
		FileWriter fw = new FileWriter(file, true);
		
		fw.write("Correct version" + "\t" + "Incorrect variation" + "\n");
		
		int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        
		
        List<CLRankingDetailEntity> resultList = clDailyRankingEntityDao.getKeywordPortpolioExtract(ownDomainId, engineId, languageId, false, rankingDate);
		for(CLRankingDetailEntity cLRankingDetailEntity : resultList) {
			fw.write(cLRankingDetailEntity.getGoogleRecommend() + "\t" + cLRankingDetailEntity.getKeywordName() + "\n");
		}
        
		fw.close();
		
		FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
        String host = ftpServerInfo.getPrivateHost();
        String ftpUsername = ftpServerInfo.getServerUserName();
        String ftpPassword = ftpServerInfo.getServerPassword();
        

        try {
			copyBySSH(host, ftpUsername, ftpPassword, ownDomainId, file.getAbsolutePath());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
    public static void copyBySSH(String host, String userId, String pw, Integer ownDomainId, String fileName)
            throws Exception {

        Connection connection = new Connection(host);
        connection.connect();
        if (connection.authenticateWithPassword(userId, pw)) {
            System.out.println(" login to FTP");
            SCPClient scpClient = connection.createSCPClient();

            String remoteFolder = "/home/<USER>/" + ownDomainId + "/";
            System.out.println(" ===remoteFolder:" + remoteFolder);

            try {
                scpClient.put(fileName, remoteFolder, "0644");
            } catch (Exception e) {
                e.printStackTrace();
                if (e instanceof java.io.IOException && e.getCause() != null && e.getCause().getMessage() != null &&
                        StringUtils.containsIgnoreCase(e.getCause().getMessage(), "No such file")) {
                    System.out.println(" =============CreateRemoteFolder:" + remoteFolder);
                    SFTPv3Client sftpClient = new SFTPv3Client(connection);
                    sftpClient.mkdir(remoteFolder, 0777);
                    scpClient.put(fileName, remoteFolder, "0644");
                }
            }

            System.out.println(" ===Saved file:" + fileName + " to " + remoteFolder);
            connection.close();
        } else {
            System.out.println(" Failed to login to target host...");
            throw new Exception(" can not login the remote server");
        }
    }

}
