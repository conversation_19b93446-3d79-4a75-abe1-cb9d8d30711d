package seoclarity.backend.cronjob;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.StringReader;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.actonia.SitemapUrlFeedInstanceEntityDAO;
import seoclarity.backend.dao.actonia.TargetUrlEntityDAO;
import seoclarity.backend.entity.SitemapUrlFeedInstanceEntity;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.cityhash.CityHashUtil;

// mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.AddManagedFeedUrlFromSitemap" -Dexec.args=""
public class AddManagedFeedUrlFromSitemap {

    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private TargetUrlEntityDAO targetUrlEntityDAO;
    
    private GroupTagEntityDAO groupTagEntityDAO;
    
    private SitemapUrlFeedInstanceEntityDAO sitemapUrlFeedInstanceEntityDAO;

    private static String folderPath = "/home/<USER>/sitemap_url_feed/needUpload/";
    public static String backUpFolder = "/home/<USER>/sitemap_url_feed/backUpFolder";
    
    public AddManagedFeedUrlFromSitemap() {
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        sitemapUrlFeedInstanceEntityDAO = SpringBeanFactory.getBean("sitemapUrlFeedInstanceEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
    }

    public static void main(String[] args) {


        AddManagedFeedUrlFromSitemap ins = new AddManagedFeedUrlFromSitemap();
        ins.process();
    }

    private void process() {

        try {
            System.out.println("===========start process ===================");
            for (File file : new File(folderPath).listFiles()) {
                if (StringUtils.endsWith(file.getName(), ".txt") && StringUtils.startsWith(file.getName(), "sitemap_")) {
                    System.out.println("file name : " + file.getName());
                    
                    getUrlListFromFile(file);
                    
//                    moveFileAndZip(file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
    }
    
    
    private static final String[] headers = new String[] {
			"sitemap",
		};
    
    private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n').withHeader(headers);
    private Integer MAX_CHECK_COUNT = 100;
    private Integer DEFAULT_LIMITED_NUM = 100;
    
    private static String DYNAMIC_TAG_NAME = "New Sitemap URLs (" + FormatUtils.formatDate(FormatUtils.getMondayOfCurrentWeek(new Date()), "yyyyWW") + ") (dt)";

    private void getUrlListFromFile(File file) throws Exception {
    	System.out.println("Uploading : " + file.getName());
    	
    	Long queueBaseTaskId = 0L;
    	
        int totalSize = 0;
        String[] parts = StringUtils.split(file.getName(), "_");
        //String prefix = sitemap_11520_20240510_20240105_13737794600659123310_8231808820833110847.txt

        Integer ownDomainId = StringUtils.isNotBlank(parts[1]) ? NumberUtils.toInt(parts[1]) : 0;
        Integer logDate = StringUtils.isNotBlank(parts[2]) ? NumberUtils.toInt(parts[2]) : 0;
        Integer lastModifyDate = StringUtils.isNotBlank(parts[3]) ? NumberUtils.toInt(parts[3]) : 1;
        String sitemapHash = StringUtils.isNotBlank(parts[4]) ? parts[4] : "";
        
        GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(ownDomainId, DYNAMIC_TAG_NAME, GroupTagEntity.TAG_TYPE_TARGET_URL);
        
        Integer groupTagId = 0;
        
        if (groupTagEntity != null && groupTagEntity.getId() > 0) {
    		groupTagId = groupTagEntity.getId();
		} else {
			groupTagId = groupTagEntityDAO.insertForDynamicTag(ownDomainId, GroupTagEntity.TAG_TYPE_TARGET_URL, DYNAMIC_TAG_NAME, DYNAMIC_TAG_NAME, 0);
		}

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);
        //ownDomainSettingEntity.getSitemapUrlFeedLimit()
        Integer limitedNum = DEFAULT_LIMITED_NUM;
        if (ownDomainSettingEntity != null) {
			System.out.println("OwnDomainSetting not found! ID:" + ownDomainId);
			
			limitedNum = ownDomainSettingEntity.getSitemapUrlFeedLimit();
	        if (limitedNum == null ||limitedNum == 0) {
	        	limitedNum = DEFAULT_LIMITED_NUM;
			}
		}
        
        System.out.println("limitedNum:" + limitedNum);
        
        List<String> siteMapList = new ArrayList<String>();
        List<String> unmanagedUrlList = new ArrayList<String>();
        try {
        	
            BufferedReader bf = new BufferedReader(new FileReader(file));
    		String content = "";
    		int i = 0;
    		while (content != null) {
    			content = bf.readLine();
    			if (content == null) {
    				break;
    			}
    			i++;
    			
                try {
                	CSVParser csvParser = new CSVParser(new StringReader(content), csvFormat);
                	String sitemap = getSiteMapEntity(csvParser.getRecords().get(0));
                	csvParser.close();
                	siteMapList.add(sitemap);
                } catch (Exception e) {
                    System.out.println("line i : " + i);
                    e.printStackTrace();
                    continue;
                }
                
                if (siteMapList.size() >= MAX_CHECK_COUNT) {
                	
                	List<String> existedList = targetUrlEntityDAO.getUnManagedUrlByIndexKey(siteMapList, ownDomainId);
                	
                	System.out.println("existedList size:" + existedList.size());
                	siteMapList.removeAll(existedList);
                	
                	System.out.println("siteMapList size:" + siteMapList.size());
                	unmanagedUrlList.addAll(siteMapList);
                	
                	System.out.println("unmanagedUrlList size:" + unmanagedUrlList.size());
                	
                	siteMapList.clear();
                    if (unmanagedUrlList.size() >= limitedNum) {
                    	unmanagedUrlList = unmanagedUrlList.subList(0, limitedNum);
                    	break;
					}
                    
                    try {
                    	System.out.println(" sleep 1 seconds!");
                    	Thread.sleep(1 * 100);
					} catch (Exception e) {
						e.printStackTrace();
					}
                }
    		}

    		bf.close();
        	
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        
        System.out.println("groupTagId:" + groupTagId);
        System.out.println("unmanagedUrlList size : " + unmanagedUrlList.size());
        
//        if (unmanagedUrlList != null && unmanagedUrlList.size() > 0 && groupTagId > 0) {
//        	
//        	try {
//        		System.out.println(" start to add queue base task");
//            	queueBaseTaskId = addManagedUrlTagQueuebaseTask(ownDomainId, unmanagedUrlList, NumberUtils.toLong(groupTagId + ""));
//        	} catch (Exception e) {
//				e.printStackTrace();
//			}
//        	
//		}
//        
//        SitemapUrlFeedInstanceEntity sitemapUrlFeedInstanceEntity = new SitemapUrlFeedInstanceEntity();
//        
//        sitemapUrlFeedInstanceEntity.setOwnDomainId(ownDomainId);
//        sitemapUrlFeedInstanceEntity.setLogDate(logDate);
//        sitemapUrlFeedInstanceEntity.setQueueBaseInfoId(NumberUtils.toInt(queueBaseTaskId +""));
//        sitemapUrlFeedInstanceEntity.setLastModifiedDate(lastModifyDate);
//        sitemapUrlFeedInstanceEntity.setSitemapMurmur3hash(sitemapHash);
//        
//        try {
//			
//        	sitemapUrlFeedInstanceEntityDAO.addInstance(sitemapUrlFeedInstanceEntity);
//		} catch (Exception e) {
////			e.printStackTrace();
//			// TODO: handle exception
//		}

    }
    
    
    //		* resourceMain: url
    //		* resourceId: tagId
    private Long addManagedUrlTagQueuebaseTask(Integer ownDomainId, List<String> urlList, Long tagId) {

        int actionType = ResourceBatchInfoEntity.TYPE_ADD;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG_ID;

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(ownDomainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(new Date());
        Long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();

        for(String url : urlList) {
        	 ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
             rbd.setInfoId(id);
             rbd.setActionType(actionType);
             rbd.setOwnDomainId(ownDomainId);
             rbd.setResourceMain(url);
             rbd.setResourceId(tagId);
             rbd.setResourceMd5(Md5Util.Md5(ownDomainId + url + tagId + new Date().getTime() + ""));
             rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
             resourceBatchDetailEntityList.add(rbd);

        }
       

        if (CollectionUtils.isNotEmpty(resourceBatchDetailEntityList)) {
            System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
            resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        }
        
        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);

        return id;

    }

    private void moveFileAndZip(String filePath) {

        File bkFolder = new File(backUpFolder);
        if (bkFolder == null || !bkFolder.isDirectory()) {
            System.out.println("BK folder is not exist, mkdir: " + backUpFolder);
            bkFolder.mkdir();
        }

        File tmpFile;
        File targetFile;

        // zip file
        try {
            tmpFile = new File(filePath);
            System.out.println("delete file : [" + tmpFile + "]");
            tmpFile.delete();
            
//            targetFile = new File(bkFolder + "/" + tmpFile.getName());
//
//            FileUtils.moveFile(tmpFile, targetFile);
//
//            System.out.println("zipped file : " + targetFile.getAbsolutePath());
//            GZipUtil.zipFile(targetFile.getAbsolutePath());
//
//            targetFile.delete();
//            System.out.println("delete file : [" + filePath + "]");
        } catch (Exception e) {
            System.out.println("delete file failed. file: [" + filePath + "]");
            e.printStackTrace();
        }

    }
    
    private String getSiteMapEntity(CSVRecord csvRecord) throws ParseException {
        return csvRecord.get("sitemap");
    }

}
