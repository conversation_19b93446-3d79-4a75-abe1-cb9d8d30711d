package seoclarity.backend.cronjob;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.clickhouse.ga.GaClarityDBEntityDAO;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.ExtractBotGaInfoFor8422DailyJob" -Dexec.args=""
public class ExtractBotGaInfoFor8422DailyJob {
	
    private static final String LOCAL_FOLDER = "/home/<USER>/";
    private GaClarityDBEntityDAO gaClarityDBEntityDAO;

	public ExtractBotGaInfoFor8422DailyJob() {
		// TODO Auto-generated constructor stub
		gaClarityDBEntityDAO = SpringBeanFactory.getBean("gaClarityDBEntityDAO");
	}

	private static Date startDate = FormatUtils.toDate("20220317", "yyyyMMdd");
	private static Date endDate = DateUtils.addDays(new Date(), -2);
	
	public static void main(String[] args) throws IOException {
		if(args != null && args.length >= 1) {
			
			endDate = FormatUtils.toDate(args[0], "yyyyMMdd");
		}
		
		ExtractBotGaInfoFor8422DailyJob extractBotGaInfoFor8422 = new ExtractBotGaInfoFor8422DailyJob();
		extractBotGaInfoFor8422.process();
	}
	
	private static Integer domainId = 8422;
	
	private static List<String> regList = new ArrayList<String>();
	static {
		/**
		 * 
		 	T3.5.02: Phone Prefix (Control)
			https://www.spokeo.com/[0-9]{3}-[0-9]{2}(3|6|8|9)($|/)
			T3.5.02: Phone Prefix (Test)
			https://www.spokeo.com/[0-9]{3}-[0-9]{2}(2|4|5|7)($|/)
			
		 */
		regList.add("https://www.spokeo.com/[0-9]{3}-[0-9]{2}(3|6|8|9)($|/)");
		regList.add("https://www.spokeo.com/[0-9]{3}-[0-9]{2}(2|4|5|7)($|/)");
	}
	
	private void process() throws IOException {
		
		boolean haveHistFile = false;
		
		File yesterdayFile = new File("/home/<USER>/8422/gabot/Googlebot_smartphone_phoneprefix_" 
				+ FormatUtils.formatDate(DateUtils.addDays(endDate, -1), "MM_dd_yyyy") + ".txt");
		
		File newFile = new File("/home/<USER>/8422/gabot/Googlebot_smartphone_phoneprefix_" 
				+ FormatUtils.formatDate(endDate, "MM_dd_yyyy") + ".txt");
		
		if (newFile != null && newFile.exists() && newFile.isFile()) {
			
			System.out.println("new file exist, deleted! path:" + "/home/<USER>/8422/gabot/Googlebot_smartphone_phoneprefix_" 
					+ FormatUtils.formatDate(endDate, "MM_dd_yyyy") + ".txt");
			newFile.delete();
		}
		
		FileWriter fw = new FileWriter(newFile, true);
		if (yesterdayFile != null && yesterdayFile.exists() && yesterdayFile.isFile()) {
			//file dont exist then do full date range
			
			haveHistFile = true;
			
			BufferedReader bf2 = new BufferedReader(new FileReader(yesterdayFile));
			String content2 = "";
			while (content2 != null) {
				content2 = bf2.readLine();
				if (StringUtils.isNotBlank(content2) && !StringUtils.equalsIgnoreCase(content2, "null")) {
					fw.write(content2 + "\n");
				}
			}
			bf2.close();
		}
		
		Date tmpDate = startDate;
		if (haveHistFile) {
			tmpDate = endDate;
		} else {
			System.out.println("Write header!");
			fw.write("Date" + "\t" + "T3.5.02: Phone Prefix (Control)" + "\t" + "T3.5.02: Phone Prefix (Test)" + "\n");
		}
		
		do {
			System.out.println("=== processing on " + FormatUtils.formatDate(tmpDate, "yyyy-MM-dd"));
			Map<String, Object> entranceMap = gaClarityDBEntityDAO.getDailyExtractPhonePrefix(domainId, startDate, tmpDate);
			if (entranceMap == null || entranceMap.keySet() == null || entranceMap.keySet().size() != 2) {
				System.out.println(" @@@@ result is not correct!");
				continue;
			}
			
			System.out.println(new Gson().toJson(entranceMap));
			Integer en1 = NumberUtils.toInt(entranceMap.get("en1") != null ? entranceMap.get("en1").toString() : "");
			Integer en2 = NumberUtils.toInt(entranceMap.get("en2") != null ? entranceMap.get("en2").toString() : "");
			
			System.out.println(FormatUtils.formatDate(tmpDate, "yyyy-MM-dd") + "-" + en1 + "-" + en2 );
			fw.write(FormatUtils.formatDate(tmpDate, "MM/dd/yyyy") + "\t" + en1 + "\t" + en2 + "\n");
			
			tmpDate = DateUtils.addDays(tmpDate, 1);
		} while (tmpDate.compareTo(endDate) <= 0);
		
		fw.close();
		
		try {
			
			FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, 
					newFile.getAbsolutePath(), LOCAL_FOLDER + domainId, 0, 3);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
