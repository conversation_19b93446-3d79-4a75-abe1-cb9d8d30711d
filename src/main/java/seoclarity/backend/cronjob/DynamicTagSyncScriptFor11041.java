package seoclarity.backend.cronjob;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.actonia.TargetUrlCrawlAdditionalContentDAO;
import seoclarity.backend.dao.clickhouse.urlclaritydb.CrawlUrlDao;
import seoclarity.backend.entity.ResultContent;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ResourceAddInfoEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.actonia.TargetUrlCrawlAdditionalEntity;
import seoclarity.backend.entity.clickhouse.crawlurl.TargetUrlHtmlDaily;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.DynamicTagSyncScriptFor11041" -Dexec.args=""
 * <AUTHOR>
 *
 */
public class DynamicTagSyncScriptFor11041 {
	
//	private ClColdDailyRankingEntityDao clColdDailyRankingEntityDao;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private CrawlUrlDao crawlUrlDao;
	private GroupTagEntityDAO groupTagEntityDAO;
//	private ResourceDeleteInfoEntityDAO resourceDeleteInfoEntityDAO;
//	private ResourceDeleteDetailEntityDAO resourceDeleteDetailEntityDAO;
//	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private TargetUrlCrawlAdditionalContentDAO targetUrlCrawlAdditionalContentDAO;
	
	private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
	private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
	
	public DynamicTagSyncScriptFor11041() {
//		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
//		clColdDailyRankingEntityDao = SpringBeanFactory.getBean("clColdDailyRankingEntityDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		crawlUrlDao = SpringBeanFactory.getBean("crawlUrlDao");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
//		resourceDeleteDetailEntityDAO = SpringBeanFactory.getBean("resourceDeleteDetailEntityDAO");
//		resourceDeleteInfoEntityDAO = SpringBeanFactory.getBean("resourceDeleteInfoEntityDAO");
		targetUrlCrawlAdditionalContentDAO = SpringBeanFactory.getBean("targetUrlCrawlAdditionalContentDAO");
		resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
		resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
	}
	
	private static Integer processOID = 11041;
	private static final int DEFAULF_USER = 214;
	public static final String MD5_SPLIT = "	";
	public static boolean isCheckAddDetailExists = false;
	
	public static void main(String[] args) {
		DynamicTagSyncScriptFor11041 dynamicTagSyncScriptForTarget = new DynamicTagSyncScriptFor11041();
		dynamicTagSyncScriptForTarget.process();
		
	}
	
	private static Map<String, String> xpathMap = new HashMap<>();
	

	private static String TAG_PREFIX = " (dt)";
//	private static Map<String, Integer> groupDictNameIdMap = new HashMap<>();
	
	
	private void process() {
		
		List<TargetUrlCrawlAdditionalEntity> xpathList = targetUrlCrawlAdditionalContentDAO.getByDomainId(processOID);
		for(TargetUrlCrawlAdditionalEntity targetUrlCrawlAdditionalEntity: xpathList) {
			
			String tagName = parseTagName(targetUrlCrawlAdditionalEntity.getFriendlyName()) + TAG_PREFIX;
			
			Integer groupTagId = 0;
        	GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(processOID, tagName, GroupTagEntity.TAG_TYPE_TARGET_URL);
        	if (groupTagEntity != null && groupTagEntity.getId() > 0) {
        		groupTagId = groupTagEntity.getId();
			} else {
				System.out.println("INS tag , tagName:" + tagName);
				groupTagId = groupTagEntityDAO.insert(processOID, GroupTagEntity.TAG_TYPE_TARGET_URL, tagName, 0);
			}
        	
			xpathMap.put(targetUrlCrawlAdditionalEntity.getId() + "", groupTagId + "");
		}
		
		System.out.println("XPATH size:" + xpathMap.keySet().size());
		
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(processOID);
		
		if (ownDomainEntity == null ) {
			System.out.println(" ownDomainEntity is not found!!!");
			return ;
		}
		
		
		List<String[]> relations = new ArrayList<>();
		try {
			List<TargetUrlHtmlDaily> targetUrlHtmlDailyList = crawlUrlDao.getAllCustomDataByDate(processOID, 
					FormatUtils.formatDate(FormatUtils.getYesterday(true), "yyyy-MM-dd"));
			
			for(TargetUrlHtmlDaily targetUrlHtmlDaily : targetUrlHtmlDailyList) {
				if (targetUrlHtmlDaily != null && StringUtils.isNotBlank(targetUrlHtmlDaily.getCustomData())) {
					
					try {
						ResultContent[] results = new Gson().fromJson(targetUrlHtmlDaily.getCustomData(), ResultContent[].class);
						if (results != null && results.length > 0 ) {
							
							for(ResultContent result : results) {
									
								System.out.println("XPATH id:" + result.getTarget_url_crawl_additional_content_id());
								if (!result.getMatch_found()) {
									System.out.println("match found:" + result.getMatch_found());
									continue;
								}
								String groupTagId = xpathMap.get(result.getTarget_url_crawl_additional_content_id() + "");
								
								if (StringUtils.isBlank(groupTagId)) {
									System.out.println("TAG not found in XPATH! id:" + result.getTarget_url_crawl_additional_content_id());
									continue;
								}
								
					        	if (NumberUtils.toInt(groupTagId) == 0) {
									System.out.println(" @@@ Group Tag Id IS NOT SETTED correctly!!!");
								} else {
									relations.add(new String[] {targetUrlHtmlDaily.getUrl(), groupTagId + ""});
									System.out.println("Relation:" + targetUrlHtmlDaily.getUrl() + ", " + groupTagId + "");
								}
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		if (relations.size() > 0) {
			System.out.println("Relation size : " + relations.size());
		}
		
		/**
	     * resourceMain: url
	     * resourceId: tagId
	     */
		int actionType = ResourceBatchInfoEntity.TYPE_ADD;
		int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG_ID;
		
		ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
		resourceBatchInfoEntity.setActionType(actionType);
		resourceBatchInfoEntity.setOwnDomainId(processOID);
		resourceBatchInfoEntity.setOperationType(operationType);
		resourceBatchInfoEntity.setStatus(ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR);
		resourceBatchInfoEntity.setUserId(DEFAULF_USER);
		resourceBatchInfoEntity.setCreateDate(new Date());
		
		long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
		
		List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
		for (String[] rel : relations) {
			String url = rel[0];
			String tagId = rel[1];
			if (StringUtils.isNotBlank(url) && StringUtils.isNotBlank(tagId) && NumberUtils.toLong(tagId) > 0) {
				ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
		        rbd.setInfoId(id);
		        rbd.setActionType(actionType);
		        rbd.setOwnDomainId(processOID);
		        rbd.setResourceMain(url);
		        rbd.setResourceId(NumberUtils.toLong(tagId));
		        rbd.setResourceMd5(Md5Util.Md5(url + tagId + processOID + FormatUtils.formatDate(new Date(), "yyyy-MM-dd") + ""));
		        rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
		        resourceBatchDetailEntityList.add(rbd);
			} else {
				System.out.println("ERROR! url: " + url + ", tagId: " + tagId);
			}
		}
		if (relations.size() == 0) {
			System.out.println("no details... skip");
			return;
		}
	    
		resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
        System.out.println("Qbs====>create new info, id: " + id + ", resourceBatchDetailEntityList size: " + resourceBatchDetailEntityList.size());
		
		resourceBatchInfoEntityDAO.updateStatusAfterProcess(id, ResourceAddInfoEntity.STATUS_NEWLY_CREATED, new Date());
		
	}
	
	public static String calculateMd5(String valueMain, String valueSubordinate) {
		String input = valueMain;
		if (StringUtils.isNotBlank(valueSubordinate)) {
			input = input + MD5_SPLIT + valueSubordinate;
		}
		return FormatUtils.hexDigest(input.getBytes());
	}
	
    private String parseTagName(String tagName) {
    	if (StringUtils.contains(tagName, '\u200e')) {
    		String[] tagNameSubArray = StringUtils.split(tagName, '\u200e');
    		for(String tag : tagNameSubArray) {
	    	   if (!StringUtils.contains(tag, '\u200e')) {
	    		   tagName = tag;
	   				break;
	   			}
    		}
		}
    	
    	tagName = StringUtils.trim(tagName);
    	return tagName;
    }
	
	
}
