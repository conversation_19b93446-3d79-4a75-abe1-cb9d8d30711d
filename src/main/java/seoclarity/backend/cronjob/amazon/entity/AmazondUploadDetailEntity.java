package seoclarity.backend.cronjob.amazon.entity;

import java.util.List;

import org.apache.commons.lang.StringUtils;

public class AmazondUploadDetailEntity {
	
	public static final int TYPE_PRODUCT_INT = 1;
	public static final int TYPE_VIDEO_SINGLE_PRODUCT_INT = 2;
	public static final int TYPE_FEATURED_ASINS_LIST_INT = 3;
	public static final int TYPE_EDITORIAL_RECOMMENDATIONS_INT = 4;
	public static final int TYPE_CARDS_INT = 5;
	public static final int TYPE_OTHER_INT = 9;
	
	public static final String TYPE_PRODUCT_STRING = "Product";
	public static final String TYPE_VIDEO_SINGLE_PRODUCT_STRING = "VIDEO_SINGLE_PRODUCT";
	public static final String TYPE_FEATURED_ASINS_LIST_STRING = "FEATURED_ASINS_LIST";
	public static final String TYPE_EDITORIAL_RECOMMENDATIONS_STRING = "EDITORIAL_RECOMMENDATIONS";
	public static final String TYPE_CARDS_STRING = "CARDS";
	
	private Integer rankPosition;
	
	private String type;

	private String title;
	
	private String link;
	
	private String image;
	
	private String asin;
	
	private Boolean sponsored;

	private Boolean prime;

	private Boolean fresh;

	private String rating;
	
	private Integer rateNumber;
	
	private String nowPrice;
	
	private String historyPrice;
	
	private String[] additionalInfo;
	
	private Boolean adsFlg;

	public Integer getRankPosition() {
		return rankPosition;
	}

	public void setRankPosition(Integer rankPosition) {
		this.rankPosition = rankPosition;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	public String getAsin() {
		return asin;
	}

	public void setAsin(String asin) {
		this.asin = asin;
	}

	public Boolean getSponsored() {
		return sponsored;
	}

	public void setSponsored(Boolean sponsored) {
		this.sponsored = sponsored;
	}

	public Boolean getPrime() {
		return prime;
	}

	public void setPrime(Boolean prime) {
		this.prime = prime;
	}

	public Boolean getFresh() {
		return fresh;
	}

	public void setFresh(Boolean fresh) {
		this.fresh = fresh;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public Integer getRateNumber() {
		return rateNumber;
	}

	public void setRateNumber(Integer rateNumber) {
		this.rateNumber = rateNumber;
	}

	public String getNowPrice() {
		return nowPrice;
	}

	public void setNowPrice(String nowPrice) {
		this.nowPrice = nowPrice;
	}

	public String getHistoryPrice() {
		return historyPrice;
	}

	public void setHistoryPrice(String historyPrice) {
		this.historyPrice = historyPrice;
	}

	public String[] getAdditionalInfo() {
		return additionalInfo;
	}

	public void setAdditionalInfo(String[] additionalInfo) {
		this.additionalInfo = additionalInfo;
	}
	
	public Boolean getAdsFlg() {
		return adsFlg;
	}

	public void setAdsFlg(Boolean adsFlg) {
		this.adsFlg = adsFlg;
	}

	//	public static final int TYPE_PRODUCT_INT = 1;
//	public static final int TYPE_VIDEO_SINGLE_PRODUCT_INT = 2;
//	public static final int TYPE_FEATURED_ASINS_LIST_INT = 3;
//	public static final int TYPE_EDITORIAL_RECOMMENDATIONS_INT = 4;
//	public static final int TYPE_CARDS_INT = 5;
//	public static final int TYPE_OTHER_INT = 9;
//	
//	public static final String TYPE_PRODUCT_STRING = "Product";
//	public static final String TYPE_VIDEO_SINGLE_PRODUCT_STRING = "VIDEO_SINGLE_PRODUCT";
//	public static final String TYPE_FEATURED_ASINS_LIST_STRING = "FEATURED_ASINS_LIST";
//	public static final String TYPE_EDITORIAL_RECOMMENDATIONS_STRING = "EDITORIAL_RECOMMENDATIONS";
//	public static final String TYPE_CARDS_STRING = "CARDS";
//	
	public int getTypeByStr() {
		int typeInt = TYPE_OTHER_INT; 
		if (StringUtils.equals(this.type, TYPE_PRODUCT_STRING)) {
			typeInt = TYPE_PRODUCT_INT;
		} else if (StringUtils.equals(this.type, TYPE_VIDEO_SINGLE_PRODUCT_STRING)) {
			typeInt = TYPE_VIDEO_SINGLE_PRODUCT_INT;
		} else if (StringUtils.equals(this.type, TYPE_FEATURED_ASINS_LIST_STRING)) {
			typeInt = TYPE_FEATURED_ASINS_LIST_INT;
		} else if (StringUtils.equals(this.type, TYPE_EDITORIAL_RECOMMENDATIONS_STRING)) {
			typeInt = TYPE_EDITORIAL_RECOMMENDATIONS_INT;
		} else if (StringUtils.equals(this.type, TYPE_CARDS_STRING)) {
			typeInt = TYPE_CARDS_INT;
		}
		
		return typeInt;
	}
	

}
