package seoclarity.backend.cronjob.amazon;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.cronjob.amazon.entity.AmazonUploadInfoEntity;
import seoclarity.backend.cronjob.amazon.entity.AmazondUploadDetailEntity;
import seoclarity.backend.dao.actonia.TiktokUploadMonitorEntityDAO;
import seoclarity.backend.dao.clickhouse.socialengine.AmazonRankingColdEntityDAO;
import seoclarity.backend.dao.clickhouse.socialengine.AmazonRankingMasterEntityDAO;
import seoclarity.backend.entity.clickhouse.amazon.AmazonAdsEntity;
import seoclarity.backend.entity.clickhouse.amazon.AmazonDetailEntity;
import seoclarity.backend.entity.clickhouse.amazon.AmazonInfoEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * 
 * <AUTHOR> @date 
 * seoclarity.backend.cronjob.amazon.AmazonUploader
 * upload to clarityDB
 */
public class AmazonUploader {
	
	private static Gson gson = new Gson();
	public static final Log logger = LogFactory.getLog(AmazonUploader.class);
	
	public static final int FLG_TRUE = 1;
	public static final int FLG_FALSE = 0;

	public static final String FLG_TRUE_STR = "Y";
	public static final String FLG_FALSE_STR = "N";
	
	
	private static List<String> processingFileList = new ArrayList<String>();
	
	private AmazonRankingColdEntityDAO amazonRankingColdEntityDAO;
	private AmazonRankingMasterEntityDAO amazonRankingMasterEntityDAO;
	private TiktokUploadMonitorEntityDAO tiktokUploadMonitorEntityDAO;
	
	private static final String databaseName = "amazon_ranking";
//	dis_ranking_detail_2025
//	dis_ranking_info_2025  
	private static final String FINAL_TABLE_INFO_TEMPLATE = "dis_ranking_info_%s";
	
	//d_ranking_detail_us_2025
	private static final String FINAL_TABLE_DETAIL_TEMPLATE = "dis_ranking_detail_%s";
	private static final String FINAL_TABLE_ADS_TEMPLATE = "dis_ads_detail_%s";
	
	private static String uploadInfoTable;
	private static String uploadDetailTable;
	private static String uploadAdsTable;
	
    public static String storeDoneFilePath = "/home/<USER>/amazon/needUpload";
    public static String storeTempFilePath = "/home/<USER>/amazon/uploading/" + FormatUtils.formatDate(new Date(), "yyyyMMddHH") + "/";    
    public static String storeBKFilePath = "/home/<USER>/amazon/backUpFolder";
    
	private static String uploadDate;
	private static String uploadYear;
	
	private static final String MONITOR_TABLE_NAME_TEMPALTE = "amazon_upload_monitor_w_%s";
	
	public AmazonUploader() {
		amazonRankingColdEntityDAO = SpringBeanFactory.getBean("amazonRankingColdEntityDAO");
		amazonRankingMasterEntityDAO = SpringBeanFactory.getBean("amazonRankingMasterEntityDAO");
		tiktokUploadMonitorEntityDAO = SpringBeanFactory.getBean("tiktokUploadMonitorEntityDAO");
		
	}

	public static void main(String args[]) {
		
		if (args != null && args.length >= 1) {
			FormatUtils.getSundayOfLastWeek(new Date());
			try {
				Date rankingDate = FormatUtils.getSundayOfLastWeek(DateUtils.parseDate(args[0], new String[]{"yyyyMMdd"}));
				uploadDate = FormatUtils.formatDate(rankingDate, "yyyyMMdd");
				uploadYear = FormatUtils.formatDate(rankingDate, "yyyy") ;
	        } catch (ParseException e) {
	            logger.error("incorrect dateformat, should be yyyyMMdd，e.g 20250410");
	        }
			
		} else {
			Date rankingDate = FormatUtils.getSundayOfLastWeek(new Date());
			uploadDate  = FormatUtils.formatDate(rankingDate, "yyyyMMdd");
			uploadYear  = FormatUtils.formatDate(rankingDate, "yyyy");
		}
		
		uploadInfoTable = String.format(FINAL_TABLE_INFO_TEMPLATE, uploadYear);
		uploadDetailTable = String.format(FINAL_TABLE_DETAIL_TEMPLATE, uploadYear);
		uploadAdsTable = String.format(FINAL_TABLE_ADS_TEMPLATE, uploadYear);
		
		AmazonUploader amazonUploader = new AmazonUploader();
		
		try {
			amazonUploader.process();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void process() {
		
		
		// get file list
		moveFilesToProcessingFolder();
		try {
			
			if (CollectionUtils.isEmpty(processingFileList)) {
				logger.info("== No file need to upload!");
				return;
			}
			
			logger.info("processingFileList size:" + processingFileList.size());

			String monitorTable = String.format(MONITOR_TABLE_NAME_TEMPALTE, uploadDate);
			
			tiktokUploadMonitorEntityDAO.dropTable(monitorTable);
			if (!tiktokUploadMonitorEntityDAO.checkTable(monitorTable)) {
				System.out.println("Not found monitor table, creating now! " + monitorTable);
				tiktokUploadMonitorEntityDAO.createTable(monitorTable);
			}
			
			File tmpFile;
			for (String amazonRankingFile : processingFileList) {
				tmpFile = new File(amazonRankingFile);
				
				if (tmpFile == null || !tmpFile.isFile()) {
					logger.info("File is not exist or is a folder : " + amazonRankingFile);
					continue;
				}
				
				logger.info("=====processing file:" + tmpFile.getAbsolutePath());
				processFile(tmpFile, monitorTable);
			}
		} catch (Exception e) {
			logger.info("Insert table failed, table: " + uploadInfoTable + ", folder: "  + storeDoneFilePath);
			moveFilesBackProcessingFolder();
			e.printStackTrace();
			return ;
		}
		
		try {
			moveFileAndZip();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			deleteTempProcessingFolder();
		}
	}
	
	private void processFile(File file, String monitorTable) throws Exception {

		List<AmazonInfoEntity> infoList = new ArrayList<>();
		List<AmazonDetailEntity> detailList = new ArrayList<>();
		List<AmazonAdsEntity> adsList = new ArrayList<>();
		
		AmazonInfoEntity amazonInfoEntity = new AmazonInfoEntity();
		AmazonDetailEntity amazonDetailEntity = new AmazonDetailEntity();
		AmazonAdsEntity amazonAdsEntity = new AmazonAdsEntity();
		List<String> attrKeyList = new ArrayList<String>();
		List<String> attrValueList = new ArrayList<String>();
		
		String rankingDate = "";
		
		List<String> uniqueList = new ArrayList<String>();
		List<AmazonUploadInfoEntity> parsedEntityList = new ArrayList<AmazonUploadInfoEntity>();
		try {

			BufferedReader bf = new BufferedReader(new FileReader(file));
			String content = "";
			
			Integer ownDomainId = 0;
			Integer relId = 0;
			String uniqKey = "";
			
//			keys[0], //keywordRankcheckId
//			keys[1], //engineId 
//			keys[2], //languageId
//			keys[3], //cityId
//			keys[4], //frequence
//			keys[5], //ownDomainId
//			keys[6]  //device
			
			while (content != null) {
				content = bf.readLine();
				if (content == null) {
					break;
				}
				
				try {
					AmazonUploadInfoEntity amazonUploadInfoEntity = gson.fromJson(content, AmazonUploadInfoEntity.class);
					
					if (amazonUploadInfoEntity.getFrequency() == null || amazonUploadInfoEntity.getFrequency() == 0) {
						amazonUploadInfoEntity.setFrequency(7);
					}
					
					if (StringUtils.isEmpty(amazonUploadInfoEntity.getDevice())) {
						amazonUploadInfoEntity.setDevice("d");
					}
					
					
					if (CollectionUtils.isEmpty(amazonUploadInfoEntity.getDomainList()) || amazonUploadInfoEntity.getDomainList().size() == 0) {
						logger.info("@@domainlist is empty, RCid:" + amazonUploadInfoEntity.getId());
						continue;
					}
					
					for(Integer i = 0; i < amazonUploadInfoEntity.getDomainList().size(); i++) {
						AmazonUploadInfoEntity amazonUploadInfoClone = clone(amazonUploadInfoEntity);
						ownDomainId = amazonUploadInfoClone.getDomainList().get(i);
						try {
							relId = amazonUploadInfoClone.getRelIdList().get(i);
						} catch (Exception e) {
							logger.info("@@relArray not matched idx, oidArray:" + amazonUploadInfoClone.getDomainList().size() 
									+ ", relArray:" + amazonUploadInfoClone.getRelIdList().size());
						}
						
						uniqKey = amazonUploadInfoClone.getId() + "-" + amazonUploadInfoClone.getEngineId() + "-" +
								amazonUploadInfoClone.getLanguageId() + "-" + 0 + "-" +
								amazonUploadInfoClone.getFrequency() + "-" + ownDomainId + "-" + amazonUploadInfoClone.getDevice();
						
						System.out.println("uniqKey:" + uniqKey);
						
						amazonUploadInfoClone.setOwnDomainId(ownDomainId);
						amazonUploadInfoClone.setRelId(relId);
						
						uniqueList.add(uniqKey);
						parsedEntityList.add(amazonUploadInfoClone);
					}
					
				} catch (Exception e) {
					logger.info("====== content can not be parse to entity, content: " + content);
					e.printStackTrace();
				}
			}
			
			bf.close();
			
			logger.info("Total check cnt:" + uniqueList.size());
			int[] results = tiktokUploadMonitorEntityDAO.insertIgnore(monitorTable, uniqueList);
			
			List<AmazonUploadInfoEntity> passedEntityList = new ArrayList<AmazonUploadInfoEntity>();
			
			for(Integer i = 0; i < results.length; i ++) {
				
				if (results[i] == 1) {
					passedEntityList.add(parsedEntityList.get(i));
				}
			}
			
			logger.info("passedEntityList:" + passedEntityList.size());
			
			for(AmazonUploadInfoEntity amazonUploadInfoEntity : passedEntityList) {
				
				rankingDate = FormatUtils.formatDate(FormatUtils.toDate(amazonUploadInfoEntity.getSendToQDate() + "", "yyyyMMdd"), "yyyy-MM-dd"); 
				amazonInfoEntity = new AmazonInfoEntity();
				
				amazonInfoEntity.setDevice(amazonUploadInfoEntity.getDevice());
				amazonInfoEntity.setKeywordName(amazonUploadInfoEntity.getKeyword());
				amazonInfoEntity.setOwnDomainId(amazonUploadInfoEntity.getOwnDomainId());
				amazonInfoEntity.setKeywordRankcheckId(amazonUploadInfoEntity.getId());
				amazonInfoEntity.setEngineId(amazonUploadInfoEntity.getEngineId());
				
				amazonInfoEntity.setLanguageId(amazonUploadInfoEntity.getLanguageId());
				amazonInfoEntity.setRankingDate(rankingDate);
				
				amazonInfoEntity.setAttrstrkey(new String[] {});
				amazonInfoEntity.setAttrstrvalue(new String[] {});
				amazonInfoEntity.setAttrintkey(new String[] {});
				amazonInfoEntity.setAttrintvalue(new Integer[] {});
				
//				amazonInfoEntity.setRelId(amazonUploadInfoEntity.getRelId());
//				amazonInfoEntity.setFrequency(amazonUploadInfoEntity.getFrequency());
				
				
				infoList.add(amazonInfoEntity);
				for(AmazondUploadDetailEntity amazondUploadDetailEntity : amazonUploadInfoEntity.getKeywordRankEntityVOs()) {
					
					if (amazondUploadDetailEntity.getAdsFlg()) {
						
						amazonAdsEntity = new AmazonAdsEntity();
						
						amazonAdsEntity.setDevice(amazonUploadInfoEntity.getDevice());
						amazonAdsEntity.setKeywordName(amazonUploadInfoEntity.getKeyword());
						amazonAdsEntity.setOwnDomainId(amazonUploadInfoEntity.getOwnDomainId());
						amazonAdsEntity.setKeywordRankcheckId(amazonUploadInfoEntity.getId());
						amazonAdsEntity.setEngineId(amazonUploadInfoEntity.getEngineId());
						
						amazonAdsEntity.setLanguageId(amazonUploadInfoEntity.getLanguageId());
						amazonAdsEntity.setRankingDate(rankingDate);
						amazonAdsEntity.setRank(amazondUploadDetailEntity.getRankPosition());
						amazonAdsEntity.setTitle(amazondUploadDetailEntity.getTitle());
						amazonAdsEntity.setLink(amazondUploadDetailEntity.getLink());
						
						amazonAdsEntity.setImage(amazondUploadDetailEntity.getImage());
						amazonAdsEntity.setAsin(amazondUploadDetailEntity.getAsin());
						amazonAdsEntity.setAdsFlag(getFlgIntFromBoolean(amazondUploadDetailEntity.getAdsFlg()));
						amazonAdsEntity.setFreshFlag(getFlgIntFromBoolean(amazondUploadDetailEntity.getAdsFlg()));
						amazonAdsEntity.setPrimeFlag(getFlgIntFromBoolean(amazondUploadDetailEntity.getPrime()));
						
						amazonAdsEntity.setRating(amazondUploadDetailEntity.getRating());
						amazonAdsEntity.setRateNumber(amazondUploadDetailEntity.getRateNumber());
						amazonAdsEntity.setPrice(amazondUploadDetailEntity.getNowPrice());
						amazonAdsEntity.setHistoryPrice(amazondUploadDetailEntity.getHistoryPrice());
						
//						amazonAdsEntity.setType(amazondUploadDetailEntity.getTypeByStr());
						adsList.add(amazonAdsEntity);
					} else {
						
						attrKeyList = new ArrayList<String>();
						attrValueList = new ArrayList<String>();
						
						attrKeyList.add("sponsored_flag");
						attrValueList.add(getFlgStrFromBoolean(amazondUploadDetailEntity.getSponsored()));
						
						attrKeyList.add("fresh_flag");
						attrValueList.add(getFlgStrFromBoolean(amazondUploadDetailEntity.getFresh()));
						
						amazonDetailEntity = new AmazonDetailEntity();
						amazonDetailEntity.setDevice(amazonUploadInfoEntity.getDevice());
						amazonDetailEntity.setKeywordName(amazonUploadInfoEntity.getKeyword());
						amazonDetailEntity.setOwnDomainId(amazonUploadInfoEntity.getOwnDomainId());
						amazonDetailEntity.setKeywordRankcheckId(amazonUploadInfoEntity.getId());
						amazonDetailEntity.setEngineId(amazonUploadInfoEntity.getEngineId());
						
						amazonDetailEntity.setLanguageId(amazonUploadInfoEntity.getLanguageId());
						amazonDetailEntity.setRankingDate(rankingDate);
						amazonDetailEntity.setRank(amazondUploadDetailEntity.getRankPosition());
						amazonDetailEntity.setType(amazondUploadDetailEntity.getTypeByStr());
						amazonDetailEntity.setAsin(amazondUploadDetailEntity.getAsin());
						
						amazonDetailEntity.setImageUrl(amazondUploadDetailEntity.getImage());
						amazonDetailEntity.setUrl(amazondUploadDetailEntity.getLink());
						amazonDetailEntity.setTitle(amazondUploadDetailEntity.getTitle());
						amazonDetailEntity.setRating(amazondUploadDetailEntity.getRating());
						amazonDetailEntity.setRateNumber(amazondUploadDetailEntity.getRateNumber());
						
						amazonDetailEntity.setPrimeFlag(getFlgIntFromBoolean(amazondUploadDetailEntity.getPrime()));
						amazonDetailEntity.setNowPrice(amazondUploadDetailEntity.getNowPrice());
						amazonDetailEntity.setHistoryPrice(amazondUploadDetailEntity.getHistoryPrice());
						amazonDetailEntity.setAdditionalInfo(amazondUploadDetailEntity.getAdditionalInfo());
						amazonDetailEntity.setAttrstrkey(attrKeyList.toArray(new String[0]));
						
						amazonDetailEntity.setAttrstrvalue(attrValueList.toArray(new String[0]));
						amazonDetailEntity.setAttrintkey(new String[] {});
						amazonDetailEntity.setAttrintvalue(new Integer[] {});
						
						detailList.add(amazonDetailEntity);
					}
				}
			}
			
			try {
				if (!CollectionUtils.isEmpty(infoList)) {
					logger.info("===Uploading to master info");
					amazonRankingMasterEntityDAO.insertForBatchAmazonInfo(infoList, getInfoTableName());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				if (!CollectionUtils.isEmpty(detailList)) {
					logger.info("===Uploading to master detail");
					amazonRankingMasterEntityDAO.insertForBatchAmazonDetail(detailList, getDetailTableName());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				if (!CollectionUtils.isEmpty(adsList)) {
					logger.info("===Uploading to master ads");
					amazonRankingMasterEntityDAO.insertForBatchAmazonAds(adsList, getAdsTableName());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				if (!CollectionUtils.isEmpty(infoList)) {
					logger.info("===Uploading to master info");
					amazonRankingColdEntityDAO.insertForBatchAmazonInfo(infoList, getInfoTableName());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				if (!CollectionUtils.isEmpty(detailList)) {
					logger.info("===Uploading to master detail");
					amazonRankingColdEntityDAO.insertForBatchAmazonDetail(detailList, getDetailTableName());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			try {
				if (!CollectionUtils.isEmpty(adsList)) {
					logger.info("===Uploading to master ads");
					amazonRankingColdEntityDAO.insertForBatchAmazonAds(adsList, getAdsTableName());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			
			
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
	}
	
	private AmazonUploadInfoEntity clone(AmazonUploadInfoEntity amazonUploadInfoEntity) {
		AmazonUploadInfoEntity AmazonUploadInfoEntityClone = new AmazonUploadInfoEntity();
		
//		scribeMap.put("queryDate", CacheModleFactory.getInstance().getFormatDate());
//		scribeMap.put("qeuryState", linkRankVO == null ? 0 : linkRankVO.size());
//      scribeMap.put("firstPageSize", linkRankVO == null ? 0 : linkRankVO.size());
//		scribeMap.put("keyword", keywordProperty.getKeywordText());
//		scribeMap.put("createDate", keywordProperty.getCreateDate());
//      scribeMap.put("domainList", keywordProperty.getDomainList());
//      scribeMap.put("searchVol", keywordProperty.getSearchVol());
//      scribeMap.put("cpc", keywordProperty.getCpc());
//		scribeMap.put("id", keywordProperty.getId());
//		scribeMap.put("sendToQDate", keywordProperty.getSendToQDate());
//		scribeMap.put("keywordRankEntityVOs", linkRankVO);
		
		AmazonUploadInfoEntityClone.setSendToQDate(amazonUploadInfoEntity.getSendToQDate());
		AmazonUploadInfoEntityClone.setLanguageId(amazonUploadInfoEntity.getLanguageId());
		AmazonUploadInfoEntityClone.setKeywordRankEntityVOs(amazonUploadInfoEntity.getKeywordRankEntityVOs());
		AmazonUploadInfoEntityClone.setDomainList(amazonUploadInfoEntity.getDomainList());
		AmazonUploadInfoEntityClone.setRelIdList(amazonUploadInfoEntity.getRelIdList());
		AmazonUploadInfoEntityClone.setId(amazonUploadInfoEntity.getId());
		AmazonUploadInfoEntityClone.setKeyword(amazonUploadInfoEntity.getKeyword());
		AmazonUploadInfoEntityClone.setDevice(amazonUploadInfoEntity.getDevice());
		AmazonUploadInfoEntityClone.setEngineId(amazonUploadInfoEntity.getEngineId());
		AmazonUploadInfoEntityClone.setFrequency(amazonUploadInfoEntity.getFrequency());
		
		
		return AmazonUploadInfoEntityClone;
	}

	public String getInfoTableName() {
		return databaseName + "." + uploadInfoTable;
	}
	
	public String getDetailTableName() {
		return databaseName + "." + uploadDetailTable;
	}
	
	public String getAdsTableName() {
		return databaseName + "." + uploadAdsTable;
	}
	
	private void moveFilesToProcessingFolder(){
		
		File targetFolder = new File(storeTempFilePath);
		if (targetFolder == null || !targetFolder.isDirectory()) {
			logger.info("Folder is not exist, mkdir: " + storeTempFilePath);
			targetFolder.mkdir();
		}
		
		File doneFolder = new File(storeDoneFilePath);
		if (doneFolder == null || !doneFolder.isDirectory()) {
			logger.info("Folder is not exist :" + doneFolder);
			return;
		}
		
		logger.info("====moving files to processing folder!! from " + storeDoneFilePath + " to " + storeTempFilePath);
		
		for (File file : doneFolder.listFiles()) {
			try {
				
				if (StringUtils.startsWith(file.getName(), "amazon_") && StringUtils.endsWith(file.getName(), ".txt") &&file.isFile()) {
					String[] keyArray = StringUtils.split(file.getName(), "_");
					String fileBelongDate = FormatUtils.formatDate(FormatUtils.getSundayOfLastWeek(FormatUtils.toDate(keyArray[1], "yyyyMMdd")), "yyyyMMdd");
					if (StringUtils.equals(fileBelongDate, uploadDate)) {
						FileUtils.moveFile(file, new File(storeTempFilePath + "/" + file.getName()));
						processingFileList.add(storeTempFilePath + "/" + file.getName());
					} else {
						logger.info("=== skip file:" + file.getName() + ", date not matched:" + fileBelongDate + "-" + uploadDate);
					}
				} else {
					logger.info("=== skip file:" + file.getName());
				}
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
	}
	
	private void moveFilesBackProcessingFolder(){
		
		File processingFolder = new File(storeTempFilePath);
		
		logger.info("====moving files back from processing folder !! from " + storeTempFilePath + " to " + storeDoneFilePath);
		
		for (File file : processingFolder.listFiles()) {
			try {
				FileUtils.moveFile(file, new File(storeDoneFilePath + "/" + file.getName()));
				processingFileList.add(storeDoneFilePath + "/" + file.getName());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		deleteTempProcessingFolder();
	}
	
	private void moveFileAndZip(){
		
		File bkFolder = new File(storeBKFilePath);
		if (bkFolder == null || !bkFolder.isDirectory()) {
			logger.info("BK folder is not exist, mkdir: " + storeBKFilePath);
			bkFolder.mkdir();
		}
		
		File tmpFile;
		File targetFile;
		
		for (String fileFullPath : processingFileList) {
			// zip file
			try {
				tmpFile = new File(fileFullPath);
				targetFile = new File(bkFolder + "/" + tmpFile.getName());
				
				FileUtils.moveFile(tmpFile, targetFile);
				
				logger.info("zipped file : " + targetFile.getAbsolutePath());
	            GZipUtil.zipFile(targetFile.getAbsolutePath());
	            
	            targetFile.delete();
	            logger.info("delete file : [" + fileFullPath + "]");
	        } catch (Exception e) {
	        	logger.info("delete file failed. file: [" + fileFullPath + "]");
	            e.printStackTrace();
	        }
			
		}
		
		deleteTempProcessingFolder();
	}
	
	private void deleteTempProcessingFolder(){
		//deleted the temp processing folder
		File tempFolder = new File(storeTempFilePath);
		
		if (tempFolder != null && tempFolder.isDirectory()) {
			tempFolder.delete();
		}
	}
	
	private String getFlgStrFromBoolean(Boolean flg) {
		if (flg) {
			return FLG_TRUE_STR;
		}
		return FLG_FALSE_STR;
	}
	
	private int getFlgIntFromBoolean(Boolean flg) {
		if (flg) {
			return FLG_TRUE;
		}
		return FLG_FALSE;
	}
}
