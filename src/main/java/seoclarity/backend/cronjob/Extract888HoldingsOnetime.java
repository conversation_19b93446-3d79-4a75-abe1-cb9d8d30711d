package seoclarity.backend.cronjob;

import java.io.FileWriter;
import java.io.IOException;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.ArrayUtils;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.Extract888HoldingsOnetime" -Dexec.args=""
public class Extract888HoldingsOnetime {
	
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private OwnDomainEntityDAO ownDomainEntityDAO;

	public Extract888HoldingsOnetime() {
		// TODO Auto-generated constructor stub
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}
	
	public static String PROCESS_COMPANY_NAME = "Evoke PLC (888 Holdings)";
	
	public static boolean isMobile = false;
	public static String processDate = "";

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		Extract888HoldingsOnetime Extract888HoldingsOnetime = new Extract888HoldingsOnetime();
		
		if (ArrayUtils.isNotEmpty(args) && args.length >= 2) {
			isMobile = BooleanUtils.toBoolean(args[0]);
			processDate = args[1];
		} else {
			System.out.println("Param incorrect!");
			return;
		}
		
		System.out.println("isMobile:" + args[0] + ", processDate :" + args[1]);
		Extract888HoldingsOnetime.processDomains();
	}
	
	private void processDomains() {
		
		List<OwnDomainEntity> ownDomainList = ownDomainEntityDAO.getDomainListBasedCompanyName(PROCESS_COMPANY_NAME);
		Date month = FormatUtils.toDate(processDate, "yyyy-MM-dd");
		try {
			FileWriter writer = new FileWriter("/home/<USER>/extract/888_" + FormatUtils.formatDate(month, "yyyyMM") + "_" + (isMobile ? "mobile" : "desktop") + ".txt", true);
			
			for(OwnDomainEntity ownDomainEntity : ownDomainList) {
				try {
					System.out.println("==== process on :" + ownDomainEntity.getId());
					process(ownDomainEntity.getId(), writer, month);
				} catch (Exception e) {
					// TODO: handle exception
					e.printStackTrace();
				}
			}
			writer.flush();
			writer.close();
			
		} catch (Exception e) {
			// TODO: handle exception
		}
	}

	
	private void process(Integer ownDomianId, FileWriter writer, Date month) throws IOException {
		
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomianId);
		
		int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        
        String startDate = FormatUtils.formatDate(FormatUtils.getMonthFirstDay(month), "yyyy-MM-dd");
        String endDate = FormatUtils.formatDate(FormatUtils.getMonthLastDay(month), "yyyy-MM-dd");
        
        List<CLRankingDetailEntity> result = clDailyRankingEntityDao.export888ExtractOneTime(ownDomianId, engineId, languageId, isMobile, ownDomainEntity.getDomain(), startDate, endDate);
        
        if (CollectionUtils.isEmpty(result)) {
        	writer.write(ownDomianId + "\t" + ownDomainEntity.getDomain() + "\t-\t-\n");
		} else {
			for(CLRankingDetailEntity CLRankingDetailEntity : result) {
	        	
	        	writer.write(CLRankingDetailEntity.getOwnDomainId() + "\t" + ownDomainEntity.getDomain() 
	        			+ "\t" + CLRankingDetailEntity.getKeywordName() + "\t" + CLRankingDetailEntity.getAvgRank() + "\n");
	        }
		}
        
       
	}
	
	
	
	
	
	
	
}
