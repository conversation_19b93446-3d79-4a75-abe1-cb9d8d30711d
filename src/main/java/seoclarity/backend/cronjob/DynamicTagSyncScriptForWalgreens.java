package seoclarity.backend.cronjob;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.GroupDictDAO;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.GroupTagRelationEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.ResourceAddDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceAddInfoEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.urlclaritydb.CrawlUrlDao;
import seoclarity.backend.entity.ResultContent;
import seoclarity.backend.entity.actonia.GroupDict;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ResourceAddDetailEntity;
import seoclarity.backend.entity.actonia.ResourceAddInfoEntity;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.entity.actonia.ResourceDeleteDetailEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.crawlurl.TargetUrlHtmlDaily;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.cronjob.DynamicTagSyncScriptForWalgreens" -Dexec.args=""
 * <AUTHOR>
 *
 */
public class DynamicTagSyncScriptForWalgreens {
	
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private CrawlUrlDao crawlUrlDao;
	private GroupTagEntityDAO groupTagEntityDAO;
	private GroupTagRelationEntityDAO groupTagRelationEntityDAO;
	private KeywordEntityDAO keywordEntityDAO;
	private ResourceAddInfoEntityDAO resourceAddInfoEntityDAO;
	private ResourceAddDetailEntityDAO resourceAddDetailEntityDAO;
	private GroupDictDAO groupDictDAO;
	private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
	private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	
	public DynamicTagSyncScriptForWalgreens() {
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		crawlUrlDao = SpringBeanFactory.getBean("crawlUrlDao");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		groupTagRelationEntityDAO = SpringBeanFactory.getBean("groupTagRelationEntityDAO");
		resourceAddInfoEntityDAO = SpringBeanFactory.getBean("resourceAddInfoEntityDAO");
		resourceAddDetailEntityDAO = SpringBeanFactory.getBean("resourceAddDetailEntityDAO");
		groupDictDAO = SpringBeanFactory.getBean("groupDictDAO");
		resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
		resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");

	}
	
	private static String processDate = FormatUtils.formatDate(FormatUtils.getYesterday(true), "yyyy-MM-dd");
	private static Integer processOID = 3458;
	private static final int DEFAULF_USER = 214;
	private static final SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
	public static final String MD5_SPLIT = "	";
	public static boolean isCheckAddDetailExists = false;
	
	
	public static void main(String[] args) {
		DynamicTagSyncScriptForWalgreens dynamicTagSyncScriptForTarget = new DynamicTagSyncScriptForWalgreens();
		dynamicTagSyncScriptForTarget.process();
		
	}

	private static Integer pageSize = 10000;
	private static String TAG_PREFIX = " (dt)";
//	private static Map<String, Integer> groupDictNameIdMap = new HashMap<>();
	
	private void process() {
		
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(processOID);
		
		if (ownDomainEntity == null ) {
			System.out.println(" ownDomainEntity is not found!!!");
			return ;
		}
		
		Integer pageNum = 0;
		int engineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        
        String resourceSearchEngine = engineId + "-" + languageId + "-d";
        System.out.println("resourceSearchEngine:" + resourceSearchEngine);
        
        List<TargetUrlHtmlDaily> cacheUrlList = crawlUrlDao.getCustomDataByDate(processOID, processDate);
        
        Map<String, TargetUrlHtmlDaily> cacheMap = new HashMap<>();
		
		cacheMap = cacheUrlList.stream().collect(Collectors.toMap(TargetUrlHtmlDaily::getUrl, Function.identity()));
		
		System.out.println("cacheMap size:" + cacheMap.size());
		
		
        while (true) {
        	//Alps: only query for desktop and national
        	List<CLRankingDetailEntity> resultList = clDailyRankingEntityDao.getManagedKeywordListForTarget(
        			processOID, engineId, languageId, processDate,
    				false, "com.walgreens", pageNum, pageSize);
        	
			if (resultList == null || resultList.size() == 0) {
    			break;
    		}
			
			pageNum ++ ;
			System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize + ", size:" + resultList.size());
			
			/**
			 *	keyword_name
			 *	keyword_rankcheck_id
			 *	url
			 */
			int num = 1;
			
			List<String[]> relations = new ArrayList<>();
			List<String[]> keywordTagParentchildRelList = new ArrayList<>();
			
			for(CLRankingDetailEntity entity : resultList) {
				
				System.out.println("pricessing on :" + num++);
				
				try {
					TargetUrlHtmlDaily targetUrlHtmlDaily = cacheMap.get(entity.getUrl());
					if (targetUrlHtmlDaily != null && StringUtils.isNotBlank(targetUrlHtmlDaily.getCustomData())) {
						
						try {
							ResultContent[] results = new Gson().fromJson(targetUrlHtmlDaily.getCustomData(), ResultContent[].class);
							
							if (results == null || results.length == 0) {
								
								System.out.println("====URL:" + entity.getUrl() + " dont have custom_data!");
								continue;
							}
							
							for(ResultContent resultContent : results) {
								
								List<String> tagList = new ArrayList<String>();
								
								try {
									//Using the example Keyword and  URL, they would like to skip the first two breadcrumbs and start capturing at the 3rd level.
									for (int i = 2; i < resultContent.getLinks().length; i ++) {
										String anchorText = resultContent.getLinks()[i].getAnchor_text();
										anchorText = StringEscapeUtils.unescapeXml(FormatUtils.ascii2native(anchorText));
										tagList.add(anchorText);
									}
								} catch (Exception e) {
									e.printStackTrace();
								}
								System.out.println("url:" + entity.getUrl());
								System.out.println("tagList:" + new Gson().toJson(tagList));
								
						        Integer groupDictId = null;
						        // if length=1 means tag name equals to group name
						        if (tagList.size() == 1) {
						        	
						        	//step 1. create or get group id
						        	String groupDictName = parseTagName(tagList.get(0));
						        	String tagName = "";
						        	if (StringUtils.isNotBlank(groupDictName)) {
						        		tagName = groupDictName = groupDictName + TAG_PREFIX;
										//query if group exist
										GroupDict groupDict = groupDictDAO.findByNameAndOid(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
										if (groupDict != null && groupDict.getId() > 0) {
											groupDictId = groupDict.getId();
											System.out.println("Found Exist Group, id:" + groupDictId + ", groupDictName:" + groupDictName);
										} else {
											//else add new group
											groupDictDAO.insert(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
											groupDict = groupDictDAO.findByNameAndOid(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
											groupDictId = groupDict.getId();
											System.out.println("adding Group, id:" + groupDictId + ", groupDictName:" + groupDictName);
										}
						        	}
						        	
						        	tagName = formatTagName(tagName);
						        	
						        	//step 2. create or get tag and update group id
						        	Integer groupTagId = 0;
						        	GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(processOID, tagName, GroupTagEntity.TAG_TYPE_KEYWORD);
						        	if (groupTagEntity != null && groupTagEntity.getId() > 0) {
						        		groupTagId = groupTagEntity.getId();
						        		if (groupTagEntity.getGroupId() == null || groupTagEntity.getGroupId() == 0) {
						        			System.out.println("Updating tag group relation, tagId:" + groupTagEntity.getId() + ", groupId:" + groupDictId);
											groupTagEntityDAO.updateGroupId(groupTagEntity.getId(), groupDictId);
										}
									} else {
										System.out.println("INS tag , tagName:" + tagName + ", groupId:" + groupDictId);
										groupTagId = groupTagEntityDAO.insertForDynamicTag(processOID, GroupTagEntity.TAG_TYPE_KEYWORD, tagName, tagName, groupDictId);
									}
						        	
						        	if (groupTagId == 0) {
										System.out.println(" @@@ Group Tag Id IS NOT SETTED correctly!!!");
									} else {
										relations.add(new String[] {entity.getKeywordName(), groupTagId + ""});
										System.out.println("Relation:" + entity.getKeywordName() + ", " + groupTagId + "");
									}
						        			
								} else if (tagList.size() > 1) {
									
									//step 1. create or get group id
						        	String groupDictName = parseTagName(tagList.get(0));
						        	if (StringUtils.isNotBlank(groupDictName)) {
						        		groupDictName = groupDictName + TAG_PREFIX;
										//query if group exist
										GroupDict groupDict = groupDictDAO.findByNameAndOid(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
										if (groupDict != null && groupDict.getId() > 0) {
											groupDictId = groupDict.getId();
											System.out.println("Found Exist Group, id:" + groupDictId + ", groupDictName:" + groupDictName);
										} else {
											//else add new group
											groupDictDAO.insert(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
											groupDict = groupDictDAO.findByNameAndOid(groupDictName, processOID, GroupDict.TAG_TYPE_KEYWORD);
											groupDictId = groupDict.getId();
											System.out.println("adding Group, id:" + groupDictId + ", groupDictName:" + groupDictName);
										}
									}
						        	
						        	Integer processTagSize = tagList.size();
						        	if (processTagSize > 3) {
										processTagSize = 3;
									}
									
									for(int i = 0 ; i < processTagSize ; i++) {
										//step 2. create or get tag and update group id
							        	Integer groupTagId = 0;
//							        	String tagName = getTagName(tagList, i + 1) + TAG_PREFIX;	
							        	String tagName = parseTagName(tagList.get(i)) + TAG_PREFIX;
//									        	System.out.println("len " + i + ", tagN:" + tagName);
							        	String friendlyName = parseTagName(tagList.get(i)) + TAG_PREFIX;
							        	
							        	GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(processOID, tagName, GroupTagEntity.TAG_TYPE_KEYWORD);
							        	if (groupTagEntity != null && groupTagEntity.getId() > 0) {
							        		groupTagId = groupTagEntity.getId();
							        		if ((groupTagEntity.getGroupId() == null || groupTagEntity.getGroupId() == 0) && i == 0) {// only update group id for the first level
												groupTagEntityDAO.updateGroupId(groupTagEntity.getId(), groupDictId);
												System.out.println("Update group tag, id:" + groupTagId + ", groupDictId:" + groupDictId + ", tagName:" + tagName);
											}
										} else {
											groupTagId = groupTagEntityDAO.insertForDynamicTag(processOID, GroupTagEntity.TAG_TYPE_KEYWORD, tagName, friendlyName, groupDictId);
											System.out.println("Adding group tag, id:" + groupTagId + ", groupDictId:" + groupDictId + ", tagName:" + tagName);
										}
							        	
							        	if (groupTagId == 0) {
											System.out.println(" @@@ Group Tag Id IS NOT SETTED correctly!!!");
										} else {
											relations.add(new String[] {entity.getKeywordName(), groupTagId + ""});
											//String[childTag, parentTag]
											if (i > 0) {
												String parentTagName = parseTagName(tagList.get(i - 1)) + TAG_PREFIX;
												System.out.println("len " + i + ", tagN:" + tagName + ", parentTagName:" + parentTagName);
									        	keywordTagParentchildRelList.add(new String[] {tagName, parentTagName});
											}
											System.out.println("Relation:" + entity.getKeywordName() + ", " + groupTagId + "");
										}
							        }
								}
							}
						        
						} catch (Exception e) {
							e.printStackTrace();
						}
						
					} else {
						System.out.println("@@@ NOT found in dis_target_url_html,. url :" + entity.getUrl());
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
				
			}
			
			
			
			if (relations.size() > 0) {
				System.out.println("Relation size : " + relations.size());
			}
			
			if (keywordTagParentchildRelList.size() > 0) {
				System.out.println("keywordTagParentchildRelList size : " + keywordTagParentchildRelList.size());
			}
			
			
			List<ResourceAddDetailEntity> addList = new ArrayList<ResourceAddDetailEntity>();
			for (String[] rel : relations) {
				String kw = rel[0];
				String tag = rel[1];
				if (StringUtils.isNotBlank(kw) && StringUtils.isNotBlank(tag)) {
	    			ResourceAddDetailEntity detail = new ResourceAddDetailEntity();
	    			detail.setResourceMain(kw);
	    			detail.setResourceSubordinate(tag);
	    			detail.setOwnDomainId(processOID);
	    			detail.setId(null);
					detail.setStatus(null);
					detail.setProcessDate(null);
					detail.setErrorMessage(null);
					detail.setStatusRank(null);
					detail.setErrorMessageRank(null);
					detail.setResourceSearchengines(resourceSearchEngine);
	    			addList.add(detail);
				}
			}
			if (relations.size() == 0) {
				System.out.println("no details... skip");
				return;
			}
			ResourceAddInfoEntity info =  addResourceAddInfo(processOID, ResourceAddInfoEntity.OPERATION_TYPE_KEYWORD_TAGID);
			resourceAddInfoEntityDAO.updateStatusAfterProcess(info.getId(), ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, new Date());
			addAddDetail(addList, info.getId());
			resourceAddInfoEntityDAO.updateStatusAfterProcess(info.getId(), ResourceAddInfoEntity.STATUS_NEWLY_CREATED, new Date());
			
			System.out.println("===== create queue task for tag parent child rel");
			int actionType = ResourceBatchInfoEntity.TYPE_ADD;
	        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_TAG_PARENT_CHILD_REL ;
        	try {
                ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
                resourceBatchInfoEntity.setActionType(actionType);
                resourceBatchInfoEntity.setOwnDomainId(processOID);
                resourceBatchInfoEntity.setOperationType(operationType);
                resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
                resourceBatchInfoEntity.setUserId(214);
                resourceBatchInfoEntity.setCreateDate(new Date());
                resourceBatchInfoEntity.setCustomFlag(2);
                resourceBatchInfoEntity.setStatusRankcheck(0);
                long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
                
                resourceBatchInfoEntityDAO.updateStatusAfterProcess(id, ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, new Date());
                
                List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
                for (String[] rel : keywordTagParentchildRelList) {
                	
                	if (StringUtils.isBlank(rel[1]) || StringUtils.isBlank(rel[0])) {
						System.out.println("==== Add Queue detail Err, Tagid is not set correctly ");
                		continue;
					}
                	
                    ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
                    rbd.setInfoId(id);
                    rbd.setActionType(actionType);
                    rbd.setOwnDomainId(processOID);
                    rbd.setResourceMain(rel[0]);
                    rbd.setResourceSubordinate(rel[1]);
                    rbd.setResourceCategory(2);
                    rbd.setResourceMd5(Md5Util.Md5(rel[0] + rel[1] + FormatUtils.formatDate(new Date(), "yyyy-MM-dd") + ""));
                    rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
                    resourceBatchDetailEntityList.add(rbd);
                }
                resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);
                System.out.println("Qbs====>create new info, id: " + id + ", resourceBatchDetailEntityList size: " + resourceBatchDetailEntityList.size());
            
                resourceBatchInfoEntityDAO.updateStatusAfterProcess(id, ResourceAddInfoEntity.STATUS_NEWLY_CREATED, new Date());
        	} catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
	}
	private String getTagName(String[] tagList, Integer num) {
		
		if (num == 1) {
			return formatTagName(parseTagName(tagList[0]));
		} else if (num == 2) {
			return formatTagName(parseTagName(tagList[0]) + ">" + parseTagName(tagList[1]));
		} else if (num == 3) {
			return formatTagName(parseTagName(tagList[0]) + ">" + parseTagName(tagList[1]) + ">" + parseTagName(tagList[2]));
		}
		
		System.out.println("@@@ tag list is empty!!");
		return "";
	}
	
	//https://www.wrike.com/open.htm?id=634813339
	public boolean isEffectTagName(String content) {
		
		if (StringUtils.isBlank(content) 
				|| !StringUtils.startsWith(content, "Target") 
				|| StringUtils.contains(content, "$")
				|| StringUtils.contains(content, "%")
				|| StringUtils.contains(content, "Try removing some ")) {
			
			return false;
		}
		return true;
	}
	
//	public Integer deleteTag(int oid, List<Integer> tags) {
//		List<ResourceDeleteDetailEntity> needProcessList = new ArrayList<ResourceDeleteDetailEntity>();
//		for (Integer tagId : tags) {
//			ResourceDeleteDetailEntity deleteDetailEntity = null;
//			if (tagId != 0) {
//				deleteDetailEntity = new ResourceDeleteDetailEntity();
//				deleteDetailEntity.setOwnDomainId(oid);
//				deleteDetailEntity.setResourceType(ResourceDeleteDetailEntity.RESOURCE_TYPE_TAG_BY_ID);
//				deleteDetailEntity.setResourceId(tagId);
//				
//				needProcessList.add(deleteDetailEntity);
//			} else {
//				System.out.println("Bad resource, tagId:" + tagId);
//			}
//		}
//		return this.batchAddDelete(needProcessList, oid, ResourceDeleteDetailEntity.RESOURCE_TYPE_TAG_BY_ID);
//	}
	
//	public Integer batchAddDelete(List<ResourceDeleteDetailEntity> delList, int oid, int resourceType) {
//		if (delList.size() > 0) {
//			try {
//				// add deleteInfo
//				ResourceDeleteInfoEntity deleteInfoEntity = addDeleteInfo(oid);
//				resourceDeleteInfoEntityDAO.updateRankcheckStatusAfterProcess(deleteInfoEntity.getId(), ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, null);
//				resourceDeleteInfoEntityDAO.updateRankcheckStatusAfterProcess(deleteInfoEntity.getId(), ResourceDeleteInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR, null);
//				// add details
//				addDeleteDetail(delList, deleteInfoEntity.getId());
//				resourceDeleteInfoEntityDAO.updateRankcheckStatusAfterProcess(deleteInfoEntity.getId(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, null);
//				resourceDeleteInfoEntityDAO.updateRankcheckStatusAfterProcess(deleteInfoEntity.getId(), ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED, null);
//				
//				return deleteInfoEntity.getId();
//			
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//		return null;
//	}
	
//	protected void addDeleteDetail(List<ResourceDeleteDetailEntity> deleteDetailList, int deleteInfoId) {
//		int createDate = Integer.valueOf(sf.format(new Date()));
//		int count = 0;
//		int exists = 0;
//		for (ResourceDeleteDetailEntity deleteDetailEntity : deleteDetailList) {
//			deleteDetailEntity.setDeleteInfoId(deleteInfoId);
//			deleteDetailEntity.setCreateDate(createDate);
//			try {
//				createMD5ForDeleteDetail(deleteDetailEntity);
//				try {
//					resourceDeleteDetailEntityDAO.insert(deleteDetailEntity);
//				} catch (Exception e) {
//					exists++;
//				}
//				count++;
//			} catch (Exception e){
//				System.out.println("insert error, deleteInfo id:" + deleteInfoId);
//				e.printStackTrace();
//			}
//		}
//		System.out.println("Create delete details, deleteInfoId:" + deleteInfoId + ", insert count:" + count + ", exists:" + exists);
//	}
	
	private void createMD5ForDeleteDetail(ResourceDeleteDetailEntity detailEntity) throws Exception{
		String md5 = calculateMd5(String.valueOf(detailEntity.getResourceId()), detailEntity.getResourceSubId() + "");
		if (!StringUtils.isEmpty(md5)) {
			detailEntity.setResource_md5(md5);
		} else {
			throw new Exception("MD5 not created...");
		}
	}
	
	private static final String TAG_HTML_SYMBOL_FILTER = "(\\s*<+\\s*|\\s*>+\\s*)";
	//Leo - https://www.wrike.com/open.htm?id=84522790
	public static final String formatTagName(String tagName) {
		// TODO not deployed after Leo confirmed
		Pattern p = Pattern.compile(TAG_HTML_SYMBOL_FILTER);
		Matcher m = p.matcher(tagName);
		StringBuffer sb = new StringBuffer();
		while (m.find()) {
	
			String matchStr = m.group();
			if (!StringUtils.startsWith(matchStr, " ")) {
				matchStr = " " + matchStr;
			}
			if (!StringUtils.endsWith(matchStr, " ")) {
				matchStr = matchStr + " ";
			}
	
			m.appendReplacement(sb, matchStr);
		}
		m.appendTail(sb);
		return sb.toString();
	//	return tagName;
	}
	
//	protected ResourceDeleteInfoEntity addDeleteInfo(int ownDomainId) {
//		ResourceDeleteInfoEntity deleteInfoEntity = new ResourceDeleteInfoEntity();
//		deleteInfoEntity.setEnabled(ResourceDeleteInfoEntity.ENABLED);
//		deleteInfoEntity.setOwnDomainId(ownDomainId);
//		deleteInfoEntity.setUserId(DEFAULF_USER);
//		deleteInfoEntity.setCreateDate(new Date());
//		deleteInfoEntity.setStatus(ResourceDeleteInfoEntity.STATUS_NEWLY_CREATED);
//		deleteInfoEntity.setStatusRankcheck(0);
//		int id = resourceDeleteInfoEntityDAO.insert(deleteInfoEntity);
//		deleteInfoEntity.setId(id);
//		System.out.println("Create deleteInfo, id:" + id + ", OID:" + ownDomainId);
//		return deleteInfoEntity;
//	}
	
//	private Integer deleteKeywordTagRelation(Integer ownDomainId, Integer userId, List<Integer> tagIds) {
//		
//		Integer infoId = saveResourceDeleteInfo(ownDomainId, userId);
//		
//		System.out.println("Create queue base for truncate keyword from tag, task infoID : " + infoId);
//		
//		Long maxKeywordTagRelationId = groupTagRelationEntityDAO.getMaxKeywordTagRelationId(ownDomainId, GroupTagEntity.TAG_TYPE_KEYWORD);
//		for (Integer gtid : tagIds) {
//			saveResourceDeleteDetail(infoId, ownDomainId, ResourceDeleteDetailEntity.RESOURCE_TYPE_DISASSOCIATE_ALL_KEYWORD_FROM_TAG, 
//					maxKeywordTagRelationId, gtid);
//		}
//		return infoId;
//	}
	
	
//	private void saveResourceDeleteDetail(int infoId, int ownDomainId, int resourceType, Long resourceId, Number resourceSubId) {
//		try {
//			resourceDeleteDetailEntityDAO.insert(infoId, ownDomainId, resourceType, resourceId, resourceSubId, null);
//		} catch (DuplicateKeyException ex) {
//			ex.printStackTrace();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
	
//	private Integer saveResourceDeleteInfo(int ownDomainId, int userId) {
//		Integer infoId = resourceDeleteInfoEntityDAO.insert(ownDomainId, userId);
//		return infoId;
//	}
	
	protected void addAddDetail(List<ResourceAddDetailEntity> detailList, int infoId) {
		int createDate = Integer.valueOf(sf.format(new Date()));
		int count = 0;
		for (ResourceAddDetailEntity detail : detailList) {
			detail.setResourceInfoId(infoId);
			detail.setCreateDate(createDate);
			if (detail.getUserId() == null) {
				detail.setUserId(DEFAULF_USER);
			}
			try {
				if (StringUtils.isEmpty(detail.getResource_md5())) {
					String md5 = calculateMd5(String.valueOf(detail.getResourceMain()), detail.getResourceSubordinate() + "");
					detail.setResource_md5(md5);
				}
				if (isCheckAddDetailExists && isExistsDetail(detail.getOwnDomainId(), detail.getResource_md5(), detail.getCreateDate())) {
					System.out.println("Exists detail, infoId:" + detail.getResourceInfoId() + ", OID:" + detail.getOwnDomainId()
					+ ", resourceMain:" + detail.getResourceMain() 
					+ " resourceSub:" + detail.getResourceSubordinate() + ", OperationType" + detail.getOperationType() + ", md5:" + detail.getResource_md5() 
					+ ", createDate:" + detail.getCreateDate());
				} else {
					resourceAddDetailEntityDAO.insertForSpecialSE(detail);
				}
				count++;
			} catch (Exception e) {
				System.out.println("insert error, resourceAddInfoId:" + infoId + ", ERROR:" + e.getMessage());
//				e.printStackTrace();
			}
		}
		System.out.println("Create add details, infoId:" + infoId + ", insert count:" + count);
	}
	
	public static String calculateMd5(String valueMain, String valueSubordinate) {
		String input = valueMain;
		if (StringUtils.isNotBlank(valueSubordinate)) {
			input = input + MD5_SPLIT + valueSubordinate;
		}
		return FormatUtils.hexDigest(input.getBytes());
	}
	
	protected boolean isExistsDetail(int oid, String resourceMD5, int createDate) {
		ResourceAddDetailEntity detail = resourceAddDetailEntityDAO.checkExists(oid, resourceMD5, createDate);
		if (detail == null) {
			return false;
		}
		return true;
	}
	
	 public KeywordEntity checkKeyword(String name, int ownDomainId) throws Exception {
        KeywordEntity keywordEntity = null;
        String keywordName = formatKeywordForInsert(name.toLowerCase());
        try {
            if (!StringUtils.isEmpty(keywordName)) {
                keywordName = URLEncoder.encode(keywordName, "UTF-8");
            }
            keywordName = CommonDataService.formatEncodeKeywordForInsert(keywordName);
            if (!StringUtils.isEmpty(keywordName)) {
                keywordName = keywordName.toLowerCase();
                keywordName = StringUtils.removeStart(keywordName, "%EF%BB%BF".toLowerCase());
                keywordEntity = getKeywordByKeywordName(keywordName, ownDomainId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return keywordEntity;
    }
 
	public static String formatKeywordForInsert(String keywordStr) {
		if (StringUtils.isBlank(keywordStr)) {
			return null;
		}
		
		keywordStr = StringUtils.chomp(keywordStr);
		keywordStr = StringUtils.chomp(keywordStr);
		
		// remove invisible characters
		keywordStr = keywordStr.replaceAll("\\s", " ");
		
		// remove multi-space characters to one space
		keywordStr = keywordStr.replaceAll(" +", " ");
		
		// remove START and END space
		keywordStr = StringUtils.stripToEmpty(keywordStr);
		
		return keywordStr;
	}
 
    public KeywordEntity getKeywordByKeywordName(String keywordName, int ownDomainId) {
        KeywordEntity keywordEntity = keywordEntityDAO.findFirstByKeywordNameAndRanked(keywordName, ownDomainId);
        if (keywordEntity == null) {
            keywordEntity = keywordEntityDAO.findFirstByKeywordName(keywordName, ownDomainId);
        }
        return keywordEntity;
    }

    private String parseTagName(String tagName) {
    	if (StringUtils.contains(tagName, '\u200e')) {
    		String[] tagNameSubArray = StringUtils.split(tagName, '\u200e');
    		for(String tag : tagNameSubArray) {
	    	   if (!StringUtils.contains(tag, '\u200e')) {
	    		   tagName = tag;
	   				break;
	   			}
    		}
		}
    	
    	tagName = StringUtils.trim(tagName);
    	tagName = StringUtils.replace(tagName, "\t", "");
    	
    	if (StringUtils.indexOf(tagName, ":") != -1) {
    		try {
    			System.out.println("Tag contains \":\" ");
        		tagName = StringUtils.trim(StringUtils.substring(tagName, StringUtils.lastIndexOf(tagName, ":") + 1, tagName.length()));
        		System.out.println(" ====> " + tagName);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
    	return tagName;
    }
	
	public ResourceAddInfoEntity addResourceAddInfo(int ownDomainId, int operationType) {
		ResourceAddInfoEntity info = new ResourceAddInfoEntity();
		info.setOperationType(operationType);
		info.setOwnDomainId(ownDomainId);
		info.setUserId(DEFAULF_USER);
		info.setCreateDate(new Date());
		info.setStatus(ResourceAddInfoEntity.STATUS_NEWLY_CREATED);
		int id = resourceAddInfoEntityDAO.insert(info);
		info.setId(id);
		System.out.println("Create addInfo, id:" + id + ", OID:" + ownDomainId);
		return info;
	}
	
	
}