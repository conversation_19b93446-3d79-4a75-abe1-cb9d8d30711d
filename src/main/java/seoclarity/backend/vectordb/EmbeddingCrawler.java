package seoclarity.backend.vectordb;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.google.gson.Gson;

import ru.yandex.clickhouse.ClickHouseArray;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDoc1Entity;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;


//mvn exec:java -Dexec.mainClass="seoclarity.backend.vectordb.EmbeddingCrawler" -Dexec.args="11813 9946428 1 1000"
public class EmbeddingCrawler {

	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;
	
	public EmbeddingCrawler() {
		// TODO Auto-generated constructor stub
		disSiteCrawlDoc1Dao = SpringBeanFactory.getBean("disSiteCrawlDoc1Dao");
	}
	
	private static String API_OPENAI_EMBEDDING = "https://api.openai.com/v1/embeddings";
	private static Map<String, String> OPENAI_DEFAULT_HEADER = new HashMap<String, String>();
	
    private static String crawlingFolderPath = "/home/<USER>/embedding/crawling";
    private static String doneFolderPath = "/home/<USER>/embedding/needUpload";
    public static final char FILE_SPLIT = '\t';
    public static final String ENCODING = "UTF-8";
    
    private static Gson gson = new Gson();
	static {
		OPENAI_DEFAULT_HEADER.put("Content-Type", "application/json");
		OPENAI_DEFAULT_HEADER.put("Authorization", "Bearer ***************************************************");
	}
	
	public static final String[] DB_COLUMNS = new String[]{
			"url_murmur_hash", "crawl_request_id", "crawl_request_id_mod", "crawl_request_date", "domain_id", "url", "title", 
			"meta", "h1", "h2", "custom_data_1", "custom_data_2", "custom_data_3", "custom_data_4", 
			"custom_data_5", "title_embedding", "meta_embedding", "h1_embedding", "h2_embedding", 
			"custom_data_1_embedding", "custom_data_2_embedding", "custom_data_3_embedding", 
			"custom_data_4_embedding", "custom_data_5_embedding"
    };
	
	private static String OPENAI_MODEL = "text-embedding-ada-002";
	
	public static void main(String[] args) {
		
//		if (args != null && args.length >= 4) {
//			
//			Integer ownDomainId = NumberUtils.toInt(args[0]);
//			Integer crawlRequestId = NumberUtils.toInt(args[1]);
//			Integer pageNumber = NumberUtils.toInt(args[2]);
//			Integer pageSize = NumberUtils.toInt(args[3]);
//			
//			EmbeddingCrawler embeddingCrawler = new EmbeddingCrawler();
//			embeddingCrawler.processByPage(ownDomainId, crawlRequestId, pageSize, pageNumber);
//		}
		
//		for(int i = 1; i <= 37; i ++) {
//			EmbeddingCrawler embeddingCrawler = new EmbeddingCrawler();
//			embeddingCrawler.processByPage(11813, 9946428, 1000, i);
//		}
		
		List<Integer> intList = new ArrayList<>();
		intList.add(6);
		intList.add(10);
		intList.add(18);
		intList.add(19);
		intList.add(24);
		intList.add(25);
		intList.add(28);
		intList.add(29);
		
		for(Integer i : intList) {
			EmbeddingCrawler embeddingCrawler = new EmbeddingCrawler();
			embeddingCrawler.processByPage(11813, 9946428, 1000, i);
		}
	}

	private void processByPage(Integer ownDomainId, Integer crawlRequestId, Integer pageSize, Integer pageNum) {
		
		try {
			File crawlingFolder = new File(crawlingFolderPath);
	        if (crawlingFolder == null || !crawlingFolder.exists() || !crawlingFolder.isDirectory()) {

	            System.out.println("folder crawlering is not exist, creating now!! ");
	            crawlingFolder.mkdirs();
	        }
	        String prefix = "embedding_" + ownDomainId + "_" + crawlRequestId + "_";
			File tempfile = File.createTempFile(prefix, ".processing", crawlingFolder);
			String targetFileName = StringUtils.replace(tempfile.getName(), ".processing", ".txt");
	        
            File doneFolder = new File(doneFolderPath);
            File targetFile = new File(doneFolderPath + "/" + targetFileName);
            if (doneFolder == null || !doneFolder.isDirectory()) {
                System.out.println("Target folder is not exist!!! folder:" + doneFolder.getAbsolutePath());
                doneFolder.mkdirs();
            }
			List<String> outputLines = new ArrayList<>();
			
			List<DisSiteCrawlDoc1Entity> siteCrawlList = disSiteCrawlDoc1Dao.getByDomainIdAndCrawlRequestId(
					ownDomainId, crawlRequestId, pageSize, pageNum);
			
			System.out.println("siteCrawlList size:" + siteCrawlList.size()); 
			int num = 0;
			for(DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity : siteCrawlList) {
				num++;
				if (num % 10 == 0) {
					System.out.println("processing on :" + num + ", url: " + disSiteCrawlDoc1Entity.getUrl());
				}
				String result = processByUrl(disSiteCrawlDoc1Entity);
				if (StringUtils.isNotBlank(result)) {
					outputLines.add(result);
				}
			}
			
			if (outputLines.size() >= 0) {
				output(tempfile, outputLines);
				FileUtils.moveFile(tempfile, targetFile);
				outputLines.clear();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private String processByUrl(DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity) throws SQLException {
		
		if (disSiteCrawlDoc1Entity == null) {
			return null;
		}
		
		String h1 = getNotNullStringFromArray(disSiteCrawlDoc1Entity.getH1());
		String h2 = getNotNullStringFromArray(disSiteCrawlDoc1Entity.getH2());
		String title = disSiteCrawlDoc1Entity.getTitle();
		String meta = disSiteCrawlDoc1Entity.getDescription();
		ClickHouseArray customDataArray = disSiteCrawlDoc1Entity.getContent();
		String customData1 = "";
		String customData2 = "";
		String customData3 = "";
		String customData4 = "";
		String customData5 = "";
		
		List<Integer> keyList = new ArrayList<>();
		List<String> valueList = new ArrayList<>();
		if (StringUtils.isNotBlank(h1)) {
			keyList.add(17);//row number
			valueList.add(h1);
		}
		
		if (StringUtils.isNotBlank(h2)) {
			keyList.add(18);
			valueList.add(h2);
		}
		
		if (StringUtils.isNotBlank(title)) {
			keyList.add(15);
			valueList.add(title);
		}
		
		if (StringUtils.isNotBlank(meta)) {
			keyList.add(16);
			valueList.add(meta);
		}
		
		int customNum = 1;
		for(String customData : (String[])customDataArray.getArray()) {
			if (StringUtils.isNotBlank(customData) && !StringUtils.equals(customData, "[]")) {
				String customDataValue = removeHtmlTag(customData);
				keyList.add(18 + customNum);// customData from row 20-24
				valueList.add(customDataValue);
				if (customNum == 1) {
					customData1 = customDataValue;
				} else if (customNum == 2) {
					customData2 = customDataValue;
				} else if (customNum == 3) {
					customData3 = customDataValue;
				} else if (customNum == 4) {
					customData4 = customDataValue;
				} else if (customNum == 5) {
					customData5 = customDataValue;
				}
			}
			
			customNum ++;
			if (customNum >= 5) {
				break;
			}
		}
		
		ParamVO paramVO = new ParamVO();
		
		paramVO.setInput(valueList.toArray(new String[valueList.size()]));
		paramVO.setModel(OPENAI_MODEL);
		
//		String dataJson = \
		
		if (keyList.size() == 0) {
			System.out.println("========= key list is empty!");
			System.out.println("Url:" + disSiteCrawlDoc1Entity.getUrl());
			return "";
		}
		
		Map<String, String> response = HttpRequestUtils.queryWebServiceFunctionPostMap(API_OPENAI_EMBEDDING, new Gson().toJson(paramVO), null, OPENAI_DEFAULT_HEADER);
		if (response!= null) {
			String responseText = response.get("response");
			
			EmbeddingResponseVO embeddingResponseVO = new Gson().fromJson(responseText, EmbeddingResponseVO.class);
			String[] row = new String[DB_COLUMNS.length];
			row[0] = disSiteCrawlDoc1Entity.getUrlMurmurHash() + "";
			row[1] = disSiteCrawlDoc1Entity.getCrawlRequestId() + "";
			row[2] = (disSiteCrawlDoc1Entity.getCrawlRequestId() % 1000) + "";
			row[3] = disSiteCrawlDoc1Entity.getCrawlRequestDate() + "";
			row[4] = disSiteCrawlDoc1Entity.getDomainId() + "";
			row[5] = disSiteCrawlDoc1Entity.getUrl() + "";
			
			row[6] = title;
			row[7] = meta;
			row[8] = h1;
			row[9] = h2;
			row[10] = customData1;
			row[11] = customData2;
			row[12] = customData3;
			row[13] = customData4;
			row[14] = customData5;
			//------------------------------------------------
			//this may need to update when row column number changes
			//------------------------------------------------
			
			if (embeddingResponseVO == null || embeddingResponseVO.getData() == null || keyList.size() != embeddingResponseVO.getData().length) {
				System.out.println("========== key and value size can not match");
				System.out.println(gson.toJson(keyList));
				System.out.println(gson.toJson(valueList));
//				return "";
			}
			for(int i = 0; i < keyList.size(); i ++) {
				try {
					row[keyList.get(i)] = gson.toJson(embeddingResponseVO.getData()[i].getEmbedding());
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			//setup empty values
			for(int i = 15; i < 24; i ++) {
				if (!keyList.contains(i)) {
					row[i] = "[0]";
				}
			}
			
			return StringUtils.join(row, FILE_SPLIT);
		} else {
			System.out.println("Resp is empty!");
			return null;
		}
		
	}
	
	private static void output(File file, List<String> outLines) throws Exception{
        FileUtils.writeLines(file, "utf-8", outLines, true);
    }
	
	private String removeHtmlTag(String inputStr) {
		String outputStr = "";
		try {
			outputStr = StringUtils.trim(StringUtils.replace(inputStr, "\\&[a-zA-Z]{1,10};", "").replaceAll("<[^>]*>", "").replaceAll("[(/>)<]", ""));
			outputStr = StringUtils.removeEnd(StringUtils.removeStart(StringUtils.removeEnd(StringUtils.removeStart(outputStr, "["), "]"), "\""), "\"");
			return outputStr;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return inputStr;
	}
	
	private String getNotNullStringFromArray(ClickHouseArray array) throws SQLException {
		if (array == null) {
			return null;
		}
		
		for(String str : (String[])array.getArray()) {
			if (StringUtils.isNotBlank(str)) {
				return str;
			}
		}
		return null;
	}
	
}
