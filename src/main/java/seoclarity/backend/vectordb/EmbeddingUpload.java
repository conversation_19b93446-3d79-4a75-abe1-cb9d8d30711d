package seoclarity.backend.vectordb;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360LwebVector05DAO;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;

/**
 * 
 * <AUTHOR>
 * @date 2021-05-13
 * seoclarity.backend.upload.GaUploadV4
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.vectordb.EmbeddingUpload" -Dexec.args=""
 * upload to clarityDB
 */
public class EmbeddingUpload {
	private static int version = 1;
	private static int specialVersion = 0;
	
	private static Integer startHour;
	private static Date processDate = new Date();
	private static boolean isBkProcess = false;
	
	private static final String databaseName = "vector_db";
	private static final String finalTableName = "dis_vector_table";
	

	public static final Log logger = LogFactory.getLog(EmbeddingUpload.class);

	private static String storeDoneFilePath = "/home/<USER>/embedding/needUpload/";
	private static String storeTempFilePath = "/home/<USER>/embedding/crawling/";
	private static String storeBKFilePath = "/home/<USER>/embedding/backUpFolder/";
	private static String storeDuplicateFilePath = "/home/<USER>/embedding/duplicate/";

	private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

	private Clarity360LwebVector05DAO clarity360LwebVector05DAO;
	
	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	
	private static List<String> processingFileList = new ArrayList<String>();
	
	private static Integer logId;

	public EmbeddingUpload() {
		
		clarity360LwebVector05DAO = SpringBeanFactory.getBean("clarity360LwebVector05DAO");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		
	}

	public static void main(String args[]) {
		
		if (args.length > 0 && StringUtils.isNotBlank(args[0])) {
			try {
				processDate = FormatUtils.toDate(args[0], "yyyyMMddHH");
			} catch (Exception e) {
				System.out.println("Date format failed, format should be yyyyMMddHH, exit!!!! : " + args[0]);
				return;
			}
			isBkProcess = true;
		}
		
		if (args.length > 1) {
			storeDoneFilePath = args[1];
		}
		
		storeTempFilePath = storeTempFilePath + "/" + FormatUtils.formatDate(processDate, "yyyyMMddHH") + "/";
		
		threadPool.init();
		CommonUtils.initThreads(10);
		EmbeddingUpload embeddingUpload = new EmbeddingUpload();
		System.out.println("--- specialVersion:" + specialVersion + ", use version:" + version + ", storeDoneFilePath:" + storeDoneFilePath + ", 30s to continue...");
		
		Calendar cal = Calendar.getInstance();
		cal.setTime(processDate);
		startHour = cal.get(Calendar.HOUR_OF_DAY);
		
		if (embeddingUpload.checkIsProcessingUpload()) {
			System.out.println("There found more than one processing still not finished, exit !!!");
			System.exit(-1);
		}
		
		File doneFolder = new File(storeDoneFilePath);
		if (doneFolder != null && doneFolder.isDirectory()) {
			boolean hasFilesNeedProcess = false;
			
			for (File file : doneFolder.listFiles()) {
				if (StringUtils.startsWith(file.getName(), "embedding_") && file.isFile()) {
					
					System.out.println("Found file " + file.getName() + ", start to process !");
					hasFilesNeedProcess = true;
					break;
				}
			}
			
			if (!hasFilesNeedProcess) {
				
				embeddingUpload.insertEmptyLog();
				System.out.println("There do not have any files need to be process, skiped+++");
				System.exit(-1);
			}

		}
		
		
		try {
			Thread.sleep(5000);
			embeddingUpload.process();
		} catch (Exception e) {
			e.printStackTrace();
		}
		threadPool.destroy();
	}
	
	//if no files need to be upload, then insert one record with final status = success(2) and upload status = success(2)
	private void insertEmptyLog(){
		
		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
		
		clarityDBUploadLogEntity.setTmpTableName("");
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName(finalTableName);
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS);
		clarityDBUploadLogEntity.setTmpTableUploadDailyRows(0);
		clarityDBUploadLogEntity.setFinalTableUploadRows(0);
		clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_EMBEDDING_OPENAI_VECTORDB);
		
		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadEndTime(new Date());
		clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}
	
	private boolean checkIsProcessingUpload(){
		
		List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(ClarityDBUploadLogEntity.UPLOAD_TYPE_EMBEDDING_OPENAI_VECTORDB);
		if (CollectionUtils.isNotEmpty(list)) {
			return true;
		}
		return false;
	}
	
	private void process() {
		
		long startTime = System.currentTimeMillis();
		
		File duplicateFolder = new File(storeDuplicateFilePath);
		if (duplicateFolder == null || !duplicateFolder.isDirectory()) {
			duplicateFolder.mkdir();
		}
		
		moveFilesToProcessingFolder();
		
			
			ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
			
			clarityDBUploadLogEntity.setTmpTableName(finalTableName);
			clarityDBUploadLogEntity.setDatabaseName(databaseName);
			clarityDBUploadLogEntity.setFinalTableName(finalTableName);
			clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
			clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_EMBEDDING_OPENAI_VECTORDB);
			try {
				clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			
			clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
			clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);
			
			logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
			System.out.println("===add log for ga_upload_log, id:" + logId);
			
			//process normal
			File doneFolder = new File(storeDoneFilePath);
			if (doneFolder == null || !doneFolder.isDirectory()) {
				System.out.println("Folder is not exist :" + doneFolder);
				long endTime = System.currentTimeMillis();
				int elapsedSeconds = (int) (endTime - startTime);

				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0, elapsedSeconds, logId);
				return;
			}
			try {
				
				File tmpFile;
				for (String gaFileFullPath : processingFileList) {
					tmpFile = new File(gaFileFullPath);
					
					if (tmpFile == null || !tmpFile.isFile()) {
						System.out.println("File is not exist or is a folder : " + gaFileFullPath);
						continue;
					}
					
					//String prefix = "ga_" + ownDomainId + "_" + gaInfoVO.getTrafficDate() + "_"  + gaInfoVO.getVersion() + "_" + (isBackProcess ? "bk_" : startHour + "_");
					
					System.out.println("=====processing file:" + tmpFile.getAbsolutePath());
					processFile(tmpFile);
				}
				
				long endTime = System.currentTimeMillis();
				int elapsedSeconds = (int) (endTime - startTime);
				
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0, elapsedSeconds, logId);
				
			} catch (Exception e) {
				System.out.println("Updated status in log table to " + ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE);
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
				System.out.println("Insert table failed, table: " + finalTableName + ", folder: "  + storeDoneFilePath);
				
				moveFilesBackProcessingFolder();
				e.printStackTrace();
				return ;
			}
			
			
			try {
				moveFileAndZip();
			} catch (Exception e) {
				clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS_MOVE_FILE_FAIL, logId);
			} finally {
				deleteTempProcessingFolder();
			}
			
			
		
	}
	
	private void moveFilesToProcessingFolder(){
		
		File targetFolder = new File(storeTempFilePath);
		if (targetFolder == null || !targetFolder.isDirectory()) {
			System.out.println("Folder is not exist, mkdir: " + storeTempFilePath);
			targetFolder.mkdir();
		}
		
		File doneFolder = new File(storeDoneFilePath);
		
		System.out.println("====moving files to processing folder, total file:" + doneFolder.length() + "!! from " + storeDoneFilePath + " to " + storeTempFilePath);
		
		for (File gaFile : doneFolder.listFiles()) {
			try {
				if (StringUtils.startsWith(gaFile.getName(), "embedding_") && gaFile.isFile()) {
					
					//String prefix = "ga_v4_" + ownDomainId + "_" + gaInfoVO.getTrafficDate() + "_"  + gaInfoVO.getVersion() + "_" + (isBackProcess ? "bk_" : startHour + "_");
					FileUtils.moveFile(gaFile, new File(storeTempFilePath + "/" + gaFile.getName()));
					processingFileList.add(storeTempFilePath + "/" + gaFile.getName());
				}
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
	}
	
	private void moveFilesBackProcessingFolder(){
		
		File processingFolder = new File(storeTempFilePath);
		
		System.out.println("====moving files back from processing folder, total file:" + processingFolder.length() + "!! from " + storeTempFilePath + " to " + storeDoneFilePath);
		
		for (File gaFile : processingFolder.listFiles()) {
			try {
				FileUtils.moveFile(gaFile, new File(storeDoneFilePath + "/" + gaFile.getName()));
				processingFileList.add(storeDoneFilePath + "/" + gaFile.getName());
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		deleteTempProcessingFolder();
	}
	
	private void moveFileAndZip(){
		
		File bkFolder = new File(storeBKFilePath);
		if (bkFolder == null || !bkFolder.isDirectory()) {
			System.out.println("BK folder is not exist, mkdir: " + storeBKFilePath);
			bkFolder.mkdir();
		}
		
		File tmpFile;
		File targetFile;
		
		for (String fileFullPath : processingFileList) {
			// zip file
			try {
				tmpFile = new File(fileFullPath);
				targetFile = new File(bkFolder + "/" + tmpFile.getName());
				
				FileUtils.moveFile(tmpFile, targetFile);
				
				System.out.println("zipped file : " + targetFile.getAbsolutePath());
	            GZipUtil.zipFile(targetFile.getAbsolutePath());
	            
	            targetFile.delete();
	            System.out.println("delete file : [" + fileFullPath + "]");
	        } catch (Exception e) {
	        	System.out.println("delete file failed. file: [" + fileFullPath + "]");
	            e.printStackTrace();
	        }
			
		}
		
		deleteTempProcessingFolder();
	}
	
	private void deleteTempProcessingFolder(){
		//deleted the temp processing folder
		File tempFolder = new File(storeTempFilePath);
		
		if (tempFolder != null && tempFolder.isDirectory()) {
			tempFolder.delete();
		}
	}

	
//	private void postFile(String tableName, File tmpFile) throws Exception{
//		if (tmpFile != null && tmpFile.exists()) {
//			
//			String sql = gaClarityDBEntityDAO.getInsertSqlV4(tmpSummaryTable);
//			
//			String fileName = tmpFile.getName();
//			String[] arrays = StringUtils.split(fileName, "_");
//			
//			Integer ownDomainId = NumberUtils.toInt(arrays[2]);
//			Date trafficDate = FormatUtils.toDate(arrays[3], "yyyyMMdd");
//			Integer version = NumberUtils.toInt(arrays[4]);
//			
//			//TODO query from clarityDB and find if there had data for that day.
//			String checkExitSql = " select count() from dis_analytics_ga4_raw where log_date = '" + FormatUtils.formatDate(trafficDate, "yyyy-MM-dd") + 
//					"' and versoin = " + version + " and domain_id = " + ownDomainId;
//			
//			System.out.println("SQL" + checkExitSql);
//			
//			//=======================================  clusterA ========================================
//			String response = ClarityDBUtils.executeQueryCSV(ClarityDBConstants.NEW_GAHOST_04, checkExitSql);
//			Integer count = 0;
//			
//			if (StringUtils.isNotBlank(response)) {
//				try {
//					count = NumberUtils.toInt(StringUtils.split(response, "\n")[0]);
//					System.out.println("OID:" + ownDomainId + ", GA API response: count:" + count);
//					
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
//			}
//			
//			if (count > 0) {
//				System.out.println("==== data exist in clarityDB : OID:" + ownDomainId + ", trafficDate:" + trafficDate + 
//						", version:" + version + ", moving file to " + storeDuplicateFilePath + "/" + tmpFile.getName());
//				
//				FileUtils.moveFile(tmpFile, new File(storeDuplicateFilePath + "/" + tmpFile.getName()));
//				
//			} else {
//				String str = ClarityDBUtils.postBatchInsert(ClarityDBConstants.NEW_GAHOST_04, sql, tmpFile.getAbsolutePath());
//				System.out.println(str);
//				
//				if (StringUtils.contains(str, "DB::Exception")) {
//					System.out.println(sql);
//					throw new Exception(str);
//				}
//			}
//			
//		} else {
//			System.out.println("File not exists:" + tmpFile.getName());
//		}
//	}
	
	private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n').withHeader(EmbeddingEntity.columns).withQuote(null);
    private final int maxInsertCount = 100000;

	private void processFile(File tmpFile) throws Exception {

	      int totalSize = 0;
	      String fileName = tmpFile.getName();
	      String[] arrays = StringUtils.split(fileName, "_");
			
	      try {
	          FileReader fr = new FileReader(tmpFile);
	          CSVParser csvParser = new CSVParser(fr, csvFormat);
	
	          List<EmbeddingEntity> embeddingList = new ArrayList<>();
	          // don't read header
	          List<CSVRecord> csvRecords = csvParser.getRecords();
	          for (int i = 0; i < csvRecords.size(); i++) {
	              CSVRecord csvRecord = csvRecords.get(i);
	              EmbeddingEntity embeddingEntity;
	              try {
	                  embeddingEntity = getEmbeddingEntity(csvRecord);
	              } catch (Exception e) {
	                  System.out.println("line i : " + i);
	                  e.printStackTrace();
	                  continue;
	              }
	              embeddingList.add(embeddingEntity);
	              if (embeddingList.size() >= maxInsertCount) {
	            	  clarity360LwebVector05DAO.insertBatch(embeddingList, finalTableName);
	                  System.out.println("finish insert for top : " + maxInsertCount + " for file :" + tmpFile.getName());
	                  embeddingList.clear();
	              	}
	          	}
	          	if (CollectionUtils.isNotEmpty(embeddingList)) {
	          		clarity360LwebVector05DAO.insertBatch(embeddingList, finalTableName);
	              	System.out.println("finish insert for left count :" + embeddingList.size());
	              	totalSize = embeddingList.size();
	              	embeddingList.clear();
	          	}
	
	          	csvParser.close();
	          	fr.close();
	      } catch (Exception e) {
	      		e.printStackTrace();
	      		return;
	      }

	}
	

	
	private EmbeddingEntity getEmbeddingEntity(CSVRecord csvRecord) throws ParseException {
		EmbeddingEntity entity = new EmbeddingEntity();
		
		entity.setCrawlRequestId(NumberUtils.toInt(csvRecord.get("crawl_request_id")));
		entity.setCrawlRequestDate(csvRecord.get("crawl_request_date"));
		entity.setDomainId(NumberUtils.toInt(csvRecord.get("domain_id")));
		entity.setUrl(csvRecord.get("url"));
		entity.setTitle(csvRecord.get("title"));
		entity.setMeta(csvRecord.get("meta"));
		entity.setH1(csvRecord.get("h1"));
		entity.setH2(csvRecord.get("h2"));
		entity.setCustomData1(csvRecord.get("custom_data_1"));
		entity.setCustomData2(csvRecord.get("custom_data_2"));
		entity.setCustomData3(csvRecord.get("custom_data_3"));
		entity.setCustomData4(csvRecord.get("custom_data_4"));
		entity.setCustomData5(csvRecord.get("custom_data_5"));
		entity.setTitleEmbedding(parseStringToFloatArray(csvRecord.get("title_embedding")));
		entity.setMetaEmbedding(parseStringToFloatArray(csvRecord.get("meta_embedding")));
		entity.setH1Embedding(parseStringToFloatArray(csvRecord.get("h1_embedding")));
		entity.setH2Embedding(parseStringToFloatArray(csvRecord.get("h2_embedding")));
		entity.setCustomData1Embedding(parseStringToFloatArray(csvRecord.get("custom_data_1_embedding")));
		entity.setCustomData2Embedding(parseStringToFloatArray(csvRecord.get("custom_data_2_embedding")));
		entity.setCustomData3Embedding(parseStringToFloatArray(csvRecord.get("custom_data_3_embedding")));
		entity.setCustomData4Embedding(parseStringToFloatArray(csvRecord.get("custom_data_4_embedding")));
		entity.setCustomData5Embedding(parseStringToFloatArray(csvRecord.get("custom_data_5_embedding")));

        return entity;
    }
	
	private static Gson gson = new Gson();
	private static Float[] DEFAULT_EMPTY_EMBEDDING = new Float[] {0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F};
	
	private Float[] parseStringToFloatArray(String input) {
		
		if (StringUtils.isBlank(input) || StringUtils.equals(input, "[]") || StringUtils.equals(input, "[0]")) {
			return DEFAULT_EMPTY_EMBEDDING;
		}
		try {
			Float[] outputArray = gson.fromJson(input, Float[].class);
			return outputArray;
		} catch (Exception e) {
			e.printStackTrace();
			return DEFAULT_EMPTY_EMBEDDING;
		}
		
		
	}

	

	public Set<Integer> getDomainSet(String domainIds) {
		Set<Integer> returnDomain = new HashSet<Integer>();
		String[] dIds = domainIds.split(",");

		if (dIds != null && dIds.length > 0) {
			for (int i = 0; i < dIds.length; i++) {
				try {
					returnDomain.add(Integer.parseInt(dIds[i]));
				} catch (Exception e) {
					
				}
			}
		}
		return returnDomain;
	}
	
	private int getVersion(int ownDomianId, Date queryDate) {
		if (specialVersion > 0) {
			return specialVersion;
		} else {
			// TODO
			return version;
		}
	}

}
