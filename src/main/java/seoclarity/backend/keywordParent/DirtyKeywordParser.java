package seoclarity.backend.keywordParent;

import com.google.gson.Gson;
import lombok.Data;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.rankcheck.*;
import seoclarity.backend.entity.rankcheck.*;
import seoclarity.backend.onetime.UploadCustomSv;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.cityhash.CityHashUtil;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1235867999
 */
public class DirtyKeywordParser {

    private static final String DEFAULT_FILE_PATH = "/Users/<USER>/Desktop/RGDeletions.txt"; // todo replace this for server path
    private static final int DEFAULT_ENGINE_ID = 1;
    private static final int DEFAULT_LANGUAGE_ID = 1;
    private static final String language = "English";
    private static final Pattern pattern = Pattern.compile("[^\\x00-\\x7F]+");

    Gson gson = new Gson();

    private static final CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true);
    private int updateCount = 0;
    private int insertCount = 0;
    private int realInsertCount = 0;
    private int skipInsertCount = 0;
    private String sourceFilePath;
    private Integer engineId;
    private Integer languageId;
    private boolean isTest;
    private int processType;
    private int wrikeId;
    private String additionaWrikeIds = "";
    private int traceKwCnt = 0;
    private int realInsertTraceKwCnt = 0;
    private SeoClarityKeywordEntityDAO keywordEntityDAO;
    private KeywordMonthlyRecommendDAO keywordRecommendDAO;
    private SeoClarityRawKeywordEntityDAO keywordEntityRawDAO;
    private KeywordAdwordsEntityDAO keywordAdwordsEntityDAO;
    private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO relationEntityDAO;
    private KeywordMonthlySearchEngineRelTraceEntityDao keywordMonthlySearchEngineRelTraceEntityDao;

    public DirtyKeywordParser() {
        keywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
        keywordRecommendDAO = SpringBeanFactory.getBean("keywordMonthlyRecommendDAO");
        keywordEntityRawDAO = SpringBeanFactory.getBean("seoClarityRawKeywordEntityDAO");
        keywordAdwordsEntityDAO = SpringBeanFactory.getBean("keywordAdwordsEntityDAO");
        relationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
        keywordMonthlySearchEngineRelTraceEntityDao = SpringBeanFactory.getBean("keywordMonthlySearchEngineRelTraceEntityDao");
    }

    /**
     * eg:
     *   process for parent:
     *      nohup /usr/bin/mvn -f /home/<USER>/source/radeL/urlUploadCustmorFile/clarity-backend-scripts/pom.xml exec:java -Dexec.mainClass="seoclarity.backend.keywordParent.DirtyKeywordParser" -Dexec.cleanupDaemonThreads=false -Dexec.args="/home/<USER>/source/radeL/urlUploadCustmorFile/tmp_file/1211_01.txt 1 1 false 1"
     *   process for no parent:
     *      nohup /usr/bin/mvn -f /home/<USER>/source/radeL/urlUploadCustmorFile/clarity-backend-scripts/pom.xml exec:java -Dexec.mainClass="seoclarity.backend.keywordParent.DirtyKeywordParser" -Dexec.cleanupDaemonThreads=false -Dexec.args="/home/<USER>/source/radeL/urlUploadCustmorFile/tmp_file/1211_02.txt 1 1 false 2"
     *   args[0]: process file path. eg: /home/<USER>/source/radeL/urlUploadCustmorFile/tmp_file/1211_02.txt
     *   args[1]: engine id. eg: 1
     *   args[2]: language id. eg: 1
     *   args[3]: test flag. eg: true (test) or false (not test)
     *   args[4]: process type. eg: 1 (parent) or 2 (no parent)
     * @param args
     */
    public static void main(String[] args) {
        DirtyKeywordParser keywordParser = new DirtyKeywordParser();
        keywordParser.startProcess(args);
    }

    private void startProcess(String[] args) {
        initParam(args);
        System.out.println("===paramInfo sourceFilePath:" + sourceFilePath + " engineId:" + engineId + " languageId:" + languageId + " isTest:" + isTest + " processType:" + processType);
        if (processType == 1) {
            process();
        } else if (processType == 2) {
            processNoParent();
        } else if (processType == 3) {
            processNoParentWithKwId();
        } else if (processType == 4) {
            updateWithKwId();
        } else if (processType == 5) {
            removeDirtyKeyword();
        } else if (processType == 6) { // update dirty keyword where hash is 0
            updateHashForDirtyKeywordHashIs0();
        } else if (processType == 7) { // reprocess dirty keyword trace
            try {
                reprocessDirtyKwTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (processType == 8) {
            traceKw();
        } else if (processType == 9) {
            removeDirtyKeywordNew();
        }
    }

    private void removeDirtyKeywordNew() {
        List<String> sourceList = null;
        try {
            sourceList = FileUtils.readLines(new File(sourceFilePath), "UTF-8");
        } catch (IOException e) {
            System.out.println("=====readFileError");
            e.printStackTrace();
            return;
        }
        if (sourceList.isEmpty()) {
            System.out.println("=====fileEmpty");
            return;
        }

        System.out.println("=====start processSize:" + sourceList.size());
        List<String> encodeKwList = new ArrayList<>();
        Map<String, String> kwNameMap = new HashMap<>();
        Map<String, String> encodeKwPNameMap = new HashMap<>();
        for (String kw : sourceList) {
            String encodeKeyword = FormatUtils.encodeKeyword(kw).toLowerCase();
            encodeKwList.add(encodeKeyword);
            kwNameMap.put(kw, encodeKeyword);
            encodeKwPNameMap.put(encodeKeyword, kw);
        }

        List<List<String>> lists = splitList(encodeKwList, 200);
        List<String> tmpSourceList = new ArrayList<>();
        List<Integer> kwIdList = new ArrayList<>();
        for (List<String> subList : lists) {
            for (String string : subList) {
                if (encodeKwPNameMap.containsKey(string)) {
                    tmpSourceList.add(encodeKwPNameMap.get(string));
                }
            }
            List<SeoClarityKeywordEntity> keywordEntityListByNames = keywordEntityDAO.getKeywordEntityListByNames(subList);
            if (keywordEntityListByNames != null && !keywordEntityListByNames.isEmpty()) {
                kwIdList.addAll(keywordEntityListByNames.stream().map(SeoClarityKeywordEntity::getId).collect(Collectors.toList()));
                if (keywordEntityListByNames.size() != subList.size()) {
                    for (String sKw : tmpSourceList) {
                        boolean flag = false;
                        for (SeoClarityKeywordEntity entity : keywordEntityListByNames) {
                            if (StringUtils.equalsIgnoreCase(sKw, entity.getKeywordText())) {
                                flag = true;
                                break;
                            }
                        }
                        if (!flag) {
                            System.out.println("=====noFoundKw:" + sKw);
                        }
                    }
                }
            }
            tmpSourceList.clear();
        }

        System.out.println("===kwInfo sourceList:" + sourceList.size() + " encodeKwList:" + encodeKwList.size() + " kwIdList:" + kwIdList.size());

        if (kwIdList.isEmpty()) {
            System.out.println("===notFoundKw");
            return;
        }

        List<List<Integer>> idLists = splitList(kwIdList, 200);
        for (List<Integer> idList : idLists) {
            List<KeywordMonthlyRecommend> exists = keywordRecommendDAO.getRankCheckIdAndSeId(idList, engineId, languageId);
            if (!exists.isEmpty()) {
                List<Integer> recommondIdList = exists.stream().map(KeywordMonthlyRecommend::getId).collect(Collectors.toList());
                System.out.println("===perDelInfo dirtyKw:" + recommondIdList.size() + " traceKw:" + recommondIdList.size() + " delId:" + recommondIdList);
                if (!isTest) {
                    keywordRecommendDAO.deleteByIdList(recommondIdList);
                    keywordMonthlySearchEngineRelTraceEntityDao.deleteByMonthlyRelIdList(KeywordMonthlySearchEngineRelTraceEntity.ACTION_TYPE_MONTHLY_RECOMMEND, recommondIdList);
                }
            }
            System.out.println("===delRecommendInfo querySize:" + idList.size() + " existsSize:" + exists.size());
        }
    }

    private void traceKw() {
        List<String> sourceList = null;
        try {
            sourceList = FileUtils.readLines(new File(sourceFilePath), "UTF-8");
        } catch (IOException e) {
            System.out.println("=====readFileError");
            e.printStackTrace();
            return;
        }
        if (sourceList.isEmpty()) {
            System.out.println("=====fileEmpty");
            return;
        }
        // 转成list<Integer>
        List<Integer> idList = sourceList.stream().map(Integer::valueOf).collect(Collectors.toList());
        List<List<Integer>> subList = splitList(idList, 200);
        Map<String, List<Integer>> map = new HashMap<>();
        for (List<Integer> innerIdList : subList) {
            List<KeywordMonthlyRecommend> remendList = keywordRecommendDAO.getRankCheckIdAndSeId(innerIdList, engineId, languageId);
            if (remendList == null || remendList.isEmpty()) {
                continue;
            }
            for (KeywordMonthlyRecommend keywordMonthlyRecommend : remendList) {
                String dateKey = FormatUtils.formatDate(keywordMonthlyRecommend.getCreateDate(), "yyyy-MM-dd");
                if (map.containsKey(dateKey)) {
                    map.get(dateKey).add(keywordMonthlyRecommend.getId());
                } else {
                    List<Integer> list = new ArrayList<>();
                    list.add(keywordMonthlyRecommend.getId());
                    map.put(dateKey, list);
                }
            }
        }
        for (String dateKey : map.keySet()) {
            List<Integer> keyIdList = map.get(dateKey);
            System.out.println("===dateKey:" + dateKey + " keyIdCnt:" + keyIdList.size());
            System.out.println("===value:" + dateKey + " keyIdList:" + keyIdList);
        }
    }

    private void updateHashForDirtyKeywordHashIs0() {
        List<KeywordMonthlyRecommend> kwList = null;
        String idStr = "";
        List<SeoClarityRawKeywordEntity> rawKeywordEntityList = null;
        Map<Integer, String> kwPHashMap = null;
        String defaultHash = "0";
        String hash = "";
        System.out.println("===start updateHashForDirtyKeywordHashIs0 ");
        int updateCnt = 0;
        while (true) {
            try {
                kwList = keywordRecommendDAO.getHashIsZeroKwList(200);
                if (kwList != null && !kwList.isEmpty()) {
                    idStr = StringUtils.join(kwList.stream().map(KeywordMonthlyRecommend::getRankcheckId).collect(Collectors.toList()), ",");
                    if (StringUtils.isNotBlank(idStr)) {
                        rawKeywordEntityList = keywordEntityRawDAO.getByKeywordIdList(idStr);
                        if (rawKeywordEntityList != null && !rawKeywordEntityList.isEmpty()) {
                            kwPHashMap = rawKeywordEntityList.stream().collect(Collectors.toMap(SeoClarityRawKeywordEntity::getKeywordId, SeoClarityRawKeywordEntity::getCdbKeywordHash, (a, b) -> a));
                            for (KeywordMonthlyRecommend recommend : kwList) {
                                hash = StringUtils.isEmpty(kwPHashMap.get(recommend.getRankcheckId())) ? defaultHash : kwPHashMap.get(recommend.getRankcheckId());
                                recommend.setChildKeywordHash(hash);
                            }
                            updateCnt += kwList.size();
                            keywordRecommendDAO.updateHashBatch(kwList);
                            rawKeywordEntityList.clear();
                            kwPHashMap.clear();
                        }
                    }
                    kwList.clear();
                } else {
                    break;
                }
                if (updateCnt % 20000 == 0) {
                    System.out.println("====already update " + updateCnt);
                }
                TimeUnit.MILLISECONDS.sleep(800);
            } catch (Exception e) {
                System.out.println("=====updateHashForDirtyKeywordHashIs0Error" + e.getMessage());
                try {
                    TimeUnit.MINUTES.sleep(5);
                } catch (InterruptedException ex) {
                    ex.printStackTrace();
                }
                e.printStackTrace();
            }
        }
        System.out.println("===end updateHashForDirtyKeywordHashIs0 ");
    }

    private void removeDirtyKeyword() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date updateDate = null;
        int dateInt = 20240209;
        try {
            updateDate = dateFormat.parse("2024-02-09");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        List<String> sourceList = null;
        try {
            sourceList = FileUtils.readLines(new File(sourceFilePath), "UTF-8");
        } catch (IOException e) {
            System.out.println("=====readFileError");
            e.printStackTrace();
            return;
        }
        if (sourceList.isEmpty()) {
            System.out.println("=====fileEmpty");
            return;
        }
        System.out.println("=====start processSize:" + sourceList.size());
        List<String> encodeKwList = new ArrayList<>();
        Map<String, String> kwNameMap = new HashMap<>();
        Map<String, String> encodeKwPNameMap = new HashMap<>();
        for (String kw : sourceList) {
            String encodeKeyword = FormatUtils.encodeKeyword(kw).toLowerCase();
            encodeKwList.add(encodeKeyword);
            kwNameMap.put(kw, encodeKeyword);
            encodeKwPNameMap.put(encodeKeyword, kw);
        }
        System.out.println("====encodeKwListSize:" + encodeKwList.size() + " kwNameMapSize:" + kwNameMap.size() + " encodeKwPNameMapSize:" + encodeKwPNameMap.size());

        Map<String, Integer> kwPIdMap = new HashMap<>();
        int skipInsert = 0;
        int testSkipInsert = 0;
        List<String> tmpSourceList = new ArrayList<>();
        List<List<String>> lists = splitList(encodeKwList, 200);
        for (List<String> subList : lists) {
            for (String string : subList) {
                if (encodeKwPNameMap.containsKey(string)) {
                    tmpSourceList.add(encodeKwPNameMap.get(string));
                }
            }
            List<SeoClarityKeywordEntity> keywordEntityListByNames = keywordEntityDAO.getKeywordEntityListByNames(subList);
            if (keywordEntityListByNames.isEmpty()) {
                // all insert
                for (String kw : tmpSourceList) {
                    String encodeKw = FormatUtils.encodeKeyword(kw);
                    if (!isTest) {
                        int id = keywordEntityDAO.insert(encodeKw.toLowerCase());
                        System.out.println("====copyKwRow kwId:" + id + " sourceKw:" + kw + " encodeKw:" + encodeKw);
                        inertToRawKeyword(kw.toLowerCase(), id);
                        kwPIdMap.put(encodeKw, id);
                    } else {
                        System.out.println("===notExistKwRow sourceKw:" + kw + " encodeKw:" + encodeKw);
                        testSkipInsert++;
                    }
                }
            } else {
                Map<String, Integer> tmpKwPIdMap = keywordEntityListByNames.stream().collect(Collectors.toMap(keywordEntity -> keywordEntity.getKeywordText().toLowerCase(), SeoClarityKeywordEntity::getId, (existing, replacement) -> existing));
                kwPIdMap.putAll(tmpKwPIdMap);
                if (keywordEntityListByNames.size() == encodeKwList.size()) {
                    tmpSourceList.clear();
                    continue;
                }
                for (String kw : tmpSourceList) {
                    String encodeKw = kwNameMap.get(kw);
                    if (kwPIdMap.containsKey(encodeKw)) {
                        skipInsert++;
                        continue;
                    }
                    if (kwPIdMap.containsKey(encodeKw.toUpperCase())) {
                        int id = kwPIdMap.get(encodeKw.toUpperCase());
                        kwPIdMap.put(kw, id);
                        skipInsert++;
                        continue;
                    }

                    if (!isTest) {
                        int id = keywordEntityDAO.insert(encodeKw.toLowerCase());
                        System.out.println("====copyKwRow kwId:" + id + " sourceKw:" + kw + " encodeKw:" + encodeKw);
                        inertToRawKeyword(kw.toLowerCase(), id);
                        kwPIdMap.put(kw, id);
                    } else {
                        System.out.println("===notExistKwRow sourceKw:" + kw + " encodeKw:" + encodeKw);
                        testSkipInsert++;
                    }
                }
            }
            tmpSourceList.clear();
        }
        List<Integer> kwIdList = new ArrayList<>(kwPIdMap.values());
        System.out.println("====finalKwPIdMap size:" + kwPIdMap.size() + " skipInsert:" + skipInsert + " testSkipInsert:" + testSkipInsert + " kwIdListSize:" + kwIdList.size());

        List<Integer> tmpKwIdList = new ArrayList<>();
        List<KeywordAdwordsEntity> adwordsEntityList = new ArrayList<>();
        int adwordsNotExistCount = 0;
        int adwordsUpdateCount = 0;


        int frequcency = 0;
        int relUpdateCnt = 0;
        int relInsertCnt = 0;

        int delCnt = 0;

        for (Integer kwId : kwIdList) {
            tmpKwIdList.add(kwId);
            if (tmpKwIdList.size() == 500) {
                adwordsEntityList = keywordAdwordsEntityDAO.getByKeywordIdList(99, languageId, tmpKwIdList);
                if (!adwordsEntityList.isEmpty()) {
                    for (KeywordAdwordsEntity keywordAdwordsEntity : adwordsEntityList) {
                        keywordAdwordsEntity.setMonthlyRelationshipCreateDate(dateInt);
                        adwordsUpdateCount++;
                    }
                    if (!isTest) {
                        keywordAdwordsEntityDAO.batchUpdateDateById(adwordsEntityList);
                    }
                    adwordsNotExistCount = adwordsNotExistCount + (500 - adwordsEntityList.size());
                }


                List<SeoClarityKeywordMonthlySearchEngineRelationEntity> relationList = relationEntityDAO.getBySeAndKwIdList(engineId, languageId, SeoClarityKeywordMonthlySearchEngineRelationEntity.DEVICE_DESKTOP, tmpKwIdList);
                if (!relationList.isEmpty()) {
                    for (SeoClarityKeywordMonthlySearchEngineRelationEntity relationEntity : relationList) {
                        if (frequcency == 0) {
                            if (relationEntity.getFrequency() != null) {
                                frequcency = relationEntity.getFrequency();
                            }
                        }
                        relUpdateCnt++;
                        relationEntity.setCreateDate(dateInt);
                    }
                    if (!isTest) {
                        relationEntityDAO.batchUpdateCreateDate(relationList);
                    }
                }

                for (Integer relKwId : tmpKwIdList) {
                    boolean exist = false;
                    for (SeoClarityKeywordMonthlySearchEngineRelationEntity checkRelEntity : relationList) {
                        if (checkRelEntity.getKeywordId() == relKwId) {
                            exist = true;
                            break;
                        }
                    }
                    if (!exist) {
                        SeoClarityKeywordMonthlySearchEngineRelationEntity relationEntity = new SeoClarityKeywordMonthlySearchEngineRelationEntity();
                        relationEntity.setKeywordId(relKwId);
                        relationEntity.setSearchEngineId(engineId);
                        relationEntity.setSearchLanguageId(languageId);
                        relationEntity.setCreateDate(dateInt);
                        relationEntity.setFrequency(frequcency);
                        if (!isTest) {
                            relationEntityDAO.insert(relationEntity);
                        }
                        relInsertCnt++;
                    }
                }

                delCnt += keywordRecommendDAO.delete(tmpKwIdList, engineId, languageId);

                tmpKwIdList.clear();
            }
        }
        if (!tmpKwIdList.isEmpty()) {
            adwordsEntityList = keywordAdwordsEntityDAO.getByKeywordIdList(99, languageId, tmpKwIdList);
            if (!adwordsEntityList.isEmpty()) {
                for (KeywordAdwordsEntity keywordAdwordsEntity : adwordsEntityList) {
                    keywordAdwordsEntity.setMonthlyRelationshipCreateDate(dateInt);
                    adwordsUpdateCount++;
                }
                if (!isTest) {
                    keywordAdwordsEntityDAO.batchUpdateDateById(adwordsEntityList);
                }
                adwordsNotExistCount = adwordsNotExistCount + (500 - adwordsEntityList.size());
            }


            List<SeoClarityKeywordMonthlySearchEngineRelationEntity> relationList = relationEntityDAO.getBySeAndKwIdList(engineId, languageId, SeoClarityKeywordMonthlySearchEngineRelationEntity.DEVICE_DESKTOP, tmpKwIdList);
            if (!relationList.isEmpty()) {
                for (SeoClarityKeywordMonthlySearchEngineRelationEntity relationEntity : relationList) {
                    if (frequcency == 0) {
                        if (relationEntity.getFrequency() != null) {
                            frequcency = relationEntity.getFrequency();
                        }
                    }
                    relUpdateCnt++;
                    relationEntity.setCreateDate(dateInt);
                }
                if (!isTest) {
                    relationEntityDAO.batchUpdateCreateDate(relationList);
                }
            }

            for (Integer relKwId : tmpKwIdList) {
                boolean exist = false;
                for (SeoClarityKeywordMonthlySearchEngineRelationEntity checkRelEntity : relationList) {
                    if (checkRelEntity.getKeywordId() == relKwId) {
                        exist = true;
                        break;
                    }
                }
                if (!exist) {
                    SeoClarityKeywordMonthlySearchEngineRelationEntity relationEntity = new SeoClarityKeywordMonthlySearchEngineRelationEntity();
                    relationEntity.setKeywordId(relKwId);
                    relationEntity.setSearchEngineId(engineId);
                    relationEntity.setSearchLanguageId(languageId);
                    relationEntity.setCreateDate(dateInt);
                    relationEntity.setFrequency(frequcency);
                    if (!isTest) {
                        relationEntityDAO.insert(relationEntity);
                    }
                    relInsertCnt++;
                }
            }

            delCnt += keywordRecommendDAO.delete(tmpKwIdList, engineId, languageId);
            tmpKwIdList.clear();
        }
        System.out.println("==info total:" + kwIdList.size() + " adwordsUpdateCount:" + adwordsUpdateCount + " adwordsNotExistCount:" + adwordsNotExistCount + " relUpdateCnt:" + relUpdateCnt + " relInsertCnt:" + relInsertCnt + " delCnt:" + delCnt);
    }
    
    private void updateWithKwId() {
        List<String> tmpSourceList = null;
        try {
            tmpSourceList = FileUtils.readLines(new File(sourceFilePath), "UTF-8");
        } catch (IOException e) {
            System.out.println("=====readFileError");
            e.printStackTrace();
            return;
        }
        if (tmpSourceList.isEmpty()) {
            System.out.println("=====fileEmpty");
            return;
        }
        List<Integer> kwIdList = new ArrayList<>();
        for (String string : tmpSourceList) {
            String[] split = string.split("\t");
            String kwId = split[0].trim();
            kwIdList.add(Integer.valueOf(kwId));
        }
        System.out.println("=====start processSize:" + tmpSourceList.size());
        List<Integer> tmpList = new ArrayList<>();
        List<KeywordMonthlyRecommend> dirtyKeywordList = new ArrayList<>();
        List<KeywordMonthlyRecommend> updateList = new ArrayList<>();

        Set<Integer> existSet = new HashSet<>();
        for (Integer i : kwIdList) {
            tmpList.add(i);
            if (tmpList.size() >= 200) {
                dirtyKeywordList = keywordRecommendDAO.getRankCheckIdAndSeId(tmpList, 150, 6);
                if (!dirtyKeywordList.isEmpty()) {
                    for (KeywordMonthlyRecommend recommend : dirtyKeywordList) {
                        existSet.add(recommend.getRankcheckId());
                    }
                }
                tmpList.clear();
            }
        }
        if (!tmpList.isEmpty()) {
            dirtyKeywordList = keywordRecommendDAO.getRankCheckIdAndSeId(tmpList, 150, 6);
            if (!dirtyKeywordList.isEmpty()) {
                for (KeywordMonthlyRecommend recommend : dirtyKeywordList) {
                    existSet.add(recommend.getRankcheckId());
                }
            }
            tmpList.clear();
        }
        System.out.println("=====existSetSize:" + existSet.size());

        List<Integer> delIdList = new ArrayList<>();
        int queryCnt = 0;
        int updateCnt = 0;
        int existCnt = 0;
        int quertCnt1 = 0;
        for (Integer i : kwIdList) {
            tmpList.add(i);
            if (tmpList.size() >= 200) {
                dirtyKeywordList = keywordRecommendDAO.getRankCheckIdAndSeId(tmpList, engineId, languageId);
                quertCnt1 += dirtyKeywordList.size();
                if (dirtyKeywordList.isEmpty()) {
                    continue;
                }
                for (KeywordMonthlyRecommend recommend : dirtyKeywordList) {
                    if (existSet.contains(recommend.getRankcheckId())) {
                        existCnt++;
                        delIdList.add(recommend.getId());
                        continue;
                    }
                    recommend.setSearchEngineId(150);
                    recommend.setSearchLanguageId(6);
                    updateList.add(recommend);
                }
                if (!isTest) {
                    keywordRecommendDAO.batchUpdateSeAndLangIdById(updateList);
                }
                updateCnt += updateList.size();
                updateList.clear();
                queryCnt += tmpList.size();
                tmpList.clear();
            }
        }
        if (!tmpList.isEmpty()) {
            dirtyKeywordList = keywordRecommendDAO.getRankCheckIdAndSeId(tmpList, engineId, languageId);
            if (!dirtyKeywordList.isEmpty()) {
                for (KeywordMonthlyRecommend recommend : dirtyKeywordList) {
                    if (existSet.contains(recommend.getRankcheckId())) {
                        existCnt++;
                        delIdList.add(recommend.getId());
                        continue;
                    }
                    recommend.setSearchEngineId(150);
                    recommend.setSearchLanguageId(6);
                    updateList.add(recommend);
                }
                if (!isTest) {
                    keywordRecommendDAO.batchUpdateSeAndLangIdById(updateList);
                }
                updateCnt += updateList.size();
                updateList.clear();
                queryCnt += tmpList.size();
                tmpList.clear();
            }
        }
        System.out.println("===updateInfo needUpdate:" + tmpSourceList.size() + " totalId:" + kwIdList.size() + " queryCnt:" + queryCnt + " queryRes:" + quertCnt1 + " updateCnt:" + updateCnt + " existCnt:" + existCnt + " delIdSize:" + delIdList.size() + " delId:" + delIdList);
    }

    private void processNoParentWithKwId() {
        List<String> tmpSourceList = null;
        try {
            tmpSourceList = FileUtils.readLines(new File(sourceFilePath), "UTF-8");
        } catch (IOException e) {
            System.out.println("=====readFileError");
            e.printStackTrace();
            return;
        }
        if (tmpSourceList.isEmpty()) {
            System.out.println("=====fileEmpty");
            return;
        }
        System.out.println("=====start processSize:" + tmpSourceList.size());
        Map<Integer, String> kwIdPKw = new HashMap<>();
        List<Integer> kwIdList = new ArrayList<>();
        for (String kwStr : tmpSourceList) {
            if (StringUtils.isBlank(kwStr)) {
                continue;
            }
            String[] split = kwStr.split("\t");
            String kwId = split[0].trim();
            String kw = split[1].trim();
            kwIdPKw.put(Integer.valueOf(kwId), kw);
            kwIdList.add(Integer.valueOf(kwId));
        }
        System.out.println("kwIdPKwSize:" + kwIdPKw.size() + " kwIdListSize:" + kwIdList.size());
        List<List<Integer>> kwIdListGroup = splitList(kwIdList, 200);
        List<Integer> existsIdList = new ArrayList<>();
        for (List<Integer> subChilidKwIdList : kwIdListGroup) {
            List<Integer> exists = keywordRecommendDAO.checkExists(subChilidKwIdList, engineId, languageId);
            existsIdList.addAll(exists);
        }
        Set<Integer> idSet = new HashSet<>(existsIdList);
        System.out.println("====existsIdListSize:" + existsIdList.size() + " idSetSize:" + idSet.size());
        int insertSize = 0;
        int updateSize = 0;
        List<KeywordMonthlyRecommend> updateList = new ArrayList<>();
        List<KeywordMonthlyRecommend> insertList = new ArrayList<>();

        int idNullCnt = 0;
        for (Integer kwId : kwIdList) {
            if (kwId == null) {
                idNullCnt++;
                continue;
            }
            String sourceKw = kwIdPKw.get(kwId);
            KeywordMonthlyRecommend recommend = new KeywordMonthlyRecommend();
            recommend.setSearchEngineId(engineId);
            recommend.setSearchLanguageId(languageId);
            recommend.setChildKeywordHash(CityHashUtil.getUnsignedUrlHash(sourceKw));
            List<String> stemmerList = SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(sourceKw, language, true, SnowBallAndNgramForForeignLanguages.STOP_KEYWORD_CHAR_SET_SIMPLE);
            recommend.setChildKeywordStream(listToString(stemmerList));
            recommend.setRankcheckId(kwId);
            if (existsIdList.contains(kwId)) {
                if (!isTest) {
                    System.out.println("====updateList kwId:" + kwId);
                }
                updateList.add(recommend);
            } else {
                if (!isTest) {
                    System.out.println("====insertList kwId:" + kwId);
                }
                recommend.setCreateDate(new Date());
                insertList.add(recommend);
            }
            if (!isTest) {
                if (!updateList.isEmpty() && updateList.size() > 200) {
                    keywordRecommendDAO.updateBatch(updateList);
                    updateSize += updateList.size();
                    updateList.clear();
                }
                if (!insertList.isEmpty() && insertList.size() > 200) {
                    keywordRecommendDAO.insertBatch(insertList);
                    insertSize += insertList.size();
                    insertList.clear();
                }
            } else {
                if (!updateList.isEmpty()) {
                    updateSize += updateList.size();
                    updateList.clear();
                }
                if (!insertList.isEmpty()) {
                    insertSize += insertList.size();
                    insertList.clear();
                }
            }
        }
        if (!isTest) {
            if (!updateList.isEmpty()) {
                keywordRecommendDAO.updateBatch(updateList);
                updateSize += updateList.size();
                updateList.clear();
            }
            if (!insertList.isEmpty()) {
                keywordRecommendDAO.insertBatch(insertList);
                insertSize += insertList.size();
                insertList.clear();
            }
        } else {
            if (!updateList.isEmpty()) {
                updateSize += updateList.size();
                updateList.clear();
            }
            if (!insertList.isEmpty()) {
                insertSize += insertList.size();
                insertList.clear();
            }
        }
        System.out.println("===endInfo total:" + kwIdList.size() + " insert:" + insertSize + " update:" + updateSize);
    }

    private void processNoParent() {
        // todo check encode
        List<String> sourceList = null;
        try {
            sourceList = FileUtils.readLines(new File(sourceFilePath), "UTF-8");
        } catch (IOException e) {
            System.out.println("=====readFileError");
            e.printStackTrace();
            return;
        }
        if (sourceList.isEmpty()) {
            System.out.println("=====fileEmpty");
            return;
        }
        System.out.println("=====start processSize:" + sourceList.size());
        List<String> encodeKwList = new ArrayList<>();
        Map<String, String> kwNameMap = new HashMap<>();
        Map<String, String> encodeKwPNameMap = new HashMap<>();
        for (String kw : sourceList) {
            String encodeKeyword = FormatUtils.encodeKeyword(kw).toLowerCase();
            encodeKwList.add(encodeKeyword);
            kwNameMap.put(kw, encodeKeyword);
            encodeKwPNameMap.put(encodeKeyword, kw);
        }
        System.out.println("====encodeKwListSize:" + encodeKwList.size() + " kwNameMapSize:" + kwNameMap.size() + " encodeKwPNameMapSize:" + encodeKwPNameMap.size());
        sourceList = sourceList.stream().distinct().collect(Collectors.toList());
        System.out.println("===dulListSize:" + sourceList.size());
        Map<String, Integer> kwPIdMap = new HashMap<>();
        int skipInsert = 0;
        List<String> tmpSourceList = new ArrayList<>();
        List<List<String>> lists = splitList(encodeKwList, 200);
        for (List<String> subList : lists) {
            for (String string : subList) {
                if (encodeKwPNameMap.containsKey(string)) {
                    tmpSourceList.add(encodeKwPNameMap.get(string));
                }
            }
            List<SeoClarityKeywordEntity> keywordEntityListByNames = keywordEntityDAO.getKeywordEntityListByNames(subList);
            if (keywordEntityListByNames.isEmpty()) {
                // all insert
                for (String kw : tmpSourceList) {
                    String encodeKw = FormatUtils.encodeKeyword(kw);
                    int id = keywordEntityDAO.insert(encodeKw.toLowerCase());
                    System.out.println("====copyKwRow kwId:" + id + " sourceKw:" + kw + " encodeKw:" + encodeKw);
                    inertToRawKeyword(kw.toLowerCase(), id);
                    kwPIdMap.put(encodeKw, id);
                }
            } else {
                Map<String, Integer> tmpKwPIdMap = keywordEntityListByNames.stream().collect(Collectors.toMap(keywordEntity -> keywordEntity.getKeywordText().toLowerCase(), SeoClarityKeywordEntity::getId, (existing, replacement) -> existing));
                kwPIdMap.putAll(tmpKwPIdMap);
                if (keywordEntityListByNames.size() == encodeKwList.size()) {
                    tmpSourceList.clear();
                    continue;
                }
                for (String kw : tmpSourceList) {
                    String encodeKw = kwNameMap.get(kw);
                    if (kwPIdMap.containsKey(encodeKw)) {
                        skipInsert++;
                        continue;
                    }
                    if (kwPIdMap.containsKey(encodeKw.toUpperCase())) {
                        int id = kwPIdMap.get(encodeKw.toUpperCase());
                        kwPIdMap.put(kw, id);
                        skipInsert++;
                        continue;
                    }

                    int id = keywordEntityDAO.insert(encodeKw.toLowerCase());
                    System.out.println("====copyKwRow kwId:" + id + " sourceKw:" + kw + " encodeKw:" + encodeKw);
                    inertToRawKeyword(kw.toLowerCase(), id);
                    kwPIdMap.put(kw, id);
                }
            }
            tmpSourceList.clear();
        }
        System.out.println("====finalKwPIdMap size:" + kwPIdMap.size() + " skipInsert:" + skipInsert);

        ArrayList<Integer> childKwIdList = new ArrayList<>(kwPIdMap.values());
        int insertSize = 0;
        int updateSize = 0;
        List<KeywordMonthlyRecommend> updateList = new ArrayList<>();
        List<KeywordMonthlyRecommend> insertList = new ArrayList<>();
        List<List<Integer>> lists1 = splitList(childKwIdList, 200);
        List<Integer> existsIdList = new ArrayList<>();
        for (List<Integer> subChilidKwIdList : lists1) {
            List<Integer> exists = keywordRecommendDAO.checkExists(subChilidKwIdList, engineId, languageId);
            existsIdList.addAll(exists);
        }

        Set<Integer> idSet = new HashSet<>(existsIdList);
        System.out.println("====existsIdListSize:" + existsIdList.size() + " idSetSize:" + idSet.size());
        for (String sourceKw : sourceList) {
            String encodeKw = kwNameMap.get(sourceKw);
            KeywordMonthlyRecommend recommend = new KeywordMonthlyRecommend();
            recommend.setSearchEngineId(engineId);
            recommend.setSearchLanguageId(languageId);
            recommend.setChildKeywordHash(CityHashUtil.getUnsignedUrlHash(sourceKw));
            List<String> stemmerList = SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(sourceKw, language, true, SnowBallAndNgramForForeignLanguages.STOP_KEYWORD_CHAR_SET_SIMPLE);
            recommend.setChildKeywordStream(listToString(stemmerList));
            Integer kwId = kwPIdMap.get(encodeKw);
            if (kwId == null) {
                System.out.println("===kwIdIsNull kw:" + sourceKw + " encodeKw:" + encodeKw);
                continue;
            }
            recommend.setRankcheckId(kwId);
            if (existsIdList.contains(kwId)) {
                if (!isTest) {
                    System.out.println("====updateList kwId:" + kwId);
                }
                updateList.add(recommend);
            } else {
                if (!isTest) {
                    System.out.println("====insertList kwId:" + kwId);
                }
                recommend.setCreateDate(new Date());
                insertList.add(recommend);
            }
            if (!isTest) {
                if (!updateList.isEmpty() && updateList.size() > 200) {
                    keywordRecommendDAO.updateBatch(updateList);
                    processTraceBack(updateList);
                    updateSize += updateList.size();
                    updateList.clear();
                }
                if (!insertList.isEmpty() && insertList.size() > 200) {
                    keywordRecommendDAO.insertBatch(insertList);
                    processTraceBack(insertList);
                    insertSize += insertList.size();
                    insertList.clear();
                }
            } else {
                if (!updateList.isEmpty() && updateList.size() > 200) {
                    updateSize += updateList.size();
                    traceKwCnt += updateList.size();
                    updateList.clear();
                }
                if (!insertList.isEmpty() && insertList.size() > 200) {
                    insertSize += insertList.size();
                    traceKwCnt += insertList.size();
                    insertList.clear();
                }
            }
        }
        if (!isTest) {
            if (!updateList.isEmpty()) {
                keywordRecommendDAO.updateBatch(updateList);
                processTraceBack(updateList);
                updateSize += updateList.size();
                updateList.clear();
            }
            if (!insertList.isEmpty()) {
                keywordRecommendDAO.insertBatch(insertList);
                processTraceBack(insertList);
                insertSize += insertList.size();
                insertList.clear();
            }
        } else {
            if (!updateList.isEmpty()) {
                updateSize += updateList.size();
                traceKwCnt += updateList.size();
                updateList.clear();
            }
            if (!insertList.isEmpty()) {
                insertSize += insertList.size();
                traceKwCnt += insertList.size();
                insertList.clear();
            }
        }
        System.out.println("===endInfo total:" + sourceList.size() + " insert:" + insertSize + " update:" + updateSize + " shouldInsertTraceKwCnt:" + traceKwCnt + " realInsertTraceKwCnt:" + realInsertTraceKwCnt);
    }

    private void reprocessDirtyKwTrace() throws Exception {
        File file = new File(sourceFilePath);
        List<String> list = FileUtils.readLines(file);
        List<List<String>> lists = splitList(list, 200);
        List<Integer> kwIds = new ArrayList<>();
        List<KeywordMonthlySearchEngineRelTraceEntity> traceEntityList = new ArrayList<>();
        for (List<String> strings : lists) {
            for (String string : strings) {
                String kwIdString = string.split(":")[1].trim();
                kwIds.add(Integer.parseInt(kwIdString));
            }
            Map<Integer, Integer> kwIdPIdMap = keywordRecommendDAO.getRecommendByRankcheckIdAndEngineAndLanguage(kwIds, engineId, languageId);
            if (kwIdPIdMap == null || kwIdPIdMap.isEmpty()) {
                System.out.println("===traceFailed query res is empty, wrikeId:" + wrikeId + " notTraceKwId:" + kwIds);
                return;
            }
            for (int rankcheckId : kwIds) {
                if (!kwIdPIdMap.containsKey(rankcheckId)) {
                    System.out.println("===kwNotTrace kwId:" + rankcheckId + " wrikeId:" + wrikeId);
                    continue;
                }
                int recommendId = kwIdPIdMap.get(rankcheckId);
                KeywordMonthlySearchEngineRelTraceEntity traceEntity = new KeywordMonthlySearchEngineRelTraceEntity();
                traceEntity.setActionType(2);
                traceEntity.setMonthlyRelId(recommendId);
                traceEntity.setWrikeId(wrikeId);
                traceEntity.setAdditionaWrikeIds(additionaWrikeIds);
                traceEntityList.add(traceEntity);
            }
            System.out.println("===startTrace sourceSize:" + kwIds.size() + " mapSize:" + kwIdPIdMap.size() + " traceSize:" + traceEntityList.size());
            int[] ints = keywordMonthlySearchEngineRelTraceEntityDao.insertBatchNew(traceEntityList);
            if (ints != null && ints.length > 0) {
                realInsertTraceKwCnt += (int) Arrays.stream(ints).filter(var -> var == 1).boxed().count();
            }
            traceKwCnt += traceEntityList.size();
            kwIds.clear();
            traceEntityList.clear();
        }
    }

    // https://www.wrike.com/open.htm?id=1370196642
    private void processTraceBack(List<KeywordMonthlyRecommend> recommendList) {
        try {
            TimeUnit.MILLISECONDS.sleep(300);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        try {
            List<Integer> kwIdList = recommendList.stream().map(KeywordMonthlyRecommend::getRankcheckId).collect(Collectors.toList());
            System.out.println("===startTrace sourceSize:" + recommendList.size() + " checkIdSize:" + kwIdList.size());
            Map<Integer, Integer> kwIdPIdMap = keywordRecommendDAO.getRecommendByRankcheckIdAndEngineAndLanguage(kwIdList, engineId, languageId);
            if (kwIdPIdMap == null || kwIdPIdMap.isEmpty()) {
                System.out.println("===traceFailed query res is empty, wrikeId:" + wrikeId + " notTraceKwId:" + kwIdList);
                return;
            }

            List<KeywordMonthlySearchEngineRelTraceEntity> traceEntityList = new ArrayList<>();
            for (KeywordMonthlyRecommend keywordMonthlyRecommend : recommendList) {
                int rankcheckId = keywordMonthlyRecommend.getRankcheckId();
                if (!kwIdPIdMap.containsKey(rankcheckId)) {
                    System.out.println("===kwNotTrace kwId:" + rankcheckId + " wrikeId:" + wrikeId);
                    continue;
                }
                int recommendId = kwIdPIdMap.get(rankcheckId);
                KeywordMonthlySearchEngineRelTraceEntity traceEntity = new KeywordMonthlySearchEngineRelTraceEntity();
                traceEntity.setActionType(2);
                traceEntity.setMonthlyRelId(recommendId);
                traceEntity.setWrikeId(wrikeId);
                traceEntity.setAdditionaWrikeIds(additionaWrikeIds);
                if (traceEntity.getMonthlyRelId() == 0) {
                    System.out.println("===traceRelIdIssue sourceCommentInfo:" + gson.toJson(keywordMonthlyRecommend) + " traceCommentInfo:" + gson.toJson(traceEntity));
                } else {
                    traceEntityList.add(traceEntity);
                }
            }
            System.out.println("===startTrace  sourceSize:" + recommendList.size() + " checkKwIdSize:" + kwIdPIdMap.size() + " mapSize:" + kwIdPIdMap.size() + " traceSize:" + traceEntityList.size());
            int[] ints = keywordMonthlySearchEngineRelTraceEntityDao.insertBatchNew(traceEntityList);
            if (ints != null && ints.length > 0) {
                realInsertTraceKwCnt += ints.length;
            }
            traceKwCnt += traceEntityList.size();
        } catch (Exception e) {
            List<Integer> kwIdList = recommendList.stream().map(KeywordMonthlyRecommend::getRankcheckId).collect(Collectors.toList());
            System.out.println("====traceError wrikeId:" + wrikeId + " notTraceKwId:" + kwIdList);
            e.printStackTrace();
        }

    }
    /*private void processTraceBack(boolean isUpdate, List<KeywordMonthlyRecommend> recommendList) {
        try {
            TimeUnit.MILLISECONDS.sleep(300);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        try {
            List<KeywordMonthlySearchEngineRelTraceEntity> traceEntityList = new ArrayList<>();
            if (isUpdate) {
                for (KeywordMonthlyRecommend keywordMonthlyRecommend : recommendList) {
                    KeywordMonthlySearchEngineRelTraceEntity traceEntity = new KeywordMonthlySearchEngineRelTraceEntity();
                    traceEntity.setActionType(2);
                    traceEntity.setMonthlyRelId(keywordMonthlyRecommend.getId());
                    traceEntity.setWrikeId(wrikeId);
                    traceEntity.setAdditionaWrikeIds(additionaWrikeIds);
                    traceEntityList.add(traceEntity);
                    if (traceEntity.getMonthlyRelId() == 0) {
                        System.out.println("===traceRelIdIssue sourceCommentInfo:" + gson.toJson(keywordMonthlyRecommend) + " traceCommentInfo:" + gson.toJson(traceEntity));
                    } else {
                        traceEntityList.add(traceEntity);
                    }
                }
                System.out.println("===startTrace isUpdate:" + isUpdate + " sourceSize:" + recommendList.size() + " traceSize:" + traceEntityList.size());
                keywordMonthlySearchEngineRelTraceEntityDao.insertBatchNew(traceEntityList);
                traceKwCnt += traceEntityList.size();
            } else {
                List<Integer> kwIdList = recommendList.stream().map(KeywordMonthlyRecommend::getRankcheckId).collect(Collectors.toList());
                System.out.println("===startTrace isUpdate:" + isUpdate + " sourceSize:" + recommendList.size() + " checkIdSize:" + kwIdList.size());
                Map<Integer, Integer> kwIdPIdMap = keywordRecommendDAO.getRecommendByRankcheckIdAndEngineAndLanguage(kwIdList, engineId, languageId);
                if (kwIdPIdMap == null || kwIdPIdMap.isEmpty()) {
                    System.out.println("===traceFailed query res is empty, wrikeId:" + wrikeId + " notTraceKwId:" + kwIdList);
                    return;
                }
                for (KeywordMonthlyRecommend keywordMonthlyRecommend : recommendList) {
                    int rankcheckId = keywordMonthlyRecommend.getRankcheckId();
                    if (!kwIdPIdMap.containsKey(rankcheckId)) {
                        System.out.println("===kwNotTrace kwId:" + rankcheckId + " wrikeId:" + wrikeId);
                        continue;
                    }
                    int recommendId = kwIdPIdMap.get(rankcheckId);
                    KeywordMonthlySearchEngineRelTraceEntity traceEntity = new KeywordMonthlySearchEngineRelTraceEntity();
                    traceEntity.setActionType(2);
                    traceEntity.setMonthlyRelId(recommendId);
                    traceEntity.setWrikeId(wrikeId);
                    traceEntity.setAdditionaWrikeIds(additionaWrikeIds);
                    if (traceEntity.getMonthlyRelId() == 0) {
                        System.out.println("===traceRelIdIssue sourceCommentInfo:" + gson.toJson(keywordMonthlyRecommend) + " traceCommentInfo:" + gson.toJson(traceEntity));
                    } else {
                        traceEntityList.add(traceEntity);
                    }
                }
                System.out.println("===startTrace isUpdate:" + isUpdate + " sourceSize:" + recommendList.size() + " checkKwIdSize:" + kwIdPIdMap.size() + " mapSize:" + kwIdPIdMap.size() + " traceSize:" + traceEntityList.size());
                keywordMonthlySearchEngineRelTraceEntityDao.insertBatchNew(traceEntityList);
                traceKwCnt += traceEntityList.size();
            }
        } catch (Exception e) {
            List<Integer> kwIdList = recommendList.stream().map(KeywordMonthlyRecommend::getRankcheckId).collect(Collectors.toList());
            System.out.println("====traceError wrikeId:" + wrikeId + " notTraceKwId:" + kwIdList);
            e.printStackTrace();
        }

    }*/

    private void process() {
        List<CSVRecord> records = null;
        try {
            CSVParser parse = csvFormat.parse(new FileReader(sourceFilePath));
            records = parse.getRecords();
        } catch (IOException e) {
            System.out.println("=====parseCsvError");
            e.printStackTrace();
            return;
        }
        if (records.isEmpty()) {
            System.out.println("=====fileEmpty");
            return;
        }

        int kwEmptyCount = 0;
        int parentKwGarbledCount = 0;
        int childKwGarbledCount = 0;
        int parentEncodeErrorCount = 0;
        int childEncodeErrorCount = 0;
        int parentKwCount = 0;
        int childKwCount = 0;
        int totalKwCount = 0; // totalKwCount = parentKwCount + childKwCount + childKwGarbledCount + parentKwGarbledCount
        List<TmpParentKeyword> parentKeywordList = new ArrayList<>();
        for (int k = 0; k < records.size(); k++) {
            CSVRecord record = records.get(k);
            TmpParentKeyword parentKeyword = new TmpParentKeyword();
            Map<String, String> childKwMap = new HashMap<>();
            for (int i = 0; i < record.size(); i++) { // i == 0 record.get(0); is parent_keyword
                String tmpKw = record.get(i).trim();
                if (StringUtils.isBlank(tmpKw)) {
                    kwEmptyCount++;
                    continue;
                }
                totalKwCount++;
                tmpKw = UploadCustomSv.removeUTF8BOM(StringUtils.lowerCase(tmpKw.trim()));
                boolean isGarbled = containsGarbledText(tmpKw);
                if (isGarbled) {
                    if (i == 0) {
                        parentKwGarbledCount++;
                        System.out.println("====garbledPKw:" + tmpKw);
                        parentKeyword.setParentIsGarbled(true);
                    } else {
                        childKwGarbledCount++;
                        System.out.println("====garbledCKw:" + tmpKw + " parentKw:" + record.get(0));
                        continue;
                    }
                }
                if (i == 0) {
                    if (!parentKeyword.parentIsGarbled) {
                        parentKwCount++;
                        parentKeyword.setParentKw(tmpKw);
                        try {
                            String encodeKw = FormatUtils.encodeKeyword(tmpKw);
//                            System.out.println("===encodeKw source:" + tmpKw + " encode:" + encodeKw);
                            parentKeyword.setParentKwEncode(encodeKw);
                        } catch (Exception e) {
                            System.out.println("====encodeParentError parentKw:" + tmpKw);
                            parentEncodeErrorCount++;
                            e.printStackTrace();
                            return;
                        }
                    }
                } else {
                    childKwCount++;
                    try {
                        String encodeKw = FormatUtils.encodeKeyword(tmpKw);
//                        System.out.println("===encodeKw source:" + tmpKw + " encode:" + encodeKw);
                        childKwMap.put(encodeKw, tmpKw);
                    } catch (Exception e) {
                        System.out.println("====encodeChildError parentKw:" + tmpKw);
                        childEncodeErrorCount++;
                        e.printStackTrace();
                    }
                }
            }
            parentKeyword.setChildKwMap(childKwMap);
            parentKeywordList.add(parentKeyword);
            if (parentKeywordList.size() >= 200) {
                processForDb(parentKeywordList);
                parentKeywordList.clear();
            }
        }
        if (!parentKeywordList.isEmpty()) {
            processForDb(parentKeywordList);
        }
        /*if (!isTest) {
            if (!parentKeywordList.isEmpty()) {
                processForDb(parentKeywordList);
            }
        } else {
            distinctParentList(parentKeywordList);
        }*/
        System.out.println("====processEnd totalKwCount:" + totalKwCount + " parentKwCount:" + parentKwCount + " childKwCount:" + childKwCount + " insertCountTotal:" + insertCount + " realInsertCount:" + realInsertCount + " skipInsertCount:" + skipInsertCount + " updateCount:" + updateCount + " parentKwGarbledCount:" + parentKwGarbledCount + " childKwGarbledCount:" + childKwGarbledCount + " parentEncodeErrorCount:" + parentEncodeErrorCount + " childEncodeErrorCount:" + childEncodeErrorCount + " emptyKwCount:" + kwEmptyCount);
    }

    private void distinctParentList(List<TmpParentKeyword> parentKeywordList) {
        Map<String, List<TmpParentKeyword>> map = new HashMap<>();
        for (TmpParentKeyword parentKeyword : parentKeywordList) {
            String parentKwEncode = parentKeyword.getParentKwEncode();
            if (map.containsKey(parentKwEncode)) {
                map.get(parentKwEncode).add(parentKeyword);
            } else {
                List<TmpParentKeyword> tmpParentKeywordList = new ArrayList<>();
                tmpParentKeywordList.add(parentKeyword);
                map.put(parentKeyword.getParentKwEncode(), tmpParentKeywordList);
            }
        }
        int i = 0;
        for (String key : map.keySet()) {
            if (map.get(key).size() > 1) {
                System.out.println("=====repeact: key:" + key + " value:" + gson.toJson(map.get(key)));
                i++;
            }
        }
        System.out.println("=====distinctParentList i:" + i);
    }

    private void processForDb(List<TmpParentKeyword> parentKeywordList) {
        System.out.println("=====processForDb parentKeywordList size:" + parentKeywordList.size());
        Set<String> parentKwEncodeList = parentKeywordList.stream().filter(tmp -> !tmp.getParentIsGarbled()).map(TmpParentKeyword::getParentKwEncode).collect(Collectors.toSet());
        System.out.println("====parentKwEncodeList size:" + parentKwEncodeList.size());
        List<String> queryKwList = new ArrayList<>(parentKwEncodeList);
        for (TmpParentKeyword tmpParentKeyword : parentKeywordList) {
            Set<String> childKwSet = tmpParentKeyword.getChildKwMap().keySet();
            queryKwList.addAll(childKwSet);
        }
        queryKwList = replaceEscape(queryKwList);
        System.out.println("====queryKwList size:" + queryKwList.size());
        List<SeoClarityKeywordEntity> keywordEntityListByNames = keywordEntityDAO.getKeywordEntityListByNames(queryKwList);
        System.out.println("====keywordEntityListByNames size:" + keywordEntityListByNames.size());
        for (SeoClarityKeywordEntity keywordEntity : keywordEntityListByNames) {
            String keywordText = keywordEntity.getKeywordText();
            int kwId = keywordEntity.getId();
            if (parentKwEncodeList.contains(keywordText)) {
                for (TmpParentKeyword parent : parentKeywordList) {
                    if (StringUtils.equalsIgnoreCase(parent.getParentKwEncode(), keywordText)) {
                        parent.setParentKwId(kwId);
                    }
                }
            } else {
                for (TmpParentKeyword parent : parentKeywordList) {
                    if (parent.getChildKwMap().containsKey(keywordText)) {
                        Map<String, Integer> childKwPIdMap = parent.getChildKwPIdMap();
                        if (childKwPIdMap == null) {
                            childKwPIdMap = new HashMap<>();
                        }
                        childKwPIdMap.put(keywordText, kwId);
                        parent.setChildKwPIdMap(childKwPIdMap);
                    }
                }
            }
        }
        System.out.println("====parentKeywordList size:" + parentKeywordList.size());
        List<Integer> childKwIdList = new ArrayList<>();
        // parent kwId 为null的先存取Id
        for (TmpParentKeyword tmpParentKeyword : parentKeywordList) {
            if (!tmpParentKeyword.parentIsGarbled) {
                if (tmpParentKeyword.getParentKwId() == null || tmpParentKeyword.getParentKwId() <= 0) {
                    if (!isTest) {
                        try {
                            int id = keywordEntityDAO.insert(tmpParentKeyword.getParentKwEncode().toLowerCase());
                            System.out.println("====copyKwRow kwId:" + id + " sourceKw:" + tmpParentKeyword.getParentKw() + " encodeKw:" + tmpParentKeyword.getParentKwEncode());
                            inertToRawKeyword(tmpParentKeyword.getParentKw().toLowerCase(), id);
                            tmpParentKeyword.setParentKwId(id);
                        } catch (Exception e) {
                            // 插入失败查询id
                            System.out.println("===insertFailedParent checkKw:" + tmpParentKeyword.getParentKwEncode());
                            List<SeoClarityKeywordEntity> keywordEntityList = keywordEntityDAO.getKeywordEntityListByNames(Collections.singletonList(tmpParentKeyword.getParentKwEncode()));
                            if (!keywordEntityList.isEmpty()) {
                                int id = keywordEntityList.get(0).getId();
                                tmpParentKeyword.setParentKwId(id);
                                System.out.println("=====checkInsertSuccessParent checkKw:" + tmpParentKeyword.getParentKwEncode() + " id:" + id);
                            } else {
                                System.out.println("=====checkInsertFailedParent checkKw:" + tmpParentKeyword.getParentKwEncode());
                            }
                        }
                    }
                }
            }
            Map<String, Integer> childKwPIdMap = tmpParentKeyword.getChildKwPIdMap();
            if (childKwPIdMap == null) {
                childKwPIdMap = new HashMap<>();
            }
            for (Map.Entry<String, String> encodeChildKwMap : tmpParentKeyword.getChildKwMap().entrySet()) {
                if (childKwPIdMap.containsKey(encodeChildKwMap.getKey())) {
                    childKwIdList.add(childKwPIdMap.get(encodeChildKwMap.getKey()));
                    continue;
                }
                if (!isTest) {
                    try {
                        int id = keywordEntityDAO.insert(encodeChildKwMap.getKey().toLowerCase());
                        System.out.println("====copyKwRow kwId:" + id + " sourceKw:" + encodeChildKwMap.getValue() + " encodeKw:" + encodeChildKwMap.getKey());
                        inertToRawKeyword(encodeChildKwMap.getValue().toLowerCase(), id);
                        childKwPIdMap.put(encodeChildKwMap.getKey(), id);
                        childKwIdList.add(id);
                    } catch (Exception e) {
                        // 插入失败查询id
                        System.out.println("===insertFailedChild checkKw:" + encodeChildKwMap.getKey());
                        List<SeoClarityKeywordEntity> keywordEntityList = keywordEntityDAO.getKeywordEntityListByNames(Collections.singletonList(encodeChildKwMap.getKey()));
                        if (!keywordEntityList.isEmpty()) {
                            int id = keywordEntityList.get(0).getId();
                            childKwPIdMap.put(encodeChildKwMap.getKey(), id);
                            childKwIdList.add(id);
                            System.out.println("=====checkInsertSuccessChild checkKw:" + encodeChildKwMap.getKey() + " id:" + id);
                        } else {
                            System.out.println("=====checkInsertFailedChild checkKw:" + tmpParentKeyword.getParentKwEncode());
                        }
                    }
                }
            }
            tmpParentKeyword.setChildKwPIdMap(childKwPIdMap);
        }
        List<Integer> existsIdList = keywordRecommendDAO.checkExists(childKwIdList, engineId, languageId);
        System.out.println("====childKWExistIdList size:" + existsIdList.size());
        List<KeywordMonthlyRecommend> updateList = new ArrayList<>();
        List<KeywordMonthlyRecommend> insertList = new ArrayList<>();
        for (TmpParentKeyword tmpParentKeyword : parentKeywordList) {
            System.out.println("====tmpParentKeyword:" + gson.toJson(tmpParentKeyword));
            Map<String, String> childKwMap = tmpParentKeyword.getChildKwMap();
            Map<String, Integer> childKwPIdMap = tmpParentKeyword.getChildKwPIdMap();
            for (String encodeChildKw : childKwMap.keySet()) {
                KeywordMonthlyRecommend recommend = new KeywordMonthlyRecommend();
                if (!tmpParentKeyword.parentIsGarbled) {
                    recommend.setParentKeywordRankcheckId(tmpParentKeyword.getParentKwId());
                    recommend.setParentKeywordHash(CityHashUtil.getUnsignedUrlHash(tmpParentKeyword.getParentKw()));
                } else {
                    recommend.setParentKeywordRankcheckId(0);
                    recommend.setParentKeywordHash("0");
                }
                recommend.setSearchEngineId(engineId);
                recommend.setSearchLanguageId(languageId);
                String childDecodeKw = childKwMap.get(encodeChildKw);
                recommend.setChildKeywordHash(CityHashUtil.getUnsignedUrlHash(childDecodeKw));
                List<String> stemmerList = SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(childDecodeKw, language, true, SnowBallAndNgramForForeignLanguages.STOP_KEYWORD_CHAR_SET_SIMPLE);
                recommend.setChildKeywordStream(listToString(stemmerList));
                Integer kwId = childKwPIdMap.get(encodeChildKw);
                recommend.setRankcheckId(kwId);
                if (existsIdList.contains(kwId)) {
                    System.out.println("====updateList kwId:" + kwId);
                    updateList.add(recommend);
                } else {
                    System.out.println("====insertList kwId:" + kwId);
                    recommend.setCreateDate(new Date());
                    insertList.add(recommend);
                }
            }
        }

        if (!insertList.isEmpty()) {
            // before insert check
            List<KeywordMonthlyRecommend> newInsertList = new ArrayList<>();
            List<KeywordMonthlyRecommend> skipInsertList = new ArrayList<>();

            Map<Integer, List<KeywordMonthlyRecommend>> rankCheckGroup = insertList.stream().collect(Collectors.groupingBy(KeywordMonthlyRecommend::getRankcheckId));

            for (Integer rankCheckId : rankCheckGroup.keySet()) {
                List<KeywordMonthlyRecommend> inserCommendList = rankCheckGroup.get(rankCheckId);
                if (inserCommendList != null && !inserCommendList.isEmpty()) {
                    newInsertList.add(inserCommendList.get(0));
                    for (int i = 0; i < inserCommendList.size(); i++) {
                        if (i == 0) {
                            continue;
                        }
                        skipInsertList.add(inserCommendList.get(i));
                    }
                }
            }
            System.out.println("======beforeCheckExist newInsertList size:" + newInsertList.size() + " skipInsertList size:" + skipInsertList.size());
            if (!newInsertList.isEmpty()) {
                List<Integer> rankCheckIdList = newInsertList.stream().map(KeywordMonthlyRecommend::getRankcheckId).collect(Collectors.toList());
                List<Integer> existsRankIdList = keywordRecommendDAO.checkExists(rankCheckIdList, engineId, languageId);
                for (KeywordMonthlyRecommend recommend : newInsertList) {
                    if (existsRankIdList.contains(recommend.getRankcheckId())) {
                        newInsertList.remove(recommend);
                        skipInsertList.add(recommend);
                    }
                }
                System.out.println("======afterCheckExist newInsertList size:" + newInsertList.size() + " skipInsertList size:" + skipInsertList.size() + " existsRankIdList size:" + existsRankIdList.size());
                if (!newInsertList.isEmpty()) {
                    if (!isTest) {
                        keywordRecommendDAO.insertBatch(newInsertList);
                    } else {
                        System.out.println("====insertListGson:" + gson.toJson(newInsertList));
                    }
                }
            }
            insertCount += insertList.size();
            realInsertCount += newInsertList.size();
            skipInsertCount += skipInsertList.size();
            System.out.println("===currentInsert needInsert:" + insertList.size() + " realInsert:" + newInsertList.size() + " skipInsert:" + skipInsertList.size());
            if (!skipInsertList.isEmpty()) {
                for (KeywordMonthlyRecommend recommend : skipInsertList) {
                    System.out.println("====skipInsertCommend:" + gson.toJson(recommend));
                }
            }
        }

        if (!updateList.isEmpty()) {
            updateCount += updateList.size();
            System.out.println("===currentUpdateSize:" + updateList.size());
            if (!isTest) {
                keywordRecommendDAO.updateBatch(updateList);
            } else {
                System.out.println("====updateListGson:" + gson.toJson(updateList));
            }
        }
    }

    private void processGarbledRecord(List<CSVRecord> garbledRecordList) {

    }
    

    private void initParam(String[] args) {
        isTest = false;
        if (args.length >= 1) {
            sourceFilePath = args[0];
        }
        if (args.length >= 2) {
            engineId = Integer.parseInt(args[1]);
        }
        if (args.length >= 3) {
            languageId = Integer.parseInt(args[2]);
        }
        if (args.length >= 4) {
            isTest = Boolean.parseBoolean(args[3]);
        }
        if (args.length >= 5) {
            processType = Integer.parseInt(args[4]);
        }
        if (args.length >= 6) {
            wrikeId = Integer.parseInt(args[5]);
        }
        if (args.length >= 7) {
            String arg = args[6];
            if (!StringUtils.equalsIgnoreCase(arg, "-")) {
                additionaWrikeIds = arg;
            }
        }
        if (sourceFilePath == null) {
            if (isTest) {
                sourceFilePath = "/Users/<USER>/Desktop/RGVariations.txt";
            } else {
                sourceFilePath = DEFAULT_FILE_PATH;
            }
        }
        if (engineId == null) {
            engineId = DEFAULT_ENGINE_ID;
        }
        if (languageId == null) {
            languageId = DEFAULT_LANGUAGE_ID;
        }
        System.out.println("====paramInit isTest:" + isTest + " engineId:" + engineId + " languageId:" + languageId + " sourceFilePath:" + sourceFilePath + " wrikeId:" + wrikeId + " additionaWrikeIds:" + additionaWrikeIds);
    }

    private void inertToRawKeyword(String sourceKw, int kwId) {
        SeoClarityRawKeywordEntity rawKeyword = new SeoClarityRawKeywordEntity();
        rawKeyword.setKeywordId(kwId);
        rawKeyword.setRawKeywordName(sourceKw.trim());

        String kwHash = CityHashUtil.getUnsignedUrlHash(sourceKw);
        rawKeyword.setCdbKeywordHash(kwHash);

        String murmurHash = MurmurHashUtils.getMurmurHash3_64(sourceKw);
        rawKeyword.setCdbKeywordMurmur3hash(murmurHash);

        if (isTest) {
            System.out.println(gson.toJson(rawKeyword));
            return;
        }
        System.out.println("====rawKwId:" + keywordEntityRawDAO.insert(rawKeyword));
    }

    //  todo
    public static boolean containsGarbledText(String input) {
        Matcher matcher = pattern.matcher(input);
        return matcher.find();
//        return false;
    }

    public static String listToString(List<String> list) {
        StringBuilder result = new StringBuilder("[");
        for (int i = 0; i < list.size(); i++) {
            result.append("'");
            result.append(list.get(i));
            result.append("'");
            if (i < list.size() - 1) {
                result.append(", ");
            }
        }
        result.append("]");
        return result.toString();
    }


    @Data
    public static class TmpParentKeyword {
        private Boolean parentIsGarbled = false;
        private Integer parentKwId;
        private String parentKw;
        private String parentKwEncode;
        private Map<String, String> childKwMap; // key: childKwEncode, value: childKw 
        private Map<String, Integer> childKwPIdMap; // key: childKwEncode, value: kwId
    }

    public <T> List<List<T>> splitList(List<T> originalList, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        int fromIndex = 0;

        while (fromIndex < originalList.size()) {
            int toIndex = Math.min(fromIndex + batchSize, originalList.size());
            batches.add(originalList.subList(fromIndex, toIndex));
            fromIndex = toIndex;
        }

        return batches;
    }

    /**
     *  aa's -> aa\'s
     *  aa\'s -> aa\\\'s
     *  aa\s -> aa\\s
     *  aas\ -> aas\\
     *
     * @param keywordName
     * @return
     */
    public static String replaceEscape(String keywordName) {
        if (keywordName == null) {
            return null;
        }
        return keywordName.replace("\\", "\\\\").replace("'", "\\'");
    }

    public static List<String> replaceEscape(List<String> keywordNameList) {
        List<String> result = new ArrayList<>();
        for (String keywordName : keywordNameList) {
            if (StringUtils.contains(keywordName, "'") || StringUtils.contains(keywordName, "\\") ) {
                String replaceKw = replaceEscape(keywordName);
                result.add(replaceKw);
            }else {
                result.add(keywordName);
            }
        }
        return result;
    }
}
