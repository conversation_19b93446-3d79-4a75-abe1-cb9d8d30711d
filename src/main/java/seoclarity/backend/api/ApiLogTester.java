package seoclarity.backend.api;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.input.CountingInputStream;
import org.apache.commons.lang.StringUtils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import lombok.extern.apachecommons.CommonsLog;

/**
 * <AUTHOR>
 * @date 2021-06-10
 * @path seoclarity.backend.api.ApiLogTester
 * test api with api logs
 */
@CommonsLog
public class ApiLogTester {
	private static final String SPLIT = "!!_!!";
	private static final String SPLIT_FOR_LOG = "\t";
	private static final int PAGE_SIZE = 10;
	private static final String METHOD_POST = "POST";
	private static final String METHOD_GET = "GET";
	private static final String API_END_POINT = "http://169.60.132.27:8183";
	private static final int API_TIMEOUT = 300 * 1000;
	
	private static String logFilePathFolder;
	
	public static void main(String[] args) {
//		logFilePathFolder = args[0];
		logFilePathFolder = "D:\\Data\\webservice_20210609";
		ApiLogTester ins = new ApiLogTester();
		ins.process();
	}
	
	private void process() {
		File[] files = new File(logFilePathFolder).listFiles();
		for (File file : files) {
			if (file.isFile()) {
				try {
					checkFile(file);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	/**
	 * line : api init catch: path!!_!!method!!_!!json params
	 */
	private void checkFile(File file) throws Exception{
		System.out.println("============Process file:" + file.getName() + "=======================");
		BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file), "UTF-8"));
		String line = null;
		List<String> lines = new ArrayList<>();
		while ((line = br.readLine()) != null) {
			if (StringUtils.startsWith(line, "api init catch:")) {
				lines.add(line);
			}
			if (lines.size() >= PAGE_SIZE) {
				apiChecker(new ArrayList<>(lines));
				lines.clear();
			}
		}
		
		br.close();
		
		if (lines.size() > 0) {
			apiChecker(new ArrayList<>(lines));
			lines.clear();
		}
		
	}
	
	private void apiChecker(List<String> lines) {
		for (String line : lines) {
			String[] cols = StringUtils.split(line, SPLIT);
			String path = cols[0];
			String method =cols[1];
			String params = cols[2];
			try {
				processApiQuery(method, path, params);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	private void processApiQuery(String method, String path, String params) {
		String url = API_END_POINT + path;
		log.info("==================================================");
		log.info("Start query:" + method + SPLIT_FOR_LOG + path);
		
		HttpRequest request = null;
		if (StringUtils.equals(METHOD_POST, method)) {
			request = HttpUtil.createPost(url).body(params);
		} else {
			request = HttpUtil.createGet(url);
			// TODO
		}
		int status = 0;
		String responseStr = null;
		String errInfo = null;
		long size = 0;
		long s = System.currentTimeMillis();
		try {
			HttpResponse response = request.timeout(API_TIMEOUT).execute();
			status = response.getStatus();
			responseStr = response.body();
			CountingInputStream counter = new CountingInputStream(response.bodyStream());
			size = counter.getByteCount() / 1024l;
			counter.close();
			response.close();
		} catch (Exception e) {
			e.printStackTrace();
			errInfo = e.getMessage();
		}
		long cost = (System.currentTimeMillis() - s) / 1000;
		boolean isComplete = isResponseComplete(responseStr, status);
		log.info("Query finished. path:" + path + SPLIT_FOR_LOG + "isComplete:" + isComplete + SPLIT_FOR_LOG + "status:" + status + SPLIT_FOR_LOG +  "cost:" + cost 
				+ SPLIT_FOR_LOG + "size:" + size + SPLIT_FOR_LOG + "errInfo:" + errInfo);
		if (!isComplete) {
			log.error("Failed query." + SPLIT_FOR_LOG + path + SPLIT_FOR_LOG + params);
		}
	}
	
	private boolean isResponseComplete(String response, int status) {
		if (status == 0 || StringUtils.isBlank(response)) {
			return false;
		}
		if (StringUtils.contains(response, "Query failed.") || StringUtils.equals(response, "[]")) {
			return false;
		}
		if (status != 200) {
			return false;
		}
		if (StringUtils.containsIgnoreCase(response, "clickhouse") && (StringUtils.containsIgnoreCase(response, "Exception") || StringUtils.contains(response, "Code:"))) {
			return false;
		}
		return true;
	}

}
