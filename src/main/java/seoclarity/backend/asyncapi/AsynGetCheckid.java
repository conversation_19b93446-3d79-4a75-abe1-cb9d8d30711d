package seoclarity.backend.asyncapi;

import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.service.CommonDataService;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static seoclarity.backend.utils.FileUtils.createFile;
import static seoclarity.backend.utils.FileUtils.outPutfile;
public class AsynGetCheckid {
    private static final String LOCAL_STORE_DIR = "/tmp/testMarven/";
    //***************************************************
    private static SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    public static String USER = "default";
    public static String PSW = "clarity99!";
    // https://www.wrike.com/open.htm?id=908593946
    public final static String TASKNAME_CONTENTFUSION_KEYWORD = "Contentfusion_Keyword";
    public final static String TASKNAME_CONTENTFUSION_KEYWORD_REALTIME = "Contentfusion_Keyword_Realtime";
    private static String TARGETPATH = "/home/<USER>/public_html/tasks/";
    private static DateFormat SDF = new SimpleDateFormat("yyyy-MM-dd");
    private static String LOG_DIR = "/tmp/contentGap/log/";
    //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/1081638150
    //private static String[] CC_TOS = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};
    private static String[] CC_TOS = new String[]{};
    public static final String SQL_ESCAPED_LINE_FEED = "\\n";
    public static final String CARRIAGE_RETURN_CHARACTER = "\r";
    public static final String ESCAPED_DOUBLE_QUOTES = "\"";

    public AsynGetCheckid() {
        File path = new File(LOG_DIR);
        if (!path.exists()) path.mkdirs();
        path = new File(LOCAL_STORE_DIR);
        if (!path.exists()) path.mkdirs();
    }

    /**
     * Add a package name prefix if the name is not absolute Remove leading "/"
     * if name is absolute
     */
    public static void checkExist(String fulloutpath,List<String> listkw) {
        try {
            List<String> listid = new ArrayList<>();
            File file = createFile(fulloutpath+"/"+"notExistUrl.txt");
            File fileResultList = createFile(fulloutpath+"/"+"checkIdList.txt");
            int i = 0;
            for (String kw:listkw) {
                String encodedKWName = CommonDataService.encodeQueueBaseKeyword(kw);
                System.out.println("======checkExist keyWords !!!!!!!!!!!"+encodedKWName);
                SeoClarityKeywordMonthlySearchEngineRelationEntity entity = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.checkExist_rank100(encodedKWName,1,1);
                Thread.sleep(300);
                if(entity!=null){
                    listid.add(String.valueOf(entity.getKeywordId())+"\t"+ kw);
                }else{
                    List<String> notExistKeyWord = new ArrayList<>();
                    notExistKeyWord.add(kw);
                    outPutfile(file,notExistKeyWord,true);

                }
            }
            outPutfile(fileResultList,listid,true);
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    public static final String SINGLE_QUOTE = "'";
    /**url: for example  http://*************:8123
     * isZip:true zipFileName =fullFileName+"zip" fullFileName will dele
     * */

    private static final String  fulloutfilename = "/home/<USER>/outfiles/";
    public static void main(String args[]) {
        if (args.length < 1) {
            System.out.println("param is error");
            return;
        }
        String inputfilepath = args[0];
        try{
            List<String> list =  RankUtils.loadFileListV1(inputfilepath);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            System.out.println("=====current time:"+formatter.format(new Date())+"=====");
            checkExist(fulloutfilename+"/"+formatter.format(new Date())+"/",list);
        }catch (Exception ex){
            ex.printStackTrace();
            System.out.println("err");
        }
    }

}

