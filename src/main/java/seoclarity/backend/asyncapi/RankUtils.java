package seoclarity.backend.asyncapi;

import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static seoclarity.backend.utils.FileUtils.createFile;
import static seoclarity.backend.utils.FileUtils.outPutfile;

public class RankUtils {

        public static final String CORS = "CORS";
        public static final String ALLOWED_ORIGINS = "allowedOrigins";
        public static final String ALLOW_ALL_SOURCES = "*";
        public static final String ALLOWED_HEADERS = "allowedHeaders";
        public static final String ALLOWED_HEADERS_LIST = "X-Requested-With,Content-Type,Accept,Origin,Token,AdminToken";
        public static final String ALLOWED_METHODS = "allowedMethods";
        public static final String ALLOWED_METHODS_LIST = "OPTIONS,GET,PUT,POST,DELETE,HEAD";
        public static final String STATIC_ASSETS_FILEPATH = "/static/";
        public static final String ALL_CHILD_URLS = "/*";

        public static final int DATA_INDEXING_BATCH_SIZE = 1000;
        public static final String MAX_NUMBER_OF_ROWS_FROM_DB = " LIMIT 5000000";
        public static final String REQUEST_BODY_STREAM_COPY = "ENTITY_STREAM_COPY";
        public static final String DEBUG_LOGGER = "sql_file";
        public static final String API_EXCEPTION_LOGGER = "api_exceptions";
        public static final String DEBUG_LOGGER2 = "debugFile";

        public static final String INNER_HITS = "inner_hits";
        public static final String UNDER_SCORE = "_";
        public static final String AGG_DELIMITER = "__";
        public static final String COMMA = ",";
        public static final String MD5_SUFFIX = "_md5";
        public static final String AGG_FIELD = "field";
        public static final String AGG = "_agg";
        public static final String SCRIPT_LANG_GROOVY = "groovy";
        public static final String LEVEL = "level";
        public static final String DUPLICATION_METRICS_INIT_SCRIPT = "init";
        public static final String DUPLICATION_METRICS_MAP_SCRIPT = "map";
        public static final String DUPLICATION_METRICS_COMBINE_SCRIPT = "combine";
        public static final String DUPLICATION_METRICS_REDUCE_SCRIPT = "reduce_duplicate_total";
        public static final String URL_FOLDER_COUNT_INIT_SCRIPT = "url-folder-scripts_init";
        public static final String URL_FOLDER_COUNT_MAP_SCRIPT = "url-folder-scripts_map";
        public static final String URL_FOLDER_COUNT_COMBINE_SCRIPT = "url-folder-scripts_combine";
        public static final String URL_FOLDER_COUNT_REDUCE_SCRIPT = "url-folder-scripts_reduce";
        public static final String OFF_PAGE_ISSUE_COUNT_INIT_SCRIPT = "off_page_init";
        public static final String OFF_PAGE_ISSUE_COUNT_MAP_SCRIPT = "off_page_map";
        public static final String OFF_PAGE_ISSUE_COUNT_COMBINE_SCRIPT = "off_page_combine";
        public static final String OFF_PAGE_ISSUE_COUNT_REDUCE_SCRIPT = "off_page_reduce";
        public static final String URL_FOLDER_TERMS_SCRIPT = "url-folder-scripts_terms";
        public static final String OFF_PAGE_ISSUE_COUNT_PARAM_1 = "post_processing_issues";
        public static final String PAGE_ANALYSIS_RESULTS = "page_analysis_results";
        public static final String POST_PROCESSING_RULES_JSON = "post_processing_rule_json";
        public static final String ANALYSIS_RULE_FILENAME = "/resources/AnalysisRulesMapping.json";
        public static final String COUNT = "count";
        public static final String COUNTS = "counts";
        public static final String DATA = "data";
        public static final String DOCS = "docs";
        public static final String SOURCE = "source";
        public static final String ITEM_COUNT = "itemcount";
        public static final String TITLE = "title";
        public static final String SEVERITY = "severity";
        public static final String DESCRIPTION = "description";
        public static final String TOOLTIP = "tooltip";
        public static final String ID = "id";
        public static final int ZERO = 0;
        public static final int ONE = 1;
        public static final int TWO = 2;
        public static final int TEN = 10;
        public static final int REDIRECT_LIMIT = 4;
        public static final int EMPTY_COL_SIZE = 0;
        public static final int DEFAULT_START_INDEX = 0;
        public static final int DEFAULT_SECOND_ELEMENT_INDEX = 0;
        public static final int DEFAULT_INT_VALUE = 0;
        public static final int COMPARATOR_EQUALITY_VALUE = 0;
        public static final int SINGLE_ELEM_COL_SIZE = 1;
        public static final String CRAWL_REQUEST_ID = "crawl_request_id";
        public static final String DOMAIN_ID = "domain_id";
        public static final int ALLOWED_NUMBER_OF_MINUTES_POINTS = 60;
        public static final int DEFAULT_THREAD_WAIT_TIME = 60;
        public static final int DEFAULT_THREAD_COUNT = 4;
        public static final String PERFORMANCE_INSIGHTS_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
        public static final String[] NUMERIC_TYPES = new String[]{"Int32", "Int8", "UInt32", "UInt64"};
        public static final String ARRAY_INT32 = "Array(Int32)";
        public static final String ARRAY_INT8 = "Array(Int8)";
        public static final String ARRAY_STRING = "Array(String)";
        public static final String TRUE_LOWERCASE = "true";
        public static final String FALSE_LOWERCASE = "false";
        public static final String TRUE = "True";
        public static final String FALSE = "False";
        public static final int TRUE_INT = 1;
        public static final String TRUE_STRING = "1";
        public static final int FALSE_INT = 0;
        public static final String FALSE_STRING = "0";
        public static final String ES_KEYWORD_STRING = ".keyword";
        public static final String NULL_VALUE_IN_QUOTES = "'NULL_VALUE'";
        public static final String AGG_STRING = "aggString";
        public static final String AGG_FIELD_NAME = "aggFieldName";
        public static final String PARENT_TABLE = "parentTable";
        public static final String CHILD_TABLE = "childTable";
        public static final String CHILD_CONDITIONS = "childConditions";
        public static final String CRAWLID = "crawlID";

        public static final String CRAWL_ID = "crawlId";
        public static final String CRAWLDEPTH_NUMBER = "depth";
        public static final String CRAWLDEPTH = "crawlDepth";
        public static final String PARENT_URLS_LIST_STRING = "parentURLsListString";
        public static final String DEFAULT_DELIMITER = ",";
        public static final String SQL_QUERY_PARAM_DELIMITER = "','";
        public static final char CSV_DELIMITER = ',';
        public static final String CSV_DELIMITER_STRING = ",";
        public static final String SITEMAPID_DELMITER = "_";
        public static final String MAPPING_CONDITION = "mappingCondition";
        public static final String QUERY_CONDITIONS = "queryConditions";
        public static final String CHILD_URL_LIST = "childURLList";
        public static final List<String> DUPLICATES_API_METRICS_ELEMENTS = Arrays.asList(
                "duplicate", "unique", "duplication", "missing", "unique", "analyzed_url_flg_s");
        public static final String ESCAPED_FW_SLASH = "\\";
        public static final String SQL_ESCAPED_FW_SLASH = "\\\\";
        public static final String SINGLE_QUOTE = "'";
        public static final String SQL_ESCAPED_SINGLE_QUOTE = "\\'";
        public static final String NULL_CHARACTER = "\0";
        public static final String SQL_ESCAPED_NULL_CHARACTER = "\\0";
        public static final String LINE_FEED_CHARACTER = "\n";
        public static final String SQL_ESCAPED_LINE_FEED = "\\n";
        public static final String CARRIAGE_RETURN_CHARACTER = "\r";
        public static final String SQL_ESCAPED_CARRIAGE_RETURN = "\\r";
        public static final String ESCAPED_DOUBLE_QUOTES = "\"";
        public static final String SQL_ESCAPED_DOUBLE_QUOTES = "\\\"";
        public static final String SUBSTITUTE_CHARACTER = "\\x1a";
        public static final String SQL_ESCAPED_SUBSTITUTE_CHARACTER = "\\Z";
        public static final String STRING_IN_QUOTES_TEMPLATE = "'%s'";
        public static final String STRING_IN_PARENTHESES_TEMPLATE = "(%s)";
        public static final String INT_LOWERCASE = "int";
        public static final String LONG_LOWERCASE = "long";
        public static final String NEW_LINE = "\n";
        public static final Float FLOAT_100 = 100.0f;
        public static final String URL_PROTOCOCAL_SEPARATOR = "://";
        public static final char QSM = '?';
        public static final char POUND_SIGN = '#';
        public static final char FORWARD_SLASH = '/';
        public static final String DOWNLAODER_CANONICAL_STATUS_FIELD = "canonicalStatusCode";
        public static final String ES_LANG_FIELD_STEMMING = "lang";
        public static final String BOOL_CONV_SCHEME_1 = "yes/no";
        public static final String BOOL_CONV_SCHEME_2 = "yes/no/na";
        public static final String ALPHA_NUMERIC_CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvxyz";
        public static final String COMPARED_WITH_CRAWLID = "comparedWithCrawlId";
        public static final String ALIAS = "alias";
        public static final String NOT_FLAG = "notFlag";
        public static final String CRAWL_SUMMARY_NEW_ALIAS = "new";
        public static final String CRAWL_SUMMARY_REMOVED_ALIAS = "removed";
        public static final String CRAWL_SUMMARY_UNCHANGED_ALIAS = "unchanged";
        public static final String CRAWL_SUMMARY_COMMON_FILTERS = "commonFilters";
        public static final String CRAWL_SUMMARY_OUTER_FILTERS = "outerConditions";
        public static final String CRAWL_SUMMARY_INNER_FILTERS = "innerConditions";
        public static final String SPACE_SURROUNDED_TEXT_TEMPLATE = " %s ";
        public static final String SORT_ODER = "sortOrder";
        public static final String COUNT_OF_ISSUES = "countOfIssues";
        public static final String VALUES = "values";
        public static final String LEFT_SQ_BRACKET = "[";
        public static final String RIGHT_SQ_BRACKET = "]";
        public static final String AGGREGATE = "aggregate";
        public static final String DOUBLE_UNDERSCORE = "__";
        public static final String CUSTOM_DELIMITER_1 = "!__!";
        public static final String CONTENTS_FIELD = "content_md5";
        public static final String COMMON_FILTERS = "commonFilters";
        public static final String FIELDS_WITH_ALIASES_TP = "fieldsWithAliases";
        public static final String hashes_TP = "hashes";
        public static final String RULES_WITH_SKIP_FLAG_TP = "RulesWithSkip";
        public static final String HITS_QUERY = "hits";
        public static final String CHILD_UNIQ_ISSUE_COUNT = "child_uniq_issue_count";


        //Partial Queries
        public static final String HREF_DOC_TYPE = "hreflang";
        public static final String FIELD_MARKER = "field";
        public static final String VALUE_MARKER = "value";
        public static final String INNER_EXPRESSION = "innerExpression";
        public static final String LOGICAL_AND = " AND ";
        public static final String LOGICAL_OR = " OR ";
        public static final String AS = " as ";
        public static final String SQL_ASTERISK = "*";
        public static final String LOGICAL_GTE = ">=";
        public static final String LOGICAL_LTE = "<=";
        public static final String SQL_LIKE = " LIKE ";
        public static final String SQL_LIKE_PREFIX = "'%";
        public static final String SQL_LIKE_SUFFIX = "%'";
        public static final String OPEN_PARANTHESES = "(";
        public static final String CLOSE_PARANTHESES = ")";
        public static final String SQL_NOT = " NOT ";
        public static final String SQL_NOT_WTS = "NOT";
        public static final String SQL_EQ = " = ";
        public static final String SQL_GROUPBY = "GROUP BY";
        public static final String TABLE_MARKER = "tableName";
        public static final String MARKER = "?";
        public static final String MARKER_WITH_DELIMITER = "?,";
        public static final String DOC_TABLE = "dis_site_crawl_doc";
        public static final String CANONICAL_TABLE = "dis_site_crawl_canonical";
        public static final String HREFLANG_TABLE = "dis_site_crawl_hreflang";
        public static final String STRUCTURED_SCHEMA_TABLE = "dis_site_crawl_structured_schema";
        public static final String CANONICAL = "canonical";
        public static final String CANONICAL_FIELD = "canonical";
        public static final String CANONICAL_TYPE_FIELD = "canonical_type";
        public static final String DOC = "doc";
        public static final String HREFLANG = "hreflang";
        public static final String HREF = "href";
        public static final String HREFLANG_ARRAY_FIELD = "hreflang_links_href";
        public static final String LANG = "lang";
        public static final String HREFLANG_LANG_FIELD = "hreflang_links_lang";
        public static final String HREFLANG_TYPE = "type";
        public static final String HREFLANG_TYPE_FIELD = "hreflang_links_type";
        public static final String PAGE_LINK_DESTINATION_URL_FIELD = "page_link_destination_url";
        public static final String PAGINATION_LINK_DIRECTION_FIELD = "pagination_links_direction";
        public static final String OG_MARKUP_CONTENT_FIELD = "og_markup_content";
        public static final String OG_MARKUP_FLAG = "og_markup_flag";
        public static final String OG_MARKUP_PROPERTY_FIELD = "og_markup_property";
        public static final String TWITTER_MARKUP_CONTENT_FIELD = "twitter_markup_content";
        public static final String TWITTER_MARKUP_PROPERTY_FIELD = "twitter_markup_property";
        public static final String HREFLANG_POST_PROCESSING_ISSUE_COUNT = "hreflang_post_processing_issues_count";
        public static final String CANONICAL_POST_PROCESSING_ISSUES_COUNT = "canonical_post_processing_issues_count";
        public static final String ROBOTS_CONTENTS_X_TAG = "robots_contents_x_tag";
        public static final String URL_HASH_FIELD = "url_hash";
        public static final String URL_MURMUR_HASH_FIELD = "url_murmur_hash";
        public static final String CANONICAL_URL_HASH = "canonical_hash";
        public static final String HREFLANG_LINKS_URL_HASH = "hreflang_links_href_hash_array";
        public static final String HREFLANG_URL_COUNT = "hreflang_url_count";
        public static final String PAGE_LINKS_URL_HASH = "page_link_destination_url_hash";
        public static final String STRUCTURED_DATA_MAPPING_TYPE = "structuredSchema";
        public static final String STRUCTURED_SCHEMA_PAGE_TYPE = "Structured Schema";
        public static final String HREFLANG_DATA = "hreflangData";



    public static List<String> loadFileListV3(String pathname,long end) {
        List<String> list = new ArrayList<>();
        String line = null;
        try {
            long count = 0;
            InputStream in = new FileInputStream(pathname);
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            while((line = reader.readLine()) != null){
                    String temp = StringUtils.trim(line);
                    if(StringUtils.isNotBlank(temp)){
                        list.add(StringUtils.trim(temp));
                        count++;
                    }
                if(end >0 && count >= end){
                    System.out.println("=====list.size:"+list.size());
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list.stream().distinct().collect(Collectors.toList());
    }

        public static List<String> loadFileListV3(File pathname,long end) {
                List<String> list = new ArrayList<>();
                String line = null;
                try {
                        long count = 0;
                        InputStream in = new FileInputStream(pathname);
                        BufferedReader reader = new BufferedReader(new InputStreamReader(in));
                        while((line = reader.readLine()) != null){
                                list.add(line);
                                count++;
                                if(end >0 && count >= end){
                                        System.out.println("=====list.size:"+list.size());
                                        break;
                                }
                        }
                } catch (Exception e) {
                        e.printStackTrace();
                }
                return list.stream().distinct().collect(Collectors.toList());
        }

        public static void copy(File oldFile, File newFile) {

                try (InputStream in = new FileInputStream(oldFile);
                     OutputStream out = new FileOutputStream(newFile)) {

                        byte[] arr = new byte[1024];
                        int len;
                        while ((len = in.read(arr)) != -1) {
                                out.write(arr, 0, len);
                        }

                } catch (Exception e) {
                        e.printStackTrace();
                }
        }

    public static List<String> loadFileListV1(String pathname) {
        List<String> list = new ArrayList<>();
        String line = null;
        try {
            InputStream in = new FileInputStream(pathname);
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            while((line = reader.readLine()) != null){
                list.add(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list.stream().distinct().collect(Collectors.toList());
    }

    public static List<String> loadFileListV1(File pathname) {
        List<String> list = new ArrayList<>();
        String line = null;
        try {
            InputStream in = new FileInputStream(pathname);
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            while((line = reader.readLine()) != null){
                list.add(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list.stream().distinct().collect(Collectors.toList());
    }
}