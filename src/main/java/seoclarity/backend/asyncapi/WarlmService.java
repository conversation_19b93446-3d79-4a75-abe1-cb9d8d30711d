package seoclarity.backend.asyncapi;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NoHttpResponseException;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.utils.*;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static seoclarity.backend.utils.HttpUtils.*;

@CommonsLog
public class WarlmService {

    //private boolean isDebug = false;

    private static final int INIT_CRAWL_REQUEST_MAX_RETRY_COUNT = 6;
    private static String checkCrawlStatusEndpoint = null;
    private static String clarityDBCrawlStatusEndpoint = null;
    //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/1041090605 marven
    private static ZeptoMailSenderComponent zeptoMailSenderComponent;
    private static CommonParamDAO commonParamDAO;
    private static String accessToken = "5bc0019e-b751-45d0-8794-c041c6902071";//warlm checked
    //private static String accessToken = "e79f3d95-bc6e-4643-a2ed-350a6685944a";//test domain4
    //checked
    private static List<String> domains = Arrays.asList(
            "amazon.com",
            "walmart.com",
            "ebay.com",
            "etsy.com",
            "target.com",
            "homedepot.com",
            "apple.com",
            "aliexpress.com",
            "lowes.com",
            "bestbuy.com",
            "alibaba.com",
            "wayfair.com",
            "macys.com",
            "kohls.com",
            "cvs.com",
            "walgreens.com",
            "ikea.com",
            "poshmark.com",
            "kroger.com",
            "bedbathandbeyond.com",
            "nordstrom.com",
            "samsclub.com",
            "instacart.com",
            "dickssportinggoods.com",
            "newegg.com",
            "heb.com",
            "autozone.com",
            "nike.com",
            "officedepot.com",
            "staples.com",
            "bhphotovideo.com",
            "advanceautoparts.com",
            "chewy.com",
            "sephora.com",
            "gamestop.com",
            "partycity.com",
            "rockauto.com",
            "safeway.com",
            "wegmans.com",
            "toysrus.com",
            "buybuybaby.com"
    );
    private static List<String> months = new ArrayList<>();

    private static List<String> domains1 = Arrays.asList(
            "safeway.com"
    );
//

    public static String initiateScrapyCrawl_Warlm(String domain,String ym,String storageid) throws Exception {
        String initCrawlEndPoint = null;
        String requestParametersInJsonWarlm = null;
        int retryCount = 0;
        String tempDomain = StringUtils.split(domain,".")[0];
        initCrawlEndPoint = "https://data.seoclarity.net/v1/task/researchgrid/details";
        // determine the crawl request parameters in JSON
        requestParametersInJsonWarlm = "{\n" +
                "\t\"ranking_month\": \"{yyyymm}\",\n" +
                "\t\"pageSize\": -1,\n" +
                "\t\"format\": \"csv\",\n" +
                "\t\"device\": \"desktop\",\n" +
                "\t\"searchEngine\": \"google.com\",\n" +
                "\t\"language\": \"en\",\n" +
                "\t\"custom_filename\": \"{fdomain}.{yyyymm}\",\n" +
                "\t\"rankingURL\": \"all\",\n" +
                "\t\"query\": {\n" +
                "\t\t\"value\": \"{domain}\",\n" +
                "\t\t\"rankType\": \"web_rank\",\n" +
                "\t\t\"filters\": [{\n" +
                "\t\t\t\"name\": \"search_volume\",\n" +
                "\t\t\t\"operation\": \">\",\n" +
                "\t\t\t\"value\": 49\n" +
                "\t\t}]\n" +
                "\t},\n" +
                "\t\"storageId\": {storageId}\n" +
                "}";
        String requestParametersInJsonOther = "{\n" +
                "\t\"ranking_month\": \"{yyyymm}\",\n" +
                "\t\"pageSize\": -1,\n" +
                "\t\"format\": \"csv\",\n" +
                "\t\"device\": \"desktop\",\n" +
                "\t\"searchEngine\": \"google.com\",\n" +
                "\t\"language\": \"en\",\n" +
                "\t\"custom_filename\": \"{fdomain}.{yyyymm}\",\n" +
                "\t\"rankingURL\": \"all\",\n" +
                "\t\"query\": {\n" +
                "\t\t\"value\": \"{domain}\",\n" +
                "\t\t\"rankType\": \"web_rank\",\n" +
                "\t\t\"filters\": [{\n" +
                "\t\t\t\"name\": \"search_volume\",\n" +
                "\t\t\t\"operation\": \">\",\n" +
                "\t\t\t\"value\": 49\n" +
                "\t\t},\n" +
                "\t\t{\n" +
                "\t\t\t\"name\": \"rank\",\n" +
                "\t\t\t\"operation\": \"<\",\n" +
                "\t\t\t\"value\": 51\n" +
                "\t\t}]\n" +
                "\t},\n" +
                "\t\"storageId\": {storageId}\n" +
                "}";

        String request = "";
        if(domain.equals("walmart.com")){
            request = requestParametersInJsonWarlm;
        }else{
            request = requestParametersInJsonOther;
        }
        request = StringUtils.replace(request,"{yyyymm}",ym);
        request = StringUtils.replace(request,"{fdomain}",tempDomain);
        request = StringUtils.replace(request,"{domain}",domain);
        request = StringUtils.replace(request,"{storageId}",storageid);

        System.out.println("initiateScrapyCrawl() initCrawlEndPoint=" + initCrawlEndPoint + ",requestParametersInJson=" + request);
        // send the Scrapy initiate crawl request
        JSONObject jsonObject = null;
        boolean sendOk = false;
        if (StringUtils.isNotBlank(initCrawlEndPoint) && StringUtils.isNotBlank(request)) {
            retryCount = 0;
            doWhileRetryInitCrawlRequest:
            while (retryCount <= INIT_CRAWL_REQUEST_MAX_RETRY_COUNT&&!sendOk) {
                try {
                    JSONObject jobj = sendHttpRequestStorm(initCrawlEndPoint, request);
                    return domain +"," + ym+ ","+ JSONUtil.toJsonStr(jobj);
                } catch (Exception e) {
                        retryCount++;
                        if (retryCount < 5) {
                            System.out.println("initiateScrapyCrawl() NoHttpResponseException retryCount=" + retryCount + ",initCrawlEndPoint="
                                    + initCrawlEndPoint + ",requestParametersInJson=" + requestParametersInJsonWarlm);
                            try {
                                Thread.sleep(50000);
                            } catch (InterruptedException e1) {
                                e1.printStackTrace();
                            }
                            continue doWhileRetryInitCrawlRequest;
                        } else {
                            throw e;
                        }

                }
                //System.out.println("sendOk:"+sendOk+",jsonObject:"+JSONUtil.toJsonStr(jsonObject));
            }
        }
        return "";
    }



    public static JSONObject sendHttpRequestStorm(String requestEndPoint, String requestParameters)
            throws Exception {
        CloseableHttpClient httpClient = null;
        httpClient = HttpUtils.getInstance().getHttpClient();
        HttpPost httpPost = null;
        CloseableHttpResponse httpResponse = null;
        int retryCount = 0;
        while (retryCount < 5) {
            try {
                httpPost = HttpUtils.getInstance().getHttpPost(requestEndPoint);
                httpPost.setEntity(new StringEntity(requestParameters, UTF_8));
                httpPost.setHeader(CACHE_CONTROL, "no-cache");
                httpPost.setHeader(CONTENT_TYPE, APPLICATION_JSON);
                httpPost.setHeader("access_token", accessToken);
                httpPost.setHeader("x-api-key", accessToken);
                httpResponse = httpClient.execute(httpPost);
                String responseBodyInString = convertResponseBodyToString(httpResponse);
                JSONObject obj = JSONUtil.parseObj(responseBodyInString);
                if(obj.get("taskId")!=null && StringUtils.isNotBlank( obj.get("taskId").toString())){
                    while(true){
                        Thread.sleep(20000);
                        JSONObject objget = sendGetHttpRequestWarlm(obj.get("taskId").toString());
                        System.out.println("taskid:"+ obj.get("taskId").toString());
                        if(objget != null && objget.get("files") !=null){
                            return objget;
                        }
                    }
                }
                System.out.println("=====reponseStr:"+responseBodyInString);
                return JSONUtil.parseObj(responseBodyInString);

            } catch (Exception e) {
                e.printStackTrace();
                retryCount++;
                if(retryCount >=5){
                    return null;
                }
            }
        }
        return null;
    }
    public static JSONObject sendGetHttpRequestWarlm(String keyid)
            throws Exception {
        CloseableHttpClient httpClient = null;
        httpClient = HttpUtils.getInstance().getHttpClient();
        HttpGet httpGet = null;
        CloseableHttpResponse httpResponse = null;
        String requestEndPoint = "http://169.60.132.27:8182/seoClarity/task/v1/research-grid/details/";
        int retryCount = 0;
        while (retryCount < 5) {
            try {
                System.out.println(requestEndPoint +keyid );
                httpGet = HttpUtils.getInstance().getHttpGet(requestEndPoint+keyid);
                //httpGet.setEntity(new StringEntity(requestParameters, UTF_8));
                httpGet.setHeader(CACHE_CONTROL, "no-cache");
                httpGet.setHeader(CONTENT_TYPE, APPLICATION_JSON);
                httpGet.setHeader("access_token", accessToken);
                httpResponse = httpClient.execute(httpGet);
                String responseBodyInString = convertResponseBodyToString(httpResponse);
                System.out.println("=====reponseStr:"+responseBodyInString);
                return JSONUtil.parseObj(responseBodyInString);
            } catch (Exception e) {
                e.printStackTrace();
                retryCount++;
                if(retryCount >=5){
                    return null;
                }
            }
        }
        return null;
    }

    public static String convertResponseBodyToString(HttpResponse response) {
        String responseBodyInString = "";
        HttpEntity resEntity = null;
        if (response != null)
            resEntity = response.getEntity();
        if (resEntity != null) {
            try {
                responseBodyInString = EntityUtils.toString(resEntity);
            } catch (ParseException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return responseBodyInString;
    }


    //"202308","202310","202312"

    private static File outputfile;
    private static boolean append = false;
    public static void main(String[] args) {
        try{
            zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
            commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
            if(args == null || args.length == 0){
                LocalDate currentDate = LocalDate.now();
                LocalDate firstDayOfLastMonth = currentDate.minusMonths(1).withDayOfMonth(1);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
                String formattedDate = firstDayOfLastMonth.format(formatter);
                log.info("====formattedDate:" + formattedDate);
                months.add(formattedDate);
            }else if(args != null && args.length > 0){
                months.add(args[0]);
            }
            log.info("======processingMonth:" + JSON.toJSONString(months));
            process();
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }



    public static void process() {
        try{
            append = false;
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
            outputfile = new File("/home/<USER>/outfiles/20240223/20240515.txt");

            //warlm
            CommonParamEntity commonParam = commonParamDAO.getById(11818,201196);
            //CommonParamEntity commonParam = commonParamDAO.getById(6060,201263);
            if(commonParam!=null && StringUtils.isNotEmpty(commonParam.getParamJson()) ){
                String bucketName = JSONUtil.parseObj(commonParam.getParamJson()).getStr("bucket").toString();
                System.out.println("=====bucketName:"+ bucketName);
                GCSUtils.deleteFileParam(commonParam.getParamJson(),bucketName,"latest/","HistoricalFiles");
            }

            //String param =
            //GCSUtils.toHistoryParam();
            for (String yyyymm: months) {
                for (String domain:domains) {
                    //String result = initiateScrapyCrawl_Warlm(domain,yyyymm,"201133");//测试用
                    String result = initiateScrapyCrawl_Warlm(domain,yyyymm,"201196");//warlm
                    List<String> list = new ArrayList<>();
                    list.add(result);
                    org.apache.commons.io.FileUtils.writeLines(outputfile, "UTF-8", list, append);
                    if(!append){
                        append=true;
                    }
                    Thread.sleep(10000);
                }
            }
            sendMailReport("Walmart's "+ String.join(",",months)+ " months  exports  successful!!! ");
        }catch (Exception ex){
            System.out.println("xxxxx");
            ex.printStackTrace();
        }
    }

    private static void sendMailReport(String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Hi all");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String subject = " Walmart extract GCS ";
        String[] ccTo = new String[]{};
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }
}
