package seoclarity.backend.asyncapi.dailyRanking;

import com.google.gson.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.asyncapi.AsynTaskProcessor;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.KeywordSubRankEntityVO;
import seoclarity.backend.entity.actonia.ApiTaskInstanceEntity;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.extract.rankingextractjson.RankExtractJsonSerpSubRanks;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.utils.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025-02-10
 * @path seoclarity.backend.asyncapi.dailyRanking.DailyRankingKeywordDetailTopXProcessorV2
 * https://www.wrike.com/open.htm?id=1589723934
 */
public class DailyRankingKeywordDetailTopXProcessorV2 {
    public static final String CDB_RI_URL = "http://cdb-ri-backup-first-external:8123";
    public static final String CH_DB_RI = "seo_daily_ranking";
    public static String USER = "default";
    public static String PSW = "clarity99!";
    private static boolean isTest = false;
    private static final boolean isDeleteFile = true;
    private static final String ENDPOINT_DEV = "https://s11-dev.seoclarity.dev/seoClarity/";
    private static final String ENDPOINT_APP = "https://api.seoclarity.net/seoClarity/";
    private static String ENDPOINT = isTest ? ENDPOINT_DEV : ENDPOINT_APP;
    private static final String KEYWORD_INFO_API = ENDPOINT + "rankIntelligenceAPI/getTopXKeywordInfoDownLoad";
    private static final String KEYWORD_DETAIL_API = ENDPOINT + "rankIntelligenceAPI/getTopXRankingDetailsDownLoad";
    private static final String KEYWORD_SERP_API = ENDPOINT + "rankIntelligenceAPI/getTopXSubrankDetailsDownLoad";
    private static final String KEYWORD_PLA_DETAIL_API = ENDPOINT + "rankIntelligenceAPI/getTopXPLADetailsDownLoad";
    private static final int TIME_OUT = 10000;
    private static final int MAX_RETRY = 10;

    private static final String FILE_NAME_PATTERN = "keywordRankingDetails_{oid}_{date}_{engine}_{language}_{device}_{location}_{taskId}";
    private static final String FILE_NAME_PATTERN_SERP = "keywordSerpRankingDetails_{oid}_{date}_{engine}_{language}_{device}_{location}_{taskId}";
    private static final String FORMAT_SQL = " format JSONEachRow";
    private static final String DEFAULT_SETTINGS = " SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 , allow_experimental_analyzer = 0;";
    private static final String TMP_FILE_PATH = "/tmp/";
    private static final Map<String, String> COMMON_HEADERS = new HashMap<>();
    {
        COMMON_HEADERS.put("Content-Type", "application/json");
    }

    private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;

    private ExtractService extractService;
    private Map<Integer, String> tagMap;

    private ApiTaskInstanceEntity apiTaskInstanceEntity;
    private String paramJson;
    private String functionName;
    private String countryName;
    private String engineName;
    private String languageName;
    private String device;
    private boolean isGeo;
    private Function<String, String[]> uploadCallBackForEachFile;
    private static final boolean isDeleteFileAfterUploadCallBack = false;

    public static DailyRankingKeywordDetailTopXProcessorV2 getInstance(ApiTaskInstanceEntity apiTaskInstanceEntity, String paramJson, String functionName) {
        return new DailyRankingKeywordDetailTopXProcessorV2(apiTaskInstanceEntity, paramJson, functionName);
    }

    public static DailyRankingKeywordDetailTopXProcessorV2 getInstance(ApiTaskInstanceEntity apiTaskInstanceEntity, String paramJson, String functionName, Function<String, String[]> uploadCallBack) {
        DailyRankingKeywordDetailTopXProcessorV2 ins = new DailyRankingKeywordDetailTopXProcessorV2(apiTaskInstanceEntity, paramJson, functionName);
        ins.uploadCallBackForEachFile = uploadCallBack;
        return ins;
    }

    private DailyRankingKeywordDetailTopXProcessorV2(ApiTaskInstanceEntity apiTaskInstanceEntity, String resultStr, String functionName) {
        this.apiTaskInstanceEntity = apiTaskInstanceEntity;
        this.paramJson = resultStr;
        this.functionName = functionName;
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
        List<GroupTagEntity> tagList = groupTagEntityDAO.getTagEntityByType(apiTaskInstanceEntity.getOwnDomainId(), GroupTagEntity.TAG_TYPE_KEYWORD);
        tagMap = new HashMap<>();
        if (tagList != null && tagList.size() > 0) {
            for (GroupTagEntity tagEntity : tagList) {
                tagMap.put(tagEntity.getId(), tagEntity.getTagName());
            }
        }
        tagList.clear();

        JsonParser jsonParser = new JsonParser();
        JsonObject obj = jsonParser.parse(resultStr).getAsJsonObject();

        String fileFormate = obj.get("fileFormate").getAsString();
        JsonObject queryJson = obj.get("queryParam").getAsJsonObject();
        int engineId = queryJson.get("engine").getAsInt();
        int languageId = queryJson.get("language").getAsInt();
        boolean isMobile = queryJson.get("mobile").getAsBoolean();
        String locationIdList = queryJson.has("queryJson") ? queryJson.get("locationIdList").getAsString() : null;

        EngineCountryLanguageMappingEntity engineCountryLanguageMapping = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageId(engineId, languageId);
        countryName = engineCountryLanguageMapping.getCountryQueryName();
        engineName = engineCountryLanguageMapping.getEngineDisplayName();
        languageName = engineCountryLanguageMapping.getLanguageQueryName();
        device = isMobile ? "mobile" : "desktop";
        isGeo = StringUtils.isNotBlank(locationIdList)
                && (StringUtils.equals(locationIdList.trim(), ",") || Arrays.stream(StringUtils.split(locationIdList, ",")).map(StringUtils::trim).anyMatch(id -> NumberUtils.isNumber(id) && Integer.parseInt(id) > 0));
        System.out.println("=========current init RI export, OID:" + apiTaskInstanceEntity.getOwnDomainId() + ", taskId:" + apiTaskInstanceEntity.getTaskId()
                + "countryName:" + countryName + ", engineName:" + engineName + ", languageName:" + languageName + ", device:" + device + ", isGeo:" + isGeo + ", resultStr:" + resultStr);
    }

    public Integer processExport(String fullFileName, boolean isFileFormatCSV, String[] err, List<String> exportFileList) throws Exception {
        return processExport(fullFileName, isFileFormatCSV, err, exportFileList, null);
    }

    public Integer processExport(String fullFileName, boolean isFileFormatCSV, String[] err, List<String> exportFileList, List<String[]> uploadUrls) throws Exception {
        // convert resultStr to gson object
        JsonParser jsonParser = new JsonParser();
        JsonObject obj = jsonParser.parse(paramJson).getAsJsonObject();

        String fileFormate = obj.get("fileFormate").getAsString();
        JsonObject queryJson = obj.get("queryParam").getAsJsonObject();
        JsonArray dateListArray = obj.get("dateList").getAsJsonArray();
        List<String> dateList = new ArrayList<>(0);
        dateListArray.forEach( o -> {
            dateList.add(o.getAsString());
        });
        int totalCnt = 0;
        List<String> fileList = new ArrayList<>(0);
        for (String date : dateList) {
            queryJson.addProperty("date", date);
            String dateIdx = StringUtils.replace(date, "-", "");
            List<String> indexList = Arrays.asList(dateIdx, dateIdx);
            queryJson.add("indexList", new Gson().toJsonTree(indexList));
            queryJson.addProperty("accessToken", ClarityDBAPIUtils.ACCESS_TOKEN_VALUE);

//            String createDateFile = StringUtils.substringBeforeLast(fullFileName, ".") + "_" + date;
//            String fileSuffix = StringUtils.substringAfterLast(fullFileName, ".");
//            String fullFileNameWithDate = createDateFile + "." + fileSuffix;
            // keywordRankingDetails_{oid}_{date}_{engine}_{language}_{device}_{location}_{taskId}
            String path = StringUtils.substringBeforeLast(fullFileName, "/");
            String fileNamePattern = StringUtils.equalsIgnoreCase(functionName, AsynTaskProcessor.TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD) ? FILE_NAME_PATTERN : FILE_NAME_PATTERN_SERP;
            String fullFileNameWithDate = path + "/" + fileNamePattern + "." + (isFileFormatCSV ? "csv" : "json");
            fullFileNameWithDate = StringUtils.replace(fullFileNameWithDate, "{oid}", apiTaskInstanceEntity.getOwnDomainId().toString());
            fullFileNameWithDate = StringUtils.replace(fullFileNameWithDate, "{date}", date);
            fullFileNameWithDate = StringUtils.replace(fullFileNameWithDate, "{engine}", engineName);
            fullFileNameWithDate = StringUtils.replace(fullFileNameWithDate, "{language}", languageName);
            fullFileNameWithDate = StringUtils.replace(fullFileNameWithDate, "{device}", device);
            fullFileNameWithDate = StringUtils.replace(fullFileNameWithDate, "{location}", isGeo ? "geo" : "national");
            fullFileNameWithDate = StringUtils.replace(fullFileNameWithDate, "{taskId}", apiTaskInstanceEntity.getTaskId());

            File fullFileNameWithDateFile = new File(fullFileNameWithDate);
            if (fullFileNameWithDateFile.exists()) {
                fullFileNameWithDateFile.delete();
            }
            int cnt = processDownload(date, queryJson.toString(), fullFileNameWithDate, err);
            processUploadCallBack(fullFileNameWithDate, uploadUrls);
            // for empty file
            File file = new File(fullFileNameWithDate);
            if (!file.exists()) {
                file.createNewFile();
            }
            fileList.add(fullFileNameWithDate);
            totalCnt += cnt;
        }


//        // zip file
//        List<String> srcFiles = fileList.stream().map(f -> {
//            File file = new File(f);
//            if (!file.exists()) {
//                System.out.println("===Wrong, can not find file:" + f + ", return empty file.");
//                try {
//                    file.createNewFile();
//                } catch (IOException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//            return file.getAbsolutePath();
//        }).filter(Objects::nonNull).collect(Collectors.toList());
//        System.out.println("============source srcFiles:" + srcFiles);
//        String fullNameZip = String.join(".", fullFileName, "zip");
//        System.out.println("============target fullNameZip:" + fullNameZip);
//        GZipUtil.createZip(srcFiles.toArray(new String[0]),fullNameZip);
//        fileList.forEach(f -> {
//            try {
//                File file = new File(f);
//                if (file.exists()) {
//                    file.delete();
//                }
//            } catch (Exception e) {e.printStackTrace();}
//        });
        exportFileList.addAll(fileList);
        return totalCnt;
    }

    private void processUploadCallBack(String fullFileNameWithDate, List<String[]> uploadUrls) {
        if (uploadCallBackForEachFile != null && uploadUrls != null) {
            try {
                String[] exportUrls = uploadCallBackForEachFile.apply(fullFileNameWithDate);
                uploadUrls.add(exportUrls);
                if (exportUrls != null && isDeleteFileAfterUploadCallBack) {
                    File file = new File(fullFileNameWithDate);
                    if (file.exists()) {
                        file.delete();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private int processDownload(String date, String jsonParams, String fullFileNameWithDate, String[] err) throws Exception {
        // get info table sql
        JsonParser jsonParser = new JsonParser();
        JsonObject jsonParamsObj = jsonParser.parse(jsonParams).getAsJsonObject();
        JsonObject nvFilterMapObj = jsonParamsObj.get("nvFilterMap").getAsJsonObject();
        // for info table, ignore rank filter
        List<String> ignoreFilterListForInfoRequest = nvFilterMapObj.entrySet().stream().filter(set -> set.getKey().startsWith("simpleRankaction")).map(Map.Entry::getKey).collect(Collectors.toList());
        if (ignoreFilterListForInfoRequest.size() > 0) {
            for (String key : ignoreFilterListForInfoRequest) {
                nvFilterMapObj.remove(key);
            }
            jsonParamsObj.add("nvFilterMap", nvFilterMapObj);
        }
        String infoJsonParam = jsonParamsObj.toString();

        // then download all keywords to file
        String responseJson = HttpRequestUtils.queryWebServiceFunctionPost(KEYWORD_INFO_API, infoJsonParam, COMMON_HEADERS, MAX_RETRY, TIME_OUT);
        JsonObject obj = jsonParser.parse(responseJson).getAsJsonObject();
        String infoSql = obj.get("data").getAsString() + FORMAT_SQL + DEFAULT_SETTINGS;

        System.out.println("==============================================================================================================");
        System.out.println("===current process date:" + date + ", fullFileNameWithDate:" + fullFileNameWithDate);
        System.out.println("===info sql:" + infoSql);

        // download to file
        String infoFile = createTempFile(date, "keywordInfo");
        int responseCode = ClarityDBUtils.httpExportFromClarityDB(CDB_RI_URL, CH_DB_RI, USER, PSW, infoSql, infoFile, false, false, err);
        if (responseCode < 0) {
            throw new Exception("Request keyword info failed, responseCode:" + responseCode);
        }

        // read file and request detail
        AtomicInteger cnt = new AtomicInteger();
        int maxKeywords = 1000;
        Path path = Paths.get(infoFile);
        List<String> lineList = new ArrayList<>(0);
        try (Stream<String> lines = Files.lines(path)) {
            lines.forEach(line -> {
                cnt.getAndIncrement();
                lineList.add(line);
                if (lineList.size() >= maxKeywords) {
                    try {
                        getKeywordDetailsAndExport(date, lineList, jsonParams, fullFileNameWithDate, err);
                    } catch (Exception e) {
                        e.printStackTrace();
                        err[0] = err[0] + "\r\n" + e.getMessage();
                    } finally {
                        lineList.clear();
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            File infoFileObj = new File(infoFile);
            if (infoFileObj.exists() && isDeleteFile) {
                infoFileObj.delete();
            }
        }
        if (lineList.size() > 0) {
            try {
                getKeywordDetailsAndExport(date, lineList, jsonParams, fullFileNameWithDate, err);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                lineList.clear();
            }
        }
        return cnt.get();
    }

    private void getKeywordDetailsAndExport(String date, List<String> keywordInfoList, String jsonParams, String exportFile, String[] err) throws Exception{
        Gson g = new Gson();
        // parse line to json
        List<DailyRankingKeywordDetailTopXConstants.KeywordInfo> infoList = keywordInfoList.stream().map(line -> g.fromJson(line, DailyRankingKeywordDetailTopXConstants.KeywordInfo.class)).collect(Collectors.toList());

//        System.out.println("==keywordInfoList:" + keywordInfoList);
//        System.out.println("==infoList:" + g.toJson(infoList));


        List<String> kidList = infoList.stream().map(info -> info.getKeyword_rankcheck_id()).collect(Collectors.toList());
        List<Integer> locationList = infoList.stream().map(DailyRankingKeywordDetailTopXConstants.KeywordInfo::getLocation_id).distinct().collect(Collectors.toList());
        String locationIdList = locationList.size() == 1 && locationList.contains(0) ? null : StringUtils.join(locationList, ",");
        // query detail
        JsonParser jsonParser = new JsonParser();
        JsonObject obj = jsonParser.parse(jsonParams).getAsJsonObject();
        if (StringUtils.isNotBlank(locationIdList)) {
            obj.addProperty("locationIdList", locationIdList);
        } else {
            obj.remove("locationIdList");
        }
        obj.add("queryKeywords", new Gson().toJsonTree(kidList));

        String detailFile = null;
        if (AsynTaskProcessor.TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX.equalsIgnoreCase(functionName)) {
            // get detail sql
            String responseJson = HttpRequestUtils.queryWebServiceFunctionPost(KEYWORD_DETAIL_API, obj.toString(), COMMON_HEADERS, MAX_RETRY, TIME_OUT);
            String detailSql = jsonParser.parse(responseJson).getAsJsonObject().get("data").getAsString() + FORMAT_SQL + DEFAULT_SETTINGS;
            System.out.println("===detailSql:" + detailSql);
            // export detail to file
            detailFile = createTempFile(date, "keywordDetail_" + System.currentTimeMillis());
            ClarityDBUtils.httpExportFromClarityDB(CDB_RI_URL, CH_DB_RI, USER, PSW, detailSql, detailFile, false, false, err);
        } else if (AsynTaskProcessor.TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX.equalsIgnoreCase(functionName)) {
            // get serpRank sql
            String responseJson = HttpRequestUtils.queryWebServiceFunctionPost(KEYWORD_SERP_API, obj.toString(), COMMON_HEADERS, MAX_RETRY, TIME_OUT);
            String detailSql = jsonParser.parse(responseJson).getAsJsonObject().get("data").getAsString() + FORMAT_SQL + DEFAULT_SETTINGS;
            System.out.println("===serpRankSql:" + detailSql);
            // export detail to file
            detailFile = createTempFile(date, "keywordDetail_" + System.currentTimeMillis());
            ClarityDBUtils.httpExportFromClarityDB(CDB_RI_URL, CH_DB_RI, USER, PSW, detailSql, detailFile, false, false, err);
        }

        // add PLA ranking details
        String plaDataFile = createTempFile(date, "keywordDetail_pla_" + System.currentTimeMillis());
        String responseForPLAJson = HttpRequestUtils.queryWebServiceFunctionPost(KEYWORD_PLA_DETAIL_API, obj.toString(), COMMON_HEADERS, MAX_RETRY, TIME_OUT);
        String plaDataSql = jsonParser.parse(responseForPLAJson).getAsJsonObject().get("data").getAsString() + FORMAT_SQL + DEFAULT_SETTINGS;
        System.out.println("===plaDataSql:" + plaDataSql);
        ClarityDBUtils.httpExportFromClarityDB(CDB_RI_URL, CH_DB_RI, USER, PSW, plaDataSql, plaDataFile, false, false, err);

        Path path = Paths.get(detailFile);
        Path plaDataPath = Paths.get(plaDataFile);
        Map<String, List<DailyRankingKeywordDetailTopXConstants.KeywordDetail>> detailMap = new HashMap<>();
        Map<String, List<DailyRankingKeywordDetailTopXConstants.SerpSubRank>> serpSubrankMap = new HashMap<>();
        Map<String, List<CLRankingDetailEntity>> secSplitplaRankKwMap = new HashMap<>();

        // read file
        try (Stream<String> lines = Files.lines(path)) {
            if (AsynTaskProcessor.TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX.equalsIgnoreCase(functionName)) {
                detailMap.putAll(lines.map(line -> g.fromJson(line, DailyRankingKeywordDetailTopXConstants.KeywordDetail.class))
                        .collect(Collectors.groupingBy(o -> o.getKeyword_rankcheck_id() + "-" + o.getLocationId())));
            } else if (AsynTaskProcessor.TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX.equalsIgnoreCase(functionName)) {
                serpSubrankMap.putAll(lines.map(line -> g.fromJson(line, DailyRankingKeywordDetailTopXConstants.SerpSubRank.class))
                        .collect(Collectors.groupingBy(o -> o.getKeyword_rankcheck_id() + "-" + o.getLocationId())));
            }
        } catch (Exception e) {
            e.printStackTrace();
            err[0] = err[0] + "\r\n" + e.getMessage();
        } finally {
            File file = new File(detailFile);
            if (file.exists() && isDeleteFile) {
                file.delete();
            }
        }
        // parse pla data
        try (Stream<String> lines = Files.lines(plaDataPath)) {
            secSplitplaRankKwMap.putAll(lines.map(line -> g.fromJson(line, CLRankingDetailEntity.class)).collect(Collectors.groupingBy(o -> o.getKeywordRankcheckId() + "-" + o.getLocationId())));
        } catch (Exception e) {
            e.printStackTrace();
            err[0] = err[0] + "\r\n" + e.getMessage();
        } finally {
            File file = new File(plaDataFile);
            if (file.exists() && isDeleteFile) {
                file.delete();
            }
        }

        System.out.println("==========export keyword detail, infoList:" + infoList.size() + "detailMap:" + detailMap.size() + ", secSplitplaRankKwMap:" + secSplitplaRankKwMap.size() + ", " + secSplitplaRankKwMap);

        // merge data
        List<String> extractLines = new ArrayList<>(0);
        for (DailyRankingKeywordDetailTopXConstants.KeywordInfo keywordInfo : infoList) {
//            System.out.println("==========export keywordInfo:" + (keywordInfo.getKeyword_rankcheck_id() + "-" + keywordInfo.getLocation_id()));
            CLRankingDetailEntity info = new CLRankingDetailEntity();
            info.setSearchEngine(engineName);
            info.setLanguageName(languageName);
            info.setCountry(countryName);
            info.setDevice(device);

            info.setRankingDate(date);
            info.setRankDate(Integer.parseInt(StringUtils.replace(date, "-", "")));
            info.setKeywordRankcheckId(Long.valueOf(keywordInfo.getKeyword_rankcheck_id()));
            info.setKeywordName(keywordInfo.getKeyword_name());
            info.setLocationName(keywordInfo.getCityName());

            info.setCpc(Float.valueOf(keywordInfo.getCpc()));
            info.setAvgSearchVolume(Long.valueOf(keywordInfo.getAvg_search_volume()));
            info.setTrueDemand(Integer.valueOf(keywordInfo.getTrueDemand()));
            info.setCreateDate(keywordInfo.getKeyword_create_date());
            info.setTagList(keywordInfo.getTags());
            info.setTagList(keywordInfo.getTags() != null ?
                keywordInfo.getTags().stream().map(id -> tagMap.containsKey(Integer.parseInt(id)) ? tagMap.get(Integer.parseInt(id)) : "")
                        .filter(StringUtils::isNotBlank).collect(Collectors.toList()) : new ArrayList<>(0));
            info.setTagIdHierarchy(keywordInfo.getHierarchyTags());
            info.setPlpUrlList(keywordInfo.getPlpUrlList());

            // serp
            info.setGoogleRecommend(keywordInfo.getGoogleRecommend());
            info.setTotalResults(keywordInfo.getTotalResults());
            info.setSerpFilterButtonTextStr(keywordInfo.getAppbarDisplayKeywords());
            info.setSerpFilterButtonTextStr(keywordInfo.getAppbarSearchedKeywords());
            info.setAppFlg(convertFlag(keywordInfo.getApp()));
            info.setImgFlg(convertFlag(keywordInfo.getImg()));
            info.setNewsFlg(convertFlag(keywordInfo.getNews()));
            info.setVideoFlg(convertFlag(keywordInfo.getVideo()));
            info.setLlFlg(convertFlag(keywordInfo.getLl()));
            info.setPpcFlg(convertFlag(keywordInfo.getPpc()));
            info.setAnswerBoxFlg(convertFlag(keywordInfo.getAnswerbox()));
            info.setHotelFlag(convertFlag(keywordInfo.getHotel()));
            info.setFlightSearchFlg(convertFlag(keywordInfo.getFlight()));
            info.setPla_flg(convertFlag(keywordInfo.getPla()));
            info.setKnog_flg(convertFlag(keywordInfo.getKnog()));
            info.setFromsourcesacrossthewebFlag(convertFlag(keywordInfo.getFromsourcesacrosstheweb()));
            info.setFindresultsonFlag(convertFlag(keywordInfo.getFindresultson()));
            info.setPopulardestinationsFlag(convertFlag(keywordInfo.getPopulardestinations()));
            info.setAioFlag(convertFlag(keywordInfo.getAi_genai_search()));
            info.setPopularRecipesFlag(convertFlag(keywordInfo.getRecipes()));
            info.setPopularStoreFlag(convertFlag(keywordInfo.getPopularStore()));
            info.setDiscussionsandforumsFlag(convertFlag(keywordInfo.getDiscussionAndForums()));
            info.setBuyingguideFlag(convertFlag(keywordInfo.getBuyingGuide()));
            info.setPeopleAlsoAskList(keywordInfo.getPeopleAlsoAskList());
            info.setThingsToKnow(keywordInfo.getThingsToKnowStr());
            if (StringUtils.isNotBlank(keywordInfo.getRefineBy_title()) && !StringUtils.equals(keywordInfo.getRefineBy_title(), "-")) {
                info.setRefineBy(keywordInfo.getRefineBy_title());
                if (StringUtils.isNotBlank(keywordInfo.getRefineBy_detail())) {
                    info.setRefineByDetails(Arrays.asList(StringUtils.split(keywordInfo.getRefineBy_detail(), '\t')));
                }
            }
            if (keywordInfo.getRelatedSearches() != null) {
                info.setRelatedSearches(keywordInfo.getRelatedSearches().stream().filter(s -> !StringUtils.equals(s, "-")).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            }
            String key = keywordInfo.getKeyword_rankcheck_id() + "-" + keywordInfo.getLocation_id();
            if (AsynTaskProcessor.TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX.equalsIgnoreCase(functionName)) {
                // get detail and subrank datalist
                List<DailyRankingKeywordDetailTopXConstants.KeywordDetail> rankDetailList = detailMap.get(key);
                List<CLRankingDetailEntity> detailRankList = Collections.emptyList();
                List<CLRankingDetailEntity> subRankList = new ArrayList<>(0);
                if (rankDetailList != null) {
                    detailRankList = rankDetailList.stream().map(detail -> {
                        CLRankingDetailEntity detailEntity = new CLRankingDetailEntity();
                        detailEntity.setUrl(detail.getRankingUrl());
                        detailEntity.setType(detail.getType());
                        detailEntity.setTrueRank(detail.getTrue_rank());
                        detailEntity.setWebRank(detail.getWeb_rank());
                        detailEntity.setLabel(detail.getLabel());
                        detailEntity.setMeta(detail.getMeta());
                        detailEntity.setPriceNumber(detail.getPriceNumber());
                        detailEntity.setRatingNumber(detail.getRatingNumber());
                        detailEntity.setCouponFlag(detail.getCouponFlag());

                        if (detail.getSub_ranks() != null && !detail.getSub_ranks().isEmpty()) {
                            for (int i = 0; i < detail.getSub_ranks().size(); i++) {
                                CLRankingDetailEntity subrank = new CLRankingDetailEntity();
                                subrank.setUrl(detail.getSubRankUrls().get(i));
                                subrank.setTrueRank(detail.getTrue_rank());
                                subrank.setSubRank(Integer.parseInt(detail.getSub_ranks().get(i)));
                                subrank.setSubRankUrl(detail.getSubRankUrls().get(i));
                                if (detail.getSubRankLabels() != null) {
                                    subrank.setSubRankLabel(detail.getSubRankLabels().get(i));
                                }
                                if (detail.getSubRankRatings() != null && detail.getSubRankRatings().size() == detail.getSub_ranks().size()) {
                                    if (StringUtils.isNotBlank(detail.getSubRankRatings().get(i)) && !StringUtils.equals(detail.getSubRankRatings().get(i), "-")) {
                                        subrank.setSubrankRating(detail.getSubRankRatings().get(i));
                                    }
                                }
                                if (detail.getSubRankPrices() != null && detail.getSubRankPrices().size() == detail.getSub_ranks().size()) {
                                    if (StringUtils.isNotBlank(detail.getSubRankPrices().get(i)) && !StringUtils.equals(detail.getSubRankPrices().get(i), "-")) {
                                        subrank.setSubrankPrice(detail.getSubRankPrices().get(i));
                                    }
                                }
                                if (detail.getSubRankTags() != null && detail.getSubRankTags().size() == detail.getSub_ranks().size()) {
                                    if (StringUtils.isNotBlank(detail.getSubRankTags().get(i)) && !StringUtils.equals(detail.getSubRankTags().get(i), "-")) {
                                        subrank.setSubrankTagName(detail.getSubRankTags().get(i));
                                    }
                                }
                                subRankList.add(subrank);
                            }
                        }
                        return detailEntity;
                    }).collect(Collectors.toList());
                } else {
                    System.out.println("===find empty ranking keyword details, date:" + date + ", key:" + keywordInfo.getKeyword_rankcheck_id() + "-" + keywordInfo.getLocation_id());
                }
                // pla data
                List<CLRankingDetailEntity> secPlaRankList = secSplitplaRankKwMap.get(key);
                Map<String, Object> secExtraParamMap = new HashMap<>();
                secExtraParamMap.put("plaRank", secPlaRankList);
                // parse to line
                extractLines.add(extractService.setExtractJsonResult(info, detailRankList, subRankList, tagMap, secExtraParamMap));
            } else if (AsynTaskProcessor.TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX.equalsIgnoreCase(functionName)) {
                // get serp subrank
                List<DailyRankingKeywordDetailTopXConstants.SerpSubRank> details = serpSubrankMap.get(key);
                if (details != null && details.size() > 0) {
                    List<DailyRankingKeywordDetailTopXConstants.SerpSubRank> brandList = details.stream().filter(o -> StringUtils.equalsIgnoreCase(o.getSerpMatchType(), "brand")).collect(Collectors.toList());
                    List<DailyRankingKeywordDetailTopXConstants.SerpSubRank> nonbrandList = details.stream().filter(o -> !StringUtils.equalsIgnoreCase(o.getSerpMatchType(), "brand")).collect(Collectors.toList());

                    Map<String, List<RankExtractJsonSerpSubRanks>> brandMap = convertSerpSubranks(brandList);
                    Map<String, List<RankExtractJsonSerpSubRanks>> nonbrandMap = convertSerpSubranks(nonbrandList);
                    info.setBrandMap(brandMap);
                    info.setNonbrandMap(nonbrandMap);
                } else {
                    System.out.println("===find empty ranking keyword serp details, date:" + date + ", key:" + keywordInfo.getKeyword_rankcheck_id() + "-" + keywordInfo.getLocation_id());
                }
                // pla data
                List<CLRankingDetailEntity> secPlaRankList = secSplitplaRankKwMap.get(key);
                Map<String, Object> secExtraParamMap = new HashMap<>();
                secExtraParamMap.put("plaRank", secPlaRankList);
                // parse to line
                extractLines.add(extractService.setExtractJsonResult(info, Collections.emptyList(), Collections.emptyList(), tagMap, secExtraParamMap));
            }
        }

        // export to file
        FileUtils.writeLines(new File(exportFile), "UTF-8", extractLines, "\n", true);
    }

    private Map<String, List<RankExtractJsonSerpSubRanks>> convertSerpSubranks(List<DailyRankingKeywordDetailTopXConstants.SerpSubRank> list) {
        Map<String, List<RankExtractJsonSerpSubRanks>> resultList = list.stream().map(o -> {
            int urltype = o.getUrl_type();
            int rankingtype = o.getRanking_type();
            String serpType = CLRankingDetailEntity.SERP_UNIVERSAL_NAME_MAP.get(urltype);
            if (StringUtils.isBlank(serpType)) {
                serpType = KeywordSubRankEntityVO.SERP_SUBRANK_RANKING_TYPE_NAME_MAP.get(rankingtype);
            }
            serpType = StringUtils.isBlank(serpType) ? "other" : serpType;
            RankExtractJsonSerpSubRanks serpSubRank = new RankExtractJsonSerpSubRanks();
            serpSubRank.setSerpTypeName(serpType);
            serpSubRank.setRank(o.getTrueRank() == null || o.getTrueRank() == 0 ? null : o.getTrueRank());
            serpSubRank.setSubrank(o.getSub_rank());

            if (KeywordRankEntityVO.TYPE_VIDEO == urltype || KeywordRankEntityVO.TYPE_SHORT_VIDEO == urltype) {
                serpSubRank.setBrand(o.getSubRank_url());
                serpSubRank.setVideoName(o.getSubRankLabel());
            } else if (KeywordRankEntityVO.TYPE_JOB_URL == urltype || KeywordRankEntityVO.TYPE_FROM_SOURCES_ACROSS_THE_WEB == urltype) {
                serpSubRank.setBrand(o.getSubRank_url());
            } else if (Arrays.asList(
                    KeywordRankEntityVO.TYPE_LOCALLISTING,
                    KeywordRankEntityVO.TYPE_HOTEL).contains(urltype)) {
                serpSubRank.setBrand(o.getSubRank_url());
                serpSubRank.setTitle(o.getSubRankLabel());
                serpSubRank.setRating(o.getRatingNumber());
            } else if (KeywordRankEntityVO.TYPE_POPULAR_PRODUCTS == urltype) {
                serpSubRank.setBrand(o.getSubRank_url());
                serpSubRank.setTitle(o.getSubRankLabel());
                serpSubRank.setPrice(o.getPrice());
                serpSubRank.setTag(o.getTag());
                serpSubRank.setRating(o.getRatingNumber());
            } else {
                serpSubRank.setUrl(o.getSubRank_url());
                serpSubRank.setTitle(o.getSubRankLabel());
                serpSubRank.setReviews(convertString(o.getReviews()));
                serpSubRank.setRating(convertString(o.getRatingNumber()));
            }

            return serpSubRank;
        }).collect(Collectors.groupingBy(RankExtractJsonSerpSubRanks::getSerpTypeName));

        return resultList;
    }

    private static String convertString(String str) {
        return StringUtils.isBlank(str) || StringUtils.equalsIgnoreCase(str, "-") ? null : str;
    }

    private String convertFlag(Integer flag) {
        return flag != null && StringUtils.equals(flag.toString(), "1") ? "y" : "n";
    }

    private String createTempFile(String date, String fileSuffix) {
        int oid = apiTaskInstanceEntity.getOwnDomainId();
        String taskId = apiTaskInstanceEntity.getTaskId();
        String fileName = oid + "_" + taskId + "_" + date + "_" + System.currentTimeMillis() + "_" + fileSuffix;
        File file = new File(TMP_FILE_PATH + fileName);
        if (file.exists()) {
            file.delete();
        }
        return file.getAbsolutePath();
    }
}
