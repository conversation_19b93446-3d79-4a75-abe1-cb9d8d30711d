package seoclarity.backend.asyncapi;

import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.testcontainers.shaded.okhttp3.*;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static seoclarity.backend.utils.FileUtils.createFile;
import static seoclarity.backend.utils.FileUtils.outPutfile;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.asyncapi.AsynTaskProcessor" -Dexec.args="102"
public class AsynGetCheckidV4 {
    private static final int MAX_LIST_SIZE = 1000;
    private static final int GET_TIME_OUT = 10 * 1000;
    private static final String END_POINT = "https://event.seoclarity.workers.dev";
    private static final String LIST_BY_KEY_WITH_CURSOR = "/cursor/{cursor}?{prefix}";
    private static final String GET_BY_KEY = "/key/{task_id}";
    private static final String LIST_BY_KEY = "/list/{prefix}";
    private static final String zeroRanksql = "as csvurl, '-' as url0,'-' rank0  format JSONEachRow";
    //***************************************************
    public static final String BASEPREFIX = "asyncdownload_contentgap_estdTrafficKWDetailNew_ownCustom_";
    private static final String LOCAL_STORE_DIR = "/tmp/testMarven/";
    //***************************************************
    private static SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private static final String DELETE_BY_KEY = "/delete/{task_id}";
    public static final int DOWNLOADMAXCOUNT = 10;
    private static  List<String> listCache = null;
    private static final String HEADER_TOKEN_KEY = "seoclarity-internal-token";
    private static final String HEADER_TOKEN_VALUE = "6603b708dd0bf24b0e7a1e68408c454e";
    private static Integer READ_TIMEOUT = 1800;
    public static int HTTP_EXPORT_RESPONSE_SCUCCESS=200;
    public static int HTTP_EXPORT_RESPONSE_ERROR=400;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD=-2;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_EXCEPTION=-1;
    private static final Map<String, String> CACHE_HEADER_MAP = new HashMap<>();


    public static String USER = "default";
    public static String PSW = "clarity99!";
    public static String FILE_FORMATE_CSV = "CSV";// "format": "CSV",    -- optional            [CSV,JSON]
    public static String FILE_FORMATE_JSON = "JSON";
    public static String FAILED_TO_STORAGE_MESSAGE = "Failed to connect to specified server, upload to default server";
    // https://www.wrike.com/open.htm?id=908593946
    private final static String TASK_NAME_CONTENTGAP_ESTDTRAFFIC = "ContentGap_EstdTraffic";
    // https://www.wrike.com/open.htm?id=913173890
    private final static String TASK_NAME_CONTENTGAP_COMPARERANK = "ContentGap_CompareRank";
    private final static int INTERVAL = 50000;

    // https://www.wrike.com/open.htm?id=919475248
    private final static String TASK_NAME_RESEARCHGRID_KEYWORDDETAIL = "ResearchGrid_KeywordDetail";
    // https://www.wrike.com/open.htm?id=922405732
    private final static String TASKNAME_RESEARCHGRID_TOPPAGES = "ResearchGrid_TopPages";
    // https://www.wrike.com/open.htm?id=922405358
    private final static String TASKNAME_RESEARCHGRID_RANKSUMMARY = "ResearchGrid_RankSummary";
    // https://www.wrike.com/open.htm?id=922406027
    private final static String TASKNAME_RESEARCHGRID_PAGESTREND = "ResearchGrid_PageTrend";
    private List<ApiTaskInfoEntity> apiTaskInfoEntitys;
    // https://www.wrike.com/open.htm?id=908593946
    public final static String TASKNAME_CONTENTFUSION_KEYWORD = "Contentfusion_Keyword";
    public final static String TASKNAME_CONTENTFUSION_KEYWORD_REALTIME = "Contentfusion_Keyword_Realtime";
    private static String TARGETPATH = "/home/<USER>/public_html/tasks/";
    private static DateFormat SDF = new SimpleDateFormat("yyyy-MM-dd");

//    private static String URL = "http://downloads.seoclarity.net/tasks";
    private static String URL_SEPARATOR = "/";
    private static String NOTASK = "Task is not ready";
    private  static int GETTASKINFOFLAG = 0;
    private static String LOG_DIR = "/tmp/contentGap/log/";
    private static String FAILED_SQL_LOG_NAME = "failed_sql.log";
    private static String SEND_TO = "<EMAIL>";
    private static String[] CC_TOS = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};
    //static private ApiTaskInstanceEntityDAO apiTaskInstanceEntityDAO;
    private List<String> taskGroups = new ArrayList<>();



    public static final String SINGLE_QUOTE = "'";
    private static final String  fulloutfilename = "/home/<USER>/outfiles/auto/";
    private static final boolean IS_DEBUG = true;
    private static long endIndex;
    private static boolean isFinished;
    private static double days;
    private static int hours =0;
    private static int today =1;
    public static void main(String args[]) {
        try{
//            seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
//            isFinished = false;
//            if(args.length <2){
//                System.out.println("input paramters count should be 2.");
//                return;
//            }
//            int rank = Integer.parseInt(args[1]);
//            System.out.println("=====args[0]:"+args[0]);
//            System.out.println("=====args[1]:"+args[1]);
//            System.out.println("=====getLastDay:"+ getLastClarityDbDay());
//            System.out.println("=====eta:"+ getEta(0.3));
//            String kw = "2019+dodge+challenger+r%2ft+392";
//            SeoClarityKeywordMonthlySearchEngineRelationEntity a = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.checkExist_rank100(kw,1,1);
//            System.out.println("=====a:"+ JSONUtil.toJsonStr(a));
            List<String> list = Arrays.asList("a", "b");
            for (int i = 0; i <list.size() ; i++) {
                list.set(i,"'"+list.get(i)+"'") ;
            }
            String test = String.join(",",list);
            System.out.println("=====test:"+test);
        }catch (Exception ex){
            ex.printStackTrace();
        }

    }

    private static String getLastClarityDbDay() throws Exception{
        Date currentDate = new Date();
        // 创建Calendar对象
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 将日期减少一个月
        calendar.add(Calendar.MONTH, -1);
        // 获取减少一个月后的日期
        Date lastMonthDate = calendar.getTime();
        SimpleDateFormat sp = new SimpleDateFormat("yyyyMM");
        return sp.format(lastMonthDate);
    }

    private static String getEta(double days)throws Exception{
        Date currentDate = new Date();
        // 创建Calendar对象
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 将日期减少一个月
        calendar.add(Calendar.DATE, (int)Math.ceil(days) -1);
        // 获取减少一个月后的日期
        Date lastMonthDate = calendar.getTime();
        SimpleDateFormat sp = new SimpleDateFormat("yyyy/MM/dd");
        return sp.format(lastMonthDate);
    }
}

