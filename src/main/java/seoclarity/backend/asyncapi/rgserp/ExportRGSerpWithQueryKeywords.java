package seoclarity.backend.asyncapi.rgserp;

import cn.hutool.core.text.csv.CsvWriteConfig;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.ZipUtil;
import com.google.gson.*;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import ru.yandex.clickhouse.ClickHouseArray;
import seoclarity.backend.dao.actonia.SolrInfoEntityDAO;
import seoclarity.backend.dao.actonia.UsersDAO;
import seoclarity.backend.dao.clickhouse.monthlyranking.CentralKeywordTokenizerDAO;
import seoclarity.backend.dao.clickhouse.monthlyranking.LwebMonthlyRankingDao;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-11-25
 * @path seoclarity.backend.asyncapi.topicexplorer.ExportRGSerpWithQueryKeywords
 * https://www.wrike.com/open.htm?id=1537185097
 */
public class ExportRGSerpWithQueryKeywords {
    private static final int MAX_PROCESS_KEYWORD_SIZE = 100;
    private static final int MAX_RESULT_COUNT = 1000;
    private static final int MAX_RETRY_COUNT = 200;
    private static final String DEFAULT_SORT_COLUMN = "avg_search_volume";
    private SimpleDateFormat yyyyMM = new SimpleDateFormat("yyyyMM");
    //	private static final String API_QUERY_URL = BaseWebserviceManager.API_ENDPOINT_IBM_INTRANET + "seoClarity/ckKeywordSv/keywordDetail";
    private static final String API_QUERY_URL = "seoClarity/ckKeywordSv/keywordDetail";
    private static final String API_QUERY_URL_SQL = "http://169.60.132.27:8183/seoClarity/contentFusionSerp/exportKeywordSERPForRG";
    private static final String INTERNAL_TYK_HEADER = "seoclarity_tky_internal_authorization";
    //	private static final int TEMP_OID = 4;
    private static final int TEMP_OID = 9688;
    private static final int DEFAULT_SV_MONTH_TREND_SIZE = 24;
    //	private static final String TEMP_FILE_PATH = "/tmp";
//	private static final String TEMP_FILE_PATH = "/home/<USER>/%OID%/";
    private static final String OUTPUT_FILE_NAME_PATTERN = "RG_SERP_FEATURE_EXTRACT_EXPORT_%YYYYMMDD%_";
    private static final String OUTPUT_FILE_NAME_SUFFIX = ".csv";
    //	private static final String FTP_OUT_PUT_FOLDER = "/home/<USER>/public_html/";
    private static final int MAX_TASK_RETRY_CNT = 10;
    private static final String[] HEADER = new String[]{
            "Keyword", "Shopping (PLA)", "PPC", "People Also Ask", "Popular Product","Price Schema OR Stock Schema In top 10","Star Rating Schema In top 10"
            ,"Video","Local Pack","Discussions and forums","Knowledge Graph","Knowledge Panel With Social Media Profile","Sitelinks","Images","News","AI Overview","Answer box"
            ,"Booking in SERP","Yelp Present","Twitter","Things to Know","Recipes","Job Pack","Hotel Pack"
    };
    private List<String> svMonthList = new ArrayList<>(0);

    private boolean isUseInternalTYKHeader = false;
    private Gson gson = new Gson();
    private JsonParser jsonParser = new JsonParser();
    private static final String DEFAULT_API_PARAM = "{" +
            "    \"accessToken\": \"%TOKEN%\"," +
            "    \"engineId\": %ENGINE_ID%," +
            "    \"languageId\": %LANGUAGE_ID%," +
            "    \"device\": \"d\"," +
            "    \"ownDomainId\":4661," +
            "    \"keywordName\":\"\"" +
            "}";

    private static boolean isTest = false;

    private UsersDAO usersDAO;
    private SolrInfoEntityDAO solrInfoEntityDAO;
    //	private ExportTableInfoEntityDAO exportTableInfoEntityDAO;
    private LwebMonthlyRankingDao lwebMonthlyRankingDao;
    private CentralKeywordTokenizerDAO centralKeywordTokenizerDAO;

    public ExportRGSerpWithQueryKeywords() {
        usersDAO = SpringBeanFactory.getBean("usersDAO");
        solrInfoEntityDAO = SpringBeanFactory.getBean("solrInfoEntityDAO");
//		exportTableInfoEntityDAO = SpringBeanFactory.getBean("exportTableInfoEntityDAO");
        lwebMonthlyRankingDao = SpringBeanFactory.getBean("lwebMonthlyRankingDao");
        centralKeywordTokenizerDAO = SpringBeanFactory.getBean("centralKeywordTokenizerDAO");
    }

    public static void main(String[] args) {

        ExportRGSerpWithQueryKeywords ins = new ExportRGSerpWithQueryKeywords();
        isTest = true;
        ins.process();
    }

    public void process() {
        File file = null;
        try {
            // get keyword list
            Set<String> decodedKeywords = getKeyWordsFromFileForCSV();
            for (String decodedKeyword : decodedKeywords) {
                System.out.println("========:keyword:=====" + decodedKeyword);
            }
            // create api query parameter
//            JsonObject jsonParam = createApiQueryParam(TEMP_OID, 1, 1, 202305, null, false, 1);
            JsonObject jsonParam = createApiQueryParam(1, 1);

            // create out file
            String fileNamePrefix = StringUtils.replace(OUTPUT_FILE_NAME_PATTERN, "%YYYYMMDD%", new SimpleDateFormat("yyyyMMdd").format(new Date()));
            try {
//				String tempPath = StringUtils.replace(TEMP_FILE_PATH, "%OID%", String.valueOf(exportTableInfoEntity.getOwnDomainId()));
//				String tempPath = CommonClarityDBExportExecutor.getLocalTempFilePath(exportTableInfoEntity.getOwnDomainId());
                String tempPath = "/home/<USER>/source/esn/";
                File pathDir = new File(tempPath);
                if (!pathDir.exists()) {
                    pathDir.mkdir();
                }
                file = File.createTempFile(fileNamePrefix, OUTPUT_FILE_NAME_SUFFIX, new File(tempPath));
            } catch (IOException e) {
                e.printStackTrace();
            }

            // out put rows
            int idx = 0;
            for (String kw : decodedKeywords) {
//				List<String[]> rows = executeApiQuery(jsonParam, kw, false, false);
                List<String[]> rows = getSqlByApiQuery(jsonParam, kw);
                if (rows != null && rows.size() > 0) {
                    idx++;
                    // append header
                    if (idx == 1) {
                        List<String> headerList = new ArrayList<>(Arrays.asList(HEADER));
                        outputToFile(rows, headerList.toArray(new String[headerList.size()]), file);
                    } else {
                        outputToFile(rows, null, file);
                    }
                } else {
                    System.out.println("===can not get result for kw:" + kw);
                }
            }

            // copy to FTP
            String outFIleName = file.getAbsolutePath();
            try {
                File zippedFile = ZipUtil.zip(file.getAbsolutePath());
                outFIleName = zippedFile.getAbsolutePath();
                System.out.println("===zip file:" + file.getAbsolutePath() + " -> " + outFIleName);
            } catch (Exception e) {
                e.printStackTrace();
            }
            String fileName = new File(outFIleName).getName();
            try {
//				FtpUtil.copyBySSHTo(FTP_OUT_PUT_FOLDER, outFIleName);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                // delete the unzipped file
                if (file.exists()) {
                    if (!isTest) {
                        file.delete();
                    }
                }
                // do not delete oput file in local server
                // export script would send this file
//				File temp = new File(outFIleName);
//				if (temp.exists()) {
                //				temp.delete();
//				}
            } catch (Exception e) {
                e.printStackTrace();
            }

//			exportTableInfoEntity.setFileName(fileName);
//
//			try {
//				System.out.println("Updating file name:" + fileName + ", resultCnt:" + idx);
//				exportTableInfoEntityDAO.updateExportTaskStatusById(exportTableInfoEntity.getId(), ExportTableInfoEntity.EXPORT_DONE, fileName, idx);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
        } catch (Exception e) {
//			System.out.println("=== execute task failed. info:" + exportTableInfoEntity.getId() + ", OID:" + exportTableInfoEntity.getOwnDomainId() + ", retryCnt:" + exportTableInfoEntity.getRetryCount());
//			exportTableInfoEntityDAO.updateExportTaskStatusByIdForRetry(exportTableInfoEntity.getId(), ExportTableInfoEntity.EXPORT_ERROR, exportTableInfoEntity.getRetryCount() + 1, "");
            e.printStackTrace();
            try {
                if (file != null) {
                    file.delete();
                }
            } catch (Exception e2) {
            }
        }
    }

    private List<String[]> executeApiQuery(JsonObject jsonParam, String kw, boolean includeDomainRank, boolean exactMatch) throws Exception {
        String kwListStr = gson.toJson(Arrays.asList(kw));
        JsonArray array = jsonParser.parse(kwListStr).getAsJsonArray();
        jsonParam.remove("queryMultiKeyword");
        jsonParam.add("queryMultiKeyword", array);

        long a = System.currentTimeMillis();
        System.out.println("=====start to query api, jsonParam:" + jsonParam.toString() + ", date:" + new Date());

        List<String[]> rowList = new ArrayList<>();
        String response = null;
        try {
            response = post(jsonParam);
            if (StringUtils.isNotBlank(response) && !StringUtils.equals(response, "[]")) {
                JsonObject obj = jsonParser.parse(response).getAsJsonObject();

                // get sv month trend list
                String lastRefreshDate = obj.get("lastRefreshDate").toString();
                System.out.println("===lastRefreshDate:" + lastRefreshDate);
                if (svMonthList.size() == 0) {
                    try {
                        Date endMonth = yyyyMM.parse(lastRefreshDate);
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(endMonth);
                        svMonthList.add(yyyyMM.format(cal.getTime()));
                        for (int i = 0; i < DEFAULT_SV_MONTH_TREND_SIZE - 1; i++) {
                            cal.add(Calendar.MONTH, -1);
                            String monthIdx = yyyyMM.format(cal.getTime());
                            svMonthList.add(monthIdx);
                        }
                        Collections.sort(svMonthList);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                JsonArray dataArray = obj.get("data").getAsJsonArray();
                if (dataArray != null && dataArray.size() > 0) {
                    for (int i = 0; i < dataArray.size(); i++) {
                        String[] row = parseLine(dataArray.get(i).getAsJsonObject(), kw, includeDomainRank);
                        if (row != null && row.length > 0) {
                            rowList.add(row);
                        }
                    }
                }
            }
            // https://www.wrike.com/open.htm?id=820417396
            // If keyword is not found in our Topic explorer data set, then we should show n/a in the search volume field
            if (rowList.size() == 0 && exactMatch) {
                String[] row = parseLine(null, kw, includeDomainRank);
                rowList.add(row);
            }
            System.out.println("===get response for kw:" + kw + ", rowList:" + rowList.size() + ", cost:" + (System.currentTimeMillis() - a) / 1000);

            return rowList;
        } catch (Exception e) {
            System.out.println("===execute query failed. jsonParam:" + jsonParam.toString() + ", response:" + response);
            throw new Exception(e);
        }
    }

    private List<String[]> getSqlByApiQuery(JsonObject jsonParam, String kw) throws Exception {
        System.out.println("jsonParam = " + jsonParam + ", kw = " + kw);
//        String kwListStr = gson.toJson(kw);
//        jsonParam.add("keywordName", jsonParser.parse(kwListStr));
        jsonParam.remove("keywordName");
        jsonParam.addProperty("keywordName", kw);
        long a = System.currentTimeMillis();
        System.out.println("=====start to query api, jsonParam:" + jsonParam.toString() + ", date:" + new Date());

        List<String[]> rowList = new ArrayList<>();
        String response = null;
        try {
            response = post(jsonParam);
            System.out.println("response = " + response);
            if (StringUtils.isNotBlank(response) && !StringUtils.equals(response, "[]")) {
                JsonObject obj = jsonParser.parse(response).getAsJsonObject();
                List<Map<String, Object>> dataArray = new ArrayList<>();
                try {
//                    dataArray = lwebMonthlyRankingDao.getSql(obj.get("data").getAsString());
                    dataArray = centralKeywordTokenizerDAO.getSql(obj.get("data").getAsString());
                } catch (Exception e) {
                    System.out.println("===execute sql failed, msg:" + e.getLocalizedMessage());
                    if (StringUtils.containsIgnoreCase(e.getLocalizedMessage(), ": Attempt to read after eof ")) {
                        int tryCnt = 0;
                        while (tryCnt < 3) {
                            tryCnt++;
                            try {
                                Thread.sleep(1000);
                                dataArray = centralKeywordTokenizerDAO.getSql(obj.get("data").getAsString());
                            } catch (Exception e1) {
                            }
                        }
                    }
                    e.printStackTrace();
                }

//				for (Map<String, Object> data : dataArray) {
//					for (Map.Entry<String, Object> entry : data.entrySet()) {
//						String key = entry.getKey();
//						Object value = entry.getValue();
//						System.out.println("Key: " + key + ", Value: " + value);
//					}
//				}


                if (dataArray != null && dataArray.size() > 0) {
                    for (int i = 0; i < dataArray.size(); i++) {
                        String[] row = parseLineForMap(dataArray.get(i), kw);
                        if (row != null && row.length > 0) {
                            rowList.add(row);
                        }
                    }
                }
            }
            // https://www.wrike.com/open.htm?id=820417396
            // If keyword is not found in our Topic explorer data set, then we should show n/a in the search volume field
//			if (rowList.size() == 0 && exactMatch) {
            if (rowList.size() == 0) {
//                String[] row = parseLine(null, kw, includeDomainRank);
                String[] row = parseLineForMap(null, kw);
                rowList.add(row);
            }
            System.out.println("===get response for kw:" + kw + ", rowList:" + rowList.size() + ", cost:" + (System.currentTimeMillis() - a) / 1000);

            return rowList;
        } catch (Exception e) {
            System.out.println("===execute query failed. jsonParam:" + jsonParam.toString() + ", response:" + response);
            throw new Exception(e);
        }
    }

    private String[] parseLine(JsonObject row, String queryKeyword, boolean includeDomainRank) {
        List<String> listRow = new ArrayList<>();
        listRow.add(queryKeyword);
        String emptyValue = "n/a";

        // default value
        String avg_search_volume = emptyValue;
        String keyword_name = queryKeyword;
        String similarityCnt = emptyValue;
        String rank = emptyValue;
        String url = emptyValue;
        String intentTypeStr = emptyValue;
        String cpc = emptyValue;
        List<String> svList = svMonthList.size() == 0 ? Collections.emptyList() : svMonthList.stream().map(x -> emptyValue).collect(Collectors.toList());

        if (row != null) {
            avg_search_volume = row.get("avg_search_volume").getAsString();
            if (StringUtils.equals(avg_search_volume, "-1")) {
                avg_search_volume = "-";
            }
            cpc = row.get("cpc") == null ? "0.0" : row.get("cpc").getAsString();
            intentTypeStr = "-";
            JsonElement intentType = row.get("intent_type");
            if (intentType != null) {
                JsonArray intent = intentType.getAsJsonArray();
                if (intent != null && intent.size() > 0) {
                    intentTypeStr = intent.get(0).getAsString();
                }
            }
            keyword_name = row.get("keyword_name").getAsString();
            similarityCnt = row.has("similarityCnt") ? row.get("similarityCnt").getAsString() : "-";
            //		String traffic_potential = row.get("traffic_potential").toString();

            rank = row.has("rank") ? row.get("rank").getAsString() : null;
            url = row.has("url") ? row.get("url").getAsString() : null;

            JsonArray monthEleList = row.get("month").getAsJsonArray();
            JsonArray svEleList = row.get("sv").getAsJsonArray();
            Map<String, String> svMap = new HashMap<>();
            svList = new ArrayList<>();

            try {
                for (int i = 0; i < monthEleList.size(); i++) {
                    svMap.put(monthEleList.get(i).getAsString(), svEleList.get(i).getAsString());
                }
                for (String month : svMonthList) {
                    svList.add(svMap.get(month) == null ? "-" : svMap.get(month));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        listRow.add(keyword_name);
        if (includeDomainRank) {
            listRow.add(rank);
            listRow.add(url);
        }
        listRow.add(avg_search_volume);
        listRow.add(intentTypeStr);
        listRow.add(cpc);
        listRow.add(similarityCnt);
        listRow.addAll(svList);

        return listRow.toArray(new String[listRow.size()]);
    }

    private String[] parseLineForMap(Map<String, Object> row, String queryKeyword) {
//		for (Map.Entry<String, Object> entry : row.entrySet()) {
//			String key = entry.getKey();
//			Object value = entry.getValue();
//			System.out.println("Key: " + key + ", Value: " + value);
//		}


        List<String> listRow = new ArrayList<>();
        listRow.add(queryKeyword);
        String emptyValue = "n/a";

        // default value
        String pla = emptyValue;
        String ppc = emptyValue;
        String peoplealsoask = emptyValue;
        String productList = emptyValue;
        String stockOrPrice = emptyValue;
        String ratingnumber = emptyValue;
        String video = emptyValue;
        String ll = emptyValue;
        String discussionAndForums = emptyValue;
        String knog = emptyValue;
        String socialInKg = emptyValue;
        String sitelinks = emptyValue;
        String img = emptyValue;
        String news = emptyValue;
        String ai_genai_search = emptyValue;
        String answerbox = emptyValue;
        String booking = emptyValue;
        String yelp = emptyValue;
        String twitter = emptyValue;
        String thingsToKnow = emptyValue;
        String recipes = emptyValue;
        String job = emptyValue;
        String hotel = emptyValue;

        if (row != null) {
            pla = row.get("pla").toString();
            ppc = row.get("ppc").toString();
            peoplealsoask = row.get("peoplealsoask").toString();
            productList = row.get("productList").toString();
            stockOrPrice = row.get("stockOrPrice").toString();
            ratingnumber = row.get("ratingnumber").toString();
            video = row.get("video").toString();
            ll = row.get("ll").toString();
            discussionAndForums = row.get("discussionAndForums").toString();
            knog = row.get("knog").toString();
            socialInKg = row.get("socialInKg").toString();
            sitelinks = row.get("sitelinks").toString();
            img = row.get("img").toString();
            news = row.get("news").toString();
            ai_genai_search = row.get("ai_genai_search").toString();
            answerbox = row.get("answerbox").toString();
            booking = row.get("booking").toString();
            yelp = row.get("yelp").toString();
            twitter = row.get("twitter").toString();
            thingsToKnow = row.get("thingsToKnow").toString();
            recipes = row.get("recipes").toString();
            job = row.get("job").toString();
            hotel = row.get("hotel").toString();
        }
        listRow.add(pla);
        listRow.add(ppc);
        listRow.add(peoplealsoask);
        listRow.add(productList);
        listRow.add(stockOrPrice);
        listRow.add(ratingnumber);
        listRow.add(video);
        listRow.add(ll);
        listRow.add(discussionAndForums);
        listRow.add(knog);
        listRow.add(socialInKg);
        listRow.add(sitelinks);
        listRow.add(img);
        listRow.add(news);
        listRow.add(ai_genai_search);
        listRow.add(answerbox);
        listRow.add(booking);
        listRow.add(yelp);
        listRow.add(twitter);
        listRow.add(thingsToKnow);
        listRow.add(recipes);
        listRow.add(job);
        listRow.add(hotel);

        return listRow.toArray(new String[listRow.size()]);
    }

    private String post(JsonObject jsonParam) {
//		String apiUrl = isTest ? API_QUERY_URL_TEST : API_QUERY_URL;
        String apiUrl = isTest ? API_QUERY_URL_SQL : API_QUERY_URL;
        Map<String, String> expandHeader = new HashMap<>();
        expandHeader.put("org.restlet.http.headers", INTERNAL_TYK_HEADER);
        int tryCnt = 0;
        do {
            tryCnt++;
            try {
                String response = HttpRequestUtils.queryWebServiceFunctionPostWithJsonParams(apiUrl, jsonParam.toString(), isUseInternalTYKHeader ? expandHeader : null);
                if (StringUtils.isNotBlank(response) && !StringUtils.contains(response, "502 Bad Gateway")) {
                    return response;
                } else if (tryCnt <= MAX_RETRY_COUNT) {
                    Thread.sleep(3000);
                    System.out.println("=resposne is empty, will re-try, tryCnt:" + tryCnt);
                }
            } catch (Exception e) {
                System.out.println("=api query faild, will re-try, tryCnt:" + tryCnt + ", error:" + e.getMessage());
                try {
                    Thread.sleep(3000);
                } catch (Exception e1) {
                }
            }
        } while (tryCnt <= MAX_RETRY_COUNT);
        return null;
    }

    private JsonObject createApiQueryParam(int engine, int language) {
        String token = "c09yxv13-opr3-d745-9734-8pu48420nj67";
        String paramStr = StringUtils.replace(DEFAULT_API_PARAM, "%TOKEN%", token);
        paramStr = StringUtils.replace(paramStr, "%ENGINE_ID%", String.valueOf(engine));
        paramStr = StringUtils.replace(paramStr, "%LANGUAGE_ID%", String.valueOf(language));
        JsonObject obj = jsonParser.parse(paramStr).getAsJsonObject();
        return obj;
    }

    private void outputToFile(List<String[]> rows, String[] hreader, File file) {
        CsvWriteConfig config = new CsvWriteConfig();
        config.setAlwaysDelimitText(true);
        CsvWriter writer = new CsvWriter(file, Charset.forName("UTF-8"), true, config);
        if (hreader != null) {
            writer.write(hreader);
        }
        if (rows != null) {
            writer.write(rows);
        }
        writer.close();
    }

    private static String formatQueryKeyword(String encodedKw) {
        String str = StringUtils.trim(encodedKw);
        try {
            str = URLDecoder.decode(str, "UTF-8");
            // https://www.wrike.com/open.htm?id=748411237
            // remove %C2%A0
            str = str.replaceAll("\\h+", " ");
            str = StringUtils.trim(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return StringUtils.lowerCase(str);
    }

    private static Set<String> getKeyWordsFromFile() {
        System.out.println("====getKeyWordsFromFile====");
        Set<String> dataSet = new LinkedHashSet<>();
        int counter = 0;
        try {
            InputStream inputStream = ExportRGSerpWithQueryKeywords.class.getResourceAsStream("/topicexplorer/T+ Exclusive Brands 5.9.xlsx");

            Workbook workbook = new XSSFWorkbook(inputStream);

            Sheet sheet = workbook.getSheetAt(0);

//			boolean skipHeader = true;

            for (Row row : sheet) {

//				if(skipHeader) {
//					skipHeader = false;
//					continue;
//				}

                Cell cell = row.getCell(0);

                if (cell != null) {
                    String value = cell.toString().trim();
                    dataSet.add(value);
                    counter++;
                }

                if (counter == 3) {
                    break;
                }

            }
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dataSet;
    }

    private static Set<String> getKeyWordsFromFileForCSV() {
        System.out.println("====getKeyWordsFromFile====");
        Set<String> dataSet = new LinkedHashSet<>();
        try {
            InputStream inputStream = ExportRGSerpWithQueryKeywords.class.getResourceAsStream("/rgserp/Gold Set 2.csv");
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));

            String line;
            while ((line = reader.readLine()) != null) {
                String value = line.trim();
                dataSet.add(value);
            }
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dataSet;
    }


//	public String executeQueryJson(String commandName, String sql) {
//		String err = null;
//		try {
//			return queryForClarityDBWithoutRetry(sql, commandName);
//		}catch (Exception e) {
//			System.out.println("===execute sql failed, msg:" + e.getLocalizedMessage());
//			if (StringUtils.containsIgnoreCase(e.getLocalizedMessage(), ": Attempt to read after eof ")) {
//				int tryCnt = 0;
//				while(tryCnt < 3) {
//					tryCnt++;
//					try {
//						Thread.sleep(1000);
//						return queryForClarityDBWithoutRetry(sql, commandName);
//					} catch (Exception e1) {}
//				}
//			}
//			e.printStackTrace();
//			err = "Query failed.";
////			CommonUtils.logClarityDBErrorMsg(e, commandName, clarityDBType);
//		}
//
//		@SuppressWarnings("unchecked")
//		Map<String, Object> result = new HashedMap();
//		result.put("meta", new ArrayList<>(1));
//		result.put("data", new ArrayList<>(1));
////		if (StringUtils.isNotBlank(err)) {
////			result.put(ClarityDBConstants.QUERY_ERROR_MSG_NAME, err);
////		}
//
//		return new Gson().toJson(result);
//	}

//	public String queryForClarityDBWithoutRetry(String sql, String command) {
//		if (StringUtils.isBlank(sql)) {
//			System.out.println("======== SQL is empty :" + sql);
//			return "[]";
//		}
//		long a = System.currentTimeMillis();
//		List<Object> objects = lwebMonthlyRankingDao.getSql(sql);
//		System.out.println("============JDBC command:" + command + " cost:" + (System.currentTimeMillis() - a));
//		Map<String, Object> result = new HashedMap();
//		result.put("meta", new ArrayList<>(0));
//		result.put("data", objects);
//
//		String str = null == objects ? "[]" : new GsonBuilder().disableHtmlEscaping().create().toJson(result);
//		return str;
//	}

}
