package seoclarity.backend.sia.url;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import seoclarity.backend.clarity360.exception.TaskException;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.SiaProjectEntityDAO;
import seoclarity.backend.dao.actonia.SiaUrlDetailEntityDAO;
import seoclarity.backend.dao.actonia.list.CdbListDetailEntityDAO;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.entity.SiaProjectEntity;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public class GatherSiaUrlDetail {
	
	private SiaProjectEntityDAO siaProjectEntityDAO;
	
	private SiaUrlDetailEntityDAO siaUrlDetailEntityDAO;
	
	private CdbListDetailEntityDAO cdbListDetailEntityDAO;
	
	private GroupTagEntityDAO groupTagEntityDAO;
	
	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;

	public GatherSiaUrlDetail() {
		// TODO Auto-generated constructor stub
		siaProjectEntityDAO = SpringBeanFactory.getBean("siaProjectEntityDAO");
		siaUrlDetailEntityDAO = SpringBeanFactory.getBean("siaUrlDetailEntityDAO");
		cdbListDetailEntityDAO = SpringBeanFactory.getBean("cdbListDetailEntityDAO");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		disSiteCrawlDoc1Dao = SpringBeanFactory.getBean("disSiteCrawlDoc1Dao");
	}
	
	private static Integer projectId;

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		
		if (args != null && args.length >= 1) {
			// backprocess by projectId
			projectId = NumberUtils.toInt(args[0]);
		}
		
		System.out.println("==== projectId:" + projectId);
		
		GatherSiaUrlDetail gatherSiaUrlDetail = new GatherSiaUrlDetail();
		gatherSiaUrlDetail.process();
	}
	
	
	private void process() {
		
		List<SiaProjectEntity> projectList;
		
		if (projectId > 0) {
			projectList = siaProjectEntityDAO.getProjectNeedProcess(projectId);
		} else {
			projectList = siaProjectEntityDAO.getProjectNeedProcess();
		}
		
		for(SiaProjectEntity siaProjectEntity : projectList) {
			
			siaProjectEntityDAO.updateProjectStatus(siaProjectEntity.getId(), SiaProjectEntity.STATUS_COLLECT_DETAIL_PENDING);
			
			try {
				
				System.out.println("==== processing project:" + siaProjectEntity.getId() + ", sourceType:" + siaProjectEntity.getSourceType());
				if (siaProjectEntity.getSourceType()  == SiaProjectEntity.SOURCE_TYPE_URL_FROM_UI) {
					System.out.println("==== source type :" + siaProjectEntity.getSourceType() + ", detail should pending by UI side directly");
					continue;
				}
				
				processDetails(siaProjectEntity);
				
				siaProjectEntityDAO.updateProjectStatus(siaProjectEntity.getId(), SiaProjectEntity.STATUS_COLLECT_DETAIL_COMLETED);
			} catch (Exception e) {
				e.printStackTrace();
				
				siaProjectEntityDAO.updateProjectStatus(siaProjectEntity.getId(), SiaProjectEntity.STATUS_COLLECT_DETAIL_ERROR);
			}
		}
	}
	
	private static final String SOURCE_SPLIT = "!_!";
	private static final Integer PAGE_SIZE = 500;
	private final static String LOCAL_PATH = "/home/<USER>/siaUrl/";
	
	private void processDetails(SiaProjectEntity siaProjectEntity) throws Exception {
		
		if (siaProjectEntity.getSourceType() == SiaProjectEntity.SOURCE_TYPE_URL_FROM_FILE) {
			processFileFromFtp(siaProjectEntity);
		} else {
			String[] sourceArray = StringUtils.split(siaProjectEntity.getSource(), SOURCE_SPLIT);
			
			Integer pageNum = 1;
			List<String> urlList = new ArrayList<String>();
			while (true) {
				
				System.out.println("=== processing page:" + pageNum + ", page size:" + PAGE_SIZE);
				
				if (siaProjectEntity.getSourceType() == SiaProjectEntity.SOURCE_TYPE_CDB_LIST) {
					urlList = cdbListDetailEntityDAO.getListByLidList(sourceArray, siaProjectEntity.getOwnDomainId(), (pageNum - 1) * PAGE_SIZE, PAGE_SIZE);
				} else if (siaProjectEntity.getSourceType() == SiaProjectEntity.SOURCE_TYPE_PAGE_TAG) {
					urlList = groupTagEntityDAO.getListByTagIdArray(sourceArray, siaProjectEntity.getOwnDomainId(), (pageNum - 1) * PAGE_SIZE, PAGE_SIZE);
				} else if (siaProjectEntity.getSourceType() == SiaProjectEntity.SOURCE_TYPE_CRAWL_ID) {
					urlList = disSiteCrawlDoc1Dao.getListByTagIdArray(sourceArray, siaProjectEntity.getOwnDomainId(), (pageNum - 1) * PAGE_SIZE, PAGE_SIZE);
				}
				
				if(CollectionUtils.isEmpty(urlList)) {
					break;
				}
				
				siaUrlDetailEntityDAO.batchInsert(urlList, siaProjectEntity.getOwnDomainId(), siaProjectEntity.getId());

				pageNum++;
				if (urlList.size() < PAGE_SIZE) {
					break;
				}
			}
		}
	}
	
	
	private void processFileFromFtp(SiaProjectEntity siaProjectEntity) throws Exception {
		String filePath = siaProjectEntity.getSource();
		
		FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, filePath, LOCAL_PATH, 0, 3);
		
		String localFilePath = LOCAL_PATH + StringUtils.substringAfterLast(filePath, "/");
		File localFile = new File(localFilePath);
		
		if (localFile == null || !localFile.isFile()) {
			throw new TaskException("Local file not found!" + localFilePath);
		}
		
		BufferedReader bf = new BufferedReader(new FileReader(localFilePath));
		String content = "";
		Integer pageNum = 1;
		
		List<String> urlList = new ArrayList<String>();
		while (content != null) {
			content = bf.readLine();
			urlList.add(content);
			if (urlList.size() >= PAGE_SIZE) {
				System.out.println("=== processing page:" + pageNum + ", page size:" + PAGE_SIZE);
				siaUrlDetailEntityDAO.batchInsert(urlList, siaProjectEntity.getOwnDomainId(), siaProjectEntity.getId());
				urlList.clear();
				pageNum++;
			}
		}
		
		if (urlList.size() > 0) {
			siaUrlDetailEntityDAO.batchInsert(urlList, siaProjectEntity.getOwnDomainId(), siaProjectEntity.getId());
		}
		
		bf.close();
	}

}
