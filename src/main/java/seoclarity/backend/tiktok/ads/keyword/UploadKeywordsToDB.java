package seoclarity.backend.tiktok.ads.keyword;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import seoclarity.backend.dao.actonia.tiktok.TiktokKeywordDao;
import seoclarity.backend.entity.actonia.tiktok.TiktokKeyword;
import seoclarity.backend.tiktok.ads.UploadTiktokToDB;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class UploadKeywordsToDB extends UploadTiktokToDB<TiktokKeyword> {

    private static final String DEFAULT_DATA_DIR = "/data/tiktok_ads/keyword";
    private final TiktokKeywordDao tikTokKeywordDao;

    public UploadKeywordsToDB(int crawlDate, String dataDir) {
        super(dataDir, crawlDate);
        tikTokKeywordDao = SpringBeanFactory.getBean("tiktokKeywordDao");
    }

    public static void main(String[] args) {
        int crawlDate = Integer.parseInt(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        if (args.length > 0) {
            // if a date is passed in, use it
            crawlDate = Integer.parseInt(args[0]);
        }
        final String dataDir = args.length > 1 ? args[1] : DEFAULT_DATA_DIR;
        UploadKeywordsToDB uploadKeywordsToDB = new UploadKeywordsToDB(crawlDate, dataDir);
        uploadKeywordsToDB.upload();
    }

    @Override
    protected void batchInsert(List<TiktokKeyword> needUploadList) {
        tikTokKeywordDao.batchInsert(needUploadList);
    }


    @Override
    protected List<TiktokKeyword> readDataFile(File file) {
        return FileUtil.readLines(file, StandardCharsets.UTF_8)
                .stream().flatMap(line -> {
                    final TiktokKeywordVO tikTokKeywordVO = JSONUtil.toBean(line, TiktokKeywordVO.class);
                    List<TiktokKeyword> tiktokKeywords = tikTokKeywordVO.convertKeywordListToTikTokKeywordList(crawlWeek, crawlDate);
                    return tiktokKeywords.stream();
                })
                .collect(Collectors.toList());
    }

}
