package seoclarity.backend.tiktok.ads;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class UploadTiktokToDB<T> {

    protected final String DATA_DIR;

    // crawlWeek: yyyyWW
    protected final Integer crawlWeek;
    // crawlDate: yyyyMMdd
    protected final Integer crawlDate;
    protected final DateTime crawlDateTime;

    protected UploadTiktokToDB(String dataDir, Integer crawlDate) {
        DATA_DIR = dataDir;
        this.crawlDate = crawlDate;
        this.crawlDateTime = DateUtil.parse(String.valueOf(crawlDate), DatePattern.PURE_DATE_FORMAT);
        this.crawlWeek = Integer.valueOf(String.format("%s%02d", crawlDateTime.year(), crawlDateTime.weekOfYear()));
    }

    public void upload() {
        final Optional<File> fileOptional = getDataFile(crawlDateTime.toDateStr());
        if (!fileOptional.isPresent()) {
            log.error("File not found for date: {}", this.crawlDate);
            return;
        }
        final List<T> needUploadList = readDataFile(fileOptional.get());
        if (needUploadList.isEmpty()) {
            log.error("No keywords in fileOptional: {}", fileOptional.get().getAbsolutePath());
            return;
        }
        log.info("Need upload list size: {}", needUploadList.size());
        batchInsert(needUploadList);
        log.info("Finish upload keywords for date: {}", this.crawlDate);
    }

    protected abstract void batchInsert(List<T> needUploadList);


    /**
     * Reads a Scribe file and converts its contents to a list of objects.
     *
     * @param file the file to read
     * @param voType the class type of the vo objects in the file
     * @param convertFunction the function to convert the vo objects to the entity type
     * @return a list of converted objects
     */
    protected <V> List<T> readDataFile(File file, Class<V> voType, Function<V, T> convertFunction) {
        return FileUtil.readLines(file, StandardCharsets.UTF_8)
                .stream().map(line -> {
                    final V vo = JSONUtil.toBean(line, voType, true);
                    try {
                        return convertFunction.apply(vo);
                    } catch (Exception e) {
                        log.error("Failed to convert: {}", line, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    protected Optional<File> getDataFile(String date) {
        final File[] files = FileUtil.ls(DATA_DIR);
        if (files == null) {
            log.error("No files in folder: {}", DATA_DIR);
            return Optional.empty();
        }
        return Arrays.stream(files)
                .filter(file -> StrUtil.containsIgnoreCase(file.getName(), date))
                .findFirst();
    }

    protected abstract List<T> readDataFile(File file);
}
