package seoclarity.backend.sender.pagespeed;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.DeleteQueueRequest;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.TargetUrlEntityDAO;
import seoclarity.backend.dao.actonia.pagespeed.ManagedTargetUrlDailyLogEntityDAO;
import seoclarity.backend.dao.actonia.pagespeed.PageSpeedSendDetailEntityDAO;
import seoclarity.backend.dao.actonia.pagespeed.PageSpeedSendInfoEntityDAO;
import seoclarity.backend.dao.actonia.pagespeed.PageSpeedShuffleEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClaritySearchEngineEntityDAO;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.entity.pagespeed.PageSpeedSendInfoEntity;
import seoclarity.backend.entity.pagespeed.PageSpeedShuffleEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static seoclarity.backend.entity.pagespeed.PageSpeedSendInfoEntity.*;

/**
 * <AUTHOR>
 * @date 2018-02-27
 * com.actonia.clarity_data_sender.sqs.SendPageSpeedTargetUrlsSQS
 * /usr/local/maven/apache-maven-3.3.3/bin/mvn exec:java -Dexec.mainClass="com.actonia.clarity_data_sender.sqs.SendPageSpeedTargetUrlsSQS" -Dexec.args="PAGE_SPEED_URL 4765" -Dexec.cleanupDaemonThreads=false
 */
@CommonsLog
public class SendPageSpeedTargetUrlsSQS {

    private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
    private static final String GOOGLE_PAGESPEED_URL = "https://www.googleapis.com/pagespeedonline/v5/runPagespeed?filter_third_party_resources=false&url=";

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private TargetUrlEntityDAO targetUrlEntityDAO;
    private ManagedTargetUrlDailyLogEntityDAO managedTargetUrlDailyLogEntityDAO;
    private PageSpeedSendInfoEntityDAO pageSpeedSendInfoEntityDAO;
    private PageSpeedShuffleEntityDAO pageSpeedShuffleEntityDAO;
    private PageSpeedSendDetailEntityDAO pageSpeedSendDetailEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ScKeywordRankManager scKeywordRankManager;
    private SeoClaritySearchEngineEntityDAO seoClaritySearchEngineEntityDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    private static final int SHUFFLE_ID_LENGTH = 12;

    public static final int EXPEDIA_DOMIAN_ID = 4765;
    private static final int MAX_TAG_LENGTH = 1000;
    private static final String SEND_TYPE_DAILY = "daily";
    private static final String SEND_TYPE_WEEKLY = "weekly";
    private static final String SEND_TYPE_MONTHLY = "monthly";

    public static final int DEVICE_MOBILE = 0;
    public static final int DEVICE_DESKTOP = 1;
    private static String sqsUrlName = "PAGE_SPEED_DESKTOP_DAILY_URL";

    private static String sqsSendType = "daily"; //daily;monthly

    private static int sqsUrlType = 1; //0:mobile;1:desktop

    private static Boolean isExpedia = false;

    private static Date queryDate = new Date();

    private static int crawlMonth;

    private static int sendInfoId;

    private static int category;

    private static int monthlySendCount = 400000;

    private static int pageSize = 2000;

    private static Boolean isComplete = false;

    private static String sendProcessDate;

    private long endShuffleId;

    public static List<Integer> processDomainList = new ArrayList<Integer>();
    Set<Integer> desktopDomainIdList = new HashSet<>();
    Set<Integer> notDesktopDomainIdList = new HashSet<>();
    Set<Integer> mobileDomainList = new HashSet<>();
    Set<Integer> notMobileDomainList = new HashSet<>();
    //added new column flag to disable pagespeed, cancel the list  https://www.wrike.com/open.htm?id=621637191
    public final static List<Integer> SKIP_DOMAIN_LIST = new ArrayList<Integer>();
    //https://www.wrike.com/open.htm?id=277277052
//    public final static List<Integer> SKIP_DOMAIN_LIST = new ArrayList<Integer>(Arrays.asList(
//            475, 4765,
//            1011, 1012, 1013, 1839, 1845, 1846, 1847, 1848, 1849, 1860, 1897, 1901, 1974, 1978, 1979, 1980, 1981, 1982, 1983,
//            2420, 2558, 2654,
//            3534, 3683, 3909, 4344, 4345, 4346, 4347, 4348, 4355, 4721, 4766,
//            5508, 5566, 5570, 5571, 5576, 5577, 5578, 5579, 5580, 5581, 5582, 5583, 5585, 5732, 5881, 5942, 5943,
//            6056, 6057, 6613, 6618, 6619, 6620, 6621, 6622, 6623, 6624, 6625, 6626, 6669,
//            7001, 7049, 7055, 7129,6686,
//            601, 602));

    public SendPageSpeedTargetUrlsSQS() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        managedTargetUrlDailyLogEntityDAO = SpringBeanFactory.getBean("managedTargetUrlDailyLogEntityDAO");
        pageSpeedSendInfoEntityDAO = SpringBeanFactory.getBean("pageSpeedSendInfoEntityDAO");
        pageSpeedSendDetailEntityDAO = SpringBeanFactory.getBean("pageSpeedSendDetailEntityDAO");
        pageSpeedShuffleEntityDAO = SpringBeanFactory.getBean("pageSpeedShuffleEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        seoClaritySearchEngineEntityDAO = SpringBeanFactory.getBean("seoClaritySearchEngineEntityDAO");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    public static void main(String[] args) throws Exception{

//        sqsUrlType = 1;
//        sqsUrlName = "PAGE_SPEED_DESKTOP_WEEKLY_URL_V5";
//        sqsSendType = "weekly";

        sqsUrlName = args[0];
        sqsSendType = args[1];
        sqsUrlType = Integer.parseInt(args[2]);

        if(args.length >= 4){
            sendProcessDate = args[3];
            queryDate = FormatUtils.toDate(args[3], "yyyy-MM-dd");
        }

        if(args.length >= 5 && StringUtils.containsIgnoreCase(args[4], ",")){
            System.out.println("========== domain list:" + args[4] + " ===============");
            String[] domainArr = StringUtils.split(args[4], ",");
            for(String oidStr: domainArr){
                processDomainList.add(Integer.parseInt(oidStr));
            }
        }

        crawlMonth = Integer.parseInt(FormatUtils.formatDate(queryDate, FormatUtils.MONTH_PATTERN));
        if (SEND_TYPE_MONTHLY.equals(sqsSendType)) {
            isExpedia = false;
//            isExpedia = Boolean.parseBoolean(args[3]);
        } else if (SEND_TYPE_WEEKLY.equals(sqsSendType)) {
            isExpedia = false;
            crawlMonth = Integer.parseInt(FormatUtils.formatDate(queryDate, FormatUtils.DATE_FORMAT_YYYYMMDD));
        }
//        crawlMonth = 20210314;
//        if(args.length > 3 && StringUtils.isNotBlank(args[3])){
//            crawlMonth = Integer.parseInt(args[3]);
//        }


        if (isExpedia) {
            category = PageSpeedSendInfoEntity.CATEGORY_EXPEDIA;
            monthlySendCount = 50000;
        } else {
            category = PageSpeedSendInfoEntity.CATEGORY_ALL_OTHERS;
        }

//		System.out.println("SQS_TYPE:" + SQS_TYPE + "\n executeDomainSet:" + executeDomainSet + "\n notExecuteDomainSet:" + notExecuteDomainSet);

        SendPageSpeedTargetUrlsSQS ins = new SendPageSpeedTargetUrlsSQS();
        threadPool.init();
        CommonUtils.initThreads(50);

        ins.process(sendProcessDate);

        threadPool.destroy();
    }

    private void process(String sendProcessDate) {
        // init sqs
        AmazonSQS amazonSQS = null;
        String queryUrl = null;

        Date date = null;
        if(StringUtils.isBlank(sendProcessDate)){
            date = CacheModleFactory.getInstance().getWorkDate();
        }else {
            date = FormatUtils.toDate(sendProcessDate, "yyyy-MM-dd");
        }


        //-------set date
//        String dateStr = "20210321";
//        try {
//            date = new SimpleDateFormat("yyyyMMdd").parse(dateStr);
//        } catch (Exception e) {
//
//        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int currentStartHour = cal.get(Calendar.HOUR_OF_DAY);

        Boolean isDeleteQueue = null;

        System.out.println("###Current day:" + date + ", startHour:" + currentStartHour);

        try {
            amazonSQS = SQSUtils.getAmazonSQS();

            System.out.println("###Will start to send SQS  queryUrl:" + queryUrl
                    + ", currentProcessDate:" + CacheModleFactory.getInstance().getWorkDate()
                    + ", workDate:" + date
                    + ", sqsName:" + sqsUrlName
                    + ", deleteAllQueue:" + isDeleteQueue + ", 2 Minitues to choose continue...");


            queryUrl = SQSUtils.createQueue(sqsUrlName, amazonSQS);

            //send to queue
            processTargetUrls(amazonSQS, queryUrl, date);

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Send keyword to AmazonSQS failed!");
            logStatus(sqsUrlName, queryUrl, "Failed", e.getMessage());
            return;
        }

    }

    private void processTargetUrls(AmazonSQS amazonSQS, String queryUrl, Date workDate) throws Exception {
        SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
        int processDate = Integer.valueOf(yyyyMMdd.format(workDate));

        processToSendUrls(processDate, workDate, amazonSQS, queryUrl);

    }

    private void processToSendUrls(int processDate, Date workDate, AmazonSQS amazonSQS, String queryUrl) throws Exception {

        List<TargetUrlEntity> sendTargetUrlList = new ArrayList<TargetUrlEntity>();
        PageSpeedSendInfoEntity insertPageSpeedSendInfoEntity = new PageSpeedSendInfoEntity();
        if (sqsSendType.equalsIgnoreCase(SEND_TYPE_DAILY)) {
            Integer crawlDate = Integer.parseInt(FormatUtils.formatDate(queryDate, FormatUtils.DATE_FORMAT_YYYYMMDD));
            Integer isSend = pageSpeedSendInfoEntityDAO.getDailyCrawlCompleted(crawlDate, sqsUrlType);
            System.out.println("************* daily isSend: " + isSend + " ,crawlDate: " + crawlDate + " ,sqsUrlType : " + sqsUrlType);

            if(FormatUtils.is2ndOfMonth(queryDate)){//2nd of month, clean up sqs,then send
                log.info("===is 2nd, cleanupQ:" + crawlDate);
                amazonSQS.deleteQueue(new DeleteQueueRequest(queryUrl));
                Thread.sleep(10000);
                queryUrl = SQSUtils.createQueue(sqsUrlName, amazonSQS);
            }

            if (isSend.equals(0)) {
                List<Integer> processingDomainList = managedTargetUrlDailyLogEntityDAO.getProcessingDomain(queryDate, SKIP_DOMAIN_LIST, sqsUrlType);
                int urlCount = 0;
                if (CollectionUtils.isNotEmpty(processingDomainList)) {
                    for (Integer domainId : processingDomainList) {
                        List<TargetUrlEntity> domainTargetUrlList = new ArrayList<TargetUrlEntity>();
                        List<TargetUrlEntity> targetUrlEntityList = managedTargetUrlDailyLogEntityDAO.queryForDaily(queryDate, domainId);
                        System.out.println("===send domain:" + domainId + ",targetUrlEntityList: " + targetUrlEntityList.size());
                        domainTargetUrlList = matchUrls(targetUrlEntityList);
                        if (CollectionUtils.isNotEmpty(domainTargetUrlList)) {
                            sendTargetUrlList.addAll(domainTargetUrlList);
                            urlCount += targetUrlEntityList.size();
                        }
                    }
                    insertPageSpeedSendInfoEntity =
                            setPageSpeedSendInfoEntityValue(urlCount, PageSpeedSendInfoEntity.SEND_STATUS_NEWLY);
                } else {
                    insertPageSpeedSendInfoEntity =
                            setPageSpeedSendInfoEntityValue(urlCount, PageSpeedSendInfoEntity.SEND_STATUS_COMPLETED);
                }
                sendInfoId = pageSpeedSendInfoEntityDAO.insert(insertPageSpeedSendInfoEntity);

                List<PageSpeedShuffleEntity> pageSpeedShuffleEntityList = new ArrayList<PageSpeedShuffleEntity>();
                for (TargetUrlEntity urlEntity : sendTargetUrlList) {
                    PageSpeedShuffleEntity pageSpeedShuffleEntity = new PageSpeedShuffleEntity();
                    pageSpeedShuffleEntity.setSendInfoId(sendInfoId);
                    pageSpeedShuffleEntity.setDevice(sqsUrlType);
                    pageSpeedShuffleEntity.setCrawlMonth(crawlMonth);
                    pageSpeedShuffleEntity.setOwnDomainId(urlEntity.getOwnDomainId());
                    pageSpeedShuffleEntity.setUrlId(urlEntity.getId().longValue());
                    pageSpeedShuffleEntity.setUrl(urlEntity.getUrl());
                    pageSpeedShuffleEntity.setShuffleId(getShuffledId(urlEntity.getId()));
                    pageSpeedShuffleEntity.setSendDate(new Date());
                    pageSpeedShuffleEntityList.add(pageSpeedShuffleEntity);
                }
                System.out.println(" ******** waiting for insert pageSpeedShuffle ~~~ ");
                pageSpeedShuffleEntityDAO.insertForBatch(pageSpeedShuffleEntityList);
                isComplete = true;
            } else {
                System.out.println(" daily was sent , exit !!! ");
            }

        } else if (sqsSendType.equalsIgnoreCase(SEND_TYPE_WEEKLY)) {

            boolean isSunday = false;
            Calendar cal = Calendar.getInstance();
            cal.setTime(workDate);

            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                isSunday = true;
            }else {
                workDate = CommonUtils.getLastSunday(workDate);
                crawlMonth = Integer.parseInt(FormatUtils.formatDate(workDate, FormatUtils.DATE_FORMAT_YYYYMMDD));
                processDate = crawlMonth;
                isSunday = true;
            }

            System.out.println("===isSunday:" + isSunday + ",crawlMonth:" + crawlMonth);
            if(isSunday){
                SKIP_DOMAIN_LIST.add(4);
                PageSpeedSendInfoEntity weeklyInfo =
                        pageSpeedSendInfoEntityDAO.getMonthlyCrawlNotCompleted(crawlMonth, sqsUrlType, category, FREQUENCY_WEEKLY);
                if (weeklyInfo == null) {//no record : insert new or exit
//                    Integer maxCrawlMonth = pageSpeedSendInfoEntityDAO.getMaxMonthlyCrawlCompleted(sqsUrlType, category, PageSpeedSendInfoEntity.FREQUENCY_WEEKLY);

                    List<OwnDomainEntity> weeklyPsDomainList = ownDomainEntityDAO.getWeeklyPageSpeedDomainList(processDomainList, sqsUrlType);
                    if(CollectionUtils.isEmpty(weeklyPsDomainList)){
                        System.out.println("====no weekly ps domain exist, exit!!");
                        return;
                    }

                    int pageSize = 3;
                    for(OwnDomainEntity ownDomainEntity : weeklyPsDomainList){
                        int domainId = ownDomainEntity.getId();
                        String pagespeedClientApikey = ScStringEncryptor.decrypt(ownDomainEntity.getPagespeedClientApikey());
                        System.out.println("====process domain:" + domainId + ",pagespeedClientApikey:" + pagespeedClientApikey);
//                        String pagespeedClientApikey = ownDomainEntity.getPagespeedClientApikey();
                        List<String> urlList = targetUrlEntityDAO.getUrlListByDomain(domainId, pageSize);
                        if(CollectionUtils.isEmpty(urlList)){
                            System.out.println("==urlList empty, skip domain:" + domainId);
                            continue;
                        }
                        for(String url: urlList){
                            String encodeUrl;
                            try {
                                encodeUrl = URLEncoder.encode(url, "utf-8");
                            } catch (Exception e) {
                                e.printStackTrace();
                                encodeUrl = url;
                            }
                            String requestUrl = GOOGLE_PAGESPEED_URL + encodeUrl + "&strategy=desktop&key=" + pagespeedClientApikey;
                            System.out.println("==requestUrl:" + requestUrl);
                            OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(300, TimeUnit.SECONDS)
                                    .readTimeout(300, TimeUnit.SECONDS)
                                    .build();

                            Response response = client.newCall(new Request.Builder().url(requestUrl).get().build()).execute();
                            if (!response.isSuccessful()) {
                                String responseBody = response.body().string();
                                log.info("====responseBody:" + responseBody);
                                try {
                                    JSONObject jsonObject = JSONObject.parseObject(responseBody);
                                    String errorStr = jsonObject.get("error").toString();
                                    ResponseErrorEntity responseErrorEntity = new Gson().fromJson(errorStr, ResponseErrorEntity.class);
                                    System.out.println("===333" + JSON.toJSONString(responseErrorEntity));
                                    if(responseErrorEntity.getCode().equals(400) && responseErrorEntity.getMessage().contains("API key not valid")){
                                        SKIP_DOMAIN_LIST.add(domainId);
                                        int varCharLength = responseBody.getBytes("utf-8").length;
                                        if(varCharLength >= MAX_TAG_LENGTH){
                                            responseBody = responseBody.substring(0, MAX_TAG_LENGTH);
                                        }
                                        ownDomainSettingEntityDAO.updatePsErrorMsg(domainId, responseBody);
                                        System.out.println("==update error msg success");
                                        break;
                                    }else {
                                        System.out.println("==other error");
                                    }
                                }catch (Exception e){
                                    e.printStackTrace();
                                    System.out.println("==catchError");
                                }

                            }else {
                                System.out.println("==good key");
                                break;
                            }
                        }

                    }

                    sendTargetUrlList = addNewSendInfo(workDate, sendTargetUrlList);

                }else {
                    System.out.println("====weeklyInfo exist ");

                    if(CollectionUtils.isNotEmpty(processDomainList)){

                        List<OwnDomainEntity> weeklyPsDomainList = ownDomainEntityDAO.getWeeklyPageSpeedDomainList(processDomainList, sqsUrlType);
                        if(CollectionUtils.isEmpty(weeklyPsDomainList)){
                            System.out.println("====no weekly ps domain exist, exit!!");
                            return;
                        }

                        int pageSize = 3;
                        for(OwnDomainEntity ownDomainEntity : weeklyPsDomainList){
                            int domainId = ownDomainEntity.getId();
                            String pagespeedClientApikey = ScStringEncryptor.decrypt(ownDomainEntity.getPagespeedClientApikey());
                            System.out.println("====process domain:" + domainId + ",pagespeedClientApikey:" + pagespeedClientApikey);
//                        String pagespeedClientApikey = ownDomainEntity.getPagespeedClientApikey();
                            List<String> urlList = targetUrlEntityDAO.getUrlListByDomain(domainId, pageSize);
                            if(CollectionUtils.isEmpty(urlList)){
                                System.out.println("==urlList empty, skip domain:" + domainId);
                                continue;
                            }
                            for(String url: urlList){
                                String encodeUrl;
                                try {
                                    encodeUrl = URLEncoder.encode(url, "utf-8");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    encodeUrl = url;
                                }
                                String requestUrl = GOOGLE_PAGESPEED_URL + encodeUrl + "&strategy=desktop&key=" + pagespeedClientApikey;
                                System.out.println("==requestUrl:" + requestUrl);
                                OkHttpClient client = new OkHttpClient().newBuilder().connectTimeout(60, TimeUnit.SECONDS)
                                        .readTimeout(60, TimeUnit.SECONDS)
                                        .build();

                                Response response = client.newCall(new Request.Builder().url(requestUrl).get().build()).execute();
                                if (!response.isSuccessful()) {
                                    String responseBody = response.body().string();
                                    JSONObject jsonObject = JSONObject.parseObject(responseBody);
                                    String errorStr = jsonObject.get("error").toString();
                                    ResponseErrorEntity responseErrorEntity = new Gson().fromJson(errorStr, ResponseErrorEntity.class);
                                    System.out.println("===333" + JSON.toJSONString(responseErrorEntity));
                                    if(responseErrorEntity.getCode().equals(400) && responseErrorEntity.getMessage().contains("API key not valid")){
                                        SKIP_DOMAIN_LIST.add(domainId);
                                        int varCharLength = responseBody.getBytes("utf-8").length;
                                        if(varCharLength >= MAX_TAG_LENGTH){
                                            responseBody = responseBody.substring(0, MAX_TAG_LENGTH);
                                        }
                                        ownDomainSettingEntityDAO.updatePsErrorMsg(domainId, responseBody);
                                        System.out.println("==update error msg success");
                                        break;
                                    }else {
                                        System.out.println("==other error");
                                    }

                                }else {
                                    System.out.println("==good key");
                                    break;
                                }
                            }

                        }
                        sendTargetUrlList = addNewSendInfo(workDate, sendTargetUrlList);
                    }

                }

            }else {
                System.out.println("====not Sunday exist!");

            }

        } else if (sqsSendType.equalsIgnoreCase(SEND_TYPE_MONTHLY)) {//monthly

            //page_speed_send_info status
            PageSpeedSendInfoEntity monthlyIsNotCompleted =
                    pageSpeedSendInfoEntityDAO.getMonthlyCrawlNotCompleted(crawlMonth, sqsUrlType, category, FREQUENCY_MONTHLY);

            if (monthlyIsNotCompleted == null) {//no record : insert new or exit

                Integer maxCrawlMonth = pageSpeedSendInfoEntityDAO.getMaxMonthlyCrawlCompleted(sqsUrlType, category, FREQUENCY_MONTHLY);

                if (maxCrawlMonth != null && maxCrawlMonth.equals(crawlMonth)) {//completed
                    System.out.println(" ********  monthly crawl completed  for  "
                            + crawlMonth + ",maxCrawlMonth:" + maxCrawlMonth + " **device**  " + sqsUrlType + " **category** " + category);
                    return;
                } else if (maxCrawlMonth != null && !maxCrawlMonth.equals(crawlMonth)) {

                    Date maxCrawlMonthDate = FormatUtils.toDate(maxCrawlMonth.toString(), FormatUtils.MONTH_PATTERN);
                    String nextMonth = FormatUtils.getPreMonth(maxCrawlMonthDate, 1, FormatUtils.MONTH_PATTERN);
                    PageSpeedSendInfoEntity lastMonthlyIsCompleted =
                            pageSpeedSendInfoEntityDAO.getSendInfo(Integer.parseInt(nextMonth), sqsUrlType, category, FREQUENCY_MONTHLY);

                    crawlMonth = Integer.parseInt(nextMonth);


                    if (lastMonthlyIsCompleted == null) {
                        if (sqsUrlType == PageSpeedSendInfoEntity.DEVICE_MOBILE) {
                            Integer maxDesktopCrawlMonth = pageSpeedSendInfoEntityDAO.getMaxMonthlyCrawlCompleted(PageSpeedSendInfoEntity.DEVICE_DESKTOP, category, FREQUENCY_MONTHLY);
                            if (maxDesktopCrawlMonth == null || !maxDesktopCrawlMonth.equals(crawlMonth)) {
                                System.out.println(" ### desktop doesn't complete ,  skip !!! " + crawlMonth + "," + category);
                                return;
                            }
                        }
                        sendTargetUrlList = addNewSendInfo(workDate, sendTargetUrlList);
                    } else {

                        processDate = lastMonthlyIsCompleted.getCrawlDate();

                        sendInfoId = lastMonthlyIsCompleted.getId();

                        sendTargetUrlList = sendByDetail(sendInfoId, crawlMonth, sendTargetUrlList, amazonSQS, queryUrl);
                        if (sendTargetUrlList == null) {
                            return;
                        }
                    }

                } else {
                    PageSpeedSendInfoEntity maxSendInfo = pageSpeedSendInfoEntityDAO.getMaxSendInfo(sqsUrlType, category, FREQUENCY_MONTHLY);

                    if (maxSendInfo == null) {

                        if (sqsUrlType == PageSpeedSendInfoEntity.DEVICE_MOBILE) {

                            Integer maxDesktopCrawlMonth = pageSpeedSendInfoEntityDAO.getMaxMonthlyCrawlCompleted(PageSpeedSendInfoEntity.DEVICE_DESKTOP, category, FREQUENCY_MONTHLY);
                            if (maxDesktopCrawlMonth == null) {
                                System.out.println(" ### desktop doesn't complete ,  skip !!! " + crawlMonth + "  , " + category);
                            } else {//add new info for last monthly mobile
                                workDate = FormatUtils.toDate(maxDesktopCrawlMonth.toString(), FormatUtils.MONTH_PATTERN);
                                processDate = maxDesktopCrawlMonth;
                                crawlMonth = maxDesktopCrawlMonth;
                                sendTargetUrlList = addNewSendInfo(workDate, sendTargetUrlList);
                                queryDate = workDate;
                            }

                        } else {
                            sendTargetUrlList = addNewSendInfo(workDate, sendTargetUrlList);
                        }
                    } else {

                        processDate = maxSendInfo.getCrawlDate();
                        sendInfoId = maxSendInfo.getId();
                        crawlMonth = maxSendInfo.getCrawlMonth();
                        sendTargetUrlList = sendByDetail(sendInfoId, crawlMonth, sendTargetUrlList, amazonSQS, queryUrl);
                        if (sendTargetUrlList == null) {
                            return;
                        }
                    }

                }

            } else {//exist record , keep sending
                sendInfoId = monthlyIsNotCompleted.getId();

                sendTargetUrlList = sendByDetail(sendInfoId, crawlMonth, sendTargetUrlList, amazonSQS, queryUrl);
                if (sendTargetUrlList == null) {
                    return;
                }

            }
        }

        //send to sqs
        String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
        while (ipAddress == null) {
            try {
                Thread.sleep(1000);
                ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
            } catch (Exception e) {
            }
            continue;
        }
        processSendCommand(amazonSQS, queryUrl, sendTargetUrlList, ipAddress, processDate,
                isComplete, crawlMonth, sendInfoId);

        do {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } while (threadPool.getThreadPool().getActiveCount() > 0);


    }

    private List<TargetUrlEntity> sendByDetail(Integer sendInfoId, Integer workMonth, List<TargetUrlEntity> sendTargetUrlList, AmazonSQS amazonSQS, String queryUrl) {

        Long startId = 0l;
        Long lastShuffleId = pageSpeedSendDetailEntityDAO.getLastEndShuffleId(sendInfoId);
        if (lastShuffleId == null) {
            System.out.println(" !!! no lastShuffleId record  , infoId " + sendInfoId);
            return null;
        }

        startId = pageSpeedShuffleEntityDAO.getStartShuffleId(sendInfoId, workMonth, lastShuffleId);
        System.out.println(" ******** startId  " + startId);

        Integer numberOfUrls = SQSUtils.getMessageNumberInQueue(amazonSQS, queryUrl);

        System.out.println(" ******** Q Count *****  " + numberOfUrls);

        monthlySendCount = monthlySendCount - numberOfUrls;

        if (monthlySendCount <= 0) {
            System.out.println(" ******** Q count is enough , don't send ! *****  " + monthlySendCount);
            return null;
        }

        System.out.println(" ******** monthlySendCount *****  " + monthlySendCount);

        sendTargetUrlList = getUrlsFromShuffle(sendInfoId, startId, pageSize, sendTargetUrlList);

        return sendTargetUrlList;
    }

    private List<TargetUrlEntity> addNewSendInfo(Date workDate, List<TargetUrlEntity> sendTargetUrlList) {

        Long startId = 0l;
        PageSpeedSendInfoEntity insertPageSpeedSendInfoEntity = new PageSpeedSendInfoEntity();

        System.out.println(" ******** insert new monthly crawl for  " + crawlMonth);

        List<TargetUrlEntity> matchUrlList = new ArrayList<TargetUrlEntity>();
        long startUrlId = 0l;
        int urlPageSize = 100000;
        int matchSize = 0;
        while (true) {
            List<TargetUrlEntity> urlList = null;
            if (sqsSendType.equalsIgnoreCase(SEND_TYPE_WEEKLY)){
                urlList = targetUrlEntityDAO.getTargetUrlsForWeekly(startUrlId, urlPageSize, processDomainList, SKIP_DOMAIN_LIST, sqsUrlType);
            }else{
                urlList = targetUrlEntityDAO.getTargetUrlsForMonthlyV2(startUrlId, urlPageSize, workDate, SKIP_DOMAIN_LIST, sqsUrlType);
            }
            if (CollectionUtils.isEmpty(urlList)) {
                break;
            }

            List<TargetUrlEntity> matchUrls = matchUrls(urlList);
            matchSize += matchUrls.size();
            matchUrlList.addAll(matchUrls);
            startUrlId = urlList.get(urlList.size() - 1).getId();
        }


        insertPageSpeedSendInfoEntity = setPageSpeedSendInfoEntityValue(matchSize, PageSpeedSendInfoEntity.SEND_STATUS_NEWLY);
        sendInfoId = pageSpeedSendInfoEntityDAO.insert(insertPageSpeedSendInfoEntity);

        System.out.println(" ******** " + crawlMonth + " crawl total count " + matchSize);

        //insert to shuffle
        List<PageSpeedShuffleEntity> pageSpeedShuffleEntityList = new ArrayList<PageSpeedShuffleEntity>();
        for (TargetUrlEntity urlEntity : matchUrlList) {
            PageSpeedShuffleEntity pageSpeedShuffleEntity = new PageSpeedShuffleEntity();
            pageSpeedShuffleEntity.setSendInfoId(sendInfoId);
            pageSpeedShuffleEntity.setDevice(sqsUrlType);
            pageSpeedShuffleEntity.setCrawlMonth(crawlMonth);
            pageSpeedShuffleEntity.setOwnDomainId(urlEntity.getOwnDomainId());
            pageSpeedShuffleEntity.setUrlId(urlEntity.getId().longValue());
            pageSpeedShuffleEntity.setUrl(urlEntity.getUrl());
            pageSpeedShuffleEntity.setShuffleId(getShuffledId(urlEntity.getId()));
            pageSpeedShuffleEntity.setSendDate(new Date());
            pageSpeedShuffleEntityList.add(pageSpeedShuffleEntity);

            if (pageSpeedShuffleEntityList.size() >= 1000) {
                System.out.println(" ******** insert pageSpeedShuffle " + pageSpeedShuffleEntityList.size());
                pageSpeedShuffleEntityDAO.insertForBatch(pageSpeedShuffleEntityList);
                pageSpeedShuffleEntityList = new ArrayList<PageSpeedShuffleEntity>();
            }
        }

        if (CollectionUtils.isNotEmpty(pageSpeedShuffleEntityList)) {
            System.out.println(" ******** insert pageSpeedShuffle " + pageSpeedShuffleEntityList.size());
            pageSpeedShuffleEntityDAO.insertForBatch(pageSpeedShuffleEntityList);
        }

//                    sendTargetUrlList = addTargetUrls(startId, pageSize, workDate, sendTargetUrlList);

        sendTargetUrlList = getUrlsFromShuffle(sendInfoId, startId, pageSize, sendTargetUrlList);
        return sendTargetUrlList;
    }


    private List<TargetUrlEntity> getUrlsFromShuffle(Integer sendInfoId, Long startId, int pageSize, List<TargetUrlEntity> sendTargetUrlList) {


        do {

            List<TargetUrlEntity> targetUrlList = pageSpeedShuffleEntityDAO.getUrls(sendInfoId, startId, pageSize, SKIP_DOMAIN_LIST, crawlMonth);
//            List<TargetUrlEntity> targetUrlList = targetUrlEntityDAO.getTargetUrlsForMonthly(startId, pageSize, workDate, isExpedia);

            if (CollectionUtils.isEmpty(targetUrlList)) {
                System.out.println(" ********************** Complete getUrlsFromShuffle size " + sendTargetUrlList.size());
                isComplete = true;
                break;
            }
            if (sendTargetUrlList.size() >= monthlySendCount && sqsSendType.equalsIgnoreCase(SEND_TYPE_MONTHLY)) {
                System.out.println(" ********************** getUrlsFromShuffle size " + sendTargetUrlList.size());
                break;
            }


            List<TargetUrlEntity> matchUrls = matchUrls(targetUrlList);

            int i = 0;
            for (TargetUrlEntity targetUrlEntity : matchUrls) {
                sendTargetUrlList.add(targetUrlEntity);
                i++;
                if (i == matchUrls.size()) {
                    Long lastShuffleId = targetUrlEntity.getShuffleId();
                    endShuffleId = lastShuffleId;
                    System.out.println("=== endShuffleId:" + endShuffleId);
                    startId = pageSpeedShuffleEntityDAO.getStartShuffleId(sendInfoId, crawlMonth, lastShuffleId);
                    System.out.println(" !!!!!!!!!! start ShuffleId " + startId);
                }
            }

        } while (true);

        return sendTargetUrlList;
    }

    private void processSendCommand(AmazonSQS amazonSQS, String queryUrl, List<TargetUrlEntity> urlList,
                                    String ip, int currentWorkDate, Boolean isComplete,
                                    int crawlMonth, int sendInfoId) {
        SendPageSpeedTargetUrlsCommand cmd = new SendPageSpeedTargetUrlsCommand(amazonSQS, queryUrl, urlList,
                ip, currentWorkDate, sqsUrlType, isComplete, crawlMonth, sendInfoId, endShuffleId);
        cmd.setStatus(true);
        try {
            threadPool.execute(cmd);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private PageSpeedSendInfoEntity setPageSpeedSendInfoEntityValue(int urlCount, int sendStatus) {

        PageSpeedSendInfoEntity pageSpeedSendInfoEntity = new PageSpeedSendInfoEntity();

        pageSpeedSendInfoEntity.setCrawlMonth(crawlMonth);
        pageSpeedSendInfoEntity.setDevice(sqsUrlType);
        int frequency = 0;
        if (sqsSendType.equalsIgnoreCase(SEND_TYPE_DAILY)) {
            frequency = FREQUENCY_DAILY;
        } else if (sqsSendType.equalsIgnoreCase(SEND_TYPE_WEEKLY)) {
            frequency = FREQUENCY_WEEKLY;
        } else if (sqsSendType.equalsIgnoreCase(SEND_TYPE_MONTHLY)) {
            frequency = FREQUENCY_MONTHLY;
        }
        pageSpeedSendInfoEntity.setFrequency(frequency);
        pageSpeedSendInfoEntity.setCategory(category);
        pageSpeedSendInfoEntity.setCrawlDate(Integer.parseInt(FormatUtils.formatDate(queryDate, FormatUtils.DATE_FORMAT_YYYYMMDD)));
        pageSpeedSendInfoEntity.setUrlCount(urlCount);
        pageSpeedSendInfoEntity.setSendStatus(sendStatus);
        pageSpeedSendInfoEntity.setAccumulatedSendCount(0);
        if(urlCount == 0){
            pageSpeedSendInfoEntity.setSendStatus(PageSpeedSendInfoEntity.SEND_STATUS_COMPLETED);
        }

        return pageSpeedSendInfoEntity;
    }


    private void logStatus(String sqsName, String queryUrl, String Subject, String errorMessage) {
        String subject = "PageSpeedSender ERROR sqsName:" + sqsName + ",crawlMonth:" + crawlMonth;
        sendMailReport(subject, subject);
    }

    public void sendMailReport(String subject, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String[] ccTo = null;
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }


    private List<TargetUrlEntity> matchUrls(List<TargetUrlEntity> urlList) {

//        List<TargetUrlEntity> resultList = new ArrayList<TargetUrlEntity>();
//        for (TargetUrlEntity url : urlList) {
//            int domainId = url.getOwnDomainId();
//            //check desktop engine
//            if(sqsUrlType == 1 && !desktopDomainIdList.contains(domainId) && !notDesktopDomainIdList.contains(domainId)){
//                log.info("======1:" + domainId + ",desktopDomainIdList:" + desktopDomainIdList + ",notDesktopDomainIdList:" + notDesktopDomainIdList);
//                OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
//                if(ownDomainEntity != null){
//                    List<DomainSearchEngineRelEntity> domainSearchEngineRelList = scKeywordRankManager.getAllSERels(ownDomainEntity);
//                    for(DomainSearchEngineRelEntity sder: domainSearchEngineRelList){
//                        if(!sder.getDevice().equals("d")){
//                            continue;
//                        }
//                        int searchEngineId = sder.getRankcheckSearchEngineId();
//                        String searchEngineName = seoClaritySearchEngineEntityDAO.getEngineNameById(searchEngineId);
//                        if(searchEngineName == null){
//                            log.error("====engine not exist:"+ searchEngineId);
//                            continue;
//                        }
//                        if(searchEngineName.contains("google.") && !searchEngineName.contains("images.google") && !searchEngineName.contains("shopping.google")
//                                && !searchEngineName.contains("shopping.trend")){
//                            desktopDomainIdList.add(domainId);
//                            break;
//                        }
//                    }
//                }
//            }
//            //check mobile engine
//            if(sqsUrlType == 0 && !mobileDomainList.contains(domainId) && !notMobileDomainList.contains(domainId)){
//                log.info("======0:" + domainId + ",mobileDomainList:" + mobileDomainList + ",notMobileDomainList:" + notMobileDomainList);
//                OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
//                if(ownDomainEntity != null){
//                    List<DomainSearchEngineRelEntity> domainSearchEngineRelList = scKeywordRankManager.getAllSERels(ownDomainEntity);
//                    for(DomainSearchEngineRelEntity sder: domainSearchEngineRelList){
//                        if(!sder.getDevice().equals("m")){
//                            continue;
//                        }
//                        int searchEngineId = sder.getRankcheckSearchEngineId();
//                        String searchEngineName = seoClaritySearchEngineEntityDAO.getEngineNameById(searchEngineId);
//                        if(searchEngineName == null){
//                            log.error("====engine not exist:"+ searchEngineId);
//                            continue;
//                        }
//                        if(searchEngineName.contains("google.") && !searchEngineName.contains("images.google") && !searchEngineName.contains("shopping.google")
//                                && !searchEngineName.contains("shopping.trend")){
//                            mobileDomainList.add(domainId);
//                            break;
//                        }
//                    }
//                }
//
//            }
//            // match url
//            if (sqsUrlType == 0 && mobileDomainList.contains(domainId)) {    //mobile
//                resultList.add(url);
//            } else if(sqsUrlType == 1 && desktopDomainIdList.contains(domainId)){
//                resultList.add(url);
//            } else if(sqsUrlType == 0 && !mobileDomainList.contains(domainId)){
//                System.out.println("not mobile DomainId:" + JSON.toJSONString(url.getOwnDomainId()) + ",id:" + url.getId());
//                notMobileDomainList.add(domainId);
//            }else if(sqsUrlType == 1 && !desktopDomainIdList.contains(domainId)){
//                System.out.println("not desktop DomainId:" + JSON.toJSONString(url.getOwnDomainId()) + ",id:" + url.getId());
//                notDesktopDomainIdList.add(domainId);
//            }else {
//                System.out.println("url device error:" + url.getOwnDomainId() + ",id:" + url.getId());
//            }
//        }
        return urlList;
    }

    private long getShuffledId(long urlId) {
        StringBuffer sb = new StringBuffer();
        StringBuffer reversedSb = new StringBuffer();
        try {
            sb.append(urlId);
            Long reversedValue = Long.parseLong(sb.reverse().toString());

            reversedSb.append(reversedValue);
            if (reversedSb != null && reversedSb.length() < SHUFFLE_ID_LENGTH) {
                int len = reversedSb.length();
                for (int i = 0; i < (SHUFFLE_ID_LENGTH - len); i++) {
                    reversedSb.append(0);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return Long.parseLong(reversedSb.toString());
    }

    class ResponseErrorEntity{
        private Integer code;
        private String message;
        private String status;
        private List<ErrorsEntity> errors;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public List<ErrorsEntity> getErrors() {
            return errors;
        }

        public void setErrors(List<ErrorsEntity> errors) {
            this.errors = errors;
        }
    }

    class ErrorsEntity{
        private String message;
        private String domain;
        private String reason;

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }
    }

}
