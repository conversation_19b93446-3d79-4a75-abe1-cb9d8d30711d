package seoclarity.backend.sender.pagespeed;

import com.alibaba.fastjson2.JSON;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.TargetUrlEntityDAO;
import seoclarity.backend.dao.actonia.pagespeed.PageSpeedSendInfoEntityDAO;
import seoclarity.backend.dao.actonia.pagespeed.PageSpeedShuffleEntityDAO;
import seoclarity.backend.dao.clickhouse.pagespeed.PageSpeedDao;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.entity.pagespeed.PageSpeedSendInfoEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.ScStringEncryptor;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.util.*;

public class SendMissingUrlsV4 {

    private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private TargetUrlEntityDAO targetUrlEntityDAO;

    private PageSpeedDao pageSpeedDao;
    private PageSpeedSendInfoEntityDAO pageSpeedSendInfoEntityDAO;
    private PageSpeedShuffleEntityDAO pageSpeedShuffleEntityDAO;

    private AmazonSQS amazonSQS;
    private String queryUrl;

    private static Integer queryCrawlMonth = 201809;

    private static int device = 1;

    private static int category = 0;

    private static int frequency = 30;

    private static String sqsUrlName = "PAGE_SPEED_TEST_DESKTOP_URL";

    private int crawlDate;


    public SendMissingUrlsV4(){
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        pageSpeedDao = SpringBeanFactory.getBean("pageSpeedDao");
        pageSpeedSendInfoEntityDAO = SpringBeanFactory.getBean("pageSpeedSendInfoEntityDAO");
        pageSpeedShuffleEntityDAO = SpringBeanFactory.getBean("pageSpeedShuffleEntityDAO");
    }

    public static void main(String[] args) {

        queryCrawlMonth = Integer.parseInt(args[0]);
        device = Integer.parseInt(args[1]);
        sqsUrlName = args[2];
        category = Integer.parseInt(args[3]);
        frequency = Integer.parseInt(args[4]);

        SendMissingUrlsV4 ins = new SendMissingUrlsV4();
        threadPool.init();
        ins.process();
        threadPool.destroy();
    }

    private void process() {

        try {
            amazonSQS = SQSUtils.getAmazonSQS();

            System.out.println("### Will start to send missing urls to SQS currentProcessDate:"
                    + CacheModleFactory.getInstance().getWorkDate()
                    + ", queryCrawlMonth: " + queryCrawlMonth
                    + ", device: " + device
                    + ", sqsName: " + sqsUrlName);


            queryUrl = SQSUtils.createQueue(sqsUrlName, amazonSQS);

            //send to queue
            processTargetUrls();

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Send keyword to AmazonSQS failed!");
            return;
        }
    }

    private void processTargetUrls() {

        PageSpeedSendInfoEntity pageSpeedSendInfoEntity = pageSpeedSendInfoEntityDAO.getSendInfo(queryCrawlMonth, device, category, frequency);
        Integer sendInfoId = pageSpeedSendInfoEntity.getId();
        List<Integer> domainList = pageSpeedShuffleEntityDAO.getDomainList(sendInfoId,queryCrawlMonth);
        System.out.println("====domainList size:" + domainList.size());

        List<TargetUrlEntity> sendList = new ArrayList<TargetUrlEntity>();
        if(CollectionUtils.isNotEmpty(domainList)){

            for(Integer domainId: domainList){

                OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);
                if((ownDomainSettingEntity.getPagespeedSetting() == null || ownDomainSettingEntity.getPagespeedSetting() == 0
                        || ownDomainSettingEntity.getPagespeedFrequency() != 30) && frequency == 30){
                    System.out.println("====skip disable monthly domain:" + domainId);
                    continue;
                }

                long startTime = System.currentTimeMillis();
                sendList.addAll(getUrlsFromShuffle(sendInfoId, domainId));

                System.out.println("============sendList size:" + sendList.size() + ",domain:" + domainId);
                System.out.println("=====耗时:" + (System.currentTimeMillis() - startTime)/1000 + " s");
            }

        }

        crawlDate = pageSpeedSendInfoEntity.getCrawlDate();
        System.out.println("============total sendList size:" + sendList.size());
        //send to sqs
        Collections.shuffle(sendList);
        sendUrls(sendList);

    }

    private List<TargetUrlEntity> getUrlsFromShuffle(Integer sendInfoId, int domainId) {

        List<TargetUrlEntity> sendList = new ArrayList<TargetUrlEntity>();
        List<String> crawledUrlList = pageSpeedDao.getUrlsMessingV4(device,queryCrawlMonth, domainId);
        System.out.println("====crawledUrlList size:" + crawledUrlList.size());

        boolean isWeekly = frequency == 7 ? true : false;
        Long startId = 0l;
        int pageSize = 1000;
        long maxShuffleId = 999999999999l;

        do {
            System.out.println("====startId:" + startId);
            List<TargetUrlEntity> shuffleList = pageSpeedShuffleEntityDAO.getMissingUrlsByPage(sendInfoId, queryCrawlMonth,domainId, startId, maxShuffleId, pageSize, isWeekly);

            if (startId >= maxShuffleId || CollectionUtils.isEmpty(shuffleList)) {
                System.out.println(" ********************** Complete shuffleList ");
                break;
            }

            for(TargetUrlEntity targetUrlEntity: shuffleList){
                if(!crawledUrlList.contains(targetUrlEntity.getUrl())){
                    sendList.add(targetUrlEntity);
                }
            }
            System.out.println("============sendList size1:" + sendList.size() + ",domain:" + domainId);
            startId = shuffleList.get(shuffleList.size() -1).getShuffleId();

        } while (true);

        return sendList;
    }


    private void sendUrls(List<TargetUrlEntity> urlList) {
        System.out.println("================ urlList   " + urlList.size());
        Map<String, String> messages = new HashMap<String, String>();

        try {
            for (TargetUrlEntity url : urlList) {

                String gson = coventToGsonStr(url);
                if (StringUtils.isBlank(gson)) {
                    continue;
                }
                messages.put(url.getId().toString(), gson);
                System.out.println("send " + url.getId());

                if (messages.size() >= 10) {
                    sendQAndInsertShuffle(messages);
                    messages = new HashMap<String, String>();
                }
            }
            if (messages.size() > 0) {
                sendQAndInsertShuffle(messages);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void sendQAndInsertShuffle(Map<String, String> messages) {
        try {
            SQSUtils.sendBatchMessageToQueue(amazonSQS, queryUrl, messages);
        } catch (Exception e) {
            System.out.println("Send sqs faild, queryUrl:" + queryUrl + ", messages:" + messages.size());
            e.printStackTrace();
        }
    }

    private String coventToGsonStr(TargetUrlEntity url) {
        if (url != null && StringUtils.isNotBlank(url.getUrl())) {
            try {
                Map<String, Object> val = new HashMap<String, Object>();
                val.put("oid", url.getOwnDomainId());
                val.put("sendDate", crawlDate);
                val.put("urlId", url.getId());
                val.put("url", url.getUrl());
                val.put("message", new Message());
                val.put("apiKey", ScStringEncryptor.decrypt(url.getPagespeedClientApikey()));
//                System.out.println("====apiKey:" + ScStringEncryptor.decrypt(url.getPagespeedClientApikey()));
                if (FormatUtils.isURL(url.getUrl())) {
                    val.put("isValid", 1);
                } else {
                    val.put("isValid", 0);
                    System.out.println(" wrong url, OID:" + url.getOwnDomainId() + ", url:" + url.getUrl());
                }
                return JSON.toJSONString(val);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            System.out.println("Url is empty, urlId:" + (url == null ? null : url.getId()));
            return null;
        }
    }

}
