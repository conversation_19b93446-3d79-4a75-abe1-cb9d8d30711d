package seoclarity.backend.sender.pagespeed;

import com.alibaba.fastjson2.JSON;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.pagespeed.PageSpeedSendDetailEntityDAO;
import seoclarity.backend.dao.actonia.pagespeed.PageSpeedSendInfoEntityDAO;
import seoclarity.backend.dao.actonia.pagespeed.PageSpeedShuffleEntityDAO;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.entity.pagespeed.PageSpeedSendDetailEntity;
import seoclarity.backend.entity.pagespeed.PageSpeedSendInfoEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.ScStringEncryptor;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-02-27
 * com.actonia.clarity_data_sender.sqs.SendPageSpeedTargetUrlsCommand
 */
public class SendPageSpeedTargetUrlsCommand extends BaseThreadCommand {
    private AmazonSQS amazonSQS;
    private String queryUrl;
//	private List<TargetUrlEntity> urlList;

    private static final int SHUFFLE_ID_LENGTH = 12;

    private List<TargetUrlEntity> urlList;
    private List<String> tokenList;
    private String ip;
    private int currentWorkDate;
    private int sqsUrlType = 1; //0:mobile;1:desktop
    private Boolean isComplete;

    private int sendInfoId;
    private static int crawlMonth;
    private int version;
    private long endShuffleId;

    private PageSpeedSendInfoEntityDAO pageSpeedSendInfoEntityDAO;
    private PageSpeedShuffleEntityDAO pageSpeedShuffleEntityDAO;
    private PageSpeedSendDetailEntityDAO pageSpeedSendDetailEntityDAO;

    public SendPageSpeedTargetUrlsCommand(AmazonSQS amazonSQS, String queryUrl, List<TargetUrlEntity> urlList, String ip,
                                          int currentWorkDate, int sqsUrlType, Boolean isComplete,
                                          int crawlMonth, int sendInfoId, long endShuffleId) {
        this.amazonSQS = amazonSQS;
        this.queryUrl = queryUrl;
        this.urlList = urlList;
        this.ip = ip;
        this.currentWorkDate = currentWorkDate;
        this.sqsUrlType = sqsUrlType;
        this.isComplete = isComplete;
        this.crawlMonth = crawlMonth;
        this.sendInfoId = sendInfoId;
        this.endShuffleId = endShuffleId;
        pageSpeedSendInfoEntityDAO = SpringBeanFactory.getBean("pageSpeedSendInfoEntityDAO");
        pageSpeedSendDetailEntityDAO = SpringBeanFactory.getBean("pageSpeedSendDetailEntityDAO");
        pageSpeedShuffleEntityDAO = SpringBeanFactory.getBean("pageSpeedShuffleEntityDAO");
    }

    public SendPageSpeedTargetUrlsCommand(AmazonSQS amazonSQS, String queryUrl, List<String> tokenList, String ip, int currentWorkDate, boolean isTokens) {
        this.amazonSQS = amazonSQS;
        this.queryUrl = queryUrl;
        this.tokenList = tokenList;
        this.ip = ip;
        this.currentWorkDate = currentWorkDate;
    }

    @Override
    protected void execute() throws Exception {
        long a = System.currentTimeMillis();
        System.out.println("Start command IP: " + ip + ", queryUrl:" + queryUrl + ", currentWorkDate:" + currentWorkDate
                + ", urlList:" + (urlList == null ? 0 : urlList.size()) + ", tokenList:" + (tokenList == null ? 0 : tokenList.size()));

        try {
            if (urlList != null && urlList.size() > 0) {
                sendUrls();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        CacheModleFactory.getInstance().setAliveIpAddress(ip);
        long b = System.currentTimeMillis();
        System.out.println("End command IP: " + ip + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
    }


    private void sendUrls() {
        System.out.println("================ urlList   " + urlList.size());
        Map<String, String> messages = new HashMap<String, String>();

        int sendCount = 0;

        try {
            for (TargetUrlEntity url : this.urlList) {

                String gson = coventToGsonStr(url);
                if (StringUtils.isBlank(gson)) {
                    continue;
                }
                messages.put(url.getId().toString(), gson);
                System.out.println("send " + url.getId());


                if (messages.size() >= 10) {
                    sendQAndInsertShuffle(messages);
                    sendCount += messages.size();
                    messages = new HashMap<String, String>();
                }

            }
            if (messages.size() > 0) {
                sendQAndInsertShuffle(messages);
                sendCount += messages.size();
            }

            Long startId = 0l;
//            long endShuffleId = 0;

            Long LastEndShuffleId = pageSpeedSendDetailEntityDAO.getLastEndShuffleId(sendInfoId);
            if (LastEndShuffleId == null) {
                startId = pageSpeedShuffleEntityDAO.getStartShuffleIdFirstLy(sendInfoId, crawlMonth);
            } else {
                startId = pageSpeedShuffleEntityDAO.getStartShuffleId(sendInfoId, crawlMonth, LastEndShuffleId);
//			startId = LastEndShuffleId + 1;
            }

            System.out.println("   ******** sendCount  " + sendCount + ",startId:" + startId + ",endShuffleId:" + endShuffleId);
//            endShuffleId = pageSpeedShuffleEntityDAO.getEndShuffleId(sendInfoId, crawlMonth, startId, sendCount);

            PageSpeedSendDetailEntity pageSpeedSendDetailEntity = setPageSpeedSendDetailEntityValue(sendInfoId, startId, endShuffleId, sendCount);

            pageSpeedSendDetailEntityDAO.insert(pageSpeedSendDetailEntity);
            PageSpeedSendInfoEntity sendInfo = pageSpeedSendInfoEntityDAO.getSendInfoById(sendInfoId);
            Integer urlCount = sendInfo.getAccumulatedSendCount() + sendCount;
            if (isComplete) {
                pageSpeedSendInfoEntityDAO.updateSendStatusById(sendInfoId, PageSpeedSendInfoEntity.SEND_STATUS_COMPLETED, urlCount);
            } else {
                pageSpeedSendInfoEntityDAO.updateSendStatusById(sendInfoId, PageSpeedSendInfoEntity.SEND_STATUS_SENDING, urlCount);
            }
        }catch (Exception e){
            e.printStackTrace();
        }



    }

    private void sendQAndInsertShuffle(Map<String, String> messages) {
        try {
            SQSUtils.sendBatchMessageToQueue(amazonSQS, queryUrl, messages);
//			pageSpeedShuffleEntityDAO.insertForBatch(pageSpeedShuffleEntityList);

        } catch (Exception e) {
            System.out.println("Send sqs faild, queryUrl:" + queryUrl + ", messages:" + messages.size());
            e.printStackTrace();
        }
    }

    private String coventToGsonStr(TargetUrlEntity url) {
        if (url != null && StringUtils.isNotBlank(url.getUrl())) {
            try {
                Map<String, Object> val = new HashMap<String, Object>();
                val.put("oid", url.getOwnDomainId());
                val.put("sendDate", currentWorkDate);
                val.put("urlId", url.getId());
                val.put("url", url.getUrl());
                val.put("message", new Message());
                val.put("version", version);
                val.put("apiKey", ScStringEncryptor.decrypt(url.getPagespeedClientApikey()));
                if (FormatUtils.isURL(url.getUrl())) {
                    val.put("isValid", 1);
                } else {
                    val.put("isValid", 0);
                    System.out.println(" wrong url, OID:" + url.getOwnDomainId() + ", url:" + url.getUrl());
                }
                return JSON.toJSONString(val);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            System.out.println("Url is empty, urlId:" + (url == null ? null : url.getId()));
            return null;
        }
    }

    @Override
    protected void undo() throws Exception {
    }

    private PageSpeedSendDetailEntity setPageSpeedSendDetailEntityValue(int sendInfoId, long startShuffleId, long endShuffleId, int sendCount) {

        PageSpeedSendDetailEntity pageSpeedSendDetailEntity = new PageSpeedSendDetailEntity();

        pageSpeedSendDetailEntity.setSendInfoId(sendInfoId);
        pageSpeedSendDetailEntity.setStartShuffleId(startShuffleId);
        pageSpeedSendDetailEntity.setEndShuffleId(endShuffleId);
        pageSpeedSendDetailEntity.setSendCount(sendCount);

        return pageSpeedSendDetailEntity;
    }


}
