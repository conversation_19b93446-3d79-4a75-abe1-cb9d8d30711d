package seoclarity.backend.sender;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.opensearch.client.opensearch.OpenSearchClient;
import seoclarity.backend.dao.actonia.hreflang.HrefLangUrlCrawlDAO;
import seoclarity.backend.dao.actonia.hreflang.HrefLangUrlDAO;
import seoclarity.backend.entity.actonia.OpenSearchUrlProperty;
import seoclarity.backend.entity.actonia.hreflang.HrefLangUrlCrawlEntity;
import seoclarity.backend.entity.actonia.hreflang.HrefLangUrlEntity;
import seoclarity.backend.opensearch.OpenSearchClientFactory;
import seoclarity.backend.opensearch.OpenSearchService;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;

public class SendHrefLangUrlToOpenSearch { // https://www.wrike.com/open.htm?id=1649012822
	
	//private static final String OPEN_SEARCH_MAPPING_FILE = "hrefLangUrl.mapping";
	private static final String KAFKA_TOPIC = OpenSearchUrlProperty.KAFKA_TOPIC_URL_CRAWL;
	
	private static final int FREQUENCY_WEEKLY = 7;
	private static final int ID_RANGE_BATCH_SIZE = 300;
	
	private static final SimpleDateFormat SDF_YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
	private static final SimpleDateFormat SDF_YYYY_MM_DD = new SimpleDateFormat("yyyy-MM-dd");
	private static final SimpleDateFormat SDF_MMDD_HHMMSS = new SimpleDateFormat("MMdd HH:mm:ss");
	private static final SimpleDateFormat SDF_YYYY_MM_DD_HHMMSS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private static final SimpleDateFormat SDF_MMDD = new SimpleDateFormat("MMdd");
	
	private int totalSentCnt = 0;
	
	private static final OpenSearchClient openSearchClient = OpenSearchClientFactory.getInstance();
	private final OpenSearchService<OpenSearchUrlProperty> openSearchService = new OpenSearchService<OpenSearchUrlProperty>(openSearchClient);
	
	private HrefLangUrlDAO hrefLangUrlDAO;
	private HrefLangUrlCrawlDAO hrefLangUrlCrawlDAO;
	private ZeptoMailSenderComponent zeptoMailSenderComponent;
	
	public SendHrefLangUrlToOpenSearch() {
		hrefLangUrlDAO = SpringBeanFactory.getBean("hrefLangUrlDAO");
		hrefLangUrlCrawlDAO = SpringBeanFactory.getBean("hrefLangUrlCrawlDAO");
		zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
	}

	public static void main(String[] args) {
		SendHrefLangUrlToOpenSearch ins = new SendHrefLangUrlToOpenSearch();
		ins.startProcess(FREQUENCY_WEEKLY);
	}
	
	private void startProcess(int frequency) {
		Date processDate = new Date();
		boolean isPeriodStartDay = false;
		int sendToQDate = 0;
		if (frequency == FREQUENCY_WEEKLY) {
			Date periodStartDate = DateUtils.truncate(CommonUtils.getLastSunday(processDate), Calendar.DAY_OF_MONTH);
			isPeriodStartDay = CommonUtils.isStartDayOfWeek(processDate);
			sendToQDate = Integer.parseInt(SDF_YYYYMMDD.format(periodStartDate));
		} else {
			// TODO now only support weekly
			System.out.println("======Error NotsupportedFrequency:" + frequency);
			sendMailReport(processDate, "SendHrefLangUrlToOpenSearchError:", "Error NotsupportedFrequency:" + frequency);
			return;
		}
		String openSearchIndexName = OpenSearchUrlProperty.INDEX_NAME_HREFLANG;
		System.out.println("=====Parameters freq:" + frequency + " isPStartDay:" + isPeriodStartDay + " QD:" + sendToQDate + " osIdxName:" + 
			openSearchIndexName + " kfkTpc:" + KAFKA_TOPIC + " startAt:" + SDF_MMDD_HHMMSS.format(processDate));
		OpenSearchUrlProperty.createOpenSearchIndex(openSearchClient, openSearchIndexName);
		process(frequency, sendToQDate, openSearchIndexName, KAFKA_TOPIC, processDate, isPeriodStartDay);
	}
	
	private void process(int frequency, int sendToQDate, String openSearchIndexName, String kfkTpc, Date processDate, boolean isPeriodStartDay) {
		try {
			Date periodStartDay = SDF_YYYYMMDD.parse(String.valueOf(sendToQDate));
			int alreadySentCnt = hrefLangUrlCrawlDAO.getSentCount(periodStartDay);
			
			HrefLangUrlEntity hrefLangUrlEntity = hrefLangUrlDAO.getUrlIdRangeToSend(periodStartDay);
			if (hrefLangUrlEntity == null || hrefLangUrlEntity.getMinId() == null) {
				System.out.println("======NoURLToSend QD:" + sendToQDate + " alreadySent:" + alreadySentCnt + " at " + SDF_MMDD_HHMMSS.format(processDate));
				return;
			}
			
			Integer minRelId = hrefLangUrlEntity.getMinId();
			Integer maxRelId = hrefLangUrlEntity.getMaxId();
			int relCnt = hrefLangUrlEntity.getCnt();
			System.out.println("====ProcessURL isPStartDay:" + isPeriodStartDay + " QD:" + sendToQDate + " idRange:" + minRelId + "-" + maxRelId + 
				" cnt:" + relCnt + " alreadySent:" + alreadySentCnt);
			if (relCnt > ID_RANGE_BATCH_SIZE) {
				long fromId = minRelId;
				while (fromId < maxRelId) {
					long toId = fromId + ID_RANGE_BATCH_SIZE;
					if (toId > maxRelId) {
						toId = maxRelId;
					}
					fromId += (fromId == minRelId ? 0 : 1);
					System.out.println(" #ProcessingIdBatch:" + fromId + "-" + toId);
					List<HrefLangUrlEntity> urlList = hrefLangUrlDAO.getUrlListToSend(periodStartDay, fromId, toId);
					System.out.println("  getDB:" + fromId + "-" + toId + " QD:" + SDF_YYYYMMDD.format(periodStartDay) + " cnt:" + (urlList == null ? 0 : urlList.size()));
					saveUrlToOpenSearch(sendToQDate, openSearchIndexName, kfkTpc, processDate, urlList);
					fromId = toId;
				}
			} else if (relCnt > 0) {
				System.out.println(" #ProcessingIdRange:" + minRelId + "-" + maxRelId);
				List<HrefLangUrlEntity> urlList = hrefLangUrlDAO.getUrlListToSend(periodStartDay, minRelId, maxRelId);
				System.out.println("  getDB:" + minRelId + "-" + maxRelId + " QD:" + SDF_YYYYMMDD.format(periodStartDay) + " cnt:" + (urlList == null ? 0 : urlList.size()));
				saveUrlToOpenSearch(sendToQDate, openSearchIndexName, kfkTpc, processDate, urlList);
			}
		} catch (Exception exp) {
			exp.printStackTrace();
			sendMailReport(processDate, "SendInspectUrlToOpenSearchError:", exp.getMessage());
		} finally {
			System.out.println("=====EndProcess totalSentCnt:" + totalSentCnt + " at " + SDF_MMDD_HHMMSS.format(processDate));
		}
	}
	
	private void saveUrlToOpenSearch(int sendToQDate, String openSearchIndexName, String kfkTpc, Date processDate, 
			List<HrefLangUrlEntity> urlList) throws Exception {
		if (urlList != null && urlList.size() > 0) {
			List<OpenSearchUrlProperty> urlPropertyList = new ArrayList<OpenSearchUrlProperty>();
			List<HrefLangUrlCrawlEntity> urlCrawlList = new ArrayList<HrefLangUrlCrawlEntity>();
			String createTime = SDF_YYYY_MM_DD_HHMMSS.format(new Date());
			Date crawlDate = SDF_YYYYMMDD.parse(String.valueOf(sendToQDate));
			for (HrefLangUrlEntity urlEntity : urlList) {
				OpenSearchUrlProperty urlProperty = new OpenSearchUrlProperty();
				// select hu.ownDomainId, hu.id, hu.url, hu.urlMurmur3Hash, hu.browserMurmur3hash
				urlProperty.setUrlType(OpenSearchUrlProperty.URL_TYPE_HREFLANG);
				urlProperty.setDomainId(urlEntity.getOwnDomainId());
				urlProperty.setInstanceId(0);
				urlProperty.setUrlId(urlEntity.getId());
				urlProperty.setUrl(urlEntity.getUrl());
				urlProperty.setUrlMurmurHash(urlEntity.getUrlMurmur3Hash());
				urlProperty.setBrowserMurmurhash(urlEntity.getBrowserMurmur3hash());
				urlProperty.setSendToQDate(sendToQDate);
				urlProperty.setKfkTpc(kfkTpc);
				urlProperty.setCreateTime(createTime);
				urlPropertyList.add(urlProperty);
				
				HrefLangUrlCrawlEntity urlCrawlEntity = new HrefLangUrlCrawlEntity();
				urlCrawlEntity.setOwnDomainId(urlEntity.getOwnDomainId());
				urlCrawlEntity.setCrawlDate(crawlDate);
				urlCrawlEntity.setUrlId(urlEntity.getId());
				urlCrawlEntity.setCrawlStatus(HrefLangUrlCrawlEntity.CEAWL_STATUS_SENT_TO_OPENSEARCH);
				urlCrawlList.add(urlCrawlEntity);
				System.out.println(" SendURL:" + urlEntity.getId());
			}
			
			System.out.println(" ##SaveToOS cnt:" + urlPropertyList.size() + " at " + SDF_MMDD_HHMMSS.format(processDate));
			openSearchService.bulkIndexDocuments(openSearchIndexName, urlPropertyList, OpenSearchUrlProperty::id);
			
			System.out.println(" ##InsCrawl cnt:" + urlCrawlList.size() + " at " + SDF_MMDD_HHMMSS.format(processDate));
			hrefLangUrlCrawlDAO.insertData(urlCrawlList);
			
			totalSentCnt += urlList.size();
		}
	}
	
    private void sendMailReport(Date startTime, String subject, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Wilber");
		reportMap.put("dateString", DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
		reportMap.put("startTime", DateFormatUtils.format(startTime, "MM/dd/yyyy HH:mm:ss"));
		reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String[] ccTo = null;
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", 
        	reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }
}