package seoclarity.backend.sender.realtimepagecrawl;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.GetQueueUrlRequest;
import com.amazonaws.services.sqs.model.GetQueueUrlResult;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.springframework.util.StopWatch;
import seoclarity.backend.dao.actonia.realtimepagecrawl.RealtimeUrlCrawlDetailDAO;
import seoclarity.backend.dao.actonia.realtimepagecrawl.RealtimeUrlCrawlInstanceDAO;
import seoclarity.backend.entity.actonia.OpenSearchUrlProperty;
import seoclarity.backend.entity.actonia.realtimepagecrawl.RealtimeUrlCrawlDetailEntity;
import seoclarity.backend.entity.actonia.realtimepagecrawl.RealtimeUrlCrawlInstanceEntity;
import seoclarity.backend.opensearch.OpenSearchClientFactory;
import seoclarity.backend.opensearch.OpenSearchService;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.SQSUtils;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

// https://www.wrike.com/open.htm?id=1473174692 —> source url from file
// https://www.wrike.com/open.htm?id=1336944322 -> source url from mysql table
@Slf4j
public class RealTimePageCrawlUrlSender {

    private final static String QUEUE_NAME_PREFIX = "SQS_IN_REALTIME_PAGE_";
    private final static String WORKER_SPLIT_TESTER_KEY_PREFIX = "event_splittester_crawlUrl_"; // https://www.wrike.com/open.htm?id=1336944322 event_splittester_urlCrawl_{groupId}_{oid}_{version}
    private final static String WORKER_UI_KEY_PREFIX = "event_urlCrawl_realtime_init_"; // event_urlCrawl_realtime_init_{OID}_{instanceId}
    private final static String WORKER_STORM_KEY_PREFIX = "event_urlCrawl_realtime_crawl_"; // event_urlCrawl_realtime_crawl_{OID}_{instanceId}
    private final static String FTP_ADDRESS = "**************";
    private final static String FTP_UNAME = "devuser";
    private final static String FTP_PASSWORD = "vdRA6kAPgGON4ms3";
    private final static String FTP_FILE_PATH = "/home/<USER>/public_html/realTimePageCrawl/domain_folder/";
    private final static String LOCAL_PATH = "/home/<USER>/source/realTimePageCrawl/tmp_file/";
    private final static String SPLIT = "_";
    private final static String resultFields = OpenSearchUrlProperty.RESULT_FIELD_RESPONSECODE;
    private final static Gson gson = new Gson();
    private final static int MSG_BATCH_SIZE = 10;
    private final static int DB_BATCH_SIZE = 200;
    private final static int PROCESS_SOURCE_TYPE_FILE = 1; // get from file
    private final static int PROCESS_SOURCE_TYPE_TABLE = 2; // get from table
    private static final OpenSearchClient openSearchClient = OpenSearchClientFactory.getInstance();
    private final OpenSearchService<OpenSearchUrlProperty> openSearchService = new OpenSearchService<OpenSearchUrlProperty>(openSearchClient);

    private RealtimeUrlCrawlInstanceDAO realtimeUrlCrawlInstanceDAO;
    private RealtimeUrlCrawlDetailDAO realtimeUrlCrawlDetailDAO;
    private AmazonSQS amazonSQS;

    private boolean isTest;
    private int processType;

    public RealTimePageCrawlUrlSender() {
        realtimeUrlCrawlInstanceDAO = SpringBeanFactory.getBean("realtimeUrlCrawlInstanceDAO");
        realtimeUrlCrawlDetailDAO = SpringBeanFactory.getBean("realtimeUrlCrawlDetailDAO");
        amazonSQS = SQSUtils.getAmazonSQS();
    }

    public static void main(String[] args) {
        new RealTimePageCrawlUrlSender().startProcess(args);
    }

    private void startProcess(String[] args) {
        isTest = Boolean.parseBoolean(args[0]);
        processType = Integer.parseInt(args[1]);
        OpenSearchUrlProperty.createOpenSearchIndex(openSearchClient, OpenSearchUrlProperty.INDEX_NAME_REALTIME);
        if (processType == 1) { // prod: process worker file
            processWorker();
        } else if (processType == 2) { // one time process -- process file
            int ownDomainId = Integer.parseInt(args[2]);
            int instanceId = Integer.parseInt(args[3]);
            int createDate = Integer.parseInt(args[4]);
            String filePath = args[5];
            processFile(ownDomainId, instanceId, filePath, createDate);
        } else if (processType == 3) { // one time process -- scan worker
            int processSourceType = Integer.parseInt(args[2]);
            String workerKeyPrefix = args[3]; // get url from file or table
            beforeProcess(workerKeyPrefix, processSourceType);
        }
    }

    private void processWorker() {
        while (true) {
            try {
                beforeProcess(WORKER_UI_KEY_PREFIX, PROCESS_SOURCE_TYPE_FILE);
                beforeProcess(WORKER_SPLIT_TESTER_KEY_PREFIX, PROCESS_SOURCE_TYPE_TABLE);
                deleteEmptyQueue();
            } catch (Exception e) {
                log.info("===currentProcessError");
                e.printStackTrace();
            }
            log.info("current process end start sleep");
            try {
                TimeUnit.MINUTES.sleep(1);
            } catch (InterruptedException e) {
                log.info("====sleepError");
                e.printStackTrace();
            }
        }
    }

    private void deleteEmptyQueue() {
        List<String> listQueues = SQSUtils.getListQueues(amazonSQS, QUEUE_NAME_PREFIX);
        if (listQueues == null || listQueues.isEmpty()) {
            log.info("===nothingQueue");
            return;
        }
        for (String queueUrl : listQueues) {
            if (StringUtils.isEmpty(queueUrl)) {
                continue;
            }
            String queueName = queueUrl.split("/")[4].trim();
            Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, queueUrl);
            int inFlight = map.get("MESSAGES_IN_FLIGHT");
            int msgCnt = map.get("MESSAGES_AVAILABLE");
            log.info("===currentQueue name:{} msgCnt:{} inFlight:{} queueUrl:{}", queueName, msgCnt, inFlight, queueUrl);

            if (inFlight == 0 && msgCnt == 0) {
                String[] split = queueName.split(SPLIT);
                int instanceId = Integer.parseInt(split[split.length - 1]);

                log.info("===delQueue instanceId:{} queueUrl:{}", instanceId, queueUrl);
                try {
                    SQSUtils.deleteQueue(amazonSQS, queueUrl);
                } catch (Exception e) {
                    log.info("===delQueueError queueUrl:{}", queueUrl);
                    realtimeUrlCrawlInstanceDAO.updateStatusById(RealtimeUrlCrawlInstanceEntity.STATUS_DELETE_SQS_ERROR, instanceId);
                    e.printStackTrace();
                }

                realtimeUrlCrawlInstanceDAO.updateStatusById(RealtimeUrlCrawlInstanceEntity.STATUS_DELETE_SQS_SUCCESS, instanceId);
            }
        }
    }

    private void beforeProcess(String keyPrefix, int processSourceType) {
        List<String> cacheList = getCacheList(keyPrefix);
        if (null == cacheList || cacheList.isEmpty()) {
            log.info("====nothingRun sourceType:{}", processSourceType);
            return;
        }

        for (String key : cacheList) {
            if (!StringUtils.containsIgnoreCase(key, keyPrefix)) {
                log.info("====keyFormatError not contains '{}' currentKey:{} sourceType:{}", keyPrefix, key, processSourceType);
                continue;
            }

            String[] split = key.split(keyPrefix);
            if (split.length != 2) {
                log.info("====keySplitArrayError currentKey:{} sourceType:{}", key, processSourceType);
                continue;
            }

            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            try {
                if (processSourceType == PROCESS_SOURCE_TYPE_FILE) {
                    int instanceId = Integer.parseInt(split[1].split("_")[1]);
                    log.info("===currentProcess instanceId:{}", instanceId);
                    process(instanceId, key);
                    stopWatch.stop();
                    log.info("===currentProcessEnd instanceId:{} useTime:{}", instanceId, stopWatch.getTotalTimeSeconds());
                } else if (processSourceType == PROCESS_SOURCE_TYPE_TABLE) {
                    String[] idArr = split[1].split("_");
                    int groupId = Integer.parseInt(idArr[0]);
                    int domainId = Integer.parseInt(idArr[1]);
                    String version = idArr[2];
                    log.info("===currentProcess groupId:{} domainId:{} version:{}", groupId, domainId, version);
                    process(groupId, domainId, version, key);
                    stopWatch.stop();
                    log.info("===currentProcessEnd groupId:{} domainId:{} version:{} useTime:{}", groupId, domainId, version, stopWatch.getTotalTimeSeconds());
                }
            } catch (Exception e) {
                log.info("===processError key:{} sourceType:{}", key, processSourceType);
                e.printStackTrace();
            }
            delWorkerKey(key);
        }
        log.info("====currentProcessEnd time sourceType:{}", processSourceType);
    }


    // process sourceType = 1 -> file
    private void process(int instanceId, String key) {
        RealtimeUrlCrawlInstanceEntity instance = realtimeUrlCrawlInstanceDAO.getById(instanceId);
        if (null == instance) {
            log.info("==queryForNull instanceId:{} key:{}", instanceId, key);
            return;
        }


        int domainId = instance.getOwnDomainId();
        int createDate = instance.getCreateDate();
        String ftpFilePath = instance.getFullPathFilename();
        String fileName = ftpFilePath.split("/")[5]; // todo 需要更改路径

        if (!downloadFileToLocal(fileName, domainId, instanceId)) {
            log.info("===downloadFileError oid:{} instanceId:{} fileName:{} ftpFilePath:{} current process error....", domainId, instanceId, fileName, ftpFilePath);
            return;
        }

        String localFilePath = LOCAL_PATH + fileName;
        processFile(domainId, instanceId, localFilePath, createDate);
    }

    // process sourceType = 2 -> table
    private void process(int groupId, int domainId, String version, String key) {
        int createDate = Integer.parseInt(FormatUtils.formatDate(new Date(), "yyyyMMdd"));
        int instanceId = getInstanceId(groupId, domainId, createDate);
        if (instanceId <= 0) {
            log.error("===insertCrawlInstanceError oid:{} groupId:{} key:{}", domainId, instanceId, key);
            return;
        }
        log.info("===instanceId:{} oid:{} groupId:{} key:{}", instanceId, domainId, groupId, key);
        // query url
        List<String> urlList = realtimeUrlCrawlInstanceDAO.getUrlListByGroupId(groupId, version);
        if (urlList == null || urlList.isEmpty()) {
            log.info("===queryForNull instanceId:{} oid:{} groupId:{} key:{}", instanceId, domainId, groupId, key);
            return;
        }
        if (!isTest) {
            realtimeUrlCrawlInstanceDAO.updateStatusByIdAndOid(RealtimeUrlCrawlInstanceEntity.STATUS_SENDING_TO_SQS, domainId, instanceId, new Date(), true);
        }
        processUrlList(domainId, instanceId, createDate, new Date(), urlList);
    }

    private int getInstanceId(int groupId, int domainId, int createDate) {
        int instanceId = 0;
        RealtimeUrlCrawlInstanceEntity checkInstance = realtimeUrlCrawlInstanceDAO.getByGroupIdAndOid(groupId, domainId);
        if (checkInstance == null) {
            // gen crawlInstance
            RealtimeUrlCrawlInstanceEntity instance = new RealtimeUrlCrawlInstanceEntity();
            instance.setGroupId(groupId);
            instance.setOwnDomainId(domainId);
            instance.setCreateDate(createDate);
            instance.setEnabled(1);
            instance.setUserId(1703);

            instance.setSendStatus(0);
            instance.setUploadStatus(0);
            instance.setFullPathFilename("");
            instance.setCreatedAt(new Date());
            instance.setUrlCountInFile(0);
            instance.setUniqueUrlCount(0);
            instance.setSqsName("");
            instanceId = realtimeUrlCrawlInstanceDAO.insert(instance);
        } else {
            instanceId = checkInstance.getId();
        }
        return instanceId;
    }

    private void processFile(int domainId, int instanceId, String localFilePath, int createDate) {
        if (!isTest) {
            realtimeUrlCrawlInstanceDAO.updateStatusByIdAndOid(RealtimeUrlCrawlInstanceEntity.STATUS_SENDING_TO_SQS, domainId, instanceId, new Date(), true);
        }
        File localFile = new File(localFilePath);
        log.info("===localFileInfo oid:{} instanceId:{} localFilePath:{}", domainId, instanceId, localFilePath);

        List<String> urlList = null;
        try {
            urlList = FileUtils.readLines(localFile);
        } catch (IOException e) {
            log.info("===readFileError oid:{} instanceId:{} localFilePath:{} errMsg:{}", domainId, instanceId, localFilePath, e.getMessage());
            if (!isTest) {
                realtimeUrlCrawlInstanceDAO.updateStatusAndMsgByIdAndOid(RealtimeUrlCrawlInstanceEntity.STATUS_SEND_SQS_ERROR, e.getMessage(), domainId, instanceId);
            }
            e.printStackTrace();
            return;
        }

        if (urlList.isEmpty()) {
            log.info("===urlListEmpty oid:{} instanceId:{} localFilePath:{}", domainId, instanceId, localFilePath);
            return;
        }
        processUrlList(domainId, instanceId, createDate, new Date(), urlList);
    }

    private void processUrlList(int domainId, int instanceId, int createDate, Date date, List<String> urlList) {
        int totalUrlCnt = urlList.size();
        urlList = urlList.stream().distinct().collect(Collectors.toList());
        int uniqueUrlCnt = urlList.size();
        String queueName = QUEUE_NAME_PREFIX + domainId + SPLIT + instanceId;

        updateInstanceInfo(instanceId, totalUrlCnt, uniqueUrlCnt, queueName);
        log.info("===urlListInfo oid:{} instanceId:{} queueName:{} totalUrlCnt:{} uniqueUrlCnt:{}", domainId, instanceId, queueName, totalUrlCnt, uniqueUrlCnt);

        List<String> sendToQueueSuccessList = new ArrayList<>();
        List<String> sendToQueueFailedList = new ArrayList<>();
        List<OpenSearchUrlProperty> messages = new ArrayList<>();

        for (String url : urlList) {
            // https://www.wrike.com/open.htm?id=1649012822
            OpenSearchUrlProperty sqsEntity = new OpenSearchUrlProperty();
            sqsEntity.setUrlType(OpenSearchUrlProperty.URL_TYPE_REALTIME);
            sqsEntity.setDomainId(domainId);
            sqsEntity.setInstanceId(instanceId);
            sqsEntity.setUrlId(0);
            sqsEntity.setUrl(url);
            sqsEntity.setUrlMurmurHash(MurmurHashUtils.getMurmurHash3_64(url));
            sqsEntity.setBrowserMurmurhash("");
            sqsEntity.setSendToQDate(createDate);
            sqsEntity.setKfkTpc(OpenSearchUrlProperty.KAFKA_TOPIC_URL_CRAWL);
            sqsEntity.setResultFields(resultFields);
            sqsEntity.setCreateTime(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
            messages.add(sqsEntity);
            if (messages.size() == DB_BATCH_SIZE) {
                try {
                    openSearchService.bulkIndexDocuments(OpenSearchUrlProperty.INDEX_NAME_REALTIME, messages, OpenSearchUrlProperty::id);
                    messages.forEach(msgEntity -> sendToQueueSuccessList.add(gson.toJson(msgEntity, OpenSearchUrlProperty.class)));
                } catch (Exception e) {
                    log.info("===currentBatchSendToQueueFailed oid:{} instanceId:{} errMsg:{}", domainId, instanceId, e.getMessage());
                    messages.forEach(msg -> sendToQueueFailedList.add(gson.toJson(msg, OpenSearchUrlProperty.class)));
                    e.printStackTrace();
                }
                messages.clear();
            }
        }

        if (!messages.isEmpty()) {
            try {
                openSearchService.bulkIndexDocuments(OpenSearchUrlProperty.INDEX_NAME_REALTIME, messages, OpenSearchUrlProperty::id);
                messages.forEach(msgEntity -> sendToQueueSuccessList.add(gson.toJson(msgEntity, OpenSearchUrlProperty.class)));
            } catch (Exception e) {
                log.info("===currentBatchSendToQueueFailed oid:{} instanceId:{} errMsg:{}", domainId, instanceId, e.getMessage());
                messages.forEach(msg -> sendToQueueFailedList.add(gson.toJson(msg, OpenSearchUrlProperty.class)));
                e.printStackTrace();
            }
            messages.clear();
        }
        int successCnt = sendToQueueSuccessList.size();
        int failedCnt = sendToQueueFailedList.size();
        log.info("===sendToOpenSearch oid:{} instanceId:{} sendSuccessCnt:{} sendFailedCnt:{}", domainId, instanceId, successCnt, failedCnt);

        // 发送worker
//        sentStartWorker(domainId, instanceId);

        if (!isTest) {
            insertBatchDetail(domainId, instanceId, sendToQueueSuccessList, sendToQueueFailedList);
            if(failedCnt > 0) {
                realtimeUrlCrawlInstanceDAO.updateStatusByIdAndOid(RealtimeUrlCrawlInstanceEntity.STATUS_SEND_SQS_ERROR, domainId, instanceId, new Date(), false);
            } else {
                realtimeUrlCrawlInstanceDAO.updateStatusByIdAndOid(RealtimeUrlCrawlInstanceEntity.STATUS_SEND_SQS_SUCCESS, domainId, instanceId, new Date(), false);
            }
        }
    }

    private void updateInstanceInfo(int instanceId, int totalUrlCnt, int uniqueUrlCnt, String queueName) {
        RealtimeUrlCrawlInstanceEntity instance = realtimeUrlCrawlInstanceDAO.getById(instanceId);
        Integer oldUrlCnt = instance.getUrlCountInFile();
        Integer oldUniqueUrlCnt = instance.getUniqueUrlCount();

        int newUrlCnt = totalUrlCnt;
        int newUniqueUrlCnt = uniqueUrlCnt;

        if (oldUrlCnt != null) {
            newUrlCnt += oldUrlCnt;
        }

        if (oldUniqueUrlCnt != null) {
            newUniqueUrlCnt += oldUniqueUrlCnt;
        }
        log.info("===instanceCntInfo instanceId:{} oldUrlCnt:{} oldUniqueUrlCnt:{} newUrlCnt:{} newUniqueUrlCnt:{}", instanceId, oldUrlCnt, oldUniqueUrlCnt, newUrlCnt, newUniqueUrlCnt);
        realtimeUrlCrawlInstanceDAO.updateCntById(instanceId, newUrlCnt, newUniqueUrlCnt, queueName);
    }

    private void insertBatchDetail(int domainId, int instanceId, List<String> successList, List<String> failedList) {
        List<RealtimeUrlCrawlDetailEntity> detailList = new ArrayList<>();
        if (!successList.isEmpty()) {
            for (String msg : successList) {
                RealtimeUrlCrawlDetailEntity detailEntity = generateDetailEntity(msg, true);
                detailList.add(detailEntity);
            }
        }

        if (!failedList.isEmpty()) {
            for (String msg : failedList) {
                RealtimeUrlCrawlDetailEntity detailEntity = generateDetailEntity(msg, false);
                detailList.add(detailEntity);
            }
        }

        log.info("===detailListInfo oid:{} instanceId:{} detailListSize:{} successCnt:{} failedCnt:{}", domainId, instanceId, detailList.size(), successList.size(), failedList.size());
        if (detailList.isEmpty()) {
            return;
        }

        List<String> murmur3HashList = null;
        Map<String, Integer> existsHashMap = null;
        List<RealtimeUrlCrawlDetailEntity> addList = new ArrayList<>();
        List<RealtimeUrlCrawlDetailEntity> updateList = new ArrayList<>();
        int addCnt = 0;
        int updateCnt = 0;

        List<List<RealtimeUrlCrawlDetailEntity>> detailSubList = CollectionSplitUtils.splitCollectionBySize(detailList, DB_BATCH_SIZE);
        for (List<RealtimeUrlCrawlDetailEntity> detailEntityList : detailSubList) {
            murmur3HashList = detailEntityList.stream().map(RealtimeUrlCrawlDetailEntity::getUrlMurmur3Hash).collect(Collectors.toList());
            existsHashMap = realtimeUrlCrawlDetailDAO.getHashListByUniqueKey(instanceId, murmur3HashList);
            if (existsHashMap == null) {
                realtimeUrlCrawlDetailDAO.insertBatch(detailEntityList);
                addCnt += detailEntityList.size();
            } else {
                if (existsHashMap.size() == detailEntityList.size()) {
                    for (RealtimeUrlCrawlDetailEntity detailEntity : detailEntityList) {
                        detailEntity.setId(existsHashMap.get(detailEntity.getUrlMurmur3Hash()));
                    }
                    realtimeUrlCrawlDetailDAO.updateBatch(detailEntityList);
                    updateCnt += detailEntityList.size();
                } else {
                    for (RealtimeUrlCrawlDetailEntity detailEntity : detailEntityList) {
                        if (existsHashMap.containsKey(detailEntity.getUrlMurmur3Hash())) {
                            detailEntity.setId(existsHashMap.get(detailEntity.getUrlMurmur3Hash()));
                            updateList.add(detailEntity);
                        } else {
                            addList.add(detailEntity);
                        }
                    }
                    if (!addList.isEmpty()) {
                        addCnt += addList.size();
                        realtimeUrlCrawlDetailDAO.insertBatch(addList);
                        addList.clear();
                    }
                    if (!updateList.isEmpty()) {
                        updateCnt += updateList.size();
                        realtimeUrlCrawlDetailDAO.updateBatch(updateList);
                        updateList.clear();
                    }
                }
            }
        }
        log.info("===insertBatchDetail oid:{} instanceId:{} detailListSize:{} addCnt:{} updateCnt:{}", domainId, instanceId, detailList.size(), addCnt, updateCnt);
    }


    private boolean downloadFileToLocal(String fileName, int domainId, int instanceId) {
        boolean result = true;
        checkLocalFilePathExist(fileName, domainId);
        log.info("====startCopyFileToLocal oid:{} fileName:{}", domainId, fileName);
        try {
            FTPUtils.copyFileToLocalBySSH(FTP_ADDRESS, FTP_UNAME, FTP_PASSWORD, Collections.singletonList(fileName), FTP_FILE_PATH, LOCAL_PATH);
        } catch (FileNotFoundException fe) {
            result = false;
            log.info("===copyFileFailed fileNotFound oid:{} instanceId:{}fileName:{} errMsg:{}", domainId, instanceId, fileName, fe.getMessage());
            realtimeUrlCrawlInstanceDAO.updateStatusAndMsgByIdAndOid(RealtimeUrlCrawlInstanceEntity.STATUS_SEND_SQS_ERROR, fe.getMessage(), domainId, instanceId);
            fe.printStackTrace();
        } catch (Exception e) {
            result = false;
            log.info("===copyFileFailed oid:{} instanceId:{}fileName:{} errMsg:{}", domainId, instanceId, fileName, e.getMessage());
            realtimeUrlCrawlInstanceDAO.updateStatusAndMsgByIdAndOid(RealtimeUrlCrawlInstanceEntity.STATUS_SEND_SQS_ERROR, e.getMessage(), domainId, instanceId);
            e.printStackTrace();
        }
        return result;
    }

    private void checkLocalFilePathExist(String fileName, int domainId) {
        File parentFile = new File(LOCAL_PATH);
        if (!parentFile.exists() && !parentFile.isDirectory()) {
            log.info("directory not exist. create directory..");
            parentFile.mkdirs();
        }

        String localFilePath = LOCAL_PATH + fileName;
        File localFile = new File(localFilePath);
        if (localFile.exists()) {
            log.info("===fileExist exec delete oid:" + domainId + " fileName:" + fileName);
            localFile.delete();
        }
    }


    private List<String> getCacheList(String keyPrefix) {
        String workerUrl = WorkerUtils.CACHE_LIST_URL + keyPrefix;
        List<String> cacheList = null;
        try {
            cacheList = WorkerUtils.getCacheList(workerUrl);
        } catch (Exception e) {
            log.info("====getCacheListError");
            e.printStackTrace();
        }
        return cacheList;
    }

    private boolean delWorkerKey(String fullKey) {
        log.info("===delKey key:" + fullKey);
        boolean delResult = true;
        try {
            WorkerUtils.doDelete(WorkerUtils.DELETE_CACHE_URL_PREFIX + fullKey, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        } catch (Exception e) {
            delResult = false;
            e.printStackTrace();
        }
        try {
            TimeUnit.SECONDS.sleep(15);
        } catch (Exception e) {
            log.info("===current sleep error key:{}", fullKey);
            e.printStackTrace();
        }
        return delResult;
    }

    private RealtimeUrlCrawlDetailEntity generateDetailEntity(String msg, boolean isSuccess) {
        OpenSearchUrlProperty entity = gson.fromJson(msg, OpenSearchUrlProperty.class);
        RealtimeUrlCrawlDetailEntity detailEntity = new RealtimeUrlCrawlDetailEntity();
        if (isSuccess) {
            detailEntity.setSendStatus(RealtimeUrlCrawlDetailEntity.SEND_TO_SQS);
        } else {
            detailEntity.setSendStatus(RealtimeUrlCrawlDetailEntity.SEND_NOT_SEND);
        }
        detailEntity.setOwnDomainId(entity.getDomainId());
        detailEntity.setCrawlInstanceId(entity.getInstanceId());
        detailEntity.setUrl(entity.getUrl());
        detailEntity.setUrlMurmur3Hash(entity.getUrlMurmurHash());
        detailEntity.setUploadStatus(RealtimeUrlCrawlDetailEntity.SEND_NOT_SEND);
        return detailEntity;
    }
}
