package seoclarity.backend.sender.urlinspection;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.DeleteQueueRequest;
import com.amazonaws.services.sqs.model.GetQueueAttributesRequest;
import com.amazonaws.services.sqs.model.QueueAttributeName;
import com.amazonaws.services.sqs.model.SetQueueAttributesRequest;
import com.google.gson.Gson;
import seoclarity.backend.dao.actonia.GwmDomainRelDAO;
import seoclarity.backend.dao.actonia.UrlInspectionShuffleDAO;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.actonia.GWMDomainRel;
import seoclarity.backend.entity.actonia.UrlInspectionProperty;
import seoclarity.backend.entity.actonia.UrlInspectionShuffleEntity;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.opensearch.OpenSearchUtil;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.SQSUtils;

public class SendInspectUrlToOpenSearch { // https://www.wrike.com/open.htm?id=1565196579
	
	private static final boolean SAVE_TO_OPENSEARCH = true;
	private static final boolean SEND_TO_SQS = false;
	
	// OpenSearch
	private static final String OPEN_SEARCH_INDEX_NAME_PREFIX = "url_inspection_";
	private static final String OPEN_SEARCH_MAPPING_FILE = "urlInspection.mapping";
	private static final int OPEN_SEARCH_DEFAULT_PROCESS_FLG = 0;
	
	private static final String SQS_NAME_GSC = "SQS_IN_URL_INSPECT_GSC_WEEKLY";
	private static final String SQS_NAME_DEAD_LETTER_SUFFIX = "_DL"; // TODO
	private static final String KAFKA_TOPIC_GSC_WEEKLY = "urlInspect_gsc_7";
	
	private static final String DATA_SOURCE_TYPE_GSC = "gsc";
	public static final String RETRIEVE_ERROR_CATEGORY_GSC = "gsc";
	private static final int FREQUENCY_WEEKLY = 7;
	private static final String LOCAL_GSC_PROFILE_TABLE_NAME = "local_gsc_profile";
	private static final String LOCAL_GSC_URL_SHUFFLE_TABLE_NAME = "local_gsc_url_shuffle";
	
	private static final int CDB_DATA_DAYS = 7; // TODO
	private static final int CDB_BCKWARDS_DAYS_WITHOUT_DATA = 3; // TODO
	private static final int DAILY_MAX_URL_COUNT = 2000;
	private static final int AUTHENTICATION_ERR_BCKWARDS_DAYS = 7; // TODO
	
	private static final int THREAD_COUNT = 5;
	private static final int RETRY_COUNT_FOR_SSLEXCEPTION = 10;
	private static final int RETRY_COUNT_EXTRACT_URL = 3;
	private static final int SHUFFLE_URL_QUERY_BATCH_SIZE = 300;
	
	private static final String SITE_URL_PREFIX_HTTP = "http://";
	private static final String SITE_URL_PREFIX_HTTPS = "https://";
	private static final String SITE_URL_TRAILING_SLASH = "/";
	private static final String GWM_PROFILE_NAME_888 = "www.888.com";
	private static final String SITE_URL_888 = "http://www.888.com";
	
	private static final SimpleDateFormat SDF_YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
	private static final SimpleDateFormat SDF_YYYY_MM_DD = new SimpleDateFormat("yyyy-MM-dd");
	private static final SimpleDateFormat SDF_MMDD_HHMMSS = new SimpleDateFormat("MMdd HH:mm:ss");
	private static final SimpleDateFormat SDF_YYYY_MM_DD_HHMMSS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private static final SimpleDateFormat SDF_MMDD = new SimpleDateFormat("MMdd");
	
	private static String dataSourceType;
	private static int frequency;
	private AmazonSQS amazonSQS;
	private String amazonQueueUrl = null;
	
	private GwmDomainRelDAO gwmDomainRelDAO;
	private GscBaseDao gscBaseDao;
	private UrlInspectionShuffleDAO urlInspectionShuffleDAO;
	private static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	private ZeptoMailSenderComponent zeptoMailSenderComponent;
	
	public SendInspectUrlToOpenSearch() {
		gwmDomainRelDAO = SpringBeanFactory.getBean("gwmDomainRelDAO");
		gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
		urlInspectionShuffleDAO = SpringBeanFactory.getBean("urlInspectionShuffleDAO");
		zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
	}

	public static void main(String[] args) throws Exception {
		Date processDate = new Date();
		int dataSource;
		String sqsName = null;
		if (args != null && args.length >= 2) {
			try {
				if (DATA_SOURCE_TYPE_GSC.equalsIgnoreCase(args[0])) {
					dataSourceType = args[0];
					dataSource = GWMDomainRel.DATA_SOURCE_GSC;
					sqsName = SQS_NAME_GSC;
				} else {
					// TODO
					System.out.println("==UnsupportedSourceType:" + args[0]);
					return;
				}
				
				frequency = Integer.parseInt(args[1]);
				if (DATA_SOURCE_TYPE_GSC.equalsIgnoreCase(dataSourceType) && frequency != FREQUENCY_WEEKLY) {
					// TODO
					System.out.println("==UnsupportedGSCFrequency:" + args[1]);
					return;
				}
			} catch (Exception exp) {
				exp.printStackTrace();
				System.out.println("==InvalidParameters:" + args);
				return;
			}
		} else {
			System.out.println("==InvalidParameters:" + args);
			return;
		}
		
		Date periodStartDate = null;
		boolean isPeriodStartDay = false;
		if (frequency == FREQUENCY_WEEKLY) {
			periodStartDate = DateUtils.truncate(CommonUtils.getLastSunday(processDate), Calendar.DAY_OF_MONTH);
			isPeriodStartDay = CommonUtils.isStartDayOfWeek(processDate);
		} 
		System.out.println("=====Parameters dataSource:" + dataSourceType + "(" + dataSource + ") freq:" + frequency + " isPStartDay:" + isPeriodStartDay + 
			"(" + SDF_YYYYMMDD.format(periodStartDate) + ") Q:" + sqsName + " startAt:" + SDF_MMDD_HHMMSS.format(processDate));
		
		try {
			SendInspectUrlToOpenSearch ins = new SendInspectUrlToOpenSearch();
			threadPool.init();
	        CommonUtils.initThreads(THREAD_COUNT);
			ins.startProcess(dataSource, processDate, periodStartDate, isPeriodStartDay, sqsName);
		} finally {
			threadPool.destroy();
		}
	}
	
	private void startProcess(int dataSource, Date processDate, Date periodStartDate, boolean isPeriodStartDay, String sqsName) {
		OpenSearchUtil openSearchUtil = new OpenSearchUtil(null);
		try {
			Date logDate = DateUtils.truncate(processDate, Calendar.DAY_OF_MONTH);
			int authenticateFromDate = Integer.parseInt(SDF_YYYYMMDD.format(DateUtils.addDays(processDate, -AUTHENTICATION_ERR_BCKWARDS_DAYS)));
			String gscStartDate = SDF_YYYY_MM_DD.format(DateUtils.addDays(processDate, -(CDB_DATA_DAYS + CDB_BCKWARDS_DAYS_WITHOUT_DATA)));
			String gscEndDate = SDF_YYYY_MM_DD.format(DateUtils.addDays(processDate, -CDB_BCKWARDS_DAYS_WITHOUT_DATA));
			
			// url_inspection_shuffle existence check
			if (!shouldSKipShuffleAtPeriodStartDay(isPeriodStartDay, dataSource, periodStartDate, processDate, sqsName)) {
				// Get GSC profile list
				List<GWMDomainRel> gscProfileList = getGscProfileList(dataSource, isPeriodStartDay, authenticateFromDate);
				System.out.println("===startProcess dataSource:" + dataSource + " freq:" + frequency + " gscProfileList:" + 
					(gscProfileList != null ? gscProfileList.size() : 0) + " authenticateFromDay:" + authenticateFromDate +
					" gscDateRange:" + gscStartDate + "  " + gscEndDate);
				if (gscProfileList != null && gscProfileList.size() > 0) {
					// save profiles to GSC local_gsc_profile
					int profileCnt = saveProfileToCDB(dataSource, periodStartDate, logDate, gscProfileList, LOCAL_GSC_PROFILE_TABLE_NAME);
					System.out.println(" EndInsProfileToCDB:" + LOCAL_GSC_PROFILE_TABLE_NAME + " periodDate:" + SDF_YYYYMMDD.format(periodStartDate) + 
						" logDate:" + SDF_YYYYMMDD.format(logDate) + " cnt:" + gscProfileList.size() + "=>" + profileCnt);
					
					// extract GSC URLs to local_gsc_url_shuffle
					int urlCnt = extractUrlInCDB(dataSource, periodStartDate, logDate, isPeriodStartDay, gscStartDate, gscEndDate, gscProfileList, 
						LOCAL_GSC_PROFILE_TABLE_NAME, LOCAL_GSC_URL_SHUFFLE_TABLE_NAME);
					System.out.println(" EndInsURLToCDB:" + LOCAL_GSC_URL_SHUFFLE_TABLE_NAME + " periodDate:" + SDF_YYYYMMDD.format(periodStartDate) + 
						" logDate:" + SDF_YYYYMMDD.format(logDate) + " cnt:" + urlCnt + " at " + SDF_MMDD_HHMMSS.format(new Date()));
					
					// save URLs to db80 url_inspection_shuffle
					Map<Integer, String> profileMap = constructProfileMap(gscProfileList); // key: profileId, value: formattedProfileName
					fillUrlInspectionShuffle(dataSource, isPeriodStartDay, periodStartDate, logDate, profileMap, LOCAL_GSC_URL_SHUFFLE_TABLE_NAME);
				}
			}
			sleep(10);
			
			// Send URLs to OpenSearch/SQS for inspection
			sendUrlToIntermediateStorage(dataSource, periodStartDate, logDate, sqsName, openSearchUtil);
			
		} catch (Exception exp) {
			exp.printStackTrace();
			sendMailReport("SendInspectUrlToOpenSearchError:", exp.getMessage());
		} finally {
			if (SAVE_TO_OPENSEARCH) {
				openSearchUtil.closeConnection();
			}
		}
		System.out.println("===EndProcess dataSource:" + dataSourceType + " freq:" + frequency + " at " + SDF_MMDD_HHMMSS.format(new Date()));
	}
	
	private boolean shouldSKipShuffleAtPeriodStartDay(boolean isPeriodStartDay, int dataSource, Date periodStartDate, Date processDate, String sqsName) {
		if (isPeriodStartDay) {
			int urlCnt = urlInspectionShuffleDAO.getUrlCount(dataSource, periodStartDate);
			if (urlCnt > 0) {
				System.out.println("==SkipShuffleAsDataExist dataSource:" + dataSource + " freq:" + frequency + " processDate:" + 
					SDF_YYYYMMDD.format(processDate) + " periodStartDate:" + SDF_YYYYMMDD.format(periodStartDate) + " urlCnt:" + urlCnt);
				return true;
			} else {
				if (SEND_TO_SQS) {
					clearSQS(sqsName);
				}
			}
		}
		return false;
	}
	
	private List<GWMDomainRel> getGscProfileList(int dataSource, boolean isPeriodStartDay, int authenticateFromDate) {
		if (DATA_SOURCE_TYPE_GSC.equals(dataSourceType)) {
			if (isPeriodStartDay) {
				return gwmDomainRelDAO.getProfileList(dataSource, RETRIEVE_ERROR_CATEGORY_GSC, authenticateFromDate);
			} else {
				// TODO implement at next phase
				
			}
		} else {
			// TODO for other data source
			
		}
		return null;
	}
	
	private int saveProfileToCDB(int dataSource, Date periodStartDate, Date logDate, List<GWMDomainRel> gscProfileList, String cdbProfileTableName) {
		List<GscEntity> profileList = new ArrayList<GscEntity>();
		for (GWMDomainRel gscDomainRel : gscProfileList) {
			GscEntity gscEntity = new GscEntity();
			gscEntity.setPeriodStartDate(periodStartDate);
			gscEntity.setDataSource(dataSource);
			gscEntity.setLogDate(logDate);
			gscEntity.setOwnDomainId(gscDomainRel.getOwnDomainId());
			gscEntity.setRelId(gscDomainRel.getId());
			gscEntity.setProfileName(gscDomainRel.getGwmDomainName());
			gscEntity.setFormattedProfileName(gscDomainRel.getFormattedProfileName());
			gscEntity.setIsAuthenticationIssue(gscDomainRel.getIsAuthenticationIssue());
			profileList.add(gscEntity);
		}
		System.out.println("  ##InsCDBProfileTbl:" + cdbProfileTableName + " pDate:" + SDF_YYYYMMDD.format(periodStartDate) + 
			" logDate:" + SDF_YYYYMMDD.format(logDate) + " cnt:" + profileList.size() + " at " + SDF_MMDD_HHMMSS.format(new Date()));
		gscBaseDao.insertLocalGscProfile(profileList, cdbProfileTableName);
		
		sleep(60);
		return gscBaseDao.getCount(SDF_YYYY_MM_DD.format(periodStartDate), SDF_YYYY_MM_DD.format(logDate), cdbProfileTableName);
	}
	
	private int extractUrlInCDB(int dataSource, Date periodStartDate, Date logDate, boolean isPeriodStartDay, String gscStartDate, String gscEndDate, 
			List<GWMDomainRel> gscProfileList, String cdbProfileTableName, String cdbUrlShuffleTableName) throws Exception {
		int maxUrlCntPerProfile = 0;
		if (isPeriodStartDay) {
			maxUrlCntPerProfile = DAILY_MAX_URL_COUNT * FREQUENCY_WEEKLY;
		} else {
			// TODO new profile
			
			
			
		}
		
		int retryCnt = 0;
		while (true) {
			try {
				System.out.println(" ##InsCDBUrlTbl:" + cdbUrlShuffleTableName + " " + dataSourceType + "_" + frequency + " thresholdCnt:" + maxUrlCntPerProfile + 
					" gscDateRange:" + gscStartDate + "  " + gscEndDate + " retryCnt:" + retryCnt + " at " + SDF_MMDD_HHMMSS.format(new Date()));
				gscBaseDao.insertLocalGscUrlShuffle(dataSource, SDF_YYYY_MM_DD.format(periodStartDate), SDF_YYYY_MM_DD.format(logDate),  
					gscStartDate, gscEndDate, maxUrlCntPerProfile, cdbProfileTableName, cdbUrlShuffleTableName);
				break;
			} catch (Exception exp) {
				exp.printStackTrace();
				System.out.println(" ==InsUrlToCDBError:" + exp.getMessage() + " retryCnt:" + retryCnt);
				if (retryCnt >= RETRY_COUNT_EXTRACT_URL) {
					System.out.println(" ==InsUrlToCDBError ExitAfterRetry:" + exp.getMessage() + " retryCnt:" + retryCnt);
					throw exp;
				}
				retryCnt++;
				sleep(30 * retryCnt);
			}
		}
		return gscBaseDao.getCount(SDF_YYYY_MM_DD.format(periodStartDate), SDF_YYYY_MM_DD.format(logDate), cdbUrlShuffleTableName);
	}
	
	private Map<Integer, String> constructProfileMap(List<GWMDomainRel> gscProfileList) {
		Map<Integer, String> map = new HashMap<Integer, String>();
		for (GWMDomainRel gscDomainRel : gscProfileList) {
			if (gscDomainRel.getIsAuthenticationIssue() == null || gscDomainRel.getIsAuthenticationIssue().intValue() != 1) {
				map.put(gscDomainRel.getId(), gscDomainRel.getFormattedProfileName());
			}
		}
		System.out.println(" constructProfileMap list:" + gscProfileList.size() + "=>" + map.size());
		return map;
	}
	
	private void fillUrlInspectionShuffle(int dataSource, boolean isPeriodStartDay, Date periodStartDate, Date logDate, Map<Integer, String> profileMap, 
			String cdbUrlShuffleTableName) {
		int hashModCnt = 200;
		for (int hashMod = 0; hashMod < hashModCnt; hashMod++) {
			List<GscEntity> urlList = null;
			int retryCnt = 0;
			while (true) {
				try {
					// formattedProfileName, urlMurmurHash, url, clicks, profileList
					urlList = gscBaseDao.getUrlList(dataSource, SDF_YYYY_MM_DD.format(periodStartDate), SDF_YYYY_MM_DD.format(logDate), cdbUrlShuffleTableName, 
						hashModCnt, hashMod);
					System.out.println(" GotCDBUrlList " + dataSourceType + "_" + SDF_YYYYMMDD.format(periodStartDate) + "_" + SDF_YYYYMMDD.format(logDate) + 
						" hashMod:" + hashModCnt + "/" + hashMod + " retrys:" + retryCnt + " urlCnt:" + (urlList != null ? urlList.size() : 0) + 
						" at " + SDF_MMDD_HHMMSS.format(new Date()));
					break;
				} catch (Exception exp) {
					exp.printStackTrace();
					System.out.println(" ==ExtractUrlInCDBError:" + exp.getMessage() + " retryCnt:" + retryCnt);
					if (retryCnt >= RETRY_COUNT_EXTRACT_URL) {
						System.out.println(" ==ExtractUrlInCDBError ExitAfterRetry:" + exp.getMessage() + " retryCnt:" + retryCnt);
						throw exp;
					}
					retryCnt++;
					sleep(retryCnt * 30);
				}
			}
			
			if (urlList != null && urlList.size() > 0) {
				Map<String, List<GscEntity>> urlMap = new HashMap<String, List<GscEntity>>(); // key: formattedProfileName
				for (GscEntity gscEntity : urlList) {
					String formattedProfileName = gscEntity.getFormattedProfileName();
					List<GscEntity> list = urlMap.get(formattedProfileName);
					if (list == null) {
						list = new ArrayList<GscEntity>();
						urlMap.put(formattedProfileName, list);
					}
					list.add(gscEntity);
				}
				for (String key : urlMap.keySet()) {
					saveShuffleUrl(dataSource, isPeriodStartDay, periodStartDate, logDate, key, urlMap.get(key), profileMap);
				}
			}
		}
	}
	
	private String[] getMatchedProfileId(String profileList, String formattedProfileName, Map<Integer, String> profileMap) {
		String[] oidProfileArr = profileList.split(","); // oid1_pId1,oid2_pId2,oid3_pId3
		for (String oidProfile : oidProfileArr) {
			String[] tmpArr = oidProfile.split("_");
			int profileId = Integer.parseInt(tmpArr[1]);
			String fProfileName = profileMap.get(profileId);
			if (StringUtils.isNotEmpty(fProfileName) && fProfileName.equals(formattedProfileName)) { // TODO equalsIgnoreCase?
				return tmpArr;
			}
		}
		return null;
	}
	
	private void saveShuffleUrl(int dataSource, boolean isPeriodStartDay, Date periodStartDate, Date logDate, String formattedProfileName, 
			List<GscEntity> urlList, Map<Integer, String> profileMap) {
		System.out.println(" saveShuffleUrl(PStartDay:" + isPeriodStartDay + ") " + dataSource + "_" + frequency + "_" + SDF_YYYYMMDD.format(periodStartDate) + 
			"_" + SDF_YYYYMMDD.format(logDate) + " profile:" + formattedProfileName + " urlCnt:" + urlList.size());
		int batchSize = 1000;
		Date tillDate = DateUtils.addDays(periodStartDate, frequency);
		List<UrlInspectionShuffleEntity> urlShuffleList = new ArrayList<UrlInspectionShuffleEntity>();
		Date sendDate = logDate;
		int sendDateInt = Integer.parseInt(SDF_YYYYMMDD.format(sendDate));
		int cnt = 0;
		for (GscEntity gscEntity : urlList) {
			UrlInspectionShuffleEntity shuffleEntity = new UrlInspectionShuffleEntity();
			shuffleEntity.setFrequency(frequency);
			shuffleEntity.setPeriodStartDate(periodStartDate);
			shuffleEntity.setSendDate(sendDateInt);
			shuffleEntity.setDataSource(dataSource);
			shuffleEntity.setUrl(gscEntity.getUrl());
			shuffleEntity.setUrlMurmur3Hash(gscEntity.getUrlMurmurHash());
			
			String profileList = gscEntity.getProfileList(); // oid1_pId1,oid2_pId2,oid3_pId3
			String[] oidProfileIdArr = getMatchedProfileId(profileList, formattedProfileName, profileMap);
			if (oidProfileIdArr == null) {
				oidProfileIdArr = getMatchedProfileId(profileList, formattedProfileName.substring(0, formattedProfileName.length() - 1), profileMap);
			}
			if (oidProfileIdArr == null) {
				// TODO
				System.out.println(" AbnromalNotFoundProfile prfList:" + profileList + " fPrfName:" + formattedProfileName);
				continue;
			}
			shuffleEntity.setOwnDomainId(Integer.parseInt(oidProfileIdArr[0]));
			shuffleEntity.setProfileId(Integer.parseInt(oidProfileIdArr[1]));
			shuffleEntity.setProfileList(profileList);
			shuffleEntity.setSendStatus(UrlInspectionShuffleEntity.SEND_STATUS_NOT_STARTED);
			urlShuffleList.add(shuffleEntity);
			cnt++;
			if (cnt >= DAILY_MAX_URL_COUNT) {
				saveUrlInspectionShuffle(urlShuffleList);
				urlShuffleList = new ArrayList<UrlInspectionShuffleEntity>();
				sendDate = DateUtils.addDays(sendDate, 1); // change date
				if (sendDate.compareTo(tillDate) >= 0) {
					System.out.println(" ReachedNextPeriod:" + SDF_YYYYMMDD.format(sendDate) + " freq:" + frequency);
					break;
				}
				sendDateInt = Integer.parseInt(SDF_YYYYMMDD.format(sendDate));
				System.out.println(" ChgSendDate=>" + sendDateInt + " resetCnt:" + cnt);
				cnt = 0;
			}
			
			if (urlShuffleList.size() >= batchSize) {
				saveUrlInspectionShuffle(urlShuffleList);
				urlShuffleList = new ArrayList<UrlInspectionShuffleEntity>();
			}
		}
		
		if (urlShuffleList.size() > 0) {
			saveUrlInspectionShuffle(urlShuffleList);
		}
	}
	
	private void saveUrlInspectionShuffle(List<UrlInspectionShuffleEntity> urlShuffleList) {
		System.out.println("  ##saveUrlToDB cnt:" + urlShuffleList.size());
		urlInspectionShuffleDAO.insertData(urlShuffleList);
	}
	
	private void sendUrlToIntermediateStorage(int dataSource, Date periodStartDate, Date logDate, String sqsName, OpenSearchUtil openSearchUtil) {
		if (SEND_TO_SQS) {
			initAmazonSQS(sqsName);
		}
		
		long a = System.currentTimeMillis();
		int totalCnt = 0;
		int sendToQDate = Integer.parseInt(SDF_YYYYMMDD.format(periodStartDate));
		String openSearchIndexName = OPEN_SEARCH_INDEX_NAME_PREFIX + sendToQDate;
		if (SAVE_TO_OPENSEARCH) {
			openSearchUtil.connectAndCreateIndexIfNotExist(openSearchIndexName, OPEN_SEARCH_MAPPING_FILE);
		}
		
		String pStartDay = SDF_MMDD.format(periodStartDate);
		Date sendDate = periodStartDate;
		int savedToOpenSearchCnt = 0;
		long updSendStatusSeconds = 0;
		while (true) {
			int sentCnt = 0;
			int sendDateInt = Integer.parseInt(SDF_YYYYMMDD.format(sendDate));
			String sendDay = SDF_MMDD.format(sendDate);
			int idx = 0;
			while (true) {
				idx++;
				List<UrlInspectionShuffleEntity> urlShuffleList = urlInspectionShuffleDAO.getUrlList(dataSource, frequency, periodStartDate, sendDateInt, 
					SHUFFLE_URL_QUERY_BATCH_SIZE);
				if (urlShuffleList == null || urlShuffleList.size() == 0) {
					System.out.println((idx == 1 ? " NoURL " : " NoMoreUrl ") + dataSource + "_" + frequency + "_" + sendToQDate + "_" + sendDateInt + "_" + idx);
					break;
				}
				sentCnt += urlShuffleList.size();
				System.out.println(" GotUrlToSend " + dataSource + "_" + frequency + "_" + pStartDay + "_" + sendDay + "_" + idx + " cnt:" + 
					urlShuffleList.size() + " at " + SDF_MMDD_HHMMSS.format(new Date()));
				updSendStatusSeconds += sendUrl(dataSource, periodStartDate, sendToQDate, sendDateInt, urlShuffleList, sqsName, openSearchIndexName, openSearchUtil);
				savedToOpenSearchCnt += logOpenSearchWriteCount(savedToOpenSearchCnt, openSearchUtil, false);
			}
			totalCnt += sentCnt;
			savedToOpenSearchCnt += logOpenSearchWriteCount(savedToOpenSearchCnt, openSearchUtil, true);
			System.out.println(" SentURLs " + dataSource + "_" + frequency + "_" + pStartDay + "_" + sendDay + "_" + idx + 
					" cnt:" + sentCnt + "(total:" + totalCnt + ") at " + SDF_MMDD_HHMMSS.format(new Date()));
			sendDate = DateUtils.addDays(sendDate, 1);
			if (SEND_TO_SQS && sendDate.compareTo(logDate) > 0 || 
					SAVE_TO_OPENSEARCH && sendDate.compareTo(DateUtils.addDays(periodStartDate, 7)) >= 0) {
				break;
			}
		}
		System.out.println("==sendUrlToIntermediateStorage totalCnt:" + totalCnt + " time:" + (System.currentTimeMillis() - a) * 1.0 / 1000 + 
			" updSendStatusSeconds:" + updSendStatusSeconds + "s");
	}
	
	private int logOpenSearchWriteCount(int savedToOpenSearchCnt, OpenSearchUtil openSearchUtil, boolean forceFlg) {
		int writeRequestCount = openSearchUtil.getWriteRequestCount();
		int writeCount = openSearchUtil.getWriteCount();
		int writeFailedCount = openSearchUtil.getWriteFailedCount();
		if (forceFlg || writeRequestCount > 0 && (writeRequestCount - savedToOpenSearchCnt) / 100000 > 0) {
			System.out.println(" OSWriteStat req:" + writeRequestCount + " write:" + writeCount + " fail:" + writeFailedCount);
			savedToOpenSearchCnt = writeCount;
		}
		return writeCount;
	}
	
	private long sendUrl(int dataSource, Date periodStartDate, int sendToQDate, int sendDate, List<UrlInspectionShuffleEntity> urlShuffleList, 
			String sqsName, String openSearchIndexName, OpenSearchUtil openSearchUtil) {
		List<UrlInspectionProperty> urlPropertyList = new ArrayList<UrlInspectionProperty>();
		List<String> urlMurmur3HashList = new ArrayList<String>();
		String createTime = SDF_YYYY_MM_DD_HHMMSS.format(new Date());
		for (UrlInspectionShuffleEntity urlShuffleEntity : urlShuffleList) {
			UrlInspectionProperty inspectUrlProperty = new UrlInspectionProperty();
//			uis.periodStartDate, uis.dataSource, uis.url, uis.urlMurmur3Hash, uis.ownDomainId, uis.profileId, uis.profileList, uis.sendStatus,
//	        rel.gwm_domain_name, formattedProfileName, ge.gsc_refresh_token, gop.clientId, gop.encryptSecret
			inspectUrlProperty.setDataSource(urlShuffleEntity.getDataSource());
			inspectUrlProperty.setFrequency(frequency);
			inspectUrlProperty.setDomainId(urlShuffleEntity.getOwnDomainId());
			inspectUrlProperty.setProfileId(urlShuffleEntity.getProfileId());
			inspectUrlProperty.setProfileList(urlShuffleEntity.getProfileList());
			inspectUrlProperty.setSiteUrl(formatSiteUrl(urlShuffleEntity.getFormattedProfileName()));
			inspectUrlProperty.setUrl(urlShuffleEntity.getUrl());
			inspectUrlProperty.setUrlMurmurHash(urlShuffleEntity.getUrlMurmur3Hash());
			inspectUrlProperty.setGscToken(urlShuffleEntity.getGscRefreshToken());
			inspectUrlProperty.setClientId(urlShuffleEntity.getClientId());
			inspectUrlProperty.setEncryptSecret(urlShuffleEntity.getEncryptSecret());
			inspectUrlProperty.setSendToQDate(sendToQDate);
			inspectUrlProperty.setKfkTpc(KAFKA_TOPIC_GSC_WEEKLY);
			
			if (SEND_TO_SQS) {
				urlPropertyList.add(inspectUrlProperty);
			}
			if (SAVE_TO_OPENSEARCH) {
				inspectUrlProperty.setProcessDate(sendDate);
				inspectUrlProperty.setProcessFlg(OPEN_SEARCH_DEFAULT_PROCESS_FLG);
				inspectUrlProperty.setCreateTime(createTime);
				openSearchUtil.batchWrite(openSearchIndexName, inspectUrlProperty, urlShuffleEntity.getUrlMurmur3Hash());
			}
			urlMurmur3HashList.add(urlShuffleEntity.getUrlMurmur3Hash());
		}
		
		if (urlPropertyList.size() > 0 && SEND_TO_SQS) {
			System.out.println(" SendUrlToSQS cnt:" + urlShuffleList.size() + "->" + urlPropertyList.size() + " at " + SDF_MMDD_HHMMSS.format(new Date()));
			startSendCommand(amazonQueueUrl, sendToQDate, urlPropertyList, sqsName);
		}
		
		long a = System.currentTimeMillis();
		if (urlMurmur3HashList.size() > 0) {
			System.out.println(" ##UpdSendStat " + dataSource + "_" + frequency + "_" + sendToQDate + "_" + sendDate + "(" + urlShuffleList.size() + ")");
			urlInspectionShuffleDAO.updateSendStatus(dataSource, frequency, periodStartDate, sendDate, urlMurmur3HashList);
		}
		return System.currentTimeMillis() - a;
	}
	
	private String formatSiteUrl(String gwmProfileName) {
		// A: Append trailing slash to profileName for profileName starts with "http://" or "https://" without trailing slash
		// B: Change profileName "www.888.com" to "http://www.888.com"
		String siteUrl = gwmProfileName;
		if ((siteUrl.startsWith(SITE_URL_PREFIX_HTTP) || siteUrl.startsWith(SITE_URL_PREFIX_HTTPS)) && !siteUrl.endsWith(SITE_URL_TRAILING_SLASH) ) {
			System.out.println(" ChgSiteUrl " + siteUrl + "->" + (siteUrl + SITE_URL_TRAILING_SLASH));
			return siteUrl + SITE_URL_TRAILING_SLASH;
		} else if (GWM_PROFILE_NAME_888.equals(siteUrl)) {
			System.out.println(" ChgSiteUrl " + siteUrl + "->" + SITE_URL_888);
			return SITE_URL_888;
		}
		return gwmProfileName;
	}
	
	private void startSendCommand(String amazonSQSUrl, int sendToQDate, List<UrlInspectionProperty> propertyList, String sqsName) {
		String ipAddress = null;
		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				sleep(1);
			}
		} while(ipAddress == null);
		SendCommand cmd = new SendCommand(ipAddress, amazonSQS, amazonSQSUrl, sendToQDate, propertyList, sqsName);
		cmd.setStatus(true);
		
		try {
			threadPool.execute(cmd);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void initAmazonSQS(String sqsName) {
		int retryCount = 0;
		while (true) {
			try {
				String accessKeyId = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextSQSAccessKey();
				String secretAccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey();
				amazonSQS = SQSUtils.getAmazonSQS(accessKeyId, secretAccessKey);
				amazonQueueUrl = SQSUtils.createQueue(sqsName, amazonSQS);
				System.out.println(" ==CreatedSQS:" + sqsName + "(retrys:" + retryCount + ") at " + SDF_MMDD_HHMMSS.format(new Date()));
				enableDeadLetterQueue(amazonSQS, amazonQueueUrl, sqsName);
				break;
			} catch (Exception exp) {
				exp.printStackTrace();
				retryCount++;
				System.out.println("======Connect to AmazonSQS failed. retryCount:" + retryCount);
				if (retryCount >= RETRY_COUNT_FOR_SSLEXCEPTION) {
					System.out.println("Connect to AmazonSQS failed sqsName:" + sqsName);
					sendMailReport("initAmazonSQS Failed sqsName:" + sqsName, exp.getMessage());
					return;
				}
				sleep(30);
			}
		}
	}
	
	private void clearSQS(String sqsName) {
		if (amazonSQS == null) {
			initAmazonSQS(sqsName);
		}
		int retryCount = 0;
		while (true) {
			try {
				amazonQueueUrl = SQSUtils.createQueue(sqsName, amazonSQS);
				System.out.println(" ==CreatedSQS:" + sqsName + "(retrys:" + retryCount + ") at " + SDF_MMDD_HHMMSS.format(new Date()));
				try {
					Thread.sleep(60000);
				} catch (Exception e) {
				}
				
				System.out.println(" ===DeleteSQS:" + sqsName + " at " + SDF_MMDD_HHMMSS.format(new Date()));
				amazonSQS.deleteQueue(new DeleteQueueRequest(amazonQueueUrl)); // TODO
				Thread.sleep(120000);
				amazonQueueUrl = SQSUtils.createQueue(sqsName, amazonSQS);
				System.out.println(" ==ReCreatedSQS:" + sqsName + "(retrys:" + retryCount + ") at " + SDF_MMDD_HHMMSS.format(new Date()));
				break;
			} catch (Exception exp) {
				exp.printStackTrace();
				retryCount++;
				System.out.println("======ReCreateQueue failed(SSLException) retryCnt:" + retryCount);
				if (retryCount >= RETRY_COUNT_FOR_SSLEXCEPTION) {
					System.out.println("clearSQS CreateQueue failed! " + sqsName);
					sendMailReport("Failed", sqsName);
				}
				try {
					Thread.sleep(30000);
				} catch (Exception sleepException) {
					sleepException.printStackTrace();
				}
			}
		}
	}
	
    private void enableDeadLetterQueue(AmazonSQS sqs, String queryUrl, String sqsName) {
    	String deadLetterSqsName = sqsName + SQS_NAME_DEAD_LETTER_SUFFIX;
        String deadQueryUrl = SQSUtils.createQueue(deadLetterSqsName, sqs);
        Map attributes = sqs.getQueueAttributes(new GetQueueAttributesRequest(deadQueryUrl).withAttributeNames(QueueAttributeName.QueueArn)).getAttributes();
        String dlqArn = (String) attributes.get(QueueAttributeName.QueueArn.toString());
        String redrivePolicy = String.format("{\"maxReceiveCount\":\"5\", \"deadLetterTargetArn\":\"%s\"}", dlqArn);
        sqs.setQueueAttributes(new SetQueueAttributesRequest().withQueueUrl(queryUrl).addAttributesEntry(QueueAttributeName.RedrivePolicy.toString(), redrivePolicy));
        System.out.println("=enableDeadLetterSQS:" + sqsName + " dlSQS:" + deadLetterSqsName);
    }
    
    class SendCommand extends BaseThreadCommand {
    	private String ip;
		private AmazonSQS amazonSQS;
		private String queryUrl;
		private List<UrlInspectionProperty> urlList;
		private int sendToQDate;
		private String sqsName;
		
		SendCommand(String ip, AmazonSQS amazonSQS, String queryUrl, int sendToQDate, List<UrlInspectionProperty> urlList, String sqsName) {
			this.ip = ip;
			this.amazonSQS = amazonSQS;
			this.queryUrl = queryUrl;
			this.sendToQDate = sendToQDate;
			this.urlList = urlList;
			this.sqsName = sqsName;
		}
    	
    	@Override
        protected void execute() throws Exception {
            long a = System.currentTimeMillis();
            System.out.println("StartCmdIP:" + ip + " QD:" + sendToQDate + " cnt:" + (urlList == null ? 0 : urlList.size()) + " queryUrl:" + queryUrl);
            try {
                if (urlList != null && urlList.size() > 0) {
                	sendUrlsToSQS();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            CacheModleFactory.getInstance().setAliveIpAddress(ip);
            long b = System.currentTimeMillis();
            System.out.println("EndCmdIP:" + ip + " time: " + (b - a) * 1.0 / 1000 + "s");
        }
    	
    	private void sendUrlsToSQS() {
    		Map<String, String> messages = new HashMap<String, String>();
			for (int idx = 0; idx < urlList.size(); idx++) {
				UrlInspectionProperty urlProperty = urlList.get(idx);
				try {
					String jsonMessage = new Gson().toJson(urlProperty);
					String key = String.valueOf(urlProperty.getUrlMurmurHash()) + Float.valueOf(String.valueOf(Math.random() * 1000)).intValue();
					messages.put(key, jsonMessage);
					if (messages.size() >= 10) {
						try {
							sendBatchMessageToSQS(messages);
							messages = new HashMap<String, String>();
							Thread.sleep(100);
						} catch (Exception e) {
							System.out.println("FaildSendCommand IP:" + ip + " messages:" + messages.size());
							e.printStackTrace();
						}
					}
					
					try {
						if ("1".equals(ip) && idx == 0) {
							System.out.println(" SQS:" + sqsName + " JsonMsg:" + jsonMessage);
						}
						System.out.println("=SendURL ds:" + urlProperty.getDataSource() + " freq:" + urlProperty.getFrequency() + " QD:" + urlProperty.getSendToQDate() + 
							" OID:" + urlProperty.getDomainId() + " pId:" + urlProperty.getProfileId() + " pList:" + urlProperty.getProfileList() + 
							" siteUrl:" + urlProperty.getSiteUrl() + " murmurHsh:" + urlProperty.getUrlMurmurHash() + " url:" + urlProperty.getUrl() + 
							" token:" + urlProperty.getGscToken() + " cid:" + urlProperty.getClientId() + " srt:" + urlProperty.getEncryptSecret() + " tpc:" + urlProperty.getKfkTpc());
					} catch (Exception exp) {
						exp.printStackTrace();
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			if (messages.size() > 0) {
				try {
					sendBatchMessageToSQS(messages);
				} catch (Exception e) {
					System.out.println("FaildSendCommand IP:" + ip + " messages:" + messages.size());
					e.printStackTrace();
				}
			}
    	}
    	
        private void sendBatchMessageToSQS(Map<String, String> messages) {
        	int cnt = 0;
        	int maxTryCnt = 3;
        	while(true) {
        		try {
        			if (cnt > 0) {
        				System.out.println("=send re-try:" + cnt);
        			}
        			SQSUtils.sendBatchMessageToQueue(amazonSQS, queryUrl, messages);
        			break;
        		} catch (Exception e) {
        			System.out.println("SendSQS faild queryUrl:" + queryUrl + " messages:" + messages.size());
        			e.printStackTrace();
        			cnt++;
        			if (cnt >= maxTryCnt) {
        				System.out.println("=SendSQS failed! re-try cnt:" + cnt);
        				e.printStackTrace();
            			break;
            		}
        			sleep(1);
    			}
        	}
        }
    	
        @Override
        protected void undo() throws Exception {
        }
    }
    
	private void sleep(int seconds) {
		try {
			Thread.sleep(seconds * 1000);
		} catch (Exception exp) {
			exp.printStackTrace();
		}
	}
    
    public void sendMailReport(String subject, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Wilber");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String[] ccTo = null;
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", 
        	reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }
}