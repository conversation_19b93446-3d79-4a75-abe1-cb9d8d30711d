package hao_test;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.S3Object;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.io.File;

public class S3Test {
//    private static final String S3_BUCKET_NAME = "fanatics.prod.partner.regulated.botclarity";

    private static final String S3_BUCKET_NAME = "msci-seoclarity-extracts";
    private static final String S3_FOLDER = "gfj/";
    private static final String FOLDER_V2 = "gfj_intl/";
    static final String S3_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
    static final String S3_SECRET_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
    static final BasicAWSCredentials awsCreds = new BasicAWSCredentials(S3_ACCESS_KEY, S3_SECRET_KEY);
    static final AmazonS3 s3 = AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(awsCreds)).withRegion("us-east-2").build();

    public static void main(String[] args) {
        String folder = "";
        if (null != args && args.length > 0) {
            folder = args[0];
        }
        S3Test in = new S3Test();
        in.process(folder);
    }

    private void process(String path) {
        //" ===###文件可能为空文件，检查一下 ======================="
        String filePath = "files/adhoc/";
        filePath = filePath + path;
        for (File file : new File(filePath).listFiles()) {
            System.out.println("===###file : " + file.getName());
            //JOB_ADHOC_8762_2709_20240726_fix.txt
            Integer domainId = Integer.parseInt(file.getName().split("_")[2]);
            String folder =FOLDER_V2;
            if (domainId == 8711){
                folder = S3_FOLDER;
            }
            sendToS3(file,folder,domainId);
        }
    }


    private void sendToS3(File f) {
        // S3 path format: s3://fanatics.prod.partner.regulated.botclarity/${date}/${domain}_${date}_log.csv


        String s3FilePath = "gfj_intl/"+f.getName();;
        System.out.println("*** " + s3FilePath);
        try {
            s3.putObject(S3_BUCKET_NAME, s3FilePath, f);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(" ###===sendToS3 success");
    }

    private void sendToS3(File f,String folder,Integer domainId) {
        String s3FilePath = ""; //https://www.wrike.com/open.htm?id=1427303648
        //8756 - gfj_intl/ca/JOB_ADHOC_8756_xxxx_yyyymmdd.txt.gz
        //8758 - gfj_intl/de/JOB_ADHOC_8758_xxxx_yyyymmdd.txt.gz
        //8761 - gfj_intl/uk/JOB_ADHOC_8761_xxxx_yyyymmdd.txt.gz
        //8762 - gfj_intl/in/JOB_ADHOC_8762_xxxx_yyyymmdd.txt.gz
        if (domainId == 8756){
            s3FilePath = folder + "ca/"+ f.getName();
        }else if (domainId == 8758){
            s3FilePath = folder + "de/"+ f.getName();
        } else if (domainId == 8761) {
            s3FilePath = folder + "uk/"+ f.getName();
        }else if (domainId == 8762){
            s3FilePath = folder + "in/"+ f.getName();
        }else {
            s3FilePath = folder + f.getName();
        }

        System.out.println("*** " + s3FilePath);
        try {
            s3.putObject(S3_BUCKET_NAME, s3FilePath, f);
        } catch (Exception e) {
            e.printStackTrace();
        }


//        S3Object s3Object = s3.getObject(S3_BUCKET_NAME, "gfj/sqs.txt");
//        System.out.println(s3Object);
        System.out.println(" ###===sendToS3 success");
    }

}
