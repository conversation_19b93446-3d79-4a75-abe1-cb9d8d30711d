package hao_test;

import seoclarity.backend.dao.actonia.VedMasterEngineFileInfoDAO;
import seoclarity.backend.entity.actonia.VedMasterEngineFileInfoEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;

public class VedMasterExport {
    private VedMasterEngineFileInfoDAO vedMasterEngineFileInfoDAO;
    public VedMasterExport() {
        vedMasterEngineFileInfoDAO = SpringBeanFactory.getBean("vedMasterEngineFileInfoDAO");
    }


    public static void main(String[] args) {
        VedMasterExport in = new VedMasterExport();
        in.process();
    }

    private void process() {
        //select vedTypeList , device ,rankType  from ved_master_engine_file_info limit 5
        List<VedMasterEngineFileInfoEntity> enList = vedMasterEngineFileInfoDAO.getList();
        // 或者写入文本文件
        writeListToFile(enList, "masterVEDEngine.txt");


    }

    public void writeListToFile(List<VedMasterEngineFileInfoEntity> list, String filePath) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            for (VedMasterEngineFileInfoEntity entity : list) {
                // 假设entity有toString()方法返回合适的字符串表示
                String line = entity.getVedTypeList() + "," + entity.getDevice() + "," + entity.getRankType();
                writer.write(line);
                writer.newLine(); // 换行
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
