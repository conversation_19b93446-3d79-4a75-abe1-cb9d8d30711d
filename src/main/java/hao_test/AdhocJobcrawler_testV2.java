package hao_test;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.sqs.AmazonSQS;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.AutoRunInfoEntityDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankProjectEntityDAO;
import seoclarity.backend.entity.actonia.adhoc.AdHocJobCrawlerEntity;
import seoclarity.backend.utils.AmazonS3UploadTool;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FileUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Adhoc Job crawler
 * https://www.wrike.com/open.htm?id=1082205455
 * get sqs job ->load file->send to ftp
 * Hao
 */
public class AdhocJobcrawler_testV2 {

    private static final String accessKey = "********************";
    private static final String secretKey = "v7Qts+qswF9qDojbKZQ1BofRbWcQLJu8CLM+eXrf";
    private static String queueName = "SQS_OUT_JOB_ADHOC_";
    private static String FTP_LOC = "/home/<USER>/";
    private static String FOLDER = "/adHocDownload/";
    private static String LOCAL_PATH = "files/adhoc/";

    /* 重试获取q次数 */
    private final int TRY_CNT = 3;
    // 每次拿消息的数量
    private final int message_max_size = 10;
    // sqs visibilityTimeOut
    // visibility_timeout = message_size_thread * 处理每条数据消耗的时间 * 1.2(系数)
    private final int visibility_timeout = 3600;
    private final int max_visibility_timeout = 43200;
    public static boolean testFlg = false;
    private static int testprojectId = 11046;
    private static int testStatus = 2;
    private static AmazonSQS amazonSQS;

    static {
        amazonSQS = SQSUtils.getAmazonSQS();
    }
    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
    private AutoRunInfoEntityDAO autoRunInfoEntityDAO;

//    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
//    private AgencyInfoManager agencyInfoManager;
//    private ZeptoMailSenderComponent zeptoMailSenderComponent;
//    private UserDAO userDAO;

    public AdhocJobcrawler_testV2() {
        autoAdhocRankProjectEntityDAO = SpringBeanFactory.getBean("autoAdhocRankProjectEntityDAO");
        autoRunInfoEntityDAO = SpringBeanFactory.getBean("autoRunInfoEntityDAO");
//        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
//        userDAO = SpringBeanFactory.getBean("userDAO");
    }

    public static void main(String[] args) {
        AdhocJobcrawler_testV2 in = new AdhocJobcrawler_testV2();
//        in.process();
//        in.process2(testprojectId,testStatus);
//        in.process3();
        in.process4();
    }

    private void process4() {
        autoRunInfoEntityDAO.updateStatus();
    }

    private void process2(Integer projectId,Integer status ) {
        autoAdhocRankProjectEntityDAO.updateRankUploadStatus(projectId,status);
    }

    private void process3() {

        String path = "i:\\11724\\adhoc\\1715\\JOB_ADHOC_1715_20230929.txt";
        File file = new File(path);
        if (null == file || 0 == file.length() || !file.exists()) {
            System.out.println("===###文件为空！");
        }
        putObj(file);
//        sentToS3(file.getName(),file.getAbsolutePath());
    }

//    private void process2() {
//        autoAdhocRankProjectEntityDAO.updateAllRankUploadStatus();
////        autoAdhocRankProjectEntityDAO.updateRankUploadStatusAndExtractStatus(1597, AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR, AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR);
////        autoAdhocRankProjectEntityDAO.updateRankUploadStatusAndExtractStatus(1594, AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR, AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR);
//
//    }

//    private void process() {
//        long start = System.currentTimeMillis();
//        //  (1): auto_adhoc_rank_project:  retrieveType=10(job) (2)rankStatus=2(完成)  (3)rankUploadStatus!=2(未完成)
//        List<AutoAdhocRankProjectEntity> projectList = autoAdhocRankProjectEntityDAO.getProject(AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_ADHOC_JOB_CRAWLER);
//        System.out.println(" =====###getProjectFromSQSByProjectId : " + projectList.size());
//
//        if (projectList.size() > 0) {
//            try {
//                getDataFromSQS(projectList);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        } else {
//            System.out.println("=====###noAdhocJobCrawlerTask======================");
//        }
//
//        long end = System.currentTimeMillis();
//        System.out.println("process time: " + (end - start) / 1000);
//    }

//    private void getDataFromSQS(List<AutoAdhocRankProjectEntity> projectList) throws Exception {
//
////        AmazonSQS service = new AmazonSQSClient(new BasicAWSCredentials(accessKey, secretKey), new ClientConfiguration().withMaxConnections(1000));
//
//        for (AutoAdhocRankProjectEntity autoEntity : projectList) {
//            autoEntity.setId(8888);
//            autoEntity.setOwnDomainId(4);
//            List<String> fileKeywords = new ArrayList<>();
//            // 一个project 创建一个本地文件
//            Integer domainid = autoEntity.getOwnDomainId();
//            File remoteFolder = new File(LOCAL_PATH + domainid + FOLDER);
//            if (!remoteFolder.exists()) {
//                remoteFolder.mkdirs();
//            }
//            String path = LOCAL_PATH + domainid + FOLDER + queueName + autoEntity.getId().toString();
//            FileWriter fileWriter = new FileWriter(path, true);
//            System.out.println(autoEntity.getId() + " domain : " + domainid + " path " + path);
//
//            flagWh:
//            while (true) {
//                //  test ADHOC_AUTOMATIC_KEYWORD_RANKCHECK_1256_DEAD_LETTER
//                String queueUrl = SQSUtils.createQueue(queueName + autoEntity.getId().toString(), amazonSQS);
////                String queueUrl = SQSUtils.createQueue("SQS_OUT_JOB_ADHOC_ZSHTEST_01", amazonSQS);
////                List<String> queueUrls = SQSUtils.getListQueues(amazonSQS, "SQS_OUT_JOB_ADHOC_1594");
//                if (StringUtils.isNotBlank(queueUrl)) {
//                    System.out.println("===###start process q: " + queueUrl);
//
//                    int tryCnt = TRY_CNT;
//                    for (int i = 0; i < tryCnt; i++) {
//                        Integer messageCnt = null;
//                        try {
//                            messageCnt = SQSUtils.getMessageNumberInQueue(amazonSQS, queueUrl);
//                        } catch (Exception e) {
//                            System.out.println("===###=>can not get message cnt: " + CollectionUtils.getErrorMsg(e) + ", will sleep 30s");
//                            try {
//                                Thread.sleep(30 * 1000);
//                            } catch (InterruptedException ex) {
//                                ex.printStackTrace();
//                            }
//                        }
//
//                        if (messageCnt != null && messageCnt > 0) {
//                            System.out.println(messageCnt);
//                            List<Message> messageFromQueue = SQSUtils.getMessageFromQueue(amazonSQS, queueUrl, message_max_size, visibility_timeout);
//
//                            // 取出数据 写入文件
//                            for (Message message : messageFromQueue) {
//                                String k = message.getBody();
//                                AdHocJobCrawlerEntity entity;
//                                System.out.println(k);
//                                entity = getFileInfo(k);
//                                String kw = writeJsonToFile(fileWriter, entity);
//                                fileWriter.write("\r\n");
//                                System.out.println(" ====###kw : " + kw);
//                                fileKeywords.add(kw);
//                            }
//
//                            if (fileKeywords.size() >= messageCnt) {
//                                System.out.println(" ===###消息拿完了=================");
//                                break;
//                            }
//                            System.out.println(fileKeywords.size());
//                            continue flagWh;
//                        } else {
//                            try {
//                                System.out.println("===###=>approximateNumberOfMessages is 0, will wait 30s and try again.");
//                                Thread.sleep(30 * 1000);
//                            } catch (InterruptedException e) {
//                                e.printStackTrace();
//                            }
//                        }
//                    }
//                    fileWriter.flush();
//                    System.out.println("===###queryUrl: " + queueUrl + ", Q message is null. will del this Q!");
//
//                    System.out.println("====###文件上传ftp ================================");
//                    saveFilesToFtp(path, domainid);
//
//                    System.out.println("====###删除本地文件 ================================");
//                    deleteFiles(path);
//
//                    System.out.println("====###更新 auto_adhoc_rank_project 状态为完成 ================================");
//                    autoAdhocRankProjectEntityDAO.updateRankUploadStatusAndExtractStatus(autoEntity.getId(), AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR, AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR);
//
//
//                    try {
//                        System.out.println("====###删除sqs ================================");
//                        System.out.println(queueUrl);
//                        if (!testFlg){
//                            amazonSQS.deleteQueue(new DeleteQueueRequest(queueUrl));
//                        }
//
//                    } catch (Exception e) {
//                        // 当前q可能已经被被其他服务器删除
//                        System.out.println("$EC=>del Q failed. msg: " + CollectionUtils.getErrorMsg(e));
//                        try {
//                            Thread.sleep(10 * 1000);
//                        } catch (InterruptedException interruptedException) {
//                            interruptedException.printStackTrace();
//                        }
//                    }
//                    System.out.println("===###sendEmail ====================================");
//                    UserEntity user = userDAO.getUser(autoEntity.getCreateUserId());
//                    sendEmail(user.getName(), user.getEmail(),  FTP_LOC + domainid + FOLDER,queueName + autoEntity.getId().toString(),
//                            autoEntity.getOwnDomainId(), autoEntity.getProjectName());
//
//
//                }
//                break ;
//            }
//        break;
//        }
//    }

    private void saveFilesToFtp(String localFileName, Integer domainid) {
        String targetPath = FTP_LOC + domainid + FOLDER;
        System.out.println("===###savefileToFTP =====" + targetPath + " file : " + localFileName);
        FTPUtils.saveFileToFTP(domainid, localFileName, targetPath);
    }

    private void deleteFiles(String fileName) {
        File localFile = new File(fileName);
        FileUtils.deleteFile(localFile);
    }


    private AdHocJobCrawlerEntity getFileInfo(String k) {
        AdHocJobCrawlerEntity en = new AdHocJobCrawlerEntity();
        JSONObject jasonk = JSONObject.parseObject(k);
        //keyword level
        if (null != jasonk.get("keyword")) {
            String keyword = jasonk.get("keyword").toString();
            en.setKeyword(keyword);
        }
        if (null != jasonk.get("queryDate")) {
            String queryDate = jasonk.get("queryDate").toString();
            en.setQueryDate(queryDate);
        }
//        if (null != jasonk.get("emptyHtml")) {
//            String emptyHtml = jasonk.get("emptyHtml").toString();
//            en.setEmptyHtml(emptyHtml);
//        }
//        if (null != jasonk.get("cityId")) {
//            String cityId = jasonk.get("cityId").toString();
//            en.setCityId(cityId);
//        }
        if (null != jasonk.get("cityName")) {
            String cityName = jasonk.get("cityName").toString();
            en.setCityName(cityName);
        }


        // url level

        if (null == jasonk.get("jobEntityVOs")) {
            System.out.println("===###keyword has null jobEntityVOs :  " + jasonk.get("keyword").toString());
        } else {
            JSONArray jobEntityVOsArr = jasonk.getJSONArray("jobEntityVOs");
            List<AdHocJobCrawlerEntity.JobEntityVO> voList = new ArrayList<>();
            for (Object obj : jobEntityVOsArr) {
                AdHocJobCrawlerEntity.JobEntityVO jobEntityVO = en.new JobEntityVO();
//                AdHocJobCrawlerEntity.JobEntityVO job = en.new JobEntityVO();
                JSONObject jobJson = (JSONObject) JSONObject.toJSON(obj);
                if (null != jobJson.get("jobTitle")) {
                    String jobTitle = jobJson.get("jobTitle").toString();
                    jobEntityVO.setJobTitle(jobTitle);
                }
                if (null != jobJson.get("jobCompany")) {
                    String jobCompany = jobJson.get("jobCompany").toString();
                    jobEntityVO.setJobCompany(jobCompany);
                }
                if (null != jobJson.get("jobLocation")) {
                    String jobLocation = jobJson.get("jobLocation").toString();
                    jobEntityVO.setJobLocation(jobLocation);
                }
                if (null != jobJson.get("jobSource")) {
                    String jobSource = jobJson.get("jobSource").toString();
                    jobEntityVO.setJobSource(jobSource);
                }
                if (null != jobJson.get("jobPostTime")) {
                    String jobPostTime = jobJson.get("jobPostTime").toString();
                    jobEntityVO.setJobPostTime(jobPostTime);
                }
                if (null != jobJson.get("jobType")) {
                    String jobType = jobJson.get("jobType").toString();
                    jobEntityVO.setJobType(jobType);
                }
                if (null != jobJson.get("jobPrimaryJobLink")) {
                    String jobPrimaryJobLink = jobJson.get("jobPrimaryJobLink").toString();
                    jobEntityVO.setJobPrimaryJobLink(jobPrimaryJobLink);
                }
                if (null != jobJson.get("jsCompany")) {
                    String jsCompany = jobJson.get("jsCompany").toString();
                    jobEntityVO.setJsCompany(jsCompany);
                }
                if (null != jobJson.get("companyId")) {
                    String companyId = null == jobJson.get("companyId") ? "" : jobJson.get("companyId").toString();
                    jobEntityVO.setCompanyId(companyId);
                }
                if (null != jobJson.get("companyCity")) {
                    String companyCity = null == jobJson.get("companyCity") ? "" : jobJson.get("companyCity").toString();
                    jobEntityVO.setCompanyCity(companyCity);
                }
                if (null != jobJson.get("companyState")) {
                    String companyState = null == jobJson.get("companyState") ? "" : jobJson.get("companyState").toString();
                    jobEntityVO.setCompanyState(companyState);
                }
                if (null != jobJson.get("minSalary")) {
                    String minSalary = null == jobJson.get("minSalary") ? "" : jobJson.get("minSalary").toString();
                    jobEntityVO.setMinSalary(minSalary);
                }
                if (null != jobJson.get("maxSalary")) {
                    String maxSalary = null == jobJson.get("maxSalary") ? "" : jobJson.get("maxSalary").toString();
                    jobEntityVO.setMaxSalary(maxSalary);
                }
                if (null != jobJson.get("salaryUnit")) {
                    String salaryUnit = null == jobJson.get("salaryUnit") ? "" : jobJson.get("salaryUnit").toString();
                    jobEntityVO.setSalaryUnit(salaryUnit);
                }
                if (null != jobJson.get("currencyCode")) {
                    String currencyCode = null == jobJson.get("currencyCode") ? "" : jobJson.get("currencyCode").toString();
                    jobEntityVO.setCurrencyCode(currencyCode);
                }
                if (null != jobJson.get("applyOnList")) {
                    Object applyOnObj = jobJson.get("applyOnList");
                    Map<String, String> applyOnList = new HashMap<>();
                    Map<String, String> applyOnMap = JSONObject.parseObject(JSONObject.toJSONString(applyOnObj), Map.class);
                    for (String s : applyOnMap.keySet()) {
                        applyOnList.put(s, applyOnMap.get(s));
                    }
                    jobEntityVO.setApplyOnList(applyOnList);
                }

                JSONArray rantingArr = new JSONArray();
                if (null != jobJson.get("ratingArray") && jobJson.getJSONArray("ratingArray").size() > 0) {
                    rantingArr = jobJson.getJSONArray("ratingArray");
                    List<AdHocJobCrawlerEntity.JobEntityVO.RatingEntity> rantingList = new ArrayList<>();
                    for (Object rantingObj : rantingArr) {
                        JSONObject rantingJson = (JSONObject) JSONObject.toJSON(rantingObj);
                        AdHocJobCrawlerEntity.JobEntityVO.RatingEntity rantingEntity = jobEntityVO.new RatingEntity();
                        if (null != rantingJson.get("link")) {
                            String link = rantingJson.get("link").toString();
                            rantingEntity.setLink(link);
                        }
                        if (null != rantingJson.get("source")) {
                            String source = rantingJson.get("source").toString();
                            rantingEntity.setSource(source);
                        }
                        if (null != rantingEntity && (StringUtils.isNotBlank(rantingEntity.getLink()) || StringUtils.isNotBlank(rantingEntity.getSource()))) {
                            rantingList.add(rantingEntity);
                        }
                    }
                    jobEntityVO.setRatingArray(rantingList);
                }

                //   salaryArray
                JSONArray salaryArr = new JSONArray();
                if (null != jobJson.get("salaryArray") && jobJson.getJSONArray("salaryArray").size() > 0) {
                    salaryArr = jobJson.getJSONArray("salaryArray");
                    List<AdHocJobCrawlerEntity.JobEntityVO.SalaryEntity> salaryList = new ArrayList<>();
                    for (Object salaryObj : salaryArr) {
                        JSONObject salaryJson = (JSONObject) JSONObject.toJSON(salaryObj);
                        AdHocJobCrawlerEntity.JobEntityVO.SalaryEntity salaryEntity = jobEntityVO.new SalaryEntity();
                        if (null != salaryJson.get("url")) {
                            String url = salaryJson.get("url").toString();
                            salaryEntity.setUrl(url);
                        }
                        if (null != salaryJson.get("title")) {
                            String title = salaryJson.get("title").toString();
                            salaryEntity.setTitle(title);
                        }
                        if (null != salaryJson.get("meta")) {
                            String meta = salaryJson.get("meta").toString();
                            salaryEntity.setMeta(meta);
                        }
                        if (null != salaryJson.get("salary")) {
                            String salary = salaryJson.get("salary").toString();
                            salaryEntity.setSalary(salary);
                        }
                        if (null != salaryJson.get("salary_unit")) {
                            String salary_unit = salaryJson.get("salary_unit").toString();
                            salaryEntity.setSalaryUnit(salary_unit);
                        }
                        if (null != salaryJson.get("source")) {
                            String source = salaryJson.get("source").toString();
                            salaryEntity.setSource(source);
                        }
                        if (null != salaryEntity && (StringUtils.isNotBlank(salaryEntity.getUrl()) || StringUtils.isNotBlank(salaryEntity.getTitle()))) {
                            salaryList.add(salaryEntity);
                        }
                    }
                    jobEntityVO.setSalaryArray(salaryList);
                }

                voList.add(jobEntityVO);

            }
            en.setJobEntityVOs(voList);
        }
        return en;
    }

    private void writeKeyToFile(AdHocJobCrawlerEntity entity) {
        String kw = entity.getKeyword();
        String path = "I:\\11724\\";
        path = path + kw;
        try {
            PrintWriter out = new PrintWriter(new FileWriter(path));
            Gson gson = new Gson();
            String jsonString = gson.toJson(entity);
            System.out.println("====###jasonWriteToFile===============");
            System.out.println(jsonString);
            out.write(jsonString);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String writeJsonToFile(FileWriter fileWriter, AdHocJobCrawlerEntity entity) {
        String kw = entity.getKeyword();
        try {
            Gson gson = new Gson();
            String jsonString = gson.toJson(entity);
            fileWriter.write(jsonString);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return kw;
    }


//    private void sendEmail(String userName, String emailAddress, String fileLoc, String filename, int ownDomainId, String projectName) throws Exception {
//
//        String emailTo = emailAddress;
//        emailTo = "<EMAIL>";
//        System.out.println("=========Send to : " + emailTo + " start!");
//
//        String subject = "Ad Hoc Data Retrieval Download Ready - " + projectName;
//        String info = "The raw data for ad hoc retrieval project - " + projectName + " is now ready for download.";
//
//        Map<String, Object> reportMap = new HashMap<String, Object>();
//        reportMap.put("userName", userName);
//        reportMap.put("info", info);
//
//        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(ownDomainId);
//
////        int sizeMb = 0;
////        File file = new File(fileLoc + filename);
////        String downloadFileName = "";
////        if (file.exists()) {
////            long fileSize = file.length();
////            sizeMb = (int) (fileSize / 1024 / 1024);
////            System.out.println(" OID:" + ownDomainId + " filename: " + (fileLoc + filename) +
////                    " size:" + fileSize + " MB:" + sizeMb);
////
////            GZipUtil.zipFile(file.getAbsolutePath(), GZipUtil.ZIPFile_POSTFIX);
////            downloadFileName = file.getAbsolutePath() + GZipUtil.ZIPFile_POSTFIX;
////        }
////
////        FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
////        String host = ftpServerInfo.getPrivateHost();
////        String ftpUsername = ftpServerInfo.getServerUserName();
////        String ftpPassword = ftpServerInfo.getServerPassword();
//
////        FTPUtils.saveFileToFTPForAdhoc(host, ftpUsername, ftpPassword, ownDomainId, downloadFileName, true);
//        //https://www.wrike.com/open.htm?id=1066001811 move to seagate
////        String linkUrl = "";
////        try {
////            boolean isSaved = SeagateUtils.saveFileToDefaultSeagate(ownDomainId, downloadFileName);
////            if(!isSaved){
////                System.out.println("===send to Seagate Failed!projectName:" + projectName);
////            }
////            linkUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, filename + GZipUtil.ZIPFile_POSTFIX);
////            System.out.println("==isSaved:" + isSaved + ",linkUrl:" + linkUrl);
////        }catch (Exception e){
////            e.printStackTrace();
////            System.out.println("===send to Seagate Failed!projectName:" + projectName);
////        }
//
////        String linkText = null;
////        try {
////            String name = URLEncoder.encode(filename + GZipUtil.ZIPFile_POSTFIX, "utf-8");
////            linkText = "https://downloads.seoclarity.net/adhocRankingExtract/" + StringUtils.replace(name, "+", "%20");
////        linkText = linkUrl;
////        } catch (UnsupportedEncodingException e) {
////            log.error(e.getMessage(), e);
////            linkText = "https://downloads.seoclarity.net/adhocRankingExtract/" + filename + GZipUtil.ZIPFile_POSTFIX;
////            linkText = linkUrl;
////        }
//
////        System.out.println("  fileLink:" + linkText);
////        reportMap.put("fileLink", linkText);
////        String[] bccTo = new String[]{};
//        String[] bccTo = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", Constants.DEV_TEAM_EMAIL};
//        bccTo = new String[]{"<EMAIL>"};
////        emailSenderComponent.sendMimeMultiPartMailAndBccWithNickname(agencyInfo.getEmailNickname(), new String[]{emailTo}, bccTo,
////                subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo);
//        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo,subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo,
//                ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, null, null);
//        System.out.println("=========Send to : " + emailAddress + " success!");
//    }


    private static final String S3_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
    private static final String S3_SECRET_KEY =AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
    private static final String S3_ROLE_ARN = "arn:aws:iam::054715827583:role/opticon_seoclarity_export_s3_role";
    private static final String S3_EXTERNAL_ID = "seoclarityDev-externalId-assumeRole";
    private static final String S3_BUCKET_NAME = "msci-seoclarity-extracts";
    private static final int S3_SESSION_DURATION_SECONDS = 3600;
    private static final int S3_RETRY_COUNT = 10;
    private static final String S3_FOLDER = "gfj/";


    private void sentToS3(String s3Key, String filePath) {
        s3Key = S3_FOLDER + s3Key;

        boolean savedFilesToS3 = AmazonS3UploadTool.sendFileToS3WithRoleArnByTransferManager(S3_ACCESS_KEY, S3_SECRET_KEY, Regions.US_EAST_2, S3_ROLE_ARN, S3_BUCKET_NAME, s3Key,
                filePath, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);

//        boolean savedFilesToS3 = AmazonS3UploadTool.sendFileToS3WithRoleAndSSEByTransferManager(
//                S3_ACCESS_KEY, S3_SECRET_KEY, S3_EXTERNAL_ID, S3_ROLE_ARN, S3_BUCKET_NAME, s3Key,
//                filePath, Regions.US_WEST_2, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
        if (!savedFilesToS3) {
            System.out.println("===send to s3 failed.");
        } else {
            System.out.println("=====send to s3 success!" + s3Key);
        }
    }
    static final String AWS_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
    static final String AWS_SECRET_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
    static final BasicAWSCredentials awsCreds = new BasicAWSCredentials(AWS_ACCESS_KEY, AWS_SECRET_KEY);
    static final AmazonS3 s3 = AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(awsCreds)).withRegion("us-east-2").build();



    private void putObj(File f) {
        String s3FilePath = S3_FOLDER + f.getName();
        System.out.println("*** " + s3FilePath);
        try {
            s3.putObject(S3_BUCKET_NAME, s3FilePath, f);
        }catch (Exception e){
            e.printStackTrace();
        }


        S3Object s3Object = s3.getObject(S3_BUCKET_NAME, "gfj/JOB_ADHOC_1715_20230929.txt");
        System.out.println(s3Object);
        System.out.println("success");
    }

}
