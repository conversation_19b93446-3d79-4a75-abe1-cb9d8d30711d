package hao_test;

import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.VedMasterEngineFileInfoDAO;
import seoclarity.backend.dao.actonia.Vs3vedTypeInfoDAO;
import seoclarity.backend.entity.actonia.VedMasterEngineFileInfoEntity;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;


/**
 * https://www.wrike.com/open.htm?id=1646469399
 * --hao
 * --250506
 */
public class Test250506 {
    private static final String folder = "files/250506Files/250618/";
    private static String errorList = "";
    private VedMasterEngineFileInfoDAO vedMasterEngineFileInfoDAO;

    public Test250506() {
        vedMasterEngineFileInfoDAO = SpringBeanFactory.getBean("vedMasterEngineFileInfoDAO");
    }

    public static void main(String[] args) {
        Test250506 in = new Test250506();
        File doneFolder = new File(folder);
        if (doneFolder != null && doneFolder.isDirectory()) {
            for (File file : doneFolder.listFiles()) {
                try {
                    System.out.println(" ****  file name  : " + file.getName());
                    in.process(file);
                    System.out.println(errorList);
                } catch (Exception e) {
//                    e.printStackTrace();
                }
            }
        }

    }

    private void process(File file) {
        Set<Integer> vedSet = new HashSet<>();
        List<VedMasterEngineFileInfoEntity> list = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] parts = line.split("\t");
                VedMasterEngineFileInfoEntity en = new VedMasterEngineFileInfoEntity();
                String vedTypeStr = parts[0];
                Integer vedType = Integer.parseInt(vedTypeStr);
                String feature = parts[1];
                String subFeature = parts[2];
                String domain = parts[3];
                try {


                if (!vedSet.contains(vedType)) {
                    vedSet.add(vedType);

                    System.out.println(vedTypeStr + " :  vedType : " + vedType + " feature : " + feature + " subFeature : " + subFeature + " domain : " + domain);

                    List<VedMasterEngineFileInfoEntity> findOld = vedMasterEngineFileInfoDAO.getByVedType(vedTypeStr);

                    if (findOld.size() == 0) {
                        System.out.println("===###找不到vedType ： " + vedType);
                        VedMasterEngineFileInfoEntity vedMasterEngineFileInfoEntity = new VedMasterEngineFileInfoEntity();
                        if (StringUtils.isNotBlank(feature)) {
                            vedMasterEngineFileInfoEntity.setFeature(feature);
                        }else {
                            vedMasterEngineFileInfoEntity.setFeature("");
                        }
                        if (StringUtils.isNotBlank(domain)) {
                            vedMasterEngineFileInfoEntity.setDomain(domain);
                        }else {
                            vedMasterEngineFileInfoEntity.setDomain("");
                        }
                        if (StringUtils.isNotBlank(subFeature)) {
                            vedMasterEngineFileInfoEntity.setSubFeature(subFeature);
                        }else {
                            vedMasterEngineFileInfoEntity.setSubFeature("");
                        }

                        vedMasterEngineFileInfoEntity.setUpdateDate(new Date());
                        vedMasterEngineFileInfoEntity.setDescription("");
                        vedMasterEngineFileInfoEntity.setVedTypeList(vedTypeStr);
                        vedMasterEngineFileInfoEntity.setUrl("");
                        vedMasterEngineFileInfoEntity.setVedTypeMurmurHash(MurmurHashUtils.getMurmurHash3_64(vedTypeStr));
                        vedMasterEngineFileInfoEntity.setCreateDate(new Date());
                        vedMasterEngineFileInfoEntity.setDevice("d");
                        vedMasterEngineFileInfoDAO.addNew(vedMasterEngineFileInfoEntity);
                        vedMasterEngineFileInfoEntity.setDevice("m");
                        vedMasterEngineFileInfoDAO.addNew(vedMasterEngineFileInfoEntity);

                    } else if (findOld.size() == 2) {
                        System.out.println("===###找到2条vedType ： " + vedType);
                        for (VedMasterEngineFileInfoEntity old : findOld) {
                            if (StringUtils.isNotBlank(feature)) {
                                old.setFeature(feature);
                            }
                            if (StringUtils.isNotBlank(domain)) {
                                old.setDomain(domain);
                            }
                            if (StringUtils.isNotBlank(subFeature)) {
                                old.setSubFeature(subFeature);
                            }
                            old.setUpdateDate(new Date());
                            vedMasterEngineFileInfoDAO.updateRows(old);
                        }
                    } else if (findOld.size() == 1) {
                        System.out.println("===###找到1条vedType ： " + vedType);
                        for (VedMasterEngineFileInfoEntity old : findOld) {
                            String device = old.getDevice();
                            if (device.equals("dm")) {

                                if (StringUtils.isNotBlank(feature)) {
                                    old.setFeature(feature);
                                }
                                if (StringUtils.isNotBlank(domain)) {
                                    old.setDomain(domain);
                                }
                                if (StringUtils.isNotBlank(subFeature)) {
                                    old.setSubFeature(subFeature);
                                }
                                old.setUpdateDate(new Date());
                                if (vedType == 151671){
                                    old.setFeature("Refinement");
                                }
                                vedMasterEngineFileInfoDAO.updateRows(old);
                            } else if (device.equals("d")) {
                                if (StringUtils.isNotBlank(feature)) {
                                    old.setFeature(feature);
                                }
                                if (StringUtils.isNotBlank(domain)) {
                                    old.setDomain(domain);
                                }
                                if (StringUtils.isNotBlank(subFeature)) {
                                    old.setSubFeature(subFeature);
                                }
                                old.setUpdateDate(new Date());
                                vedMasterEngineFileInfoDAO.updateRows(old);
                                old.setDevice("m");
                                vedMasterEngineFileInfoDAO.addNew(old);
                            } else if (device.equals("m")) {
                                if (StringUtils.isNotBlank(feature)) {
                                    old.setFeature(feature);
                                }
                                if (StringUtils.isNotBlank(domain)) {
                                    old.setDomain(domain);
                                }
                                if (StringUtils.isNotBlank(subFeature)) {
                                    old.setSubFeature(subFeature);
                                }
                                old.setUpdateDate(new Date());
                                vedMasterEngineFileInfoDAO.updateRows(old);
                                old.setDevice("d");
                                vedMasterEngineFileInfoDAO.addNew(old);
                            }
                        }
                    }
                }
                }catch (Exception e){
                    System.out.println("===###Error : " + vedTypeStr);
                    errorList  = errorList + " ,  " + vedTypeStr;
                }

            }
        } catch (IOException e) {
            e.getMessage();
//            e.printStackTrace();
        }

    }
}
