package hao_test;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import seoclarity.backend.utils.cityhash.CityHashUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;

public class S3CheckTest {
    public static final String ALI_ACCESS_KEY = "STX11L0ZZFJN0PS6R1MSD4OH";
    public static final String ALI_SECRET_KEY = "4uKP6YQbx9U+l+0LATY6oHdh1zhAqeu1SAXGEYgpwVO";
    public static final String ALI_BUCKET_NAME = "clarity-site-audit-central";
    private static String tempFilePath = "/home/<USER>/file/zipTmp";
    private static AWSCredentials aliCredentials = new BasicAWSCredentials(ALI_ACCESS_KEY, ALI_SECRET_KEY);
    private static AmazonS3 aliS3Client;

    public static void main(String[] args) {
        S3CheckTest in = new S3CheckTest();
        in.process();
    }

    private void process() {
        aliS3Client = new AmazonS3Client(aliCredentials);
        aliS3Client.setEndpoint("s3.us-central-2.clarity1.lyve.seagate.com");

        String urlhash = CityHashUtil.getCityHash64ForString(("https://www.unitedvanlines.com/moving-tips/blog/category/city-guides"));
        String k = "4/10013920/" + urlhash;
//        GetObjectRequest rangeObjectRequest = new GetObjectRequest(ALI_BUCKET_NAME, k);
//        S3Object objectPortion = aliS3Client.getObject(rangeObjectRequest);
        System.out.println("key : " + k);
        try {
            S3Object o = aliS3Client.getObject(ALI_BUCKET_NAME, k);
            try {
                S3ObjectInputStream s3is = o.getObjectContent();
                // File localFile = new File("D:\\Doc\\work\\ticket\\Rank\\S3\\BBFiles\\76b8a941-195f-4c78-8e57-d51503e6d409_2.zip");
                File localFile = new File("files/aaa.gz");
                FileOutputStream fos = new FileOutputStream(localFile);
                byte[] read_buf = new byte[1024];
                int read_len = 0;
                while ((read_len = s3is.read(read_buf)) > 0) {
                    fos.write(read_buf, 0, read_len);
                }
                s3is.close();
                fos.close();
                System.out.println(localFile.length());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }catch (Exception e){
            e.printStackTrace();
        }


    }
}
