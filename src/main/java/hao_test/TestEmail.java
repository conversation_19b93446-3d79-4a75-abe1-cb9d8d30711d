package hao_test;

import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;

import java.util.HashMap;
import java.util.Map;

public class TestEmail {
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public TestEmail() {
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }
    public static void main(String[] args) {
        TestEmail in = new TestEmail();
        in.process();
    }

    private void process() {
        String subject = "==================================";
        String message = "==================================:";
        sendMailReport(subject, message);
    }

    private void sendMailReport(String subject, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "All");
        reportMap.put("successMessage", message);
//        String emailTo = "<EMAIL>";
        String emailTo = "<EMAIL>";
//        String[] ccTo = new String[]{};
        String[] ccTo = new String[]{"<EMAIL>"};

        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap,
                null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }
}
