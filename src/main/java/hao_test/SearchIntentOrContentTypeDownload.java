package hao_test;

import org.json.JSONArray;
import org.json.JSONObject;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SearchIntentOrContentTypeDownload {
    private static Map<String, String> menuMap = new HashMap<>();
    private static final String SPILT = "\t";
    private CommonParamDAO commonParamDAO;

    public SearchIntentOrContentTypeDownload() {
        commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
    }

    private static int domainId = 8810;
    private static String type = "UrlTextType";  // UrlTextType    --Content Type => URL  /  KwTextType    --Search Intent => Keyword

    static {
        menuMap.put("eq", "equal");
        menuMap.put("neq", "not equal");
        menuMap.put("ct", "contain");
        menuMap.put("nct", "not contain");
        menuMap.put("sw", "start with");
        menuMap.put("ew", "end with");
        menuMap.put("inc", "include");
        menuMap.put("ninc", "exclude");
        menuMap.put("regex", "regex");
        menuMap.put("like", "like");
        menuMap.put("pt", "regexp");
        menuMap.put("npt", "regexp not match");
    }

    public static void main(String[] args) {
        SearchIntentOrContentTypeDownload in = new SearchIntentOrContentTypeDownload();
        if (null != args && args.length >0){
            if (null != args[0]){
                domainId = Integer.parseInt(args[0]);
                type = args[1];
            }
        }
        in.process();
    }

    private void process() {
        String filename = "";
        if ("UrlTextType".equals(type)){
            filename = "Content Type";
        }else if ("KwTextType".equals(type)){
           filename = "Search Intent";
        }

        List<CommonParamEntity> list = commonParamDAO.getRegExListByDomainIdAndFuncName(domainId,type);
//        for (CommonParamEntity entity : list) {
//            System.out.println(entity.getTitle());
//            System.out.println(entity.getParamJson());
//            String json = "{\"leaf\":false,\"action\":\"ct\",\"value\":\"\",\"level\":0,\"cond\":\"or\",\"items\":[{\"leaf\":true,\"action\":\"ct\",\"value\":\"/shop/baby-kids\",\"items\":[],\"cond\":\"and\",\"level\":1},{\"leaf\":true,\"action\":\"ct\",\"value\":\"/shop/baby-child\",\"items\":[],\"cond\":\"and\",\"level\":1}]}";
//
//        }

        String filePath = filename + "_" + domainId + ".txt";

        try (FileWriter writer = new FileWriter(filePath)) {
            String header = filename + " Name" + SPILT + "Pattern" + SPILT + "String";
            writer.write(header);// header

            // 遍历列表并写入文件
            for (CommonParamEntity entity : list) {
                String title = entity.getTitle();
                String paramJson = entity.getParamJson();
                // 解析 paramJson
                JSONObject jsonObject = new JSONObject(paramJson);
                JSONArray items = jsonObject.getJSONArray("items");
                if (items.length() > 0) {
                    for (int i = 0; i < items.length(); i++) {
                        JSONObject item = items.getJSONObject(i);
                        String col = title + SPILT + menuMap.get(item.getString("action")) + SPILT + item.getString("value");
                        writer.write("\n" + col);
                    }

                } else {
                    String col = title + SPILT + menuMap.get(jsonObject.getString("action")) + SPILT + jsonObject.getString("value");
                    writer.write("\n" + col);
                }


            }
            System.out.println("导出成功！");
        } catch (IOException e) {
            e.printStackTrace();
            System.err.println("导出失败：" + e.getMessage());
        }
    }


}
