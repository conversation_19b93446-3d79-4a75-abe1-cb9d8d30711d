{"crawl_request_id": {"type": "UInt32", "copyFrom": "crawl_request_log_id_i"}, "crawl_request_date": {"type": "Date", "createFrom": "crawl_date_long"}, "domain_id": {"type": "UInt32", "copyFrom": "domain_id_i"}, "url": {"type": "String"}, "url_murmur_hash": {"type": "UInt32"}, "url_hash": {"type": "UInt64"}, "encoding": {"type": "String"}, "schema_type": {"type": "String"}, "errors.path": {"type": "<PERSON><PERSON><PERSON>(String)"}, "errors.type": {"type": "<PERSON><PERSON><PERSON>(String)"}, "warning_count": {"type": "UInt32"}, "markup": {"type": "String"}, "validation_errors": {"type": "String"}, "warnings.message": {"type": "<PERSON><PERSON><PERSON>(String)"}, "warnings.path": {"type": "<PERSON><PERSON><PERSON>(String)"}, "warnings.type": {"type": "<PERSON><PERSON><PERSON>(String)"}, "errors.message": {"type": "<PERSON><PERSON><PERSON>(String)"}, "error_count": {"type": "UInt32"}}