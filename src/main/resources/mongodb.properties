#new shard only desktop from 2016/05/10 - persent
#mongodb.host=***************
mongodb.host=*************

#for old shard desktop from begin - 2016/05/10
oldshard.20160510.host=*************
mongodb.port=40000

#for all mobile data 
mongodb.mobile.historical.host=***********
mongodb.mobile.historical.port=40000

mongodb.user=
mongodb.password=
db.auth("shinetech","k8XBb2Yh");

# google analytics mongodb router on local machine
mongodb.host.google.analytics=localhost
mongodb.port.google.analytics=30000
mongodb.user.google.analytics=
mongodb.password.google.analytics=


# for replica set
#mongodb.replicaset.host=***************:40000,*************:40000,**************:40000
#mongodb.replicaset.host=************:40000,************:40000
#mongodb.replicaset.host=************:40000,************:40000
mongodb.replicaset.host=**************:40000,**************:40000
