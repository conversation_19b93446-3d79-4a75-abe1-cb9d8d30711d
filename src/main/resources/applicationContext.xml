<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
          http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
          http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
          http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
          http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd" >

	<context:property-placeholder location="jdbc.properties" ignore-unresolvable="true"/>
	<context:property-placeholder location="mailjet.properties" ignore-unresolvable="true"/>
	<context:property-placeholder location="serverInfo.properties" ignore-unresolvable="true"/>
	<context:property-placeholder location="kafka.properties" ignore-unresolvable="true"/>
	<context:component-scan base-package="seoclarity.backend" />

	<!--<import resource="applicationContext-email.xml"></import>-->
	<bean id="FTPServerInfo" class="seoclarity.backend.entity.bean.FTPServerInfoBean">
		<property name="privateIp" value="${serverInfo.privateIp}" />
		<property name="privateHost" value="${serverInfo.privateHost}" />
		<property name="publicIp" value="${serverInfo.publicIp}" />
		<property name="publicHost" value="${serverInfo.publicHost}" />
		<property name="serverUserName" value="${serverInfo.serverUserName}" />
		<property name="serverPassword" value="${serverInfo.serverPassword}" />
		<property name="serverTargetFolder" value="${serverInfo.serverTargetFolder}" />
	</bean>

	<bean id="siteClarityKafkaConsumerConfig" class="seoclarity.backend.upload.siteclarity.kafka.KafkaConfigVo">
		<property name="kafkaServer" value="${siteclarity.consumer.kafka_bootstrap_servers}" />
		<property name="kafkaTopic" value="${siteclarity.consumer.kafka_topic}" />
		<property name="kafkaGroupId" value="${siteclarity.consumer.kafka_group_id}" />
		<property name="maxPollRecords" value="${siteclarity.consumer.max_poll_records}" />
		<property name="maxPartitionFetchBytes" value="${siteclarity.consumer.max_partition_fetch_bytes}" />
		<property name="fetchMaxBytes" value="${siteclarity.consumer.fetch_max_bytes}" />
		<property name="autoCommit" value="${siteclarity.consumer.auto_commit}" />
	</bean>

</beans>