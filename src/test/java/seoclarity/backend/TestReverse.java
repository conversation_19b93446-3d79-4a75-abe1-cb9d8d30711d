package seoclarity.backend;

import seoclarity.backend.utils.CommonUtils;

public class TestReverse {

	public static void main(String[] args) {
		String competitorDomain = "abcnews.go.com";
//		String reverseDomain = CommonUtils.reverseDomainNameByDot(competitorDomain);
//		
//		System.out.println(reverseDomain);
		
		System.out.println(CommonUtils.getReverseRootDomain("content.abia.org:8080", true));
		System.out.println(CommonUtils.getReverseRootDomain("content.abia.org:8080", false));
		
		System.out.println(CommonUtils.getReverseRootDomain("www.livepark19.com/", true));
		System.out.println(CommonUtils.getReverseRootDomain("www.livepark19.com/", false));
		
		System.out.println(CommonUtils.getReverseRootDomain("www.livepark19.com/", true));
		System.out.println(CommonUtils.getReverseRootDomain("www.livepark19.com/", false));
		
		System.out.println(CommonUtils.getReverseRootDomain("www.stregispuntamita.com ", true));
		System.out.println(CommonUtils.getReverseRootDomain("www.stregispuntamita.com ", false));
		
		System.out.println(CommonUtils.getReverseRootDomain("menards.pittsburghpaints.com", true));
		System.out.println(CommonUtils.getReverseRootDomain("menards.pittsburghpaints.com", false));
		
		System.out.println(CommonUtils.getReverseRootDomain("www.w2ogroup.com…", true));
		System.out.println(CommonUtils.getReverseRootDomain("www.w2ogroup.com…", false));
		
		System.out.println(CommonUtils.getReverseRootDomain("www.sunbingo.co.ul", true));
		System.out.println(CommonUtils.getReverseRootDomain("www.sunbingo.co.ul", false));

//		System.out.println(Integer.MAX_VALUE);
	}

}
